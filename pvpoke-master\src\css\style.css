@charset "UTF-8";
/* VARIABLES */
/* MIXINS */
/* STRUCTURE */
html {
  height: 100%;
}

body, html {
  margin: 0;
  font-family: Arial, sans-serif;
  color: #000;
}

body {
  background-color: #000 !important;
  background: url("../img/themes/sunflower/sunflower-bg.jpg");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  background-position: center bottom;
  min-height: 100%;
}

.clear {
  clear: both;
}

.flex {
  display: flex;
}
.flex.space-between {
  justify-content: space-between;
}
.flex.cols-3 > div {
  flex-basis: 33%;
}

.hide {
  display: none;
}

.mobile {
  display: none;
}

header {
  padding: 3px;
  box-sizing: border-box;
  background: #f9fdff;
  position: fixed;
  z-index: 25;
  width: 100%;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 6px;
  top: 0;
}
header .header-wrap {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
header a img {
  display: block;
  height: 30px;
}
header .menu {
  padding: 3px 0;
}
header .menu a {
  display: block;
  margin-left: 20px;
  color: #003462;
  text-decoration: none;
  background-repeat: no-repeat;
  background-position: 8px center;
  padding: 4px 8px 4px 30px;
  background-size: 16px 16px;
  font-weight: bold;
  border-radius: 20px;
  -webkit-transition: background-color 0.2s ease-out 0s;
  -moz-transition: background-color 0.2s ease-out 0s;
  -o-transition: background-color 0.2s ease-out 0s;
  transition: background-color 0.2s ease-out 0s;
}
header .menu a.twitter {
  color: #01bbff !important;
}
header .menu a.tera {
  color: #d250a3 !important;
}
header .menu > a.selected, header .menu .parent-menu > a.selected {
  background-color: #c4def5;
  -webkit-transition: background-color 0s ease-out 0s;
  -moz-transition: background-color 0s ease-out 0s;
  -o-transition: background-color 0s ease-out 0s;
  transition: background-color 0s ease-out 0s;
}
header .menu .parent-menu {
  position: relative;
}
header .menu .parent-menu .submenu {
  max-height: 0;
  overflow: hidden;
  -webkit-transition: max-height 0.125s ease-out 0s;
  -moz-transition: max-height 0.125s ease-out 0s;
  -o-transition: max-height 0.125s ease-out 0s;
  transition: max-height 0.125s ease-out 0s;
}
header .menu .parent-menu .submenu.active {
  display: block;
  max-height: 600px;
  pointer-events: auto;
}
header .menu .parent-menu > a {
  position: relative;
}
header .menu .parent-menu > a span {
  position: absolute;
  display: block;
  content: ".";
  font-size: 0;
  width: 12px;
  height: 4px;
  background: url("../img/themes/sunflower/dropdown-triangle-white.png");
  background-repeat: no-repeat;
  right: 0px !important;
  left: auto;
  top: 4px;
  padding: 13px 12px;
  background-position: center;
}
header .menu .parent-menu .icon-rankings + .submenu a {
  padding-left: 24px;
  background-repeat: no-repeat;
  background-position: 0 center;
}
header .menu .parent-menu .icon-rankings + .submenu a.nav-great {
  background-image: url("../img/themes/sunflower/nav-league-great.png");
}
header .menu .parent-menu .icon-rankings + .submenu a.nav-ultra {
  background-image: url("../img/themes/sunflower/nav-league-ultra.png");
}
header .menu .parent-menu .icon-rankings + .submenu a.nav-master {
  background-image: url("../img/themes/sunflower/nav-league-master.png");
}
header .hamburger .meat {
  width: 30px;
  height: 5px;
  margin: 3px 0;
  border-radius: 8px;
  background: #000;
}

.header-ticker {
  display: flex;
  justify-content: center;
  background: #4b8f56;
  padding: 3px;
  width: 100%;
  position: fixed;
  z-index: 25;
}
.header-ticker a {
  text-decoration: none;
  font-size: 13px;
  color: #fff;
  border: 1px solid #fff;
  padding: 3px 10px;
  border-radius: 4px;
  margin: 1px 0;
}
.header-ticker.old-version {
  background: #554778;
}

.banner-link {
  display: block;
  padding: 10px;
  border-radius: 8px;
  background: #554778;
  margin-bottom: 10px;
  color: #f9fdff;
  font-size: 14px;
}
.banner-link a {
  color: #f9fdff;
}

footer {
  max-width: 800px;
  position: relative;
  z-index: 10;
  text-align: center;
  font-size: 12px;
  margin: 0 auto;
}
footer p:first-of-type {
  margin-top: 0;
}
footer a.domz-link {
  color: #c7007d;
  font-weight: 900;
  background-image: url("../img/themes/sunflower/domz.png");
  background-repeat: no-repeat;
  background-size: contain;
  padding-left: 26px;
  text-decoration: none;
}

#main {
  max-width: 800px;
  margin: auto;
  padding: 60px 10px 10px 10px;
  border-radius: 12px;
}
#main > h1 {
  margin: 0;
}

#main {
  position: relative;
  z-index: 10;
}

.main-wrap {
  margin-bottom: 100px;
}

.section {
  margin: 10px 0;
  height: auto !important;
}
.section.white {
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 8px;
}
.section.error {
  display: none;
  text-align: center;
}
.section p:first-of-type {
  margin-top: 0;
}
.section .toggle-content p:first-of-type {
  margin: 1em 0;
}
.section p.description {
  margin: 1em 0;
}
.section p {
  font-size: 14px;
}
.section p.small {
  font-size: 12px;
}
.section h2 {
  font-size: 18px;
}
.section .continentals {
  margin-bottom: 15px;
}
.section .continentals .check {
  margin-right: 10px;
}

.section.about {
  text-align: left;
}
.section.about p {
  margin: 1em 0;
}

.toggle-content {
  display: none;
}

.active + .toggle-content {
  display: block;
}

b {
  font-weight: 700;
}

/* HOME PAGE STYLING */
.home {
  margin-top: 0;
}
.home a.button {
  display: block;
  text-decoration: none;
  color: #f9fdff;
  border-radius: 50px;
  margin: 20px auto;
  padding: 10px 20px;
  box-sizing: border-box;
  max-width: 400px;
  background: #003462; /* Old browsers */
  background: -moz-linear-gradient(top, #285e85 30%, #003462 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #285e85 30%, #003462 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #285e85 30%, #003462 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.home a.button .btn-icon {
  width: 30px;
  height: 30px;
  margin-right: 20px;
}
.home a.button .btn-icon.btn-icon-battle {
  background-image: url("../img/themes/sunflower/nav-battle-white.png");
}
.home a.button .btn-icon.btn-icon-rankings {
  background-image: url("../img/themes/sunflower/nav-rankings-white.png");
}
.home a.button .btn-icon.btn-icon-team {
  background-image: url("../img/themes/sunflower/nav-team-white.png");
}
.home a.button .btn-icon.btn-icon-train {
  background-image: url("../img/themes/sunflower/nav-train-white.png");
}
.home a.button .btn-icon.btn-icon-heart {
  background-image: url("../img/themes/sunflower/nav-heart-white.png");
}
.home a.button .btn-icon.btn-icon-tera {
  background-image: url("../img/themes/sunflower/nav-tera-white.png");
}
.home a.button h2 {
  margin: 0;
  font-size: 24px;
  color: #f9fdff;
}
.home a.button h2.no-logo {
  padding-left: 0;
}
.home a.button p {
  margin: 0;
  font-size: 14px;
}
.home a.button.tera-button {
  background-image: url("../img/tera-button-bg.jpg") !important;
  background-size: cover !important;
  background-repeat: no-repeat;
  color: #f9fdff;
}
.home a.button.tera-button h2 {
  font-size: 24px;
}
.home a.button.discord {
  background: #5567e3 !important;
  color: #fff;
}
.home a.button.discord h2 {
  color: #fff;
}
.home a.button.github {
  background: #fff !important;
  color: #1f2328;
}
.home a.button.github h2 {
  color: #1f2328;
}
.home a.button.twitch {
  background: #9147ff !important;
  color: #fff;
}
.home a.button.twitch h2 {
  color: #fff;
}
.home a.button.patreon {
  background: #f96854 !important;
  color: #fff;
}
.home a.button.patreon h2 {
  color: #fff;
}
.home h3 {
  margin-bottom: 10px;
}
.home .flex.new-header {
  margin-top: 45px;
  justify-content: space-between;
  align-items: flex-end;
}
.home .flex.new-header a {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 20px;
  background-image: url("../img/themes/sunflower/rss-blue.png");
  background-repeat: no-repeat;
}

.icon-battle {
  background-image: url("../img/themes/sunflower/nav-battle-blue.png");
}

.icon-train {
  background-image: url("../img/themes/sunflower/nav-train-blue.png");
}

.icon-rankings {
  background-image: url("../img/themes/sunflower/nav-rankings-blue.png");
}

.icon-team {
  background-image: url("../img/themes/sunflower/nav-team-blue.png");
}

.icon-heart {
  background-image: url("../img/themes/sunflower/nav-heart-blue.png");
}

.icon-articles {
  background-image: url("../img/icon_articles.png");
}

.icon-moves {
  background-image: url("../img/icon_moves.png");
}

.icon-settings {
  background-image: url("../img/icon_settings.png");
}

.icon-twitter {
  background-image: url("../img/icon_twitter.png");
  min-height: 18px;
}
.icon-twitter span {
  display: none;
}

.icon-unite {
  background-image: url("../img/favicon-unite.png");
  min-height: 18px;
}
.icon-unite span {
  display: none;
}

.icon-tera {
  background-image: url("../img/favicon_tera.png");
  min-height: 18px;
}
.icon-tera span {
  display: none;
}

/* POKEMON SELECT */
.poke-select-container {
  display: flex;
  justify-content: space-between;
}

.poke {
  position: relative;
  max-width: 200px;
}
.poke:nth-of-type(2) a.swap {
  display: none;
}
.poke input, .poke select {
  display: block;
  margin: 10px 0;
  max-width: 200px;
  background: #f9fdff;
  border: 1px solid #111;
}
.poke input.poke-select, .poke select.poke-select {
  font-size: 18px;
  font-weight: bold;
  border: 2px solid #003462;
}
.poke input.poke-select option:checked, .poke select.poke-select option:checked {
  font-weight: bold;
  background: #ddd;
  color: #000;
}
.poke input.charged, .poke select.charged {
  font-weight: bold;
}
.poke input.poke-search, .poke select.poke-search {
  background: #c4def5;
  border: 1px solid #285e85;
  border-radius: 30px;
  padding: 3px 13px;
}
.poke input.poke-search::placeholder, .poke select.poke-search::placeholder {
  color: rgba(0, 52, 98, 0.75);
}
.poke .form-select-container {
  visibility: hidden;
  position: relative;
}
.poke .form-select-container .form-select, .poke .form-select-container a.form-link {
  display: block;
  width: 75%;
  margin: 10px 0 10px 25%;
  font-size: 11px;
  padding: 2px;
  box-sizing: border-box;
}
.poke .form-select-container a.form-link {
  display: none;
  background: #f9fdff;
  border-radius: 4px;
  padding: 3px;
  border: 1px solid;
}
.poke .form-select-container .form-select-border {
  position: absolute;
  top: 0;
  left: 4%;
  width: 27px;
  height: 11px;
  border-left: 1px dashed;
  border-bottom: 1px dashed;
  opacity: 0.75;
  display: block;
  border-bottom-left-radius: 4px;
}
.poke .form-select-container .form-select-border:after {
  content: "▶";
  position: absolute;
  left: 19px;
  top: 4px;
  font-size: 11px;
  line-height: 14px;
}
.poke button.auto-select {
  font-size: 11px;
}
.poke .poke-search-container {
  width: 100%;
  align-items: center;
}
.poke .poke-search-container .poke-search {
  margin: 0;
}
.poke .poke-search-container a.search-info {
  line-height: 16px;
}
.poke .poke-stats {
  box-sizing: border-box;
  border-radius: 8px;
  padding: 5px;
  max-width: 200px;
  background: rgba(255, 255, 255, 0.9);
  display: none;
}
.poke .poke-stats .stat-container {
  font-size: 10px;
  color: #111;
}
.poke .poke-stats .stat-container .stat-label {
  width: 80px;
  float: left;
}
.poke .poke-stats .stat-container .stat-label .label-name {
  display: inline-block;
  width: 35px;
}
.poke .poke-stats .stat-container .stat-label .stat {
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
}
.poke .poke-stats .stat-container .stat-label .stat.buff {
  color: #f1841c;
}
.poke .poke-stats .stat-container .stat-label .stat.debuff {
  color: #0158d0;
}
.poke .poke-stats .stat-container.overall .stat-label {
  width: auto;
  margin-top: 5px;
  border-top: 1px solid #666;
  padding-top: 5px;
}
.poke .poke-stats .stat-container .bar-back {
  width: 100px;
  height: 4px;
  border-radius: 8px;
  float: left;
}
.poke .poke-stats .stat-container .bar-back .bar {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  margin-top: 7px;
  background: rgba(0, 0, 0, 0.6);
  -webkit-transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
  -moz-transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
  -o-transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
  transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
}
.poke .poke-stats .damage-adjustments {
  display: flex;
  border: 1px solid #888;
  border-radius: 8px;
  font-size: 10px;
  justify-content: center;
  text-align: center;
  margin: 5px 0 10px 0;
}
.poke .poke-stats .damage-adjustments .adjustment {
  padding: 5px;
  width: 50%;
}
.poke .poke-stats .damage-adjustments .adjustment .value {
  font-size: 14px;
  font-weight: bold;
}
.poke .poke-stats .damage-adjustments .adjustment .value.buff {
  color: #f1841c;
}
.poke .poke-stats .damage-adjustments .adjustment .value.debuff {
  color: #0158d0;
}
.poke .poke-stats .damage-adjustments .label {
  font-size: 10px;
}
.poke .poke-stats .types {
  margin-bottom: 3px;
  text-align: center;
}
.poke .poke-stats .types .type {
  display: inline-block;
  border-radius: 4px;
  padding: 2px 8px;
  margin: 0 2px;
  font-size: 12px;
}
.poke .poke-stats h3 {
  margin: 0;
  font-weight: normal;
}
.poke .poke-stats h3.cp {
  text-align: center;
}
.poke .poke-stats h3.cp .identifier {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 20px;
  background: #9e34ef;
}
.poke .poke-stats h3.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #111;
  margin-top: 20px;
}
.poke .poke-stats a {
  font-size: 14px;
  text-decoration: none;
  color: #003462;
  display: block;
  margin: 5px 0;
}
.poke .poke-stats a.clear-selection {
  margin-top: 10px;
}
.poke .poke-stats .mega-cp-container {
  border: 1px solid #888;
  border-radius: 8px;
  padding: 5px;
  margin: 10px 0;
  text-align: center;
  display: none;
}
.poke .poke-stats .mega-cp-container h3.section-title {
  font-size: 12px;
  margin-top: 0;
}
.poke .poke-stats .mega-cp-container .mega-cp {
  font-size: 14px;
}
.poke .poke-stats .advanced-section {
  margin-top: 15px;
}
.poke .poke-stats .advanced-section .fields {
  display: none;
}
.poke .poke-stats .advanced-section .fields .ivs input {
  display: block;
  width: 27%;
  max-width: 66px;
  background: none;
  border: none;
  border-bottom: 1px solid #888;
  border-radius: 0px;
  height: 22px;
  margin-bottom: 0;
  -moz-appearance: textfield; /* Firefox */
}
.poke .poke-stats .advanced-section .fields .ivs input::-webkit-outer-spin-button, .poke .poke-stats .advanced-section .fields .ivs input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}
.poke .poke-stats .advanced-section .fields .ivs input:focus {
  outline: none;
  border-bottom: 3px solid #ffcf01;
}
.poke .poke-stats .advanced-section .fields .ivs input + span {
  margin-top: 10px;
}
.poke .poke-stats .advanced-section .fields input.level,
.poke .poke-stats .advanced-section .fields label.level {
  width: 25%;
}
.poke .poke-stats .advanced-section .fields label.iv {
  width: 27%;
  max-width: 66px;
}
.poke .poke-stats .advanced-section .fields label {
  margin-top: 3px;
  font-size: 12px;
  font-weight: bold;
  display: block;
}
.poke .poke-stats .advanced-section .fields .ivs-group {
  width: 75%;
  display: flex;
  justify-content: end;
  align-items: center;
}
.poke .poke-stats .advanced-section .fields .ivs-group.labels {
  align-items: start;
}
.poke .poke-stats .advanced-section .iv-rank {
  border: 1px solid #111;
  cursor: pointer;
}
.poke .poke-stats .advanced-section .iv-rank .info-icon {
  color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.95);
  width: 14px;
  height: 14px;
  border-radius: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  float: right;
}
.poke .poke-stats .advanced-section .maximize-section {
  border: 1px solid #888;
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 15px;
  clear: both;
}
.poke .poke-stats .advanced-section .maximize-section .check {
  display: inline-block;
  font-size: 12px;
  margin-right: 3px;
  margin-bottom: 5px;
  margin-top: 0;
}
.poke .poke-stats .advanced-section .maximize-section .check span {
  width: 12px;
  height: 12px;
}
.poke .poke-stats .advanced-section .maximize-section .check.auto-level {
  margin-top: 10px;
  text-align: left;
  display: block;
}
.poke .poke-stats .advanced-section .maximize-section .check-group {
  text-align: left;
}
.poke .poke-stats .advanced-section .maximize-section .level-cap-group {
  display: flex;
  margin-top: 10px;
}
.poke .poke-stats .advanced-section .maximize-section .level-cap-group div:first-of-type {
  font-size: 12px;
  font-weight: bold;
  margin-right: 10px;
}
.poke .poke-stats .advanced-section .maximize-section .level-cap-group .check {
  margin-right: 10px;
}
.poke .poke-stats .advanced-section .maximize-section .maximize-stats {
  font-size: 12px;
  margin-top: 5px;
}
.poke .poke-stats .advanced-section .maximize-section .restore-default {
  font-size: 12px;
}
.poke .poke-stats .advanced-section.active .fields {
  display: block;
}
.poke .poke-stats .move-select.fast {
  margin-top: 5px;
}
.poke .fifty-warning {
  display: none;
  font-weight: bold;
  font-size: 12px;
  padding: 4px;
  border-radius: 8px;
  border: 1px solid #000;
  margin: 6px 0;
}
.poke .legacy {
  font-size: 12px;
  margin-top: 10px;
}
.poke .options {
  margin: 20px 0;
}
.poke .options .start-hp,
.poke .options .start-energy {
  margin: 5px 0;
  max-width: 120px;
  max-height: 23px;
}
.poke .options .flex {
  justify-content: space-between;
}
.poke .options .shield-baiting,
.poke .options .show-ivs,
.poke .options .optimize-timing,
.poke .options .switch-delay,
.poke .options .priority,
.poke .options .negate-fast-moves {
  text-align: left;
  font-size: 14px;
}
.poke .options .show-ivs {
  margin-bottom: 15px;
}
.poke .options .add-fast-move {
  border: 1px solid #000;
  outline: none;
  border-radius: 8px;
  cursor: pointer;
  margin: 0px 0px 10px 40px;
  float: right;
}
.poke .options .add-fast-move.dark {
  color: #fff;
}
.poke .options .label {
  font-size: 14px;
  margin: 5px 5px 5px 0;
}
.poke .options.multi-battle-options {
  margin-top: 0;
}
.poke .options.multi-battle-options h3:first-of-type {
  margin-top: 0;
}
.poke .options .stat-modifiers input {
  float: left;
  width: 50%;
}
.poke .custom-ranking-options {
  display: none;
}
.poke .form-group {
  border: 1px solid #888;
  border-radius: 30px;
  margin-bottom: 15px;
  clear: both;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  overflow: hidden;
}
.poke .form-group .option {
  text-align: left;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  flex-grow: 1;
  margin-top: 0;
  border-left: 1px solid #999;
  border-right: 1px solid #999;
  background: #eee;
  cursor: pointer;
}
.poke .form-group .option.on {
  background: #ddd;
  -moz-box-shadow: inset 0 0 10px #999;
  -webkit-box-shadow: inset 0 0 10px #999;
  box-shadow: inset 0 0 10px #999;
}
.poke .form-group .option.on div.icon {
  opacity: 1;
}
.poke .form-group .option:first-of-type {
  border-left: none;
}
.poke .form-group .option:last-of-type {
  border-right: none;
}
.poke .form-group.shield-picker .option {
  font-size: 14px;
}
.poke .form-group.shield-picker div.icon {
  width: 20px;
  height: 15px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-right: 5px;
  opacity: 0.25;
}
.poke .form-group.shield-picker .option:nth-of-type(1) div.icon {
  background-image: url("../img/shield_none.png");
}
.poke .form-group.shield-picker .option:nth-of-type(2) div.icon {
  background-image: url("../img/shield.png?v=2");
}
.poke .form-group.shield-picker .option:nth-of-type(3) div.icon {
  background-image: url("../img/shield_two.png?v=2");
  width: 25px;
  height: 20px;
}
.poke a.random,
.poke a.swap {
  font-size: 10px;
  text-decoration: none;
  color: #003748;
  padding: 3px 3px 3px 16px;
  background: rgba(255, 255, 255, 0.75);
  display: inline-block;
  background-image: url("../img/random.png");
  background-repeat: no-repeat;
  background-position: 3px center;
  border-radius: 8px;
  margin-bottom: 10px;
}
.poke a.swap {
  padding-left: 18px;
  background-image: url("../img/swap.png");
}
.poke .move-details {
  font-size: 12px;
  min-height: 15px;
  text-align: left;
}
.poke .move-details .move-stat {
  color: #111;
}
.poke .move-details .move-stat .stat {
  width: 15px;
  height: 14px;
  background: #999;
  display: inline-block;
  border-radius: 15px;
  text-align: center;
  padding-top: 1px;
  color: #f9fdff;
  font-weight: bold;
  background: #111;
}
.poke.multi {
  display: none;
  float: right;
}
.poke.multi .poke-stats {
  display: block;
}
.poke.multi .poke-count.error {
  color: #e66a19;
}
.poke.multi .format-select, .poke.multi .cup-select {
  margin-top: 62px;
}
.poke.multi .cup-select {
  width: 100%;
}
.poke.multi .cup-select option[value=custom] {
  display: block;
}
.poke.multi .charged-count-select {
  display: none !important;
}
.poke.multi .custom-options {
  display: none;
}
.poke.multi .custom-options a.custom-group-sort {
  font-weight: bold;
  font-size: 12px;
  text-decoration: underline;
  visibility: hidden;
  padding-right: 20px;
}
.poke.multi .custom-options .check.show-move-counts {
  visibility: hidden;
  margin-top: 5px;
  font-size: 12px;
}
.poke.multi .custom-options .check.show-move-counts span {
  width: 12px;
  height: 12px;
}
.poke.multi .custom-options .add-poke-btn {
  font-size: 14px;
  width: 100%;
  margin-bottom: 0;
}
.poke.multi .custom-options .export-btn,
.poke.multi .custom-options .search-string-btn {
  margin-top: 15px;
}
.poke.multi .custom-options .rankings-container {
  margin-top: 5px;
}
.poke.multi .custom-options p {
  font-size: 12px;
}
.poke.multi .search-string-btn {
  margin-top: 5px;
  margin-bottom: 15px;
}
.poke.multi .rankings-container {
  max-height: 410px;
}
.poke.multi .rankings-container .rank {
  padding: 0;
  position: relative;
  border-left: none;
  display: flex;
}
.poke.multi .rankings-container .rank:after {
  border-radius: 8px;
}
.poke.multi .rankings-container .rank.warning {
  border: 3px solid #e66a19;
}
.poke.multi .rankings-container .rank .name-container {
  float: none;
  flex: 1;
  padding: 5px;
  font-size: 15px;
}
.poke.multi .rankings-container .rank .name-container .number {
  padding-right: 5px;
  font-size: 12px;
  font-weight: normal;
  opacity: 0.5;
}
.poke.multi .rankings-container .rank span.cliffhanger-points {
  font-weight: normal;
  border: 1px solid;
  border-radius: 20px;
  width: 20px;
  display: inline-block;
  text-align: center;
  margin-right: 5px;
  padding-top: 2px;
}
.poke.multi .rankings-container .rank .name {
  display: block;
}
.poke.multi .rankings-container .rank .moves {
  margin-left: 0;
}
.poke.multi .rankings-container .rank .remove {
  font-weight: bold;
  background: rgba(0, 0, 0, 0.5);
  color: #f9fdff;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 14px;
  height: 20px;
}
.poke.multi .rankings-container .rank .remove:after {
  content: "x";
}
.poke.multi .rankings-container .rank .remove:hover {
  background: #ff0000;
}
.poke.multi .quick-fill-buttons {
  justify-content: space-around;
}
.poke.multi .team-warning {
  display: none;
  font-size: 12px;
  font-weight: bold;
  padding: 4px;
  border-radius: 8px;
  border: 3px solid #e66a19;
  color: #e66a19;
  margin-bottom: 5px;
}
.poke .energy-label {
  margin-top: 15px;
  text-align: center;
  font-size: 12px;
  line-height: 14px;
}
.poke .energy-label .num {
  font-size: 16px;
  font-weight: bold;
}
.poke .hp.bar-back .bar.damage {
  display: none;
  -webkit-animation: BLINK_ANIMATION 2s infinite;
  -moz-animation: BLINK_ANIMATION 2s infinite;
  -o-animation: BLINK_ANIMATION 2s infinite;
  animation: BLINK_ANIMATION 2s infinite;
}

.hp.bar-back {
  position: relative;
  border: 3px solid #666;
  padding: 2px;
  width: 100%;
  height: 30px;
  box-sizing: border-box;
  margin-top: 20px;
  background: #003462;
}
.hp.bar-back .bar {
  width: 100%;
  height: 100%;
  background-color: #0eb084;
}
.hp.bar-back .bar[color=yellow] {
  background-color: #eabd49;
}
.hp.bar-back .bar[color=red] {
  background-color: #aa0505;
}
.hp.bar-back .bar:nth-child(1) {
  -webkit-transition: width 0.2s ease-out 0s;
  -moz-transition: width 0.2s ease-out 0s;
  -o-transition: width 0.2s ease-out 0s;
  transition: width 0.2s ease-out 0s;
}
.hp.bar-back .bar.damage {
  background: #ff5115;
  position: relative;
  max-width: 100%;
  top: -20px;
}
.hp.bar-back .stat {
  position: absolute;
  width: 100%;
  left: 0;
  top: 4px;
  text-align: center;
  color: #fff;
  font-weight: bold;
}

.move-bars {
  display: flex;
  justify-content: space-around;
}
.move-bars .move-bar {
  width: 40px;
  height: 40px;
  border-radius: 40px;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(0, 0, 0, 0);
  box-sizing: border-box;
  color: #f9fdff;
  margin-top: 5px;
  display: none;
  cursor: pointer;
  -webkit-transition: border 0.2s ease-out 0s;
  -moz-transition: border 0.2s ease-out 0s;
  -o-transition: border 0.2s ease-out 0s;
  transition: border 0.2s ease-out 0s;
}
.move-bars .move-bar.active {
  border: 2px solid rgb(0, 0, 0);
}
.move-bars .move-bar .label {
  position: relative;
  z-index: 10;
  text-align: center;
  padding-top: 10px;
  font-weight: bold;
  pointer-events: none;
}
.move-bars .move-bar .bar, .move-bars .move-bar .bar-back {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.move-bars .move-bar .bar {
  -webkit-transition: height 0.2s ease-out 0s;
  -moz-transition: height 0.2s ease-out 0s;
  -o-transition: height 0.2s ease-out 0s;
  transition: height 0.2s ease-out 0s;
  bottom: -5%;
}
.move-bars .move-bar .bar:nth-child(3), .move-bars .move-bar .bar:nth-child(4) {
  border-top: 2px solid #fff;
  background: rgba(0, 0, 0, 0.35) !important;
}
.move-bars .move-bar .bar-back {
  opacity: 0.3;
}

.poke-select-container.single .poke:nth-of-type(1) a.random {
  float: left;
}
.poke-select-container.single .poke:nth-of-type(1) a.swap {
  float: right;
}
.poke-select-container.single .poke:nth-of-type(2) {
  display: block;
}
.poke-select-container.single .poke:nth-of-type(2) a.random {
  float: right;
}
.poke-select-container.single .poke:nth-of-type(2) a.swap {
  float: left;
}
.poke-select-container.single .poke:nth-of-type(3) {
  display: none;
}
.poke-select-container.multi .poke:nth-of-type(2),
.poke-select-container.multi .hp.bar-back,
.poke-select-container.multi .move-bars {
  display: none;
}
.poke-select-container.multi .poke:nth-of-type(3) {
  display: block;
}
.poke-select-container.multi a.swap {
  display: none;
}
.poke-select-container.matrix .poke.single,
.poke-select-container.matrix .hp.bar-back,
.poke-select-container.matrix .move-bars {
  display: none;
}
.poke-select-container.matrix .poke.multi {
  display: block;
  float: left;
}
.poke-select-container.matrix .poke.multi .format-select, .poke-select-container.matrix .poke.multi .cup-select {
  display: none;
}
.poke-select-container.matrix .poke.multi .custom-options {
  display: block;
}
.poke-select-container.matrix .poke.multi .multi-battle-options {
  display: none;
}
.poke-select-container.matrix .delete-list-confirm + .poke.multi {
  float: right;
}
.poke-select-container.train .swap {
  display: none;
}

.arrow-up {
  display: none;
}

.active .arrow-up {
  display: inline;
}
.active .arrow-down {
  display: none;
}

.iv-rank {
  color: rgba(255, 255, 255, 0.95);
  font-size: 13px;
  font-weight: bold;
  margin: 10px 0;
  background: rgba(0, 0, 0, 0.25);
  padding: 6px;
  border-radius: 8px;
}
.iv-rank .count {
  font-weight: normal;
  font-size: 12px;
}

.modal .iv-rank-details h3 {
  border-bottom: 1px solid #111;
  margin-bottom: 6px;
  margin-top: 0;
  padding-bottom: 3px;
}
.modal .iv-rank-details .stats-table {
  width: 100%;
}
.modal .iv-rank-details .stats-table thead {
  font-weight: bold;
}
.modal .iv-rank-details .iv-rank {
  margin: 2px 0;
}
.modal .iv-rank-details .iv-rank-result {
  margin-top: 30px;
}
.modal .iv-rank-details .iv-rank-result.primary {
  border: 1px solid #111;
  padding: 6px;
  border-radius: 8px;
  margin-top: 0;
}
.modal .iv-rank-details .iv-rank-result td.league-1500,
.modal .iv-rank-details .iv-rank-result td.league-2500 {
  padding-left: 28px;
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: 4px center;
}
.modal .iv-rank-details .iv-rank-result td.league-1500.league-1500,
.modal .iv-rank-details .iv-rank-result td.league-2500.league-1500 {
  background-image: url("../img/themes/sunflower/nav-league-great.png");
}
.modal .iv-rank-details .iv-rank-result td.league-1500.league-2500,
.modal .iv-rank-details .iv-rank-result td.league-2500.league-2500 {
  background-image: url("../img/themes/sunflower/nav-league-ultra.png");
}

/* GENERAL FORM STYLING */
select, input {
  border-radius: 30px;
  padding: 3px 8px;
  box-sizing: border-box;
  width: 100%;
  color: #111;
  border: 2px solid #285e85;
}

.league-select,
.cup-select,
.format-select,
.slot-select,
.mode-select,
.category-select {
  font-size: 18px;
  width: auto;
  background: none;
  font-weight: bold;
  max-width: 250px;
}

.button {
  position: relative;
  display: block;
  font-size: 18px;
  margin: 10px auto;
  border-radius: 30px;
  border: 2px solid #000;
  padding: 10px 18px;
  cursor: pointer;
  text-decoration: none;
  color: #000;
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.button.update-btn {
  display: none;
}
.button span.btn-content-wrap {
  display: flex;
  align-items: center;
}
.button span.btn-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: 100%;
  margin-right: 8px;
}
.button span.btn-icon.btn-icon-battle {
  background-image: url("../img/themes/sunflower/nav-battle-blue.png");
}
.button span.btn-icon.btn-icon-team {
  background-image: url("../img/themes/sunflower/nav-team-blue.png");
}
.button span.btn-icon.btn-icon-train {
  background-image: url("../img/themes/sunflower/nav-train-blue.png");
}
.button span.btn-label {
  flex: 1;
}

.button, .button-highlight, select, .advanced-section .iv-rank {
  position: relative;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
  -webkit-transition: box-shadow 0.25s ease-out 0s;
  -moz-transition: box-shadow 0.25s ease-out 0s;
  -o-transition: box-shadow 0.25s ease-out 0s;
  transition: box-shadow 0.25s ease-out 0s;
}
.button:after, .button-highlight:after, select:after, .advanced-section .iv-rank:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  pointer-events: none;
  border-radius: 50px;
  -webkit-transition: background-color 0.25s ease-out 0s;
  -moz-transition: background-color 0.25s ease-out 0s;
  -o-transition: background-color 0.25s ease-out 0s;
  transition: background-color 0.25s ease-out 0s;
}
.button:hover, .button-highlight:hover, select:hover, .advanced-section .iv-rank:hover {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
  -webkit-transition: box-shadow 0s ease-out 0s;
  -moz-transition: box-shadow 0s ease-out 0s;
  -o-transition: box-shadow 0s ease-out 0s;
  transition: box-shadow 0s ease-out 0s;
}
.button:hover:after, .button-highlight:hover:after, select:hover:after, .advanced-section .iv-rank:hover:after {
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-transition: background-color 0s ease-out 0s;
  -moz-transition: background-color 0s ease-out 0s;
  -o-transition: background-color 0s ease-out 0s;
  transition: background-color 0s ease-out 0s;
}

.pokebox a.open-pokebox {
  display: block;
  border: 1px solid #000;
  padding: 10px;
  cursor: pointer;
  text-decoration: none;
  color: #000;
  margin: 10px auto;
  border-radius: 30px;
  background: url("../img/pokebattler_logo.png") #f9fdff;
  background-size: 24px;
  background-position: 5px center;
  background-repeat: no-repeat;
  padding: 8px 5px 8px 25px;
  text-align: center;
  font-size: 12px;
}
.pokebox a.open-pokebox span:first-of-type {
  margin-right: 3px;
}

.share-link-container {
  text-align: center;
  margin-top: 20px;
}
.share-link-container p {
  margin: 0;
}

.share-link,
.rank .details .share-link.detail-section {
  display: inline-block;
  background: #6296be;
  background-image: url("../img/link.png");
  background-repeat: no-repeat;
  background-position: 6px center;
  background-size: 20px;
  padding: 5px 5px 5px 35px;
  border-radius: 8px;
  margin: 5px auto;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
}
.share-link input,
.rank .details .share-link.detail-section input {
  width: auto;
  display: inline-block;
  border: none;
  background: #003462;
  padding: 5px;
}
.share-link .copy,
.rank .details .share-link.detail-section .copy {
  display: inline-block;
  font-weight: bold;
  font-size: 13px;
  cursor: pointer;
  color: #f9fdff;
  margin: 5px;
  border-radius: 8px;
}

.continue-container {
  margin: 15px auto;
}
.continue-container p {
  margin: 0;
}
.continue-container .name {
  font-weight: bold;
}
.continue-container .button {
  display: inline-block;
  margin: 5px auto;
}

.check {
  margin-top: 10px;
  cursor: pointer;
}
.check span {
  display: block;
  width: 17px;
  height: 17px;
  border-radius: 8px;
  background: #111;
  border: 2px solid #111;
  box-sizing: border-box;
  margin: 0px 5px 0 0;
  float: left;
}
.check.on span {
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.check:hover:not(.on) span {
  background: #555;
}

/* BATTLE TIMELINE */
.battle-results {
  display: none;
  text-align: center;
}
.battle-results .timeline-container {
  max-width: 95%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.5);
  height: 80px;
  border-radius: 8px;
  border: 1px solid #000;
  position: relative;
  overflow: hidden;
}
.battle-results .timeline-container .tracker {
  position: absolute;
  width: 1px;
  height: 100%;
  border-left: 1px solid #000;
  top: 0;
}
.battle-results .timeline-container.sandbox-mode {
  background-color: rgba(255, 240, 200, 0.75);
  border: 1px solid #ffcf01;
}
.battle-results .timeline-container.sandbox-mode .item.charged:hover,
.battle-results .timeline-container.sandbox-mode .item.interaction:hover,
.battle-results .timeline-container.sandbox-mode .item.fast:not(.disabled):hover {
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  border-color: #f9a01b;
}
.battle-results .timeline-container.sandbox-mode .item.disabled {
  opacity: 0.5;
}
.battle-results .timeline-container.sandbox-mode .item.disabled:hover {
  cursor: initial;
}
.battle-results .timeline-container.sandbox-mode .item.interaction {
  display: block !important;
  border: 2px solid #000;
  cursor: pointer;
}
.battle-results .timeline-container.sandbox-mode .item.interaction.disabled {
  cursor: pointer;
  border: 2px solid #666;
}
.battle-results .timeline-container.sandbox-mode .item.interaction.both {
  border: 3px solid #000;
}
.battle-results .timeline-container.sandbox-mode .item.interaction.wait {
  border-style: dotted;
}
.battle-results .timeline-container.zoom {
  height: 90px;
  overflow-x: scroll;
}
.battle-results .sandbox-btn-container {
  max-width: 95%;
  margin: 0 auto;
}
.battle-results .sandbox-btn-container .sandbox-btn {
  -webkit-transition: background 0.25s 0s ease-out;
  -moz-transition: background 0.25s 0s ease-out;
  -o-transition: background 0.25s 0s ease-out;
  transition: background 0.25s 0s ease-out;
  position: relative;
  background: #888;
  color: #f9fdff;
  border-radius: 12px;
  font-size: 17px;
  line-height: 16px;
  text-decoration: none;
  cursor: pointer;
  padding: 1px 8px;
  margin-bottom: 5px;
  float: right;
  overflow: hidden;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently
supported by Chrome and Opera */
}
.battle-results .sandbox-btn-container .sandbox-btn span {
  position: relative;
  z-index: 5;
}
.battle-results .sandbox-btn-container .sandbox-btn.active {
  color: #111;
  background: #ffcf01;
}
.battle-results .sandbox-btn-container .sandbox-btn.active .btn-background {
  left: 0;
}
.battle-results .sandbox-btn-container .sandbox {
  width: 19px;
  height: 19px;
  border-radius: 12px;
  background: #ffcf01;
  float: right;
  margin: 0 10px;
  cursor: pointer;
  -moz-transform: scale(0, 0);
  -webkit-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -ms-transform: scale(0, 0);
  transform: scale(0, 0);
  -webkit-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  -moz-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  -o-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
}
.battle-results .sandbox-btn-container .sandbox.active {
  -moz-transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
}
.battle-results .sandbox-btn-container .clear-btn:after {
  content: "↻";
}
.battle-results .playback {
  max-width: 300px;
  margin: 10px auto;
  padding: 2px 10px;
}
.battle-results .playback .flex {
  display: flex;
  justify-content: space-between;
}
.battle-results .playback .playback-btn {
  width: 16px;
  height: 19px;
  background-position: center center;
  background-repeat: no-repeat;
}
.battle-results .playback .playback-btn.play {
  background-image: url("../img/playback_play.png");
}
.battle-results .playback .playback-btn.play.active {
  background-image: url("../img/playback_pause.png");
}
.battle-results .playback .playback-btn.replay {
  background-image: url("../img/playback_replay.png");
}
.battle-results .playback .playback-speed,
.battle-results .playback .playback-scale {
  padding: 0;
  background: none;
  border-radius: 8px;
  width: 85px;
}
.battle-results .playback .disclaimer {
  display: none;
  font-size: 12px;
  margin-top: 10px;
}
.battle-results .bulk-btn {
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
  color: #f9fdff;
  background: url("../img/histogram-icon.png") no-repeat 8px center, rgb(62, 94, 172) !important;
  padding-left: 30px;
}
.battle-results .summary {
  display: inline-block;
  margin: 15px auto;
  font-size: 18px;
  line-height: 26px;
}
.battle-results .summary .battle-summary-line {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.battle-results .summary .battle-summary-line span {
  margin-right: 6px;
}
.battle-results .summary .battle-summary-line span.time {
  margin-left: 6px;
}
.battle-results .summary .battle-summary-line span.rating {
  margin: 0 0 0 6px;
  padding: 2px 8px;
  border-radius: 30px;
}
.battle-results .summary .battle-summary-line span.rating span {
  padding: 0;
}
.battle-results .summary span.time,
.battle-results .summary span.name {
  padding: 0;
}
.battle-results .summary .disclaimer {
  margin: 15px 0;
  font-size: 14px;
  text-align: left;
}
.battle-results .summary p {
  font-size: 14px;
}
.battle-results .summary .turn-margin-description {
  font-size: 12px;
  margin-top: 15px;
  line-height: 18px;
}
.battle-results .summary .turn-margin-description span.turn-margin {
  color: #000;
}
.battle-results .summary .turn-margin-description span.turn-margin[value=extreme] {
  background: #ff4d4d;
}
.battle-results .summary .turn-margin-description span.turn-margin[value=low] {
  background: #fff943;
}
.battle-results .summary .turn-margin-description span.turn-margin[value=medium] {
  background: #94ff43;
}
.battle-results .summary .turn-margin-description span.turn-margin[value=high] {
  background: #43ff85;
}
.battle-results .summary .bulk-outcomes {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.battle-results .summary .bulk-outcomes .outcome {
  padding: 0 2px;
}
.battle-results .summary .bulk-outcomes a.rating span {
  padding: 0;
  margin: 0 6px 0 0;
}
.battle-results .summary .bulk-outcomes .outcome-label {
  font-size: 14px;
  font-weight: bold;
  line-height: 16px;
  min-height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.battle-results .summary .histogram span {
  padding: 0;
}
.battle-results .tip {
  margin: 15px;
  text-align: center;
  font-size: 12px;
}
.battle-results .battle-details, .battle-results.matrix {
  text-align: left;
}
.battle-results .battle-details table, .battle-results.matrix table {
  margin: 10px auto 0 auto;
  text-align: center;
}
.battle-results .battle-details table .name, .battle-results.matrix table .name {
  font-size: 14px;
  font-weight: bold;
}
.battle-results .battle-details table th span, .battle-results.matrix table th span {
  font-weight: normal;
  font-size: 11px;
  display: block;
}
.battle-results .battle-details table .rating.star, .battle-results.matrix table .rating.star {
  display: block;
  background-image: url("../img/starburst_red.png");
  text-decoration: none;
}
.battle-results .battle-details table .rating.star.win, .battle-results.matrix table .rating.star.win {
  background-image: url("../img/starburst.png");
}
.battle-results .battle-details table[mode=attack] .rating, .battle-results.matrix table[mode=attack] .rating {
  font-size: 14px;
  line-height: 18px;
  padding: 5px 10px 5px 31px;
}
.battle-results .battle-details .breakpoints-section,
.battle-results .battle-details .matchup-detail-section,
.battle-results .battle-details .cmp-section,
.battle-results .battle-details .optimal-timing-section, .battle-results.matrix .breakpoints-section,
.battle-results.matrix .matchup-detail-section,
.battle-results.matrix .cmp-section,
.battle-results.matrix .optimal-timing-section {
  max-width: 340px;
  margin: 0 auto 35px auto;
}
.battle-results .battle-details .breakpoints-section .bold,
.battle-results .battle-details .matchup-detail-section .bold,
.battle-results .battle-details .cmp-section .bold,
.battle-results .battle-details .optimal-timing-section .bold, .battle-results.matrix .breakpoints-section .bold,
.battle-results.matrix .matchup-detail-section .bold,
.battle-results.matrix .cmp-section .bold,
.battle-results.matrix .optimal-timing-section .bold {
  font-weight: bold;
}
.battle-results .battle-details .breakpoints-section .ivs .button,
.battle-results .battle-details .matchup-detail-section .ivs .button,
.battle-results .battle-details .cmp-section .ivs .button,
.battle-results .battle-details .optimal-timing-section .ivs .button, .battle-results.matrix .breakpoints-section .ivs .button,
.battle-results.matrix .matchup-detail-section .ivs .button,
.battle-results.matrix .cmp-section .ivs .button,
.battle-results.matrix .optimal-timing-section .ivs .button {
  font-size: 14px;
  margin: 0;
  padding: 5px;
  border-radius: 8px;
  border: none;
  background: #111 !important;
  color: #f9fdff;
}
.battle-results .battle-details .breakpoints-section .golden-combination .button,
.battle-results .battle-details .matchup-detail-section .golden-combination .button,
.battle-results .battle-details .cmp-section .golden-combination .button,
.battle-results .battle-details .optimal-timing-section .golden-combination .button, .battle-results.matrix .breakpoints-section .golden-combination .button,
.battle-results.matrix .matchup-detail-section .golden-combination .button,
.battle-results.matrix .cmp-section .golden-combination .button,
.battle-results.matrix .optimal-timing-section .golden-combination .button {
  display: inline-block;
  font-size: 14px;
  padding: 5px 15px;
  margin: 0;
  font-weight: bold;
  border: none;
  background: #111 !important;
  color: #f9fdff;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline,
.battle-results .battle-details .cmp-section .optimal-timing-timeline,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline, .battle-results.matrix .breakpoints-section .optimal-timing-timeline,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline,
.battle-results.matrix .cmp-section .optimal-timing-timeline,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline {
  width: 100%;
  margin: 1em auto 0 auto;
  background: rgba(255, 255, 255, 0.5);
  height: 80px;
  border-radius: 8px;
  border: 1px solid #000;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline {
  display: flex;
  border: none;
  width: 100%;
  margin: 0px !important;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline:first-of-type,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline:first-of-type,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline:first-of-type,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline:first-of-type, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline:first-of-type,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline:first-of-type,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline:first-of-type,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline:first-of-type {
  margin-top: 15px !important;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline:last-of-type,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline:last-of-type,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline:last-of-type,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline:last-of-type, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline:last-of-type,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline:last-of-type,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline:last-of-type,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline:last-of-type {
  margin-bottom: 15px !important;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline .item,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline .item,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline .item,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline .item, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline .item,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline .item,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline .item,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline .item {
  display: flex;
  height: 20px;
  border: 1px solid #000;
  box-sizing: border-box;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline .item.fade,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline .item.fade,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline .item.fade,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline .item.fade, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline .item.fade,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline .item.fade,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline .item.fade,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline .item.fade {
  opacity: 0.4;
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline .item.fade .chunk,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline .item.fade .chunk,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline .item.fade .chunk,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline .item.fade .chunk, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline .item.fade .chunk,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline .item.fade .chunk,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline .item.fade .chunk,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline .item.fade .chunk {
  border: 1px dashed rgba(0, 0, 0, 0.25);
}
.battle-results .battle-details .breakpoints-section .optimal-timing-timeline .timeline .item .chunk,
.battle-results .battle-details .matchup-detail-section .optimal-timing-timeline .timeline .item .chunk,
.battle-results .battle-details .cmp-section .optimal-timing-timeline .timeline .item .chunk,
.battle-results .battle-details .optimal-timing-section .optimal-timing-timeline .timeline .item .chunk, .battle-results.matrix .breakpoints-section .optimal-timing-timeline .timeline .item .chunk,
.battle-results.matrix .matchup-detail-section .optimal-timing-timeline .timeline .item .chunk,
.battle-results.matrix .cmp-section .optimal-timing-timeline .timeline .item .chunk,
.battle-results.matrix .optimal-timing-section .optimal-timing-timeline .timeline .item .chunk {
  flex: 1;
  border: 1px dashed rgba(0, 0, 0, 0.125);
  box-sizing: border-box;
}
.battle-results .battle-details .breakpoints-section .optimal-1, .battle-results .battle-details .breakpoints-section .optimal-2, .battle-results .battle-details .breakpoints-section .optimal-3,
.battle-results .battle-details .matchup-detail-section .optimal-1,
.battle-results .battle-details .matchup-detail-section .optimal-2,
.battle-results .battle-details .matchup-detail-section .optimal-3,
.battle-results .battle-details .cmp-section .optimal-1,
.battle-results .battle-details .cmp-section .optimal-2,
.battle-results .battle-details .cmp-section .optimal-3,
.battle-results .battle-details .optimal-timing-section .optimal-1,
.battle-results .battle-details .optimal-timing-section .optimal-2,
.battle-results .battle-details .optimal-timing-section .optimal-3, .battle-results.matrix .breakpoints-section .optimal-1, .battle-results.matrix .breakpoints-section .optimal-2, .battle-results.matrix .breakpoints-section .optimal-3,
.battle-results.matrix .matchup-detail-section .optimal-1,
.battle-results.matrix .matchup-detail-section .optimal-2,
.battle-results.matrix .matchup-detail-section .optimal-3,
.battle-results.matrix .cmp-section .optimal-1,
.battle-results.matrix .cmp-section .optimal-2,
.battle-results.matrix .cmp-section .optimal-3,
.battle-results.matrix .optimal-timing-section .optimal-1,
.battle-results.matrix .optimal-timing-section .optimal-2,
.battle-results.matrix .optimal-timing-section .optimal-3 {
  font-weight: bold;
}
.battle-results .battle-details select.breakpoint-move,
.battle-results .battle-details select.bulkpoint-move, .battle-results.matrix select.breakpoint-move,
.battle-results.matrix select.bulkpoint-move {
  margin-top: 15px;
  font-size: 16px;
  font-weight: bold;
}
.battle-results .battle-details .ranking-categories, .battle-results.matrix .ranking-categories {
  float: none;
  margin-top: 10px;
}
.battle-results .battle-details p, .battle-results.matrix p {
  font-size: 14px;
  margin-bottom: 0 !important;
}
.battle-results .battle-details .rating {
  border-radius: 30px;
}
.battle-results.multi {
  text-align: left;
}
.battle-results.multi .poke-search {
  margin-top: 15px;
}
.battle-results.multi .button {
  display: inline-block;
}
.battle-results.multi .rankings-container .rank:hover {
  border-left: none;
  cursor: initial;
}
.battle-results.multi .multi-battle-sort {
  display: block;
  margin-top: 15px;
  margin-left: 20px;
}
.battle-results.matrix .table-container {
  overflow: scroll;
  max-height: 500px;
}
.battle-results.matrix .rating-table {
  margin-top: 0;
}
.battle-results.matrix .rating-table th.selected, .battle-results.matrix .rating-table td.selected {
  background: #546fe8;
}
.battle-results.matrix .breakpoint-mode {
  max-width: 150px;
  margin: 10px 0;
}
.battle-results.matrix .difference-table {
  width: 100%;
}
.battle-results.matrix .difference-table th:nth-of-type(2) {
  padding: 3px 6px;
  width: 100px;
}
.battle-results.matrix .difference-table th.number {
  padding-left: 5px;
}
.battle-results.matrix .difference-table td {
  text-align: left;
}
.battle-results.matrix .difference-table tr:first-of-type {
  background: #444;
  color: #eee;
}
.battle-results.matrix .difference-table .differences {
  font-size: 12px;
}
.battle-results.matrix .difference-table a.difference {
  color: #fff6d4;
  font-size: 12px;
  font-weight: bold;
  border-radius: 8px;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  padding: 3px 6px;
  margin: 0 5px 5px 0;
}
.battle-results.matrix .difference-table a.difference span {
  font-weight: normal;
  font-size: 11px;
}
.battle-results.matrix .difference-table a.difference.win {
  background: #1073d3;
}
.battle-results.matrix .difference-table a.difference.lose {
  background: #9e2085;
}

a.download-csv {
  display: none;
}

.rating-table {
  text-align: center;
}
.rating-table tr {
  background: none;
}
.rating-table thead th {
  position: -webkit-sticky; /* for Safari */
  position: sticky;
  top: 0;
  background: #444;
  color: #eee;
  font-size: 14px;
  font-weight: normal;
  z-index: 11;
}
.rating-table tbody th {
  position: -webkit-sticky; /* for Safari */
  position: sticky;
  left: 0;
  background: #fcfcfc;
  text-align: center;
  padding: 2px 5px;
  z-index: 10;
}
.rating-table tr:nth-of-type(2n) th {
  background: #ddd;
}
.rating-table .shield {
  display: inline-block;
  width: 17px;
  height: 16px;
  background-size: contain;
  background-image: url("../img/shield.png");
}
.rating-table .shield.shield-none {
  background-image: url("../img/shield_none.png");
}
.rating-table .shield.shield-double {
  width: 36px;
}
.rating-table a.rating {
  padding: 5px 8px;
}
.rating-table a.rating.margin-6 span {
  margin: 0 6px 0 0;
}

.matchup-detail-section .rating-table td.y-axis {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  width: 30px;
}
.matchup-detail-section .rating-table td.y-axis .shield {
  height: 30px;
  background-repeat: no-repeat;
  background-position: center;
}
.matchup-detail-section .rating-table .x-axis {
  display: flex;
  justify-content: center;
  align-items: center;
}
.matchup-detail-section .rating-table .x-axis .shield {
  margin-left: 4px;
}

.detail-section.performance {
  position: relative;
}
.detail-section.performance button.ranking-compare {
  position: absolute;
  top: 12px;
  right: 5px;
  border-radius: 8px;
  border: 1px solid #000;
  font-size: 12px;
  background: none;
  cursor: pointer;
  z-index: 5;
  max-width: 120px;
}
.detail-section.performance button.ranking-compare.dark, .detail-section.performance button.ranking-compare.dragon {
  color: #fff;
}
.detail-section.performance .pokemon-compare-label {
  font-size: 12px;
  font-weight: bold;
}

.timeline {
  border-top: 1px dashed #000;
  position: relative;
  width: 100%;
  margin: 25px 0 30px 0;
  top: 0;
  left: 0;
}
.timeline:nth-of-type(2) {
  margin: 35px 0 25px 0;
}
.timeline .item-container {
  position: absolute;
  top: 0;
}
.timeline .item-container .item {
  display: block;
  cursor: pointer;
  position: relative;
  left: -50%;
  z-index: 10;
  text-decoration: none;
  -moz-transform: scale(0, 0);
  -webkit-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -ms-transform: scale(0, 0);
  transform: scale(0, 0);
  -webkit-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  -moz-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  -o-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
}
.timeline .item-container .item.fast {
  width: 7px;
  height: 8px;
  top: -5px;
  border: 1px solid #000;
  z-index: 10;
}
.timeline .item-container .item.charged {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  top: -12px;
  border: 2px solid #000;
  z-index: 10;
}
.timeline .item-container .item.shield {
  width: 22px;
  height: 20px;
  top: -10px;
  background: url("../img/shield.png");
  z-index: 10;
}
.timeline .item-container .item.faint {
  color: #ff0000;
  font-size: 20px;
  top: -11px;
  font-weight: bold;
}
.timeline .item-container .item.faint::after {
  content: "X";
}
.timeline .item-container .item.tap {
  width: 6px;
  height: 2px;
  top: -2px;
  background: #000;
  cursor: initial;
  border: 2px solid #000;
  border-radius: 12px;
  background: none !important;
}
.timeline .item-container .item.tap.interaction {
  display: none;
}
.timeline .item-container .item.buff {
  background-image: url("../img/move_buff.png") !important;
  background-position: center 0% !important;
  background-repeat: no-repeat !important;
  -webkit-animation: BUFF_ANIMATION 1.5s infinite; /* Safari 4+ */
  -moz-animation: BUFF_ANIMATION 1.5s infinite; /* Fx 5+ */
  -o-animation: BUFF_ANIMATION 1.5s infinite; /* Opera 12+ */
  animation: BUFF_ANIMATION 1.5s infinite; /* IE 10+, Fx 29+ */
  animation-timing-function: linear;
}
.timeline .item-container .item.debuff {
  background-image: url("../img/move_debuff.png") !important;
  background-position: center 50% !important;
  background-repeat: no-repeat !important;
  -webkit-animation: DEBUFF_ANIMATION 1.5s infinite; /* Safari 4+ */
  -moz-animation: DEBUFF_ANIMATION 1.5s infinite; /* Fx 5+ */
  -o-animation: DEBUFF_ANIMATION 1.5s infinite; /* Opera 12+ */
  animation: DEBUFF_ANIMATION 1.5s infinite; /* IE 10+, Fx 29+ */
  animation-timing-function: linear;
}
.timeline .item-container .item.switchAvailable {
  width: 12px;
  height: 12px;
  border-radius: 12px;
  background: #fff;
  border: 1px solid #333;
  top: -21px;
  z-index: 10;
}
.timeline .item-container .item.switchAvailable:after {
  font-size: 11px;
  color: #333;
  content: "⇅";
  position: relative;
  top: -4px;
}
.timeline .item-container .item.active {
  -moz-transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
}

.sandbox {
  display: none;
}

.stats-table .label {
  max-width: 85px;
  text-align: left;
  font-weight: bold;
  font-size: 12px;
}
.stats-table .label span:after {
  font-size: 14px;
  content: "▴";
  visibility: hidden;
}
.stats-table .label.asc span:after {
  visibility: visible;
  content: "▴";
}
.stats-table .label.desc span:after {
  visibility: visible;
  content: "▾";
}
.stats-table tr:nth-child(2n) {
  background: rgba(0, 0, 0, 0.15);
}
.stats-table td {
  padding: 5px;
}
.stats-table span.type {
  display: block;
  border-radius: 8px;
  padding: 2px 4px;
  text-align: center;
}
.stats-table span.type.dark, .stats-table span.type.dragon, .stats-table span.type.ghost {
  color: #f9fdff;
}
.stats-table.sortable-table .label {
  cursor: pointer;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently
supported by Chrome and Opera */
}
.stats-table .status-effect-description {
  font-size: 12px;
}
.stats-table .rating span {
  margin: 0 6px 0 0 !important;
}

/* Buff and debuff background animations */
@-webkit-keyframes BUFF_ANIMATION {
  0% {
    background-position: center -50%;
  }
  100% {
    background-position: center 150%;
  }
}
@-moz-keyframes BUFF_ANIMATION {
  0% {
    background-position: center -50%;
  }
  100% {
    background-position: center 150%;
  }
}
@-o-keyframes BUFF_ANIMATION {
  0% {
    background-position: center -50%;
  }
  100% {
    background-position: center 150%;
  }
}
@keyframes BUFF_ANIMATION {
  0% {
    background-position: center -50%;
  }
  100% {
    background-position: center 150%;
  }
}
@-webkit-keyframes DEBUFF_ANIMATION {
  0% {
    background-position: center 150%;
  }
  100% {
    background-position: center -50%;
  }
}
@-moz-keyframes DEBUFF_ANIMATION {
  0% {
    background-position: center 150%;
  }
  100% {
    background-position: center -50%;
  }
}
@-o-keyframes DEBUFF_ANIMATION {
  0% {
    background-position: center 150%;
  }
  100% {
    background-position: center -50%;
  }
}
@keyframes DEBUFF_ANIMATION {
  0% {
    background-position: center 150%;
  }
  100% {
    background-position: center -50%;
  }
}
@-webkit-keyframes BLINK_ANIMATION {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes BLINK_ANIMATION {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes BLINK_ANIMATION {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}
@keyframes BLINK_ANIMATION {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}
.tooltip {
  position: absolute;
  border-radius: 8px;
  padding: 5px;
  width: 130px;
  text-align: left;
  z-index: 20;
  pointer-events: none;
}
.tooltip .details {
  font-size: 14px;
}
.tooltip .details span.label {
  font-size: 12px;
  font-weight: bold;
  font-style: italic;
}
.tooltip.faint {
  background: #ff0000;
}
.tooltip.shield {
  background: #df46e5;
}
.tooltip.tap {
  background: #eee;
}
.tooltip.dark {
  color: #fff;
}
.tooltip.switchAvailable {
  background: #fff;
}
.tooltip h3 {
  margin: 0;
  font-size: 14px;
}

/* RANKING STYLING */
.ranking-header {
  width: 50%;
  float: left;
  font-weight: bold;
  padding: 5px 0px;
  box-sizing: border-box;
}
.ranking-header.right {
  text-align: right;
}

.rankings-container {
  margin-top: 20px;
  max-height: 600px;
  overflow-y: scroll;
}
.rankings-container.threats, .rankings-container.alternatives {
  overflow-y: initial;
}
.rankings-container > .rank:hover, .rankings-container > .rank.selected {
  border-left: 20px solid #222;
}
.rankings-container.pokebox-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-height: 350px;
  margin-top: 0;
}
.rankings-container.pokebox-list .rank {
  width: 49%;
  border-left: none;
  box-sizing: border-box;
  border: 3px solid rgba(0, 0, 0, 0);
}
.rankings-container.pokebox-list .rank:hover {
  border: 3px solid rgba(0, 0, 0, 0.4);
}
.rankings-container.pokebox-list .rank.selected {
  border: 3px solid #000;
}
.rankings-container.pokebox-list .name-container {
  float: none;
  padding: 0;
}

.rank {
  padding: 5px 10px;
  border-radius: 8px;
  margin-bottom: 5px;
  border-left: 20px solid rgba(0, 0, 0, 0.1);
  position: relative;
}
.rank.hide {
  display: none !important;
}
.rank.selected .expand-label:after {
  content: "-";
}
.rank .expand-label {
  position: absolute;
  color: rgba(255, 255, 255, 0.75);
  left: -16px;
  font-size: 24px;
  top: 10px;
  width: 14px;
  text-align: center;
}
.rank .expand-label:after {
  content: "+";
}
.rank .name-container {
  float: left;
  padding: 5px 0;
}
.rank .number {
  padding-right: 10px;
}
.rank .name {
  font-weight: bold;
}
.rank .xl-info-icon {
  display: inline-block;
  margin-left: 10px;
  opacity: 0.3;
  position: relative;
  top: 3px;
  width: 22px;
  height: 18px;
  background: url("../img/xl-icon-small.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.rank .rating-container {
  /*a{
  	display:inline-block;
  	width:32px;
  	height:28px;
  	background-image:url('../img/eye.png');
  	background-repeat: no-repeat;
  	background-position: 0 center;
  	margin-right:10px;
  	float:right;
  	opacity: .5;
  }*/
}
.rank .rating-container .rating {
  float: right;
  color: #fff6d4;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  text-align: right;
  padding: 2px 6px;
  font-size: 15px;
}
.rank .rating-container .rating.score-rating {
  width: 55px !important;
}
.rank .rating-container .rating span {
  margin-left: 0;
  margin-right: 6px;
}
.rank .moves {
  display: block;
  font-size: 12px;
  max-width: 300px;
}
.rank .moves .move {
  width: 16px;
  height: 16px;
  border: 1px solid #000;
  border-radius: 12px;
  padding: 3px;
  line-height: 16px;
  text-align: center;
  margin-right: 5px;
}
.rank .moves .move:first-of-type {
  border: none;
}
.rank .moves .count {
  display: none;
  margin-left: 5px;
  background: rgba(0, 0, 0, 0.25);
  color: #fff;
  border-radius: 20px;
  min-width: 16px;
  text-align: center;
  font-size: 11px;
  padding-top: 1px;
}
.rank .moves .count.fast {
  border-radius: 0;
}
.rank .cliffhanger-points {
  border: 1px solid;
  border-radius: 8px;
  padding: 0 2px;
  margin-right: 5px;
  text-align: center;
  width: 38px;
  position: relative;
  top: -1px;
}
.rank .moveset .rating {
  background: rgba(0, 0, 0, 0.7);
  padding: 5px;
}
.rank .details {
  display: none;
  cursor: initial;
}
.rank .details.active {
  display: flex;
  flex-wrap: wrap;
}
.rank .details .detail-section {
  background: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 8px;
  box-sizing: border-box;
  margin-top: 5px;
}
.rank .details .detail-section.float {
  width: 49.5%;
}
.rank .details .detail-section.float.margin {
  margin-right: 1%;
}
.rank .details .detail-section.overall, .rank .details .detail-section.stats, .rank .details .detail-section.typing {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}
.rank .details .detail-section.overall .rating-container, .rank .details .detail-section.stats .rating-container, .rank .details .detail-section.typing .rating-container {
  text-align: center;
  padding: 5px;
}
.rank .details .detail-section.overall .rating-container .ranking-header, .rank .details .detail-section.stats .rating-container .ranking-header, .rank .details .detail-section.typing .rating-container .ranking-header {
  float: none;
  margin: 0;
  width: 100%;
  padding: 0 5px;
  font-size: 14px;
}
.rank .details .detail-section.overall .rating-container .rating, .rank .details .detail-section.stats .rating-container .rating, .rank .details .detail-section.typing .rating-container .rating {
  padding: 5px;
  display: inline-block;
  float: none;
  text-align: center;
  margin: 0 auto;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.65);
}
.rank .details .detail-section.overall .type, .rank .details .detail-section.stats .type, .rank .details .detail-section.typing .type {
  display: inline-block;
  text-transform: capitalize;
  padding: 2px 12px;
}
.rank .details .detail-section.traits-container, .rank .details .detail-section.similar-pokemon, .rank .details .detail-section.partner-pokemon {
  width: 100%;
}
.rank .details .detail-section.traits-container .ranking-header, .rank .details .detail-section.similar-pokemon .ranking-header, .rank .details .detail-section.partner-pokemon .ranking-header {
  float: none;
  width: 100%;
}
.rank .details .detail-section.traits-container .ranking-header a.trait-info, .rank .details .detail-section.similar-pokemon .ranking-header a.trait-info, .rank .details .detail-section.partner-pokemon .ranking-header a.trait-info {
  text-decoration: none;
  color: #00171c;
  border: 1px solid #00171c;
  border-radius: 20px;
  text-align: center;
  width: 15px;
  height: 15px;
  font-weight: bold;
  margin-left: 5px;
  display: inline-block;
  font-size: 12px;
  position: relative;
  top: -4px;
}
.rank .details .detail-section.traits-container .list, .rank .details .detail-section.similar-pokemon .list, .rank .details .detail-section.partner-pokemon .list {
  justify-content: center;
}
.rank .details .detail-section .stat-details .stat-row {
  display: flex;
  align-items: baseline;
  margin: 5px 0;
}
.rank .details .detail-section .stat-details .stat-row.overall {
  border-bottom: 1px solid;
  padding-bottom: 10px;
  margin-bottom: 20px;
}
.rank .details .detail-section .stat-details .stat-row.overall .value {
  font-size: 16px;
  background-color: rgba(0, 0, 0, 0.65);
  text-align: center;
  color: #fff6d4;
}
.rank .details .detail-section .stat-details .stat-row.level .value, .rank .details .detail-section .stat-details .stat-row.rank-1 .value {
  text-align: left;
  padding: 0;
}
.rank .details .detail-section .stat-details .stat-row .label {
  font-size: 12px;
  min-width: 50px;
}
.rank .details .detail-section .stat-details .stat-row .value {
  font-size: 14px;
  font-weight: bold;
  padding: 2px 5px;
  text-align: right;
  font-weight: bold;
  border-radius: 8px;
  min-width: 45px;
  margin-right: 5px;
}
.rank .details .detail-section .stat-details .stat-row .bar-container {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  align-items: center;
}
.rank .details .detail-section .stat-details .stat-row .bar-container .bar {
  width: 0px;
  height: 10px;
  border-radius: 4px;
}
.rank .details .detail-section .stat-details .stat-row .bar-container .bar.shadow {
  position: relative;
  background: rgba(158, 52, 255, 0.5);
}
.rank .details .detail-section .stat-details .stat-row .bar-container .shadow-mult {
  background: #9e34ef;
  color: #fff;
  padding: 2px 4px;
  font-size: 12px;
  border-radius: 4px;
  margin-left: 5px;
  display: none;
  position: relative;
}
.rank .details .detail-section .stat-details .xl-info-container {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.rank .details .detail-section .stat-details .xl-info-container .icon {
  width: 28px;
  height: 28px;
  background-image: url("../img/xl-icons.png");
}
.rank .details .detail-section .stat-details .xl-info-container.regular .xl-info.regular {
  display: block;
}
.rank .details .detail-section .stat-details .xl-info-container.regular .icon {
  background-position: 0px;
}
.rank .details .detail-section .stat-details .xl-info-container.mixed .xl-info.mixed {
  display: block;
}
.rank .details .detail-section .stat-details .xl-info-container.mixed .icon {
  background-position: -28px;
}
.rank .details .detail-section .stat-details .xl-info-container.xl .xl-info.xl {
  display: block;
}
.rank .details .detail-section .stat-details .xl-info-container.xl .icon {
  background-position: -56px;
}
.rank .details .detail-section .stat-details .xl-info-container.unavailable .xl-info.unavailable {
  display: block;
}
.rank .details .detail-section .stat-details .xl-info-container.unavailable .icon {
  background-position: -84px;
}
.rank .details .detail-section .stat-details .xl-info-container.xs .xl-info.xs {
  display: block;
}
.rank .details .detail-section .stat-details .xl-info-container.xs .icon {
  background-position: -84px;
}
.rank .details .detail-section.performance .ranking-header {
  float: none;
}
.rank .details .detail-section.performance .hexagon-container {
  padding: 20px;
  position: relative;
  max-width: 350px;
  margin: 0 auto;
}
.rank .details .detail-section.performance .hexagon-container .hexagon {
  margin: 10px auto;
  display: block;
  max-width: 100%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label {
  text-align: center;
  position: absolute;
  font-size: 12px;
}
.rank .details .detail-section.performance .hexagon-container .chart-label .label {
  font-size: 10px;
}
.rank .details .detail-section.performance .hexagon-container .chart-label .value {
  font-weight: bold;
}
.rank .details .detail-section.performance .hexagon-container .chart-label .comparison {
  color: rgba(255, 255, 255, 0.9);
  position: absolute;
  top: -3px;
  left: 34px;
  padding: 3px 5px;
  border-radius: 15px;
  display: none;
  width: 40px;
  box-sizing: border-box;
  text-align: center;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(1) {
  top: 0%;
  left: 47%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(1) .comparison {
  left: -44px;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(2) {
  top: 87%;
  left: 46%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(3) {
  top: 25%;
  left: 72%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(4) {
  top: 72%;
  left: 70%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(5) {
  top: 25%;
  left: 18%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(5) .comparison {
  left: -38px;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(6) {
  top: 72%;
  left: 15%;
}
.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(6) .comparison {
  left: -38px;
}
.rank .details .detail-section .moveset.show-stats .name-container {
  border-bottom: 1px solid;
}
.rank .details .detail-section .moveset.show-stats .name-container.stats-container {
  display: flex;
}
.rank .details .detail-section .moveset.show-stats.fast .move-detail-template {
  cursor: pointer;
}
.rank .details .detail-section .moveset.show-stats.fast .move-detail-template:hover {
  border-left: 10px solid rgba(0, 0, 0, 0.2);
}
.rank .details .detail-section .moveset.show-stats.fast .move-detail-template:hover.selected {
  border-left: 10px solid #000;
}
.rank .details .detail-section .moveset .name-container {
  float: none;
  padding-bottom: 3px;
  margin-bottom: 3px;
  justify-content: space-between;
  align-items: baseline;
  font-size: 14px;
}
.rank .details .detail-section .moveset .name-container.stats-container {
  border: none;
  margin: 0;
  padding: 0;
  display: none;
  overflow: hidden;
}
.rank .details .detail-section .moveset .name-container.stats-container .damage, .rank .details .detail-section .moveset .name-container.stats-container .energy, .rank .details .detail-section .moveset .name-container.stats-container .dpe, .rank .details .detail-section .moveset .name-container.stats-container .dpt, .rank .details .detail-section .moveset .name-container.stats-container .ept, .rank .details .detail-section .moveset .name-container.stats-container .turns {
  min-width: 75px;
}
.rank .details .detail-section .moveset .name-container.stats-container .dpe, .rank .details .detail-section .moveset .name-container.stats-container .turns {
  min-width: 50px;
  text-align: right;
}
.rank .details .detail-section .moveset .name-container.stats-container.move-effect, .rank .details .detail-section .moveset .name-container.stats-container.move-count {
  justify-content: flex-start;
}
.rank .details .detail-section .moveset .name-container.stats-container.move-effect div, .rank .details .detail-section .moveset .name-container.stats-container.move-count div {
  margin: 5px 5px 0 0;
}
.rank .details .detail-section .moveset .stats-container {
  font-size: 12px;
  justify-content: space-between;
}
.rank .details .detail-section .moveset .archetype {
  display: flex;
  align-items: center;
  margin-top: 2px;
  font-size: 12px;
}
.rank .details .detail-section .moveset .archetype .name, .rank .details .detail-section .moveset .archetype .icon {
  display: block;
}
.rank .details .detail-section .moveset .archetype .icon {
  width: 16px;
  height: 16px;
  background-image: url("../img/move-archetype-icons.png");
  background-repeat: no-repeat;
  margin-left: 5px;
}
.rank .details .detail-section .moveset .archetype .icon.spam {
  background-position: -16px 0;
}
.rank .details .detail-section .moveset .archetype .icon.self-debuff {
  background-position: -32px 0;
}
.rank .details .detail-section .moveset .archetype .icon.debuff {
  background-position: -48px 0;
}
.rank .details .detail-section .moveset .archetype .icon.boost {
  background-position: -64px 0;
}
.rank .details .detail-section .moveset .archetype .icon.high-energy {
  background-position: -80px 0;
}
.rank .details .detail-section .moveset .archetype .icon.general {
  background-position: -96px 0;
}
.rank .details .detail-section .moveset .archetype .icon.low-quality {
  border-radius: 16px;
  background: #000;
  color: #fff;
  text-align: center;
  font-weight: bold;
}
.rank .details .detail-section .moveset .archetype .icon.low-quality:before {
  content: "x";
}
.rank .details .detail-section .ranking-header.stat-toggle {
  text-align: right;
}
.rank .details .detail-section .ranking-header.stat-toggle a.show-move-stats {
  padding: 2px 8px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: normal;
  border: 1px solid #000;
  color: #000;
  font-size: 12px;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}
.rank .details .detail-section .ranking-header.stat-toggle a.show-move-stats.on {
  background: #444;
  color: #fff;
}
.rank .details .detail-section .rank {
  border-left: 10px solid rgba(0, 0, 0, 0.1);
}
.rank .details .detail-section .rank.selected {
  border-left: 10px solid rgb(0, 0, 0);
}
.rank .details .detail-section .rank.recommended {
  border-left: 10px solid rgb(0, 0, 0);
  margin: 20px 0 10px 0;
  font-size: 12px;
}
.rank .details .detail-section .footnote {
  font-size: 12px;
}
.rank .details .detail-section .move {
  display: inline-block;
  padding: 5px;
  margin-right: 5px;
  border-radius: 8px;
}
.rank .details .detail-section .move.charged {
  font-weight: bold;
}
.rank .details .detail-section .type {
  border-radius: 12px;
  margin: 3px;
}
.rank .details .detail-section .resistances, .rank .details .detail-section .weaknesses {
  display: flex;
  flex-wrap: wrap;
}
.rank .details .detail-section .resistances .type, .rank .details .detail-section .weaknesses .type {
  display: flex;
}
.rank .details .detail-section .resistances .type div:nth-of-type(2), .rank .details .detail-section .weaknesses .type div:nth-of-type(2) {
  text-transform: capitalize;
  padding: 2px 8px 2px 4px;
  font-size: 12px;
  line-height: 18px;
}
.rank .details .detail-section .resistances .multiplier, .rank .details .detail-section .weaknesses .multiplier {
  font-size: 12px;
  background: #333;
  color: #ddd;
  border-radius: 12px;
  padding: 2px 4px;
  line-height: 18px;
}
.rank .details .detail-section .rating-container .rating {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
}
.rank .details .multi-battle-link {
  width: 100%;
  margin: 10px 0;
  text-align: center;
}
.rank .details .multi-battle-link p {
  margin: 0;
}
.rank .details .multi-battle-link a.button {
  font-size: 16px;
  display: inline-block;
  margin: 0;
}
.rank .details h2 {
  margin: 0 0 5px 0;
}

.rank.typed-ranking {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 8px 10px;
}
.rank.typed-ranking .pokemon-info {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-width: 0;
}
.rank.typed-ranking .pokemon-info .name-container {
  margin-right: 8px;
  align-items: center;
  margin-top: -3px;
  margin-bottom: -3px;
  min-width: 46.5%;
}
.rank.typed-ranking .pokemon-info .type-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-right: 15px;
  align-self: flex-start;
}
.rank.typed-ranking .pokemon-info .type-container .type-info {
  display: inline-flex;
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 12px;
  line-height: 1.2;
  border: 1px solid #222;
  color: #111;
}
.rank.typed-ranking .pokemon-info .type-container .type-info.dark, .rank.typed-ranking .pokemon-info .type-container .type-info.dragon, .rank.typed-ranking .pokemon-info .type-container .type-info.ghost {
  color: #f9fdff;
}
.rank.typed-ranking .pokemon-info .moves {
  width: 100%;
  margin-top: 3px;
}
.rank.typed-ranking > .rating-container {
  margin-left: auto;
  align-self: flex-start;
}

.ranking-filters .ranking-filter {
  max-width: 60%;
  padding-right: 20px;
  box-sizing: border-box;
}
.ranking-filters .ranking-filter:last-of-type {
  max-width: 40%;
  padding-right: 0;
}
.ranking-filters .ranking-filter h4 {
  margin: 0 0 5px 0;
  font-size: 12px;
}
.ranking-filters .ranking-filter select {
  max-width: 100%;
}
.ranking-filters .ranking-filter select optgroup {
  font-weight: normal;
}
.ranking-filters .ranking-filter .sort-button {
  background: none !important;
  font-size: 14px;
  text-align: left;
  margin: 0;
  padding: 5px 8px;
  min-width: 100px;
}
.ranking-filters .ranking-filter .flex {
  justify-content: space-between;
}
.ranking-filters .ranking-filter a.format-rules {
  font-size: 12px;
  display: none;
}

/* Detail tabs */
.detail-tab-nav {
  display: none;
}

.detail-tab {
  display: flex;
  flex-wrap: wrap;
  flex-basis: 100%;
}
.detail-tab.active {
  display: flex;
}

.detail-tabs-on .detail-tab-nav {
  margin-top: 10px;
  flex-basis: 100%;
  display: flex;
  flex-wrap: wrap;
}
.detail-tabs-on .detail-tab-nav a {
  color: #111;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 5px 5px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 8px;
  margin-right: 5px;
}
.detail-tabs-on .detail-tab-nav a .icon {
  display: block;
  background-size: 200%;
  background-repeat: no-repeat;
  background-position: 0 center;
  margin: 0 4px 0 2px;
}
.detail-tabs-on .detail-tab-nav a[tab=matchups] .icon {
  width: 18px;
  height: 18px;
  background-image: url("../img/detail-tab-matchups.png");
}
.detail-tabs-on .detail-tab-nav a[tab=moves] .icon {
  width: 17px;
  height: 17px;
  background-image: url("../img/detail-tab-moves.png");
}
.detail-tabs-on .detail-tab-nav a[tab=misc] .icon {
  width: 17px;
  height: 5px;
  background-image: url("../img/detail-tab-misc.png");
}
.detail-tabs-on .detail-tab-nav a[tab=stats] .icon {
  width: 15px;
  height: 17px;
  background-image: url("../img/detail-tab-stats.png");
}
.detail-tabs-on .detail-tab-nav a:hover {
  background-color: rgba(0, 0, 0, 0.15);
}
.detail-tabs-on .detail-tab-nav a.active {
  background-color: #111;
  color: #f9fdff;
}
.detail-tabs-on .detail-tab-nav a.active .icon {
  background-position: 100% center;
}
.detail-tabs-on .detail-tab {
  display: none;
}
.detail-tabs-on .detail-tab.active {
  display: flex;
}

.rankings-container.show-move-counts .count {
  display: inline-block;
}

.ranking-checks {
  font-size: 14px;
}
.ranking-checks .check.xl {
  margin-right: 10px;
}

.traits,
.similar-pokemon .list,
.partner-pokemon .list {
  display: flex;
  flex-wrap: wrap;
}
.traits > div, .traits > a,
.similar-pokemon .list > div,
.similar-pokemon .list > a,
.partner-pokemon .list > div,
.partner-pokemon .list > a {
  margin: 3px;
  padding: 5px 8px;
  border-radius: 12px;
  cursor: pointer;
  border: 2px solid rgba(0, 0, 0, 0);
  text-decoration: none;
  color: #000;
}
.traits > div.dark, .traits > div.dragon, .traits > div.ghost, .traits > a.dark, .traits > a.dragon, .traits > a.ghost,
.similar-pokemon .list > div.dark,
.similar-pokemon .list > div.dragon,
.similar-pokemon .list > div.ghost,
.similar-pokemon .list > a.dark,
.similar-pokemon .list > a.dragon,
.similar-pokemon .list > a.ghost,
.partner-pokemon .list > div.dark,
.partner-pokemon .list > div.dragon,
.partner-pokemon .list > div.ghost,
.partner-pokemon .list > a.dark,
.partner-pokemon .list > a.dragon,
.partner-pokemon .list > a.ghost {
  color: #fff;
}
.traits > div:hover, .traits > a:hover,
.similar-pokemon .list > div:hover,
.similar-pokemon .list > a:hover,
.partner-pokemon .list > div:hover,
.partner-pokemon .list > a:hover {
  border: 2px solid rgba(0, 0, 0, 0.25);
}
.traits > div.pro, .traits > a.pro,
.similar-pokemon .list > div.pro,
.similar-pokemon .list > a.pro,
.partner-pokemon .list > div.pro,
.partner-pokemon .list > a.pro {
  background: rgba(23, 222, 75, 0.44);
}
.traits > div.con, .traits > a.con,
.similar-pokemon .list > div.con,
.similar-pokemon .list > a.con,
.partner-pokemon .list > div.con,
.partner-pokemon .list > a.con {
  background: rgba(255, 88, 88, 0.67);
}

.modal .traits > div {
  display: flex;
  width: 100%;
}
.modal .traits > div .name {
  font-weight: bold;
  font-size: 14px;
  flex-basis: 35%;
}
.modal .traits > div .desc {
  font-size: 12px;
  padding-left: 5px;
  flex-basis: 65%;
}

.modal .trait-modal .traits > div {
  cursor: default;
  border: none !important;
}

.modal .search-traits-selector .traits > div {
  display: block;
  width: auto;
}
.modal .search-traits-selector .traits > div.selected {
  border: 2px solid rgb(0, 0, 0);
}

.modal .sort-group .center .button {
  display: block;
  min-width: 200px;
}

.rankings-container > .rank,
.rankings-container > .rank > .rating-container {
  cursor: pointer;
}

.rating.star,
.rankings-container > .rank > .rating-container > .rating.star {
  background-image: url("../img/starburst.png");
  background-size: contain;
  background-repeat: no-repeat;
  color: #fff6d4;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 5px 5px 5px 36px;
  font-weight: bold;
  width: auto;
  text-decoration: none;
}
.rating.star.loss,
.rankings-container > .rank > .rating-container > .rating.star.loss {
  background-image: url("../img/starburst_red.png");
}

.rankings-container > .rank > .rating-container > .rating {
  background-image: none;
  width: 80px;
  text-align: center;
  font-weight: bold;
  box-sizing: border-box;
}

.ranking-categories {
  float: right;
  display: flex;
  flex-wrap: wrap;
}
.ranking-categories a {
  text-decoration: none;
  color: #003462;
  font-size: 14px;
  padding: 8px 10px;
  margin: 0 2px;
  border-radius: 30px;
  display: inline-block;
  -webkit-transition: background-color 0.2s ease-out 0s;
  -moz-transition: background-color 0.2s ease-out 0s;
  -o-transition: background-color 0.2s ease-out 0s;
  transition: background-color 0.2s ease-out 0s;
}
.ranking-categories a.selected, .ranking-categories a:hover {
  background: #c4def5;
  font-weight: bold;
  -webkit-transition: background-color 0s ease-out 0s;
  -moz-transition: background-color 0s ease-out 0s;
  -o-transition: background-color 0s ease-out 0s;
  transition: background-color 0s ease-out 0s;
}
.ranking-categories.training-mode-select {
  margin-bottom: 15px;
}

.poke-search-container {
  display: flex;
}
.poke-search-container .poke-search {
  max-width: 200px;
  display: block;
}
.poke-search-container a.search-info,
.poke-search-container a.search-traits {
  text-decoration: none;
  color: #003462;
  border: 1px solid #003462;
  border-radius: 20px;
  text-align: center;
  width: 15px;
  height: 15px;
  font-weight: bold;
  margin-left: 5px;
}
.poke-search-container a.search-traits {
  background: rgba(23, 222, 75, 0.44);
}

.options {
  margin: 40px 0 20px 0;
}

.check.limited .limited-title {
  display: inline;
}

.limited-title {
  color: #ffcf01;
  text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
  font-weight: bold;
}

.rankings-container .limited-rank {
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.rankings-container .limited-rank .name-container {
  color: #000;
}

.asterisk {
  font-size: 12px;
  margin-bottom: 15px;
}

/* TEAM BUILD STYLING */
.team-build {
  display: flex;
  justify-content: space-between;
}
.team-build .poke {
  float: none;
}
.team-build .poke a.random {
  margin-bottom: 0;
}
.team-build .poke a.swap {
  display: none;
}
.team-build .poke:nth-of-type(2) .poke-stats {
  text-align: left;
}
.team-build .poke:nth-of-type(2) .poke-stats .stat-label {
  float: left !important;
}
.team-build .poke:nth-of-type(2) .poke-stats .bar-back {
  -moz-transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
}
.team-build .poke:nth-of-type(2) .poke-stats .hp.bar-back .stat {
  -moz-transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
}
.team-build .poke .options .shield-section, .team-build .poke .options .toggle, .team-build .poke .options .toggle-content, .team-build .poke .bar-back, .team-build .poke .move-bars {
  display: none;
}
.team-build .poke.multi {
  display: block;
}
.team-build .poke.multi .poke-stats {
  display: block;
}
.team-build .poke.multi .custom-options {
  display: block;
}
.team-build .poke.multi .options {
  display: none;
}

.team-content a.toggle {
  margin-top: 10px;
}

.team-advanced .poke .poke-stats {
  background: none;
}
.team-advanced .flex:nth-of-type(2) {
  justify-content: space-between;
  flex-wrap: wrap;
}
.team-advanced .flex:nth-of-type(2) .flex-section {
  max-width: 350px;
}
.team-advanced .poke.flex {
  float: none;
  max-width: none;
  flex-wrap: wrap;
}
.team-advanced .poke.flex .flex-break {
  flex-basis: 100%;
}
.team-advanced .team-option {
  font-size: 12px;
  margin-right: 20px;
  margin-bottom: 10px;
}
.team-advanced .team-option h3 {
  font-size: 14px;
  margin-top: 0;
}

.typings {
  display: none;
}
.typings a.toggle {
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}
.typings a.toggle:first-of-type {
  margin-top: 0;
}
.typings .defense,
.typings .offense {
  overflow-x: scroll;
}
.typings .defense table td,
.typings .offense table td {
  padding: 5px 10px;
}
.typings .defense table td.heading,
.typings .offense table td.heading {
  font-weight: bold;
}
.typings .defense table td.name,
.typings .offense table td.name {
  display: block;
  width: 120px;
}
.typings .overview-section {
  border-radius: 8px;
  padding: 4px 8px;
  margin-bottom: 10px;
  background: rgba(0, 0, 0, 0.05);
}
.typings .overview-section .flex {
  justify-content: space-between;
}
.typings .overview-section h3 {
  margin: 10px 0 0px 0;
  font-size: 18px;
  line-height: 26px;
}
.typings .overview-section .grade {
  font-size: 24px;
  font-weight: bold;
  padding: 6px 8px;
  border-radius: 8px;
  line-height: 30px;
}
.typings .overview-section .notes div {
  display: none;
  font-size: 12px;
  border-top: rgba(0, 0, 0, 0.25) 1px solid;
  margin-top: 5px;
  padding-top: 5px;
}
.typings .overview-section .grade[grade=A] {
  background: #018dba;
  color: #fff6d4;
}
.typings .overview-section .grade[grade=B] {
  background: #1b79b4;
  color: #a5d6fb;
}
.typings .overview-section .grade[grade=C] {
  background: #968cb6;
  color: #fff6d4;
}
.typings .overview-section .grade[grade=D] {
  background: #96359e;
  color: #fd91e0;
}
.typings .overview-section .grade[grade=F] {
  background: #b9008f;
  color: #ff92e2;
}

.summary li {
  line-height: 24px;
  margin-bottom: 10px;
}

.summary span,
.article span.type {
  padding: 2px 5px;
  border-radius: 8px;
  font-weight: bold;
}

.article span.type {
  font-weight: normal;
}

.typeRating {
  border: 2px solid #000;
  border-radius: 8px;
  text-align: center;
  margin: 5px;
  overflow: hidden;
}
.typeRating h4 {
  margin: 0;
  padding: 2px 10px;
}

.threats.rankings-container .rank,
.alternatives.rankings-container .rank,
.section.battle-results .rankings-container .rank {
  cursor: initial;
  border-left: none;
}
.threats.rankings-container .rank:hover,
.alternatives.rankings-container .rank:hover,
.section.battle-results .rankings-container .rank:hover {
  border-left: none;
}

.table-container {
  overflow-x: scroll;
}

.histograms {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}
.histograms .histogram {
  position: relative;
  width: 31%;
  text-align: center;
  padding-left: 20px;
  box-sizing: border-box;
}
.histograms .histogram .chart {
  height: 200px;
  border: 1px solid #003462;
  background: rgba(255, 255, 255, 0.6);
  overflow: hidden;
}
.histograms .histogram .chart .segment {
  float: left;
  position: relative;
  height: 100%;
  box-sizing: border-box;
}
.histograms .histogram .chart .segment:nth-of-type(10) {
  border-right: 1px dashed rgba(0, 0, 0, 0.5);
}
.histograms .histogram .chart .segment .bar {
  position: absolute;
  bottom: 0;
  width: 100%;
  -webkit-transition: height 0.3s ease-out 0s;
  -moz-transition: height 0.3s ease-out 0s;
  -o-transition: height 0.3s ease-out 0s;
  transition: height 0.3s ease-out 0s;
}
.histograms .histogram h2 {
  font-size: 18px;
}
.histograms .histogram .move-label {
  font-size: 12px;
  height: 28px;
}
.histograms .histogram .star.rating {
  font-size: 14px;
  display: inline-block;
}
.histograms .histogram .x-axis {
  display: flex;
  justify-content: space-between;
}
.histograms .histogram .x-axis div {
  font-size: 12px;
  width: 27px;
}
.histograms .histogram .x-axis div:first-of-type {
  text-align: left;
}
.histograms .histogram .button {
  font-size: 12px;
  border: 1px solid #000;
  padding: 5px;
  display: none;
}
.histograms .histogram .label-x-axis {
  position: absolute;
  left: -25px;
  top: 40%;
  -moz-transform: rotate(270deg);
  -webkit-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}
.histograms .histogram .label-y-axis {
  color: #f9fdff;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 5px 5px 5px 5px;
  font-weight: bold;
  font-size: 16px;
}
.histograms .histogram .stats {
  text-align: left;
  margin: 10px 0;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}

/* ARTICLE */
.article p:first-of-type {
  margin-top: 1em;
}
.article p, .article ul {
  font-size: 14px;
  line-height: 20px;
}
.article ul li {
  margin-bottom: 10px;
}
.article .article-section h3 {
  font-size: 18px;
  border-bottom: 1px solid;
  padding-bottom: 4px;
}
.article h2 {
  font-size: 22px;
  margin-bottom: 10px;
}
.article h3.article-header,
.article .tip {
  background: rgb(98, 114, 139);
  padding: 10px;
  border-radius: 8px;
  color: #f9fdff;
  line-height: 24px;
}
.article table {
  border: none;
  margin: 0 auto;
}
.article table td {
  padding: 5px;
  vertical-align: top;
}
.article table td.buff {
  color: #f1841c;
}
.article table tr:nth-of-type(2n) {
  background: rgba(0, 0, 0, 0.1);
}
.article table.alternatives-table {
  min-width: 600px;
}
.article .rating-table td {
  padding: 3px;
}
.article .rating-table td.name {
  vertical-align: middle;
  text-align: left;
  padding-right: 10px;
}
.article .rating-table td.name-small {
  font-size: 14px;
  max-width: 80px;
  vertical-align: bottom;
}
.article .rating-table.legend {
  margin: 0;
  text-align: left;
}
.article .rating-table.legend td {
  padding: 5px;
}
.article .rating-table.legend a.rating {
  text-align: center;
}
.article .rating-table a.rating {
  width: 55px;
  box-sizing: border-box;
}
.article .rating-table.matrix-table th.arrow, .article .rating-table.matrix-table thead td.arrow, .article .rating-table.meta-table th.arrow, .article .rating-table.meta-table thead td.arrow, .article .rating-table.threats-table th.arrow, .article .rating-table.threats-table thead td.arrow, .article .rating-table.alternatives-table th.arrow, .article .rating-table.alternatives-table thead td.arrow {
  background-image: url("../img/matrix-arrow-header.png");
  background-repeat: no-repeat;
  background-position: right 4px bottom 4px;
}
.article .rating-table.matrix-table th.name, .article .rating-table.meta-table th.name, .article .rating-table.threats-table th.name, .article .rating-table.alternatives-table th.name {
  background-image: url("../img/matrix-arrow.png");
  background-repeat: no-repeat;
  background-position: right 4px center;
  padding-right: 20px;
}
.article .rating-table.matrix-table td.name-small, .article .rating-table.meta-table td.name-small, .article .rating-table.threats-table td.name-small, .article .rating-table.alternatives-table td.name-small {
  padding-bottom: 12px;
}
.article .rating-table.matrix-table a.rating {
  width: 70px;
  padding: 5px;
  border-radius: 8px !important;
}
.article .rating-table.matrix-table a.rating span {
  margin: 0 6px 0 0;
}
.article .rating-table.matrix-table a.rating:after {
  border-radius: 8px !important;
}
.article .rating-table.matrix-table .matrix-record,
.article .rating-table.matrix-table .matrix-average {
  font-size: 14px;
  white-space: nowrap;
  padding: 8px 10px 0px 10px;
}
.article .rating-table.matrix-table .rating.average {
  padding: 5px;
  width: 85px;
  box-sizing: border-box;
  pointer-events: none;
}
.article .rating-table.matrix-table .rating.average span {
  margin: 0 6px 0 0;
}
.article .rating-table.matrix-table th:first-of-type {
  min-width: 18px;
}
.article .results-buttons {
  display: flex;
  justify-content: center;
}
.article .results-buttons a.button {
  margin: 10px;
}
.article .threats-table .name,
.article .alternatives-table .name,
.article .meta-table .name {
  min-width: 120px;
  font-size: 12px;
}
.article .threats-table th,
.article .alternatives-table th,
.article .meta-table th {
  text-align: left;
}
.article .threats-table .button.add,
.article .alternatives-table .button.add,
.article .meta-table .button.add {
  display: inline-block;
  padding: 0 5px;
  margin: 0 0 0 5px;
  border-radius: 30px;
}
.article .threats-table .region-label,
.article .alternatives-table .region-label,
.article .meta-table .region-label {
  font-size: 14px;
  font-weight: normal;
  font-style: italic;
  text-transform: capitalize;
}
.article .threats-table .region-label.kanto,
.article .alternatives-table .region-label.kanto,
.article .meta-table .region-label.kanto {
  color: #ce0000;
}
.article .threats-table .region-label.johto,
.article .alternatives-table .region-label.johto,
.article .meta-table .region-label.johto {
  color: #a59662;
}
.article .threats-table .region-label.hoenn,
.article .alternatives-table .region-label.hoenn,
.article .meta-table .region-label.hoenn {
  color: #2665f3;
}
.article .threats-table .region-label.sinnoh,
.article .alternatives-table .region-label.sinnoh,
.article .meta-table .region-label.sinnoh {
  color: #821a37;
}
.article .threats-table .region-label.unova,
.article .alternatives-table .region-label.unova,
.article .meta-table .region-label.unova {
  color: #333;
}
.article .threats-table .region-label.alola,
.article .alternatives-table .region-label.alola,
.article .meta-table .region-label.alola {
  color: #fb900b;
}
.article .threats-table .region-label.galar,
.article .alternatives-table .region-label.galar,
.article .meta-table .region-label.galar {
  color: #946000;
}

.matrix-table th.number {
  font-weight: normal;
  font-size: 12px;
  text-align: left;
  color: rgb(181, 181, 181);
  padding-right: 0;
}

.matrix-table th:nth-of-type(2) {
  left: 18px;
}

a.rating, .rating-container .rating, .summary .rating,
.stats-table .rating, .train-table .rating {
  display: flex;
  padding: 5px 12px;
  text-decoration: none;
  font-weight: bold;
  margin: 0 auto;
  justify-content: center;
  align-items: center;
}
a.rating span, .rating-container .rating span, .summary .rating span,
.stats-table .rating span, .train-table .rating span {
  display: block;
  margin: 0 auto;
  width: 18px;
  height: 18px;
  background-size: 100%;
  background-repeat: no-repeat;
}
a.rating i, .rating-container .rating i, .summary .rating i,
.stats-table .rating i, .train-table .rating i {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 13px;
}
a.rating i:after, .rating-container .rating i:after, .summary .rating i:after,
.stats-table .rating i:after, .train-table .rating i:after {
  content: "→";
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  line-height: 0;
  position: absolute;
  right: 8px;
}
a.rating.win, .rating-container .rating.win, .summary .rating.win,
.stats-table .rating.win, .train-table .rating.win {
  background-color: #1280b6;
  color: #fff6d4;
}
a.rating.win span, .rating-container .rating.win span, .summary .rating.win span,
.stats-table .rating.win span, .train-table .rating.win span {
  border-radius: 20px;
  background: #fff6d4;
}
a.rating.win i, .rating-container .rating.win i, .summary .rating.win i,
.stats-table .rating.win i, .train-table .rating.win i {
  color: #fff6d4;
}
a.rating.close-win, .rating-container .rating.close-win, .summary .rating.close-win,
.stats-table .rating.close-win, .train-table .rating.close-win {
  background-color: #2e6bb0;
  color: #fff6d4;
}
a.rating.close-win span, .rating-container .rating.close-win span, .summary .rating.close-win span,
.stats-table .rating.close-win span, .train-table .rating.close-win span {
  border-radius: 20px;
  border: 2px solid #a5d6fb;
  box-sizing: border-box;
}
a.rating.close-win i, .rating-container .rating.close-win i, .summary .rating.close-win i,
.stats-table .rating.close-win i, .train-table .rating.close-win i {
  color: #fff6d4;
}
a.rating.close-loss, .rating-container .rating.close-loss, .summary .rating.close-loss,
.stats-table .rating.close-loss, .train-table .rating.close-loss {
  background-color: #a91c7e;
  color: #ffd3f4;
}
a.rating.close-loss span, .rating-container .rating.close-loss span, .summary .rating.close-loss span,
.stats-table .rating.close-loss span, .train-table .rating.close-loss span {
  background-image: url("../img/themes/sunflower/rating-close-loss.png?v=7");
  opacity: 0.9;
}
a.rating.close-loss i, .rating-container .rating.close-loss i, .summary .rating.close-loss i,
.stats-table .rating.close-loss i, .train-table .rating.close-loss i {
  color: #ffd3f4;
}
a.rating.loss, .rating-container .rating.loss, .summary .rating.loss,
.stats-table .rating.loss, .train-table .rating.loss {
  background-color: #c71b71;
  color: #ffc9f1;
}
a.rating.loss span, .rating-container .rating.loss span, .summary .rating.loss span,
.stats-table .rating.loss span, .train-table .rating.loss span {
  background-image: url("../img/themes/sunflower/rating-loss.png?v=7");
}
a.rating.loss i, .rating-container .rating.loss i, .summary .rating.loss i,
.stats-table .rating.loss i, .train-table .rating.loss i {
  color: #ffd3f4;
}
a.rating.tie, .rating-container .rating.tie, .summary .rating.tie,
.stats-table .rating.tie, .train-table .rating.tie {
  background-color: rgb(150, 140, 182) !important;
  color: #f2f2f2;
}
a.rating.tie span, .rating-container .rating.tie span, .summary .rating.tie span,
.stats-table .rating.tie span, .train-table .rating.tie span {
  background-image: url("../img/themes/sunflower/rating-tie.png");
  opacity: 0.75;
}
a.rating.tie i, .rating-container .rating.tie i, .summary .rating.tie i,
.stats-table .rating.tie i, .train-table .rating.tie i {
  color: #f2f2f2;
}

a.rating {
  border-radius: 30px !important;
  position: relative;
  -webkit-transition: box-shadow 0.25s ease-out 0s;
  -moz-transition: box-shadow 0.25s ease-out 0s;
  -o-transition: box-shadow 0.25s ease-out 0s;
  transition: box-shadow 0.25s ease-out 0s;
}
a.rating:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  pointer-events: none;
  border-radius: 30px !important;
  -webkit-transition: background-color 0.25s ease-out 0s;
  -moz-transition: background-color 0.25s ease-out 0s;
  -o-transition: background-color 0.25s ease-out 0s;
  transition: background-color 0.25s ease-out 0s;
}
a.rating:hover {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.55) !important;
  -webkit-transition: box-shadow 0s ease-out 0s;
  -moz-transition: box-shadow 0s ease-out 0s;
  -o-transition: box-shadow 0s ease-out 0s;
  transition: box-shadow 0s ease-out 0s;
}
a.rating:hover:after {
  background: rgba(255, 255, 255, 0.1);
  -webkit-transition: background-color 0s ease-out 0s;
  -moz-transition: background-color 0s ease-out 0s;
  -o-transition: background-color 0s ease-out 0s;
  transition: background-color 0s ease-out 0s;
}

.rank a.rating {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.25);
}

.article-item {
  margin-bottom: 10px;
}
.article-item .col-9 {
  width: 75%;
  padding: 0 10px;
}
.article-item img {
  display: block;
  border: 1px solid #000;
}
.article-item h4 {
  margin-top: 0;
}
.article-item .tags {
  display: flex;
}
.article-item .tags a {
  display: block;
  text-decoration: none;
  background: #003462;
  color: #f9fdff;
  border-radius: 8px;
  font-size: 12px;
  padding: 4px 8px;
  margin-right: 5px;
}

.article .date, .article-item .date {
  margin-bottom: 1em;
  font-size: 14px;
}
.article h1:first-of-type,
.article h4:first-of-type, .article-item h1:first-of-type,
.article-item h4:first-of-type {
  margin: 0;
}
.article img, .article-item img {
  display: block;
  max-width: 100%;
  border: 1px solid #000;
}

/* CONTRIBUTORS */
.contribute h2 {
  font-size: 20px;
}
.contribute .supporters {
  justify-content: center;
  flex-wrap: wrap;
}
.contribute .supporters .supporter-container {
  margin-bottom: 15px;
}
.contribute .supporters .supporter-container img {
  display: block;
  max-height: 85px;
  margin: 0 auto;
}
.contribute .supporters .supporter-container .space {
  width: 85px;
  height: 85px;
}
.contribute .supporters .supporter {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  margin: 5px;
}

/* MOVES PAGE */
.move-table-container {
  max-width: 400px;
  margin: 0 auto;
}
.move-table-container .poke-search {
  margin-top: 15px;
}

.move-explore-container {
  max-width: 500px;
  margin: 0 auto;
}
.move-explore-container .move-select-container {
  justify-content: space-between;
}
.move-explore-container .move-select-container .move-select-item {
  max-width: 180px;
}
.move-explore-container .move-select-container .poke-search {
  margin-bottom: 5px;
}
.move-explore-container .move-select-container .check {
  font-size: 14px;
}
.move-explore-container .move-select-container h3 {
  font-size: 14px;
}
.move-explore-container .moveset-stats {
  flex-wrap: wrap;
  justify-content: center;
  text-align: center;
}
.move-explore-container .moveset-stats .stat {
  margin: 10px 15px;
}
.move-explore-container .moveset-stats .stat h3 {
  margin-top: 0;
  font-size: 14px;
}
.move-explore-container .explore-results {
  margin-top: 30px;
}
.move-explore-container .explore-results h2 {
  margin-top: 30px;
}
.move-explore-container .explore-results h2 + div {
  font-size: 14px;
}
.move-explore-container .explore-results .rankings-container {
  margin-top: 10px;
}
.move-explore-container .explore-results a.rank {
  display: block;
  text-decoration: none;
  color: #000;
}
.move-explore-container .explore-results a.rank:hover {
  border-left: none;
}
.move-explore-container .explore-results a.rank .name-container {
  pointer-events: none;
  float: none;
}

.moves p:first-of-type {
  margin-top: 1em;
}

/* MODAL WINDOW */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: scroll;
}
.modal .modal-container {
  position: relative;
  background: #f9fdff;
  margin: 0 auto;
  max-width: 400px;
  vertical-align: middle;
  margin-top: 50px;
  border-radius: 8px;
  margin-bottom: 75px;
}
.modal .modal-container .modal-header {
  padding: 10px;
  background: #003462;
  color: #f9fdff;
  font-size: 24px;
  font-weight: bold;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.modal .modal-container .modal-close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 10px;
  color: #f9fdff;
  cursor: pointer;
}
.modal .modal-container .modal-close:after {
  content: "X";
}
.modal .modal-container .modal-content {
  padding: 10px;
}
.modal .modal-container .poke.single {
  float: none;
  margin: 0 auto;
  max-width: 200px;
}
.modal .modal-container .poke.single .poke-stats {
  background: none;
}
.modal .modal-container .poke.single .clear-selection,
.modal .modal-container .poke.single .hp.bar-back,
.modal .modal-container .poke.single .move-bars,
.modal .modal-container .poke.single .random,
.modal .modal-container .poke.single .swap,
.modal .modal-container .poke.single .auto-select,
.modal .modal-container .poke.single .options .shield-section,
.modal .modal-container .poke.single .options .toggle,
.modal .modal-container .poke.single .options .toggle-content {
  display: none;
}
.modal .modal-container .poke.single .pokebox {
  display: none !important;
}
.modal .modal-container .poke.single[context=modalrankcompare] .advanced-section, .modal .modal-container .poke.single[context=modalrankcompare] .move-select-container, .modal .modal-container .poke.single[context=modalrankcompare] .options {
  display: none;
}
.modal .modal-container .poke-search {
  background: none !important;
  margin-bottom: 5px;
}
.modal .modal-container .move-select {
  max-width: 200px;
}
.modal .sandbox-move-select .move-select {
  font-size: 18px;
  margin-bottom: 10px;
}
.modal .sandbox-move-select .charge-select {
  max-width: 200px;
  margin-bottom: 10px;
}
.modal .sandbox-move-select .move-stats {
  flex-wrap: wrap;
  justify-content: space-around;
  text-align: center;
}
.modal .sandbox-move-select .move-stats > div {
  padding: 10px;
}
.modal .sandbox-move-select .move-stats span.stat {
  display: block;
  font-weight: bold;
}
.modal .list-export .list-text,
.modal .list-export .team-string-text,
.modal .search-string-window .list-text,
.modal .search-string-window .team-string-text {
  width: 100%;
  height: 200px;
}
.modal .list-export .copy,
.modal .search-string-window .copy {
  display: inline-block;
  cursor: pointer;
  font-weight: bold;
  background: #6296be;
  color: #f9fdff;
  padding: 5px;
  border-radius: 8px;
  margin: 5px 0;
}
.modal .list-export .search-string-options .check,
.modal .search-string-window .search-string-options .check {
  margin-right: 10px;
}
.modal .list-export .export-options,
.modal .search-string-window .export-options {
  display: flex;
  justify-content: space-between;
}
.modal .list-export .export-options a.json,
.modal .search-string-window .export-options a.json {
  font-size: 12px;
}
.modal .save-list input {
  font-size: 18px;
}
.modal .button {
  margin: 25px auto 0 auto;
  display: inline-block;
}
.modal .pokebox-options {
  display: flex;
}
.modal .pokebox-options a {
  padding: 4px 8px;
  border-radius: 8px;
  text-decoration: none;
  font-size: 14px;
  color: #fff;
  background: #003462;
  margin-right: 10px;
  margin-bottom: 10px;
}
.modal .pokebox-options a.pokebox-edit {
  border: 1px solid #000;
  color: #000;
  background: url("../img/pokebattler_logo.png") #f9fdff;
  background-repeat: no-repeat;
  background-position: 5px center;
  background-size: 20px;
  padding: 4px 8px 4px 30px;
}
.modal .pokebox-options a.pvpoke-sponsor {
  border: 1px solid #000;
  color: #000;
  background: url("../img/app_192.png") #f9fdff;
  background-repeat: no-repeat;
  background-position: 5px center;
  background-size: 20px;
  padding: 4px 8px 4px 30px;
  margin-left: auto;
}
.modal .pokebox-import ol {
  line-height: 30px;
}
.modal .pokebox-import input.pokebox-id {
  max-width: 200px;
}
.modal .pokebox-import .poke-count-container {
  font-size: 12px;
  margin-bottom: 5px;
}
.modal .pokebox-import .poke-search-container {
  margin-top: 10px;
  justify-content: space-between;
  align-items: center;
}
.modal .pokebox-import .poke-search-container .check {
  margin-top: 0;
}
.modal .pokebox-import[select-mode=multi] .multi {
  display: block;
}
.modal .pokebox-import[select-mode=single] .multi {
  display: none;
}
.modal a.compare-poke,
.modal a.duplicate-poke,
.modal a.compare-pokebox {
  display: block;
  font-size: 14px;
  margin-top: 25px;
}

/* TEXTS & FONTS */
h1.title {
  font-size: 18px;
  margin: 0;
}

h2 {
  font-size: 14px;
  margin-bottom: 0;
}

a.toggle {
  color: #003462;
  text-decoration: none;
  font-weight: bold;
  display: block;
}

a.pokebattler {
  background-image: url("../img/pokebattler_logo.png");
  background-size: 20px;
  background-repeat: no-repeat;
  padding-left: 24px;
}

.center {
  text-align: center;
}

/* TYPE COLORS */
.bug {
  background: #aec92c; /* Old browsers */
  background: -moz-linear-gradient(top, #9bc231 30%, #aec92c 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #9bc231 30%, #aec92c 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #9bc231 30%, #aec92c 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.dark {
  background: #6e7681; /* Old browsers */
  background: -moz-linear-gradient(top, #52505e 30%, #6e7681 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #52505e 30%, #6e7681 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #52505e 30%, #6e7681 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.dragon {
  background: #067fc4; /* Old browsers */
  background: -moz-linear-gradient(top, #1065b6 30%, #067fc4 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #1065b6 30%, #067fc4 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #1065b6 30%, #067fc4 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.electric {
  background: #fedf6b; /* Old browsers */
  background: -moz-linear-gradient(top, #f3d43e 30%, #fedf6b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #f3d43e 30%, #fedf6b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #f3d43e 30%, #fedf6b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.fairy {
  background: #f6a7e8; /* Old browsers */
  background: -moz-linear-gradient(top, #eb8de1 30%, #f6a7e8 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #eb8de1 30%, #f6a7e8 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #eb8de1 30%, #f6a7e8 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.fighting {
  background: #e34448; /* Old browsers */
  background: -moz-linear-gradient(top, #ce3d64 30%, #e34448 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ce3d64 30%, #e34448 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ce3d64 30%, #e34448 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.fire {
  background: #feb04b; /* Old browsers */
  background: -moz-linear-gradient(top, #fe9d59 30%, #feb04b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #fe9d59 30%, #feb04b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #fe9d59 30%, #feb04b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.flying {
  background: #a7c1f2; /* Old browsers */
  background: -moz-linear-gradient(top, #91a8de 30%, #a7c1f2 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #91a8de 30%, #a7c1f2 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #91a8de 30%, #a7c1f2 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.ghost {
  background: #7571d0; /* Old browsers */
  background: -moz-linear-gradient(top, #5069ac 30%, #7571d0 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #5069ac 30%, #7571d0 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #5069ac 30%, #7571d0 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.grass {
  background: #59c079; /* Old browsers */
  background: -moz-linear-gradient(top, #5fbb50 30%, #59c079 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #5fbb50 30%, #59c079 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #5fbb50 30%, #59c079 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.ground {
  background: #d2976b; /* Old browsers */
  background: -moz-linear-gradient(top, #d87b40 30%, #d2976b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #d87b40 30%, #d2976b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #d87b40 30%, #d2976b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.ice {
  background: #94ddd6; /* Old browsers */
  background: -moz-linear-gradient(top, #74d3bd 30%, #94ddd6 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #74d3bd 30%, #94ddd6 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #74d3bd 30%, #94ddd6 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.normal {
  background: #a3a49e; /* Old browsers */
  background: -moz-linear-gradient(top, #909aa3 30%, #a3a49e 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #909aa3 30%, #a3a49e 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #909aa3 30%, #a3a49e 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.poison {
  background: #a662c7; /* Old browsers */
  background: -moz-linear-gradient(top, #c662d6 30%, #a662c7 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #c662d6 30%, #a662c7 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #c662d6 30%, #a662c7 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.psychic {
  background: #fda194; /* Old browsers */
  background: -moz-linear-gradient(top, #f2726f 30%, #fda194 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #f2726f 30%, #fda194 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #f2726f 30%, #fda194 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.rock {
  background: #d7cd90; /* Old browsers */
  background: -moz-linear-gradient(top, #c7b98c 30%, #d7cd90 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #c7b98c 30%, #d7cd90 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #c7b98c 30%, #d7cd90 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.steel {
  background: #5aafb4; /* Old browsers */
  background: -moz-linear-gradient(top, #50879c 30%, #5aafb4 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #50879c 30%, #5aafb4 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #50879c 30%, #5aafb4 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.water {
  background: #6ac7e9; /* Old browsers */
  background: -moz-linear-gradient(top, #4f91db 30%, #6ac7e9 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #4f91db 30%, #6ac7e9 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #4f91db 30%, #6ac7e9 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.dark, .dragon, .ghost {
  color: #f9fdff;
}
.dark > .name-container, .dragon > .name-container, .ghost > .name-container {
  color: #f9fdff;
}
.dark option, .dragon option, .ghost option {
  color: #000;
}
.dark .detail-section, .dragon .detail-section, .ghost .detail-section {
  color: #000;
}

.buff.bug, .debuff.bug {
  background-color: #9bc231 !important;
}
.buff.dark, .debuff.dark {
  background-color: #52505e !important;
}
.buff.dragon, .debuff.dragon {
  background-color: #1065b6 !important;
}
.buff.electric, .debuff.electric {
  background-color: #f3d43e !important;
}
.buff.fairy, .debuff.fairy {
  background-color: #eb8de1 !important;
}
.buff.fighting, .debuff.fighting {
  background-color: #ce3d64 !important;
}
.buff.fire, .debuff.fire {
  background-color: #fe9d59 !important;
}
.buff.flying, .debuff.flying {
  background-color: #91a8de !important;
}
.buff.ghost, .debuff.ghost {
  background-color: #5069ac !important;
}
.buff.grass, .debuff.grass {
  background-color: #5fbb50 !important;
}
.buff.ground, .debuff.ground {
  background-color: #d87b40 !important;
}
.buff.ice, .debuff.ice {
  background-color: #74d3bd !important;
}
.buff.normal, .debuff.normal {
  background-color: #909aa3 !important;
}
.buff.poison, .debuff.poison {
  background-color: #c662d6 !important;
}
.buff.psychic, .debuff.psychic {
  background-color: #f2726f !important;
}
.buff.rock, .debuff.rock {
  background-color: #c7b98c !important;
}
.buff.steel, .debuff.steel {
  background-color: #50879c !important;
}
.buff.water, .debuff.water {
  background-color: #4f91db !important;
}

.tap.bug {
  border-color: #9bc231 !important;
}
.tap.dark {
  border-color: #52505e !important;
}
.tap.dragon {
  border-color: #1065b6 !important;
}
.tap.electric {
  border-color: #f3d43e !important;
}
.tap.fairy {
  border-color: #eb8de1 !important;
}
.tap.fighting {
  border-color: #ce3d64 !important;
}
.tap.fire {
  border-color: #fe9d59 !important;
}
.tap.flying {
  border-color: #91a8de !important;
}
.tap.ghost {
  border-color: #5069ac !important;
}
.tap.grass {
  border-color: #5fbb50 !important;
}
.tap.ground {
  border-color: #d87b40 !important;
}
.tap.ice {
  border-color: #74d3bd !important;
}
.tap.normal {
  border-color: #909aa3 !important;
}
.tap.poison {
  border-color: #c662d6 !important;
}
.tap.psychic {
  border-color: #f2726f !important;
}
.tap.rock {
  border-color: #c7b98c !important;
}
.tap.steel {
  border-color: #50879c !important;
}
.tap.water {
  border-color: #4f91db !important;
}

/* RESPONSIVE */
@media only screen and (max-width: 468px) {
  .rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(1) {
    top: 0%;
    left: 44%;
  }
  .rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(2) {
    top: 87%;
    left: 43%;
  }
  .rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(3) {
    top: 25%;
    left: 74%;
  }
  .rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(4) {
    top: 72%;
    left: 72%;
  }
  .rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(5) {
    top: 25%;
    left: 10%;
  }
  .rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(6) {
    top: 72%;
    left: 7%;
  }
}
@media only screen and (max-width: 720px) {
  .mobile {
    display: block;
  }
  .desktop {
    display: none !important;
  }
  .header-ticker {
    z-index: 50;
  }
  header {
    z-index: 50;
    padding: 3px 10px;
  }
  header .title {
    float: none;
    text-align: center;
  }
  header .hamburger {
    margin-top: 4px;
  }
  header .menu {
    display: none;
    float: none;
    clear: both;
    margin-top: 5px;
    padding: 0;
    flex: 0 0 100%;
    background: #003462;
    position: fixed;
    width: 100%;
    margin: 0 -10px;
    top: 37px;
    height: calc(100% - 37px);
    padding: 8px 8px;
    box-sizing: border-box;
    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.4);
    overflow-y: auto;
  }
  header .menu a {
    padding: 9px 8px 9px 38px;
    margin: 0;
    font-size: 18px;
    background-size: 18px 18px;
    background-position: 8px center;
    color: #f9fdff;
  }
  header .menu > a.selected, header .menu .parent-menu > a.selected {
    background-color: #285e85;
    -webkit-transition: background-color 0s ease-out 0s;
    -moz-transition: background-color 0s ease-out 0s;
    -o-transition: background-color 0s ease-out 0s;
    transition: background-color 0s ease-out 0s;
  }
  header .menu .parent-menu a {
    position: relative;
  }
  header .menu .parent-menu a span {
    width: 20px;
    height: 10px;
    background: url("../img/themes/sunflower/dropdown-triangle-large.png");
    background-repeat: no-repeat;
    background-position: center;
    top: 2px;
    -webkit-transition: all 0.125s ease-out 0s;
    -moz-transition: all 0.125s ease-out 0s;
    -o-transition: all 0.125s ease-out 0s;
    transition: all 0.125s ease-out 0s;
  }
  header .menu .parent-menu a.active span {
    transform: rotate(180deg);
  }
  header .menu .icon-battle {
    background-image: url("../img/themes/sunflower/nav-battle-white.png");
  }
  header .menu .icon-train {
    background-image: url("../img/themes/sunflower/nav-train-white.png");
  }
  header .menu .icon-rankings {
    background-image: url("../img/themes/sunflower/nav-rankings-white.png");
  }
  header .menu .icon-team {
    background-image: url("../img/themes/sunflower/nav-team-white.png");
  }
  header .menu .icon-heart {
    background-image: url("../img/themes/sunflower/nav-heart-white.png");
  }
  header .menu .submenu {
    padding-left: 38px;
  }
  header .menu .submenu a {
    font-size: 16px;
  }
  header .menu .submenu a:first-of-type {
    margin-top: 0;
  }
  header .menu .submenu a {
    padding-left: 24px;
  }
  header .menu .more-parent-menu .submenu {
    padding-left: 0;
    display: block;
    max-height: 350px;
  }
  header .menu .more-parent-menu .submenu a {
    font-size: 18px;
    padding-left: 38px;
  }
  .header-ticker {
    position: fixed;
    z-index: 50;
  }
  .header-ticker a {
    font-size: 11px;
  }
  body {
    background-position: center top;
  }
  #main > h1 {
    font-size: 26px;
  }
  .tooltip.sandbox {
    display: none !important;
  }
  .stats-table.sortable-table {
    font-size: 14px;
  }
  .poke {
    width: 49%;
  }
  .poke .poke-stats {
    max-width: 100%;
  }
  .poke input, .poke select, .poke .bar-back {
    max-width: 100%;
  }
  .poke .poke-stats .stat-container .bar-back {
    width: 60px;
  }
  .poke a.search-info {
    display: none;
  }
  .modal .poke {
    width: 100%;
  }
  .rank .details .detail-section.float {
    float: none;
    width: 100%;
  }
  .rank .details .detail-section.float:first-of-type {
    margin: 5px 0 5px 0;
  }
  .rank .details .detail-section.float.margin {
    margin-right: 0;
  }
  .rank .details .detail-section .name {
    font-size: 14px;
  }
  .rank .details .detail-section .archetype .name {
    font-size: 10px;
  }
  .rank .rating-container .rating {
    width: auto !important;
  }
  .modal .search-traits-selector .traits > div,
  .similar-pokemon a, .partner-pokemon a {
    font-size: 12px;
  }
  .histograms {
    display: block;
  }
  .histograms .histogram {
    width: 80%;
    max-width: 600px;
    margin: 20px auto;
  }
  .flex .col-3 {
    flex-basis: 25%;
  }
  .flex .col-9 {
    flex-basis: 75%;
  }
  .league-select,
  .format-select,
  .cup-select,
  .category-select {
    max-width: 48%;
    margin-bottom: 5px;
    font-size: 16px;
  }
  .ranking-categories a {
    font-size: 14px;
    padding: 5px;
  }
  .rank .details .detail-section .moveset .name-container.stats-container > div {
    min-width: auto !important;
  }
  .traits > div {
    font-size: 12px;
  }
}
/* SETTINGS */
.settings select, .settings input {
  max-width: 200px;
}
.settings .button.save {
  display: inline-block;
  margin-top: 30px;
}

/* CUSTOM RANKINGS */
.custom-rankings .league-select {
  margin-bottom: 15px;
}
.custom-rankings .include,
.custom-rankings .exclude {
  margin-bottom: 15px;
}
.custom-rankings .include > h3:first-of-type,
.custom-rankings .exclude > h3:first-of-type {
  margin: 0;
}
.custom-rankings .include > p:first-of-type,
.custom-rankings .exclude > p:first-of-type {
  margin-top: 0;
}
.custom-rankings .include .filter {
  background: #c9ebf5;
}
.custom-rankings .exclude .filter {
  background: #f1b9b9;
}
.custom-rankings .filters {
  margin-bottom: 5px;
}
.custom-rankings .filters .filter {
  padding: 10px;
  border: 2px solid #000;
  border-radius: 12px;
  margin-bottom: 8px;
  position: relative;
}
.custom-rankings .filters .filter .toggle-content {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.25);
}
.custom-rankings .filters .filter .field-container {
  display: flex;
  flex-wrap: wrap;
}
.custom-rankings .filters .filter label {
  font-weight: bold;
  font-size: 14px;
  padding-top: 4px;
  margin-right: 10px;
}
.custom-rankings .filters .filter select {
  max-width: 150px;
}
.custom-rankings .filters .filter .field-section {
  display: none;
}
.custom-rankings .filters .filter .field-section.type .check, .custom-rankings .filters .filter .field-section.tag .check, .custom-rankings .filters .filter .field-section.cost .check, .custom-rankings .filters .filter .field-section.distance .check, .custom-rankings .filters .filter .field-section.move-type .check, .custom-rankings .filters .filter .field-section.evolution .check {
  margin: 10px 5px;
}
.custom-rankings .filters .filter .field-section.type .select-all, .custom-rankings .filters .filter .field-section.tag .select-all, .custom-rankings .filters .filter .field-section.cost .select-all, .custom-rankings .filters .filter .field-section.distance .select-all, .custom-rankings .filters .filter .field-section.move-type .select-all, .custom-rankings .filters .filter .field-section.evolution .select-all {
  margin-right: 10px;
}
.custom-rankings .filters .filter .field-section.dex input {
  max-width: 100px;
  margin-right: 10px;
}
.custom-rankings .filters .filter .remove {
  cursor: pointer;
  color: #ff0000;
  position: absolute;
  top: 10px;
  right: 10px;
}
.custom-rankings .filters .filter[type=type] .field-section.type {
  display: block;
}
.custom-rankings .filters .filter[type=tag] .field-section.tag {
  display: block;
}
.custom-rankings .filters .filter[type=id] .field-section.id {
  display: block;
}
.custom-rankings .filters .filter[type=dex] .field-section.dex {
  display: block;
}
.custom-rankings .filters .filter[type=evolution] .field-section.evolution {
  display: block;
}
.custom-rankings .filters .filter[type=cost] .field-section.cost {
  display: block;
}
.custom-rankings .filters .filter[type=distance] .field-section.distance {
  display: block;
}
.custom-rankings .filters .filter[type=move] .field-section.move {
  display: block;
}
.custom-rankings .filters .filter[type=moveType] .field-section.move-type {
  display: block;
}
.custom-rankings .advanced .flex-section {
  display: flex;
}
.custom-rankings .advanced .flex-section div {
  margin-right: 30px;
}
.custom-rankings .advanced .format-select {
  display: none;
}
.custom-rankings .advanced .cup-select {
  display: block;
  font-size: 14px;
  max-width: 200px;
}
.custom-rankings .advanced select,
.custom-rankings .advanced input {
  max-width: 100px;
}
.custom-rankings .advanced h3 {
  font-size: 14px;
  margin-bottom: 5px;
}
.custom-rankings .import-custom-group {
  margin-top: 15px;
}

.custom-rankings-list {
  display: none;
}
.custom-rankings-list h3:first-of-type {
  margin-top: 0;
}
.custom-rankings-list textarea.pokemon-list {
  width: 100%;
  height: 150px;
  font-family: sans-serif;
}

.custom-rankings-results {
  display: none;
}
.custom-rankings-results h3:first-of-type {
  margin-top: 0;
}

.custom-rankings-overrides .poke.multi,
.custom-rankings-meta-group .poke.multi,
.custom-rankings .poke.multi {
  display: block;
  float: none;
}
.custom-rankings-overrides .poke.multi .poke-stats,
.custom-rankings-meta-group .poke.multi .poke-stats,
.custom-rankings .poke.multi .poke-stats {
  padding: 0;
  background: none;
}
.custom-rankings-overrides .poke.multi .cup-select,
.custom-rankings-overrides .poke.multi .options,
.custom-rankings-meta-group .poke.multi .cup-select,
.custom-rankings-meta-group .poke.multi .options,
.custom-rankings .poke.multi .cup-select,
.custom-rankings .poke.multi .options {
  display: none;
}
.custom-rankings-overrides .poke.multi .custom-options,
.custom-rankings-meta-group .poke.multi .custom-options,
.custom-rankings .poke.multi .custom-options {
  display: block;
}
.custom-rankings-overrides h3,
.custom-rankings-meta-group h3,
.custom-rankings h3 {
  margin-top: 0;
}
.custom-rankings-overrides a.swap,
.custom-rankings-meta-group a.swap,
.custom-rankings a.swap {
  display: none;
}

.custom-rankings-import h3 {
  margin-top: 0;
}
.custom-rankings-import textarea.import {
  width: 100%;
  height: 100px;
}
.custom-rankings-import .copy {
  display: inline-block;
  cursor: pointer;
  font-weight: bold;
  background: #6296be;
  color: #f9fdff;
  padding: 5px;
  border-radius: 8px;
  margin: 5px 0;
}

/* TRAINING ANALYSIS */
.analysis-container .table-container {
  max-height: 500px;
  margin-bottom: 20px;
}
.analysis-container .format-select {
  max-width: 143px;
}
.analysis-container .column-description {
  font-size: 12px;
}
.analysis-container .poke-search-container {
  margin: 15px 0;
}
.analysis-container .sort-category {
  margin: 20px 0 10px 0;
  display: flex;
}
.analysis-container .sort-category div, .analysis-container .sort-category a {
  margin-right: 15px;
}
.analysis-container .sort-category a {
  display: block;
  background: #003462;
  padding: 4px 8px;
  text-decoration: none;
  border-radius: 12px;
  color: #fff;
}
.analysis-container .sort-category a.selected {
  font-weight: bold;
}
.analysis-container .low-volume {
  color: #ff5115;
  font-weight: bold;
}
.analysis-container a.download-csv {
  margin: 0;
  font-size: 14px;
}
.analysis-container .date-updated {
  font-style: italic;
  font-size: 12px;
}
.analysis-container.screenshot .poke-search-container, .analysis-container.screenshot a.download-csv, .analysis-container.screenshot p.column-description {
  display: none;
}

.train-table {
  width: 100%;
  margin-top: 5px;
}
.train-table thead {
  font-size: 12px;
  font-weight: bold;
}
.train-table thead td.poke-name {
  padding: 0;
  width: auto;
}
.train-table thead a {
  text-decoration: none;
}
.train-table thead a.selected, .train-table thead a:hover {
  text-decoration: underline;
}
.train-table td.poke-name {
  display: flex;
  padding: 6px 2px;
  width: 125px;
  vertical-align: top;
}
.train-table td.poke-name .sprite-container {
  width: 30px;
  height: 30px;
  position: relative;
  background: none;
  border: none;
  margin: 0 5px 0 0;
  padding: 0;
  flex-shrink: 0;
}
.train-table td.poke-name .number {
  padding-right: 10px;
}
.train-table td.poke-name .name {
  font-size: 14px;
  font-weight: bold;
  max-width: 100px;
}
.train-table td.poke-name .moves {
  font-size: 12px;
}
.train-table td.poke-name .team-member {
  display: flex;
}
.train-table td.individual-score,
.train-table td.team-score,
.train-table td.usage {
  padding: 2px 16px 2px 0;
  font-weight: bold;
  font-size: 14px;
  line-height: 0;
}
.train-table td.team-score .score {
  background-image: url("../img/starburst_red.png");
  background-color: rgb(52, 102, 174);
  background-size: 25px;
  background-repeat: no-repeat;
  background-position: 4px center;
  padding-left: 30px;
  line-height: 25px;
  color: #fff6d4;
  display: inline-block;
  padding: 4px 4px 4px 30px;
  border-radius: 12px;
  min-width: 40px;
  text-align: center;
}
.train-table td.team-score .score.win {
  background-image: url("../img/starburst.png");
}
.train-table td.link a {
  display: block;
  width: 32px;
  height: 28px;
  background-image: url(../img/eye.png);
  background-repeat: no-repeat;
  background-position: 0 center;
  margin-right: 10px;
  opacity: 0.5;
}
.train-table tr:nth-child(2n) {
  background: rgba(0, 0, 0, 0.15);
}
.train-table a.usage-link {
  display: inline-block;
  width: 20px;
  height: 18px;
  background-image: url(../img/usage-link.png);
  background-repeat: no-repeat;
  background-position: 0 center;
  margin-left: 10px;
}
.train-table span.usage-value {
  display: block;
  width: 35px;
}
.train-table .usage .flex {
  align-items: center;
}
.train-table .rating {
  width: 80px;
  padding: 5px;
  margin: 0;
  box-sizing: border-box;
}
.train-table .rating span {
  margin: 0 6px 0 0;
}

.usage-modal .usage-container {
  display: flex;
}
.usage-modal .usage-container .y-axis-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 5px;
  box-sizing: border-box;
  width: 7%;
}
.usage-modal .usage-container .y-axis-container .label {
  writing-mode: vertical-rl;
  transform: rotate(180deg);
}
.usage-modal .usage-container .y-axis-container .value {
  font-size: 11px;
}
.usage-modal .x-axis-container {
  margin-left: 7%;
  margin-top: 5px;
  text-align: center;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.usage-modal .x-axis-container .value {
  font-size: 11px;
}
.usage-modal .canvas-container {
  position: relative;
  width: 92%;
}
.usage-modal .usage-chart {
  display: block;
  max-width: 100%;
  border: 1px dashed #003462;
  border-radius: 8px;
}
.usage-modal .usage-chart[canvas-id="0"] {
  background: rgba(0, 52, 98, 0.025);
}
.usage-modal .usage-chart[canvas-id="1"] {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.8;
}
.usage-modal .usage-compare-select {
  max-width: 175px;
  margin-bottom: 10px;
}
.usage-modal .usage-legend {
  display: inline-block;
  border-top: 4px solid #000;
  width: 25px;
  height: 4px;
  margin-right: 3px;
}
.usage-modal .usage-legend.dashed {
  border-style: dashed;
  border-bottom-color: rgba(0, 0, 0, 0);
  border-left-color: rgba(0, 0, 0, 0);
  border-right-color: rgba(0, 0, 0, 0);
}

/* RSS Feed */
.feed-container {
  position: relative;
  margin: 0 auto 10px auto;
  height: 150px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.05);
  -webkit-transition: all 0.25s ease-out 0s;
  -moz-transition: all 0.25s ease-out 0s;
  -o-transition: all 0.25s ease-out 0s;
  transition: all 0.25s ease-out 0s;
}
.feed-container:after {
  content: " ";
  width: 140%;
  height: 100px;
  display: block;
  position: absolute;
  bottom: 0;
  box-shadow: inset 0 -14px 10px;
  pointer-events: none;
  left: -25%;
  opacity: 0.15;
  display: none;
}
.feed-container.expanded {
  height: 400px;
}
.feed-container.expanded .feed {
  height: 100%;
  overflow-y: auto;
}
.feed-container.expanded .feed-expand:after {
  content: "∧";
}
.feed-container .feed {
  padding: 8px;
  position: relative;
  height: 150px;
  overflow: hidden;
  -webkit-transition: all 0.25s ease-out 0s;
  -moz-transition: all 0.25s ease-out 0s;
  -o-transition: all 0.25s ease-out 0s;
  transition: all 0.25s ease-out 0s;
}
.feed-container .feed-expand {
  cursor: pointer;
  background: #f8fbfe;
  color: #003462;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.3);
  width: 50px;
  height: 50px;
  border-radius: 50px;
  position: absolute;
  bottom: -24px;
  left: calc(50% - 25px);
  font-size: 18px;
  font-weight: bold;
  border: 2px solid #003462;
  display: none;
}
.feed-container .feed-expand:after {
  content: "∨";
}

a.feed-subscribe {
  display: block;
  color: #003462;
  font-size: 12px;
  text-align: right;
}

.feed .news-item {
  font-size: 14px;
  line-height: 18px;
  padding: 15px 0;
  border-radius: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}
.feed .news-item:first-of-type {
  padding-top: 0;
}
.feed .news-item:last-of-type {
  border-bottom: none;
}
.feed .news-item h4 {
  margin-top: 0;
  font-size: 14px;
}
.feed .news-item .news-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.75);
  margin-top: 10px;
}
.feed .news-item .news-info a {
  display: flex;
  color: #f9fdff;
  padding: 2px 10px;
  text-decoration: none;
  justify-content: space-between;
  border-radius: 12px;
  border: 1px solid #000;
  background: #003462; /* Old browsers */
  background: -moz-linear-gradient(top, #285e85 30%, #003462 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #285e85 30%, #003462 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #285e85 30%, #003462 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.feed .news-item .news-info a .link-text {
  text-decoration: underline;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 145px;
}

.latest-section {
  padding: 9px 8px 9px 8px;
  font-size: 14px;
}
.latest-section h4 {
  display: flex;
  justify-content: space-between;
  margin: 0;
  border-bottom: 1px solid #5896ce;
  color: #558ec2;
}
.latest-section h4 a {
  display: block;
  width: 14px;
  height: 14px;
  background-image: url("../img/themes/sunflower/rss-white.png");
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 14px 14px;
  border-radius: 0;
  padding: 0 !important;
}
.latest-section .date {
  font-size: 12px;
  color: #558ec2;
  padding-left: 38px;
}
.latest-section a {
  font-size: 14px !important;
  padding-bottom: 5px !important;
  line-height: 20px;
}

@media only screen and (min-width: 481px) {
  .home a.button h2 {
    font-size: 32px;
  }
}
@media only screen and (min-width: 721px) {
  header .menu {
    position: relative;
  }
  header .menu .menu-content {
    display: flex;
    align-items: center;
  }
  header .menu .more {
    font-size: 40px;
    font-family: serif;
    line-height: 0;
    padding: 0;
  }
  header .menu .more:after {
    content: "...";
  }
  header .menu .parent-menu {
    position: relative;
  }
  header .menu .parent-menu.more-parent-menu > a {
    background: none !important;
  }
  header .menu .parent-menu.more-parent-menu > a:after {
    display: none;
  }
  header .menu .parent-menu.more-parent-menu .safe-mouse-space {
    position: absolute;
    width: 150%;
    height: 100%;
    left: 95%;
    top: 0;
  }
  header .menu .parent-menu .submenu .icon-rankings {
    font-size: 14px;
  }
  header .menu .parent-menu > a span {
    right: auto !important;
    left: 50% !important;
    bottom: -4px !important;
    top: auto !important;
    padding: 0 !important;
    background: url("../img/themes/sunflower/dropdown-triangle.png");
  }
  header .menu .menu-content > a:hover, header .menu .menu-content > .parent-menu:hover > a {
    background-color: #c4def5;
    -webkit-transition: background-color 0s ease-out 0s;
    -moz-transition: background-color 0s ease-out 0s;
    -o-transition: background-color 0s ease-out 0s;
    transition: background-color 0s ease-out 0s;
  }
  header .menu .submenu {
    padding-top: 15px;
    position: absolute;
    padding-left: 20px;
    top: 18px;
    pointer-events: none;
    white-space: nowrap;
  }
  header .menu .submenu.active {
    padding-bottom: 100px;
    padding-right: 50px;
  }
  header .menu .submenu .submenu-wrap {
    padding: 2px 5px;
    background: #003462;
    border-radius: 0 0 8px 8px;
    box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.2);
  }
  header .menu .submenu a {
    float: none;
    margin: 0;
    color: #f9fdff;
    font-size: 14px;
    padding: 10px 8px;
    -webkit-transition: color 0.2s ease-out 0s;
    -moz-transition: color 0.2s ease-out 0s;
    -o-transition: color 0.2s ease-out 0s;
    transition: color 0.2s ease-out 0s;
  }
  header .menu .submenu a:hover {
    color: #c4def5;
    -webkit-transition: background-color 0s ease-out 0s;
    -moz-transition: background-color 0s ease-out 0s;
    -o-transition: background-color 0s ease-out 0s;
    transition: background-color 0s ease-out 0s;
  }
  .rank .details .detail-section.overall .rating-container .ranking-header {
    font-size: 16px;
  }
  .rank .details .detail-section .weaknesses .type div:nth-of-type(2),
  .rank .details .detail-section .resistances .type div:nth-of-type(2) {
    font-size: 16px;
    line-height: 16px;
  }
  .rankings-container {
    max-height: 1000px;
  }
  .rankings-container.pokebox-list {
    max-height: 600px;
  }
  .table-container {
    overflow-x: auto;
  }
  .article .threats-table .name,
  .article .alternatives-table .name,
  .article .meta-table .name {
    font-size: 16px;
  }
  a.download-csv {
    display: inline-block;
  }
  .battle-results.matrix .table-container {
    max-height: 800px;
  }
  .analysis-container .table-container {
    max-height: 600px;
  }
  .train-table td.poke-name {
    padding: 8px 4px;
    width: 200px;
  }
  .train-table td.poke-name .name {
    font-size: 16px;
    max-width: none;
  }
  .train-table.teams td.poke-name {
    display: table-cell;
  }
  .train-table td.individual-score,
  .train-table td.team-score,
  .train-table td.usage {
    font-size: 20px;
    padding: 4px 20px 4px 0;
  }
  .train-table td.individual-score .rating,
  .train-table td.team-score .rating,
  .train-table td.usage .rating {
    min-width: 90px;
  }
  .train-table span.usage-value {
    width: 55px;
  }
  .analysis-container .format-select {
    max-width: 100%;
  }
}
.colorblind a.rating.win, .colorblind .rating-container .rating.win, .colorblind .summary .rating.win, .colorblind .stats-table .rating.win, .colorblind .train-table .rating.win,
.colorblind .battle-results.matrix .difference-table a.difference.win {
  background: #1A85FF;
}
.colorblind a.rating.close-win, .colorblind .rating-container .rating.close-win, .colorblind .summary .rating.close-win, .colorblind .stats-table .rating.close-win, .colorblind .train-table .rating.close-win {
  background: #3176e9;
}
.colorblind a.rating.close-loss, .colorblind .rating-container .rating.close-loss, .colorblind .summary .rating.close-loss, .colorblind .stats-table .rating.close-loss, .colorblind .train-table .rating.close-loss {
  background: #c71965;
  color: #ffe4f8;
}
.colorblind a.rating.close-loss span, .colorblind .rating-container .rating.close-loss span, .colorblind .summary .rating.close-loss span, .colorblind .stats-table .rating.close-loss span, .colorblind .train-table .rating.close-loss span {
  background-image: url("../img/themes/sunflower/rating-close-loss-dark.png?v=7");
  opacity: 0.7;
}
.colorblind a.rating.loss, .colorblind .rating-container .rating.loss, .colorblind .summary .rating.loss, .colorblind .stats-table .rating.loss, .colorblind .train-table .rating.loss {
  background: #D41159;
  color: #ffe4f8;
}
.colorblind a.rating.loss span, .colorblind .rating-container .rating.loss span, .colorblind .summary .rating.loss span, .colorblind .stats-table .rating.loss span, .colorblind .train-table .rating.loss span {
  background-image: url("../img/themes/sunflower/rating-loss-dark.png?v=7");
  opacity: 1;
}
.colorblind .battle-results.matrix .difference-table a.difference.lose {
  background: #D41159;
  color: #ffe4f8;
}

#dev-panel {
  width: 300px;
  position: fixed;
  right: -320px;
  top: 100px;
  background: #01475f;
  z-index: 1000;
  padding: 10px;
  -webkit-transition: right 0.2s ease-out 0s;
  -moz-transition: right 0.2s ease-out 0s;
  -o-transition: right 0.2s ease-out 0s;
  transition: right 0.2s ease-out 0s;
}
#dev-panel.active {
  right: 0px;
}
#dev-panel a.wrench {
  width: 50px;
  height: 50px;
  background: url("../articles/article-assets/developer-notes-1-27-0/thumb.jpg");
  background-size: 100%;
  display: block;
  position: absolute;
  left: -50px;
  top: 0;
}
#dev-panel a {
  display: block;
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;
}
#dev-panel h3 {
  color: #eee;
  margin-top: 0;
}
#dev-panel h4 {
  color: #ccc;
  border-bottom: 1px solid #bbb;
  margin-bottom: 0;
}

@media only screen and (max-width: 480px) {
  #dev-panel {
    display: none;
  }
}
/* Ad styling and display */
#nitro-header-mobile,
#nitro-body-mobile,
#nitro-body-desktop,
#nitro-body-mobile,
#nitro-sidebar-right,
#nitro-sidebar-right-300,
#nitro-sidebar-left,
#nitro-sidebar-left-300 {
  display: none;
}

#nitro-header-mobile {
  overflow: hidden;
}

.mobile-ad-container,
.desktop-ad-container {
  overflow: hidden;
  clip-path: inset(0 0 0 0);
  margin: 0 auto 5px auto;
  display: none;
}

.section.battle .mobile-ad-container {
  margin: 10px auto 5px auto;
}

iframe#bh-div {
  display: none !important;
}

@media only screen and (min-width: 420px) {
  .rank .details .detail-tab-nav a {
    font-size: 14px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 767px) {
  .nitro-pin-container {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 25;
  }
  #nitro-header-mobile {
    display: block;
    margin: 0 auto;
  }
  #nitro-body-mobile {
    display: block;
    margin-top: 30px;
  }
  .mobile-ad-container {
    display: block;
    width: 320px;
    height: 70px;
  }
}
@media only screen and (min-width: 768px) {
  #nitro-header-desktop {
    display: block;
  }
  #nitro-body-desktop {
    display: block;
    margin-top: 30px;
  }
  .desktop-ad-container {
    display: block;
    width: 728px;
    height: 110px;
    margin: 20px auto;
  }
  .desktop-ad-container.nitro-header {
    margin: 0 auto;
  }
}
@media only screen and (min-width: 1190px) and (max-width: 1449px) {
  #nitro-sidebar-left {
    display: block;
    position: fixed;
    top: 95px;
    left: 20px;
  }
  #nitro-sidebar-right {
    display: block;
    position: fixed;
    top: 95px;
    right: 20px;
  }
}
@media only screen and (min-width: 1450px) {
  #nitro-sidebar-left-300 {
    display: block;
    position: fixed;
    top: 95px;
    left: 20px;
  }
  #nitro-sidebar-right-300 {
    display: block;
    position: fixed;
    top: 95px;
    right: 20px;
  }
}
/* Printing for meta scorecard */
@media print {
  .background {
    display: none;
  }
  .scorecard-print .section {
    display: none !important;
  }
  .scorecard-print .section.typings {
    display: block !important;
  }
  .scorecard-print .button {
    display: none !important;
  }
  .scorecard-print .toggle,
  .scorecard-print .toggle-content {
    display: none !important;
  }
  .scorecard-print .toggle-content:nth-of-type(1) {
    display: block !important;
  }
  .scorecard-print .article .rating-table a {
    background: none;
    padding: 2px 5px;
  }
  .scorecard-print .article .rating-table a.close-win span {
    border: 2px solid #1b79b4;
  }
  .scorecard-print .article .rating-table a.win span {
    background: #1b79b4;
  }
  .scorecard-print .article .rating-table a.tie span {
    color: #aaa;
  }
  .scorecard-print .article .rating-table a span {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  .scorecard-print .rating-table.legend {
    font-size: 12px;
  }
  .scorecard-print #main h1 {
    display: none;
  }
  .scorecard-print #main p {
    display: none;
  }
  .scorecard-print header, .scorecard-print footer {
    display: none;
  }
  .scorecard-print .share-link-container {
    display: none;
  }
  .scorecard-print .article table tr:nth-of-type(2n) {
    background: rgba(0, 0, 0, 0.07);
    -webkit-print-color-adjust: exact;
  }
  .table-container {
    overflow-x: auto;
  }
}

/*# sourceMappingURL=style.css.map */
/* Matrix State UI Indicators */
.battle-results.matrix .matrix-state-indicator {
  background: #f9fdff;
  border: 1px solid #c4def5;
  border-radius: 8px;
  padding: 10px 15px;
  margin: 10px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.battle-results.matrix .matrix-state-indicator .saved-state-icon {
  font-size: 16px;
}

.battle-results.matrix .matrix-state-indicator .saved-state-text {
  flex: 1;
  color: #003462;
  font-weight: bold;
}

.battle-results.matrix .matrix-state-indicator .clear-state-btn {
  background: #ff4444;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.battle-results.matrix .matrix-state-indicator .clear-state-btn:hover {
  background: #cc0000;
}

.battle-results.matrix .matrix-share-container {
  background: #f9fdff;
  border: 1px solid #c4def5;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
}

.battle-results.matrix .matrix-share-container p {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #003462;
  font-weight: bold;
}

.battle-results.matrix .matrix-share-container .share-link {
  display: flex;
  gap: 5px;
}

.battle-results.matrix .matrix-share-container .share-link input {
  flex: 1;
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  background: #fff;
}

.battle-results.matrix .matrix-share-container .share-link .copy {
  background: #1073d3;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.battle-results.matrix .matrix-share-container .share-link .copy:hove
/
* Matrix Notification Styles */
.matrix-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  word-wrap: break-word;
  display: none;
}

.matrix-notification.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.matrix-notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.matrix-notification.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.matrix-notification.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

@media only screen and (max-width: 480px) {
  .matrix-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}