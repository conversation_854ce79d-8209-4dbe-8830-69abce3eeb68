# PvPoke Battle Engine Extraction Guide for React Native

## Executive Summary

This guide provides a comprehensive analysis for extracting PvPoke's core battle simulation logic into a React Native mobile app using NativeWind for styling. The focus is on the battle calculator that determines win/loss/tie outcomes and battle ratings between two Pokemon.

## 1. Core Battle Simulation Architecture

### 1.1 Key JavaScript Files to Extract

**Primary Battle Engine:**
- `src/js/battle/Battle.js` (1,868 lines) - Main battle simulation loop
- `src/js/battle/DamageCalculator.js` (93 lines) - Damage calculation formulas
- `src/js/pokemon/Pokemon.js` (2,382 lines) - Pokemon class with stats and battle methods
- `src/js/GameMaster.js` (962 lines) - Data loading and Pokemon/move management

**Supporting Files:**
- `src/js/battle/rankers/Ranker.js` - Battle rating calculations
- `src/js/battle/actions/ActionLogic.js` - Move decision logic
- `src/data/gamemaster.json` - Pokemon and move data (20MB+ JSON file)

### 1.2 Core Battle Flow

```javascript
// Simplified battle simulation flow
1. Initialize Battle() instance
2. Set Pokemon with battle.setNewPokemon(pokemon1, 0) and battle.setNewPokemon(pokemon2, 1)
3. Configure battle parameters (CP limit, shields, energy)
4. Call battle.simulate() which:
   - Runs main battle loop: while(pokemon[0].hp > 0 && pokemon[1].hp > 0)
   - Each turn: battle.step() processes actions for both Pokemon
   - Calculates damage using DamageCalculator.damage()
   - Applies type effectiveness, STAB, and stat modifiers
   - Handles energy gain/loss and shield usage
5. Determine winner based on battle ratings: pokemon.getBattleRating()
```

## 2. Battle Rating System

### 2.1 Rating Calculation Formula

```javascript
// From Pokemon.js line 2016
getBattleRating() {
    var opponent = battle.getOpponent(this.index);
    return Math.floor(
        (500 * ((opponent.stats.hp - opponent.hp) / opponent.stats.hp)) + 
        (500 * (this.hp / this.stats.hp))
    );
}
```

**Rating Scale:**
- **1000**: Perfect win (opponent fainted, full HP remaining)
- **750-999**: Win 
- **500-749**: Close win
- **500**: Tie (both faint simultaneously)
- **250-499**: Close loss
- **1-249**: Loss
- **0**: Perfect loss (fainted, opponent at full HP)

### 2.2 Win/Loss Determination

```javascript
// From Battle.js lines 667-690
if(battleRatings[0] > battleRatings[1]){
    winner = { pokemon: pokemon[0], rating: battleRatings[0] };
} else if(battleRatings[1] > battleRatings[0]){
    winner = { pokemon: pokemon[1], rating: battleRatings[1] };
} else {
    winner = { pokemon: false, rating: battleRatings[0] }; // Tie
}
```

## 3. Damage Calculation Engine

### 3.1 Core Damage Formula

```javascript
// From DamageCalculator.js line 36
static damage(attacker, defender, move, charge = 1) {
    var effectiveness = defender.typeEffectiveness[move.type];
    var damage = Math.floor(
        move.power * 
        move.stab * 
        (attacker.getEffectiveStat(0) / defender.getEffectiveStat(1)) * 
        effectiveness * 
        charge * 
        0.5 * 
        DamageMultiplier.BONUS
    ) + 1;
    return damage;
}
```

### 3.2 Damage Multipliers

```javascript
// From DamageCalculator.js lines 2-10
class DamageMultiplier {
    static BONUS = 1.2999999523162841796875;
    static SUPER_EFFECTIVE = 1.60000002384185791015625;
    static RESISTED = 0.625;
    static DOUBLE_RESISTED = 0.390625;
    static STAB = 1.2000000476837158203125; // Same Type Attack Bonus
    static SHADOW_ATK = 1.2;
    static SHADOW_DEF = 0.83333331;
}
```

## 4. Data Requirements

### 4.1 Essential Data from gamemaster.json

**Pokemon Data Structure:**
```json
{
    "dex": 1,
    "speciesName": "Bulbasaur",
    "speciesId": "bulbasaur",
    "baseStats": {"atk": 118, "def": 111, "hp": 128},
    "types": ["grass", "poison"],
    "fastMoves": ["vine_whip", "tackle"],
    "chargedMoves": ["sludge_bomb", "seed_bomb", "power_whip"],
    "released": true
}
```

**Move Data Structure:**
```json
{
    "moveId": "vine_whip",
    "name": "Vine Whip",
    "type": "grass",
    "power": 7,
    "energy": 6,
    "energyGain": 6,
    "cooldown": 2500,
    "archetype": "Fast"
}
```

### 4.2 Type Effectiveness Matrix

The battle engine requires a complete type effectiveness chart for calculating damage multipliers. This is embedded in the Pokemon class via `getTypeEffectivenessArray()`.

## 5. React Native Architecture Design

### 5.1 Recommended Project Structure

```
src/
├── components/
│   ├── PokemonSelector.tsx     # Pokemon selection interface
│   ├── BattleResults.tsx       # Display battle outcomes
│   └── BattleSimulator.tsx     # Main battle interface
├── engine/
│   ├── Battle.ts               # Ported battle simulation
│   ├── Pokemon.ts              # Pokemon class with battle methods
│   ├── DamageCalculator.ts     # Damage calculation utilities
│   └── GameMaster.ts           # Data management
├── data/
│   ├── gamemaster.json         # Pokemon/move data
│   └── typeChart.json          # Type effectiveness matrix
└── utils/
    ├── dataLoader.ts           # Async data loading
    └── storage.ts              # Local data caching
```

### 5.2 Core Components with NativeWind

**Pokemon Selector Component:**
```tsx
import { View, Text, FlatList, TouchableOpacity } from 'react-native';

export const PokemonSelector = ({ onSelect }) => {
  return (
    <View className="flex-1 bg-gray-100 p-4">
      <Text className="text-2xl font-bold text-center mb-4">
        Select Pokemon
      </Text>
      <FlatList
        data={pokemonList}
        renderItem={({ item }) => (
          <TouchableOpacity 
            className="bg-white p-4 mb-2 rounded-lg shadow-sm flex-row items-center"
            onPress={() => onSelect(item)}
          >
            <Text className="text-lg font-semibold">{item.speciesName}</Text>
            <View className="flex-row ml-auto">
              {item.types.map(type => (
                <View key={type} className={`px-2 py-1 rounded ml-1 ${getTypeColor(type)}`}>
                  <Text className="text-white text-xs uppercase">{type}</Text>
                </View>
              ))}
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
};
```

**Battle Results Component:**
```tsx
export const BattleResults = ({ winner, pokemon1, pokemon2, rating }) => {
  const getRatingColor = (rating: number) => {
    if (rating >= 750) return 'bg-green-500';
    if (rating >= 500) return 'bg-yellow-500';
    if (rating === 500) return 'bg-gray-500';
    return 'bg-red-500';
  };

  return (
    <View className="bg-white p-6 rounded-lg shadow-lg m-4">
      <Text className="text-2xl font-bold text-center mb-4">Battle Results</Text>
      
      <View className="flex-row justify-between items-center mb-4">
        <PokemonCard pokemon={pokemon1} />
        <Text className="text-lg font-bold">VS</Text>
        <PokemonCard pokemon={pokemon2} />
      </View>

      <View className={`p-4 rounded-lg ${getRatingColor(rating)}`}>
        <Text className="text-white text-center text-xl font-bold">
          {winner ? `${winner.speciesName} Wins!` : 'Tie!'}
        </Text>
        <Text className="text-white text-center text-lg">
          Rating: {rating}
        </Text>
      </View>
    </View>
  );
};
```

## 6. Technical Feasibility Assessment

### 6.1 Porting Complexity: **Medium-High**

**Challenges:**
- **Large Codebase**: 5,000+ lines of battle logic to port
- **Complex Dependencies**: Intricate relationships between Battle, Pokemon, and GameMaster classes
- **Floating Point Precision**: Damage calculations use specific floating-point constants
- **State Management**: Battle simulation maintains complex state across multiple turns

**Advantages:**
- **Pure Logic**: No DOM manipulation, mostly mathematical calculations
- **Well-Structured**: Clear separation between data, battle logic, and calculations
- **No External APIs**: All calculations are client-side

### 6.2 Performance Considerations

**Mobile Optimization Strategies:**
```typescript
// Lazy load Pokemon data
const loadPokemonData = async () => {
  const data = await AsyncStorage.getItem('gamemaster');
  if (!data) {
    const response = await fetch('gamemaster.json');
    const gamemaster = await response.json();
    await AsyncStorage.setItem('gamemaster', JSON.stringify(gamemaster));
    return gamemaster;
  }
  return JSON.parse(data);
};

// Optimize battle calculations for mobile
class MobileBattle extends Battle {
  simulate() {
    // Add performance monitoring
    const startTime = performance.now();
    const result = super.simulate();
    const endTime = performance.now();
    console.log(`Battle simulation took ${endTime - startTime}ms`);
    return result;
  }
}
```

### 6.3 Data Loading Strategy

**Recommended Approach:**
1. **Bundle Essential Data**: Include top 100 Pokemon in app bundle
2. **Lazy Load Full Dataset**: Download complete gamemaster.json on first launch
3. **Incremental Updates**: Check for data updates periodically
4. **Offline Support**: Cache all data locally for offline battles

```typescript
// Data loading implementation
export class DataManager {
  private static instance: DataManager;
  private gamemaster: any = null;

  static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  async loadGameMaster(): Promise<any> {
    if (this.gamemaster) return this.gamemaster;
    
    try {
      // Try to load from local storage first
      const cached = await AsyncStorage.getItem('gamemaster');
      if (cached) {
        this.gamemaster = JSON.parse(cached);
        return this.gamemaster;
      }
      
      // Fallback to bundled data
      const bundled = require('../data/gamemaster.json');
      this.gamemaster = bundled;
      return this.gamemaster;
    } catch (error) {
      console.error('Failed to load game master data:', error);
      throw error;
    }
  }
}
```

## 7. MVP Implementation Roadmap

### Phase 1: Core Engine (2-3 weeks)
- [ ] Port `DamageCalculator.ts` with all multipliers and formulas
- [ ] Create `Pokemon.ts` class with essential battle methods
- [ ] Implement basic `Battle.ts` simulation loop
- [ ] Add type effectiveness calculations

### Phase 2: Data Integration (1-2 weeks)
- [ ] Create `GameMaster.ts` for data management
- [ ] Implement Pokemon/move data loading
- [ ] Add local storage caching
- [ ] Create data validation utilities

### Phase 3: UI Components (2-3 weeks)
- [ ] Build Pokemon selector with search/filter
- [ ] Create battle configuration screen (CP, shields)
- [ ] Implement battle results display
- [ ] Add battle history tracking

### Phase 4: Polish & Optimization (1-2 weeks)
- [ ] Performance optimization for mobile
- [ ] Error handling and edge cases
- [ ] Offline support
- [ ] App store preparation

**Total Estimated Development Time: 6-10 weeks**

## 8. Key Success Factors

1. **Accuracy First**: Ensure battle calculations match PvPoke exactly
2. **Performance**: Optimize for mobile devices with limited resources
3. **User Experience**: Simple, intuitive interface for quick battles
4. **Data Management**: Efficient loading and caching of large datasets
5. **Offline Support**: Full functionality without internet connection

## 9. Critical Implementation Details

### 9.1 Exact Damage Formula Porting

**Critical Constants (must be exact):**
```typescript
// These floating-point values MUST match exactly for accurate calculations
export const DAMAGE_MULTIPLIERS = {
  BONUS: 1.2999999523162841796875,
  SUPER_EFFECTIVE: 1.60000002384185791015625,
  RESISTED: 0.625,
  DOUBLE_RESISTED: 0.390625,
  STAB: 1.2000000476837158203125,
  SHADOW_ATK: 1.2,
  SHADOW_DEF: 0.83333331
} as const;
```

### 9.2 CP Calculation Formula

```typescript
// From Pokemon.js line 366 - Critical for accurate Pokemon stats
calculateCP(cpm: number, atkIV: number, defIV: number, hpIV: number): number {
  return Math.floor(
    ((this.baseStats.atk + atkIV) *
     Math.pow(this.baseStats.def + defIV, 0.5) *
     Math.pow(this.baseStats.hp + hpIV, 0.5) *
     Math.pow(cpm, 2)) / 10
  );
}
```

### 9.3 Battle State Management

```typescript
interface BattleState {
  pokemon: [Pokemon, Pokemon];
  time: number;
  turns: number;
  phase: 'neutral' | 'charging' | 'switching' | 'game_paused';
  timeline: BattleEvent[];
  winner: Pokemon | null;
  battleRatings: [number, number];
}

class MobileBattle {
  private state: BattleState;

  simulate(): BattleResult {
    this.initializeBattle();

    while (this.shouldContinueBattle()) {
      this.processStep();

      // Mobile-specific: yield control periodically
      if (this.state.turns % 100 === 0) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    return this.calculateResult();
  }
}
```

### 9.4 Memory Management for Mobile

```typescript
// Optimize for mobile memory constraints
export class OptimizedGameMaster {
  private pokemonCache = new Map<string, Pokemon>();
  private readonly MAX_CACHE_SIZE = 50; // Limit cached Pokemon

  getPokemon(id: string): Pokemon {
    if (this.pokemonCache.has(id)) {
      return this.pokemonCache.get(id)!;
    }

    if (this.pokemonCache.size >= this.MAX_CACHE_SIZE) {
      // Remove oldest entry
      const firstKey = this.pokemonCache.keys().next().value;
      this.pokemonCache.delete(firstKey);
    }

    const pokemon = this.createPokemon(id);
    this.pokemonCache.set(id, pokemon);
    return pokemon;
  }
}
```

## 10. Testing Strategy

### 10.1 Battle Accuracy Validation

```typescript
// Test against known PvPoke results
const VALIDATION_BATTLES = [
  {
    pokemon1: { id: 'azumarill', cp: 1500, ivs: [8, 15, 15] },
    pokemon2: { id: 'registeel', cp: 1500, ivs: [0, 15, 14] },
    expectedRating: 245, // Known PvPoke result
    expectedWinner: 'registeel'
  },
  // Add more validation cases
];

describe('Battle Engine Accuracy', () => {
  test.each(VALIDATION_BATTLES)('Battle: $pokemon1.id vs $pokemon2.id',
    async ({ pokemon1, pokemon2, expectedRating, expectedWinner }) => {
      const battle = new MobileBattle();
      const result = await battle.simulate(pokemon1, pokemon2);

      expect(result.rating).toBeCloseTo(expectedRating, 0);
      expect(result.winner?.id).toBe(expectedWinner);
    }
  );
});
```

### 10.2 Performance Benchmarks

```typescript
// Mobile performance targets
const PERFORMANCE_TARGETS = {
  BATTLE_SIMULATION_MS: 100,    // Max time for single battle
  DATA_LOAD_MS: 2000,           // Max time to load gamemaster
  MEMORY_USAGE_MB: 50,          // Max memory footprint
  BATTERY_DRAIN_PERCENT: 1      // Max battery per 100 battles
};
```

## 11. Deployment Considerations

### 11.1 App Store Optimization

**Bundle Size Management:**
- Core app: ~15MB (essential Pokemon data)
- Full dataset: Download on demand (~25MB)
- Incremental updates: Only changed data

**Platform-Specific Optimizations:**
```typescript
// iOS-specific optimizations
if (Platform.OS === 'ios') {
  // Use iOS-optimized data structures
  // Leverage Metal Performance Shaders for calculations
}

// Android-specific optimizations
if (Platform.OS === 'android') {
  // Use Android-optimized memory management
  // Leverage RenderScript for parallel calculations
}
```

### 11.2 Legal Considerations

**Important:** This implementation should:
- Use only publicly available game data
- Implement original calculation logic (not copy PvPoke code)
- Credit PvPoke as inspiration
- Ensure compliance with Pokemon GO Terms of Service

## 12. Alternative Approaches

### 12.1 Hybrid Web Approach

Instead of full native port, consider:
```typescript
// Embed PvPoke's battle engine in WebView
import { WebView } from 'react-native-webview';

const BattleWebView = () => (
  <WebView
    source={{ uri: 'file:///battle-engine.html' }}
    onMessage={handleBattleResult}
    injectedJavaScript={`
      // Inject battle parameters
      window.simulateBattle(pokemon1, pokemon2);
    `}
  />
);
```

**Pros:** Faster development, guaranteed accuracy
**Cons:** Performance overhead, less native feel

### 12.2 Server-Side API

```typescript
// Offload calculations to server
const simulateBattle = async (pokemon1: Pokemon, pokemon2: Pokemon) => {
  const response = await fetch('/api/battle', {
    method: 'POST',
    body: JSON.stringify({ pokemon1, pokemon2 })
  });
  return response.json();
};
```

**Pros:** Always up-to-date, reduced app complexity
**Cons:** Requires internet, server costs, latency

## Conclusion

Extracting PvPoke's battle simulation logic for a React Native app is technically feasible but requires careful attention to calculation accuracy and mobile performance optimization. The core battle engine is well-structured and can be successfully ported to TypeScript/React Native with proper planning and execution.

**Key Success Factors:**
1. **Mathematical Precision**: Exact replication of damage formulas and constants
2. **Mobile Performance**: Optimized for limited CPU/memory resources
3. **Data Management**: Efficient loading and caching strategies
4. **User Experience**: Simple, fast interface for quick battle simulations
5. **Accuracy Validation**: Comprehensive testing against known PvPoke results

The MVP should focus on the essential battle simulation functionality while maintaining the mathematical precision that makes PvPoke's calculations trusted by the Pokemon GO PvP community. With proper implementation, this mobile app could provide offline battle simulation capabilities that match PvPoke's accuracy.
