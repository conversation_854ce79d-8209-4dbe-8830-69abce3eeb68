[{"speciesId": "hippopotas", "speciesName": "Hippopotas", "rating": 621, "matchups": [{"opponent": "bastiodon", "rating": 838, "opRating": 161}, {"opponent": "vespiquen", "rating": 657, "opRating": 342}, {"opponent": "lickilicky", "rating": 625, "opRating": 375}, {"opponent": "qwilfish_his<PERSON>an", "rating": 546, "opRating": 453}, {"opponent": "drapion_shadow", "rating": 513}], "counters": [{"opponent": "dusknoir_shadow", "rating": 283}, {"opponent": "drifb<PERSON>", "rating": 327}, {"opponent": "gastrodon", "rating": 345}, {"opponent": "gliscor", "rating": 349}, {"opponent": "spiritomb", "rating": 423}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9938}, {"moveId": "SAND_ATTACK", "uses": 18889}, {"moveId": "BITE", "uses": 8656}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 13897}, {"moveId": "RETURN", "uses": 2675}, {"moveId": "DIG", "uses": 11328}, {"moveId": "BODY_SLAM", "uses": 9561}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 100, "stats": {"product": 1956, "atk": 115.1, "def": 111.7, "hp": 152}}, {"speciesId": "empoleon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 597, "matchups": [{"opponent": "abomasnow_shadow", "rating": 758}, {"opponent": "bastiodon", "rating": 750}, {"opponent": "gliscor", "rating": 668}, {"opponent": "drapion_shadow", "rating": 577}, {"opponent": "spiritomb", "rating": 536}], "counters": [{"opponent": "gastrodon", "rating": 122}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "sneasler", "rating": 348}, {"opponent": "drifb<PERSON>", "rating": 461}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 12212}, {"moveId": "STEEL_WING", "uses": 12040}, {"moveId": "METAL_CLAW", "uses": 13236}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 1959}, {"moveId": "HYDRO_CANNON", "uses": 18472}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 3257}, {"moveId": "DRILL_PECK", "uses": 9504}, {"moveId": "BLIZZARD", "uses": 4217}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 100, "stats": {"product": 1788, "atk": 125.1, "def": 117, "hp": 122}}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 606, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 897, "opRating": 102}, {"opponent": "abomasnow_shadow", "rating": 819, "opRating": 180}, {"opponent": "bastiodon", "rating": 799, "opRating": 200}, {"opponent": "gliscor", "rating": 688}, {"opponent": "drapion_shadow", "rating": 635}], "counters": [{"opponent": "gastrodon", "rating": 101}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "gallade_shadow", "rating": 264}, {"opponent": "drifb<PERSON>", "rating": 394}, {"opponent": "spiritomb", "rating": 447}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 12581}, {"moveId": "STEEL_WING", "uses": 12100}, {"moveId": "METAL_CLAW", "uses": 12872}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 1967}, {"moveId": "HYDRO_CANNON", "uses": 18471}, {"moveId": "FLASH_CANNON", "uses": 3252}, {"moveId": "DRILL_PECK", "uses": 9516}, {"moveId": "BLIZZARD", "uses": 4223}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 100, "stats": {"product": 1788, "atk": 125.1, "def": 117, "hp": 122}}, {"speciesId": "hippopotas_shadow", "speciesName": "Hip<PERSON><PERSON><PERSON> (Shadow)", "rating": 595, "matchups": [{"opponent": "magnezone_shadow", "rating": 904, "opRating": 95}, {"opponent": "bastiodon", "rating": 875, "opRating": 125}, {"opponent": "vespiquen", "rating": 592, "opRating": 407}, {"opponent": "bibarel", "rating": 565, "opRating": 434}, {"opponent": "lickilicky", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "gliscor", "rating": 362}, {"opponent": "drifb<PERSON>", "rating": 411}, {"opponent": "drapion_shadow", "rating": 427}, {"opponent": "gastrodon", "rating": 461}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9729}, {"moveId": "SAND_ATTACK", "uses": 19853}, {"moveId": "BITE", "uses": 7873}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 14836}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DIG", "uses": 12035}, {"moveId": "BODY_SLAM", "uses": 10595}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 100, "stats": {"product": 1956, "atk": 115.1, "def": 111.7, "hp": 152}}, {"speciesId": "bastiodon", "speciesName": "Bastiodon", "rating": 606, "matchups": [{"opponent": "drifb<PERSON>", "rating": 730}, {"opponent": "abomasnow_shadow", "rating": 726}, {"opponent": "qwilfish_his<PERSON>an", "rating": 600}, {"opponent": "drapion_shadow", "rating": 568}, {"opponent": "spiritomb", "rating": 553}], "counters": [{"opponent": "gastrodon", "rating": 136}, {"opponent": "gallade_shadow", "rating": 144}, {"opponent": "sneasler", "rating": 160}, {"opponent": "gliscor", "rating": 262}, {"opponent": "dusknoir_shadow", "rating": 283}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 23135}, {"moveId": "IRON_TAIL", "uses": 14365}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15498}, {"moveId": "RETURN", "uses": 5407}, {"moveId": "FLASH_CANNON", "uses": 6638}, {"moveId": "FLAMETHROWER", "uses": 9973}]}, "moveset": ["SMACK_DOWN", "STONE_EDGE", "FLAMETHROWER"], "score": 100, "stats": {"product": 2776, "atk": 80.6, "def": 247.6, "hp": 139}}, {"speciesId": "kricketune", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 637, "matchups": [{"opponent": "gallade_shadow", "rating": 660}, {"opponent": "drapion_shadow", "rating": 647}, {"opponent": "spiritomb", "rating": 614}, {"opponent": "gastrodon", "rating": 598}, {"opponent": "abomasnow_shadow", "rating": 545}], "counters": [{"opponent": "sneasler", "rating": 183}, {"opponent": "drifb<PERSON>", "rating": 234}, {"opponent": "bastiodon", "rating": 244}, {"opponent": "gliscor", "rating": 323}, {"opponent": "dusknoir_shadow", "rating": 416}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 9238}, {"moveId": "FURY_CUTTER", "uses": 28262}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18109}, {"moveId": "BUG_BUZZ", "uses": 4840}, {"moveId": "AERIAL_ACE", "uses": 14543}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "AERIAL_ACE"], "score": 100, "stats": {"product": 1751, "atk": 127.7, "def": 89.5, "hp": 153}}, {"speciesId": "bastiodon_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "vespiquen", "rating": 845, "opRating": 154}, {"opponent": "abomasnow_shadow", "rating": 834, "opRating": 165}, {"opponent": "drifb<PERSON>", "rating": 687}, {"opponent": "qwilfish_his<PERSON>an", "rating": 535, "opRating": 464}, {"opponent": "spiritomb", "rating": 528}], "counters": [{"opponent": "gastrodon", "rating": 163}, {"opponent": "gallade_shadow", "rating": 173}, {"opponent": "gliscor", "rating": 237}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "drapion_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 23282}, {"moveId": "IRON_TAIL", "uses": 14218}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 18038}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 7824}, {"moveId": "FLAMETHROWER", "uses": 11601}]}, "moveset": ["SMACK_DOWN", "STONE_EDGE", "FLAMETHROWER"], "score": 100, "stats": {"product": 2776, "atk": 80.6, "def": 247.6, "hp": 139}}, {"speciesId": "avalugg_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 585, "matchups": [{"opponent": "drifb<PERSON>", "rating": 770}, {"opponent": "gliscor", "rating": 721}, {"opponent": "gliscor_shadow", "rating": 668, "opRating": 331}, {"opponent": "vespiquen", "rating": 663, "opRating": 336}, {"opponent": "qwilfish_his<PERSON>an", "rating": 549, "opRating": 450}], "counters": [{"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "bastiodon", "rating": 294}, {"opponent": "gastrodon", "rating": 354}, {"opponent": "drapion_shadow", "rating": 411}, {"opponent": "spiritomb", "rating": 418}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 8887}, {"moveId": "POWDER_SNOW", "uses": 21335}, {"moveId": "BITE", "uses": 7237}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 9289}, {"moveId": "ICY_WIND", "uses": 13654}, {"moveId": "CRUNCH", "uses": 8414}, {"moveId": "BLIZZARD", "uses": 6129}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ROCK_SLIDE"], "score": 100, "stats": {"product": 1921, "atk": 116.4, "def": 135.1, "hp": 122}}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 537, "matchups": [{"opponent": "gastrodon", "rating": 671}, {"opponent": "qwilfish_his<PERSON>an", "rating": 572, "opRating": 428}, {"opponent": "gliscor", "rating": 552}, {"opponent": "gliscor_shadow", "rating": 540, "opRating": 460}, {"opponent": "lickilicky", "rating": 524, "opRating": 476}], "counters": [{"opponent": "drapion_shadow", "rating": 194}, {"opponent": "drifb<PERSON>", "rating": 296}, {"opponent": "spiritomb", "rating": 360}, {"opponent": "bastiodon", "rating": 388}, {"opponent": "dusknoir_shadow", "rating": 477}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 8244}, {"moveId": "SAND_ATTACK", "uses": 10179}, {"moveId": "QUICK_ATTACK", "uses": 10174}, {"moveId": "GUST", "uses": 8956}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 1593}, {"moveId": "FLY", "uses": 11157}, {"moveId": "CLOSE_COMBAT", "uses": 11599}, {"moveId": "BRAVE_BIRD", "uses": 13157}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 100, "stats": {"product": 1594, "atk": 140.3, "def": 90.8, "hp": 125}}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 488, "matchups": [{"opponent": "dusknoir_shadow", "rating": 644}, {"opponent": "gastrodon", "rating": 608}, {"opponent": "hippo<PERSON><PERSON>", "rating": 548, "opRating": 452}, {"opponent": "gliscor", "rating": 540}, {"opponent": "bastiodon", "rating": 516}], "counters": [{"opponent": "drifb<PERSON>", "rating": 102}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "abomasnow_shadow", "rating": 255}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "spiritomb", "rating": 394}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 8136}, {"moveId": "SAND_ATTACK", "uses": 10303}, {"moveId": "QUICK_ATTACK", "uses": 10359}, {"moveId": "GUST", "uses": 8688}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 1567}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLY", "uses": 11199}, {"moveId": "CLOSE_COMBAT", "uses": 11595}, {"moveId": "BRAVE_BIRD", "uses": 13161}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 100, "stats": {"product": 1594, "atk": 140.3, "def": 90.8, "hp": 125}}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 534, "matchups": [{"opponent": "gallade_shadow", "rating": 875, "opRating": 125}, {"opponent": "dusknoir_shadow", "rating": 826}, {"opponent": "drifb<PERSON>", "rating": 657}, {"opponent": "sneasler", "rating": 608, "opRating": 391}, {"opponent": "bastiodon", "rating": 524}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "gastrodon", "rating": 273}, {"opponent": "gliscor", "rating": 275}, {"opponent": "abomasnow_shadow", "rating": 297}, {"opponent": "spiritomb", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 12246}, {"moveId": "ASTONISH", "uses": 25254}], "chargedMoves": [{"moveId": "LOW_SWEEP", "uses": 9880}, {"moveId": "HYPER_BEAM", "uses": 8033}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "AERIAL_ACE", "uses": 19576}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 100, "stats": {"product": 1670, "atk": 133.8, "def": 100.5, "hp": 124}}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 463, "matchups": [{"opponent": "gliscor", "rating": 792}, {"opponent": "gliscor_shadow", "rating": 742, "opRating": 257}, {"opponent": "spiritomb", "rating": 560}, {"opponent": "abomasnow", "rating": 540, "opRating": 459}, {"opponent": "gastrodon", "rating": 535}], "counters": [{"opponent": "drapion_shadow", "rating": 233}, {"opponent": "bastiodon", "rating": 241}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "gallade_shadow", "rating": 293}, {"opponent": "drifb<PERSON>", "rating": 375}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 23516}, {"moveId": "FROST_BREATH", "uses": 13984}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 5104}, {"moveId": "LAST_RESORT", "uses": 3998}, {"moveId": "ICY_WIND", "uses": 6281}, {"moveId": "ICE_BEAM", "uses": 4095}, {"moveId": "AVALANCHE", "uses": 18077}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 100, "stats": {"product": 1645, "atk": 135.7, "def": 122.3, "hp": 99}}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 541, "matchups": [{"opponent": "abomasnow", "rating": 914, "opRating": 85}, {"opponent": "abomasnow_shadow", "rating": 903, "opRating": 96}, {"opponent": "magnezone_shadow", "rating": 810, "opRating": 189}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 608, "opRating": 391}, {"opponent": "spiritomb", "rating": 531, "opRating": 468}], "counters": [{"opponent": "gastrodon", "rating": 104}, {"opponent": "drifb<PERSON>", "rating": 299}, {"opponent": "drapion_shadow", "rating": 305}, {"opponent": "gliscor", "rating": 366}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15370}, {"moveId": "ROCK_SMASH", "uses": 4476}, {"moveId": "FIRE_FANG", "uses": 17650}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12353}, {"moveId": "ROCK_SLIDE", "uses": 8453}, {"moveId": "FLAMETHROWER", "uses": 8640}, {"moveId": "CRUNCH", "uses": 7996}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "ROCK_SLIDE"], "score": 100, "stats": {"product": 1699, "atk": 132.4, "def": 99.4, "hp": 129}}, {"speciesId": "s<PERSON><PERSON><PERSON>", "speciesName": "Skorupi", "rating": 456, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "electrode_hisuian", "rating": 681, "opRating": 318}, {"opponent": "gallade_shadow", "rating": 637, "opRating": 362}, {"opponent": "abomasnow_shadow", "rating": 588, "opRating": 411}, {"opponent": "toxicroak", "rating": 561, "opRating": 438}], "counters": [{"opponent": "dusknoir_shadow", "rating": 233}, {"opponent": "gastrodon", "rating": 264}, {"opponent": "drifb<PERSON>", "rating": 270}, {"opponent": "drapion_shadow", "rating": 351}, {"opponent": "gliscor", "rating": 426}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21545}, {"moveId": "INFESTATION", "uses": 15955}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 3355}, {"moveId": "RETURN", "uses": 3666}, {"moveId": "CROSS_POISON", "uses": 12549}, {"moveId": "AQUA_TAIL", "uses": 17848}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 100, "stats": {"product": 1430, "atk": 90.7, "def": 139.4, "hp": 113}}, {"speciesId": "snover", "speciesName": "Snover", "rating": 388, "matchups": [{"opponent": "electivire", "rating": 661, "opRating": 338}, {"opponent": "gastrodon", "rating": 644}, {"opponent": "electivire_shadow", "rating": 563, "opRating": 436}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "opRating": 478}, {"opponent": "abomasnow_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drifb<PERSON>", "rating": 253}, {"opponent": "gliscor", "rating": 349}, {"opponent": "spiritomb", "rating": 408}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 14588}, {"moveId": "LEAFAGE", "uses": 11654}, {"moveId": "ICE_SHARD", "uses": 11258}], "chargedMoves": [{"moveId": "STOMP", "uses": 7345}, {"moveId": "RETURN", "uses": 4146}, {"moveId": "ICE_BEAM", "uses": 14640}, {"moveId": "ENERGY_BALL", "uses": 11395}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 100, "stats": {"product": 1564, "atk": 109.2, "def": 100.8, "hp": 142}}, {"speciesId": "skorup<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 454, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 712, "opRating": 287}, {"opponent": "rhyperior_shadow", "rating": 650, "opRating": 349}, {"opponent": "gallade", "rating": 637, "opRating": 362}, {"opponent": "lucario", "rating": 579, "opRating": 420}, {"opponent": "abomasnow", "rating": 570, "opRating": 429}], "counters": [{"opponent": "drifb<PERSON>", "rating": 212}, {"opponent": "gastrodon", "rating": 223}, {"opponent": "drapion_shadow", "rating": 254}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 288}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21026}, {"moveId": "INFESTATION", "uses": 16474}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 3726}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CROSS_POISON", "uses": 13985}, {"moveId": "AQUA_TAIL", "uses": 19793}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 100, "stats": {"product": 1430, "atk": 90.7, "def": 139.4, "hp": 113}}, {"speciesId": "snover_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 371, "matchups": [{"opponent": "lumineon", "rating": 792, "opRating": 207}, {"opponent": "shellos", "rating": 669, "opRating": 330}, {"opponent": "garcho<PERSON>_shadow", "rating": 647, "opRating": 352}, {"opponent": "electivire", "rating": 563, "opRating": 436}, {"opponent": "gastrodon", "rating": 531}], "counters": [{"opponent": "drapion_shadow", "rating": 194}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drifb<PERSON>", "rating": 303}, {"opponent": "gliscor", "rating": 435}, {"opponent": "spiritomb", "rating": 480}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 14800}, {"moveId": "LEAFAGE", "uses": 11504}, {"moveId": "ICE_SHARD", "uses": 11207}], "chargedMoves": [{"moveId": "STOMP", "uses": 8552}, {"moveId": "ICE_BEAM", "uses": 16329}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ENERGY_BALL", "uses": 12594}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 100, "stats": {"product": 1564, "atk": 109.2, "def": 100.8, "hp": 142}}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 363, "matchups": [{"opponent": "rhyperior_shadow", "rating": 930, "opRating": 69}, {"opponent": "rhyperior", "rating": 930, "opRating": 69}, {"opponent": "gastrodon", "rating": 922}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 715, "opRating": 284}, {"opponent": "bibarel", "rating": 516, "opRating": 483}], "counters": [{"opponent": "drifb<PERSON>", "rating": 145}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gliscor", "rating": 245}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "spiritomb", "rating": 322}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 666}, {"moveId": "MAGICAL_LEAF", "uses": 5404}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2056}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1783}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2019}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1707}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1319}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2452}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2197}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2418}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1868}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2560}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2114}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1872}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1819}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1730}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1903}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1507}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4125}, {"moveId": "SEED_FLARE", "uses": 5997}, {"moveId": "GRASS_KNOT", "uses": 20204}, {"moveId": "ENERGY_BALL", "uses": 7125}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 100, "stats": {"product": 1614, "atk": 139.3, "def": 94.1, "hp": 123}}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 371, "matchups": [{"opponent": "drifb<PERSON>_shadow", "rating": 750, "opRating": 250}, {"opponent": "gliscor", "rating": 722}, {"opponent": "gliscor_shadow", "rating": 704, "opRating": 295}, {"opponent": "drifb<PERSON>", "rating": 604}, {"opponent": "garcho<PERSON>_shadow", "rating": 568, "opRating": 431}], "counters": [{"opponent": "bastiodon", "rating": 143}, {"opponent": "gastrodon", "rating": 211}, {"opponent": "drapion_shadow", "rating": 228}, {"opponent": "spiritomb", "rating": 269}, {"opponent": "dusknoir_shadow", "rating": 355}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 526}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2510}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2076}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2389}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2157}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1588}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2974}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2878}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2282}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2199}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2427}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2510}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2496}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2190}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2075}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2224}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1871}], "chargedMoves": [{"moveId": "THUNDER", "uses": 8698}, {"moveId": "GIGA_IMPACT", "uses": 2589}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOCUS_BLAST", "uses": 7470}, {"moveId": "CRUSH_GRIP", "uses": 18634}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "THUNDER"], "score": 99.9, "stats": {"product": 1618, "atk": 138.3, "def": 106.2, "hp": 110}}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 333, "matchups": [{"opponent": "gliscor_shadow", "rating": 725, "opRating": 274}, {"opponent": "garcho<PERSON>", "rating": 702, "opRating": 297}, {"opponent": "garcho<PERSON>_shadow", "rating": 635, "opRating": 364}, {"opponent": "drifloon_shadow", "rating": 635, "opRating": 364}, {"opponent": "giratina_origin", "rating": 608, "opRating": 391}], "counters": [{"opponent": "gastrodon", "rating": 169}, {"opponent": "drapion_shadow", "rating": 203}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "drifb<PERSON>", "rating": 423}, {"opponent": "gliscor", "rating": 456}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 528}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2497}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2112}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2405}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2145}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1579}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2983}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2787}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2268}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2227}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2426}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2537}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2392}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2209}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2097}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2250}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1863}], "chargedMoves": [{"moveId": "THUNDER", "uses": 8671}, {"moveId": "GIGA_IMPACT", "uses": 2614}, {"moveId": "FOCUS_BLAST", "uses": 7470}, {"moveId": "CRUSH_GRIP", "uses": 18655}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "THUNDER"], "score": 99.9, "stats": {"product": 1620, "atk": 137.9, "def": 105.8, "hp": 111}}, {"speciesId": "kleavor", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 584, "matchups": [{"opponent": "abomasnow", "rating": 754, "opRating": 245}, {"opponent": "magnezone_shadow", "rating": 634, "opRating": 365}, {"opponent": "spiritomb", "rating": 533}, {"opponent": "gastrodon", "rating": 514}, {"opponent": "qwilfish_his<PERSON>an", "rating": 509, "opRating": 490}], "counters": [{"opponent": "drapion_shadow", "rating": 245}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "bastiodon", "rating": 352}, {"opponent": "drifb<PERSON>", "rating": 363}, {"opponent": "gliscor", "rating": 366}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 11686}, {"moveId": "FURY_CUTTER", "uses": 18118}, {"moveId": "AIR_SLASH", "uses": 7686}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10201}, {"moveId": "TRAILBLAZE", "uses": 9343}, {"moveId": "STONE_EDGE", "uses": 9258}, {"moveId": "ROCK_SLIDE", "uses": 8738}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TRAILBLAZE"], "score": 99.4, "stats": {"product": 1561, "atk": 143.6, "def": 104.5, "hp": 104}}, {"speciesId": "bonsly", "speciesName": "Bon<PERSON><PERSON>", "rating": 531, "matchups": [{"opponent": "vespiquen", "rating": 771, "opRating": 228}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "abomasnow_shadow", "rating": 610, "opRating": 389}, {"opponent": "froslass", "rating": 555, "opRating": 444}, {"opponent": "drifb<PERSON>", "rating": 551}], "counters": [{"opponent": "spiritomb", "rating": 331}, {"opponent": "gastrodon", "rating": 342}, {"opponent": "gliscor", "rating": 366}, {"opponent": "drapion_shadow", "rating": 389}, {"opponent": "dusknoir_shadow", "rating": 405}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 19140}, {"moveId": "COUNTER", "uses": 18360}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 17745}, {"moveId": "ROCK_SLIDE", "uses": 11387}, {"moveId": "EARTHQUAKE", "uses": 8440}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 99.2, "stats": {"product": 1845, "atk": 116.8, "def": 124.3, "hp": 127}}, {"speciesId": "pip<PERSON>p", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 411, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 690, "opRating": 309}, {"opponent": "rhyperior_shadow", "rating": 618, "opRating": 381}, {"opponent": "heatran", "rating": 618, "opRating": 381}, {"opponent": "hippo<PERSON><PERSON>", "rating": 564, "opRating": 435}, {"opponent": "gliscor_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dusknoir_shadow", "rating": 200}, {"opponent": "gastrodon", "rating": 291}, {"opponent": "drifb<PERSON>", "rating": 356}, {"opponent": "drapion_shadow", "rating": 389}, {"opponent": "gliscor", "rating": 452}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 2270}, {"moveId": "BUBBLE", "uses": 35230}], "chargedMoves": [{"moveId": "RETURN", "uses": 4018}, {"moveId": "ICY_WIND", "uses": 13612}, {"moveId": "DRILL_PECK", "uses": 14008}, {"moveId": "BUBBLE_BEAM", "uses": 5819}]}, "moveset": ["BUBBLE", "DRILL_PECK", "ICY_WIND"], "score": 99.1, "stats": {"product": 1374, "atk": 106.7, "def": 98.3, "hp": 131}}, {"speciesId": "piplup_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 391, "matchups": [{"opponent": "heatran_shadow", "rating": 660, "opRating": 339}, {"opponent": "rhyperior", "rating": 618, "opRating": 381}, {"opponent": "rhyperior_shadow", "rating": 599, "opRating": 400}, {"opponent": "heatran", "rating": 587, "opRating": 412}, {"opponent": "gliscor", "rating": 511}], "counters": [{"opponent": "gastrodon", "rating": 169}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "dusknoir_shadow", "rating": 238}, {"opponent": "spiritomb", "rating": 394}, {"opponent": "drifb<PERSON>", "rating": 399}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 2116}, {"moveId": "BUBBLE", "uses": 35384}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 15136}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRILL_PECK", "uses": 15858}, {"moveId": "BUBBLE_BEAM", "uses": 6482}]}, "moveset": ["BUBBLE", "DRILL_PECK", "ICY_WIND"], "score": 99.1, "stats": {"product": 1374, "atk": 106.7, "def": 98.3, "hp": 131}}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 503, "matchups": [{"opponent": "gastrodon", "rating": 732}, {"opponent": "gallade_shadow", "rating": 728, "opRating": 272}, {"opponent": "electivire_shadow", "rating": 708, "opRating": 292}, {"opponent": "abomasnow_shadow", "rating": 675, "opRating": 324}, {"opponent": "spiritomb", "rating": 548}], "counters": [{"opponent": "drifb<PERSON>", "rating": 138}, {"opponent": "bastiodon", "rating": 147}, {"opponent": "gliscor", "rating": 193}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drapion_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 18055}, {"moveId": "BUG_BITE", "uses": 19445}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 9906}, {"moveId": "ANCIENT_POWER", "uses": 10567}, {"moveId": "AERIAL_ACE", "uses": 17011}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "ANCIENT_POWER"], "score": 97.7, "stats": {"product": 1643, "atk": 136.8, "def": 96, "hp": 125}}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 581, "matchups": [{"opponent": "abomasnow_shadow", "rating": 604, "opRating": 395}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 594, "opRating": 405}, {"opponent": "bibarel", "rating": 588, "opRating": 411}, {"opponent": "gliscor_shadow", "rating": 550, "opRating": 449}, {"opponent": "drifb<PERSON>", "rating": 512}], "counters": [{"opponent": "gastrodon", "rating": 178}, {"opponent": "dusknoir_shadow", "rating": 205}, {"opponent": "gliscor", "rating": 353}, {"opponent": "spiritomb", "rating": 447}, {"opponent": "drapion_shadow", "rating": 474}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 23542}, {"moveId": "BITE", "uses": 13958}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 9499}, {"moveId": "SLUDGE_BOMB", "uses": 5898}, {"moveId": "RETURN", "uses": 3352}, {"moveId": "FLAMETHROWER", "uses": 6679}, {"moveId": "CRUNCH", "uses": 11958}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 97.2, "stats": {"product": 1831, "atk": 121.7, "def": 95.1, "hp": 158}}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 574, "matchups": [{"opponent": "drapion_shadow", "rating": 642}, {"opponent": "drifb<PERSON>", "rating": 620}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 579, "opRating": 420}, {"opponent": "abomasnow_shadow", "rating": 553, "opRating": 446}, {"opponent": "gliscor", "rating": 550}], "counters": [{"opponent": "gastrodon", "rating": 190}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "bastiodon", "rating": 305}, {"opponent": "gallade_shadow", "rating": 413}, {"opponent": "spiritomb", "rating": 451}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 24646}, {"moveId": "BITE", "uses": 12854}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 10405}, {"moveId": "SLUDGE_BOMB", "uses": 6635}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 7428}, {"moveId": "CRUNCH", "uses": 12929}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 97.2, "stats": {"product": 1831, "atk": 121.7, "def": 95.1, "hp": 158}}, {"speciesId": "stunky_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 549, "matchups": [{"opponent": "froslass", "rating": 853, "opRating": 146}, {"opponent": "dusknoir_shadow", "rating": 792}, {"opponent": "dusknoir", "rating": 758, "opRating": 241}, {"opponent": "empoleon_shadow", "rating": 670, "opRating": 329}, {"opponent": "drifb<PERSON>", "rating": 639}], "counters": [{"opponent": "gliscor", "rating": 284}, {"opponent": "gastrodon", "rating": 312}, {"opponent": "drapion_shadow", "rating": 334}, {"opponent": "bastiodon", "rating": 381}, {"opponent": "spiritomb", "rating": 451}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 13149}, {"moveId": "BITE", "uses": 24351}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 10409}, {"moveId": "SLUDGE_BOMB", "uses": 6628}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 7477}, {"moveId": "CRUNCH", "uses": 12918}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 97.2, "stats": {"product": 1482, "atk": 114.2, "def": 88.2, "hp": 147}}, {"speciesId": "stunky", "speciesName": "Stunky", "rating": 514, "matchups": [{"opponent": "dusknoir", "rating": 806, "opRating": 193}, {"opponent": "dusknoir_shadow", "rating": 758}, {"opponent": "electivire_shadow", "rating": 758, "opRating": 241}, {"opponent": "drifb<PERSON>", "rating": 663}, {"opponent": "froslass", "rating": 527, "opRating": 472}], "counters": [{"opponent": "drapion_shadow", "rating": 224}, {"opponent": "gastrodon", "rating": 273}, {"opponent": "bastiodon", "rating": 381}, {"opponent": "gliscor", "rating": 387}, {"opponent": "spiritomb", "rating": 418}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 13620}, {"moveId": "BITE", "uses": 23880}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 9552}, {"moveId": "SLUDGE_BOMB", "uses": 5942}, {"moveId": "RETURN", "uses": 3310}, {"moveId": "FLAMETHROWER", "uses": 6711}, {"moveId": "CRUNCH", "uses": 11939}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 97.2, "stats": {"product": 1482, "atk": 114.2, "def": 88.2, "hp": 147}}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 538, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 729, "opRating": 270}, {"opponent": "qwilfish_his<PERSON>an", "rating": 611, "opRating": 388}, {"opponent": "drapion_shadow", "rating": 586}, {"opponent": "gliscor", "rating": 559}, {"opponent": "vespiquen", "rating": 531, "opRating": 468}], "counters": [{"opponent": "dusknoir_shadow", "rating": 144}, {"opponent": "spiritomb", "rating": 278}, {"opponent": "drifb<PERSON>", "rating": 330}, {"opponent": "bastiodon", "rating": 338}, {"opponent": "gastrodon", "rating": 401}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 28366}, {"moveId": "ROCK_SMASH", "uses": 9134}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 5310}, {"moveId": "THUNDER_PUNCH", "uses": 4632}, {"moveId": "SWIFT", "uses": 6160}, {"moveId": "ICE_PUNCH", "uses": 6617}, {"moveId": "HIGH_HORSEPOWER", "uses": 5256}, {"moveId": "FIRE_PUNCH", "uses": 5406}, {"moveId": "AERIAL_ACE", "uses": 4173}]}, "moveset": ["TACKLE", "ICE_PUNCH", "SWIFT"], "score": 97, "stats": {"product": 1786, "atk": 125.6, "def": 98.6, "hp": 144}}, {"speciesId": "toxicroak_shadow", "speciesName": "To<PERSON>croa<PERSON> (Shadow)", "rating": 623, "matchups": [{"opponent": "lickilicky", "rating": 887, "opRating": 112}, {"opponent": "bibarel", "rating": 860, "opRating": 139}, {"opponent": "bastiodon", "rating": 763, "opRating": 236}, {"opponent": "qwilfish_his<PERSON>an", "rating": 627, "opRating": 372}, {"opponent": "drapion_shadow", "rating": 604}], "counters": [{"opponent": "gastrodon", "rating": 98}, {"opponent": "drifb<PERSON>", "rating": 188}, {"opponent": "dusknoir_shadow", "rating": 344}, {"opponent": "gliscor", "rating": 349}, {"opponent": "spiritomb", "rating": 360}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 11646}, {"moveId": "POISON_JAB", "uses": 8748}, {"moveId": "MUD_SHOT", "uses": 8813}, {"moveId": "COUNTER", "uses": 8239}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6265}, {"moveId": "SHADOW_BALL", "uses": 8248}, {"moveId": "MUD_BOMB", "uses": 9058}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DYNAMIC_PUNCH", "uses": 13974}]}, "moveset": ["POISON_STING", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 96.8, "stats": {"product": 1648, "atk": 136.2, "def": 93.7, "hp": 129}}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 654, "matchups": [{"opponent": "lickilicky", "rating": 887, "opRating": 112}, {"opponent": "bastiodon", "rating": 802, "opRating": 197}, {"opponent": "qwilfish_his<PERSON>an", "rating": 666, "opRating": 333}, {"opponent": "drapion_shadow", "rating": 658}, {"opponent": "abomasnow_shadow", "rating": 534, "opRating": 465}], "counters": [{"opponent": "gastrodon", "rating": 92}, {"opponent": "dusknoir_shadow", "rating": 261}, {"opponent": "gliscor", "rating": 297}, {"opponent": "spiritomb", "rating": 370}, {"opponent": "drifb<PERSON>", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 11249}, {"moveId": "POISON_JAB", "uses": 9131}, {"moveId": "MUD_SHOT", "uses": 8569}, {"moveId": "COUNTER", "uses": 8543}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6239}, {"moveId": "SHADOW_BALL", "uses": 8226}, {"moveId": "MUD_BOMB", "uses": 9065}, {"moveId": "DYNAMIC_PUNCH", "uses": 14019}]}, "moveset": ["POISON_STING", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 96.8, "stats": {"product": 1648, "atk": 136.2, "def": 93.7, "hp": 129}}, {"speciesId": "luxio", "speciesName": "Luxio", "rating": 423, "matchups": [{"opponent": "mantyke", "rating": 687, "opRating": 312}, {"opponent": "bronzong", "rating": 625, "opRating": 374}, {"opponent": "drapion", "rating": 611, "opRating": 388}, {"opponent": "electivire", "rating": 600, "opRating": 399}, {"opponent": "spiritomb", "rating": 575, "opRating": 424}], "counters": [{"opponent": "gastrodon", "rating": 133}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drapion_shadow", "rating": 258}, {"opponent": "drifb<PERSON>", "rating": 303}, {"opponent": "gliscor", "rating": 366}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 25994}, {"moveId": "BITE", "uses": 11506}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19630}, {"moveId": "THUNDERBOLT", "uses": 3574}, {"moveId": "RETURN", "uses": 4184}, {"moveId": "CRUNCH", "uses": 10093}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 96.7, "stats": {"product": 1687, "atk": 133.2, "def": 91, "hp": 139}}, {"speciesId": "luxio_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 401, "matchups": [{"opponent": "probopass_shadow", "rating": 809, "opRating": 190}, {"opponent": "rotom_heat", "rating": 809, "opRating": 190}, {"opponent": "empoleon_shadow", "rating": 798, "opRating": 201}, {"opponent": "mantyke", "rating": 769, "opRating": 230}, {"opponent": "bronzong", "rating": 532, "opRating": 467}], "counters": [{"opponent": "gastrodon", "rating": 110}, {"opponent": "gliscor", "rating": 159}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "drifb<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 26030}, {"moveId": "BITE", "uses": 11470}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 21841}, {"moveId": "THUNDERBOLT", "uses": 4018}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CRUNCH", "uses": 11637}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 96.7, "stats": {"product": 1677, "atk": 133.7, "def": 90.2, "hp": 139}}, {"speciesId": "buneary", "speciesName": "Buneary", "rating": 578, "matchups": [{"opponent": "sneasler", "rating": 885, "opRating": 114}, {"opponent": "electivire_shadow", "rating": 796, "opRating": 203}, {"opponent": "abomasnow_shadow", "rating": 570, "opRating": 429}, {"opponent": "froslass", "rating": 566, "opRating": 433}, {"opponent": "bibarel", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dusknoir_shadow", "rating": 322}, {"opponent": "gastrodon", "rating": 348}, {"opponent": "gliscor", "rating": 387}, {"opponent": "drifb<PERSON>", "rating": 428}, {"opponent": "drapion_shadow", "rating": 453}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 34277}, {"moveId": "POUND", "uses": 3223}], "chargedMoves": [{"moveId": "SWIFT", "uses": 20375}, {"moveId": "FIRE_PUNCH", "uses": 17125}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "FIRE_PUNCH"], "score": 96.3, "stats": {"product": 1659, "atk": 121.8, "def": 100.8, "hp": 135}}, {"speciesId": "rampardos_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 501, "matchups": [{"opponent": "drifb<PERSON>", "rating": 757}, {"opponent": "abomasnow_shadow", "rating": 757, "opRating": 242}, {"opponent": "froslass", "rating": 757, "opRating": 242}, {"opponent": "vespiquen", "rating": 718, "opRating": 281}, {"opponent": "electivire_shadow", "rating": 678, "opRating": 321}], "counters": [{"opponent": "gastrodon", "rating": 163}, {"opponent": "spiritomb", "rating": 346}, {"opponent": "gliscor", "rating": 366}, {"opponent": "drapion_shadow", "rating": 381}, {"opponent": "dusknoir_shadow", "rating": 400}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 5944}, {"moveId": "SMACK_DOWN", "uses": 31556}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 16321}, {"moveId": "OUTRAGE", "uses": 10004}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 11192}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "FLAMETHROWER"], "score": 95.9, "stats": {"product": 1376, "atk": 162.2, "def": 67.2, "hp": 126}}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 466, "matchups": [{"opponent": "froslass", "rating": 757, "opRating": 242}, {"opponent": "vespiquen", "rating": 726, "opRating": 273}, {"opponent": "drifb<PERSON>", "rating": 718}, {"opponent": "abomasnow_shadow", "rating": 710, "opRating": 289}, {"opponent": "abomasnow", "rating": 690, "opRating": 309}], "counters": [{"opponent": "gastrodon", "rating": 133}, {"opponent": "gliscor", "rating": 306}, {"opponent": "drapion_shadow", "rating": 330}, {"opponent": "dusknoir_shadow", "rating": 338}, {"opponent": "spiritomb", "rating": 341}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 6212}, {"moveId": "SMACK_DOWN", "uses": 31288}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 16298}, {"moveId": "OUTRAGE", "uses": 10029}, {"moveId": "FLAMETHROWER", "uses": 11214}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "FLAMETHROWER"], "score": 95.9, "stats": {"product": 1376, "atk": 162.2, "def": 67.2, "hp": 126}}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 596, "matchups": [{"opponent": "gallade_shadow", "rating": 873, "opRating": 126}, {"opponent": "spiritomb", "rating": 839}, {"opponent": "sneasler", "rating": 778, "opRating": 221}, {"opponent": "gliscor", "rating": 691}, {"opponent": "gastrodon", "rating": 691}], "counters": [{"opponent": "bastiodon", "rating": 176}, {"opponent": "abomasnow_shadow", "rating": 335}, {"opponent": "drifb<PERSON>", "rating": 346}, {"opponent": "drapion_shadow", "rating": 444}, {"opponent": "dusknoir_shadow", "rating": 494}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_WATER", "uses": 2085}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1751}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1972}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1790}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1320}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2317}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2396}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1752}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1846}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2603}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2129}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2115}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1812}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1699}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1865}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1509}, {"moveId": "CHARM", "uses": 2988}, {"moveId": "AIR_SLASH", "uses": 3539}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 7755}, {"moveId": "FLAMETHROWER", "uses": 4621}, {"moveId": "DAZZLING_GLEAM", "uses": 6058}, {"moveId": "AURA_SPHERE", "uses": 5901}, {"moveId": "ANCIENT_POWER", "uses": 5048}, {"moveId": "AERIAL_ACE", "uses": 8040}]}, "moveset": ["CHARM", "PSYSHOCK", "AURA_SPHERE"], "score": 95.7, "stats": {"product": 1799, "atk": 124.2, "def": 125.8, "hp": 115}}, {"speciesId": "p<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "matchups": [{"opponent": "drifb<PERSON>", "rating": 750}, {"opponent": "gallade_shadow", "rating": 711, "opRating": 288}, {"opponent": "vespiquen", "rating": 690, "opRating": 309}, {"opponent": "sneasler", "rating": 570, "opRating": 429}, {"opponent": "qwilfish_his<PERSON>an", "rating": 545, "opRating": 454}], "counters": [{"opponent": "gastrodon", "rating": 187}, {"opponent": "gliscor", "rating": 189}, {"opponent": "spiritomb", "rating": 384}, {"opponent": "drapion_shadow", "rating": 483}, {"opponent": "dusknoir_shadow", "rating": 488}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 21918}, {"moveId": "SPARK", "uses": 15582}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 23743}, {"moveId": "THUNDERBOLT", "uses": 7384}, {"moveId": "THUNDER", "uses": 6400}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 95.7, "stats": {"product": 2044, "atk": 91.5, "def": 157.1, "hp": 142}}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 689, "matchups": [{"opponent": "gastrodon", "rating": 821}, {"opponent": "gallade_shadow", "rating": 803, "opRating": 196}, {"opponent": "qwilfish_his<PERSON>an", "rating": 647, "opRating": 352}, {"opponent": "drapion_shadow", "rating": 625}, {"opponent": "dusknoir_shadow", "rating": 530}], "counters": [{"opponent": "bastiodon", "rating": 161}, {"opponent": "abomasnow_shadow", "rating": 335}, {"opponent": "drifb<PERSON>", "rating": 339}, {"opponent": "spiritomb", "rating": 437}, {"opponent": "gliscor", "rating": 452}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 8751}, {"moveId": "FURY_CUTTER", "uses": 12528}, {"moveId": "BUG_BITE", "uses": 9120}, {"moveId": "AIR_SLASH", "uses": 7118}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 14414}, {"moveId": "SIGNAL_BEAM", "uses": 3341}, {"moveId": "POWER_GEM", "uses": 11937}, {"moveId": "FELL_STINGER", "uses": 3960}, {"moveId": "BUG_BUZZ", "uses": 3927}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 95.2, "stats": {"product": 2062, "atk": 108.8, "def": 143.4, "hp": 132}}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 551, "matchups": [{"opponent": "gastrodon", "rating": 848}, {"opponent": "bibarel", "rating": 848, "opRating": 151}, {"opponent": "hippo<PERSON><PERSON>", "rating": 710, "opRating": 289}, {"opponent": "lickilicky", "rating": 514, "opRating": 485}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509, "opRating": 490}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "drifb<PERSON>", "rating": 193}, {"opponent": "gliscor", "rating": 344}, {"opponent": "dusknoir_shadow", "rating": 388}, {"opponent": "spiritomb", "rating": 471}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 7507}, {"moveId": "QUICK_ATTACK", "uses": 14082}, {"moveId": "BULLET_SEED", "uses": 15921}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 2175}, {"moveId": "LEAF_BLADE", "uses": 26030}, {"moveId": "LAST_RESORT", "uses": 5630}, {"moveId": "ENERGY_BALL", "uses": 3636}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 94.8, "stats": {"product": 1750, "atk": 128.1, "def": 133.9, "hp": 102}}, {"speciesId": "froslass", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 632, "matchups": [{"opponent": "drifb<PERSON>", "rating": 726}, {"opponent": "vespiquen", "rating": 715, "opRating": 284}, {"opponent": "abomasnow_shadow", "rating": 711, "opRating": 288}, {"opponent": "gliscor", "rating": 665}, {"opponent": "gallade_shadow", "rating": 592, "opRating": 407}], "counters": [{"opponent": "drapion_shadow", "rating": 105}, {"opponent": "bastiodon", "rating": 262}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "spiritomb", "rating": 394}, {"opponent": "gastrodon", "rating": 446}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18970}, {"moveId": "HEX", "uses": 18530}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 5426}, {"moveId": "SHADOW_BALL", "uses": 7563}, {"moveId": "RETURN", "uses": 2589}, {"moveId": "CRUNCH", "uses": 6564}, {"moveId": "AVALANCHE", "uses": 15438}]}, "moveset": ["HEX", "AVALANCHE", "SHADOW_BALL"], "score": 94.3, "stats": {"product": 1831, "atk": 122.5, "def": 114.8, "hp": 130}}, {"speciesId": "froslass_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 590, "matchups": [{"opponent": "drifb<PERSON>", "rating": 700}, {"opponent": "vespiquen", "rating": 692, "opRating": 307}, {"opponent": "abomasnow_shadow", "rating": 688, "opRating": 311}, {"opponent": "gliscor", "rating": 634}, {"opponent": "gastrodon", "rating": 611}], "counters": [{"opponent": "drapion_shadow", "rating": 131}, {"opponent": "spiritomb", "rating": 177}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "bastiodon", "rating": 302}, {"opponent": "gallade_shadow", "rating": 475}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18877}, {"moveId": "HEX", "uses": 18623}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 5822}, {"moveId": "SHADOW_BALL", "uses": 8043}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CRUNCH", "uses": 7038}, {"moveId": "AVALANCHE", "uses": 16583}]}, "moveset": ["HEX", "AVALANCHE", "SHADOW_BALL"], "score": 94.3, "stats": {"product": 1831, "atk": 122.5, "def": 114.8, "hp": 130}}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 420, "matchups": [{"opponent": "gastrodon", "rating": 837}, {"opponent": "hippo<PERSON><PERSON>", "rating": 632, "opRating": 367}, {"opponent": "bibarel", "rating": 606, "opRating": 393}, {"opponent": "lickilicky", "rating": 571, "opRating": 428}, {"opponent": "magnezone_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "drifb<PERSON>", "rating": 181}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "gliscor", "rating": 215}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "spiritomb", "rating": 302}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 736}, {"moveId": "MAGICAL_LEAF", "uses": 5087}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2162}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1840}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2073}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1743}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1361}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2550}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2285}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2554}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1935}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2071}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2185}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1995}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1878}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1793}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1952}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1549}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4126}, {"moveId": "SEED_FLARE", "uses": 5995}, {"moveId": "GRASS_KNOT", "uses": 20165}, {"moveId": "ENERGY_BALL", "uses": 7125}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "SEED_FLARE"], "score": 94.3, "stats": {"product": 1902, "atk": 117.8, "def": 122.2, "hp": 132}}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 667, "matchups": [{"opponent": "gallade_shadow", "rating": 954, "opRating": 45}, {"opponent": "abomasnow_shadow", "rating": 870, "opRating": 129}, {"opponent": "sneasler", "rating": 745, "opRating": 254}, {"opponent": "bastiodon", "rating": 550, "opRating": 450}, {"opponent": "qwilfish_his<PERSON>an", "rating": 504, "opRating": 495}], "counters": [{"opponent": "gastrodon", "rating": 294}, {"opponent": "drifb<PERSON>", "rating": 306}, {"opponent": "gliscor", "rating": 353}, {"opponent": "drapion_shadow", "rating": 385}, {"opponent": "dusknoir_shadow", "rating": 405}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 15834}, {"moveId": "FEINT_ATTACK", "uses": 8765}, {"moveId": "CONFUSION", "uses": 12887}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 14313}, {"moveId": "PSYCHIC", "uses": 2405}, {"moveId": "PAYBACK", "uses": 6270}, {"moveId": "HEAVY_SLAM", "uses": 6737}, {"moveId": "FLASH_CANNON", "uses": 2159}, {"moveId": "BULLDOZE", "uses": 5672}]}, "moveset": ["METAL_SOUND", "PSYSHOCK", "PAYBACK"], "score": 94.2, "stats": {"product": 2026, "atk": 110.8, "def": 152.2, "hp": 120}}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 601, "matchups": [{"opponent": "dusknoir_shadow", "rating": 832}, {"opponent": "abomasnow_shadow", "rating": 674, "opRating": 325}, {"opponent": "qwilfish_his<PERSON>an", "rating": 661, "opRating": 338}, {"opponent": "drifb<PERSON>", "rating": 637}, {"opponent": "gliscor", "rating": 580}], "counters": [{"opponent": "gallade_shadow", "rating": 125}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gastrodon", "rating": 389}, {"opponent": "spiritomb", "rating": 432}, {"opponent": "bastiodon", "rating": 492}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 15021}, {"moveId": "TAKE_DOWN", "uses": 2944}, {"moveId": "ROLLOUT", "uses": 19501}], "chargedMoves": [{"moveId": "SURF", "uses": 22826}, {"moveId": "HYPER_FANG", "uses": 11489}, {"moveId": "HYPER_BEAM", "uses": 3085}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 94, "stats": {"product": 1819, "atk": 122.9, "def": 99.2, "hp": 149}}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 598, "matchups": [{"opponent": "abomasnow_shadow", "rating": 718, "opRating": 281}, {"opponent": "drifb<PERSON>", "rating": 687}, {"opponent": "gliscor", "rating": 563}, {"opponent": "lickilicky", "rating": 546, "opRating": 453}, {"opponent": "qwilfish_his<PERSON>an", "rating": 533, "opRating": 466}], "counters": [{"opponent": "gastrodon", "rating": 321}, {"opponent": "spiritomb", "rating": 384}, {"opponent": "bastiodon", "rating": 428}, {"opponent": "dusknoir_shadow", "rating": 438}, {"opponent": "drapion_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 15043}, {"moveId": "TAKE_DOWN", "uses": 3154}, {"moveId": "ROLLOUT", "uses": 19290}], "chargedMoves": [{"moveId": "SURF", "uses": 21106}, {"moveId": "RETURN", "uses": 3501}, {"moveId": "HYPER_FANG", "uses": 10230}, {"moveId": "HYPER_BEAM", "uses": 2702}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 94, "stats": {"product": 1819, "atk": 122.9, "def": 99.2, "hp": 149}}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 658, "matchups": [{"opponent": "abomasnow_shadow", "rating": 828, "opRating": 171}, {"opponent": "empoleon_shadow", "rating": 787, "opRating": 212}, {"opponent": "sneasler", "rating": 703, "opRating": 296}, {"opponent": "vespiquen", "rating": 606, "opRating": 393}, {"opponent": "gallade_shadow", "rating": 560, "opRating": 439}], "counters": [{"opponent": "gastrodon", "rating": 145}, {"opponent": "drapion_shadow", "rating": 173}, {"opponent": "gliscor", "rating": 396}, {"opponent": "dusknoir_shadow", "rating": 411}, {"opponent": "drifb<PERSON>", "rating": 456}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 19876}, {"moveId": "FIRE_SPIN", "uses": 17624}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 5235}, {"moveId": "SCORCHING_SANDS", "uses": 8123}, {"moveId": "PSYCHIC", "uses": 3751}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_PUNCH", "uses": 12807}, {"moveId": "FIRE_BLAST", "uses": 2194}, {"moveId": "BRICK_BREAK", "uses": 5491}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 93.6, "stats": {"product": 1589, "atk": 140.8, "def": 104.5, "hp": 108}}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 652, "matchups": [{"opponent": "abomasnow_shadow", "rating": 828, "opRating": 171}, {"opponent": "empoleon_shadow", "rating": 828, "opRating": 171}, {"opponent": "electivire_shadow", "rating": 745, "opRating": 254}, {"opponent": "abomasnow", "rating": 722, "opRating": 277}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 703, "opRating": 296}], "counters": [{"opponent": "drapion_shadow", "rating": 173}, {"opponent": "gastrodon", "rating": 255}, {"opponent": "gliscor", "rating": 314}, {"opponent": "dusknoir_shadow", "rating": 372}, {"opponent": "drifb<PERSON>", "rating": 389}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 19815}, {"moveId": "FIRE_SPIN", "uses": 17685}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 5223}, {"moveId": "SCORCHING_SANDS", "uses": 8113}, {"moveId": "PSYCHIC", "uses": 3768}, {"moveId": "FIRE_PUNCH", "uses": 12843}, {"moveId": "FIRE_BLAST", "uses": 2184}, {"moveId": "BRICK_BREAK", "uses": 5506}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 93.6, "stats": {"product": 1589, "atk": 140.8, "def": 104.5, "hp": 108}}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 513, "matchups": [{"opponent": "electivire_shadow", "rating": 936, "opRating": 63}, {"opponent": "bibarel", "rating": 921, "opRating": 78}, {"opponent": "gastrodon", "rating": 890}, {"opponent": "hippo<PERSON><PERSON>", "rating": 751, "opRating": 248}, {"opponent": "gallade_shadow", "rating": 586, "opRating": 413}], "counters": [{"opponent": "drifb<PERSON>", "rating": 222}, {"opponent": "drapion_shadow", "rating": 309}, {"opponent": "gliscor", "rating": 331}, {"opponent": "dusknoir_shadow", "rating": 427}, {"opponent": "spiritomb", "rating": 475}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20731}, {"moveId": "BITE", "uses": 16769}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 8510}, {"moveId": "SOLAR_BEAM", "uses": 1838}, {"moveId": "SAND_TOMB", "uses": 3941}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FRENZY_PLANT", "uses": 16476}, {"moveId": "EARTHQUAKE", "uses": 6800}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 93.6, "stats": {"product": 1867, "atk": 119.9, "def": 117, "hp": 133}}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 479, "matchups": [{"opponent": "electivire_shadow", "rating": 928, "opRating": 71}, {"opponent": "bibarel", "rating": 917, "opRating": 82}, {"opponent": "hippo<PERSON><PERSON>", "rating": 913, "opRating": 86}, {"opponent": "gastrodon", "rating": 906}, {"opponent": "bastiodon", "rating": 511, "opRating": 488}], "counters": [{"opponent": "drifb<PERSON>", "rating": 177}, {"opponent": "gliscor", "rating": 284}, {"opponent": "drapion_shadow", "rating": 300}, {"opponent": "dusknoir_shadow", "rating": 366}, {"opponent": "spiritomb", "rating": 403}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20305}, {"moveId": "BITE", "uses": 17195}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 8519}, {"moveId": "SOLAR_BEAM", "uses": 1844}, {"moveId": "SAND_TOMB", "uses": 3942}, {"moveId": "FRENZY_PLANT", "uses": 16471}, {"moveId": "EARTHQUAKE", "uses": 6792}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 93.6, "stats": {"product": 1867, "atk": 119.9, "def": 117, "hp": 133}}, {"speciesId": "shellos", "speciesName": "<PERSON><PERSON>", "rating": 531, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 765, "opRating": 234}, {"opponent": "bastiodon", "rating": 722, "opRating": 277}, {"opponent": "sneasler", "rating": 659, "opRating": 340}, {"opponent": "drapion_shadow", "rating": 584}, {"opponent": "bibarel", "rating": 566, "opRating": 433}], "counters": [{"opponent": "drifb<PERSON>", "rating": 148}, {"opponent": "gliscor", "rating": 219}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "gastrodon", "rating": 306}, {"opponent": "spiritomb", "rating": 375}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 5198}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2792}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1861}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2090}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1870}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1432}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2593}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2186}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1953}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2053}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2078}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2186}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1917}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1904}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1829}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2067}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1565}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 10893}, {"moveId": "MUD_BOMB", "uses": 14549}, {"moveId": "BODY_SLAM", "uses": 12043}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "BODY_SLAM"], "score": 93.4, "stats": {"product": 1660, "atk": 99.1, "def": 100.8, "hp": 166}}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 696, "matchups": [{"opponent": "bastiodon", "rating": 716}, {"opponent": "drifb<PERSON>", "rating": 683}, {"opponent": "gliscor", "rating": 666}, {"opponent": "gastrodon", "rating": 627}, {"opponent": "abomasnow_shadow", "rating": 538}], "counters": [{"opponent": "drapion_shadow", "rating": 207}, {"opponent": "qwilfish_his<PERSON>an", "rating": 235}, {"opponent": "spiritomb", "rating": 293}, {"opponent": "sneasler", "rating": 473}, {"opponent": "lickilicky", "rating": 481}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 20317}, {"moveId": "ASTONISH", "uses": 17183}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 12341}, {"moveId": "SHADOW_BALL", "uses": 3338}, {"moveId": "PSYCHIC", "uses": 3013}, {"moveId": "POLTERGEIST", "uses": 2171}, {"moveId": "OMINOUS_WIND", "uses": 1761}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DYNAMIC_PUNCH", "uses": 10397}, {"moveId": "DARK_PULSE", "uses": 4504}]}, "moveset": ["ASTONISH", "DYNAMIC_PUNCH", "SHADOW_PUNCH"], "score": 92.4, "stats": {"product": 1878, "atk": 119.8, "def": 174.1, "hp": 90}}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 703, "matchups": [{"opponent": "drifb<PERSON>", "rating": 761}, {"opponent": "bastiodon", "rating": 683}, {"opponent": "gastrodon", "rating": 594}, {"opponent": "abomasnow_shadow", "rating": 577}, {"opponent": "gliscor", "rating": 511}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "qwilfish_his<PERSON>an", "rating": 206}, {"opponent": "sneasler", "rating": 379}, {"opponent": "spiritomb", "rating": 437}, {"opponent": "lickilicky", "rating": 496}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 20028}, {"moveId": "ASTONISH", "uses": 17472}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 11586}, {"moveId": "SHADOW_BALL", "uses": 3138}, {"moveId": "RETURN", "uses": 2464}, {"moveId": "PSYCHIC", "uses": 2727}, {"moveId": "POLTERGEIST", "uses": 2086}, {"moveId": "OMINOUS_WIND", "uses": 1605}, {"moveId": "DYNAMIC_PUNCH", "uses": 9646}, {"moveId": "DARK_PULSE", "uses": 4262}]}, "moveset": ["ASTONISH", "DYNAMIC_PUNCH", "SHADOW_PUNCH"], "score": 92.4, "stats": {"product": 1878, "atk": 119.8, "def": 174.1, "hp": 90}}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 596, "matchups": [{"opponent": "gastrodon", "rating": 753}, {"opponent": "sneasler", "rating": 672, "opRating": 327}, {"opponent": "gallade_shadow", "rating": 632, "opRating": 367}, {"opponent": "bibarel", "rating": 574, "opRating": 425}, {"opponent": "lickilicky", "rating": 537, "opRating": 462}], "counters": [{"opponent": "dusknoir_shadow", "rating": 138}, {"opponent": "drifb<PERSON>", "rating": 270}, {"opponent": "gliscor", "rating": 340}, {"opponent": "spiritomb", "rating": 350}, {"opponent": "drapion_shadow", "rating": 381}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 20256}, {"moveId": "CONFUSION", "uses": 17244}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 9913}, {"moveId": "GRASS_KNOT", "uses": 11688}, {"moveId": "FUTURE_SIGHT", "uses": 8730}, {"moveId": "AURORA_BEAM", "uses": 7182}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 92.2, "stats": {"product": 2416, "atk": 92.6, "def": 161, "hp": 162}}, {"speciesId": "cress<PERSON>a_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 566, "matchups": [{"opponent": "gallade_shadow", "rating": 756, "opRating": 243}, {"opponent": "gastrodon", "rating": 712}, {"opponent": "bibarel", "rating": 679, "opRating": 320}, {"opponent": "sneasler", "rating": 635, "opRating": 364}, {"opponent": "lickilicky", "rating": 577, "opRating": 422}], "counters": [{"opponent": "drifb<PERSON>", "rating": 110}, {"opponent": "dusknoir_shadow", "rating": 127}, {"opponent": "gliscor", "rating": 323}, {"opponent": "spiritomb", "rating": 408}, {"opponent": "drapion_shadow", "rating": 423}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 19972}, {"moveId": "CONFUSION", "uses": 17528}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 9909}, {"moveId": "GRASS_KNOT", "uses": 11646}, {"moveId": "FUTURE_SIGHT", "uses": 8718}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "AURORA_BEAM", "uses": 7175}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 92.2, "stats": {"product": 2414, "atk": 93.2, "def": 159.8, "hp": 162}}, {"speciesId": "gabite", "speciesName": "Gabite", "rating": 431, "matchups": [{"opponent": "magnezone_shadow", "rating": 935, "opRating": 64}, {"opponent": "bastiodon", "rating": 825, "opRating": 174}, {"opponent": "drapion_shadow", "rating": 670}, {"opponent": "bibarel", "rating": 560, "opRating": 439}, {"opponent": "qwilfish_his<PERSON>an", "rating": 549, "opRating": 450}], "counters": [{"opponent": "drifb<PERSON>", "rating": 76}, {"opponent": "gliscor", "rating": 125}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "gastrodon", "rating": 363}, {"opponent": "spiritomb", "rating": 394}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 4768}, {"moveId": "MUD_SHOT", "uses": 32732}], "chargedMoves": [{"moveId": "TWISTER", "uses": 7383}, {"moveId": "RETURN", "uses": 5550}, {"moveId": "FLAMETHROWER", "uses": 11087}, {"moveId": "DIG", "uses": 13495}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 92.1, "stats": {"product": 1741, "atk": 128.7, "def": 102.4, "hp": 132}}, {"speciesId": "gabite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 406, "matchups": [{"opponent": "magnezone_shadow", "rating": 939, "opRating": 60}, {"opponent": "magnezone", "rating": 935, "opRating": 64}, {"opponent": "bastiodon", "rating": 791, "opRating": 208}, {"opponent": "electrode_hisuian", "rating": 708, "opRating": 291}, {"opponent": "bibarel", "rating": 503, "opRating": 496}], "counters": [{"opponent": "drifb<PERSON>", "rating": 76}, {"opponent": "gliscor", "rating": 107}, {"opponent": "gastrodon", "rating": 166}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drapion_shadow", "rating": 330}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 4856}, {"moveId": "MUD_SHOT", "uses": 32644}], "chargedMoves": [{"moveId": "TWISTER", "uses": 8848}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 13150}, {"moveId": "DIG", "uses": 15407}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 92.1, "stats": {"product": 1741, "atk": 128.7, "def": 102.4, "hp": 132}}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 518, "matchups": [{"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "froslass", "rating": 789, "opRating": 210}, {"opponent": "bastiodon", "rating": 714, "opRating": 285}, {"opponent": "spiritomb", "rating": 680, "opRating": 319}, {"opponent": "vespiquen", "rating": 621, "opRating": 378}], "counters": [{"opponent": "gastrodon", "rating": 145}, {"opponent": "drapion_shadow", "rating": 237}, {"opponent": "drifb<PERSON>", "rating": 287}, {"opponent": "gliscor", "rating": 353}, {"opponent": "dusknoir_shadow", "rating": 355}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 6339}, {"moveId": "FIRE_SPIN", "uses": 31161}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 3159}, {"moveId": "FLAMETHROWER", "uses": 3638}, {"moveId": "CLOSE_COMBAT", "uses": 14844}, {"moveId": "BLAST_BURN", "uses": 15838}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 91.6, "stats": {"product": 1633, "atk": 136.6, "def": 100.4, "hp": 119}}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 541, "matchups": [{"opponent": "abomasnow_shadow", "rating": 890, "opRating": 109}, {"opponent": "froslass", "rating": 768, "opRating": 231}, {"opponent": "magnezone_shadow", "rating": 756, "opRating": 243}, {"opponent": "bastiodon", "rating": 655, "opRating": 344}, {"opponent": "spiritomb", "rating": 617, "opRating": 382}], "counters": [{"opponent": "gastrodon", "rating": 136}, {"opponent": "drapion_shadow", "rating": 279}, {"opponent": "gliscor", "rating": 306}, {"opponent": "drifb<PERSON>", "rating": 337}, {"opponent": "dusknoir_shadow", "rating": 338}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 5724}, {"moveId": "FIRE_SPIN", "uses": 31776}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 3159}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 3635}, {"moveId": "CLOSE_COMBAT", "uses": 14817}, {"moveId": "BLAST_BURN", "uses": 15820}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 91.6, "stats": {"product": 1633, "atk": 136.6, "def": 100.4, "hp": 119}}, {"speciesId": "mantyke", "speciesName": "Mantyke", "rating": 574, "matchups": [{"opponent": "gliscor", "rating": 754}, {"opponent": "gastrodon", "rating": 654}, {"opponent": "gallade_shadow", "rating": 654}, {"opponent": "drifb<PERSON>", "rating": 570}, {"opponent": "dusknoir_shadow", "rating": 537}], "counters": [{"opponent": "bastiodon", "rating": 384}, {"opponent": "lickilicky", "rating": 387}, {"opponent": "spiritomb", "rating": 394}, {"opponent": "abomasnow_shadow", "rating": 423}, {"opponent": "drapion_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 14078}, {"moveId": "BUBBLE", "uses": 23422}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 10741}, {"moveId": "ICE_BEAM", "uses": 10321}, {"moveId": "AERIAL_ACE", "uses": 16486}]}, "moveset": ["BUBBLE", "AERIAL_ACE", "WATER_PULSE"], "score": 91.2, "stats": {"product": 1973, "atk": 100.8, "def": 163, "hp": 120}}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 609, "matchups": [{"opponent": "drifb<PERSON>", "rating": 793}, {"opponent": "gliscor", "rating": 778}, {"opponent": "bastiodon", "rating": 735}, {"opponent": "gastrodon", "rating": 572}, {"opponent": "spiritomb", "rating": 572}], "counters": [{"opponent": "gallade_shadow", "rating": 235}, {"opponent": "lickilicky", "rating": 471}, {"opponent": "abomasnow_shadow", "rating": 472}, {"opponent": "dusknoir_shadow", "rating": 494}, {"opponent": "drapion_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 20171}, {"moveId": "MUD_SLAP", "uses": 17329}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 3947}, {"moveId": "ICICLE_SPEAR", "uses": 9189}, {"moveId": "HIGH_HORSEPOWER", "uses": 5464}, {"moveId": "BULLDOZE", "uses": 4476}, {"moveId": "AVALANCHE", "uses": 10958}, {"moveId": "ANCIENT_POWER", "uses": 3551}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 91.1, "stats": {"product": 1642, "atk": 136.2, "def": 87.3, "hp": 138}}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 562, "matchups": [{"opponent": "drifb<PERSON>", "rating": 768}, {"opponent": "gliscor", "rating": 721}, {"opponent": "vespiquen", "rating": 706, "opRating": 293}, {"opponent": "bastiodon", "rating": 670}, {"opponent": "dusknoir_shadow", "rating": 514}], "counters": [{"opponent": "drapion_shadow", "rating": 207}, {"opponent": "abomasnow_shadow", "rating": 223}, {"opponent": "gallade_shadow", "rating": 274}, {"opponent": "spiritomb", "rating": 490}, {"opponent": "gastrodon", "rating": 494}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 20290}, {"moveId": "MUD_SLAP", "uses": 17210}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 3949}, {"moveId": "ICICLE_SPEAR", "uses": 9190}, {"moveId": "HIGH_HORSEPOWER", "uses": 5465}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BULLDOZE", "uses": 4462}, {"moveId": "AVALANCHE", "uses": 10994}, {"moveId": "ANCIENT_POWER", "uses": 3534}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 91.1, "stats": {"product": 1642, "atk": 136.2, "def": 87.3, "hp": 138}}, {"speciesId": "chatot", "speciesName": "Chatot", "rating": 389, "matchups": [{"opponent": "kleavor", "rating": 755, "opRating": 244}, {"opponent": "giratina_origin", "rating": 693, "opRating": 306}, {"opponent": "rhyperior", "rating": 653, "opRating": 346}, {"opponent": "gliscor", "rating": 574}, {"opponent": "drifb<PERSON>", "rating": 537}], "counters": [{"opponent": "bastiodon", "rating": 154}, {"opponent": "drapion_shadow", "rating": 258}, {"opponent": "spiritomb", "rating": 331}, {"opponent": "dusknoir_shadow", "rating": 338}, {"opponent": "gastrodon", "rating": 473}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 20844}, {"moveId": "PECK", "uses": 16656}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 16651}, {"moveId": "NIGHT_SHADE", "uses": 16147}, {"moveId": "HEAT_WAVE", "uses": 4699}]}, "moveset": ["STEEL_WING", "SKY_ATTACK", "NIGHT_SHADE"], "score": 90.8, "stats": {"product": 1610, "atk": 139.6, "def": 78.4, "hp": 147}}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 395, "matchups": [{"opponent": "empoleon_shadow", "rating": 645, "opRating": 354}, {"opponent": "bronzong", "rating": 618, "opRating": 381}, {"opponent": "bibarel", "rating": 537, "opRating": 462}, {"opponent": "drapion", "rating": 526, "opRating": 473}, {"opponent": "spiritomb", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gastrodon", "rating": 101}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drapion_shadow", "rating": 190}, {"opponent": "drifb<PERSON>", "rating": 246}, {"opponent": "gliscor", "rating": 288}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20409}, {"moveId": "ASTONISH", "uses": 17091}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16326}, {"moveId": "THUNDER", "uses": 7110}, {"moveId": "HYDRO_PUMP", "uses": 14063}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 90.6, "stats": {"product": 1737, "atk": 128.8, "def": 144.9, "hp": 93}}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 662, "matchups": [{"opponent": "drapion_shadow", "rating": 653}, {"opponent": "abomasnow_shadow", "rating": 625}, {"opponent": "bastiodon", "rating": 556}, {"opponent": "gliscor", "rating": 528}, {"opponent": "dusknoir_shadow", "rating": 518}], "counters": [{"opponent": "gallade_shadow", "rating": 120}, {"opponent": "sneasler", "rating": 325}, {"opponent": "gastrodon", "rating": 330}, {"opponent": "spiritomb", "rating": 375}, {"opponent": "drifb<PERSON>", "rating": 406}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 2625}, {"moveId": "ROLLOUT", "uses": 21866}, {"moveId": "LICK", "uses": 13040}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4342}, {"moveId": "SHADOW_BALL", "uses": 9625}, {"moveId": "HYPER_BEAM", "uses": 3035}, {"moveId": "EARTHQUAKE", "uses": 7020}, {"moveId": "BODY_SLAM", "uses": 13475}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "EARTHQUAKE"], "score": 90.5, "stats": {"product": 2124, "atk": 105.7, "def": 125.5, "hp": 160}}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 655, "matchups": [{"opponent": "bastiodon", "rating": 889, "opRating": 110}, {"opponent": "abomasnow_shadow", "rating": 853, "opRating": 146}, {"opponent": "lickilicky", "rating": 778, "opRating": 221}, {"opponent": "drapion_shadow", "rating": 650}, {"opponent": "spiritomb", "rating": 597}], "counters": [{"opponent": "dusknoir_shadow", "rating": 161}, {"opponent": "gastrodon", "rating": 226}, {"opponent": "gallade_shadow", "rating": 293}, {"opponent": "drifb<PERSON>", "rating": 322}, {"opponent": "gliscor", "rating": 383}], "moves": {"fastMoves": [{"moveId": "FORCE_PALM", "uses": 13056}, {"moveId": "COUNTER", "uses": 9000}, {"moveId": "BULLET_PUNCH", "uses": 15457}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 5739}, {"moveId": "SHADOW_BALL", "uses": 5314}, {"moveId": "POWER_UP_PUNCH", "uses": 2137}, {"moveId": "FLASH_CANNON", "uses": 2799}, {"moveId": "CLOSE_COMBAT", "uses": 12110}, {"moveId": "BLAZE_KICK", "uses": 6709}, {"moveId": "AURA_SPHERE", "uses": 2740}]}, "moveset": ["FORCE_PALM", "THUNDER_PUNCH", "SHADOW_BALL"], "score": 90.5, "stats": {"product": 1544, "atk": 145.7, "def": 93.7, "hp": 113}}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 638, "matchups": [{"opponent": "dusknoir_shadow", "rating": 706}, {"opponent": "gallade_shadow", "rating": 706}, {"opponent": "drifb<PERSON>", "rating": 548}, {"opponent": "drapion_shadow", "rating": 533}, {"opponent": "qwilfish_his<PERSON>an", "rating": 519}], "counters": [{"opponent": "gastrodon", "rating": 336}, {"opponent": "gliscor", "rating": 366}, {"opponent": "gliscor_shadow", "rating": 426}, {"opponent": "bastiodon", "rating": 446}, {"opponent": "abomasnow_shadow", "rating": 475}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 24683}, {"moveId": "FEINT_ATTACK", "uses": 12817}], "chargedMoves": [{"moveId": "SHADOW_SNEAK", "uses": 7049}, {"moveId": "SHADOW_BALL", "uses": 12444}, {"moveId": "ROCK_TOMB", "uses": 14838}, {"moveId": "OMINOUS_WIND", "uses": 3177}]}, "moveset": ["SUCKER_PUNCH", "ROCK_TOMB", "SHADOW_BALL"], "score": 89.8, "stats": {"product": 1857, "atk": 120.7, "def": 147.8, "hp": 104}}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 656, "matchups": [{"opponent": "drifb<PERSON>", "rating": 849}, {"opponent": "sneasler", "rating": 776, "opRating": 223}, {"opponent": "gliscor", "rating": 730}, {"opponent": "gastrodon", "rating": 632}, {"opponent": "spiritomb", "rating": 524}], "counters": [{"opponent": "gallade_shadow", "rating": 264}, {"opponent": "bastiodon", "rating": 273}, {"opponent": "qwilfish_his<PERSON>an", "rating": 413}, {"opponent": "drapion_shadow", "rating": 423}, {"opponent": "dusknoir_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 5789}, {"moveId": "POWDER_SNOW", "uses": 19175}, {"moveId": "LEAFAGE", "uses": 12559}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 17049}, {"moveId": "OUTRAGE", "uses": 4523}, {"moveId": "ICY_WIND", "uses": 5770}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ENERGY_BALL", "uses": 7432}, {"moveId": "BLIZZARD", "uses": 2615}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 89.7, "stats": {"product": 1888, "atk": 117.8, "def": 112, "hp": 143}}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 633, "matchups": [{"opponent": "gliscor", "rating": 743}, {"opponent": "drifb<PERSON>", "rating": 632}, {"opponent": "spiritomb", "rating": 570}, {"opponent": "bastiodon", "rating": 535}, {"opponent": "drapion_shadow", "rating": 513}], "counters": [{"opponent": "gastrodon", "rating": 107}, {"opponent": "bibarel", "rating": 214}, {"opponent": "dusknoir_shadow", "rating": 394}, {"opponent": "gallade_shadow", "rating": 408}, {"opponent": "lickilicky", "rating": 478}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 23669}, {"moveId": "BUG_BITE", "uses": 13831}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 6388}, {"moveId": "MAGMA_STORM", "uses": 13160}, {"moveId": "IRON_HEAD", "uses": 5505}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 3566}, {"moveId": "FIRE_BLAST", "uses": 1892}, {"moveId": "EARTH_POWER", "uses": 7050}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 89.6, "stats": {"product": 1706, "atk": 130.7, "def": 115.4, "hp": 113}}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 630, "matchups": [{"opponent": "abomasnow_shadow", "rating": 941, "opRating": 58}, {"opponent": "gliscor", "rating": 756}, {"opponent": "qwilfish_his<PERSON>an", "rating": 599, "opRating": 400}, {"opponent": "drapion_shadow", "rating": 572}, {"opponent": "spiritomb", "rating": 554}], "counters": [{"opponent": "gastrodon", "rating": 86}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "gallade_shadow", "rating": 350}, {"opponent": "bastiodon", "rating": 420}, {"opponent": "drifb<PERSON>", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 23035}, {"moveId": "BUG_BITE", "uses": 14465}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 6365}, {"moveId": "MAGMA_STORM", "uses": 13203}, {"moveId": "IRON_HEAD", "uses": 5474}, {"moveId": "FLAMETHROWER", "uses": 3568}, {"moveId": "FIRE_BLAST", "uses": 1888}, {"moveId": "EARTH_POWER", "uses": 7052}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 89.6, "stats": {"product": 1697, "atk": 131.9, "def": 115.8, "hp": 111}}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "sneasler", "rating": 928, "opRating": 71}, {"opponent": "toxicroak", "rating": 910, "opRating": 89}, {"opponent": "hippo<PERSON><PERSON>", "rating": 660, "opRating": 339}, {"opponent": "drifb<PERSON>", "rating": 640}, {"opponent": "bibarel", "rating": 551, "opRating": 448}], "counters": [{"opponent": "spiritomb", "rating": 139}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gliscor", "rating": 288}, {"opponent": "gastrodon", "rating": 288}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 3127}, {"moveId": "TACKLE", "uses": 15054}, {"moveId": "CONFUSION", "uses": 19300}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15373}, {"moveId": "STOMP", "uses": 8372}, {"moveId": "PSYCHIC", "uses": 6104}, {"moveId": "MEGAHORN", "uses": 7591}]}, "moveset": ["CONFUSION", "WILD_CHARGE", "STOMP"], "score": 89.6, "stats": {"product": 1767, "atk": 126.6, "def": 95.5, "hp": 146}}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 513, "matchups": [{"opponent": "gastrodon", "rating": 897}, {"opponent": "electivire_shadow", "rating": 882, "opRating": 117}, {"opponent": "bibarel", "rating": 868, "opRating": 131}, {"opponent": "hippo<PERSON><PERSON>", "rating": 698, "opRating": 301}, {"opponent": "gallade_shadow", "rating": 663, "opRating": 336}], "counters": [{"opponent": "drapion_shadow", "rating": 233}, {"opponent": "drifb<PERSON>", "rating": 241}, {"opponent": "gliscor", "rating": 306}, {"opponent": "dusknoir_shadow", "rating": 394}, {"opponent": "spiritomb", "rating": 475}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20581}, {"moveId": "BITE", "uses": 16919}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4692}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ENERGY_BALL", "uses": 15899}, {"moveId": "BODY_SLAM", "uses": 16869}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 89.4, "stats": {"product": 1908, "atk": 117.8, "def": 114.8, "hp": 141}}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 480, "matchups": [{"opponent": "gastrodon", "rating": 911}, {"opponent": "bibarel", "rating": 872, "opRating": 127}, {"opponent": "hippo<PERSON><PERSON>", "rating": 787, "opRating": 212}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 698, "opRating": 301}, {"opponent": "electivire_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "drifb<PERSON>", "rating": 236}, {"opponent": "drapion_shadow", "rating": 258}, {"opponent": "gliscor", "rating": 262}, {"opponent": "spiritomb", "rating": 403}, {"opponent": "dusknoir_shadow", "rating": 405}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20173}, {"moveId": "BITE", "uses": 17327}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4315}, {"moveId": "RETURN", "uses": 3695}, {"moveId": "ENERGY_BALL", "uses": 14649}, {"moveId": "BODY_SLAM", "uses": 14894}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 89.4, "stats": {"product": 1908, "atk": 117.8, "def": 114.8, "hp": 141}}, {"speciesId": "lumineon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 491, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 609, "opRating": 390}, {"opponent": "lickilicky", "rating": 609, "opRating": 390}, {"opponent": "hippo<PERSON><PERSON>", "rating": 583, "opRating": 416}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 554, "opRating": 445}, {"opponent": "drifb<PERSON>", "rating": 547}], "counters": [{"opponent": "gastrodon", "rating": 270}, {"opponent": "dusknoir_shadow", "rating": 283}, {"opponent": "spiritomb", "rating": 293}, {"opponent": "drapion_shadow", "rating": 385}, {"opponent": "bastiodon", "rating": 485}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 18971}, {"moveId": "WATERFALL", "uses": 18529}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 15183}, {"moveId": "SILVER_WIND", "uses": 11146}, {"moveId": "BLIZZARD", "uses": 11113}]}, "moveset": ["WATER_GUN", "WATER_PULSE", "SILVER_WIND"], "score": 89.2, "stats": {"product": 2052, "atk": 109, "def": 137.4, "hp": 137}}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 607, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 628, "opRating": 371}, {"opponent": "drapion_shadow", "rating": 543}, {"opponent": "abomasnow_shadow", "rating": 541, "opRating": 458}, {"opponent": "gliscor", "rating": 513}, {"opponent": "gastrodon", "rating": 504}], "counters": [{"opponent": "drifb<PERSON>", "rating": 253}, {"opponent": "spiritomb", "rating": 254}, {"opponent": "gallade_shadow", "rating": 278}, {"opponent": "dusknoir_shadow", "rating": 327}, {"opponent": "bastiodon", "rating": 460}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 18331}, {"moveId": "LICK", "uses": 19169}], "chargedMoves": [{"moveId": "GUNK_SHOT", "uses": 5066}, {"moveId": "BULLDOZE", "uses": 11139}, {"moveId": "BODY_SLAM", "uses": 21261}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 89.1, "stats": {"product": 2172, "atk": 103.1, "def": 96.5, "hp": 218}}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 504, "matchups": [{"opponent": "electrode_hisuian", "rating": 796, "opRating": 203}, {"opponent": "magnezone_shadow", "rating": 656, "opRating": 343}, {"opponent": "gastrodon", "rating": 652}, {"opponent": "gallade_shadow", "rating": 648, "opRating": 351}, {"opponent": "drapion_shadow", "rating": 511}], "counters": [{"opponent": "drifb<PERSON>", "rating": 169}, {"opponent": "gliscor", "rating": 185}, {"opponent": "bastiodon", "rating": 269}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "spiritomb", "rating": 384}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 15685}, {"moveId": "BUG_BITE", "uses": 21815}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 6272}, {"moveId": "ENERGY_BALL", "uses": 17093}, {"moveId": "BUG_BUZZ", "uses": 14157}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 89.1, "stats": {"product": 2040, "atk": 110, "def": 144.8, "hp": 128}}, {"speciesId": "cranidos_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 164, "matchups": [], "counters": [{"opponent": "drifb<PERSON>", "rating": 64}, {"opponent": "dusknoir_shadow", "rating": 66}, {"opponent": "gastrodon", "rating": 104}, {"opponent": "drapion_shadow", "rating": 152}, {"opponent": "gliscor", "rating": 155}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 18232}, {"moveId": "TAKE_DOWN", "uses": 19268}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18108}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BULLDOZE", "uses": 8163}, {"moveId": "ANCIENT_POWER", "uses": 11176}]}, "moveset": ["TAKE_DOWN", "ANCIENT_POWER", "BULLDOZE"], "score": 89.1, "stats": {"product": 1372, "atk": 163.9, "def": 62.9, "hp": 133}}, {"speciesId": "cranidos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 150, "matchups": [], "counters": [{"opponent": "drifb<PERSON>", "rating": 52}, {"opponent": "dusknoir_shadow", "rating": 72}, {"opponent": "gastrodon", "rating": 89}, {"opponent": "gliscor", "rating": 125}, {"opponent": "drapion_shadow", "rating": 127}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 18104}, {"moveId": "TAKE_DOWN", "uses": 19396}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 16183}, {"moveId": "RETURN", "uses": 4158}, {"moveId": "BULLDOZE", "uses": 7233}, {"moveId": "ANCIENT_POWER", "uses": 9968}]}, "moveset": ["TAKE_DOWN", "ANCIENT_POWER", "BULLDOZE"], "score": 89.1, "stats": {"product": 1372, "atk": 163.9, "def": 62.9, "hp": 133}}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 375, "matchups": [{"opponent": "mantyke", "rating": 677, "opRating": 322}, {"opponent": "skuntank", "rating": 645, "opRating": 354}, {"opponent": "purugly", "rating": 629, "opRating": 370}, {"opponent": "spiritomb", "rating": 510, "opRating": 489}, {"opponent": "drifb<PERSON>_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gastrodon", "rating": 101}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drapion_shadow", "rating": 190}, {"opponent": "drifb<PERSON>", "rating": 241}, {"opponent": "gliscor", "rating": 288}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20965}, {"moveId": "ASTONISH", "uses": 16535}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 15132}, {"moveId": "THUNDER", "uses": 6522}, {"moveId": "BLIZZARD", "uses": 15870}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 88.3, "stats": {"product": 1737, "atk": 128.8, "def": 144.9, "hp": 93}}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 406, "matchups": [{"opponent": "toxicroak", "rating": 675, "opRating": 324}, {"opponent": "sneasler", "rating": 632, "opRating": 367}, {"opponent": "gastrodon", "rating": 572}, {"opponent": "gallade_shadow", "rating": 566, "opRating": 433}, {"opponent": "gliscor", "rating": 513}], "counters": [{"opponent": "bastiodon", "rating": 122}, {"opponent": "drifb<PERSON>", "rating": 193}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "dusknoir_shadow", "rating": 238}, {"opponent": "spiritomb", "rating": 240}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 6568}, {"moveId": "AIR_SLASH", "uses": 30932}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 4445}, {"moveId": "OMINOUS_WIND", "uses": 3520}, {"moveId": "FLY", "uses": 11389}, {"moveId": "DAZZLING_GLEAM", "uses": 4820}, {"moveId": "BRAVE_BIRD", "uses": 13362}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "FLY"], "score": 88.3, "stats": {"product": 1741, "atk": 128.5, "def": 89.6, "hp": 151}}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 600, "matchups": [{"opponent": "gastrodon", "rating": 744}, {"opponent": "drifb<PERSON>", "rating": 720}, {"opponent": "hippo<PERSON><PERSON>", "rating": 702, "opRating": 297}, {"opponent": "qwilfish_his<PERSON>an", "rating": 562, "opRating": 437}, {"opponent": "gliscor", "rating": 503}], "counters": [{"opponent": "gallade_shadow", "rating": 221}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "bastiodon", "rating": 320}, {"opponent": "spiritomb", "rating": 456}, {"opponent": "drapion_shadow", "rating": 487}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 6012}, {"moveId": "POWDER_SNOW", "uses": 18968}, {"moveId": "LEAFAGE", "uses": 12584}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 16003}, {"moveId": "RETURN", "uses": 2489}, {"moveId": "OUTRAGE", "uses": 4185}, {"moveId": "ICY_WIND", "uses": 5449}, {"moveId": "ENERGY_BALL", "uses": 6958}, {"moveId": "BLIZZARD", "uses": 2468}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 88, "stats": {"product": 1888, "atk": 117.8, "def": 112, "hp": 143}}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 623, "matchups": [{"opponent": "vespiquen", "rating": 800, "opRating": 199}, {"opponent": "bibarel", "rating": 703, "opRating": 296}, {"opponent": "drifb<PERSON>", "rating": 671}, {"opponent": "abomasnow_shadow", "rating": 643, "opRating": 356}, {"opponent": "gallade_shadow", "rating": 625, "opRating": 375}], "counters": [{"opponent": "drapion_shadow", "rating": 131}, {"opponent": "gastrodon", "rating": 145}, {"opponent": "spiritomb", "rating": 149}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 435}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19347}, {"moveId": "EMBER", "uses": 18153}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10401}, {"moveId": "SHADOW_BALL", "uses": 3628}, {"moveId": "OVERHEAT", "uses": 5782}, {"moveId": "NIGHT_SHADE", "uses": 7785}, {"moveId": "FIRE_PUNCH", "uses": 9994}]}, "moveset": ["HEX", "FIRE_PUNCH", "WILD_CHARGE"], "score": 88, "stats": {"product": 1611, "atk": 139.6, "def": 106.8, "hp": 108}}, {"speciesId": "growl<PERSON>e_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 531, "matchups": [{"opponent": "abomasnow_shadow", "rating": 897, "opRating": 102}, {"opponent": "abomasnow", "rating": 883, "opRating": 116}, {"opponent": "gliscor_shadow", "rating": 665, "opRating": 334}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 647, "opRating": 352}, {"opponent": "spiritomb", "rating": 521, "opRating": 478}], "counters": [{"opponent": "gastrodon", "rating": 104}, {"opponent": "drapion_shadow", "rating": 241}, {"opponent": "drifb<PERSON>", "rating": 275}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "gliscor", "rating": 487}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 27962}, {"moveId": "BITE", "uses": 9538}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 13107}, {"moveId": "FLAMETHROWER", "uses": 12364}, {"moveId": "CRUNCH", "uses": 12007}]}, "moveset": ["EMBER", "ROCK_SLIDE", "FLAMETHROWER"], "score": 87.5, "stats": {"product": 1684, "atk": 131.9, "def": 89.9, "hp": 142}}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 453, "matchups": [{"opponent": "cress<PERSON>a_shadow", "rating": 822, "opRating": 177}, {"opponent": "munchlax", "rating": 673, "opRating": 326}, {"opponent": "empoleon_shadow", "rating": 649, "opRating": 350}, {"opponent": "bastiodon", "rating": 524, "opRating": 475}, {"opponent": "gastrodon", "rating": 504}], "counters": [{"opponent": "drapion_shadow", "rating": 131}, {"opponent": "spiritomb", "rating": 149}, {"opponent": "dusknoir_shadow", "rating": 227}, {"opponent": "gliscor", "rating": 237}, {"opponent": "drifb<PERSON>", "rating": 301}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 9761}, {"moveId": "PSYWAVE", "uses": 10167}, {"moveId": "MAGICAL_LEAF", "uses": 6731}, {"moveId": "HEX", "uses": 10755}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 15923}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DAZZLING_GLEAM", "uses": 10405}, {"moveId": "DARK_PULSE", "uses": 11024}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 87.5, "stats": {"product": 1693, "atk": 131.6, "def": 123.6, "hp": 104}}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 465, "matchups": [{"opponent": "empoleon_shadow", "rating": 706, "opRating": 293}, {"opponent": "gallade", "rating": 576, "opRating": 423}, {"opponent": "bronzong", "rating": 548, "opRating": 451}, {"opponent": "gliscor", "rating": 538}, {"opponent": "drifb<PERSON>", "rating": 509}], "counters": [{"opponent": "drapion_shadow", "rating": 105}, {"opponent": "spiritomb", "rating": 168}, {"opponent": "dusknoir_shadow", "rating": 227}, {"opponent": "bastiodon", "rating": 446}, {"opponent": "gastrodon", "rating": 488}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 9850}, {"moveId": "PSYWAVE", "uses": 10384}, {"moveId": "MAGICAL_LEAF", "uses": 6812}, {"moveId": "HEX", "uses": 10476}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 15948}, {"moveId": "DAZZLING_GLEAM", "uses": 10411}, {"moveId": "DARK_PULSE", "uses": 11076}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 87.5, "stats": {"product": 1693, "atk": 131.6, "def": 123.6, "hp": 104}}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 585, "matchups": [{"opponent": "sneasler", "rating": 884, "opRating": 115}, {"opponent": "dusknoir_shadow", "rating": 839}, {"opponent": "drifb<PERSON>", "rating": 708}, {"opponent": "spiritomb", "rating": 559}, {"opponent": "abomasnow_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gliscor", "rating": 284}, {"opponent": "gastrodon", "rating": 339}, {"opponent": "bastiodon", "rating": 395}, {"opponent": "gallade_shadow", "rating": 437}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 26316}, {"moveId": "SCRATCH", "uses": 11184}], "chargedMoves": [{"moveId": "THUNDER", "uses": 10670}, {"moveId": "PLAY_ROUGH", "uses": 9197}, {"moveId": "FRUSTRATION", "uses": 2}, {"moveId": "AERIAL_ACE", "uses": 17575}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 86.9, "stats": {"product": 1775, "atk": 125.5, "def": 105.5, "hp": 134}}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 558, "matchups": [{"opponent": "sneasler", "rating": 884, "opRating": 115}, {"opponent": "dusknoir_shadow", "rating": 865}, {"opponent": "drifb<PERSON>", "rating": 574}, {"opponent": "bibarel", "rating": 544, "opRating": 455}, {"opponent": "abomasnow_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "bastiodon", "rating": 294}, {"opponent": "drapion_shadow", "rating": 326}, {"opponent": "gastrodon", "rating": 327}, {"opponent": "spiritomb", "rating": 365}, {"opponent": "gliscor", "rating": 366}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25812}, {"moveId": "SCRATCH", "uses": 11688}], "chargedMoves": [{"moveId": "THUNDER", "uses": 8651}, {"moveId": "RETURN", "uses": 7957}, {"moveId": "PLAY_ROUGH", "uses": 7136}, {"moveId": "AERIAL_ACE", "uses": 13743}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 86.9, "stats": {"product": 1775, "atk": 125.5, "def": 105.5, "hp": 134}}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 534, "matchups": [{"opponent": "vespiquen", "rating": 815, "opRating": 184}, {"opponent": "drifb<PERSON>", "rating": 651}, {"opponent": "abomasnow_shadow", "rating": 550, "opRating": 449}, {"opponent": "spiritomb", "rating": 512}, {"opponent": "bastiodon", "rating": 504}], "counters": [{"opponent": "gastrodon", "rating": 74}, {"opponent": "gallade_shadow", "rating": 177}, {"opponent": "dusknoir_shadow", "rating": 205}, {"opponent": "gliscor", "rating": 262}, {"opponent": "drapion_shadow", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 17331}, {"moveId": "ROCK_THROW", "uses": 20169}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 2321}, {"moveId": "THUNDERBOLT", "uses": 6464}, {"moveId": "ROCK_SLIDE", "uses": 11735}, {"moveId": "RETURN", "uses": 4420}, {"moveId": "MAGNET_BOMB", "uses": 12574}]}, "moveset": ["SPARK", "ROCK_SLIDE", "ZAP_CANNON"], "score": 86.9, "stats": {"product": 2320, "atk": 95.9, "def": 203.1, "hp": 119}}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 512, "matchups": [{"opponent": "vespiquen", "rating": 798, "opRating": 201}, {"opponent": "drifb<PERSON>", "rating": 785}, {"opponent": "abomasnow_shadow", "rating": 567, "opRating": 432}, {"opponent": "qwilfish_his<PERSON>an", "rating": 550, "opRating": 449}, {"opponent": "drapion_shadow", "rating": 516}], "counters": [{"opponent": "gastrodon", "rating": 83}, {"opponent": "dusknoir_shadow", "rating": 205}, {"opponent": "gliscor", "rating": 297}, {"opponent": "spiritomb", "rating": 365}, {"opponent": "bastiodon", "rating": 449}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 17554}, {"moveId": "ROCK_THROW", "uses": 19946}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 2574}, {"moveId": "THUNDERBOLT", "uses": 7234}, {"moveId": "ROCK_SLIDE", "uses": 13323}, {"moveId": "MAGNET_BOMB", "uses": 14288}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "ZAP_CANNON"], "score": 86.9, "stats": {"product": 2305, "atk": 97.3, "def": 198.9, "hp": 119}}, {"speciesId": "<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 502, "matchups": [{"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 873, "opRating": 126}, {"opponent": "gastrodon", "rating": 702}, {"opponent": "gallade_shadow", "rating": 702, "opRating": 297}, {"opponent": "abomasnow_shadow", "rating": 677, "opRating": 322}, {"opponent": "electivire_shadow", "rating": 677, "opRating": 322}], "counters": [{"opponent": "drifb<PERSON>", "rating": 138}, {"opponent": "gliscor", "rating": 193}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drapion_shadow", "rating": 338}, {"opponent": "spiritomb", "rating": 394}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 20731}, {"moveId": "AIR_SLASH", "uses": 16769}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 4456}, {"moveId": "BUG_BUZZ", "uses": 12208}, {"moveId": "AERIAL_ACE", "uses": 20857}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "BUG_BUZZ"], "score": 86.7, "stats": {"product": 1603, "atk": 140, "def": 82.9, "hp": 138}}, {"speciesId": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 635, "matchups": [{"opponent": "drifb<PERSON>", "rating": 856}, {"opponent": "drapion_shadow", "rating": 640}, {"opponent": "dusknoir_shadow", "rating": 625}, {"opponent": "spiritomb", "rating": 549}, {"opponent": "gliscor", "rating": 526}], "counters": [{"opponent": "gallade_shadow", "rating": 278}, {"opponent": "gastrodon", "rating": 395}, {"opponent": "bastiodon", "rating": 395}, {"opponent": "sneasler", "rating": 450}, {"opponent": "lickilicky", "rating": 453}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 10821}, {"moveId": "SNARL", "uses": 12423}, {"moveId": "FURY_CUTTER", "uses": 14303}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 8134}, {"moveId": "RAZOR_SHELL", "uses": 9878}, {"moveId": "ICY_WIND", "uses": 10539}, {"moveId": "DARK_PULSE", "uses": 8932}]}, "moveset": ["FURY_CUTTER", "DARK_PULSE", "ICY_WIND"], "score": 86.5, "stats": {"product": 1694, "atk": 132.6, "def": 96.7, "hp": 132}}, {"speciesId": "staravia", "speciesName": "Staravia", "rating": 538, "matchups": [{"opponent": "gastrodon", "rating": 696}, {"opponent": "qwilfish_his<PERSON>an", "rating": 607, "opRating": 392}, {"opponent": "gliscor", "rating": 600}, {"opponent": "gliscor_shadow", "rating": 574, "opRating": 425}, {"opponent": "lickilicky", "rating": 559, "opRating": 440}], "counters": [{"opponent": "bastiodon", "rating": 104}, {"opponent": "drapion_shadow", "rating": 237}, {"opponent": "drifb<PERSON>", "rating": 284}, {"opponent": "dusknoir_shadow", "rating": 455}, {"opponent": "spiritomb", "rating": 490}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 11124}, {"moveId": "SAND_ATTACK", "uses": 13128}, {"moveId": "QUICK_ATTACK", "uses": 13274}], "chargedMoves": [{"moveId": "RETURN", "uses": 3679}, {"moveId": "HEAT_WAVE", "uses": 2012}, {"moveId": "FLY", "uses": 10832}, {"moveId": "BRAVE_BIRD", "uses": 12755}, {"moveId": "AERIAL_ACE", "uses": 8268}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 86.3, "stats": {"product": 1631, "atk": 131.9, "def": 91.5, "hp": 135}}, {"speciesId": "staravia_shadow", "speciesName": "Staravia (Shadow)", "rating": 501, "matchups": [{"opponent": "dusknoir_shadow", "rating": 670}, {"opponent": "gastrodon", "rating": 637}, {"opponent": "gliscor", "rating": 574}, {"opponent": "qwilfish_his<PERSON>an", "rating": 529, "opRating": 470}, {"opponent": "spiritomb", "rating": 514}], "counters": [{"opponent": "bastiodon", "rating": 104}, {"opponent": "abomasnow_shadow", "rating": 223}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "drifb<PERSON>", "rating": 325}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 11154}, {"moveId": "SAND_ATTACK", "uses": 13045}, {"moveId": "QUICK_ATTACK", "uses": 13270}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 2228}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLY", "uses": 12007}, {"moveId": "BRAVE_BIRD", "uses": 14095}, {"moveId": "AERIAL_ACE", "uses": 9134}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 86.3, "stats": {"product": 1631, "atk": 131.9, "def": 91.5, "hp": 135}}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 645, "matchups": [{"opponent": "bastiodon", "rating": 863}, {"opponent": "sneasler", "rating": 818, "opRating": 181}, {"opponent": "drapion_shadow", "rating": 684}, {"opponent": "qwilfish_his<PERSON>an", "rating": 666, "opRating": 333}, {"opponent": "spiritomb", "rating": 663}], "counters": [{"opponent": "gliscor", "rating": 232}, {"opponent": "drifb<PERSON>", "rating": 258}, {"opponent": "gallade_shadow", "rating": 269}, {"opponent": "abomasnow_shadow", "rating": 367}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 6383}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2677}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1756}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2008}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1725}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1336}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2505}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2824}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1816}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1868}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2016}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2079}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1819}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1765}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1721}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1904}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1482}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 10075}, {"moveId": "EARTH_POWER", "uses": 11994}, {"moveId": "EARTHQUAKE", "uses": 4397}, {"moveId": "BODY_SLAM", "uses": 11043}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 85.6, "stats": {"product": 1967, "atk": 113.2, "def": 103.4, "hp": 168}}, {"speciesId": "qwilfish_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 691, "matchups": [{"opponent": "drifb<PERSON>", "rating": 880}, {"opponent": "sneasler", "rating": 830, "opRating": 169}, {"opponent": "dusknoir_shadow", "rating": 764}, {"opponent": "drapion_shadow", "rating": 661}, {"opponent": "abomasnow_shadow", "rating": 586, "opRating": 413}], "counters": [{"opponent": "gliscor", "rating": 327}, {"opponent": "gastrodon", "rating": 333}, {"opponent": "bastiodon", "rating": 399}, {"opponent": "gallade_shadow", "rating": 427}, {"opponent": "spiritomb", "rating": 480}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21091}, {"moveId": "POISON_JAB", "uses": 16409}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 5649}, {"moveId": "SHADOW_BALL", "uses": 5683}, {"moveId": "ICE_BEAM", "uses": 6397}, {"moveId": "DARK_PULSE", "uses": 7845}, {"moveId": "AQUA_TAIL", "uses": 11962}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 85.6, "stats": {"product": 1742, "atk": 128, "def": 112.3, "hp": 121}}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 689, "matchups": [{"opponent": "drifb<PERSON>", "rating": 881}, {"opponent": "sneasler", "rating": 831, "opRating": 168}, {"opponent": "dusknoir_shadow", "rating": 766}, {"opponent": "qwilfish_his<PERSON>an", "rating": 672, "opRating": 327}, {"opponent": "drapion_shadow", "rating": 651}], "counters": [{"opponent": "gliscor", "rating": 331}, {"opponent": "gastrodon", "rating": 339}, {"opponent": "bastiodon", "rating": 410}, {"opponent": "gallade_shadow", "rating": 432}, {"opponent": "spiritomb", "rating": 490}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 20909}, {"moveId": "POISON_JAB", "uses": 16591}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 5647}, {"moveId": "SHADOW_BALL", "uses": 5692}, {"moveId": "ICE_BEAM", "uses": 6420}, {"moveId": "DARK_PULSE", "uses": 7821}, {"moveId": "AQUA_TAIL", "uses": 11996}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 85.6, "stats": {"product": 1708, "atk": 131, "def": 106.8, "hp": 122}}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 661, "matchups": [{"opponent": "bastiodon", "rating": 762}, {"opponent": "gastrodon", "rating": 706}, {"opponent": "spiritomb", "rating": 573}, {"opponent": "drapion_shadow", "rating": 560}, {"opponent": "gallade_shadow", "rating": 556}], "counters": [{"opponent": "drifb<PERSON>", "rating": 253}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 321}, {"opponent": "abomasnow_shadow", "rating": 335}, {"opponent": "bibarel", "rating": 419}, {"opponent": "dusknoir_shadow", "rating": 472}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 10496}, {"moveId": "SAND_ATTACK", "uses": 13714}, {"moveId": "FURY_CUTTER", "uses": 13276}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 4323}, {"moveId": "NIGHT_SLASH", "uses": 12815}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 7541}, {"moveId": "AERIAL_ACE", "uses": 12760}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 85, "stats": {"product": 1939, "atk": 115.6, "def": 144.4, "hp": 116}}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 658, "matchups": [{"opponent": "gastrodon", "rating": 767}, {"opponent": "bastiodon", "rating": 737}, {"opponent": "qwilfish_his<PERSON>an", "rating": 672}, {"opponent": "gallade_shadow", "rating": 642}, {"opponent": "spiritomb", "rating": 633}], "counters": [{"opponent": "drifb<PERSON>", "rating": 208}, {"opponent": "abomasnow_shadow", "rating": 269}, {"opponent": "dusknoir_shadow", "rating": 333}, {"opponent": "drapion_shadow", "rating": 402}, {"opponent": "lickilicky", "rating": 471}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 10889}, {"moveId": "SAND_ATTACK", "uses": 13483}, {"moveId": "FURY_CUTTER", "uses": 13115}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 4384}, {"moveId": "NIGHT_SLASH", "uses": 12878}, {"moveId": "EARTHQUAKE", "uses": 7522}, {"moveId": "AERIAL_ACE", "uses": 12740}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 85, "stats": {"product": 1939, "atk": 115.6, "def": 144.4, "hp": 116}}, {"speciesId": "wormadam_trash", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Trash)", "rating": 600, "matchups": [{"opponent": "abomasnow_shadow", "rating": 777, "opRating": 222}, {"opponent": "lickilicky", "rating": 629, "opRating": 370}, {"opponent": "gliscor", "rating": 614}, {"opponent": "gallade_shadow", "rating": 600, "opRating": 400}, {"opponent": "qwilfish_his<PERSON>an", "rating": 555, "opRating": 444}], "counters": [{"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "drifb<PERSON>", "rating": 356}, {"opponent": "spiritomb", "rating": 384}, {"opponent": "gastrodon", "rating": 407}, {"opponent": "drapion_shadow", "rating": 411}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 14039}, {"moveId": "CONFUSION", "uses": 9973}, {"moveId": "BUG_BITE", "uses": 13400}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 6150}, {"moveId": "IRON_HEAD", "uses": 16202}, {"moveId": "BUG_BUZZ", "uses": 15106}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "IRON_HEAD"], "score": 85, "stats": {"product": 2142, "atk": 104.9, "def": 151.1, "hp": 135}}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 528, "matchups": [{"opponent": "drifb<PERSON>", "rating": 863}, {"opponent": "dusknoir_shadow", "rating": 731}, {"opponent": "abomasnow_shadow", "rating": 632, "opRating": 367}, {"opponent": "sneasler", "rating": 570, "opRating": 429}, {"opponent": "qwilfish_his<PERSON>an", "rating": 561, "opRating": 438}], "counters": [{"opponent": "gliscor", "rating": 125}, {"opponent": "bastiodon", "rating": 187}, {"opponent": "gastrodon", "rating": 416}, {"opponent": "spiritomb", "rating": 437}, {"opponent": "drapion_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15207}, {"moveId": "ICE_SHARD", "uses": 13683}, {"moveId": "FEINT_ATTACK", "uses": 8619}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 5744}, {"moveId": "FOUL_PLAY", "uses": 10355}, {"moveId": "FOCUS_BLAST", "uses": 5034}, {"moveId": "AVALANCHE", "uses": 16393}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 85, "stats": {"product": 1582, "atk": 141.9, "def": 105.1, "hp": 106}}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 483, "matchups": [{"opponent": "drifb<PERSON>", "rating": 830}, {"opponent": "dusknoir", "rating": 731, "opRating": 268}, {"opponent": "dusknoir_shadow", "rating": 665}, {"opponent": "spiritomb", "rating": 655}, {"opponent": "abomasnow_shadow", "rating": 599, "opRating": 400}], "counters": [{"opponent": "drapion_shadow", "rating": 88}, {"opponent": "gliscor", "rating": 155}, {"opponent": "bastiodon", "rating": 197}, {"opponent": "gallade_shadow", "rating": 206}, {"opponent": "gastrodon", "rating": 494}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15319}, {"moveId": "ICE_SHARD", "uses": 13839}, {"moveId": "FEINT_ATTACK", "uses": 8322}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 5714}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOUL_PLAY", "uses": 10369}, {"moveId": "FOCUS_BLAST", "uses": 5009}, {"moveId": "AVALANCHE", "uses": 16391}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 85, "stats": {"product": 1582, "atk": 141.9, "def": 105.1, "hp": 106}}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 458, "matchups": [{"opponent": "bibarel", "rating": 876, "opRating": 123}, {"opponent": "bastiodon", "rating": 760, "opRating": 239}, {"opponent": "gastrodon", "rating": 606}, {"opponent": "hippo<PERSON><PERSON>", "rating": 581, "opRating": 418}, {"opponent": "lickilicky", "rating": 547, "opRating": 452}], "counters": [{"opponent": "drifb<PERSON>", "rating": 78}, {"opponent": "gliscor", "rating": 125}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "drapion_shadow", "rating": 364}, {"opponent": "spiritomb", "rating": 475}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 17545}, {"moveId": "BULLET_SEED", "uses": 19955}], "chargedMoves": [{"moveId": "UPPER_HAND", "uses": 13590}, {"moveId": "SOLAR_BEAM", "uses": 2558}, {"moveId": "PETAL_BLIZZARD", "uses": 6273}, {"moveId": "CLOSE_COMBAT", "uses": 15010}]}, "moveset": ["BULLET_SEED", "PETAL_BLIZZARD", "UPPER_HAND"], "score": 84.9, "stats": {"product": 1683, "atk": 133.4, "def": 107.7, "hp": 117}}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 440, "matchups": [{"opponent": "spiritomb", "rating": 658}, {"opponent": "empoleon_shadow", "rating": 624, "opRating": 375}, {"opponent": "gastrodon", "rating": 617}, {"opponent": "bronzong", "rating": 610, "opRating": 389}, {"opponent": "dusknoir", "rating": 568, "opRating": 431}], "counters": [{"opponent": "bastiodon", "rating": 89}, {"opponent": "drapion_shadow", "rating": 105}, {"opponent": "gliscor", "rating": 155}, {"opponent": "drifb<PERSON>", "rating": 198}, {"opponent": "dusknoir_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27713}, {"moveId": "PECK", "uses": 9787}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 3947}, {"moveId": "PSYCHIC", "uses": 3982}, {"moveId": "DARK_PULSE", "uses": 11321}, {"moveId": "BRAVE_BIRD", "uses": 18243}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 84.1, "stats": {"product": 1500, "atk": 149.9, "def": 68.9, "hp": 145}}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 455, "matchups": [{"opponent": "rhyperior_shadow", "rating": 823, "opRating": 176}, {"opponent": "empoleon_shadow", "rating": 719, "opRating": 280}, {"opponent": "hippo<PERSON><PERSON>", "rating": 653, "opRating": 346}, {"opponent": "gastrodon", "rating": 638}, {"opponent": "bibarel", "rating": 638, "opRating": 361}], "counters": [{"opponent": "drapion_shadow", "rating": 118}, {"opponent": "gliscor", "rating": 176}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drifb<PERSON>", "rating": 380}, {"opponent": "spiritomb", "rating": 480}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 26118}, {"moveId": "BITE", "uses": 11382}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 16312}, {"moveId": "ENERGY_BALL", "uses": 5771}, {"moveId": "CRUNCH", "uses": 15291}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 84.1, "stats": {"product": 1724, "atk": 130.1, "def": 101.8, "hp": 130}}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 386, "matchups": [{"opponent": "cresselia", "rating": 913, "opRating": 86}, {"opponent": "spiritomb", "rating": 562}, {"opponent": "gastrodon", "rating": 551}, {"opponent": "bronzong", "rating": 548, "opRating": 451}, {"opponent": "empoleon_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "bastiodon", "rating": 79}, {"opponent": "drapion_shadow", "rating": 88}, {"opponent": "gliscor", "rating": 155}, {"opponent": "drifb<PERSON>", "rating": 232}, {"opponent": "dusknoir_shadow", "rating": 316}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28422}, {"moveId": "PECK", "uses": 9078}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 3958}, {"moveId": "PSYCHIC", "uses": 3993}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DARK_PULSE", "uses": 11319}, {"moveId": "BRAVE_BIRD", "uses": 18228}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 84.1, "stats": {"product": 1500, "atk": 149.9, "def": 68.9, "hp": 145}}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 697, "matchups": [{"opponent": "gallade_shadow", "rating": 919}, {"opponent": "dusknoir_shadow", "rating": 817}, {"opponent": "gliscor", "rating": 627}, {"opponent": "drifb<PERSON>", "rating": 597}, {"opponent": "abomasnow_shadow", "rating": 572}], "counters": [{"opponent": "lickilicky", "rating": 278}, {"opponent": "gastrodon", "rating": 279}, {"opponent": "sneasler", "rating": 352}, {"opponent": "bastiodon", "rating": 377}, {"opponent": "spiritomb", "rating": 480}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 12298}, {"moveId": "INFESTATION", "uses": 7972}, {"moveId": "ICE_FANG", "uses": 10705}, {"moveId": "BITE", "uses": 6568}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6228}, {"moveId": "RETURN", "uses": 3206}, {"moveId": "FELL_STINGER", "uses": 2218}, {"moveId": "CRUNCH", "uses": 11815}, {"moveId": "AQUA_TAIL", "uses": 13980}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 83, "stats": {"product": 1903, "atk": 117.8, "def": 136.8, "hp": 118}}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 735, "matchups": [{"opponent": "dusknoir", "rating": 817, "opRating": 182}, {"opponent": "dusknoir_shadow", "rating": 792}, {"opponent": "drifb<PERSON>", "rating": 669}, {"opponent": "gliscor", "rating": 597}, {"opponent": "abomasnow_shadow", "rating": 576}], "counters": [{"opponent": "gastrodon", "rating": 315}, {"opponent": "qwilfish_his<PERSON>an", "rating": 338}, {"opponent": "bastiodon", "rating": 431}, {"opponent": "gallade_shadow", "rating": 451}, {"opponent": "spiritomb", "rating": 466}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 12326}, {"moveId": "INFESTATION", "uses": 8037}, {"moveId": "ICE_FANG", "uses": 11023}, {"moveId": "BITE", "uses": 6129}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6917}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FELL_STINGER", "uses": 2508}, {"moveId": "CRUNCH", "uses": 12803}, {"moveId": "AQUA_TAIL", "uses": 15259}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 83, "stats": {"product": 1903, "atk": 117.8, "def": 136.8, "hp": 118}}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 499, "matchups": [{"opponent": "gallade", "rating": 863, "opRating": 136}, {"opponent": "gastrodon", "rating": 698}, {"opponent": "hippo<PERSON><PERSON>", "rating": 676, "opRating": 323}, {"opponent": "bibarel", "rating": 595, "opRating": 404}, {"opponent": "electivire_shadow", "rating": 522, "opRating": 477}], "counters": [{"opponent": "drapion_shadow", "rating": 144}, {"opponent": "gliscor", "rating": 176}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drifb<PERSON>", "rating": 334}, {"opponent": "spiritomb", "rating": 461}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 22690}, {"moveId": "INFESTATION", "uses": 14810}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 2946}, {"moveId": "SLUDGE_BOMB", "uses": 5847}, {"moveId": "ROCK_SLIDE", "uses": 9493}, {"moveId": "POWER_WHIP", "uses": 14259}, {"moveId": "ANCIENT_POWER", "uses": 4862}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 83, "stats": {"product": 1852, "atk": 120.7, "def": 112.7, "hp": 136}}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 463, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 654, "opRating": 345}, {"opponent": "gastrodon", "rating": 643}, {"opponent": "bastiodon", "rating": 613}, {"opponent": "spiritomb", "rating": 599}, {"opponent": "bibarel", "rating": 518, "opRating": 481}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gliscor", "rating": 176}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "gallade_shadow", "rating": 264}, {"opponent": "drifb<PERSON>", "rating": 387}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 23007}, {"moveId": "INFESTATION", "uses": 14493}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 2944}, {"moveId": "SLUDGE_BOMB", "uses": 5864}, {"moveId": "ROCK_SLIDE", "uses": 9739}, {"moveId": "POWER_WHIP", "uses": 14294}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ANCIENT_POWER", "uses": 4625}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 83, "stats": {"product": 1852, "atk": 120.7, "def": 112.7, "hp": 136}}, {"speciesId": "turtwig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 384, "matchups": [{"opponent": "gastrodon", "rating": 629}, {"opponent": "hippow<PERSON>_shadow", "rating": 618, "opRating": 381}, {"opponent": "electivire", "rating": 544, "opRating": 455}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "bibarel", "rating": 522, "opRating": 477}], "counters": [{"opponent": "drifb<PERSON>", "rating": 93}, {"opponent": "dusknoir_shadow", "rating": 127}, {"opponent": "drapion_shadow", "rating": 165}, {"opponent": "gliscor", "rating": 185}, {"opponent": "spiritomb", "rating": 187}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 18299}, {"moveId": "RAZOR_LEAF", "uses": 19201}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 14159}, {"moveId": "RETURN", "uses": 3253}, {"moveId": "ENERGY_BALL", "uses": 6351}, {"moveId": "BODY_SLAM", "uses": 13719}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 83, "stats": {"product": 1597, "atk": 112.6, "def": 105, "hp": 135}}, {"speciesId": "turtwig_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 374, "matchups": [{"opponent": "shellos", "rating": 659, "opRating": 340}, {"opponent": "luxray", "rating": 633, "opRating": 366}, {"opponent": "hippo<PERSON><PERSON>", "rating": 618, "opRating": 381}, {"opponent": "hippow<PERSON>_shadow", "rating": 585, "opRating": 414}, {"opponent": "gastrodon", "rating": 518}], "counters": [{"opponent": "drifb<PERSON>", "rating": 107}, {"opponent": "dusknoir_shadow", "rating": 127}, {"opponent": "gliscor", "rating": 185}, {"opponent": "spiritomb", "rating": 187}, {"opponent": "drapion_shadow", "rating": 245}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 17956}, {"moveId": "RAZOR_LEAF", "uses": 19544}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 15289}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ENERGY_BALL", "uses": 6876}, {"moveId": "BODY_SLAM", "uses": 15360}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 83, "stats": {"product": 1597, "atk": 112.6, "def": 105, "hp": 135}}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 505, "matchups": [{"opponent": "magmortar", "rating": 815, "opRating": 184}, {"opponent": "magmortar_shadow", "rating": 812, "opRating": 187}, {"opponent": "bastiodon", "rating": 657, "opRating": 342}, {"opponent": "bronzong", "rating": 533, "opRating": 466}, {"opponent": "spiritomb", "rating": 507, "opRating": 492}], "counters": [{"opponent": "drapion_shadow", "rating": 245}, {"opponent": "gastrodon", "rating": 264}, {"opponent": "drifb<PERSON>", "rating": 277}, {"opponent": "dusknoir_shadow", "rating": 372}, {"opponent": "gliscor", "rating": 418}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 18727}, {"moveId": "WATERFALL", "uses": 18773}], "chargedMoves": [{"moveId": "SWIFT", "uses": 10102}, {"moveId": "LIQUIDATION", "uses": 6546}, {"moveId": "HYDRO_PUMP", "uses": 2501}, {"moveId": "AQUA_JET", "uses": 18277}]}, "moveset": ["WATER_GUN", "AQUA_JET", "SWIFT"], "score": 82.9, "stats": {"product": 1564, "atk": 143.8, "def": 81.7, "hp": 133}}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 596, "matchups": [{"opponent": "drifb<PERSON>", "rating": 812}, {"opponent": "gallade_shadow", "rating": 812, "opRating": 187}, {"opponent": "gliscor_shadow", "rating": 786, "opRating": 213}, {"opponent": "sneasler", "rating": 734, "opRating": 265}, {"opponent": "dusknoir_shadow", "rating": 682}], "counters": [{"opponent": "spiritomb", "rating": 177}, {"opponent": "drapion_shadow", "rating": 194}, {"opponent": "gastrodon", "rating": 425}, {"opponent": "bastiodon", "rating": 435}, {"opponent": "gliscor", "rating": 456}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 14328}, {"moveId": "SHADOW_CLAW", "uses": 23172}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 5685}, {"moveId": "SHADOW_BALL", "uses": 12196}, {"moveId": "FOUL_PLAY", "uses": 11308}, {"moveId": "FLAMETHROWER", "uses": 8388}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "FOUL_PLAY"], "score": 82.4, "stats": {"product": 1374, "atk": 163.4, "def": 87.5, "hp": 96}}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 339, "matchups": [{"opponent": "gastrodon", "rating": 688}, {"opponent": "hippopotas", "rating": 669, "opRating": 330}, {"opponent": "bibarel", "rating": 653, "opRating": 346}, {"opponent": "hippow<PERSON>_shadow", "rating": 623, "opRating": 376}, {"opponent": "hippo<PERSON><PERSON>", "rating": 596, "opRating": 403}], "counters": [{"opponent": "drapion_shadow", "rating": 80}, {"opponent": "gliscor", "rating": 125}, {"opponent": "dusknoir_shadow", "rating": 161}, {"opponent": "drifb<PERSON>", "rating": 270}, {"opponent": "spiritomb", "rating": 475}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 14558}, {"moveId": "BULLET_SEED", "uses": 22942}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 13037}, {"moveId": "HYPER_BEAM", "uses": 7583}, {"moveId": "DAZZLING_GLEAM", "uses": 16921}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 82.3, "stats": {"product": 1843, "atk": 121.8, "def": 116.2, "hp": 130}}, {"speciesId": "drifb<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 611, "matchups": [{"opponent": "gallade_shadow", "rating": 880, "opRating": 119}, {"opponent": "gastrodon", "rating": 822}, {"opponent": "gliscor", "rating": 717}, {"opponent": "gliscor_shadow", "rating": 686, "opRating": 313}, {"opponent": "dusknoir_shadow", "rating": 511}], "counters": [{"opponent": "drapion_shadow", "rating": 207}, {"opponent": "bastiodon", "rating": 273}, {"opponent": "spiritomb", "rating": 293}, {"opponent": "abomasnow_shadow", "rating": 318}, {"opponent": "drifb<PERSON>", "rating": 459}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19356}, {"moveId": "ASTONISH", "uses": 18144}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 9955}, {"moveId": "OMINOUS_WIND", "uses": 5016}, {"moveId": "MYSTICAL_FIRE", "uses": 10497}, {"moveId": "ICY_WIND", "uses": 12032}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "ICY_WIND", "SHADOW_BALL"], "score": 82.2, "stats": {"product": 1887, "atk": 119.1, "def": 75.7, "hp": 209}}, {"speciesId": "rotom", "speciesName": "Rotom", "rating": 528, "matchups": [{"opponent": "gallade_shadow", "rating": 863, "opRating": 136}, {"opponent": "froslass", "rating": 698, "opRating": 301}, {"opponent": "electrode_hisuian", "rating": 655, "opRating": 344}, {"opponent": "gliscor_shadow", "rating": 594, "opRating": 405}, {"opponent": "drifb<PERSON>", "rating": 514}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "gastrodon", "rating": 252}, {"opponent": "spiritomb", "rating": 264}, {"opponent": "dusknoir_shadow", "rating": 394}, {"opponent": "gliscor", "rating": 495}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 17760}, {"moveId": "ASTONISH", "uses": 19740}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16510}, {"moveId": "THUNDER", "uses": 7153}, {"moveId": "OMINOUS_WIND", "uses": 13837}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 81.5, "stats": {"product": 1691, "atk": 132.4, "def": 120.4, "hp": 106}}, {"speciesId": "palkia_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 630, "matchups": [{"opponent": "gallade_shadow", "rating": 794, "opRating": 205}, {"opponent": "sneasler", "rating": 679, "opRating": 320}, {"opponent": "bibarel", "rating": 635, "opRating": 365}, {"opponent": "lickilicky", "rating": 530, "opRating": 470}, {"opponent": "bastiodon", "rating": 515, "opRating": 485}], "counters": [{"opponent": "drifb<PERSON>", "rating": 356}, {"opponent": "gliscor", "rating": 366}, {"opponent": "gastrodon", "rating": 395}, {"opponent": "drapion_shadow", "rating": 423}, {"opponent": "dusknoir_shadow", "rating": 427}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 18855}, {"moveId": "DRAGON_BREATH", "uses": 18645}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 2984}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_BLAST", "uses": 5022}, {"moveId": "DRACO_METEOR", "uses": 6089}, {"moveId": "AQUA_TAIL", "uses": 23252}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "DRACO_METEOR"], "score": 81.4, "stats": {"product": 1589, "atk": 141.3, "def": 112.4, "hp": 100}}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 549, "matchups": [{"opponent": "toxicroak", "rating": 911, "opRating": 88}, {"opponent": "sneasler", "rating": 826, "opRating": 173}, {"opponent": "gallade_shadow", "rating": 733, "opRating": 266}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 596, "opRating": 403}, {"opponent": "lickilicky", "rating": 572, "opRating": 427}], "counters": [{"opponent": "drifb<PERSON>", "rating": 232}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 275}, {"opponent": "gastrodon", "rating": 312}, {"opponent": "drapion_shadow", "rating": 347}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 16621}, {"moveId": "CONFUSION", "uses": 20879}], "chargedMoves": [{"moveId": "THUNDER", "uses": 9863}, {"moveId": "SWIFT", "uses": 17024}, {"moveId": "FUTURE_SIGHT", "uses": 10592}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 80.2, "stats": {"product": 2215, "atk": 101.3, "def": 176.1, "hp": 124}}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 475, "matchups": [{"opponent": "toxicroak", "rating": 875, "opRating": 125}, {"opponent": "sneasler", "rating": 823, "opRating": 176}, {"opponent": "electrode_hisuian", "rating": 676, "opRating": 323}, {"opponent": "munchlax", "rating": 556, "opRating": 443}, {"opponent": "bibarel", "rating": 543, "opRating": 456}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "drifb<PERSON>", "rating": 284}, {"opponent": "dusknoir_shadow", "rating": 305}, {"opponent": "gliscor", "rating": 422}, {"opponent": "gastrodon", "rating": 443}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 16495}, {"moveId": "CONFUSION", "uses": 21005}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16440}, {"moveId": "FUTURE_SIGHT", "uses": 10655}, {"moveId": "BLIZZARD", "uses": 10402}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 80.2, "stats": {"product": 1827, "atk": 122.4, "def": 128.6, "hp": 116}}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 456, "matchups": [{"opponent": "toxicroak", "rating": 878, "opRating": 121}, {"opponent": "roserade", "rating": 831, "opRating": 168}, {"opponent": "magmortar_shadow", "rating": 827, "opRating": 172}, {"opponent": "sneasler", "rating": 761, "opRating": 238}, {"opponent": "electivire_shadow", "rating": 742, "opRating": 257}], "counters": [{"opponent": "drapion_shadow", "rating": 190}, {"opponent": "dusknoir_shadow", "rating": 222}, {"opponent": "drifb<PERSON>", "rating": 289}, {"opponent": "gliscor", "rating": 306}, {"opponent": "gastrodon", "rating": 342}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 16580}, {"moveId": "CONFUSION", "uses": 20920}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17821}, {"moveId": "FUTURE_SIGHT", "uses": 11561}, {"moveId": "FIRE_BLAST", "uses": 8148}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 80.2, "stats": {"product": 1476, "atk": 150.9, "def": 91.4, "hp": 107}}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 682, "matchups": [{"opponent": "bastiodon", "rating": 855}, {"opponent": "abomasnow_shadow", "rating": 735}, {"opponent": "gastrodon", "rating": 730}, {"opponent": "qwilfish_his<PERSON>an", "rating": 572}, {"opponent": "drapion_shadow", "rating": 548}], "counters": [{"opponent": "dusknoir_shadow", "rating": 122}, {"opponent": "vespiquen", "rating": 196}, {"opponent": "spiritomb", "rating": 293}, {"opponent": "drifb<PERSON>", "rating": 294}, {"opponent": "gliscor", "rating": 357}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 18294}, {"moveId": "LOW_KICK", "uses": 2268}, {"moveId": "CONFUSION", "uses": 11466}, {"moveId": "CHARM", "uses": 5526}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 7154}, {"moveId": "PSYCHIC", "uses": 2215}, {"moveId": "LEAF_BLADE", "uses": 13506}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 14525}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 79.6, "stats": {"product": 1642, "atk": 136.5, "def": 115.6, "hp": 104}}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 680, "matchups": [{"opponent": "abomasnow_shadow", "rating": 778, "opRating": 221}, {"opponent": "gastrodon", "rating": 754}, {"opponent": "bastiodon", "rating": 745, "opRating": 254}, {"opponent": "lickilicky", "rating": 740, "opRating": 259}, {"opponent": "sneasler", "rating": 687, "opRating": 312}], "counters": [{"opponent": "drapion_shadow", "rating": 80}, {"opponent": "dusknoir_shadow", "rating": 138}, {"opponent": "drifb<PERSON>", "rating": 246}, {"opponent": "gliscor", "rating": 323}, {"opponent": "spiritomb", "rating": 394}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 17836}, {"moveId": "LOW_KICK", "uses": 2433}, {"moveId": "CONFUSION", "uses": 11466}, {"moveId": "CHARM", "uses": 5767}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 7163}, {"moveId": "PSYCHIC", "uses": 2233}, {"moveId": "LEAF_BLADE", "uses": 13532}, {"moveId": "CLOSE_COMBAT", "uses": 14499}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 79.6, "stats": {"product": 1642, "atk": 136.5, "def": 115.6, "hp": 104}}, {"speciesId": "dialga_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 644, "matchups": [{"opponent": "gallade_shadow", "rating": 883, "opRating": 116}, {"opponent": "sneasler", "rating": 700, "opRating": 299}, {"opponent": "bibarel", "rating": 630, "opRating": 369}, {"opponent": "gliscor_shadow", "rating": 598, "opRating": 401}, {"opponent": "gliscor", "rating": 584}], "counters": [{"opponent": "gastrodon", "rating": 252}, {"opponent": "drifb<PERSON>", "rating": 368}, {"opponent": "spiritomb", "rating": 418}, {"opponent": "drapion_shadow", "rating": 470}, {"opponent": "dusknoir_shadow", "rating": 494}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 15832}, {"moveId": "DRAGON_BREATH", "uses": 21668}], "chargedMoves": [{"moveId": "THUNDER", "uses": 10800}, {"moveId": "IRON_HEAD", "uses": 15312}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRACO_METEOR", "uses": 11404}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 79.6, "stats": {"product": 1623, "atk": 137.9, "def": 109.9, "hp": 107}}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 501, "matchups": [{"opponent": "gallade_shadow", "rating": 866, "opRating": 133}, {"opponent": "dusknoir_shadow", "rating": 826}, {"opponent": "dusknoir", "rating": 774, "opRating": 225}, {"opponent": "drifb<PERSON>", "rating": 697}, {"opponent": "froslass", "rating": 564, "opRating": 435}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "bastiodon", "rating": 334}, {"opponent": "gastrodon", "rating": 404}, {"opponent": "gliscor", "rating": 426}, {"opponent": "spiritomb", "rating": 475}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 12348}, {"moveId": "ASTONISH", "uses": 25152}], "chargedMoves": [{"moveId": "RETURN", "uses": 8547}, {"moveId": "LOW_SWEEP", "uses": 8626}, {"moveId": "HYPER_BEAM", "uses": 3302}, {"moveId": "AERIAL_ACE", "uses": 16969}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 79.6, "stats": {"product": 1670, "atk": 133.8, "def": 100.5, "hp": 124}}, {"speciesId": "snea<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 535, "matchups": [{"opponent": "vespiquen", "rating": 729, "opRating": 270}, {"opponent": "drapion_shadow", "rating": 626}, {"opponent": "abomasnow_shadow", "rating": 545, "opRating": 454}, {"opponent": "bibarel", "rating": 540, "opRating": 459}, {"opponent": "bastiodon", "rating": 504, "opRating": 495}], "counters": [{"opponent": "gastrodon", "rating": 151}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "spiritomb", "rating": 254}, {"opponent": "drifb<PERSON>", "rating": 311}, {"opponent": "gliscor", "rating": 426}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 8919}, {"moveId": "POISON_JAB", "uses": 28581}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 7490}, {"moveId": "RETURN", "uses": 3112}, {"moveId": "CLOSE_COMBAT", "uses": 17130}, {"moveId": "AERIAL_ACE", "uses": 9794}]}, "moveset": ["POISON_JAB", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 79.5, "stats": {"product": 1662, "atk": 133.9, "def": 111.7, "hp": 111}}, {"speciesId": "snea<PERSON>_<PERSON><PERSON>an_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) (<PERSON>)", "rating": 537, "matchups": [{"opponent": "bastiodon", "rating": 792, "opRating": 207}, {"opponent": "abomasnow_shadow", "rating": 779, "opRating": 220}, {"opponent": "vespiquen", "rating": 671, "opRating": 328}, {"opponent": "drapion_shadow", "rating": 585}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}], "counters": [{"opponent": "gastrodon", "rating": 145}, {"opponent": "gliscor", "rating": 211}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "spiritomb", "rating": 254}, {"opponent": "drifb<PERSON>", "rating": 363}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 8525}, {"moveId": "POISON_JAB", "uses": 28975}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 8134}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 18503}, {"moveId": "AERIAL_ACE", "uses": 10816}]}, "moveset": ["POISON_JAB", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 79.5, "stats": {"product": 1662, "atk": 133.9, "def": 111.7, "hp": 111}}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 639, "matchups": [{"opponent": "gliscor", "rating": 791}, {"opponent": "gliscor_shadow", "rating": 746, "opRating": 253}, {"opponent": "gastrodon", "rating": 741}, {"opponent": "gallade_shadow", "rating": 705, "opRating": 294}, {"opponent": "vespiquen", "rating": 660, "opRating": 339}], "counters": [{"opponent": "abomasnow_shadow", "rating": 150}, {"opponent": "bastiodon", "rating": 269}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "drapion_shadow", "rating": 330}, {"opponent": "spiritomb", "rating": 451}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19163}, {"moveId": "ASTONISH", "uses": 18337}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 9108}, {"moveId": "RETURN", "uses": 3341}, {"moveId": "OMINOUS_WIND", "uses": 4637}, {"moveId": "MYSTICAL_FIRE", "uses": 9497}, {"moveId": "ICY_WIND", "uses": 10906}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 79.3, "stats": {"product": 1887, "atk": 119.1, "def": 75.7, "hp": 209}}, {"speciesId": "drifloon", "speciesName": "Drifloon", "rating": 585, "matchups": [{"opponent": "gliscor", "rating": 766}, {"opponent": "gliscor_shadow", "rating": 723, "opRating": 276}, {"opponent": "gallade_shadow", "rating": 704, "opRating": 295}, {"opponent": "gastrodon", "rating": 698}, {"opponent": "vespiquen", "rating": 653, "opRating": 346}], "counters": [{"opponent": "bastiodon", "rating": 258}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "drapion_shadow", "rating": 300}, {"opponent": "drifb<PERSON>", "rating": 387}, {"opponent": "spiritomb", "rating": 413}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19037}, {"moveId": "ASTONISH", "uses": 18463}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 12176}, {"moveId": "RETURN", "uses": 4760}, {"moveId": "OMINOUS_WIND", "uses": 6278}, {"moveId": "ICY_WIND", "uses": 14380}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 79.3, "stats": {"product": 1647, "atk": 110.9, "def": 79.8, "hp": 186}}, {"speciesId": "drifloon_shadow", "speciesName": "Dr<PERSON><PERSON><PERSON> (Shadow)", "rating": 589, "matchups": [{"opponent": "gastrodon", "rating": 801}, {"opponent": "gliscor", "rating": 723}, {"opponent": "gliscor_shadow", "rating": 690, "opRating": 309}, {"opponent": "gallade_shadow", "rating": 645, "opRating": 354}, {"opponent": "vespiquen", "rating": 588, "opRating": 411}], "counters": [{"opponent": "spiritomb", "rating": 168}, {"opponent": "bastiodon", "rating": 251}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "drifb<PERSON>", "rating": 255}, {"opponent": "drapion_shadow", "rating": 296}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19134}, {"moveId": "ASTONISH", "uses": 18366}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 13702}, {"moveId": "OMINOUS_WIND", "uses": 6996}, {"moveId": "ICY_WIND", "uses": 16743}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 79.3, "stats": {"product": 1647, "atk": 110.9, "def": 79.8, "hp": 186}}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 666, "matchups": [{"opponent": "bastiodon", "rating": 841, "opRating": 158}, {"opponent": "sneasler", "rating": 661, "opRating": 338}, {"opponent": "qwilfish_his<PERSON>an", "rating": 658, "opRating": 341}, {"opponent": "drapion_shadow", "rating": 633}, {"opponent": "dusknoir_shadow", "rating": 514}], "counters": [{"opponent": "gliscor", "rating": 245}, {"opponent": "gallade_shadow", "rating": 317}, {"opponent": "gastrodon", "rating": 375}, {"opponent": "drifb<PERSON>", "rating": 392}, {"opponent": "spiritomb", "rating": 418}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 6916}, {"moveId": "SAND_ATTACK", "uses": 10390}, {"moveId": "ICE_FANG", "uses": 8708}, {"moveId": "FIRE_FANG", "uses": 7830}, {"moveId": "BITE", "uses": 3688}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 11551}, {"moveId": "STONE_EDGE", "uses": 3138}, {"moveId": "SCORCHING_SANDS", "uses": 9695}, {"moveId": "EARTH_POWER", "uses": 3798}, {"moveId": "EARTHQUAKE", "uses": 2804}, {"moveId": "BODY_SLAM", "uses": 6619}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 77.8, "stats": {"product": 1926, "atk": 116.1, "def": 116.7, "hp": 142}}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 676, "matchups": [{"opponent": "bastiodon", "rating": 873, "opRating": 126}, {"opponent": "sneasler", "rating": 785, "opRating": 214}, {"opponent": "qwilfish_his<PERSON>an", "rating": 633, "opRating": 366}, {"opponent": "drapion_shadow", "rating": 605}, {"opponent": "spiritomb", "rating": 602}], "counters": [{"opponent": "gallade_shadow", "rating": 250}, {"opponent": "gliscor", "rating": 271}, {"opponent": "drifb<PERSON>", "rating": 291}, {"opponent": "dusknoir_shadow", "rating": 433}, {"opponent": "gastrodon", "rating": 497}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 6758}, {"moveId": "SAND_ATTACK", "uses": 11126}, {"moveId": "ICE_FANG", "uses": 8630}, {"moveId": "FIRE_FANG", "uses": 7699}, {"moveId": "BITE", "uses": 3271}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 11539}, {"moveId": "STONE_EDGE", "uses": 3133}, {"moveId": "SCORCHING_SANDS", "uses": 9727}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTH_POWER", "uses": 3795}, {"moveId": "EARTHQUAKE", "uses": 2825}, {"moveId": "BODY_SLAM", "uses": 6566}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 77.8, "stats": {"product": 1926, "atk": 116.1, "def": 116.7, "hp": 142}}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 600, "matchups": [{"opponent": "gallade_shadow", "rating": 732, "opRating": 267}, {"opponent": "gliscor", "rating": 669}, {"opponent": "gastrodon", "rating": 663}, {"opponent": "gliscor_shadow", "rating": 640, "opRating": 359}, {"opponent": "vespiquen", "rating": 591, "opRating": 408}], "counters": [{"opponent": "drapion_shadow", "rating": 207}, {"opponent": "drifb<PERSON>", "rating": 332}, {"opponent": "spiritomb", "rating": 346}, {"opponent": "bastiodon", "rating": 417}, {"opponent": "dusknoir_shadow", "rating": 433}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 20549}, {"moveId": "DRAGON_TAIL", "uses": 16951}], "chargedMoves": [{"moveId": "SHADOW_FORCE", "uses": 1702}, {"moveId": "SHADOW_BALL", "uses": 16572}, {"moveId": "OMINOUS_WIND", "uses": 8482}, {"moveId": "DRAGON_PULSE", "uses": 10754}]}, "moveset": ["SHADOW_CLAW", "OMINOUS_WIND", "SHADOW_BALL"], "score": 77.8, "stats": {"product": 1895, "atk": 118.4, "def": 104.5, "hp": 153}}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 563, "matchups": [{"opponent": "abomasnow_shadow", "rating": 895, "opRating": 104}, {"opponent": "empoleon_shadow", "rating": 823, "opRating": 176}, {"opponent": "spiritomb", "rating": 744, "opRating": 255}, {"opponent": "bastiodon", "rating": 651, "opRating": 348}, {"opponent": "vespiquen", "rating": 568, "opRating": 431}], "counters": [{"opponent": "gastrodon", "rating": 166}, {"opponent": "drifb<PERSON>", "rating": 296}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "drapion_shadow", "rating": 360}, {"opponent": "gliscor", "rating": 362}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 8846}, {"moveId": "EMBER", "uses": 28654}], "chargedMoves": [{"moveId": "RETURN", "uses": 6326}, {"moveId": "LOW_SWEEP", "uses": 10536}, {"moveId": "FLAME_WHEEL", "uses": 3787}, {"moveId": "FLAMETHROWER", "uses": 16895}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 77.4, "stats": {"product": 1731, "atk": 129.6, "def": 96, "hp": 139}}, {"speciesId": "monferno_shadow", "speciesName": "Mon<PERSON> (Shadow)", "rating": 592, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "empoleon_shadow", "rating": 780, "opRating": 219}, {"opponent": "vespiquen", "rating": 726, "opRating": 273}, {"opponent": "spiritomb", "rating": 694, "opRating": 305}, {"opponent": "bastiodon", "rating": 607, "opRating": 392}], "counters": [{"opponent": "gastrodon", "rating": 163}, {"opponent": "gliscor", "rating": 262}, {"opponent": "drapion_shadow", "rating": 275}, {"opponent": "drifb<PERSON>", "rating": 349}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 8663}, {"moveId": "EMBER", "uses": 28837}], "chargedMoves": [{"moveId": "LOW_SWEEP", "uses": 12767}, {"moveId": "FRUSTRATION", "uses": 1}, {"moveId": "FLAME_WHEEL", "uses": 4530}, {"moveId": "FLAMETHROWER", "uses": 20184}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 77.4, "stats": {"product": 1731, "atk": 129.6, "def": 96, "hp": 139}}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 563, "matchups": [{"opponent": "bastiodon", "rating": 794, "opRating": 205}, {"opponent": "gliscor_shadow", "rating": 794, "opRating": 205}, {"opponent": "lickilicky", "rating": 733, "opRating": 266}, {"opponent": "qwilfish_his<PERSON>an", "rating": 568, "opRating": 431}, {"opponent": "abomasnow_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "dusknoir_shadow", "rating": 261}, {"opponent": "gastrodon", "rating": 327}, {"opponent": "drifb<PERSON>", "rating": 423}, {"opponent": "drapion_shadow", "rating": 453}, {"opponent": "gliscor", "rating": 478}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 2482}, {"moveId": "LOW_KICK", "uses": 4146}, {"moveId": "DOUBLE_KICK", "uses": 30852}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 14258}, {"moveId": "HYPER_BEAM", "uses": 4461}, {"moveId": "FOCUS_BLAST", "uses": 6551}, {"moveId": "FIRE_PUNCH", "uses": 12206}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FOCUS_BLAST"], "score": 76.5, "stats": {"product": 1996, "atk": 112, "def": 143.6, "hp": 124}}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 627, "matchups": [{"opponent": "bastiodon", "rating": 839, "opRating": 160}, {"opponent": "lickilicky", "rating": 674, "opRating": 325}, {"opponent": "drapion_shadow", "rating": 620}, {"opponent": "vespiquen", "rating": 540, "opRating": 459}, {"opponent": "dusknoir_shadow", "rating": 526}], "counters": [{"opponent": "gastrodon", "rating": 181}, {"opponent": "gliscor", "rating": 383}, {"opponent": "gallade_shadow", "rating": 389}, {"opponent": "spiritomb", "rating": 413}, {"opponent": "drifb<PERSON>", "rating": 464}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 18891}, {"moveId": "ROCK_SMASH", "uses": 4178}, {"moveId": "POISON_JAB", "uses": 14414}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 8145}, {"moveId": "CLOSE_COMBAT", "uses": 18518}, {"moveId": "AERIAL_ACE", "uses": 10838}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 76.4, "stats": {"product": 1543, "atk": 145.4, "def": 94.7, "hp": 112}}, {"speciesId": "snea<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 607, "matchups": [{"opponent": "bastiodon", "rating": 808}, {"opponent": "drifb<PERSON>", "rating": 714}, {"opponent": "spiritomb", "rating": 647}, {"opponent": "gallade_shadow", "rating": 531}, {"opponent": "drapion_shadow", "rating": 526}], "counters": [{"opponent": "gastrodon", "rating": 169}, {"opponent": "qwilfish_his<PERSON>an", "rating": 169}, {"opponent": "abomasnow_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 409}, {"opponent": "dusknoir_shadow", "rating": 450}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 19047}, {"moveId": "ROCK_SMASH", "uses": 3842}, {"moveId": "POISON_JAB", "uses": 14644}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 8123}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 18495}, {"moveId": "AERIAL_ACE", "uses": 10898}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 76.4, "stats": {"product": 1543, "atk": 145.4, "def": 94.7, "hp": 112}}, {"speciesId": "rotom_fan", "speciesName": "<PERSON><PERSON><PERSON> (Fan)", "rating": 443, "matchups": [{"opponent": "bronzong", "rating": 629, "opRating": 370}, {"opponent": "dusknoir_shadow", "rating": 618}, {"opponent": "gliscor", "rating": 586}, {"opponent": "gallade_shadow", "rating": 548}, {"opponent": "spiritomb", "rating": 510}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "abomasnow_shadow", "rating": 234}, {"opponent": "bastiodon", "rating": 241}, {"opponent": "drifb<PERSON>", "rating": 387}, {"opponent": "gastrodon", "rating": 494}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 21013}, {"moveId": "AIR_SLASH", "uses": 16487}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18191}, {"moveId": "THUNDER", "uses": 7907}, {"moveId": "OMINOUS_WIND", "uses": 11362}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 76.4, "stats": {"product": 1737, "atk": 128.8, "def": 144.9, "hp": 93}}, {"speciesId": "prin<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 514, "matchups": [{"opponent": "bastiodon", "rating": 731, "opRating": 268}, {"opponent": "gliscor", "rating": 672}, {"opponent": "lickilicky", "rating": 628, "opRating": 371}, {"opponent": "gliscor_shadow", "rating": 588, "opRating": 411}, {"opponent": "qwilfish_his<PERSON>an", "rating": 566, "opRating": 433}], "counters": [{"opponent": "dusknoir_shadow", "rating": 238}, {"opponent": "gastrodon", "rating": 375}, {"opponent": "spiritomb", "rating": 403}, {"opponent": "drapion_shadow", "rating": 444}, {"opponent": "drifb<PERSON>", "rating": 444}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 15138}, {"moveId": "BUBBLE", "uses": 22362}], "chargedMoves": [{"moveId": "RETURN", "uses": 5477}, {"moveId": "ICY_WIND", "uses": 17199}, {"moveId": "HYDRO_PUMP", "uses": 8487}, {"moveId": "BUBBLE_BEAM", "uses": 6312}]}, "moveset": ["BUBBLE", "ICY_WIND", "HYDRO_PUMP"], "score": 76.1, "stats": {"product": 1889, "atk": 118.6, "def": 117, "hp": 136}}, {"speciesId": "prinplup_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 480, "matchups": [{"opponent": "bastiodon", "rating": 702, "opRating": 297}, {"opponent": "bibarel", "rating": 654, "opRating": 345}, {"opponent": "lickilicky", "rating": 591, "opRating": 408}, {"opponent": "gliscor", "rating": 588}, {"opponent": "qwilfish_his<PERSON>an", "rating": 518, "opRating": 481}], "counters": [{"opponent": "drapion_shadow", "rating": 241}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "gastrodon", "rating": 404}, {"opponent": "spiritomb", "rating": 461}, {"opponent": "drifb<PERSON>", "rating": 466}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 14958}, {"moveId": "BUBBLE", "uses": 22542}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 20216}, {"moveId": "HYDRO_PUMP", "uses": 9942}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BUBBLE_BEAM", "uses": 7275}]}, "moveset": ["BUBBLE", "ICY_WIND", "HYDRO_PUMP"], "score": 76.1, "stats": {"product": 1889, "atk": 118.6, "def": 117, "hp": 136}}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 568, "matchups": [{"opponent": "bibarel", "rating": 883, "opRating": 116}, {"opponent": "sneasler", "rating": 778, "opRating": 221}, {"opponent": "lickilicky", "rating": 754, "opRating": 245}, {"opponent": "bastiodon", "rating": 733, "opRating": 266}, {"opponent": "empoleon_shadow", "rating": 729, "opRating": 270}], "counters": [{"opponent": "drifb<PERSON>", "rating": 227}, {"opponent": "gliscor", "rating": 232}, {"opponent": "dusknoir_shadow", "rating": 294}, {"opponent": "gastrodon", "rating": 437}, {"opponent": "drapion_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 19290}, {"moveId": "MAGICAL_LEAF", "uses": 18210}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 10516}, {"moveId": "NIGHT_SHADE", "uses": 7195}, {"moveId": "ENERGY_BALL", "uses": 3052}, {"moveId": "AURA_SPHERE", "uses": 8722}, {"moveId": "AERIAL_ACE", "uses": 7952}]}, "moveset": ["PSYCHO_CUT", "AERIAL_ACE", "AURA_SPHERE"], "score": 76, "stats": {"product": 1729, "atk": 129.6, "def": 107.5, "hp": 124}}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 626, "matchups": [{"opponent": "drifb<PERSON>", "rating": 773}, {"opponent": "gliscor", "rating": 745}, {"opponent": "sneasler", "rating": 703, "opRating": 296}, {"opponent": "spiritomb", "rating": 578}, {"opponent": "bastiodon", "rating": 523}], "counters": [{"opponent": "gastrodon", "rating": 98}, {"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "drapion_shadow", "rating": 173}, {"opponent": "gallade_shadow", "rating": 235}, {"opponent": "dusknoir_shadow", "rating": 466}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34131}, {"moveId": "LOW_KICK", "uses": 3369}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13201}, {"moveId": "THUNDER_PUNCH", "uses": 7588}, {"moveId": "THUNDER", "uses": 2138}, {"moveId": "ICE_PUNCH", "uses": 9439}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 5189}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 75.9, "stats": {"product": 1562, "atk": 143.3, "def": 100.8, "hp": 108}}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 624, "matchups": [{"opponent": "drifb<PERSON>", "rating": 800}, {"opponent": "gliscor", "rating": 745}, {"opponent": "spiritomb", "rating": 550}, {"opponent": "dusknoir_shadow", "rating": 541}, {"opponent": "gallade_shadow", "rating": 504}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gastrodon", "rating": 247}, {"opponent": "bastiodon", "rating": 284}, {"opponent": "abomasnow_shadow", "rating": 307}, {"opponent": "lickilicky", "rating": 434}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 33609}, {"moveId": "LOW_KICK", "uses": 3891}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13163}, {"moveId": "THUNDER_PUNCH", "uses": 7631}, {"moveId": "THUNDER", "uses": 2138}, {"moveId": "ICE_PUNCH", "uses": 9452}, {"moveId": "FLAMETHROWER", "uses": 5131}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 75.9, "stats": {"product": 1562, "atk": 143.3, "def": 100.8, "hp": 108}}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 507, "matchups": [{"opponent": "gliscor", "rating": 736}, {"opponent": "abomasnow_shadow", "rating": 655, "opRating": 344}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 655, "opRating": 344}, {"opponent": "spiritomb", "rating": 575}, {"opponent": "gliscor_shadow", "rating": 575, "opRating": 424}], "counters": [{"opponent": "gastrodon", "rating": 62}, {"opponent": "drapion_shadow", "rating": 173}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drifb<PERSON>", "rating": 246}, {"opponent": "bastiodon", "rating": 258}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 21649}, {"moveId": "ASTONISH", "uses": 15851}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 14176}, {"moveId": "THUNDER", "uses": 6150}, {"moveId": "OVERHEAT", "uses": 17148}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 73.6, "stats": {"product": 1737, "atk": 128.8, "def": 144.9, "hp": 93}}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 448, "matchups": [{"opponent": "empoleon_shadow", "rating": 801, "opRating": 198}, {"opponent": "bibarel", "rating": 650, "opRating": 349}, {"opponent": "bronzong", "rating": 607, "opRating": 392}, {"opponent": "magnezone_shadow", "rating": 602, "opRating": 397}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "opRating": 478}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gliscor", "rating": 206}, {"opponent": "gastrodon", "rating": 282}, {"opponent": "drifb<PERSON>", "rating": 358}, {"opponent": "dusknoir_shadow", "rating": 405}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 19413}, {"moveId": "ASTONISH", "uses": 18087}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18192}, {"moveId": "THUNDER", "uses": 7923}, {"moveId": "OMINOUS_WIND", "uses": 11369}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 73.3, "stats": {"product": 1737, "atk": 128.8, "def": 144.9, "hp": 93}}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 473, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 753, "opRating": 246}, {"opponent": "abomasnow_shadow", "rating": 719, "opRating": 280}, {"opponent": "bibarel", "rating": 692, "opRating": 307}, {"opponent": "gastrodon", "rating": 688}, {"opponent": "spiritomb", "rating": 619}], "counters": [{"opponent": "gliscor", "rating": 125}, {"opponent": "drifb<PERSON>", "rating": 200}, {"opponent": "drapion_shadow", "rating": 245}, {"opponent": "dusknoir_shadow", "rating": 333}, {"opponent": "bastiodon", "rating": 370}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 12404}, {"moveId": "BULLET_SEED", "uses": 25096}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 19145}, {"moveId": "SOLAR_BEAM", "uses": 7098}, {"moveId": "HYPER_BEAM", "uses": 3395}, {"moveId": "DAZZLING_GLEAM", "uses": 7837}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 72.6, "stats": {"product": 1843, "atk": 121.8, "def": 116.2, "hp": 130}}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 688, "matchups": [{"opponent": "drifb<PERSON>", "rating": 839}, {"opponent": "qwilfish_his<PERSON>an", "rating": 800, "opRating": 200}, {"opponent": "dusknoir_shadow", "rating": 708}, {"opponent": "drapion_shadow", "rating": 678}, {"opponent": "spiritomb", "rating": 604}], "counters": [{"opponent": "gliscor", "rating": 241}, {"opponent": "abomasnow_shadow", "rating": 255}, {"opponent": "gastrodon", "rating": 366}, {"opponent": "gallade_shadow", "rating": 379}, {"opponent": "bastiodon", "rating": 413}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 26915}, {"moveId": "TACKLE", "uses": 10585}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19359}, {"moveId": "SWIFT", "uses": 9791}, {"moveId": "ENERGY_BALL", "uses": 8328}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 71.4, "stats": {"product": 1825, "atk": 123.2, "def": 128.7, "hp": 115}}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 427, "matchups": [{"opponent": "sneasler", "rating": 824, "opRating": 175}, {"opponent": "snea<PERSON>_shadow", "rating": 824, "opRating": 175}, {"opponent": "toxicroak", "rating": 739, "opRating": 260}, {"opponent": "gliscor_shadow", "rating": 653, "opRating": 346}, {"opponent": "drifb<PERSON>", "rating": 585}], "counters": [{"opponent": "dusknoir_shadow", "rating": 144}, {"opponent": "drapion_shadow", "rating": 148}, {"opponent": "spiritomb", "rating": 192}, {"opponent": "gliscor", "rating": 362}, {"opponent": "gastrodon", "rating": 386}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 6521}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1970}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1667}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1884}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1656}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1255}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2432}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2137}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1916}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1747}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1891}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2010}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1810}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1748}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1598}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1766}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1434}, {"moveId": "CHARGE_BEAM", "uses": 2170}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 7025}, {"moveId": "TRI_ATTACK", "uses": 10621}, {"moveId": "SOLAR_BEAM", "uses": 5879}, {"moveId": "HYPER_BEAM", "uses": 5274}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BLIZZARD", "uses": 8723}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 69.9, "stats": {"product": 1522, "atk": 147.6, "def": 88.1, "hp": 117}}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 424, "matchups": [{"opponent": "sneasler", "rating": 867, "opRating": 132}, {"opponent": "purugly_shadow", "rating": 841, "opRating": 158}, {"opponent": "snea<PERSON>_shadow", "rating": 824, "opRating": 175}, {"opponent": "toxicroak_shadow", "rating": 739, "opRating": 260}, {"opponent": "drifb<PERSON>", "rating": 666}], "counters": [{"opponent": "dusknoir_shadow", "rating": 144}, {"opponent": "drapion_shadow", "rating": 165}, {"opponent": "spiritomb", "rating": 206}, {"opponent": "gliscor", "rating": 331}, {"opponent": "gastrodon", "rating": 345}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 5854}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2022}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1705}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1946}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1736}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1257}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2565}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2176}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1886}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1876}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1930}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2040}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1847}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1766}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1684}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1886}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1476}, {"moveId": "CHARGE_BEAM", "uses": 2125}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 7052}, {"moveId": "TRI_ATTACK", "uses": 10627}, {"moveId": "SOLAR_BEAM", "uses": 5893}, {"moveId": "HYPER_BEAM", "uses": 5256}, {"moveId": "BLIZZARD", "uses": 8710}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 69.9, "stats": {"product": 1522, "atk": 147.6, "def": 88.1, "hp": 117}}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 575, "matchups": [{"opponent": "gastrodon", "rating": 664}, {"opponent": "gallade_shadow", "rating": 652, "opRating": 347}, {"opponent": "hippo<PERSON><PERSON>", "rating": 625, "opRating": 375}, {"opponent": "lickilicky", "rating": 597, "opRating": 402}, {"opponent": "sneasler", "rating": 535, "opRating": 464}], "counters": [{"opponent": "drifb<PERSON>", "rating": 169}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "gliscor", "rating": 318}, {"opponent": "spiritomb", "rating": 375}, {"opponent": "drapion_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 15841}, {"moveId": "BUG_BITE", "uses": 21659}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 7011}, {"moveId": "BULLDOZE", "uses": 14989}, {"moveId": "BUG_BUZZ", "uses": 15521}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BULLDOZE"], "score": 69.7, "stats": {"product": 2040, "atk": 110, "def": 144.8, "hp": 128}}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 668, "matchups": [{"opponent": "bibarel", "rating": 814, "opRating": 185}, {"opponent": "gliscor", "rating": 788}, {"opponent": "abomasnow_shadow", "rating": 664}, {"opponent": "gastrodon", "rating": 634}, {"opponent": "dusknoir_shadow", "rating": 561}], "counters": [{"opponent": "gallade_shadow", "rating": 221}, {"opponent": "drapion_shadow", "rating": 296}, {"opponent": "bastiodon", "rating": 298}, {"opponent": "qwilfish_his<PERSON>an", "rating": 305}, {"opponent": "drifb<PERSON>", "rating": 315}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 3982}, {"moveId": "POISON_STING", "uses": 9458}, {"moveId": "POISON_JAB", "uses": 7981}, {"moveId": "MAGICAL_LEAF", "uses": 7845}, {"moveId": "BULLET_SEED", "uses": 8191}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 12626}, {"moveId": "SOLAR_BEAM", "uses": 1755}, {"moveId": "SLUDGE_BOMB", "uses": 4914}, {"moveId": "LEAF_STORM", "uses": 5005}, {"moveId": "GRASS_KNOT", "uses": 8356}, {"moveId": "DAZZLING_GLEAM", "uses": 4817}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 68.1, "stats": {"product": 1574, "atk": 141.9, "def": 114.3, "hp": 97}}, {"speciesId": "rhyperior_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 637, "matchups": [{"opponent": "sneasler", "rating": 839, "opRating": 160}, {"opponent": "bastiodon", "rating": 828}, {"opponent": "spiritomb", "rating": 600}, {"opponent": "dusknoir_shadow", "rating": 578}, {"opponent": "gliscor", "rating": 559}], "counters": [{"opponent": "abomasnow_shadow", "rating": 213}, {"opponent": "gastrodon", "rating": 252}, {"opponent": "drifb<PERSON>", "rating": 272}, {"opponent": "gallade_shadow", "rating": 365}, {"opponent": "drapion_shadow", "rating": 427}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 13494}, {"moveId": "MUD_SLAP", "uses": 24006}], "chargedMoves": [{"moveId": "SURF", "uses": 5293}, {"moveId": "SUPER_POWER", "uses": 6299}, {"moveId": "STONE_EDGE", "uses": 2762}, {"moveId": "SKULL_BASH", "uses": 2084}, {"moveId": "ROCK_WRECKER", "uses": 9910}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 4314}, {"moveId": "BREAKING_SWIPE", "uses": 6770}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 67.8, "stats": {"product": 1775, "atk": 126.7, "def": 104.5, "hp": 134}}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 635, "matchups": [{"opponent": "bastiodon", "rating": 828, "opRating": 171}, {"opponent": "sneasler", "rating": 817, "opRating": 182}, {"opponent": "dusknoir_shadow", "rating": 656}, {"opponent": "spiritomb", "rating": 578}, {"opponent": "lickilicky", "rating": 526, "opRating": 473}], "counters": [{"opponent": "drifb<PERSON>", "rating": 234}, {"opponent": "gallade_shadow", "rating": 317}, {"opponent": "drapion_shadow", "rating": 364}, {"opponent": "gastrodon", "rating": 372}, {"opponent": "gliscor", "rating": 487}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 13655}, {"moveId": "MUD_SLAP", "uses": 23845}], "chargedMoves": [{"moveId": "SURF", "uses": 5294}, {"moveId": "SUPER_POWER", "uses": 6299}, {"moveId": "STONE_EDGE", "uses": 2757}, {"moveId": "SKULL_BASH", "uses": 2106}, {"moveId": "ROCK_WRECKER", "uses": 9957}, {"moveId": "EARTHQUAKE", "uses": 4313}, {"moveId": "BREAKING_SWIPE", "uses": 6777}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 67.8, "stats": {"product": 1775, "atk": 126.7, "def": 104.5, "hp": 134}}, {"speciesId": "garcho<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 592, "matchups": [{"opponent": "electivire_shadow", "rating": 932, "opRating": 67}, {"opponent": "toxicroak", "rating": 837, "opRating": 162}, {"opponent": "toxicroak_shadow", "rating": 837, "opRating": 162}, {"opponent": "sneasler", "rating": 718, "opRating": 281}, {"opponent": "magnezone_shadow", "rating": 666, "opRating": 333}], "counters": [{"opponent": "gastrodon", "rating": 336}, {"opponent": "drifb<PERSON>", "rating": 342}, {"opponent": "gliscor", "rating": 366}, {"opponent": "drapion_shadow", "rating": 385}, {"opponent": "dusknoir_shadow", "rating": 472}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 18628}, {"moveId": "DRAGON_TAIL", "uses": 18872}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 4654}, {"moveId": "OUTRAGE", "uses": 11739}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_BLAST", "uses": 5834}, {"moveId": "EARTH_POWER", "uses": 11165}, {"moveId": "EARTHQUAKE", "uses": 4057}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 65.2, "stats": {"product": 1694, "atk": 132.4, "def": 101.4, "hp": 126}}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 574, "matchups": [{"opponent": "bibarel", "rating": 567, "opRating": 432}, {"opponent": "bastiodon", "rating": 563, "opRating": 436}, {"opponent": "spiritomb", "rating": 551, "opRating": 448}, {"opponent": "lickilicky", "rating": 551, "opRating": 448}, {"opponent": "sneasler", "rating": 527, "opRating": 472}], "counters": [{"opponent": "drifb<PERSON>", "rating": 282}, {"opponent": "gastrodon", "rating": 375}, {"opponent": "dusknoir_shadow", "rating": 394}, {"opponent": "gliscor", "rating": 409}, {"opponent": "drapion_shadow", "rating": 423}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 18289}, {"moveId": "DRAGON_TAIL", "uses": 19211}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 4694}, {"moveId": "OUTRAGE", "uses": 11747}, {"moveId": "FIRE_BLAST", "uses": 5821}, {"moveId": "EARTH_POWER", "uses": 11176}, {"moveId": "EARTHQUAKE", "uses": 4051}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 65.2, "stats": {"product": 1694, "atk": 132.4, "def": 101.4, "hp": 126}}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 545, "matchups": [{"opponent": "empoleon_shadow", "rating": 827, "opRating": 172}, {"opponent": "sneasler", "rating": 743, "opRating": 256}, {"opponent": "spiritomb", "rating": 592}, {"opponent": "drifb<PERSON>", "rating": 563}, {"opponent": "bibarel", "rating": 512, "opRating": 487}], "counters": [{"opponent": "drapion_shadow", "rating": 203}, {"opponent": "gastrodon", "rating": 232}, {"opponent": "gliscor", "rating": 293}, {"opponent": "dusknoir_shadow", "rating": 416}, {"opponent": "bastiodon", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 4664}, {"moveId": "SNARL", "uses": 4980}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1838}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1541}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1769}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1607}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1148}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2214}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2133}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1635}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1657}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1749}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1860}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1786}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2140}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1512}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1649}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1386}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17396}, {"moveId": "PSYCHIC_FANGS", "uses": 9273}, {"moveId": "HYPER_BEAM", "uses": 2540}, {"moveId": "CRUNCH", "uses": 8338}]}, "moveset": ["SPARK", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 63.6, "stats": {"product": 1626, "atk": 138, "def": 98.9, "hp": 119}}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 518, "matchups": [{"opponent": "bibarel", "rating": 789, "opRating": 210}, {"opponent": "drifb<PERSON>", "rating": 760}, {"opponent": "sneasler", "rating": 731, "opRating": 268}, {"opponent": "lickilicky", "rating": 634, "opRating": 365}, {"opponent": "spiritomb", "rating": 533}], "counters": [{"opponent": "drapion_shadow", "rating": 80}, {"opponent": "dusknoir_shadow", "rating": 205}, {"opponent": "bastiodon", "rating": 215}, {"opponent": "gastrodon", "rating": 220}, {"opponent": "gliscor", "rating": 327}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 4733}, {"moveId": "SNARL", "uses": 5051}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1837}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1536}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1715}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1647}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1144}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2173}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2112}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1661}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1635}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1780}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1870}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1804}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2068}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1487}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1627}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1356}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17415}, {"moveId": "PSYCHIC_FANGS", "uses": 9240}, {"moveId": "HYPER_BEAM", "uses": 2550}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CRUNCH", "uses": 8310}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 60.3, "stats": {"product": 1626, "atk": 138, "def": 98.9, "hp": 119}}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 632, "matchups": [{"opponent": "drifb<PERSON>", "rating": 789}, {"opponent": "drapion_shadow", "rating": 700}, {"opponent": "bastiodon", "rating": 691}, {"opponent": "abomasnow_shadow", "rating": 632}, {"opponent": "spiritomb", "rating": 583}], "counters": [{"opponent": "gastrodon", "rating": 74}, {"opponent": "gliscor", "rating": 271}, {"opponent": "gallade_shadow", "rating": 274}, {"opponent": "dusknoir_shadow", "rating": 400}, {"opponent": "lickilicky", "rating": 437}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12486}, {"moveId": "SPARK", "uses": 7612}, {"moveId": "METAL_SOUND", "uses": 11754}, {"moveId": "CHARGE_BEAM", "uses": 5608}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 2785}, {"moveId": "WILD_CHARGE", "uses": 20370}, {"moveId": "MIRROR_SHOT", "uses": 9469}, {"moveId": "FLASH_CANNON", "uses": 4985}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 60.2, "stats": {"product": 1661, "atk": 134.3, "def": 121.1, "hp": 102}}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 635, "matchups": [{"opponent": "drifb<PERSON>", "rating": 754}, {"opponent": "qwilfish_his<PERSON>an", "rating": 681, "opRating": 318}, {"opponent": "drapion_shadow", "rating": 656}, {"opponent": "bastiodon", "rating": 627}, {"opponent": "spiritomb", "rating": 602}], "counters": [{"opponent": "gastrodon", "rating": 89}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "gallade_shadow", "rating": 312}, {"opponent": "gliscor", "rating": 422}, {"opponent": "abomasnow_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12947}, {"moveId": "SPARK", "uses": 7376}, {"moveId": "METAL_SOUND", "uses": 11815}, {"moveId": "CHARGE_BEAM", "uses": 5381}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 2791}, {"moveId": "WILD_CHARGE", "uses": 20403}, {"moveId": "MIRROR_SHOT", "uses": 9444}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 5017}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 60.2, "stats": {"product": 1661, "atk": 134.3, "def": 121.1, "hp": 102}}]