{"name": "genetic", "title": "Genetic", "include": [{"filterType": "id", "name": "Species", "values": ["alomomola", "amoon<PERSON>s", "annihilape", "araquanid", "arbok", "a<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "banette", "barbara<PERSON>", "basculin", "beedrill", "bibarel", "blastoise", "brionne", "bruxish", "bulbasaur", "carracosta", "castform_rainy", "ceruledge", "chandelure", "clamperl", "clauncher", "clawitzer", "cloyster", "cofagrigus", "corphish", "corsola", "crawdaunt", "crobat", "cro<PERSON><PERSON>", "decid<PERSON><PERSON>", "dewgong", "<PERSON><PERSON><PERSON>", "dragalge", "drapion", "drifb<PERSON>", "drifloon", "dusclops", "dusknoir", "dustox", "empoleon", "feraligatr", "floatzel", "frillish", "<PERSON><PERSON><PERSON>", "froslass", "garbodor", "gengar", "g<PERSON><PERSON>o", "giratina_origin", "gloom", "golbat", "goldeen", "golduck", "golisopod", "gorebyss", "gourgeist_average", "gourgeist_large", "gourgeist_small", "gourgeist_super", "gren<PERSON><PERSON>", "grimer", "grimer_alolan", "gyarados", "haunter", "hoopa", "houndstone", "hunt<PERSON>", "ivysaur", "jellicent", "kabuto", "kabutops", "king<PERSON>", "kingler", "koffing", "krabby", "lampent", "<PERSON>ras", "lombre", "ludico<PERSON>", "lumineon", "mantine", "mantyke", "marowak_alolan", "milotic", "misdreavus", "mismagius", "muk", "muk_alolan", "nidorina", "<PERSON><PERSON><PERSON>", "octillery", "oddish", "omanyte", "omastar", "oricorio_sensu", "over<PERSON><PERSON>l", "pelipper", "pip<PERSON>p", "poipole", "politoed", "poliwhirl", "poliwrath", "popp<PERSON>", "primarina", "prin<PERSON><PERSON><PERSON>", "pumpkaboo_average", "pumpkaboo_large", "pumpkaboo_small", "pumpkaboo_super", "quaquaval", "quaxwell", "qwilfish", "qwilfish_his<PERSON>an", "relicanth", "revavroom", "roselia", "roserade", "sableye", "salazzle", "samu<PERSON>t", "samu<PERSON><PERSON>_<PERSON><PERSON>an", "scolipede", "seadra", "seaking", "sealeo", "seel", "seviper", "sharpedo", "shellos", "simipour", "skeledirge", "s<PERSON><PERSON><PERSON>", "skrelp", "skuntank", "slowbro", "slowbro_galarian", "slowking", "slowking_galarian", "slowpoke", "sneasel", "sneasel_shadow", "snea<PERSON>_his<PERSON>an", "sneasler", "spiritomb", "starmie", "stunky", "suicune", "swalot", "swanna", "tapu_fini", "tentacool", "tentacruel", "t<PERSON><PERSON><PERSON>", "totodile", "toxapex", "toxicroak", "trevenant", "typhlosion_hisuian", "vaporeon", "varoom", "venomoth", "venusaur", "victreebel", "vileplume", "wailmer", "wailord", "walrein", "wartortle", "weepinbell", "weezing", "weezing_galarian", "whirlipede", "wugtrio", "abomasnow", "abomasnow_shadow", "aerodactyl", "aerodactyl_shadow", "aggron", "aggron_shadow", "anorith", "anorith_shadow", "arm<PERSON>", "<PERSON><PERSON>_<PERSON>", "aron", "aron_shadow", "articuno", "articuno_shadow", "bastiodon", "bastiodon_shadow", "blaziken", "blazi<PERSON>_shadow", "cacturne", "cacturne_shadow", "charizard", "charizard_shadow", "combusken", "combusken_shadow", "cradily", "cradily_shadow", "crustle", "crustle_shadow", "dragonite", "dragonite_shadow", "dwebble", "dwebble_shadow", "exeggcute", "exeggcute_shadow", "exeggutor", "exeggutor_alolan", "exeggutor_alolan_shadow", "exeggutor_shadow", "ferroseed", "ferroseed_shadow", "ferrothorn", "ferroth<PERSON>_shadow", "forretress", "forretress_shadow", "gallade", "gallade_shadow", "gardevoir", "gardevoir_shadow", "girafarig", "girafarig_shadow", "ho_oh", "ho_oh_shadow", "honch<PERSON><PERSON>", "honchk<PERSON>_shadow", "houndoom", "houndoom_shadow", "houndour", "houndour_shadow", "infernape", "infernape_shadow", "<PERSON><PERSON><PERSON>", "jumpluff_shadow", "<PERSON>on", "lair<PERSON>_shadow", "latias", "latias_shadow", "latios", "latios_shadow", "<PERSON><PERSON>", "<PERSON><PERSON>_shadow", "lileep", "lileep_shadow", "lugia", "lugia_shadow", "ma<PERSON>le", "mawile_shadow", "metagross", "metagross_shadow", "metang", "metang_shadow", "moltres", "moltres_shadow", "monferno", "monferno_shadow", "murkrow", "murkrow_shadow", "ninetales_alolan", "ninetales_alolan_shadow", "nuzleaf", "nuzleaf_shadow", "pidgeot", "pidgeot_shadow", "pidgeotto", "pidgeott<PERSON>_shadow", "probopass", "probopass_shadow", "raticate_alolan", "raticate_alolan_shadow", "salamence", "salamence_shadow", "sandshrew_alolan", "sandshrew_alolan_shadow", "sandslash_alolan", "sandslash_alolan_shadow", "s<PERSON><PERSON>", "sci<PERSON>_shadow", "scyther", "scyther_shadow", "shiftry", "shiftry_shadow", "<PERSON><PERSON><PERSON><PERSON>", "skar<PERSON><PERSON>_shadow", "snover", "snover_shadow", "staraptor", "staraptor_shadow", "staravia", "staravia_shadow", "tranquill", "tranquill_shadow", "tyranitar", "tyranitar_shadow", "unfezant", "unfezant_shadow", "weavile", "weavile_shadow", "xatu", "xatu_shadow"]}], "exclude": [], "overrides": [], "league": 1500, "useDefaultMovesets": 1}