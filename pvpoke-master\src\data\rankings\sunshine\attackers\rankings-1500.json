[{"speciesId": "magcargo", "speciesName": "Magcargo", "rating": 462, "matchups": [{"opponent": "pidgeot", "rating": 735, "opRating": 264}, {"opponent": "jumpluff_shadow", "rating": 666}, {"opponent": "talonflame", "rating": 598}, {"opponent": "skeledirge", "rating": 581, "opRating": 418}, {"opponent": "furret", "rating": 521}], "counters": [{"opponent": "clodsire", "rating": 110}, {"opponent": "claydol", "rating": 140}, {"opponent": "gligar", "rating": 187}, {"opponent": "diggersby", "rating": 201}, {"opponent": "cradily", "rating": 347}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27248}, {"moveId": "EMBER", "uses": 18411}, {"moveId": "ROCK_THROW", "uses": 12633}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 28956}, {"moveId": "OVERHEAT", "uses": 16533}, {"moveId": "STONE_EDGE", "uses": 9956}, {"moveId": "HEAT_WAVE", "uses": 2970}]}, "moveset": ["INCINERATE", "ROCK_TOMB", "OVERHEAT"], "score": 100}, {"speciesId": "gligar", "speciesName": "Gligar", "rating": 504, "matchups": [{"opponent": "swadloon", "rating": 641, "opRating": 358}, {"opponent": "drampa", "rating": 568, "opRating": 431}, {"opponent": "clodsire", "rating": 526}, {"opponent": "tropius", "rating": 511, "opRating": 488}, {"opponent": "flygon_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 96}, {"opponent": "jumpluff_shadow", "rating": 333}, {"opponent": "furret", "rating": 387}, {"opponent": "diggersby", "rating": 405}, {"opponent": "cradily", "rating": 434}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32529}, {"moveId": "WING_ATTACK", "uses": 25771}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 20902}, {"moveId": "NIGHT_SLASH", "uses": 19090}, {"moveId": "DIG", "uses": 12341}, {"moveId": "RETURN", "uses": 6013}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "DIG"], "score": 97.2}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 425, "matchups": [{"opponent": "jumpluff_shadow", "rating": 643, "opRating": 356}, {"opponent": "flygon", "rating": 640, "opRating": 360}, {"opponent": "gligar", "rating": 626}, {"opponent": "drampa", "rating": 560, "opRating": 440}, {"opponent": "talonflame", "rating": 543}], "counters": [{"opponent": "cradily", "rating": 180}, {"opponent": "magcargo", "rating": 222}, {"opponent": "furret", "rating": 250}, {"opponent": "clodsire", "rating": 274}, {"opponent": "diggersby", "rating": 301}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 32122}, {"moveId": "TACKLE", "uses": 21438}, {"moveId": "ZEN_HEADBUTT", "uses": 4717}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21500}, {"moveId": "ICE_BEAM", "uses": 16909}, {"moveId": "STOMP", "uses": 7627}, {"moveId": "THUNDERBOLT", "uses": 7179}, {"moveId": "GYRO_BALL", "uses": 5054}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 94.5}, {"speciesId": "diggersby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 463, "matchups": [{"opponent": "steelix_shadow", "rating": 727, "opRating": 272}, {"opponent": "ninetales_shadow", "rating": 672, "opRating": 327}, {"opponent": "steelix", "rating": 658, "opRating": 341}, {"opponent": "clodsire", "rating": 551}, {"opponent": "marowak_alolan", "rating": 517, "opRating": 482}], "counters": [{"opponent": "cradily", "rating": 180}, {"opponent": "jumpluff_shadow", "rating": 271}, {"opponent": "talonflame", "rating": 307}, {"opponent": "furret", "rating": 331}, {"opponent": "gligar", "rating": 385}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31624}, {"moveId": "MUD_SHOT", "uses": 26676}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 17358}, {"moveId": "SCORCHING_SANDS", "uses": 17185}, {"moveId": "RETURN", "uses": 9391}, {"moveId": "DIG", "uses": 5724}, {"moveId": "EARTHQUAKE", "uses": 4899}, {"moveId": "HYPER_BEAM", "uses": 3604}]}, "moveset": ["QUICK_ATTACK", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 93.3}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 457, "matchups": [{"opponent": "quagsire_shadow", "rating": 618, "opRating": 381}, {"opponent": "pidgeot", "rating": 604, "opRating": 395}, {"opponent": "jumpluff_shadow", "rating": 593}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 576, "opRating": 423}, {"opponent": "drampa", "rating": 524, "opRating": 475}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "clodsire", "rating": 213}, {"opponent": "gligar", "rating": 278}, {"opponent": "diggersby", "rating": 350}, {"opponent": "furret", "rating": 387}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22806}, {"moveId": "BULLET_SEED", "uses": 19845}, {"moveId": "INFESTATION", "uses": 15655}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 17412}, {"moveId": "GRASS_KNOT", "uses": 14330}, {"moveId": "ROCK_SLIDE", "uses": 11243}, {"moveId": "STONE_EDGE", "uses": 5933}, {"moveId": "BULLDOZE", "uses": 5047}, {"moveId": "RETURN", "uses": 4329}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 92.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 517, "matchups": [{"opponent": "gastrodon", "rating": 777, "opRating": 222}, {"opponent": "lura<PERSON>s", "rating": 738, "opRating": 261}, {"opponent": "flygon_shadow", "rating": 643, "opRating": 356}, {"opponent": "flygon", "rating": 555, "opRating": 444}, {"opponent": "drampa", "rating": 535, "opRating": 464}], "counters": [{"opponent": "talonflame", "rating": 55}, {"opponent": "clodsire", "rating": 233}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 332}, {"opponent": "jumpluff_shadow", "rating": 408}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 23098}, {"moveId": "BULLET_SEED", "uses": 19657}, {"moveId": "INFESTATION", "uses": 15516}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 17824}, {"moveId": "AERIAL_ACE", "uses": 16120}, {"moveId": "ENERGY_BALL", "uses": 10514}, {"moveId": "DAZZLING_GLEAM", "uses": 6039}, {"moveId": "RETURN", "uses": 4656}, {"moveId": "SOLAR_BEAM", "uses": 3090}]}, "moveset": ["FAIRY_WIND", "AERIAL_ACE", "ACROBATICS"], "score": 91.5}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 384, "matchups": [{"opponent": "magcargo", "rating": 711, "opRating": 288}, {"opponent": "ninetales_shadow", "rating": 711, "opRating": 288}, {"opponent": "skeledirge", "rating": 568, "opRating": 431}, {"opponent": "claydol", "rating": 552, "opRating": 447}, {"opponent": "talonflame", "rating": 521}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "cradily", "rating": 65}, {"opponent": "furret", "rating": 115}, {"opponent": "gligar", "rating": 465}, {"opponent": "clodsire", "rating": 478}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30100}, {"moveId": "WATER_GUN", "uses": 28200}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 22565}, {"moveId": "MUD_BOMB", "uses": 12001}, {"moveId": "STONE_EDGE", "uses": 8331}, {"moveId": "SLUDGE_BOMB", "uses": 7186}, {"moveId": "RETURN", "uses": 3835}, {"moveId": "EARTHQUAKE", "uses": 2909}, {"moveId": "ACID_SPRAY", "uses": 1584}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 87.6}, {"speciesId": "marowak_alolan", "speciesName": "Marowak (Alolan)", "rating": 469, "matchups": [{"opponent": "parasect", "rating": 864, "opRating": 136}, {"opponent": "abomasnow", "rating": 824, "opRating": 176}, {"opponent": "abomasnow_shadow", "rating": 800, "opRating": 200}, {"opponent": "tropius", "rating": 608, "opRating": 392}, {"opponent": "ninetales", "rating": 556, "opRating": 444}], "counters": [{"opponent": "talonflame", "rating": 181}, {"opponent": "gligar", "rating": 259}, {"opponent": "clodsire", "rating": 295}, {"opponent": "cradily", "rating": 326}, {"opponent": "jumpluff_shadow", "rating": 490}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 25539}, {"moveId": "FIRE_SPIN", "uses": 24611}, {"moveId": "ROCK_SMASH", "uses": 8134}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 18636}, {"moveId": "SHADOW_BONE", "uses": 15770}, {"moveId": "FIRE_BLAST", "uses": 7144}, {"moveId": "FLAME_WHEEL", "uses": 5911}, {"moveId": "RETURN", "uses": 5874}, {"moveId": "SHADOW_BALL", "uses": 4997}]}, "moveset": ["FIRE_SPIN", "BONE_CLUB", "SHADOW_BONE"], "score": 87.2}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 439, "matchups": [{"opponent": "abomasnow_shadow", "rating": 707, "opRating": 292}, {"opponent": "gligar", "rating": 600}, {"opponent": "gliscor", "rating": 577, "opRating": 422}, {"opponent": "ninetales_shadow", "rating": 533, "opRating": 466}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 507, "opRating": 492}], "counters": [{"opponent": "magcargo", "rating": 68}, {"opponent": "clodsire", "rating": 115}, {"opponent": "cradily", "rating": 145}, {"opponent": "furret", "rating": 228}, {"opponent": "jumpluff_shadow", "rating": 316}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27079}, {"moveId": "FIRE_SPIN", "uses": 15454}, {"moveId": "STEEL_WING", "uses": 9060}, {"moveId": "PECK", "uses": 6744}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22109}, {"moveId": "FLY", "uses": 18861}, {"moveId": "FLAME_CHARGE", "uses": 10826}, {"moveId": "HURRICANE", "uses": 4198}, {"moveId": "FIRE_BLAST", "uses": 2505}]}, "moveset": ["INCINERATE", "FLY", "BRAVE_BIRD"], "score": 86.1}, {"speciesId": "jumpluff_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 478, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 823, "opRating": 176}, {"opponent": "gogoat", "rating": 823, "opRating": 176}, {"opponent": "lura<PERSON>s", "rating": 718, "opRating": 281}, {"opponent": "flygon", "rating": 643, "opRating": 356}, {"opponent": "flygon_shadow", "rating": 575, "opRating": 424}], "counters": [{"opponent": "talonflame", "rating": 55}, {"opponent": "magcargo", "rating": 64}, {"opponent": "gligar", "rating": 251}, {"opponent": "clodsire", "rating": 262}, {"opponent": "cradily", "rating": 302}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 22973}, {"moveId": "BULLET_SEED", "uses": 19655}, {"moveId": "INFESTATION", "uses": 15626}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 19425}, {"moveId": "AERIAL_ACE", "uses": 17584}, {"moveId": "ENERGY_BALL", "uses": 11320}, {"moveId": "DAZZLING_GLEAM", "uses": 6586}, {"moveId": "SOLAR_BEAM", "uses": 3204}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FAIRY_WIND", "AERIAL_ACE", "ACROBATICS"], "score": 85.4}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 439, "matchups": [{"opponent": "wormadam_plant", "rating": 866, "opRating": 133}, {"opponent": "<PERSON><PERSON>e", "rating": 746, "opRating": 253}, {"opponent": "diggersby", "rating": 556, "opRating": 443}, {"opponent": "marowak_alolan", "rating": 514, "opRating": 485}, {"opponent": "tropius", "rating": 510, "opRating": 489}], "counters": [{"opponent": "talonflame", "rating": 148}, {"opponent": "clodsire", "rating": 274}, {"opponent": "cradily", "rating": 295}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "gligar", "rating": 408}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 18323}, {"moveId": "WING_ATTACK", "uses": 16567}, {"moveId": "AIR_SLASH", "uses": 12582}, {"moveId": "STEEL_WING", "uses": 10782}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20968}, {"moveId": "AIR_CUTTER", "uses": 18012}, {"moveId": "AERIAL_ACE", "uses": 6684}, {"moveId": "RETURN", "uses": 6095}, {"moveId": "HURRICANE", "uses": 3696}, {"moveId": "FEATHER_DANCE", "uses": 2547}]}, "moveset": ["GUST", "AIR_CUTTER", "BRAVE_BIRD"], "score": 85.1}, {"speciesId": "dunsparce", "speciesName": "Dunsparce", "rating": 385, "matchups": [{"opponent": "abomasnow_shadow", "rating": 592, "opRating": 407}, {"opponent": "magcargo", "rating": 548, "opRating": 451}, {"opponent": "marowak_alolan", "rating": 535, "opRating": 464}, {"opponent": "pidgeot", "rating": 519, "opRating": 480}, {"opponent": "talonflame", "rating": 502}], "counters": [{"opponent": "cradily", "rating": 250}, {"opponent": "clodsire", "rating": 250}, {"opponent": "gligar", "rating": 274}, {"opponent": "diggersby", "rating": 321}, {"opponent": "jumpluff_shadow", "rating": 339}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 27488}, {"moveId": "ASTONISH", "uses": 19989}, {"moveId": "BITE", "uses": 10832}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27805}, {"moveId": "ROCK_SLIDE", "uses": 23490}, {"moveId": "DIG", "uses": 6991}]}, "moveset": ["ROLLOUT", "DRILL_RUN", "ROCK_SLIDE"], "score": 84.5}, {"speciesId": "castform_sunny", "speciesName": "Castform (Sunny)", "rating": 446, "matchups": [{"opponent": "abomasnow", "rating": 797, "opRating": 202}, {"opponent": "<PERSON><PERSON>e", "rating": 756, "opRating": 243}, {"opponent": "abomasnow_shadow", "rating": 739, "opRating": 260}, {"opponent": "lura<PERSON>s", "rating": 732, "opRating": 267}, {"opponent": "tropius", "rating": 616, "opRating": 383}], "counters": [{"opponent": "clodsire", "rating": 274}, {"opponent": "cradily", "rating": 288}, {"opponent": "talonflame", "rating": 325}, {"opponent": "gligar", "rating": 343}, {"opponent": "jumpluff_shadow", "rating": 460}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 37896}, {"moveId": "TACKLE", "uses": 20404}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 42859}, {"moveId": "SOLAR_BEAM", "uses": 10397}, {"moveId": "FIRE_BLAST", "uses": 4985}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 83.5}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 406, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 639, "opRating": 360}, {"opponent": "claydol", "rating": 608, "opRating": 391}, {"opponent": "flygon_shadow", "rating": 562, "opRating": 437}, {"opponent": "flygon", "rating": 559, "opRating": 440}, {"opponent": "tropius", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 292}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "gligar", "rating": 347}, {"opponent": "clodsire", "rating": 382}, {"opponent": "cradily", "rating": 451}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 31795}, {"moveId": "LEAFAGE", "uses": 18389}, {"moveId": "RAZOR_LEAF", "uses": 8138}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 26262}, {"moveId": "ICY_WIND", "uses": 8947}, {"moveId": "ENERGY_BALL", "uses": 8502}, {"moveId": "OUTRAGE", "uses": 6433}, {"moveId": "RETURN", "uses": 4110}, {"moveId": "BLIZZARD", "uses": 4012}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 82.9}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 448, "matchups": [{"opponent": "abomasnow", "rating": 777, "opRating": 222}, {"opponent": "<PERSON><PERSON>e", "rating": 757, "opRating": 242}, {"opponent": "abomasnow_shadow", "rating": 734, "opRating": 265}, {"opponent": "tropius", "rating": 654, "opRating": 345}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 535, "opRating": 464}], "counters": [{"opponent": "clodsire", "rating": 146}, {"opponent": "talonflame", "rating": 151}, {"opponent": "cradily", "rating": 277}, {"opponent": "gligar", "rating": 343}, {"opponent": "jumpluff_shadow", "rating": 464}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 24475}, {"moveId": "FIRE_SPIN", "uses": 21697}, {"moveId": "FEINT_ATTACK", "uses": 12172}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 16601}, {"moveId": "PSYSHOCK", "uses": 11676}, {"moveId": "SCORCHING_SANDS", "uses": 8875}, {"moveId": "OVERHEAT", "uses": 6672}, {"moveId": "SOLAR_BEAM", "uses": 4048}, {"moveId": "FLAMETHROWER", "uses": 3661}, {"moveId": "RETURN", "uses": 3643}, {"moveId": "FIRE_BLAST", "uses": 1945}, {"moveId": "HEAT_WAVE", "uses": 1133}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 82.6}, {"speciesId": "clodsire", "speciesName": "Clodsire", "rating": 432, "matchups": [{"opponent": "parasect", "rating": 716, "opRating": 283}, {"opponent": "swadloon", "rating": 685, "opRating": 314}, {"opponent": "farfetchd", "rating": 658, "opRating": 341}, {"opponent": "castform_sunny", "rating": 615, "opRating": 384}, {"opponent": "magcargo", "rating": 569, "opRating": 430}], "counters": [{"opponent": "diggersby", "rating": 77}, {"opponent": "gligar", "rating": 103}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 357}, {"opponent": "jumpluff_shadow", "rating": 473}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 32922}, {"moveId": "MUD_SHOT", "uses": 25378}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 12380}, {"moveId": "STONE_EDGE", "uses": 11664}, {"moveId": "SLUDGE_BOMB", "uses": 11621}, {"moveId": "WATER_PULSE", "uses": 10068}, {"moveId": "EARTHQUAKE", "uses": 9977}, {"moveId": "ACID_SPRAY", "uses": 2643}]}, "moveset": ["POISON_STING", "EARTHQUAKE", "STONE_EDGE"], "score": 82.2}, {"speciesId": "diggersby_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 401, "matchups": [{"opponent": "steelix", "rating": 727, "opRating": 272}, {"opponent": "ninetales", "rating": 672, "opRating": 327}, {"opponent": "steelix_shadow", "rating": 666, "opRating": 333}, {"opponent": "litleo", "rating": 617, "opRating": 382}, {"opponent": "magcargo", "rating": 520, "opRating": 479}], "counters": [{"opponent": "talonflame", "rating": 151}, {"opponent": "cradily", "rating": 267}, {"opponent": "jumpluff_shadow", "rating": 339}, {"opponent": "gligar", "rating": 351}, {"opponent": "clodsire", "rating": 396}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31257}, {"moveId": "MUD_SHOT", "uses": 27043}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19403}, {"moveId": "SCORCHING_SANDS", "uses": 18813}, {"moveId": "HYPER_BEAM", "uses": 8406}, {"moveId": "DIG", "uses": 6209}, {"moveId": "EARTHQUAKE", "uses": 5433}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "SCORCHING_SANDS", "FIRE_PUNCH"], "score": 81.3}, {"speciesId": "lickitung", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 383, "matchups": [{"opponent": "whiscash", "rating": 629, "opRating": 370}, {"opponent": "marowak_alolan", "rating": 596, "opRating": 403}, {"opponent": "marowak_alolan_shadow", "rating": 559, "opRating": 440}, {"opponent": "marowak", "rating": 559, "opRating": 440}, {"opponent": "claydol", "rating": 505, "opRating": 494}], "counters": [{"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "clodsire", "rating": 305}, {"opponent": "talonflame", "rating": 314}, {"opponent": "gligar", "rating": 362}, {"opponent": "cradily", "rating": 395}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 44734}, {"moveId": "ZEN_HEADBUTT", "uses": 13566}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 19488}, {"moveId": "WRAP", "uses": 16023}, {"moveId": "POWER_WHIP", "uses": 12669}, {"moveId": "STOMP", "uses": 6930}, {"moveId": "HYPER_BEAM", "uses": 3126}]}, "moveset": ["LICK", "BODY_SLAM", "POWER_WHIP"], "score": 81.3}, {"speciesId": "furret", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 401, "matchups": [{"opponent": "lickitung", "rating": 603, "opRating": 396}, {"opponent": "quagsire_shadow", "rating": 590, "opRating": 409}, {"opponent": "claydol", "rating": 556, "opRating": 443}, {"opponent": "marowak_alolan", "rating": 537, "opRating": 462}, {"opponent": "skeledirge", "rating": 506, "opRating": 493}], "counters": [{"opponent": "talonflame", "rating": 137}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "clodsire", "rating": 302}, {"opponent": "gligar", "rating": 347}, {"opponent": "cradily", "rating": 350}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 30256}, {"moveId": "QUICK_ATTACK", "uses": 28044}], "chargedMoves": [{"moveId": "SWIFT", "uses": 23076}, {"moveId": "TRAILBLAZE", "uses": 13175}, {"moveId": "BRICK_BREAK", "uses": 10846}, {"moveId": "DIG", "uses": 8108}, {"moveId": "HYPER_BEAM", "uses": 3135}]}, "moveset": ["SUCKER_PUNCH", "SWIFT", "TRAILBLAZE"], "score": 81}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 391, "matchups": [{"opponent": "pidgeot", "rating": 769, "opRating": 230}, {"opponent": "pidgeot_shadow", "rating": 722, "opRating": 277}, {"opponent": "farfetchd", "rating": 674, "opRating": 325}, {"opponent": "drampa", "rating": 595, "opRating": 404}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 563, "opRating": 436}], "counters": [{"opponent": "gligar", "rating": 209}, {"opponent": "clodsire", "rating": 274}, {"opponent": "cradily", "rating": 357}, {"opponent": "talonflame", "rating": 433}, {"opponent": "jumpluff_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 26884}, {"moveId": "THUNDER_FANG", "uses": 18148}, {"moveId": "IRON_TAIL", "uses": 13340}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 15679}, {"moveId": "PSYCHIC_FANGS", "uses": 12442}, {"moveId": "CRUNCH", "uses": 10144}, {"moveId": "EARTHQUAKE", "uses": 7879}, {"moveId": "HEAVY_SLAM", "uses": 7630}, {"moveId": "RETURN", "uses": 4552}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 80.8}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 373, "matchups": [{"opponent": "magcargo", "rating": 705, "opRating": 294}, {"opponent": "ninetales_shadow", "rating": 654, "opRating": 345}, {"opponent": "typhlosion_shadow", "rating": 609, "opRating": 390}, {"opponent": "skeledirge", "rating": 598, "opRating": 401}, {"opponent": "piloswine", "rating": 584, "opRating": 415}], "counters": [{"opponent": "cradily", "rating": 76}, {"opponent": "jumpluff_shadow", "rating": 160}, {"opponent": "talonflame", "rating": 288}, {"opponent": "clodsire", "rating": 334}, {"opponent": "gligar", "rating": 362}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29679}, {"moveId": "WATER_GUN", "uses": 28621}], "chargedMoves": [{"moveId": "SCALD", "uses": 19846}, {"moveId": "MUD_BOMB", "uses": 16214}, {"moveId": "BLIZZARD", "uses": 11558}, {"moveId": "RETURN", "uses": 5446}, {"moveId": "WATER_PULSE", "uses": 5184}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 80.8}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 453, "matchups": [{"opponent": "whiscash", "rating": 832, "opRating": 167}, {"opponent": "gogoat", "rating": 811, "opRating": 188}, {"opponent": "gastrodon", "rating": 767, "opRating": 232}, {"opponent": "lura<PERSON>s", "rating": 711, "opRating": 288}, {"opponent": "quagsire_shadow", "rating": 561, "opRating": 438}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "clodsire", "rating": 230}, {"opponent": "cradily", "rating": 270}, {"opponent": "gligar", "rating": 305}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37120}, {"moveId": "RAZOR_LEAF", "uses": 21180}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 23643}, {"moveId": "AERIAL_ACE", "uses": 14673}, {"moveId": "BRUTAL_SWING", "uses": 13400}, {"moveId": "STOMP", "uses": 6570}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 80.6}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 388, "matchups": [{"opponent": "steelix", "rating": 785, "opRating": 214}, {"opponent": "chansey", "rating": 735, "opRating": 264}, {"opponent": "heatran", "rating": 723, "opRating": 276}, {"opponent": "magcargo", "rating": 607, "opRating": 392}, {"opponent": "clodsire", "rating": 533}], "counters": [{"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "talonflame", "rating": 181}, {"opponent": "cradily", "rating": 222}, {"opponent": "furret", "rating": 253}, {"opponent": "gligar", "rating": 263}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23593}, {"moveId": "CONFUSION", "uses": 19231}, {"moveId": "EXTRASENSORY", "uses": 15449}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 12691}, {"moveId": "ICE_BEAM", "uses": 11608}, {"moveId": "SCORCHING_SANDS", "uses": 11443}, {"moveId": "PSYCHIC", "uses": 6083}, {"moveId": "SHADOW_BALL", "uses": 5630}, {"moveId": "EARTH_POWER", "uses": 4479}, {"moveId": "EARTHQUAKE", "uses": 3280}, {"moveId": "GYRO_BALL", "uses": 3067}]}, "moveset": ["MUD_SLAP", "ROCK_TOMB", "ICE_BEAM"], "score": 80.4}, {"speciesId": "marowak", "speciesName": "Marowak", "rating": 397, "matchups": [{"opponent": "castform_sunny", "rating": 648, "opRating": 352}, {"opponent": "marowak_alolan", "rating": 620, "opRating": 380}, {"opponent": "drampa", "rating": 576, "opRating": 424}, {"opponent": "magcargo", "rating": 552, "opRating": 448}, {"opponent": "skeledirge", "rating": 536, "opRating": 464}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 137}, {"opponent": "gligar", "rating": 145}, {"opponent": "cradily", "rating": 343}, {"opponent": "clodsire", "rating": 490}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 44940}, {"moveId": "ROCK_SMASH", "uses": 13360}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 27182}, {"moveId": "ROCK_SLIDE", "uses": 14371}, {"moveId": "RETURN", "uses": 6933}, {"moveId": "DIG", "uses": 5305}, {"moveId": "EARTHQUAKE", "uses": 4540}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "ROCK_SLIDE"], "score": 80.1}, {"speciesId": "wigglytuff", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 400, "matchups": [{"opponent": "exeggutor_alolan_shadow", "rating": 899, "opRating": 100}, {"opponent": "flygon_shadow", "rating": 829, "opRating": 170}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 809, "opRating": 190}, {"opponent": "drampa", "rating": 710, "opRating": 289}, {"opponent": "flygon", "rating": 668, "opRating": 331}], "counters": [{"opponent": "talonflame", "rating": 148}, {"opponent": "clodsire", "rating": 175}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "cradily", "rating": 315}, {"opponent": "gligar", "rating": 385}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 26996}, {"moveId": "CHARM", "uses": 26966}, {"moveId": "POUND", "uses": 4321}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17923}, {"moveId": "ICY_WIND", "uses": 15899}, {"moveId": "DISARMING_VOICE", "uses": 10450}, {"moveId": "ICE_BEAM", "uses": 5114}, {"moveId": "DAZZLING_GLEAM", "uses": 3858}, {"moveId": "PLAY_ROUGH", "uses": 2719}, {"moveId": "HYPER_BEAM", "uses": 2489}]}, "moveset": ["CHARM", "ICY_WIND", "SWIFT"], "score": 79.9}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 386, "matchups": [{"opponent": "magcargo", "rating": 776, "opRating": 223}, {"opponent": "clodsire", "rating": 681, "opRating": 318}, {"opponent": "typhlosion_shadow", "rating": 610, "opRating": 389}, {"opponent": "ninetales_shadow", "rating": 586, "opRating": 413}, {"opponent": "skeledirge", "rating": 547, "opRating": 452}], "counters": [{"opponent": "cradily", "rating": 114}, {"opponent": "talonflame", "rating": 137}, {"opponent": "jumpluff_shadow", "rating": 143}, {"opponent": "furret", "rating": 175}, {"opponent": "gligar", "rating": 183}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8691}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4808}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4368}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3741}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3353}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3350}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3286}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3011}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2880}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2828}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2818}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2809}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2757}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2623}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2599}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2319}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2033}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 20105}, {"moveId": "WATER_PULSE", "uses": 16631}, {"moveId": "EARTH_POWER", "uses": 15837}, {"moveId": "EARTHQUAKE", "uses": 5823}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 79.7}, {"speciesId": "gligar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 430, "matchups": [{"opponent": "marowak_shadow", "rating": 702, "opRating": 297}, {"opponent": "obstagoon", "rating": 675, "opRating": 324}, {"opponent": "parasect", "rating": 641, "opRating": 358}, {"opponent": "swadloon", "rating": 603, "opRating": 396}, {"opponent": "flygon", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 358}, {"opponent": "clodsire", "rating": 365}, {"opponent": "jumpluff_shadow", "rating": 372}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32588}, {"moveId": "WING_ATTACK", "uses": 25712}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 23279}, {"moveId": "NIGHT_SLASH", "uses": 21328}, {"moveId": "DIG", "uses": 13689}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "DIG"], "score": 79.7}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 360, "matchups": [{"opponent": "nidoqueen", "rating": 825, "opRating": 174}, {"opponent": "gliscor", "rating": 642, "opRating": 357}, {"opponent": "parasect", "rating": 614, "opRating": 385}, {"opponent": "gligar_shadow", "rating": 587, "opRating": 412}, {"opponent": "claydol", "rating": 555, "opRating": 444}], "counters": [{"opponent": "talonflame", "rating": 255}, {"opponent": "cradily", "rating": 329}, {"opponent": "clodsire", "rating": 346}, {"opponent": "jumpluff_shadow", "rating": 408}, {"opponent": "gligar", "rating": 416}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 31251}, {"moveId": "ASTONISH", "uses": 27049}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 20817}, {"moveId": "ROCK_TOMB", "uses": 18696}, {"moveId": "SHADOW_BALL", "uses": 12190}, {"moveId": "SAND_TOMB", "uses": 6596}]}, "moveset": ["SHADOW_CLAW", "ROCK_TOMB", "SHADOW_BALL"], "score": 79.4}, {"speciesId": "marowak_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 436, "matchups": [{"opponent": "parasect", "rating": 860, "opRating": 140}, {"opponent": "abomasnow_shadow", "rating": 804, "opRating": 196}, {"opponent": "abomasnow", "rating": 792, "opRating": 208}, {"opponent": "tropius", "rating": 664, "opRating": 336}, {"opponent": "ninetales_shadow", "rating": 568, "opRating": 432}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "clodsire", "rating": 146}, {"opponent": "gligar", "rating": 267}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "cradily", "rating": 395}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 25766}, {"moveId": "FIRE_SPIN", "uses": 24613}, {"moveId": "ROCK_SMASH", "uses": 7920}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 20629}, {"moveId": "SHADOW_BONE", "uses": 17268}, {"moveId": "FIRE_BLAST", "uses": 8122}, {"moveId": "FLAME_WHEEL", "uses": 6786}, {"moveId": "SHADOW_BALL", "uses": 5482}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SHADOW_BONE", "BONE_CLUB"], "score": 79.2}, {"speciesId": "geodude_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 350, "matchups": [{"opponent": "talonflame", "rating": 893}, {"opponent": "fletchinder", "rating": 867, "opRating": 132}, {"opponent": "typhlosion_shadow", "rating": 752, "opRating": 247}, {"opponent": "ninetales", "rating": 566, "opRating": 433}, {"opponent": "pidgeot", "rating": 544, "opRating": 455}], "counters": [{"opponent": "clodsire", "rating": 96}, {"opponent": "diggersby", "rating": 100}, {"opponent": "cradily", "rating": 170}, {"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 434}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32681}, {"moveId": "TACKLE", "uses": 25619}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 26245}, {"moveId": "ROCK_SLIDE", "uses": 16837}, {"moveId": "DIG", "uses": 15163}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 77.6}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 385, "matchups": [{"opponent": "skeledirge", "rating": 634, "opRating": 365}, {"opponent": "fletchinder", "rating": 600, "opRating": 400}, {"opponent": "marowak_alolan", "rating": 575, "opRating": 425}, {"opponent": "marowak_alolan_shadow", "rating": 565, "opRating": 434}, {"opponent": "abomasnow_shadow", "rating": 562, "opRating": 437}], "counters": [{"opponent": "cradily", "rating": 180}, {"opponent": "clodsire", "rating": 213}, {"opponent": "talonflame", "rating": 248}, {"opponent": "jumpluff_shadow", "rating": 264}, {"opponent": "gligar", "rating": 377}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 34981}, {"moveId": "LICK", "uses": 18154}, {"moveId": "ZEN_HEADBUTT", "uses": 5165}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25137}, {"moveId": "SHADOW_BALL", "uses": 11099}, {"moveId": "EARTHQUAKE", "uses": 8966}, {"moveId": "SOLAR_BEAM", "uses": 6797}, {"moveId": "HYPER_BEAM", "uses": 6333}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "SHADOW_BALL"], "score": 77.6}, {"speciesId": "pidgeot_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 416, "matchups": [{"opponent": "wormadam_plant", "rating": 799, "opRating": 200}, {"opponent": "parasect", "rating": 746, "opRating": 253}, {"opponent": "<PERSON><PERSON>e", "rating": 700, "opRating": 299}, {"opponent": "tropius", "rating": 612, "opRating": 387}, {"opponent": "diggersby", "rating": 514, "opRating": 485}], "counters": [{"opponent": "cradily", "rating": 138}, {"opponent": "talonflame", "rating": 181}, {"opponent": "clodsire", "rating": 182}, {"opponent": "gligar", "rating": 251}, {"opponent": "jumpluff_shadow", "rating": 362}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 18563}, {"moveId": "WING_ATTACK", "uses": 16787}, {"moveId": "AIR_SLASH", "uses": 12489}, {"moveId": "STEEL_WING", "uses": 10444}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 23355}, {"moveId": "AIR_CUTTER", "uses": 20061}, {"moveId": "AERIAL_ACE", "uses": 7580}, {"moveId": "HURRICANE", "uses": 4083}, {"moveId": "FEATHER_DANCE", "uses": 2915}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["GUST", "BRAVE_BIRD", "AIR_CUTTER"], "score": 77.6}, {"speciesId": "farfetchd", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 413, "matchups": [{"opponent": "wormadam_plant", "rating": 854, "opRating": 145}, {"opponent": "lura<PERSON>s", "rating": 702, "opRating": 297}, {"opponent": "swampert_shadow", "rating": 606, "opRating": 393}, {"opponent": "claydol", "rating": 561, "opRating": 438}, {"opponent": "diggersby", "rating": 519, "opRating": 480}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "cradily", "rating": 190}, {"opponent": "gligar", "rating": 232}, {"opponent": "clodsire", "rating": 235}, {"opponent": "jumpluff_shadow", "rating": 313}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31602}, {"moveId": "AIR_SLASH", "uses": 19673}, {"moveId": "CUT", "uses": 6978}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 24760}, {"moveId": "LEAF_BLADE", "uses": 24204}, {"moveId": "AERIAL_ACE", "uses": 9314}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "AIR_CUTTER"], "score": 77.4}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 390, "matchups": [{"opponent": "snorlax", "rating": 715, "opRating": 284}, {"opponent": "amoon<PERSON>s", "rating": 603, "opRating": 396}, {"opponent": "moltres", "rating": 603, "opRating": 396}, {"opponent": "lickitung", "rating": 527, "opRating": 472}, {"opponent": "arcanine", "rating": 506, "opRating": 493}], "counters": [{"opponent": "cradily", "rating": 239}, {"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "talonflame", "rating": 303}, {"opponent": "clodsire", "rating": 317}, {"opponent": "gligar", "rating": 347}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 33611}, {"moveId": "LICK", "uses": 24689}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35705}, {"moveId": "BULLDOZE", "uses": 13390}, {"moveId": "GUNK_SHOT", "uses": 9207}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 77.4}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 419, "matchups": [{"opponent": "bellossom", "rating": 702, "opRating": 297}, {"opponent": "gastrodon", "rating": 659, "opRating": 340}, {"opponent": "parasect", "rating": 629, "opRating": 370}, {"opponent": "marowak", "rating": 599, "opRating": 400}, {"opponent": "swadloon", "rating": 594, "opRating": 405}], "counters": [{"opponent": "talonflame", "rating": 55}, {"opponent": "cradily", "rating": 284}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "gligar", "rating": 385}, {"opponent": "clodsire", "rating": 466}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 21709}, {"moveId": "SAND_ATTACK", "uses": 19272}, {"moveId": "WING_ATTACK", "uses": 17318}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 21982}, {"moveId": "NIGHT_SLASH", "uses": 19739}, {"moveId": "EARTHQUAKE", "uses": 10508}, {"moveId": "SAND_TOMB", "uses": 6093}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 77.2}, {"speciesId": "skeledirge", "speciesName": "Skeledirge", "rating": 409, "matchups": [{"opponent": "abomasnow_shadow", "rating": 787, "opRating": 212}, {"opponent": "tropius", "rating": 654, "opRating": 345}, {"opponent": "castform_sunny", "rating": 640, "opRating": 359}, {"opponent": "ninetales_shadow", "rating": 539, "opRating": 460}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 122}, {"opponent": "clodsire", "rating": 165}, {"opponent": "gligar", "rating": 229}, {"opponent": "jumpluff_shadow", "rating": 316}, {"opponent": "cradily", "rating": 472}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31962}, {"moveId": "HEX", "uses": 20614}, {"moveId": "BITE", "uses": 5698}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15452}, {"moveId": "TORCH_SONG", "uses": 15003}, {"moveId": "CRUNCH", "uses": 8820}, {"moveId": "SHADOW_BALL", "uses": 8401}, {"moveId": "DISARMING_VOICE", "uses": 7022}, {"moveId": "FLAMETHROWER", "uses": 3478}]}, "moveset": ["INCINERATE", "TORCH_SONG", "SHADOW_BALL"], "score": 76.9}, {"speciesId": "lileep", "speciesName": "<PERSON><PERSON>", "rating": 398, "matchups": [{"opponent": "whiscash", "rating": 645, "opRating": 354}, {"opponent": "fletchinder", "rating": 615, "opRating": 384}, {"opponent": "munchlax", "rating": 605, "opRating": 394}, {"opponent": "gastrodon", "rating": 596, "opRating": 403}, {"opponent": "tropius", "rating": 516, "opRating": 483}], "counters": [{"opponent": "talonflame", "rating": 203}, {"opponent": "clodsire", "rating": 213}, {"opponent": "gligar", "rating": 282}, {"opponent": "cradily", "rating": 336}, {"opponent": "jumpluff_shadow", "rating": 388}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22863}, {"moveId": "BULLET_SEED", "uses": 19712}, {"moveId": "INFESTATION", "uses": 15750}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 23029}, {"moveId": "GRASS_KNOT", "uses": 21823}, {"moveId": "RETURN", "uses": 7860}, {"moveId": "MIRROR_COAT", "uses": 5547}]}, "moveset": ["ACID", "GRASS_KNOT", "ANCIENT_POWER"], "score": 76.5}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 358, "matchups": [{"opponent": "pidgeot", "rating": 715, "opRating": 284}, {"opponent": "pidgeot_shadow", "rating": 697, "opRating": 302}, {"opponent": "noctowl", "rating": 656, "opRating": 343}, {"opponent": "magcargo", "rating": 537, "opRating": 462}, {"opponent": "typhlosion_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "gligar", "rating": 167}, {"opponent": "cradily", "rating": 204}, {"opponent": "clodsire", "rating": 377}, {"opponent": "jumpluff_shadow", "rating": 382}, {"opponent": "talonflame", "rating": 455}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30613}, {"moveId": "MUD_SHOT", "uses": 27687}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 25794}, {"moveId": "MUDDY_WATER", "uses": 16782}, {"moveId": "DISCHARGE", "uses": 15714}]}, "moveset": ["THUNDER_SHOCK", "MUD_BOMB", "DISCHARGE"], "score": 76.3}, {"speciesId": "steelix_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 366, "matchups": [{"opponent": "pidgeot", "rating": 722, "opRating": 277}, {"opponent": "pidgeot_shadow", "rating": 642, "opRating": 357}, {"opponent": "noctowl", "rating": 634, "opRating": 365}, {"opponent": "farfetchd", "rating": 626, "opRating": 373}, {"opponent": "drampa", "rating": 587, "opRating": 412}], "counters": [{"opponent": "gligar", "rating": 248}, {"opponent": "clodsire", "rating": 331}, {"opponent": "talonflame", "rating": 337}, {"opponent": "jumpluff_shadow", "rating": 395}, {"opponent": "cradily", "rating": 427}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 27188}, {"moveId": "THUNDER_FANG", "uses": 18161}, {"moveId": "IRON_TAIL", "uses": 13041}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 17069}, {"moveId": "PSYCHIC_FANGS", "uses": 13426}, {"moveId": "CRUNCH", "uses": 10909}, {"moveId": "EARTHQUAKE", "uses": 8502}, {"moveId": "HEAVY_SLAM", "uses": 8296}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 76}, {"speciesId": "onix", "speciesName": "Onix", "rating": 333, "matchups": [{"opponent": "talonflame", "rating": 776}, {"opponent": "pidgeot", "rating": 709, "opRating": 290}, {"opponent": "typhlosion_shadow", "rating": 647, "opRating": 352}, {"opponent": "magcargo", "rating": 538, "opRating": 461}, {"opponent": "ninetales_shadow", "rating": 528, "opRating": 471}], "counters": [{"opponent": "diggersby", "rating": 114}, {"opponent": "clodsire", "rating": 158}, {"opponent": "cradily", "rating": 184}, {"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 460}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 31922}, {"moveId": "TACKLE", "uses": 26378}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 16134}, {"moveId": "STONE_EDGE", "uses": 12434}, {"moveId": "ROCK_SLIDE", "uses": 11804}, {"moveId": "HEAVY_SLAM", "uses": 5613}, {"moveId": "SAND_TOMB", "uses": 4984}, {"moveId": "RETURN", "uses": 4518}, {"moveId": "IRON_HEAD", "uses": 2769}]}, "moveset": ["ROCK_THROW", "BREAKING_SWIPE", "STONE_EDGE"], "score": 75.8}, {"speciesId": "geodude", "speciesName": "Geodude", "rating": 330, "matchups": [{"opponent": "talonflame", "rating": 867}, {"opponent": "pidgeot", "rating": 637, "opRating": 362}, {"opponent": "ninetales", "rating": 601, "opRating": 398}, {"opponent": "ninetales_shadow", "rating": 566, "opRating": 433}, {"opponent": "typhlosion_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "clodsire", "rating": 96}, {"opponent": "cradily", "rating": 145}, {"opponent": "furret", "rating": 175}, {"opponent": "gligar", "rating": 206}, {"opponent": "jumpluff_shadow", "rating": 428}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32343}, {"moveId": "TACKLE", "uses": 25957}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 23337}, {"moveId": "ROCK_SLIDE", "uses": 15096}, {"moveId": "DIG", "uses": 13325}, {"moveId": "RETURN", "uses": 6519}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 75.6}, {"speciesId": "<PERSON><PERSON>e", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 399, "matchups": [{"opponent": "quagsire", "rating": 732, "opRating": 267}, {"opponent": "quagsire_shadow", "rating": 697, "opRating": 302}, {"opponent": "gastrodon", "rating": 669, "opRating": 330}, {"opponent": "marowak", "rating": 665, "opRating": 334}, {"opponent": "claydol", "rating": 612, "opRating": 387}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "jumpluff_shadow", "rating": 169}, {"opponent": "talonflame", "rating": 181}, {"opponent": "clodsire", "rating": 278}, {"opponent": "cradily", "rating": 288}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42353}, {"moveId": "IRON_TAIL", "uses": 15947}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 17234}, {"moveId": "LEAF_TORNADO", "uses": 16629}, {"moveId": "WRAP", "uses": 16180}, {"moveId": "RETURN", "uses": 8196}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "WRAP"], "score": 75.6}, {"speciesId": "drampa", "speciesName": "Drampa", "rating": 378, "matchups": [{"opponent": "servine_shadow", "rating": 608, "opRating": 391}, {"opponent": "lickitung", "rating": 534, "opRating": 465}, {"opponent": "ninetales", "rating": 530, "opRating": 469}, {"opponent": "castform_sunny", "rating": 504, "opRating": 495}, {"opponent": "litleo", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 232}, {"opponent": "clodsire", "rating": 233}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 37288}, {"moveId": "EXTRASENSORY", "uses": 21012}], "chargedMoves": [{"moveId": "SWIFT", "uses": 22780}, {"moveId": "FLY", "uses": 18174}, {"moveId": "OUTRAGE", "uses": 12869}, {"moveId": "DRAGON_PULSE", "uses": 4297}]}, "moveset": ["DRAGON_BREATH", "SWIFT", "FLY"], "score": 75.3}, {"speciesId": "hippopotas", "speciesName": "Hippopotas", "rating": 351, "matchups": [{"opponent": "steelix", "rating": 680, "opRating": 319}, {"opponent": "fletchinder", "rating": 555, "opRating": 444}, {"opponent": "castform_sunny", "rating": 519, "opRating": 480}, {"opponent": "magcargo", "rating": 516, "opRating": 483}, {"opponent": "ninetales", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 129}, {"opponent": "jumpluff_shadow", "rating": 330}, {"opponent": "cradily", "rating": 333}, {"opponent": "clodsire", "rating": 423}, {"opponent": "gligar", "rating": 438}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 27859}, {"moveId": "TACKLE", "uses": 17261}, {"moveId": "BITE", "uses": 13128}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21525}, {"moveId": "BODY_SLAM", "uses": 17540}, {"moveId": "DIG", "uses": 14926}, {"moveId": "RETURN", "uses": 4285}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 75.3}, {"speciesId": "chansey", "speciesName": "<PERSON><PERSON>", "rating": 378, "matchups": [{"opponent": "amoon<PERSON>s", "rating": 610, "opRating": 389}, {"opponent": "oricorio_baile", "rating": 567, "opRating": 432}, {"opponent": "marowak_alolan", "rating": 536, "opRating": 463}, {"opponent": "lickitung", "rating": 528, "opRating": 471}, {"opponent": "miltank", "rating": 523, "opRating": 476}], "counters": [{"opponent": "talonflame", "rating": 192}, {"opponent": "cradily", "rating": 236}, {"opponent": "jumpluff_shadow", "rating": 294}, {"opponent": "clodsire", "rating": 391}, {"opponent": "gligar", "rating": 400}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 32786}, {"moveId": "POUND", "uses": 25514}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 19946}, {"moveId": "PSYCHIC", "uses": 16840}, {"moveId": "HYPER_BEAM", "uses": 16583}, {"moveId": "PSYBEAM", "uses": 4975}]}, "moveset": ["ZEN_HEADBUTT", "DAZZLING_GLEAM", "PSYCHIC"], "score": 75.1}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 366, "matchups": [{"opponent": "noctowl", "rating": 673, "opRating": 326}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 593, "opRating": 406}, {"opponent": "tropius", "rating": 562, "opRating": 437}, {"opponent": "jumpluff_shadow", "rating": 513}, {"opponent": "marowak_alolan", "rating": 513, "opRating": 486}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "furret", "rating": 206}, {"opponent": "clodsire", "rating": 245}, {"opponent": "gligar", "rating": 347}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 23320}, {"moveId": "BULLET_SEED", "uses": 19811}, {"moveId": "INFESTATION", "uses": 15152}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18783}, {"moveId": "GRASS_KNOT", "uses": 15450}, {"moveId": "ROCK_SLIDE", "uses": 12114}, {"moveId": "STONE_EDGE", "uses": 6332}, {"moveId": "BULLDOZE", "uses": 5471}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 74.7}, {"speciesId": "fletchinder", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 405, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 772, "opRating": 227}, {"opponent": "wormadam_sandy", "rating": 741, "opRating": 258}, {"opponent": "tropius", "rating": 653, "opRating": 346}, {"opponent": "abomasnow_shadow", "rating": 643, "opRating": 356}, {"opponent": "gliscor", "rating": 566, "opRating": 433}], "counters": [{"opponent": "clodsire", "rating": 103}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 125}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "gligar", "rating": 492}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 29279}, {"moveId": "STEEL_WING", "uses": 15785}, {"moveId": "PECK", "uses": 13232}], "chargedMoves": [{"moveId": "FLY", "uses": 24220}, {"moveId": "AERIAL_ACE", "uses": 18469}, {"moveId": "FLAME_CHARGE", "uses": 13778}, {"moveId": "HEAT_WAVE", "uses": 2037}]}, "moveset": ["EMBER", "FLY", "AERIAL_ACE"], "score": 74.2}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 360, "matchups": [{"opponent": "pignite", "rating": 656, "opRating": 344}, {"opponent": "litleo", "rating": 620, "opRating": 380}, {"opponent": "darum<PERSON>", "rating": 616, "opRating": 384}, {"opponent": "fletchinder", "rating": 612, "opRating": 388}, {"opponent": "centiskorch", "rating": 540, "opRating": 460}], "counters": [{"opponent": "jumpluff_shadow", "rating": 133}, {"opponent": "talonflame", "rating": 181}, {"opponent": "gligar", "rating": 244}, {"opponent": "clodsire", "rating": 341}, {"opponent": "cradily", "rating": 406}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 21222}, {"moveId": "SAND_ATTACK", "uses": 19576}, {"moveId": "MUD_SHOT", "uses": 17592}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 19330}, {"moveId": "SCORCHING_SANDS", "uses": 12962}, {"moveId": "STONE_EDGE", "uses": 10714}, {"moveId": "BOOMBURST", "uses": 6425}, {"moveId": "EARTH_POWER", "uses": 5118}, {"moveId": "EARTHQUAKE", "uses": 3707}]}, "moveset": ["DRAGON_TAIL", "SCORCHING_SANDS", "DRAGON_CLAW"], "score": 74}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 407, "matchups": [{"opponent": "swadloon", "rating": 723, "opRating": 276}, {"opponent": "lura<PERSON>s", "rating": 630, "opRating": 369}, {"opponent": "tropius", "rating": 601, "opRating": 398}, {"opponent": "marowak_alolan_shadow", "rating": 541, "opRating": 458}, {"opponent": "marowak_alolan", "rating": 514, "opRating": 485}], "counters": [{"opponent": "clodsire", "rating": 127}, {"opponent": "cradily", "rating": 138}, {"opponent": "talonflame", "rating": 225}, {"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "gligar", "rating": 362}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34397}, {"moveId": "EXTRASENSORY", "uses": 23903}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 24310}, {"moveId": "NIGHT_SHADE", "uses": 15285}, {"moveId": "PSYCHIC", "uses": 11703}, {"moveId": "SHADOW_BALL", "uses": 6917}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "NIGHT_SHADE"], "score": 74}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 370, "matchups": [{"opponent": "ferrothorn", "rating": 667, "opRating": 332}, {"opponent": "lickilicky", "rating": 627, "opRating": 372}, {"opponent": "abomasnow", "rating": 587, "opRating": 412}, {"opponent": "miltank", "rating": 554, "opRating": 445}, {"opponent": "furret", "rating": 529, "opRating": 470}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 129}, {"opponent": "clodsire", "rating": 206}, {"opponent": "cradily", "rating": 458}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 34903}, {"moveId": "LICK", "uses": 23397}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25233}, {"moveId": "CROSS_CHOP", "uses": 21580}, {"moveId": "HYPER_BEAM", "uses": 6195}, {"moveId": "GUNK_SHOT", "uses": 4750}, {"moveId": "OBSTRUCT", "uses": 647}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "CROSS_CHOP"], "score": 73.8}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 293, "matchups": [{"opponent": "gligar", "rating": 652}, {"opponent": "clodsire", "rating": 649, "opRating": 350}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "gligar_shadow", "rating": 620, "opRating": 379}, {"opponent": "jumpluff_shadow", "rating": 542}], "counters": [{"opponent": "magcargo", "rating": 64}, {"opponent": "talonflame", "rating": 114}, {"opponent": "furret", "rating": 159}, {"opponent": "diggersby", "rating": 379}, {"opponent": "cradily", "rating": 475}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34805}, {"moveId": "ICE_SHARD", "uses": 23495}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19958}, {"moveId": "ICICLE_SPEAR", "uses": 16751}, {"moveId": "STONE_EDGE", "uses": 6991}, {"moveId": "HIGH_HORSEPOWER", "uses": 6449}, {"moveId": "BULLDOZE", "uses": 5291}, {"moveId": "RETURN", "uses": 2783}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 73.8}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 398, "matchups": [{"opponent": "whiscash", "rating": 781, "opRating": 218}, {"opponent": "gastrodon", "rating": 710, "opRating": 289}, {"opponent": "quagsire_shadow", "rating": 671, "opRating": 328}, {"opponent": "claydol", "rating": 644, "opRating": 355}, {"opponent": "swampert_shadow", "rating": 574, "opRating": 425}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "clodsire", "rating": 187}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 274}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 20107}, {"moveId": "BULLET_SEED", "uses": 15990}, {"moveId": "MAGICAL_LEAF", "uses": 14758}, {"moveId": "RAZOR_LEAF", "uses": 7403}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36600}, {"moveId": "DAZZLING_GLEAM", "uses": 9672}, {"moveId": "RETURN", "uses": 8215}, {"moveId": "PETAL_BLIZZARD", "uses": 3709}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 73.5}, {"speciesId": "oinkologne_female", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Female)", "rating": 355, "matchups": [{"opponent": "lilligant", "rating": 712, "opRating": 287}, {"opponent": "chansey", "rating": 598, "opRating": 401}, {"opponent": "quagsire", "rating": 586, "opRating": 413}, {"opponent": "lickitung", "rating": 574, "opRating": 425}, {"opponent": "rhyhorn", "rating": 508, "opRating": 491}], "counters": [{"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "talonflame", "rating": 244}, {"opponent": "clodsire", "rating": 298}, {"opponent": "cradily", "rating": 305}, {"opponent": "gligar", "rating": 408}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 46713}, {"moveId": "TAKE_DOWN", "uses": 11587}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28505}, {"moveId": "TRAILBLAZE", "uses": 18372}, {"moveId": "DIG", "uses": 11429}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 73.3}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 365, "matchups": [{"opponent": "salazzle", "rating": 651, "opRating": 348}, {"opponent": "chansey", "rating": 626, "opRating": 373}, {"opponent": "tauros_blaze", "rating": 616, "opRating": 383}, {"opponent": "quagsire", "rating": 602, "opRating": 397}, {"opponent": "lickitung", "rating": 602, "opRating": 397}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "talonflame", "rating": 196}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 347}, {"opponent": "clodsire", "rating": 451}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 47131}, {"moveId": "ZEN_HEADBUTT", "uses": 10266}, {"moveId": "YAWN", "uses": 933}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 19467}, {"moveId": "TRAILBLAZE", "uses": 13885}, {"moveId": "FUTURE_SIGHT", "uses": 9260}, {"moveId": "PSYCHIC", "uses": 8916}, {"moveId": "FOUL_PLAY", "uses": 6735}]}, "moveset": ["CONFUSION", "BRUTAL_SWING", "TRAILBLAZE"], "score": 73.1}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 388, "matchups": [{"opponent": "marowak", "rating": 738, "opRating": 261}, {"opponent": "quagsire_shadow", "rating": 688, "opRating": 311}, {"opponent": "claydol", "rating": 600, "opRating": 400}, {"opponent": "flygon_shadow", "rating": 538, "opRating": 461}, {"opponent": "diggersby", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 118}, {"opponent": "clodsire", "rating": 290}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45586}, {"moveId": "IRON_TAIL", "uses": 12714}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 20911}, {"moveId": "AERIAL_ACE", "uses": 15385}, {"moveId": "LEAF_TORNADO", "uses": 10806}, {"moveId": "RETURN", "uses": 5666}, {"moveId": "GRASS_KNOT", "uses": 5512}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 73.1}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 410, "matchups": [{"opponent": "whiscash", "rating": 764, "opRating": 235}, {"opponent": "gastrodon", "rating": 668, "opRating": 331}, {"opponent": "quagsire", "rating": 663, "opRating": 336}, {"opponent": "quagsire_shadow", "rating": 575, "opRating": 424}, {"opponent": "furret", "rating": 571, "opRating": 428}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "gligar", "rating": 141}, {"opponent": "clodsire", "rating": 266}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28673}, {"moveId": "LEAFAGE", "uses": 21162}, {"moveId": "RAZOR_LEAF", "uses": 8483}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 23152}, {"moveId": "SUPER_POWER", "uses": 14714}, {"moveId": "X_SCISSOR", "uses": 11729}, {"moveId": "TRAILBLAZE", "uses": 5743}, {"moveId": "LEAF_STORM", "uses": 3000}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 72.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Zygarde (50% Forme)", "rating": 367, "matchups": [{"opponent": "geodude", "rating": 631, "opRating": 368}, {"opponent": "nidoqueen", "rating": 586, "opRating": 413}, {"opponent": "entei", "rating": 578, "opRating": 421}, {"opponent": "torkoal", "rating": 571, "opRating": 428}, {"opponent": "geodude_shadow", "rating": 545, "opRating": 454}], "counters": [{"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "talonflame", "rating": 248}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 284}, {"opponent": "clodsire", "rating": 348}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 38035}, {"moveId": "BITE", "uses": 14985}, {"moveId": "ZEN_HEADBUTT", "uses": 5245}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 15943}, {"moveId": "OUTRAGE", "uses": 15821}, {"moveId": "EARTHQUAKE", "uses": 10749}, {"moveId": "BULLDOZE", "uses": 10100}, {"moveId": "HYPER_BEAM", "uses": 5794}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "EARTHQUAKE"], "score": 72.8}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 397, "matchups": [{"opponent": "abomasnow", "rating": 781, "opRating": 218}, {"opponent": "abomasnow_shadow", "rating": 769, "opRating": 230}, {"opponent": "<PERSON><PERSON>e", "rating": 714, "opRating": 285}, {"opponent": "tropius", "rating": 650, "opRating": 349}, {"opponent": "jumpluff_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "magcargo", "rating": 64}, {"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 137}, {"opponent": "cradily", "rating": 166}, {"opponent": "gligar", "rating": 232}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 24773}, {"moveId": "FIRE_SPIN", "uses": 21940}, {"moveId": "FEINT_ATTACK", "uses": 11645}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17614}, {"moveId": "PSYSHOCK", "uses": 12545}, {"moveId": "SCORCHING_SANDS", "uses": 9515}, {"moveId": "OVERHEAT", "uses": 7070}, {"moveId": "SOLAR_BEAM", "uses": 4266}, {"moveId": "FLAMETHROWER", "uses": 3763}, {"moveId": "FIRE_BLAST", "uses": 2046}, {"moveId": "HEAT_WAVE", "uses": 1200}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 72.6}, {"speciesId": "gogoat", "speciesName": "Gogoat", "rating": 370, "matchups": [{"opponent": "whiscash", "rating": 775, "opRating": 224}, {"opponent": "marowak", "rating": 724, "opRating": 275}, {"opponent": "quagsire_shadow", "rating": 639, "opRating": 360}, {"opponent": "swampert_shadow", "rating": 545, "opRating": 454}, {"opponent": "claydol", "rating": 518, "opRating": 481}], "counters": [{"opponent": "jumpluff_shadow", "rating": 88}, {"opponent": "talonflame", "rating": 111}, {"opponent": "clodsire", "rating": 250}, {"opponent": "cradily", "rating": 298}, {"opponent": "gligar", "rating": 328}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 38684}, {"moveId": "ROCK_SMASH", "uses": 12567}, {"moveId": "ZEN_HEADBUTT", "uses": 7022}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37611}, {"moveId": "BRICK_BREAK", "uses": 14748}, {"moveId": "SEED_BOMB", "uses": 5873}]}, "moveset": ["VINE_WHIP", "LEAF_BLADE", "BRICK_BREAK"], "score": 72.4}, {"speciesId": "swadloon", "speciesName": "Swadloon", "rating": 403, "matchups": [{"opponent": "gogoat", "rating": 788, "opRating": 211}, {"opponent": "whiscash", "rating": 711, "opRating": 288}, {"opponent": "gastrodon", "rating": 696, "opRating": 303}, {"opponent": "claydol", "rating": 670, "opRating": 329}, {"opponent": "<PERSON><PERSON>e", "rating": 648, "opRating": 351}], "counters": [{"opponent": "talonflame", "rating": 111}, {"opponent": "clodsire", "rating": 132}, {"opponent": "gligar", "rating": 141}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "cradily", "rating": 364}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 40394}, {"moveId": "STRUGGLE_BUG", "uses": 17906}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 20190}, {"moveId": "SILVER_WIND", "uses": 20074}, {"moveId": "BUG_BUZZ", "uses": 17996}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "SILVER_WIND"], "score": 72.4}, {"speciesId": "turtonator", "speciesName": "Turtonator", "rating": 393, "matchups": [{"opponent": "ninetales_shadow", "rating": 734, "opRating": 265}, {"opponent": "abomasnow_shadow", "rating": 734, "opRating": 265}, {"opponent": "ninetales", "rating": 659, "opRating": 340}, {"opponent": "castform_sunny", "rating": 659, "opRating": 340}, {"opponent": "typhlosion_shadow", "rating": 615, "opRating": 384}], "counters": [{"opponent": "clodsire", "rating": 182}, {"opponent": "gligar", "rating": 202}, {"opponent": "cradily", "rating": 215}, {"opponent": "talonflame", "rating": 248}, {"opponent": "jumpluff_shadow", "rating": 290}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 24712}, {"moveId": "EMBER", "uses": 17682}, {"moveId": "FIRE_SPIN", "uses": 15895}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 18250}, {"moveId": "OVERHEAT", "uses": 16892}, {"moveId": "DRAGON_PULSE", "uses": 15925}, {"moveId": "FLASH_CANNON", "uses": 7196}]}, "moveset": ["INCINERATE", "DRAGON_PULSE", "OVERHEAT"], "score": 72.4}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 369, "matchups": [{"opponent": "swadloon", "rating": 771, "opRating": 228}, {"opponent": "abomasnow_shadow", "rating": 684, "opRating": 315}, {"opponent": "diggersby", "rating": 592, "opRating": 407}, {"opponent": "castform_sunny", "rating": 592, "opRating": 407}, {"opponent": "gligar", "rating": 552}], "counters": [{"opponent": "magcargo", "rating": 68}, {"opponent": "clodsire", "rating": 122}, {"opponent": "talonflame", "rating": 125}, {"opponent": "cradily", "rating": 145}, {"opponent": "jumpluff_shadow", "rating": 316}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 13757}, {"moveId": "EXTRASENSORY", "uses": 3975}, {"moveId": "STEEL_WING", "uses": 3600}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3250}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3192}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3043}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2686}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2597}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2464}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2343}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2308}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2130}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2089}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2063}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1967}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1816}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1783}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1689}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1472}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27174}, {"moveId": "SACRED_FIRE", "uses": 14636}, {"moveId": "EARTHQUAKE", "uses": 7685}, {"moveId": "SOLAR_BEAM", "uses": 5858}, {"moveId": "FIRE_BLAST", "uses": 2951}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 71.9}, {"speciesId": "onix_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 323, "matchups": [{"opponent": "talonflame", "rating": 685}, {"opponent": "pidgeot", "rating": 657, "opRating": 342}, {"opponent": "typhlosion_shadow", "rating": 633, "opRating": 366}, {"opponent": "pidgeot_shadow", "rating": 628, "opRating": 371}, {"opponent": "jumpluff_shadow", "rating": 504}], "counters": [{"opponent": "diggersby", "rating": 114}, {"opponent": "cradily", "rating": 121}, {"opponent": "clodsire", "rating": 137}, {"opponent": "furret", "rating": 153}, {"opponent": "gligar", "rating": 232}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32987}, {"moveId": "TACKLE", "uses": 25313}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 17647}, {"moveId": "STONE_EDGE", "uses": 13363}, {"moveId": "ROCK_SLIDE", "uses": 12699}, {"moveId": "HEAVY_SLAM", "uses": 6081}, {"moveId": "SAND_TOMB", "uses": 5402}, {"moveId": "IRON_HEAD", "uses": 3100}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "BREAKING_SWIPE", "STONE_EDGE"], "score": 71.9}, {"speciesId": "servine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 371, "matchups": [{"opponent": "whiscash", "rating": 778, "opRating": 221}, {"opponent": "quagsire", "rating": 697, "opRating": 302}, {"opponent": "quagsire_shadow", "rating": 619, "opRating": 380}, {"opponent": "swampert_shadow", "rating": 570, "opRating": 429}, {"opponent": "claydol", "rating": 538, "opRating": 461}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "talonflame", "rating": 207}, {"opponent": "clodsire", "rating": 338}, {"opponent": "cradily", "rating": 340}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42615}, {"moveId": "IRON_TAIL", "uses": 15685}], "chargedMoves": [{"moveId": "WRAP", "uses": 19932}, {"moveId": "GRASS_KNOT", "uses": 19525}, {"moveId": "LEAF_TORNADO", "uses": 18846}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "WRAP"], "score": 71.9}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 386, "matchups": [{"opponent": "stunfisk", "rating": 687, "opRating": 312}, {"opponent": "gastrodon", "rating": 601, "opRating": 398}, {"opponent": "marowak", "rating": 578, "opRating": 421}, {"opponent": "bellossom", "rating": 562, "opRating": 437}, {"opponent": "claydol", "rating": 542, "opRating": 457}], "counters": [{"opponent": "talonflame", "rating": 111}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 164}, {"opponent": "clodsire", "rating": 336}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 33060}, {"moveId": "CONFUSION", "uses": 25240}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 27248}, {"moveId": "BULLDOZE", "uses": 21233}, {"moveId": "PSYBEAM", "uses": 9844}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BULLDOZE"], "score": 71.9}, {"speciesId": "obstagoon_shadow", "speciesName": "Obstagoon (Shadow)", "rating": 352, "matchups": [{"opponent": "lickitung", "rating": 697, "opRating": 302}, {"opponent": "chansey", "rating": 671, "opRating": 328}, {"opponent": "steelix", "rating": 667, "opRating": 332}, {"opponent": "lileep", "rating": 580, "opRating": 419}, {"opponent": "cradily", "rating": 569}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 225}, {"opponent": "magcargo", "rating": 243}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 35612}, {"moveId": "LICK", "uses": 22688}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25209}, {"moveId": "CROSS_CHOP", "uses": 21555}, {"moveId": "HYPER_BEAM", "uses": 6167}, {"moveId": "GUNK_SHOT", "uses": 4742}, {"moveId": "OBSTRUCT", "uses": 633}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "NIGHT_SLASH"], "score": 71.7}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 347, "matchups": [{"opponent": "ninetales_shadow", "rating": 736, "opRating": 263}, {"opponent": "magcargo", "rating": 655, "opRating": 344}, {"opponent": "skeledirge", "rating": 565, "opRating": 434}, {"opponent": "typhlosion_shadow", "rating": 559, "opRating": 440}, {"opponent": "clodsire", "rating": 512, "opRating": 487}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "cradily", "rating": 65}, {"opponent": "furret", "rating": 78}, {"opponent": "talonflame", "rating": 81}, {"opponent": "gligar", "rating": 324}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30543}, {"moveId": "WATER_GUN", "uses": 27757}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 23953}, {"moveId": "MUD_BOMB", "uses": 12770}, {"moveId": "STONE_EDGE", "uses": 8926}, {"moveId": "SLUDGE_BOMB", "uses": 7789}, {"moveId": "EARTHQUAKE", "uses": 3031}, {"moveId": "ACID_SPRAY", "uses": 1704}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 71.7}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 371, "matchups": [{"opponent": "lileep", "rating": 718, "opRating": 281}, {"opponent": "quagsire_shadow", "rating": 625, "opRating": 375}, {"opponent": "miltank", "rating": 585, "opRating": 414}, {"opponent": "swampert_shadow", "rating": 515, "opRating": 484}, {"opponent": "cradily", "rating": 511}], "counters": [{"opponent": "talonflame", "rating": 33}, {"opponent": "magcargo", "rating": 55}, {"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "clodsire", "rating": 250}, {"opponent": "gligar", "rating": 305}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30456}, {"moveId": "METAL_CLAW", "uses": 27844}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18583}, {"moveId": "MIRROR_SHOT", "uses": 14342}, {"moveId": "THUNDER", "uses": 7523}, {"moveId": "FLASH_CANNON", "uses": 7415}, {"moveId": "RETURN", "uses": 7413}, {"moveId": "ACID_SPRAY", "uses": 3043}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "FLASH_CANNON"], "score": 71.5}, {"speciesId": "golem", "speciesName": "Golem", "rating": 322, "matchups": [{"opponent": "magcargo", "rating": 636, "opRating": 363}, {"opponent": "pidgeot", "rating": 581, "opRating": 418}, {"opponent": "talonflame", "rating": 559}, {"opponent": "typhlosion_shadow", "rating": 534, "opRating": 465}, {"opponent": "ninetales_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 128}, {"opponent": "furret", "rating": 153}, {"opponent": "clodsire", "rating": 233}, {"opponent": "jumpluff_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23413}, {"moveId": "MUD_SHOT", "uses": 18541}, {"moveId": "ROCK_THROW", "uses": 16412}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17511}, {"moveId": "ROCK_BLAST", "uses": 15734}, {"moveId": "EARTHQUAKE", "uses": 13223}, {"moveId": "ANCIENT_POWER", "uses": 11795}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "ROCK_BLAST"], "score": 71.5}, {"speciesId": "litleo", "speciesName": "Litleo", "rating": 388, "matchups": [{"opponent": "abomasnow_shadow", "rating": 791, "opRating": 208}, {"opponent": "swadloon", "rating": 711, "opRating": 288}, {"opponent": "tropius", "rating": 659, "opRating": 340}, {"opponent": "skeledirge", "rating": 638, "opRating": 361}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 125}, {"opponent": "cradily", "rating": 187}, {"opponent": "gligar", "rating": 229}, {"opponent": "clodsire", "rating": 240}, {"opponent": "jumpluff_shadow", "rating": 316}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 20972}, {"moveId": "EMBER", "uses": 14553}, {"moveId": "FIRE_FANG", "uses": 12718}, {"moveId": "TACKLE", "uses": 10055}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24314}, {"moveId": "FLAME_CHARGE", "uses": 24016}, {"moveId": "FLAMETHROWER", "uses": 9982}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "CRUNCH"], "score": 71.5}, {"speciesId": "spinda", "speciesName": "Spinda", "rating": 346, "matchups": [{"opponent": "pumpkaboo_super", "rating": 683, "opRating": 316}, {"opponent": "gourgeist_super", "rating": 623, "opRating": 376}, {"opponent": "run<PERSON><PERSON>", "rating": 556, "opRating": 443}, {"opponent": "chansey", "rating": 545, "opRating": 454}, {"opponent": "marowak_alolan", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "gligar", "rating": 202}, {"opponent": "clodsire", "rating": 346}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 32042}, {"moveId": "PSYCHO_CUT", "uses": 26258}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 27099}, {"moveId": "ROCK_TOMB", "uses": 20579}, {"moveId": "DIG", "uses": 10616}]}, "moveset": ["SUCKER_PUNCH", "ROCK_TOMB", "ICY_WIND"], "score": 71.5}, {"speciesId": "rhyhorn", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 342, "matchups": [{"opponent": "magcargo", "rating": 783, "opRating": 216}, {"opponent": "steelix", "rating": 742, "opRating": 257}, {"opponent": "ninetales", "rating": 672, "opRating": 327}, {"opponent": "typhlosion_shadow", "rating": 625, "opRating": 374}, {"opponent": "ninetales_shadow", "rating": 597, "opRating": 402}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 114}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "talonflame", "rating": 314}, {"opponent": "clodsire", "rating": 454}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 42534}, {"moveId": "ROCK_SMASH", "uses": 15766}], "chargedMoves": [{"moveId": "BULLDOZE", "uses": 16842}, {"moveId": "STOMP", "uses": 16416}, {"moveId": "HORN_ATTACK", "uses": 15284}, {"moveId": "RETURN", "uses": 9747}]}, "moveset": ["MUD_SLAP", "BULLDOZE", "STOMP"], "score": 70.8}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 336, "matchups": [{"opponent": "pignite", "rating": 576, "opRating": 424}, {"opponent": "arcanine", "rating": 552, "opRating": 448}, {"opponent": "litleo", "rating": 548, "opRating": 452}, {"opponent": "hippopotas", "rating": 524, "opRating": 476}, {"opponent": "flapple", "rating": 520, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 160}, {"opponent": "talonflame", "rating": 211}, {"opponent": "clodsire", "rating": 237}, {"opponent": "gligar", "rating": 255}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 21152}, {"moveId": "SAND_ATTACK", "uses": 19579}, {"moveId": "MUD_SHOT", "uses": 17562}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 19288}, {"moveId": "SCORCHING_SANDS", "uses": 12965}, {"moveId": "STONE_EDGE", "uses": 10731}, {"moveId": "BOOMBURST", "uses": 6432}, {"moveId": "EARTH_POWER", "uses": 5115}, {"moveId": "EARTHQUAKE", "uses": 3706}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "SCORCHING_SANDS"], "score": 70.6}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 333, "matchups": [{"opponent": "typhlosion_hisuian", "rating": 599, "opRating": 400}, {"opponent": "pignite", "rating": 559, "opRating": 440}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 523, "opRating": 476}, {"opponent": "darum<PERSON>", "rating": 511, "opRating": 488}, {"opponent": "magcargo", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 270}, {"opponent": "cradily", "rating": 322}, {"opponent": "clodsire", "rating": 375}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31672}, {"moveId": "MUD_SHOT", "uses": 26628}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 19557}, {"moveId": "EARTH_POWER", "uses": 16743}, {"moveId": "FIRE_BLAST", "uses": 8842}, {"moveId": "SAND_TOMB", "uses": 7015}, {"moveId": "EARTHQUAKE", "uses": 6065}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 70.6}, {"speciesId": "linoone", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 342, "matchups": [{"opponent": "rhyhorn", "rating": 655, "opRating": 344}, {"opponent": "whiscash", "rating": 649, "opRating": 350}, {"opponent": "quagsire_shadow", "rating": 603, "opRating": 396}, {"opponent": "quagsire", "rating": 590, "opRating": 409}, {"opponent": "gastrodon", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "clodsire", "rating": 225}, {"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "cradily", "rating": 350}, {"opponent": "gligar", "rating": 408}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 22025}, {"moveId": "SAND_ATTACK", "uses": 20041}, {"moveId": "TACKLE", "uses": 16233}], "chargedMoves": [{"moveId": "SWIFT", "uses": 28150}, {"moveId": "GRASS_KNOT", "uses": 13762}, {"moveId": "DIG", "uses": 9976}, {"moveId": "THUNDER", "uses": 6382}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "GRASS_KNOT"], "score": 70.6}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 344, "matchups": [{"opponent": "quagsire_shadow", "rating": 633, "opRating": 366}, {"opponent": "furret", "rating": 564, "opRating": 435}, {"opponent": "cradily", "rating": 536}, {"opponent": "claydol", "rating": 528, "opRating": 471}, {"opponent": "flygon", "rating": 528, "opRating": 471}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "magcargo", "rating": 94}, {"opponent": "gligar", "rating": 110}, {"opponent": "clodsire", "rating": 314}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 35393}, {"moveId": "SMACK_DOWN", "uses": 16565}, {"moveId": "LOW_KICK", "uses": 6329}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21115}, {"moveId": "FRENZY_PLANT", "uses": 19157}, {"moveId": "THUNDER_PUNCH", "uses": 8803}, {"moveId": "ENERGY_BALL", "uses": 3643}, {"moveId": "GYRO_BALL", "uses": 3496}, {"moveId": "SOLAR_BEAM", "uses": 2195}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SUPER_POWER"], "score": 70.3}, {"speciesId": "marowak_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 369, "matchups": [{"opponent": "heatran", "rating": 840, "opRating": 160}, {"opponent": "steelix", "rating": 812, "opRating": 188}, {"opponent": "steelix_shadow", "rating": 796, "opRating": 204}, {"opponent": "clodsire", "rating": 640, "opRating": 360}, {"opponent": "marowak_alolan", "rating": 552, "opRating": 448}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 125}, {"opponent": "cradily", "rating": 190}, {"opponent": "furret", "rating": 265}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45272}, {"moveId": "ROCK_SMASH", "uses": 13028}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 30513}, {"moveId": "ROCK_SLIDE", "uses": 16768}, {"moveId": "DIG", "uses": 5880}, {"moveId": "EARTHQUAKE", "uses": 5087}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "ROCK_SLIDE"], "score": 70.3}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 339, "matchups": [{"opponent": "steelix", "rating": 811, "opRating": 188}, {"opponent": "pidgeot", "rating": 691, "opRating": 308}, {"opponent": "pidgeot_shadow", "rating": 645, "opRating": 354}, {"opponent": "drampa", "rating": 587, "opRating": 412}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 164}, {"opponent": "jumpluff_shadow", "rating": 408}, {"opponent": "cradily", "rating": 420}, {"opponent": "clodsire", "rating": 451}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31022}, {"moveId": "METAL_CLAW", "uses": 27278}], "chargedMoves": [{"moveId": "MUDDY_WATER", "uses": 17437}, {"moveId": "ROCK_SLIDE", "uses": 17314}, {"moveId": "EARTHQUAKE", "uses": 13551}, {"moveId": "FLASH_CANNON", "uses": 10050}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 70.3}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 374, "matchups": [{"opponent": "wormadam_plant", "rating": 796, "opRating": 204}, {"opponent": "abomasnow", "rating": 768, "opRating": 232}, {"opponent": "abomasnow_shadow", "rating": 740, "opRating": 260}, {"opponent": "tropius", "rating": 604, "opRating": 396}, {"opponent": "castform_sunny", "rating": 552, "opRating": 448}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 211}, {"opponent": "clodsire", "rating": 233}, {"opponent": "gligar", "rating": 290}, {"opponent": "jumpluff_shadow", "rating": 418}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 20870}, {"moveId": "SNARL", "uses": 18314}, {"moveId": "THUNDER_FANG", "uses": 11932}, {"moveId": "BITE", "uses": 7106}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 11564}, {"moveId": "WILD_CHARGE", "uses": 10939}, {"moveId": "FLAMETHROWER", "uses": 10081}, {"moveId": "CRUNCH", "uses": 9433}, {"moveId": "SCORCHING_SANDS", "uses": 8831}, {"moveId": "BULLDOZE", "uses": 4720}, {"moveId": "FIRE_BLAST", "uses": 2705}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 70.1}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 315, "matchups": [{"opponent": "castform_sunny", "rating": 750, "opRating": 250}, {"opponent": "ninetales", "rating": 746, "opRating": 253}, {"opponent": "magcargo", "rating": 705, "opRating": 294}, {"opponent": "ninetales_shadow", "rating": 691, "opRating": 308}, {"opponent": "skeledirge", "rating": 518, "opRating": 481}], "counters": [{"opponent": "cradily", "rating": 65}, {"opponent": "talonflame", "rating": 81}, {"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "gligar", "rating": 412}, {"opponent": "clodsire", "rating": 437}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31683}, {"moveId": "WATER_GUN", "uses": 26617}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 23447}, {"moveId": "SLUDGE", "uses": 12212}, {"moveId": "MUDDY_WATER", "uses": 8965}, {"moveId": "SURF", "uses": 6534}, {"moveId": "EARTHQUAKE", "uses": 5182}, {"moveId": "SLUDGE_WAVE", "uses": 2203}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 70.1}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 374, "matchups": [{"opponent": "oranguru", "rating": 625, "opRating": 375}, {"opponent": "lickitung", "rating": 593, "opRating": 406}, {"opponent": "abomasnow", "rating": 585, "opRating": 414}, {"opponent": "drampa", "rating": 554, "opRating": 445}, {"opponent": "dunsparce", "rating": 523, "opRating": 476}], "counters": [{"opponent": "talonflame", "rating": 62}, {"opponent": "clodsire", "rating": 177}, {"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "cradily", "rating": 267}, {"opponent": "gligar", "rating": 305}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 31019}, {"moveId": "TACKLE", "uses": 21651}, {"moveId": "TAKE_DOWN", "uses": 5629}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26507}, {"moveId": "WILD_CHARGE", "uses": 18496}, {"moveId": "PAYBACK", "uses": 13289}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "PAYBACK"], "score": 69.9}, {"speciesId": "golurk_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 333, "matchups": [{"opponent": "magcargo", "rating": 742, "opRating": 257}, {"opponent": "steelix", "rating": 730, "opRating": 269}, {"opponent": "typhlosion_shadow", "rating": 640, "opRating": 359}, {"opponent": "diggersby", "rating": 621, "opRating": 378}, {"opponent": "clodsire", "rating": 515}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 137}, {"opponent": "gligar", "rating": 137}, {"opponent": "cradily", "rating": 156}, {"opponent": "furret", "rating": 190}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31719}, {"moveId": "ASTONISH", "uses": 26581}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 22177}, {"moveId": "SHADOW_PUNCH", "uses": 20787}, {"moveId": "EARTH_POWER", "uses": 11642}, {"moveId": "POLTERGEIST", "uses": 3670}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "DYNAMIC_PUNCH"], "score": 69.9}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 351, "matchups": [{"opponent": "swampert_shadow", "rating": 929, "opRating": 70}, {"opponent": "quagsire", "rating": 822, "opRating": 177}, {"opponent": "quagsire_shadow", "rating": 812, "opRating": 187}, {"opponent": "whiscash", "rating": 808, "opRating": 191}, {"opponent": "gastrodon", "rating": 762, "opRating": 237}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 213}, {"opponent": "cradily", "rating": 281}, {"opponent": "clodsire", "rating": 305}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30383}, {"moveId": "BITE", "uses": 27917}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30596}, {"moveId": "ENERGY_BALL", "uses": 21343}, {"moveId": "SOLAR_BEAM", "uses": 6279}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 69.9}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 326, "matchups": [{"opponent": "lickilicky", "rating": 620, "opRating": 379}, {"opponent": "lickitung", "rating": 608, "opRating": 391}, {"opponent": "miltank", "rating": 564, "opRating": 435}, {"opponent": "dunsparce", "rating": 548, "opRating": 451}, {"opponent": "steelix", "rating": 540, "opRating": 459}], "counters": [{"opponent": "talonflame", "rating": 203}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "clodsire", "rating": 396}, {"opponent": "gligar", "rating": 404}, {"opponent": "cradily", "rating": 430}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47052}, {"moveId": "LOW_KICK", "uses": 7476}, {"moveId": "POUND", "uses": 3784}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 24132}, {"moveId": "FIRE_PUNCH", "uses": 15386}, {"moveId": "FOCUS_BLAST", "uses": 10834}, {"moveId": "HYPER_BEAM", "uses": 7936}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FOCUS_BLAST"], "score": 69.9}, {"speciesId": "garcho<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 337, "matchups": [{"opponent": "houndour_shadow", "rating": 805, "opRating": 194}, {"opponent": "chandelure_shadow", "rating": 750, "opRating": 250}, {"opponent": "onix", "rating": 571, "opRating": 428}, {"opponent": "moltres_shadow", "rating": 547, "opRating": 452}, {"opponent": "exeggutor_alolan_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "clodsire", "rating": 204}, {"opponent": "jumpluff_shadow", "rating": 212}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 281}, {"opponent": "cradily", "rating": 291}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31216}, {"moveId": "MUD_SHOT", "uses": 27084}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 19553}, {"moveId": "EARTH_POWER", "uses": 16713}, {"moveId": "FIRE_BLAST", "uses": 8845}, {"moveId": "SAND_TOMB", "uses": 6996}, {"moveId": "EARTHQUAKE", "uses": 6080}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 69.7}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 334, "matchups": [{"opponent": "ninetales", "rating": 632, "opRating": 367}, {"opponent": "magcargo", "rating": 628, "opRating": 371}, {"opponent": "pidgeot", "rating": 617, "opRating": 382}, {"opponent": "castform_sunny", "rating": 588, "opRating": 411}, {"opponent": "typhlosion_shadow", "rating": 518, "opRating": 481}], "counters": [{"opponent": "gligar", "rating": 76}, {"opponent": "cradily", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "clodsire", "rating": 194}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 47213}, {"moveId": "ROCK_SMASH", "uses": 11087}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 14762}, {"moveId": "SURF", "uses": 13291}, {"moveId": "STONE_EDGE", "uses": 12916}, {"moveId": "MEGAHORN", "uses": 10269}, {"moveId": "EARTHQUAKE", "uses": 7067}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "STONE_EDGE"], "score": 69.4}, {"speciesId": "rhy<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 345, "matchups": [{"opponent": "magcargo", "rating": 647, "opRating": 352}, {"opponent": "ninetales", "rating": 597, "opRating": 402}, {"opponent": "typhlosion_shadow", "rating": 547, "opRating": 452}, {"opponent": "ninetales_shadow", "rating": 518, "opRating": 481}, {"opponent": "skeledirge", "rating": 512, "opRating": 487}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 142}, {"opponent": "jumpluff_shadow", "rating": 202}, {"opponent": "talonflame", "rating": 240}, {"opponent": "clodsire", "rating": 247}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 42609}, {"moveId": "ROCK_SMASH", "uses": 15691}], "chargedMoves": [{"moveId": "STOMP", "uses": 20319}, {"moveId": "BULLDOZE", "uses": 19320}, {"moveId": "HORN_ATTACK", "uses": 18615}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BULLDOZE", "STOMP"], "score": 69.4}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 343, "matchups": [{"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "geodude_shadow", "rating": 887, "opRating": 112}, {"opponent": "gastrodon", "rating": 751, "opRating": 248}, {"opponent": "quagsire", "rating": 748, "opRating": 251}, {"opponent": "quagsire_shadow", "rating": 721, "opRating": 278}], "counters": [{"opponent": "jumpluff_shadow", "rating": 91}, {"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 187}, {"opponent": "clodsire", "rating": 305}, {"opponent": "cradily", "rating": 336}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30610}, {"moveId": "BITE", "uses": 27690}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25707}, {"moveId": "STONE_EDGE", "uses": 15253}, {"moveId": "EARTHQUAKE", "uses": 9210}, {"moveId": "SAND_TOMB", "uses": 5352}, {"moveId": "SOLAR_BEAM", "uses": 2880}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 69.2}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 377, "matchups": [{"opponent": "whiscash", "rating": 769, "opRating": 230}, {"opponent": "<PERSON><PERSON>e", "rating": 691, "opRating": 308}, {"opponent": "quagsire", "rating": 671, "opRating": 328}, {"opponent": "quagsire_shadow", "rating": 585, "opRating": 414}, {"opponent": "swampert_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "gligar", "rating": 83}, {"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "clodsire", "rating": 211}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 20211}, {"moveId": "BULLET_SEED", "uses": 16217}, {"moveId": "MAGICAL_LEAF", "uses": 14731}, {"moveId": "RAZOR_LEAF", "uses": 7161}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42059}, {"moveId": "DAZZLING_GLEAM", "uses": 11802}, {"moveId": "PETAL_BLIZZARD", "uses": 4364}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 68.7}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 344, "matchups": [{"opponent": "marowak", "rating": 607, "opRating": 392}, {"opponent": "steelix", "rating": 590, "opRating": 409}, {"opponent": "cradily", "rating": 564}, {"opponent": "<PERSON><PERSON>e", "rating": 560, "opRating": 439}, {"opponent": "swadloon", "rating": 547, "opRating": 452}], "counters": [{"opponent": "talonflame", "rating": 55}, {"opponent": "clodsire", "rating": 132}, {"opponent": "magcargo", "rating": 136}, {"opponent": "gligar", "rating": 312}, {"opponent": "jumpluff_shadow", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 21655}, {"moveId": "SAND_ATTACK", "uses": 19661}, {"moveId": "WING_ATTACK", "uses": 16963}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 21914}, {"moveId": "NIGHT_SLASH", "uses": 19760}, {"moveId": "EARTHQUAKE", "uses": 10494}, {"moveId": "SAND_TOMB", "uses": 6054}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 68.7}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 344, "matchups": [{"opponent": "rhyhorn", "rating": 697, "opRating": 302}, {"opponent": "quagsire", "rating": 627, "opRating": 372}, {"opponent": "chansey", "rating": 604, "opRating": 395}, {"opponent": "lickitung", "rating": 587, "opRating": 412}, {"opponent": "gastrodon", "rating": 570, "opRating": 429}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 183}, {"opponent": "talonflame", "rating": 207}, {"opponent": "cradily", "rating": 222}, {"opponent": "clodsire", "rating": 247}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 16415}, {"moveId": "MUD_SHOT", "uses": 16193}, {"moveId": "TACKLE", "uses": 16140}, {"moveId": "BITE", "uses": 9564}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24992}, {"moveId": "CRUNCH", "uses": 16637}, {"moveId": "TRAILBLAZE", "uses": 16633}]}, "moveset": ["MUD_SHOT", "BODY_SLAM", "TRAILBLAZE"], "score": 68.7}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 342, "matchups": [{"opponent": "castform_sunny", "rating": 742, "opRating": 257}, {"opponent": "ninetales", "rating": 626, "opRating": 373}, {"opponent": "magcargo", "rating": 623, "opRating": 376}, {"opponent": "pidgeot", "rating": 611, "opRating": 388}, {"opponent": "typhlosion_shadow", "rating": 526, "opRating": 473}], "counters": [{"opponent": "gligar", "rating": 76}, {"opponent": "cradily", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "clodsire", "rating": 194}, {"opponent": "talonflame", "rating": 348}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 37125}, {"moveId": "SMACK_DOWN", "uses": 21175}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 14963}, {"moveId": "SUPER_POWER", "uses": 10973}, {"moveId": "BREAKING_SWIPE", "uses": 10501}, {"moveId": "SURF", "uses": 9339}, {"moveId": "EARTHQUAKE", "uses": 4812}, {"moveId": "STONE_EDGE", "uses": 4241}, {"moveId": "SKULL_BASH", "uses": 3422}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 68.7}, {"speciesId": "tauros_blaze", "speciesName": "<PERSON><PERSON> (Blaze)", "rating": 339, "matchups": [{"opponent": "litleo", "rating": 684, "opRating": 315}, {"opponent": "<PERSON><PERSON>e", "rating": 671, "opRating": 328}, {"opponent": "abomasnow", "rating": 653, "opRating": 346}, {"opponent": "abomasnow_shadow", "rating": 614, "opRating": 385}, {"opponent": "miltank", "rating": 570, "opRating": 429}], "counters": [{"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "talonflame", "rating": 144}, {"opponent": "clodsire", "rating": 192}, {"opponent": "gligar", "rating": 263}, {"opponent": "cradily", "rating": 437}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 37200}, {"moveId": "TACKLE", "uses": 16667}, {"moveId": "ZEN_HEADBUTT", "uses": 4421}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21058}, {"moveId": "TRAILBLAZE", "uses": 17798}, {"moveId": "EARTHQUAKE", "uses": 10687}, {"moveId": "IRON_HEAD", "uses": 8790}]}, "moveset": ["DOUBLE_KICK", "FLAME_CHARGE", "TRAILBLAZE"], "score": 68.5}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 367, "matchups": [{"opponent": "abomasnow", "rating": 845, "opRating": 154}, {"opponent": "abomasnow_shadow", "rating": 823, "opRating": 176}, {"opponent": "<PERSON><PERSON>e", "rating": 752, "opRating": 247}, {"opponent": "tropius", "rating": 694, "opRating": 305}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 544, "opRating": 455}], "counters": [{"opponent": "clodsire", "rating": 146}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 392}, {"opponent": "cradily", "rating": 420}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 34690}, {"moveId": "BUG_BITE", "uses": 23610}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 19776}, {"moveId": "STONE_EDGE", "uses": 11707}, {"moveId": "EARTH_POWER", "uses": 9867}, {"moveId": "IRON_HEAD", "uses": 8683}, {"moveId": "FLAMETHROWER", "uses": 5410}, {"moveId": "FIRE_BLAST", "uses": 2852}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 68.1}, {"speciesId": "blazi<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 361, "matchups": [{"opponent": "heatran", "rating": 789, "opRating": 210}, {"opponent": "houndoom_shadow", "rating": 773, "opRating": 226}, {"opponent": "abomasnow", "rating": 655, "opRating": 344}, {"opponent": "furret", "rating": 533, "opRating": 466}, {"opponent": "abomasnow_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "jumpluff_shadow", "rating": 98}, {"opponent": "clodsire", "rating": 103}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 315}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31001}, {"moveId": "COUNTER", "uses": 27299}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14718}, {"moveId": "BRAVE_BIRD", "uses": 12602}, {"moveId": "BLAZE_KICK", "uses": 10787}, {"moveId": "STONE_EDGE", "uses": 8785}, {"moveId": "FOCUS_BLAST", "uses": 8276}, {"moveId": "OVERHEAT", "uses": 3084}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BLAZE_KICK", "STONE_EDGE"], "score": 67.8}, {"speciesId": "whiscash_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 333, "matchups": [{"opponent": "ninetales_shadow", "rating": 716, "opRating": 283}, {"opponent": "magcargo", "rating": 646, "opRating": 353}, {"opponent": "skeledirge", "rating": 547, "opRating": 452}, {"opponent": "claydol", "rating": 530, "opRating": 469}, {"opponent": "typhlosion_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "cradily", "rating": 76}, {"opponent": "talonflame", "rating": 81}, {"opponent": "gligar", "rating": 95}, {"opponent": "clodsire", "rating": 399}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30418}, {"moveId": "WATER_GUN", "uses": 27882}], "chargedMoves": [{"moveId": "SCALD", "uses": 21710}, {"moveId": "MUD_BOMB", "uses": 17820}, {"moveId": "BLIZZARD", "uses": 12999}, {"moveId": "WATER_PULSE", "uses": 5694}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 67.8}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 367, "matchups": [{"opponent": "abomasnow_shadow", "rating": 851, "opRating": 148}, {"opponent": "<PERSON><PERSON>e", "rating": 788, "opRating": 211}, {"opponent": "tropius", "rating": 747, "opRating": 252}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 644, "opRating": 355}, {"opponent": "jumpluff_shadow", "rating": 536, "opRating": 463}], "counters": [{"opponent": "magcargo", "rating": 68}, {"opponent": "clodsire", "rating": 122}, {"opponent": "gligar", "rating": 125}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 368}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33611}, {"moveId": "BUG_BITE", "uses": 24689}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 19796}, {"moveId": "STONE_EDGE", "uses": 11693}, {"moveId": "EARTH_POWER", "uses": 9873}, {"moveId": "IRON_HEAD", "uses": 8675}, {"moveId": "FLAMETHROWER", "uses": 5403}, {"moveId": "FIRE_BLAST", "uses": 2842}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 67.6}, {"speciesId": "parasect", "speciesName": "Parasect", "rating": 390, "matchups": [{"opponent": "gogoat", "rating": 849, "opRating": 150}, {"opponent": "<PERSON><PERSON>e", "rating": 776, "opRating": 223}, {"opponent": "claydol", "rating": 573, "opRating": 426}, {"opponent": "swampert_shadow", "rating": 548, "opRating": 451}, {"opponent": "flygon", "rating": 512, "opRating": 487}], "counters": [{"opponent": "clodsire", "rating": 79}, {"opponent": "gligar", "rating": 80}, {"opponent": "jumpluff_shadow", "rating": 101}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 197}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28553}, {"moveId": "BUG_BITE", "uses": 20558}, {"moveId": "STRUGGLE_BUG", "uses": 9159}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25473}, {"moveId": "CROSS_POISON", "uses": 21741}, {"moveId": "SOLAR_BEAM", "uses": 11117}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "CROSS_POISON"], "score": 67.6}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 355, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 770, "opRating": 229}, {"opponent": "rilla<PERSON>m", "rating": 770, "opRating": 229}, {"opponent": "thwackey", "rating": 751, "opRating": 248}, {"opponent": "chandelure_shadow", "rating": 587, "opRating": 412}, {"opponent": "whiscash", "rating": 522, "opRating": 477}], "counters": [{"opponent": "jumpluff_shadow", "rating": 127}, {"opponent": "gligar", "rating": 137}, {"opponent": "clodsire", "rating": 170}, {"opponent": "cradily", "rating": 208}, {"opponent": "talonflame", "rating": 281}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 32581}, {"moveId": "BULLET_SEED", "uses": 25719}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20344}, {"moveId": "DRAGON_PULSE", "uses": 16951}, {"moveId": "DRACO_METEOR", "uses": 15061}, {"moveId": "SOLAR_BEAM", "uses": 5747}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 67.1}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 317, "matchups": [{"opponent": "onix", "rating": 772, "opRating": 228}, {"opponent": "steelix_shadow", "rating": 671, "opRating": 328}, {"opponent": "stunfisk", "rating": 640, "opRating": 360}, {"opponent": "steelix", "rating": 628, "opRating": 372}, {"opponent": "chansey", "rating": 616, "opRating": 384}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "cradily", "rating": 340}, {"opponent": "gligar", "rating": 438}, {"opponent": "clodsire", "rating": 447}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 22770}, {"moveId": "MUD_SHOT", "uses": 20394}, {"moveId": "METAL_CLAW", "uses": 15098}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 15322}, {"moveId": "ROCK_TOMB", "uses": 14852}, {"moveId": "SCORCHING_SANDS", "uses": 12801}, {"moveId": "BULLDOZE", "uses": 6854}, {"moveId": "RETURN", "uses": 4915}, {"moveId": "EARTHQUAKE", "uses": 3705}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "SCORCHING_SANDS"], "score": 67.1}, {"speciesId": "pignite", "speciesName": "Pignite", "rating": 346, "matchups": [{"opponent": "ferrothorn", "rating": 762, "opRating": 237}, {"opponent": "abomasnow", "rating": 746, "opRating": 253}, {"opponent": "swadloon", "rating": 708, "opRating": 291}, {"opponent": "abomasnow_shadow", "rating": 683, "opRating": 316}, {"opponent": "castform_sunny", "rating": 601, "opRating": 398}], "counters": [{"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 205}, {"opponent": "clodsire", "rating": 245}, {"opponent": "cradily", "rating": 336}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38402}, {"moveId": "TACKLE", "uses": 19898}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22884}, {"moveId": "FLAME_CHARGE", "uses": 19442}, {"moveId": "FLAMETHROWER", "uses": 8045}, {"moveId": "RETURN", "uses": 7997}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 66.9}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 357, "matchups": [{"opponent": "geodude_shadow", "rating": 796, "opRating": 203}, {"opponent": "geodude", "rating": 781, "opRating": 218}, {"opponent": "obstagoon_shadow", "rating": 766, "opRating": 233}, {"opponent": "cacturne_shadow", "rating": 766, "opRating": 233}, {"opponent": "furret", "rating": 553, "opRating": 446}], "counters": [{"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "gligar", "rating": 80}, {"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 170}, {"opponent": "cradily", "rating": 315}], "moves": {"fastMoves": [{"moveId": "FORCE_PALM", "uses": 23605}, {"moveId": "BULLET_SEED", "uses": 18108}, {"moveId": "COUNTER", "uses": 16575}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25797}, {"moveId": "GRASS_KNOT", "uses": 12378}, {"moveId": "SLUDGE_BOMB", "uses": 10267}, {"moveId": "SEED_BOMB", "uses": 9896}]}, "moveset": ["FORCE_PALM", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 66.7}, {"speciesId": "rhyperior_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 339, "matchups": [{"opponent": "heatran_shadow", "rating": 888, "opRating": 111}, {"opponent": "magcargo", "rating": 854, "opRating": 145}, {"opponent": "entei", "rating": 798, "opRating": 201}, {"opponent": "castform_sunny", "rating": 682, "opRating": 317}, {"opponent": "marowak_alolan", "rating": 544, "opRating": 455}], "counters": [{"opponent": "gligar", "rating": 64}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 181}, {"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "clodsire", "rating": 242}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 37775}, {"moveId": "SMACK_DOWN", "uses": 20525}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 14987}, {"moveId": "SUPER_POWER", "uses": 10975}, {"moveId": "BREAKING_SWIPE", "uses": 10499}, {"moveId": "SURF", "uses": 9330}, {"moveId": "EARTHQUAKE", "uses": 4835}, {"moveId": "STONE_EDGE", "uses": 4246}, {"moveId": "SKULL_BASH", "uses": 3437}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 66.7}, {"speciesId": "vibrava", "speciesName": "Vibrava", "rating": 319, "matchups": [{"opponent": "typhlosion_hisuian", "rating": 594, "opRating": 405}, {"opponent": "growl<PERSON>e_his<PERSON>an", "rating": 570, "opRating": 429}, {"opponent": "pignite", "rating": 551, "opRating": 448}, {"opponent": "combusken", "rating": 527, "opRating": 472}, {"opponent": "rapidash", "rating": 515, "opRating": 484}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "talonflame", "rating": 229}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 309}, {"opponent": "clodsire", "rating": 382}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21755}, {"moveId": "SAND_ATTACK", "uses": 19238}, {"moveId": "MUD_SHOT", "uses": 17294}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 19348}, {"moveId": "BUG_BUZZ", "uses": 13328}, {"moveId": "BULLDOZE", "uses": 10276}, {"moveId": "RETURN", "uses": 9209}, {"moveId": "SAND_TOMB", "uses": 6389}]}, "moveset": ["DRAGON_BREATH", "SAND_TOMB", "BUG_BUZZ"], "score": 66.7}, {"speciesId": "vibrava_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 324, "matchups": [{"opponent": "unfezant_shadow", "rating": 586, "opRating": 413}, {"opponent": "typhlosion_hisuian", "rating": 559, "opRating": 440}, {"opponent": "pupitar_shadow", "rating": 547, "opRating": 452}, {"opponent": "onix", "rating": 507, "opRating": 492}, {"opponent": "rapidash", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "cradily", "rating": 239}, {"opponent": "clodsire", "rating": 252}, {"opponent": "gligar", "rating": 274}, {"opponent": "talonflame", "rating": 285}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21810}, {"moveId": "SAND_ATTACK", "uses": 19216}, {"moveId": "MUD_SHOT", "uses": 17361}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22517}, {"moveId": "BUG_BUZZ", "uses": 16387}, {"moveId": "BULLDOZE", "uses": 11942}, {"moveId": "SAND_TOMB", "uses": 7443}, {"moveId": "FRUSTRATION", "uses": 40}]}, "moveset": ["DRAGON_BREATH", "SAND_TOMB", "BUG_BUZZ"], "score": 66.7}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 379, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 871, "opRating": 128}, {"opponent": "thwackey", "rating": 871, "opRating": 128}, {"opponent": "cacturne_shadow", "rating": 843, "opRating": 156}, {"opponent": "gogoat", "rating": 777, "opRating": 222}, {"opponent": "whiscash", "rating": 687, "opRating": 312}], "counters": [{"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 122}, {"opponent": "clodsire", "rating": 132}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "cradily", "rating": 295}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 33420}, {"moveId": "CONFUSION", "uses": 24880}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 24553}, {"moveId": "ENERGY_BALL", "uses": 23457}, {"moveId": "PSYBEAM", "uses": 10322}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 66.7}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 365, "matchups": [{"opponent": "wormadam_plant", "rating": 820, "opRating": 180}, {"opponent": "abomasnow", "rating": 724, "opRating": 276}, {"opponent": "abomasnow_shadow", "rating": 720, "opRating": 280}, {"opponent": "lura<PERSON>s", "rating": 668, "opRating": 332}, {"opponent": "tropius", "rating": 548, "opRating": 452}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 170}, {"opponent": "clodsire", "rating": 264}, {"opponent": "jumpluff_shadow", "rating": 297}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 21183}, {"moveId": "SNARL", "uses": 19029}, {"moveId": "THUNDER_FANG", "uses": 11731}, {"moveId": "BITE", "uses": 6324}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 11558}, {"moveId": "WILD_CHARGE", "uses": 10934}, {"moveId": "FLAMETHROWER", "uses": 10073}, {"moveId": "CRUNCH", "uses": 9453}, {"moveId": "SCORCHING_SANDS", "uses": 8805}, {"moveId": "BULLDOZE", "uses": 4710}, {"moveId": "FIRE_BLAST", "uses": 2707}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 66.5}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 365, "matchups": [{"opponent": "wormadam_plant", "rating": 833, "opRating": 166}, {"opponent": "lura<PERSON>s", "rating": 811, "opRating": 188}, {"opponent": "swadloon", "rating": 758, "opRating": 241}, {"opponent": "parasect", "rating": 710, "opRating": 289}, {"opponent": "abomasnow_shadow", "rating": 618, "opRating": 381}], "counters": [{"opponent": "clodsire", "rating": 144}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 177}, {"opponent": "gligar", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 15646}, {"moveId": "EXTRASENSORY", "uses": 3992}, {"moveId": "STEEL_WING", "uses": 3566}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3138}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3058}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2958}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2548}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2528}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2387}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2257}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2254}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2024}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1960}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1944}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1843}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1695}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1662}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1588}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1426}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27140}, {"moveId": "SACRED_FIRE", "uses": 14605}, {"moveId": "EARTHQUAKE", "uses": 7692}, {"moveId": "SOLAR_BEAM", "uses": 5848}, {"moveId": "FIRE_BLAST", "uses": 2957}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 66.5}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 340, "matchups": [{"opponent": "quagsire_shadow", "rating": 822, "opRating": 177}, {"opponent": "whiscash", "rating": 804, "opRating": 195}, {"opponent": "gastrodon", "rating": 797, "opRating": 202}, {"opponent": "quagsire", "rating": 719, "opRating": 280}, {"opponent": "swampert_shadow", "rating": 613, "opRating": 386}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 88}, {"opponent": "gligar", "rating": 232}, {"opponent": "clodsire", "rating": 237}, {"opponent": "cradily", "rating": 270}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30037}, {"moveId": "BITE", "uses": 28263}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26293}, {"moveId": "ENERGY_BALL", "uses": 19322}, {"moveId": "RETURN", "uses": 6895}, {"moveId": "SOLAR_BEAM", "uses": 5705}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 66.2}, {"speciesId": "rhydon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 335, "matchups": [{"opponent": "heatran_shadow", "rating": 889, "opRating": 110}, {"opponent": "magcargo", "rating": 856, "opRating": 143}, {"opponent": "entei", "rating": 801, "opRating": 198}, {"opponent": "castform_sunny", "rating": 797, "opRating": 202}, {"opponent": "marowak_alolan", "rating": 551, "opRating": 448}], "counters": [{"opponent": "gligar", "rating": 64}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 181}, {"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "clodsire", "rating": 230}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 48000}, {"moveId": "ROCK_SMASH", "uses": 10300}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 14715}, {"moveId": "SURF", "uses": 13297}, {"moveId": "STONE_EDGE", "uses": 12939}, {"moveId": "MEGAHORN", "uses": 10273}, {"moveId": "EARTHQUAKE", "uses": 7077}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "STONE_EDGE"], "score": 66.2}, {"speciesId": "serperior_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 357, "matchups": [{"opponent": "marowak", "rating": 684, "opRating": 315}, {"opponent": "quagsire_shadow", "rating": 600, "opRating": 400}, {"opponent": "swampert_shadow", "rating": 546, "opRating": 453}, {"opponent": "flygon", "rating": 538, "opRating": 461}, {"opponent": "claydol", "rating": 523, "opRating": 476}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 156}, {"opponent": "cradily", "rating": 354}, {"opponent": "clodsire", "rating": 355}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45701}, {"moveId": "IRON_TAIL", "uses": 12599}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 22854}, {"moveId": "AERIAL_ACE", "uses": 17540}, {"moveId": "LEAF_TORNADO", "uses": 11813}, {"moveId": "GRASS_KNOT", "uses": 6190}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 66}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 356, "matchups": [{"opponent": "abomasnow", "rating": 824, "opRating": 175}, {"opponent": "abomasnow_shadow", "rating": 804, "opRating": 195}, {"opponent": "parasect", "rating": 753, "opRating": 246}, {"opponent": "tropius", "rating": 585, "opRating": 414}, {"opponent": "castform_sunny", "rating": 566, "opRating": 433}], "counters": [{"opponent": "clodsire", "rating": 158}, {"opponent": "talonflame", "rating": 196}, {"opponent": "cradily", "rating": 208}, {"opponent": "gligar", "rating": 251}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30495}, {"moveId": "FIRE_SPIN", "uses": 27805}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 28572}, {"moveId": "EARTHQUAKE", "uses": 17429}, {"moveId": "SOLAR_BEAM", "uses": 12345}]}, "moveset": ["EMBER", "OVERHEAT", "EARTHQUAKE"], "score": 66}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 371, "matchups": [{"opponent": "abomasnow_shadow", "rating": 797, "opRating": 202}, {"opponent": "abomasnow", "rating": 797, "opRating": 202}, {"opponent": "parasect", "rating": 773, "opRating": 226}, {"opponent": "tropius", "rating": 668, "opRating": 331}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 122}, {"opponent": "cradily", "rating": 187}, {"opponent": "gligar", "rating": 202}, {"opponent": "jumpluff_shadow", "rating": 303}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 44555}, {"moveId": "BITE", "uses": 13745}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21894}, {"moveId": "FLAMETHROWER", "uses": 19746}, {"moveId": "DISARMING_VOICE", "uses": 16769}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 65.8}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 333, "matchups": [{"opponent": "gastrodon", "rating": 887, "opRating": 112}, {"opponent": "quagsire", "rating": 785, "opRating": 214}, {"opponent": "hippopotas", "rating": 785, "opRating": 214}, {"opponent": "quagsire_shadow", "rating": 748, "opRating": 251}, {"opponent": "swampert_shadow", "rating": 646, "opRating": 353}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 88}, {"opponent": "gligar", "rating": 251}, {"opponent": "clodsire", "rating": 290}, {"opponent": "cradily", "rating": 340}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30391}, {"moveId": "BITE", "uses": 27909}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25744}, {"moveId": "STONE_EDGE", "uses": 15277}, {"moveId": "EARTHQUAKE", "uses": 9200}, {"moveId": "SAND_TOMB", "uses": 5405}, {"moveId": "SOLAR_BEAM", "uses": 2881}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 65.8}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 362, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 699, "opRating": 300}, {"opponent": "gogoat", "rating": 685, "opRating": 314}, {"opponent": "serperior", "rating": 667, "opRating": 332}, {"opponent": "flygon_shadow", "rating": 534, "opRating": 465}, {"opponent": "flygon", "rating": 517, "opRating": 482}], "counters": [{"opponent": "talonflame", "rating": 96}, {"opponent": "clodsire", "rating": 117}, {"opponent": "cradily", "rating": 149}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "gligar", "rating": 309}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32554}, {"moveId": "LEAFAGE", "uses": 18124}, {"moveId": "RAZOR_LEAF", "uses": 7606}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 28103}, {"moveId": "ICY_WIND", "uses": 9527}, {"moveId": "ENERGY_BALL", "uses": 9149}, {"moveId": "OUTRAGE", "uses": 7095}, {"moveId": "BLIZZARD", "uses": 4239}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 65.6}, {"speciesId": "don<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 315, "matchups": [{"opponent": "heatran_shadow", "rating": 821, "opRating": 178}, {"opponent": "magcargo", "rating": 773, "opRating": 226}, {"opponent": "steelix", "rating": 746, "opRating": 253}, {"opponent": "steelix_shadow", "rating": 722, "opRating": 277}, {"opponent": "clodsire", "rating": 559, "opRating": 440}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 110}, {"opponent": "talonflame", "rating": 122}, {"opponent": "cradily", "rating": 156}, {"opponent": "furret", "rating": 178}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23069}, {"moveId": "COUNTER", "uses": 15793}, {"moveId": "TACKLE", "uses": 10836}, {"moveId": "CHARM", "uses": 8596}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16638}, {"moveId": "TRAILBLAZE", "uses": 15204}, {"moveId": "EARTHQUAKE", "uses": 11773}, {"moveId": "HEAVY_SLAM", "uses": 8173}, {"moveId": "PLAY_ROUGH", "uses": 6523}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "TRAILBLAZE", "BODY_SLAM"], "score": 65.6}, {"speciesId": "thwackey", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 336, "matchups": [{"opponent": "gastrodon", "rating": 871, "opRating": 128}, {"opponent": "quagsire_shadow", "rating": 808, "opRating": 191}, {"opponent": "whiscash", "rating": 786, "opRating": 213}, {"opponent": "marowak", "rating": 661, "opRating": 338}, {"opponent": "swampert_shadow", "rating": 577, "opRating": 422}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 206}, {"opponent": "clodsire", "rating": 252}, {"opponent": "cradily", "rating": 256}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 32420}, {"moveId": "SCRATCH", "uses": 25880}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 43142}, {"moveId": "ENERGY_BALL", "uses": 15158}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 65.6}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 338, "matchups": [{"opponent": "abomasnow_shadow", "rating": 755, "opRating": 244}, {"opponent": "ninetales", "rating": 682, "opRating": 317}, {"opponent": "ninetales_shadow", "rating": 651, "opRating": 348}, {"opponent": "jumpluff_shadow", "rating": 538, "opRating": 461}, {"opponent": "pidgeot", "rating": 527, "opRating": 472}], "counters": [{"opponent": "diggersby", "rating": 120}, {"opponent": "clodsire", "rating": 137}, {"opponent": "gligar", "rating": 160}, {"opponent": "cradily", "rating": 166}, {"opponent": "talonflame", "rating": 244}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 25366}, {"moveId": "SNARL", "uses": 24328}, {"moveId": "ROCK_SMASH", "uses": 8640}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 15794}, {"moveId": "FLAMETHROWER", "uses": 14446}, {"moveId": "CRUNCH", "uses": 14301}, {"moveId": "WILD_CHARGE", "uses": 13745}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "ROCK_SLIDE"], "score": 65.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 322, "matchups": [{"opponent": "heatran_shadow", "rating": 809, "opRating": 190}, {"opponent": "geodude_shadow", "rating": 785, "opRating": 214}, {"opponent": "steelix_shadow", "rating": 746, "opRating": 253}, {"opponent": "steelix", "rating": 650, "opRating": 349}, {"opponent": "clodsire", "rating": 599, "opRating": 400}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "furret", "rating": 228}, {"opponent": "gligar", "rating": 248}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22565}, {"moveId": "COUNTER", "uses": 15946}, {"moveId": "TACKLE", "uses": 11022}, {"moveId": "CHARM", "uses": 8760}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16697}, {"moveId": "TRAILBLAZE", "uses": 15162}, {"moveId": "EARTHQUAKE", "uses": 11748}, {"moveId": "HEAVY_SLAM", "uses": 8147}, {"moveId": "PLAY_ROUGH", "uses": 6546}]}, "moveset": ["MUD_SLAP", "TRAILBLAZE", "BODY_SLAM"], "score": 65.3}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 368, "matchups": [{"opponent": "swadloon", "rating": 838, "opRating": 161}, {"opponent": "wormadam_plant", "rating": 827, "opRating": 172}, {"opponent": "abomasnow", "rating": 758, "opRating": 241}, {"opponent": "abomasnow_shadow", "rating": 741, "opRating": 258}, {"opponent": "lura<PERSON>s", "rating": 687, "opRating": 312}], "counters": [{"opponent": "clodsire", "rating": 151}, {"opponent": "cradily", "rating": 170}, {"opponent": "talonflame", "rating": 177}, {"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 274}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31372}, {"moveId": "FIRE_FANG", "uses": 26928}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 15283}, {"moveId": "FLAME_CHARGE", "uses": 14789}, {"moveId": "OVERHEAT", "uses": 11459}, {"moveId": "IRON_HEAD", "uses": 7248}, {"moveId": "FLAMETHROWER", "uses": 6078}, {"moveId": "FIRE_BLAST", "uses": 3334}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 65.3}, {"speciesId": "flapple", "speciesName": "Flapple", "rating": 344, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 777, "opRating": 222}, {"opponent": "rilla<PERSON>m", "rating": 777, "opRating": 222}, {"opponent": "grotle", "rating": 576, "opRating": 423}, {"opponent": "whiscash", "rating": 534, "opRating": 465}, {"opponent": "gogoat", "rating": 525, "opRating": 474}], "counters": [{"opponent": "gligar", "rating": 137}, {"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "cradily", "rating": 211}, {"opponent": "clodsire", "rating": 213}, {"opponent": "talonflame", "rating": 225}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 31653}, {"moveId": "BULLET_SEED", "uses": 26647}], "chargedMoves": [{"moveId": "FLY", "uses": 22365}, {"moveId": "SEED_BOMB", "uses": 15633}, {"moveId": "OUTRAGE", "uses": 15260}, {"moveId": "DRAGON_PULSE", "uses": 5016}]}, "moveset": ["DRAGON_BREATH", "FLY", "SEED_BOMB"], "score": 65.3}, {"speciesId": "graveler", "speciesName": "<PERSON><PERSON>", "rating": 315, "matchups": [{"opponent": "magcargo", "rating": 650, "opRating": 349}, {"opponent": "ninetales", "rating": 637, "opRating": 362}, {"opponent": "pidgeot", "rating": 586, "opRating": 413}, {"opponent": "typhlosion_shadow", "rating": 560, "opRating": 439}, {"opponent": "ninetales_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "jumpluff_shadow", "rating": 91}, {"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 211}, {"opponent": "clodsire", "rating": 348}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23294}, {"moveId": "MUD_SHOT", "uses": 18567}, {"moveId": "ROCK_THROW", "uses": 16437}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 14174}, {"moveId": "ROCK_SLIDE", "uses": 13327}, {"moveId": "ROCK_BLAST", "uses": 12730}, {"moveId": "DIG", "uses": 12106}, {"moveId": "RETURN", "uses": 5911}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "DIG"], "score": 65.3}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 367, "matchups": [{"opponent": "leavanny", "rating": 915, "opRating": 84}, {"opponent": "abomasnow", "rating": 898, "opRating": 101}, {"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "wormadam_plant", "rating": 703, "opRating": 296}, {"opponent": "chansey", "rating": 661, "opRating": 338}], "counters": [{"opponent": "clodsire", "rating": 151}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 194}, {"opponent": "gligar", "rating": 213}, {"opponent": "jumpluff_shadow", "rating": 297}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 29358}, {"moveId": "FIRE_FANG", "uses": 28942}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 25728}, {"moveId": "FLAMETHROWER", "uses": 16203}, {"moveId": "CRUNCH", "uses": 12027}, {"moveId": "FIRE_BLAST", "uses": 4262}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 65.3}, {"speciesId": "pignite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 339, "matchups": [{"opponent": "wormadam_plant", "rating": 879, "opRating": 120}, {"opponent": "swadloon", "rating": 800, "opRating": 199}, {"opponent": "parasect", "rating": 740, "opRating": 259}, {"opponent": "abomasnow_shadow", "rating": 737, "opRating": 262}, {"opponent": "ferrothorn", "rating": 718, "opRating": 281}], "counters": [{"opponent": "talonflame", "rating": 148}, {"opponent": "gligar", "rating": 160}, {"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "clodsire", "rating": 290}, {"opponent": "cradily", "rating": 392}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38633}, {"moveId": "TACKLE", "uses": 19667}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 26314}, {"moveId": "FLAME_CHARGE", "uses": 22519}, {"moveId": "FLAMETHROWER", "uses": 9442}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 65.3}, {"speciesId": "vigoroth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 323, "matchups": [{"opponent": "snorlax", "rating": 762, "opRating": 237}, {"opponent": "lickitung", "rating": 693, "opRating": 306}, {"opponent": "oinkologne_female", "rating": 596, "opRating": 403}, {"opponent": "lickilicky", "rating": 531, "opRating": 468}, {"opponent": "noctowl", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 133}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 228}, {"opponent": "cradily", "rating": 496}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37733}, {"moveId": "SCRATCH", "uses": 20567}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 22571}, {"moveId": "ROCK_SLIDE", "uses": 14009}, {"moveId": "BRICK_BREAK", "uses": 13681}, {"moveId": "BULLDOZE", "uses": 7968}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 65.3}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 342, "matchups": [{"opponent": "grotle_shadow", "rating": 706, "opRating": 293}, {"opponent": "gogoat", "rating": 663, "opRating": 336}, {"opponent": "whiscash", "rating": 581, "opRating": 418}, {"opponent": "quagsire", "rating": 576, "opRating": 423}, {"opponent": "quagsire_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "talonflame", "rating": 122}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 263}, {"opponent": "clodsire", "rating": 331}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33690}, {"moveId": "FEINT_ATTACK", "uses": 24610}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 18536}, {"moveId": "FOUL_PLAY", "uses": 16580}, {"moveId": "SLUDGE_BOMB", "uses": 16336}, {"moveId": "RETURN", "uses": 6822}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 65.1}, {"speciesId": "whimsicott", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 358, "matchups": [{"opponent": "whiscash", "rating": 771, "opRating": 228}, {"opponent": "flygon", "rating": 707, "opRating": 292}, {"opponent": "gastrodon", "rating": 694, "opRating": 305}, {"opponent": "flygon_shadow", "rating": 661, "opRating": 338}, {"opponent": "quagsire_shadow", "rating": 639, "opRating": 360}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 97}, {"opponent": "clodsire", "rating": 177}, {"opponent": "gligar", "rating": 278}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 31906}, {"moveId": "CHARM", "uses": 14618}, {"moveId": "RAZOR_LEAF", "uses": 11769}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 17009}, {"moveId": "MOONBLAST", "uses": 15106}, {"moveId": "SEED_BOMB", "uses": 13570}, {"moveId": "HURRICANE", "uses": 12577}]}, "moveset": ["FAIRY_WIND", "SEED_BOMB", "MOONBLAST"], "score": 64.9}, {"speciesId": "drilbur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 326, "matchups": [{"opponent": "salazzle", "rating": 880, "opRating": 119}, {"opponent": "heatran_shadow", "rating": 788, "opRating": 211}, {"opponent": "heatran", "rating": 760, "opRating": 239}, {"opponent": "magcargo", "rating": 725, "opRating": 274}, {"opponent": "typhlosion_shadow", "rating": 612, "opRating": 387}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 170}, {"opponent": "cradily", "rating": 208}, {"opponent": "clodsire", "rating": 254}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45683}, {"moveId": "SCRATCH", "uses": 12617}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27212}, {"moveId": "ROCK_TOMB", "uses": 24120}, {"moveId": "DIG", "uses": 6835}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_TOMB"], "score": 64.6}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 360, "matchups": [{"opponent": "wormadam_plant", "rating": 807, "opRating": 192}, {"opponent": "abomasnow", "rating": 796, "opRating": 203}, {"opponent": "abomasnow_shadow", "rating": 775, "opRating": 225}, {"opponent": "ferrothorn", "rating": 764, "opRating": 235}, {"opponent": "swadloon", "rating": 671, "opRating": 328}], "counters": [{"opponent": "clodsire", "rating": 134}, {"opponent": "cradily", "rating": 145}, {"opponent": "talonflame", "rating": 148}, {"opponent": "gligar", "rating": 263}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30952}, {"moveId": "FIRE_FANG", "uses": 27348}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 15275}, {"moveId": "FLAME_CHARGE", "uses": 14834}, {"moveId": "OVERHEAT", "uses": 11445}, {"moveId": "IRON_HEAD", "uses": 7286}, {"moveId": "FLAMETHROWER", "uses": 6095}, {"moveId": "FIRE_BLAST", "uses": 3333}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 64.6}, {"speciesId": "excadrill_shadow", "speciesName": "Excadrill (Shadow)", "rating": 321, "matchups": [{"opponent": "geodude_shadow", "rating": 850, "opRating": 150}, {"opponent": "geodude", "rating": 839, "opRating": 160}, {"opponent": "onix", "rating": 732, "opRating": 267}, {"opponent": "steelix_shadow", "rating": 721, "opRating": 278}, {"opponent": "magcargo", "rating": 539, "opRating": 460}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 76}, {"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 266}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23157}, {"moveId": "MUD_SHOT", "uses": 18611}, {"moveId": "METAL_CLAW", "uses": 16526}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20733}, {"moveId": "ROCK_SLIDE", "uses": 13666}, {"moveId": "IRON_HEAD", "uses": 11624}, {"moveId": "SCORCHING_SANDS", "uses": 7850}, {"moveId": "EARTHQUAKE", "uses": 4424}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_SLIDE"], "score": 64.6}, {"speciesId": "hippopotas_shadow", "speciesName": "Hip<PERSON><PERSON><PERSON> (Shadow)", "rating": 298, "matchups": [{"opponent": "onix", "rating": 769, "opRating": 230}, {"opponent": "steelix_shadow", "rating": 631, "opRating": 368}, {"opponent": "steelix", "rating": 618, "opRating": 381}, {"opponent": "stunfisk", "rating": 615, "opRating": 384}, {"opponent": "chansey", "rating": 598, "opRating": 401}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "cradily", "rating": 364}, {"opponent": "clodsire", "rating": 447}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 28896}, {"moveId": "TACKLE", "uses": 16670}, {"moveId": "BITE", "uses": 12682}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 23058}, {"moveId": "BODY_SLAM", "uses": 19174}, {"moveId": "DIG", "uses": 16021}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 64.6}, {"speciesId": "pumpkaboo_super", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Super)", "rating": 342, "matchups": [{"opponent": "stunfisk", "rating": 716, "opRating": 283}, {"opponent": "munchlax", "rating": 702, "opRating": 297}, {"opponent": "gastrodon", "rating": 606, "opRating": 393}, {"opponent": "marowak", "rating": 592, "opRating": 407}, {"opponent": "claydol", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 264}, {"opponent": "cradily", "rating": 291}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 37870}, {"moveId": "RAZOR_LEAF", "uses": 20430}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23651}, {"moveId": "FOUL_PLAY", "uses": 23228}, {"moveId": "SHADOW_SNEAK", "uses": 11395}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 64.6}, {"speciesId": "zygarde_10", "speciesName": "Zygarde (10% Forme)", "rating": 315, "matchups": [{"opponent": "gabite_shadow", "rating": 821, "opRating": 178}, {"opponent": "regigigas_shadow", "rating": 678, "opRating": 321}, {"opponent": "typhlosion_hisuian", "rating": 596, "opRating": 403}, {"opponent": "gabite", "rating": 530, "opRating": 469}, {"opponent": "pignite", "rating": 520, "opRating": 479}], "counters": [{"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "talonflame", "rating": 244}, {"opponent": "clodsire", "rating": 262}, {"opponent": "gligar", "rating": 270}, {"opponent": "cradily", "rating": 277}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 39511}, {"moveId": "BITE", "uses": 13801}, {"moveId": "ZEN_HEADBUTT", "uses": 4998}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 15914}, {"moveId": "OUTRAGE", "uses": 15858}, {"moveId": "EARTHQUAKE", "uses": 10761}, {"moveId": "BULLDOZE", "uses": 10102}, {"moveId": "HYPER_BEAM", "uses": 5781}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "EARTHQUAKE"], "score": 64.6}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 335, "matchups": [{"opponent": "heatran_shadow", "rating": 789, "opRating": 210}, {"opponent": "houndoom_shadow", "rating": 764, "opRating": 235}, {"opponent": "abomasnow_shadow", "rating": 689, "opRating": 310}, {"opponent": "abomasnow", "rating": 655, "opRating": 344}, {"opponent": "swadloon", "rating": 537, "opRating": 462}], "counters": [{"opponent": "clodsire", "rating": 86}, {"opponent": "gligar", "rating": 106}, {"opponent": "jumpluff_shadow", "rating": 114}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 364}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30613}, {"moveId": "COUNTER", "uses": 27687}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14742}, {"moveId": "BRAVE_BIRD", "uses": 12605}, {"moveId": "BLAZE_KICK", "uses": 10819}, {"moveId": "STONE_EDGE", "uses": 8794}, {"moveId": "FOCUS_BLAST", "uses": 8273}, {"moveId": "OVERHEAT", "uses": 3077}]}, "moveset": ["COUNTER", "BLAZE_KICK", "STONE_EDGE"], "score": 64.4}, {"speciesId": "dolliv", "speciesName": "Doll<PERSON>", "rating": 322, "matchups": [{"opponent": "whiscash", "rating": 765, "opRating": 234}, {"opponent": "gastrodon", "rating": 680, "opRating": 319}, {"opponent": "quagsire_shadow", "rating": 611, "opRating": 388}, {"opponent": "swampert_shadow", "rating": 569, "opRating": 430}, {"opponent": "claydol", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 74}, {"opponent": "clodsire", "rating": 310}, {"opponent": "cradily", "rating": 312}, {"opponent": "gligar", "rating": 354}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 24022}, {"moveId": "TACKLE", "uses": 21611}, {"moveId": "RAZOR_LEAF", "uses": 12695}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 26397}, {"moveId": "EARTH_POWER", "uses": 15992}, {"moveId": "SEED_BOMB", "uses": 8448}, {"moveId": "ENERGY_BALL", "uses": 7521}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 64.4}, {"speciesId": "cacturne_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 339, "matchups": [{"opponent": "golurk_shadow", "rating": 768, "opRating": 231}, {"opponent": "oranguru", "rating": 652, "opRating": 347}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 553, "opRating": 446}, {"opponent": "chansey", "rating": 537, "opRating": 462}, {"opponent": "stunfisk", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 152}, {"opponent": "clodsire", "rating": 170}, {"opponent": "cradily", "rating": 197}, {"opponent": "talonflame", "rating": 259}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 25920}, {"moveId": "SAND_ATTACK", "uses": 16745}, {"moveId": "POISON_JAB", "uses": 15619}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 18860}, {"moveId": "TRAILBLAZE", "uses": 14410}, {"moveId": "DARK_PULSE", "uses": 13178}, {"moveId": "PAYBACK", "uses": 5906}, {"moveId": "GRASS_KNOT", "uses": 5773}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DYNAMIC_PUNCH"], "score": 64.2}, {"speciesId": "girafarig", "speciesName": "Girafarig", "rating": 291, "matchups": [{"opponent": "clodsire", "rating": 626, "opRating": 373}, {"opponent": "trevenant", "rating": 569, "opRating": 430}, {"opponent": "vigoroth", "rating": 550, "opRating": 450}, {"opponent": "thwackey", "rating": 550, "opRating": 450}, {"opponent": "sandslash", "rating": 523, "opRating": 476}], "counters": [{"opponent": "magcargo", "rating": 145}, {"opponent": "talonflame", "rating": 170}, {"opponent": "gligar", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 22182}, {"moveId": "CONFUSION", "uses": 21297}, {"moveId": "TACKLE", "uses": 14803}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 22369}, {"moveId": "TRAILBLAZE", "uses": 14438}, {"moveId": "RETURN", "uses": 8280}, {"moveId": "THUNDERBOLT", "uses": 6294}, {"moveId": "PSYCHIC", "uses": 4290}, {"moveId": "MIRROR_COAT", "uses": 2594}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 64.2}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 323, "matchups": [{"opponent": "heatran_shadow", "rating": 884, "opRating": 115}, {"opponent": "heatran", "rating": 880, "opRating": 119}, {"opponent": "magcargo", "rating": 858, "opRating": 141}, {"opponent": "castform_sunny", "rating": 683, "opRating": 316}, {"opponent": "marowak_alolan", "rating": 555, "opRating": 444}], "counters": [{"opponent": "gligar", "rating": 64}, {"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 181}, {"opponent": "clodsire", "rating": 230}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23559}, {"moveId": "MUD_SHOT", "uses": 18755}, {"moveId": "ROCK_THROW", "uses": 16046}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17541}, {"moveId": "ROCK_BLAST", "uses": 15789}, {"moveId": "EARTHQUAKE", "uses": 13274}, {"moveId": "ANCIENT_POWER", "uses": 11582}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "ROCK_BLAST"], "score": 64.2}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 320, "matchups": [{"opponent": "onix", "rating": 788, "opRating": 211}, {"opponent": "steelix", "rating": 661, "opRating": 338}, {"opponent": "fletchinder", "rating": 626, "opRating": 373}, {"opponent": "litleo", "rating": 559, "opRating": 440}, {"opponent": "marowak_alolan", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 114}, {"opponent": "gligar", "rating": 213}, {"opponent": "jumpluff_shadow", "rating": 241}, {"opponent": "cradily", "rating": 364}, {"opponent": "clodsire", "rating": 451}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 15899}, {"moveId": "SAND_ATTACK", "uses": 15774}, {"moveId": "FIRE_FANG", "uses": 11711}, {"moveId": "THUNDER_FANG", "uses": 9214}, {"moveId": "BITE", "uses": 5730}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 18195}, {"moveId": "SCORCHING_SANDS", "uses": 13805}, {"moveId": "BODY_SLAM", "uses": 12078}, {"moveId": "EARTH_POWER", "uses": 5381}, {"moveId": "STONE_EDGE", "uses": 4943}, {"moveId": "EARTHQUAKE", "uses": 4023}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 64.2}, {"speciesId": "vigoroth", "speciesName": "Vigoroth", "rating": 340, "matchups": [{"opponent": "raticate_alolan", "rating": 655, "opRating": 344}, {"opponent": "miltank", "rating": 589, "opRating": 410}, {"opponent": "lickitung", "rating": 582, "opRating": 417}, {"opponent": "pidgeotto", "rating": 579, "opRating": 420}, {"opponent": "chansey", "rating": 555, "opRating": 444}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 152}, {"opponent": "clodsire", "rating": 209}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 36644}, {"moveId": "SCRATCH", "uses": 21656}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 20274}, {"moveId": "ROCK_SLIDE", "uses": 12873}, {"moveId": "BRICK_BREAK", "uses": 12515}, {"moveId": "BULLDOZE", "uses": 7345}, {"moveId": "RETURN", "uses": 5370}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 64.2}, {"speciesId": "da<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 358, "matchups": [{"opponent": "swadloon", "rating": 808, "opRating": 191}, {"opponent": "wormadam_plant", "rating": 796, "opRating": 203}, {"opponent": "abomasnow", "rating": 742, "opRating": 257}, {"opponent": "abomasnow_shadow", "rating": 726, "opRating": 273}, {"opponent": "<PERSON><PERSON>e", "rating": 630, "opRating": 369}], "counters": [{"opponent": "clodsire", "rating": 151}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 187}, {"opponent": "cradily", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 297}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 36384}, {"moveId": "TACKLE", "uses": 21916}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 42327}, {"moveId": "FLAME_CHARGE", "uses": 15854}, {"moveId": "FRUSTRATION", "uses": 12}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 64}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 346, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 746, "opRating": 253}, {"opponent": "swadloon", "rating": 707, "opRating": 292}, {"opponent": "lura<PERSON>s", "rating": 695, "opRating": 304}, {"opponent": "wormadam_sandy", "rating": 675, "opRating": 324}, {"opponent": "tropius", "rating": 585, "opRating": 414}], "counters": [{"opponent": "clodsire", "rating": 79}, {"opponent": "cradily", "rating": 100}, {"opponent": "talonflame", "rating": 170}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "gligar", "rating": 393}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 52550}, {"moveId": "POUND", "uses": 5750}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 36642}, {"moveId": "AERIAL_ACE", "uses": 13904}, {"moveId": "HURRICANE", "uses": 7869}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 64}, {"speciesId": "quilladin", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 327, "matchups": [{"opponent": "whiscash", "rating": 773, "opRating": 226}, {"opponent": "marowak", "rating": 707, "opRating": 292}, {"opponent": "claydol", "rating": 665, "opRating": 334}, {"opponent": "quagsire_shadow", "rating": 657, "opRating": 342}, {"opponent": "swampert_shadow", "rating": 576, "opRating": 423}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "cradily", "rating": 211}, {"opponent": "gligar", "rating": 255}, {"opponent": "clodsire", "rating": 281}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45951}, {"moveId": "LOW_KICK", "uses": 12349}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28487}, {"moveId": "ENERGY_BALL", "uses": 21022}, {"moveId": "GYRO_BALL", "uses": 8796}]}, "moveset": ["VINE_WHIP", "BODY_SLAM", "ENERGY_BALL"], "score": 64}, {"speciesId": "centiskorch", "speciesName": "Centiskorch", "rating": 372, "matchups": [{"opponent": "abomasnow_shadow", "rating": 768, "opRating": 231}, {"opponent": "abomasnow", "rating": 761, "opRating": 238}, {"opponent": "parasect", "rating": 731, "opRating": 268}, {"opponent": "swadloon", "rating": 687, "opRating": 312}, {"opponent": "piloswine", "rating": 573, "opRating": 426}], "counters": [{"opponent": "clodsire", "rating": 103}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 149}, {"opponent": "gligar", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 209}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30685}, {"moveId": "BUG_BITE", "uses": 27615}], "chargedMoves": [{"moveId": "LUNGE", "uses": 20680}, {"moveId": "CRUNCH", "uses": 18756}, {"moveId": "BUG_BUZZ", "uses": 13431}, {"moveId": "HEAT_WAVE", "uses": 5415}]}, "moveset": ["EMBER", "LUNGE", "CRUNCH"], "score": 63.7}, {"speciesId": "delcatty", "speciesName": "Delcat<PERSON>", "rating": 322, "matchups": [{"opponent": "cacturne_shadow", "rating": 771, "opRating": 228}, {"opponent": "garcho<PERSON>", "rating": 689, "opRating": 310}, {"opponent": "garcho<PERSON>_shadow", "rating": 683, "opRating": 316}, {"opponent": "vibrava", "rating": 683, "opRating": 316}, {"opponent": "drampa", "rating": 539, "opRating": 460}], "counters": [{"opponent": "talonflame", "rating": 129}, {"opponent": "clodsire", "rating": 132}, {"opponent": "jumpluff_shadow", "rating": 235}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 309}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 29065}, {"moveId": "CHARM", "uses": 21530}, {"moveId": "ZEN_HEADBUTT", "uses": 7654}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28972}, {"moveId": "DISARMING_VOICE", "uses": 23299}, {"moveId": "PLAY_ROUGH", "uses": 6081}]}, "moveset": ["CHARM", "WILD_CHARGE", "DISARMING_VOICE"], "score": 63.7}, {"speciesId": "kartana", "speciesName": "Kartana", "rating": 319, "matchups": [{"opponent": "swampert_shadow", "rating": 873, "opRating": 126}, {"opponent": "geodude_shadow", "rating": 848, "opRating": 151}, {"opponent": "quagsire_shadow", "rating": 841, "opRating": 158}, {"opponent": "gastrodon", "rating": 721, "opRating": 278}, {"opponent": "quagsire", "rating": 677, "opRating": 322}], "counters": [{"opponent": "jumpluff_shadow", "rating": 91}, {"opponent": "talonflame", "rating": 92}, {"opponent": "clodsire", "rating": 192}, {"opponent": "gligar", "rating": 206}, {"opponent": "cradily", "rating": 368}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 35573}, {"moveId": "RAZOR_LEAF", "uses": 22727}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 24106}, {"moveId": "NIGHT_SLASH", "uses": 13219}, {"moveId": "AERIAL_ACE", "uses": 10736}, {"moveId": "X_SCISSOR", "uses": 10281}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "NIGHT_SLASH"], "score": 63.7}, {"speciesId": "marshtomp", "speciesName": "Marshtom<PERSON>", "rating": 306, "matchups": [{"opponent": "castform_sunny", "rating": 742, "opRating": 257}, {"opponent": "magcargo", "rating": 717, "opRating": 282}, {"opponent": "ninetales_shadow", "rating": 700, "opRating": 300}, {"opponent": "skeledirge", "rating": 539, "opRating": 460}, {"opponent": "typhlosion_shadow", "rating": 525, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "cradily", "rating": 65}, {"opponent": "talonflame", "rating": 70}, {"opponent": "gligar", "rating": 148}, {"opponent": "clodsire", "rating": 350}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30400}, {"moveId": "WATER_GUN", "uses": 27900}], "chargedMoves": [{"moveId": "SURF", "uses": 21260}, {"moveId": "SLUDGE", "uses": 16247}, {"moveId": "MUD_BOMB", "uses": 15889}, {"moveId": "RETURN", "uses": 4925}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 63.7}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 338, "matchups": [{"opponent": "thwackey", "rating": 633, "opRating": 366}, {"opponent": "grotle", "rating": 614, "opRating": 385}, {"opponent": "whiscash", "rating": 568, "opRating": 431}, {"opponent": "gogoat", "rating": 568, "opRating": 431}, {"opponent": "stunfisk", "rating": 526, "opRating": 473}], "counters": [{"opponent": "gligar", "rating": 152}, {"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "cradily", "rating": 201}, {"opponent": "clodsire", "rating": 204}, {"opponent": "talonflame", "rating": 244}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33138}, {"moveId": "BULLET_SEED", "uses": 25162}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20150}, {"moveId": "DRAGON_PULSE", "uses": 16843}, {"moveId": "DRACO_METEOR", "uses": 14963}, {"moveId": "SOLAR_BEAM", "uses": 6200}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 63.5}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 323, "matchups": [{"opponent": "whiscash", "rating": 782, "opRating": 217}, {"opponent": "marowak", "rating": 717, "opRating": 282}, {"opponent": "quagsire", "rating": 706, "opRating": 293}, {"opponent": "quagsire_shadow", "rating": 667, "opRating": 332}, {"opponent": "claydol", "rating": 580, "opRating": 419}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 118}, {"opponent": "clodsire", "rating": 293}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 27060}, {"moveId": "MAGICAL_LEAF", "uses": 21049}, {"moveId": "RAZOR_LEAF", "uses": 10223}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 31141}, {"moveId": "EARTHQUAKE", "uses": 9804}, {"moveId": "RETURN", "uses": 9532}, {"moveId": "PETAL_BLIZZARD", "uses": 4338}, {"moveId": "SOLAR_BEAM", "uses": 3546}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 63.5}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 266, "matchups": [{"opponent": "noctowl", "rating": 649, "opRating": 350}, {"opponent": "clodsire", "rating": 629, "opRating": 370}, {"opponent": "gligar", "rating": 620}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 542, "opRating": 457}, {"opponent": "flygon_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "magcargo", "rating": 94}, {"opponent": "talonflame", "rating": 137}, {"opponent": "cradily", "rating": 149}, {"opponent": "furret", "rating": 153}, {"opponent": "jumpluff_shadow", "rating": 356}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 35392}, {"moveId": "ICE_SHARD", "uses": 22908}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 20902}, {"moveId": "ICICLE_SPEAR", "uses": 17524}, {"moveId": "STONE_EDGE", "uses": 7431}, {"moveId": "HIGH_HORSEPOWER", "uses": 6771}, {"moveId": "BULLDOZE", "uses": 5584}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 63.5}, {"speciesId": "raticate_alolan", "speciesName": "Raticate (Alolan)", "rating": 334, "matchups": [{"opponent": "oranguru", "rating": 673, "opRating": 326}, {"opponent": "chansey", "rating": 610, "opRating": 390}, {"opponent": "lickitung", "rating": 570, "opRating": 430}, {"opponent": "furret", "rating": 550, "opRating": 450}, {"opponent": "lickilicky", "rating": 533, "opRating": 466}], "counters": [{"opponent": "clodsire", "rating": 137}, {"opponent": "gligar", "rating": 141}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 232}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 36134}, {"moveId": "BITE", "uses": 22166}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28626}, {"moveId": "HYPER_FANG", "uses": 18494}, {"moveId": "RETURN", "uses": 6390}, {"moveId": "HYPER_BEAM", "uses": 4822}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "RETURN"], "score": 63.5}, {"speciesId": "rilla<PERSON>m", "speciesName": "R<PERSON>boom", "rating": 327, "matchups": [{"opponent": "rhyperior_shadow", "rating": 895, "opRating": 104}, {"opponent": "rhyperior", "rating": 875, "opRating": 124}, {"opponent": "quagsire_shadow", "rating": 786, "opRating": 213}, {"opponent": "gastrodon", "rating": 744, "opRating": 255}, {"opponent": "swampert_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 187}, {"opponent": "cradily", "rating": 232}, {"opponent": "clodsire", "rating": 271}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 32647}, {"moveId": "SCRATCH", "uses": 25653}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28421}, {"moveId": "EARTH_POWER", "uses": 19727}, {"moveId": "ENERGY_BALL", "uses": 10156}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "EARTH_POWER"], "score": 63.5}, {"speciesId": "castform", "speciesName": "Castform", "rating": 316, "matchups": [{"opponent": "whiscash", "rating": 684, "opRating": 315}, {"opponent": "quagsire", "rating": 633, "opRating": 366}, {"opponent": "quagsire_shadow", "rating": 602, "opRating": 397}, {"opponent": "gastrodon", "rating": 558, "opRating": 441}, {"opponent": "marowak_alolan", "rating": 527, "opRating": 472}], "counters": [{"opponent": "talonflame", "rating": 96}, {"opponent": "cradily", "rating": 215}, {"opponent": "clodsire", "rating": 245}, {"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "gligar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 33613}, {"moveId": "TACKLE", "uses": 24687}], "chargedMoves": [{"moveId": "WEATHER_BALL_NORMAL", "uses": 20789}, {"moveId": "WEATHER_BALL_ROCK", "uses": 20420}, {"moveId": "ENERGY_BALL", "uses": 9342}, {"moveId": "HURRICANE", "uses": 7663}]}, "moveset": ["HEX", "WEATHER_BALL_ROCK", "ENERGY_BALL"], "score": 63.3}, {"speciesId": "graveler_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 328, "matchups": [{"opponent": "heatran", "rating": 878, "opRating": 121}, {"opponent": "heatran_shadow", "rating": 860, "opRating": 139}, {"opponent": "typhlosion_shadow", "rating": 804, "opRating": 195}, {"opponent": "castform_sunny", "rating": 686, "opRating": 313}, {"opponent": "magcargo", "rating": 586, "opRating": 413}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 76}, {"opponent": "cradily", "rating": 173}, {"opponent": "talonflame", "rating": 181}, {"opponent": "clodsire", "rating": 230}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23406}, {"moveId": "MUD_SHOT", "uses": 18585}, {"moveId": "ROCK_THROW", "uses": 16333}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15624}, {"moveId": "ROCK_SLIDE", "uses": 14805}, {"moveId": "ROCK_BLAST", "uses": 14066}, {"moveId": "DIG", "uses": 13663}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "DIG"], "score": 63.3}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 310, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 727, "opRating": 272}, {"opponent": "oranguru", "rating": 589, "opRating": 410}, {"opponent": "claydol", "rating": 567, "opRating": 432}, {"opponent": "lickitung", "rating": 563, "opRating": 436}, {"opponent": "marowak_alolan", "rating": 522, "opRating": 477}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "cradily", "rating": 229}, {"opponent": "gligar", "rating": 232}, {"opponent": "clodsire", "rating": 372}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 24782}, {"moveId": "SAND_ATTACK", "uses": 20101}, {"moveId": "BITE", "uses": 9099}, {"moveId": "TAKE_DOWN", "uses": 4307}], "chargedMoves": [{"moveId": "SURF", "uses": 25585}, {"moveId": "DARK_PULSE", "uses": 16937}, {"moveId": "GRASS_KNOT", "uses": 15786}]}, "moveset": ["SUCKER_PUNCH", "SURF", "GRASS_KNOT"], "score": 63}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 336, "matchups": [{"opponent": "parasect", "rating": 741, "opRating": 258}, {"opponent": "bellossom_shadow", "rating": 705, "opRating": 294}, {"opponent": "noctowl", "rating": 629, "opRating": 370}, {"opponent": "jumpluff_shadow", "rating": 615, "opRating": 384}, {"opponent": "fletchinder", "rating": 600, "opRating": 399}], "counters": [{"opponent": "magcargo", "rating": 64}, {"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 161}, {"opponent": "cradily", "rating": 256}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 26718}, {"moveId": "POISON_JAB", "uses": 22851}, {"moveId": "BITE", "uses": 8723}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 15738}, {"moveId": "EARTH_POWER", "uses": 15549}, {"moveId": "STONE_EDGE", "uses": 12816}, {"moveId": "SLUDGE_WAVE", "uses": 8493}, {"moveId": "EARTHQUAKE", "uses": 5672}]}, "moveset": ["POISON_STING", "STONE_EDGE", "POISON_FANG"], "score": 63}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 352, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 725, "opRating": 274}, {"opponent": "lura<PERSON>s", "rating": 683, "opRating": 316}, {"opponent": "bellossom", "rating": 618, "opRating": 381}, {"opponent": "wigglytuff", "rating": 591, "opRating": 408}, {"opponent": "swampert_shadow", "rating": 553, "opRating": 446}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "clodsire", "rating": 223}, {"opponent": "cradily", "rating": 326}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 29878}, {"moveId": "MAGICAL_LEAF", "uses": 19926}, {"moveId": "RAZOR_LEAF", "uses": 8514}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28276}, {"moveId": "SLUDGE_BOMB", "uses": 13698}, {"moveId": "LEAF_TORNADO", "uses": 5379}, {"moveId": "RETURN", "uses": 5317}, {"moveId": "ACID_SPRAY", "uses": 3093}, {"moveId": "SOLAR_BEAM", "uses": 2388}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 62.8}, {"speciesId": "darum<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 350, "matchups": [{"opponent": "wormadam_plant", "rating": 828, "opRating": 171}, {"opponent": "abomasnow", "rating": 777, "opRating": 222}, {"opponent": "abomasnow_shadow", "rating": 754, "opRating": 245}, {"opponent": "swadloon", "rating": 716, "opRating": 283}, {"opponent": "parasect", "rating": 716, "opRating": 283}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 134}, {"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 232}, {"opponent": "jumpluff_shadow", "rating": 411}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 36537}, {"moveId": "TACKLE", "uses": 21763}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 33336}, {"moveId": "FLAME_CHARGE", "uses": 12551}, {"moveId": "RETURN", "uses": 12440}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 62.6}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 319, "matchups": [{"opponent": "salazzle", "rating": 794, "opRating": 205}, {"opponent": "heatran", "rating": 620, "opRating": 379}, {"opponent": "heatran_shadow", "rating": 594, "opRating": 405}, {"opponent": "rhy<PERSON>_shadow", "rating": 542, "opRating": 457}, {"opponent": "marowak_alolan", "rating": 529, "opRating": 470}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 77}, {"opponent": "cradily", "rating": 222}, {"opponent": "gligar", "rating": 225}, {"opponent": "clodsire", "rating": 367}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45918}, {"moveId": "LOW_KICK", "uses": 12382}], "chargedMoves": [{"moveId": "STOMP", "uses": 13695}, {"moveId": "CRUNCH", "uses": 13095}, {"moveId": "BRICK_BREAK", "uses": 11071}, {"moveId": "OUTRAGE", "uses": 8710}, {"moveId": "EARTHQUAKE", "uses": 6786}, {"moveId": "POWER_UP_PUNCH", "uses": 4844}]}, "moveset": ["MUD_SLAP", "STOMP", "CRUNCH"], "score": 62.6}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 348, "matchups": [{"opponent": "wormadam_plant", "rating": 847, "opRating": 152}, {"opponent": "ferrothorn", "rating": 809, "opRating": 190}, {"opponent": "swadloon", "rating": 762, "opRating": 237}, {"opponent": "lura<PERSON>s", "rating": 720, "opRating": 279}, {"opponent": "abomasnow_shadow", "rating": 542, "opRating": 457}], "counters": [{"opponent": "clodsire", "rating": 144}, {"opponent": "talonflame", "rating": 151}, {"opponent": "cradily", "rating": 190}, {"opponent": "gligar", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 294}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31337}, {"moveId": "WING_ATTACK", "uses": 26963}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18488}, {"moveId": "ANCIENT_POWER", "uses": 16541}, {"moveId": "OVERHEAT", "uses": 15903}, {"moveId": "FIRE_BLAST", "uses": 4685}, {"moveId": "HEAT_WAVE", "uses": 2690}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 62.6}, {"speciesId": "pupitar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 302, "matchups": [{"opponent": "chandelure_shadow", "rating": 856, "opRating": 143}, {"opponent": "chandelure", "rating": 827, "opRating": 172}, {"opponent": "unfezant_shadow", "rating": 769, "opRating": 230}, {"opponent": "typhlosion_hisuian", "rating": 669, "opRating": 330}, {"opponent": "pidgeotto", "rating": 604, "opRating": 395}], "counters": [{"opponent": "cradily", "rating": 156}, {"opponent": "gligar", "rating": 160}, {"opponent": "clodsire", "rating": 163}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "talonflame", "rating": 418}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33423}, {"moveId": "ROCK_SMASH", "uses": 24877}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 22138}, {"moveId": "CRUNCH", "uses": 19374}, {"moveId": "DIG", "uses": 16709}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "ANCIENT_POWER", "DIG"], "score": 62.6}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 320, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 593, "opRating": 406}, {"opponent": "piloswine", "rating": 577, "opRating": 422}, {"opponent": "grotle_shadow", "rating": 532, "opRating": 467}, {"opponent": "entei", "rating": 529, "opRating": 470}, {"opponent": "steelix", "rating": 502, "opRating": 497}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 233}, {"opponent": "jumpluff_shadow", "rating": 281}, {"opponent": "gligar", "rating": 332}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 46382}, {"moveId": "ZEN_HEADBUTT", "uses": 9916}, {"moveId": "YAWN", "uses": 2091}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16281}, {"moveId": "BODY_SLAM", "uses": 16068}, {"moveId": "OUTRAGE", "uses": 7599}, {"moveId": "EARTHQUAKE", "uses": 6155}, {"moveId": "HEAVY_SLAM", "uses": 5710}, {"moveId": "SKULL_BASH", "uses": 3914}, {"moveId": "HYPER_BEAM", "uses": 2613}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 62.6}, {"speciesId": "bayleef", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 327, "matchups": [{"opponent": "gastrodon", "rating": 785, "opRating": 214}, {"opponent": "quagsire_shadow", "rating": 711, "opRating": 288}, {"opponent": "swampert_shadow", "rating": 647, "opRating": 352}, {"opponent": "claydol", "rating": 580, "opRating": 419}, {"opponent": "diggersby", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 55}, {"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "clodsire", "rating": 153}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 177}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 25146}, {"moveId": "TACKLE", "uses": 19422}, {"moveId": "RAZOR_LEAF", "uses": 13713}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21638}, {"moveId": "ANCIENT_POWER", "uses": 19762}, {"moveId": "RETURN", "uses": 9099}, {"moveId": "ENERGY_BALL", "uses": 7792}]}, "moveset": ["MAGICAL_LEAF", "ANCIENT_POWER", "ENERGY_BALL"], "score": 62.4}, {"speciesId": "drilbur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 307, "matchups": [{"opponent": "heatran", "rating": 802, "opRating": 197}, {"opponent": "heatran_shadow", "rating": 760, "opRating": 239}, {"opponent": "geodude_shadow", "rating": 753, "opRating": 246}, {"opponent": "typhlosion_shadow", "rating": 676, "opRating": 323}, {"opponent": "steelix", "rating": 580, "opRating": 419}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 151}, {"opponent": "cradily", "rating": 173}, {"opponent": "gligar", "rating": 297}, {"opponent": "clodsire", "rating": 391}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 44497}, {"moveId": "SCRATCH", "uses": 13803}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 24029}, {"moveId": "ROCK_TOMB", "uses": 20956}, {"moveId": "RETURN", "uses": 7246}, {"moveId": "DIG", "uses": 6090}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_TOMB"], "score": 62.4}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 327, "matchups": [{"opponent": "blazi<PERSON>_shadow", "rating": 863, "opRating": 136}, {"opponent": "munchlax", "rating": 781, "opRating": 218}, {"opponent": "steelix", "rating": 757, "opRating": 242}, {"opponent": "steelix_shadow", "rating": 730, "opRating": 269}, {"opponent": "diggersby_shadow", "rating": 621, "opRating": 378}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 128}, {"opponent": "clodsire", "rating": 485}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31351}, {"moveId": "ASTONISH", "uses": 26949}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 22217}, {"moveId": "SHADOW_PUNCH", "uses": 20815}, {"moveId": "EARTH_POWER", "uses": 11642}, {"moveId": "POLTERGEIST", "uses": 3668}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "DYNAMIC_PUNCH"], "score": 62.4}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 331, "matchups": [{"opponent": "whiscash", "rating": 769, "opRating": 230}, {"opponent": "gastrodon", "rating": 695, "opRating": 304}, {"opponent": "quagsire_shadow", "rating": 643, "opRating": 356}, {"opponent": "claydol", "rating": 534, "opRating": 465}, {"opponent": "swampert_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 137}, {"opponent": "clodsire", "rating": 300}, {"opponent": "cradily", "rating": 312}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40199}, {"moveId": "RAZOR_LEAF", "uses": 18101}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17072}, {"moveId": "SEED_BOMB", "uses": 16065}, {"moveId": "SHADOW_BALL", "uses": 13864}, {"moveId": "FIRE_BLAST", "uses": 6912}, {"moveId": "POLTERGEIST", "uses": 4464}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 62.4}, {"speciesId": "growl<PERSON>e_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 313, "matchups": [{"opponent": "ninetales_shadow", "rating": 647, "opRating": 352}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 609, "opRating": 390}, {"opponent": "pidgeot", "rating": 563, "opRating": 436}, {"opponent": "abomasnow_shadow", "rating": 510, "opRating": 489}, {"opponent": "jumpluff_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "diggersby", "rating": 120}, {"opponent": "gligar", "rating": 133}, {"opponent": "clodsire", "rating": 137}, {"opponent": "cradily", "rating": 184}, {"opponent": "talonflame", "rating": 229}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43663}, {"moveId": "BITE", "uses": 14637}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 21955}, {"moveId": "CRUNCH", "uses": 18379}, {"moveId": "FLAMETHROWER", "uses": 17992}]}, "moveset": ["EMBER", "ROCK_SLIDE", "FLAMETHROWER"], "score": 62.4}, {"speciesId": "kecleon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 325, "matchups": [{"opponent": "pumpkaboo_super", "rating": 661, "opRating": 338}, {"opponent": "noctowl", "rating": 610, "opRating": 389}, {"opponent": "claydol", "rating": 572, "opRating": 427}, {"opponent": "run<PERSON><PERSON>", "rating": 542, "opRating": 457}, {"opponent": "oranguru", "rating": 525, "opRating": 474}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "clodsire", "rating": 189}, {"opponent": "cradily", "rating": 229}, {"opponent": "gligar", "rating": 232}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39941}, {"moveId": "LICK", "uses": 18359}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 13834}, {"moveId": "FOUL_PLAY", "uses": 13271}, {"moveId": "AERIAL_ACE", "uses": 12959}, {"moveId": "FLAMETHROWER", "uses": 8268}, {"moveId": "THUNDER", "uses": 5465}, {"moveId": "SHADOW_SNEAK", "uses": 4538}]}, "moveset": ["SUCKER_PUNCH", "AERIAL_ACE", "ICE_BEAM"], "score": 62.4}, {"speciesId": "chandelure_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 345, "matchups": [{"opponent": "parasect", "rating": 903, "opRating": 96}, {"opponent": "abomasnow_shadow", "rating": 892, "opRating": 107}, {"opponent": "swadloon", "rating": 838, "opRating": 161}, {"opponent": "wormadam_plant", "rating": 838, "opRating": 161}, {"opponent": "abomasnow", "rating": 655, "opRating": 344}], "counters": [{"opponent": "gligar", "rating": 152}, {"opponent": "clodsire", "rating": 173}, {"opponent": "talonflame", "rating": 181}, {"opponent": "cradily", "rating": 218}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27860}, {"moveId": "HEX", "uses": 16817}, {"moveId": "FIRE_SPIN", "uses": 13610}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16632}, {"moveId": "SHADOW_BALL", "uses": 13598}, {"moveId": "OVERHEAT", "uses": 12825}, {"moveId": "ENERGY_BALL", "uses": 10796}, {"moveId": "POLTERGEIST", "uses": 4399}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "SHADOW_BALL"], "score": 62.1}, {"speciesId": "loudred_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 311, "matchups": [{"opponent": "chandelure_shadow", "rating": 758, "opRating": 241}, {"opponent": "pumpkaboo_super", "rating": 679, "opRating": 320}, {"opponent": "golurk_shadow", "rating": 676, "opRating": 323}, {"opponent": "run<PERSON><PERSON>", "rating": 609, "opRating": 390}, {"opponent": "marowak_alolan_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "cradily", "rating": 208}, {"opponent": "clodsire", "rating": 233}, {"opponent": "talonflame", "rating": 237}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 32474}, {"moveId": "ROCK_SMASH", "uses": 25826}], "chargedMoves": [{"moveId": "STOMP", "uses": 24541}, {"moveId": "DISARMING_VOICE", "uses": 17952}, {"moveId": "FLAMETHROWER", "uses": 15730}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "STOMP", "DISARMING_VOICE"], "score": 62.1}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 332, "matchups": [{"opponent": "wormadam_plant", "rating": 863, "opRating": 136}, {"opponent": "abomasnow", "rating": 773, "opRating": 226}, {"opponent": "parasect", "rating": 737, "opRating": 262}, {"opponent": "abomasnow_shadow", "rating": 665, "opRating": 334}, {"opponent": "miltank", "rating": 528, "opRating": 471}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "clodsire", "rating": 117}, {"opponent": "gligar", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 205}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 40968}, {"moveId": "ROCK_SMASH", "uses": 17332}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22179}, {"moveId": "LOW_SWEEP", "uses": 19848}, {"moveId": "RETURN", "uses": 11271}, {"moveId": "FLAME_WHEEL", "uses": 4938}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 62.1}, {"speciesId": "pidgeotto", "speciesName": "Pidgeotto", "rating": 351, "matchups": [{"opponent": "grotle", "rating": 755, "opRating": 244}, {"opponent": "grotle_shadow", "rating": 738, "opRating": 261}, {"opponent": "swadloon", "rating": 680, "opRating": 319}, {"opponent": "wormadam_plant", "rating": 602, "opRating": 397}, {"opponent": "parasect", "rating": 585, "opRating": 414}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "cradily", "rating": 118}, {"opponent": "clodsire", "rating": 213}, {"opponent": "gligar", "rating": 255}, {"opponent": "jumpluff_shadow", "rating": 356}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34030}, {"moveId": "STEEL_WING", "uses": 24270}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 30475}, {"moveId": "AERIAL_ACE", "uses": 11464}, {"moveId": "RETURN", "uses": 10186}, {"moveId": "TWISTER", "uses": 6284}]}, "moveset": ["WING_ATTACK", "AIR_CUTTER", "AERIAL_ACE"], "score": 62.1}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 340, "matchups": [{"opponent": "whiscash", "rating": 782, "opRating": 217}, {"opponent": "quagsire", "rating": 701, "opRating": 298}, {"opponent": "munchlax", "rating": 693, "opRating": 306}, {"opponent": "gastrodon", "rating": 689, "opRating": 310}, {"opponent": "quagsire_shadow", "rating": 658, "opRating": 341}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 137}, {"opponent": "clodsire", "rating": 298}, {"opponent": "cradily", "rating": 312}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40082}, {"moveId": "RAZOR_LEAF", "uses": 18218}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17033}, {"moveId": "SEED_BOMB", "uses": 16051}, {"moveId": "SHADOW_BALL", "uses": 13878}, {"moveId": "FIRE_BLAST", "uses": 6917}, {"moveId": "POLTERGEIST", "uses": 4467}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 61.9}, {"speciesId": "watchog_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 307, "matchups": [{"opponent": "chandelure_shadow", "rating": 760, "opRating": 240}, {"opponent": "golurk_shadow", "rating": 679, "opRating": 320}, {"opponent": "pumpkaboo_super", "rating": 675, "opRating": 324}, {"opponent": "run<PERSON><PERSON>", "rating": 596, "opRating": 404}, {"opponent": "marowak_alolan_shadow", "rating": 512, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "clodsire", "rating": 201}, {"opponent": "cradily", "rating": 218}, {"opponent": "talonflame", "rating": 237}, {"opponent": "gligar", "rating": 251}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 37461}, {"moveId": "LOW_KICK", "uses": 20839}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21168}, {"moveId": "HYPER_FANG", "uses": 20079}, {"moveId": "GRASS_KNOT", "uses": 17054}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "HYPER_FANG"], "score": 61.9}, {"speciesId": "ceruledge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 351, "matchups": [{"opponent": "parasect", "rating": 825, "opRating": 174}, {"opponent": "abomasnow_shadow", "rating": 745, "opRating": 254}, {"opponent": "abomasnow", "rating": 740, "opRating": 259}, {"opponent": "lura<PERSON>s", "rating": 731, "opRating": 268}, {"opponent": "tropius", "rating": 589, "opRating": 410}], "counters": [{"opponent": "clodsire", "rating": 132}, {"opponent": "talonflame", "rating": 137}, {"opponent": "cradily", "rating": 166}, {"opponent": "gligar", "rating": 248}, {"opponent": "jumpluff_shadow", "rating": 267}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34723}, {"moveId": "EMBER", "uses": 23577}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23918}, {"moveId": "SHADOW_BALL", "uses": 21170}, {"moveId": "FLAMETHROWER", "uses": 9924}, {"moveId": "HEAT_WAVE", "uses": 3201}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 61.7}, {"speciesId": "combusken", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 324, "matchups": [{"opponent": "abomasnow", "rating": 763, "opRating": 236}, {"opponent": "parasect", "rating": 729, "opRating": 270}, {"opponent": "swadloon", "rating": 675, "opRating": 324}, {"opponent": "wormadam_plant", "rating": 671, "opRating": 328}, {"opponent": "abomasnow_shadow", "rating": 656, "opRating": 343}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 205}, {"opponent": "gligar", "rating": 209}, {"opponent": "clodsire", "rating": 250}, {"opponent": "cradily", "rating": 399}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 42548}, {"moveId": "PECK", "uses": 15752}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21763}, {"moveId": "ROCK_SLIDE", "uses": 18224}, {"moveId": "RETURN", "uses": 9249}, {"moveId": "FLAMETHROWER", "uses": 9038}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 61.7}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 340, "matchups": [{"opponent": "abomasnow", "rating": 761, "opRating": 238}, {"opponent": "abomasnow_shadow", "rating": 750, "opRating": 250}, {"opponent": "tropius", "rating": 603, "opRating": 396}, {"opponent": "swadloon", "rating": 583, "opRating": 416}, {"opponent": "skeledirge", "rating": 547, "opRating": 452}], "counters": [{"opponent": "clodsire", "rating": 129}, {"opponent": "talonflame", "rating": 137}, {"opponent": "cradily", "rating": 166}, {"opponent": "gligar", "rating": 171}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 25915}, {"moveId": "EMBER", "uses": 16508}, {"moveId": "FIRE_FANG", "uses": 13052}, {"moveId": "TAKE_DOWN", "uses": 2773}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 19476}, {"moveId": "DARK_PULSE", "uses": 15394}, {"moveId": "OVERHEAT", "uses": 14992}, {"moveId": "SOLAR_BEAM", "uses": 8503}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "DARK_PULSE"], "score": 61.7}, {"speciesId": "quilava", "speciesName": "Quilava", "rating": 331, "matchups": [{"opponent": "abomasnow", "rating": 757, "opRating": 242}, {"opponent": "ferrothorn", "rating": 750, "opRating": 250}, {"opponent": "parasect", "rating": 714, "opRating": 285}, {"opponent": "swadloon", "rating": 683, "opRating": 316}, {"opponent": "abomasnow_shadow", "rating": 667, "opRating": 332}], "counters": [{"opponent": "talonflame", "rating": 118}, {"opponent": "gligar", "rating": 202}, {"opponent": "clodsire", "rating": 245}, {"opponent": "cradily", "rating": 263}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38214}, {"moveId": "TACKLE", "uses": 20086}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23351}, {"moveId": "DIG", "uses": 15296}, {"moveId": "RETURN", "uses": 10009}, {"moveId": "FLAMETHROWER", "uses": 9707}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 61.7}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 305, "matchups": [{"opponent": "heatran", "rating": 813, "opRating": 186}, {"opponent": "geodude_shadow", "rating": 790, "opRating": 209}, {"opponent": "heatran_shadow", "rating": 782, "opRating": 217}, {"opponent": "steelix", "rating": 604, "opRating": 395}, {"opponent": "clodsire", "rating": 565, "opRating": 434}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 156}, {"opponent": "diggersby", "rating": 209}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31670}, {"moveId": "SNARL", "uses": 26630}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22251}, {"moveId": "BRICK_BREAK", "uses": 13910}, {"moveId": "EARTHQUAKE", "uses": 11247}, {"moveId": "OUTRAGE", "uses": 10825}]}, "moveset": ["MUD_SLAP", "CRUNCH", "EARTHQUAKE"], "score": 61.5}, {"speciesId": "cacturne", "speciesName": "Cacturne", "rating": 324, "matchups": [{"opponent": "oranguru", "rating": 706, "opRating": 293}, {"opponent": "gourgeist_super", "rating": 677, "opRating": 322}, {"opponent": "gourgeist_large", "rating": 673, "opRating": 326}, {"opponent": "chansey", "rating": 578, "opRating": 421}, {"opponent": "stunfisk", "rating": 561, "opRating": 438}], "counters": [{"opponent": "jumpluff_shadow", "rating": 130}, {"opponent": "gligar", "rating": 133}, {"opponent": "cradily", "rating": 173}, {"opponent": "talonflame", "rating": 237}, {"opponent": "clodsire", "rating": 310}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 25482}, {"moveId": "SAND_ATTACK", "uses": 17051}, {"moveId": "POISON_JAB", "uses": 15714}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 17266}, {"moveId": "TRAILBLAZE", "uses": 13467}, {"moveId": "DARK_PULSE", "uses": 11913}, {"moveId": "PAYBACK", "uses": 5395}, {"moveId": "GRASS_KNOT", "uses": 5360}, {"moveId": "RETURN", "uses": 4866}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DYNAMIC_PUNCH"], "score": 61.2}, {"speciesId": "dug<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 308, "matchups": [{"opponent": "heatran_shadow", "rating": 790, "opRating": 210}, {"opponent": "heatran", "rating": 760, "opRating": 240}, {"opponent": "magcargo", "rating": 730, "opRating": 270}, {"opponent": "steelix", "rating": 725, "opRating": 275}, {"opponent": "typhlosion_shadow", "rating": 625, "opRating": 375}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 80}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 305}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 21436}, {"moveId": "MUD_SLAP", "uses": 20660}, {"moveId": "MUD_SHOT", "uses": 16187}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 29668}, {"moveId": "STONE_EDGE", "uses": 21444}, {"moveId": "EARTHQUAKE", "uses": 7071}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "STONE_EDGE"], "score": 61.2}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 333, "matchups": [{"opponent": "whiscash", "rating": 774, "opRating": 225}, {"opponent": "gastrodon", "rating": 704, "opRating": 295}, {"opponent": "quagsire", "rating": 692, "opRating": 307}, {"opponent": "munchlax", "rating": 676, "opRating": 323}, {"opponent": "quagsire_shadow", "rating": 647, "opRating": 352}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 137}, {"opponent": "clodsire", "rating": 300}, {"opponent": "cradily", "rating": 312}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40146}, {"moveId": "RAZOR_LEAF", "uses": 18154}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17076}, {"moveId": "SEED_BOMB", "uses": 16064}, {"moveId": "SHADOW_BALL", "uses": 13865}, {"moveId": "FIRE_BLAST", "uses": 6912}, {"moveId": "POLTERGEIST", "uses": 4464}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 61.2}, {"speciesId": "krokorok", "speciesName": "Krokorok", "rating": 306, "matchups": [{"opponent": "heatran", "rating": 816, "opRating": 183}, {"opponent": "geodude_shadow", "rating": 778, "opRating": 221}, {"opponent": "heatran_shadow", "rating": 774, "opRating": 225}, {"opponent": "steelix", "rating": 612, "opRating": 387}, {"opponent": "clodsire", "rating": 524}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 156}, {"opponent": "diggersby", "rating": 209}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38699}, {"moveId": "BITE", "uses": 19601}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31196}, {"moveId": "EARTHQUAKE", "uses": 14022}, {"moveId": "BULLDOZE", "uses": 13069}]}, "moveset": ["MUD_SLAP", "CRUNCH", "EARTHQUAKE"], "score": 61.2}, {"speciesId": "loudred", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 296, "matchups": [{"opponent": "lampent_shadow", "rating": 865, "opRating": 134}, {"opponent": "chandelure_shadow", "rating": 696, "opRating": 303}, {"opponent": "pumpkaboo_large", "rating": 587, "opRating": 412}, {"opponent": "pumpkaboo_super", "rating": 573, "opRating": 426}, {"opponent": "decid<PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "talonflame", "rating": 177}, {"opponent": "clodsire", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 255}, {"opponent": "cradily", "rating": 284}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33142}, {"moveId": "ROCK_SMASH", "uses": 25158}], "chargedMoves": [{"moveId": "STOMP", "uses": 19350}, {"moveId": "DISARMING_VOICE", "uses": 14598}, {"moveId": "FLAMETHROWER", "uses": 12966}, {"moveId": "RETURN", "uses": 11351}]}, "moveset": ["BITE", "STOMP", "DISARMING_VOICE"], "score": 61.2}, {"speciesId": "oinkologne", "speciesName": "Oinkologne", "rating": 297, "matchups": [{"opponent": "lilligant", "rating": 647, "opRating": 352}, {"opponent": "lickitung", "rating": 581, "opRating": 418}, {"opponent": "shiinotic", "rating": 562, "opRating": 437}, {"opponent": "ma<PERSON><PERSON>", "rating": 553, "opRating": 446}, {"opponent": "onix", "rating": 512, "opRating": 487}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 351}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 46234}, {"moveId": "TAKE_DOWN", "uses": 12066}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28530}, {"moveId": "TRAILBLAZE", "uses": 18364}, {"moveId": "DIG", "uses": 11414}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 61.2}, {"speciesId": "tranquill", "speciesName": "Tran<PERSON>ll", "rating": 337, "matchups": [{"opponent": "breloom", "rating": 737, "opRating": 262}, {"opponent": "swadloon", "rating": 618, "opRating": 381}, {"opponent": "gogoat", "rating": 583, "opRating": 416}, {"opponent": "wormadam_sandy", "rating": 520, "opRating": 479}, {"opponent": "<PERSON><PERSON>e", "rating": 513, "opRating": 486}], "counters": [{"opponent": "cradily", "rating": 125}, {"opponent": "talonflame", "rating": 151}, {"opponent": "clodsire", "rating": 175}, {"opponent": "gligar", "rating": 209}, {"opponent": "jumpluff_shadow", "rating": 254}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32197}, {"moveId": "STEEL_WING", "uses": 26103}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 30319}, {"moveId": "RETURN", "uses": 13343}, {"moveId": "SKY_ATTACK", "uses": 10061}, {"moveId": "HEAT_WAVE", "uses": 4590}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "RETURN"], "score": 61.2}, {"speciesId": "watchog", "speciesName": "Watchog", "rating": 294, "matchups": [{"opponent": "lampent_shadow", "rating": 856, "opRating": 144}, {"opponent": "chandelure_shadow", "rating": 700, "opRating": 300}, {"opponent": "pumpkaboo_large", "rating": 592, "opRating": 408}, {"opponent": "pumpkaboo_average", "rating": 588, "opRating": 412}, {"opponent": "pumpkaboo_super", "rating": 580, "opRating": 420}], "counters": [{"opponent": "talonflame", "rating": 177}, {"opponent": "clodsire", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 263}, {"opponent": "cradily", "rating": 284}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 38431}, {"moveId": "LOW_KICK", "uses": 19869}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19042}, {"moveId": "HYPER_FANG", "uses": 17651}, {"moveId": "GRASS_KNOT", "uses": 15545}, {"moveId": "RETURN", "uses": 6036}]}, "moveset": ["BITE", "CRUNCH", "GRASS_KNOT"], "score": 61.2}, {"speciesId": "mudsdale", "speciesName": "Mudsdale", "rating": 304, "matchups": [{"opponent": "heatran_shadow", "rating": 808, "opRating": 191}, {"opponent": "entei_shadow", "rating": 702, "opRating": 297}, {"opponent": "steelix", "rating": 632, "opRating": 367}, {"opponent": "steelix_shadow", "rating": 595, "opRating": 404}, {"opponent": "dunsparce", "rating": 525, "opRating": 474}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 173}, {"opponent": "cradily", "rating": 187}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 430}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43784}, {"moveId": "ROCK_SMASH", "uses": 14516}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21726}, {"moveId": "EARTHQUAKE", "uses": 13146}, {"moveId": "BULLDOZE", "uses": 12346}, {"moveId": "HEAVY_SLAM", "uses": 11065}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTHQUAKE"], "score": 61}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 330, "matchups": [{"opponent": "whiscash", "rating": 725, "opRating": 274}, {"opponent": "<PERSON><PERSON>e", "rating": 644, "opRating": 355}, {"opponent": "marowak", "rating": 617, "opRating": 382}, {"opponent": "furret", "rating": 522, "opRating": 477}, {"opponent": "quagsire_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "gligar", "rating": 83}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "clodsire", "rating": 187}, {"opponent": "cradily", "rating": 218}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31955}, {"moveId": "BULLET_SEED", "uses": 26345}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 19604}, {"moveId": "BREAKING_SWIPE", "uses": 12285}, {"moveId": "AERIAL_ACE", "uses": 9847}, {"moveId": "FRENZY_PLANT", "uses": 7281}, {"moveId": "DRAGON_CLAW", "uses": 4859}, {"moveId": "EARTHQUAKE", "uses": 4456}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 61}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 333, "matchups": [{"opponent": "stunfisk", "rating": 672, "opRating": 327}, {"opponent": "munchlax", "rating": 647, "opRating": 352}, {"opponent": "whiscash", "rating": 617, "opRating": 382}, {"opponent": "gastrodon", "rating": 588, "opRating": 411}, {"opponent": "marowak", "rating": 516, "opRating": 483}], "counters": [{"opponent": "jumpluff_shadow", "rating": 133}, {"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 218}, {"opponent": "gligar", "rating": 244}, {"opponent": "cradily", "rating": 256}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20013}, {"moveId": "LEAFAGE", "uses": 16828}, {"moveId": "MAGICAL_LEAF", "uses": 15615}, {"moveId": "RAZOR_LEAF", "uses": 5843}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 21922}, {"moveId": "BRAVE_BIRD", "uses": 19188}, {"moveId": "SPIRIT_SHACKLE", "uses": 10248}, {"moveId": "ENERGY_BALL", "uses": 4196}, {"moveId": "SHADOW_SNEAK", "uses": 2694}]}, "moveset": ["ASTONISH", "FRENZY_PLANT", "SPIRIT_SHACKLE"], "score": 60.8}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 312, "matchups": [{"opponent": "geodude_shadow", "rating": 839, "opRating": 160}, {"opponent": "steelix", "rating": 664, "opRating": 335}, {"opponent": "munchlax", "rating": 585, "opRating": 414}, {"opponent": "typhlosion_shadow", "rating": 525, "opRating": 475}, {"opponent": "miltank", "rating": 514, "opRating": 485}], "counters": [{"opponent": "gligar", "rating": 61}, {"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 151}, {"opponent": "clodsire", "rating": 218}, {"opponent": "cradily", "rating": 315}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22879}, {"moveId": "MUD_SHOT", "uses": 18727}, {"moveId": "METAL_CLAW", "uses": 16703}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20724}, {"moveId": "ROCK_SLIDE", "uses": 13671}, {"moveId": "IRON_HEAD", "uses": 11612}, {"moveId": "SCORCHING_SANDS", "uses": 7850}, {"moveId": "EARTHQUAKE", "uses": 4477}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_SLIDE"], "score": 60.8}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 335, "matchups": [{"opponent": "swadloon", "rating": 716, "opRating": 283}, {"opponent": "lura<PERSON>s", "rating": 677, "opRating": 322}, {"opponent": "abomasnow_shadow", "rating": 593, "opRating": 406}, {"opponent": "tropius", "rating": 550, "opRating": 449}, {"opponent": "farfetchd", "rating": 546, "opRating": 453}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "gligar", "rating": 312}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31182}, {"moveId": "WING_ATTACK", "uses": 27118}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18503}, {"moveId": "ANCIENT_POWER", "uses": 16563}, {"moveId": "OVERHEAT", "uses": 15913}, {"moveId": "FIRE_BLAST", "uses": 4673}, {"moveId": "HEAT_WAVE", "uses": 2693}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 60.8}, {"speciesId": "shiinotic", "speciesName": "Shiinotic", "rating": 308, "matchups": [{"opponent": "flygon", "rating": 699, "opRating": 300}, {"opponent": "flygon_shadow", "rating": 634, "opRating": 365}, {"opponent": "marowak", "rating": 605, "opRating": 394}, {"opponent": "claydol", "rating": 520, "opRating": 479}, {"opponent": "quagsire_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "jumpluff_shadow", "rating": 114}, {"opponent": "cradily", "rating": 145}, {"opponent": "clodsire", "rating": 156}, {"opponent": "talonflame", "rating": 166}, {"opponent": "gligar", "rating": 190}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 58300}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 21240}, {"moveId": "MOONBLAST", "uses": 19658}, {"moveId": "SLUDGE_BOMB", "uses": 17368}]}, "moveset": ["ASTONISH", "MOONBLAST", "SEED_BOMB"], "score": 60.8}, {"speciesId": "gra<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 315, "matchups": [{"opponent": "heatran_shadow", "rating": 773, "opRating": 226}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 756, "opRating": 243}, {"opponent": "amoon<PERSON>s", "rating": 647, "opRating": 352}, {"opponent": "whimsicott", "rating": 582, "opRating": 417}, {"opponent": "wigglytuff", "rating": 521, "opRating": 478}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 125}, {"opponent": "clodsire", "rating": 170}, {"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "cradily", "rating": 312}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 30701}, {"moveId": "MUD_SLAP", "uses": 27599}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 30531}, {"moveId": "SLUDGE_BOMB", "uses": 24982}, {"moveId": "ACID_SPRAY", "uses": 2915}]}, "moveset": ["MUD_SLAP", "POISON_FANG", "SLUDGE_BOMB"], "score": 60.1}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 329, "matchups": [{"opponent": "abomasnow_shadow", "rating": 754, "opRating": 245}, {"opponent": "abomasnow", "rating": 754, "opRating": 245}, {"opponent": "swadloon", "rating": 696, "opRating": 303}, {"opponent": "tropius", "rating": 607, "opRating": 392}, {"opponent": "ninetales", "rating": 535, "opRating": 464}], "counters": [{"opponent": "talonflame", "rating": 137}, {"opponent": "cradily", "rating": 156}, {"opponent": "gligar", "rating": 171}, {"opponent": "clodsire", "rating": 266}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 24945}, {"moveId": "EMBER", "uses": 16204}, {"moveId": "FIRE_SPIN", "uses": 14292}, {"moveId": "LOW_KICK", "uses": 2898}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 15238}, {"moveId": "FLAME_CHARGE", "uses": 14835}, {"moveId": "WILD_CHARGE", "uses": 13909}, {"moveId": "FIRE_BLAST", "uses": 6703}, {"moveId": "SCORCHING_SANDS", "uses": 5745}, {"moveId": "HEAT_WAVE", "uses": 2021}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "WILD_CHARGE"], "score": 60.1}, {"speciesId": "dart<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 334, "matchups": [{"opponent": "whiscash", "rating": 798, "opRating": 201}, {"opponent": "gogoat", "rating": 769, "opRating": 230}, {"opponent": "marowak", "rating": 678, "opRating": 321}, {"opponent": "swampert_shadow", "rating": 668, "opRating": 331}, {"opponent": "quagsire_shadow", "rating": 574, "opRating": 425}], "counters": [{"opponent": "jumpluff_shadow", "rating": 45}, {"opponent": "talonflame", "rating": 55}, {"opponent": "cradily", "rating": 156}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 274}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 19930}, {"moveId": "MAGICAL_LEAF", "uses": 18729}, {"moveId": "PECK", "uses": 12380}, {"moveId": "RAZOR_LEAF", "uses": 7347}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36665}, {"moveId": "SEED_BOMB", "uses": 14976}, {"moveId": "ENERGY_BALL", "uses": 6711}]}, "moveset": ["LEAFAGE", "BRAVE_BIRD", "SEED_BOMB"], "score": 59.9}, {"speciesId": "go<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 305, "matchups": [{"opponent": "salazzle", "rating": 890, "opRating": 109}, {"opponent": "heatran", "rating": 773, "opRating": 226}, {"opponent": "geodude_shadow", "rating": 744, "opRating": 255}, {"opponent": "steelix", "rating": 730, "opRating": 269}, {"opponent": "clodsire", "rating": 514}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 110}, {"opponent": "talonflame", "rating": 122}, {"opponent": "cradily", "rating": 142}, {"opponent": "furret", "rating": 165}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31169}, {"moveId": "ASTONISH", "uses": 27131}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 29214}, {"moveId": "BRICK_BREAK", "uses": 20422}, {"moveId": "NIGHT_SHADE", "uses": 8606}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "BRICK_BREAK"], "score": 59.9}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 326, "matchups": [{"opponent": "whiscash", "rating": 757, "opRating": 242}, {"opponent": "gastrodon", "rating": 682, "opRating": 317}, {"opponent": "quagsire", "rating": 672, "opRating": 327}, {"opponent": "munchlax", "rating": 644, "opRating": 355}, {"opponent": "quagsire_shadow", "rating": 630, "opRating": 369}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 137}, {"opponent": "clodsire", "rating": 302}, {"opponent": "cradily", "rating": 312}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40231}, {"moveId": "RAZOR_LEAF", "uses": 18069}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17039}, {"moveId": "SEED_BOMB", "uses": 16060}, {"moveId": "SHADOW_BALL", "uses": 13845}, {"moveId": "FIRE_BLAST", "uses": 6902}, {"moveId": "POLTERGEIST", "uses": 4467}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 59.9}, {"speciesId": "gumshoos", "speciesName": "<PERSON>ums<PERSON><PERSON>", "rating": 289, "matchups": [{"opponent": "lampent_shadow", "rating": 857, "opRating": 142}, {"opponent": "chandelure_shadow", "rating": 768, "opRating": 231}, {"opponent": "chandelure", "rating": 714, "opRating": 285}, {"opponent": "pumpkaboo_large", "rating": 544, "opRating": 455}, {"opponent": "pumpkaboo_super", "rating": 530, "opRating": 469}], "counters": [{"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "clodsire", "rating": 189}, {"opponent": "talonflame", "rating": 237}, {"opponent": "cradily", "rating": 243}, {"opponent": "gligar", "rating": 251}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 39493}, {"moveId": "TAKE_DOWN", "uses": 18807}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22998}, {"moveId": "CRUNCH", "uses": 18375}, {"moveId": "HYPER_FANG", "uses": 16902}]}, "moveset": ["BITE", "ROCK_TOMB", "CRUNCH"], "score": 59.9}, {"speciesId": "raboot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 334, "matchups": [{"opponent": "abomasnow", "rating": 776, "opRating": 223}, {"opponent": "abomasnow_shadow", "rating": 765, "opRating": 234}, {"opponent": "ferrothorn", "rating": 742, "opRating": 257}, {"opponent": "parasect", "rating": 711, "opRating": 288}, {"opponent": "swadloon", "rating": 603, "opRating": 396}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 138}, {"opponent": "gligar", "rating": 194}, {"opponent": "clodsire", "rating": 252}, {"opponent": "jumpluff_shadow", "rating": 333}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 37279}, {"moveId": "TACKLE", "uses": 21021}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 41167}, {"moveId": "FLAMETHROWER", "uses": 17133}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 59.9}, {"speciesId": "charmeleon", "speciesName": "Charmeleon", "rating": 337, "matchups": [{"opponent": "abomasnow", "rating": 769, "opRating": 230}, {"opponent": "<PERSON><PERSON>e", "rating": 691, "opRating": 308}, {"opponent": "abomasnow_shadow", "rating": 667, "opRating": 332}, {"opponent": "tropius", "rating": 625, "opRating": 375}, {"opponent": "castform_sunny", "rating": 546, "opRating": 453}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "clodsire", "rating": 115}, {"opponent": "cradily", "rating": 135}, {"opponent": "gligar", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25426}, {"moveId": "FIRE_FANG", "uses": 22481}, {"moveId": "SCRATCH", "uses": 10388}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 30997}, {"moveId": "RETURN", "uses": 11716}, {"moveId": "FLAMETHROWER", "uses": 9679}, {"moveId": "FLAME_BURST", "uses": 5883}]}, "moveset": ["EMBER", "FIRE_PUNCH", "RETURN"], "score": 59.6}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 269, "matchups": [{"opponent": "whiscash", "rating": 694, "opRating": 305}, {"opponent": "claydol", "rating": 649, "opRating": 350}, {"opponent": "diggersby", "rating": 544, "opRating": 455}, {"opponent": "steelix", "rating": 537, "opRating": 462}, {"opponent": "marowak", "rating": 514, "opRating": 485}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "cradily", "rating": 170}, {"opponent": "talonflame", "rating": 203}, {"opponent": "clodsire", "rating": 218}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 44242}, {"moveId": "RAZOR_LEAF", "uses": 14058}], "chargedMoves": [{"moveId": "SCALD", "uses": 19229}, {"moveId": "ICE_BEAM", "uses": 13175}, {"moveId": "ENERGY_BALL", "uses": 8044}, {"moveId": "LEAF_STORM", "uses": 6988}, {"moveId": "BLIZZARD", "uses": 4606}, {"moveId": "HYDRO_PUMP", "uses": 3862}, {"moveId": "SOLAR_BEAM", "uses": 2313}]}, "moveset": ["BUBBLE", "SCALD", "LEAF_STORM"], "score": 59.6}, {"speciesId": "pumpkaboo_large", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Large)", "rating": 326, "matchups": [{"opponent": "stunfisk", "rating": 706, "opRating": 293}, {"opponent": "munchlax", "rating": 684, "opRating": 315}, {"opponent": "gastrodon", "rating": 631, "opRating": 368}, {"opponent": "whiscash", "rating": 609, "opRating": 390}, {"opponent": "marowak", "rating": 567, "opRating": 432}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 264}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38041}, {"moveId": "RAZOR_LEAF", "uses": 20259}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23578}, {"moveId": "FOUL_PLAY", "uses": 23241}, {"moveId": "SHADOW_SNEAK", "uses": 11411}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 59.6}, {"speciesId": "sandshrew", "speciesName": "Sandshrew", "rating": 282, "matchups": [{"opponent": "onix", "rating": 736, "opRating": 263}, {"opponent": "nidoqueen", "rating": 677, "opRating": 322}, {"opponent": "steelix", "rating": 590, "opRating": 409}, {"opponent": "chansey", "rating": 590, "opRating": 409}, {"opponent": "steelix_shadow", "rating": 555, "opRating": 444}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 255}, {"opponent": "jumpluff_shadow", "rating": 323}, {"opponent": "cradily", "rating": 336}, {"opponent": "clodsire", "rating": 413}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 25077}, {"moveId": "MUD_SHOT", "uses": 22135}, {"moveId": "SCRATCH", "uses": 11073}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18862}, {"moveId": "DIG", "uses": 13408}, {"moveId": "ROCK_SLIDE", "uses": 12203}, {"moveId": "RETURN", "uses": 7105}, {"moveId": "SAND_TOMB", "uses": 6701}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 59.6}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 333, "matchups": [{"opponent": "abomasnow", "rating": 760, "opRating": 239}, {"opponent": "abomasnow_shadow", "rating": 747, "opRating": 252}, {"opponent": "<PERSON><PERSON>e", "rating": 688, "opRating": 311}, {"opponent": "tropius", "rating": 623, "opRating": 376}, {"opponent": "jumpluff_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "magcargo", "rating": 55}, {"opponent": "talonflame", "rating": 92}, {"opponent": "cradily", "rating": 107}, {"opponent": "clodsire", "rating": 132}, {"opponent": "gligar", "rating": 171}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34650}, {"moveId": "EMBER", "uses": 23650}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 31935}, {"moveId": "FLAME_CHARGE", "uses": 16987}, {"moveId": "FLAMETHROWER", "uses": 7081}, {"moveId": "HEAT_WAVE", "uses": 2248}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 59.4}, {"speciesId": "darmanitan_standard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Standard) (Shadow)", "rating": 328, "matchups": [{"opponent": "abomasnow_shadow", "rating": 891, "opRating": 108}, {"opponent": "swadloon", "rating": 782, "opRating": 217}, {"opponent": "wormadam_plant", "rating": 782, "opRating": 217}, {"opponent": "grotle_shadow", "rating": 721, "opRating": 278}, {"opponent": "abomasnow", "rating": 681, "opRating": 318}], "counters": [{"opponent": "gligar", "rating": 152}, {"opponent": "clodsire", "rating": 173}, {"opponent": "talonflame", "rating": 181}, {"opponent": "cradily", "rating": 208}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34573}, {"moveId": "FIRE_FANG", "uses": 14504}, {"moveId": "TACKLE", "uses": 9210}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17752}, {"moveId": "ROCK_SLIDE", "uses": 17335}, {"moveId": "FOCUS_BLAST", "uses": 13337}, {"moveId": "PSYCHIC", "uses": 9853}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 59.4}, {"speciesId": "pupitar", "speciesName": "Pupitar", "rating": 284, "matchups": [{"opponent": "chandelure_shadow", "rating": 827, "opRating": 172}, {"opponent": "typhlosion_hisuian", "rating": 687, "opRating": 312}, {"opponent": "pidgeotto", "rating": 622, "opRating": 377}, {"opponent": "oricorio_baile", "rating": 597, "opRating": 402}, {"opponent": "moltres", "rating": 589, "opRating": 410}], "counters": [{"opponent": "cradily", "rating": 156}, {"opponent": "clodsire", "rating": 165}, {"opponent": "jumpluff_shadow", "rating": 267}, {"opponent": "gligar", "rating": 286}, {"opponent": "talonflame", "rating": 292}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33800}, {"moveId": "ROCK_SMASH", "uses": 24500}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 19373}, {"moveId": "CRUNCH", "uses": 16709}, {"moveId": "DIG", "uses": 14646}, {"moveId": "RETURN", "uses": 7557}]}, "moveset": ["BITE", "ANCIENT_POWER", "DIG"], "score": 59.4}, {"speciesId": "dug<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 291, "matchups": [{"opponent": "nidoqueen", "rating": 827, "opRating": 172}, {"opponent": "geodude_shadow", "rating": 800, "opRating": 200}, {"opponent": "onix", "rating": 777, "opRating": 222}, {"opponent": "steelix", "rating": 755, "opRating": 244}, {"opponent": "steelix_shadow", "rating": 677, "opRating": 322}], "counters": [{"opponent": "gligar", "rating": 64}, {"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 122}, {"opponent": "clodsire", "rating": 230}, {"opponent": "cradily", "rating": 454}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22459}, {"moveId": "SAND_ATTACK", "uses": 19678}, {"moveId": "METAL_CLAW", "uses": 16156}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 26174}, {"moveId": "IRON_HEAD", "uses": 16171}, {"moveId": "RETURN", "uses": 9700}, {"moveId": "EARTHQUAKE", "uses": 6213}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "IRON_HEAD"], "score": 59.2}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 337, "matchups": [{"opponent": "abomasnow_shadow", "rating": 750, "opRating": 250}, {"opponent": "wormadam_plant", "rating": 728, "opRating": 271}, {"opponent": "ferrothorn", "rating": 728, "opRating": 271}, {"opponent": "abomasnow", "rating": 682, "opRating": 317}, {"opponent": "oranguru", "rating": 682, "opRating": 317}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 134}, {"opponent": "gligar", "rating": 160}, {"opponent": "cradily", "rating": 170}, {"opponent": "jumpluff_shadow", "rating": 251}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 29196}, {"moveId": "SNARL", "uses": 29104}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 25757}, {"moveId": "FLAMETHROWER", "uses": 16167}, {"moveId": "CRUNCH", "uses": 12052}, {"moveId": "FIRE_BLAST", "uses": 4313}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 59.2}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 258, "matchups": [{"opponent": "ninetales", "rating": 691, "opRating": 308}, {"opponent": "castform_sunny", "rating": 691, "opRating": 308}, {"opponent": "magcargo", "rating": 647, "opRating": 352}, {"opponent": "marowak_alolan", "rating": 540, "opRating": 459}, {"opponent": "clodsire", "rating": 525, "opRating": 474}], "counters": [{"opponent": "jumpluff_shadow", "rating": 22}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 86}, {"opponent": "furret", "rating": 96}, {"opponent": "gligar", "rating": 469}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32632}, {"moveId": "WATER_GUN", "uses": 25668}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 23411}, {"moveId": "SLUDGE", "uses": 12230}, {"moveId": "MUDDY_WATER", "uses": 8953}, {"moveId": "SURF", "uses": 6528}, {"moveId": "EARTHQUAKE", "uses": 5189}, {"moveId": "SLUDGE_WAVE", "uses": 2212}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 59.2}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 266, "matchups": [{"opponent": "marowak_alolan", "rating": 724, "opRating": 275}, {"opponent": "skeledirge", "rating": 697, "opRating": 302}, {"opponent": "magcargo", "rating": 614, "opRating": 385}, {"opponent": "ninetales_shadow", "rating": 577, "opRating": 422}, {"opponent": "piloswine", "rating": 567, "opRating": 432}], "counters": [{"opponent": "cradily", "rating": 59}, {"opponent": "gligar", "rating": 110}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "talonflame", "rating": 196}, {"opponent": "clodsire", "rating": 293}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 29955}, {"moveId": "WATER_GUN", "uses": 23445}, {"moveId": "TAKE_DOWN", "uses": 4927}], "chargedMoves": [{"moveId": "SURF", "uses": 31635}, {"moveId": "HYPER_FANG", "uses": 16621}, {"moveId": "RETURN", "uses": 5748}, {"moveId": "HYPER_BEAM", "uses": 4332}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 58.9}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 338, "matchups": [{"opponent": "wormadam_plant", "rating": 837, "opRating": 162}, {"opponent": "abomasnow", "rating": 760, "opRating": 239}, {"opponent": "abomasnow_shadow", "rating": 747, "opRating": 252}, {"opponent": "swadloon", "rating": 747, "opRating": 252}, {"opponent": "tropius", "rating": 623, "opRating": 376}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "cradily", "rating": 107}, {"opponent": "clodsire", "rating": 132}, {"opponent": "gligar", "rating": 171}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 26242}, {"moveId": "EMBER", "uses": 16535}, {"moveId": "SHADOW_CLAW", "uses": 15528}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 28487}, {"moveId": "THUNDER_PUNCH", "uses": 12365}, {"moveId": "SOLAR_BEAM", "uses": 7885}, {"moveId": "OVERHEAT", "uses": 5989}, {"moveId": "FIRE_BLAST", "uses": 3531}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 58.9}, {"speciesId": "combusken_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 319, "matchups": [{"opponent": "wormadam_plant", "rating": 877, "opRating": 122}, {"opponent": "swadloon", "rating": 770, "opRating": 229}, {"opponent": "abomasnow", "rating": 732, "opRating": 267}, {"opponent": "abomasnow_shadow", "rating": 729, "opRating": 270}, {"opponent": "parasect", "rating": 713, "opRating": 286}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 156}, {"opponent": "gligar", "rating": 217}, {"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "cradily", "rating": 263}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43061}, {"moveId": "PECK", "uses": 15239}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25792}, {"moveId": "ROCK_SLIDE", "uses": 21654}, {"moveId": "FLAMETHROWER", "uses": 10806}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 58.7}, {"speciesId": "<PERSON><PERSON><PERSON>_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 306, "matchups": [{"opponent": "geodude_shadow", "rating": 844, "opRating": 155}, {"opponent": "nidoqueen", "rating": 772, "opRating": 227}, {"opponent": "steelix_shadow", "rating": 688, "opRating": 311}, {"opponent": "steelix", "rating": 677, "opRating": 322}, {"opponent": "chansey", "rating": 561, "opRating": 438}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 151}, {"opponent": "cradily", "rating": 170}, {"opponent": "clodsire", "rating": 278}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23110}, {"moveId": "SAND_ATTACK", "uses": 19160}, {"moveId": "METAL_CLAW", "uses": 16059}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 30600}, {"moveId": "IRON_HEAD", "uses": 20271}, {"moveId": "EARTHQUAKE", "uses": 7229}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "IRON_HEAD"], "score": 58.7}, {"speciesId": "ferroth<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 302, "matchups": [{"opponent": "quagsire", "rating": 625, "opRating": 375}, {"opponent": "steelix", "rating": 617, "opRating": 382}, {"opponent": "lileep", "rating": 585, "opRating": 414}, {"opponent": "claydol", "rating": 562, "opRating": 437}, {"opponent": "quagsire_shadow", "rating": 531, "opRating": 468}], "counters": [{"opponent": "talonflame", "rating": 33}, {"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 125}, {"opponent": "clodsire", "rating": 305}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30646}, {"moveId": "METAL_CLAW", "uses": 27654}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20892}, {"moveId": "MIRROR_SHOT", "uses": 16409}, {"moveId": "THUNDER", "uses": 8863}, {"moveId": "FLASH_CANNON", "uses": 8556}, {"moveId": "ACID_SPRAY", "uses": 3565}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "THUNDER"], "score": 58.5}, {"speciesId": "lileep_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 321, "matchups": [{"opponent": "whiscash", "rating": 675, "opRating": 324}, {"opponent": "hippopotas", "rating": 652, "opRating": 347}, {"opponent": "munchlax", "rating": 592, "opRating": 407}, {"opponent": "chansey", "rating": 559, "opRating": 440}, {"opponent": "litleo", "rating": 526, "opRating": 473}], "counters": [{"opponent": "talonflame", "rating": 114}, {"opponent": "gligar", "rating": 141}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "clodsire", "rating": 235}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22890}, {"moveId": "BULLET_SEED", "uses": 19839}, {"moveId": "INFESTATION", "uses": 15534}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 26695}, {"moveId": "GRASS_KNOT", "uses": 24923}, {"moveId": "MIRROR_COAT", "uses": 6646}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "GRASS_KNOT", "ANCIENT_POWER"], "score": 58.5}, {"speciesId": "palossand", "speciesName": "Palossand", "rating": 285, "matchups": [{"opponent": "chandelure_shadow", "rating": 731, "opRating": 268}, {"opponent": "steelix", "rating": 645, "opRating": 354}, {"opponent": "steelix_shadow", "rating": 615, "opRating": 384}, {"opponent": "munchlax", "rating": 585, "opRating": 414}, {"opponent": "wormadam_sandy", "rating": 582, "opRating": 417}], "counters": [{"opponent": "talonflame", "rating": 185}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 253}, {"opponent": "clodsire", "rating": 478}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20439}, {"moveId": "SAND_ATTACK", "uses": 19961}, {"moveId": "MUD_SHOT", "uses": 17821}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22489}, {"moveId": "SHADOW_BALL", "uses": 19537}, {"moveId": "EARTH_POWER", "uses": 8883}, {"moveId": "SAND_TOMB", "uses": 7504}]}, "moveset": ["ASTONISH", "SCORCHING_SANDS", "SHADOW_BALL"], "score": 58.5}, {"speciesId": "braixen", "speciesName": "Braixen", "rating": 327, "matchups": [{"opponent": "abomasnow", "rating": 756, "opRating": 243}, {"opponent": "swadloon", "rating": 715, "opRating": 284}, {"opponent": "<PERSON><PERSON>e", "rating": 674, "opRating": 325}, {"opponent": "abomasnow_shadow", "rating": 646, "opRating": 353}, {"opponent": "tropius", "rating": 577, "opRating": 422}], "counters": [{"opponent": "clodsire", "rating": 103}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43599}, {"moveId": "SCRATCH", "uses": 14701}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 28624}, {"moveId": "FLAME_CHARGE", "uses": 20911}, {"moveId": "FLAMETHROWER", "uses": 8790}]}, "moveset": ["EMBER", "PSYSHOCK", "FLAME_CHARGE"], "score": 58.3}, {"speciesId": "tranquill_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 323, "matchups": [{"opponent": "breloom", "rating": 748, "opRating": 251}, {"opponent": "parasect", "rating": 653, "opRating": 346}, {"opponent": "swadloon", "rating": 566, "opRating": 433}, {"opponent": "lickitung", "rating": 562, "opRating": 437}, {"opponent": "gastrodon", "rating": 517, "opRating": 482}], "counters": [{"opponent": "clodsire", "rating": 108}, {"opponent": "cradily", "rating": 138}, {"opponent": "talonflame", "rating": 188}, {"opponent": "jumpluff_shadow", "rating": 212}, {"opponent": "gligar", "rating": 217}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32563}, {"moveId": "STEEL_WING", "uses": 25737}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 39244}, {"moveId": "SKY_ATTACK", "uses": 13039}, {"moveId": "HEAT_WAVE", "uses": 5887}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "SKY_ATTACK"], "score": 58.3}, {"speciesId": "lampent", "speciesName": "<PERSON><PERSON>", "rating": 320, "matchups": [{"opponent": "wormadam_plant", "rating": 852, "opRating": 147}, {"opponent": "parasect", "rating": 782, "opRating": 217}, {"opponent": "abomasnow", "rating": 771, "opRating": 228}, {"opponent": "swadloon", "rating": 651, "opRating": 348}, {"opponent": "abomasnow_shadow", "rating": 647, "opRating": 352}], "counters": [{"opponent": "clodsire", "rating": 137}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 166}, {"opponent": "gligar", "rating": 232}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31305}, {"moveId": "ASTONISH", "uses": 26995}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 19631}, {"moveId": "FLAME_BURST", "uses": 18735}, {"moveId": "RETURN", "uses": 14934}, {"moveId": "HEAT_WAVE", "uses": 5027}]}, "moveset": ["EMBER", "ENERGY_BALL", "FLAME_BURST"], "score": 58}, {"speciesId": "bayleef_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 308, "matchups": [{"opponent": "whiscash", "rating": 788, "opRating": 211}, {"opponent": "gastrodon", "rating": 760, "opRating": 239}, {"opponent": "quagsire", "rating": 711, "opRating": 288}, {"opponent": "quagsire_shadow", "rating": 654, "opRating": 345}, {"opponent": "swampert_shadow", "rating": 612, "opRating": 387}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 74}, {"opponent": "gligar", "rating": 190}, {"opponent": "clodsire", "rating": 197}, {"opponent": "cradily", "rating": 225}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 25733}, {"moveId": "TACKLE", "uses": 19225}, {"moveId": "RAZOR_LEAF", "uses": 13283}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 25264}, {"moveId": "ANCIENT_POWER", "uses": 23907}, {"moveId": "ENERGY_BALL", "uses": 9048}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MAGICAL_LEAF", "ANCIENT_POWER", "ENERGY_BALL"], "score": 57.8}, {"speciesId": "<PERSON><PERSON>o", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 285, "matchups": [{"opponent": "heatran", "rating": 800, "opRating": 200}, {"opponent": "heatran_shadow", "rating": 760, "opRating": 240}, {"opponent": "steelix", "rating": 745, "opRating": 255}, {"opponent": "steelix_shadow", "rating": 725, "opRating": 275}, {"opponent": "clodsire", "rating": 540, "opRating": 460}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 142}, {"opponent": "talonflame", "rating": 151}, {"opponent": "diggersby", "rating": 204}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 21227}, {"moveId": "MUD_SLAP", "uses": 20640}, {"moveId": "MUD_SHOT", "uses": 16428}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 25578}, {"moveId": "STONE_EDGE", "uses": 17890}, {"moveId": "RETURN", "uses": 8615}, {"moveId": "EARTHQUAKE", "uses": 6047}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "STONE_EDGE"], "score": 57.8}, {"speciesId": "monferno_shadow", "speciesName": "Mon<PERSON> (Shadow)", "rating": 320, "matchups": [{"opponent": "wormadam_plant", "rating": 884, "opRating": 115}, {"opponent": "swadloon", "rating": 794, "opRating": 205}, {"opponent": "abomasnow_shadow", "rating": 737, "opRating": 262}, {"opponent": "parasect", "rating": 723, "opRating": 276}, {"opponent": "<PERSON><PERSON>e", "rating": 615, "opRating": 384}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 141}, {"opponent": "clodsire", "rating": 156}, {"opponent": "cradily", "rating": 225}, {"opponent": "jumpluff_shadow", "rating": 251}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41042}, {"moveId": "ROCK_SMASH", "uses": 17258}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 27330}, {"moveId": "LOW_SWEEP", "uses": 24836}, {"moveId": "FLAME_WHEEL", "uses": 6079}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 57.8}, {"speciesId": "quilava_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 329, "matchups": [{"opponent": "wormadam_plant", "rating": 777, "opRating": 222}, {"opponent": "abomasnow_shadow", "rating": 742, "opRating": 257}, {"opponent": "ferrothorn", "rating": 726, "opRating": 273}, {"opponent": "parasect", "rating": 722, "opRating": 277}, {"opponent": "swadloon", "rating": 652, "opRating": 347}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "cradily", "rating": 145}, {"opponent": "talonflame", "rating": 148}, {"opponent": "gligar", "rating": 160}, {"opponent": "jumpluff_shadow", "rating": 251}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38602}, {"moveId": "TACKLE", "uses": 19698}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 27996}, {"moveId": "DIG", "uses": 18557}, {"moveId": "FLAMETHROWER", "uses": 11761}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 57.8}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 285, "matchups": [{"opponent": "infernape_shadow", "rating": 743, "opRating": 256}, {"opponent": "breloom", "rating": 726, "opRating": 273}, {"opponent": "bewear", "rating": 660, "opRating": 339}, {"opponent": "tauros_blaze", "rating": 537, "opRating": 462}, {"opponent": "lilligant", "rating": 520, "opRating": 479}], "counters": [{"opponent": "gligar", "rating": 213}, {"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "talonflame", "rating": 222}, {"opponent": "cradily", "rating": 253}, {"opponent": "clodsire", "rating": 319}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 29425}, {"moveId": "TACKLE", "uses": 22955}, {"moveId": "ZEN_HEADBUTT", "uses": 5854}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 16741}, {"moveId": "STOMP", "uses": 15547}, {"moveId": "MEGAHORN", "uses": 15154}, {"moveId": "PSYCHIC", "uses": 10871}]}, "moveset": ["CONFUSION", "STOMP", "WILD_CHARGE"], "score": 57.8}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 312, "matchups": [{"opponent": "rilla<PERSON>m", "rating": 816, "opRating": 183}, {"opponent": "thwackey", "rating": 742, "opRating": 257}, {"opponent": "gogoat", "rating": 714, "opRating": 285}, {"opponent": "grotle_shadow", "rating": 680, "opRating": 319}, {"opponent": "lura<PERSON>s", "rating": 675, "opRating": 324}], "counters": [{"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 278}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33891}, {"moveId": "FEINT_ATTACK", "uses": 24409}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20649}, {"moveId": "FOUL_PLAY", "uses": 18854}, {"moveId": "SLUDGE_BOMB", "uses": 18728}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 57.6}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 287, "matchups": [{"opponent": "cacturne", "rating": 726, "opRating": 273}, {"opponent": "vibrava_shadow", "rating": 726, "opRating": 273}, {"opponent": "cacturne_shadow", "rating": 671, "opRating": 328}, {"opponent": "exeggutor_alolan_shadow", "rating": 648, "opRating": 351}, {"opponent": "garcho<PERSON>", "rating": 621, "opRating": 378}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "talonflame", "rating": 148}, {"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "cradily", "rating": 246}, {"opponent": "gligar", "rating": 270}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 47018}, {"moveId": "POUND", "uses": 11282}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 37424}, {"moveId": "HYPER_BEAM", "uses": 11528}, {"moveId": "THUNDERBOLT", "uses": 9338}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 57.6}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 322, "matchups": [{"opponent": "wormadam_plant", "rating": 848, "opRating": 151}, {"opponent": "swadloon", "rating": 764, "opRating": 235}, {"opponent": "abomasnow", "rating": 722, "opRating": 277}, {"opponent": "parasect", "rating": 718, "opRating": 281}, {"opponent": "abomasnow_shadow", "rating": 701, "opRating": 298}], "counters": [{"opponent": "clodsire", "rating": 146}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 152}, {"opponent": "cradily", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 235}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46857}, {"moveId": "ROCK_SMASH", "uses": 11443}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28465}, {"moveId": "BLAST_BURN", "uses": 20513}, {"moveId": "FLAMETHROWER", "uses": 4738}, {"moveId": "SOLAR_BEAM", "uses": 4516}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 57.6}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 338, "matchups": [{"opponent": "ferrothorn", "rating": 956, "opRating": 43}, {"opponent": "wormadam_plant", "rating": 859, "opRating": 140}, {"opponent": "abomasnow_shadow", "rating": 728, "opRating": 271}, {"opponent": "tropius", "rating": 543, "opRating": 456}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 513, "opRating": 486}], "counters": [{"opponent": "talonflame", "rating": 100}, {"opponent": "cradily", "rating": 121}, {"opponent": "gligar", "rating": 125}, {"opponent": "clodsire", "rating": 137}, {"opponent": "jumpluff_shadow", "rating": 192}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36007}, {"moveId": "POISON_JAB", "uses": 22293}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22637}, {"moveId": "SLUDGE_WAVE", "uses": 12268}, {"moveId": "FIRE_BLAST", "uses": 11741}, {"moveId": "DRAGON_PULSE", "uses": 11655}]}, "moveset": ["INCINERATE", "POISON_FANG", "DRAGON_PULSE"], "score": 57.6}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 266, "matchups": [{"opponent": "exploud_shadow", "rating": 620, "opRating": 379}, {"opponent": "sandygast", "rating": 583, "opRating": 416}, {"opponent": "phanpy", "rating": 570, "opRating": 429}, {"opponent": "lilligant", "rating": 550, "opRating": 450}, {"opponent": "sandshrew_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 255}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 47877}, {"moveId": "ZEN_HEADBUTT", "uses": 10423}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 19343}, {"moveId": "TRAILBLAZE", "uses": 18392}, {"moveId": "EARTHQUAKE", "uses": 10412}, {"moveId": "IRON_HEAD", "uses": 10170}]}, "moveset": ["TACKLE", "TRAILBLAZE", "HORN_ATTACK"], "score": 57.6}, {"speciesId": "camerupt_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 318, "matchups": [{"opponent": "wormadam_plant", "rating": 820, "opRating": 180}, {"opponent": "parasect", "rating": 692, "opRating": 308}, {"opponent": "abomasnow", "rating": 592, "opRating": 408}, {"opponent": "chansey", "rating": 580, "opRating": 420}, {"opponent": "abomasnow_shadow", "rating": 540, "opRating": 460}], "counters": [{"opponent": "gligar", "rating": 137}, {"opponent": "clodsire", "rating": 153}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 303}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 33391}, {"moveId": "EMBER", "uses": 19985}, {"moveId": "ROCK_SMASH", "uses": 4928}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 22012}, {"moveId": "EARTH_POWER", "uses": 20130}, {"moveId": "SOLAR_BEAM", "uses": 8741}, {"moveId": "EARTHQUAKE", "uses": 7308}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 57.4}, {"speciesId": "magby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 310, "matchups": [{"opponent": "wormadam_plant", "rating": 762, "opRating": 237}, {"opponent": "abomasnow_shadow", "rating": 683, "opRating": 316}, {"opponent": "lura<PERSON>s", "rating": 662, "opRating": 337}, {"opponent": "abomasnow", "rating": 662, "opRating": 337}, {"opponent": "swadloon", "rating": 625, "opRating": 375}], "counters": [{"opponent": "clodsire", "rating": 45}, {"opponent": "gligar", "rating": 49}, {"opponent": "talonflame", "rating": 81}, {"opponent": "jumpluff_shadow", "rating": 81}, {"opponent": "cradily", "rating": 274}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 30701}, {"moveId": "EMBER", "uses": 27599}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26480}, {"moveId": "BRICK_BREAK", "uses": 18470}, {"moveId": "FLAMETHROWER", "uses": 8299}, {"moveId": "FLAME_BURST", "uses": 5025}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "BRICK_BREAK"], "score": 57.4}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 242, "matchups": [{"opponent": "clodsire", "rating": 601, "opRating": 398}, {"opponent": "steelix", "rating": 594, "opRating": 405}, {"opponent": "gligar", "rating": 586}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 547, "opRating": 452}, {"opponent": "noctowl", "rating": 528, "opRating": 471}], "counters": [{"opponent": "magcargo", "rating": 94}, {"opponent": "furret", "rating": 131}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 149}, {"opponent": "jumpluff_shadow", "rating": 218}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34135}, {"moveId": "MUD_SLAP", "uses": 24165}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19390}, {"moveId": "ICICLE_SPEAR", "uses": 16249}, {"moveId": "STONE_EDGE", "uses": 6192}, {"moveId": "HIGH_HORSEPOWER", "uses": 6092}, {"moveId": "ANCIENT_POWER", "uses": 5552}, {"moveId": "BULLDOZE", "uses": 4962}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 57.4}, {"speciesId": "pumpkaboo_average", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Average)", "rating": 310, "matchups": [{"opponent": "stunfisk", "rating": 690, "opRating": 309}, {"opponent": "munchlax", "rating": 666, "opRating": 333}, {"opponent": "whiscash", "rating": 638, "opRating": 361}, {"opponent": "gastrodon", "rating": 615, "opRating": 384}, {"opponent": "marowak", "rating": 547, "opRating": 452}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 266}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38126}, {"moveId": "RAZOR_LEAF", "uses": 20174}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23619}, {"moveId": "FOUL_PLAY", "uses": 23215}, {"moveId": "SHADOW_SNEAK", "uses": 11398}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 57.4}, {"speciesId": "sandshrew_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 251, "matchups": [{"opponent": "onix", "rating": 724, "opRating": 275}, {"opponent": "nidoqueen", "rating": 629, "opRating": 370}, {"opponent": "steelix_shadow", "rating": 562, "opRating": 437}, {"opponent": "steelix", "rating": 555, "opRating": 444}, {"opponent": "chansey", "rating": 539, "opRating": 460}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 274}, {"opponent": "cradily", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 372}, {"opponent": "clodsire", "rating": 454}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 25318}, {"moveId": "MUD_SHOT", "uses": 22574}, {"moveId": "SCRATCH", "uses": 10389}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21429}, {"moveId": "DIG", "uses": 15317}, {"moveId": "ROCK_SLIDE", "uses": 13869}, {"moveId": "SAND_TOMB", "uses": 7677}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 57.4}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 283, "matchups": [{"opponent": "oricorio_baile", "rating": 574, "opRating": 425}, {"opponent": "tranquill", "rating": 555, "opRating": 444}, {"opponent": "lickitung", "rating": 542, "opRating": 457}, {"opponent": "pidgeotto", "rating": 534, "opRating": 465}, {"opponent": "amoon<PERSON>s", "rating": 506, "opRating": 493}], "counters": [{"opponent": "jumpluff_shadow", "rating": 173}, {"opponent": "talonflame", "rating": 181}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 286}, {"opponent": "clodsire", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 32119}, {"moveId": "POUND", "uses": 26181}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22804}, {"moveId": "DAZZLING_GLEAM", "uses": 13720}, {"moveId": "PSYCHIC", "uses": 11121}, {"moveId": "HYPER_BEAM", "uses": 10695}]}, "moveset": ["ZEN_HEADBUTT", "WILD_CHARGE", "DAZZLING_GLEAM"], "score": 57.1}, {"speciesId": "lampent_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 289, "matchups": [{"opponent": "chandelure_shadow", "rating": 829, "opRating": 170}, {"opponent": "chandelure", "rating": 790, "opRating": 209}, {"opponent": "blaziken", "rating": 604, "opRating": 395}, {"opponent": "swadloon", "rating": 515, "opRating": 484}, {"opponent": "wormadam_plant", "rating": 507, "opRating": 492}], "counters": [{"opponent": "clodsire", "rating": 156}, {"opponent": "gligar", "rating": 167}, {"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "talonflame", "rating": 259}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31026}, {"moveId": "ASTONISH", "uses": 27274}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 26011}, {"moveId": "FLAME_BURST", "uses": 25416}, {"moveId": "HEAT_WAVE", "uses": 6770}, {"moveId": "FRUSTRATION", "uses": 100}]}, "moveset": ["ASTONISH", "ENERGY_BALL", "FLAME_BURST"], "score": 57.1}, {"speciesId": "mudbray", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 291, "matchups": [{"opponent": "geodude_shadow", "rating": 798, "opRating": 201}, {"opponent": "heatran_shadow", "rating": 791, "opRating": 208}, {"opponent": "chandelure_shadow", "rating": 783, "opRating": 216}, {"opponent": "entei_shadow", "rating": 664, "opRating": 335}, {"opponent": "steelix", "rating": 582, "opRating": 417}], "counters": [{"opponent": "jumpluff_shadow", "rating": 52}, {"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 128}, {"opponent": "clodsire", "rating": 456}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43759}, {"moveId": "ROCK_SMASH", "uses": 14541}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27441}, {"moveId": "EARTHQUAKE", "uses": 15916}, {"moveId": "BULLDOZE", "uses": 14906}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTHQUAKE"], "score": 57.1}, {"speciesId": "emboar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 314, "matchups": [{"opponent": "wormadam_plant", "rating": 891, "opRating": 108}, {"opponent": "swadloon", "rating": 789, "opRating": 210}, {"opponent": "abomasnow", "rating": 721, "opRating": 278}, {"opponent": "abomasnow_shadow", "rating": 704, "opRating": 295}, {"opponent": "parasect", "rating": 704, "opRating": 295}], "counters": [{"opponent": "clodsire", "rating": 156}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 170}, {"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 274}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 49797}, {"moveId": "LOW_KICK", "uses": 8503}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24315}, {"moveId": "ROCK_SLIDE", "uses": 12774}, {"moveId": "FOCUS_BLAST", "uses": 12763}, {"moveId": "FLAME_CHARGE", "uses": 6677}, {"moveId": "HEAT_WAVE", "uses": 1876}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 56.9}, {"speciesId": "larve<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 330, "matchups": [{"opponent": "abomasnow", "rating": 763, "opRating": 236}, {"opponent": "abomasnow_shadow", "rating": 744, "opRating": 255}, {"opponent": "gogoat", "rating": 721, "opRating": 278}, {"opponent": "swadloon", "rating": 648, "opRating": 351}, {"opponent": "lura<PERSON>s", "rating": 633, "opRating": 366}], "counters": [{"opponent": "clodsire", "rating": 100}, {"opponent": "talonflame", "rating": 118}, {"opponent": "cradily", "rating": 145}, {"opponent": "jumpluff_shadow", "rating": 205}, {"opponent": "gligar", "rating": 209}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30686}, {"moveId": "BUG_BITE", "uses": 27614}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 29471}, {"moveId": "BUG_BUZZ", "uses": 23365}, {"moveId": "FLAME_WHEEL", "uses": 5446}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BUG_BUZZ"], "score": 56.9}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 310, "matchups": [{"opponent": "gogoat", "rating": 760, "opRating": 239}, {"opponent": "servine_shadow", "rating": 735, "opRating": 264}, {"opponent": "bellossom", "rating": 662, "opRating": 337}, {"opponent": "swampert_shadow", "rating": 630, "opRating": 369}, {"opponent": "quagsire_shadow", "rating": 512, "opRating": 487}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 310}, {"opponent": "cradily", "rating": 340}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44257}, {"moveId": "RAZOR_LEAF", "uses": 14043}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30490}, {"moveId": "SLUDGE_BOMB", "uses": 20164}, {"moveId": "PETAL_BLIZZARD", "uses": 4240}, {"moveId": "SOLAR_BEAM", "uses": 3520}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 56.9}, {"speciesId": "ponyta", "speciesName": "Ponyta", "rating": 311, "matchups": [{"opponent": "abomasnow_shadow", "rating": 724, "opRating": 275}, {"opponent": "parasect", "rating": 706, "opRating": 293}, {"opponent": "swadloon", "rating": 702, "opRating": 297}, {"opponent": "abomasnow", "rating": 676, "opRating": 323}, {"opponent": "<PERSON><PERSON>e", "rating": 676, "opRating": 323}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 160}, {"opponent": "clodsire", "rating": 271}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38843}, {"moveId": "TACKLE", "uses": 19457}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23977}, {"moveId": "STOMP", "uses": 19067}, {"moveId": "FIRE_BLAST", "uses": 10746}, {"moveId": "FLAME_WHEEL", "uses": 4547}]}, "moveset": ["EMBER", "FLAME_CHARGE", "STOMP"], "score": 56.7}, {"speciesId": "appletun", "speciesName": "Appletun", "rating": 306, "matchups": [{"opponent": "whiscash", "rating": 686, "opRating": 313}, {"opponent": "<PERSON><PERSON>e", "rating": 605, "opRating": 394}, {"opponent": "gastrodon", "rating": 602, "opRating": 397}, {"opponent": "marowak", "rating": 593, "opRating": 406}, {"opponent": "quagsire_shadow", "rating": 546, "opRating": 453}], "counters": [{"opponent": "jumpluff_shadow", "rating": 114}, {"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 175}, {"opponent": "gligar", "rating": 190}, {"opponent": "cradily", "rating": 208}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30501}, {"moveId": "ASTONISH", "uses": 27799}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23428}, {"moveId": "SEED_BOMB", "uses": 18671}, {"moveId": "ENERGY_BALL", "uses": 8383}, {"moveId": "DRAGON_PULSE", "uses": 7801}]}, "moveset": ["ASTONISH", "SEED_BOMB", "OUTRAGE"], "score": 56.4}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 321, "matchups": [{"opponent": "breloom", "rating": 838, "opRating": 161}, {"opponent": "abomasnow", "rating": 725, "opRating": 274}, {"opponent": "parasect", "rating": 720, "opRating": 279}, {"opponent": "abomasnow_shadow", "rating": 672, "opRating": 327}, {"opponent": "munchlax", "rating": 612, "opRating": 387}], "counters": [{"opponent": "gligar", "rating": 129}, {"opponent": "clodsire", "rating": 144}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 177}, {"opponent": "jumpluff_shadow", "rating": 284}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27060}, {"moveId": "HEX", "uses": 16900}, {"moveId": "FIRE_SPIN", "uses": 14417}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16598}, {"moveId": "SHADOW_BALL", "uses": 13642}, {"moveId": "OVERHEAT", "uses": 12839}, {"moveId": "ENERGY_BALL", "uses": 10831}, {"moveId": "POLTERGEIST", "uses": 4440}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "SHADOW_BALL"], "score": 56.4}, {"speciesId": "charm<PERSON>on_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 322, "matchups": [{"opponent": "wormadam_plant", "rating": 789, "opRating": 210}, {"opponent": "abomasnow_shadow", "rating": 742, "opRating": 257}, {"opponent": "parasect", "rating": 722, "opRating": 277}, {"opponent": "<PERSON><PERSON>e", "rating": 656, "opRating": 343}, {"opponent": "tropius", "rating": 601, "opRating": 398}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 145}, {"opponent": "jumpluff_shadow", "rating": 251}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25941}, {"moveId": "FIRE_FANG", "uses": 22513}, {"moveId": "SCRATCH", "uses": 9830}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38685}, {"moveId": "FLAMETHROWER", "uses": 12211}, {"moveId": "FLAME_BURST", "uses": 7493}, {"moveId": "FRUSTRATION", "uses": 12}]}, "moveset": ["EMBER", "FIRE_PUNCH", "FLAMETHROWER"], "score": 56.4}, {"speciesId": "rufflet", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 310, "matchups": [{"opponent": "swadloon", "rating": 621, "opRating": 378}, {"opponent": "tropius", "rating": 605, "opRating": 394}, {"opponent": "gastrodon", "rating": 572, "opRating": 427}, {"opponent": "farfetchd", "rating": 532, "opRating": 467}, {"opponent": "noctowl", "rating": 503, "opRating": 496}], "counters": [{"opponent": "cradily", "rating": 111}, {"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 134}, {"opponent": "gligar", "rating": 202}, {"opponent": "jumpluff_shadow", "rating": 254}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 39914}, {"moveId": "PECK", "uses": 18386}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18554}, {"moveId": "FLY", "uses": 15693}, {"moveId": "ROCK_TOMB", "uses": 12134}, {"moveId": "AERIAL_ACE", "uses": 11976}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "FLY"], "score": 56.4}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 324, "matchups": [{"opponent": "wormadam_plant", "rating": 824, "opRating": 175}, {"opponent": "swadloon", "rating": 745, "opRating": 254}, {"opponent": "abomasnow_shadow", "rating": 722, "opRating": 277}, {"opponent": "bellossom", "rating": 648, "opRating": 351}, {"opponent": "tropius", "rating": 587, "opRating": 412}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "cradily", "rating": 128}, {"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 199}, {"opponent": "gligar", "rating": 209}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 29623}, {"moveId": "HEX", "uses": 28677}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 16915}, {"moveId": "WILD_CHARGE", "uses": 14260}, {"moveId": "NIGHT_SHADE", "uses": 11902}, {"moveId": "OVERHEAT", "uses": 9780}, {"moveId": "SHADOW_BALL", "uses": 5474}]}, "moveset": ["EMBER", "FIRE_PUNCH", "WILD_CHARGE"], "score": 56.4}, {"speciesId": "go<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 303, "matchups": [{"opponent": "blazi<PERSON>_shadow", "rating": 875, "opRating": 124}, {"opponent": "heatran_shadow", "rating": 787, "opRating": 212}, {"opponent": "geodude_shadow", "rating": 776, "opRating": 223}, {"opponent": "steelix_shadow", "rating": 730, "opRating": 269}, {"opponent": "steelix", "rating": 609, "opRating": 390}], "counters": [{"opponent": "jumpluff_shadow", "rating": 52}, {"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 114}, {"opponent": "clodsire", "rating": 430}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31319}, {"moveId": "ASTONISH", "uses": 26981}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 25715}, {"moveId": "BRICK_BREAK", "uses": 17223}, {"moveId": "RETURN", "uses": 7725}, {"moveId": "NIGHT_SHADE", "uses": 7622}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "BRICK_BREAK"], "score": 56.2}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 276, "matchups": [{"opponent": "onix", "rating": 725, "opRating": 274}, {"opponent": "cradily", "rating": 542}, {"opponent": "ferrothorn", "rating": 539, "opRating": 460}, {"opponent": "steelix_shadow", "rating": 539, "opRating": 460}, {"opponent": "lickitung", "rating": 503, "opRating": 496}], "counters": [{"opponent": "diggersby", "rating": 71}, {"opponent": "jumpluff_shadow", "rating": 81}, {"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 271}, {"opponent": "gligar", "rating": 305}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 31122}, {"moveId": "TACKLE", "uses": 20397}, {"moveId": "LOW_KICK", "uses": 6706}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 29074}, {"moveId": "STOMP", "uses": 15709}, {"moveId": "PAYBACK", "uses": 11494}, {"moveId": "DRAIN_PUNCH", "uses": 2023}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "STOMP"], "score": 56}, {"speciesId": "camerupt", "speciesName": "Camerupt", "rating": 299, "matchups": [{"opponent": "parasect", "rating": 760, "opRating": 240}, {"opponent": "breloom", "rating": 679, "opRating": 320}, {"opponent": "abomasnow", "rating": 620, "opRating": 380}, {"opponent": "abomasnow_shadow", "rating": 612, "opRating": 388}, {"opponent": "steelix", "rating": 584, "opRating": 416}], "counters": [{"opponent": "clodsire", "rating": 132}, {"opponent": "talonflame", "rating": 137}, {"opponent": "gligar", "rating": 171}, {"opponent": "cradily", "rating": 263}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32093}, {"moveId": "EMBER", "uses": 20811}, {"moveId": "ROCK_SMASH", "uses": 5446}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 18576}, {"moveId": "EARTH_POWER", "uses": 17448}, {"moveId": "RETURN", "uses": 8518}, {"moveId": "SOLAR_BEAM", "uses": 7571}, {"moveId": "EARTHQUAKE", "uses": 6375}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 56}, {"speciesId": "cubone", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 284, "matchups": [{"opponent": "nidoqueen", "rating": 712, "opRating": 287}, {"opponent": "steelix", "rating": 673, "opRating": 326}, {"opponent": "steelix_shadow", "rating": 618, "opRating": 381}, {"opponent": "litleo", "rating": 578, "opRating": 421}, {"opponent": "arcanine", "rating": 578, "opRating": 421}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 77}, {"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 263}, {"opponent": "clodsire", "rating": 413}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43906}, {"moveId": "ROCK_SMASH", "uses": 14394}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 34871}, {"moveId": "RETURN", "uses": 11260}, {"moveId": "DIG", "uses": 6665}, {"moveId": "BULLDOZE", "uses": 5482}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "RETURN"], "score": 56}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 281, "matchups": [{"opponent": "lilligant", "rating": 595, "opRating": 404}, {"opponent": "lampent", "rating": 544, "opRating": 455}, {"opponent": "salazzle", "rating": 529, "opRating": 470}, {"opponent": "ceruledge", "rating": 525, "opRating": 474}, {"opponent": "run<PERSON><PERSON>", "rating": 522, "opRating": 477}], "counters": [{"opponent": "talonflame", "rating": 48}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "gligar", "rating": 183}, {"opponent": "clodsire", "rating": 247}, {"opponent": "cradily", "rating": 260}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 25763}, {"moveId": "MUD_SHOT", "uses": 25498}, {"moveId": "TAKE_DOWN", "uses": 7009}], "chargedMoves": [{"moveId": "SWIFT", "uses": 32162}, {"moveId": "CRUNCH", "uses": 18637}, {"moveId": "PLAY_ROUGH", "uses": 7568}]}, "moveset": ["MUD_SHOT", "SWIFT", "CRUNCH"], "score": 56}, {"speciesId": "pumpkaboo_small", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Small)", "rating": 299, "matchups": [{"opponent": "stunfisk", "rating": 672, "opRating": 327}, {"opponent": "munchlax", "rating": 647, "opRating": 352}, {"opponent": "whiscash", "rating": 621, "opRating": 378}, {"opponent": "gastrodon", "rating": 592, "opRating": 407}, {"opponent": "marowak", "rating": 521, "opRating": 478}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 286}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38198}, {"moveId": "RAZOR_LEAF", "uses": 20102}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23636}, {"moveId": "FOUL_PLAY", "uses": 23204}, {"moveId": "SHADOW_SNEAK", "uses": 11397}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 55.8}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 295, "matchups": [{"opponent": "whiscash", "rating": 698, "opRating": 301}, {"opponent": "munchlax", "rating": 637, "opRating": 362}, {"opponent": "marowak", "rating": 614, "opRating": 385}, {"opponent": "gastrodon", "rating": 603, "opRating": 396}, {"opponent": "quagsire_shadow", "rating": 545, "opRating": 454}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 194}, {"opponent": "cradily", "rating": 322}, {"opponent": "clodsire", "rating": 360}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 29196}, {"moveId": "SHADOW_CLAW", "uses": 29104}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 21436}, {"moveId": "SEED_BOMB", "uses": 18985}, {"moveId": "SHADOW_BALL", "uses": 17833}]}, "moveset": ["SHADOW_CLAW", "SEED_BOMB", "SHADOW_BALL"], "score": 55.8}, {"speciesId": "unfezant_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 301, "matchups": [{"opponent": "breloom", "rating": 672, "opRating": 327}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 672, "opRating": 327}, {"opponent": "parasect", "rating": 618, "opRating": 381}, {"opponent": "blazi<PERSON>_shadow", "rating": 598, "opRating": 401}, {"opponent": "swadloon", "rating": 524, "opRating": 475}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 203}, {"opponent": "gligar", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 245}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32702}, {"moveId": "STEEL_WING", "uses": 25598}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33535}, {"moveId": "HYPER_BEAM", "uses": 17238}, {"moveId": "HEAT_WAVE", "uses": 7454}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 55.8}, {"speciesId": "aipom", "speciesName": "Aipom", "rating": 264, "matchups": [{"opponent": "pumpkaboo_super", "rating": 607, "opRating": 392}, {"opponent": "pumpkaboo_large", "rating": 603, "opRating": 396}, {"opponent": "thwackey", "rating": 566, "opRating": 433}, {"opponent": "rilla<PERSON>m", "rating": 566, "opRating": 433}, {"opponent": "gourgeist_super", "rating": 514, "opRating": 485}], "counters": [{"opponent": "talonflame", "rating": 137}, {"opponent": "clodsire", "rating": 283}, {"opponent": "cradily", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 320}, {"opponent": "gligar", "rating": 332}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35397}, {"moveId": "SCRATCH", "uses": 22903}], "chargedMoves": [{"moveId": "SWIFT", "uses": 26862}, {"moveId": "AERIAL_ACE", "uses": 17165}, {"moveId": "LOW_SWEEP", "uses": 9665}, {"moveId": "RETURN", "uses": 4666}]}, "moveset": ["ASTONISH", "SWIFT", "AERIAL_ACE"], "score": 55.5}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 315, "matchups": [{"opponent": "abomasnow_shadow", "rating": 768, "opRating": 231}, {"opponent": "ferrothorn", "rating": 763, "opRating": 236}, {"opponent": "abomasnow", "rating": 686, "opRating": 313}, {"opponent": "swadloon", "rating": 554, "opRating": 445}, {"opponent": "tropius", "rating": 536, "opRating": 463}], "counters": [{"opponent": "clodsire", "rating": 122}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 152}, {"opponent": "cradily", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 323}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39464}, {"moveId": "SCRATCH", "uses": 11744}, {"moveId": "ZEN_HEADBUTT", "uses": 7111}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 20752}, {"moveId": "MYSTICAL_FIRE", "uses": 14913}, {"moveId": "PSYCHIC", "uses": 9585}, {"moveId": "FLAME_CHARGE", "uses": 5689}, {"moveId": "FLAMETHROWER", "uses": 4804}, {"moveId": "FIRE_BLAST", "uses": 2684}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 55.3}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 300, "matchups": [{"opponent": "cacturne", "rating": 723, "opRating": 276}, {"opponent": "cacturne_shadow", "rating": 710, "opRating": 289}, {"opponent": "breloom", "rating": 671, "opRating": 328}, {"opponent": "exeggutor_alolan_shadow", "rating": 649, "opRating": 350}, {"opponent": "garcho<PERSON>_shadow", "rating": 649, "opRating": 350}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 148}, {"opponent": "gligar", "rating": 152}, {"opponent": "cradily", "rating": 173}, {"opponent": "jumpluff_shadow", "rating": 179}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 7381}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4216}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3666}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3529}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3304}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3285}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3245}, {"moveId": "CHARM", "uses": 3237}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2950}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2920}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2849}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2820}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2792}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2725}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2582}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2555}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2324}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2111}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 26853}, {"moveId": "HYPER_BEAM", "uses": 20673}, {"moveId": "SOLAR_BEAM", "uses": 10824}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 55.3}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 310, "matchups": [{"opponent": "wormadam_plant", "rating": 833, "opRating": 166}, {"opponent": "abomasnow_shadow", "rating": 684, "opRating": 315}, {"opponent": "abomasnow", "rating": 666, "opRating": 333}, {"opponent": "lura<PERSON>s", "rating": 662, "opRating": 337}, {"opponent": "ninetales", "rating": 552, "opRating": 447}], "counters": [{"opponent": "gligar", "rating": 49}, {"opponent": "clodsire", "rating": 52}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 356}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 31423}, {"moveId": "EMBER", "uses": 26877}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23004}, {"moveId": "SCORCHING_SANDS", "uses": 16728}, {"moveId": "RETURN", "uses": 7395}, {"moveId": "FLAMETHROWER", "uses": 7179}, {"moveId": "FIRE_BLAST", "uses": 3906}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 55.1}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 318, "matchups": [{"opponent": "wormadam_plant", "rating": 807, "opRating": 192}, {"opponent": "abomasnow_shadow", "rating": 735, "opRating": 264}, {"opponent": "abomasnow", "rating": 735, "opRating": 264}, {"opponent": "<PERSON><PERSON>e", "rating": 649, "opRating": 350}, {"opponent": "tropius", "rating": 559, "opRating": 440}], "counters": [{"opponent": "clodsire", "rating": 103}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 128}, {"opponent": "gligar", "rating": 141}, {"opponent": "jumpluff_shadow", "rating": 205}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27065}, {"moveId": "EMBER", "uses": 15788}, {"moveId": "SHADOW_CLAW", "uses": 15445}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 28455}, {"moveId": "THUNDER_PUNCH", "uses": 12323}, {"moveId": "SOLAR_BEAM", "uses": 7885}, {"moveId": "OVERHEAT", "uses": 5999}, {"moveId": "FIRE_BLAST", "uses": 3516}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 55.1}, {"speciesId": "arboliva", "speciesName": "Arboliva", "rating": 286, "matchups": [{"opponent": "whiscash", "rating": 726, "opRating": 273}, {"opponent": "quagsire", "rating": 639, "opRating": 360}, {"opponent": "gastrodon", "rating": 630, "opRating": 369}, {"opponent": "quagsire_shadow", "rating": 569, "opRating": 430}, {"opponent": "swampert_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "gligar", "rating": 137}, {"opponent": "talonflame", "rating": 159}, {"opponent": "clodsire", "rating": 185}, {"opponent": "cradily", "rating": 291}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 24246}, {"moveId": "TACKLE", "uses": 21475}, {"moveId": "RAZOR_LEAF", "uses": 12574}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 26398}, {"moveId": "EARTH_POWER", "uses": 16009}, {"moveId": "SEED_BOMB", "uses": 8446}, {"moveId": "ENERGY_BALL", "uses": 7512}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 54.8}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 311, "matchups": [{"opponent": "wormadam_plant", "rating": 756, "opRating": 243}, {"opponent": "abomasnow_shadow", "rating": 730, "opRating": 269}, {"opponent": "ferrothorn", "rating": 705, "opRating": 294}, {"opponent": "parasect", "rating": 705, "opRating": 294}, {"opponent": "abomasnow", "rating": 670, "opRating": 329}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 137}, {"opponent": "cradily", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 248}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38369}, {"moveId": "TACKLE", "uses": 19931}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 27987}, {"moveId": "FOCUS_BLAST", "uses": 18577}, {"moveId": "FLAMETHROWER", "uses": 11648}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 54.8}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 301, "matchups": [{"opponent": "abomasnow_shadow", "rating": 739, "opRating": 260}, {"opponent": "ferrothorn", "rating": 726, "opRating": 273}, {"opponent": "parasect", "rating": 710, "opRating": 289}, {"opponent": "abomasnow", "rating": 680, "opRating": 319}, {"opponent": "swadloon", "rating": 642, "opRating": 357}], "counters": [{"opponent": "clodsire", "rating": 122}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 208}, {"opponent": "gligar", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 245}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45851}, {"moveId": "ROCK_SMASH", "uses": 12449}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28476}, {"moveId": "BLAST_BURN", "uses": 20533}, {"moveId": "FLAMETHROWER", "uses": 4735}, {"moveId": "SOLAR_BEAM", "uses": 4505}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 54.8}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 289, "matchups": [{"opponent": "parasect", "rating": 719, "opRating": 280}, {"opponent": "bellossom", "rating": 705, "opRating": 294}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "tropius", "rating": 593, "opRating": 406}, {"opponent": "jumpluff_shadow", "rating": 557, "opRating": 442}], "counters": [{"opponent": "clodsire", "rating": 36}, {"opponent": "magcargo", "rating": 64}, {"opponent": "gligar", "rating": 87}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 302}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 27253}, {"moveId": "POISON_JAB", "uses": 22957}, {"moveId": "BITE", "uses": 8088}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 15732}, {"moveId": "EARTH_POWER", "uses": 15559}, {"moveId": "STONE_EDGE", "uses": 12781}, {"moveId": "SLUDGE_WAVE", "uses": 8475}, {"moveId": "EARTHQUAKE", "uses": 5700}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "STONE_EDGE", "POISON_FANG"], "score": 54.8}, {"speciesId": "pidgeott<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 321, "matchups": [{"opponent": "gourgeist_large", "rating": 778, "opRating": 221}, {"opponent": "serperior_shadow", "rating": 670, "opRating": 329}, {"opponent": "swadloon", "rating": 656, "opRating": 343}, {"opponent": "servine_shadow", "rating": 636, "opRating": 363}, {"opponent": "serperior", "rating": 632, "opRating": 367}], "counters": [{"opponent": "cradily", "rating": 86}, {"opponent": "clodsire", "rating": 98}, {"opponent": "talonflame", "rating": 114}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "gligar", "rating": 282}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 33638}, {"moveId": "STEEL_WING", "uses": 24662}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 36676}, {"moveId": "AERIAL_ACE", "uses": 13864}, {"moveId": "TWISTER", "uses": 7740}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WING_ATTACK", "AIR_CUTTER", "AERIAL_ACE"], "score": 54.8}, {"speciesId": "tsar<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 278, "matchups": [{"opponent": "whiscash", "rating": 784, "opRating": 215}, {"opponent": "gastrodon", "rating": 665, "opRating": 334}, {"opponent": "quagsire", "rating": 633, "opRating": 366}, {"opponent": "quagsire_shadow", "rating": 559, "opRating": 440}, {"opponent": "swampert_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "gligar", "rating": 137}, {"opponent": "cradily", "rating": 142}, {"opponent": "talonflame", "rating": 244}, {"opponent": "clodsire", "rating": 372}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 32032}, {"moveId": "CHARM", "uses": 14096}, {"moveId": "RAZOR_LEAF", "uses": 12171}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 18824}, {"moveId": "GRASS_KNOT", "uses": 12206}, {"moveId": "HIGH_JUMP_KICK", "uses": 11840}, {"moveId": "STOMP", "uses": 8406}, {"moveId": "ENERGY_BALL", "uses": 4413}, {"moveId": "DRAINING_KISS", "uses": 2666}]}, "moveset": ["MAGICAL_LEAF", "TRIPLE_AXEL", "HIGH_JUMP_KICK"], "score": 54.8}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 311, "matchups": [{"opponent": "thwackey", "rating": 809, "opRating": 190}, {"opponent": "gogoat", "rating": 770, "opRating": 229}, {"opponent": "<PERSON><PERSON>e", "rating": 671, "opRating": 328}, {"opponent": "lura<PERSON>s", "rating": 591, "opRating": 408}, {"opponent": "bellossom", "rating": 587, "opRating": 412}], "counters": [{"opponent": "clodsire", "rating": 52}, {"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 163}, {"opponent": "talonflame", "rating": 185}, {"opponent": "jumpluff_shadow", "rating": 232}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 30614}, {"moveId": "MAGICAL_LEAF", "uses": 20001}, {"moveId": "RAZOR_LEAF", "uses": 7693}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30720}, {"moveId": "SLUDGE_BOMB", "uses": 15676}, {"moveId": "LEAF_TORNADO", "uses": 5874}, {"moveId": "ACID_SPRAY", "uses": 3465}, {"moveId": "SOLAR_BEAM", "uses": 2546}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 54.8}, {"speciesId": "girafarig_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 281, "matchups": [{"opponent": "blazi<PERSON>_shadow", "rating": 700, "opRating": 300}, {"opponent": "chansey", "rating": 634, "opRating": 365}, {"opponent": "snorlax", "rating": 623, "opRating": 376}, {"opponent": "unfezant_shadow", "rating": 623, "opRating": 376}, {"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 523, "opRating": 476}], "counters": [{"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 197}, {"opponent": "talonflame", "rating": 203}, {"opponent": "clodsire", "rating": 276}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 21955}, {"moveId": "CONFUSION", "uses": 21429}, {"moveId": "TACKLE", "uses": 14935}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 26053}, {"moveId": "TRAILBLAZE", "uses": 16595}, {"moveId": "THUNDERBOLT", "uses": 7517}, {"moveId": "PSYCHIC", "uses": 4965}, {"moveId": "MIRROR_COAT", "uses": 3102}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 54.6}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 324, "matchups": [{"opponent": "gogoat", "rating": 822, "opRating": 177}, {"opponent": "gastrodon", "rating": 760, "opRating": 239}, {"opponent": "whiscash", "rating": 756, "opRating": 243}, {"opponent": "claydol", "rating": 520, "opRating": 479}, {"opponent": "swampert_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "clodsire", "rating": 74}, {"opponent": "gligar", "rating": 80}, {"opponent": "cradily", "rating": 86}, {"opponent": "talonflame", "rating": 96}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25564}, {"moveId": "BUG_BITE", "uses": 23424}, {"moveId": "RAZOR_LEAF", "uses": 9309}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30258}, {"moveId": "X_SCISSOR", "uses": 18642}, {"moveId": "SILVER_WIND", "uses": 5528}, {"moveId": "LEAF_STORM", "uses": 3708}]}, "moveset": ["SHADOW_CLAW", "LEAF_BLADE", "X_SCISSOR"], "score": 54.6}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 299, "matchups": [{"opponent": "wormadam_plant", "rating": 870, "opRating": 129}, {"opponent": "swadloon", "rating": 792, "opRating": 207}, {"opponent": "abomasnow_shadow", "rating": 734, "opRating": 265}, {"opponent": "parasect", "rating": 724, "opRating": 275}, {"opponent": "abomasnow", "rating": 683, "opRating": 316}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 137}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 218}, {"opponent": "jumpluff_shadow", "rating": 228}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 48939}, {"moveId": "LOW_KICK", "uses": 9361}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24334}, {"moveId": "FOCUS_BLAST", "uses": 12778}, {"moveId": "ROCK_SLIDE", "uses": 12766}, {"moveId": "FLAME_CHARGE", "uses": 6663}, {"moveId": "HEAT_WAVE", "uses": 1899}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 54.4}, {"speciesId": "phanpy", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 257, "matchups": [{"opponent": "geodude", "rating": 637, "opRating": 362}, {"opponent": "onix", "rating": 588, "opRating": 411}, {"opponent": "geodude_shadow", "rating": 572, "opRating": 427}, {"opponent": "onix_shadow", "rating": 569, "opRating": 430}, {"opponent": "chansey", "rating": 510, "opRating": 489}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "cradily", "rating": 239}, {"opponent": "gligar", "rating": 282}, {"opponent": "clodsire", "rating": 379}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 38428}, {"moveId": "ROCK_SMASH", "uses": 19872}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 15583}, {"moveId": "BODY_SLAM", "uses": 14816}, {"moveId": "ROCK_SLIDE", "uses": 13949}, {"moveId": "BULLDOZE", "uses": 10019}, {"moveId": "RETURN", "uses": 3918}]}, "moveset": ["TACKLE", "TRAILBLAZE", "BODY_SLAM"], "score": 54.4}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 277, "matchups": [{"opponent": "steelix_shadow", "rating": 753, "opRating": 246}, {"opponent": "onix", "rating": 693, "opRating": 306}, {"opponent": "steelix", "rating": 630, "opRating": 369}, {"opponent": "arcanine", "rating": 566, "opRating": 433}, {"opponent": "litleo", "rating": 563, "opRating": 436}], "counters": [{"opponent": "cradily", "rating": 100}, {"opponent": "talonflame", "rating": 114}, {"opponent": "gligar", "rating": 213}, {"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "clodsire", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 16417}, {"moveId": "ICE_FANG", "uses": 15911}, {"moveId": "FIRE_FANG", "uses": 11566}, {"moveId": "THUNDER_FANG", "uses": 8959}, {"moveId": "BITE", "uses": 5466}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 18171}, {"moveId": "SCORCHING_SANDS", "uses": 13825}, {"moveId": "BODY_SLAM", "uses": 12048}, {"moveId": "EARTH_POWER", "uses": 5375}, {"moveId": "STONE_EDGE", "uses": 4927}, {"moveId": "EARTHQUAKE", "uses": 4029}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 54.2}, {"speciesId": "marshtomp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 257, "matchups": [{"opponent": "castform_sunny", "rating": 701, "opRating": 298}, {"opponent": "ninetales", "rating": 697, "opRating": 302}, {"opponent": "magcargo", "rating": 658, "opRating": 341}, {"opponent": "litleo", "rating": 597, "opRating": 402}, {"opponent": "marowak_alolan", "rating": 557, "opRating": 442}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 70}, {"opponent": "gligar", "rating": 80}, {"opponent": "cradily", "rating": 86}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30838}, {"moveId": "WATER_GUN", "uses": 27462}], "chargedMoves": [{"moveId": "SURF", "uses": 23063}, {"moveId": "SLUDGE", "uses": 17960}, {"moveId": "MUD_BOMB", "uses": 17223}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 54.2}, {"speciesId": "linoone_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 270, "matchups": [{"opponent": "girafarig", "rating": 694, "opRating": 305}, {"opponent": "ponyta", "rating": 587, "opRating": 412}, {"opponent": "armarouge", "rating": 558, "opRating": 441}, {"opponent": "oranguru", "rating": 542, "opRating": 457}, {"opponent": "ceruledge", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 77}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "gligar", "rating": 213}, {"opponent": "cradily", "rating": 246}, {"opponent": "clodsire", "rating": 257}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36262}, {"moveId": "LICK", "uses": 22038}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29580}, {"moveId": "DIG", "uses": 13623}, {"moveId": "GUNK_SHOT", "uses": 7691}, {"moveId": "RETURN", "uses": 7394}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 53.9}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 299, "matchups": [{"opponent": "abomasnow_shadow", "rating": 746, "opRating": 253}, {"opponent": "ferrothorn", "rating": 730, "opRating": 269}, {"opponent": "parasect", "rating": 718, "opRating": 281}, {"opponent": "abomasnow", "rating": 690, "opRating": 309}, {"opponent": "swadloon", "rating": 579, "opRating": 420}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 156}, {"opponent": "gligar", "rating": 171}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 40466}, {"moveId": "BITE", "uses": 17834}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 27911}, {"moveId": "FLAMETHROWER", "uses": 23895}, {"moveId": "FIRE_BLAST", "uses": 6496}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 53.9}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 283, "matchups": [{"opponent": "pumpkaboo_large", "rating": 628, "opRating": 371}, {"opponent": "stunfisk_galarian", "rating": 574, "opRating": 425}, {"opponent": "gourgeist_super", "rating": 558, "opRating": 441}, {"opponent": "grotle", "rating": 532, "opRating": 467}, {"opponent": "cradily_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 187}, {"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 293}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 47020}, {"moveId": "ZEN_HEADBUTT", "uses": 9734}, {"moveId": "YAWN", "uses": 1611}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16281}, {"moveId": "BODY_SLAM", "uses": 15939}, {"moveId": "OUTRAGE", "uses": 7605}, {"moveId": "EARTHQUAKE", "uses": 6169}, {"moveId": "HEAVY_SLAM", "uses": 5724}, {"moveId": "SKULL_BASH", "uses": 3911}, {"moveId": "HYPER_BEAM", "uses": 2620}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 53.9}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 301, "matchups": [{"opponent": "wormadam_plant", "rating": 793, "opRating": 206}, {"opponent": "abomasnow", "rating": 739, "opRating": 260}, {"opponent": "abomasnow_shadow", "rating": 717, "opRating": 282}, {"opponent": "swadloon", "rating": 644, "opRating": 355}, {"opponent": "parasect", "rating": 644, "opRating": 355}], "counters": [{"opponent": "gligar", "rating": 129}, {"opponent": "clodsire", "rating": 144}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 177}, {"opponent": "jumpluff_shadow", "rating": 284}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32462}, {"moveId": "FIRE_FANG", "uses": 15778}, {"moveId": "TACKLE", "uses": 10052}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17743}, {"moveId": "ROCK_SLIDE", "uses": 17405}, {"moveId": "FOCUS_BLAST", "uses": 13305}, {"moveId": "PSYCHIC", "uses": 9873}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 53.7}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 309, "matchups": [{"opponent": "abomasnow_shadow", "rating": 757, "opRating": 242}, {"opponent": "parasect", "rating": 731, "opRating": 268}, {"opponent": "ferrothorn", "rating": 697, "opRating": 302}, {"opponent": "abomasnow", "rating": 671, "opRating": 328}, {"opponent": "swadloon", "rating": 597, "opRating": 402}], "counters": [{"opponent": "clodsire", "rating": 98}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 128}, {"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 294}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38467}, {"moveId": "LICK", "uses": 19833}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 27228}, {"moveId": "THUNDER_PUNCH", "uses": 18852}, {"moveId": "POWER_UP_PUNCH", "uses": 12238}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 53.7}, {"speciesId": "houndour_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 296, "matchups": [{"opponent": "abomasnow", "rating": 662, "opRating": 337}, {"opponent": "ferrothorn", "rating": 645, "opRating": 354}, {"opponent": "abomasnow_shadow", "rating": 641, "opRating": 358}, {"opponent": "chansey", "rating": 616, "opRating": 383}, {"opponent": "wormadam_plant", "rating": 600, "opRating": 400}], "counters": [{"opponent": "clodsire", "rating": 134}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 170}, {"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 274}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 35993}, {"moveId": "FEINT_ATTACK", "uses": 22307}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28164}, {"moveId": "FLAMETHROWER", "uses": 19175}, {"moveId": "DARK_PULSE", "uses": 10978}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 53.7}, {"speciesId": "raticate_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Al<PERSON><PERSON>) (Shadow)", "rating": 279, "matchups": [{"opponent": "oranguru", "rating": 623, "opRating": 376}, {"opponent": "decid<PERSON><PERSON>", "rating": 613, "opRating": 386}, {"opponent": "lickitung", "rating": 593, "opRating": 406}, {"opponent": "noctowl", "rating": 546, "opRating": 453}, {"opponent": "run<PERSON><PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 158}, {"opponent": "cradily", "rating": 302}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 36978}, {"moveId": "BITE", "uses": 21322}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31724}, {"moveId": "HYPER_FANG", "uses": 20882}, {"moveId": "HYPER_BEAM", "uses": 5418}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_BEAM"], "score": 53.7}, {"speciesId": "torracat", "speciesName": "Torracat", "rating": 302, "matchups": [{"opponent": "wormadam_plant", "rating": 788, "opRating": 211}, {"opponent": "abomasnow_shadow", "rating": 751, "opRating": 248}, {"opponent": "parasect", "rating": 666, "opRating": 333}, {"opponent": "abomasnow", "rating": 651, "opRating": 348}, {"opponent": "swadloon", "rating": 592, "opRating": 407}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 164}, {"opponent": "jumpluff_shadow", "rating": 264}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41329}, {"moveId": "BITE", "uses": 16971}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24316}, {"moveId": "FLAME_CHARGE", "uses": 24028}, {"moveId": "FLAMETHROWER", "uses": 9997}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 53.7}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 299, "matchups": [{"opponent": "parasect", "rating": 711, "opRating": 288}, {"opponent": "breloom", "rating": 685, "opRating": 314}, {"opponent": "lickitung", "rating": 585, "opRating": 414}, {"opponent": "swadloon", "rating": 555, "opRating": 444}, {"opponent": "gastrodon", "rating": 537, "opRating": 462}], "counters": [{"opponent": "clodsire", "rating": 96}, {"opponent": "cradily", "rating": 121}, {"opponent": "talonflame", "rating": 170}, {"opponent": "gligar", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 196}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32764}, {"moveId": "STEEL_WING", "uses": 25536}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 17242}, {"moveId": "BRAVE_BIRD", "uses": 17218}, {"moveId": "FLY", "uses": 14611}, {"moveId": "ROCK_SLIDE", "uses": 7465}, {"moveId": "HEAT_WAVE", "uses": 1598}]}, "moveset": ["AIR_SLASH", "FLY", "CLOSE_COMBAT"], "score": 53.5}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 294, "matchups": [{"opponent": "quagsire_shadow", "rating": 766, "opRating": 233}, {"opponent": "whiscash", "rating": 741, "opRating": 258}, {"opponent": "dunsparce", "rating": 633, "opRating": 366}, {"opponent": "marowak", "rating": 556, "opRating": 443}, {"opponent": "swampert_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "talonflame", "rating": 62}, {"opponent": "gligar", "rating": 80}, {"opponent": "cradily", "rating": 225}, {"opponent": "clodsire", "rating": 269}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 31329}, {"moveId": "MAGICAL_LEAF", "uses": 26971}], "chargedMoves": [{"moveId": "AURA_SPHERE", "uses": 15833}, {"moveId": "TRAILBLAZE", "uses": 15379}, {"moveId": "AERIAL_ACE", "uses": 13774}, {"moveId": "NIGHT_SHADE", "uses": 8811}, {"moveId": "ENERGY_BALL", "uses": 4465}]}, "moveset": ["MAGICAL_LEAF", "AERIAL_ACE", "AURA_SPHERE"], "score": 53.5}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 272, "matchups": [{"opponent": "whiscash", "rating": 764, "opRating": 235}, {"opponent": "marowak", "rating": 669, "opRating": 330}, {"opponent": "quagsire_shadow", "rating": 584, "opRating": 415}, {"opponent": "swampert_shadow", "rating": 547, "opRating": 452}, {"opponent": "claydol", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 141}, {"opponent": "clodsire", "rating": 298}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33567}, {"moveId": "INFESTATION", "uses": 24733}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20021}, {"moveId": "ROCK_SLIDE", "uses": 14953}, {"moveId": "SLUDGE_BOMB", "uses": 11990}, {"moveId": "ANCIENT_POWER", "uses": 7161}, {"moveId": "SOLAR_BEAM", "uses": 4217}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 53.5}, {"speciesId": "trumbeak", "speciesName": "Trumbeak", "rating": 296, "matchups": [{"opponent": "breloom", "rating": 742, "opRating": 257}, {"opponent": "leavanny", "rating": 742, "opRating": 257}, {"opponent": "parasect", "rating": 708, "opRating": 291}, {"opponent": "swadloon", "rating": 526, "opRating": 473}, {"opponent": "gastrodon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "clodsire", "rating": 100}, {"opponent": "cradily", "rating": 121}, {"opponent": "talonflame", "rating": 177}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "PECK", "uses": 37901}, {"moveId": "ROCK_SMASH", "uses": 20399}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 35032}, {"moveId": "ROCK_BLAST", "uses": 14905}, {"moveId": "SKY_ATTACK", "uses": 8273}]}, "moveset": ["PECK", "DRILL_PECK", "ROCK_BLAST"], "score": 53.5}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 283, "matchups": [{"opponent": "whiscash", "rating": 759, "opRating": 240}, {"opponent": "quagsire", "rating": 667, "opRating": 332}, {"opponent": "marowak", "rating": 664, "opRating": 335}, {"opponent": "quagsire_shadow", "rating": 599, "opRating": 400}, {"opponent": "swampert_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 158}, {"opponent": "cradily", "rating": 357}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 27088}, {"moveId": "MAGICAL_LEAF", "uses": 21245}, {"moveId": "RAZOR_LEAF", "uses": 9974}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 36923}, {"moveId": "EARTHQUAKE", "uses": 11888}, {"moveId": "PETAL_BLIZZARD", "uses": 5209}, {"moveId": "SOLAR_BEAM", "uses": 4117}, {"moveId": "FRUSTRATION", "uses": 21}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 53.3}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 271, "matchups": [{"opponent": "quagsire", "rating": 689, "opRating": 310}, {"opponent": "gastrodon", "rating": 685, "opRating": 314}, {"opponent": "claydol", "rating": 640, "opRating": 359}, {"opponent": "quagsire_shadow", "rating": 617, "opRating": 382}, {"opponent": "swampert_shadow", "rating": 575, "opRating": 424}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 74}, {"opponent": "clodsire", "rating": 175}, {"opponent": "cradily", "rating": 187}, {"opponent": "gligar", "rating": 190}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 7572}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4406}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3831}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3704}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3387}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3383}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3378}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3033}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3026}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2935}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2878}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2865}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2811}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2652}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2619}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2367}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2089}, {"moveId": "ZEN_HEADBUTT", "uses": 1256}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31379}, {"moveId": "ENERGY_BALL", "uses": 11078}, {"moveId": "SEED_FLARE", "uses": 9320}, {"moveId": "SOLAR_BEAM", "uses": 6430}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "SEED_FLARE"], "score": 53.3}, {"speciesId": "buneary", "speciesName": "Buneary", "rating": 258, "matchups": [{"opponent": "lilligant", "rating": 637, "opRating": 362}, {"opponent": "ferroth<PERSON>_shadow", "rating": 537, "opRating": 462}, {"opponent": "nuzleaf", "rating": 525, "opRating": 474}, {"opponent": "rilla<PERSON>m", "rating": 518, "opRating": 481}, {"opponent": "abomasnow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 223}, {"opponent": "cradily", "rating": 246}, {"opponent": "gligar", "rating": 293}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 53124}, {"moveId": "POUND", "uses": 5176}], "chargedMoves": [{"moveId": "SWIFT", "uses": 36677}, {"moveId": "FIRE_PUNCH", "uses": 21623}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "FIRE_PUNCH"], "score": 53}, {"speciesId": "ivysaur", "speciesName": "Ivysaur", "rating": 282, "matchups": [{"opponent": "chesnaught", "rating": 782, "opRating": 217}, {"opponent": "gogoat", "rating": 755, "opRating": 244}, {"opponent": "swampert_shadow", "rating": 648, "opRating": 351}, {"opponent": "bellossom", "rating": 603, "opRating": 396}, {"opponent": "quagsire_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 295}, {"opponent": "cradily", "rating": 329}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42413}, {"moveId": "RAZOR_LEAF", "uses": 15887}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22635}, {"moveId": "SLUDGE_BOMB", "uses": 21794}, {"moveId": "RETURN", "uses": 9123}, {"moveId": "SOLAR_BEAM", "uses": 4736}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 53}, {"speciesId": "stufful", "speciesName": "Stufful", "rating": 264, "matchups": [{"opponent": "lickitung", "rating": 711, "opRating": 288}, {"opponent": "miltank", "rating": 596, "opRating": 403}, {"opponent": "lickilicky", "rating": 596, "opRating": 403}, {"opponent": "wormadam_sandy", "rating": 576, "opRating": 423}, {"opponent": "parasect", "rating": 544, "opRating": 455}], "counters": [{"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "talonflame", "rating": 170}, {"opponent": "cradily", "rating": 190}, {"opponent": "gligar", "rating": 244}, {"opponent": "clodsire", "rating": 312}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 48467}, {"moveId": "TAKE_DOWN", "uses": 9833}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 31250}, {"moveId": "STOMP", "uses": 18437}, {"moveId": "BRICK_BREAK", "uses": 8592}]}, "moveset": ["TACKLE", "SUPER_POWER", "STOMP"], "score": 53}, {"speciesId": "volcanion", "speciesName": "Volcanion", "rating": 290, "matchups": [{"opponent": "abomasnow_shadow", "rating": 839, "opRating": 160}, {"opponent": "abomasnow", "rating": 830, "opRating": 169}, {"opponent": "swadloon", "rating": 694, "opRating": 305}, {"opponent": "parasect", "rating": 694, "opRating": 305}, {"opponent": "ninetales", "rating": 519, "opRating": 480}], "counters": [{"opponent": "gligar", "rating": 114}, {"opponent": "clodsire", "rating": 129}, {"opponent": "talonflame", "rating": 137}, {"opponent": "cradily", "rating": 166}, {"opponent": "jumpluff_shadow", "rating": 258}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36366}, {"moveId": "WATER_GUN", "uses": 19675}, {"moveId": "TAKE_DOWN", "uses": 2250}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 16872}, {"moveId": "HYDRO_PUMP", "uses": 15507}, {"moveId": "EARTH_POWER", "uses": 13389}, {"moveId": "SLUDGE_BOMB", "uses": 12690}]}, "moveset": ["INCINERATE", "OVERHEAT", "HYDRO_PUMP"], "score": 53}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 291, "matchups": [{"opponent": "whiscash", "rating": 794, "opRating": 205}, {"opponent": "gastrodon", "rating": 666, "opRating": 333}, {"opponent": "quagsire", "rating": 647, "opRating": 352}, {"opponent": "marowak_shadow", "rating": 617, "opRating": 382}, {"opponent": "quagsire_shadow", "rating": 593, "opRating": 406}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 211}, {"opponent": "clodsire", "rating": 259}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 23908}, {"moveId": "BULLET_SEED", "uses": 23542}, {"moveId": "RAZOR_LEAF", "uses": 10771}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 38270}, {"moveId": "LAST_RESORT", "uses": 11450}, {"moveId": "ENERGY_BALL", "uses": 5378}, {"moveId": "SOLAR_BEAM", "uses": 3159}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 52.8}, {"speciesId": "fuecoco", "speciesName": "Fuecoco", "rating": 297, "matchups": [{"opponent": "ferrothorn", "rating": 763, "opRating": 236}, {"opponent": "abomasnow", "rating": 759, "opRating": 240}, {"opponent": "parasect", "rating": 736, "opRating": 263}, {"opponent": "abomasnow_shadow", "rating": 661, "opRating": 338}, {"opponent": "farfetchd", "rating": 546, "opRating": 453}], "counters": [{"opponent": "clodsire", "rating": 103}, {"opponent": "talonflame", "rating": 111}, {"opponent": "cradily", "rating": 128}, {"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 44230}, {"moveId": "BITE", "uses": 14070}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21896}, {"moveId": "FLAMETHROWER", "uses": 19715}, {"moveId": "DISARMING_VOICE", "uses": 16812}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 52.3}, {"speciesId": "growlithe", "speciesName": "Grow<PERSON>he", "rating": 308, "matchups": [{"opponent": "wormadam_plant", "rating": 788, "opRating": 211}, {"opponent": "abomasnow", "rating": 670, "opRating": 329}, {"opponent": "parasect", "rating": 644, "opRating": 355}, {"opponent": "abomasnow_shadow", "rating": 640, "opRating": 359}, {"opponent": "swadloon", "rating": 585, "opRating": 414}], "counters": [{"opponent": "clodsire", "rating": 100}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 121}, {"opponent": "gligar", "rating": 137}, {"opponent": "jumpluff_shadow", "rating": 267}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 40573}, {"moveId": "BITE", "uses": 17727}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25473}, {"moveId": "FLAMETHROWER", "uses": 21776}, {"moveId": "RETURN", "uses": 6237}, {"moveId": "FLAME_WHEEL", "uses": 4840}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 52.3}, {"speciesId": "growlithe_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 296, "matchups": [{"opponent": "wormadam_plant", "rating": 800, "opRating": 200}, {"opponent": "abomasnow", "rating": 725, "opRating": 274}, {"opponent": "abomasnow_shadow", "rating": 700, "opRating": 300}, {"opponent": "serperior", "rating": 655, "opRating": 344}, {"opponent": "<PERSON><PERSON>e", "rating": 592, "opRating": 407}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 145}, {"opponent": "jumpluff_shadow", "rating": 251}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41022}, {"moveId": "BITE", "uses": 17278}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28789}, {"moveId": "FLAMETHROWER", "uses": 24076}, {"moveId": "FLAME_WHEEL", "uses": 5477}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 52.3}, {"speciesId": "dhelmise", "speciesName": "Dhelmise", "rating": 272, "matchups": [{"opponent": "whiscash", "rating": 690, "opRating": 309}, {"opponent": "munchlax", "rating": 604, "opRating": 395}, {"opponent": "steelix_shadow", "rating": 600, "opRating": 400}, {"opponent": "marowak", "rating": 566, "opRating": 433}, {"opponent": "quagsire", "rating": 566, "opRating": 433}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 180}, {"opponent": "gligar", "rating": 183}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "clodsire", "rating": 286}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 21956}, {"moveId": "METAL_SOUND", "uses": 18218}, {"moveId": "ASTONISH", "uses": 18100}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18799}, {"moveId": "SHADOW_BALL", "uses": 15589}, {"moveId": "WRAP", "uses": 15149}, {"moveId": "HEAVY_SLAM", "uses": 8700}]}, "moveset": ["SHADOW_CLAW", "POWER_WHIP", "WRAP"], "score": 52.1}, {"speciesId": "deerling", "speciesName": "<PERSON><PERSON>", "rating": 247, "matchups": [{"opponent": "whiscash", "rating": 609, "opRating": 390}, {"opponent": "marshtomp", "rating": 580, "opRating": 419}, {"opponent": "cubone", "rating": 514, "opRating": 485}, {"opponent": "cu<PERSON>_shadow", "rating": 514, "opRating": 485}, {"opponent": "stunfisk", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 98}, {"opponent": "talonflame", "rating": 166}, {"opponent": "cradily", "rating": 260}, {"opponent": "gligar", "rating": 274}, {"opponent": "clodsire", "rating": 307}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 47317}, {"moveId": "TAKE_DOWN", "uses": 10983}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22490}, {"moveId": "TRAILBLAZE", "uses": 22325}, {"moveId": "SEED_BOMB", "uses": 7142}, {"moveId": "ENERGY_BALL", "uses": 6446}]}, "moveset": ["TACKLE", "WILD_CHARGE", "TRAILBLAZE"], "score": 51.9}, {"speciesId": "exeggcute", "speciesName": "Exeggcute", "rating": 275, "matchups": [{"opponent": "whiscash", "rating": 626, "opRating": 373}, {"opponent": "breloom", "rating": 612, "opRating": 387}, {"opponent": "rhyhorn", "rating": 521, "opRating": 478}, {"opponent": "marowak", "rating": 507, "opRating": 492}, {"opponent": "stunfisk", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 98}, {"opponent": "gligar", "rating": 137}, {"opponent": "talonflame", "rating": 177}, {"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 262}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30866}, {"moveId": "BULLET_SEED", "uses": 27434}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 19055}, {"moveId": "ANCIENT_POWER", "uses": 17710}, {"moveId": "PSYCHIC", "uses": 13282}, {"moveId": "RETURN", "uses": 8292}]}, "moveset": ["CONFUSION", "SEED_BOMB", "ANCIENT_POWER"], "score": 51.9}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 284, "matchups": [{"opponent": "thwackey", "rating": 829, "opRating": 170}, {"opponent": "grotle_shadow", "rating": 814, "opRating": 185}, {"opponent": "bellossom_shadow", "rating": 659, "opRating": 340}, {"opponent": "lura<PERSON>s", "rating": 613, "opRating": 386}, {"opponent": "diggersby", "rating": 520, "opRating": 479}], "counters": [{"opponent": "gligar", "rating": 64}, {"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "clodsire", "rating": 144}, {"opponent": "cradily", "rating": 222}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 15666}, {"moveId": "POISON_JAB", "uses": 14047}, {"moveId": "BULLET_SEED", "uses": 12285}, {"moveId": "MAGICAL_LEAF", "uses": 11193}, {"moveId": "RAZOR_LEAF", "uses": 5064}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17044}, {"moveId": "GRASS_KNOT", "uses": 12680}, {"moveId": "SLUDGE_BOMB", "uses": 11928}, {"moveId": "LEAF_STORM", "uses": 7764}, {"moveId": "DAZZLING_GLEAM", "uses": 6275}, {"moveId": "SOLAR_BEAM", "uses": 2653}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 51.7}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 285, "matchups": [{"opponent": "breloom", "rating": 737, "opRating": 262}, {"opponent": "leavanny", "rating": 709, "opRating": 290}, {"opponent": "parasect", "rating": 684, "opRating": 315}, {"opponent": "rilla<PERSON>m", "rating": 655, "opRating": 344}, {"opponent": "swadloon", "rating": 516, "opRating": 483}], "counters": [{"opponent": "clodsire", "rating": 108}, {"opponent": "cradily", "rating": 121}, {"opponent": "talonflame", "rating": 166}, {"opponent": "gligar", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 196}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32393}, {"moveId": "STEEL_WING", "uses": 25907}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33652}, {"moveId": "HYPER_BEAM", "uses": 17290}, {"moveId": "HEAT_WAVE", "uses": 7488}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 51.7}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 279, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 735, "opRating": 264}, {"opponent": "servine_shadow", "rating": 678, "opRating": 321}, {"opponent": "gogoat", "rating": 674, "opRating": 325}, {"opponent": "swampert_shadow", "rating": 589, "opRating": 410}, {"opponent": "bellossom", "rating": 585, "opRating": 414}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 125}, {"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 377}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45152}, {"moveId": "RAZOR_LEAF", "uses": 13148}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30454}, {"moveId": "SLUDGE_BOMB", "uses": 20157}, {"moveId": "PETAL_BLIZZARD", "uses": 4228}, {"moveId": "SOLAR_BEAM", "uses": 3538}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 51.7}, {"speciesId": "gloom", "speciesName": "Gloom", "rating": 289, "matchups": [{"opponent": "gogoat", "rating": 755, "opRating": 244}, {"opponent": "grotle", "rating": 721, "opRating": 278}, {"opponent": "<PERSON><PERSON>e", "rating": 599, "opRating": 400}, {"opponent": "bellossom", "rating": 591, "opRating": 408}, {"opponent": "swadloon", "rating": 522, "opRating": 477}], "counters": [{"opponent": "clodsire", "rating": 64}, {"opponent": "gligar", "rating": 118}, {"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 40887}, {"moveId": "RAZOR_LEAF", "uses": 17413}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 21065}, {"moveId": "PETAL_BLIZZARD", "uses": 14905}, {"moveId": "MOONBLAST", "uses": 13029}, {"moveId": "RETURN", "uses": 9400}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 51.4}, {"speciesId": "roselia", "speciesName": "Roselia", "rating": 279, "matchups": [{"opponent": "rilla<PERSON>m", "rating": 818, "opRating": 181}, {"opponent": "breloom", "rating": 795, "opRating": 204}, {"opponent": "thwackey", "rating": 781, "opRating": 218}, {"opponent": "servine_shadow", "rating": 650, "opRating": 350}, {"opponent": "gogoat", "rating": 645, "opRating": 354}], "counters": [{"opponent": "clodsire", "rating": 81}, {"opponent": "gligar", "rating": 152}, {"opponent": "talonflame", "rating": 207}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "cradily", "rating": 236}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 27132}, {"moveId": "MAGICAL_LEAF", "uses": 20672}, {"moveId": "RAZOR_LEAF", "uses": 10539}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 26069}, {"moveId": "PETAL_BLIZZARD", "uses": 17839}, {"moveId": "DAZZLING_GLEAM", "uses": 14398}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 51.4}, {"speciesId": "cu<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 264, "matchups": [{"opponent": "arcanine_<PERSON><PERSON>an", "rating": 779, "opRating": 220}, {"opponent": "steelix_shadow", "rating": 759, "opRating": 240}, {"opponent": "onix", "rating": 736, "opRating": 263}, {"opponent": "nidoqueen", "rating": 677, "opRating": 322}, {"opponent": "steelix", "rating": 641, "opRating": 358}], "counters": [{"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "talonflame", "rating": 92}, {"opponent": "cradily", "rating": 114}, {"opponent": "gligar", "rating": 133}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43406}, {"moveId": "ROCK_SMASH", "uses": 14894}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 43139}, {"moveId": "DIG", "uses": 8255}, {"moveId": "BULLDOZE", "uses": 6704}, {"moveId": "FRUSTRATION", "uses": 79}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "DIG"], "score": 51.2}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 294, "matchups": [{"opponent": "pidgeotto", "rating": 682, "opRating": 317}, {"opponent": "noctowl", "rating": 678, "opRating": 321}, {"opponent": "pidgeot", "rating": 652, "opRating": 347}, {"opponent": "farfetchd", "rating": 626, "opRating": 373}, {"opponent": "pidgeot_shadow", "rating": 586, "opRating": 413}], "counters": [{"opponent": "cradily", "rating": 128}, {"opponent": "clodsire", "rating": 149}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 198}, {"opponent": "jumpluff_shadow", "rating": 212}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 38224}, {"moveId": "TACKLE", "uses": 20076}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 23751}, {"moveId": "SWIFT", "uses": 19481}, {"moveId": "ENERGY_BALL", "uses": 15109}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 51.2}, {"speciesId": "phanpy_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 237, "matchups": [{"opponent": "onix_shadow", "rating": 642, "opRating": 357}, {"opponent": "lilligant", "rating": 588, "opRating": 411}, {"opponent": "geodude", "rating": 572, "opRating": 427}, {"opponent": "onix", "rating": 569, "opRating": 430}, {"opponent": "nidoqueen", "rating": 561, "opRating": 438}], "counters": [{"opponent": "cradily", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "talonflame", "rating": 159}, {"opponent": "clodsire", "rating": 310}, {"opponent": "gligar", "rating": 354}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 38748}, {"moveId": "ROCK_SMASH", "uses": 19552}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 16608}, {"moveId": "BODY_SLAM", "uses": 16079}, {"moveId": "ROCK_SLIDE", "uses": 14999}, {"moveId": "BULLDOZE", "uses": 10667}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "TRAILBLAZE", "BODY_SLAM"], "score": 51.2}, {"speciesId": "porygon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 254, "matchups": [{"opponent": "exploud", "rating": 655, "opRating": 344}, {"opponent": "exploud_shadow", "rating": 581, "opRating": 418}, {"opponent": "ambipom_shadow", "rating": 562, "opRating": 437}, {"opponent": "slaking", "rating": 555, "opRating": 444}, {"opponent": "lilligant", "rating": 533, "opRating": 466}], "counters": [{"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "cradily", "rating": 170}, {"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 189}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 5072}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4371}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3889}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3403}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3395}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3388}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3182}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3025}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3014}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2952}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2899}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2895}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2876}, {"moveId": "CHARGE_BEAM", "uses": 2854}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2636}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2578}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2431}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2062}, {"moveId": "ZEN_HEADBUTT", "uses": 1132}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 12947}, {"moveId": "DISCHARGE", "uses": 12217}, {"moveId": "HYPER_BEAM", "uses": 10405}, {"moveId": "SOLAR_BEAM", "uses": 9556}, {"moveId": "ZAP_CANNON", "uses": 6536}, {"moveId": "PSYBEAM", "uses": 6505}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "DISCHARGE", "SIGNAL_BEAM"], "score": 51}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 233, "matchups": [{"opponent": "vibrava_shadow", "rating": 684, "opRating": 315}, {"opponent": "exeggutor_alolan_shadow", "rating": 639, "opRating": 360}, {"opponent": "garcho<PERSON>", "rating": 594, "opRating": 405}, {"opponent": "torterra", "rating": 567, "opRating": 432}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 549, "opRating": 450}], "counters": [{"opponent": "talonflame", "rating": 148}, {"opponent": "clodsire", "rating": 187}, {"opponent": "cradily", "rating": 246}, {"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "gligar", "rating": 404}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5251}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4514}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4192}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3998}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3995}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3601}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3576}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3412}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3402}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3399}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3393}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3355}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3097}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3054}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2752}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2436}, {"moveId": "ZEN_HEADBUTT", "uses": 936}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 32850}, {"moveId": "FOCUS_BLAST", "uses": 12962}, {"moveId": "THUNDER", "uses": 7768}, {"moveId": "GIGA_IMPACT", "uses": 4631}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "FOCUS_BLAST"], "score": 51}, {"speciesId": "swellow", "speciesName": "Swellow", "rating": 292, "matchups": [{"opponent": "breloom", "rating": 735, "opRating": 264}, {"opponent": "amoon<PERSON>s", "rating": 669, "opRating": 330}, {"opponent": "<PERSON><PERSON>e", "rating": 657, "opRating": 342}, {"opponent": "servine_shadow", "rating": 570, "opRating": 429}, {"opponent": "lickitung", "rating": 537, "opRating": 462}], "counters": [{"opponent": "cradily", "rating": 97}, {"opponent": "clodsire", "rating": 98}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 160}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 36328}, {"moveId": "STEEL_WING", "uses": 21972}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27110}, {"moveId": "AERIAL_ACE", "uses": 17544}, {"moveId": "RETURN", "uses": 7866}, {"moveId": "SKY_ATTACK", "uses": 5973}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "AERIAL_ACE"], "score": 51}, {"speciesId": "audino", "speciesName": "Audino", "rating": 252, "matchups": [{"opponent": "slaking", "rating": 657, "opRating": 342}, {"opponent": "slaking_shadow", "rating": 597, "opRating": 402}, {"opponent": "exploud", "rating": 550, "opRating": 450}, {"opponent": "bulbasaur", "rating": 510, "opRating": 489}], "counters": [{"opponent": "jumpluff_shadow", "rating": 133}, {"opponent": "talonflame", "rating": 151}, {"opponent": "cradily", "rating": 156}, {"opponent": "clodsire", "rating": 295}, {"opponent": "gligar", "rating": 297}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 33776}, {"moveId": "POUND", "uses": 24524}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29561}, {"moveId": "DISARMING_VOICE", "uses": 15867}, {"moveId": "HYPER_BEAM", "uses": 7090}, {"moveId": "DAZZLING_GLEAM", "uses": 5815}]}, "moveset": ["ZEN_HEADBUTT", "BODY_SLAM", "DISARMING_VOICE"], "score": 50.7}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Eldegoss", "rating": 268, "matchups": [{"opponent": "whiscash", "rating": 781, "opRating": 218}, {"opponent": "gastrodon", "rating": 659, "opRating": 340}, {"opponent": "quagsire", "rating": 626, "opRating": 373}, {"opponent": "marowak", "rating": 605, "opRating": 394}, {"opponent": "quagsire_shadow", "rating": 571, "opRating": 428}], "counters": [{"opponent": "talonflame", "rating": 33}, {"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 83}, {"opponent": "clodsire", "rating": 242}, {"opponent": "cradily", "rating": 256}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35391}, {"moveId": "RAZOR_LEAF", "uses": 22909}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 43142}, {"moveId": "ENERGY_BALL", "uses": 15158}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "ENERGY_BALL"], "score": 50.7}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 286, "matchups": [{"opponent": "wormadam_plant", "rating": 810, "opRating": 190}, {"opponent": "abomasnow", "rating": 720, "opRating": 280}, {"opponent": "abomasnow_shadow", "rating": 705, "opRating": 295}, {"opponent": "swadloon", "rating": 675, "opRating": 325}, {"opponent": "parasect", "rating": 665, "opRating": 335}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 145}, {"opponent": "jumpluff_shadow", "rating": 228}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30819}, {"moveId": "FIRE_SPIN", "uses": 27481}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21628}, {"moveId": "FLAMETHROWER", "uses": 12149}, {"moveId": "OVERHEAT", "uses": 11312}, {"moveId": "LAST_RESORT", "uses": 8066}, {"moveId": "FIRE_BLAST", "uses": 3189}, {"moveId": "HEAT_WAVE", "uses": 1942}]}, "moveset": ["EMBER", "SUPER_POWER", "FLAMETHROWER"], "score": 50.7}, {"speciesId": "meowscarada", "speciesName": "Meowscarada", "rating": 293, "matchups": [{"opponent": "oranguru", "rating": 704, "opRating": 295}, {"opponent": "whiscash", "rating": 673, "opRating": 326}, {"opponent": "marowak", "rating": 582, "opRating": 417}, {"opponent": "chansey", "rating": 582, "opRating": 417}, {"opponent": "quagsire_shadow", "rating": 513, "opRating": 486}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 81}, {"opponent": "gligar", "rating": 99}, {"opponent": "cradily", "rating": 125}, {"opponent": "clodsire", "rating": 161}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 40561}, {"moveId": "CHARM", "uses": 17739}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 21353}, {"moveId": "FRENZY_PLANT", "uses": 17602}, {"moveId": "FLOWER_TRICK", "uses": 6961}, {"moveId": "GRASS_KNOT", "uses": 4735}, {"moveId": "PLAY_ROUGH", "uses": 4297}, {"moveId": "ENERGY_BALL", "uses": 3423}]}, "moveset": ["LEAFAGE", "NIGHT_SLASH", "FRENZY_PLANT"], "score": 50.7}, {"speciesId": "lombre", "speciesName": "Lombre", "rating": 234, "matchups": [{"opponent": "whiscash", "rating": 697, "opRating": 302}, {"opponent": "stunfisk_galarian", "rating": 619, "opRating": 380}, {"opponent": "gastrodon", "rating": 535, "opRating": 464}, {"opponent": "marowak", "rating": 514, "opRating": 485}, {"opponent": "diggersby_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "cradily", "rating": 142}, {"opponent": "talonflame", "rating": 185}, {"opponent": "gligar", "rating": 187}, {"opponent": "clodsire", "rating": 197}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 43133}, {"moveId": "RAZOR_LEAF", "uses": 15167}], "chargedMoves": [{"moveId": "SCALD", "uses": 21746}, {"moveId": "ICE_BEAM", "uses": 16294}, {"moveId": "GRASS_KNOT", "uses": 13932}, {"moveId": "BUBBLE_BEAM", "uses": 6386}]}, "moveset": ["BUBBLE", "SCALD", "GRASS_KNOT"], "score": 50.5}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 242, "matchups": [{"opponent": "flapple", "rating": 695, "opRating": 304}, {"opponent": "vibrava", "rating": 695, "opRating": 304}, {"opponent": "exeggutor_alolan_shadow", "rating": 666, "opRating": 333}, {"opponent": "pidgeotto", "rating": 586, "opRating": 413}, {"opponent": "steelix", "rating": 518, "opRating": 481}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 170}, {"opponent": "clodsire", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "gligar", "rating": 416}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34723}, {"moveId": "MUD_SLAP", "uses": 23577}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19377}, {"moveId": "ICICLE_SPEAR", "uses": 16237}, {"moveId": "STONE_EDGE", "uses": 6181}, {"moveId": "HIGH_HORSEPOWER", "uses": 6096}, {"moveId": "ANCIENT_POWER", "uses": 5540}, {"moveId": "BULLDOZE", "uses": 4959}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 50.3}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 247, "matchups": [{"opponent": "onix_shadow", "rating": 664, "opRating": 336}, {"opponent": "geodude_shadow", "rating": 600, "opRating": 400}, {"opponent": "steelix", "rating": 552, "opRating": 448}, {"opponent": "chansey", "rating": 552, "opRating": 448}, {"opponent": "steelix_shadow", "rating": 524, "opRating": 476}], "counters": [{"opponent": "gligar", "rating": 83}, {"opponent": "cradily", "rating": 100}, {"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 375}, {"opponent": "clodsire", "rating": 454}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 22861}, {"moveId": "MUD_SHOT", "uses": 20239}, {"moveId": "METAL_CLAW", "uses": 15208}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 16876}, {"moveId": "ROCK_TOMB", "uses": 16178}, {"moveId": "SCORCHING_SANDS", "uses": 13879}, {"moveId": "BULLDOZE", "uses": 7440}, {"moveId": "EARTHQUAKE", "uses": 3921}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "NIGHT_SLASH"], "score": 50.3}, {"speciesId": "sandygast", "speciesName": "Sandygast", "rating": 252, "matchups": [{"opponent": "chandelure_shadow", "rating": 711, "opRating": 288}, {"opponent": "onix", "rating": 637, "opRating": 362}, {"opponent": "steelix", "rating": 629, "opRating": 370}, {"opponent": "steelix_shadow", "rating": 570, "opRating": 429}, {"opponent": "munchlax", "rating": 514, "opRating": 485}], "counters": [{"opponent": "talonflame", "rating": 185}, {"opponent": "clodsire", "rating": 197}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 253}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20236}, {"moveId": "SAND_ATTACK", "uses": 20117}, {"moveId": "MUD_SHOT", "uses": 17909}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22488}, {"moveId": "SHADOW_BALL", "uses": 19546}, {"moveId": "EARTH_POWER", "uses": 8875}, {"moveId": "SAND_TOMB", "uses": 7479}]}, "moveset": ["ASTONISH", "SCORCHING_SANDS", "SHADOW_BALL"], "score": 50.3}, {"speciesId": "sceptile_shadow", "speciesName": "<PERSON>eptile (Shadow)", "rating": 294, "matchups": [{"opponent": "whiscash", "rating": 644, "opRating": 355}, {"opponent": "gogoat", "rating": 635, "opRating": 364}, {"opponent": "<PERSON><PERSON>e", "rating": 621, "opRating": 378}, {"opponent": "marowak", "rating": 599, "opRating": 400}, {"opponent": "serperior", "rating": 549, "opRating": 450}], "counters": [{"opponent": "clodsire", "rating": 79}, {"opponent": "gligar", "rating": 83}, {"opponent": "talonflame", "rating": 114}, {"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "cradily", "rating": 197}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32181}, {"moveId": "BULLET_SEED", "uses": 26119}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 19575}, {"moveId": "BREAKING_SWIPE", "uses": 12263}, {"moveId": "AERIAL_ACE", "uses": 9863}, {"moveId": "FRENZY_PLANT", "uses": 7269}, {"moveId": "DRAGON_CLAW", "uses": 4838}, {"moveId": "EARTHQUAKE", "uses": 4455}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 50.3}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 281, "matchups": [{"opponent": "whiscash", "rating": 737, "opRating": 262}, {"opponent": "oranguru", "rating": 706, "opRating": 293}, {"opponent": "gastrodon", "rating": 685, "opRating": 314}, {"opponent": "quagsire", "rating": 625, "opRating": 374}, {"opponent": "quagsire_shadow", "rating": 569, "opRating": 430}], "counters": [{"opponent": "gligar", "rating": 49}, {"opponent": "jumpluff_shadow", "rating": 55}, {"opponent": "cradily", "rating": 86}, {"opponent": "clodsire", "rating": 233}, {"opponent": "talonflame", "rating": 281}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 19329}, {"moveId": "BULLET_SEED", "uses": 18498}, {"moveId": "FEINT_ATTACK", "uses": 13475}, {"moveId": "RAZOR_LEAF", "uses": 7013}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 25246}, {"moveId": "FOUL_PLAY", "uses": 15893}, {"moveId": "HURRICANE", "uses": 7595}, {"moveId": "LEAF_TORNADO", "uses": 4805}, {"moveId": "RETURN", "uses": 4771}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 50.3}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 218, "matchups": [{"opponent": "castform_sunny", "rating": 708, "opRating": 291}, {"opponent": "marowak_alolan", "rating": 651, "opRating": 348}, {"opponent": "skeledirge", "rating": 627, "opRating": 372}, {"opponent": "ninetales", "rating": 577, "opRating": 422}, {"opponent": "magcargo", "rating": 526, "opRating": 473}], "counters": [{"opponent": "cradily", "rating": 72}, {"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "gligar", "rating": 137}, {"opponent": "talonflame", "rating": 240}, {"opponent": "clodsire", "rating": 358}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 31180}, {"moveId": "WATER_GUN", "uses": 22577}, {"moveId": "TAKE_DOWN", "uses": 4572}], "chargedMoves": [{"moveId": "SURF", "uses": 34423}, {"moveId": "HYPER_FANG", "uses": 18778}, {"moveId": "HYPER_BEAM", "uses": 5060}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 50.1}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 269, "matchups": [{"opponent": "whiscash", "rating": 767, "opRating": 232}, {"opponent": "gastrodon", "rating": 697, "opRating": 302}, {"opponent": "quagsire_shadow", "rating": 600, "opRating": 399}, {"opponent": "marowak", "rating": 565, "opRating": 434}, {"opponent": "swampert_shadow", "rating": 558, "opRating": 441}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 74}, {"opponent": "gligar", "rating": 133}, {"opponent": "clodsire", "rating": 175}, {"opponent": "cradily", "rating": 187}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 24806}, {"moveId": "MAGICAL_LEAF", "uses": 23159}, {"moveId": "CHARGE_BEAM", "uses": 10346}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 15051}, {"moveId": "PSYCHIC", "uses": 13775}, {"moveId": "LEAF_STORM", "uses": 11894}, {"moveId": "DAZZLING_GLEAM", "uses": 10848}, {"moveId": "HYPER_BEAM", "uses": 6737}]}, "moveset": ["MAGICAL_LEAF", "SEED_BOMB", "LEAF_STORM"], "score": 50.1}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 241, "matchups": [{"opponent": "appletun", "rating": 694, "opRating": 305}, {"opponent": "pumpkaboo_super", "rating": 605, "opRating": 394}, {"opponent": "pumpkaboo_large", "rating": 598, "opRating": 401}, {"opponent": "gourgeist_super", "rating": 547, "opRating": 452}, {"opponent": "claydol", "rating": 519, "opRating": 480}], "counters": [{"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 175}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "gligar", "rating": 190}, {"opponent": "cradily", "rating": 222}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 37902}, {"moveId": "BITE", "uses": 20398}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 17841}, {"moveId": "BOOMBURST", "uses": 14594}, {"moveId": "DISARMING_VOICE", "uses": 13626}, {"moveId": "FIRE_BLAST", "uses": 6902}, {"moveId": "RETURN", "uses": 5409}]}, "moveset": ["ASTONISH", "CRUNCH", "DISARMING_VOICE"], "score": 49.8}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 235, "matchups": [{"opponent": "gourgeist_small", "rating": 708, "opRating": 291}, {"opponent": "pumpkaboo_super", "rating": 615, "opRating": 384}, {"opponent": "pumpkaboo_large", "rating": 611, "opRating": 388}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 589, "opRating": 410}, {"opponent": "rilla<PERSON>m", "rating": 563, "opRating": 436}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "clodsire", "rating": 237}, {"opponent": "gligar", "rating": 274}, {"opponent": "cradily", "rating": 312}, {"opponent": "jumpluff_shadow", "rating": 313}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 36634}, {"moveId": "SCRATCH", "uses": 21666}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 24674}, {"moveId": "RETURN", "uses": 14508}, {"moveId": "PLAY_ROUGH", "uses": 9928}, {"moveId": "THUNDER", "uses": 9177}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 49.8}, {"speciesId": "ursa<PERSON>na_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 248, "matchups": [{"opponent": "onix", "rating": 604, "opRating": 395}, {"opponent": "audino", "rating": 597, "opRating": 402}, {"opponent": "electrode_hisuian", "rating": 541, "opRating": 458}, {"opponent": "lilligant", "rating": 527, "opRating": 472}, {"opponent": "steelix", "rating": 517, "opRating": 482}], "counters": [{"opponent": "cradily", "rating": 104}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 244}, {"opponent": "clodsire", "rating": 336}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 43387}, {"moveId": "ROCK_SMASH", "uses": 14913}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 12058}, {"moveId": "SWIFT", "uses": 11514}, {"moveId": "TRAILBLAZE", "uses": 8087}, {"moveId": "FIRE_PUNCH", "uses": 7489}, {"moveId": "AERIAL_ACE", "uses": 7169}, {"moveId": "HIGH_HORSEPOWER", "uses": 6753}, {"moveId": "THUNDER_PUNCH", "uses": 5297}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 49.8}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 233, "matchups": [{"opponent": "pumpkaboo_small", "rating": 691, "opRating": 308}, {"opponent": "chandelure_shadow", "rating": 665, "opRating": 334}, {"opponent": "trevenant", "rating": 643, "opRating": 356}, {"opponent": "pumpkaboo_super", "rating": 570, "opRating": 429}, {"opponent": "pumpkaboo_large", "rating": 538, "opRating": 461}], "counters": [{"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "talonflame", "rating": 185}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 218}, {"opponent": "cradily", "rating": 253}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38659}, {"moveId": "BITE", "uses": 19641}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19541}, {"moveId": "BOOMBURST", "uses": 16240}, {"moveId": "DISARMING_VOICE", "uses": 15002}, {"moveId": "FIRE_BLAST", "uses": 7531}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "CRUNCH", "DISARMING_VOICE"], "score": 49.6}, {"speciesId": "gloom_shadow", "speciesName": "Gloom (Shadow)", "rating": 278, "matchups": [{"opponent": "thwackey", "rating": 801, "opRating": 198}, {"opponent": "gogoat", "rating": 702, "opRating": 297}, {"opponent": "whimsicott", "rating": 645, "opRating": 354}, {"opponent": "<PERSON><PERSON>e", "rating": 515, "opRating": 484}, {"opponent": "bellossom", "rating": 503, "opRating": 496}], "counters": [{"opponent": "clodsire", "rating": 88}, {"opponent": "gligar", "rating": 106}, {"opponent": "talonflame", "rating": 185}, {"opponent": "cradily", "rating": 208}, {"opponent": "jumpluff_shadow", "rating": 209}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41584}, {"moveId": "RAZOR_LEAF", "uses": 16716}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25358}, {"moveId": "PETAL_BLIZZARD", "uses": 17342}, {"moveId": "MOONBLAST", "uses": 15420}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 49.4}, {"speciesId": "oddish", "speciesName": "<PERSON><PERSON>", "rating": 268, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 737, "opRating": 262}, {"opponent": "servine_shadow", "rating": 679, "opRating": 320}, {"opponent": "gogoat", "rating": 679, "opRating": 320}, {"opponent": "bellossom", "rating": 583, "opRating": 416}, {"opponent": "quagsire", "rating": 516, "opRating": 483}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "talonflame", "rating": 159}, {"opponent": "clodsire", "rating": 185}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 40874}, {"moveId": "RAZOR_LEAF", "uses": 17426}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19547}, {"moveId": "SEED_BOMB", "uses": 18724}, {"moveId": "MOONBLAST", "uses": 11559}, {"moveId": "RETURN", "uses": 8396}]}, "moveset": ["ACID", "SEED_BOMB", "SLUDGE_BOMB"], "score": 49.4}, {"speciesId": "swellow_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 277, "matchups": [{"opponent": "breloom", "rating": 685, "opRating": 314}, {"opponent": "amoon<PERSON>s", "rating": 652, "opRating": 347}, {"opponent": "parasect", "rating": 586, "opRating": 413}, {"opponent": "<PERSON><PERSON>e", "rating": 570, "opRating": 429}, {"opponent": "lickitung", "rating": 504, "opRating": 495}], "counters": [{"opponent": "clodsire", "rating": 100}, {"opponent": "cradily", "rating": 121}, {"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 37028}, {"moveId": "STEEL_WING", "uses": 21272}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31383}, {"moveId": "AERIAL_ACE", "uses": 20236}, {"moveId": "SKY_ATTACK", "uses": 6845}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "AERIAL_ACE"], "score": 49.4}, {"speciesId": "houndour", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 274, "matchups": [{"opponent": "kartana", "rating": 833, "opRating": 166}, {"opponent": "abomasnow", "rating": 691, "opRating": 308}, {"opponent": "abomasnow_shadow", "rating": 679, "opRating": 320}, {"opponent": "oranguru", "rating": 658, "opRating": 341}, {"opponent": "ferrothorn", "rating": 658, "opRating": 341}], "counters": [{"opponent": "clodsire", "rating": 117}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 160}, {"opponent": "jumpluff_shadow", "rating": 228}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 35606}, {"moveId": "FEINT_ATTACK", "uses": 22694}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24266}, {"moveId": "FLAMETHROWER", "uses": 16660}, {"moveId": "DARK_PULSE", "uses": 9373}, {"moveId": "RETURN", "uses": 7927}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 49.2}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 236, "matchups": [{"opponent": "steelix", "rating": 732, "opRating": 267}, {"opponent": "steelix_shadow", "rating": 673, "opRating": 326}, {"opponent": "obstagoon", "rating": 645, "opRating": 354}, {"opponent": "lileep", "rating": 637, "opRating": 362}, {"opponent": "cradily", "rating": 625}], "counters": [{"opponent": "clodsire", "rating": 50}, {"opponent": "talonflame", "rating": 77}, {"opponent": "gligar", "rating": 103}, {"opponent": "diggersby", "rating": 117}, {"opponent": "jumpluff_shadow", "rating": 130}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14685}, {"moveId": "FURY_CUTTER", "uses": 14361}, {"moveId": "DOUBLE_KICK", "uses": 13053}, {"moveId": "POISON_JAB", "uses": 12711}, {"moveId": "IRON_TAIL", "uses": 3457}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 16726}, {"moveId": "EARTH_POWER", "uses": 16445}, {"moveId": "SLUDGE_WAVE", "uses": 12319}, {"moveId": "SAND_TOMB", "uses": 6873}, {"moveId": "EARTHQUAKE", "uses": 5931}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 49.2}, {"speciesId": "aipom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 239, "matchups": [{"opponent": "pumpkaboo_super", "rating": 685, "opRating": 314}, {"opponent": "pumpkaboo_large", "rating": 685, "opRating": 314}, {"opponent": "gourgeist_small", "rating": 674, "opRating": 325}, {"opponent": "run<PERSON><PERSON>", "rating": 622, "opRating": 377}, {"opponent": "thwackey", "rating": 592, "opRating": 407}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 170}, {"opponent": "cradily", "rating": 222}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35561}, {"moveId": "SCRATCH", "uses": 22739}], "chargedMoves": [{"moveId": "SWIFT", "uses": 29311}, {"moveId": "AERIAL_ACE", "uses": 18522}, {"moveId": "LOW_SWEEP", "uses": 10443}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "SWIFT", "AERIAL_ACE"], "score": 48.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 249, "matchups": [{"opponent": "whiscash", "rating": 751, "opRating": 248}, {"opponent": "stunfisk", "rating": 687, "opRating": 312}, {"opponent": "gastrodon", "rating": 667, "opRating": 332}, {"opponent": "quagsire_shadow", "rating": 583, "opRating": 416}, {"opponent": "swampert_shadow", "rating": 545, "opRating": 454}], "counters": [{"opponent": "jumpluff_shadow", "rating": 45}, {"opponent": "talonflame", "rating": 88}, {"opponent": "gligar", "rating": 152}, {"opponent": "clodsire", "rating": 158}, {"opponent": "cradily", "rating": 194}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 36828}, {"moveId": "CHARM", "uses": 21472}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31693}, {"moveId": "PLAY_ROUGH", "uses": 15242}, {"moveId": "ENERGY_BALL", "uses": 11269}]}, "moveset": ["LEAFAGE", "GRASS_KNOT", "PLAY_ROUGH"], "score": 48.5}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 268, "matchups": [{"opponent": "oranguru", "rating": 748, "opRating": 251}, {"opponent": "whiscash", "rating": 702, "opRating": 297}, {"opponent": "marowak", "rating": 667, "opRating": 332}, {"opponent": "gastrodon", "rating": 611, "opRating": 388}, {"opponent": "quagsire", "rating": 569, "opRating": 430}], "counters": [{"opponent": "jumpluff_shadow", "rating": 52}, {"opponent": "gligar", "rating": 61}, {"opponent": "cradily", "rating": 72}, {"opponent": "clodsire", "rating": 88}, {"opponent": "talonflame", "rating": 337}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 19796}, {"moveId": "BULLET_SEED", "uses": 19206}, {"moveId": "FEINT_ATTACK", "uses": 13061}, {"moveId": "RAZOR_LEAF", "uses": 6218}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 27095}, {"moveId": "FOUL_PLAY", "uses": 17527}, {"moveId": "HURRICANE", "uses": 8451}, {"moveId": "LEAF_TORNADO", "uses": 5194}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 48.5}, {"speciesId": "g<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 275, "matchups": [{"opponent": "whiscash", "rating": 696, "opRating": 303}, {"opponent": "gastrodon", "rating": 666, "opRating": 333}, {"opponent": "marowak_shadow", "rating": 645, "opRating": 354}, {"opponent": "quagsire", "rating": 581, "opRating": 418}, {"opponent": "quagsire_shadow", "rating": 521, "opRating": 478}], "counters": [{"opponent": "cradily", "rating": 59}, {"opponent": "gligar", "rating": 80}, {"opponent": "jumpluff_shadow", "rating": 81}, {"opponent": "talonflame", "rating": 107}, {"opponent": "clodsire", "rating": 259}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 29409}, {"moveId": "QUICK_ATTACK", "uses": 28891}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30260}, {"moveId": "AERIAL_ACE", "uses": 16121}, {"moveId": "GRASS_KNOT", "uses": 5937}, {"moveId": "RETURN", "uses": 5853}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "AERIAL_ACE"], "score": 48.2}, {"speciesId": "linoone_galarian_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>) (<PERSON>)", "rating": 238, "matchups": [{"opponent": "pupitar_shadow", "rating": 775, "opRating": 224}, {"opponent": "oranguru", "rating": 694, "opRating": 305}, {"opponent": "wyr<PERSON><PERSON>", "rating": 672, "opRating": 327}, {"opponent": "girafarig", "rating": 662, "opRating": 337}, {"opponent": "marowak_alolan_shadow", "rating": 538, "opRating": 461}], "counters": [{"opponent": "jumpluff_shadow", "rating": 84}, {"opponent": "talonflame", "rating": 96}, {"opponent": "gligar", "rating": 236}, {"opponent": "cradily", "rating": 270}, {"opponent": "clodsire", "rating": 300}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37014}, {"moveId": "LICK", "uses": 21286}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34193}, {"moveId": "DIG", "uses": 15275}, {"moveId": "GUNK_SHOT", "uses": 8787}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 48.2}, {"speciesId": "persian", "speciesName": "Persian", "rating": 240, "matchups": [{"opponent": "pumpkaboo_large", "rating": 631, "opRating": 368}, {"opponent": "pumpkaboo_super", "rating": 620, "opRating": 379}, {"opponent": "decid<PERSON><PERSON>", "rating": 540, "opRating": 459}, {"opponent": "gourgeist_super", "rating": 529, "opRating": 470}, {"opponent": "run<PERSON><PERSON>", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "cradily", "rating": 184}, {"opponent": "gligar", "rating": 187}, {"opponent": "clodsire", "rating": 197}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 32026}, {"moveId": "SCRATCH", "uses": 26274}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 18638}, {"moveId": "POWER_GEM", "uses": 13594}, {"moveId": "RETURN", "uses": 8521}, {"moveId": "FOUL_PLAY", "uses": 6962}, {"moveId": "PLAY_ROUGH", "uses": 6001}, {"moveId": "PAYBACK", "uses": 4584}]}, "moveset": ["FEINT_ATTACK", "NIGHT_SLASH", "POWER_GEM"], "score": 48.2}, {"speciesId": "ivy<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 254, "matchups": [{"opponent": "gogoat", "rating": 705, "opRating": 294}, {"opponent": "geodude_shadow", "rating": 651, "opRating": 348}, {"opponent": "swampert_shadow", "rating": 585, "opRating": 414}, {"opponent": "<PERSON><PERSON>e", "rating": 546, "opRating": 453}, {"opponent": "bellossom", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 184}, {"opponent": "clodsire", "rating": 362}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42962}, {"moveId": "RAZOR_LEAF", "uses": 15338}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 26525}, {"moveId": "POWER_WHIP", "uses": 26229}, {"moveId": "SOLAR_BEAM", "uses": 5531}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 48}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 277, "matchups": [{"opponent": "wormadam_plant", "rating": 736, "opRating": 263}, {"opponent": "swadloon", "rating": 685, "opRating": 314}, {"opponent": "abomasnow_shadow", "rating": 662, "opRating": 337}, {"opponent": "abomasnow", "rating": 643, "opRating": 356}, {"opponent": "lura<PERSON>s", "rating": 638, "opRating": 361}], "counters": [{"opponent": "clodsire", "rating": 45}, {"opponent": "gligar", "rating": 49}, {"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 128}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33069}, {"moveId": "FIRE_SPIN", "uses": 25231}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19286}, {"moveId": "SCORCHING_SANDS", "uses": 12464}, {"moveId": "BRICK_BREAK", "uses": 11220}, {"moveId": "PSYCHIC", "uses": 6151}, {"moveId": "THUNDERBOLT", "uses": 6048}, {"moveId": "FIRE_BLAST", "uses": 3219}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 48}, {"speciesId": "nuzleaf", "speciesName": "Nuz<PERSON>", "rating": 270, "matchups": [{"opponent": "oranguru", "rating": 732, "opRating": 267}, {"opponent": "whiscash", "rating": 700, "opRating": 299}, {"opponent": "gourgeist_super", "rating": 617, "opRating": 382}, {"opponent": "quagsire", "rating": 589, "opRating": 410}, {"opponent": "run<PERSON><PERSON>", "rating": 570, "opRating": 429}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "clodsire", "rating": 134}, {"opponent": "cradily", "rating": 135}, {"opponent": "talonflame", "rating": 159}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 36629}, {"moveId": "RAZOR_LEAF", "uses": 21671}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28041}, {"moveId": "FOUL_PLAY", "uses": 18898}, {"moveId": "RETURN", "uses": 5679}, {"moveId": "GRASS_KNOT", "uses": 5524}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 48}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 237, "matchups": [{"opponent": "gourgeist_super", "rating": 599, "opRating": 400}, {"opponent": "gourgeist_large", "rating": 591, "opRating": 408}, {"opponent": "gourgeist_average", "rating": 591, "opRating": 408}, {"opponent": "stunfisk", "rating": 519, "opRating": 480}, {"opponent": "whiscash", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 91}, {"opponent": "gligar", "rating": 122}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 267}, {"opponent": "clodsire", "rating": 300}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 45331}, {"moveId": "TAKE_DOWN", "uses": 12969}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 18648}, {"moveId": "WILD_CHARGE", "uses": 16669}, {"moveId": "MEGAHORN", "uses": 13131}, {"moveId": "HYPER_BEAM", "uses": 6447}, {"moveId": "SOLAR_BEAM", "uses": 3354}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "TRAILBLAZE"], "score": 48}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 224, "matchups": [{"opponent": "onix", "rating": 727, "opRating": 272}, {"opponent": "steelix", "rating": 616, "opRating": 383}, {"opponent": "magcargo", "rating": 603, "opRating": 396}, {"opponent": "ninetales_shadow", "rating": 564, "opRating": 435}, {"opponent": "turtonator", "rating": 561, "opRating": 438}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "cradily", "rating": 76}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 81}, {"opponent": "diggersby", "rating": 114}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31814}, {"moveId": "MUD_SHOT", "uses": 26486}], "chargedMoves": [{"moveId": "MUDDY_WATER", "uses": 24823}, {"moveId": "EARTH_POWER", "uses": 17624}, {"moveId": "SLUDGE_BOMB", "uses": 15844}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "SLUDGE_BOMB"], "score": 48}, {"speciesId": "stant<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 241, "matchups": [{"opponent": "slaking_shadow", "rating": 976, "opRating": 23}, {"opponent": "regigigas_shadow", "rating": 671, "opRating": 328}, {"opponent": "exploud", "rating": 562, "opRating": 437}, {"opponent": "chansey", "rating": 527, "opRating": 472}], "counters": [{"opponent": "cradily", "rating": 100}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "clodsire", "rating": 156}, {"opponent": "gligar", "rating": 221}, {"opponent": "talonflame", "rating": 225}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 49380}, {"moveId": "ZEN_HEADBUTT", "uses": 8920}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20305}, {"moveId": "STOMP", "uses": 19463}, {"moveId": "MEGAHORN", "uses": 18466}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 48}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 244, "matchups": [{"opponent": "onix_shadow", "rating": 604, "opRating": 395}, {"opponent": "steelix", "rating": 600, "opRating": 399}, {"opponent": "geodude", "rating": 552, "opRating": 447}, {"opponent": "steelix_shadow", "rating": 517, "opRating": 482}, {"opponent": "onix", "rating": 513, "opRating": 486}], "counters": [{"opponent": "cradily", "rating": 107}, {"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 43141}, {"moveId": "ROCK_SMASH", "uses": 15159}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 12029}, {"moveId": "SWIFT", "uses": 11559}, {"moveId": "TRAILBLAZE", "uses": 8084}, {"moveId": "FIRE_PUNCH", "uses": 7480}, {"moveId": "AERIAL_ACE", "uses": 7165}, {"moveId": "HIGH_HORSEPOWER", "uses": 6747}, {"moveId": "THUNDER_PUNCH", "uses": 5302}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 48}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 278, "matchups": [{"opponent": "abomasnow", "rating": 763, "opRating": 236}, {"opponent": "ferrothorn", "rating": 751, "opRating": 248}, {"opponent": "serperior", "rating": 701, "opRating": 298}, {"opponent": "abomasnow_shadow", "rating": 670, "opRating": 329}, {"opponent": "swadloon", "rating": 585, "opRating": 414}], "counters": [{"opponent": "cradily", "rating": 72}, {"opponent": "clodsire", "rating": 76}, {"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 129}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30772}, {"moveId": "CONFUSION", "uses": 27528}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 36898}, {"moveId": "PSYCHIC", "uses": 8818}, {"moveId": "FOCUS_BLAST", "uses": 8323}, {"moveId": "OVERHEAT", "uses": 4313}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 47.8}, {"speciesId": "palpitoad", "speciesName": "Palpitoad", "rating": 226, "matchups": [{"opponent": "onix", "rating": 742, "opRating": 257}, {"opponent": "ninetales", "rating": 652, "opRating": 347}, {"opponent": "steelix", "rating": 639, "opRating": 360}, {"opponent": "turtonator", "rating": 586, "opRating": 413}, {"opponent": "magcargo", "rating": 562, "opRating": 437}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "gligar", "rating": 49}, {"opponent": "cradily", "rating": 76}, {"opponent": "talonflame", "rating": 114}, {"opponent": "clodsire", "rating": 435}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31777}, {"moveId": "MUD_SHOT", "uses": 26523}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 22783}, {"moveId": "EARTH_POWER", "uses": 21667}, {"moveId": "SLUDGE_WAVE", "uses": 13805}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "WATER_PULSE"], "score": 47.3}, {"speciesId": "stantler", "speciesName": "<PERSON><PERSON>", "rating": 235, "matchups": [{"opponent": "exploud", "rating": 647, "opRating": 352}, {"opponent": "exploud_shadow", "rating": 569, "opRating": 430}, {"opponent": "slaking", "rating": 550, "opRating": 449}, {"opponent": "lilligant", "rating": 511, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "cradily", "rating": 156}, {"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 197}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 49264}, {"moveId": "ZEN_HEADBUTT", "uses": 9036}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17378}, {"moveId": "STOMP", "uses": 15955}, {"moveId": "MEGAHORN", "uses": 15549}, {"moveId": "RETURN", "uses": 9404}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 47.3}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 270, "matchups": [{"opponent": "bayleef", "rating": 733, "opRating": 266}, {"opponent": "gogoat", "rating": 704, "opRating": 295}, {"opponent": "grotle", "rating": 695, "opRating": 304}, {"opponent": "<PERSON><PERSON>e", "rating": 554, "opRating": 445}, {"opponent": "bellossom", "rating": 550, "opRating": 450}], "counters": [{"opponent": "clodsire", "rating": 64}, {"opponent": "gligar", "rating": 118}, {"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "cradily", "rating": 208}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41186}, {"moveId": "RAZOR_LEAF", "uses": 17114}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23722}, {"moveId": "PETAL_BLIZZARD", "uses": 14646}, {"moveId": "MOONBLAST", "uses": 13993}, {"moveId": "SOLAR_BEAM", "uses": 5853}]}, "moveset": ["ACID", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 47.3}, {"speciesId": "nuzleaf_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 256, "matchups": [{"opponent": "oranguru", "rating": 671, "opRating": 328}, {"opponent": "whiscash", "rating": 627, "opRating": 372}, {"opponent": "stunfisk", "rating": 566, "opRating": 433}, {"opponent": "hippopotas", "rating": 547, "opRating": 452}, {"opponent": "rhyhorn", "rating": 522, "opRating": 477}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "jumpluff_shadow", "rating": 98}, {"opponent": "clodsire", "rating": 117}, {"opponent": "cradily", "rating": 128}, {"opponent": "talonflame", "rating": 185}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 37218}, {"moveId": "RAZOR_LEAF", "uses": 21082}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30685}, {"moveId": "FOUL_PLAY", "uses": 21427}, {"moveId": "GRASS_KNOT", "uses": 6113}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 47.1}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 263, "matchups": [{"opponent": "rilla<PERSON>m", "rating": 816, "opRating": 183}, {"opponent": "thwackey", "rating": 783, "opRating": 216}, {"opponent": "bayleef", "rating": 666, "opRating": 333}, {"opponent": "gogoat", "rating": 654, "opRating": 345}, {"opponent": "torterra", "rating": 620, "opRating": 379}], "counters": [{"opponent": "clodsire", "rating": 67}, {"opponent": "gligar", "rating": 106}, {"opponent": "talonflame", "rating": 185}, {"opponent": "cradily", "rating": 201}, {"opponent": "jumpluff_shadow", "rating": 209}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 42391}, {"moveId": "RAZOR_LEAF", "uses": 15909}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23686}, {"moveId": "PETAL_BLIZZARD", "uses": 14638}, {"moveId": "MOONBLAST", "uses": 14010}, {"moveId": "SOLAR_BEAM", "uses": 5867}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 47.1}, {"speciesId": "fearow", "speciesName": "<PERSON>ow", "rating": 246, "matchups": [{"opponent": "swadloon", "rating": 637, "opRating": 362}, {"opponent": "gourgeist_super", "rating": 596, "opRating": 403}, {"opponent": "gourgeist_large", "rating": 588, "opRating": 411}, {"opponent": "gourgeist_average", "rating": 588, "opRating": 411}, {"opponent": "gogoat", "rating": 508, "opRating": 491}], "counters": [{"opponent": "talonflame", "rating": 118}, {"opponent": "clodsire", "rating": 122}, {"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "cradily", "rating": 170}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 31967}, {"moveId": "PECK", "uses": 26333}], "chargedMoves": [{"moveId": "FLY", "uses": 20051}, {"moveId": "AERIAL_ACE", "uses": 15326}, {"moveId": "DRILL_RUN", "uses": 13893}, {"moveId": "SKY_ATTACK", "uses": 5122}, {"moveId": "TWISTER", "uses": 4026}]}, "moveset": ["STEEL_WING", "FLY", "DRILL_RUN"], "score": 46.9}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 268, "matchups": [{"opponent": "wormadam_plant", "rating": 750, "opRating": 250}, {"opponent": "abomasnow", "rating": 671, "opRating": 328}, {"opponent": "abomasnow_shadow", "rating": 653, "opRating": 346}, {"opponent": "castform_sunny", "rating": 631, "opRating": 368}, {"opponent": "swadloon", "rating": 596, "opRating": 403}], "counters": [{"opponent": "clodsire", "rating": 45}, {"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "gligar", "rating": 72}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 149}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 31483}, {"moveId": "EMBER", "uses": 26817}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26325}, {"moveId": "SCORCHING_SANDS", "uses": 19297}, {"moveId": "FLAMETHROWER", "uses": 8227}, {"moveId": "FIRE_BLAST", "uses": 4445}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 46.9}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 252, "matchups": [{"opponent": "rhydon", "rating": 918, "opRating": 81}, {"opponent": "graveler_shadow", "rating": 918, "opRating": 81}, {"opponent": "gastrodon", "rating": 764, "opRating": 235}, {"opponent": "whiscash", "rating": 764, "opRating": 235}, {"opponent": "rhyperior", "rating": 756, "opRating": 243}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "talonflame", "rating": 77}, {"opponent": "clodsire", "rating": 120}, {"opponent": "cradily", "rating": 138}, {"opponent": "gligar", "rating": 152}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 8231}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4260}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4209}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3679}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3594}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3294}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3288}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2956}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2899}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2835}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2795}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2772}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2720}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2582}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2553}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2305}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2073}, {"moveId": "ZEN_HEADBUTT", "uses": 1207}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31355}, {"moveId": "ENERGY_BALL", "uses": 11077}, {"moveId": "SEED_FLARE", "uses": 9318}, {"moveId": "SOLAR_BEAM", "uses": 6417}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 46.6}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 246, "matchups": [{"opponent": "oranguru", "rating": 613, "opRating": 386}, {"opponent": "quilladin", "rating": 569, "opRating": 430}, {"opponent": "dolliv", "rating": 562, "opRating": 437}, {"opponent": "rhy<PERSON>_shadow", "rating": 525, "opRating": 474}, {"opponent": "grotle", "rating": 525, "opRating": 474}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "talonflame", "rating": 48}, {"opponent": "cradily", "rating": 138}, {"opponent": "gligar", "rating": 183}, {"opponent": "clodsire", "rating": 250}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 47994}, {"moveId": "ZEN_HEADBUTT", "uses": 10306}], "chargedMoves": [{"moveId": "STOMP", "uses": 20887}, {"moveId": "MEGAHORN", "uses": 18449}, {"moveId": "EARTHQUAKE", "uses": 11816}, {"moveId": "SKULL_BASH", "uses": 7281}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 46.4}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 221, "matchups": [{"opponent": "steelix", "rating": 673, "opRating": 326}, {"opponent": "steelix_shadow", "rating": 618, "opRating": 381}, {"opponent": "lileep", "rating": 586, "opRating": 413}, {"opponent": "obstagoon", "rating": 582, "opRating": 417}, {"opponent": "cradily", "rating": 570}], "counters": [{"opponent": "clodsire", "rating": 62}, {"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 96}, {"opponent": "diggersby", "rating": 117}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14939}, {"moveId": "FURY_CUTTER", "uses": 14536}, {"moveId": "DOUBLE_KICK", "uses": 13233}, {"moveId": "POISON_JAB", "uses": 12445}, {"moveId": "IRON_TAIL", "uses": 3162}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 16702}, {"moveId": "EARTH_POWER", "uses": 16400}, {"moveId": "SLUDGE_WAVE", "uses": 12284}, {"moveId": "SAND_TOMB", "uses": 6890}, {"moveId": "EARTHQUAKE", "uses": 5934}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 46.4}, {"speciesId": "staravia", "speciesName": "Staravia", "rating": 272, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 674, "opRating": 325}, {"opponent": "servine_shadow", "rating": 596, "opRating": 403}, {"opponent": "gastrodon", "rating": 548, "opRating": 451}, {"opponent": "lickitung", "rating": 548, "opRating": 451}, {"opponent": "swadloon", "rating": 529, "opRating": 470}], "counters": [{"opponent": "clodsire", "rating": 74}, {"opponent": "cradily", "rating": 76}, {"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 156}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 21786}, {"moveId": "SAND_ATTACK", "uses": 18437}, {"moveId": "WING_ATTACK", "uses": 18114}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20080}, {"moveId": "FLY", "uses": 17087}, {"moveId": "AERIAL_ACE", "uses": 13052}, {"moveId": "RETURN", "uses": 5929}, {"moveId": "HEAT_WAVE", "uses": 2087}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 46.2}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 225, "matchups": [{"opponent": "pidgeotto", "rating": 776, "opRating": 223}, {"opponent": "tranquill", "rating": 728, "opRating": 271}, {"opponent": "noctowl", "rating": 704, "opRating": 295}, {"opponent": "pidgeot", "rating": 552, "opRating": 447}, {"opponent": "fletchinder", "rating": 538, "opRating": 461}], "counters": [{"opponent": "clodsire", "rating": 38}, {"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 246}, {"opponent": "talonflame", "rating": 285}, {"opponent": "jumpluff_shadow", "rating": 388}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 20165}, {"moveId": "VOLT_SWITCH", "uses": 19426}, {"moveId": "MUD_SLAP", "uses": 18724}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 20233}, {"moveId": "PARABOLIC_CHARGE", "uses": 15635}, {"moveId": "GRASS_KNOT", "uses": 12144}, {"moveId": "BULLDOZE", "uses": 6589}, {"moveId": "THUNDERBOLT", "uses": 3739}]}, "moveset": ["VOLT_SWITCH", "BREAKING_SWIPE", "PARABOLIC_CHARGE"], "score": 45.7}, {"speciesId": "persian_shadow", "speciesName": "Persian (Shadow)", "rating": 225, "matchups": [{"opponent": "lilligant", "rating": 591, "opRating": 408}, {"opponent": "pumpkaboo_super", "rating": 558, "opRating": 441}, {"opponent": "pumpkaboo_large", "rating": 551, "opRating": 448}, {"opponent": "pumpkaboo_average", "rating": 551, "opRating": 448}, {"opponent": "pumpkaboo_small", "rating": 547, "opRating": 452}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "gligar", "rating": 156}, {"opponent": "cradily", "rating": 170}, {"opponent": "clodsire", "rating": 240}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 32596}, {"moveId": "SCRATCH", "uses": 25704}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 21783}, {"moveId": "POWER_GEM", "uses": 15764}, {"moveId": "FOUL_PLAY", "uses": 8170}, {"moveId": "PLAY_ROUGH", "uses": 7184}, {"moveId": "PAYBACK", "uses": 5362}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "NIGHT_SLASH", "POWER_GEM"], "score": 45.7}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 242, "matchups": [{"opponent": "rhydon_shadow", "rating": 793, "opRating": 206}, {"opponent": "stunfisk", "rating": 681, "opRating": 318}, {"opponent": "whiscash", "rating": 625, "opRating": 375}, {"opponent": "quagsire_shadow", "rating": 551, "opRating": 448}, {"opponent": "swampert_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 142}, {"opponent": "clodsire", "rating": 336}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33806}, {"moveId": "INFESTATION", "uses": 24494}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 19537}, {"moveId": "SLUDGE_BOMB", "uses": 15945}, {"moveId": "POWER_WHIP", "uses": 9795}, {"moveId": "RETURN", "uses": 8929}, {"moveId": "SOLAR_BEAM", "uses": 4111}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 45.5}, {"speciesId": "weepin<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 253, "matchups": [{"opponent": "thwackey", "rating": 785, "opRating": 214}, {"opponent": "rilla<PERSON>m", "rating": 785, "opRating": 214}, {"opponent": "gogoat", "rating": 621, "opRating": 378}, {"opponent": "servine_shadow", "rating": 592, "opRating": 407}, {"opponent": "grotle_shadow", "rating": 546, "opRating": 453}], "counters": [{"opponent": "clodsire", "rating": 52}, {"opponent": "gligar", "rating": 76}, {"opponent": "cradily", "rating": 166}, {"opponent": "talonflame", "rating": 185}, {"opponent": "jumpluff_shadow", "rating": 232}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 28663}, {"moveId": "BULLET_SEED", "uses": 20898}, {"moveId": "RAZOR_LEAF", "uses": 8743}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22743}, {"moveId": "POWER_WHIP", "uses": 19712}, {"moveId": "SEED_BOMB", "uses": 15827}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "POWER_WHIP", "SLUDGE_BOMB"], "score": 45.3}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 248, "matchups": [{"opponent": "cacturne_shadow", "rating": 733, "opRating": 266}, {"opponent": "lickitung", "rating": 635, "opRating": 364}, {"opponent": "dunsparce", "rating": 569, "opRating": 430}, {"opponent": "munchlax", "rating": 540, "opRating": 459}, {"opponent": "steelix_shadow", "rating": 536, "opRating": 463}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "clodsire", "rating": 79}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 225}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31751}, {"moveId": "SHADOW_CLAW", "uses": 26549}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28252}, {"moveId": "NIGHT_SLASH", "uses": 21482}, {"moveId": "DIG", "uses": 8602}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 45.3}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 207, "matchups": [{"opponent": "gourgeist_super", "rating": 671, "opRating": 328}, {"opponent": "gourgeist_large", "rating": 671, "opRating": 328}, {"opponent": "gourgeist_average", "rating": 671, "opRating": 328}, {"opponent": "gourgeist_small", "rating": 667, "opRating": 332}, {"opponent": "thwackey", "rating": 589, "opRating": 410}], "counters": [{"opponent": "jumpluff_shadow", "rating": 101}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 177}, {"opponent": "clodsire", "rating": 230}, {"opponent": "gligar", "rating": 339}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 36819}, {"moveId": "SCRATCH", "uses": 21481}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 32692}, {"moveId": "PLAY_ROUGH", "uses": 13355}, {"moveId": "THUNDER", "uses": 12144}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 45.1}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 213, "matchups": [{"opponent": "pumpkaboo_small", "rating": 677, "opRating": 322}, {"opponent": "pumpkaboo_super", "rating": 661, "opRating": 338}, {"opponent": "pumpkaboo_large", "rating": 661, "opRating": 338}, {"opponent": "pumpkaboo_average", "rating": 657, "opRating": 342}, {"opponent": "thwackey", "rating": 528, "opRating": 471}], "counters": [{"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "cradily", "rating": 170}, {"opponent": "talonflame", "rating": 188}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 271}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 34801}, {"moveId": "SCRATCH", "uses": 23499}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 24756}, {"moveId": "RETURN", "uses": 14124}, {"moveId": "LOW_SWEEP", "uses": 13898}, {"moveId": "HYPER_BEAM", "uses": 5466}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 44.8}, {"speciesId": "eevee", "speciesName": "Eevee", "rating": 221, "matchups": [{"opponent": "slaking_shadow", "rating": 751, "opRating": 248}, {"opponent": "slaking", "rating": 603, "opRating": 396}, {"opponent": "lilligant", "rating": 518, "opRating": 481}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "cradily", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "gligar", "rating": 244}, {"opponent": "clodsire", "rating": 274}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 33769}, {"moveId": "TACKLE", "uses": 24531}], "chargedMoves": [{"moveId": "SWIFT", "uses": 28748}, {"moveId": "BODY_SLAM", "uses": 11885}, {"moveId": "DIG", "uses": 11376}, {"moveId": "LAST_RESORT", "uses": 6226}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "DIG"], "score": 44.8}, {"speciesId": "weepinbell", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 253, "matchups": [{"opponent": "bayleef", "rating": 694, "opRating": 305}, {"opponent": "thwackey", "rating": 669, "opRating": 330}, {"opponent": "gogoat", "rating": 647, "opRating": 352}, {"opponent": "grotle", "rating": 647, "opRating": 352}, {"opponent": "whimsicott", "rating": 629, "opRating": 370}], "counters": [{"opponent": "clodsire", "rating": 81}, {"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "cradily", "rating": 208}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 28279}, {"moveId": "BULLET_SEED", "uses": 20514}, {"moveId": "RAZOR_LEAF", "uses": 9468}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19166}, {"moveId": "POWER_WHIP", "uses": 17568}, {"moveId": "SEED_BOMB", "uses": 14001}, {"moveId": "RETURN", "uses": 7656}]}, "moveset": ["ACID", "POWER_WHIP", "SLUDGE_BOMB"], "score": 44.8}, {"speciesId": "chatot", "speciesName": "Chatot", "rating": 232, "matchups": [{"opponent": "palossand", "rating": 639, "opRating": 360}, {"opponent": "gourgeist_super", "rating": 544, "opRating": 455}, {"opponent": "gourgeist_large", "rating": 544, "opRating": 455}, {"opponent": "gourgeist_average", "rating": 544, "opRating": 455}, {"opponent": "gourgeist_small", "rating": 537, "opRating": 462}], "counters": [{"opponent": "clodsire", "rating": 100}, {"opponent": "jumpluff_shadow", "rating": 114}, {"opponent": "talonflame", "rating": 118}, {"opponent": "cradily", "rating": 194}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 30444}, {"moveId": "PECK", "uses": 27856}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30831}, {"moveId": "NIGHT_SHADE", "uses": 20573}, {"moveId": "HEAT_WAVE", "uses": 7013}]}, "moveset": ["STEEL_WING", "SKY_ATTACK", "NIGHT_SHADE"], "score": 44.6}, {"speciesId": "oddish_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 252, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 679, "opRating": 320}, {"opponent": "gogoat", "rating": 620, "opRating": 379}, {"opponent": "servine_shadow", "rating": 616, "opRating": 383}, {"opponent": "grotle", "rating": 616, "opRating": 383}, {"opponent": "obstagoon", "rating": 541, "opRating": 458}], "counters": [{"opponent": "clodsire", "rating": 52}, {"opponent": "gligar", "rating": 68}, {"opponent": "cradily", "rating": 166}, {"opponent": "talonflame", "rating": 185}, {"opponent": "jumpluff_shadow", "rating": 209}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41386}, {"moveId": "RAZOR_LEAF", "uses": 16914}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23205}, {"moveId": "SEED_BOMB", "uses": 21458}, {"moveId": "MOONBLAST", "uses": 13551}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "SEED_BOMB", "SLUDGE_BOMB"], "score": 44.6}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 243, "matchups": [{"opponent": "whiscash", "rating": 691, "opRating": 308}, {"opponent": "stunfisk", "rating": 643, "opRating": 356}, {"opponent": "marowak", "rating": 610, "opRating": 389}, {"opponent": "quagsire", "rating": 584, "opRating": 415}, {"opponent": "quagsire_shadow", "rating": 547, "opRating": 452}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 106}, {"opponent": "clodsire", "rating": 146}, {"opponent": "cradily", "rating": 166}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33891}, {"moveId": "INFESTATION", "uses": 24409}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20004}, {"moveId": "ROCK_SLIDE", "uses": 14940}, {"moveId": "SLUDGE_BOMB", "uses": 11978}, {"moveId": "ANCIENT_POWER", "uses": 7132}, {"moveId": "SOLAR_BEAM", "uses": 4218}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 44.6}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 226, "matchups": [{"opponent": "unfezant_shadow", "rating": 638, "opRating": 361}, {"opponent": "onix_shadow", "rating": 579, "opRating": 420}, {"opponent": "thwackey", "rating": 563, "opRating": 436}, {"opponent": "steelix_shadow", "rating": 555, "opRating": 444}, {"opponent": "rilla<PERSON>m", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 101}, {"opponent": "clodsire", "rating": 110}, {"opponent": "cradily", "rating": 128}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24194}, {"moveId": "COUNTER", "uses": 19559}, {"moveId": "METAL_CLAW", "uses": 14528}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20660}, {"moveId": "SWIFT", "uses": 19311}, {"moveId": "TRAILBLAZE", "uses": 11318}, {"moveId": "PLAY_ROUGH", "uses": 4393}, {"moveId": "HYPER_BEAM", "uses": 2607}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 44.6}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 261, "matchups": [{"opponent": "oranguru", "rating": 774, "opRating": 225}, {"opponent": "abomasnow", "rating": 740, "opRating": 259}, {"opponent": "<PERSON><PERSON>e", "rating": 698, "opRating": 301}, {"opponent": "abomasnow_shadow", "rating": 599, "opRating": 400}, {"opponent": "castform_sunny", "rating": 557, "opRating": 442}], "counters": [{"opponent": "clodsire", "rating": 62}, {"opponent": "cradily", "rating": 72}, {"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 96}, {"opponent": "gligar", "rating": 110}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 20723}, {"moveId": "SNARL", "uses": 19994}, {"moveId": "FIRE_FANG", "uses": 17566}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 16882}, {"moveId": "BLAZE_KICK", "uses": 12413}, {"moveId": "DARKEST_LARIAT", "uses": 11677}, {"moveId": "DARK_PULSE", "uses": 10653}, {"moveId": "FLAME_CHARGE", "uses": 4740}, {"moveId": "FIRE_BLAST", "uses": 2119}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 44.4}, {"speciesId": "porygon", "speciesName": "Porygon", "rating": 230, "matchups": [{"opponent": "linoone", "rating": 577, "opRating": 422}, {"opponent": "lilligant", "rating": 555, "opRating": 444}, {"opponent": "noctowl", "rating": 551, "opRating": 448}, {"opponent": "stunfisk", "rating": 533, "opRating": 466}, {"opponent": "amoon<PERSON>s", "rating": 503, "opRating": 496}], "counters": [{"opponent": "clodsire", "rating": 127}, {"opponent": "talonflame", "rating": 133}, {"opponent": "cradily", "rating": 138}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 6526}, {"moveId": "TACKLE", "uses": 4550}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3924}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3495}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3045}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3019}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2989}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2762}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2729}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2724}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2641}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2595}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2588}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2577}, {"moveId": "CHARGE_BEAM", "uses": 2493}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2362}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2322}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2129}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1869}, {"moveId": "ZEN_HEADBUTT", "uses": 1042}], "chargedMoves": [{"moveId": "RETURN", "uses": 11627}, {"moveId": "SIGNAL_BEAM", "uses": 11367}, {"moveId": "DISCHARGE", "uses": 10817}, {"moveId": "SOLAR_BEAM", "uses": 8490}, {"moveId": "ZAP_CANNON", "uses": 5768}, {"moveId": "PSYBEAM", "uses": 5669}, {"moveId": "HYPER_BEAM", "uses": 4515}]}, "moveset": ["QUICK_ATTACK", "DISCHARGE", "HYPER_BEAM"], "score": 44.1}, {"speciesId": "snover", "speciesName": "Snover", "rating": 233, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 704, "opRating": 295}, {"opponent": "stunfisk", "rating": 697, "opRating": 302}, {"opponent": "exeggutor_alolan", "rating": 654, "opRating": 345}, {"opponent": "whiscash", "rating": 637, "opRating": 362}, {"opponent": "vibrava_shadow", "rating": 633, "opRating": 366}], "counters": [{"opponent": "talonflame", "rating": 103}, {"opponent": "cradily", "rating": 125}, {"opponent": "clodsire", "rating": 132}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "gligar", "rating": 248}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 23726}, {"moveId": "ICE_SHARD", "uses": 18640}, {"moveId": "LEAFAGE", "uses": 15991}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 24282}, {"moveId": "ENERGY_BALL", "uses": 14420}, {"moveId": "STOMP", "uses": 12398}, {"moveId": "RETURN", "uses": 7157}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 44.1}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 257, "matchups": [{"opponent": "<PERSON><PERSON>e", "rating": 648, "opRating": 352}, {"opponent": "tropius", "rating": 588, "opRating": 412}, {"opponent": "chansey", "rating": 580, "opRating": 420}, {"opponent": "lickitung", "rating": 552, "opRating": 448}, {"opponent": "munchlax", "rating": 532, "opRating": 468}], "counters": [{"opponent": "cradily", "rating": 65}, {"opponent": "clodsire", "rating": 74}, {"opponent": "jumpluff_shadow", "rating": 101}, {"opponent": "talonflame", "rating": 133}, {"opponent": "gligar", "rating": 156}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 16651}, {"moveId": "SAND_ATTACK", "uses": 14547}, {"moveId": "GUST", "uses": 14007}, {"moveId": "WING_ATTACK", "uses": 13039}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20008}, {"moveId": "CLOSE_COMBAT", "uses": 19478}, {"moveId": "FLY", "uses": 16981}, {"moveId": "HEAT_WAVE", "uses": 1926}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 44.1}, {"speciesId": "chespin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 242, "matchups": [{"opponent": "whiscash", "rating": 661, "opRating": 338}, {"opponent": "gastrodon", "rating": 639, "opRating": 360}, {"opponent": "quagsire", "rating": 625, "opRating": 375}, {"opponent": "quagsire_shadow", "rating": 573, "opRating": 426}, {"opponent": "swampert_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 48}, {"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 152}, {"opponent": "clodsire", "rating": 225}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 48831}, {"moveId": "TAKE_DOWN", "uses": 9469}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27604}, {"moveId": "SEED_BOMB", "uses": 22253}, {"moveId": "GYRO_BALL", "uses": 8426}]}, "moveset": ["VINE_WHIP", "BODY_SLAM", "SEED_BOMB"], "score": 43.9}, {"speciesId": "snover_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 227, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 718, "opRating": 281}, {"opponent": "torterra", "rating": 704, "opRating": 295}, {"opponent": "garcho<PERSON>_shadow", "rating": 647, "opRating": 352}, {"opponent": "vibrava", "rating": 633, "opRating": 366}, {"opponent": "stunfisk", "rating": 609, "opRating": 390}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "talonflame", "rating": 129}, {"opponent": "cradily", "rating": 149}, {"opponent": "gligar", "rating": 229}, {"opponent": "jumpluff_shadow", "rating": 251}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 23911}, {"moveId": "ICE_SHARD", "uses": 18446}, {"moveId": "LEAFAGE", "uses": 15997}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 27357}, {"moveId": "ENERGY_BALL", "uses": 16213}, {"moveId": "STOMP", "uses": 14682}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 43.9}, {"speciesId": "ferroseed", "speciesName": "Ferroseed", "rating": 229, "matchups": [{"opponent": "grotle_shadow", "rating": 655, "opRating": 344}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 642, "opRating": 357}, {"opponent": "thwackey", "rating": 609, "opRating": 390}, {"opponent": "lileep", "rating": 558, "opRating": 441}, {"opponent": "wigglytuff", "rating": 512, "opRating": 487}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "clodsire", "rating": 110}, {"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 141}, {"opponent": "cradily", "rating": 343}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 30632}, {"moveId": "TACKLE", "uses": 27668}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 25202}, {"moveId": "RETURN", "uses": 17095}, {"moveId": "FLASH_CANNON", "uses": 8001}, {"moveId": "GYRO_BALL", "uses": 7980}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "RETURN"], "score": 43.7}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 236, "matchups": [{"opponent": "whiscash", "rating": 692, "opRating": 307}, {"opponent": "stunfisk", "rating": 684, "opRating": 315}, {"opponent": "marowak", "rating": 607, "opRating": 392}, {"opponent": "quagsire", "rating": 588, "opRating": 411}, {"opponent": "quagsire_shadow", "rating": 542, "opRating": 457}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 106}, {"opponent": "cradily", "rating": 131}, {"opponent": "clodsire", "rating": 331}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 39487}, {"moveId": "BITE", "uses": 18813}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25704}, {"moveId": "POWER_WHIP", "uses": 23965}, {"moveId": "ENERGY_BALL", "uses": 8465}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 43.5}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 259, "matchups": [{"opponent": "stunfisk", "rating": 692, "opRating": 307}, {"opponent": "gastrodon", "rating": 626, "opRating": 373}, {"opponent": "whiscash", "rating": 600, "opRating": 400}, {"opponent": "marowak", "rating": 561, "opRating": 438}, {"opponent": "steelix", "rating": 538, "opRating": 461}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 40}, {"opponent": "cradily", "rating": 104}, {"opponent": "gligar", "rating": 110}, {"opponent": "clodsire", "rating": 168}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38871}, {"moveId": "RAZOR_LEAF", "uses": 19429}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28315}, {"moveId": "SOLAR_BEAM", "uses": 11500}, {"moveId": "DAZZLING_GLEAM", "uses": 11275}, {"moveId": "HYPER_BEAM", "uses": 7238}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 43.2}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 224, "matchups": [{"opponent": "palossand", "rating": 627, "opRating": 372}, {"opponent": "gourgeist_super", "rating": 531, "opRating": 468}, {"opponent": "gourgeist_large", "rating": 531, "opRating": 468}, {"opponent": "gourgeist_average", "rating": 531, "opRating": 468}, {"opponent": "gourgeist_small", "rating": 531, "opRating": 468}], "counters": [{"opponent": "clodsire", "rating": 100}, {"opponent": "talonflame", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 137}, {"opponent": "cradily", "rating": 194}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 30380}, {"moveId": "FEINT_ATTACK", "uses": 27920}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18971}, {"moveId": "DRILL_PECK", "uses": 16925}, {"moveId": "AIR_CUTTER", "uses": 16260}, {"moveId": "AERIAL_ACE", "uses": 6049}]}, "moveset": ["STEEL_WING", "BRAVE_BIRD", "DRILL_PECK"], "score": 43}, {"speciesId": "staravia_shadow", "speciesName": "Staravia (Shadow)", "rating": 238, "matchups": [{"opponent": "amoon<PERSON>s", "rating": 644, "opRating": 355}, {"opponent": "pumpkaboo_super", "rating": 607, "opRating": 392}, {"opponent": "<PERSON><PERSON>e", "rating": 596, "opRating": 403}, {"opponent": "lickitung", "rating": 537, "opRating": 462}, {"opponent": "chansey", "rating": 503, "opRating": 496}], "counters": [{"opponent": "cradily", "rating": 76}, {"opponent": "clodsire", "rating": 103}, {"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 22157}, {"moveId": "SAND_ATTACK", "uses": 18370}, {"moveId": "WING_ATTACK", "uses": 17814}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22396}, {"moveId": "FLY", "uses": 19065}, {"moveId": "AERIAL_ACE", "uses": 14554}, {"moveId": "HEAT_WAVE", "uses": 2269}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 43}, {"speciesId": "gabite", "speciesName": "Gabite", "rating": 198, "matchups": [{"opponent": "onix", "rating": 640, "opRating": 359}, {"opponent": "nidoqueen", "rating": 636, "opRating": 363}, {"opponent": "pignite", "rating": 598, "opRating": 401}, {"opponent": "ninetales", "rating": 590, "opRating": 409}, {"opponent": "stunfisk", "rating": 590, "opRating": 409}], "counters": [{"opponent": "jumpluff_shadow", "rating": 26}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 315}, {"opponent": "clodsire", "rating": 415}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 49293}, {"moveId": "TAKE_DOWN", "uses": 9007}], "chargedMoves": [{"moveId": "DIG", "uses": 19468}, {"moveId": "FLAMETHROWER", "uses": 16567}, {"moveId": "TWISTER", "uses": 12248}, {"moveId": "RETURN", "uses": 10060}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 42.8}, {"speciesId": "herdier", "speciesName": "<PERSON><PERSON>", "rating": 198, "matchups": [{"opponent": "slaking_shadow", "rating": 563, "opRating": 436}, {"opponent": "lampent_shadow", "rating": 535, "opRating": 464}, {"opponent": "pupitar", "rating": 517, "opRating": 482}, {"opponent": "onix", "rating": 510, "opRating": 489}, {"opponent": "aipom", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "gligar", "rating": 95}, {"opponent": "cradily", "rating": 319}, {"opponent": "clodsire", "rating": 365}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 29831}, {"moveId": "LICK", "uses": 20286}, {"moveId": "TAKE_DOWN", "uses": 8192}], "chargedMoves": [{"moveId": "DIG", "uses": 23518}, {"moveId": "PLAY_ROUGH", "uses": 18280}, {"moveId": "THUNDERBOLT", "uses": 16558}]}, "moveset": ["SAND_ATTACK", "DIG", "THUNDERBOLT"], "score": 42.8}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 262, "matchups": [{"opponent": "quagsire", "rating": 645, "opRating": 354}, {"opponent": "quagsire_shadow", "rating": 641, "opRating": 358}, {"opponent": "gastrodon", "rating": 641, "opRating": 358}, {"opponent": "dunsparce", "rating": 636, "opRating": 363}, {"opponent": "miltank", "rating": 517, "opRating": 482}], "counters": [{"opponent": "jumpluff_shadow", "rating": 19}, {"opponent": "talonflame", "rating": 33}, {"opponent": "gligar", "rating": 45}, {"opponent": "cradily", "rating": 100}, {"opponent": "clodsire", "rating": 192}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 32254}, {"moveId": "MAGICAL_LEAF", "uses": 26046}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25339}, {"moveId": "UPPER_HAND", "uses": 23124}, {"moveId": "PETAL_BLIZZARD", "uses": 6834}, {"moveId": "SOLAR_BEAM", "uses": 2777}]}, "moveset": ["BULLET_SEED", "CLOSE_COMBAT", "UPPER_HAND"], "score": 42.8}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 241, "matchups": [{"opponent": "wormadam_plant", "rating": 736, "opRating": 263}, {"opponent": "ferrothorn", "rating": 666, "opRating": 333}, {"opponent": "swadloon", "rating": 569, "opRating": 430}, {"opponent": "abomasnow", "rating": 555, "opRating": 444}, {"opponent": "bellossom", "rating": 537, "opRating": 462}], "counters": [{"opponent": "clodsire", "rating": 45}, {"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "gligar", "rating": 72}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 149}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33551}, {"moveId": "FIRE_SPIN", "uses": 24749}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19294}, {"moveId": "SCORCHING_SANDS", "uses": 12475}, {"moveId": "BRICK_BREAK", "uses": 11192}, {"moveId": "PSYCHIC", "uses": 6138}, {"moveId": "THUNDERBOLT", "uses": 6035}, {"moveId": "FIRE_BLAST", "uses": 3238}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 42.8}, {"speciesId": "te<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 202, "matchups": [{"opponent": "lilligant", "rating": 577, "opRating": 422}, {"opponent": "sandygast", "rating": 545, "opRating": 454}, {"opponent": "pumpkaboo_small", "rating": 542, "opRating": 457}, {"opponent": "palossand", "rating": 538, "opRating": 461}, {"opponent": "pumpkaboo_average", "rating": 528, "opRating": 471}], "counters": [{"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 170}, {"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 290}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 32088}, {"moveId": "SCRATCH", "uses": 26212}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16546}, {"moveId": "CROSS_CHOP", "uses": 15764}, {"moveId": "CRUNCH", "uses": 9747}, {"moveId": "TRAILBLAZE", "uses": 9726}, {"moveId": "PLAY_ROUGH", "uses": 3620}, {"moveId": "RETURN", "uses": 2808}]}, "moveset": ["LICK", "SWIFT", "CROSS_CHOP"], "score": 42.8}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 206, "matchups": [{"opponent": "sandygast", "rating": 612, "opRating": 388}, {"opponent": "palossand", "rating": 588, "opRating": 412}, {"opponent": "oricorio_baile", "rating": 584, "opRating": 416}, {"opponent": "pidgeotto", "rating": 524, "opRating": 476}, {"opponent": "dhelmise", "rating": 516, "opRating": 484}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "gligar", "rating": 91}, {"opponent": "clodsire", "rating": 305}, {"opponent": "cradily", "rating": 326}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23706}, {"moveId": "SAND_ATTACK", "uses": 18897}, {"moveId": "LICK", "uses": 11485}, {"moveId": "TAKE_DOWN", "uses": 4134}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 23885}, {"moveId": "WILD_CHARGE", "uses": 23876}, {"moveId": "PLAY_ROUGH", "uses": 10490}]}, "moveset": ["SAND_ATTACK", "WILD_CHARGE", "CRUNCH"], "score": 42.5}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 207, "matchups": [{"opponent": "lampent_shadow", "rating": 830, "opRating": 169}, {"opponent": "chandelure_shadow", "rating": 725, "opRating": 274}, {"opponent": "gourgeist_small", "rating": 629, "opRating": 370}, {"opponent": "run<PERSON><PERSON>", "rating": 572, "opRating": 427}, {"opponent": "trevenant", "rating": 564, "opRating": 435}], "counters": [{"opponent": "cradily", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 133}, {"opponent": "clodsire", "rating": 134}, {"opponent": "talonflame", "rating": 181}, {"opponent": "gligar", "rating": 209}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35406}, {"moveId": "SCRATCH", "uses": 22894}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28793}, {"moveId": "LOW_SWEEP", "uses": 16170}, {"moveId": "HYPER_BEAM", "uses": 13240}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 42.3}, {"speciesId": "raticate", "speciesName": "Raticate", "rating": 201, "matchups": [{"opponent": "exploud_shadow", "rating": 618, "opRating": 381}, {"opponent": "rowlet", "rating": 610, "opRating": 389}, {"opponent": "purugly", "rating": 602, "opRating": 397}, {"opponent": "lilligant", "rating": 557, "opRating": 442}, {"opponent": "snover", "rating": 512, "opRating": 487}], "counters": [{"opponent": "talonflame", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 156}, {"opponent": "cradily", "rating": 281}, {"opponent": "clodsire", "rating": 343}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39796}, {"moveId": "BITE", "uses": 18504}], "chargedMoves": [{"moveId": "HYPER_FANG", "uses": 25446}, {"moveId": "DIG", "uses": 17503}, {"moveId": "RETURN", "uses": 8697}, {"moveId": "HYPER_BEAM", "uses": 6748}]}, "moveset": ["QUICK_ATTACK", "HYPER_FANG", "DIG"], "score": 41.9}, {"speciesId": "teddiu<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 199, "matchups": [{"opponent": "trevenant", "rating": 528, "opRating": 471}, {"opponent": "exploud_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "clodsire", "rating": 132}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "cradily", "rating": 170}, {"opponent": "talonflame", "rating": 170}, {"opponent": "gligar", "rating": 244}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 33088}, {"moveId": "SCRATCH", "uses": 25212}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17583}, {"moveId": "CROSS_CHOP", "uses": 16473}, {"moveId": "CRUNCH", "uses": 10225}, {"moveId": "TRAILBLAZE", "uses": 10129}, {"moveId": "PLAY_ROUGH", "uses": 3794}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "SWIFT", "CROSS_CHOP"], "score": 41.9}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 188, "matchups": [{"opponent": "stunfisk", "rating": 657, "opRating": 342}, {"opponent": "clodsire", "rating": 618, "opRating": 381}, {"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 596, "opRating": 403}, {"opponent": "tauros_blaze", "rating": 526, "opRating": 473}, {"opponent": "stunfisk_galarian", "rating": 517, "opRating": 482}], "counters": [{"opponent": "jumpluff_shadow", "rating": 22}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 81}, {"opponent": "diggersby", "rating": 117}, {"opponent": "cradily", "rating": 315}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36415}, {"moveId": "ROCK_THROW", "uses": 21885}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 17511}, {"moveId": "ROCK_SLIDE", "uses": 16238}, {"moveId": "OUTRAGE", "uses": 12734}, {"moveId": "FOCUS_BLAST", "uses": 11703}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "ROCK_SLIDE"], "score": 41.6}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 202, "matchups": [{"opponent": "noctowl", "rating": 677, "opRating": 322}, {"opponent": "pidgeot", "rating": 580, "opRating": 419}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 580, "opRating": 419}, {"opponent": "tropius", "rating": 569, "opRating": 430}, {"opponent": "jumpluff_shadow", "rating": 521, "opRating": 478}], "counters": [{"opponent": "clodsire", "rating": 36}, {"opponent": "diggersby", "rating": 43}, {"opponent": "cradily", "rating": 52}, {"opponent": "gligar", "rating": 53}, {"opponent": "talonflame", "rating": 300}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34036}, {"moveId": "ASTONISH", "uses": 24264}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 31028}, {"moveId": "THUNDERBOLT", "uses": 19003}, {"moveId": "THUNDER", "uses": 8275}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 41.4}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 228, "matchups": [{"opponent": "lickitung", "rating": 704, "opRating": 296}, {"opponent": "amoon<PERSON>s", "rating": 612, "opRating": 388}, {"opponent": "rhyhorn", "rating": 592, "opRating": 408}, {"opponent": "<PERSON><PERSON>e", "rating": 560, "opRating": 440}, {"opponent": "whiscash", "rating": 524, "opRating": 476}], "counters": [{"opponent": "cradily", "rating": 65}, {"opponent": "clodsire", "rating": 88}, {"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 17232}, {"moveId": "SAND_ATTACK", "uses": 14493}, {"moveId": "GUST", "uses": 13622}, {"moveId": "WING_ATTACK", "uses": 12929}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 19998}, {"moveId": "CLOSE_COMBAT", "uses": 19482}, {"moveId": "FLY", "uses": 16976}, {"moveId": "HEAT_WAVE", "uses": 1937}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 41.4}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 217, "matchups": [{"opponent": "whiscash", "rating": 670, "opRating": 329}, {"opponent": "stunfisk", "rating": 623, "opRating": 376}, {"opponent": "marowak", "rating": 583, "opRating": 416}, {"opponent": "quagsire", "rating": 555, "opRating": 444}, {"opponent": "quagsire_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 91}, {"opponent": "cradily", "rating": 128}, {"opponent": "clodsire", "rating": 338}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 39572}, {"moveId": "BITE", "uses": 18728}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 27063}, {"moveId": "GRASS_KNOT", "uses": 25780}, {"moveId": "SOLAR_BEAM", "uses": 5476}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "CRUNCH"], "score": 41.2}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 200, "matchups": [{"opponent": "chandelure_shadow", "rating": 656, "opRating": 343}, {"opponent": "lampent_shadow", "rating": 656, "opRating": 343}, {"opponent": "phanpy", "rating": 562, "opRating": 437}, {"opponent": "regigigas_shadow", "rating": 562, "opRating": 437}, {"opponent": "turtwig", "rating": 546, "opRating": 453}], "counters": [{"opponent": "clodsire", "rating": 120}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "cradily", "rating": 149}, {"opponent": "gligar", "rating": 160}, {"opponent": "talonflame", "rating": 185}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 33410}, {"moveId": "SNARL", "uses": 24890}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 18714}, {"moveId": "SHADOW_BALL", "uses": 15639}, {"moveId": "SLUDGE_BOMB", "uses": 12069}, {"moveId": "FLAMETHROWER", "uses": 11965}]}, "moveset": ["SHADOW_CLAW", "FOUL_PLAY", "SHADOW_BALL"], "score": 41.2}, {"speciesId": "ferroseed_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 214, "matchups": [{"opponent": "grotle", "rating": 655, "opRating": 344}, {"opponent": "thwackey", "rating": 651, "opRating": 348}, {"opponent": "torterra", "rating": 642, "opRating": 357}, {"opponent": "onix", "rating": 626, "opRating": 373}, {"opponent": "bayleef", "rating": 613, "opRating": 386}], "counters": [{"opponent": "talonflame", "rating": 77}, {"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 118}, {"opponent": "clodsire", "rating": 137}, {"opponent": "cradily", "rating": 392}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 31327}, {"moveId": "TACKLE", "uses": 26973}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 35586}, {"moveId": "FLASH_CANNON", "uses": 11370}, {"moveId": "GYRO_BALL", "uses": 11120}, {"moveId": "FRUSTRATION", "uses": 137}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "FLASH_CANNON"], "score": 41}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 217, "matchups": [{"opponent": "rhydon_shadow", "rating": 806, "opRating": 193}, {"opponent": "rhydon", "rating": 793, "opRating": 206}, {"opponent": "whiscash", "rating": 655, "opRating": 344}, {"opponent": "stunfisk", "rating": 646, "opRating": 353}, {"opponent": "quagsire_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 81}, {"opponent": "gligar", "rating": 114}, {"opponent": "clodsire", "rating": 134}, {"opponent": "cradily", "rating": 138}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 34035}, {"moveId": "INFESTATION", "uses": 24265}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 22497}, {"moveId": "SLUDGE_BOMB", "uses": 19721}, {"moveId": "POWER_WHIP", "uses": 11253}, {"moveId": "SOLAR_BEAM", "uses": 4750}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 40.7}, {"speciesId": "rowlet", "speciesName": "<PERSON><PERSON>", "rating": 226, "matchups": [{"opponent": "rhy<PERSON>_shadow", "rating": 730, "opRating": 269}, {"opponent": "gastrodon", "rating": 668, "opRating": 331}, {"opponent": "whiscash", "rating": 652, "opRating": 347}, {"opponent": "rhyhorn", "rating": 642, "opRating": 357}, {"opponent": "marowak_shadow", "rating": 545, "opRating": 454}], "counters": [{"opponent": "jumpluff_shadow", "rating": 45}, {"opponent": "talonflame", "rating": 55}, {"opponent": "cradily", "rating": 97}, {"opponent": "clodsire", "rating": 98}, {"opponent": "gligar", "rating": 156}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 16809}, {"moveId": "MAGICAL_LEAF", "uses": 16457}, {"moveId": "TACKLE", "uses": 14020}, {"moveId": "RAZOR_LEAF", "uses": 11063}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 40254}, {"moveId": "ENERGY_BALL", "uses": 18046}]}, "moveset": ["LEAFAGE", "SEED_BOMB", "ENERGY_BALL"], "score": 40.5}, {"speciesId": "bulbasaur", "speciesName": "Bulbasaur", "rating": 196, "matchups": [{"opponent": "chesnaught", "rating": 695, "opRating": 304}, {"opponent": "thwackey", "rating": 645, "opRating": 354}, {"opponent": "grotle_shadow", "rating": 600, "opRating": 400}, {"opponent": "grotle", "rating": 595, "opRating": 404}, {"opponent": "stunfisk", "rating": 525, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "gligar", "rating": 118}, {"opponent": "clodsire", "rating": 286}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 36772}, {"moveId": "TACKLE", "uses": 21528}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19073}, {"moveId": "POWER_WHIP", "uses": 17574}, {"moveId": "SEED_BOMB", "uses": 13985}, {"moveId": "RETURN", "uses": 7679}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 39.8}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 202, "matchups": [{"opponent": "delcatty", "rating": 583, "opRating": 416}, {"opponent": "onix", "rating": 579, "opRating": 420}, {"opponent": "castform", "rating": 571, "opRating": 428}, {"opponent": "steelix", "rating": 555, "opRating": 444}, {"opponent": "dunsparce", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 101}, {"opponent": "clodsire", "rating": 103}, {"opponent": "cradily", "rating": 107}, {"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 159}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24782}, {"moveId": "COUNTER", "uses": 19324}, {"moveId": "METAL_CLAW", "uses": 14191}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20693}, {"moveId": "SWIFT", "uses": 19306}, {"moveId": "TRAILBLAZE", "uses": 11317}, {"moveId": "PLAY_ROUGH", "uses": 4394}, {"moveId": "HYPER_BEAM", "uses": 2607}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 39.8}, {"speciesId": "turtwig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 221, "matchups": [{"opponent": "whiscash", "rating": 633, "opRating": 366}, {"opponent": "marshtomp", "rating": 603, "opRating": 396}, {"opponent": "cu<PERSON>_shadow", "rating": 592, "opRating": 407}, {"opponent": "lilligant", "rating": 566, "opRating": 433}, {"opponent": "cubone", "rating": 551, "opRating": 448}], "counters": [{"opponent": "cradily", "rating": 79}, {"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 107}, {"opponent": "gligar", "rating": 125}, {"opponent": "clodsire", "rating": 132}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 31284}, {"moveId": "RAZOR_LEAF", "uses": 27016}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24635}, {"moveId": "SEED_BOMB", "uses": 19022}, {"moveId": "ENERGY_BALL", "uses": 8539}, {"moveId": "RETURN", "uses": 6036}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 39.4}, {"speciesId": "turtwig_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 208, "matchups": [{"opponent": "stunfisk", "rating": 592, "opRating": 407}, {"opponent": "cu<PERSON>_shadow", "rating": 577, "opRating": 422}, {"opponent": "arboliva", "rating": 529, "opRating": 470}, {"opponent": "lilligant", "rating": 525, "opRating": 474}, {"opponent": "ludico<PERSON>", "rating": 514, "opRating": 485}], "counters": [{"opponent": "gligar", "rating": 99}, {"opponent": "cradily", "rating": 107}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "talonflame", "rating": 159}, {"opponent": "clodsire", "rating": 161}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 30941}, {"moveId": "RAZOR_LEAF", "uses": 27359}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28073}, {"moveId": "SEED_BOMB", "uses": 20848}, {"moveId": "ENERGY_BALL", "uses": 9309}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 39.1}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 199, "matchups": [{"opponent": "arcanine_<PERSON><PERSON>an", "rating": 681, "opRating": 318}], "counters": [{"opponent": "jumpluff_shadow", "rating": 32}, {"opponent": "gligar", "rating": 57}, {"opponent": "talonflame", "rating": 111}, {"opponent": "cradily", "rating": 121}, {"opponent": "clodsire", "rating": 156}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5282}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4572}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4198}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4059}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4004}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3611}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3558}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3436}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3384}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3371}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3370}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3355}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3091}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2974}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2722}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2408}, {"moveId": "ZEN_HEADBUTT", "uses": 836}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 32787}, {"moveId": "FOCUS_BLAST", "uses": 12948}, {"moveId": "THUNDER", "uses": 7740}, {"moveId": "GIGA_IMPACT", "uses": 4605}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HIDDEN_POWER_GROUND", "THUNDER", "FOCUS_BLAST"], "score": 38.9}, {"speciesId": "skiddo", "speciesName": "Skiddo", "rating": 215, "matchups": [{"opponent": "herdier", "rating": 605, "opRating": 394}, {"opponent": "regigigas_shadow", "rating": 586, "opRating": 413}, {"opponent": "linoone_galarian", "rating": 562, "opRating": 437}, {"opponent": "kartana", "rating": 523, "opRating": 476}, {"opponent": "obstagoon_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "gligar", "rating": 68}, {"opponent": "talonflame", "rating": 74}, {"opponent": "clodsire", "rating": 88}, {"opponent": "cradily", "rating": 190}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 38185}, {"moveId": "ZEN_HEADBUTT", "uses": 20115}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 20806}, {"moveId": "SEED_BOMB", "uses": 19736}, {"moveId": "BRICK_BREAK", "uses": 17789}]}, "moveset": ["ROCK_SMASH", "SEED_BOMB", "ROCK_SLIDE"], "score": 38.9}, {"speciesId": "raticate_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 191, "matchups": [{"opponent": "exploud", "rating": 618, "opRating": 381}, {"opponent": "purugly", "rating": 545, "opRating": 454}, {"opponent": "exploud_shadow", "rating": 536, "opRating": 463}, {"opponent": "slaking", "rating": 516, "opRating": 483}], "counters": [{"opponent": "cradily", "rating": 97}, {"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "clodsire", "rating": 122}, {"opponent": "talonflame", "rating": 159}, {"opponent": "gligar", "rating": 194}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 40735}, {"moveId": "BITE", "uses": 17565}], "chargedMoves": [{"moveId": "HYPER_FANG", "uses": 30240}, {"moveId": "DIG", "uses": 19933}, {"moveId": "HYPER_BEAM", "uses": 8123}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "HYPER_FANG", "DIG"], "score": 38}, {"speciesId": "gabite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 184, "matchups": [{"opponent": "onix", "rating": 606, "opRating": 393}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 571, "opRating": 428}, {"opponent": "heatran", "rating": 556, "opRating": 443}, {"opponent": "stunfisk", "rating": 530, "opRating": 469}, {"opponent": "pignite", "rating": 518, "opRating": 481}], "counters": [{"opponent": "jumpluff_shadow", "rating": 26}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 170}, {"opponent": "clodsire", "rating": 204}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 49290}, {"moveId": "TAKE_DOWN", "uses": 9010}], "chargedMoves": [{"moveId": "DIG", "uses": 23033}, {"moveId": "FLAMETHROWER", "uses": 20083}, {"moveId": "TWISTER", "uses": 15074}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 37.5}, {"speciesId": "g<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 207, "matchups": [{"opponent": "whiscash", "rating": 675, "opRating": 324}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 658, "opRating": 341}, {"opponent": "marowak", "rating": 645, "opRating": 354}, {"opponent": "gastrodon", "rating": 611, "opRating": 388}, {"opponent": "quagsire", "rating": 534, "opRating": 465}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "gligar", "rating": 61}, {"opponent": "cradily", "rating": 72}, {"opponent": "clodsire", "rating": 74}, {"opponent": "talonflame", "rating": 159}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 29819}, {"moveId": "QUICK_ATTACK", "uses": 28481}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33186}, {"moveId": "AERIAL_ACE", "uses": 18553}, {"moveId": "GRASS_KNOT", "uses": 6647}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "AERIAL_ACE"], "score": 37.5}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 183, "matchups": [{"opponent": "noctowl", "rating": 559, "opRating": 440}, {"opponent": "lickitung", "rating": 511, "opRating": 488}, {"opponent": "lampent_shadow", "rating": 511, "opRating": 488}, {"opponent": "aipom", "rating": 511, "opRating": 488}, {"opponent": "lilligant", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 59}, {"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "clodsire", "rating": 189}, {"opponent": "gligar", "rating": 255}, {"opponent": "cradily", "rating": 281}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7092}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4319}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3797}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3408}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3348}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3321}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3021}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3009}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2986}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2928}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2888}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2867}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2794}, {"moveId": "CHARGE_BEAM", "uses": 2596}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2595}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2594}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2410}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2026}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25677}, {"moveId": "HYPER_BEAM", "uses": 12531}, {"moveId": "SOLAR_BEAM", "uses": 11150}, {"moveId": "ZAP_CANNON", "uses": 9015}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 36.4}, {"speciesId": "trapinch_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 185, "matchups": [{"opponent": "slaking", "rating": 625, "opRating": 375}, {"opponent": "steelix", "rating": 591, "opRating": 408}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 566, "opRating": 433}, {"opponent": "slaking_shadow", "rating": 558, "opRating": 441}, {"opponent": "steelix_shadow", "rating": 554, "opRating": 445}], "counters": [{"opponent": "gligar", "rating": 45}, {"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "cradily", "rating": 100}, {"opponent": "talonflame", "rating": 114}, {"opponent": "clodsire", "rating": 156}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24309}, {"moveId": "MUD_SHOT", "uses": 22643}, {"moveId": "STRUGGLE_BUG", "uses": 11382}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22129}, {"moveId": "SCORCHING_SANDS", "uses": 21731}, {"moveId": "DIG", "uses": 7295}, {"moveId": "SAND_TOMB", "uses": 7162}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "SCORCHING_SANDS", "CRUNCH"], "score": 35.9}, {"speciesId": "trapinch", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 176, "matchups": [{"opponent": "steelix", "rating": 679, "opRating": 320}, {"opponent": "slaking_shadow", "rating": 625, "opRating": 375}, {"opponent": "steelix_shadow", "rating": 591, "opRating": 408}, {"opponent": "regigigas_shadow", "rating": 566, "opRating": 433}, {"opponent": "onix", "rating": 529, "opRating": 470}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 61}, {"opponent": "cradily", "rating": 100}, {"opponent": "talonflame", "rating": 114}, {"opponent": "clodsire", "rating": 156}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24840}, {"moveId": "MUD_SHOT", "uses": 22172}, {"moveId": "STRUGGLE_BUG", "uses": 11300}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 18992}, {"moveId": "CRUNCH", "uses": 18168}, {"moveId": "RETURN", "uses": 8452}, {"moveId": "DIG", "uses": 6389}, {"moveId": "SAND_TOMB", "uses": 6263}]}, "moveset": ["SAND_ATTACK", "SCORCHING_SANDS", "CRUNCH"], "score": 35}, {"speciesId": "grou<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 177, "matchups": [{"opponent": "steelix_shadow", "rating": 666, "opRating": 333}, {"opponent": "onix", "rating": 614, "opRating": 385}, {"opponent": "pupitar_shadow", "rating": 561, "opRating": 438}, {"opponent": "lilligant", "rating": 533, "opRating": 466}, {"opponent": "steelix", "rating": 514, "opRating": 485}], "counters": [{"opponent": "jumpluff_shadow", "rating": 22}, {"opponent": "gligar", "rating": 57}, {"opponent": "talonflame", "rating": 81}, {"opponent": "cradily", "rating": 97}, {"opponent": "clodsire", "rating": 298}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31936}, {"moveId": "DRAGON_TAIL", "uses": 26364}], "chargedMoves": [{"moveId": "PRECIPICE_BLADES", "uses": 21950}, {"moveId": "FIRE_PUNCH", "uses": 20045}, {"moveId": "SOLAR_BEAM", "uses": 7090}, {"moveId": "EARTHQUAKE", "uses": 5766}, {"moveId": "FIRE_BLAST", "uses": 3304}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "PRECIPICE_BLADES", "FIRE_PUNCH"], "score": 34.8}, {"speciesId": "bulbasaur_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 188, "matchups": [{"opponent": "thwackey", "rating": 620, "opRating": 379}, {"opponent": "gogoat", "rating": 600, "opRating": 400}, {"opponent": "grotle", "rating": 600, "opRating": 400}, {"opponent": "geodude_shadow", "rating": 587, "opRating": 412}, {"opponent": "grotle_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 55}, {"opponent": "clodsire", "rating": 86}, {"opponent": "gligar", "rating": 91}, {"opponent": "cradily", "rating": 121}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 37282}, {"moveId": "TACKLE", "uses": 21018}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22736}, {"moveId": "POWER_WHIP", "uses": 19751}, {"moveId": "SEED_BOMB", "uses": 15827}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 34.6}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 155, "matchups": [{"opponent": "regigigas_shadow", "rating": 724, "opRating": 275}, {"opponent": "exploud", "rating": 637, "opRating": 362}, {"opponent": "lilligant", "rating": 614, "opRating": 385}, {"opponent": "exploud_shadow", "rating": 559, "opRating": 440}, {"opponent": "blissey", "rating": 511, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "talonflame", "rating": 118}, {"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 211}, {"opponent": "gligar", "rating": 278}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7607}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4322}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3832}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3364}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3324}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3304}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2998}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2988}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2949}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2871}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2841}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2821}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2808}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2594}, {"moveId": "CHARGE_BEAM", "uses": 2591}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2562}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2366}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2046}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25576}, {"moveId": "HYPER_BEAM", "uses": 12555}, {"moveId": "SOLAR_BEAM", "uses": 11137}, {"moveId": "ZAP_CANNON", "uses": 9014}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 34.3}, {"speciesId": "komala", "speciesName": "Komala", "rating": 165, "matchups": [{"opponent": "exploud", "rating": 605, "opRating": 394}, {"opponent": "exploud_shadow", "rating": 559, "opRating": 440}, {"opponent": "sandygast", "rating": 536, "opRating": 463}, {"opponent": "palossand", "rating": 532, "opRating": 467}, {"opponent": "blissey", "rating": 504, "opRating": 495}], "counters": [{"opponent": "clodsire", "rating": 79}, {"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "cradily", "rating": 125}, {"opponent": "gligar", "rating": 137}, {"opponent": "talonflame", "rating": 281}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 57421}, {"moveId": "YAWN", "uses": 879}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 25428}, {"moveId": "BULLDOZE", "uses": 17239}, {"moveId": "PLAY_ROUGH", "uses": 15692}]}, "moveset": ["ROLLOUT", "PAYBACK", "BULLDOZE"], "score": 34.1}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 207, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 686, "opRating": 313}, {"opponent": "rilla<PERSON>m", "rating": 686, "opRating": 313}, {"opponent": "gourgeist_super", "rating": 569, "opRating": 430}, {"opponent": "swadloon", "rating": 544, "opRating": 455}, {"opponent": "wormadam_plant", "rating": 516, "opRating": 483}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 33}, {"opponent": "clodsire", "rating": 40}, {"opponent": "cradily", "rating": 59}, {"opponent": "gligar", "rating": 110}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 28489}, {"moveId": "PECK", "uses": 19087}, {"moveId": "ROCK_SMASH", "uses": 10730}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 36447}, {"moveId": "ROCK_BLAST", "uses": 15243}, {"moveId": "FLASH_CANNON", "uses": 6625}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 33.9}, {"speciesId": "exeggcute_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 186, "matchups": [{"opponent": "whiscash", "rating": 661, "opRating": 338}, {"opponent": "gastrodon", "rating": 647, "opRating": 352}, {"opponent": "quagsire", "rating": 623, "opRating": 376}, {"opponent": "marowak_shadow", "rating": 602, "opRating": 397}, {"opponent": "quagsire_shadow", "rating": 570, "opRating": 429}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 33}, {"opponent": "gligar", "rating": 61}, {"opponent": "cradily", "rating": 93}, {"opponent": "clodsire", "rating": 225}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30655}, {"moveId": "BULLET_SEED", "uses": 27645}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 21802}, {"moveId": "ANCIENT_POWER", "uses": 20795}, {"moveId": "PSYCHIC", "uses": 15592}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "ANCIENT_POWER"], "score": 33}, {"speciesId": "sunflora", "speciesName": "Sunflora", "rating": 193, "matchups": [{"opponent": "rilla<PERSON>m", "rating": 704, "opRating": 295}, {"opponent": "stunfisk", "rating": 674, "opRating": 325}, {"opponent": "gastrodon", "rating": 575, "opRating": 424}, {"opponent": "whiscash", "rating": 518, "opRating": 481}, {"opponent": "marowak", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 33}, {"opponent": "gligar", "rating": 76}, {"opponent": "cradily", "rating": 83}, {"opponent": "clodsire", "rating": 151}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 41093}, {"moveId": "RAZOR_LEAF", "uses": 17207}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23906}, {"moveId": "LEAF_STORM", "uses": 19617}, {"moveId": "PETAL_BLIZZARD", "uses": 8175}, {"moveId": "SOLAR_BEAM", "uses": 6611}]}, "moveset": ["BULLET_SEED", "LEAF_STORM", "SLUDGE_BOMB"], "score": 33}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 169, "matchups": [{"opponent": "gastrodon", "rating": 626, "opRating": 373}, {"opponent": "whiscash", "rating": 592, "opRating": 407}, {"opponent": "stunfisk", "rating": 584, "opRating": 415}, {"opponent": "quagsire", "rating": 557, "opRating": 442}, {"opponent": "dunsparce", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 32}, {"opponent": "talonflame", "rating": 40}, {"opponent": "clodsire", "rating": 79}, {"opponent": "cradily", "rating": 104}, {"opponent": "gligar", "rating": 110}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36034}, {"moveId": "RAZOR_LEAF", "uses": 22266}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 23320}, {"moveId": "SOLAR_BEAM", "uses": 19794}, {"moveId": "HYPER_BEAM", "uses": 15234}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 30.5}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 178, "matchups": [{"opponent": "whiscash", "rating": 687, "opRating": 312}, {"opponent": "gastrodon", "rating": 633, "opRating": 366}, {"opponent": "marowak_shadow", "rating": 610, "opRating": 389}, {"opponent": "quagsire", "rating": 572, "opRating": 427}, {"opponent": "quagsire_shadow", "rating": 515, "opRating": 484}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 40}, {"opponent": "gligar", "rating": 45}, {"opponent": "cradily", "rating": 72}, {"opponent": "clodsire", "rating": 86}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19620}, {"moveId": "BULLET_SEED", "uses": 18664}, {"moveId": "EXTRASENSORY", "uses": 15030}, {"moveId": "ZEN_HEADBUTT", "uses": 4995}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26176}, {"moveId": "PSYCHIC", "uses": 24349}, {"moveId": "SOLAR_BEAM", "uses": 7734}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 30.5}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 172, "matchups": [{"opponent": "marshtomp_shadow", "rating": 652, "opRating": 347}, {"opponent": "pupitar_shadow", "rating": 640, "opRating": 359}, {"opponent": "ludico<PERSON>", "rating": 625, "opRating": 375}, {"opponent": "stunfisk", "rating": 597, "opRating": 402}, {"opponent": "marshtomp", "rating": 542, "opRating": 457}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "talonflame", "rating": 33}, {"opponent": "gligar", "rating": 64}, {"opponent": "cradily", "rating": 72}, {"opponent": "clodsire", "rating": 197}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29282}, {"moveId": "BULLET_SEED", "uses": 29018}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33203}, {"moveId": "PETAL_BLIZZARD", "uses": 17922}, {"moveId": "SOLAR_BEAM", "uses": 7134}]}, "moveset": ["BULLET_SEED", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 30.2}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 167, "matchups": [{"opponent": "lombre", "rating": 661, "opRating": 338}, {"opponent": "noctowl", "rating": 623, "opRating": 376}, {"opponent": "ludico<PERSON>", "rating": 602, "opRating": 397}, {"opponent": "shaymin_sky", "rating": 553, "opRating": 446}, {"opponent": "bibarel", "rating": 543, "opRating": 456}], "counters": [{"opponent": "clodsire", "rating": 45}, {"opponent": "cradily", "rating": 72}, {"opponent": "gligar", "rating": 72}, {"opponent": "jumpluff_shadow", "rating": 107}, {"opponent": "talonflame", "rating": 159}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 29658}, {"moveId": "ASTONISH", "uses": 28642}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 26214}, {"moveId": "OMINOUS_WIND", "uses": 20673}, {"moveId": "THUNDER", "uses": 11375}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 29.6}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 157, "matchups": [{"opponent": "whiscash", "rating": 652, "opRating": 347}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 622, "opRating": 377}, {"opponent": "marowak", "rating": 610, "opRating": 389}, {"opponent": "gastrodon", "rating": 576, "opRating": 423}, {"opponent": "quagsire", "rating": 515, "opRating": 484}], "counters": [{"opponent": "jumpluff_shadow", "rating": 29}, {"opponent": "gligar", "rating": 57}, {"opponent": "talonflame", "rating": 59}, {"opponent": "cradily", "rating": 72}, {"opponent": "clodsire", "rating": 74}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19494}, {"moveId": "BULLET_SEED", "uses": 19140}, {"moveId": "EXTRASENSORY", "uses": 14890}, {"moveId": "ZEN_HEADBUTT", "uses": 4778}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26169}, {"moveId": "PSYCHIC", "uses": 24330}, {"moveId": "SOLAR_BEAM", "uses": 7669}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 28.2}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 119, "matchups": [{"opponent": "regigigas", "rating": 700, "opRating": 299}, {"opponent": "regigigas_shadow", "rating": 641, "opRating": 358}, {"opponent": "slaking", "rating": 585, "opRating": 414}, {"opponent": "unfezant_shadow", "rating": 581, "opRating": 418}, {"opponent": "exploud", "rating": 542, "opRating": 457}], "counters": [{"opponent": "clodsire", "rating": 40}, {"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "cradily", "rating": 52}, {"opponent": "gligar", "rating": 68}, {"opponent": "talonflame", "rating": 118}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9022}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4378}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3716}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3252}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3224}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3205}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2943}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2941}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2848}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2783}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2733}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2726}, {"moveId": "CHARGE_BEAM", "uses": 2706}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2690}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2486}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2450}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2262}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1938}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18504}, {"moveId": "BLIZZARD", "uses": 15646}, {"moveId": "HYPER_BEAM", "uses": 9121}, {"moveId": "SOLAR_BEAM", "uses": 8153}, {"moveId": "ZAP_CANNON", "uses": 6960}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 21.6}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 117, "matchups": [{"opponent": "regigigas", "rating": 670, "opRating": 329}, {"opponent": "unfezant", "rating": 581, "opRating": 418}, {"opponent": "gumshoos", "rating": 529, "opRating": 470}, {"opponent": "unfezant_shadow", "rating": 521, "opRating": 478}, {"opponent": "audino", "rating": 512, "opRating": 487}], "counters": [{"opponent": "cradily", "rating": 52}, {"opponent": "gligar", "rating": 53}, {"opponent": "clodsire", "rating": 69}, {"opponent": "jumpluff_shadow", "rating": 98}, {"opponent": "talonflame", "rating": 118}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9896}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4405}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3679}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3174}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3170}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3140}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2872}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2864}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2860}, {"moveId": "CHARGE_BEAM", "uses": 2717}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2688}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2653}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2647}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2616}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2423}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2385}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2174}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1904}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18477}, {"moveId": "BLIZZARD", "uses": 15633}, {"moveId": "HYPER_BEAM", "uses": 9119}, {"moveId": "SOLAR_BEAM", "uses": 8161}, {"moveId": "ZAP_CANNON", "uses": 6960}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 21.4}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 22, "matchups": [], "counters": [{"opponent": "jumpluff_shadow", "rating": 13}, {"opponent": "talonflame", "rating": 14}, {"opponent": "clodsire", "rating": 14}, {"opponent": "cradily", "rating": 20}, {"opponent": "gligar", "rating": 22}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 58300}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29810}, {"moveId": "EARTHQUAKE", "uses": 11849}, {"moveId": "PLAY_ROUGH", "uses": 8855}, {"moveId": "HYPER_BEAM", "uses": 7720}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 4.3}, {"speciesId": "slaking_shadow", "speciesName": "Slaking (Shadow)", "rating": 19, "matchups": [], "counters": [{"opponent": "clodsire", "rating": 12}, {"opponent": "cradily", "rating": 13}, {"opponent": "jumpluff_shadow", "rating": 13}, {"opponent": "talonflame", "rating": 14}, {"opponent": "gligar", "rating": 15}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 58300}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29353}, {"moveId": "EARTHQUAKE", "uses": 11732}, {"moveId": "PLAY_ROUGH", "uses": 8826}, {"moveId": "HYPER_BEAM", "uses": 8269}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 3.6}]