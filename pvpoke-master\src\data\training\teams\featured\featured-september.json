[{"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "alphafeeb", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "swampert", "fastMove": "MUD_SHOT", "chargedMoves": ["HYDRO_CANNON", "EARTHQUAKE"]}, {"speciesId": "bronzong", "fastMove": "CONFUSION", "chargedMoves": ["PSYSHOCK", "HEAVY_SLAM"]}, {"speciesId": "venusaur", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "SLUDGE_BOMB"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "SHADOW_SNEAK"]}, {"speciesId": "magneton", "fastMove": "THUNDER_SHOCK", "chargedMoves": ["DISCHARGE", "MAGNET_BOMB"]}, {"speciesId": "munchlax", "fastMove": "LICK", "chargedMoves": ["BODY_SLAM", "BULLDOZE"]}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "choostemaster", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "bastiodon", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "FLAMETHROWER"]}, {"speciesId": "hypno", "fastMove": "CONFUSION", "chargedMoves": ["SHADOW_BALL", "THUNDER_PUNCH"]}, {"speciesId": "tropius", "fastMove": "RAZOR_LEAF", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"]}, {"speciesId": "forretress", "fastMove": "BUG_BITE", "chargedMoves": ["HEAVY_SLAM", "EARTHQUAKE"]}, {"speciesId": "mantine", "fastMove": "WING_ATTACK", "chargedMoves": ["AERIAL_ACE", "ICE_BEAM"]}, {"speciesId": "quagsire", "fastMove": "MUD_SHOT", "chargedMoves": ["STONE_EDGE", "EARTHQUAKE"]}]}, {"name": "datboiMuk", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is a Season 1 Chicago Championship finalist that specializes in running obscure lineups.", "link": "https://twitter.com/datboimuk", "pokemon": [{"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "AIR_SLASH", "chargedMoves": ["SKY_ATTACK", "FLASH_CANNON"]}, {"speciesId": "bronzong", "fastMove": "CONFUSION", "chargedMoves": ["PSYSHOCK", "BULLDOZE"]}, {"speciesId": "victreebel", "fastMove": "RAZOR_LEAF", "chargedMoves": ["LEAF_BLADE", "SLUDGE_BOMB"]}, {"speciesId": "umbreon", "fastMove": "SNARL", "chargedMoves": ["FOUL_PLAY", "LAST_RESORT"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"]}, {"speciesId": "tentacruel", "fastMove": "POISON_JAB", "chargedMoves": ["HYDRO_PUMP", "SLUDGE_WAVE"]}]}, {"name": "gastonagustin", "slug": "gastonagustin", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "AIR_SLASH", "chargedMoves": ["SKY_ATTACK", "FLASH_CANNON"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "<PERSON>ras", "fastMove": "ICE_SHARD", "chargedMoves": ["SURF", "SKULL_BASH"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "relicanth", "fastMove": "WATER_GUN", "chargedMoves": ["ANCIENT_POWER", "AQUA_TAIL"]}]}, {"name": "<PERSON><PERSON>", "slug": "gotts", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "bronzong", "fastMove": "CONFUSION", "chargedMoves": ["PSYSHOCK", "BULLDOZE"]}, {"speciesId": "tropius", "fastMove": "RAZOR_LEAF", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"]}, {"speciesId": "swampert", "fastMove": "MUD_SHOT", "chargedMoves": ["HYDRO_CANNON", "EARTHQUAKE"]}, {"speciesId": "castform_sunny", "fastMove": "EMBER", "chargedMoves": ["WEATHER_BALL_FIRE", "SOLAR_BEAM"]}, {"speciesId": "clefable", "fastMove": "CHARM", "chargedMoves": ["METEOR_MASH", "PSYCHIC"]}, {"speciesId": "haunter", "fastMove": "SHADOW_CLAW", "chargedMoves": ["SHADOW_PUNCH", "SHADOW_BALL"]}]}, {"name": "itsSmogondevo #1", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "swampert", "fastMove": "MUD_SHOT", "chargedMoves": ["HYDRO_CANNON", "EARTHQUAKE"]}, {"speciesId": "castform_sunny", "fastMove": "EMBER", "chargedMoves": ["WEATHER_BALL_FIRE", "SOLAR_BEAM"]}, {"speciesId": "<PERSON><PERSON><PERSON>", "fastMove": "CONFUSION", "chargedMoves": ["METEOR_MASH", "PSYCHIC"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "r<PERSON><PERSON>_alolan", "fastMove": "SPARK", "chargedMoves": ["THUNDER_PUNCH", "WILD_CHARGE"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "AERIAL_ACE"]}]}, {"name": "itsSmogondevo #2", "slug": "itsSmogondevo-2", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "hypno", "fastMove": "CONFUSION", "chargedMoves": ["SHADOW_BALL", "FIRE_PUNCH"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "AERIAL_ACE"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "probopass", "fastMove": "SPARK", "chargedMoves": ["ROCK_SLIDE", "THUNDERBOLT"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"]}]}, {"name": "KepBepoc", "slug": "kepbepoc", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "tentacruel", "fastMove": "POISON_JAB", "chargedMoves": ["HYDRO_PUMP", "SLUDGE_WAVE"], "level": 23, "ivs": [3, 15, 15]}, {"speciesId": "swampert", "fastMove": "MUD_SHOT", "chargedMoves": ["HYDRO_CANNON", "EARTHQUAKE"], "level": 19, "ivs": [0, 15, 13]}, {"speciesId": "vigoroth", "fastMove": "COUNTER", "chargedMoves": ["BODY_SLAM", "BULLDOZE"], "level": 29.5, "ivs": [1, 13, 7]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"], "level": 40, "ivs": [14, 13, 13]}, {"speciesId": "tropius", "fastMove": "AIR_SLASH", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"], "level": 28.5, "ivs": [14, 8, 3]}, {"speciesId": "probopass", "fastMove": "SPARK", "chargedMoves": ["ROCK_SLIDE", "MAGNET_BOMB"], "level": 25, "ivs": [15, 13, 13]}]}, {"name": "KnightsOoofRen", "slug": "renn", "img": "renn", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "KakunaMattata42’s rival. Been a Pokemon Fan since I was 1 year old and still on the grind. Out here trying to improve my PvP skills to clap some noobs. Peru & UMN-TC Represent. ‘If you can’t giddy up then giddy out my way’.", "link": "#", "pokemon": [{"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "AERIAL_ACE"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["ICE_BEAM", "PLAY_ROUGH"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "hypno", "fastMove": "CONFUSION", "chargedMoves": ["FIRE_PUNCH", "ICE_PUNCH"]}, {"speciesId": "<PERSON>ras", "fastMove": "ICE_SHARD", "chargedMoves": ["SURF", "SKULL_BASH"]}]}, {"name": "PolymersUp", "slug": "polymersup", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "deoxys_defense", "fastMove": "COUNTER", "chargedMoves": ["PSYCHO_BOOST", "ROCK_SLIDE"]}, {"speciesId": "munchlax", "fastMove": "LICK", "chargedMoves": ["BODY_SLAM", "GUNK_SHOT"]}, {"speciesId": "tropius", "fastMove": "AIR_SLASH", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"]}, {"speciesId": "<PERSON>ras", "fastMove": "ICE_SHARD", "chargedMoves": ["SURF", "SKULL_BASH"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "FLAMETHROWER"]}, {"speciesId": "steelix", "fastMove": "DRAGON_TAIL", "chargedMoves": ["HEAVY_SLAM", "EARTHQUAKE"]}]}, {"name": "Richie<PERSON>ett85", "slug": "<PERSON><PERSON><PERSON>ett85", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "medicham", "fastMove": "COUNTER", "chargedMoves": ["POWER_UP_PUNCH", "ICE_PUNCH"]}, {"speciesId": "bastiodon", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "FLAMETHROWER"]}, {"speciesId": "<PERSON>ras", "fastMove": "ICE_SHARD", "chargedMoves": ["SURF", "SKULL_BASH"]}, {"speciesId": "muk_alolan", "fastMove": "POISON_JAB", "chargedMoves": ["ACID_SPRAY", "DARK_PULSE"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "AERIAL_ACE"]}, {"speciesId": "linoone", "fastMove": "SHADOW_CLAW", "chargedMoves": ["GRASS_KNOT", "THUNDER"]}]}, {"name": "squawkdir<PERSON>tome", "slug": "squawkdir<PERSON>tome", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "deoxys_defense", "fastMove": "COUNTER", "chargedMoves": ["PSYCHO_BOOST", "THUNDERBOLT"]}, {"speciesId": "bastiodon", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "FLAMETHROWER"]}, {"speciesId": "umbreon", "fastMove": "SNARL", "chargedMoves": ["FOUL_PLAY", "LAST_RESORT"]}, {"speciesId": "quagsire", "fastMove": "MUD_SHOT", "chargedMoves": ["STONE_EDGE", "SLUDGE_BOMB"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "FLAMETHROWER"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["POISON_FANG", "SHADOW_BALL"]}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "beedrill", "fastMove": "POISON_JAB", "chargedMoves": ["X_SCISSOR", "SLUDGE_BOMB"]}, {"speciesId": "altaria", "fastMove": "DRAGON_BREATH", "chargedMoves": ["SKY_ATTACK", "DRAGON_PULSE"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "SHADOW_SNEAK"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "clefable", "fastMove": "CHARM", "chargedMoves": ["METEOR_MASH", "PSYCHIC"]}, {"speciesId": "poliwrath", "fastMove": "MUD_SHOT", "chargedMoves": ["POWER_UP_PUNCH", "ICE_PUNCH"]}]}, {"name": "twastell", "slug": "twastell", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "tropius", "fastMove": "AIR_SLASH", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"]}, {"speciesId": "bastiodon", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "FLAMETHROWER"]}, {"speciesId": "hypno", "fastMove": "CONFUSION", "chargedMoves": ["ICE_PUNCH", "SHADOW_BALL"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"]}, {"speciesId": "lucario", "fastMove": "COUNTER", "chargedMoves": ["POWER_UP_PUNCH", "SHADOW_BALL"]}, {"speciesId": "blastoise", "fastMove": "WATER_GUN", "chargedMoves": ["HYDRO_CANNON", "ICE_BEAM"]}]}, {"name": "Vergyverg", "slug": "vergyverg", "img": "vergyverg", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "Pokemon Go enthusiast! Primarily battles in the Texas area. Started pvp'ing late in the game around the Nightmare Cup. Quickly made it to challenger and hope to achieve <PERSON> in season 2 now that I'll be starting at the beginning of the season.", "link": "https://www.youtube.com/channel/UCOeIQWAJYD-zy7T5-IRZy7Q", "pokemon": [{"speciesId": "bayleef", "fastMove": "RAZOR_LEAF", "chargedMoves": ["ANCIENT_POWER", "GRASS_KNOT"]}, {"speciesId": "relicanth", "fastMove": "WATER_GUN", "chargedMoves": ["AQUA_TAIL", "ANCIENT_POWER"]}, {"speciesId": "mantine", "fastMove": "BUBBLE", "chargedMoves": ["AERIAL_ACE", "ICE_BEAM"]}, {"speciesId": "marshtomp", "fastMove": "MUD_SHOT", "chargedMoves": ["MUD_BOMB", "SURF"]}, {"speciesId": "aggron", "fastMove": "SMACK_DOWN", "chargedMoves": ["HEAVY_SLAM", "STONE_EDGE"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["POISON_FANG", "SHADOW_BALL"]}]}, {"name": "VeryCoolGuy", "slug": "verycoolguy", "img": "gostadium", "cup": "cliffhanger", "cupName": "<PERSON><PERSON><PERSON>", "league": 1500, "description": "A Worldwide Community of dedicated Pokémon GO Trainers and Content Creators committed to cultivating the PvP Community as a premiere mobile gaming E-Sport. Cliffhanger is a special format designed by GO Stadium with a tiered team-building system.", "link": "https://discord.gg/fbczGXZ", "pokemon": [{"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["PLAY_ROUGH", "HYDRO_PUMP"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "<PERSON>ras", "fastMove": "ICE_SHARD", "chargedMoves": ["SURF", "SKULL_BASH"]}, {"speciesId": "forretress", "fastMove": "BUG_BITE", "chargedMoves": ["HEAVY_SLAM", "EARTHQUAKE"]}, {"speciesId": "muk_alolan", "fastMove": "POISON_JAB", "chargedMoves": ["DARK_PULSE", "SLUDGE_WAVE"]}, {"speciesId": "gliscor", "fastMove": "FURY_CUTTER", "chargedMoves": ["EARTHQUAKE", "AERIAL_ACE"]}]}, {"name": "BGold", "slug": "bgold", "img": "bgold", "cup": "safari", "cupName": "Montreal Safari", "league": 1500, "description": "Water type enthusiast and gym leader.", "link": "https://twitter.com/B_Goldwax", "pokemon": [{"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["ICE_BEAM", "PLAY_ROUGH"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "AIR_SLASH", "chargedMoves": ["SKY_ATTACK", "FLASH_CANNON"]}, {"speciesId": "munchlax", "fastMove": "LICK", "chargedMoves": ["BODY_SLAM", "BULLDOZE"]}, {"speciesId": "ludico<PERSON>", "fastMove": "RAZOR_LEAF", "chargedMoves": ["ICE_BEAM", "HYDRO_PUMP"]}, {"speciesId": "charizard", "fastMove": "FIRE_SPIN", "chargedMoves": ["DRAGON_CLAW", "BLAST_BURN"]}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "mat<PERSON><PERSON>", "img": "mat<PERSON><PERSON>", "cup": "safari", "cupName": "Montreal Safari", "league": 1500, "description": "Made Top 4 during the Season 1 NA Championship. Love to team build and experiment with new and interesting Pokemon.", "link": "https://twitter.com/MattSuarPvP", "pokemon": [{"speciesId": "camerupt", "fastMove": "EMBER", "chargedMoves": ["EARTH_POWER", "SOLAR_BEAM"]}, {"speciesId": "ludico<PERSON>", "fastMove": "RAZOR_LEAF", "chargedMoves": ["ICE_BEAM", "HYDRO_PUMP"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "AIR_SLASH", "chargedMoves": ["SKY_ATTACK", "FLASH_CANNON"]}, {"speciesId": "drifb<PERSON>", "fastMove": "HEX", "chargedMoves": ["ICY_WIND", "OMINOUS_WIND"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "DYNAMIC_PUNCH"]}, {"speciesId": "aggron", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "HEAVY_SLAM"]}]}, {"name": "Toshi9227", "slug": "toshi9227", "img": "toshi9227", "cup": "safari", "cupName": "Montreal Safari", "league": 1500, "description": "TL40x12, 100mil club and <PERSON><PERSON><PERSON> Trainer, #10 global rank (for now).", "link": "https://twitter.com/ShutupToshi9227", "pokemon": [{"speciesId": "bastiodon", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "FLAMETHROWER"]}, {"speciesId": "tropius", "fastMove": "AIR_SLASH", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"]}, {"speciesId": "ivysaur", "fastMove": "VINE_WHIP", "chargedMoves": ["POWER_WHIP", "SLUDGE_BOMB"]}, {"speciesId": "mantine", "fastMove": "WING_ATTACK", "chargedMoves": ["AERIAL_ACE", "ICE_BEAM"]}, {"speciesId": "heracross", "fastMove": "COUNTER", "chargedMoves": ["MEGAHORN", "EARTHQUAKE"]}, {"speciesId": "munchlax", "fastMove": "LICK", "chargedMoves": ["BODY_SLAM", "BULLDOZE"]}]}]