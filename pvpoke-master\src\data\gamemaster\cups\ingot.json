{"name": "ingot", "title": "Ingot", "include": [{"filterType": "type", "name": "Type", "values": ["dark", "electric", "fighting", "fire", "poison", "steel"]}, {"filterType": "id", "name": "Species", "values": ["rotom_wash", "wormadam_sandy", "archen", "archeops", "qwilfish_his<PERSON>an", "arctibax", "tyrunt", "stunfisk_galarian", "toxapex", "tentacruel", "cloyster", "araquanid", "cradily", "amaura", "avalugg_his<PERSON>an", "arm<PERSON>", "<PERSON>on", "", "steelix", "nidoking", "nidoqueen", "piloswine", "u<PERSON><PERSON><PERSON>", "", "camerupt", "munchlax", "lileep"]}], "exclude": [{"filterType": "type", "name": "Type", "values": ["dragon", "ground", "rock", "water", "ghost", "fighting"]}, {"filterType": "id", "name": "Species", "values": ["gligar", "drapion", "vikavolt", "heliolisk", "mor<PERSON><PERSON>_full_belly", "marowak_alolan", "grimer", "malamar", "salazzle", "nidorina", "qwilfish_his<PERSON>an", "p<PERSON><PERSON><PERSON>", "mandibuzz", "pangoro", "bellibolt", "corviknight", "gra<PERSON><PERSON><PERSON>"]}, {"filterType": "tag", "name": "Tag", "values": ["shadow", "mega"]}], "overrides": [], "league": 1500, "useDefaultMovesets": 1, "levelCap": 50, "includeLowStatProduct": true}