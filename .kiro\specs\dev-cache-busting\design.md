# Design Document

## Overview

The development cache-busting system will provide automatic asset versioning during development to eliminate browser caching issues. The solution will enhance the existing cache-busting mechanism in PvPoke by making it more robust and comprehensive, covering all static assets including CSS, JavaScript, and other resources.

## Architecture

The system will build upon the existing `$SITE_VERSION` mechanism in `header.php` but improve its reliability and scope:

1. **Environment Detection**: Enhanced logic to reliably detect development vs production environments
2. **Asset Versioning**: Comprehensive versioning system for all static assets
3. **Cache Headers**: Additional HTTP headers to prevent aggressive caching during development
4. **Fallback Mechanisms**: Multiple approaches to ensure cache-busting works across different setups

## Components and Interfaces

### Environment Detection Component

**Purpose**: Reliably determine if the application is running in development mode

**Implementation**:
- Check multiple indicators: URL patterns, file paths, environment variables
- Support Docker and local development setups
- Provide fallback detection methods

**Interface**:
```php
function isDevelopmentEnvironment(): bool
```

### Asset Version Generator

**Purpose**: Generate unique version strings for static assets

**Implementation**:
- Use timestamp-based versioning for development
- Use file modification time as fallback
- Generate random versions as last resort

**Interface**:
```php
function generateAssetVersion(string $assetPath = ''): string
```

### Cache Control Headers

**Purpose**: Send appropriate HTTP headers to prevent browser caching during development

**Implementation**:
- Add no-cache headers for development
- Preserve caching headers for production

## Data Models

### Development Environment Detection

```php
class DevelopmentDetector {
    private array $indicators = [
        'url_patterns' => ['localhost', '127.0.0.1', 'src/', 'dev.'],
        'paths' => ['/src/', '/dev/', '/development/'],
        'env_vars' => ['APP_ENV', 'ENVIRONMENT', 'NODE_ENV']
    ];
    
    public function isDevelopment(): bool;
    private function checkUrlPatterns(): bool;
    private function checkPaths(): bool;
    private function checkEnvironmentVars(): bool;
}
```

### Asset Version Manager

```php
class AssetVersionManager {
    private string $baseVersion;
    private bool $isDevelopment;
    
    public function getVersion(string $assetType = 'default'): string;
    public function getTimestampVersion(): string;
    public function getFileModificationVersion(string $filePath): string;
    public function getRandomVersion(): string;
}
```

## Error Handling

### Fallback Versioning
- If timestamp generation fails, fall back to random number generation
- If file modification time is unavailable, use current timestamp
- Always provide a valid version string to prevent broken asset loading

### Environment Detection Failures
- Default to development mode if detection is uncertain (safer for development workflow)
- Log detection results for debugging purposes
- Provide manual override capability

### Asset Loading Failures
- Ensure version parameters don't break asset URLs
- Handle special characters in version strings
- Maintain backward compatibility with existing asset references

## Testing Strategy

### Unit Tests
- Test environment detection logic with various URL patterns
- Test version generation with different inputs
- Test fallback mechanisms when primary methods fail

### Integration Tests
- Test complete asset loading with version parameters
- Test behavior in Docker environment
- Test behavior with local PHP server
- Verify production mode doesn't break existing functionality

### Manual Testing
- Verify CSS changes are immediately visible after browser refresh
- Test with different browsers to ensure compatibility
- Verify production deployment isn't affected
- Test with various development server configurations

## Implementation Approach

### Phase 1: Enhanced Environment Detection
1. Improve the existing environment detection logic
2. Add multiple detection methods for reliability
3. Test across different development setups

### Phase 2: Comprehensive Asset Versioning
1. Apply versioning to all CSS files consistently
2. Extend versioning to JavaScript files
3. Add versioning to other static assets as needed

### Phase 3: Cache Control Headers
1. Add appropriate HTTP headers for development
2. Ensure headers don't interfere with production caching
3. Test header effectiveness across browsers

### Phase 4: Testing and Validation
1. Comprehensive testing across development environments
2. Validation that production behavior is unchanged
3. Performance testing to ensure no negative impact