[{"speciesId": "mewtwo_shadow", "speciesName": "<PERSON><PERSON>t<PERSON> (Shadow)", "rating": 896, "matchups": [{"opponent": "mewtwo", "rating": 927}, {"opponent": "garcho<PERSON>", "rating": 861}, {"opponent": "metagross", "rating": 809}, {"opponent": "zacian_hero", "rating": 794}, {"opponent": "dialga", "rating": 591}], "counters": [{"opponent": "yveltal", "rating": 232}, {"opponent": "lugia", "rating": 366}, {"opponent": "grou<PERSON>", "rating": 421}, {"opponent": "palkia", "rating": 482}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 47844}, {"moveId": "CONFUSION", "uses": 28656}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19750}, {"moveId": "SHADOW_BALL", "uses": 11890}, {"moveId": "ICE_BEAM", "uses": 11521}, {"moveId": "FLAMETHROWER", "uses": 8762}, {"moveId": "THUNDERBOLT", "uses": 8481}, {"moveId": "FOCUS_BLAST", "uses": 7598}, {"moveId": "PSYCHIC", "uses": 4615}, {"moveId": "HYPER_BEAM", "uses": 3739}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 100}, {"speciesId": "dragonite_shadow", "speciesName": "Dragonite (Shadow)", "rating": 900, "matchups": [{"opponent": "garcho<PERSON>", "rating": 949}, {"opponent": "excadrill", "rating": 944}, {"opponent": "giratina_origin", "rating": 829}, {"opponent": "mewtwo", "rating": 531}, {"opponent": "dialga", "rating": 518}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 165}, {"opponent": "sylveon", "rating": 247}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "lugia", "rating": 473}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33091}, {"moveId": "DRAGON_BREATH", "uses": 32966}, {"moveId": "STEEL_WING", "uses": 10531}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 24344}, {"moveId": "SUPER_POWER", "uses": 20051}, {"moveId": "HURRICANE", "uses": 11515}, {"moveId": "OUTRAGE", "uses": 6819}, {"moveId": "HYPER_BEAM", "uses": 5140}, {"moveId": "DRAGON_PULSE", "uses": 4490}, {"moveId": "DRACO_METEOR", "uses": 4065}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 97.1}, {"speciesId": "mewtwo", "speciesName": "Mewtwo", "rating": 874, "matchups": [{"opponent": "garcho<PERSON>", "rating": 861}, {"opponent": "zacian_hero", "rating": 828}, {"opponent": "dialga", "rating": 606}, {"opponent": "gyarados", "rating": 606}, {"opponent": "giratina_origin", "rating": 505}], "counters": [{"opponent": "yveltal", "rating": 203}, {"opponent": "mew", "rating": 470}, {"opponent": "metagross", "rating": 476}, {"opponent": "raikou_shadow", "rating": 491}, {"opponent": "dragonite_shadow", "rating": 494}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 46778}, {"moveId": "CONFUSION", "uses": 29722}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19056}, {"moveId": "SHADOW_BALL", "uses": 11490}, {"moveId": "ICE_BEAM", "uses": 11102}, {"moveId": "FLAMETHROWER", "uses": 8441}, {"moveId": "THUNDERBOLT", "uses": 8121}, {"moveId": "FOCUS_BLAST", "uses": 7342}, {"moveId": "RETURN", "uses": 4641}, {"moveId": "PSYCHIC", "uses": 4399}, {"moveId": "HYPER_BEAM", "uses": 1742}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 96.5}, {"speciesId": "grou<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 891, "matchups": [{"opponent": "zacian_hero", "rating": 866}, {"opponent": "metagross", "rating": 850}, {"opponent": "dialga", "rating": 790}, {"opponent": "garcho<PERSON>", "rating": 616}, {"opponent": "mewtwo", "rating": 595}], "counters": [{"opponent": "dragonite", "rating": 236}, {"opponent": "lugia", "rating": 278}, {"opponent": "gyarados", "rating": 337}, {"opponent": "kyogre", "rating": 423}, {"opponent": "yveltal", "rating": 429}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43160}, {"moveId": "DRAGON_TAIL", "uses": 33340}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 29652}, {"moveId": "FIRE_PUNCH", "uses": 28459}, {"moveId": "SOLAR_BEAM", "uses": 12776}, {"moveId": "FIRE_BLAST", "uses": 5731}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "FIRE_PUNCH"], "score": 96.2}, {"speciesId": "dragonite", "speciesName": "Dragonite", "rating": 889, "matchups": [{"opponent": "garcho<PERSON>", "rating": 944}, {"opponent": "excadrill", "rating": 944}, {"opponent": "giratina_origin", "rating": 805}, {"opponent": "dialga", "rating": 625}, {"opponent": "mewtwo", "rating": 582}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 196}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "lugia", "rating": 371}, {"opponent": "metagross", "rating": 418}, {"opponent": "gyarados", "rating": 489}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 32860}, {"moveId": "DRAGON_TAIL", "uses": 32187}, {"moveId": "STEEL_WING", "uses": 11435}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 23263}, {"moveId": "SUPER_POWER", "uses": 19252}, {"moveId": "HURRICANE", "uses": 10844}, {"moveId": "OUTRAGE", "uses": 6403}, {"moveId": "RETURN", "uses": 6269}, {"moveId": "DRAGON_PULSE", "uses": 4338}, {"moveId": "DRACO_METEOR", "uses": 3768}, {"moveId": "HYPER_BEAM", "uses": 2453}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 95.8}, {"speciesId": "genesect_chill", "speciesName": "Genesect (Chill)", "rating": 855, "matchups": [{"opponent": "garcho<PERSON>", "rating": 882}, {"opponent": "dragonite", "rating": 825}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "dialga", "rating": 674}, {"opponent": "gyarados", "rating": 648}], "counters": [{"opponent": "ho_oh", "rating": 270}, {"opponent": "kyogre", "rating": 372}, {"opponent": "giratina_altered", "rating": 414}, {"opponent": "giratina_origin", "rating": 434}, {"opponent": "grou<PERSON>", "rating": 475}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 52049}, {"moveId": "METAL_CLAW", "uses": 24451}], "chargedMoves": [{"moveId": "TECHNO_BLAST_CHILL", "uses": 26650}, {"moveId": "X_SCISSOR", "uses": 22707}, {"moveId": "MAGNET_BOMB", "uses": 19737}, {"moveId": "ICE_BEAM", "uses": 7542}]}, "moveset": ["FURY_CUTTER", "TECHNO_BLAST_CHILL", "X_SCISSOR"], "score": 95.7}, {"speciesId": "zekrom", "speciesName": "Zekrom", "rating": 871, "matchups": [{"opponent": "metagross", "rating": 855}, {"opponent": "garcho<PERSON>", "rating": 834}, {"opponent": "giratina_origin", "rating": 834}, {"opponent": "mewtwo", "rating": 600}, {"opponent": "dialga", "rating": 521}], "counters": [{"opponent": "sylveon", "rating": 412}, {"opponent": "grou<PERSON>", "rating": 413}, {"opponent": "zacian_hero", "rating": 439}, {"opponent": "excadrill", "rating": 458}, {"opponent": "swampert", "rating": 487}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47050}, {"moveId": "CHARGE_BEAM", "uses": 29450}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34901}, {"moveId": "CRUNCH", "uses": 18868}, {"moveId": "OUTRAGE", "uses": 16900}, {"moveId": "FLASH_CANNON", "uses": 5848}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "WILD_CHARGE"], "score": 94.8}, {"speciesId": "latios_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 872, "matchups": [{"opponent": "mewtwo", "rating": 901}, {"opponent": "garcho<PERSON>", "rating": 892}, {"opponent": "zacian_hero", "rating": 808}, {"opponent": "giratina_origin", "rating": 735}, {"opponent": "gyarados", "rating": 595}], "counters": [{"opponent": "dialga", "rating": 448}, {"opponent": "metagross", "rating": 459}, {"opponent": "lugia", "rating": 473}, {"opponent": "grou<PERSON>", "rating": 478}, {"opponent": "yveltal", "rating": 484}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 67519}, {"moveId": "ZEN_HEADBUTT", "uses": 8981}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 33218}, {"moveId": "LUSTER_PURGE", "uses": 21564}, {"moveId": "PSYCHIC", "uses": 14246}, {"moveId": "SOLAR_BEAM", "uses": 7470}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 94.7}, {"speciesId": "reshiram", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 870, "matchups": [{"opponent": "metagross", "rating": 845}, {"opponent": "garcho<PERSON>", "rating": 834}, {"opponent": "giratina_origin", "rating": 834}, {"opponent": "mewtwo", "rating": 600}, {"opponent": "dialga", "rating": 502}], "counters": [{"opponent": "excadrill", "rating": 244}, {"opponent": "lugia", "rating": 371}, {"opponent": "grou<PERSON>", "rating": 413}, {"opponent": "swampert", "rating": 475}, {"opponent": "palkia", "rating": 494}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48381}, {"moveId": "FIRE_FANG", "uses": 28119}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24042}, {"moveId": "OVERHEAT", "uses": 19654}, {"moveId": "STONE_EDGE", "uses": 19288}, {"moveId": "DRACO_METEOR", "uses": 13362}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "OVERHEAT"], "score": 93.8}, {"speciesId": "florges", "speciesName": "Florges", "rating": 820, "matchups": [{"opponent": "dialga", "rating": 818}, {"opponent": "gyarados", "rating": 797}, {"opponent": "giratina_origin", "rating": 705}, {"opponent": "garcho<PERSON>", "rating": 657}, {"opponent": "mewtwo", "rating": 616}], "counters": [{"opponent": "ho_oh", "rating": 203}, {"opponent": "metagross", "rating": 232}, {"opponent": "kyogre", "rating": 353}, {"opponent": "excadrill", "rating": 423}, {"opponent": "magnezone_shadow", "rating": 442}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 28119}, {"moveId": "VINE_WHIP", "uses": 22192}, {"moveId": "TACKLE", "uses": 15672}, {"moveId": "RAZOR_LEAF", "uses": 10521}], "chargedMoves": [{"moveId": "DISARMING_VOICE", "uses": 34864}, {"moveId": "PSYCHIC", "uses": 15470}, {"moveId": "MOONBLAST", "uses": 14204}, {"moveId": "PETAL_BLIZZARD", "uses": 11983}]}, "moveset": ["FAIRY_WIND", "DISARMING_VOICE", "MOONBLAST"], "score": 93.5}, {"speciesId": "lugia_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 846, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "zacian_hero", "rating": 659}, {"opponent": "mewtwo", "rating": 633}, {"opponent": "gyarados", "rating": 600}, {"opponent": "dialga", "rating": 514}], "counters": [{"opponent": "zapdos", "rating": 451}, {"opponent": "giratina_origin", "rating": 452}, {"opponent": "zap<PERSON>_shadow", "rating": 465}, {"opponent": "genesect_shock", "rating": 465}, {"opponent": "genesect_chill", "rating": 465}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42616}, {"moveId": "EXTRASENSORY", "uses": 33884}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 29440}, {"moveId": "AEROBLAST", "uses": 21931}, {"moveId": "FUTURE_SIGHT", "uses": 15153}, {"moveId": "HYDRO_PUMP", "uses": 9792}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 93.5}, {"speciesId": "giratina_altered", "speciesName": "Giratina (Altered)", "rating": 867, "matchups": [{"opponent": "gyarados", "rating": 709}, {"opponent": "mewtwo", "rating": 693}, {"opponent": "metagross", "rating": 617}, {"opponent": "garcho<PERSON>", "rating": 587}, {"opponent": "dialga", "rating": 523}], "counters": [{"opponent": "yveltal", "rating": 417}, {"opponent": "sylveon", "rating": 445}, {"opponent": "lugia", "rating": 473}, {"opponent": "giratina_origin", "rating": 492}, {"opponent": "zekrom", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 42477}, {"moveId": "DRAGON_BREATH", "uses": 34023}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 38955}, {"moveId": "ANCIENT_POWER", "uses": 18980}, {"moveId": "SHADOW_SNEAK", "uses": 18523}]}, "moveset": ["SHADOW_CLAW", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 92.9}, {"speciesId": "yveltal", "speciesName": "Y<PERSON><PERSON>", "rating": 832, "matchups": [{"opponent": "mewtwo", "rating": 947}, {"opponent": "metagross", "rating": 773}, {"opponent": "giratina_origin", "rating": 735}, {"opponent": "dialga", "rating": 712}, {"opponent": "garcho<PERSON>", "rating": 648}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 205}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "ho_oh", "rating": 273}, {"opponent": "dragonite", "rating": 329}, {"opponent": "zekrom", "rating": 339}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27727}, {"moveId": "GUST", "uses": 25651}, {"moveId": "SUCKER_PUNCH", "uses": 23052}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27441}, {"moveId": "HURRICANE", "uses": 15662}, {"moveId": "FOCUS_BLAST", "uses": 13289}, {"moveId": "PSYCHIC", "uses": 12874}, {"moveId": "HYPER_BEAM", "uses": 7253}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 92.1}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 823, "matchups": [{"opponent": "excadrill", "rating": 948}, {"opponent": "metagross", "rating": 826}, {"opponent": "dialga", "rating": 739}, {"opponent": "garcho<PERSON>", "rating": 581}, {"opponent": "mewtwo", "rating": 538}], "counters": [{"opponent": "kyogre", "rating": 326}, {"opponent": "giratina_altered", "rating": 384}, {"opponent": "swampert", "rating": 395}, {"opponent": "giratina_origin", "rating": 438}, {"opponent": "snorlax", "rating": 486}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51719}, {"moveId": "ROCK_THROW", "uses": 24781}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 25398}, {"moveId": "EARTH_POWER", "uses": 24557}, {"moveId": "OUTRAGE", "uses": 15210}, {"moveId": "FOCUS_BLAST", "uses": 11274}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTH_POWER"], "score": 91.9}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 797, "matchups": [{"opponent": "garcho<PERSON>", "rating": 893}, {"opponent": "excadrill", "rating": 893}, {"opponent": "giratina_origin", "rating": 809}, {"opponent": "gyarados", "rating": 718}, {"opponent": "dialga", "rating": 639}], "counters": [{"opponent": "kyogre", "rating": 266}, {"opponent": "snorlax", "rating": 366}, {"opponent": "zacian_hero", "rating": 424}, {"opponent": "mewtwo", "rating": 460}, {"opponent": "palkia", "rating": 494}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52584}, {"moveId": "MUD_SLAP", "uses": 23916}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38956}, {"moveId": "STONE_EDGE", "uses": 13266}, {"moveId": "BULLDOZE", "uses": 12156}, {"moveId": "ANCIENT_POWER", "uses": 12014}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 91.8}, {"speciesId": "gyarado<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 834, "matchups": [{"opponent": "garcho<PERSON>", "rating": 940}, {"opponent": "metagross", "rating": 817}, {"opponent": "giratina_origin", "rating": 798}, {"opponent": "mewtwo", "rating": 525}, {"opponent": "dialga", "rating": 502}], "counters": [{"opponent": "zacian_hero", "rating": 306}, {"opponent": "lugia", "rating": 400}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 452}, {"opponent": "sylveon", "rating": 456}, {"opponent": "ho_oh", "rating": 494}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22633}, {"moveId": "DRAGON_TAIL", "uses": 22292}, {"moveId": "WATERFALL", "uses": 21007}, {"moveId": "BITE", "uses": 10549}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 28835}, {"moveId": "CRUNCH", "uses": 20178}, {"moveId": "OUTRAGE", "uses": 12243}, {"moveId": "TWISTER", "uses": 6540}, {"moveId": "HYDRO_PUMP", "uses": 4662}, {"moveId": "DRAGON_PULSE", "uses": 3984}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 91.6}, {"speciesId": "lugia", "speciesName": "Lugia", "rating": 840, "matchups": [{"opponent": "garcho<PERSON>", "rating": 785}, {"opponent": "dragonite", "rating": 702}, {"opponent": "mewtwo", "rating": 671}, {"opponent": "gyarados", "rating": 604}, {"opponent": "giratina_origin", "rating": 519}], "counters": [{"opponent": "metagross", "rating": 427}, {"opponent": "zap<PERSON>_shadow", "rating": 451}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 471}, {"opponent": "zekrom", "rating": 483}, {"opponent": "dialga", "rating": 494}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42206}, {"moveId": "EXTRASENSORY", "uses": 34294}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26335}, {"moveId": "AEROBLAST", "uses": 19553}, {"moveId": "FUTURE_SIGHT", "uses": 13649}, {"moveId": "HYDRO_PUMP", "uses": 8800}, {"moveId": "RETURN", "uses": 8238}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 91.4}, {"speciesId": "genesect_douse", "speciesName": "Genesect (Douse)", "rating": 870, "matchups": [{"opponent": "excadrill", "rating": 911}, {"opponent": "metagross", "rating": 819}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "garcho<PERSON>", "rating": 544}, {"opponent": "dialga", "rating": 515}], "counters": [{"opponent": "giratina_origin", "rating": 235}, {"opponent": "zekrom", "rating": 388}, {"opponent": "dragonite", "rating": 390}, {"opponent": "ho_oh", "rating": 395}, {"opponent": "grou<PERSON>", "rating": 475}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50371}, {"moveId": "METAL_CLAW", "uses": 26129}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24952}, {"moveId": "TECHNO_BLAST_DOUSE", "uses": 22968}, {"moveId": "MAGNET_BOMB", "uses": 22125}, {"moveId": "GUNK_SHOT", "uses": 6554}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_DOUSE"], "score": 91}, {"speciesId": "zacian_hero", "speciesName": "<PERSON><PERSON><PERSON> (Hero)", "rating": 823, "matchups": [{"opponent": "excadrill", "rating": 916}, {"opponent": "dragonite", "rating": 852}, {"opponent": "dialga", "rating": 789}, {"opponent": "gyarados", "rating": 679}, {"opponent": "garcho<PERSON>", "rating": 650}], "counters": [{"opponent": "metagross", "rating": 334}, {"opponent": "kyogre", "rating": 366}, {"opponent": "swampert", "rating": 407}, {"opponent": "giratina_origin", "rating": 444}, {"opponent": "mewtwo", "rating": 447}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27363}, {"moveId": "QUICK_ATTACK", "uses": 26470}, {"moveId": "FIRE_FANG", "uses": 12479}, {"moveId": "METAL_CLAW", "uses": 10191}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27795}, {"moveId": "WILD_CHARGE", "uses": 27136}, {"moveId": "PLAY_ROUGH", "uses": 12798}, {"moveId": "IRON_HEAD", "uses": 8947}]}, "moveset": ["QUICK_ATTACK", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 90.9}, {"speciesId": "genesect_burn", "speciesName": "Genesect (Burn)", "rating": 844, "matchups": [{"opponent": "excadrill", "rating": 911}, {"opponent": "metagross", "rating": 882}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "dialga", "rating": 674}, {"opponent": "garcho<PERSON>", "rating": 518}], "counters": [{"opponent": "giratina_origin", "rating": 235}, {"opponent": "grou<PERSON>", "rating": 334}, {"opponent": "kyogre", "rating": 372}, {"opponent": "zekrom", "rating": 388}, {"opponent": "dragonite", "rating": 390}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50451}, {"moveId": "METAL_CLAW", "uses": 26049}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24641}, {"moveId": "MAGNET_BOMB", "uses": 22846}, {"moveId": "TECHNO_BLAST_BURN", "uses": 22708}, {"moveId": "FLAMETHROWER", "uses": 6413}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_BURN"], "score": 90.5}, {"speciesId": "palkia", "speciesName": "Pa<PERSON><PERSON>", "rating": 875, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "giratina_origin", "rating": 698}, {"opponent": "excadrill", "rating": 692}, {"opponent": "metagross", "rating": 652}, {"opponent": "mewtwo", "rating": 561}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 261}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "dialga", "rating": 345}, {"opponent": "gyarados", "rating": 443}, {"opponent": "lugia", "rating": 495}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 38481}, {"moveId": "DRAGON_BREATH", "uses": 38019}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 41137}, {"moveId": "DRACO_METEOR", "uses": 18130}, {"moveId": "FIRE_BLAST", "uses": 10856}, {"moveId": "HYDRO_PUMP", "uses": 6410}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "DRACO_METEOR"], "score": 90.5}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 812, "matchups": [{"opponent": "excadrill", "rating": 907}, {"opponent": "metagross", "rating": 873}, {"opponent": "dialga", "rating": 761}, {"opponent": "mewtwo", "rating": 567}, {"opponent": "garcho<PERSON>", "rating": 527}], "counters": [{"opponent": "dragonite", "rating": 252}, {"opponent": "lugia", "rating": 357}, {"opponent": "gyarados", "rating": 425}, {"opponent": "kyogre", "rating": 448}, {"opponent": "giratina_altered", "rating": 462}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 46748}, {"moveId": "WATER_GUN", "uses": 29752}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 29385}, {"moveId": "EARTHQUAKE", "uses": 13459}, {"moveId": "MUDDY_WATER", "uses": 11273}, {"moveId": "SURF", "uses": 9728}, {"moveId": "SLUDGE_WAVE", "uses": 6689}, {"moveId": "RETURN", "uses": 5862}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 90.2}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 830, "matchups": [{"opponent": "zacian_hero", "rating": 892}, {"opponent": "mewtwo", "rating": 822}, {"opponent": "dialga", "rating": 744}, {"opponent": "lugia", "rating": 715}, {"opponent": "gyarados", "rating": 549}], "counters": [{"opponent": "grou<PERSON>", "rating": 198}, {"opponent": "swampert", "rating": 325}, {"opponent": "giratina_origin", "rating": 380}, {"opponent": "excadrill", "rating": 386}, {"opponent": "garcho<PERSON>", "rating": 441}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 67101}, {"moveId": "ZEN_HEADBUTT", "uses": 9399}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 29562}, {"moveId": "PSYCHIC", "uses": 17034}, {"moveId": "EARTHQUAKE", "uses": 16341}, {"moveId": "RETURN", "uses": 8922}, {"moveId": "FLASH_CANNON", "uses": 4749}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 90}, {"speciesId": "meloetta_aria", "speciesName": "<PERSON><PERSON><PERSON> (Aria)", "rating": 860, "matchups": [{"opponent": "giratina_origin", "rating": 748}, {"opponent": "zacian_hero", "rating": 651}, {"opponent": "garcho<PERSON>", "rating": 646}, {"opponent": "mewtwo", "rating": 634}, {"opponent": "gyarados", "rating": 597}], "counters": [{"opponent": "metagross", "rating": 311}, {"opponent": "dialga", "rating": 369}, {"opponent": "lugia", "rating": 400}, {"opponent": "lugia_shadow", "rating": 440}, {"opponent": "ho_oh", "rating": 492}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 41423}, {"moveId": "CONFUSION", "uses": 35077}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30116}, {"moveId": "THUNDERBOLT", "uses": 19602}, {"moveId": "HYPER_BEAM", "uses": 13754}, {"moveId": "DAZZLING_GLEAM", "uses": 13218}]}, "moveset": ["QUICK_ATTACK", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 89.9}, {"speciesId": "zap<PERSON>_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 861, "matchups": [{"opponent": "garcho<PERSON>", "rating": 932}, {"opponent": "zacian_hero", "rating": 793}, {"opponent": "excadrill", "rating": 768}, {"opponent": "dialga", "rating": 661}, {"opponent": "gyarados", "rating": 596}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "lugia", "rating": 233}, {"opponent": "metagross", "rating": 366}, {"opponent": "giratina_origin", "rating": 374}, {"opponent": "ho_oh", "rating": 466}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 76500}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34238}, {"moveId": "BRAVE_BIRD", "uses": 28895}, {"moveId": "ANCIENT_POWER", "uses": 13366}]}, "moveset": ["COUNTER", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 89.9}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 859, "matchups": [{"opponent": "metagross", "rating": 858}, {"opponent": "excadrill", "rating": 743}, {"opponent": "mewtwo", "rating": 699}, {"opponent": "zacian_hero", "rating": 631}, {"opponent": "lugia", "rating": 569}], "counters": [{"opponent": "dragonite", "rating": 428}, {"opponent": "dialga", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 441}, {"opponent": "gyarados", "rating": 445}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 455}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 43346}, {"moveId": "DRAGON_TAIL", "uses": 33154}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 36703}, {"moveId": "DRAGON_PULSE", "uses": 21113}, {"moveId": "OMINOUS_WIND", "uses": 18680}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "OMINOUS_WIND"], "score": 89.6}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 799, "matchups": [{"opponent": "garcho<PERSON>", "rating": 893}, {"opponent": "lugia", "rating": 741}, {"opponent": "giratina_origin", "rating": 679}, {"opponent": "dialga", "rating": 606}, {"opponent": "mewtwo", "rating": 513}], "counters": [{"opponent": "kyogre", "rating": 277}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "palkia", "rating": 412}, {"opponent": "gyarados", "rating": 425}, {"opponent": "metagross", "rating": 438}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 51439}, {"moveId": "MUD_SLAP", "uses": 25061}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36181}, {"moveId": "STONE_EDGE", "uses": 12258}, {"moveId": "BULLDOZE", "uses": 11118}, {"moveId": "ANCIENT_POWER", "uses": 11080}, {"moveId": "RETURN", "uses": 5756}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 89.6}, {"speciesId": "land<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 849, "matchups": [{"opponent": "excadrill", "rating": 953}, {"opponent": "metagross", "rating": 744}, {"opponent": "dialga", "rating": 654}, {"opponent": "mewtwo", "rating": 535}, {"opponent": "zacian_hero", "rating": 513}], "counters": [{"opponent": "kyogre", "rating": 315}, {"opponent": "giratina_origin", "rating": 374}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "dragonite", "rating": 468}, {"opponent": "gyarados", "rating": 476}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51155}, {"moveId": "EXTRASENSORY", "uses": 25345}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24315}, {"moveId": "STONE_EDGE", "uses": 22090}, {"moveId": "EARTHQUAKE", "uses": 18662}, {"moveId": "BULLDOZE", "uses": 11426}]}, "moveset": ["MUD_SHOT", "SUPER_POWER", "STONE_EDGE"], "score": 89.3}, {"speciesId": "hydreigon", "speciesName": "Hydreigon", "rating": 828, "matchups": [{"opponent": "mewtwo", "rating": 939}, {"opponent": "garcho<PERSON>", "rating": 880}, {"opponent": "giratina_origin", "rating": 880}, {"opponent": "metagross", "rating": 738}, {"opponent": "gyarados", "rating": 600}], "counters": [{"opponent": "zacian_hero", "rating": 265}, {"opponent": "grou<PERSON>", "rating": 307}, {"opponent": "lugia", "rating": 378}, {"opponent": "dialga", "rating": 418}, {"opponent": "yveltal", "rating": 442}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 50301}, {"moveId": "BITE", "uses": 26199}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 40278}, {"moveId": "DRAGON_PULSE", "uses": 15077}, {"moveId": "DARK_PULSE", "uses": 12389}, {"moveId": "FLASH_CANNON", "uses": 8710}]}, "moveset": ["DRAGON_BREATH", "BRUTAL_SWING", "FLASH_CANNON"], "score": 89.2}, {"speciesId": "gyarados", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 814, "matchups": [{"opponent": "garcho<PERSON>", "rating": 930}, {"opponent": "giratina_origin", "rating": 688}, {"opponent": "metagross", "rating": 652}, {"opponent": "mewtwo", "rating": 587}, {"opponent": "lugia", "rating": 561}], "counters": [{"opponent": "dialga", "rating": 410}, {"opponent": "sylveon", "rating": 440}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 452}, {"opponent": "zacian_hero", "rating": 453}, {"opponent": "giratina_altered", "rating": 484}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22460}, {"moveId": "DRAGON_TAIL", "uses": 21786}, {"moveId": "WATERFALL", "uses": 20912}, {"moveId": "BITE", "uses": 11362}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 26291}, {"moveId": "CRUNCH", "uses": 18448}, {"moveId": "OUTRAGE", "uses": 11087}, {"moveId": "RETURN", "uses": 6833}, {"moveId": "TWISTER", "uses": 5978}, {"moveId": "HYDRO_PUMP", "uses": 4155}, {"moveId": "DRAGON_PULSE", "uses": 3733}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 89.1}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 821, "matchups": [{"opponent": "excadrill", "rating": 911}, {"opponent": "giratina_origin", "rating": 712}, {"opponent": "dialga", "rating": 686}, {"opponent": "mewtwo", "rating": 617}, {"opponent": "garcho<PERSON>", "rating": 591}], "counters": [{"opponent": "dragonite", "rating": 348}, {"opponent": "zekrom", "rating": 377}, {"opponent": "ho_oh", "rating": 393}, {"opponent": "lugia", "rating": 466}, {"opponent": "grou<PERSON>", "rating": 470}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66225}, {"moveId": "ZEN_HEADBUTT", "uses": 8672}, {"moveId": "YAWN", "uses": 1639}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26072}, {"moveId": "SUPER_POWER", "uses": 16866}, {"moveId": "OUTRAGE", "uses": 9584}, {"moveId": "EARTHQUAKE", "uses": 9350}, {"moveId": "HEAVY_SLAM", "uses": 7183}, {"moveId": "SKULL_BASH", "uses": 4464}, {"moveId": "HYPER_BEAM", "uses": 2956}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 88.9}, {"speciesId": "genesect_shock", "speciesName": "Genesect (Shock)", "rating": 858, "matchups": [{"opponent": "gyarados", "rating": 882}, {"opponent": "metagross", "rating": 819}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "garcho<PERSON>", "rating": 518}, {"opponent": "dialga", "rating": 515}], "counters": [{"opponent": "grou<PERSON>", "rating": 206}, {"opponent": "giratina_origin", "rating": 235}, {"opponent": "excadrill", "rating": 327}, {"opponent": "zekrom", "rating": 388}, {"opponent": "dragonite", "rating": 417}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50712}, {"moveId": "METAL_CLAW", "uses": 25788}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25920}, {"moveId": "MAGNET_BOMB", "uses": 23199}, {"moveId": "TECHNO_BLAST_SHOCK", "uses": 22203}, {"moveId": "ZAP_CANNON", "uses": 5208}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_SHOCK"], "score": 88.7}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 843, "matchups": [{"opponent": "excadrill", "rating": 903}, {"opponent": "metagross", "rating": 833}, {"opponent": "dialga", "rating": 652}, {"opponent": "dragonite", "rating": 652}, {"opponent": "giratina_origin", "rating": 558}], "counters": [{"opponent": "lugia", "rating": 214}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "gyarados", "rating": 373}, {"opponent": "kyogre", "rating": 399}, {"opponent": "mewtwo", "rating": 473}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39892}, {"moveId": "DRAGON_TAIL", "uses": 36608}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 24656}, {"moveId": "EARTH_POWER", "uses": 22570}, {"moveId": "FIRE_BLAST", "uses": 9838}, {"moveId": "EARTHQUAKE", "uses": 9671}, {"moveId": "SAND_TOMB", "uses": 9510}]}, "moveset": ["MUD_SHOT", "OUTRAGE", "EARTH_POWER"], "score": 88.3}, {"speciesId": "zarude", "speciesName": "Zarude", "rating": 799, "matchups": [{"opponent": "mewtwo", "rating": 949}, {"opponent": "excadrill", "rating": 795}, {"opponent": "giratina_origin", "rating": 721}, {"opponent": "gyarados", "rating": 694}, {"opponent": "garcho<PERSON>", "rating": 673}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 266}, {"opponent": "dragonite", "rating": 268}, {"opponent": "sylveon", "rating": 409}, {"opponent": "lugia", "rating": 473}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 49294}, {"moveId": "BITE", "uses": 27206}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37160}, {"moveId": "POWER_WHIP", "uses": 29004}, {"moveId": "ENERGY_BALL", "uses": 10565}]}, "moveset": ["VINE_WHIP", "DARK_PULSE", "POWER_WHIP"], "score": 88.2}, {"speciesId": "kyogre", "speciesName": "Kyogre", "rating": 814, "matchups": [{"opponent": "zacian_hero", "rating": 866}, {"opponent": "metagross", "rating": 673}, {"opponent": "mewtwo", "rating": 605}, {"opponent": "garcho<PERSON>", "rating": 600}, {"opponent": "giratina_origin", "rating": 527}], "counters": [{"opponent": "palkia", "rating": 236}, {"opponent": "zekrom", "rating": 328}, {"opponent": "dragonite", "rating": 329}, {"opponent": "dialga", "rating": 372}, {"opponent": "meloetta_aria", "rating": 482}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 76500}], "chargedMoves": [{"moveId": "SURF", "uses": 37748}, {"moveId": "BLIZZARD", "uses": 17476}, {"moveId": "THUNDER", "uses": 15183}, {"moveId": "HYDRO_PUMP", "uses": 6017}]}, "moveset": ["WATERFALL", "SURF", "BLIZZARD"], "score": 88}, {"speciesId": "latios", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 874, "matchups": [{"opponent": "garcho<PERSON>", "rating": 909}, {"opponent": "giratina_origin", "rating": 718}, {"opponent": "excadrill", "rating": 665}, {"opponent": "dragonite", "rating": 610}, {"opponent": "gyarados", "rating": 537}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 371}, {"opponent": "metagross", "rating": 389}, {"opponent": "mewtwo", "rating": 463}, {"opponent": "zacian_hero", "rating": 482}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 66661}, {"moveId": "ZEN_HEADBUTT", "uses": 9839}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30043}, {"moveId": "LUSTER_PURGE", "uses": 19613}, {"moveId": "PSYCHIC", "uses": 12995}, {"moveId": "RETURN", "uses": 7313}, {"moveId": "SOLAR_BEAM", "uses": 6720}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 87.6}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 855, "matchups": [{"opponent": "gyarados", "rating": 814}, {"opponent": "metagross", "rating": 645}, {"opponent": "lugia", "rating": 626}, {"opponent": "mewtwo", "rating": 569}, {"opponent": "garcho<PERSON>", "rating": 548}], "counters": [{"opponent": "zekrom", "rating": 285}, {"opponent": "giratina_origin", "rating": 332}, {"opponent": "excadrill", "rating": 341}, {"opponent": "dialga", "rating": 399}, {"opponent": "giratina_altered", "rating": 402}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 49299}, {"moveId": "CHARGE_BEAM", "uses": 27201}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 29501}, {"moveId": "THUNDERBOLT", "uses": 14293}, {"moveId": "ANCIENT_POWER", "uses": 13143}, {"moveId": "RETURN", "uses": 7340}, {"moveId": "THUNDER", "uses": 6270}, {"moveId": "ZAP_CANNON", "uses": 6004}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 87.6}, {"speciesId": "x<PERSON><PERSON>", "speciesName": "Xerneas", "rating": 791, "matchups": [{"opponent": "dialga", "rating": 833}, {"opponent": "dragonite", "rating": 792}, {"opponent": "gyarados", "rating": 728}, {"opponent": "garcho<PERSON>", "rating": 621}, {"opponent": "zacian_hero", "rating": 586}], "counters": [{"opponent": "grou<PERSON>", "rating": 198}, {"opponent": "ho_oh", "rating": 273}, {"opponent": "metagross", "rating": 383}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "giratina_origin", "rating": 444}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 68372}, {"moveId": "ZEN_HEADBUTT", "uses": 8128}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27209}, {"moveId": "MOONBLAST", "uses": 19191}, {"moveId": "MEGAHORN", "uses": 14038}, {"moveId": "THUNDER", "uses": 10856}, {"moveId": "GIGA_IMPACT", "uses": 5376}]}, "moveset": ["TACKLE", "CLOSE_COMBAT", "MOONBLAST"], "score": 87.4}, {"speciesId": "buzzwole", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 852, "matchups": [{"opponent": "excadrill", "rating": 817}, {"opponent": "swampert", "rating": 798}, {"opponent": "dialga", "rating": 731}, {"opponent": "garcho<PERSON>", "rating": 688}, {"opponent": "metagross", "rating": 559}], "counters": [{"opponent": "gyarados", "rating": 342}, {"opponent": "giratina_origin", "rating": 380}, {"opponent": "mewtwo", "rating": 395}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "dragonite", "rating": 452}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45467}, {"moveId": "POISON_JAB", "uses": 31033}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34367}, {"moveId": "LUNGE", "uses": 25671}, {"moveId": "FELL_STINGER", "uses": 8201}, {"moveId": "POWER_UP_PUNCH", "uses": 8190}]}, "moveset": ["COUNTER", "SUPER_POWER", "LUNGE"], "score": 87.2}, {"speciesId": "metagross_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 813, "matchups": [{"opponent": "zacian_hero", "rating": 892}, {"opponent": "metagross", "rating": 834}, {"opponent": "garcho<PERSON>", "rating": 787}, {"opponent": "dialga", "rating": 744}, {"opponent": "lugia", "rating": 659}], "counters": [{"opponent": "grou<PERSON>", "rating": 230}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "gyarados", "rating": 376}, {"opponent": "swampert", "rating": 390}, {"opponent": "giratina_origin", "rating": 446}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 68140}, {"moveId": "ZEN_HEADBUTT", "uses": 8360}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 33442}, {"moveId": "PSYCHIC", "uses": 19550}, {"moveId": "EARTHQUAKE", "uses": 18248}, {"moveId": "FLASH_CANNON", "uses": 5202}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 87.2}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 826, "matchups": [{"opponent": "excadrill", "rating": 937}, {"opponent": "metagross", "rating": 848}, {"opponent": "dialga", "rating": 713}, {"opponent": "zacian_hero", "rating": 542}, {"opponent": "mewtwo", "rating": 512}], "counters": [{"opponent": "dragonite", "rating": 321}, {"opponent": "gyarados", "rating": 337}, {"opponent": "giratina_origin", "rating": 344}, {"opponent": "lugia", "rating": 409}, {"opponent": "garcho<PERSON>", "rating": 485}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 48527}, {"moveId": "WATER_GUN", "uses": 27973}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 31752}, {"moveId": "EARTHQUAKE", "uses": 14537}, {"moveId": "MUDDY_WATER", "uses": 12201}, {"moveId": "SURF", "uses": 10539}, {"moveId": "SLUDGE_WAVE", "uses": 7447}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 87.2}, {"speciesId": "zap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 878, "matchups": [{"opponent": "gyarados", "rating": 814}, {"opponent": "zacian_hero", "rating": 787}, {"opponent": "lugia", "rating": 701}, {"opponent": "dragonite", "rating": 610}, {"opponent": "metagross", "rating": 580}], "counters": [{"opponent": "mewtwo", "rating": 333}, {"opponent": "dialga", "rating": 366}, {"opponent": "giratina_origin", "rating": 390}, {"opponent": "garcho<PERSON>", "rating": 403}, {"opponent": "excadrill", "rating": 430}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50222}, {"moveId": "CHARGE_BEAM", "uses": 26278}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 33026}, {"moveId": "THUNDERBOLT", "uses": 15595}, {"moveId": "ANCIENT_POWER", "uses": 14481}, {"moveId": "THUNDER", "uses": 6915}, {"moveId": "ZAP_CANNON", "uses": 6440}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 87.2}, {"speciesId": "dialga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 826, "matchups": [{"opponent": "giratina_origin", "rating": 703}, {"opponent": "lugia", "rating": 665}, {"opponent": "metagross", "rating": 663}, {"opponent": "mewtwo", "rating": 635}, {"opponent": "gyarados", "rating": 622}], "counters": [{"opponent": "excadrill", "rating": 253}, {"opponent": "grou<PERSON>", "rating": 274}, {"opponent": "swampert", "rating": 313}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 486}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52339}, {"moveId": "METAL_CLAW", "uses": 24161}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 28880}, {"moveId": "DRACO_METEOR", "uses": 25271}, {"moveId": "THUNDER", "uses": 22482}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 87.1}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 812, "matchups": [{"opponent": "zacian_hero", "rating": 721}, {"opponent": "metagross", "rating": 705}, {"opponent": "mewtwo", "rating": 679}, {"opponent": "dialga", "rating": 674}, {"opponent": "gyarados", "rating": 513}], "counters": [{"opponent": "lugia", "rating": 335}, {"opponent": "giratina_origin", "rating": 356}, {"opponent": "garcho<PERSON>", "rating": 462}, {"opponent": "swampert", "rating": 465}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16085}, {"moveId": "EXTRASENSORY", "uses": 5269}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4062}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4057}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3959}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3699}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3487}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3436}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3423}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3229}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3161}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3090}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3030}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2955}, {"moveId": "STEEL_WING", "uses": 2873}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2795}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2702}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2619}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2480}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 29075}, {"moveId": "SACRED_FIRE", "uses": 18811}, {"moveId": "EARTHQUAKE", "uses": 11937}, {"moveId": "RETURN", "uses": 6427}, {"moveId": "SOLAR_BEAM", "uses": 6366}, {"moveId": "FIRE_BLAST", "uses": 3749}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 86.7}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 798, "matchups": [{"opponent": "zacian_hero", "rating": 708}, {"opponent": "metagross", "rating": 679}, {"opponent": "dialga", "rating": 593}, {"opponent": "mewtwo", "rating": 593}, {"opponent": "gyarados", "rating": 546}], "counters": [{"opponent": "zekrom", "rating": 372}, {"opponent": "lugia", "rating": 388}, {"opponent": "giratina_origin", "rating": 428}, {"opponent": "ho_oh", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 481}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 18664}, {"moveId": "EXTRASENSORY", "uses": 5401}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3963}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3862}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3737}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3557}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3277}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3273}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3236}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3061}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3017}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2958}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2832}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2811}, {"moveId": "STEEL_WING", "uses": 2669}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2536}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2483}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2357}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2320}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 32096}, {"moveId": "SACRED_FIRE", "uses": 20406}, {"moveId": "EARTHQUAKE", "uses": 12863}, {"moveId": "SOLAR_BEAM", "uses": 6980}, {"moveId": "FIRE_BLAST", "uses": 4143}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 86.4}, {"speciesId": "terrakion", "speciesName": "Terrakion", "rating": 849, "matchups": [{"opponent": "excadrill", "rating": 930}, {"opponent": "dialga", "rating": 731}, {"opponent": "metagross", "rating": 625}, {"opponent": "swampert", "rating": 577, "opRating": 422}, {"opponent": "gyarados", "rating": 529}], "counters": [{"opponent": "mewtwo", "rating": 325}, {"opponent": "giratina_origin", "rating": 400}, {"opponent": "garcho<PERSON>", "rating": 462}, {"opponent": "lugia", "rating": 469}, {"opponent": "zacian_hero", "rating": 473}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47078}, {"moveId": "SMACK_DOWN", "uses": 26762}, {"moveId": "ZEN_HEADBUTT", "uses": 2633}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 24125}, {"moveId": "ROCK_SLIDE", "uses": 22743}, {"moveId": "CLOSE_COMBAT", "uses": 21388}, {"moveId": "EARTHQUAKE", "uses": 8214}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "ROCK_SLIDE"], "score": 86.4}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 832, "matchups": [{"opponent": "mewtwo", "rating": 686}, {"opponent": "gyarados", "rating": 648}, {"opponent": "metagross", "rating": 610}, {"opponent": "garcho<PERSON>", "rating": 544}, {"opponent": "dialga", "rating": 515}], "counters": [{"opponent": "giratina_origin", "rating": 217}, {"opponent": "grou<PERSON>", "rating": 334}, {"opponent": "dragonite", "rating": 417}, {"opponent": "excadrill", "rating": 448}, {"opponent": "kyogre", "rating": 494}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 49244}, {"moveId": "METAL_CLAW", "uses": 27256}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26835}, {"moveId": "MAGNET_BOMB", "uses": 25108}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 20825}, {"moveId": "HYPER_BEAM", "uses": 3605}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 86.1}, {"speciesId": "kommo_o", "speciesName": "Kommo-o", "rating": 835, "matchups": [{"opponent": "garcho<PERSON>", "rating": 887}, {"opponent": "excadrill", "rating": 701}, {"opponent": "giratina_origin", "rating": 695}, {"opponent": "dialga", "rating": 612}, {"opponent": "dragonite", "rating": 594}], "counters": [{"opponent": "zacian_hero", "rating": 242}, {"opponent": "lugia", "rating": 242}, {"opponent": "mewtwo", "rating": 361}, {"opponent": "gyarados", "rating": 399}, {"opponent": "metagross", "rating": 430}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42103}, {"moveId": "POISON_JAB", "uses": 34397}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35291}, {"moveId": "DRAGON_CLAW", "uses": 29134}, {"moveId": "FLAMETHROWER", "uses": 12125}]}, "moveset": ["DRAGON_TAIL", "CLOSE_COMBAT", "DRAGON_CLAW"], "score": 86}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 753, "matchups": [{"opponent": "giratina_origin", "rating": 884}, {"opponent": "garcho<PERSON>", "rating": 856}, {"opponent": "excadrill", "rating": 856}, {"opponent": "mewtwo", "rating": 828}, {"opponent": "gyarados", "rating": 688}], "counters": [{"opponent": "zacian_hero", "rating": 338}, {"opponent": "snorlax", "rating": 375}, {"opponent": "lugia", "rating": 430}, {"opponent": "dialga", "rating": 437}, {"opponent": "metagross", "rating": 444}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 12935}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4979}, {"moveId": "CHARGE_BEAM", "uses": 4680}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4295}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4041}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3954}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3950}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3865}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3791}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3677}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3621}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3615}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3575}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3356}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3291}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3272}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3105}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3006}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 23080}, {"moveId": "BLIZZARD", "uses": 17613}, {"moveId": "ZAP_CANNON", "uses": 14703}, {"moveId": "HYPER_BEAM", "uses": 11273}, {"moveId": "SOLAR_BEAM", "uses": 9648}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 86}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 840, "matchups": [{"opponent": "dialga", "rating": 841}, {"opponent": "gyarados", "rating": 788}, {"opponent": "lugia", "rating": 762}, {"opponent": "excadrill", "rating": 594}, {"opponent": "dragonite", "rating": 581}], "counters": [{"opponent": "garcho<PERSON>", "rating": 251}, {"opponent": "metagross", "rating": 340}, {"opponent": "giratina_origin", "rating": 426}, {"opponent": "mewtwo", "rating": 447}, {"opponent": "zacian_hero", "rating": 456}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 76500}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 26439}, {"moveId": "ROCK_SLIDE", "uses": 21521}, {"moveId": "THUNDERBOLT", "uses": 12336}, {"moveId": "FLASH_CANNON", "uses": 9874}, {"moveId": "HYPER_BEAM", "uses": 6464}]}, "moveset": ["THUNDER_SHOCK", "SUPER_POWER", "ROCK_SLIDE"], "score": 85.8}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 738, "matchups": [{"opponent": "mewtwo", "rating": 920}, {"opponent": "garcho<PERSON>", "rating": 885}, {"opponent": "excadrill", "rating": 837}, {"opponent": "giratina_origin", "rating": 805}, {"opponent": "dialga", "rating": 538}], "counters": [{"opponent": "zacian_hero", "rating": 349}, {"opponent": "gyarados", "rating": 427}, {"opponent": "kyogre", "rating": 451}, {"opponent": "grou<PERSON>", "rating": 475}, {"opponent": "yveltal", "rating": 481}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 31529}, {"moveId": "ICE_SHARD", "uses": 27732}, {"moveId": "FEINT_ATTACK", "uses": 17213}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41685}, {"moveId": "FOUL_PLAY", "uses": 23972}, {"moveId": "FOCUS_BLAST", "uses": 10743}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 85.6}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 812, "matchups": [{"opponent": "metagross", "rating": 816}, {"opponent": "dialga", "rating": 755}, {"opponent": "mewtwo", "rating": 662}, {"opponent": "lugia", "rating": 651}, {"opponent": "gyarados", "rating": 567}], "counters": [{"opponent": "zacian_hero", "rating": 297}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "swampert", "rating": 323}, {"opponent": "dragonite", "rating": 329}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38876}, {"moveId": "MUD_SLAP", "uses": 20145}, {"moveId": "METAL_CLAW", "uses": 17474}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32016}, {"moveId": "ROCK_SLIDE", "uses": 22232}, {"moveId": "IRON_HEAD", "uses": 13983}, {"moveId": "EARTHQUAKE", "uses": 8238}]}, "moveset": ["MUD_SHOT", "DRILL_RUN", "ROCK_SLIDE"], "score": 85.3}, {"speciesId": "mew", "speciesName": "Mew", "rating": 820, "matchups": [{"opponent": "gyarados", "rating": 870}, {"opponent": "metagross", "rating": 788}, {"opponent": "zacian_hero", "rating": 736}, {"opponent": "garcho<PERSON>", "rating": 599}, {"opponent": "mewtwo", "rating": 537}], "counters": [{"opponent": "dragonite", "rating": 188}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "zekrom", "rating": 301}, {"opponent": "dialga", "rating": 361}, {"opponent": "swampert", "rating": 455}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 12080}, {"moveId": "VOLT_SWITCH", "uses": 10834}, {"moveId": "SNARL", "uses": 9099}, {"moveId": "POISON_JAB", "uses": 7728}, {"moveId": "DRAGON_TAIL", "uses": 7177}, {"moveId": "INFESTATION", "uses": 7063}, {"moveId": "CHARGE_BEAM", "uses": 4685}, {"moveId": "WATERFALL", "uses": 4596}, {"moveId": "FROST_BREATH", "uses": 4389}, {"moveId": "STEEL_WING", "uses": 2898}, {"moveId": "STRUGGLE_BUG", "uses": 2591}, {"moveId": "ROCK_SMASH", "uses": 2010}, {"moveId": "CUT", "uses": 1048}, {"moveId": "POUND", "uses": 101}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8457}, {"moveId": "DRAGON_CLAW", "uses": 5896}, {"moveId": "SURF", "uses": 5602}, {"moveId": "ROCK_SLIDE", "uses": 5281}, {"moveId": "PSYSHOCK", "uses": 4898}, {"moveId": "ICE_BEAM", "uses": 4722}, {"moveId": "FLAME_CHARGE", "uses": 4342}, {"moveId": "DARK_PULSE", "uses": 4286}, {"moveId": "GRASS_KNOT", "uses": 4185}, {"moveId": "FOCUS_BLAST", "uses": 2849}, {"moveId": "LOW_SWEEP", "uses": 2583}, {"moveId": "BULLDOZE", "uses": 2292}, {"moveId": "STONE_EDGE", "uses": 2144}, {"moveId": "DAZZLING_GLEAM", "uses": 2021}, {"moveId": "PSYCHIC", "uses": 1888}, {"moveId": "ANCIENT_POWER", "uses": 1824}, {"moveId": "OVERHEAT", "uses": 1717}, {"moveId": "BLIZZARD", "uses": 1668}, {"moveId": "GYRO_BALL", "uses": 1529}, {"moveId": "ENERGY_BALL", "uses": 1481}, {"moveId": "FLASH_CANNON", "uses": 1469}, {"moveId": "THUNDERBOLT", "uses": 1468}, {"moveId": "HYPER_BEAM", "uses": 1416}, {"moveId": "THUNDER", "uses": 1360}, {"moveId": "SOLAR_BEAM", "uses": 815}]}, "moveset": ["SHADOW_CLAW", "SURF", "WILD_CHARGE"], "score": 84.9}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 846, "matchups": [{"opponent": "zacian_hero", "rating": 882}, {"opponent": "metagross", "rating": 863}, {"opponent": "gyarados", "rating": 853}, {"opponent": "lugia", "rating": 824}, {"opponent": "mewtwo", "rating": 525}], "counters": [{"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "swampert", "rating": 286}, {"opponent": "giratina_origin", "rating": 304}, {"opponent": "dragonite", "rating": 460}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47955}, {"moveId": "CHARGE_BEAM", "uses": 28545}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 41025}, {"moveId": "MIRROR_SHOT", "uses": 19026}, {"moveId": "FLASH_CANNON", "uses": 10172}, {"moveId": "ZAP_CANNON", "uses": 6381}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 84.7}, {"speciesId": "regirock", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 826, "matchups": [{"opponent": "lugia", "rating": 700}, {"opponent": "zacian_hero", "rating": 662}, {"opponent": "gyarados", "rating": 610}, {"opponent": "giratina_origin", "rating": 595}, {"opponent": "garcho<PERSON>", "rating": 508}], "counters": [{"opponent": "grou<PERSON>", "rating": 279}, {"opponent": "metagross", "rating": 316}, {"opponent": "swampert", "rating": 323}, {"opponent": "dialga", "rating": 459}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 40999}, {"moveId": "ROCK_THROW", "uses": 25528}, {"moveId": "ROCK_SMASH", "uses": 9966}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 30549}, {"moveId": "EARTHQUAKE", "uses": 18978}, {"moveId": "FOCUS_BLAST", "uses": 14039}, {"moveId": "ZAP_CANNON", "uses": 12845}]}, "moveset": ["LOCK_ON", "STONE_EDGE", "FOCUS_BLAST"], "score": 84.7}, {"speciesId": "cobalion", "speciesName": "Cobalion", "rating": 840, "matchups": [{"opponent": "dialga", "rating": 813}, {"opponent": "gyarados", "rating": 707}, {"opponent": "metagross", "rating": 680}, {"opponent": "excadrill", "rating": 569}, {"opponent": "zacian_hero", "rating": 547}], "counters": [{"opponent": "giratina_origin", "rating": 346}, {"opponent": "garcho<PERSON>", "rating": 354}, {"opponent": "dragonite", "rating": 369}, {"opponent": "lugia", "rating": 388}, {"opponent": "mewtwo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 50433}, {"moveId": "METAL_CLAW", "uses": 21837}, {"moveId": "ZEN_HEADBUTT", "uses": 4202}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 26266}, {"moveId": "CLOSE_COMBAT", "uses": 24146}, {"moveId": "STONE_EDGE", "uses": 14745}, {"moveId": "IRON_HEAD", "uses": 11219}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "STONE_EDGE"], "score": 84.3}, {"speciesId": "kyurem", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 828, "matchups": [{"opponent": "garcho<PERSON>", "rating": 905}, {"opponent": "giratina_origin", "rating": 788}, {"opponent": "dragonite", "rating": 649}, {"opponent": "swampert", "rating": 635}, {"opponent": "mewtwo", "rating": 594}], "counters": [{"opponent": "lugia", "rating": 292}, {"opponent": "metagross", "rating": 334}, {"opponent": "excadrill", "rating": 386}, {"opponent": "dialga", "rating": 421}, {"opponent": "gyarados", "rating": 481}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 56539}, {"moveId": "STEEL_WING", "uses": 19961}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 40958}, {"moveId": "BLIZZARD", "uses": 22095}, {"moveId": "DRACO_METEOR", "uses": 13652}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "BLIZZARD"], "score": 83.8}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 790, "matchups": [{"opponent": "zacian_hero", "rating": 851}, {"opponent": "giratina_origin", "rating": 730}, {"opponent": "mewtwo", "rating": 659}, {"opponent": "metagross", "rating": 520}, {"opponent": "dialga", "rating": 517}], "counters": [{"opponent": "lugia", "rating": 340}, {"opponent": "garcho<PERSON>", "rating": 401}, {"opponent": "dragonite", "rating": 406}, {"opponent": "zekrom", "rating": 410}, {"opponent": "gyarados", "rating": 461}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 65217}, {"moveId": "ZEN_HEADBUTT", "uses": 9286}, {"moveId": "YAWN", "uses": 2105}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24726}, {"moveId": "SUPER_POWER", "uses": 16228}, {"moveId": "OUTRAGE", "uses": 9161}, {"moveId": "EARTHQUAKE", "uses": 8980}, {"moveId": "HEAVY_SLAM", "uses": 6851}, {"moveId": "SKULL_BASH", "uses": 4249}, {"moveId": "RETURN", "uses": 3601}, {"moveId": "HYPER_BEAM", "uses": 2780}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 83.8}, {"speciesId": "avalugg", "speciesName": "Avalugg", "rating": 752, "matchups": [{"opponent": "garcho<PERSON>", "rating": 912}, {"opponent": "dragonite", "rating": 760}, {"opponent": "giratina_origin", "rating": 742}, {"opponent": "lugia", "rating": 639}, {"opponent": "gyarados", "rating": 618}], "counters": [{"opponent": "metagross", "rating": 279}, {"opponent": "ho_oh", "rating": 419}, {"opponent": "dialga", "rating": 459}, {"opponent": "mewtwo", "rating": 473}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 52491}, {"moveId": "BITE", "uses": 24009}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 31694}, {"moveId": "BODY_SLAM", "uses": 17221}, {"moveId": "CRUNCH", "uses": 14424}, {"moveId": "EARTHQUAKE", "uses": 10125}, {"moveId": "MIRROR_COAT", "uses": 3078}]}, "moveset": ["ICE_FANG", "AVALANCHE", "BODY_SLAM"], "score": 83.7}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 783, "matchups": [{"opponent": "mewtwo", "rating": 939}, {"opponent": "metagross", "rating": 843}, {"opponent": "giratina_origin", "rating": 643}, {"opponent": "dialga", "rating": 614}, {"opponent": "lugia", "rating": 525}], "counters": [{"opponent": "grou<PERSON>", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "dragonite", "rating": 364}, {"opponent": "gyarados", "rating": 384}, {"opponent": "zacian_hero", "rating": 390}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45949}, {"moveId": "FEINT_ATTACK", "uses": 30551}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 26754}, {"moveId": "SHADOW_BALL", "uses": 20460}, {"moveId": "SLUDGE_BOMB", "uses": 15037}, {"moveId": "FOCUS_BLAST", "uses": 14140}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 83.6}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 782, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "zacian_hero", "rating": 836}, {"opponent": "dialga", "rating": 661}, {"opponent": "mewtwo", "rating": 564}, {"opponent": "garcho<PERSON>", "rating": 526}], "counters": [{"opponent": "giratina_origin", "rating": 276}, {"opponent": "gyarados", "rating": 327}, {"opponent": "dragonite", "rating": 327}, {"opponent": "excadrill", "rating": 344}, {"opponent": "swampert", "rating": 363}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38975}, {"moveId": "WING_ATTACK", "uses": 37525}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26925}, {"moveId": "OVERHEAT", "uses": 17006}, {"moveId": "ANCIENT_POWER", "uses": 16534}, {"moveId": "RETURN", "uses": 8183}, {"moveId": "FIRE_BLAST", "uses": 4884}, {"moveId": "HEAT_WAVE", "uses": 2995}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 83.2}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 827, "matchups": [{"opponent": "excadrill", "rating": 875}, {"opponent": "metagross", "rating": 741}, {"opponent": "dialga", "rating": 720}, {"opponent": "yveltal", "rating": 595, "opRating": 404}, {"opponent": "zekrom", "rating": 561, "opRating": 438}], "counters": [{"opponent": "giratina_origin", "rating": 350}, {"opponent": "gyarados", "rating": 360}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "mewtwo", "rating": 385}, {"opponent": "garcho<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 38399}, {"moveId": "POISON_JAB", "uses": 30041}, {"moveId": "ROCK_SMASH", "uses": 8023}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 44039}, {"moveId": "X_SCISSOR", "uses": 18546}, {"moveId": "AERIAL_ACE", "uses": 13900}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "X_SCISSOR"], "score": 83.1}, {"speciesId": "virizion", "speciesName": "Virizion", "rating": 789, "matchups": [{"opponent": "excadrill", "rating": 827}, {"opponent": "dialga", "rating": 752}, {"opponent": "gyarados", "rating": 662}, {"opponent": "garcho<PERSON>", "rating": 651}, {"opponent": "metagross", "rating": 526}], "counters": [{"opponent": "lugia", "rating": 145}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 235}, {"opponent": "dragonite", "rating": 263}, {"opponent": "giratina_origin", "rating": 288}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 41001}, {"moveId": "QUICK_ATTACK", "uses": 32535}, {"moveId": "ZEN_HEADBUTT", "uses": 2914}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26363}, {"moveId": "SACRED_SWORD", "uses": 20086}, {"moveId": "CLOSE_COMBAT", "uses": 18251}, {"moveId": "STONE_EDGE", "uses": 11900}]}, "moveset": ["DOUBLE_KICK", "LEAF_BLADE", "SACRED_SWORD"], "score": 83}, {"speciesId": "charizard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 776, "matchups": [{"opponent": "garcho<PERSON>", "rating": 889}, {"opponent": "metagross", "rating": 782}, {"opponent": "dragonite", "rating": 622}, {"opponent": "dialga", "rating": 568}, {"opponent": "zacian_hero", "rating": 520}], "counters": [{"opponent": "excadrill", "rating": 283}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "lugia", "rating": 397}, {"opponent": "gyarados", "rating": 448}, {"opponent": "giratina_origin", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17162}, {"moveId": "DRAGON_BREATH", "uses": 16260}, {"moveId": "WING_ATTACK", "uses": 15738}, {"moveId": "EMBER", "uses": 15399}, {"moveId": "AIR_SLASH", "uses": 11917}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 32754}, {"moveId": "DRAGON_CLAW", "uses": 25115}, {"moveId": "FLAMETHROWER", "uses": 7495}, {"moveId": "OVERHEAT", "uses": 7114}, {"moveId": "FIRE_BLAST", "uses": 4099}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 82.7}, {"speciesId": "goodra", "speciesName": "<PERSON><PERSON>", "rating": 819, "matchups": [{"opponent": "grou<PERSON>", "rating": 817}, {"opponent": "excadrill", "rating": 706}, {"opponent": "mewtwo", "rating": 650}, {"opponent": "giratina_origin", "rating": 612}, {"opponent": "dialga", "rating": 529}], "counters": [{"opponent": "zacian_hero", "rating": 320}, {"opponent": "gyarados", "rating": 335}, {"opponent": "lugia", "rating": 359}, {"opponent": "dragonite", "rating": 428}, {"opponent": "garcho<PERSON>", "rating": 429}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 49362}, {"moveId": "WATER_GUN", "uses": 27138}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22418}, {"moveId": "MUDDY_WATER", "uses": 21432}, {"moveId": "DRACO_METEOR", "uses": 19631}, {"moveId": "SLUDGE_WAVE", "uses": 12970}]}, "moveset": ["DRAGON_BREATH", "MUDDY_WATER", "DRACO_METEOR"], "score": 82.7}, {"speciesId": "z<PERSON><PERSON><PERSON>_hero", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hero)", "rating": 836, "matchups": [{"opponent": "excadrill", "rating": 927}, {"opponent": "metagross", "rating": 754}, {"opponent": "giratina_origin", "rating": 710}, {"opponent": "dialga", "rating": 673}, {"opponent": "zekrom", "rating": 580, "opRating": 419}], "counters": [{"opponent": "zacian_hero", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "lugia", "rating": 423}, {"opponent": "gyarados", "rating": 471}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 26736}, {"moveId": "QUICK_ATTACK", "uses": 25433}, {"moveId": "ICE_FANG", "uses": 14894}, {"moveId": "METAL_CLAW", "uses": 9459}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35180}, {"moveId": "CRUNCH", "uses": 19522}, {"moveId": "MOONBLAST", "uses": 13108}, {"moveId": "IRON_HEAD", "uses": 8855}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 82.7}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 732, "matchups": [{"opponent": "garcho<PERSON>", "rating": 894}, {"opponent": "giratina_origin", "rating": 738}, {"opponent": "mewtwo", "rating": 687}, {"opponent": "lugia", "rating": 678}, {"opponent": "dialga", "rating": 614}], "counters": [{"opponent": "zacian_hero", "rating": 286}, {"opponent": "gyarados", "rating": 360}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 396}, {"opponent": "excadrill", "rating": 409}, {"opponent": "grou<PERSON>", "rating": 437}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30687}, {"moveId": "ICE_SHARD", "uses": 27692}, {"moveId": "FEINT_ATTACK", "uses": 18150}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38245}, {"moveId": "FOUL_PLAY", "uses": 21884}, {"moveId": "FOCUS_BLAST", "uses": 9841}, {"moveId": "RETURN", "uses": 6568}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 82.5}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Ray<PERSON><PERSON>", "rating": 789, "matchups": [{"opponent": "garcho<PERSON>", "rating": 934}, {"opponent": "giratina_origin", "rating": 777}, {"opponent": "gyarados", "rating": 620}, {"opponent": "excadrill", "rating": 620}, {"opponent": "mewtwo", "rating": 547}], "counters": [{"opponent": "zacian_hero", "rating": 283}, {"opponent": "dialga", "rating": 307}, {"opponent": "metagross", "rating": 322}, {"opponent": "lugia", "rating": 428}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 46871}, {"moveId": "AIR_SLASH", "uses": 29629}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23883}, {"moveId": "ANCIENT_POWER", "uses": 19217}, {"moveId": "AERIAL_ACE", "uses": 17519}, {"moveId": "HURRICANE", "uses": 15938}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "ANCIENT_POWER"], "score": 82.3}, {"speciesId": "articuno", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 731, "matchups": [{"opponent": "garcho<PERSON>", "rating": 954}, {"opponent": "dragonite", "rating": 793}, {"opponent": "giratina_origin", "rating": 674}, {"opponent": "gyarados", "rating": 599}, {"opponent": "mewtwo", "rating": 518}], "counters": [{"opponent": "metagross", "rating": 261}, {"opponent": "ho_oh", "rating": 382}, {"opponent": "excadrill", "rating": 416}, {"opponent": "dialga", "rating": 461}, {"opponent": "zekrom", "rating": 489}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 45527}, {"moveId": "FROST_BREATH", "uses": 30973}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 28475}, {"moveId": "ANCIENT_POWER", "uses": 13983}, {"moveId": "HURRICANE", "uses": 11580}, {"moveId": "ICE_BEAM", "uses": 9186}, {"moveId": "RETURN", "uses": 6862}, {"moveId": "BLIZZARD", "uses": 6411}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 82.2}, {"speciesId": "articuno_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 750, "matchups": [{"opponent": "garcho<PERSON>", "rating": 954}, {"opponent": "dragonite", "rating": 733}, {"opponent": "lugia", "rating": 645}, {"opponent": "zacian_hero", "rating": 634}, {"opponent": "giratina_origin", "rating": 599}], "counters": [{"opponent": "metagross", "rating": 319}, {"opponent": "dialga", "rating": 383}, {"opponent": "mewtwo", "rating": 393}, {"opponent": "ho_oh", "rating": 453}, {"opponent": "excadrill", "rating": 493}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46311}, {"moveId": "FROST_BREATH", "uses": 30189}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31093}, {"moveId": "ANCIENT_POWER", "uses": 15581}, {"moveId": "HURRICANE", "uses": 12989}, {"moveId": "ICE_BEAM", "uses": 10012}, {"moveId": "BLIZZARD", "uses": 7019}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 82.2}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 831, "matchups": [{"opponent": "metagross", "rating": 777}, {"opponent": "gyarados", "rating": 768}, {"opponent": "garcho<PERSON>", "rating": 740}, {"opponent": "zacian_hero", "rating": 731}, {"opponent": "lugia", "rating": 704}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "excadrill", "rating": 248}, {"opponent": "swampert", "rating": 296}, {"opponent": "mewtwo", "rating": 424}, {"opponent": "giratina_origin", "rating": 426}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 68153}, {"moveId": "LOW_KICK", "uses": 8347}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28343}, {"moveId": "ICE_PUNCH", "uses": 19259}, {"moveId": "THUNDER_PUNCH", "uses": 13792}, {"moveId": "FLAMETHROWER", "uses": 10506}, {"moveId": "THUNDER", "uses": 4506}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 82.2}, {"speciesId": "gengar", "speciesName": "Gengar", "rating": 759, "matchups": [{"opponent": "excadrill", "rating": 774}, {"opponent": "mewtwo", "rating": 742}, {"opponent": "metagross", "rating": 742}, {"opponent": "giratina_origin", "rating": 647}, {"opponent": "zacian_hero", "rating": 584}], "counters": [{"opponent": "grou<PERSON>", "rating": 423}, {"opponent": "garcho<PERSON>", "rating": 443}, {"opponent": "dialga", "rating": 459}, {"opponent": "gyarados", "rating": 497}, {"opponent": "dragonite", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 28923}, {"moveId": "HEX", "uses": 18804}, {"moveId": "LICK", "uses": 15008}, {"moveId": "SUCKER_PUNCH", "uses": 13841}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16632}, {"moveId": "SHADOW_PUNCH", "uses": 16497}, {"moveId": "SLUDGE_BOMB", "uses": 11755}, {"moveId": "DARK_PULSE", "uses": 10841}, {"moveId": "FOCUS_BLAST", "uses": 8834}, {"moveId": "PSYCHIC", "uses": 8084}, {"moveId": "SLUDGE_WAVE", "uses": 3963}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SHADOW_PUNCH"], "score": 82.2}, {"speciesId": "walrein", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 741, "matchups": [{"opponent": "garcho<PERSON>", "rating": 895}, {"opponent": "dragonite", "rating": 800}, {"opponent": "dialga", "rating": 602}, {"opponent": "giratina_origin", "rating": 544}, {"opponent": "gyarados", "rating": 518}], "counters": [{"opponent": "ho_oh", "rating": 291}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "metagross", "rating": 380}, {"opponent": "zacian_hero", "rating": 416}, {"opponent": "kyogre", "rating": 470}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 36578}, {"moveId": "WATERFALL", "uses": 21595}, {"moveId": "FROST_BREATH", "uses": 18320}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 35783}, {"moveId": "EARTHQUAKE", "uses": 16216}, {"moveId": "RETURN", "uses": 8629}, {"moveId": "WATER_PULSE", "uses": 8082}, {"moveId": "BLIZZARD", "uses": 7842}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 82.2}, {"speciesId": "walrein_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 763, "matchups": [{"opponent": "garcho<PERSON>", "rating": 916}, {"opponent": "dragonite", "rating": 788}, {"opponent": "dialga", "rating": 676}, {"opponent": "excadrill", "rating": 595}, {"opponent": "swampert", "rating": 541, "opRating": 458}], "counters": [{"opponent": "giratina_origin", "rating": 368}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "lugia", "rating": 466}, {"opponent": "gyarados", "rating": 471}, {"opponent": "metagross", "rating": 479}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 38095}, {"moveId": "WATERFALL", "uses": 21140}, {"moveId": "FROST_BREATH", "uses": 17261}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 40045}, {"moveId": "EARTHQUAKE", "uses": 18400}, {"moveId": "WATER_PULSE", "uses": 9251}, {"moveId": "BLIZZARD", "uses": 8827}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 81.4}, {"speciesId": "raikou_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 793, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "zacian_hero", "rating": 814}, {"opponent": "gyarados", "rating": 766}, {"opponent": "lugia", "rating": 704}, {"opponent": "mewtwo", "rating": 521}], "counters": [{"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "zekrom", "rating": 339}, {"opponent": "swampert", "rating": 415}, {"opponent": "dialga", "rating": 418}, {"opponent": "giratina_origin", "rating": 426}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41884}, {"moveId": "THUNDER_SHOCK", "uses": 34616}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 40233}, {"moveId": "SHADOW_BALL", "uses": 22541}, {"moveId": "THUNDERBOLT", "uses": 7196}, {"moveId": "THUNDER", "uses": 6356}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 81.1}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 811, "matchups": [{"opponent": "gyarados", "rating": 784}, {"opponent": "metagross", "rating": 775}, {"opponent": "lugia", "rating": 724}, {"opponent": "dragonite", "rating": 639}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622, "opRating": 377}], "counters": [{"opponent": "zacian_hero", "rating": 335}, {"opponent": "mewtwo", "rating": 351}, {"opponent": "dialga", "rating": 423}, {"opponent": "giratina_origin", "rating": 470}, {"opponent": "garcho<PERSON>", "rating": 478}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40761}, {"moveId": "SPARK", "uses": 35739}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 28544}, {"moveId": "POWER_WHIP", "uses": 23103}, {"moveId": "DAZZLING_GLEAM", "uses": 14289}, {"moveId": "THUNDER", "uses": 10540}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "DAZZLING_GLEAM"], "score": 81.1}, {"speciesId": "tapu_fini", "speciesName": "<PERSON><PERSON>", "rating": 756, "matchups": [{"opponent": "dragonite", "rating": 703}, {"opponent": "excadrill", "rating": 700}, {"opponent": "garcho<PERSON>", "rating": 630}, {"opponent": "gyarados", "rating": 627}, {"opponent": "lugia", "rating": 506}], "counters": [{"opponent": "metagross", "rating": 322}, {"opponent": "giratina_origin", "rating": 422}, {"opponent": "zacian_hero", "rating": 433}, {"opponent": "dialga", "rating": 475}, {"opponent": "mewtwo", "rating": 476}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 8524}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5660}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5171}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4708}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4569}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4566}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4464}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4241}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4224}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4176}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4155}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4131}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3683}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3671}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3626}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3577}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3405}], "chargedMoves": [{"moveId": "SURF", "uses": 31424}, {"moveId": "MOONBLAST", "uses": 22737}, {"moveId": "ICE_BEAM", "uses": 17309}, {"moveId": "HYDRO_PUMP", "uses": 4985}]}, "moveset": ["WATER_GUN", "SURF", "MOONBLAST"], "score": 81}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 777, "matchups": [{"opponent": "excadrill", "rating": 902}, {"opponent": "metagross", "rating": 820}, {"opponent": "mewtwo", "rating": 626}, {"opponent": "dialga", "rating": 589}, {"opponent": "zacian_hero", "rating": 579}], "counters": [{"opponent": "giratina_origin", "rating": 207}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "grou<PERSON>", "rating": 309}, {"opponent": "swampert", "rating": 340}, {"opponent": "lugia", "rating": 357}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 43188}, {"moveId": "CONFUSION", "uses": 33312}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 48095}, {"moveId": "PSYCHIC", "uses": 14166}, {"moveId": "FOCUS_BLAST", "uses": 8652}, {"moveId": "OVERHEAT", "uses": 5512}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 80.9}, {"speciesId": "machamp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 800, "matchups": [{"opponent": "excadrill", "rating": 903}, {"opponent": "ho_oh", "rating": 771, "opRating": 228}, {"opponent": "metagross", "rating": 682}, {"opponent": "dialga", "rating": 680}, {"opponent": "zekrom", "rating": 508, "opRating": 491}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 389}, {"opponent": "dragonite", "rating": 486}, {"opponent": "gyarados", "rating": 487}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29997}, {"moveId": "KARATE_CHOP", "uses": 27854}, {"moveId": "BULLET_PUNCH", "uses": 18629}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 21204}, {"moveId": "CROSS_CHOP", "uses": 14603}, {"moveId": "ROCK_SLIDE", "uses": 12553}, {"moveId": "PAYBACK", "uses": 9131}, {"moveId": "HEAVY_SLAM", "uses": 5824}, {"moveId": "DYNAMIC_PUNCH", "uses": 5706}, {"moveId": "STONE_EDGE", "uses": 4922}, {"moveId": "SUBMISSION", "uses": 2508}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 80}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 796, "matchups": [{"opponent": "gyarados", "rating": 814}, {"opponent": "garcho<PERSON>", "rating": 777}, {"opponent": "metagross", "rating": 756}, {"opponent": "lugia", "rating": 661}, {"opponent": "dragonite", "rating": 631}], "counters": [{"opponent": "dialga", "rating": 342}, {"opponent": "giratina_origin", "rating": 344}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "mewtwo", "rating": 369}, {"opponent": "swampert", "rating": 378}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 67022}, {"moveId": "LOW_KICK", "uses": 9478}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26489}, {"moveId": "ICE_PUNCH", "uses": 17904}, {"moveId": "THUNDER_PUNCH", "uses": 13001}, {"moveId": "FLAMETHROWER", "uses": 9810}, {"moveId": "RETURN", "uses": 5123}, {"moveId": "THUNDER", "uses": 4190}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 79.9}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 715, "matchups": [{"opponent": "garcho<PERSON>", "rating": 894}, {"opponent": "dialga", "rating": 561}, {"opponent": "zacian_hero", "rating": 539}, {"opponent": "mewtwo", "rating": 530}, {"opponent": "giratina_origin", "rating": 509}], "counters": [{"opponent": "metagross", "rating": 235}, {"opponent": "swampert", "rating": 358}, {"opponent": "grou<PERSON>", "rating": 410}, {"opponent": "gyarados", "rating": 463}, {"opponent": "ho_oh", "rating": 476}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23849}, {"moveId": "FIRE_FANG", "uses": 19763}, {"moveId": "THUNDER_FANG", "uses": 19386}, {"moveId": "BITE", "uses": 13438}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 23531}, {"moveId": "BODY_SLAM", "uses": 20725}, {"moveId": "EARTH_POWER", "uses": 18018}, {"moveId": "EARTHQUAKE", "uses": 7804}, {"moveId": "STONE_EDGE", "uses": 6337}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 79.9}, {"speciesId": "tapu_bulu", "speciesName": "Tapu Bulu", "rating": 746, "matchups": [{"opponent": "garcho<PERSON>", "rating": 914}, {"opponent": "dragonite", "rating": 837}, {"opponent": "swampert", "rating": 818}, {"opponent": "gyarados", "rating": 713}, {"opponent": "zacian_hero", "rating": 541}], "counters": [{"opponent": "lugia", "rating": 316}, {"opponent": "dialga", "rating": 336}, {"opponent": "metagross", "rating": 369}, {"opponent": "mewtwo", "rating": 406}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 59601}, {"moveId": "ROCK_SMASH", "uses": 16899}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28660}, {"moveId": "MEGAHORN", "uses": 23257}, {"moveId": "DAZZLING_GLEAM", "uses": 18633}, {"moveId": "SOLAR_BEAM", "uses": 5973}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "DAZZLING_GLEAM"], "score": 79.9}, {"speciesId": "haxorus", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 811, "matchups": [{"opponent": "excadrill", "rating": 876}, {"opponent": "garcho<PERSON>", "rating": 840}, {"opponent": "kyogre", "rating": 753, "opRating": 246}, {"opponent": "metagross", "rating": 722}, {"opponent": "swampert", "rating": 551}], "counters": [{"opponent": "gyarados", "rating": 309}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "dragonite", "rating": 369}, {"opponent": "giratina_origin", "rating": 468}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 38311}, {"moveId": "DRAGON_TAIL", "uses": 38189}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 25655}, {"moveId": "NIGHT_SLASH", "uses": 22519}, {"moveId": "SURF", "uses": 17708}, {"moveId": "EARTHQUAKE", "uses": 10636}]}, "moveset": ["COUNTER", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 79.2}, {"speciesId": "nihilego", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 835, "matchups": [{"opponent": "zacian_hero", "rating": 913}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 852}, {"opponent": "gyarados", "rating": 850}, {"opponent": "dragonite", "rating": 801}, {"opponent": "lugia", "rating": 712}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "metagross", "rating": 241}, {"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "giratina_origin", "rating": 498}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54787}, {"moveId": "ACID", "uses": 18741}, {"moveId": "POUND", "uses": 3087}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 41280}, {"moveId": "SLUDGE_BOMB", "uses": 22024}, {"moveId": "POWER_GEM", "uses": 7510}, {"moveId": "GUNK_SHOT", "uses": 5709}]}, "moveset": ["POISON_JAB", "ROCK_SLIDE", "SLUDGE_BOMB"], "score": 79.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 767, "matchups": [{"opponent": "metagross", "rating": 771}, {"opponent": "dialga", "rating": 661}, {"opponent": "excadrill", "rating": 650}, {"opponent": "swampert", "rating": 591}, {"opponent": "zacian_hero", "rating": 586}], "counters": [{"opponent": "giratina_origin", "rating": 310}, {"opponent": "dragonite", "rating": 353}, {"opponent": "gyarados", "rating": 368}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "garcho<PERSON>", "rating": 485}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 27004}, {"moveId": "MUD_SLAP", "uses": 18411}, {"moveId": "CHARM", "uses": 15810}, {"moveId": "TACKLE", "uses": 15255}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30790}, {"moveId": "EARTHQUAKE", "uses": 22412}, {"moveId": "PLAY_ROUGH", "uses": 11855}, {"moveId": "HEAVY_SLAM", "uses": 11562}]}, "moveset": ["COUNTER", "BODY_SLAM", "EARTHQUAKE"], "score": 78.8}, {"speciesId": "con<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 784, "matchups": [{"opponent": "gyarados", "rating": 766}, {"opponent": "metagross", "rating": 709}, {"opponent": "excadrill", "rating": 656}, {"opponent": "dialga", "rating": 637}, {"opponent": "garcho<PERSON>", "rating": 514}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "lugia", "rating": 271}, {"opponent": "giratina_origin", "rating": 280}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "grou<PERSON>", "rating": 453}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45928}, {"moveId": "POISON_JAB", "uses": 30572}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 36169}, {"moveId": "STONE_EDGE", "uses": 30450}, {"moveId": "FOCUS_BLAST", "uses": 9869}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "STONE_EDGE"], "score": 78.7}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 780, "matchups": [{"opponent": "metagross", "rating": 863}, {"opponent": "dialga", "rating": 683}, {"opponent": "zacian_hero", "rating": 639}, {"opponent": "mewtwo", "rating": 614}, {"opponent": "excadrill", "rating": 511}], "counters": [{"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "giratina_origin", "rating": 254}, {"opponent": "dragonite", "rating": 260}, {"opponent": "gyarados", "rating": 304}, {"opponent": "lugia", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45106}, {"moveId": "FIRE_FANG", "uses": 31394}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 22101}, {"moveId": "OVERHEAT", "uses": 17099}, {"moveId": "IRON_HEAD", "uses": 12557}, {"moveId": "RETURN", "uses": 10658}, {"moveId": "FLAMETHROWER", "uses": 9151}, {"moveId": "FIRE_BLAST", "uses": 5005}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 78.6}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 767, "matchups": [{"opponent": "metagross", "rating": 885}, {"opponent": "dialga", "rating": 643}, {"opponent": "mewtwo", "rating": 638}, {"opponent": "zacian_hero", "rating": 574}, {"opponent": "lugia", "rating": 569}], "counters": [{"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "dragonite", "rating": 329}, {"opponent": "gyarados", "rating": 340}, {"opponent": "excadrill", "rating": 344}, {"opponent": "giratina_origin", "rating": 444}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46650}, {"moveId": "BUG_BITE", "uses": 29850}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 24928}, {"moveId": "STONE_EDGE", "uses": 24488}, {"moveId": "IRON_HEAD", "uses": 20256}, {"moveId": "FIRE_BLAST", "uses": 6751}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "IRON_HEAD"], "score": 78.4}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 754, "matchups": [{"opponent": "garcho<PERSON>", "rating": 884}, {"opponent": "giratina_origin", "rating": 727}, {"opponent": "grou<PERSON>", "rating": 696}, {"opponent": "gyarados", "rating": 693}, {"opponent": "dragonite", "rating": 660}], "counters": [{"opponent": "mewtwo", "rating": 294}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "lugia", "rating": 371}, {"opponent": "dialga", "rating": 388}, {"opponent": "excadrill", "rating": 486}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 11810}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5036}, {"moveId": "CHARGE_BEAM", "uses": 4531}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4316}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4131}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4048}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4026}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3927}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3814}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3746}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3704}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3701}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3673}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3394}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3353}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3324}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3176}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3069}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 20561}, {"moveId": "BLIZZARD", "uses": 16047}, {"moveId": "ZAP_CANNON", "uses": 13210}, {"moveId": "RETURN", "uses": 12990}, {"moveId": "SOLAR_BEAM", "uses": 8602}, {"moveId": "HYPER_BEAM", "uses": 5018}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 78.4}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 794, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "zacian_hero", "rating": 803}, {"opponent": "grou<PERSON>", "rating": 795}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 739}, {"opponent": "dialga", "rating": 594}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "dragonite", "rating": 385}, {"opponent": "gyarados", "rating": 386}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38580}, {"moveId": "WING_ATTACK", "uses": 37920}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30392}, {"moveId": "OVERHEAT", "uses": 18816}, {"moveId": "ANCIENT_POWER", "uses": 18490}, {"moveId": "FIRE_BLAST", "uses": 5461}, {"moveId": "HEAT_WAVE", "uses": 3207}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 78.3}, {"speciesId": "suicune_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 747, "matchups": [{"opponent": "dragonite", "rating": 723}, {"opponent": "metagross", "rating": 649}, {"opponent": "mewtwo", "rating": 582}, {"opponent": "garcho<PERSON>", "rating": 554}, {"opponent": "giratina_origin", "rating": 519}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "gyarados", "rating": 317}, {"opponent": "zekrom", "rating": 375}, {"opponent": "kyogre", "rating": 410}, {"opponent": "lugia", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7339}, {"moveId": "ICE_FANG", "uses": 6260}, {"moveId": "EXTRASENSORY", "uses": 4736}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4628}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4588}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4096}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3989}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3936}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3879}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3753}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3704}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3561}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3512}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3434}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3182}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3179}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3161}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3003}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2963}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31534}, {"moveId": "HYDRO_PUMP", "uses": 18284}, {"moveId": "BUBBLE_BEAM", "uses": 13558}, {"moveId": "WATER_PULSE", "uses": 13014}, {"moveId": "FRUSTRATION", "uses": 4}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 78.3}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 715, "matchups": [{"opponent": "dragonite", "rating": 727}, {"opponent": "dialga", "rating": 633}, {"opponent": "zacian_hero", "rating": 605}, {"opponent": "garcho<PERSON>", "rating": 596}, {"opponent": "giratina_origin", "rating": 502}], "counters": [{"opponent": "zekrom", "rating": 423}, {"opponent": "gyarados", "rating": 425}, {"opponent": "metagross", "rating": 433}, {"opponent": "mewtwo", "rating": 437}, {"opponent": "lugia", "rating": 473}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23340}, {"moveId": "THUNDER_FANG", "uses": 19464}, {"moveId": "FIRE_FANG", "uses": 19409}, {"moveId": "BITE", "uses": 14249}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 22787}, {"moveId": "BODY_SLAM", "uses": 19789}, {"moveId": "EARTH_POWER", "uses": 17379}, {"moveId": "EARTHQUAKE", "uses": 7525}, {"moveId": "STONE_EDGE", "uses": 6069}, {"moveId": "RETURN", "uses": 2889}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 78.1}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 760, "matchups": [{"opponent": "excadrill", "rating": 935}, {"opponent": "metagross", "rating": 783}, {"opponent": "dialga", "rating": 631}, {"opponent": "mewtwo", "rating": 521}, {"opponent": "garcho<PERSON>", "rating": 521}], "counters": [{"opponent": "gyarados", "rating": 262}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "lugia", "rating": 361}, {"opponent": "swampert", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38308}, {"moveId": "WING_ATTACK", "uses": 38192}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30911}, {"moveId": "EARTHQUAKE", "uses": 18791}, {"moveId": "AERIAL_ACE", "uses": 17399}, {"moveId": "SAND_TOMB", "uses": 9255}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "EARTHQUAKE"], "score": 77.9}, {"speciesId": "pinsir_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 769, "matchups": [{"opponent": "excadrill", "rating": 926}, {"opponent": "garcho<PERSON>", "rating": 875}, {"opponent": "metagross", "rating": 674}, {"opponent": "swampert", "rating": 630}, {"opponent": "dialga", "rating": 533}], "counters": [{"opponent": "giratina_origin", "rating": 233}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "lugia", "rating": 297}, {"opponent": "gyarados", "rating": 368}, {"opponent": "mewtwo", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40168}, {"moveId": "BUG_BITE", "uses": 26807}, {"moveId": "ROCK_SMASH", "uses": 9535}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25015}, {"moveId": "X_SCISSOR", "uses": 22158}, {"moveId": "SUPER_POWER", "uses": 18626}, {"moveId": "VICE_GRIP", "uses": 7704}, {"moveId": "SUBMISSION", "uses": 3015}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 77.9}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 784, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "gyarados", "rating": 768}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 717, "opRating": 282}, {"opponent": "lugia", "rating": 677}, {"opponent": "mewtwo", "rating": 604}], "counters": [{"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "dialga", "rating": 355}, {"opponent": "giratina_origin", "rating": 408}, {"opponent": "zacian_hero", "rating": 421}, {"opponent": "dragonite", "rating": 444}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41517}, {"moveId": "THUNDER_SHOCK", "uses": 34983}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 36350}, {"moveId": "SHADOW_BALL", "uses": 19381}, {"moveId": "RETURN", "uses": 8531}, {"moveId": "THUNDERBOLT", "uses": 6654}, {"moveId": "THUNDER", "uses": 5808}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 77.7}, {"speciesId": "sir<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 776, "matchups": [{"opponent": "swampert", "rating": 924}, {"opponent": "excadrill", "rating": 900}, {"opponent": "dialga", "rating": 686}, {"opponent": "metagross", "rating": 682}, {"opponent": "zekrom", "rating": 582, "opRating": 417}], "counters": [{"opponent": "giratina_origin", "rating": 249}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "gyarados", "rating": 342}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "zacian_hero", "rating": 453}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44533}, {"moveId": "FURY_CUTTER", "uses": 31967}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24573}, {"moveId": "LEAF_BLADE", "uses": 19527}, {"moveId": "NIGHT_SLASH", "uses": 17403}, {"moveId": "BRAVE_BIRD", "uses": 14971}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 77.7}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 750, "matchups": [{"opponent": "metagross", "rating": 706}, {"opponent": "dialga", "rating": 661}, {"opponent": "mewtwo", "rating": 621}, {"opponent": "zacian_hero", "rating": 589}, {"opponent": "giratina_origin", "rating": 535}], "counters": [{"opponent": "gyarados", "rating": 193}, {"opponent": "dragonite", "rating": 194}, {"opponent": "grou<PERSON>", "rating": 220}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "lugia", "rating": 376}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 40297}, {"moveId": "MUD_SLAP", "uses": 36203}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 24524}, {"moveId": "SUPER_POWER", "uses": 15091}, {"moveId": "SURF", "uses": 12716}, {"moveId": "EARTHQUAKE", "uses": 11750}, {"moveId": "STONE_EDGE", "uses": 6953}, {"moveId": "SKULL_BASH", "uses": 5457}]}, "moveset": ["MUD_SLAP", "ROCK_WRECKER", "SURF"], "score": 77.6}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 771, "matchups": [{"opponent": "excadrill", "rating": 843}, {"opponent": "metagross", "rating": 829}, {"opponent": "zacian_hero", "rating": 795}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747}, {"opponent": "dialga", "rating": 651}], "counters": [{"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "mewtwo", "rating": 299}, {"opponent": "dragonite", "rating": 380}, {"opponent": "gyarados", "rating": 394}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 41271}, {"moveId": "FIRE_FANG", "uses": 18204}, {"moveId": "TACKLE", "uses": 16992}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27041}, {"moveId": "OVERHEAT", "uses": 21180}, {"moveId": "PSYCHIC", "uses": 14545}, {"moveId": "FOCUS_BLAST", "uses": 13773}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 77.5}, {"speciesId": "heracross", "speciesName": "Heracross", "rating": 770, "matchups": [{"opponent": "metagross", "rating": 732}, {"opponent": "swampert", "rating": 729}, {"opponent": "excadrill", "rating": 712}, {"opponent": "dialga", "rating": 691}, {"opponent": "garcho<PERSON>", "rating": 517}], "counters": [{"opponent": "mewtwo", "rating": 229}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "dragonite", "rating": 324}, {"opponent": "gyarados", "rating": 363}, {"opponent": "grou<PERSON>", "rating": 486}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 58120}, {"moveId": "STRUGGLE_BUG", "uses": 18380}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32820}, {"moveId": "MEGAHORN", "uses": 18336}, {"moveId": "ROCK_BLAST", "uses": 15061}, {"moveId": "EARTHQUAKE", "uses": 10227}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ROCK_BLAST"], "score": 77.5}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 730, "matchups": [{"opponent": "mewtwo", "rating": 870}, {"opponent": "metagross", "rating": 794}, {"opponent": "swampert", "rating": 783}, {"opponent": "excadrill", "rating": 676}, {"opponent": "zacian_hero", "rating": 556}], "counters": [{"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "lugia", "rating": 378}, {"opponent": "dialga", "rating": 410}, {"opponent": "gyarados", "rating": 420}, {"opponent": "giratina_origin", "rating": 466}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 50325}, {"moveId": "SUCKER_PUNCH", "uses": 26175}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 29959}, {"moveId": "SEED_BOMB", "uses": 24019}, {"moveId": "FOUL_PLAY", "uses": 22539}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SEED_BOMB"], "score": 77.3}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 767, "matchups": [{"opponent": "dialga", "rating": 786}, {"opponent": "mewtwo", "rating": 671}, {"opponent": "swampert", "rating": 643}, {"opponent": "excadrill", "rating": 601}, {"opponent": "metagross", "rating": 595}], "counters": [{"opponent": "lugia", "rating": 283}, {"opponent": "dragonite", "rating": 300}, {"opponent": "gyarados", "rating": 332}, {"opponent": "garcho<PERSON>", "rating": 354}, {"opponent": "zacian_hero", "rating": 456}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46035}, {"moveId": "BUG_BITE", "uses": 30465}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 30577}, {"moveId": "MEGAHORN", "uses": 27412}, {"moveId": "AERIAL_ACE", "uses": 14674}, {"moveId": "ACID_SPRAY", "uses": 3957}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 77.2}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 714, "matchups": [{"opponent": "garcho<PERSON>", "rating": 885}, {"opponent": "giratina_origin", "rating": 778}, {"opponent": "dragonite", "rating": 701}, {"opponent": "excadrill", "rating": 637}, {"opponent": "lugia", "rating": 593}], "counters": [{"opponent": "metagross", "rating": 281}, {"opponent": "zacian_hero", "rating": 335}, {"opponent": "dialga", "rating": 385}, {"opponent": "mewtwo", "rating": 421}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47811}, {"moveId": "FROST_BREATH", "uses": 28689}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38906}, {"moveId": "ICY_WIND", "uses": 13524}, {"moveId": "LAST_RESORT", "uses": 9564}, {"moveId": "ICE_BEAM", "uses": 8742}, {"moveId": "WATER_PULSE", "uses": 5669}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 77.2}, {"speciesId": "pangoro", "speciesName": "Pangoro", "rating": 760, "matchups": [{"opponent": "excadrill", "rating": 914}, {"opponent": "metagross", "rating": 770}, {"opponent": "mewtwo", "rating": 698}, {"opponent": "dialga", "rating": 657}, {"opponent": "giratina_origin", "rating": 628}], "counters": [{"opponent": "zacian_hero", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "lugia", "rating": 321}, {"opponent": "gyarados", "rating": 347}, {"opponent": "dragonite", "rating": 364}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42461}, {"moveId": "BULLET_PUNCH", "uses": 28010}, {"moveId": "LOW_KICK", "uses": 6061}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29034}, {"moveId": "NIGHT_SLASH", "uses": 26574}, {"moveId": "ROCK_SLIDE", "uses": 14191}, {"moveId": "IRON_HEAD", "uses": 6620}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 77.2}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 763, "matchups": [{"opponent": "metagross", "rating": 818}, {"opponent": "zekrom", "rating": 595, "opRating": 404}, {"opponent": "grou<PERSON>", "rating": 589}, {"opponent": "dialga", "rating": 520}, {"opponent": "mewtwo", "rating": 514}], "counters": [{"opponent": "gyarados", "rating": 350}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "garcho<PERSON>", "rating": 471}, {"opponent": "dragonite", "rating": 478}, {"opponent": "zacian_hero", "rating": 491}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16905}, {"moveId": "DRAGON_BREATH", "uses": 16469}, {"moveId": "EMBER", "uses": 15616}, {"moveId": "WING_ATTACK", "uses": 15425}, {"moveId": "AIR_SLASH", "uses": 12125}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 29950}, {"moveId": "DRAGON_CLAW", "uses": 22480}, {"moveId": "RETURN", "uses": 6965}, {"moveId": "FLAMETHROWER", "uses": 6802}, {"moveId": "OVERHEAT", "uses": 6383}, {"moveId": "FIRE_BLAST", "uses": 3587}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 77}, {"speciesId": "machamp", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 779, "matchups": [{"opponent": "excadrill", "rating": 922}, {"opponent": "swampert", "rating": 685}, {"opponent": "metagross", "rating": 674}, {"opponent": "dialga", "rating": 658}, {"opponent": "gyarados", "rating": 510}], "counters": [{"opponent": "mewtwo", "rating": 200}, {"opponent": "giratina_origin", "rating": 219}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "dragonite", "rating": 406}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 30039}, {"moveId": "KARATE_CHOP", "uses": 27377}, {"moveId": "BULLET_PUNCH", "uses": 19159}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20214}, {"moveId": "CROSS_CHOP", "uses": 13901}, {"moveId": "ROCK_SLIDE", "uses": 11838}, {"moveId": "PAYBACK", "uses": 8599}, {"moveId": "HEAVY_SLAM", "uses": 5477}, {"moveId": "DYNAMIC_PUNCH", "uses": 5340}, {"moveId": "STONE_EDGE", "uses": 4671}, {"moveId": "RETURN", "uses": 4027}, {"moveId": "SUBMISSION", "uses": 2455}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 76.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 709, "matchups": [{"opponent": "garcho<PERSON>", "rating": 924}, {"opponent": "dragonite", "rating": 808}, {"opponent": "gyarados", "rating": 558}, {"opponent": "zacian_hero", "rating": 553}, {"opponent": "giratina_origin", "rating": 544}], "counters": [{"opponent": "metagross", "rating": 235}, {"opponent": "excadrill", "rating": 304}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "dialga", "rating": 421}, {"opponent": "swampert", "rating": 480}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 7538}, {"moveId": "AIR_SLASH", "uses": 6337}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4941}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4866}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4404}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4251}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4207}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4202}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4167}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3889}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3832}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3780}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3741}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3361}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3361}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3321}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3204}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3110}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 21444}, {"moveId": "AERIAL_ACE", "uses": 19697}, {"moveId": "FLAMETHROWER", "uses": 18665}, {"moveId": "DAZZLING_GLEAM", "uses": 16758}]}, "moveset": ["CHARM", "ANCIENT_POWER", "FLAMETHROWER"], "score": 76.8}, {"speciesId": "al<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 743, "matchups": [{"opponent": "excadrill", "rating": 829}, {"opponent": "garcho<PERSON>", "rating": 755}, {"opponent": "metagross", "rating": 629}, {"opponent": "dialga", "rating": 551}, {"opponent": "grou<PERSON>", "rating": 522}], "counters": [{"opponent": "zacian_hero", "rating": 332}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "lugia", "rating": 371}, {"opponent": "giratina_origin", "rating": 442}, {"opponent": "gyarados", "rating": 474}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29471}, {"moveId": "PSYCHO_CUT", "uses": 26434}, {"moveId": "CONFUSION", "uses": 20553}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18317}, {"moveId": "FIRE_PUNCH", "uses": 16664}, {"moveId": "PSYCHIC", "uses": 15556}, {"moveId": "FOCUS_BLAST", "uses": 10929}, {"moveId": "DAZZLING_GLEAM", "uses": 8264}, {"moveId": "FUTURE_SIGHT", "uses": 6697}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 76.6}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 757, "matchups": [{"opponent": "swampert", "rating": 909}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "dialga", "rating": 568}, {"opponent": "excadrill", "rating": 551}, {"opponent": "zekrom", "rating": 529, "opRating": 470}], "counters": [{"opponent": "giratina_origin", "rating": 314}, {"opponent": "dragonite", "rating": 359}, {"opponent": "metagross", "rating": 430}, {"opponent": "gyarados", "rating": 474}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44736}, {"moveId": "CHARM", "uses": 21977}, {"moveId": "LOW_KICK", "uses": 9787}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31138}, {"moveId": "LEAF_BLADE", "uses": 25796}, {"moveId": "SYNCHRONOISE", "uses": 13430}, {"moveId": "PSYCHIC", "uses": 6189}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 76.2}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 728, "matchups": [{"opponent": "gyarados", "rating": 767}, {"opponent": "lugia", "rating": 706}, {"opponent": "metagross", "rating": 607}, {"opponent": "mewtwo", "rating": 581}, {"opponent": "dialga", "rating": 558}], "counters": [{"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "swampert", "rating": 293}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "dragonite", "rating": 303}, {"opponent": "excadrill", "rating": 474}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42476}, {"moveId": "METAL_CLAW", "uses": 21833}, {"moveId": "ROCK_SMASH", "uses": 12190}], "chargedMoves": [{"moveId": "FLASH_CANNON", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "ZAP_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 76.1}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 771, "matchups": [{"opponent": "metagross", "rating": 805}, {"opponent": "ho_oh", "rating": 797, "opRating": 202}, {"opponent": "gyarados", "rating": 773}, {"opponent": "lugia", "rating": 702}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 700, "opRating": 300}], "counters": [{"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "mewtwo", "rating": 367}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "dialga", "rating": 383}, {"opponent": "giratina_origin", "rating": 480}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58317}, {"moveId": "ASTONISH", "uses": 18183}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25634}, {"moveId": "THUNDER_PUNCH", "uses": 23883}, {"moveId": "BRICK_BREAK", "uses": 19324}, {"moveId": "THUNDER", "uses": 7717}]}, "moveset": ["THUNDER_SHOCK", "CRUNCH", "THUNDER"], "score": 76.1}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 795, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 776}, {"opponent": "zacian_hero", "rating": 713}, {"opponent": "dragonite", "rating": 703}, {"opponent": "gyarados", "rating": 614}, {"opponent": "mewtwo", "rating": 549}], "counters": [{"opponent": "giratina_origin", "rating": 300}, {"opponent": "dialga", "rating": 317}, {"opponent": "garcho<PERSON>", "rating": 349}, {"opponent": "excadrill", "rating": 379}, {"opponent": "lugia", "rating": 480}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 46834}, {"moveId": "CHARGE_BEAM", "uses": 29666}], "chargedMoves": [{"moveId": "DOOM_DESIRE", "uses": 45658}, {"moveId": "PSYCHIC", "uses": 20203}, {"moveId": "DAZZLING_GLEAM", "uses": 10555}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 75.9}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 723, "matchups": [{"opponent": "metagross", "rating": 770}, {"opponent": "giratina_origin", "rating": 750}, {"opponent": "mewtwo", "rating": 734}, {"opponent": "dialga", "rating": 672}, {"opponent": "excadrill", "rating": 641}], "counters": [{"opponent": "dragonite", "rating": 276}, {"opponent": "gyarados", "rating": 278}, {"opponent": "garcho<PERSON>", "rating": 366}, {"opponent": "swampert", "rating": 437}, {"opponent": "lugia", "rating": 447}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46534}, {"moveId": "MUD_SLAP", "uses": 29966}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36925}, {"moveId": "EARTHQUAKE", "uses": 23060}, {"moveId": "OUTRAGE", "uses": 16506}]}, "moveset": ["SNARL", "CRUNCH", "EARTHQUAKE"], "score": 75.9}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 757, "matchups": [{"opponent": "giratina_origin", "rating": 798}, {"opponent": "metagross", "rating": 764}, {"opponent": "excadrill", "rating": 653}, {"opponent": "dialga", "rating": 627}, {"opponent": "garcho<PERSON>", "rating": 504}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "dragonite", "rating": 377}, {"opponent": "lugia", "rating": 383}, {"opponent": "gyarados", "rating": 443}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45278}, {"moveId": "TACKLE", "uses": 23410}, {"moveId": "LOW_KICK", "uses": 7730}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34072}, {"moveId": "STOMP", "uses": 22040}, {"moveId": "PAYBACK", "uses": 18048}, {"moveId": "DRAIN_PUNCH", "uses": 2419}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "PAYBACK"], "score": 75.6}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 800, "matchups": [{"opponent": "gyarados", "rating": 901}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 856}, {"opponent": "lugia", "rating": 773}, {"opponent": "mewtwo", "rating": 681}, {"opponent": "metagross", "rating": 589}], "counters": [{"opponent": "garcho<PERSON>", "rating": 187}, {"opponent": "giratina_origin", "rating": 207}, {"opponent": "dialga", "rating": 282}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "dragonite", "rating": 377}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47593}, {"moveId": "CHARGE_BEAM", "uses": 28907}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37217}, {"moveId": "MIRROR_SHOT", "uses": 16867}, {"moveId": "FLASH_CANNON", "uses": 8819}, {"moveId": "RETURN", "uses": 7888}, {"moveId": "ZAP_CANNON", "uses": 5722}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 75.5}, {"speciesId": "tapu_lele", "speciesName": "<PERSON><PERSON>", "rating": 707, "matchups": [{"opponent": "dialga", "rating": 786}, {"opponent": "dragonite", "rating": 757}, {"opponent": "gyarados", "rating": 681}, {"opponent": "garcho<PERSON>", "rating": 576}, {"opponent": "zacian_hero", "rating": 535}], "counters": [{"opponent": "mewtwo", "rating": 174}, {"opponent": "giratina_origin", "rating": 205}, {"opponent": "grou<PERSON>", "rating": 247}, {"opponent": "lugia", "rating": 342}, {"opponent": "zekrom", "rating": 489}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56923}, {"moveId": "ASTONISH", "uses": 19577}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28466}, {"moveId": "PSYSHOCK", "uses": 24971}, {"moveId": "FOCUS_BLAST", "uses": 14679}, {"moveId": "FUTURE_SIGHT", "uses": 8382}]}, "moveset": ["CONFUSION", "MOONBLAST", "PSYSHOCK"], "score": 75.5}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 768, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 735}, {"opponent": "dialga", "rating": 621}, {"opponent": "zacian_hero", "rating": 582}, {"opponent": "mewtwo", "rating": 542}], "counters": [{"opponent": "giratina_origin", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "gyarados", "rating": 288}, {"opponent": "lugia", "rating": 359}, {"opponent": "excadrill", "rating": 448}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46921}, {"moveId": "FIRE_FANG", "uses": 29579}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25491}, {"moveId": "OVERHEAT", "uses": 19691}, {"moveId": "IRON_HEAD", "uses": 14862}, {"moveId": "FLAMETHROWER", "uses": 10558}, {"moveId": "FIRE_BLAST", "uses": 5915}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 75.3}, {"speciesId": "aurorus", "speciesName": "Au<PERSON><PERSON>", "rating": 693, "matchups": [{"opponent": "garcho<PERSON>", "rating": 844}, {"opponent": "gyarados", "rating": 802}, {"opponent": "dragonite", "rating": 795}, {"opponent": "lugia", "rating": 651}, {"opponent": "giratina_origin", "rating": 534}], "counters": [{"opponent": "zacian_hero", "rating": 234}, {"opponent": "metagross", "rating": 250}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "excadrill", "rating": 462}, {"opponent": "dialga", "rating": 494}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37823}, {"moveId": "ROCK_THROW", "uses": 20912}, {"moveId": "FROST_BREATH", "uses": 17784}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 38366}, {"moveId": "ANCIENT_POWER", "uses": 16829}, {"moveId": "THUNDERBOLT", "uses": 10716}, {"moveId": "BLIZZARD", "uses": 5853}, {"moveId": "HYPER_BEAM", "uses": 4817}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 74.5}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 779, "matchups": [{"opponent": "metagross", "rating": 822}, {"opponent": "excadrill", "rating": 813}, {"opponent": "yveltal", "rating": 671, "opRating": 328}, {"opponent": "dialga", "rating": 630}, {"opponent": "snorlax", "rating": 566, "opRating": 433}], "counters": [{"opponent": "giratina_origin", "rating": 233}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "gyarados", "rating": 335}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46254}, {"moveId": "FIRE_SPIN", "uses": 30246}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 21228}, {"moveId": "BRAVE_BIRD", "uses": 16005}, {"moveId": "BLAZE_KICK", "uses": 13114}, {"moveId": "STONE_EDGE", "uses": 11839}, {"moveId": "FOCUS_BLAST", "uses": 9829}, {"moveId": "OVERHEAT", "uses": 4523}]}, "moveset": ["COUNTER", "BLAZE_KICK", "BLAST_BURN"], "score": 74.4}, {"speciesId": "milotic", "speciesName": "Milo<PERSON>", "rating": 723, "matchups": [{"opponent": "swampert", "rating": 641}, {"opponent": "gyarados", "rating": 613}, {"opponent": "garcho<PERSON>", "rating": 610}, {"opponent": "giratina_origin", "rating": 582}, {"opponent": "grou<PERSON>", "rating": 579}], "counters": [{"opponent": "metagross", "rating": 363}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "zacian_hero", "rating": 450}, {"opponent": "dragonite", "rating": 457}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 40697}, {"moveId": "WATERFALL", "uses": 35803}], "chargedMoves": [{"moveId": "SURF", "uses": 46232}, {"moveId": "BLIZZARD", "uses": 20007}, {"moveId": "HYPER_BEAM", "uses": 10148}]}, "moveset": ["DRAGON_TAIL", "SURF", "BLIZZARD"], "score": 74.4}, {"speciesId": "suicune", "speciesName": "Suicune", "rating": 716, "matchups": [{"opponent": "excadrill", "rating": 738}, {"opponent": "metagross", "rating": 644}, {"opponent": "garcho<PERSON>", "rating": 634}, {"opponent": "giratina_origin", "rating": 587}, {"opponent": "dragonite", "rating": 549}], "counters": [{"opponent": "dialga", "rating": 377}, {"opponent": "lugia", "rating": 388}, {"opponent": "mewtwo", "rating": 421}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "gyarados", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7172}, {"moveId": "ICE_FANG", "uses": 6328}, {"moveId": "EXTRASENSORY", "uses": 4673}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4567}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4488}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4129}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4011}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3946}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3862}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3685}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3646}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3571}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3490}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3448}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3153}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3139}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3137}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3016}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2908}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26454}, {"moveId": "HYDRO_PUMP", "uses": 15222}, {"moveId": "RETURN", "uses": 12644}, {"moveId": "BUBBLE_BEAM", "uses": 11307}, {"moveId": "WATER_PULSE", "uses": 10905}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 74.4}, {"speciesId": "mr_rime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 650, "matchups": [{"opponent": "garcho<PERSON>", "rating": 886}, {"opponent": "dragonite", "rating": 720}, {"opponent": "zacian_hero", "rating": 595}, {"opponent": "mewtwo", "rating": 555}, {"opponent": "dialga", "rating": 540}], "counters": [{"opponent": "metagross", "rating": 308}, {"opponent": "gyarados", "rating": 324}, {"opponent": "giratina_origin", "rating": 378}, {"opponent": "excadrill", "rating": 453}, {"opponent": "grou<PERSON>", "rating": 461}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38575}, {"moveId": "CONFUSION", "uses": 31217}, {"moveId": "ZEN_HEADBUTT", "uses": 6704}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 30904}, {"moveId": "ICE_PUNCH", "uses": 26748}, {"moveId": "PSYCHIC", "uses": 15575}, {"moveId": "PSYBEAM", "uses": 3094}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ICE_PUNCH"], "score": 74.3}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 789, "matchups": [{"opponent": "zacian_hero", "rating": 802}, {"opponent": "swampert", "rating": 746}, {"opponent": "gyarados", "rating": 700}, {"opponent": "grou<PERSON>", "rating": 566}, {"opponent": "excadrill", "rating": 542}], "counters": [{"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "lugia", "rating": 319}, {"opponent": "dialga", "rating": 331}, {"opponent": "metagross", "rating": 470}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 32620}, {"moveId": "BULLET_SEED", "uses": 28514}, {"moveId": "RAZOR_LEAF", "uses": 15379}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 25178}, {"moveId": "GRASS_KNOT", "uses": 16438}, {"moveId": "SLUDGE_BOMB", "uses": 14399}, {"moveId": "LEAF_STORM", "uses": 9531}, {"moveId": "DAZZLING_GLEAM", "uses": 7482}, {"moveId": "SOLAR_BEAM", "uses": 3420}]}, "moveset": ["POISON_JAB", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 74.3}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 698, "matchups": [{"opponent": "giratina_origin", "rating": 831}, {"opponent": "mewtwo", "rating": 776}, {"opponent": "excadrill", "rating": 715}, {"opponent": "dialga", "rating": 642}, {"opponent": "metagross", "rating": 542}], "counters": [{"opponent": "zacian_hero", "rating": 164}, {"opponent": "dragonite", "rating": 194}, {"opponent": "lugia", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45075}, {"moveId": "LICK", "uses": 31425}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 38011}, {"moveId": "CROSS_CHOP", "uses": 22265}, {"moveId": "HYPER_BEAM", "uses": 8625}, {"moveId": "GUNK_SHOT", "uses": 6642}, {"moveId": "OBSTRUCT", "uses": 914}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "OBSTRUCT"], "score": 74.2}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 713, "matchups": [{"opponent": "mewtwo", "rating": 863}, {"opponent": "metagross", "rating": 818}, {"opponent": "zacian_hero", "rating": 782}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 711}, {"opponent": "dialga", "rating": 568}], "counters": [{"opponent": "giratina_origin", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "gyarados", "rating": 314}, {"opponent": "lugia", "rating": 340}, {"opponent": "excadrill", "rating": 355}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31258}, {"moveId": "SHADOW_CLAW", "uses": 29096}, {"moveId": "EMBER", "uses": 16085}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 48148}, {"moveId": "SOLAR_BEAM", "uses": 12026}, {"moveId": "OVERHEAT", "uses": 10321}, {"moveId": "FIRE_BLAST", "uses": 6014}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 74.2}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 743, "matchups": [{"opponent": "swampert", "rating": 716}, {"opponent": "garcho<PERSON>", "rating": 699}, {"opponent": "dragonite", "rating": 608}, {"opponent": "grou<PERSON>", "rating": 567}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 541, "opRating": 458}], "counters": [{"opponent": "mewtwo", "rating": 385}, {"opponent": "gyarados", "rating": 391}, {"opponent": "dialga", "rating": 394}, {"opponent": "giratina_origin", "rating": 448}, {"opponent": "zacian_hero", "rating": 488}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 40768}, {"moveId": "CONFUSION", "uses": 35732}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21703}, {"moveId": "MOONBLAST", "uses": 21479}, {"moveId": "FUTURE_SIGHT", "uses": 18979}, {"moveId": "AURORA_BEAM", "uses": 14320}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 74}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 760, "matchups": [{"opponent": "swampert", "rating": 820}, {"opponent": "excadrill", "rating": 716}, {"opponent": "gyarados", "rating": 534}, {"opponent": "garcho<PERSON>", "rating": 522}, {"opponent": "zacian_hero", "rating": 504}], "counters": [{"opponent": "metagross", "rating": 267}, {"opponent": "dialga", "rating": 285}, {"opponent": "dragonite", "rating": 327}, {"opponent": "lugia", "rating": 373}, {"opponent": "mewtwo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44475}, {"moveId": "INFESTATION", "uses": 32025}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 24867}, {"moveId": "ROCK_SLIDE", "uses": 24486}, {"moveId": "SLUDGE_BOMB", "uses": 13073}, {"moveId": "ANCIENT_POWER", "uses": 8878}, {"moveId": "SOLAR_BEAM", "uses": 5344}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 73.9}, {"speciesId": "vaporeon", "speciesName": "Vaporeon", "rating": 733, "matchups": [{"opponent": "excadrill", "rating": 916}, {"opponent": "metagross", "rating": 669}, {"opponent": "zacian_hero", "rating": 624}, {"opponent": "garcho<PERSON>", "rating": 575}, {"opponent": "grou<PERSON>", "rating": 565}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "dialga", "rating": 312}, {"opponent": "lugia", "rating": 326}, {"opponent": "gyarados", "rating": 376}, {"opponent": "mewtwo", "rating": 466}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 76500}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38171}, {"moveId": "LAST_RESORT", "uses": 14534}, {"moveId": "SCALD", "uses": 13516}, {"moveId": "HYDRO_PUMP", "uses": 6023}, {"moveId": "WATER_PULSE", "uses": 4344}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "LAST_RESORT"], "score": 73.9}, {"speciesId": "gardevoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 666, "matchups": [{"opponent": "dragonite", "rating": 866}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "zekrom", "rating": 801}, {"opponent": "dialga", "rating": 675}, {"opponent": "gyarados", "rating": 603}], "counters": [{"opponent": "excadrill", "rating": 358}, {"opponent": "lugia", "rating": 402}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "zacian_hero", "rating": 465}, {"opponent": "giratina_origin", "rating": 480}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31058}, {"moveId": "CHARM", "uses": 27006}, {"moveId": "CHARGE_BEAM", "uses": 18474}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26130}, {"moveId": "SYNCHRONOISE", "uses": 23219}, {"moveId": "DAZZLING_GLEAM", "uses": 16483}, {"moveId": "PSYCHIC", "uses": 10569}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 73.8}, {"speciesId": "primarina", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 680, "matchups": [{"opponent": "dragonite", "rating": 869}, {"opponent": "garcho<PERSON>", "rating": 845}, {"opponent": "gyarados", "rating": 619}, {"opponent": "swampert", "rating": 558}, {"opponent": "dialga", "rating": 537}], "counters": [{"opponent": "excadrill", "rating": 327}, {"opponent": "lugia", "rating": 335}, {"opponent": "zacian_hero", "rating": 416}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "giratina_origin", "rating": 450}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 38527}, {"moveId": "WATERFALL", "uses": 37973}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 37947}, {"moveId": "HYDRO_PUMP", "uses": 19676}, {"moveId": "PSYCHIC", "uses": 18770}]}, "moveset": ["CHARM", "MOONBLAST", "HYDRO_PUMP"], "score": 73.8}, {"speciesId": "regice", "speciesName": "Regice", "rating": 744, "matchups": [{"opponent": "gyarados", "rating": 636}, {"opponent": "zacian_hero", "rating": 595}, {"opponent": "swampert", "rating": 566}, {"opponent": "excadrill", "rating": 520}, {"opponent": "lugia", "rating": 502}], "counters": [{"opponent": "mewtwo", "rating": 283}, {"opponent": "metagross", "rating": 380}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "garcho<PERSON>", "rating": 434}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42318}, {"moveId": "FROST_BREATH", "uses": 24637}, {"moveId": "ROCK_SMASH", "uses": 9554}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 25653}, {"moveId": "EARTHQUAKE", "uses": 19605}, {"moveId": "THUNDER", "uses": 16944}, {"moveId": "FOCUS_BLAST", "uses": 14223}]}, "moveset": ["LOCK_ON", "THUNDER", "EARTHQUAKE"], "score": 73.8}, {"speciesId": "sylveon", "speciesName": "Sylveon", "rating": 709, "matchups": [{"opponent": "zekrom", "rating": 842}, {"opponent": "dragonite", "rating": 822}, {"opponent": "garcho<PERSON>", "rating": 644}, {"opponent": "zacian_hero", "rating": 579}, {"opponent": "gyarados", "rating": 569}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "mewtwo", "rating": 367}, {"opponent": "grou<PERSON>", "rating": 394}, {"opponent": "swampert", "rating": 450}, {"opponent": "dialga", "rating": 461}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39645}, {"moveId": "CHARM", "uses": 36855}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 27113}, {"moveId": "PSYSHOCK", "uses": 18294}, {"moveId": "LAST_RESORT", "uses": 13214}, {"moveId": "DRAINING_KISS", "uses": 11005}, {"moveId": "DAZZLING_GLEAM", "uses": 6884}]}, "moveset": ["CHARM", "MOONBLAST", "PSYSHOCK"], "score": 73.8}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 703, "matchups": [{"opponent": "mewtwo", "rating": 930}, {"opponent": "metagross", "rating": 738}, {"opponent": "gyarados", "rating": 614}, {"opponent": "swampert", "rating": 577}, {"opponent": "dragonite", "rating": 504}], "counters": [{"opponent": "dialga", "rating": 334}, {"opponent": "excadrill", "rating": 462}, {"opponent": "giratina_origin", "rating": 468}, {"opponent": "garcho<PERSON>", "rating": 469}, {"opponent": "zacian_hero", "rating": 471}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 58783}, {"moveId": "PECK", "uses": 17717}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 26975}, {"moveId": "DARK_PULSE", "uses": 20532}, {"moveId": "SKY_ATTACK", "uses": 20112}, {"moveId": "PSYCHIC", "uses": 8753}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 73.7}, {"speciesId": "beartic", "speciesName": "Bear<PERSON>", "rating": 699, "matchups": [{"opponent": "garcho<PERSON>", "rating": 925}, {"opponent": "dragonite", "rating": 765}, {"opponent": "giratina_origin", "rating": 649}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 626, "opRating": 373}, {"opponent": "excadrill", "rating": 546}], "counters": [{"opponent": "mewtwo", "rating": 302}, {"opponent": "metagross", "rating": 372}, {"opponent": "lugia", "rating": 445}, {"opponent": "dialga", "rating": 467}, {"opponent": "gyarados", "rating": 469}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52492}, {"moveId": "CHARM", "uses": 24008}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 36204}, {"moveId": "SURF", "uses": 27989}, {"moveId": "PLAY_ROUGH", "uses": 12288}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "SURF"], "score": 73.5}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 758, "matchups": [{"opponent": "zacian_hero", "rating": 904}, {"opponent": "metagross", "rating": 820}, {"opponent": "excadrill", "rating": 785}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 714}, {"opponent": "dialga", "rating": 556}], "counters": [{"opponent": "mewtwo", "rating": 179}, {"opponent": "giratina_origin", "rating": 203}, {"opponent": "grou<PERSON>", "rating": 369}, {"opponent": "garcho<PERSON>", "rating": 384}, {"opponent": "lugia", "rating": 395}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 35175}, {"moveId": "HEX", "uses": 23678}, {"moveId": "FIRE_SPIN", "uses": 17623}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26515}, {"moveId": "FLAME_CHARGE", "uses": 20818}, {"moveId": "OVERHEAT", "uses": 16093}, {"moveId": "ENERGY_BALL", "uses": 13059}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 73.3}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 764, "matchups": [{"opponent": "excadrill", "rating": 847}, {"opponent": "metagross", "rating": 759}, {"opponent": "gyarados", "rating": 707}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 560, "opRating": 439}, {"opponent": "dialga", "rating": 521}], "counters": [{"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "lugia", "rating": 290}, {"opponent": "dragonite", "rating": 329}, {"opponent": "mewtwo", "rating": 341}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 40031}, {"moveId": "FIRE_SPIN", "uses": 36469}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 25615}, {"moveId": "BRICK_BREAK", "uses": 17296}, {"moveId": "THUNDERBOLT", "uses": 15009}, {"moveId": "PSYCHIC", "uses": 13388}, {"moveId": "FIRE_BLAST", "uses": 5124}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 73.3}, {"speciesId": "sci<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 743, "matchups": [{"opponent": "zacian_hero", "rating": 856}, {"opponent": "metagross", "rating": 789}, {"opponent": "sylveon", "rating": 754, "opRating": 245}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 732, "opRating": 267}, {"opponent": "excadrill", "rating": 512}], "counters": [{"opponent": "garcho<PERSON>", "rating": 387}, {"opponent": "dialga", "rating": 415}, {"opponent": "gyarados", "rating": 435}, {"opponent": "mewtwo", "rating": 440}, {"opponent": "giratina_origin", "rating": 480}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38468}, {"moveId": "BULLET_PUNCH", "uses": 38032}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 34177}, {"moveId": "X_SCISSOR", "uses": 25195}, {"moveId": "IRON_HEAD", "uses": 17052}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "IRON_HEAD"], "score": 73.2}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 736, "matchups": [{"opponent": "swampert", "rating": 843}, {"opponent": "excadrill", "rating": 756}, {"opponent": "gyarados", "rating": 631}, {"opponent": "garcho<PERSON>", "rating": 582}, {"opponent": "zacian_hero", "rating": 562}], "counters": [{"opponent": "metagross", "rating": 244}, {"opponent": "dialga", "rating": 252}, {"opponent": "giratina_origin", "rating": 318}, {"opponent": "lugia", "rating": 340}, {"opponent": "mewtwo", "rating": 406}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44340}, {"moveId": "INFESTATION", "uses": 32160}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22319}, {"moveId": "ROCK_SLIDE", "uses": 21916}, {"moveId": "SLUDGE_BOMB", "uses": 11545}, {"moveId": "RETURN", "uses": 8112}, {"moveId": "ANCIENT_POWER", "uses": 7846}, {"moveId": "SOLAR_BEAM", "uses": 4753}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 73.2}, {"speciesId": "sandslash_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 666, "matchups": [{"opponent": "garcho<PERSON>", "rating": 832}, {"opponent": "dragonite", "rating": 814}, {"opponent": "lugia", "rating": 628}, {"opponent": "giratina_origin", "rating": 591}, {"opponent": "mewtwo", "rating": 518}], "counters": [{"opponent": "zacian_hero", "rating": 303}, {"opponent": "excadrill", "rating": 309}, {"opponent": "grou<PERSON>", "rating": 312}, {"opponent": "metagross", "rating": 313}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 33177}, {"moveId": "SHADOW_CLAW", "uses": 30459}, {"moveId": "METAL_CLAW", "uses": 12870}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 34255}, {"moveId": "BLIZZARD", "uses": 17748}, {"moveId": "BULLDOZE", "uses": 13217}, {"moveId": "GYRO_BALL", "uses": 11202}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 73.1}, {"speciesId": "golisopod", "speciesName": "Golisopod", "rating": 716, "matchups": [{"opponent": "swampert", "rating": 679}, {"opponent": "metagross", "rating": 631}, {"opponent": "kyogre", "rating": 603, "opRating": 396}, {"opponent": "mewtwo", "rating": 582}, {"opponent": "garcho<PERSON>", "rating": 573}], "counters": [{"opponent": "dragonite", "rating": 401}, {"opponent": "dialga", "rating": 407}, {"opponent": "zacian_hero", "rating": 413}, {"opponent": "giratina_origin", "rating": 424}, {"opponent": "gyarados", "rating": 451}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 26036}, {"moveId": "FURY_CUTTER", "uses": 21254}, {"moveId": "WATERFALL", "uses": 18906}, {"moveId": "METAL_CLAW", "uses": 10302}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 38593}, {"moveId": "AERIAL_ACE", "uses": 19142}, {"moveId": "AQUA_JET", "uses": 18770}]}, "moveset": ["SHADOW_CLAW", "X_SCISSOR", "AERIAL_ACE"], "score": 72.7}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 695, "matchups": [{"opponent": "giratina_origin", "rating": 868}, {"opponent": "excadrill", "rating": 868}, {"opponent": "dialga", "rating": 629}, {"opponent": "metagross", "rating": 620}, {"opponent": "zekrom", "rating": 545, "opRating": 454}], "counters": [{"opponent": "mewtwo", "rating": 263}, {"opponent": "gyarados", "rating": 345}, {"opponent": "lugia", "rating": 347}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "garcho<PERSON>", "rating": 490}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34821}, {"moveId": "COUNTER", "uses": 30647}, {"moveId": "METAL_CLAW", "uses": 11024}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 43704}, {"moveId": "PLAY_ROUGH", "uses": 17088}, {"moveId": "HYPER_BEAM", "uses": 15434}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 72.6}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 749, "matchups": [{"opponent": "mewtwo", "rating": 836}, {"opponent": "giratina_origin", "rating": 723}, {"opponent": "swampert", "rating": 645}, {"opponent": "gyarados", "rating": 607}, {"opponent": "lugia", "rating": 583}], "counters": [{"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "dialga", "rating": 331}, {"opponent": "dragonite", "rating": 332}, {"opponent": "metagross", "rating": 363}, {"opponent": "grou<PERSON>", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39098}, {"moveId": "WING_ATTACK", "uses": 37402}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36310}, {"moveId": "PAYBACK", "uses": 23939}, {"moveId": "ANCIENT_POWER", "uses": 16406}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 72.4}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 745, "matchups": [{"opponent": "swampert", "rating": 909}, {"opponent": "swampert_shadow", "rating": 909, "opRating": 90}, {"opponent": "dialga", "rating": 639}, {"opponent": "excadrill", "rating": 603}, {"opponent": "zekrom", "rating": 542, "opRating": 457}], "counters": [{"opponent": "mewtwo", "rating": 312}, {"opponent": "metagross", "rating": 360}, {"opponent": "zacian_hero", "rating": 398}, {"opponent": "gyarados", "rating": 399}, {"opponent": "garcho<PERSON>", "rating": 450}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42891}, {"moveId": "CHARM", "uses": 23005}, {"moveId": "LOW_KICK", "uses": 10556}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29158}, {"moveId": "LEAF_BLADE", "uses": 23879}, {"moveId": "SYNCHRONOISE", "uses": 12178}, {"moveId": "RETURN", "uses": 5618}, {"moveId": "PSYCHIC", "uses": 5512}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 72.3}, {"speciesId": "lap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 695, "matchups": [{"opponent": "garcho<PERSON>", "rating": 875}, {"opponent": "excadrill", "rating": 591}, {"opponent": "swampert", "rating": 587}, {"opponent": "dragonite", "rating": 563}, {"opponent": "giratina_origin", "rating": 526}], "counters": [{"opponent": "zacian_hero", "rating": 300}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "dialga", "rating": 396}, {"opponent": "gyarados", "rating": 425}, {"opponent": "lugia", "rating": 428}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 31164}, {"moveId": "WATER_GUN", "uses": 25158}, {"moveId": "FROST_BREATH", "uses": 20259}], "chargedMoves": [{"moveId": "SURF", "uses": 27782}, {"moveId": "ICE_BEAM", "uses": 20569}, {"moveId": "SKULL_BASH", "uses": 8560}, {"moveId": "DRAGON_PULSE", "uses": 7894}, {"moveId": "BLIZZARD", "uses": 7117}, {"moveId": "HYDRO_PUMP", "uses": 4377}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 72.3}, {"speciesId": "samu<PERSON>t", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 736, "matchups": [{"opponent": "metagross", "rating": 817}, {"opponent": "zacian_hero", "rating": 780}, {"opponent": "excadrill", "rating": 631}, {"opponent": "swampert", "rating": 548}, {"opponent": "mewtwo", "rating": 510}], "counters": [{"opponent": "giratina_origin", "rating": 227}, {"opponent": "dialga", "rating": 304}, {"opponent": "gyarados", "rating": 314}, {"opponent": "lugia", "rating": 338}, {"opponent": "garcho<PERSON>", "rating": 453}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 43371}, {"moveId": "WATERFALL", "uses": 33129}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 33885}, {"moveId": "RAZOR_SHELL", "uses": 14301}, {"moveId": "MEGAHORN", "uses": 13757}, {"moveId": "BLIZZARD", "uses": 10946}, {"moveId": "HYDRO_PUMP", "uses": 3551}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "RAZOR_SHELL"], "score": 72.3}, {"speciesId": "mewtwo_armored", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Armored)", "rating": 762, "matchups": [{"opponent": "excadrill", "rating": 729}, {"opponent": "swampert", "rating": 585}, {"opponent": "zacian_hero", "rating": 565}, {"opponent": "gyarados", "rating": 528}, {"opponent": "grou<PERSON>", "rating": 502}], "counters": [{"opponent": "metagross", "rating": 270}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "dialga", "rating": 377}, {"opponent": "garcho<PERSON>", "rating": 441}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66224}, {"moveId": "IRON_TAIL", "uses": 10276}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 25579}, {"moveId": "ROCK_SLIDE", "uses": 18840}, {"moveId": "DYNAMIC_PUNCH", "uses": 15517}, {"moveId": "EARTHQUAKE", "uses": 11433}, {"moveId": "FUTURE_SIGHT", "uses": 5243}]}, "moveset": ["CONFUSION", "PSYSTRIKE", "DYNAMIC_PUNCH"], "score": 72.1}, {"speciesId": "poliwrath_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 680, "matchups": [{"opponent": "excadrill", "rating": 922}, {"opponent": "garcho<PERSON>", "rating": 868}, {"opponent": "dragonite", "rating": 610}, {"opponent": "metagross", "rating": 580}, {"opponent": "dialga", "rating": 577}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "lugia", "rating": 235}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "giratina_origin", "rating": 334}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36034}, {"moveId": "BUBBLE", "uses": 29935}, {"moveId": "ROCK_SMASH", "uses": 10534}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 21671}, {"moveId": "DYNAMIC_PUNCH", "uses": 20057}, {"moveId": "SCALD", "uses": 19225}, {"moveId": "POWER_UP_PUNCH", "uses": 6753}, {"moveId": "SUBMISSION", "uses": 4552}, {"moveId": "HYDRO_PUMP", "uses": 4412}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 72.1}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 720, "matchups": [{"opponent": "excadrill", "rating": 862}, {"opponent": "electivire_shadow", "rating": 829, "opRating": 170}, {"opponent": "metagross", "rating": 711}, {"opponent": "mewtwo", "rating": 592}, {"opponent": "dialga", "rating": 588}], "counters": [{"opponent": "gyarados", "rating": 239}, {"opponent": "lugia", "rating": 292}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "garcho<PERSON>", "rating": 457}, {"opponent": "swampert", "rating": 465}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29133}, {"moveId": "PSYCHO_CUT", "uses": 26122}, {"moveId": "CONFUSION", "uses": 21251}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16628}, {"moveId": "FIRE_PUNCH", "uses": 15303}, {"moveId": "PSYCHIC", "uses": 13971}, {"moveId": "FOCUS_BLAST", "uses": 10001}, {"moveId": "DAZZLING_GLEAM", "uses": 7449}, {"moveId": "RETURN", "uses": 7103}, {"moveId": "FUTURE_SIGHT", "uses": 6031}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 72}, {"speciesId": "tyranitar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 699, "matchups": [{"opponent": "mewtwo", "rating": 748}, {"opponent": "lugia", "rating": 748}, {"opponent": "giratina_origin", "rating": 699}, {"opponent": "metagross", "rating": 579}, {"opponent": "gyarados", "rating": 547}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 415}, {"opponent": "dialga", "rating": 483}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43725}, {"moveId": "BITE", "uses": 25800}, {"moveId": "IRON_TAIL", "uses": 6932}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37793}, {"moveId": "STONE_EDGE", "uses": 29088}, {"moveId": "FIRE_BLAST", "uses": 9627}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 71.8}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 771, "matchups": [{"opponent": "mewtwo", "rating": 702}, {"opponent": "mewtwo_shadow", "rating": 660, "opRating": 339}, {"opponent": "sylveon", "rating": 620, "opRating": 379}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "zacian_hero", "rating": 561}], "counters": [{"opponent": "garcho<PERSON>", "rating": 291}, {"opponent": "dialga", "rating": 328}, {"opponent": "metagross", "rating": 421}, {"opponent": "gyarados", "rating": 471}, {"opponent": "giratina_origin", "rating": 494}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 39141}, {"moveId": "POISON_STING", "uses": 37359}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17992}, {"moveId": "DARK_PULSE", "uses": 17807}, {"moveId": "ICE_BEAM", "uses": 13690}, {"moveId": "SHADOW_BALL", "uses": 13483}, {"moveId": "SLUDGE_BOMB", "uses": 13432}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 71.7}, {"speciesId": "articuno_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 806, "matchups": [{"opponent": "swampert", "rating": 768}, {"opponent": "gyarados", "rating": 602}, {"opponent": "zacian_hero", "rating": 553}, {"opponent": "lugia", "rating": 526}, {"opponent": "dragonite", "rating": 521}], "counters": [{"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "metagross", "rating": 247}, {"opponent": "dialga", "rating": 277}, {"opponent": "giratina_origin", "rating": 356}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 43734}, {"moveId": "CONFUSION", "uses": 32766}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 41522}, {"moveId": "ANCIENT_POWER", "uses": 18982}, {"moveId": "FUTURE_SIGHT", "uses": 16003}]}, "moveset": ["PSYCHO_CUT", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 71.5}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 769, "matchups": [{"opponent": "melmetal", "rating": 901, "opRating": 98}, {"opponent": "metagross", "rating": 805}, {"opponent": "excadrill", "rating": 796}, {"opponent": "dialga", "rating": 729}, {"opponent": "snorlax", "rating": 636, "opRating": 363}], "counters": [{"opponent": "mewtwo", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "lugia", "rating": 280}, {"opponent": "giratina_origin", "rating": 364}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45219}, {"moveId": "BULLET_PUNCH", "uses": 31281}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34732}, {"moveId": "SHADOW_BALL", "uses": 18768}, {"moveId": "FLASH_CANNON", "uses": 8956}, {"moveId": "AURA_SPHERE", "uses": 7797}, {"moveId": "POWER_UP_PUNCH", "uses": 6114}]}, "moveset": ["COUNTER", "POWER_UP_PUNCH", "SHADOW_BALL"], "score": 71.5}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 713, "matchups": [{"opponent": "swampert", "rating": 845}, {"opponent": "excadrill", "rating": 791}, {"opponent": "zacian_hero", "rating": 736}, {"opponent": "gyarados", "rating": 718}, {"opponent": "garcho<PERSON>", "rating": 664}], "counters": [{"opponent": "giratina_origin", "rating": 153}, {"opponent": "metagross", "rating": 168}, {"opponent": "dialga", "rating": 187}, {"opponent": "lugia", "rating": 235}, {"opponent": "dragonite", "rating": 303}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 29871}, {"moveId": "CONFUSION", "uses": 29617}, {"moveId": "CHARGE_BEAM", "uses": 16969}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 22118}, {"moveId": "PSYCHIC", "uses": 20069}, {"moveId": "LEAF_STORM", "uses": 15090}, {"moveId": "DAZZLING_GLEAM", "uses": 11043}, {"moveId": "HYPER_BEAM", "uses": 8082}]}, "moveset": ["CONFUSION", "SEED_BOMB", "LEAF_STORM"], "score": 71.2}, {"speciesId": "pinsir", "speciesName": "Pinsir", "rating": 735, "matchups": [{"opponent": "excadrill", "rating": 916}, {"opponent": "swampert", "rating": 677}, {"opponent": "metagross", "rating": 667}, {"opponent": "grou<PERSON>", "rating": 587}, {"opponent": "dialga", "rating": 577}], "counters": [{"opponent": "giratina_origin", "rating": 213}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "lugia", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "mewtwo", "rating": 401}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40216}, {"moveId": "BUG_BITE", "uses": 26429}, {"moveId": "ROCK_SMASH", "uses": 9849}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 22932}, {"moveId": "X_SCISSOR", "uses": 20037}, {"moveId": "SUPER_POWER", "uses": 17062}, {"moveId": "RETURN", "uses": 7259}, {"moveId": "VICE_GRIP", "uses": 6541}, {"moveId": "SUBMISSION", "uses": 2758}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 71.1}, {"speciesId": "tapu_koko", "speciesName": "<PERSON><PERSON>", "rating": 730, "matchups": [{"opponent": "gyarados", "rating": 843}, {"opponent": "zekrom", "rating": 812}, {"opponent": "lugia", "rating": 719}, {"opponent": "dragonite", "rating": 691}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 589}], "counters": [{"opponent": "giratina_origin", "rating": 213}, {"opponent": "dialga", "rating": 448}, {"opponent": "metagross", "rating": 450}, {"opponent": "mewtwo", "rating": 453}, {"opponent": "zacian_hero", "rating": 468}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43952}, {"moveId": "QUICK_ATTACK", "uses": 32548}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31128}, {"moveId": "THUNDERBOLT", "uses": 20730}, {"moveId": "DAZZLING_GLEAM", "uses": 15532}, {"moveId": "THUNDER", "uses": 9180}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "DAZZLING_GLEAM"], "score": 71.1}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 659, "matchups": [{"opponent": "mewtwo", "rating": 859}, {"opponent": "metagross", "rating": 778}, {"opponent": "dialga", "rating": 674}, {"opponent": "giratina_origin", "rating": 600}, {"opponent": "lugia", "rating": 600}], "counters": [{"opponent": "zacian_hero", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dragonite", "rating": 303}, {"opponent": "swampert", "rating": 325}, {"opponent": "excadrill", "rating": 367}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 51411}, {"moveId": "METAL_CLAW", "uses": 25089}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27649}, {"moveId": "X_SCISSOR", "uses": 18496}, {"moveId": "IRON_HEAD", "uses": 17263}, {"moveId": "FOCUS_BLAST", "uses": 13126}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 71}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 726, "matchups": [{"opponent": "excadrill", "rating": 814}, {"opponent": "metagross", "rating": 798}, {"opponent": "gyarados", "rating": 765}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 628}, {"opponent": "dialga", "rating": 600}], "counters": [{"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "lugia", "rating": 254}, {"opponent": "dragonite", "rating": 289}, {"opponent": "zacian_hero", "rating": 439}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39780}, {"moveId": "FIRE_SPIN", "uses": 36720}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23059}, {"moveId": "BRICK_BREAK", "uses": 15631}, {"moveId": "THUNDERBOLT", "uses": 13332}, {"moveId": "PSYCHIC", "uses": 11749}, {"moveId": "RETURN", "uses": 8208}, {"moveId": "FIRE_BLAST", "uses": 4692}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 71}, {"speciesId": "tyranitar", "speciesName": "Tyranitar", "rating": 705, "matchups": [{"opponent": "ho_oh", "rating": 907, "opRating": 92}, {"opponent": "mewtwo", "rating": 778}, {"opponent": "giratina_origin", "rating": 716}, {"opponent": "lugia", "rating": 711}, {"opponent": "gyarados", "rating": 604}], "counters": [{"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "dialga", "rating": 388}, {"opponent": "excadrill", "rating": 400}, {"opponent": "dragonite", "rating": 406}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 41926}, {"moveId": "BITE", "uses": 27268}, {"moveId": "IRON_TAIL", "uses": 7292}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 33528}, {"moveId": "STONE_EDGE", "uses": 25488}, {"moveId": "RETURN", "uses": 9051}, {"moveId": "FIRE_BLAST", "uses": 8420}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 70.9}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 681, "matchups": [{"opponent": "excadrill", "rating": 948}, {"opponent": "swampert", "rating": 834}, {"opponent": "gyarados", "rating": 638}, {"opponent": "garcho<PERSON>", "rating": 614}, {"opponent": "dialga", "rating": 586}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "giratina_origin", "rating": 247}, {"opponent": "zekrom", "rating": 339}, {"opponent": "metagross", "rating": 340}, {"opponent": "zacian_hero", "rating": 341}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42895}, {"moveId": "SMACK_DOWN", "uses": 24829}, {"moveId": "LOW_KICK", "uses": 8711}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 40601}, {"moveId": "ENERGY_BALL", "uses": 19896}, {"moveId": "GYRO_BALL", "uses": 10192}, {"moveId": "SOLAR_BEAM", "uses": 5822}]}, "moveset": ["VINE_WHIP", "SUPER_POWER", "ENERGY_BALL"], "score": 70.6}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 693, "matchups": [{"opponent": "garcho<PERSON>", "rating": 912}, {"opponent": "swampert", "rating": 819}, {"opponent": "grou<PERSON>", "rating": 716}, {"opponent": "ho_oh", "rating": 657, "opRating": 342}, {"opponent": "excadrill", "rating": 636}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 463}, {"opponent": "giratina_origin", "rating": 464}, {"opponent": "gyarados", "rating": 474}, {"opponent": "metagross", "rating": 494}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42492}, {"moveId": "BULLET_SEED", "uses": 34008}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26942}, {"moveId": "DRAGON_PULSE", "uses": 19515}, {"moveId": "DRACO_METEOR", "uses": 17431}, {"moveId": "SOLAR_BEAM", "uses": 12482}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 69.9}, {"speciesId": "thundurus_therian", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Therian)", "rating": 721, "matchups": [{"opponent": "metagross", "rating": 776}, {"opponent": "lugia", "rating": 732}, {"opponent": "gyarados", "rating": 714}, {"opponent": "dragonite", "rating": 644}, {"opponent": "excadrill", "rating": 520}], "counters": [{"opponent": "mewtwo", "rating": 197}, {"opponent": "giratina_origin", "rating": 245}, {"opponent": "dialga", "rating": 339}, {"opponent": "grou<PERSON>", "rating": 464}, {"opponent": "zacian_hero", "rating": 497}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 57091}, {"moveId": "BITE", "uses": 19409}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27780}, {"moveId": "FOCUS_BLAST", "uses": 21844}, {"moveId": "SLUDGE_WAVE", "uses": 14847}, {"moveId": "THUNDER", "uses": 12036}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "FOCUS_BLAST"], "score": 69.9}, {"speciesId": "cloyster_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 646, "matchups": [{"opponent": "garcho<PERSON>", "rating": 866}, {"opponent": "dragonite", "rating": 649}, {"opponent": "excadrill", "rating": 559}, {"opponent": "giratina_origin", "rating": 507}, {"opponent": "lugia", "rating": 503}], "counters": [{"opponent": "metagross", "rating": 276}, {"opponent": "dialga", "rating": 355}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "gyarados", "rating": 404}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 48330}, {"moveId": "FROST_BREATH", "uses": 28170}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 40010}, {"moveId": "ICY_WIND", "uses": 13923}, {"moveId": "HYDRO_PUMP", "uses": 11231}, {"moveId": "BLIZZARD", "uses": 6187}, {"moveId": "AURORA_BEAM", "uses": 5050}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 69.4}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 672, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "dragonite", "rating": 634}, {"opponent": "grou<PERSON>", "rating": 585}, {"opponent": "swampert", "rating": 565}, {"opponent": "giratina_origin", "rating": 548}], "counters": [{"opponent": "dialga", "rating": 334}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "zacian_hero", "rating": 355}, {"opponent": "metagross", "rating": 363}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 30363}, {"moveId": "WATER_GUN", "uses": 26068}, {"moveId": "FROST_BREATH", "uses": 20049}], "chargedMoves": [{"moveId": "SURF", "uses": 25535}, {"moveId": "ICE_BEAM", "uses": 18999}, {"moveId": "SKULL_BASH", "uses": 7696}, {"moveId": "DRAGON_PULSE", "uses": 7154}, {"moveId": "BLIZZARD", "uses": 6645}, {"moveId": "RETURN", "uses": 6501}, {"moveId": "HYDRO_PUMP", "uses": 4131}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 69.4}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 700, "matchups": [{"opponent": "swampert", "rating": 936}, {"opponent": "excadrill", "rating": 731}, {"opponent": "garcho<PERSON>", "rating": 546}, {"opponent": "zacian_hero", "rating": 536}, {"opponent": "gyarados", "rating": 506}], "counters": [{"opponent": "giratina_origin", "rating": 213}, {"opponent": "metagross", "rating": 270}, {"opponent": "dragonite", "rating": 300}, {"opponent": "dialga", "rating": 304}, {"opponent": "mewtwo", "rating": 486}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30505}, {"moveId": "BULLET_SEED", "uses": 29440}, {"moveId": "RAZOR_LEAF", "uses": 16552}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 51120}, {"moveId": "LAST_RESORT", "uses": 13854}, {"moveId": "ENERGY_BALL", "uses": 7222}, {"moveId": "SOLAR_BEAM", "uses": 4256}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 69.4}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 711, "matchups": [{"opponent": "zarude", "rating": 926, "opRating": 73}, {"opponent": "latios", "rating": 777, "opRating": 222}, {"opponent": "mewtwo", "rating": 665}, {"opponent": "lugia", "rating": 547}, {"opponent": "metagross", "rating": 541}], "counters": [{"opponent": "garcho<PERSON>", "rating": 340}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "gyarados", "rating": 479}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38889}, {"moveId": "BULLET_PUNCH", "uses": 37611}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30568}, {"moveId": "X_SCISSOR", "uses": 22600}, {"moveId": "IRON_HEAD", "uses": 15053}, {"moveId": "RETURN", "uses": 8424}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 69.1}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 711, "matchups": [{"opponent": "mewtwo_shadow", "rating": 951, "opRating": 48}, {"opponent": "metagross", "rating": 842}, {"opponent": "mewtwo", "rating": 817}, {"opponent": "giratina_origin", "rating": 682}, {"opponent": "giratina_altered", "rating": 670, "opRating": 329}], "counters": [{"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "gyarados", "rating": 304}, {"opponent": "excadrill", "rating": 332}, {"opponent": "dialga", "rating": 423}, {"opponent": "lugia", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28437}, {"moveId": "DOUBLE_KICK", "uses": 27403}, {"moveId": "FIRE_FANG", "uses": 20654}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37377}, {"moveId": "FLAME_CHARGE", "uses": 31962}, {"moveId": "FIRE_BLAST", "uses": 7096}]}, "moveset": ["SNARL", "DARK_PULSE", "FIRE_BLAST"], "score": 68.9}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 653, "matchups": [{"opponent": "garcho<PERSON>", "rating": 814}, {"opponent": "dragonite", "rating": 804}, {"opponent": "zekrom", "rating": 768}, {"opponent": "lugia", "rating": 682}, {"opponent": "gyarados", "rating": 640}], "counters": [{"opponent": "zacian_hero", "rating": 242}, {"opponent": "excadrill", "rating": 262}, {"opponent": "mewtwo", "rating": 437}, {"opponent": "dialga", "rating": 440}, {"opponent": "giratina_origin", "rating": 478}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32472}, {"moveId": "SHADOW_CLAW", "uses": 30551}, {"moveId": "METAL_CLAW", "uses": 13517}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 30526}, {"moveId": "BLIZZARD", "uses": 15869}, {"moveId": "BULLDOZE", "uses": 11471}, {"moveId": "GYRO_BALL", "uses": 9801}, {"moveId": "RETURN", "uses": 8985}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 68.9}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 700, "matchups": [{"opponent": "nihilego", "rating": 850, "opRating": 149}, {"opponent": "raikou_shadow", "rating": 850, "opRating": 149}, {"opponent": "excadrill", "rating": 759}, {"opponent": "reshiram", "rating": 667, "opRating": 332}, {"opponent": "dialga", "rating": 582}], "counters": [{"opponent": "giratina_origin", "rating": 336}, {"opponent": "garcho<PERSON>", "rating": 403}, {"opponent": "mewtwo", "rating": 424}, {"opponent": "zacian_hero", "rating": 442}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38922}, {"moveId": "WING_ATTACK", "uses": 37578}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 27748}, {"moveId": "EARTHQUAKE", "uses": 17198}, {"moveId": "AERIAL_ACE", "uses": 15282}, {"moveId": "SAND_TOMB", "uses": 8365}, {"moveId": "RETURN", "uses": 7862}]}, "moveset": ["WING_ATTACK", "NIGHT_SLASH", "EARTHQUAKE"], "score": 68.5}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 720, "matchups": [{"opponent": "yveltal", "rating": 857, "opRating": 142}, {"opponent": "ho_oh", "rating": 813, "opRating": 186}, {"opponent": "zacian_hero", "rating": 752}, {"opponent": "gyarados", "rating": 720}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 616, "opRating": 383}], "counters": [{"opponent": "dialga", "rating": 274}, {"opponent": "mewtwo", "rating": 427}, {"opponent": "dragonite", "rating": 438}, {"opponent": "lugia", "rating": 440}, {"opponent": "metagross", "rating": 444}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11803}, {"moveId": "SNARL", "uses": 9579}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4407}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4177}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4093}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3767}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3646}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3616}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3530}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3524}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3286}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3276}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3243}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3018}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2993}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2908}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2874}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2683}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34302}, {"moveId": "CRUNCH", "uses": 19564}, {"moveId": "PSYCHIC_FANGS", "uses": 17336}, {"moveId": "HYPER_BEAM", "uses": 5296}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 68.3}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 658, "matchups": [{"opponent": "garcho<PERSON>", "rating": 924}, {"opponent": "swampert", "rating": 760}, {"opponent": "dragonite", "rating": 733}, {"opponent": "zekrom", "rating": 545, "opRating": 454}, {"opponent": "excadrill", "rating": 513}], "counters": [{"opponent": "mewtwo", "rating": 335}, {"opponent": "dialga", "rating": 372}, {"opponent": "gyarados", "rating": 378}, {"opponent": "lugia", "rating": 414}, {"opponent": "giratina_origin", "rating": 492}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 55058}, {"moveId": "RAZOR_LEAF", "uses": 21442}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 42674}, {"moveId": "ENERGY_BALL", "uses": 16051}, {"moveId": "OUTRAGE", "uses": 11234}, {"moveId": "BLIZZARD", "uses": 6489}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 68.2}, {"speciesId": "espeon", "speciesName": "Espeon", "rating": 722, "matchups": [{"opponent": "zacian_hero", "rating": 761}, {"opponent": "metagross", "rating": 667}, {"opponent": "excadrill", "rating": 536}, {"opponent": "zekrom", "rating": 526, "opRating": 473}, {"opponent": "swampert", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 220}, {"opponent": "mewtwo", "rating": 244}, {"opponent": "garcho<PERSON>", "rating": 387}, {"opponent": "gyarados", "rating": 461}, {"opponent": "dragonite", "rating": 462}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 67188}, {"moveId": "ZEN_HEADBUTT", "uses": 9312}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 24781}, {"moveId": "SHADOW_BALL", "uses": 19353}, {"moveId": "PSYCHIC", "uses": 13641}, {"moveId": "LAST_RESORT", "uses": 9978}, {"moveId": "FUTURE_SIGHT", "uses": 5998}, {"moveId": "PSYBEAM", "uses": 2738}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "SHADOW_BALL"], "score": 68}, {"speciesId": "<PERSON><PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 675, "matchups": [{"opponent": "swampert", "rating": 898}, {"opponent": "metagross", "rating": 693}, {"opponent": "dialga", "rating": 635}, {"opponent": "yveltal", "rating": 614, "opRating": 385}, {"opponent": "zekrom", "rating": 569, "opRating": 430}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 343}, {"opponent": "gyarados", "rating": 358}, {"opponent": "excadrill", "rating": 444}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 47107}, {"moveId": "BULLET_PUNCH", "uses": 29393}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32310}, {"moveId": "SUPER_POWER", "uses": 23842}, {"moveId": "HEAVY_SLAM", "uses": 11712}, {"moveId": "DYNAMIC_PUNCH", "uses": 8644}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "SUPER_POWER"], "score": 68}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 694, "matchups": [{"opponent": "gyarados", "rating": 782}, {"opponent": "metagross", "rating": 771}, {"opponent": "lugia", "rating": 658}, {"opponent": "sylveon", "rating": 591, "opRating": 408}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}], "counters": [{"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "giratina_origin", "rating": 338}, {"opponent": "mewtwo", "rating": 408}, {"opponent": "dialga", "rating": 459}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30662}, {"moveId": "FIRE_FANG", "uses": 20507}, {"moveId": "THUNDER_FANG", "uses": 14468}, {"moveId": "BITE", "uses": 10775}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22451}, {"moveId": "CRUNCH", "uses": 16069}, {"moveId": "PSYCHIC_FANGS", "uses": 14508}, {"moveId": "FLAMETHROWER", "uses": 13271}, {"moveId": "BULLDOZE", "uses": 6694}, {"moveId": "FIRE_BLAST", "uses": 3567}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 67.9}, {"speciesId": "golem", "speciesName": "Golem", "rating": 685, "matchups": [{"opponent": "ho_oh", "rating": 892, "opRating": 107}, {"opponent": "dialga", "rating": 677}, {"opponent": "metagross", "rating": 677}, {"opponent": "zekrom", "rating": 662, "opRating": 337}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 517, "opRating": 482}], "counters": [{"opponent": "zacian_hero", "rating": 317}, {"opponent": "garcho<PERSON>", "rating": 326}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "gyarados", "rating": 427}, {"opponent": "lugia", "rating": 464}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 33302}, {"moveId": "ROCK_THROW", "uses": 22653}, {"moveId": "MUD_SLAP", "uses": 20508}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 20166}, {"moveId": "STONE_EDGE", "uses": 18955}, {"moveId": "ROCK_BLAST", "uses": 17135}, {"moveId": "ANCIENT_POWER", "uses": 13003}, {"moveId": "RETURN", "uses": 7271}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 67.9}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 639, "matchups": [{"opponent": "garcho<PERSON>", "rating": 863}, {"opponent": "lugia", "rating": 684}, {"opponent": "dragonite", "rating": 666}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 641, "opRating": 358}, {"opponent": "grou<PERSON>", "rating": 514, "opRating": 485}], "counters": [{"opponent": "mewtwo", "rating": 338}, {"opponent": "dialga", "rating": 366}, {"opponent": "gyarados", "rating": 371}, {"opponent": "giratina_origin", "rating": 454}, {"opponent": "excadrill", "rating": 497}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44967}, {"moveId": "ICE_SHARD", "uses": 31533}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 45135}, {"moveId": "STONE_EDGE", "uses": 17119}, {"moveId": "BULLDOZE", "uses": 14143}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 67.9}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 652, "matchups": [{"opponent": "swampert", "rating": 814}, {"opponent": "metagross", "rating": 744}, {"opponent": "zacian_hero", "rating": 648}, {"opponent": "garcho<PERSON>", "rating": 573}, {"opponent": "excadrill", "rating": 564}], "counters": [{"opponent": "lugia", "rating": 290}, {"opponent": "gyarados", "rating": 317}, {"opponent": "dialga", "rating": 336}, {"opponent": "giratina_origin", "rating": 372}, {"opponent": "mewtwo", "rating": 494}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49536}, {"moveId": "RAZOR_LEAF", "uses": 26964}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26367}, {"moveId": "SEED_BOMB", "uses": 21689}, {"moveId": "FOUL_PLAY", "uses": 19767}, {"moveId": "FIRE_BLAST", "uses": 8622}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 67.7}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 720, "matchups": [{"opponent": "magnezone_shadow", "rating": 956, "opRating": 43}, {"opponent": "electivire_shadow", "rating": 956, "opRating": 43}, {"opponent": "excadrill", "rating": 895}, {"opponent": "metagross", "rating": 709}, {"opponent": "kyogre", "rating": 526, "opRating": 473}], "counters": [{"opponent": "zacian_hero", "rating": 329}, {"opponent": "mewtwo", "rating": 343}, {"opponent": "giratina_origin", "rating": 416}, {"opponent": "garcho<PERSON>", "rating": 455}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40513}, {"moveId": "DRAGON_TAIL", "uses": 35987}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30283}, {"moveId": "EARTH_POWER", "uses": 20161}, {"moveId": "STONE_EDGE", "uses": 17386}, {"moveId": "EARTHQUAKE", "uses": 8760}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 67.4}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 744, "matchups": [{"opponent": "zacian_hero", "rating": 803}, {"opponent": "sylveon", "rating": 760, "opRating": 239}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 739, "opRating": 260}, {"opponent": "zekrom", "rating": 674, "opRating": 325}, {"opponent": "yveltal", "rating": 505, "opRating": 494}], "counters": [{"opponent": "mewtwo", "rating": 307}, {"opponent": "gyarados", "rating": 365}, {"opponent": "metagross", "rating": 441}, {"opponent": "dialga", "rating": 442}, {"opponent": "dragonite", "rating": 465}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57619}, {"moveId": "BITE", "uses": 18881}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22456}, {"moveId": "EARTH_POWER", "uses": 20733}, {"moveId": "STONE_EDGE", "uses": 15250}, {"moveId": "SLUDGE_WAVE", "uses": 8966}, {"moveId": "EARTHQUAKE", "uses": 8907}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 67.3}, {"speciesId": "politoed_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 689, "matchups": [{"opponent": "garcho<PERSON>", "rating": 889}, {"opponent": "ho_oh", "rating": 836, "opRating": 163}, {"opponent": "dragonite", "rating": 658}, {"opponent": "zekrom", "rating": 658}, {"opponent": "excadrill", "rating": 607}], "counters": [{"opponent": "mewtwo", "rating": 203}, {"opponent": "lugia", "rating": 280}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "dialga", "rating": 415}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41533}, {"moveId": "BUBBLE", "uses": 34967}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 32483}, {"moveId": "BLIZZARD", "uses": 13360}, {"moveId": "SURF", "uses": 13213}, {"moveId": "EARTHQUAKE", "uses": 13077}, {"moveId": "HYDRO_PUMP", "uses": 4214}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "BLIZZARD"], "score": 67.2}, {"speciesId": "cryogonal", "speciesName": "Cryogonal", "rating": 629, "matchups": [{"opponent": "garcho<PERSON>", "rating": 892}, {"opponent": "zekrom", "rating": 671, "opRating": 328}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 619, "opRating": 380}, {"opponent": "dragonite", "rating": 581}, {"opponent": "giratina_origin", "rating": 514}], "counters": [{"opponent": "metagross", "rating": 375}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "dialga", "rating": 448}, {"opponent": "lugia", "rating": 459}, {"opponent": "gyarados", "rating": 481}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 44927}, {"moveId": "FROST_BREATH", "uses": 31573}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39091}, {"moveId": "AURORA_BEAM", "uses": 19594}, {"moveId": "SOLAR_BEAM", "uses": 10049}, {"moveId": "WATER_PULSE", "uses": 7783}]}, "moveset": ["ICE_SHARD", "NIGHT_SLASH", "AURORA_BEAM"], "score": 67.1}, {"speciesId": "salamence_shadow", "speciesName": "Salamence (Shadow)", "rating": 689, "matchups": [{"opponent": "garcho<PERSON>", "rating": 935}, {"opponent": "grou<PERSON>", "rating": 688}, {"opponent": "swampert", "rating": 621}, {"opponent": "yveltal", "rating": 518, "opRating": 481}, {"opponent": "dragonite", "rating": 507}], "counters": [{"opponent": "metagross", "rating": 293}, {"opponent": "mewtwo", "rating": 299}, {"opponent": "dialga", "rating": 307}, {"opponent": "gyarados", "rating": 373}, {"opponent": "giratina_origin", "rating": 464}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 44976}, {"moveId": "FIRE_FANG", "uses": 19533}, {"moveId": "BITE", "uses": 11976}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 35116}, {"moveId": "FIRE_BLAST", "uses": 15428}, {"moveId": "HYDRO_PUMP", "uses": 15292}, {"moveId": "DRACO_METEOR", "uses": 10544}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "FIRE_BLAST"], "score": 67.1}, {"speciesId": "gran<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 638, "matchups": [{"opponent": "excadrill", "rating": 868}, {"opponent": "magnezone_shadow", "rating": 825, "opRating": 174}, {"opponent": "dialga", "rating": 771}, {"opponent": "zekrom", "rating": 758, "opRating": 241}, {"opponent": "lugia", "rating": 516}], "counters": [{"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "metagross", "rating": 363}, {"opponent": "mewtwo", "rating": 411}, {"opponent": "dragonite", "rating": 462}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37145}, {"moveId": "CHARM", "uses": 26000}, {"moveId": "BITE", "uses": 13378}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34082}, {"moveId": "CRUNCH", "uses": 25748}, {"moveId": "PLAY_ROUGH", "uses": 16574}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 66.9}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 701, "matchups": [{"opponent": "mewtwo", "rating": 711}, {"opponent": "giratina_origin", "rating": 663}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "zacian_hero", "rating": 586}, {"opponent": "gyarados", "rating": 533}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "metagross", "rating": 331}, {"opponent": "dragonite", "rating": 356}, {"opponent": "lugia", "rating": 483}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29582}, {"moveId": "SNARL", "uses": 28412}, {"moveId": "BITE", "uses": 18499}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 43644}, {"moveId": "SLUDGE_WAVE", "uses": 19058}, {"moveId": "GUNK_SHOT", "uses": 7380}, {"moveId": "ACID_SPRAY", "uses": 6431}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "SLUDGE_WAVE"], "score": 66.9}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 625, "matchups": [{"opponent": "garcho<PERSON>", "rating": 885}, {"opponent": "dragonite", "rating": 713}, {"opponent": "zekrom", "rating": 549, "opRating": 450}, {"opponent": "excadrill", "rating": 547}, {"opponent": "dialga", "rating": 504}], "counters": [{"opponent": "zacian_hero", "rating": 291}, {"opponent": "mewtwo", "rating": 299}, {"opponent": "gyarados", "rating": 304}, {"opponent": "giratina_origin", "rating": 386}, {"opponent": "lugia", "rating": 459}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44589}, {"moveId": "ICE_SHARD", "uses": 31911}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41410}, {"moveId": "STONE_EDGE", "uses": 15296}, {"moveId": "BULLDOZE", "uses": 12861}, {"moveId": "RETURN", "uses": 6834}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 66.9}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 653, "matchups": [{"opponent": "excadrill", "rating": 761}, {"opponent": "garcho<PERSON>", "rating": 561}, {"opponent": "mewtwo", "rating": 537}, {"opponent": "zacian_hero", "rating": 537}, {"opponent": "gyarados", "rating": 505}], "counters": [{"opponent": "giratina_origin", "rating": 175}, {"opponent": "lugia", "rating": 202}, {"opponent": "metagross", "rating": 357}, {"opponent": "dialga", "rating": 372}, {"opponent": "grou<PERSON>", "rating": 448}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53195}, {"moveId": "RAZOR_LEAF", "uses": 23305}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38998}, {"moveId": "EARTHQUAKE", "uses": 17012}, {"moveId": "RETURN", "uses": 10786}, {"moveId": "PETAL_BLIZZARD", "uses": 5390}, {"moveId": "SOLAR_BEAM", "uses": 4433}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 66.8}, {"speciesId": "ninetales_alolan", "speciesName": "Ninetales (Alolan)", "rating": 615, "matchups": [{"opponent": "dragonite", "rating": 854}, {"opponent": "garcho<PERSON>", "rating": 847}, {"opponent": "zekrom", "rating": 835}, {"opponent": "lugia", "rating": 652}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 577, "opRating": 422}], "counters": [{"opponent": "zacian_hero", "rating": 349}, {"opponent": "mewtwo", "rating": 351}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "dialga", "rating": 470}, {"opponent": "gyarados", "rating": 492}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37354}, {"moveId": "CHARM", "uses": 21200}, {"moveId": "FEINT_ATTACK", "uses": 18000}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39772}, {"moveId": "PSYSHOCK", "uses": 12574}, {"moveId": "DAZZLING_GLEAM", "uses": 9501}, {"moveId": "ICE_BEAM", "uses": 8582}, {"moveId": "BLIZZARD", "uses": 5943}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "PSYSHOCK"], "score": 66.8}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 680, "matchups": [{"opponent": "mewtwo", "rating": 738}, {"opponent": "metagross", "rating": 614}, {"opponent": "ho_oh", "rating": 609, "opRating": 390}, {"opponent": "snorlax", "rating": 562, "opRating": 437}, {"opponent": "giratina_origin", "rating": 539}], "counters": [{"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "dialga", "rating": 307}, {"opponent": "zacian_hero", "rating": 384}, {"opponent": "dragonite", "rating": 436}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 56877}, {"moveId": "PECK", "uses": 19623}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 24818}, {"moveId": "DARK_PULSE", "uses": 19093}, {"moveId": "SKY_ATTACK", "uses": 18713}, {"moveId": "PSYCHIC", "uses": 8076}, {"moveId": "RETURN", "uses": 5795}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 66.6}, {"speciesId": "hitmon<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 653, "matchups": [{"opponent": "excadrill", "rating": 854}, {"opponent": "snorlax", "rating": 748, "opRating": 251}, {"opponent": "metagross", "rating": 586}, {"opponent": "swampert", "rating": 555}, {"opponent": "dialga", "rating": 523}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "gyarados", "rating": 314}, {"opponent": "dragonite", "rating": 449}, {"opponent": "garcho<PERSON>", "rating": 485}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42612}, {"moveId": "BULLET_PUNCH", "uses": 26356}, {"moveId": "ROCK_SMASH", "uses": 7543}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24710}, {"moveId": "ICE_PUNCH", "uses": 16079}, {"moveId": "THUNDER_PUNCH", "uses": 11851}, {"moveId": "BRICK_BREAK", "uses": 10973}, {"moveId": "FIRE_PUNCH", "uses": 10645}, {"moveId": "POWER_UP_PUNCH", "uses": 2163}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 66.3}, {"speciesId": "manectric_shadow", "speciesName": "Man<PERSON><PERSON> (Shadow)", "rating": 704, "matchups": [{"opponent": "metagross", "rating": 773}, {"opponent": "mewtwo", "rating": 764}, {"opponent": "ho_oh", "rating": 748}, {"opponent": "gyarados", "rating": 710}, {"opponent": "lugia", "rating": 614}], "counters": [{"opponent": "zacian_hero", "rating": 294}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "excadrill", "rating": 302}, {"opponent": "swampert", "rating": 305}, {"opponent": "garcho<PERSON>", "rating": 326}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 39795}, {"moveId": "CHARGE_BEAM", "uses": 20318}, {"moveId": "THUNDER_FANG", "uses": 16355}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35726}, {"moveId": "PSYCHIC_FANGS", "uses": 19238}, {"moveId": "OVERHEAT", "uses": 12002}, {"moveId": "THUNDER", "uses": 5721}, {"moveId": "FLAME_BURST", "uses": 3978}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 66.3}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 661, "matchups": [{"opponent": "ho_oh", "rating": 644, "opRating": 355}, {"opponent": "dialga", "rating": 637}, {"opponent": "metagross", "rating": 627}, {"opponent": "mewtwo", "rating": 567}, {"opponent": "zacian_hero", "rating": 526}], "counters": [{"opponent": "giratina_origin", "rating": 282}, {"opponent": "lugia", "rating": 314}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "excadrill", "rating": 376}, {"opponent": "zekrom", "rating": 486}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 58407}, {"moveId": "ROCK_SMASH", "uses": 18093}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23007}, {"moveId": "SURF", "uses": 19569}, {"moveId": "EARTHQUAKE", "uses": 18352}, {"moveId": "MEGAHORN", "uses": 15514}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "SURF"], "score": 66.3}, {"speciesId": "ampha<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 674, "matchups": [{"opponent": "metagross", "rating": 771}, {"opponent": "gyarados", "rating": 768}, {"opponent": "lugia", "rating": 674}, {"opponent": "dialga", "rating": 577}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567}], "counters": [{"opponent": "giratina_origin", "rating": 169}, {"opponent": "swampert", "rating": 233}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "dragonite", "rating": 340}, {"opponent": "zacian_hero", "rating": 343}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 53344}, {"moveId": "CHARGE_BEAM", "uses": 23156}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 24287}, {"moveId": "FOCUS_BLAST", "uses": 14823}, {"moveId": "DRAGON_PULSE", "uses": 12872}, {"moveId": "POWER_GEM", "uses": 9080}, {"moveId": "THUNDER", "uses": 7857}, {"moveId": "ZAP_CANNON", "uses": 7598}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 66.2}, {"speciesId": "feraligatr", "speciesName": "Feraligatr", "rating": 686, "matchups": [{"opponent": "grou<PERSON>", "rating": 862}, {"opponent": "ho_oh", "rating": 856, "opRating": 143}, {"opponent": "excadrill", "rating": 651}, {"opponent": "sylveon", "rating": 525, "opRating": 474}, {"opponent": "metagross", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 370}, {"opponent": "lugia", "rating": 378}, {"opponent": "mewtwo", "rating": 427}, {"opponent": "zacian_hero", "rating": 488}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22917}, {"moveId": "WATERFALL", "uses": 21450}, {"moveId": "ICE_FANG", "uses": 20247}, {"moveId": "BITE", "uses": 11919}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 34406}, {"moveId": "CRUNCH", "uses": 17780}, {"moveId": "ICE_BEAM", "uses": 14293}, {"moveId": "RETURN", "uses": 6365}, {"moveId": "HYDRO_PUMP", "uses": 3695}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 66}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 707, "matchups": [{"opponent": "metagross", "rating": 848}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 639}, {"opponent": "sylveon", "rating": 639, "opRating": 360}, {"opponent": "zacian_hero", "rating": 538}, {"opponent": "snorlax", "rating": 529, "opRating": 470}], "counters": [{"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "gyarados", "rating": 260}, {"opponent": "lugia", "rating": 280}, {"opponent": "mewtwo", "rating": 468}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 30244}, {"moveId": "SHADOW_CLAW", "uses": 29080}, {"moveId": "EMBER", "uses": 17105}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 41687}, {"moveId": "RETURN", "uses": 10666}, {"moveId": "SOLAR_BEAM", "uses": 10117}, {"moveId": "OVERHEAT", "uses": 8872}, {"moveId": "FIRE_BLAST", "uses": 5149}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 66}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 721, "matchups": [{"opponent": "swampert", "rating": 741}, {"opponent": "grou<PERSON>", "rating": 613}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 613, "opRating": 386}, {"opponent": "excadrill", "rating": 578}, {"opponent": "zacian_hero", "rating": 549}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "metagross", "rating": 276}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "mewtwo", "rating": 408}, {"opponent": "gyarados", "rating": 427}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53984}, {"moveId": "RAZOR_LEAF", "uses": 22516}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 42972}, {"moveId": "SLUDGE_BOMB", "uses": 22731}, {"moveId": "PETAL_BLIZZARD", "uses": 5924}, {"moveId": "SOLAR_BEAM", "uses": 4967}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 66}, {"speciesId": "feraligatr_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 698, "matchups": [{"opponent": "ho_oh", "rating": 873, "opRating": 126}, {"opponent": "grou<PERSON>", "rating": 778}, {"opponent": "metagross", "rating": 772}, {"opponent": "excadrill", "rating": 592}, {"opponent": "snorlax", "rating": 542, "opRating": 457}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "giratina_origin", "rating": 292}, {"opponent": "dialga", "rating": 307}, {"opponent": "garcho<PERSON>", "rating": 373}, {"opponent": "zacian_hero", "rating": 427}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22446}, {"moveId": "WATERFALL", "uses": 21930}, {"moveId": "ICE_FANG", "uses": 20337}, {"moveId": "BITE", "uses": 11894}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37397}, {"moveId": "CRUNCH", "uses": 19331}, {"moveId": "ICE_BEAM", "uses": 15639}, {"moveId": "HYDRO_PUMP", "uses": 4020}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 65.8}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 673, "matchups": [{"opponent": "pinsir_shadow", "rating": 893, "opRating": 106}, {"opponent": "garcho<PERSON>", "rating": 878}, {"opponent": "mewtwo_shadow", "rating": 832, "opRating": 167}, {"opponent": "grou<PERSON>", "rating": 527}, {"opponent": "zekrom", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 323}, {"opponent": "giratina_origin", "rating": 366}, {"opponent": "gyarados", "rating": 368}, {"opponent": "metagross", "rating": 398}, {"opponent": "mewtwo", "rating": 463}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 51916}, {"moveId": "STEEL_WING", "uses": 24584}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 26459}, {"moveId": "CRUNCH", "uses": 25148}, {"moveId": "ANCIENT_POWER", "uses": 24873}]}, "moveset": ["WING_ATTACK", "DRAGON_CLAW", "CRUNCH"], "score": 65.7}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 626, "matchups": [{"opponent": "garcho<PERSON>", "rating": 889}, {"opponent": "dragonite", "rating": 696}, {"opponent": "zekrom", "rating": 625, "opRating": 374}, {"opponent": "swampert", "rating": 562}, {"opponent": "giratina_origin", "rating": 535}], "counters": [{"opponent": "dialga", "rating": 326}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "gyarados", "rating": 371}, {"opponent": "excadrill", "rating": 388}, {"opponent": "lugia", "rating": 426}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47649}, {"moveId": "FROST_BREATH", "uses": 28851}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36471}, {"moveId": "ICY_WIND", "uses": 12697}, {"moveId": "HYDRO_PUMP", "uses": 10121}, {"moveId": "RETURN", "uses": 7063}, {"moveId": "BLIZZARD", "uses": 5644}, {"moveId": "AURORA_BEAM", "uses": 4557}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 65.7}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 698, "matchups": [{"opponent": "snor<PERSON>_shadow", "rating": 836, "opRating": 163}, {"opponent": "snorlax", "rating": 819, "opRating": 180}, {"opponent": "dialga", "rating": 691}, {"opponent": "swampert", "rating": 556}, {"opponent": "excadrill", "rating": 530}], "counters": [{"opponent": "gyarados", "rating": 298}, {"opponent": "dragonite", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 316}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46914}, {"moveId": "BULLET_PUNCH", "uses": 29586}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29272}, {"moveId": "SUPER_POWER", "uses": 21762}, {"moveId": "HEAVY_SLAM", "uses": 10186}, {"moveId": "DYNAMIC_PUNCH", "uses": 7861}, {"moveId": "RETURN", "uses": 7385}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "HEAVY_SLAM"], "score": 65.7}, {"speciesId": "tyrantrum", "speciesName": "Tyrantrum", "rating": 703, "matchups": [{"opponent": "garcho<PERSON>", "rating": 772}, {"opponent": "ho_oh", "rating": 666, "opRating": 333}, {"opponent": "mewtwo", "rating": 548}, {"opponent": "dragonite", "rating": 514}, {"opponent": "giratina_origin", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "gyarados", "rating": 270}, {"opponent": "lugia", "rating": 304}, {"opponent": "swampert", "rating": 405}, {"opponent": "metagross", "rating": 418}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 35531}, {"moveId": "ROCK_THROW", "uses": 24047}, {"moveId": "CHARM", "uses": 16895}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23606}, {"moveId": "CRUNCH", "uses": 20678}, {"moveId": "OUTRAGE", "uses": 17971}, {"moveId": "EARTHQUAKE", "uses": 14370}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "CRUNCH"], "score": 65.7}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 624, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "swampert", "rating": 811}, {"opponent": "dragonite", "rating": 690}, {"opponent": "zekrom", "rating": 610, "opRating": 389}, {"opponent": "excadrill", "rating": 564}], "counters": [{"opponent": "lugia", "rating": 330}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "dialga", "rating": 355}, {"opponent": "giratina_origin", "rating": 396}, {"opponent": "gyarados", "rating": 430}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 54282}, {"moveId": "RAZOR_LEAF", "uses": 22218}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39301}, {"moveId": "ENERGY_BALL", "uses": 14452}, {"moveId": "OUTRAGE", "uses": 10294}, {"moveId": "RETURN", "uses": 6598}, {"moveId": "BLIZZARD", "uses": 5986}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 65.6}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 710, "matchups": [{"opponent": "nihilego", "rating": 896, "opRating": 103}, {"opponent": "ho_oh", "rating": 828, "opRating": 171}, {"opponent": "snorlax", "rating": 699, "opRating": 300}, {"opponent": "sylveon", "rating": 693, "opRating": 306}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 660, "opRating": 339}], "counters": [{"opponent": "dialga", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 394}, {"opponent": "zacian_hero", "rating": 453}, {"opponent": "metagross", "rating": 459}, {"opponent": "mewtwo", "rating": 476}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 43807}, {"moveId": "METAL_CLAW", "uses": 32693}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 35920}, {"moveId": "DRILL_PECK", "uses": 19061}, {"moveId": "BLIZZARD", "uses": 10615}, {"moveId": "FLASH_CANNON", "uses": 7105}, {"moveId": "HYDRO_PUMP", "uses": 3853}]}, "moveset": ["WATERFALL", "HYDRO_CANNON", "FLASH_CANNON"], "score": 65.6}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 699, "matchups": [{"opponent": "yveltal", "rating": 859, "opRating": 140}, {"opponent": "gyarados", "rating": 702}, {"opponent": "kyogre", "rating": 662, "opRating": 337}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 626, "opRating": 373}, {"opponent": "giratina_origin", "rating": 547}], "counters": [{"opponent": "mewtwo", "rating": 278}, {"opponent": "dialga", "rating": 282}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "metagross", "rating": 389}, {"opponent": "lugia", "rating": 488}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9267}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4991}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4448}, {"moveId": "CHARGE_BEAM", "uses": 4424}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4295}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4231}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4218}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4037}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3975}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3914}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3848}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3834}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3795}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3554}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3537}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3487}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3359}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3240}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 30724}, {"moveId": "ZAP_CANNON", "uses": 18352}, {"moveId": "HYPER_BEAM", "uses": 15000}, {"moveId": "SOLAR_BEAM", "uses": 12289}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 65.6}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "gyarados", "rating": 814}, {"opponent": "dialga", "rating": 607}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 586}, {"opponent": "lugia", "rating": 508}, {"opponent": "zacian_hero", "rating": 505}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "swampert", "rating": 186}, {"opponent": "dragonite", "rating": 284}, {"opponent": "mewtwo", "rating": 385}, {"opponent": "metagross", "rating": 476}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 51990}, {"moveId": "CHARGE_BEAM", "uses": 24510}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 21742}, {"moveId": "FOCUS_BLAST", "uses": 13038}, {"moveId": "DRAGON_PULSE", "uses": 11066}, {"moveId": "RETURN", "uses": 8789}, {"moveId": "POWER_GEM", "uses": 8015}, {"moveId": "THUNDER", "uses": 7001}, {"moveId": "ZAP_CANNON", "uses": 6805}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 65.4}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 653, "matchups": [{"opponent": "mewtwo_shadow", "rating": 932, "opRating": 67}, {"opponent": "metagross", "rating": 804}, {"opponent": "excadrill", "rating": 801}, {"opponent": "mewtwo", "rating": 704}, {"opponent": "lugia", "rating": 570}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "gyarados", "rating": 327}, {"opponent": "giratina_origin", "rating": 394}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46713}, {"moveId": "FIRE_FANG", "uses": 29787}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34739}, {"moveId": "FLAMETHROWER", "uses": 20972}, {"moveId": "FOUL_PLAY", "uses": 15014}, {"moveId": "FIRE_BLAST", "uses": 5841}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 65.2}, {"speciesId": "primeape", "speciesName": "Primeape", "rating": 662, "matchups": [{"opponent": "melmetal", "rating": 875, "opRating": 124}, {"opponent": "excadrill", "rating": 845}, {"opponent": "snorlax", "rating": 765, "opRating": 234}, {"opponent": "dialga", "rating": 634}, {"opponent": "metagross", "rating": 543}], "counters": [{"opponent": "mewtwo", "rating": 273}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "gyarados", "rating": 296}, {"opponent": "garcho<PERSON>", "rating": 396}, {"opponent": "swampert", "rating": 450}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37346}, {"moveId": "KARATE_CHOP", "uses": 34451}, {"moveId": "LOW_KICK", "uses": 4693}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23348}, {"moveId": "NIGHT_SLASH", "uses": 18725}, {"moveId": "CROSS_CHOP", "uses": 16155}, {"moveId": "ICE_PUNCH", "uses": 15213}, {"moveId": "LOW_SWEEP", "uses": 3024}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 65.1}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 690, "matchups": [{"opponent": "swampert", "rating": 860}, {"opponent": "kyogre", "rating": 729, "opRating": 270}, {"opponent": "ho_oh", "rating": 719, "opRating": 280}, {"opponent": "excadrill", "rating": 677}, {"opponent": "grou<PERSON>", "rating": 574, "opRating": 425}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "giratina_origin", "rating": 386}, {"opponent": "mewtwo", "rating": 414}, {"opponent": "metagross", "rating": 421}, {"opponent": "garcho<PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43042}, {"moveId": "BULLET_SEED", "uses": 33458}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 23504}, {"moveId": "DRAGON_PULSE", "uses": 16660}, {"moveId": "DRACO_METEOR", "uses": 14849}, {"moveId": "SOLAR_BEAM", "uses": 10703}, {"moveId": "RETURN", "uses": 10633}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 64.9}, {"speciesId": "poliwrath", "speciesName": "Poliwrath", "rating": 659, "matchups": [{"opponent": "excadrill", "rating": 639}, {"opponent": "snorlax", "rating": 599, "opRating": 400}, {"opponent": "dialga", "rating": 594}, {"opponent": "kyogre", "rating": 534, "opRating": 465}, {"opponent": "garcho<PERSON>", "rating": 505}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "giratina_origin", "rating": 280}, {"opponent": "gyarados", "rating": 324}, {"opponent": "dragonite", "rating": 486}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 35254}, {"moveId": "BUBBLE", "uses": 30017}, {"moveId": "ROCK_SMASH", "uses": 11264}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 19773}, {"moveId": "DYNAMIC_PUNCH", "uses": 18485}, {"moveId": "SCALD", "uses": 17531}, {"moveId": "RETURN", "uses": 6447}, {"moveId": "POWER_UP_PUNCH", "uses": 6217}, {"moveId": "SUBMISSION", "uses": 4074}, {"moveId": "HYDRO_PUMP", "uses": 3961}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 64.9}, {"speciesId": "gigalith", "speciesName": "Gigalith", "rating": 669, "matchups": [{"opponent": "ho_oh", "rating": 870, "opRating": 129}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 603, "opRating": 396}, {"opponent": "lugia", "rating": 592}, {"opponent": "mewtwo", "rating": 539}, {"opponent": "zacian_hero", "rating": 505}], "counters": [{"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "metagross", "rating": 302}, {"opponent": "dragonite", "rating": 364}, {"opponent": "gyarados", "rating": 420}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43910}, {"moveId": "MUD_SLAP", "uses": 32590}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 31636}, {"moveId": "SUPER_POWER", "uses": 26352}, {"moveId": "HEAVY_SLAM", "uses": 10660}, {"moveId": "SOLAR_BEAM", "uses": 7797}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "SUPER_POWER"], "score": 64.7}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 689, "matchups": [{"opponent": "ho_oh", "rating": 943, "opRating": 56}, {"opponent": "gyarados", "rating": 832}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 713}, {"opponent": "metagross", "rating": 701}, {"opponent": "lugia", "rating": 551}], "counters": [{"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "dialga", "rating": 342}, {"opponent": "mewtwo", "rating": 359}, {"opponent": "dragonite", "rating": 377}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41581}, {"moveId": "FIRE_FANG", "uses": 25078}, {"moveId": "ROCK_SMASH", "uses": 9765}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22273}, {"moveId": "ROCK_SLIDE", "uses": 21163}, {"moveId": "CRUNCH", "uses": 17823}, {"moveId": "FLAMETHROWER", "uses": 15277}]}, "moveset": ["SNARL", "WILD_CHARGE", "ROCK_SLIDE"], "score": 64.6}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 628, "matchups": [{"opponent": "swampert", "rating": 948}, {"opponent": "mewtwo", "rating": 693}, {"opponent": "kyogre", "rating": 674, "opRating": 325}, {"opponent": "metagross", "rating": 610}, {"opponent": "excadrill", "rating": 575}], "counters": [{"opponent": "dialga", "rating": 282}, {"opponent": "zacian_hero", "rating": 404}, {"opponent": "garcho<PERSON>", "rating": 415}, {"opponent": "giratina_origin", "rating": 440}, {"opponent": "gyarados", "rating": 476}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25677}, {"moveId": "BULLET_SEED", "uses": 23269}, {"moveId": "FEINT_ATTACK", "uses": 17624}, {"moveId": "RAZOR_LEAF", "uses": 10029}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36523}, {"moveId": "FOUL_PLAY", "uses": 24108}, {"moveId": "HURRICANE", "uses": 8834}, {"moveId": "LEAF_TORNADO", "uses": 6932}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 64.5}, {"speciesId": "gardevoir", "speciesName": "Gardevoir", "rating": 637, "matchups": [{"opponent": "yveltal", "rating": 866, "opRating": 133}, {"opponent": "palkia", "rating": 860, "opRating": 139}, {"opponent": "dragonite", "rating": 847}, {"opponent": "zekrom", "rating": 847, "opRating": 152}, {"opponent": "garcho<PERSON>", "rating": 525}], "counters": [{"opponent": "mewtwo", "rating": 335}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "giratina_origin", "rating": 400}, {"opponent": "dialga", "rating": 421}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30252}, {"moveId": "CHARM", "uses": 27822}, {"moveId": "CHARGE_BEAM", "uses": 18487}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 23032}, {"moveId": "SYNCHRONOISE", "uses": 20239}, {"moveId": "DAZZLING_GLEAM", "uses": 14316}, {"moveId": "RETURN", "uses": 9641}, {"moveId": "PSYCHIC", "uses": 9174}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 64.3}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 704, "matchups": [{"opponent": "swampert", "rating": 793}, {"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 677}, {"opponent": "zacian_hero", "rating": 610}, {"opponent": "excadrill", "rating": 593}], "counters": [{"opponent": "dialga", "rating": 247}, {"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "gyarados", "rating": 396}, {"opponent": "grou<PERSON>", "rating": 480}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 52924}, {"moveId": "RAZOR_LEAF", "uses": 23576}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38000}, {"moveId": "SLUDGE_BOMB", "uses": 19392}, {"moveId": "RETURN", "uses": 9514}, {"moveId": "PETAL_BLIZZARD", "uses": 5231}, {"moveId": "SOLAR_BEAM", "uses": 4237}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 64.1}, {"speciesId": "d<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 668, "matchups": [{"opponent": "xurkitree", "rating": 889, "opRating": 110}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "darmanitan_standard", "rating": 832, "opRating": 167}, {"opponent": "haxorus", "rating": 754, "opRating": 245}, {"opponent": "swampert", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 320}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "giratina_origin", "rating": 446}, {"opponent": "metagross", "rating": 462}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 52851}, {"moveId": "BITE", "uses": 23649}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 35910}, {"moveId": "NIGHT_SLASH", "uses": 32832}, {"moveId": "HYPER_BEAM", "uses": 7699}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 64}, {"speciesId": "latias", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 680, "matchups": [{"opponent": "kyogre", "rating": 671, "opRating": 328}, {"opponent": "swampert", "rating": 665}, {"opponent": "ho_oh", "rating": 569, "opRating": 430}, {"opponent": "grou<PERSON>", "rating": 549}, {"opponent": "mewtwo", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 317}, {"opponent": "metagross", "rating": 369}, {"opponent": "giratina_origin", "rating": 378}, {"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "lugia", "rating": 400}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 46233}, {"moveId": "CHARM", "uses": 23612}, {"moveId": "ZEN_HEADBUTT", "uses": 6663}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 21902}, {"moveId": "OUTRAGE", "uses": 19623}, {"moveId": "PSYCHIC", "uses": 14566}, {"moveId": "THUNDER", "uses": 12166}, {"moveId": "RETURN", "uses": 8025}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 64}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 661, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 689, "opRating": 310}, {"opponent": "dialga", "rating": 633}, {"opponent": "sylveon", "rating": 544, "opRating": 455}, {"opponent": "excadrill", "rating": 518}, {"opponent": "zekrom", "rating": 504, "opRating": 495}], "counters": [{"opponent": "gyarados", "rating": 365}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "metagross", "rating": 427}, {"opponent": "lugia", "rating": 438}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 50442}, {"moveId": "METAL_CLAW", "uses": 26058}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26259}, {"moveId": "EARTHQUAKE", "uses": 23730}, {"moveId": "MUDDY_WATER", "uses": 15306}, {"moveId": "FLASH_CANNON", "uses": 11205}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 64}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 608, "matchups": [{"opponent": "victini", "rating": 926, "opRating": 73}, {"opponent": "mewtwo", "rating": 820}, {"opponent": "mewtwo_shadow", "rating": 785, "opRating": 214}, {"opponent": "metagross", "rating": 714}, {"opponent": "giratina_origin", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 388}, {"opponent": "garcho<PERSON>", "rating": 389}, {"opponent": "lugia", "rating": 402}, {"opponent": "gyarados", "rating": 420}, {"opponent": "zacian_hero", "rating": 421}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43848}, {"moveId": "SUCKER_PUNCH", "uses": 32652}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37782}, {"moveId": "DARK_PULSE", "uses": 24659}, {"moveId": "DAZZLING_GLEAM", "uses": 13883}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 63.9}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 653, "matchups": [{"opponent": "porygon_z_shadow", "rating": 936, "opRating": 63}, {"opponent": "mamos<PERSON>_shadow", "rating": 853, "opRating": 146}, {"opponent": "dialga", "rating": 614}, {"opponent": "lugia", "rating": 547}, {"opponent": "metagross", "rating": 519}], "counters": [{"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "giratina_origin", "rating": 320}, {"opponent": "gyarados", "rating": 443}, {"opponent": "mewtwo", "rating": 460}, {"opponent": "excadrill", "rating": 481}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 53739}, {"moveId": "METAL_CLAW", "uses": 22761}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31201}, {"moveId": "FOUL_PLAY", "uses": 20458}, {"moveId": "IRON_HEAD", "uses": 14576}, {"moveId": "PLAY_ROUGH", "uses": 10224}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "FOUL_PLAY"], "score": 63.9}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 653, "matchups": [{"opponent": "xurkitree", "rating": 942, "opRating": 57}, {"opponent": "excadrill", "rating": 887}, {"opponent": "metagross", "rating": 750}, {"opponent": "dialga", "rating": 667}, {"opponent": "zekrom", "rating": 667}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "gyarados", "rating": 260}, {"opponent": "lugia", "rating": 304}, {"opponent": "giratina_origin", "rating": 310}, {"opponent": "garcho<PERSON>", "rating": 338}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53782}, {"moveId": "METAL_CLAW", "uses": 22718}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 32037}, {"moveId": "EARTHQUAKE", "uses": 19905}, {"moveId": "ROCK_TOMB", "uses": 12242}, {"moveId": "BULLDOZE", "uses": 12220}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 63.9}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 689, "matchups": [{"opponent": "yveltal", "rating": 875, "opRating": 125}, {"opponent": "gyarados", "rating": 776}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 686}, {"opponent": "lugia", "rating": 619}, {"opponent": "metagross", "rating": 552}], "counters": [{"opponent": "dialga", "rating": 252}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "mewtwo", "rating": 351}, {"opponent": "zacian_hero", "rating": 367}, {"opponent": "dragonite", "rating": 372}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 10938}, {"moveId": "SNARL", "uses": 9077}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4573}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4330}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4067}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3834}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3738}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3619}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3616}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3589}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3456}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3305}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3266}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3056}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2995}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2960}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2926}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2750}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32792}, {"moveId": "CRUNCH", "uses": 18668}, {"moveId": "PSYCHIC_FANGS", "uses": 16249}, {"moveId": "RETURN", "uses": 6283}, {"moveId": "HYPER_BEAM", "uses": 2493}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 63.8}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 669, "matchups": [{"opponent": "swampert", "rating": 802}, {"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 697}, {"opponent": "grou<PERSON>", "rating": 627}, {"opponent": "sylveon", "rating": 578, "opRating": 421}], "counters": [{"opponent": "mewtwo", "rating": 346}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "gyarados", "rating": 391}, {"opponent": "dialga", "rating": 410}, {"opponent": "metagross", "rating": 453}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54021}, {"moveId": "RAZOR_LEAF", "uses": 22479}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 45472}, {"moveId": "EARTHQUAKE", "uses": 19572}, {"moveId": "PETAL_BLIZZARD", "uses": 6230}, {"moveId": "SOLAR_BEAM", "uses": 5138}, {"moveId": "FRUSTRATION", "uses": 28}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 63.5}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 601, "matchups": [{"opponent": "metagross", "rating": 733}, {"opponent": "garcho<PERSON>", "rating": 684}, {"opponent": "grou<PERSON>", "rating": 605}, {"opponent": "zacian_hero", "rating": 593}, {"opponent": "dragonite", "rating": 541}], "counters": [{"opponent": "lugia", "rating": 311}, {"opponent": "gyarados", "rating": 314}, {"opponent": "dialga", "rating": 336}, {"opponent": "excadrill", "rating": 411}, {"opponent": "mewtwo", "rating": 492}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55642}, {"moveId": "ASTONISH", "uses": 20858}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31415}, {"moveId": "SHADOW_BALL", "uses": 29830}, {"moveId": "OMINOUS_WIND", "uses": 15296}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 63.4}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 680, "matchups": [{"opponent": "tapu_bulu", "rating": 882, "opRating": 117}, {"opponent": "gengar", "rating": 853, "opRating": 146}, {"opponent": "metagross", "rating": 665}, {"opponent": "mewtwo", "rating": 636}, {"opponent": "giratina_origin", "rating": 573}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "lugia", "rating": 373}, {"opponent": "gyarados", "rating": 425}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24781}, {"moveId": "INFESTATION", "uses": 18842}, {"moveId": "ICE_FANG", "uses": 17737}, {"moveId": "BITE", "uses": 15067}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31070}, {"moveId": "AQUA_TAIL", "uses": 22763}, {"moveId": "SLUDGE_BOMB", "uses": 17245}, {"moveId": "FELL_STINGER", "uses": 5423}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 63.3}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 689, "matchups": [{"opponent": "electivire_shadow", "rating": 976, "opRating": 23}, {"opponent": "magnezone_shadow", "rating": 956, "opRating": 43}, {"opponent": "xurkitree", "rating": 950, "opRating": 49}, {"opponent": "excadrill", "rating": 915}, {"opponent": "nihilego", "rating": 895, "opRating": 104}], "counters": [{"opponent": "mewtwo", "rating": 278}, {"opponent": "giratina_origin", "rating": 356}, {"opponent": "garcho<PERSON>", "rating": 394}, {"opponent": "dialga", "rating": 421}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39912}, {"moveId": "DRAGON_TAIL", "uses": 36588}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27581}, {"moveId": "EARTH_POWER", "uses": 18517}, {"moveId": "STONE_EDGE", "uses": 15667}, {"moveId": "EARTHQUAKE", "uses": 7868}, {"moveId": "RETURN", "uses": 6735}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 63.3}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 604, "matchups": [{"opponent": "mewtwo", "rating": 736}, {"opponent": "swampert", "rating": 736}, {"opponent": "metagross", "rating": 647}, {"opponent": "excadrill", "rating": 623}, {"opponent": "giratina_origin", "rating": 521}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "lugia", "rating": 385}, {"opponent": "gyarados", "rating": 409}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25114}, {"moveId": "BULLET_SEED", "uses": 22772}, {"moveId": "FEINT_ATTACK", "uses": 17835}, {"moveId": "RAZOR_LEAF", "uses": 10871}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33770}, {"moveId": "FOUL_PLAY", "uses": 22102}, {"moveId": "HURRICANE", "uses": 7999}, {"moveId": "LEAF_TORNADO", "uses": 6487}, {"moveId": "RETURN", "uses": 6172}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 63.3}, {"speciesId": "blastoise", "speciesName": "Blastoise", "rating": 645, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 900, "opRating": 100}, {"opponent": "mamos<PERSON>_shadow", "rating": 867, "opRating": 132}, {"opponent": "excadrill", "rating": 685}, {"opponent": "garcho<PERSON>", "rating": 567}, {"opponent": "zacian_hero", "rating": 544}], "counters": [{"opponent": "dialga", "rating": 315}, {"opponent": "gyarados", "rating": 345}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "lugia", "rating": 411}, {"opponent": "metagross", "rating": 497}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 50860}, {"moveId": "BITE", "uses": 25640}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37012}, {"moveId": "ICE_BEAM", "uses": 15605}, {"moveId": "SKULL_BASH", "uses": 8003}, {"moveId": "RETURN", "uses": 6686}, {"moveId": "FLASH_CANNON", "uses": 5260}, {"moveId": "HYDRO_PUMP", "uses": 4036}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 63.2}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 664, "matchups": [{"opponent": "manectric_shadow", "rating": 932, "opRating": 67}, {"opponent": "weavile_shadow", "rating": 907, "opRating": 92}, {"opponent": "weavile", "rating": 904, "opRating": 95}, {"opponent": "swampert", "rating": 615}, {"opponent": "mewtwo", "rating": 561}], "counters": [{"opponent": "dialga", "rating": 361}, {"opponent": "gyarados", "rating": 391}, {"opponent": "garcho<PERSON>", "rating": 424}, {"opponent": "excadrill", "rating": 472}, {"opponent": "giratina_origin", "rating": 474}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 65848}, {"moveId": "ZEN_HEADBUTT", "uses": 10652}], "chargedMoves": [{"moveId": "STOMP", "uses": 26194}, {"moveId": "MEGAHORN", "uses": 21399}, {"moveId": "EARTHQUAKE", "uses": 19748}, {"moveId": "SKULL_BASH", "uses": 9108}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 63.2}, {"speciesId": "muk_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 698, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "sylveon", "rating": 733, "opRating": 266}, {"opponent": "gyarados", "rating": 709}, {"opponent": "zacian_hero", "rating": 625}, {"opponent": "yveltal", "rating": 572, "opRating": 427}], "counters": [{"opponent": "mewtwo", "rating": 283}, {"opponent": "garcho<PERSON>", "rating": 288}, {"opponent": "dialga", "rating": 296}, {"opponent": "giratina_origin", "rating": 324}, {"opponent": "metagross", "rating": 372}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 31964}, {"moveId": "LICK", "uses": 23209}, {"moveId": "INFESTATION", "uses": 21340}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27101}, {"moveId": "THUNDER_PUNCH", "uses": 22901}, {"moveId": "SLUDGE_WAVE", "uses": 15303}, {"moveId": "GUNK_SHOT", "uses": 6016}, {"moveId": "ACID_SPRAY", "uses": 5134}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 63.2}, {"speciesId": "forretress_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 605, "matchups": [{"opponent": "metagross", "rating": 783}, {"opponent": "mewtwo", "rating": 637}, {"opponent": "dialga", "rating": 582}, {"opponent": "zacian_hero", "rating": 548}, {"opponent": "excadrill", "rating": 545}], "counters": [{"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "dragonite", "rating": 220}, {"opponent": "gyarados", "rating": 265}, {"opponent": "lugia", "rating": 283}, {"opponent": "swampert", "rating": 435}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47037}, {"moveId": "STRUGGLE_BUG", "uses": 29463}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 20498}, {"moveId": "EARTHQUAKE", "uses": 17498}, {"moveId": "HEAVY_SLAM", "uses": 16805}, {"moveId": "ROCK_TOMB", "uses": 13156}, {"moveId": "SAND_TOMB", "uses": 8574}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 63}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 712, "matchups": [{"opponent": "magnezone_shadow", "rating": 827, "opRating": 172}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 782}, {"opponent": "sylveon", "rating": 782, "opRating": 217}, {"opponent": "zacian_hero", "rating": 677}, {"opponent": "yveltal", "rating": 567, "opRating": 432}], "counters": [{"opponent": "mewtwo", "rating": 252}, {"opponent": "metagross", "rating": 348}, {"opponent": "dialga", "rating": 358}, {"opponent": "lugia", "rating": 376}, {"opponent": "gyarados", "rating": 404}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54566}, {"moveId": "BITE", "uses": 21934}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 20423}, {"moveId": "EARTH_POWER", "uses": 19281}, {"moveId": "STONE_EDGE", "uses": 14082}, {"moveId": "SLUDGE_WAVE", "uses": 8285}, {"moveId": "EARTHQUAKE", "uses": 8280}, {"moveId": "RETURN", "uses": 6269}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 62.9}, {"speciesId": "gren<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 614, "matchups": [{"opponent": "excadrill", "rating": 884}, {"opponent": "ho_oh", "rating": 809, "opRating": 190}, {"opponent": "metagross", "rating": 793}, {"opponent": "mewtwo", "rating": 681}, {"opponent": "giratina_origin", "rating": 509}], "counters": [{"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "lugia", "rating": 297}, {"opponent": "dialga", "rating": 328}, {"opponent": "gyarados", "rating": 360}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 40085}, {"moveId": "FEINT_ATTACK", "uses": 36415}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 36098}, {"moveId": "SURF", "uses": 26381}, {"moveId": "AERIAL_ACE", "uses": 9829}, {"moveId": "HYDRO_PUMP", "uses": 4262}]}, "moveset": ["BUBBLE", "NIGHT_SLASH", "SURF"], "score": 62.8}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 667, "matchups": [{"opponent": "moltres_shadow", "rating": 868, "opRating": 131}, {"opponent": "gyarados", "rating": 825}, {"opponent": "metagross", "rating": 809}, {"opponent": "gyarado<PERSON>_shadow", "rating": 782, "opRating": 217}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 620, "opRating": 379}], "counters": [{"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "mewtwo", "rating": 382}, {"opponent": "dialga", "rating": 388}, {"opponent": "lugia", "rating": 423}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28904}, {"moveId": "FIRE_FANG", "uses": 20623}, {"moveId": "THUNDER_FANG", "uses": 15080}, {"moveId": "BITE", "uses": 11860}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20994}, {"moveId": "CRUNCH", "uses": 14871}, {"moveId": "PSYCHIC_FANGS", "uses": 13488}, {"moveId": "FLAMETHROWER", "uses": 12385}, {"moveId": "BULLDOZE", "uses": 6327}, {"moveId": "RETURN", "uses": 5109}, {"moveId": "FIRE_BLAST", "uses": 3339}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 62.7}, {"speciesId": "crawdaunt", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 605, "matchups": [{"opponent": "excadrill", "rating": 843}, {"opponent": "metagross", "rating": 772}, {"opponent": "ho_oh", "rating": 751, "opRating": 248}, {"opponent": "mewtwo", "rating": 731}, {"opponent": "giratina_origin", "rating": 503}], "counters": [{"opponent": "garcho<PERSON>", "rating": 288}, {"opponent": "lugia", "rating": 316}, {"opponent": "dragonite", "rating": 329}, {"opponent": "gyarados", "rating": 335}, {"opponent": "dialga", "rating": 345}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44459}, {"moveId": "WATERFALL", "uses": 32041}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39308}, {"moveId": "CRABHAMMER", "uses": 23015}, {"moveId": "VICE_GRIP", "uses": 7342}, {"moveId": "BUBBLE_BEAM", "uses": 6932}]}, "moveset": ["SNARL", "NIGHT_SLASH", "CRABHAMMER"], "score": 62.7}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 677, "matchups": [{"opponent": "genesect_douse", "rating": 909, "opRating": 90}, {"opponent": "metagross", "rating": 816}, {"opponent": "sylveon", "rating": 579, "opRating": 420}, {"opponent": "dialga", "rating": 574}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537, "opRating": 462}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "giratina_origin", "rating": 264}, {"opponent": "lugia", "rating": 326}, {"opponent": "excadrill", "rating": 374}, {"opponent": "zacian_hero", "rating": 430}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 61856}, {"moveId": "LOW_KICK", "uses": 14644}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 31123}, {"moveId": "ROCK_SLIDE", "uses": 20749}, {"moveId": "FOCUS_BLAST", "uses": 13638}, {"moveId": "FLAME_CHARGE", "uses": 8563}, {"moveId": "HEAT_WAVE", "uses": 2362}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 62.5}, {"speciesId": "hoopa_unbound", "speciesName": "<PERSON><PERSON><PERSON> (Unbound)", "rating": 633, "matchups": [{"opponent": "ho_oh", "rating": 710, "opRating": 289}, {"opponent": "giratina_altered", "rating": 649, "opRating": 350}, {"opponent": "mewtwo", "rating": 630}, {"opponent": "excadrill", "rating": 557}, {"opponent": "dialga", "rating": 519}], "counters": [{"opponent": "giratina_origin", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "gyarados", "rating": 381}, {"opponent": "lugia", "rating": 404}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56626}, {"moveId": "ASTONISH", "uses": 19874}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 30848}, {"moveId": "SHADOW_BALL", "uses": 23159}, {"moveId": "PSYCHIC", "uses": 22437}]}, "moveset": ["CONFUSION", "DARK_PULSE", "SHADOW_BALL"], "score": 62.4}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 628, "matchups": [{"opponent": "excadrill", "rating": 826}, {"opponent": "metagross", "rating": 740}, {"opponent": "mewtwo_shadow", "rating": 704, "opRating": 295}, {"opponent": "mewtwo", "rating": 664}, {"opponent": "giratina_origin", "rating": 557}], "counters": [{"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "gyarados", "rating": 273}, {"opponent": "dialga", "rating": 328}, {"opponent": "lugia", "rating": 419}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45807}, {"moveId": "FIRE_FANG", "uses": 30693}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30712}, {"moveId": "FLAMETHROWER", "uses": 18520}, {"moveId": "FOUL_PLAY", "uses": 13334}, {"moveId": "RETURN", "uses": 8942}, {"moveId": "FIRE_BLAST", "uses": 5026}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 62.4}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 671, "matchups": [{"opponent": "xurkitree", "rating": 856, "opRating": 143}, {"opponent": "electivire_shadow", "rating": 730, "opRating": 269}, {"opponent": "sylveon", "rating": 564, "opRating": 435}, {"opponent": "mewtwo", "rating": 519}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "dialga", "rating": 336}, {"opponent": "giratina_origin", "rating": 364}, {"opponent": "gyarados", "rating": 494}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 8572}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5109}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4472}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4357}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4301}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4289}, {"moveId": "CHARGE_BEAM", "uses": 4247}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4076}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4039}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3936}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3923}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3859}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3843}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3584}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3578}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3526}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3411}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3309}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 26314}, {"moveId": "RETURN", "uses": 16644}, {"moveId": "ZAP_CANNON", "uses": 16191}, {"moveId": "SOLAR_BEAM", "uses": 10797}, {"moveId": "HYPER_BEAM", "uses": 6445}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "RETURN"], "score": 62.3}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "zacian_hero", "rating": 763}, {"opponent": "mewtwo", "rating": 595}, {"opponent": "sylveon", "rating": 551, "opRating": 448}, {"opponent": "giratina_origin", "rating": 514}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 512, "opRating": 487}], "counters": [{"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "dialga", "rating": 266}, {"opponent": "metagross", "rating": 375}, {"opponent": "gyarados", "rating": 412}, {"opponent": "dragonite", "rating": 412}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 48052}, {"moveId": "BITE", "uses": 28448}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38955}, {"moveId": "SLUDGE_BOMB", "uses": 21343}, {"moveId": "FLAMETHROWER", "uses": 16106}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 62.3}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 656, "matchups": [{"opponent": "zekrom", "rating": 649, "opRating": 350}, {"opponent": "palkia", "rating": 585, "opRating": 414}, {"opponent": "giratina_altered", "rating": 564, "opRating": 435}, {"opponent": "giratina_origin", "rating": 551}, {"opponent": "dragonite", "rating": 545}], "counters": [{"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "gyarados", "rating": 402}, {"opponent": "lugia", "rating": 402}, {"opponent": "dialga", "rating": 448}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 41477}, {"moveId": "THUNDER_FANG", "uses": 24184}, {"moveId": "IRON_TAIL", "uses": 10800}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22568}, {"moveId": "PSYCHIC_FANGS", "uses": 20095}, {"moveId": "EARTHQUAKE", "uses": 19183}, {"moveId": "HEAVY_SLAM", "uses": 14666}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "PSYCHIC_FANGS"], "score": 62.3}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 683, "matchups": [{"opponent": "zarude", "rating": 853, "opRating": 146}, {"opponent": "metagross", "rating": 787}, {"opponent": "gyarados", "rating": 709}, {"opponent": "kyogre", "rating": 565, "opRating": 434}, {"opponent": "yveltal", "rating": 529, "opRating": 470}], "counters": [{"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "dialga", "rating": 258}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "lugia", "rating": 459}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 31584}, {"moveId": "BUG_BITE", "uses": 23893}, {"moveId": "MUD_SLAP", "uses": 20972}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26876}, {"moveId": "CRUNCH", "uses": 25611}, {"moveId": "DISCHARGE", "uses": 24099}]}, "moveset": ["SPARK", "X_SCISSOR", "CRUNCH"], "score": 62.2}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 624, "matchups": [{"opponent": "swampert", "rating": 801}, {"opponent": "zacian_hero", "rating": 612}, {"opponent": "excadrill", "rating": 539}, {"opponent": "kyogre", "rating": 530, "opRating": 469}, {"opponent": "sylveon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "gyarados", "rating": 314}, {"opponent": "dialga", "rating": 334}, {"opponent": "metagross", "rating": 468}, {"opponent": "mewtwo", "rating": 489}, {"opponent": "garcho<PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49445}, {"moveId": "RAZOR_LEAF", "uses": 27055}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26374}, {"moveId": "SEED_BOMB", "uses": 21786}, {"moveId": "FOUL_PLAY", "uses": 19812}, {"moveId": "FIRE_BLAST", "uses": 8613}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 61.9}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 664, "matchups": [{"opponent": "swampert", "rating": 935}, {"opponent": "kyogre", "rating": 662, "opRating": 337}, {"opponent": "excadrill", "rating": 654}, {"opponent": "grou<PERSON>", "rating": 564}, {"opponent": "zacian_hero", "rating": 538}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "giratina_origin", "rating": 272}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "gyarados", "rating": 409}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 27025}, {"moveId": "CONFUSION", "uses": 24968}, {"moveId": "EXTRASENSORY", "uses": 19427}, {"moveId": "ZEN_HEADBUTT", "uses": 5227}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 31798}, {"moveId": "PSYCHIC", "uses": 30000}, {"moveId": "SOLAR_BEAM", "uses": 14537}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 61.7}, {"speciesId": "blastoise_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 654, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 867, "opRating": 132}, {"opponent": "grou<PERSON>", "rating": 855}, {"opponent": "ho_oh_shadow", "rating": 844, "opRating": 155}, {"opponent": "ho_oh", "rating": 835, "opRating": 164}, {"opponent": "excadrill", "rating": 608}], "counters": [{"opponent": "dialga", "rating": 339}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "mewtwo", "rating": 390}, {"opponent": "zacian_hero", "rating": 421}, {"opponent": "metagross", "rating": 468}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51295}, {"moveId": "BITE", "uses": 25205}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 40309}, {"moveId": "ICE_BEAM", "uses": 16991}, {"moveId": "SKULL_BASH", "uses": 8985}, {"moveId": "FLASH_CANNON", "uses": 5884}, {"moveId": "HYDRO_PUMP", "uses": 4204}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 61.6}, {"speciesId": "aggron_shadow", "speciesName": "A<PERSON><PERSON> (Shadow)", "rating": 613, "matchups": [{"opponent": "ho_oh", "rating": 729, "opRating": 270}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 710}, {"opponent": "mewtwo", "rating": 659}, {"opponent": "lugia", "rating": 589}, {"opponent": "gyarados", "rating": 582}], "counters": [{"opponent": "metagross", "rating": 328}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "dragonite", "rating": 364}, {"opponent": "dialga", "rating": 453}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 34100}, {"moveId": "SMACK_DOWN", "uses": 33062}, {"moveId": "IRON_TAIL", "uses": 9446}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 31432}, {"moveId": "HEAVY_SLAM", "uses": 21916}, {"moveId": "THUNDER", "uses": 14626}, {"moveId": "ROCK_TOMB", "uses": 8482}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 61.4}, {"speciesId": "golem_alolan", "speciesName": "Golem (Alolan)", "rating": 671, "matchups": [{"opponent": "ho_oh", "rating": 866, "opRating": 133}, {"opponent": "gyarados", "rating": 750}, {"opponent": "yveltal", "rating": 654, "opRating": 345}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "lugia", "rating": 578}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "giratina_origin", "rating": 274}, {"opponent": "metagross", "rating": 398}, {"opponent": "mewtwo", "rating": 466}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 34317}, {"moveId": "ROLLOUT", "uses": 24148}, {"moveId": "ROCK_THROW", "uses": 18065}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37054}, {"moveId": "STONE_EDGE", "uses": 20633}, {"moveId": "ROCK_BLAST", "uses": 18799}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "STONE_EDGE"], "score": 61.4}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 603, "matchups": [{"opponent": "xurkitree", "rating": 942, "opRating": 57}, {"opponent": "excadrill", "rating": 887}, {"opponent": "metagross", "rating": 719}, {"opponent": "dialga", "rating": 615}, {"opponent": "zekrom", "rating": 600}], "counters": [{"opponent": "lugia", "rating": 264}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "zacian_hero", "rating": 404}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53140}, {"moveId": "METAL_CLAW", "uses": 23360}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28716}, {"moveId": "EARTHQUAKE", "uses": 17839}, {"moveId": "BULLDOZE", "uses": 10934}, {"moveId": "ROCK_TOMB", "uses": 10632}, {"moveId": "RETURN", "uses": 8497}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 61.4}, {"speciesId": "sawk", "speciesName": "Sawk", "rating": 641, "matchups": [{"opponent": "zacian_hero", "rating": 759}, {"opponent": "yveltal", "rating": 710, "opRating": 289}, {"opponent": "excadrill", "rating": 542}, {"opponent": "dialga", "rating": 521}, {"opponent": "zekrom", "rating": 521, "opRating": 478}], "counters": [{"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "lugia", "rating": 288}, {"opponent": "dragonite", "rating": 316}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57515}, {"moveId": "LOW_KICK", "uses": 18985}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41154}, {"moveId": "FOCUS_BLAST", "uses": 18616}, {"moveId": "LOW_SWEEP", "uses": 16724}]}, "moveset": ["POISON_JAB", "BODY_SLAM", "FOCUS_BLAST"], "score": 61.4}, {"speciesId": "darmanitan_galarian_standard", "speciesName": "Dar<PERSON><PERSON> (Galarian)", "rating": 601, "matchups": [{"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "lugia", "rating": 694}, {"opponent": "dragonite", "rating": 694}, {"opponent": "giratina_origin", "rating": 579}, {"opponent": "yveltal", "rating": 514, "opRating": 485}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "gyarados", "rating": 280}, {"opponent": "dialga", "rating": 285}, {"opponent": "grou<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 43352}, {"moveId": "TACKLE", "uses": 33148}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30937}, {"moveId": "ICE_PUNCH", "uses": 18579}, {"moveId": "SUPER_POWER", "uses": 18554}, {"moveId": "OVERHEAT", "uses": 8407}]}, "moveset": ["ICE_FANG", "AVALANCHE", "ICE_PUNCH"], "score": 61.3}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 579, "matchups": [{"opponent": "victini", "rating": 911, "opRating": 88}, {"opponent": "mewtwo", "rating": 855}, {"opponent": "mewtwo_shadow", "rating": 820, "opRating": 179}, {"opponent": "metagross", "rating": 725}, {"opponent": "zacian_hero", "rating": 538}], "counters": [{"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "dialga", "rating": 339}, {"opponent": "gyarados", "rating": 353}, {"opponent": "giratina_origin", "rating": 424}, {"opponent": "excadrill", "rating": 474}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43297}, {"moveId": "SUCKER_PUNCH", "uses": 33203}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32451}, {"moveId": "DARK_PULSE", "uses": 21040}, {"moveId": "DAZZLING_GLEAM", "uses": 11575}, {"moveId": "RETURN", "uses": 11311}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 61.3}, {"speciesId": "salamence", "speciesName": "Salamence", "rating": 663, "matchups": [{"opponent": "grou<PERSON>", "rating": 729}, {"opponent": "swampert", "rating": 657}, {"opponent": "yveltal", "rating": 592, "opRating": 407}, {"opponent": "snorlax", "rating": 590, "opRating": 409}, {"opponent": "kyogre", "rating": 585, "opRating": 414}], "counters": [{"opponent": "dialga", "rating": 307}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "garcho<PERSON>", "rating": 434}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43048}, {"moveId": "FIRE_FANG", "uses": 19965}, {"moveId": "BITE", "uses": 13405}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 29307}, {"moveId": "RETURN", "uses": 13027}, {"moveId": "FIRE_BLAST", "uses": 12802}, {"moveId": "HYDRO_PUMP", "uses": 12566}, {"moveId": "DRACO_METEOR", "uses": 8800}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "RETURN"], "score": 61.3}, {"speciesId": "aggron", "speciesName": "Aggron", "rating": 637, "matchups": [{"opponent": "ho_oh", "rating": 668, "opRating": 331}, {"opponent": "mewtwo", "rating": 652}, {"opponent": "gyarados", "rating": 621}, {"opponent": "lugia", "rating": 614}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570}], "counters": [{"opponent": "metagross", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "dragonite", "rating": 300}, {"opponent": "dialga", "rating": 331}, {"opponent": "giratina_origin", "rating": 496}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33829}, {"moveId": "SMACK_DOWN", "uses": 32657}, {"moveId": "IRON_TAIL", "uses": 9987}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 27233}, {"moveId": "HEAVY_SLAM", "uses": 18649}, {"moveId": "THUNDER", "uses": 12763}, {"moveId": "RETURN", "uses": 10407}, {"moveId": "ROCK_TOMB", "uses": 7336}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 61.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 623, "matchups": [{"opponent": "darkrai", "rating": 901, "opRating": 98}, {"opponent": "excadrill", "rating": 854}, {"opponent": "xurkitree", "rating": 811, "opRating": 188}, {"opponent": "ho_oh", "rating": 618, "opRating": 381}, {"opponent": "dialga", "rating": 614}], "counters": [{"opponent": "mewtwo", "rating": 250}, {"opponent": "gyarados", "rating": 255}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "metagross", "rating": 409}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 56967}, {"moveId": "ROCK_SMASH", "uses": 11657}, {"moveId": "LOW_KICK", "uses": 7871}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28856}, {"moveId": "STONE_EDGE", "uses": 15000}, {"moveId": "BRICK_BREAK", "uses": 12766}, {"moveId": "STOMP", "uses": 10195}, {"moveId": "RETURN", "uses": 6017}, {"moveId": "LOW_SWEEP", "uses": 3744}]}, "moveset": ["DOUBLE_KICK", "CLOSE_COMBAT", "STONE_EDGE"], "score": 61.2}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 631, "matchups": [{"opponent": "swampert", "rating": 964}, {"opponent": "kyogre", "rating": 710, "opRating": 289}, {"opponent": "excadrill", "rating": 671}, {"opponent": "grou<PERSON>", "rating": 608}, {"opponent": "zekrom", "rating": 519, "opRating": 480}], "counters": [{"opponent": "mewtwo", "rating": 252}, {"opponent": "dialga", "rating": 342}, {"opponent": "garcho<PERSON>", "rating": 392}, {"opponent": "zacian_hero", "rating": 401}, {"opponent": "gyarados", "rating": 440}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 40510}, {"moveId": "FURY_CUTTER", "uses": 35990}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30196}, {"moveId": "DRAGON_CLAW", "uses": 17085}, {"moveId": "FRENZY_PLANT", "uses": 11193}, {"moveId": "EARTHQUAKE", "uses": 10021}, {"moveId": "AERIAL_ACE", "uses": 8024}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DRAGON_CLAW"], "score": 61.2}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 683, "matchups": [{"opponent": "cobalion", "rating": 899, "opRating": 100}, {"opponent": "metagross", "rating": 850}, {"opponent": "zarude", "rating": 850, "opRating": 149}, {"opponent": "sylveon", "rating": 612, "opRating": 387}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570, "opRating": 429}], "counters": [{"opponent": "excadrill", "rating": 365}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "lugia", "rating": 409}, {"opponent": "dialga", "rating": 451}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48452}, {"moveId": "SCRATCH", "uses": 17745}, {"moveId": "ZEN_HEADBUTT", "uses": 10380}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 30371}, {"moveId": "PSYCHIC", "uses": 26638}, {"moveId": "FLAMETHROWER", "uses": 12827}, {"moveId": "FIRE_BLAST", "uses": 6827}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "PSYCHIC"], "score": 61.1}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 617, "matchups": [{"opponent": "excadrill", "rating": 888}, {"opponent": "magnezone_shadow", "rating": 844, "opRating": 155}, {"opponent": "giratina_origin", "rating": 633}, {"opponent": "metagross", "rating": 624}, {"opponent": "giratina_altered", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "gyarados", "rating": 345}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "lugia", "rating": 361}, {"opponent": "dialga", "rating": 451}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45516}, {"moveId": "FURY_CUTTER", "uses": 30984}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35968}, {"moveId": "NIGHT_SLASH", "uses": 35924}, {"moveId": "DIG", "uses": 4513}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 61.1}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 655, "matchups": [{"opponent": "excadrill", "rating": 812}, {"opponent": "metagross", "rating": 761}, {"opponent": "sylveon", "rating": 573, "opRating": 426}, {"opponent": "dialga", "rating": 543}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 526}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "gyarados", "rating": 242}, {"opponent": "lugia", "rating": 266}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39117}, {"moveId": "EMBER", "uses": 37383}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24744}, {"moveId": "FLAMETHROWER", "uses": 17079}, {"moveId": "OVERHEAT", "uses": 15886}, {"moveId": "LAST_RESORT", "uses": 11547}, {"moveId": "FIRE_BLAST", "uses": 4524}, {"moveId": "HEAT_WAVE", "uses": 2737}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 61}, {"speciesId": "hitmonchan", "speciesName": "Hitmonchan", "rating": 637, "matchups": [{"opponent": "darkrai", "rating": 885, "opRating": 114}, {"opponent": "ma<PERSON><PERSON>", "rating": 799, "opRating": 200}, {"opponent": "snorlax", "rating": 618, "opRating": 381}, {"opponent": "dialga", "rating": 602}, {"opponent": "excadrill", "rating": 562}], "counters": [{"opponent": "mewtwo", "rating": 218}, {"opponent": "gyarados", "rating": 288}, {"opponent": "dragonite", "rating": 369}, {"opponent": "garcho<PERSON>", "rating": 415}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 41559}, {"moveId": "BULLET_PUNCH", "uses": 26897}, {"moveId": "ROCK_SMASH", "uses": 8039}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23367}, {"moveId": "ICE_PUNCH", "uses": 15110}, {"moveId": "THUNDER_PUNCH", "uses": 11042}, {"moveId": "BRICK_BREAK", "uses": 10388}, {"moveId": "FIRE_PUNCH", "uses": 10013}, {"moveId": "RETURN", "uses": 4623}, {"moveId": "POWER_UP_PUNCH", "uses": 2079}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 61}, {"speciesId": "omastar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 672, "matchups": [{"opponent": "ho_oh", "rating": 907, "opRating": 92}, {"opponent": "yveltal", "rating": 843, "opRating": 156}, {"opponent": "gyarados", "rating": 691}, {"opponent": "dragonite", "rating": 614}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 563, "opRating": 436}], "counters": [{"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "dialga", "rating": 252}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "metagross", "rating": 392}, {"opponent": "lugia", "rating": 409}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30031}, {"moveId": "ROCK_THROW", "uses": 23754}, {"moveId": "WATER_GUN", "uses": 22745}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30177}, {"moveId": "ROCK_BLAST", "uses": 21564}, {"moveId": "HYDRO_PUMP", "uses": 14073}, {"moveId": "ANCIENT_POWER", "uses": 10874}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 61}, {"speciesId": "barbara<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 668, "matchups": [{"opponent": "ho_oh_shadow", "rating": 931, "opRating": 68}, {"opponent": "ho_oh", "rating": 921, "opRating": 78}, {"opponent": "moltres_shadow", "rating": 921, "opRating": 78}, {"opponent": "moltres", "rating": 921, "opRating": 78}, {"opponent": "zap<PERSON>_shadow", "rating": 828, "opRating": 171}], "counters": [{"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "dialga", "rating": 347}, {"opponent": "mewtwo", "rating": 453}, {"opponent": "gyarados", "rating": 484}, {"opponent": "lugia", "rating": 485}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31159}, {"moveId": "WATER_GUN", "uses": 26712}, {"moveId": "MUD_SLAP", "uses": 18655}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26407}, {"moveId": "CROSS_CHOP", "uses": 24123}, {"moveId": "GRASS_KNOT", "uses": 16312}, {"moveId": "SKULL_BASH", "uses": 9642}]}, "moveset": ["FURY_CUTTER", "STONE_EDGE", "CROSS_CHOP"], "score": 60.7}, {"speciesId": "latias_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 677, "matchups": [{"opponent": "haxorus", "rating": 851, "opRating": 148}, {"opponent": "darmanitan_standard", "rating": 851, "opRating": 148}, {"opponent": "kyogre", "rating": 619, "opRating": 380}, {"opponent": "swampert", "rating": 569}, {"opponent": "snorlax", "rating": 569, "opRating": 430}], "counters": [{"opponent": "mewtwo", "rating": 328}, {"opponent": "dialga", "rating": 342}, {"opponent": "giratina_origin", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47335}, {"moveId": "CHARM", "uses": 22888}, {"moveId": "ZEN_HEADBUTT", "uses": 6266}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 24405}, {"moveId": "OUTRAGE", "uses": 22057}, {"moveId": "PSYCHIC", "uses": 16223}, {"moveId": "THUNDER", "uses": 13740}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 60.7}, {"speciesId": "cofagrigus", "speciesName": "<PERSON><PERSON>g<PERSON><PERSON>", "rating": 608, "matchups": [{"opponent": "al<PERSON><PERSON>_shadow", "rating": 888, "opRating": 111}, {"opponent": "metagross", "rating": 672}, {"opponent": "zacian_hero", "rating": 597}, {"opponent": "lugia", "rating": 543}, {"opponent": "sylveon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "giratina_origin", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "gyarados", "rating": 350}, {"opponent": "dialga", "rating": 355}, {"opponent": "mewtwo", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 56165}, {"moveId": "ASTONISH", "uses": 14593}, {"moveId": "ZEN_HEADBUTT", "uses": 5765}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 35645}, {"moveId": "DARK_PULSE", "uses": 23270}, {"moveId": "PSYCHIC", "uses": 17496}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "DARK_PULSE"], "score": 60.6}, {"speciesId": "<PERSON><PERSON>_<PERSON>", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 684, "matchups": [{"opponent": "charizard", "rating": 804, "opRating": 195}, {"opponent": "moltres_shadow", "rating": 771, "opRating": 228}, {"opponent": "ho_oh", "rating": 707, "opRating": 292}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509, "opRating": 490}, {"opponent": "yveltal", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "mewtwo", "rating": 359}, {"opponent": "lugia", "rating": 402}, {"opponent": "zacian_hero", "rating": 488}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50842}, {"moveId": "STRUGGLE_BUG", "uses": 25658}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 33870}, {"moveId": "CROSS_POISON", "uses": 31392}, {"moveId": "WATER_PULSE", "uses": 11121}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 60.2}, {"speciesId": "crustle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 668, "matchups": [{"opponent": "ho_oh", "rating": 729, "opRating": 270}, {"opponent": "yveltal", "rating": 608, "opRating": 391}, {"opponent": "snorlax", "rating": 605, "opRating": 394}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550, "opRating": 449}, {"opponent": "mewtwo", "rating": 515}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "metagross", "rating": 345}, {"opponent": "lugia", "rating": 359}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40723}, {"moveId": "SMACK_DOWN", "uses": 35777}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28866}, {"moveId": "X_SCISSOR", "uses": 26873}, {"moveId": "ROCK_BLAST", "uses": 20690}]}, "moveset": ["FURY_CUTTER", "ROCK_SLIDE", "X_SCISSOR"], "score": 60.2}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 599, "matchups": [{"opponent": "metagross", "rating": 714}, {"opponent": "excadrill", "rating": 595}, {"opponent": "zacian_hero", "rating": 546}, {"opponent": "dialga", "rating": 508}, {"opponent": "zekrom", "rating": 508}], "counters": [{"opponent": "gyarados", "rating": 180}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "grou<PERSON>", "rating": 282}, {"opponent": "giratina_origin", "rating": 294}, {"opponent": "mewtwo", "rating": 361}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 49448}, {"moveId": "ASTONISH", "uses": 27052}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 32163}, {"moveId": "EARTH_POWER", "uses": 23031}, {"moveId": "DYNAMIC_PUNCH", "uses": 21378}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "EARTH_POWER"], "score": 60.2}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 614, "matchups": [{"opponent": "electivire_shadow", "rating": 947, "opRating": 52}, {"opponent": "magnezone_shadow", "rating": 920, "opRating": 79}, {"opponent": "excadrill", "rating": 653}, {"opponent": "metagross", "rating": 622}, {"opponent": "dialga", "rating": 507}], "counters": [{"opponent": "lugia", "rating": 214}, {"opponent": "giratina_origin", "rating": 227}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "zacian_hero", "rating": 367}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41919}, {"moveId": "BUBBLE", "uses": 34581}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 30090}, {"moveId": "MUDDY_WATER", "uses": 26523}, {"moveId": "SLUDGE_BOMB", "uses": 19914}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "MUDDY_WATER"], "score": 60.2}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 642, "matchups": [{"opponent": "swampert", "rating": 956}, {"opponent": "kyogre", "rating": 713, "opRating": 286}, {"opponent": "excadrill", "rating": 672}, {"opponent": "grou<PERSON>", "rating": 646}, {"opponent": "garcho<PERSON>", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "metagross", "rating": 252}, {"opponent": "gyarados", "rating": 342}, {"opponent": "zacian_hero", "rating": 433}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 26174}, {"moveId": "CONFUSION", "uses": 25132}, {"moveId": "EXTRASENSORY", "uses": 19707}, {"moveId": "ZEN_HEADBUTT", "uses": 5512}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26829}, {"moveId": "PSYCHIC", "uses": 24512}, {"moveId": "RETURN", "uses": 12888}, {"moveId": "SOLAR_BEAM", "uses": 12221}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 60.1}, {"speciesId": "gran<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 602, "matchups": [{"opponent": "dialga", "rating": 739}, {"opponent": "latios", "rating": 690, "opRating": 309}, {"opponent": "latios_shadow", "rating": 674, "opRating": 325}, {"opponent": "excadrill", "rating": 510}, {"opponent": "metagross", "rating": 508}], "counters": [{"opponent": "garcho<PERSON>", "rating": 194}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "gyarados", "rating": 384}, {"opponent": "dragonite", "rating": 390}, {"opponent": "lugia", "rating": 480}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 35505}, {"moveId": "CHARM", "uses": 26938}, {"moveId": "BITE", "uses": 13991}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30805}, {"moveId": "CRUNCH", "uses": 22882}, {"moveId": "PLAY_ROUGH", "uses": 14507}, {"moveId": "RETURN", "uses": 8431}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 60.1}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 673, "matchups": [{"opponent": "gyarados", "rating": 742}, {"opponent": "metagross", "rating": 729}, {"opponent": "ho_oh", "rating": 662, "opRating": 337}, {"opponent": "dragonite", "rating": 528}, {"opponent": "kyogre", "rating": 512, "opRating": 487}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "dialga", "rating": 192}, {"opponent": "giratina_origin", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 373}, {"opponent": "lugia", "rating": 445}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 38620}, {"moveId": "CHARGE_BEAM", "uses": 20547}, {"moveId": "THUNDER_FANG", "uses": 17326}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 33055}, {"moveId": "PSYCHIC_FANGS", "uses": 17184}, {"moveId": "OVERHEAT", "uses": 10835}, {"moveId": "RETURN", "uses": 6611}, {"moveId": "THUNDER", "uses": 5173}, {"moveId": "FLAME_BURST", "uses": 3610}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 60.1}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 633, "matchups": [{"opponent": "excadrill", "rating": 835}, {"opponent": "metagross", "rating": 828}, {"opponent": "genesect_douse", "rating": 748, "opRating": 251}, {"opponent": "sylveon", "rating": 661, "opRating": 338}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 590, "opRating": 409}], "counters": [{"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "gyarados", "rating": 306}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32178}, {"moveId": "FIRE_SPIN", "uses": 20146}, {"moveId": "EMBER", "uses": 19637}, {"moveId": "LOW_KICK", "uses": 4625}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32922}, {"moveId": "FLAME_CHARGE", "uses": 27443}, {"moveId": "FIRE_BLAST", "uses": 12390}, {"moveId": "HEAT_WAVE", "uses": 3688}]}, "moveset": ["INCINERATE", "DRILL_RUN", "FLAME_CHARGE"], "score": 60}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 639, "matchups": [{"opponent": "darmanitan_standard", "rating": 805, "opRating": 194}, {"opponent": "chandelure", "rating": 792, "opRating": 207}, {"opponent": "mewtwo", "rating": 678}, {"opponent": "mewtwo_shadow", "rating": 636, "opRating": 363}, {"opponent": "giratina_origin", "rating": 595}], "counters": [{"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "dialga", "rating": 304}, {"opponent": "lugia", "rating": 330}, {"opponent": "gyarados", "rating": 376}, {"opponent": "metagross", "rating": 453}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24538}, {"moveId": "INFESTATION", "uses": 18922}, {"moveId": "ICE_FANG", "uses": 17811}, {"moveId": "BITE", "uses": 15182}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28373}, {"moveId": "AQUA_TAIL", "uses": 20669}, {"moveId": "SLUDGE_BOMB", "uses": 15416}, {"moveId": "RETURN", "uses": 7092}, {"moveId": "FELL_STINGER", "uses": 5018}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 59.7}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 605, "matchups": [{"opponent": "mew", "rating": 907, "opRating": 92}, {"opponent": "giratina_origin", "rating": 721}, {"opponent": "gyarados", "rating": 587}, {"opponent": "giratina_altered", "rating": 553, "opRating": 446}, {"opponent": "mewtwo", "rating": 522}], "counters": [{"opponent": "dialga", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "dragonite", "rating": 382}, {"opponent": "lugia", "rating": 442}, {"opponent": "metagross", "rating": 459}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 35916}, {"moveId": "ICE_FANG", "uses": 32011}, {"moveId": "TAKE_DOWN", "uses": 8611}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35404}, {"moveId": "CRUNCH", "uses": 27741}, {"moveId": "PLAY_ROUGH", "uses": 13436}]}, "moveset": ["LICK", "WILD_CHARGE", "CRUNCH"], "score": 59.7}, {"speciesId": "rapidash_galarian", "speciesName": "Rapidash (Galarian)", "rating": 622, "matchups": [{"opponent": "mewtwo_shadow", "rating": 862, "opRating": 137}, {"opponent": "latios_shadow", "rating": 828, "opRating": 171}, {"opponent": "zekrom", "rating": 795, "opRating": 204}, {"opponent": "dragonite", "rating": 701}, {"opponent": "palkia", "rating": 563, "opRating": 436}], "counters": [{"opponent": "dialga", "rating": 380}, {"opponent": "garcho<PERSON>", "rating": 384}, {"opponent": "lugia", "rating": 395}, {"opponent": "mewtwo", "rating": 424}, {"opponent": "gyarados", "rating": 474}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 36326}, {"moveId": "PSYCHO_CUT", "uses": 32415}, {"moveId": "LOW_KICK", "uses": 7743}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27416}, {"moveId": "MEGAHORN", "uses": 17179}, {"moveId": "PSYCHIC", "uses": 16831}, {"moveId": "PLAY_ROUGH", "uses": 15031}]}, "moveset": ["FAIRY_WIND", "BODY_SLAM", "MEGAHORN"], "score": 59.4}, {"speciesId": "scrafty", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 616, "matchups": [{"opponent": "weavile_shadow", "rating": 963, "opRating": 36}, {"opponent": "porygon_z", "rating": 932, "opRating": 67}, {"opponent": "magnezone_shadow", "rating": 758, "opRating": 241}, {"opponent": "excadrill", "rating": 634}, {"opponent": "dialga", "rating": 567}], "counters": [{"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "metagross", "rating": 392}, {"opponent": "giratina_origin", "rating": 428}, {"opponent": "gyarados", "rating": 430}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43429}, {"moveId": "SNARL", "uses": 33071}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 50723}, {"moveId": "POWER_UP_PUNCH", "uses": 19088}, {"moveId": "ACID_SPRAY", "uses": 6745}]}, "moveset": ["COUNTER", "FOUL_PLAY", "POWER_UP_PUNCH"], "score": 59.4}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 592, "matchups": [{"opponent": "mewtwo_shadow", "rating": 889, "opRating": 110}, {"opponent": "swampert", "rating": 785}, {"opponent": "zacian_hero", "rating": 583}, {"opponent": "kyogre", "rating": 513, "opRating": 486}, {"opponent": "excadrill", "rating": 506}], "counters": [{"opponent": "gyarados", "rating": 309}, {"opponent": "dialga", "rating": 342}, {"opponent": "metagross", "rating": 462}, {"opponent": "mewtwo", "rating": 484}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49366}, {"moveId": "RAZOR_LEAF", "uses": 27134}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26353}, {"moveId": "SEED_BOMB", "uses": 21721}, {"moveId": "FOUL_PLAY", "uses": 19747}, {"moveId": "FIRE_BLAST", "uses": 8558}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 59.2}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 676, "matchups": [{"opponent": "sylveon", "rating": 757, "opRating": 242}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 733}, {"opponent": "zacian_hero", "rating": 675}, {"opponent": "yveltal", "rating": 625, "opRating": 375}, {"opponent": "gyarados", "rating": 572}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "dragonite", "rating": 377}, {"opponent": "lugia", "rating": 407}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26723}, {"moveId": "LICK", "uses": 20141}, {"moveId": "INFESTATION", "uses": 19380}, {"moveId": "ACID", "uses": 10309}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 23324}, {"moveId": "THUNDER_PUNCH", "uses": 20187}, {"moveId": "SLUDGE_WAVE", "uses": 13034}, {"moveId": "RETURN", "uses": 10273}, {"moveId": "GUNK_SHOT", "uses": 5125}, {"moveId": "ACID_SPRAY", "uses": 4431}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 59.2}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 649, "matchups": [{"opponent": "zarude", "rating": 931, "opRating": 68}, {"opponent": "mewtwo_shadow", "rating": 888, "opRating": 111}, {"opponent": "darkrai", "rating": 866, "opRating": 133}, {"opponent": "latios", "rating": 715, "opRating": 284}, {"opponent": "mewtwo", "rating": 503}], "counters": [{"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "dialga", "rating": 301}, {"opponent": "gyarados", "rating": 453}, {"opponent": "metagross", "rating": 488}, {"opponent": "zacian_hero", "rating": 491}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 43363}, {"moveId": "METAL_CLAW", "uses": 33137}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 32678}, {"moveId": "STONE_EDGE", "uses": 23841}, {"moveId": "IRON_HEAD", "uses": 19962}]}, "moveset": ["BUG_BITE", "X_SCISSOR", "STONE_EDGE"], "score": 59.1}, {"speciesId": "magneton_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 639, "matchups": [{"opponent": "gyarados", "rating": 818}, {"opponent": "gyarado<PERSON>_shadow", "rating": 759, "opRating": 240}, {"opponent": "sylveon", "rating": 618, "opRating": 381}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 586, "opRating": 413}, {"opponent": "lugia", "rating": 527}], "counters": [{"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "dialga", "rating": 323}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 31053}, {"moveId": "SPARK", "uses": 28122}, {"moveId": "CHARGE_BEAM", "uses": 17363}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 32058}, {"moveId": "DISCHARGE", "uses": 27056}, {"moveId": "ZAP_CANNON", "uses": 10577}, {"moveId": "FLASH_CANNON", "uses": 6798}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 59.1}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 580, "matchups": [{"opponent": "swampert", "rating": 948}, {"opponent": "excadrill", "rating": 725}, {"opponent": "gyarados", "rating": 643}, {"opponent": "zacian_hero", "rating": 548}, {"opponent": "mewtwo", "rating": 518}], "counters": [{"opponent": "metagross", "rating": 188}, {"opponent": "lugia", "rating": 197}, {"opponent": "dialga", "rating": 214}, {"opponent": "grou<PERSON>", "rating": 440}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 65293}, {"moveId": "IRON_TAIL", "uses": 11207}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 34938}, {"moveId": "LEAF_TORNADO", "uses": 17955}, {"moveId": "AERIAL_ACE", "uses": 14225}, {"moveId": "GRASS_KNOT", "uses": 9214}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "LEAF_TORNADO"], "score": 59.1}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 601, "matchups": [{"opponent": "excadrill", "rating": 943}, {"opponent": "bewear", "rating": 879, "opRating": 120}, {"opponent": "zarude", "rating": 783, "opRating": 216}, {"opponent": "zacian_hero", "rating": 705}, {"opponent": "giratina_origin", "rating": 522}], "counters": [{"opponent": "lugia", "rating": 295}, {"opponent": "metagross", "rating": 311}, {"opponent": "dialga", "rating": 413}, {"opponent": "garcho<PERSON>", "rating": 429}, {"opponent": "gyarados", "rating": 481}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30898}, {"moveId": "GUST", "uses": 23543}, {"moveId": "WING_ATTACK", "uses": 21975}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33305}, {"moveId": "CLOSE_COMBAT", "uses": 28806}, {"moveId": "RETURN", "uses": 10487}, {"moveId": "HEAT_WAVE", "uses": 3916}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 59.1}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 604, "matchups": [{"opponent": "giratina_origin", "rating": 775}, {"opponent": "garcho<PERSON>", "rating": 631}, {"opponent": "lugia", "rating": 616}, {"opponent": "grou<PERSON>", "rating": 553}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537, "opRating": 462}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "dialga", "rating": 241}, {"opponent": "gyarados", "rating": 304}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "dragonite", "rating": 470}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5903}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5574}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 5211}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 5080}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5074}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4994}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4913}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4828}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4503}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4499}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4441}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4143}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4086}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4055}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3901}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3801}, {"moveId": "ZEN_HEADBUTT", "uses": 1264}], "chargedMoves": [{"moveId": "THUNDER", "uses": 27031}, {"moveId": "FOCUS_BLAST", "uses": 26081}, {"moveId": "GIGA_IMPACT", "uses": 23494}]}, "moveset": ["HIDDEN_POWER_ICE", "THUNDER", "FOCUS_BLAST"], "score": 59}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 589, "matchups": [{"opponent": "articuno_galarian", "rating": 823, "opRating": 176}, {"opponent": "metagross", "rating": 672}, {"opponent": "zacian_hero", "rating": 597}, {"opponent": "sylveon", "rating": 521, "opRating": 478}, {"opponent": "lugia", "rating": 517}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "gyarados", "rating": 337}, {"opponent": "dialga", "rating": 355}, {"opponent": "giratina_origin", "rating": 374}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 60579}, {"moveId": "ASTONISH", "uses": 15921}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 41479}, {"moveId": "ROCK_TOMB", "uses": 17588}, {"moveId": "SAND_TOMB", "uses": 17433}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "ROCK_TOMB"], "score": 59}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 662, "matchups": [{"opponent": "pangoro", "rating": 900, "opRating": 99}, {"opponent": "excadrill", "rating": 895}, {"opponent": "porygon_z", "rating": 892, "opRating": 107}, {"opponent": "ma<PERSON><PERSON>", "rating": 819, "opRating": 180}, {"opponent": "giratina_origin", "rating": 655}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "lugia", "rating": 369}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34155}, {"moveId": "COUNTER", "uses": 30630}, {"moveId": "METAL_CLAW", "uses": 11723}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 38934}, {"moveId": "RETURN", "uses": 16501}, {"moveId": "PLAY_ROUGH", "uses": 14693}, {"moveId": "HYPER_BEAM", "uses": 6382}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "RETURN"], "score": 58.9}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 607, "matchups": [{"opponent": "gengar", "rating": 872, "opRating": 127}, {"opponent": "electivire_shadow", "rating": 732, "opRating": 267}, {"opponent": "giratina_origin", "rating": 653}, {"opponent": "sylveon", "rating": 541, "opRating": 458}, {"opponent": "giratina_altered", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 326}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "gyarados", "rating": 394}, {"opponent": "metagross", "rating": 485}, {"opponent": "mewtwo", "rating": 486}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66153}, {"moveId": "ZEN_HEADBUTT", "uses": 10347}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35299}, {"moveId": "SHADOW_BALL", "uses": 17283}, {"moveId": "EARTHQUAKE", "uses": 13378}, {"moveId": "SOLAR_BEAM", "uses": 6581}, {"moveId": "HYPER_BEAM", "uses": 3914}]}, "moveset": ["LICK", "BODY_SLAM", "SHADOW_BALL"], "score": 58.8}, {"speciesId": "omastar", "speciesName": "Omastar", "rating": 637, "matchups": [{"opponent": "ho_oh", "rating": 920, "opRating": 79}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 640, "opRating": 359}, {"opponent": "yveltal", "rating": 624, "opRating": 375}, {"opponent": "lugia", "rating": 538}, {"opponent": "gyarados", "rating": 531}], "counters": [{"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "metagross", "rating": 313}, {"opponent": "dialga", "rating": 361}, {"opponent": "mewtwo", "rating": 390}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29309}, {"moveId": "ROCK_THROW", "uses": 23586}, {"moveId": "WATER_GUN", "uses": 23574}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26862}, {"moveId": "ROCK_BLAST", "uses": 19274}, {"moveId": "HYDRO_PUMP", "uses": 12267}, {"moveId": "ANCIENT_POWER", "uses": 9730}, {"moveId": "RETURN", "uses": 8428}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 58.8}, {"speciesId": "scyther_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 621, "matchups": [{"opponent": "zarude", "rating": 901, "opRating": 98}, {"opponent": "tangrowth_shadow", "rating": 891, "opRating": 108}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "mewtwo_shadow", "rating": 805, "opRating": 194}, {"opponent": "metagross", "rating": 595}], "counters": [{"opponent": "dialga", "rating": 355}, {"opponent": "gyarados", "rating": 358}, {"opponent": "giratina_origin", "rating": 388}, {"opponent": "garcho<PERSON>", "rating": 403}, {"opponent": "mewtwo", "rating": 419}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35920}, {"moveId": "AIR_SLASH", "uses": 25079}, {"moveId": "STEEL_WING", "uses": 15427}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28329}, {"moveId": "X_SCISSOR", "uses": 20071}, {"moveId": "AERIAL_ACE", "uses": 14973}, {"moveId": "BUG_BUZZ", "uses": 13072}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 58.8}, {"speciesId": "falinks", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 619, "matchups": [{"opponent": "darkrai", "rating": 902, "opRating": 97}, {"opponent": "excadrill", "rating": 872}, {"opponent": "ma<PERSON><PERSON>", "rating": 812, "opRating": 187}, {"opponent": "dialga", "rating": 627}, {"opponent": "snorlax", "rating": 510, "opRating": 489}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "zacian_hero", "rating": 263}, {"opponent": "gyarados", "rating": 265}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "metagross", "rating": 375}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 62419}, {"moveId": "ROCK_SMASH", "uses": 14081}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34667}, {"moveId": "MEGAHORN", "uses": 20915}, {"moveId": "BRICK_BREAK", "uses": 20863}]}, "moveset": ["COUNTER", "SUPER_POWER", "MEGAHORN"], "score": 58.6}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 637, "matchups": [{"opponent": "genesect_shock", "rating": 927, "opRating": 72}, {"opponent": "genesect_chill", "rating": 927, "opRating": 72}, {"opponent": "genesect_burn", "rating": 927, "opRating": 72}, {"opponent": "metagross", "rating": 816}, {"opponent": "dialga", "rating": 626}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "giratina_origin", "rating": 223}, {"opponent": "lugia", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "excadrill", "rating": 311}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 58950}, {"moveId": "ROCK_SMASH", "uses": 17550}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 33667}, {"moveId": "BLAST_BURN", "uses": 29733}, {"moveId": "FLAMETHROWER", "uses": 6852}, {"moveId": "SOLAR_BEAM", "uses": 6250}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 58.1}, {"speciesId": "kingler", "speciesName": "<PERSON><PERSON>", "rating": 667, "matchups": [{"opponent": "excadrill", "rating": 892}, {"opponent": "mamos<PERSON>_shadow", "rating": 877, "opRating": 122}, {"opponent": "darmanitan_standard", "rating": 874, "opRating": 125}, {"opponent": "ho_oh", "rating": 774, "opRating": 225}, {"opponent": "sylveon", "rating": 529, "opRating": 470}], "counters": [{"opponent": "mewtwo", "rating": 231}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "dialga", "rating": 271}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "metagross", "rating": 447}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32009}, {"moveId": "BUBBLE", "uses": 28570}, {"moveId": "METAL_CLAW", "uses": 15904}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 35508}, {"moveId": "X_SCISSOR", "uses": 24814}, {"moveId": "VICE_GRIP", "uses": 11044}, {"moveId": "WATER_PULSE", "uses": 5143}]}, "moveset": ["MUD_SHOT", "CRABHAMMER", "X_SCISSOR"], "score": 58.1}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 636, "matchups": [{"opponent": "swampert", "rating": 773}, {"opponent": "kyogre", "rating": 773, "opRating": 226}, {"opponent": "excadrill", "rating": 662}, {"opponent": "grou<PERSON>", "rating": 608}, {"opponent": "snorlax", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 244}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "metagross", "rating": 345}, {"opponent": "gyarados", "rating": 363}, {"opponent": "garcho<PERSON>", "rating": 377}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51108}, {"moveId": "RAZOR_LEAF", "uses": 25392}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37386}, {"moveId": "SUPER_POWER", "uses": 20536}, {"moveId": "X_SCISSOR", "uses": 13997}, {"moveId": "LEAF_STORM", "uses": 4639}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 58.1}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 650, "matchups": [{"opponent": "charizard_shadow", "rating": 804, "opRating": 195}, {"opponent": "zarude", "rating": 795, "opRating": 204}, {"opponent": "ho_oh", "rating": 753, "opRating": 246}, {"opponent": "zacian_hero", "rating": 554}, {"opponent": "sylveon", "rating": 539, "opRating": 460}], "counters": [{"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "metagross", "rating": 305}, {"opponent": "dialga", "rating": 312}, {"opponent": "lugia", "rating": 359}, {"opponent": "mewtwo", "rating": 364}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51316}, {"moveId": "STRUGGLE_BUG", "uses": 25184}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 29060}, {"moveId": "CROSS_POISON", "uses": 26919}, {"moveId": "RETURN", "uses": 11288}, {"moveId": "WATER_PULSE", "uses": 9273}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 57.9}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 629, "matchups": [{"opponent": "gyarados", "rating": 714}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}, {"opponent": "lugia", "rating": 563}, {"opponent": "yveltal", "rating": 553, "opRating": 446}, {"opponent": "metagross", "rating": 516}], "counters": [{"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "dialga", "rating": 230}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "dragonite", "rating": 335}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43368}, {"moveId": "THUNDER_SHOCK", "uses": 33132}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 26609}, {"moveId": "LAST_RESORT", "uses": 19099}, {"moveId": "THUNDERBOLT", "uses": 11483}, {"moveId": "THUNDER", "uses": 9978}, {"moveId": "ZAP_CANNON", "uses": 9570}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LAST_RESORT"], "score": 57.7}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 627, "matchups": [{"opponent": "yveltal", "rating": 879, "opRating": 120}, {"opponent": "ho_oh", "rating": 714, "opRating": 285}, {"opponent": "zacian_hero", "rating": 667}, {"opponent": "gyarados", "rating": 644}, {"opponent": "dragonite", "rating": 503}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "dialga", "rating": 250}, {"opponent": "metagross", "rating": 287}, {"opponent": "lugia", "rating": 354}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 58831}, {"moveId": "LOW_KICK", "uses": 17669}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 28765}, {"moveId": "STONE_EDGE", "uses": 26236}, {"moveId": "GRASS_KNOT", "uses": 21421}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 57.7}, {"speciesId": "relicanth", "speciesName": "Relicanth", "rating": 615, "matchups": [{"opponent": "ho_oh", "rating": 937, "opRating": 62}, {"opponent": "ho_oh_shadow", "rating": 927, "opRating": 72}, {"opponent": "zacian_hero", "rating": 549}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 544, "opRating": 455}, {"opponent": "excadrill", "rating": 514}], "counters": [{"opponent": "dialga", "rating": 290}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "gyarados", "rating": 358}, {"opponent": "metagross", "rating": 363}, {"opponent": "lugia", "rating": 388}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66192}, {"moveId": "ZEN_HEADBUTT", "uses": 10308}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 40571}, {"moveId": "ANCIENT_POWER", "uses": 29639}, {"moveId": "HYDRO_PUMP", "uses": 6318}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "ANCIENT_POWER"], "score": 57.7}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 577, "matchups": [{"opponent": "xurkitree", "rating": 941, "opRating": 58}, {"opponent": "magnezone_shadow", "rating": 885, "opRating": 114}, {"opponent": "dialga", "rating": 612}, {"opponent": "gyarados", "rating": 565}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "opRating": 478}], "counters": [{"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "metagross", "rating": 395}, {"opponent": "excadrill", "rating": 483}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38612}, {"moveId": "THUNDER_SHOCK", "uses": 37888}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 33841}, {"moveId": "DISCHARGE", "uses": 26057}, {"moveId": "MUDDY_WATER", "uses": 16592}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "DISCHARGE"], "score": 57.4}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 598, "matchups": [{"opponent": "metagross_shadow", "rating": 783, "opRating": 216}, {"opponent": "mewtwo", "rating": 667}, {"opponent": "mewtwo_shadow", "rating": 637, "opRating": 362}, {"opponent": "excadrill", "rating": 625}, {"opponent": "sylveon", "rating": 542, "opRating": 457}], "counters": [{"opponent": "metagross", "rating": 238}, {"opponent": "gyarados", "rating": 270}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "zacian_hero", "rating": 471}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47806}, {"moveId": "STRUGGLE_BUG", "uses": 28694}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 18203}, {"moveId": "EARTHQUAKE", "uses": 15505}, {"moveId": "HEAVY_SLAM", "uses": 14782}, {"moveId": "ROCK_TOMB", "uses": 11353}, {"moveId": "RETURN", "uses": 8910}, {"moveId": "SAND_TOMB", "uses": 7655}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 57.3}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 570, "matchups": [{"opponent": "magnezone_shadow", "rating": 904, "opRating": 95}, {"opponent": "nihilego", "rating": 830, "opRating": 169}, {"opponent": "dragonite", "rating": 704}, {"opponent": "excadrill", "rating": 616}, {"opponent": "dialga", "rating": 537}], "counters": [{"opponent": "lugia", "rating": 223}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "metagross", "rating": 406}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41989}, {"moveId": "WATER_GUN", "uses": 34511}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 41466}, {"moveId": "BLIZZARD", "uses": 22842}, {"moveId": "WATER_PULSE", "uses": 12195}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "BLIZZARD"], "score": 57.3}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 626, "matchups": [{"opponent": "gyarados", "rating": 818}, {"opponent": "sylveon", "rating": 681, "opRating": 318}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "lugia", "rating": 610}, {"opponent": "kyogre", "rating": 523, "opRating": 476}], "counters": [{"opponent": "mewtwo", "rating": 260}, {"opponent": "dialga", "rating": 279}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "metagross", "rating": 424}, {"opponent": "dragonite", "rating": 428}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30723}, {"moveId": "SPARK", "uses": 28187}, {"moveId": "CHARGE_BEAM", "uses": 17581}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 27312}, {"moveId": "DISCHARGE", "uses": 23733}, {"moveId": "RETURN", "uses": 10733}, {"moveId": "ZAP_CANNON", "uses": 9084}, {"moveId": "FLASH_CANNON", "uses": 5827}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 57}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 611, "matchups": [{"opponent": "zarude", "rating": 826, "opRating": 173}, {"opponent": "snorlax", "rating": 792, "opRating": 207}, {"opponent": "cobalion", "rating": 744, "opRating": 255}, {"opponent": "yveltal", "rating": 627, "opRating": 372}, {"opponent": "dialga", "rating": 519}], "counters": [{"opponent": "excadrill", "rating": 374}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "zacian_hero", "rating": 424}, {"opponent": "metagross", "rating": 438}, {"opponent": "swampert", "rating": 450}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42189}, {"moveId": "POISON_JAB", "uses": 34311}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 28884}, {"moveId": "SLUDGE_BOMB", "uses": 24636}, {"moveId": "MUD_BOMB", "uses": 23008}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "SLUDGE_BOMB"], "score": 57}, {"speciesId": "dewgong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 538, "matchups": [{"opponent": "dragonite", "rating": 712}, {"opponent": "garcho<PERSON>", "rating": 655}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "grou<PERSON>", "rating": 561}, {"opponent": "giratina_origin", "rating": 505}], "counters": [{"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 323}, {"opponent": "lugia", "rating": 345}, {"opponent": "gyarados", "rating": 389}, {"opponent": "excadrill", "rating": 460}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38783}, {"moveId": "FROST_BREATH", "uses": 29661}, {"moveId": "IRON_TAIL", "uses": 8056}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 36267}, {"moveId": "BLIZZARD", "uses": 16155}, {"moveId": "AQUA_JET", "uses": 12695}, {"moveId": "AURORA_BEAM", "uses": 6481}, {"moveId": "WATER_PULSE", "uses": 4743}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "BLIZZARD"], "score": 56.9}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 632, "matchups": [{"opponent": "gyarados", "rating": 848}, {"opponent": "yveltal", "rating": 845, "opRating": 154}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 746}, {"opponent": "sylveon", "rating": 658, "opRating": 341}, {"opponent": "lugia", "rating": 573}], "counters": [{"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "dialga", "rating": 263}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "metagross", "rating": 345}, {"opponent": "mewtwo", "rating": 411}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 48967}, {"moveId": "CHARGE_BEAM", "uses": 27533}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 29933}, {"moveId": "ZAP_CANNON", "uses": 19263}, {"moveId": "FLASH_CANNON", "uses": 15565}, {"moveId": "HYPER_BEAM", "uses": 11765}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 56.9}, {"speciesId": "lycanroc_midnight", "speciesName": "Lycanroc (Midnight)", "rating": 655, "matchups": [{"opponent": "weavile_shadow", "rating": 865, "opRating": 134}, {"opponent": "moltres", "rating": 839, "opRating": 160}, {"opponent": "ho_oh", "rating": 828, "opRating": 171}, {"opponent": "ho_oh_shadow", "rating": 800, "opRating": 199}, {"opponent": "yveltal", "rating": 558, "opRating": 441}], "counters": [{"opponent": "giratina_origin", "rating": 241}, {"opponent": "gyarados", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "lugia", "rating": 302}, {"opponent": "metagross", "rating": 395}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43979}, {"moveId": "ROCK_THROW", "uses": 32521}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26957}, {"moveId": "CRUNCH", "uses": 26472}, {"moveId": "PSYCHIC_FANGS", "uses": 23119}]}, "moveset": ["COUNTER", "STONE_EDGE", "CRUNCH"], "score": 56.9}, {"speciesId": "mandibuzz", "speciesName": "Mandibuzz", "rating": 551, "matchups": [{"opponent": "mewtwo", "rating": 790}, {"opponent": "mewtwo_shadow", "rating": 774, "opRating": 225}, {"opponent": "metagross_shadow", "rating": 769, "opRating": 230}, {"opponent": "giratina_origin", "rating": 630}, {"opponent": "metagross", "rating": 506}], "counters": [{"opponent": "dialga", "rating": 282}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "gyarados", "rating": 412}, {"opponent": "swampert", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44541}, {"moveId": "AIR_SLASH", "uses": 31959}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 28257}, {"moveId": "SHADOW_BALL", "uses": 18643}, {"moveId": "AERIAL_ACE", "uses": 17595}, {"moveId": "DARK_PULSE", "uses": 12092}]}, "moveset": ["SNARL", "FOUL_PLAY", "SHADOW_BALL"], "score": 56.9}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 592, "matchups": [{"opponent": "snorlax", "rating": 682, "opRating": 317}, {"opponent": "zap<PERSON>_shadow", "rating": 670, "opRating": 329}, {"opponent": "giratina_origin", "rating": 634}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 572, "opRating": 427}, {"opponent": "garcho<PERSON>", "rating": 528}], "counters": [{"opponent": "dialga", "rating": 307}, {"opponent": "mewtwo", "rating": 322}, {"opponent": "lugia", "rating": 326}, {"opponent": "dragonite", "rating": 409}, {"opponent": "gyarados", "rating": 432}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 36092}, {"moveId": "TACKLE", "uses": 35273}, {"moveId": "ZEN_HEADBUTT", "uses": 5157}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33455}, {"moveId": "ICE_BEAM", "uses": 16634}, {"moveId": "THUNDERBOLT", "uses": 12096}, {"moveId": "STOMP", "uses": 8259}, {"moveId": "GYRO_BALL", "uses": 6026}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 56.9}, {"speciesId": "crobat_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 828, "opRating": 171}, {"opponent": "buzzwole", "rating": 783, "opRating": 216}, {"opponent": "sylveon", "rating": 657, "opRating": 342}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 626, "opRating": 373}, {"opponent": "zacian_hero", "rating": 536}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "mewtwo", "rating": 296}, {"opponent": "excadrill", "rating": 453}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45736}, {"moveId": "BITE", "uses": 30764}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 26947}, {"moveId": "SHADOW_BALL", "uses": 21744}, {"moveId": "POISON_FANG", "uses": 12080}, {"moveId": "AIR_CUTTER", "uses": 8267}, {"moveId": "SLUDGE_BOMB", "uses": 7310}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 56.8}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 637, "matchups": [{"opponent": "yveltal", "rating": 868, "opRating": 131}, {"opponent": "ho_oh", "rating": 753, "opRating": 246}, {"opponent": "gyarados", "rating": 707}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 634}], "counters": [{"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "mewtwo", "rating": 223}, {"opponent": "excadrill", "rating": 304}, {"opponent": "metagross", "rating": 334}, {"opponent": "dialga", "rating": 364}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 63232}, {"moveId": "LOW_KICK", "uses": 13268}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 45145}, {"moveId": "FLAME_CHARGE", "uses": 21794}, {"moveId": "DISCHARGE", "uses": 9661}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 56.8}, {"speciesId": "politoed", "speciesName": "Politoed", "rating": 593, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 889, "opRating": 110}, {"opponent": "ma<PERSON><PERSON>", "rating": 879, "opRating": 120}, {"opponent": "moltres_shadow", "rating": 846, "opRating": 153}, {"opponent": "excadrill", "rating": 655}, {"opponent": "zacian_hero", "rating": 505}], "counters": [{"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "dialga", "rating": 320}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "metagross", "rating": 438}, {"opponent": "swampert", "rating": 457}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41321}, {"moveId": "BUBBLE", "uses": 35179}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 29640}, {"moveId": "SURF", "uses": 12078}, {"moveId": "BLIZZARD", "uses": 11972}, {"moveId": "EARTHQUAKE", "uses": 11741}, {"moveId": "RETURN", "uses": 7223}, {"moveId": "HYDRO_PUMP", "uses": 3917}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "SURF"], "score": 56.4}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 575, "matchups": [{"opponent": "excadrill", "rating": 943}, {"opponent": "garcho<PERSON>", "rating": 896}, {"opponent": "snor<PERSON>_shadow", "rating": 828, "opRating": 171}, {"opponent": "yveltal", "rating": 752, "opRating": 247}, {"opponent": "zacian_hero", "rating": 648}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "metagross", "rating": 331}, {"opponent": "lugia", "rating": 350}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "grou<PERSON>", "rating": 491}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31808}, {"moveId": "GUST", "uses": 22876}, {"moveId": "WING_ATTACK", "uses": 21780}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 39295}, {"moveId": "CLOSE_COMBAT", "uses": 32705}, {"moveId": "HEAT_WAVE", "uses": 4413}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 56.2}, {"speciesId": "carracosta", "speciesName": "Carracosta", "rating": 612, "matchups": [{"opponent": "ho_oh", "rating": 923, "opRating": 76}, {"opponent": "ho_oh_shadow", "rating": 911, "opRating": 88}, {"opponent": "moltres_shadow", "rating": 904, "opRating": 95}, {"opponent": "excadrill", "rating": 843}, {"opponent": "sylveon", "rating": 558, "opRating": 441}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "metagross", "rating": 305}, {"opponent": "lugia", "rating": 354}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39342}, {"moveId": "ROCK_THROW", "uses": 37158}], "chargedMoves": [{"moveId": "SURF", "uses": 29814}, {"moveId": "BODY_SLAM", "uses": 25690}, {"moveId": "ANCIENT_POWER", "uses": 21027}]}, "moveset": ["WATER_GUN", "SURF", "BODY_SLAM"], "score": 56.1}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 602, "matchups": [{"opponent": "gengar", "rating": 794, "opRating": 205}, {"opponent": "gyarado<PERSON>_shadow", "rating": 674, "opRating": 325}, {"opponent": "zap<PERSON>_shadow", "rating": 648, "opRating": 351}, {"opponent": "metagross", "rating": 561}, {"opponent": "gyarados", "rating": 550}], "counters": [{"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "dialga", "rating": 372}, {"opponent": "lugia", "rating": 411}, {"opponent": "dragonite", "rating": 422}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 57662}, {"moveId": "ACID", "uses": 18838}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27198}, {"moveId": "CRUNCH", "uses": 25144}, {"moveId": "THUNDERBOLT", "uses": 20578}, {"moveId": "ACID_SPRAY", "uses": 3584}]}, "moveset": ["SPARK", "DRAGON_CLAW", "CRUNCH"], "score": 55.9}, {"speciesId": "<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 610, "matchups": [{"opponent": "darmanitan_standard", "rating": 875, "opRating": 125}, {"opponent": "excadrill", "rating": 866}, {"opponent": "mamos<PERSON>_shadow", "rating": 840, "opRating": 159}, {"opponent": "garcho<PERSON>", "rating": 822}, {"opponent": "ma<PERSON><PERSON>", "rating": 822, "opRating": 177}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "dialga", "rating": 288}, {"opponent": "metagross", "rating": 354}, {"opponent": "grou<PERSON>", "rating": 355}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39914}, {"moveId": "CONFUSION", "uses": 36586}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 22969}, {"moveId": "ICE_BEAM", "uses": 18259}, {"moveId": "SYNCHRONOISE", "uses": 11949}, {"moveId": "HYDRO_PUMP", "uses": 10213}, {"moveId": "BUBBLE_BEAM", "uses": 7545}, {"moveId": "PSYCHIC", "uses": 5450}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 55.9}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 611, "matchups": [{"opponent": "genesect_douse", "rating": 895, "opRating": 104}, {"opponent": "genesect_shock", "rating": 895, "opRating": 104}, {"opponent": "excadrill", "rating": 755}, {"opponent": "metagross", "rating": 734}, {"opponent": "sylveon", "rating": 516, "opRating": 483}], "counters": [{"opponent": "gyarados", "rating": 244}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "dialga", "rating": 331}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38518}, {"moveId": "EMBER", "uses": 37982}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 48588}, {"moveId": "FLAMETHROWER", "uses": 18119}, {"moveId": "FIRE_BLAST", "uses": 9887}, {"moveId": "FRUSTRATION", "uses": 7}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "FLAMETHROWER"], "score": 55.9}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 613, "matchups": [{"opponent": "tapu_bulu", "rating": 912, "opRating": 87}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 587, "opRating": 412}, {"opponent": "sylveon", "rating": 582, "opRating": 417}, {"opponent": "giratina_origin", "rating": 558}, {"opponent": "zacian_hero", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "metagross", "rating": 293}, {"opponent": "gyarados", "rating": 378}, {"opponent": "mewtwo", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 46921}, {"moveId": "BITE", "uses": 29579}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34622}, {"moveId": "SLUDGE_BOMB", "uses": 18496}, {"moveId": "FLAMETHROWER", "uses": 14463}, {"moveId": "RETURN", "uses": 8916}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 55.9}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 555, "matchups": [{"opponent": "mewtwo_shadow", "rating": 773, "opRating": 226}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "mewtwo", "rating": 685}, {"opponent": "giratina_origin", "rating": 597}, {"opponent": "metagross", "rating": 536}], "counters": [{"opponent": "dialga", "rating": 282}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "garcho<PERSON>", "rating": 354}, {"opponent": "gyarados", "rating": 396}, {"opponent": "excadrill", "rating": 490}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42131}, {"moveId": "FEINT_ATTACK", "uses": 34369}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 33525}, {"moveId": "PSYCHIC", "uses": 15073}, {"moveId": "DARK_PULSE", "uses": 14252}, {"moveId": "LAST_RESORT", "uses": 13656}]}, "moveset": ["SNARL", "FOUL_PLAY", "PSYCHIC"], "score": 55.9}, {"speciesId": "clefable", "speciesName": "Clefable", "rating": 551, "matchups": [{"opponent": "dragonite", "rating": 739}, {"opponent": "palkia", "rating": 664, "opRating": 335}, {"opponent": "yveltal", "rating": 657, "opRating": 342}, {"opponent": "garcho<PERSON>", "rating": 538}, {"opponent": "zekrom", "rating": 507, "opRating": 492}], "counters": [{"opponent": "mewtwo", "rating": 307}, {"opponent": "dialga", "rating": 331}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 41417}, {"moveId": "CHARGE_BEAM", "uses": 26346}, {"moveId": "ZEN_HEADBUTT", "uses": 5637}, {"moveId": "POUND", "uses": 3093}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28514}, {"moveId": "METEOR_MASH", "uses": 25672}, {"moveId": "PSYCHIC", "uses": 15001}, {"moveId": "DAZZLING_GLEAM", "uses": 7275}]}, "moveset": ["CHARM", "MOONBLAST", "METEOR_MASH"], "score": 55.8}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 594, "matchups": [{"opponent": "ursaring_shadow", "rating": 900, "opRating": 99}, {"opponent": "honchk<PERSON>_shadow", "rating": 859, "opRating": 140}, {"opponent": "snorlax", "rating": 619, "opRating": 380}, {"opponent": "giratina_origin", "rating": 530}, {"opponent": "sylveon", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 285}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "lugia", "rating": 383}, {"opponent": "mewtwo", "rating": 414}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 28637}, {"moveId": "BULLET_SEED", "uses": 28581}, {"moveId": "BITE", "uses": 19255}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 47796}, {"moveId": "CRUNCH", "uses": 28704}]}, "moveset": ["TACKLE", "BODY_SLAM", "CRUNCH"], "score": 55.7}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 600, "matchups": [{"opponent": "metagross_shadow", "rating": 729, "opRating": 270}, {"opponent": "excadrill", "rating": 699}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 649, "opRating": 350}, {"opponent": "giratina_origin", "rating": 585}, {"opponent": "zacian_hero", "rating": 519}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "gyarados", "rating": 394}, {"opponent": "mewtwo", "rating": 403}, {"opponent": "swampert", "rating": 405}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 76500}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 42568}, {"moveId": "EARTHQUAKE", "uses": 17056}, {"moveId": "PLAY_ROUGH", "uses": 12079}, {"moveId": "HYPER_BEAM", "uses": 4742}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 55.7}, {"speciesId": "golduck", "speciesName": "Gold<PERSON>", "rating": 596, "matchups": [{"opponent": "garcho<PERSON>", "rating": 857}, {"opponent": "mamos<PERSON>_shadow", "rating": 822, "opRating": 177}, {"opponent": "moltres_shadow", "rating": 811, "opRating": 188}, {"opponent": "excadrill", "rating": 578}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "dialga", "rating": 285}, {"opponent": "lugia", "rating": 292}, {"opponent": "metagross", "rating": 357}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41620}, {"moveId": "CONFUSION", "uses": 34880}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 21099}, {"moveId": "ICE_BEAM", "uses": 16447}, {"moveId": "SYNCHRONOISE", "uses": 10706}, {"moveId": "HYDRO_PUMP", "uses": 9228}, {"moveId": "RETURN", "uses": 7188}, {"moveId": "BUBBLE_BEAM", "uses": 6871}, {"moveId": "PSYCHIC", "uses": 4864}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 55.6}, {"speciesId": "kabutops", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 617, "matchups": [{"opponent": "ho_oh", "rating": 933, "opRating": 66}, {"opponent": "ho_oh_shadow", "rating": 922, "opRating": 77}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "opRating": 478}, {"opponent": "yveltal", "rating": 514, "opRating": 485}, {"opponent": "sylveon", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 218}, {"opponent": "dialga", "rating": 293}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "lugia", "rating": 390}, {"opponent": "gyarados", "rating": 409}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 25552}, {"moveId": "FURY_CUTTER", "uses": 22789}, {"moveId": "WATERFALL", "uses": 20482}, {"moveId": "ROCK_SMASH", "uses": 7635}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 32979}, {"moveId": "ANCIENT_POWER", "uses": 29722}, {"moveId": "WATER_PULSE", "uses": 13791}]}, "moveset": ["MUD_SHOT", "STONE_EDGE", "ANCIENT_POWER"], "score": 55.3}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 604, "matchups": [{"opponent": "zarude", "rating": 896, "opRating": 103}, {"opponent": "xurkitree", "rating": 878, "opRating": 121}, {"opponent": "swampert", "rating": 792}, {"opponent": "kyogre", "rating": 692, "opRating": 307}, {"opponent": "excadrill", "rating": 545}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "metagross", "rating": 348}, {"opponent": "mewtwo", "rating": 414}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 44803}, {"moveId": "RAZOR_LEAF", "uses": 31697}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42053}, {"moveId": "X_SCISSOR", "uses": 21423}, {"moveId": "SILVER_WIND", "uses": 7735}, {"moveId": "LEAF_STORM", "uses": 5158}]}, "moveset": ["BUG_BITE", "LEAF_BLADE", "X_SCISSOR"], "score": 55.3}, {"speciesId": "absol_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 555, "matchups": [{"opponent": "mewtwo", "rating": 906}, {"opponent": "articuno_galarian", "rating": 906, "opRating": 93}, {"opponent": "mewtwo_shadow", "rating": 862, "opRating": 137}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 828, "opRating": 171}, {"opponent": "metagross", "rating": 674}], "counters": [{"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "lugia", "rating": 383}, {"opponent": "gyarados", "rating": 399}, {"opponent": "excadrill", "rating": 465}, {"opponent": "giratina_origin", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42295}, {"moveId": "PSYCHO_CUT", "uses": 34205}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 28417}, {"moveId": "MEGAHORN", "uses": 19975}, {"moveId": "THUNDER", "uses": 15045}, {"moveId": "PAYBACK", "uses": 12922}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 55.2}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 565, "matchups": [{"opponent": "snorlax", "rating": 742, "opRating": 257}, {"opponent": "swampert", "rating": 686}, {"opponent": "kyogre", "rating": 686, "opRating": 313}, {"opponent": "excadrill", "rating": 644}, {"opponent": "metagross", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "gyarados", "rating": 389}, {"opponent": "dialga", "rating": 394}, {"opponent": "garcho<PERSON>", "rating": 431}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44210}, {"moveId": "BULLET_SEED", "uses": 32290}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25552}, {"moveId": "GRASS_KNOT", "uses": 19205}, {"moveId": "SEED_BOMB", "uses": 17642}, {"moveId": "SLUDGE_BOMB", "uses": 14205}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 55}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 609, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 828, "opRating": 171}, {"opponent": "buzzwole", "rating": 783, "opRating": 216}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 688}, {"opponent": "sylveon", "rating": 668, "opRating": 331}, {"opponent": "zacian_hero", "rating": 570}], "counters": [{"opponent": "mewtwo", "rating": 257}, {"opponent": "lugia", "rating": 304}, {"opponent": "dialga", "rating": 331}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "gyarados", "rating": 384}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45270}, {"moveId": "BITE", "uses": 31230}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 24413}, {"moveId": "SHADOW_BALL", "uses": 19582}, {"moveId": "POISON_FANG", "uses": 11069}, {"moveId": "AIR_CUTTER", "uses": 7447}, {"moveId": "RETURN", "uses": 7402}, {"moveId": "SLUDGE_BOMB", "uses": 6517}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 54.8}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 549, "matchups": [{"opponent": "magnezone", "rating": 873, "opRating": 126}, {"opponent": "metagross", "rating": 762}, {"opponent": "excadrill", "rating": 538}, {"opponent": "dialga", "rating": 528}, {"opponent": "zekrom", "rating": 528}], "counters": [{"opponent": "giratina_origin", "rating": 258}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dragonite", "rating": 295}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "gyarados", "rating": 301}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 44547}, {"moveId": "WATER_GUN", "uses": 31953}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 30418}, {"moveId": "STONE_EDGE", "uses": 24938}, {"moveId": "SLUDGE_BOMB", "uses": 17149}, {"moveId": "ACID_SPRAY", "uses": 3862}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 54.8}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 620, "matchups": [{"opponent": "excadrill", "rating": 925}, {"opponent": "metagross", "rating": 773}, {"opponent": "sylveon", "rating": 571, "opRating": 428}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 568}, {"opponent": "grou<PERSON>", "rating": 541}], "counters": [{"opponent": "mewtwo", "rating": 135}, {"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "gyarados", "rating": 164}, {"opponent": "dialga", "rating": 279}, {"opponent": "zacian_hero", "rating": 387}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 35825}, {"moveId": "FIRE_SPIN", "uses": 21120}, {"moveId": "PECK", "uses": 10138}, {"moveId": "STEEL_WING", "uses": 9457}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37693}, {"moveId": "FLAME_CHARGE", "uses": 21972}, {"moveId": "FIRE_BLAST", "uses": 9998}, {"moveId": "HURRICANE", "uses": 6913}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "FLAME_CHARGE"], "score": 54.8}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 627, "matchups": [{"opponent": "sylveon", "rating": 764, "opRating": 235}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 735}, {"opponent": "zacian_hero", "rating": 680}, {"opponent": "yveltal", "rating": 607, "opRating": 392}, {"opponent": "dragonite", "rating": 505}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "lugia", "rating": 397}, {"opponent": "gyarados", "rating": 471}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56272}, {"moveId": "ACID", "uses": 20228}], "chargedMoves": [{"moveId": "SCALD", "uses": 31022}, {"moveId": "BLIZZARD", "uses": 17582}, {"moveId": "SLUDGE_WAVE", "uses": 15559}, {"moveId": "HYDRO_PUMP", "uses": 6927}, {"moveId": "ACID_SPRAY", "uses": 5307}]}, "moveset": ["POISON_JAB", "SCALD", "BLIZZARD"], "score": 54.8}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 543, "matchups": [{"opponent": "magnezone_shadow", "rating": 914, "opRating": 85}, {"opponent": "magnezone", "rating": 896, "opRating": 103}, {"opponent": "excadrill", "rating": 612}, {"opponent": "metagross", "rating": 596}, {"opponent": "dialga", "rating": 569}], "counters": [{"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "swampert", "rating": 298}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "zacian_hero", "rating": 447}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8188}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 6202}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5292}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5134}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4523}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4452}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4427}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4239}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4197}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4039}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4026}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3998}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3620}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3538}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3534}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3507}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3392}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33692}, {"moveId": "EARTH_POWER", "uses": 23725}, {"moveId": "EARTHQUAKE", "uses": 10305}, {"moveId": "WATER_PULSE", "uses": 8844}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 54.5}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 558, "matchups": [{"opponent": "swampert", "rating": 961}, {"opponent": "swampert_shadow", "rating": 951, "opRating": 48}, {"opponent": "excadrill", "rating": 726}, {"opponent": "grou<PERSON>", "rating": 646}, {"opponent": "kyogre", "rating": 626, "opRating": 373}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "zacian_hero", "rating": 401}, {"opponent": "mewtwo", "rating": 403}, {"opponent": "garcho<PERSON>", "rating": 448}, {"opponent": "gyarados", "rating": 456}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41822}, {"moveId": "BITE", "uses": 34678}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30944}, {"moveId": "STONE_EDGE", "uses": 17848}, {"moveId": "EARTHQUAKE", "uses": 16340}, {"moveId": "SAND_TOMB", "uses": 7884}, {"moveId": "SOLAR_BEAM", "uses": 3524}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 54.4}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 562, "matchups": [{"opponent": "dragonite", "rating": 721}, {"opponent": "sylveon", "rating": 694, "opRating": 305}, {"opponent": "yveltal", "rating": 684, "opRating": 315}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 664}, {"opponent": "zacian_hero", "rating": 506}], "counters": [{"opponent": "mewtwo", "rating": 171}, {"opponent": "dialga", "rating": 290}, {"opponent": "giratina_origin", "rating": 308}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "gyarados", "rating": 479}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 48774}, {"moveId": "TACKLE", "uses": 27726}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 23787}, {"moveId": "PLAY_ROUGH", "uses": 23137}, {"moveId": "OVERHEAT", "uses": 19883}, {"moveId": "HYPER_BEAM", "uses": 9659}]}, "moveset": ["FAIRY_WIND", "SLUDGE", "PLAY_ROUGH"], "score": 54.4}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 581, "matchups": [{"opponent": "swampert", "rating": 960}, {"opponent": "swampert_shadow", "rating": 942, "opRating": 57}, {"opponent": "kyogre", "rating": 722, "opRating": 277}, {"opponent": "excadrill", "rating": 646}, {"opponent": "grou<PERSON>", "rating": 597}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "mewtwo", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "zacian_hero", "rating": 384}, {"opponent": "gyarados", "rating": 414}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 39734}, {"moveId": "RAZOR_LEAF", "uses": 22137}, {"moveId": "ACID", "uses": 14629}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 57475}, {"moveId": "DAZZLING_GLEAM", "uses": 13108}, {"moveId": "PETAL_BLIZZARD", "uses": 5943}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 54.2}, {"speciesId": "scyther", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 590, "matchups": [{"opponent": "al<PERSON><PERSON>_shadow", "rating": 920, "opRating": 79}, {"opponent": "tangrowth", "rating": 920, "opRating": 79}, {"opponent": "zarude", "rating": 891, "opRating": 108}, {"opponent": "tangrowth_shadow", "rating": 891, "opRating": 108}, {"opponent": "darkrai", "rating": 840, "opRating": 159}], "counters": [{"opponent": "mewtwo", "rating": 333}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "dialga", "rating": 345}, {"opponent": "garcho<PERSON>", "rating": 462}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35728}, {"moveId": "AIR_SLASH", "uses": 25144}, {"moveId": "STEEL_WING", "uses": 15656}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25836}, {"moveId": "X_SCISSOR", "uses": 18292}, {"moveId": "AERIAL_ACE", "uses": 13366}, {"moveId": "BUG_BUZZ", "uses": 11886}, {"moveId": "RETURN", "uses": 7044}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 54.2}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 564, "matchups": [{"opponent": "blaziken", "rating": 858, "opRating": 141}, {"opponent": "mewtwo_shadow", "rating": 829, "opRating": 170}, {"opponent": "lucario", "rating": 829, "opRating": 170}, {"opponent": "machamp_shadow", "rating": 795, "opRating": 204}, {"opponent": "metagross", "rating": 745}], "counters": [{"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "dialga", "rating": 331}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "giratina_origin", "rating": 434}, {"opponent": "excadrill", "rating": 483}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55711}, {"moveId": "ASTONISH", "uses": 20789}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 28438}, {"moveId": "DARK_PULSE", "uses": 18924}, {"moveId": "OMINOUS_WIND", "uses": 14564}, {"moveId": "PSYCHIC", "uses": 14531}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 54.1}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 583, "matchups": [{"opponent": "swampert", "rating": 923}, {"opponent": "swampert_shadow", "rating": 923, "opRating": 76}, {"opponent": "mamos<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "kyogre", "rating": 610, "opRating": 389}, {"opponent": "sylveon", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dialga", "rating": 220}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "garcho<PERSON>", "rating": 392}, {"opponent": "mewtwo", "rating": 440}, {"opponent": "gyarados", "rating": 489}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 46305}, {"moveId": "METAL_CLAW", "uses": 30195}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 25476}, {"moveId": "MIRROR_SHOT", "uses": 21645}, {"moveId": "THUNDER", "uses": 14620}, {"moveId": "FLASH_CANNON", "uses": 11292}, {"moveId": "ACID_SPRAY", "uses": 3525}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "MIRROR_SHOT"], "score": 54.1}, {"speciesId": "seaking", "speciesName": "Seaking", "rating": 543, "matchups": [{"opponent": "manectric_shadow", "rating": 886, "opRating": 113}, {"opponent": "darmanitan_standard", "rating": 819, "opRating": 180}, {"opponent": "tapu_bulu", "rating": 796, "opRating": 203}, {"opponent": "garcho<PERSON>", "rating": 534}, {"opponent": "sylveon", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "gyarados", "rating": 384}, {"opponent": "mewtwo", "rating": 393}, {"opponent": "lugia", "rating": 407}, {"opponent": "zacian_hero", "rating": 453}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 33508}, {"moveId": "WATERFALL", "uses": 29380}, {"moveId": "PECK", "uses": 13621}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 23991}, {"moveId": "ICY_WIND", "uses": 23338}, {"moveId": "MEGAHORN", "uses": 14864}, {"moveId": "ICE_BEAM", "uses": 7525}, {"moveId": "WATER_PULSE", "uses": 6845}]}, "moveset": ["POISON_JAB", "DRILL_RUN", "ICY_WIND"], "score": 54.1}, {"speciesId": "dragalge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 640, "matchups": [{"opponent": "chandelure", "rating": 828, "opRating": 171}, {"opponent": "raikou_shadow", "rating": 815, "opRating": 184}, {"opponent": "entei_shadow", "rating": 795, "opRating": 204}, {"opponent": "zap<PERSON>_shadow", "rating": 617, "opRating": 382}, {"opponent": "kyogre", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 222}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "giratina_origin", "rating": 296}, {"opponent": "dragonite", "rating": 332}, {"opponent": "garcho<PERSON>", "rating": 356}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 37610}, {"moveId": "WATER_GUN", "uses": 24388}, {"moveId": "ACID", "uses": 14401}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 31004}, {"moveId": "OUTRAGE", "uses": 27306}, {"moveId": "GUNK_SHOT", "uses": 13272}, {"moveId": "HYDRO_PUMP", "uses": 4930}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "OUTRAGE"], "score": 54}, {"speciesId": "torn<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 561, "matchups": [{"opponent": "grou<PERSON>", "rating": 608}, {"opponent": "garcho<PERSON>", "rating": 558}, {"opponent": "swampert", "rating": 544}, {"opponent": "zacian_hero", "rating": 502}, {"opponent": "sylveon", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "metagross", "rating": 212}, {"opponent": "gyarados", "rating": 291}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "lugia", "rating": 400}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 58448}, {"moveId": "ASTONISH", "uses": 18052}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 26520}, {"moveId": "PSYCHIC", "uses": 21116}, {"moveId": "FOCUS_BLAST", "uses": 20280}, {"moveId": "HEAT_WAVE", "uses": 8544}]}, "moveset": ["GUST", "HURRICANE", "PSYCHIC"], "score": 54}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 565, "matchups": [{"opponent": "swampert_shadow", "rating": 960, "opRating": 39}, {"opponent": "swampert", "rating": 801}, {"opponent": "excadrill", "rating": 710}, {"opponent": "kyogre", "rating": 585, "opRating": 414}, {"opponent": "zacian_hero", "rating": 533}], "counters": [{"opponent": "dialga", "rating": 222}, {"opponent": "metagross", "rating": 281}, {"opponent": "gyarados", "rating": 342}, {"opponent": "mewtwo", "rating": 351}, {"opponent": "garcho<PERSON>", "rating": 441}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38579}, {"moveId": "RAZOR_LEAF", "uses": 22640}, {"moveId": "ACID", "uses": 15277}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 50305}, {"moveId": "DAZZLING_GLEAM", "uses": 10891}, {"moveId": "RETURN", "uses": 10297}, {"moveId": "PETAL_BLIZZARD", "uses": 5283}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 53.9}, {"speciesId": "jellicent", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 560, "matchups": [{"opponent": "chandelure", "rating": 860, "opRating": 139}, {"opponent": "metagross_shadow", "rating": 823, "opRating": 176}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 815, "opRating": 184}, {"opponent": "landorus_incarnate", "rating": 674, "opRating": 325}, {"opponent": "garcho<PERSON>", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 288}, {"opponent": "giratina_origin", "rating": 328}, {"opponent": "lugia", "rating": 464}, {"opponent": "metagross", "rating": 473}, {"opponent": "zacian_hero", "rating": 494}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 41490}, {"moveId": "BUBBLE", "uses": 35010}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37811}, {"moveId": "ICE_BEAM", "uses": 25798}, {"moveId": "BUBBLE_BEAM", "uses": 12931}]}, "moveset": ["HEX", "SHADOW_BALL", "ICE_BEAM"], "score": 53.9}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 549, "matchups": [{"opponent": "swampert", "rating": 956}, {"opponent": "excadrill", "rating": 646}, {"opponent": "garcho<PERSON>", "rating": 597}, {"opponent": "sylveon", "rating": 528, "opRating": 471}, {"opponent": "snorlax", "rating": 528, "opRating": 471}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "gyarados", "rating": 396}, {"opponent": "zacian_hero", "rating": 456}, {"opponent": "grou<PERSON>", "rating": 491}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41225}, {"moveId": "BITE", "uses": 35275}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 28463}, {"moveId": "STONE_EDGE", "uses": 16016}, {"moveId": "EARTHQUAKE", "uses": 15183}, {"moveId": "SAND_TOMB", "uses": 7377}, {"moveId": "RETURN", "uses": 6268}, {"moveId": "SOLAR_BEAM", "uses": 3141}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 53.9}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 567, "matchups": [{"opponent": "entei", "rating": 722, "opRating": 277}, {"opponent": "metagross_shadow", "rating": 655, "opRating": 344}, {"opponent": "excadrill", "rating": 564}, {"opponent": "zekrom", "rating": 554, "opRating": 445}, {"opponent": "reshiram", "rating": 554, "opRating": 445}], "counters": [{"opponent": "mewtwo", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "giratina_origin", "rating": 302}, {"opponent": "metagross", "rating": 491}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 63770}, {"moveId": "ZEN_HEADBUTT", "uses": 12730}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 32816}, {"moveId": "EARTHQUAKE", "uses": 25020}, {"moveId": "IRON_HEAD", "uses": 18716}]}, "moveset": ["TACKLE", "HORN_ATTACK", "EARTHQUAKE"], "score": 53.7}, {"speciesId": "deoxys_defense", "speciesName": "<PERSON><PERSON><PERSON> (Defense)", "rating": 593, "matchups": [{"opponent": "weavile", "rating": 858, "opRating": 141}, {"opponent": "weavile_shadow", "rating": 830, "opRating": 169}, {"opponent": "lucario", "rating": 803, "opRating": 196}, {"opponent": "charizard_shadow", "rating": 748, "opRating": 251}, {"opponent": "blaziken", "rating": 704, "opRating": 295}], "counters": [{"opponent": "garcho<PERSON>", "rating": 267}, {"opponent": "mewtwo", "rating": 296}, {"opponent": "dialga", "rating": 391}, {"opponent": "zacian_hero", "rating": 436}, {"opponent": "gyarados", "rating": 453}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 67154}, {"moveId": "ZEN_HEADBUTT", "uses": 9346}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 32984}, {"moveId": "ROCK_SLIDE", "uses": 27448}, {"moveId": "THUNDERBOLT", "uses": 16101}]}, "moveset": ["COUNTER", "PSYCHO_BOOST", "ROCK_SLIDE"], "score": 53.6}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 536, "matchups": [{"opponent": "al<PERSON><PERSON>_shadow", "rating": 858, "opRating": 141}, {"opponent": "metagross_shadow", "rating": 745, "opRating": 254}, {"opponent": "metagross", "rating": 683}, {"opponent": "latios_shadow", "rating": 629, "opRating": 370}, {"opponent": "zacian_hero", "rating": 533}], "counters": [{"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "gyarados", "rating": 314}, {"opponent": "dialga", "rating": 323}, {"opponent": "giratina_origin", "rating": 370}, {"opponent": "mewtwo", "rating": 492}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55386}, {"moveId": "ASTONISH", "uses": 21114}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 25253}, {"moveId": "DARK_PULSE", "uses": 16501}, {"moveId": "OMINOUS_WIND", "uses": 12990}, {"moveId": "PSYCHIC", "uses": 12569}, {"moveId": "RETURN", "uses": 9112}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 53.6}, {"speciesId": "glalie", "speciesName": "G<PERSON><PERSON>", "rating": 519, "matchups": [{"opponent": "garcho<PERSON>", "rating": 869}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 837, "opRating": 162}, {"opponent": "dragonite", "rating": 677}, {"opponent": "latios_shadow", "rating": 630, "opRating": 369}, {"opponent": "dragonite_shadow", "rating": 613, "opRating": 386}], "counters": [{"opponent": "dialga", "rating": 298}, {"opponent": "gyarados", "rating": 324}, {"opponent": "lugia", "rating": 333}, {"opponent": "metagross", "rating": 340}, {"opponent": "giratina_origin", "rating": 358}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46692}, {"moveId": "FROST_BREATH", "uses": 29808}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 49165}, {"moveId": "SHADOW_BALL", "uses": 20398}, {"moveId": "GYRO_BALL", "uses": 6982}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "SHADOW_BALL"], "score": 53.6}, {"speciesId": "king<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 611, "matchups": [{"opponent": "chandelure", "rating": 890, "opRating": 109}, {"opponent": "entei_shadow", "rating": 792, "opRating": 207}, {"opponent": "kyogre", "rating": 728, "opRating": 271}, {"opponent": "swampert", "rating": 554}, {"opponent": "ho_oh", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "giratina_origin", "rating": 324}, {"opponent": "metagross", "rating": 337}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 32957}, {"moveId": "WATER_GUN", "uses": 22298}, {"moveId": "WATERFALL", "uses": 21301}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23149}, {"moveId": "OCTAZOOKA", "uses": 15357}, {"moveId": "BLIZZARD", "uses": 14865}, {"moveId": "HYDRO_PUMP", "uses": 12946}, {"moveId": "RETURN", "uses": 10018}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 53.6}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 580, "matchups": [{"opponent": "genesect_douse", "rating": 897, "opRating": 102}, {"opponent": "excadrill", "rating": 847}, {"opponent": "metagross", "rating": 779}, {"opponent": "sylveon", "rating": 562, "opRating": 437}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 518}], "counters": [{"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "gyarados", "rating": 234}, {"opponent": "mewtwo", "rating": 255}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "dialga", "rating": 288}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 28351}, {"moveId": "EMBER", "uses": 27230}, {"moveId": "FEINT_ATTACK", "uses": 20926}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 30703}, {"moveId": "PSYSHOCK", "uses": 13537}, {"moveId": "OVERHEAT", "uses": 12345}, {"moveId": "SOLAR_BEAM", "uses": 7435}, {"moveId": "FLAMETHROWER", "uses": 6635}, {"moveId": "FIRE_BLAST", "uses": 3553}, {"moveId": "HEAT_WAVE", "uses": 2152}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 53.5}, {"speciesId": "malamar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 588, "matchups": [{"opponent": "weavile_shadow", "rating": 900, "opRating": 100}, {"opponent": "espeon", "rating": 900, "opRating": 100}, {"opponent": "weavile", "rating": 897, "opRating": 102}, {"opponent": "mewtwo", "rating": 644}, {"opponent": "excadrill", "rating": 611}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "gyarados", "rating": 384}, {"opponent": "metagross", "rating": 447}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 57830}, {"moveId": "PECK", "uses": 18670}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 32006}, {"moveId": "SUPER_POWER", "uses": 28282}, {"moveId": "PSYBEAM", "uses": 8546}, {"moveId": "HYPER_BEAM", "uses": 7695}]}, "moveset": ["PSYCHO_CUT", "FOUL_PLAY", "SUPER_POWER"], "score": 53.4}, {"speciesId": "pheromosa", "speciesName": "Pheromosa", "rating": 530, "matchups": [{"opponent": "yveltal", "rating": 832, "opRating": 167}, {"opponent": "garcho<PERSON>", "rating": 829}, {"opponent": "metagross", "rating": 547}, {"opponent": "swampert", "rating": 512}, {"opponent": "excadrill", "rating": 509}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "lugia", "rating": 323}, {"opponent": "mewtwo", "rating": 385}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 60209}, {"moveId": "LOW_KICK", "uses": 16291}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 37071}, {"moveId": "LUNGE", "uses": 20642}, {"moveId": "BUG_BUZZ", "uses": 13410}, {"moveId": "FOCUS_BLAST", "uses": 5420}]}, "moveset": ["BUG_BITE", "CLOSE_COMBAT", "LUNGE"], "score": 53.4}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 554, "matchups": [{"opponent": "mewtwo_shadow", "rating": 862, "opRating": 137}, {"opponent": "swampert", "rating": 766}, {"opponent": "swampert_shadow", "rating": 740, "opRating": 259}, {"opponent": "latios_shadow", "rating": 681, "opRating": 318}, {"opponent": "zacian_hero", "rating": 540}], "counters": [{"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dialga", "rating": 298}, {"opponent": "gyarados", "rating": 306}, {"opponent": "mewtwo", "rating": 434}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49282}, {"moveId": "RAZOR_LEAF", "uses": 27218}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26410}, {"moveId": "SEED_BOMB", "uses": 21650}, {"moveId": "FOUL_PLAY", "uses": 19823}, {"moveId": "FIRE_BLAST", "uses": 8563}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 53.3}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 605, "matchups": [{"opponent": "yveltal", "rating": 788, "opRating": 211}, {"opponent": "sylveon", "rating": 788, "opRating": 211}, {"opponent": "genesect_douse", "rating": 788, "opRating": 211}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 762, "opRating": 237}, {"opponent": "metagross", "rating": 743}], "counters": [{"opponent": "mewtwo", "rating": 197}, {"opponent": "dialga", "rating": 198}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "gyarados", "rating": 404}, {"opponent": "lugia", "rating": 407}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 42679}, {"moveId": "POISON_JAB", "uses": 33821}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 32748}, {"moveId": "FIRE_BLAST", "uses": 16170}, {"moveId": "DRAGON_PULSE", "uses": 14383}, {"moveId": "SLUDGE_WAVE", "uses": 13192}]}, "moveset": ["INCINERATE", "POISON_FANG", "FIRE_BLAST"], "score": 53.3}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 620, "matchups": [{"opponent": "tapu_bulu", "rating": 933, "opRating": 66}, {"opponent": "zarude", "rating": 890, "opRating": 109}, {"opponent": "sylveon", "rating": 647, "opRating": 352}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609}, {"opponent": "zacian_hero", "rating": 510}], "counters": [{"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "dialga", "rating": 258}, {"opponent": "lugia", "rating": 283}, {"opponent": "metagross", "rating": 308}, {"opponent": "mewtwo", "rating": 330}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 43109}, {"moveId": "BUG_BITE", "uses": 33391}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25304}, {"moveId": "MEGAHORN", "uses": 24147}, {"moveId": "SLUDGE_BOMB", "uses": 19148}, {"moveId": "GYRO_BALL", "uses": 7856}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "MEGAHORN"], "score": 53.1}, {"speciesId": "skar<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 569, "matchups": [{"opponent": "genesect", "rating": 677, "opRating": 322}, {"opponent": "buzzwole", "rating": 674, "opRating": 325}, {"opponent": "sylveon", "rating": 573, "opRating": 426}, {"opponent": "zacian_hero", "rating": 550}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "metagross", "rating": 328}, {"opponent": "gyarados", "rating": 329}, {"opponent": "garcho<PERSON>", "rating": 441}, {"opponent": "mewtwo", "rating": 447}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 43955}, {"moveId": "STEEL_WING", "uses": 32545}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37532}, {"moveId": "SKY_ATTACK", "uses": 28085}, {"moveId": "FLASH_CANNON", "uses": 10845}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 53}, {"speciesId": "hitmontop", "speciesName": "Hitmontop", "rating": 570, "matchups": [{"opponent": "pangoro", "rating": 913, "opRating": 86}, {"opponent": "darkrai", "rating": 885, "opRating": 114}, {"opponent": "ma<PERSON><PERSON>", "rating": 791, "opRating": 208}, {"opponent": "magnezone_shadow", "rating": 669, "opRating": 330}, {"opponent": "dialga", "rating": 574}], "counters": [{"opponent": "zacian_hero", "rating": 257}, {"opponent": "excadrill", "rating": 327}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "metagross", "rating": 383}, {"opponent": "gyarados", "rating": 389}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 63320}, {"moveId": "ROCK_SMASH", "uses": 13180}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 46270}, {"moveId": "STONE_EDGE", "uses": 22108}, {"moveId": "GYRO_BALL", "uses": 8123}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "STONE_EDGE"], "score": 52.9}, {"speciesId": "king<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 579, "matchups": [{"opponent": "chandelure", "rating": 875, "opRating": 125}, {"opponent": "kyogre", "rating": 814, "opRating": 185}, {"opponent": "entei", "rating": 792, "opRating": 207}, {"opponent": "excadrill", "rating": 530}, {"opponent": "snorlax", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "giratina_origin", "rating": 378}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33715}, {"moveId": "WATERFALL", "uses": 21737}, {"moveId": "WATER_GUN", "uses": 21152}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 26760}, {"moveId": "OCTAZOOKA", "uses": 17672}, {"moveId": "BLIZZARD", "uses": 17066}, {"moveId": "HYDRO_PUMP", "uses": 14798}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 52.5}, {"speciesId": "aromatisse", "speciesName": "Aromatisse", "rating": 521, "matchups": [{"opponent": "dragonite_shadow", "rating": 811, "opRating": 188}, {"opponent": "dragonite", "rating": 660}, {"opponent": "yveltal", "rating": 646, "opRating": 353}, {"opponent": "palkia", "rating": 646, "opRating": 353}, {"opponent": "garcho<PERSON>", "rating": 537}], "counters": [{"opponent": "mewtwo", "rating": 283}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dialga", "rating": 331}, {"opponent": "giratina_origin", "rating": 342}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46608}, {"moveId": "CHARGE_BEAM", "uses": 29892}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 30010}, {"moveId": "THUNDERBOLT", "uses": 18546}, {"moveId": "PSYCHIC", "uses": 15747}, {"moveId": "DRAINING_KISS", "uses": 12175}]}, "moveset": ["CHARM", "MOONBLAST", "THUNDERBOLT"], "score": 52.4}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 633, "matchups": [{"opponent": "zarude", "rating": 897, "opRating": 102}, {"opponent": "heracross", "rating": 886, "opRating": 113}, {"opponent": "blaziken", "rating": 863, "opRating": 136}, {"opponent": "virizion", "rating": 858, "opRating": 141}, {"opponent": "yveltal", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "gyarados", "rating": 381}, {"opponent": "garcho<PERSON>", "rating": 455}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 40807}, {"moveId": "BUG_BITE", "uses": 35693}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26369}, {"moveId": "ANCIENT_POWER", "uses": 25647}, {"moveId": "AERIAL_ACE", "uses": 24431}]}, "moveset": ["WING_ATTACK", "BUG_BUZZ", "ANCIENT_POWER"], "score": 52.3}, {"speciesId": "lanturn", "speciesName": "Lanturn", "rating": 568, "matchups": [{"opponent": "gyarados", "rating": 797}, {"opponent": "gyarado<PERSON>_shadow", "rating": 757, "opRating": 242}, {"opponent": "kyogre", "rating": 592, "opRating": 407}, {"opponent": "ho_oh", "rating": 582, "opRating": 417}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "metagross", "rating": 331}, {"opponent": "mewtwo", "rating": 372}, {"opponent": "zacian_hero", "rating": 398}, {"opponent": "lugia", "rating": 404}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 29324}, {"moveId": "WATER_GUN", "uses": 28600}, {"moveId": "CHARGE_BEAM", "uses": 18548}], "chargedMoves": [{"moveId": "SURF", "uses": 39492}, {"moveId": "THUNDERBOLT", "uses": 21456}, {"moveId": "THUNDER", "uses": 9271}, {"moveId": "HYDRO_PUMP", "uses": 6175}]}, "moveset": ["SPARK", "SURF", "THUNDERBOLT"], "score": 52.2}, {"speciesId": "electabuzz_shadow", "speciesName": "Electabuzz (Shadow)", "rating": 575, "matchups": [{"opponent": "yveltal", "rating": 808, "opRating": 191}, {"opponent": "gyarados", "rating": 744}, {"opponent": "ho_oh", "rating": 644, "opRating": 355}, {"opponent": "lugia", "rating": 604}, {"opponent": "kyogre", "rating": 550, "opRating": 449}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "dragonite", "rating": 308}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "metagross", "rating": 465}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58071}, {"moveId": "LOW_KICK", "uses": 18429}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 44949}, {"moveId": "THUNDERBOLT", "uses": 16800}, {"moveId": "THUNDER", "uses": 14524}, {"moveId": "FRUSTRATION", "uses": 109}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 52}, {"speciesId": "absol", "speciesName": "Absol", "rating": 514, "matchups": [{"opponent": "articuno_galarian", "rating": 916, "opRating": 83}, {"opponent": "mewtwo_shadow", "rating": 906, "opRating": 93}, {"opponent": "darkrai", "rating": 855, "opRating": 144}, {"opponent": "mewtwo", "rating": 681}, {"opponent": "metagross", "rating": 560}], "counters": [{"opponent": "dialga", "rating": 296}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "gyarados", "rating": 337}, {"opponent": "excadrill", "rating": 397}, {"opponent": "giratina_origin", "rating": 404}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41593}, {"moveId": "PSYCHO_CUT", "uses": 34907}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25230}, {"moveId": "MEGAHORN", "uses": 17503}, {"moveId": "THUNDER", "uses": 13135}, {"moveId": "PAYBACK", "uses": 11473}, {"moveId": "RETURN", "uses": 9300}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 51.8}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 566, "matchups": [{"opponent": "moltres", "rating": 822, "opRating": 177}, {"opponent": "charizard", "rating": 813, "opRating": 186}, {"opponent": "moltres_shadow", "rating": 783, "opRating": 216}, {"opponent": "swampert", "rating": 736}, {"opponent": "swampert_shadow", "rating": 700, "opRating": 300}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "gyarados", "rating": 373}, {"opponent": "zacian_hero", "rating": 459}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36209}, {"moveId": "INFESTATION", "uses": 28974}, {"moveId": "ACID", "uses": 11310}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 29600}, {"moveId": "GRASS_KNOT", "uses": 25418}, {"moveId": "BULLDOZE", "uses": 12147}, {"moveId": "RETURN", "uses": 9384}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 51.5}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 562, "matchups": [{"opponent": "heracross", "rating": 793, "opRating": 206}, {"opponent": "swampert", "rating": 674}, {"opponent": "buzzwole", "rating": 648, "opRating": 351}, {"opponent": "giratina_origin", "rating": 546}, {"opponent": "grou<PERSON>", "rating": 526}], "counters": [{"opponent": "mewtwo", "rating": 223}, {"opponent": "dialga", "rating": 236}, {"opponent": "metagross", "rating": 241}, {"opponent": "garcho<PERSON>", "rating": 349}, {"opponent": "excadrill", "rating": 388}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 37766}, {"moveId": "PECK", "uses": 24296}, {"moveId": "ROCK_SMASH", "uses": 14410}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 44805}, {"moveId": "ROCK_BLAST", "uses": 22134}, {"moveId": "FLASH_CANNON", "uses": 9598}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 51.5}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 590, "matchups": [{"opponent": "raikou_shadow", "rating": 875, "opRating": 124}, {"opponent": "sylveon", "rating": 736, "opRating": 263}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 647}, {"opponent": "zacian_hero", "rating": 586}, {"opponent": "zekrom", "rating": 528}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "lugia", "rating": 261}, {"opponent": "metagross", "rating": 380}, {"opponent": "dialga", "rating": 413}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26092}, {"moveId": "DOUBLE_KICK", "uses": 25222}, {"moveId": "FURY_CUTTER", "uses": 21327}, {"moveId": "IRON_TAIL", "uses": 3901}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 20306}, {"moveId": "MEGAHORN", "uses": 16721}, {"moveId": "SLUDGE_WAVE", "uses": 13215}, {"moveId": "EARTHQUAKE", "uses": 8793}, {"moveId": "RETURN", "uses": 8775}, {"moveId": "SAND_TOMB", "uses": 8637}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 51.4}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 556, "matchups": [{"opponent": "honchk<PERSON>_shadow", "rating": 903, "opRating": 96}, {"opponent": "honch<PERSON><PERSON>", "rating": 903, "opRating": 96}, {"opponent": "ursaring_shadow", "rating": 893, "opRating": 106}, {"opponent": "weavile_shadow", "rating": 884, "opRating": 115}, {"opponent": "gyarado<PERSON>_shadow", "rating": 796, "opRating": 203}], "counters": [{"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "dialga", "rating": 380}, {"opponent": "excadrill", "rating": 425}, {"opponent": "gyarados", "rating": 438}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 39796}, {"moveId": "TACKLE", "uses": 30048}, {"moveId": "TAKE_DOWN", "uses": 6604}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34428}, {"moveId": "WILD_CHARGE", "uses": 27243}, {"moveId": "PAYBACK", "uses": 14955}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "WILD_CHARGE"], "score": 50.9}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 546, "matchups": [{"opponent": "genesect_douse", "rating": 897, "opRating": 102}, {"opponent": "genesect_shock", "rating": 897, "opRating": 102}, {"opponent": "genesect_chill", "rating": 897, "opRating": 102}, {"opponent": "metagross", "rating": 822}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 562, "opRating": 437}], "counters": [{"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 355}, {"opponent": "excadrill", "rating": 416}, {"opponent": "zacian_hero", "rating": 424}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 27787}, {"moveId": "EMBER", "uses": 27704}, {"moveId": "FEINT_ATTACK", "uses": 21015}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28171}, {"moveId": "PSYSHOCK", "uses": 12269}, {"moveId": "OVERHEAT", "uses": 11272}, {"moveId": "SOLAR_BEAM", "uses": 6623}, {"moveId": "RETURN", "uses": 6570}, {"moveId": "FLAMETHROWER", "uses": 6142}, {"moveId": "FIRE_BLAST", "uses": 3306}, {"moveId": "HEAT_WAVE", "uses": 2001}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 50.8}, {"speciesId": "altaria", "speciesName": "Altaria", "rating": 567, "matchups": [{"opponent": "magmortar_shadow", "rating": 878, "opRating": 121}, {"opponent": "blaziken", "rating": 814, "opRating": 185}, {"opponent": "grou<PERSON>", "rating": 606}, {"opponent": "swampert", "rating": 564}, {"opponent": "kyogre", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "gyarados", "rating": 329}, {"opponent": "garcho<PERSON>", "rating": 349}, {"opponent": "mewtwo", "rating": 359}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52620}, {"moveId": "PECK", "uses": 23880}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 34981}, {"moveId": "MOONBLAST", "uses": 19791}, {"moveId": "DRAGON_PULSE", "uses": 16746}, {"moveId": "DAZZLING_GLEAM", "uses": 5108}]}, "moveset": ["DRAGON_BREATH", "SKY_ATTACK", "MOONBLAST"], "score": 50.7}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 508, "matchups": [{"opponent": "gengar", "rating": 858, "opRating": 141}, {"opponent": "darmanitan_standard", "rating": 769, "opRating": 230}, {"opponent": "metagross_shadow", "rating": 716, "opRating": 283}, {"opponent": "giratina_origin", "rating": 627}, {"opponent": "excadrill", "rating": 627}], "counters": [{"opponent": "zacian_hero", "rating": 289}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "mewtwo", "rating": 322}, {"opponent": "dialga", "rating": 410}, {"opponent": "metagross", "rating": 465}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 57431}, {"moveId": "LOW_KICK", "uses": 19069}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19633}, {"moveId": "STOMP", "uses": 17812}, {"moveId": "BRICK_BREAK", "uses": 13320}, {"moveId": "OUTRAGE", "uses": 11604}, {"moveId": "EARTHQUAKE", "uses": 11451}, {"moveId": "POWER_UP_PUNCH", "uses": 2744}]}, "moveset": ["MUD_SLAP", "CRUNCH", "STOMP"], "score": 50.6}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 552, "matchups": [{"opponent": "ho_oh", "rating": 767, "opRating": 232}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 714}, {"opponent": "sylveon", "rating": 686, "opRating": 313}, {"opponent": "lugia", "rating": 591}, {"opponent": "gyarados", "rating": 559}], "counters": [{"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "dialga", "rating": 252}, {"opponent": "dragonite", "rating": 311}, {"opponent": "mewtwo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39419}, {"moveId": "ROCK_THROW", "uses": 37081}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34326}, {"moveId": "MAGNET_BOMB", "uses": 27636}, {"moveId": "THUNDERBOLT", "uses": 14427}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 50.4}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 541, "matchups": [{"opponent": "genesect_douse", "rating": 897, "opRating": 102}, {"opponent": "metagross", "rating": 788}, {"opponent": "sylveon", "rating": 661, "opRating": 338}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 630}, {"opponent": "giratina_origin", "rating": 530}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "dialga", "rating": 331}, {"opponent": "excadrill", "rating": 425}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 60445}, {"moveId": "TAKE_DOWN", "uses": 16055}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25089}, {"moveId": "DARK_PULSE", "uses": 21832}, {"moveId": "OVERHEAT", "uses": 19312}, {"moveId": "SOLAR_BEAM", "uses": 10283}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "DARK_PULSE"], "score": 50.4}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 531, "matchups": [{"opponent": "porygon_z_shadow", "rating": 875, "opRating": 125}, {"opponent": "swampert", "rating": 734}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "excadrill", "rating": 597}, {"opponent": "grou<PERSON>", "rating": 530}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "metagross", "rating": 319}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "gyarados", "rating": 358}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51777}, {"moveId": "BITE", "uses": 24723}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37988}, {"moveId": "GRASS_KNOT", "uses": 31726}, {"moveId": "SOLAR_BEAM", "uses": 6736}]}, "moveset": ["VINE_WHIP", "CRUNCH", "GRASS_KNOT"], "score": 50.4}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 576, "matchups": [{"opponent": "darmanitan_standard", "rating": 786, "opRating": 213}, {"opponent": "moltres", "rating": 783, "opRating": 216}, {"opponent": "ho_oh_shadow", "rating": 763, "opRating": 236}, {"opponent": "moltres_shadow", "rating": 744, "opRating": 255}, {"opponent": "swampert", "rating": 700}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "dragonite", "rating": 359}, {"opponent": "gyarados", "rating": 417}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36481}, {"moveId": "INFESTATION", "uses": 29470}, {"moveId": "ACID", "uses": 10591}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 33817}, {"moveId": "GRASS_KNOT", "uses": 28903}, {"moveId": "BULLDOZE", "uses": 13792}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 50.3}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 596, "matchups": [{"opponent": "ho_oh", "rating": 838, "opRating": 161}, {"opponent": "moltres", "rating": 838, "opRating": 161}, {"opponent": "moltres_shadow", "rating": 830, "opRating": 169}, {"opponent": "ho_oh_shadow", "rating": 799, "opRating": 200}, {"opponent": "gyarados", "rating": 748}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dragonite", "rating": 292}, {"opponent": "metagross", "rating": 328}, {"opponent": "lugia", "rating": 485}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 57226}, {"moveId": "ASTONISH", "uses": 19274}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34718}, {"moveId": "HYDRO_PUMP", "uses": 26673}, {"moveId": "THUNDER", "uses": 15106}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 50.2}, {"speciesId": "sudowoodo", "speciesName": "Sudowoodo", "rating": 544, "matchups": [{"opponent": "porygon_z", "rating": 920, "opRating": 79}, {"opponent": "darmanitan_standard", "rating": 856, "opRating": 143}, {"opponent": "moltres_shadow", "rating": 818, "opRating": 181}, {"opponent": "dialga", "rating": 582}, {"opponent": "ho_oh", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 218}, {"opponent": "metagross", "rating": 247}, {"opponent": "dragonite", "rating": 311}, {"opponent": "gyarados", "rating": 314}, {"opponent": "lugia", "rating": 335}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44247}, {"moveId": "ROCK_THROW", "uses": 32253}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30330}, {"moveId": "EARTHQUAKE", "uses": 18330}, {"moveId": "STONE_EDGE", "uses": 12083}, {"moveId": "RETURN", "uses": 9235}, {"moveId": "ROCK_TOMB", "uses": 6527}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 50.2}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 536, "matchups": [{"opponent": "gengar", "rating": 795, "opRating": 204}, {"opponent": "sneasler", "rating": 733, "opRating": 266}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 661, "opRating": 338}, {"opponent": "xurkitree", "rating": 618, "opRating": 381}, {"opponent": "giratina_origin", "rating": 559}], "counters": [{"opponent": "dialga", "rating": 274}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "swampert", "rating": 462}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 61767}, {"moveId": "ZEN_HEADBUTT", "uses": 12847}, {"moveId": "YAWN", "uses": 1947}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 36397}, {"moveId": "PSYCHIC", "uses": 27933}, {"moveId": "FUTURE_SIGHT", "uses": 12192}]}, "moveset": ["CONFUSION", "FOUL_PLAY", "PSYCHIC"], "score": 50.1}, {"speciesId": "aerodactyl_shadow", "speciesName": "Aerodactyl (Shadow)", "rating": 542, "matchups": [{"opponent": "moltres_shadow", "rating": 883, "opRating": 116}, {"opponent": "ho_oh", "rating": 845, "opRating": 154}, {"opponent": "moltres", "rating": 843, "opRating": 156}, {"opponent": "gyarados", "rating": 648}, {"opponent": "lugia", "rating": 619}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "dragonite", "rating": 449}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36425}, {"moveId": "BITE", "uses": 20421}, {"moveId": "STEEL_WING", "uses": 19682}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29254}, {"moveId": "EARTH_POWER", "uses": 19282}, {"moveId": "IRON_HEAD", "uses": 10734}, {"moveId": "ANCIENT_POWER", "uses": 10481}, {"moveId": "HYPER_BEAM", "uses": 6619}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 50}, {"speciesId": "lycanroc_midday", "speciesName": "Lycanroc (Midday)", "rating": 569, "matchups": [{"opponent": "victini", "rating": 887, "opRating": 112}, {"opponent": "darmanitan_standard", "rating": 826, "opRating": 173}, {"opponent": "ho_oh", "rating": 814, "opRating": 185}, {"opponent": "moltres", "rating": 801, "opRating": 198}, {"opponent": "ho_oh_shadow", "rating": 768, "opRating": 231}], "counters": [{"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "giratina_origin", "rating": 358}, {"opponent": "lugia", "rating": 371}, {"opponent": "dialga", "rating": 402}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40140}, {"moveId": "ROCK_THROW", "uses": 36360}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27470}, {"moveId": "STONE_EDGE", "uses": 26229}, {"moveId": "CRUNCH", "uses": 22758}]}, "moveset": ["SUCKER_PUNCH", "DRILL_RUN", "STONE_EDGE"], "score": 50}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 532, "matchups": [{"opponent": "magnezone_shadow", "rating": 873, "opRating": 126}, {"opponent": "magnezone", "rating": 873, "opRating": 126}, {"opponent": "nihilego", "rating": 827, "opRating": 172}, {"opponent": "melmetal", "rating": 765, "opRating": 234}, {"opponent": "excadrill", "rating": 618}], "counters": [{"opponent": "mewtwo", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "zacian_hero", "rating": 355}, {"opponent": "dialga", "rating": 434}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43149}, {"moveId": "WATER_GUN", "uses": 33351}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 26774}, {"moveId": "STONE_EDGE", "uses": 21723}, {"moveId": "SLUDGE_BOMB", "uses": 14704}, {"moveId": "RETURN", "uses": 9966}, {"moveId": "ACID_SPRAY", "uses": 3320}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 50}, {"speciesId": "jynx", "speciesName": "Jynx", "rating": 510, "matchups": [{"opponent": "garcho<PERSON>", "rating": 848}, {"opponent": "sneasler", "rating": 775, "opRating": 224}, {"opponent": "tangrowth_shadow", "rating": 714, "opRating": 285}, {"opponent": "zap<PERSON>_galarian", "rating": 590, "opRating": 409}, {"opponent": "dragonite", "rating": 533}], "counters": [{"opponent": "gyarados", "rating": 234}, {"opponent": "dialga", "rating": 274}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "lugia", "rating": 300}, {"opponent": "giratina_origin", "rating": 342}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 41880}, {"moveId": "FROST_BREATH", "uses": 32953}, {"moveId": "POUND", "uses": 1604}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30933}, {"moveId": "ICE_PUNCH", "uses": 18640}, {"moveId": "PSYSHOCK", "uses": 13822}, {"moveId": "FOCUS_BLAST", "uses": 8970}, {"moveId": "DRAINING_KISS", "uses": 4110}]}, "moveset": ["CONFUSION", "AVALANCHE", "ICE_PUNCH"], "score": 49.8}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 499, "matchups": [{"opponent": "swampert", "rating": 845}, {"opponent": "garcho<PERSON>", "rating": 626}, {"opponent": "zacian_hero", "rating": 512}, {"opponent": "gyarados", "rating": 509}, {"opponent": "grou<PERSON>", "rating": 504}], "counters": [{"opponent": "metagross", "rating": 148}, {"opponent": "dialga", "rating": 214}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "excadrill", "rating": 327}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 337}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6264}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5709}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 5639}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5059}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4969}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4898}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4704}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4595}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4522}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4466}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4395}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4347}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3980}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3937}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3838}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3648}, {"moveId": "ZEN_HEADBUTT", "uses": 1355}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41198}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12214}, {"moveId": "SOLAR_BEAM", "uses": 8438}]}, "moveset": ["HIDDEN_POWER_GRASS", "GRASS_KNOT", "ENERGY_BALL"], "score": 49.8}, {"speciesId": "tornadus_incarnate", "speciesName": "Tornadus (Incarnate)", "rating": 554, "matchups": [{"opponent": "rhyperior", "rating": 850, "opRating": 150}, {"opponent": "heracross", "rating": 802, "opRating": 197}, {"opponent": "buzzwole", "rating": 767, "opRating": 232}, {"opponent": "virizion", "rating": 761, "opRating": 238}, {"opponent": "grou<PERSON>", "rating": 579}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "zacian_hero", "rating": 424}, {"opponent": "garcho<PERSON>", "rating": 448}, {"opponent": "metagross", "rating": 459}, {"opponent": "excadrill", "rating": 479}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47760}, {"moveId": "BITE", "uses": 28740}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25205}, {"moveId": "GRASS_KNOT", "uses": 21791}, {"moveId": "HURRICANE", "uses": 20322}, {"moveId": "HYPER_BEAM", "uses": 9201}]}, "moveset": ["AIR_SLASH", "DARK_PULSE", "GRASS_KNOT"], "score": 49.8}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 550, "matchups": [{"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "moltres_shadow", "rating": 846, "opRating": 153}, {"opponent": "ho_oh", "rating": 844, "opRating": 155}, {"opponent": "ho_oh_shadow", "rating": 816, "opRating": 183}, {"opponent": "lugia", "rating": 614}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 198}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "dragonite", "rating": 470}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 68005}, {"moveId": "ZEN_HEADBUTT", "uses": 8495}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 38189}, {"moveId": "OUTRAGE", "uses": 19214}, {"moveId": "FLAMETHROWER", "uses": 19179}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "OUTRAGE"], "score": 49.7}, {"speciesId": "aerodactyl", "speciesName": "Aerodactyl", "rating": 546, "matchups": [{"opponent": "ho_oh_shadow", "rating": 845, "opRating": 154}, {"opponent": "moltres_shadow", "rating": 843, "opRating": 156}, {"opponent": "ho_oh", "rating": 837, "opRating": 162}, {"opponent": "zacian_hero", "rating": 517}, {"opponent": "lugia", "rating": 517}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 373}, {"opponent": "dragonite", "rating": 385}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36300}, {"moveId": "BITE", "uses": 20855}, {"moveId": "STEEL_WING", "uses": 19390}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27654}, {"moveId": "EARTH_POWER", "uses": 18025}, {"moveId": "IRON_HEAD", "uses": 10020}, {"moveId": "ANCIENT_POWER", "uses": 9899}, {"moveId": "RETURN", "uses": 7913}, {"moveId": "HYPER_BEAM", "uses": 3063}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 49.6}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 566, "matchups": [{"opponent": "gyarados", "rating": 757}, {"opponent": "yveltal", "rating": 750}, {"opponent": "ho_oh", "rating": 679}, {"opponent": "lugia", "rating": 556}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 556}], "counters": [{"opponent": "giratina_origin", "rating": 129}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "dialga", "rating": 154}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "metagross", "rating": 389}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 29130}, {"moveId": "THUNDER_SHOCK", "uses": 25718}, {"moveId": "SPARK", "uses": 21545}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32415}, {"moveId": "THUNDER_PUNCH", "uses": 15861}, {"moveId": "PSYCHIC", "uses": 14309}, {"moveId": "GRASS_KNOT", "uses": 13874}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 49.6}, {"speciesId": "hunt<PERSON>", "speciesName": "Huntail", "rating": 539, "matchups": [{"opponent": "darmanitan_standard", "rating": 874, "opRating": 125}, {"opponent": "mamos<PERSON>_shadow", "rating": 848, "opRating": 151}, {"opponent": "landorus_incarnate", "rating": 818, "opRating": 181}, {"opponent": "ma<PERSON><PERSON>", "rating": 788, "opRating": 211}, {"opponent": "excadrill", "rating": 525}], "counters": [{"opponent": "giratina_origin", "rating": 254}, {"opponent": "dialga", "rating": 260}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "metagross", "rating": 450}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 49357}, {"moveId": "BITE", "uses": 27143}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 33338}, {"moveId": "CRUNCH", "uses": 24018}, {"moveId": "ICE_BEAM", "uses": 19103}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "CRUNCH"], "score": 49.5}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 540, "matchups": [{"opponent": "ho_oh", "rating": 764}, {"opponent": "sylveon", "rating": 700, "opRating": 299}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 676}, {"opponent": "lugia", "rating": 609}, {"opponent": "gyarados", "rating": 598}], "counters": [{"opponent": "zacian_hero", "rating": 222}, {"opponent": "dialga", "rating": 241}, {"opponent": "dragonite", "rating": 242}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "mewtwo", "rating": 320}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39729}, {"moveId": "ROCK_THROW", "uses": 36771}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30475}, {"moveId": "MAGNET_BOMB", "uses": 24114}, {"moveId": "THUNDERBOLT", "uses": 12937}, {"moveId": "RETURN", "uses": 9006}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 49.2}, {"speciesId": "slurpuff", "speciesName": "Slurpuff", "rating": 520, "matchups": [{"opponent": "kommo_o", "rating": 876, "opRating": 123}, {"opponent": "hydreigon", "rating": 876, "opRating": 123}, {"opponent": "dragonite", "rating": 669}, {"opponent": "yveltal", "rating": 609, "opRating": 390}, {"opponent": "palkia", "rating": 589, "opRating": 410}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "metagross", "rating": 290}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 384}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 32094}, {"moveId": "CHARM", "uses": 25940}, {"moveId": "TACKLE", "uses": 18433}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22384}, {"moveId": "PLAY_ROUGH", "uses": 22007}, {"moveId": "ENERGY_BALL", "uses": 18113}, {"moveId": "DRAINING_KISS", "uses": 13928}]}, "moveset": ["FAIRY_WIND", "FLAMETHROWER", "PLAY_ROUGH"], "score": 49}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 553, "matchups": [{"opponent": "nidoqueen_shadow", "rating": 805, "opRating": 194}, {"opponent": "gallade_shadow", "rating": 795, "opRating": 204}, {"opponent": "virizion", "rating": 718, "opRating": 281}, {"opponent": "buzzwole", "rating": 714, "opRating": 285}, {"opponent": "genesect", "rating": 627, "opRating": 372}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "gyarados", "rating": 311}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 373}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 43610}, {"moveId": "STEEL_WING", "uses": 32890}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33563}, {"moveId": "SKY_ATTACK", "uses": 25157}, {"moveId": "FLASH_CANNON", "uses": 9652}, {"moveId": "RETURN", "uses": 8186}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 48.8}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 497, "matchups": [{"opponent": "blaziken", "rating": 802, "opRating": 197}, {"opponent": "machamp", "rating": 802, "opRating": 197}, {"opponent": "heracross", "rating": 802, "opRating": 197}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 654, "opRating": 345}, {"opponent": "dragonite", "rating": 591}], "counters": [{"opponent": "dialga", "rating": 277}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "gyarados", "rating": 340}, {"opponent": "garcho<PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 7980}, {"moveId": "WATER_GUN", "uses": 6699}, {"moveId": "TACKLE", "uses": 4953}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4570}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4262}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3944}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3861}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3821}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3734}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3716}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3511}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3503}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3481}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3407}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3339}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3062}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3045}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3011}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2851}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 20419}, {"moveId": "PSYCHIC", "uses": 17234}, {"moveId": "THUNDER", "uses": 13364}, {"moveId": "HYDRO_PUMP", "uses": 12939}, {"moveId": "POWER_GEM", "uses": 8964}, {"moveId": "PSYBEAM", "uses": 3534}]}, "moveset": ["QUICK_ATTACK", "ICE_BEAM", "PSYCHIC"], "score": 48.8}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 577, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 873, "opRating": 126}, {"opponent": "florges", "rating": 811, "opRating": 188}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 770}, {"opponent": "sylveon", "rating": 765, "opRating": 234}, {"opponent": "zacian_hero", "rating": 677}], "counters": [{"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 195}, {"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "lugia", "rating": 245}, {"opponent": "gyarados", "rating": 363}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 41257}, {"moveId": "CONFUSION", "uses": 35243}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 28880}, {"moveId": "PSYCHIC", "uses": 27227}, {"moveId": "FOCUS_BLAST", "uses": 20348}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PSYCHIC"], "score": 48.6}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 543, "matchups": [{"opponent": "ho_oh", "rating": 703, "opRating": 296}, {"opponent": "kyogre", "rating": 582, "opRating": 417}, {"opponent": "swampert", "rating": 541}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 527, "opRating": 472}, {"opponent": "yveltal", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "giratina_origin", "rating": 372}, {"opponent": "metagross", "rating": 404}, {"opponent": "lugia", "rating": 404}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 32311}, {"moveId": "QUICK_ATTACK", "uses": 26805}, {"moveId": "MUD_SLAP", "uses": 17373}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27646}, {"moveId": "GRASS_KNOT", "uses": 24981}, {"moveId": "BULLDOZE", "uses": 16515}, {"moveId": "PARABOLIC_CHARGE", "uses": 7255}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "GRASS_KNOT"], "score": 48.4}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 510, "matchups": [{"opponent": "gyarados", "rating": 785}, {"opponent": "charizard_shadow", "rating": 785, "opRating": 214}, {"opponent": "gyarado<PERSON>_shadow", "rating": 732, "opRating": 267}, {"opponent": "swampert", "rating": 728}, {"opponent": "kyogre", "rating": 679, "opRating": 320}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "metagross", "rating": 438}, {"opponent": "lugia", "rating": 454}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50348}, {"moveId": "TACKLE", "uses": 26152}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 49835}, {"moveId": "ENERGY_BALL", "uses": 20555}, {"moveId": "SWIFT", "uses": 6139}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ENERGY_BALL"], "score": 48.2}, {"speciesId": "sudowoodo_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 563, "matchups": [{"opponent": "darmanitan_standard", "rating": 831, "opRating": 168}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "moltres", "rating": 818, "opRating": 181}, {"opponent": "ho_oh_shadow", "rating": 805, "opRating": 194}, {"opponent": "moltres_shadow", "rating": 796, "opRating": 203}], "counters": [{"opponent": "metagross", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "dialga", "rating": 320}, {"opponent": "dragonite", "rating": 385}, {"opponent": "gyarados", "rating": 389}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44935}, {"moveId": "ROCK_THROW", "uses": 31565}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34376}, {"moveId": "EARTHQUAKE", "uses": 20983}, {"moveId": "STONE_EDGE", "uses": 13664}, {"moveId": "ROCK_TOMB", "uses": 7452}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 48.2}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 495, "matchups": [{"opponent": "rhyperior", "rating": 811, "opRating": 188}, {"opponent": "swampert", "rating": 726}, {"opponent": "landorus_incarnate", "rating": 686, "opRating": 313}, {"opponent": "kyogre", "rating": 665, "opRating": 334}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 659, "opRating": 340}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "zacian_hero", "rating": 387}, {"opponent": "metagross", "rating": 389}, {"opponent": "garcho<PERSON>", "rating": 438}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 48139}, {"moveId": "RAZOR_LEAF", "uses": 28361}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 21128}, {"moveId": "ENERGY_BALL", "uses": 16143}, {"moveId": "LEAF_STORM", "uses": 14059}, {"moveId": "HYDRO_PUMP", "uses": 13100}, {"moveId": "BLIZZARD", "uses": 7305}, {"moveId": "SOLAR_BEAM", "uses": 4631}]}, "moveset": ["BUBBLE", "ICE_BEAM", "ENERGY_BALL"], "score": 48}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 546, "matchups": [{"opponent": "bewear", "rating": 892, "opRating": 107}, {"opponent": "tangrowth", "rating": 857, "opRating": 142}, {"opponent": "tangrowth_shadow", "rating": 822, "opRating": 177}, {"opponent": "giratina_origin", "rating": 688}, {"opponent": "grou<PERSON>", "rating": 526}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 230}, {"opponent": "metagross", "rating": 255}, {"opponent": "excadrill", "rating": 332}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47263}, {"moveId": "STEEL_WING", "uses": 29237}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 49780}, {"moveId": "HYPER_BEAM", "uses": 17444}, {"moveId": "HEAT_WAVE", "uses": 9297}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 48}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 523, "matchups": [{"opponent": "gyarados", "rating": 771}, {"opponent": "charizard", "rating": 771, "opRating": 228}, {"opponent": "gyarado<PERSON>_shadow", "rating": 696, "opRating": 303}, {"opponent": "kyogre", "rating": 681, "opRating": 318}, {"opponent": "metagross", "rating": 519}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "mewtwo", "rating": 278}, {"opponent": "lugia", "rating": 290}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dragonite", "rating": 292}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 55478}, {"moveId": "ASTONISH", "uses": 21022}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34968}, {"moveId": "OMINOUS_WIND", "uses": 26250}, {"moveId": "THUNDER", "uses": 15165}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 47.7}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 567, "matchups": [{"opponent": "rai<PERSON>u", "rating": 875, "opRating": 124}, {"opponent": "raikou_shadow", "rating": 858, "opRating": 141}, {"opponent": "sylveon", "rating": 684, "opRating": 315}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 592}, {"opponent": "zacian_hero", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 250}, {"opponent": "lugia", "rating": 319}, {"opponent": "metagross", "rating": 479}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 25983}, {"moveId": "DOUBLE_KICK", "uses": 25802}, {"moveId": "FURY_CUTTER", "uses": 21148}, {"moveId": "IRON_TAIL", "uses": 3511}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 22481}, {"moveId": "MEGAHORN", "uses": 19146}, {"moveId": "SLUDGE_WAVE", "uses": 15596}, {"moveId": "EARTHQUAKE", "uses": 9686}, {"moveId": "SAND_TOMB", "uses": 9430}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 47.5}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 507, "matchups": [{"opponent": "swampert", "rating": 911}, {"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "tapu_fini", "rating": 850, "opRating": 149}, {"opponent": "kyogre", "rating": 695, "opRating": 304}, {"opponent": "sylveon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "gyarados", "rating": 371}, {"opponent": "zacian_hero", "rating": 439}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42825}, {"moveId": "ACID", "uses": 33675}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25853}, {"moveId": "MOONBLAST", "uses": 25576}, {"moveId": "PETAL_BLIZZARD", "uses": 17835}, {"moveId": "SOLAR_BEAM", "uses": 7064}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "MOONBLAST"], "score": 47.4}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 538, "matchups": [{"opponent": "metagross", "rating": 794}, {"opponent": "genesect_douse", "rating": 769, "opRating": 230}, {"opponent": "genesect_shock", "rating": 769, "opRating": 230}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 595}, {"opponent": "sylveon", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "excadrill", "rating": 346}, {"opponent": "gyarados", "rating": 363}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 41507}, {"moveId": "LICK", "uses": 34993}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 33507}, {"moveId": "THUNDER_PUNCH", "uses": 29705}, {"moveId": "POWER_UP_PUNCH", "uses": 13272}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "THUNDER_PUNCH"], "score": 47.3}, {"speciesId": "wailord", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "darmanitan_standard", "rating": 866, "opRating": 133}, {"opponent": "ma<PERSON><PERSON>", "rating": 860, "opRating": 139}, {"opponent": "mamos<PERSON>_shadow", "rating": 853, "opRating": 146}, {"opponent": "moltres_shadow", "rating": 837, "opRating": 162}, {"opponent": "excadrill", "rating": 598}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "metagross", "rating": 331}, {"opponent": "zacian_hero", "rating": 338}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66863}, {"moveId": "ZEN_HEADBUTT", "uses": 9638}], "chargedMoves": [{"moveId": "SURF", "uses": 46202}, {"moveId": "BLIZZARD", "uses": 20064}, {"moveId": "HYPER_BEAM", "uses": 10204}]}, "moveset": ["WATER_GUN", "SURF", "BLIZZARD"], "score": 47.3}, {"speciesId": "clawitzer", "speciesName": "Clawitzer", "rating": 522, "matchups": [{"opponent": "ho_oh", "rating": 838, "opRating": 161}, {"opponent": "ho_oh_shadow", "rating": 806, "opRating": 193}, {"opponent": "entei", "rating": 806, "opRating": 193}, {"opponent": "heatran", "rating": 797, "opRating": 202}, {"opponent": "excadrill", "rating": 531}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 307}, {"opponent": "dialga", "rating": 339}, {"opponent": "metagross", "rating": 488}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41710}, {"moveId": "SMACK_DOWN", "uses": 34790}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 29818}, {"moveId": "ICE_BEAM", "uses": 21617}, {"moveId": "DARK_PULSE", "uses": 20824}, {"moveId": "WATER_PULSE", "uses": 4381}]}, "moveset": ["WATER_GUN", "CRABHAMMER", "ICE_BEAM"], "score": 47.1}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 505, "matchups": [{"opponent": "swampert", "rating": 949}, {"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 880, "opRating": 119}, {"opponent": "tapu_fini", "rating": 824, "opRating": 175}, {"opponent": "kyogre", "rating": 589, "opRating": 410}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "garcho<PERSON>", "rating": 340}, {"opponent": "zacian_hero", "rating": 447}, {"opponent": "excadrill", "rating": 486}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42736}, {"moveId": "ASTONISH", "uses": 33764}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34127}, {"moveId": "ENERGY_BALL", "uses": 21336}, {"moveId": "SHADOW_SNEAK", "uses": 21080}]}, "moveset": ["RAZOR_LEAF", "BRAVE_BIRD", "ENERGY_BALL"], "score": 47.1}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 558, "matchups": [{"opponent": "genesect_douse", "rating": 848, "opRating": 151}, {"opponent": "genesect_shock", "rating": 848, "opRating": 151}, {"opponent": "genesect_chill", "rating": 848, "opRating": 151}, {"opponent": "genesect_burn", "rating": 848, "opRating": 151}, {"opponent": "genesect", "rating": 848, "opRating": 151}], "counters": [{"opponent": "mewtwo", "rating": 200}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dialga", "rating": 339}, {"opponent": "excadrill", "rating": 416}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38895}, {"moveId": "EMBER", "uses": 37605}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38243}, {"moveId": "RETURN", "uses": 16380}, {"moveId": "FLAMETHROWER", "uses": 14258}, {"moveId": "FIRE_BLAST", "uses": 7700}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "RETURN"], "score": 47}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 493, "matchups": [{"opponent": "gengar", "rating": 858, "opRating": 141}, {"opponent": "trevenant", "rating": 815, "opRating": 184}, {"opponent": "heracross", "rating": 748, "opRating": 251}, {"opponent": "virizion", "rating": 696, "opRating": 303}, {"opponent": "giratina_origin", "rating": 626}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "metagross", "rating": 284}, {"opponent": "gyarados", "rating": 288}, {"opponent": "mewtwo", "rating": 369}, {"opponent": "garcho<PERSON>", "rating": 396}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 45376}, {"moveId": "EXTRASENSORY", "uses": 31124}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33870}, {"moveId": "SHADOW_BALL", "uses": 23973}, {"moveId": "PSYCHIC", "uses": 14221}, {"moveId": "NIGHT_SHADE", "uses": 4428}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "SHADOW_BALL"], "score": 47}, {"speciesId": "masquerain", "speciesName": "Masquerain", "rating": 494, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 894, "opRating": 105}, {"opponent": "weavile_shadow", "rating": 869, "opRating": 130}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 866, "opRating": 133}, {"opponent": "obstagoon", "rating": 783, "opRating": 216}, {"opponent": "zarude", "rating": 691, "opRating": 308}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "metagross", "rating": 351}, {"opponent": "garcho<PERSON>", "rating": 375}, {"opponent": "mewtwo", "rating": 416}, {"opponent": "excadrill", "rating": 416}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 38652}, {"moveId": "AIR_SLASH", "uses": 37848}], "chargedMoves": [{"moveId": "LUNGE", "uses": 30543}, {"moveId": "OMINOUS_WIND", "uses": 14002}, {"moveId": "AIR_CUTTER", "uses": 11993}, {"moveId": "SILVER_WIND", "uses": 10939}, {"moveId": "BUBBLE_BEAM", "uses": 9099}]}, "moveset": ["INFESTATION", "LUNGE", "OMINOUS_WIND"], "score": 46.8}, {"speciesId": "pelipper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 538, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 742, "opRating": 257}, {"opponent": "buzzwole", "rating": 742, "opRating": 257}, {"opponent": "grou<PERSON>", "rating": 644}, {"opponent": "swampert", "rating": 538}, {"opponent": "excadrill", "rating": 517}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "metagross", "rating": 276}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "zacian_hero", "rating": 312}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 39004}, {"moveId": "WATER_GUN", "uses": 37496}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 40552}, {"moveId": "HURRICANE", "uses": 16154}, {"moveId": "BLIZZARD", "uses": 14580}, {"moveId": "HYDRO_PUMP", "uses": 5375}]}, "moveset": ["WING_ATTACK", "WEATHER_BALL_WATER", "HURRICANE"], "score": 46.8}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 510, "matchups": [{"opponent": "suicune_shadow", "rating": 895, "opRating": 104}, {"opponent": "kyogre", "rating": 738, "opRating": 261}, {"opponent": "swampert", "rating": 714}, {"opponent": "excadrill", "rating": 567}, {"opponent": "grou<PERSON>", "rating": 526}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "metagross", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "gyarados", "rating": 371}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44705}, {"moveId": "INFESTATION", "uses": 31795}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31624}, {"moveId": "SLUDGE_BOMB", "uses": 22200}, {"moveId": "POWER_WHIP", "uses": 15839}, {"moveId": "SOLAR_BEAM", "uses": 6649}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 46.8}, {"speciesId": "electrode_shadow", "speciesName": "Electrode (Shadow)", "rating": 494, "matchups": [{"opponent": "suicune_shadow", "rating": 855, "opRating": 144}, {"opponent": "walrein_shadow", "rating": 806, "opRating": 193}, {"opponent": "moltres", "rating": 700, "opRating": 299}, {"opponent": "moltres_shadow", "rating": 679, "opRating": 320}, {"opponent": "gyarados", "rating": 626}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "lugia", "rating": 321}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "metagross", "rating": 383}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33529}, {"moveId": "SPARK", "uses": 24403}, {"moveId": "TACKLE", "uses": 18573}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 29664}, {"moveId": "DISCHARGE", "uses": 25933}, {"moveId": "THUNDERBOLT", "uses": 11083}, {"moveId": "HYPER_BEAM", "uses": 9731}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 46.2}, {"speciesId": "be<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 531, "matchups": [{"opponent": "sneasler", "rating": 783, "opRating": 216}, {"opponent": "blaziken", "rating": 777, "opRating": 222}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 740, "opRating": 259}, {"opponent": "ho_oh", "rating": 737, "opRating": 262}, {"opponent": "zapdos", "rating": 628, "opRating": 371}], "counters": [{"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "dialga", "rating": 260}, {"opponent": "gyarados", "rating": 273}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "excadrill", "rating": 444}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 55680}, {"moveId": "ASTONISH", "uses": 20820}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29086}, {"moveId": "DARK_PULSE", "uses": 24809}, {"moveId": "PSYCHIC", "uses": 22751}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "DARK_PULSE"], "score": 46}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 516, "matchups": [{"opponent": "metagross", "rating": 777}, {"opponent": "genesect_douse", "rating": 746, "opRating": 253}, {"opponent": "genesect_shock", "rating": 746, "opRating": 253}, {"opponent": "genesect_chill", "rating": 746, "opRating": 253}, {"opponent": "genesect_burn", "rating": 746, "opRating": 253}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "gyarados", "rating": 237}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "mewtwo", "rating": 361}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48851}, {"moveId": "BITE", "uses": 27649}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38706}, {"moveId": "FLAMETHROWER", "uses": 29808}, {"moveId": "FIRE_BLAST", "uses": 8063}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 46}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 499, "matchups": [{"opponent": "swampert", "rating": 892}, {"opponent": "swampert_shadow", "rating": 880, "opRating": 119}, {"opponent": "sylveon", "rating": 578, "opRating": 421}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 543, "opRating": 456}, {"opponent": "zacian_hero", "rating": 523}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "gyarados", "rating": 304}, {"opponent": "excadrill", "rating": 395}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 44134}, {"moveId": "ACID", "uses": 32366}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 39424}, {"moveId": "SLUDGE_BOMB", "uses": 15212}, {"moveId": "LEAF_TORNADO", "uses": 7599}, {"moveId": "RETURN", "uses": 7450}, {"moveId": "ACID_SPRAY", "uses": 3460}, {"moveId": "SOLAR_BEAM", "uses": 3313}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 46}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 504, "matchups": [{"opponent": "swampert_shadow", "rating": 909, "opRating": 90}, {"opponent": "swampert", "rating": 880}, {"opponent": "florges", "rating": 694, "opRating": 305}, {"opponent": "kyogre", "rating": 630, "opRating": 369}, {"opponent": "sylveon", "rating": 590, "opRating": 409}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43469}, {"moveId": "ACID", "uses": 33031}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 43201}, {"moveId": "SLUDGE_BOMB", "uses": 17470}, {"moveId": "LEAF_TORNADO", "uses": 8222}, {"moveId": "ACID_SPRAY", "uses": 3901}, {"moveId": "SOLAR_BEAM", "uses": 3617}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 46}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 492, "matchups": [{"opponent": "suicune_shadow", "rating": 849, "opRating": 150}, {"opponent": "tapu_fini", "rating": 812, "opRating": 187}, {"opponent": "swampert", "rating": 736}, {"opponent": "swampert_shadow", "rating": 699, "opRating": 300}, {"opponent": "kyogre", "rating": 628, "opRating": 371}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "metagross", "rating": 302}, {"opponent": "excadrill", "rating": 441}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 50691}, {"moveId": "BITE", "uses": 25809}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36251}, {"moveId": "POWER_WHIP", "uses": 29638}, {"moveId": "ENERGY_BALL", "uses": 10665}]}, "moveset": ["VINE_WHIP", "CRUNCH", "POWER_WHIP"], "score": 45.9}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 501, "matchups": [{"opponent": "magnezone", "rating": 950, "opRating": 49}, {"opponent": "magnezone_shadow", "rating": 944, "opRating": 55}, {"opponent": "raikou_shadow", "rating": 875, "opRating": 125}, {"opponent": "nihilego", "rating": 857, "opRating": 142}, {"opponent": "xurkitree", "rating": 819, "opRating": 180}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "dialga", "rating": 418}, {"opponent": "excadrill", "rating": 425}, {"opponent": "metagross", "rating": 468}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38363}, {"moveId": "ROCK_THROW", "uses": 38137}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 22404}, {"moveId": "STONE_EDGE", "uses": 20709}, {"moveId": "ROCK_BLAST", "uses": 18697}, {"moveId": "ANCIENT_POWER", "uses": 14631}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "EARTHQUAKE", "STONE_EDGE"], "score": 45.9}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 505, "matchups": [{"opponent": "ursaring_shadow", "rating": 869, "opRating": 130}, {"opponent": "gengar", "rating": 841, "opRating": 158}, {"opponent": "venusaur_shadow", "rating": 802, "opRating": 197}, {"opponent": "trevenant", "rating": 757, "opRating": 242}, {"opponent": "giratina_origin", "rating": 647}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "lugia", "rating": 354}, {"opponent": "metagross", "rating": 424}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 47959}, {"moveId": "STEEL_WING", "uses": 28541}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34428}, {"moveId": "DRILL_PECK", "uses": 31299}, {"moveId": "AERIAL_ACE", "uses": 6889}, {"moveId": "AIR_CUTTER", "uses": 3825}]}, "moveset": ["FEINT_ATTACK", "BRAVE_BIRD", "DRILL_PECK"], "score": 45.5}, {"speciesId": "hoopa", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 518, "matchups": [{"opponent": "heracross", "rating": 926, "opRating": 73}, {"opponent": "machamp", "rating": 920, "opRating": 79}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 907, "opRating": 92}, {"opponent": "zap<PERSON>_galarian", "rating": 707, "opRating": 292}, {"opponent": "zacian_hero", "rating": 589}], "counters": [{"opponent": "mewtwo", "rating": 174}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "dialga", "rating": 220}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "gyarados", "rating": 327}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 52600}, {"moveId": "ASTONISH", "uses": 23900}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 43481}, {"moveId": "PSYCHIC", "uses": 27344}, {"moveId": "PSYBEAM", "uses": 5670}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 45.5}, {"speciesId": "simipour", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 513, "matchups": [{"opponent": "land<PERSON><PERSON>_therian", "rating": 829, "opRating": 170}, {"opponent": "ma<PERSON><PERSON>", "rating": 829, "opRating": 170}, {"opponent": "moltres", "rating": 826, "opRating": 173}, {"opponent": "mamos<PERSON>_shadow", "rating": 795, "opRating": 204}, {"opponent": "moltres_shadow", "rating": 783, "opRating": 216}], "counters": [{"opponent": "dialga", "rating": 127}, {"opponent": "giratina_origin", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "zacian_hero", "rating": 343}, {"opponent": "metagross", "rating": 401}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 48607}, {"moveId": "BITE", "uses": 27893}], "chargedMoves": [{"moveId": "SURF", "uses": 39485}, {"moveId": "CRUNCH", "uses": 30678}, {"moveId": "HYDRO_PUMP", "uses": 6309}]}, "moveset": ["WATER_GUN", "SURF", "CRUNCH"], "score": 45.4}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 485, "matchups": [{"opponent": "swampert", "rating": 923}, {"opponent": "swampert_shadow", "rating": 911, "opRating": 88}, {"opponent": "kyogre", "rating": 539, "opRating": 460}, {"opponent": "sylveon", "rating": 539, "opRating": 460}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "excadrill", "rating": 448}, {"opponent": "zacian_hero", "rating": 456}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43383}, {"moveId": "ACID", "uses": 33117}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 21930}, {"moveId": "SLUDGE_BOMB", "uses": 21802}, {"moveId": "PETAL_BLIZZARD", "uses": 15469}, {"moveId": "RETURN", "uses": 10996}, {"moveId": "SOLAR_BEAM", "uses": 6381}]}, "moveset": ["RAZOR_LEAF", "MOONBLAST", "SLUDGE_BOMB"], "score": 45.1}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 517, "matchups": [{"opponent": "articuno_galarian", "rating": 786, "opRating": 213}, {"opponent": "blaziken", "rating": 707, "opRating": 292}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 692, "opRating": 307}, {"opponent": "sneasler", "rating": 658, "opRating": 341}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 560}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "grou<PERSON>", "rating": 426}, {"opponent": "zacian_hero", "rating": 473}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42752}, {"moveId": "EXTRASENSORY", "uses": 33748}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 35878}, {"moveId": "THUNDER", "uses": 28079}, {"moveId": "SWIFT", "uses": 12531}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "THUNDER"], "score": 44.9}, {"speciesId": "mantine", "speciesName": "<PERSON><PERSON>", "rating": 469, "matchups": [{"opponent": "heracross", "rating": 828, "opRating": 171}, {"opponent": "gliscor_shadow", "rating": 805, "opRating": 194}, {"opponent": "roserade", "rating": 674, "opRating": 325}, {"opponent": "buzzwole", "rating": 661, "opRating": 338}, {"opponent": "grou<PERSON>", "rating": 597}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "gyarados", "rating": 301}, {"opponent": "garcho<PERSON>", "rating": 467}, {"opponent": "swampert", "rating": 495}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 26883}, {"moveId": "BUBBLE", "uses": 26157}, {"moveId": "BULLET_SEED", "uses": 23480}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26865}, {"moveId": "AERIAL_ACE", "uses": 24171}, {"moveId": "BUBBLE_BEAM", "uses": 13000}, {"moveId": "WATER_PULSE", "uses": 12440}]}, "moveset": ["WING_ATTACK", "ICE_BEAM", "AERIAL_ACE"], "score": 44.7}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 505, "matchups": [{"opponent": "sneasler", "rating": 787, "opRating": 212}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 779, "opRating": 220}, {"opponent": "sylveon", "rating": 543, "opRating": 456}, {"opponent": "zacian_hero", "rating": 531}, {"opponent": "grou<PERSON>", "rating": 505}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "dialga", "rating": 176}, {"opponent": "gyarados", "rating": 273}, {"opponent": "dragonite", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 330}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42833}, {"moveId": "EXTRASENSORY", "uses": 33667}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 33687}, {"moveId": "BLIZZARD", "uses": 30331}, {"moveId": "SWIFT", "uses": 12395}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "BLIZZARD"], "score": 44.6}, {"speciesId": "musharna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 502, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 675, "opRating": 324}, {"opponent": "machamp_shadow", "rating": 615, "opRating": 384}, {"opponent": "sneasler", "rating": 611, "opRating": 388}, {"opponent": "kommo_o", "rating": 571, "opRating": 428}, {"opponent": "terrakion", "rating": 553, "opRating": 446}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "garcho<PERSON>", "rating": 288}, {"opponent": "gyarados", "rating": 373}, {"opponent": "zacian_hero", "rating": 378}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 51870}, {"moveId": "ZEN_HEADBUTT", "uses": 24630}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 42382}, {"moveId": "DAZZLING_GLEAM", "uses": 19894}, {"moveId": "FUTURE_SIGHT", "uses": 14235}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 44.3}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 520, "matchups": [{"opponent": "florges", "rating": 737, "opRating": 262}, {"opponent": "sylveon", "rating": 719, "opRating": 280}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 688}, {"opponent": "buzzwole", "rating": 664, "opRating": 335}, {"opponent": "zacian_hero", "rating": 646}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 198}, {"opponent": "gyarados", "rating": 237}, {"opponent": "lugia", "rating": 266}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31704}, {"moveId": "HEX", "uses": 30457}, {"moveId": "ACID", "uses": 14365}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32813}, {"moveId": "FUTURE_SIGHT", "uses": 24288}, {"moveId": "SLUDGE_WAVE", "uses": 19394}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 44.1}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 524, "matchups": [{"opponent": "weavile_shadow", "rating": 855, "opRating": 144}, {"opponent": "gyarado<PERSON>_shadow", "rating": 744, "opRating": 255}, {"opponent": "gyarados", "rating": 687}, {"opponent": "zap<PERSON>_galarian", "rating": 593, "opRating": 406}, {"opponent": "kyogre", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "mewtwo", "rating": 195}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "lugia", "rating": 316}, {"opponent": "metagross", "rating": 337}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 60392}, {"moveId": "LOW_KICK", "uses": 16108}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 34821}, {"moveId": "RETURN", "uses": 17571}, {"moveId": "THUNDERBOLT", "uses": 12959}, {"moveId": "THUNDER", "uses": 11163}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 44}, {"speciesId": "weezing_shadow", "speciesName": "Weez<PERSON> (Shadow)", "rating": 453, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 775, "opRating": 224}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 738, "opRating": 261}, {"opponent": "mewtwo_shadow", "rating": 694, "opRating": 305}, {"opponent": "trevenant", "rating": 684, "opRating": 315}, {"opponent": "latios_shadow", "rating": 593, "opRating": 406}], "counters": [{"opponent": "dialga", "rating": 247}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "metagross", "rating": 404}, {"opponent": "mewtwo", "rating": 460}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 42071}, {"moveId": "TACKLE", "uses": 34429}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 21611}, {"moveId": "DARK_PULSE", "uses": 20126}, {"moveId": "SLUDGE_BOMB", "uses": 19848}, {"moveId": "THUNDERBOLT", "uses": 14938}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 43.5}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 520, "matchups": [{"opponent": "nidoqueen", "rating": 868, "opRating": 131}, {"opponent": "articuno_galarian", "rating": 809, "opRating": 190}, {"opponent": "nihilego", "rating": 687, "opRating": 312}, {"opponent": "roserade", "rating": 664, "opRating": 335}, {"opponent": "sylveon", "rating": 546, "opRating": 453}], "counters": [{"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "dialga", "rating": 247}, {"opponent": "dragonite", "rating": 329}, {"opponent": "gyarados", "rating": 353}, {"opponent": "zacian_hero", "rating": 473}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42980}, {"moveId": "FEINT_ATTACK", "uses": 33520}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 20984}, {"moveId": "PAYBACK", "uses": 17236}, {"moveId": "HEAVY_SLAM", "uses": 15112}, {"moveId": "BULLDOZE", "uses": 10084}, {"moveId": "PSYCHIC", "uses": 8141}, {"moveId": "FLASH_CANNON", "uses": 4836}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 43.2}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 432, "matchups": [{"opponent": "pangoro", "rating": 901, "opRating": 98}, {"opponent": "weavile", "rating": 863, "opRating": 136}, {"opponent": "weavile_shadow", "rating": 843, "opRating": 156}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "kommo_o", "rating": 710, "opRating": 289}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "dragonite", "rating": 382}, {"opponent": "garcho<PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 6474}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 5472}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5349}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4877}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4715}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4643}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4549}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4396}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4387}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4289}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4219}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4185}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4152}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3848}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3823}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3707}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3588}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 35992}, {"moveId": "HYPER_BEAM", "uses": 26093}, {"moveId": "SOLAR_BEAM", "uses": 14577}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 43.2}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 491, "matchups": [{"opponent": "rhyperior", "rating": 808, "opRating": 191}, {"opponent": "tapu_fini", "rating": 795, "opRating": 204}, {"opponent": "swampert", "rating": 775}, {"opponent": "swampert_shadow", "rating": 714, "opRating": 285}, {"opponent": "ma<PERSON><PERSON>", "rating": 694, "opRating": 305}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "gyarados", "rating": 317}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "excadrill", "rating": 437}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44242}, {"moveId": "INFESTATION", "uses": 32258}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 26994}, {"moveId": "SLUDGE_BOMB", "uses": 17769}, {"moveId": "POWER_WHIP", "uses": 13499}, {"moveId": "RETURN", "uses": 12434}, {"moveId": "SOLAR_BEAM", "uses": 5685}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 43.2}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 476, "matchups": [{"opponent": "heracross", "rating": 831, "opRating": 168}, {"opponent": "rhyperior", "rating": 791, "opRating": 208}, {"opponent": "swampert", "rating": 716}, {"opponent": "virizion", "rating": 708, "opRating": 291}, {"opponent": "swampert_shadow", "rating": 665, "opRating": 334}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "zacian_hero", "rating": 367}, {"opponent": "garcho<PERSON>", "rating": 373}, {"opponent": "gyarados", "rating": 389}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 41711}, {"moveId": "RAZOR_LEAF", "uses": 34789}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 45796}, {"moveId": "AERIAL_ACE", "uses": 16485}, {"moveId": "STOMP", "uses": 14209}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 43.1}, {"speciesId": "vanilluxe", "speciesName": "Vanilluxe", "rating": 435, "matchups": [{"opponent": "flygon_shadow", "rating": 908, "opRating": 91}, {"opponent": "gliscor_shadow", "rating": 876, "opRating": 123}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 857, "opRating": 142}, {"opponent": "dragonite", "rating": 617}, {"opponent": "dragonite_shadow", "rating": 617, "opRating": 382}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 285}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 490}], "moves": {"fastMoves": [{"moveId": "FROST_BREATH", "uses": 54145}, {"moveId": "ASTONISH", "uses": 22355}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 38173}, {"moveId": "SIGNAL_BEAM", "uses": 23023}, {"moveId": "FLASH_CANNON", "uses": 15326}]}, "moveset": ["FROST_BREATH", "BLIZZARD", "SIGNAL_BEAM"], "score": 43}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 465, "matchups": [{"opponent": "honchk<PERSON>_shadow", "rating": 823, "opRating": 176}, {"opponent": "gyarados", "rating": 700}, {"opponent": "moltres_shadow", "rating": 700, "opRating": 299}, {"opponent": "charizard_shadow", "rating": 700, "opRating": 299}, {"opponent": "gyarado<PERSON>_shadow", "rating": 626, "opRating": 373}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "lugia", "rating": 264}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "metagross", "rating": 331}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33356}, {"moveId": "SPARK", "uses": 24389}, {"moveId": "TACKLE", "uses": 18760}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 26921}, {"moveId": "DISCHARGE", "uses": 23894}, {"moveId": "RETURN", "uses": 11023}, {"moveId": "THUNDERBOLT", "uses": 10163}, {"moveId": "HYPER_BEAM", "uses": 4338}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 42.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "sneasler", "rating": 767, "opRating": 232}, {"opponent": "ho_oh_shadow", "rating": 726, "opRating": 273}, {"opponent": "electivire", "rating": 726, "opRating": 273}, {"opponent": "magmortar_shadow", "rating": 684, "opRating": 315}, {"opponent": "terrakion", "rating": 585, "opRating": 414}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dialga", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "zacian_hero", "rating": 372}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43546}, {"moveId": "CHARM", "uses": 32954}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 37382}, {"moveId": "PSYCHIC", "uses": 27302}, {"moveId": "FUTURE_SIGHT", "uses": 11793}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 42.5}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 466, "matchups": [{"opponent": "feraligatr_shadow", "rating": 753, "opRating": 246}, {"opponent": "xurkitree", "rating": 676, "opRating": 323}, {"opponent": "samu<PERSON>t", "rating": 665, "opRating": 334}, {"opponent": "metagross_shadow", "rating": 590, "opRating": 409}, {"opponent": "electivire_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "dialga", "rating": 269}, {"opponent": "giratina_origin", "rating": 316}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "metagross", "rating": 427}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52204}, {"moveId": "ASTONISH", "uses": 24296}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 27411}, {"moveId": "GRASS_KNOT", "uses": 26755}, {"moveId": "SLUDGE_BOMB", "uses": 22287}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 42.4}, {"speciesId": "gur<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 516, "matchups": [{"opponent": "weavile", "rating": 898, "opRating": 101}, {"opponent": "weavile_shadow", "rating": 879, "opRating": 120}, {"opponent": "porygon_z_shadow", "rating": 862, "opRating": 137}, {"opponent": "tapu_bulu", "rating": 859, "opRating": 140}, {"opponent": "moltres", "rating": 778, "opRating": 221}], "counters": [{"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 187}, {"opponent": "metagross", "rating": 229}, {"opponent": "excadrill", "rating": 374}, {"opponent": "zacian_hero", "rating": 375}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56881}, {"moveId": "LOW_KICK", "uses": 19619}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 34046}, {"moveId": "STONE_EDGE", "uses": 32501}, {"moveId": "LOW_SWEEP", "uses": 10105}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 42.4}, {"speciesId": "hypno_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 470, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 761, "opRating": 238}, {"opponent": "sneasler", "rating": 744, "opRating": 255}, {"opponent": "machamp_shadow", "rating": 660, "opRating": 339}, {"opponent": "landorus_incarnate", "rating": 609, "opRating": 390}, {"opponent": "zap<PERSON>_galarian", "rating": 550, "opRating": 449}], "counters": [{"opponent": "mewtwo", "rating": 218}, {"opponent": "dialga", "rating": 239}, {"opponent": "giratina_origin", "rating": 254}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "garcho<PERSON>", "rating": 368}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66997}, {"moveId": "ZEN_HEADBUTT", "uses": 9503}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14860}, {"moveId": "PSYSHOCK", "uses": 11934}, {"moveId": "SHADOW_BALL", "uses": 11547}, {"moveId": "FIRE_PUNCH", "uses": 11226}, {"moveId": "THUNDER_PUNCH", "uses": 11031}, {"moveId": "FOCUS_BLAST", "uses": 7316}, {"moveId": "PSYCHIC", "uses": 4605}, {"moveId": "FUTURE_SIGHT", "uses": 3952}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 42.2}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 471, "matchups": [{"opponent": "bewear", "rating": 916, "opRating": 83}, {"opponent": "golem", "rating": 875, "opRating": 124}, {"opponent": "snor<PERSON>_shadow", "rating": 694, "opRating": 305}, {"opponent": "hydreigon", "rating": 577, "opRating": 422}, {"opponent": "excadrill", "rating": 563}], "counters": [{"opponent": "mewtwo", "rating": 140}, {"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "metagross", "rating": 357}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 54529}, {"moveId": "LOW_KICK", "uses": 11963}, {"moveId": "POUND", "uses": 9974}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 34750}, {"moveId": "FOCUS_BLAST", "uses": 20943}, {"moveId": "HYPER_BEAM", "uses": 20785}]}, "moveset": ["DOUBLE_KICK", "FIRE_PUNCH", "FOCUS_BLAST"], "score": 42.2}, {"speciesId": "lair<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 465, "matchups": [{"opponent": "porygon_z_shadow", "rating": 890, "opRating": 109}, {"opponent": "charizard_shadow", "rating": 753, "opRating": 246}, {"opponent": "moltres", "rating": 725, "opRating": 274}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609, "opRating": 390}, {"opponent": "sylveon", "rating": 573, "opRating": 426}], "counters": [{"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "dialga", "rating": 233}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "lugia", "rating": 430}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54741}, {"moveId": "IRON_TAIL", "uses": 21759}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28339}, {"moveId": "BODY_SLAM", "uses": 27334}, {"moveId": "HEAVY_SLAM", "uses": 14736}, {"moveId": "ROCK_TOMB", "uses": 6082}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 42.1}, {"speciesId": "slowbro", "speciesName": "Slowbro", "rating": 458, "matchups": [{"opponent": "sneasler", "rating": 788, "opRating": 211}, {"opponent": "blaziken", "rating": 703, "opRating": 296}, {"opponent": "mamos<PERSON>_shadow", "rating": 649, "opRating": 350}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 543}, {"opponent": "garcho<PERSON>", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "lugia", "rating": 242}, {"opponent": "zacian_hero", "rating": 462}, {"opponent": "swampert", "rating": 470}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38606}, {"moveId": "WATER_GUN", "uses": 37894}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26532}, {"moveId": "PSYCHIC", "uses": 23954}, {"moveId": "WATER_PULSE", "uses": 13233}, {"moveId": "RETURN", "uses": 12785}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 42}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 484, "matchups": [{"opponent": "barbara<PERSON>", "rating": 854, "opRating": 145}, {"opponent": "golem", "rating": 808, "opRating": 191}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 805, "opRating": 194}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 773, "opRating": 226}, {"opponent": "pinsir_shadow", "rating": 610, "opRating": 389}], "counters": [{"opponent": "dialga", "rating": 195}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "gyarados", "rating": 265}, {"opponent": "zacian_hero", "rating": 398}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 63124}, {"moveId": "TAKE_DOWN", "uses": 13376}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 38572}, {"moveId": "SEED_BOMB", "uses": 20680}, {"moveId": "GUNK_SHOT", "uses": 12004}, {"moveId": "ACID_SPRAY", "uses": 5237}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 41.9}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 439, "matchups": [{"opponent": "exeggutor", "rating": 818, "opRating": 181}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 812, "opRating": 187}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 775, "opRating": 224}, {"opponent": "exeggutor_shadow", "rating": 775, "opRating": 224}, {"opponent": "zacian_hero", "rating": 506}], "counters": [{"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "dialga", "rating": 233}, {"opponent": "giratina_origin", "rating": 245}, {"opponent": "metagross", "rating": 354}, {"opponent": "mewtwo", "rating": 372}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 33039}, {"moveId": "TACKLE", "uses": 26653}, {"moveId": "ACID", "uses": 16775}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 19305}, {"moveId": "DARK_PULSE", "uses": 17904}, {"moveId": "SLUDGE_BOMB", "uses": 17427}, {"moveId": "THUNDERBOLT", "uses": 13275}, {"moveId": "RETURN", "uses": 8581}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 41.4}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 451, "matchups": [{"opponent": "barbara<PERSON>", "rating": 816, "opRating": 183}, {"opponent": "manectric_shadow", "rating": 694, "opRating": 305}, {"opponent": "tapu_fini", "rating": 631, "opRating": 368}, {"opponent": "swampert", "rating": 581}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 554, "opRating": 445}], "counters": [{"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "dialga", "rating": 236}, {"opponent": "gyarados", "rating": 301}, {"opponent": "zacian_hero", "rating": 329}, {"opponent": "metagross", "rating": 354}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52411}, {"moveId": "ASTONISH", "uses": 24089}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 24236}, {"moveId": "GRASS_KNOT", "uses": 23831}, {"moveId": "SLUDGE_BOMB", "uses": 19279}, {"moveId": "RETURN", "uses": 9141}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 41.3}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 432, "matchups": [{"opponent": "<PERSON>rserker", "rating": 937, "opRating": 62}, {"opponent": "heracross", "rating": 723, "opRating": 276}, {"opponent": "obstagoon", "rating": 721, "opRating": 278}, {"opponent": "trevenant", "rating": 684, "opRating": 315}, {"opponent": "excadrill", "rating": 616}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "metagross", "rating": 343}, {"opponent": "dialga", "rating": 448}, {"opponent": "giratina_origin", "rating": 482}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 51811}, {"moveId": "STEEL_WING", "uses": 24689}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 28722}, {"moveId": "CLOSE_COMBAT", "uses": 26573}, {"moveId": "ROCK_SLIDE", "uses": 17801}, {"moveId": "HEAT_WAVE", "uses": 3401}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 41.3}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 462, "matchups": [{"opponent": "exeggutor_shadow", "rating": 939, "opRating": 60}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 891, "opRating": 108}, {"opponent": "honchk<PERSON>_shadow", "rating": 863, "opRating": 136}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 856, "opRating": 143}, {"opponent": "zarude", "rating": 662, "opRating": 337}], "counters": [{"opponent": "dialga", "rating": 168}, {"opponent": "gyarados", "rating": 247}, {"opponent": "metagross", "rating": 311}, {"opponent": "garcho<PERSON>", "rating": 377}, {"opponent": "mewtwo", "rating": 427}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 22464}, {"moveId": "BUG_BITE", "uses": 19356}, {"moveId": "POISON_STING", "uses": 17583}, {"moveId": "AIR_SLASH", "uses": 17121}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 29863}, {"moveId": "BUG_BUZZ", "uses": 19500}, {"moveId": "POWER_GEM", "uses": 14029}, {"moveId": "SIGNAL_BEAM", "uses": 8404}, {"moveId": "FELL_STINGER", "uses": 4907}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 41.1}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 463, "matchups": [{"opponent": "gengar", "rating": 804, "opRating": 195}, {"opponent": "sneasler", "rating": 719, "opRating": 280}, {"opponent": "roserade", "rating": 688, "opRating": 311}, {"opponent": "darmanitan_standard", "rating": 628, "opRating": 371}, {"opponent": "giratina_origin", "rating": 540}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "dialga", "rating": 206}, {"opponent": "garcho<PERSON>", "rating": 267}, {"opponent": "gyarados", "rating": 317}, {"opponent": "dragonite", "rating": 335}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 38999}, {"moveId": "POUND", "uses": 37501}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 29324}, {"moveId": "HYPER_BEAM", "uses": 24670}, {"moveId": "DAZZLING_GLEAM", "uses": 22496}]}, "moveset": ["ZEN_HEADBUTT", "PSYCHIC", "HYPER_BEAM"], "score": 41}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 426, "matchups": [{"opponent": "gengar", "rating": 843, "opRating": 156}, {"opponent": "manectric", "rating": 805, "opRating": 194}, {"opponent": "infernape", "rating": 765, "opRating": 234}, {"opponent": "victini", "rating": 567, "opRating": 432}, {"opponent": "giratina_origin", "rating": 553}], "counters": [{"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "dialga", "rating": 241}, {"opponent": "lugia", "rating": 273}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "gyarados", "rating": 353}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 39510}, {"moveId": "TACKLE", "uses": 36990}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 53110}, {"moveId": "BULLDOZE", "uses": 14677}, {"moveId": "GUNK_SHOT", "uses": 8773}]}, "moveset": ["LICK", "BODY_SLAM", "BULLDOZE"], "score": 41}, {"speciesId": "deoxys_speed", "speciesName": "<PERSON><PERSON><PERSON> (Speed)", "rating": 461, "matchups": [{"opponent": "blaziken", "rating": 779, "opRating": 220}, {"opponent": "charizard", "rating": 712, "opRating": 287}, {"opponent": "moltres_shadow", "rating": 681, "opRating": 318}, {"opponent": "machamp_shadow", "rating": 633, "opRating": 366}, {"opponent": "gyarados", "rating": 543}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "mewtwo", "rating": 213}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "metagross", "rating": 290}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 59298}, {"moveId": "ZEN_HEADBUTT", "uses": 17202}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 43923}, {"moveId": "THUNDERBOLT", "uses": 23687}, {"moveId": "SWIFT", "uses": 8858}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 40.7}, {"speciesId": "hypno", "speciesName": "Hypno", "rating": 451, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 800, "opRating": 199}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "machamp", "rating": 648, "opRating": 351}, {"opponent": "roserade", "rating": 634, "opRating": 365}, {"opponent": "terrakion", "rating": 592, "opRating": 407}], "counters": [{"opponent": "mewtwo", "rating": 187}, {"opponent": "dialga", "rating": 220}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "dragonite", "rating": 345}, {"opponent": "garcho<PERSON>", "rating": 354}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66888}, {"moveId": "ZEN_HEADBUTT", "uses": 9612}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14023}, {"moveId": "PSYSHOCK", "uses": 11242}, {"moveId": "SHADOW_BALL", "uses": 10881}, {"moveId": "FIRE_PUNCH", "uses": 10627}, {"moveId": "THUNDER_PUNCH", "uses": 10368}, {"moveId": "FOCUS_BLAST", "uses": 6921}, {"moveId": "RETURN", "uses": 4404}, {"moveId": "PSYCHIC", "uses": 4387}, {"moveId": "FUTURE_SIGHT", "uses": 3793}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 40.7}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 444, "matchups": [{"opponent": "heracross", "rating": 886, "opRating": 113}, {"opponent": "virizion", "rating": 695, "opRating": 304}, {"opponent": "buzzwole", "rating": 630, "opRating": 369}, {"opponent": "garcho<PERSON>", "rating": 544}, {"opponent": "sylveon", "rating": 537, "opRating": 462}], "counters": [{"opponent": "gyarados", "rating": 188}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "dialga", "rating": 326}, {"opponent": "grou<PERSON>", "rating": 491}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 64006}, {"moveId": "ZEN_HEADBUTT", "uses": 12494}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 38298}, {"moveId": "PSYCHIC", "uses": 17146}, {"moveId": "OMINOUS_WIND", "uses": 11681}, {"moveId": "DAZZLING_GLEAM", "uses": 9351}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "PSYCHIC"], "score": 40.4}, {"speciesId": "comfey", "speciesName": "Comfey", "rating": 435, "matchups": [{"opponent": "golem", "rating": 879, "opRating": 120}, {"opponent": "pangoro", "rating": 713, "opRating": 286}, {"opponent": "swampert", "rating": 643}, {"opponent": "swampert_shadow", "rating": 596, "opRating": 403}, {"opponent": "dragonite_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "excadrill", "rating": 348}, {"opponent": "gyarados", "rating": 407}, {"opponent": "dragonite", "rating": 444}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 8304}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5381}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4941}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4724}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4592}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4509}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4435}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4370}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4232}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4182}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4173}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4058}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3793}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3726}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3692}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3685}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3485}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 38044}, {"moveId": "DRAINING_KISS", "uses": 28554}, {"moveId": "PETAL_BLIZZARD", "uses": 9817}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "DRAINING_KISS"], "score": 40.3}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 461, "matchups": [{"opponent": "trevenant", "rating": 795, "opRating": 204}, {"opponent": "gengar", "rating": 735, "opRating": 264}, {"opponent": "tapu_bulu", "rating": 698, "opRating": 301}, {"opponent": "heracross", "rating": 684, "opRating": 315}, {"opponent": "giratina_origin", "rating": 551}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "excadrill", "rating": 374}, {"opponent": "grou<PERSON>", "rating": 453}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 25054}, {"moveId": "WING_ATTACK", "uses": 22213}, {"moveId": "AIR_SLASH", "uses": 17701}, {"moveId": "STEEL_WING", "uses": 11376}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 42064}, {"moveId": "AERIAL_ACE", "uses": 16981}, {"moveId": "HURRICANE", "uses": 7650}, {"moveId": "FEATHER_DANCE", "uses": 5343}, {"moveId": "AIR_CUTTER", "uses": 4590}]}, "moveset": ["GUST", "BRAVE_BIRD", "AERIAL_ACE"], "score": 40.2}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 433, "matchups": [{"opponent": "gengar", "rating": 761, "opRating": 238}, {"opponent": "empoleon", "rating": 761, "opRating": 238}, {"opponent": "mew", "rating": 691, "opRating": 308}, {"opponent": "articuno_galarian", "rating": 680, "opRating": 319}, {"opponent": "giratina_origin", "rating": 537}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "dialga", "rating": 260}, {"opponent": "lugia", "rating": 345}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "metagross", "rating": 412}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 59850}, {"moveId": "TAKE_DOWN", "uses": 16650}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35227}, {"moveId": "MEGAHORN", "uses": 19471}, {"moveId": "SOLAR_BEAM", "uses": 11483}, {"moveId": "HYPER_BEAM", "uses": 10238}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "MEGAHORN"], "score": 40}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 443, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 799, "opRating": 200}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 759, "opRating": 240}, {"opponent": "espeon", "rating": 748, "opRating": 251}, {"opponent": "metagross_shadow", "rating": 618, "opRating": 381}, {"opponent": "mewtwo", "rating": 515}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "excadrill", "rating": 409}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40468}, {"moveId": "FEINT_ATTACK", "uses": 36032}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 42019}, {"moveId": "SHADOW_SNEAK", "uses": 23730}, {"moveId": "OMINOUS_WIND", "uses": 10763}]}, "moveset": ["SUCKER_PUNCH", "SHADOW_BALL", "SHADOW_SNEAK"], "score": 40}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 453, "matchups": [{"opponent": "blaziken", "rating": 850, "opRating": 149}, {"opponent": "machamp_shadow", "rating": 777, "opRating": 222}, {"opponent": "sneasler", "rating": 762, "opRating": 237}, {"opponent": "zap<PERSON>_galarian", "rating": 719, "opRating": 280}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 719, "opRating": 280}], "counters": [{"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "gyarados", "rating": 280}, {"opponent": "zacian_hero", "rating": 349}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43161}, {"moveId": "EXTRASENSORY", "uses": 33339}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 39685}, {"moveId": "FIRE_BLAST", "uses": 22578}, {"moveId": "SWIFT", "uses": 14269}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "FIRE_BLAST"], "score": 39.9}, {"speciesId": "deoxys", "speciesName": "Deoxys", "rating": 462, "matchups": [{"opponent": "magmortar_shadow", "rating": 799, "opRating": 200}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 775, "opRating": 224}, {"opponent": "zap<PERSON>_galarian", "rating": 641, "opRating": 358}, {"opponent": "ho_oh", "rating": 633, "opRating": 366}, {"opponent": "yveltal", "rating": 586, "opRating": 413}], "counters": [{"opponent": "mewtwo", "rating": 276}, {"opponent": "garcho<PERSON>", "rating": 291}, {"opponent": "lugia", "rating": 364}, {"opponent": "metagross", "rating": 372}, {"opponent": "zacian_hero", "rating": 375}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 60266}, {"moveId": "ZEN_HEADBUTT", "uses": 16234}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 42149}, {"moveId": "THUNDERBOLT", "uses": 22791}, {"moveId": "HYPER_BEAM", "uses": 11541}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 39.8}, {"speciesId": "octillery", "speciesName": "Octillery", "rating": 424, "matchups": [{"opponent": "honchk<PERSON>_shadow", "rating": 875, "opRating": 125}, {"opponent": "gliscor_shadow", "rating": 814, "opRating": 185}, {"opponent": "chandelure", "rating": 783, "opRating": 216}, {"opponent": "moltres_shadow", "rating": 777, "opRating": 222}, {"opponent": "charizard", "rating": 679, "opRating": 320}], "counters": [{"opponent": "mewtwo", "rating": 197}, {"opponent": "dialga", "rating": 209}, {"opponent": "metagross", "rating": 290}, {"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "dragonite", "rating": 420}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 27013}, {"moveId": "WATER_GUN", "uses": 24920}, {"moveId": "MUD_SHOT", "uses": 24556}], "chargedMoves": [{"moveId": "OCTAZOOKA", "uses": 26802}, {"moveId": "AURORA_BEAM", "uses": 21826}, {"moveId": "GUNK_SHOT", "uses": 13787}, {"moveId": "WATER_PULSE", "uses": 8014}, {"moveId": "ACID_SPRAY", "uses": 5953}]}, "moveset": ["LOCK_ON", "OCTAZOOKA", "AURORA_BEAM"], "score": 39.8}, {"speciesId": "slowking", "speciesName": "Slowking", "rating": 459, "matchups": [{"opponent": "sneasler", "rating": 788, "opRating": 211}, {"opponent": "blaziken", "rating": 703, "opRating": 296}, {"opponent": "mamos<PERSON>_shadow", "rating": 649, "opRating": 350}, {"opponent": "ma<PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "machamp_shadow", "rating": 603, "opRating": 396}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 408}, {"opponent": "zacian_hero", "rating": 462}, {"opponent": "swampert", "rating": 470}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38615}, {"moveId": "WATER_GUN", "uses": 37885}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 26851}, {"moveId": "BLIZZARD", "uses": 21445}, {"moveId": "RETURN", "uses": 14137}, {"moveId": "FIRE_BLAST", "uses": 14101}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 39.8}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 399, "matchups": [{"opponent": "pangoro", "rating": 890, "opRating": 109}, {"opponent": "honch<PERSON><PERSON>", "rating": 847, "opRating": 152}, {"opponent": "honchk<PERSON>_shadow", "rating": 832, "opRating": 167}, {"opponent": "weavile_shadow", "rating": 804, "opRating": 195}, {"opponent": "giratina_origin", "rating": 609}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "metagross", "rating": 261}, {"opponent": "dragonite", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 64591}, {"moveId": "POUND", "uses": 11909}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38734}, {"moveId": "THUNDERBOLT", "uses": 21568}, {"moveId": "HYPER_BEAM", "uses": 16162}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 39.7}, {"speciesId": "lunatone", "speciesName": "Lunatone", "rating": 443, "matchups": [{"opponent": "ho_oh", "rating": 803}, {"opponent": "ho_oh_shadow", "rating": 771, "opRating": 228}, {"opponent": "entei_shadow", "rating": 766, "opRating": 233}, {"opponent": "sneasler", "rating": 760, "opRating": 239}, {"opponent": "zacian_hero", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "lugia", "rating": 242}, {"opponent": "dialga", "rating": 323}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 39955}, {"moveId": "ROCK_THROW", "uses": 36545}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 36346}, {"moveId": "MOONBLAST", "uses": 20450}, {"moveId": "PSYCHIC", "uses": 19659}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "MOONBLAST"], "score": 39.7}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 441, "matchups": [{"opponent": "charizard", "rating": 834, "opRating": 165}, {"opponent": "charizard_shadow", "rating": 753, "opRating": 246}, {"opponent": "moltres_shadow", "rating": 725, "opRating": 274}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 616, "opRating": 383}, {"opponent": "sylveon", "rating": 602, "opRating": 397}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "gyarados", "rating": 342}, {"opponent": "lugia", "rating": 397}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 53993}, {"moveId": "IRON_TAIL", "uses": 22507}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27026}, {"moveId": "BODY_SLAM", "uses": 25836}, {"moveId": "HEAVY_SLAM", "uses": 14066}, {"moveId": "ROCK_TOMB", "uses": 5904}, {"moveId": "RETURN", "uses": 3729}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 39.4}, {"speciesId": "chimecho", "speciesName": "Chi<PERSON><PERSON>", "rating": 448, "matchups": [{"opponent": "blaziken", "rating": 795, "opRating": 204}, {"opponent": "venusaur_shadow", "rating": 795, "opRating": 204}, {"opponent": "gallade_shadow", "rating": 759, "opRating": 240}, {"opponent": "machamp_shadow", "rating": 661, "opRating": 338}, {"opponent": "terrakion", "rating": 530, "opRating": 469}], "counters": [{"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "dialga", "rating": 271}, {"opponent": "metagross", "rating": 299}, {"opponent": "gyarados", "rating": 301}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 52807}, {"moveId": "ASTONISH", "uses": 23693}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30432}, {"moveId": "SHADOW_BALL", "uses": 28766}, {"moveId": "ENERGY_BALL", "uses": 17330}]}, "moveset": ["EXTRASENSORY", "PSYSHOCK", "SHADOW_BALL"], "score": 39.3}, {"speciesId": "sigilyph", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 459, "matchups": [{"opponent": "venusaur_shadow", "rating": 846, "opRating": 153}, {"opponent": "virizion", "rating": 790, "opRating": 209}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 703, "opRating": 296}, {"opponent": "moltres_shadow", "rating": 671, "opRating": 328}, {"opponent": "buzzwole", "rating": 606, "opRating": 393}], "counters": [{"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "dialga", "rating": 203}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 58149}, {"moveId": "ZEN_HEADBUTT", "uses": 18351}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 28863}, {"moveId": "SIGNAL_BEAM", "uses": 19480}, {"moveId": "AIR_CUTTER", "uses": 15117}, {"moveId": "PSYBEAM", "uses": 13060}]}, "moveset": ["AIR_SLASH", "ANCIENT_POWER", "SIGNAL_BEAM"], "score": 38.8}, {"speciesId": "grumpig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 448, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 793, "opRating": 206}, {"opponent": "chandelure", "rating": 750, "opRating": 250}, {"opponent": "machamp", "rating": 633, "opRating": 366}, {"opponent": "zap<PERSON>_galarian", "rating": 619, "opRating": 380}, {"opponent": "terrakion", "rating": 590, "opRating": 409}], "counters": [{"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "dialga", "rating": 279}, {"opponent": "metagross", "rating": 293}, {"opponent": "zacian_hero", "rating": 300}, {"opponent": "excadrill", "rating": 337}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 44508}, {"moveId": "CHARGE_BEAM", "uses": 31992}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37605}, {"moveId": "PSYCHIC", "uses": 31990}, {"moveId": "MIRROR_COAT", "uses": 6790}]}, "moveset": ["EXTRASENSORY", "SHADOW_BALL", "PSYCHIC"], "score": 38.7}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 474, "matchups": [{"opponent": "tapu_bulu", "rating": 890, "opRating": 109}, {"opponent": "suicune_shadow", "rating": 850, "opRating": 149}, {"opponent": "swampert", "rating": 704}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "swampert_shadow", "rating": 667, "opRating": 332}], "counters": [{"opponent": "dialga", "rating": 127}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "giratina_origin", "rating": 175}, {"opponent": "dragonite", "rating": 260}, {"opponent": "zacian_hero", "rating": 361}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 38436}, {"moveId": "BULLET_SEED", "uses": 38064}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33813}, {"moveId": "PETAL_BLIZZARD", "uses": 30456}, {"moveId": "SOLAR_BEAM", "uses": 12288}]}, "moveset": ["POISON_JAB", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 38.6}, {"speciesId": "solrock", "speciesName": "Solrock", "rating": 439, "matchups": [{"opponent": "ho_oh", "rating": 803}, {"opponent": "ho_oh_shadow", "rating": 771, "opRating": 228}, {"opponent": "entei_shadow", "rating": 766, "opRating": 233}, {"opponent": "sneasler", "rating": 760, "opRating": 239}, {"opponent": "zacian_hero", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 241}, {"opponent": "lugia", "rating": 242}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 39991}, {"moveId": "ROCK_THROW", "uses": 36509}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 42226}, {"moveId": "PSYCHIC", "uses": 22905}, {"moveId": "SOLAR_BEAM", "uses": 11363}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 38.6}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 456, "matchups": [{"opponent": "tangrowth_shadow", "rating": 896, "opRating": 103}, {"opponent": "tapu_bulu", "rating": 774, "opRating": 225}, {"opponent": "virizion", "rating": 753, "opRating": 246}, {"opponent": "buzzwole", "rating": 740, "opRating": 259}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 698, "opRating": 301}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "gyarados", "rating": 286}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "grou<PERSON>", "rating": 453}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66388}, {"moveId": "POUND", "uses": 10112}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35280}, {"moveId": "HURRICANE", "uses": 31982}, {"moveId": "AIR_CUTTER", "uses": 9257}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 38.5}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 421, "matchups": [{"opponent": "tapu_bulu", "rating": 721, "opRating": 278}, {"opponent": "florges", "rating": 597, "opRating": 402}, {"opponent": "zarude", "rating": 597, "opRating": 402}, {"opponent": "zacian_hero", "rating": 589}, {"opponent": "x<PERSON><PERSON>", "rating": 589, "opRating": 410}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "mewtwo", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "dragonite", "rating": 340}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54752}, {"moveId": "ROCK_SMASH", "uses": 21748}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 33589}, {"moveId": "SLUDGE_BOMB", "uses": 28773}, {"moveId": "GUNK_SHOT", "uses": 7474}, {"moveId": "ACID_SPRAY", "uses": 6614}]}, "moveset": ["INFESTATION", "ICE_BEAM", "SLUDGE_BOMB"], "score": 38.2}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 448, "matchups": [{"opponent": "heatran", "rating": 671, "opRating": 328}, {"opponent": "zarude", "rating": 617, "opRating": 382}, {"opponent": "genesect_chill", "rating": 614, "opRating": 385}, {"opponent": "genesect_burn", "rating": 614, "opRating": 385}, {"opponent": "metagross", "rating": 611}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "lugia", "rating": 173}, {"opponent": "excadrill", "rating": 213}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "dialga", "rating": 369}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38914}, {"moveId": "EMBER", "uses": 37586}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 34853}, {"moveId": "EARTHQUAKE", "uses": 26313}, {"moveId": "SOLAR_BEAM", "uses": 15389}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 38.2}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 431, "matchups": [{"opponent": "weavile", "rating": 737, "opRating": 262}, {"opponent": "weavile_shadow", "rating": 681, "opRating": 318}, {"opponent": "darkrai", "rating": 623, "opRating": 376}, {"opponent": "zarude", "rating": 587, "opRating": 412}, {"opponent": "mewtwo_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "gyarados", "rating": 273}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "metagross", "rating": 398}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 39551}, {"moveId": "INFESTATION", "uses": 36949}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 39576}, {"moveId": "BUBBLE_BEAM", "uses": 22426}, {"moveId": "MIRROR_COAT", "uses": 14547}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 38.1}, {"speciesId": "slowking_shadow", "speciesName": "Slowking (Shadow)", "rating": 451, "matchups": [{"opponent": "sneasler", "rating": 798, "opRating": 201}, {"opponent": "blaziken", "rating": 780, "opRating": 219}, {"opponent": "machamp_shadow", "rating": 654, "opRating": 345}, {"opponent": "ma<PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "sylveon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "gyarados", "rating": 273}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40036}, {"moveId": "WATER_GUN", "uses": 36464}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 33184}, {"moveId": "BLIZZARD", "uses": 26051}, {"moveId": "FIRE_BLAST", "uses": 17151}, {"moveId": "FRUSTRATION", "uses": 8}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 38.1}, {"speciesId": "slowbro_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 448, "matchups": [{"opponent": "sneasler", "rating": 798, "opRating": 201}, {"opponent": "blaziken", "rating": 780, "opRating": 219}, {"opponent": "machamp_shadow", "rating": 654, "opRating": 345}, {"opponent": "ma<PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "sylveon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "gyarados", "rating": 273}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40017}, {"moveId": "WATER_GUN", "uses": 36483}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31497}, {"moveId": "PSYCHIC", "uses": 28904}, {"moveId": "WATER_PULSE", "uses": 15911}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 37.8}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 388, "matchups": [{"opponent": "quagsire_shadow", "rating": 826, "opRating": 173}, {"opponent": "absol_shadow", "rating": 814, "opRating": 185}, {"opponent": "honchk<PERSON>_shadow", "rating": 783, "opRating": 216}, {"opponent": "staraptor", "rating": 704, "opRating": 295}, {"opponent": "staraptor_shadow", "rating": 695, "opRating": 304}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "gyarados", "rating": 324}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 48059}, {"moveId": "ASTONISH", "uses": 28441}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28289}, {"moveId": "LOW_SWEEP", "uses": 24296}, {"moveId": "HYPER_BEAM", "uses": 23678}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SCRATCH", "AERIAL_ACE", "LOW_SWEEP"], "score": 37.4}, {"speciesId": "throh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 429, "matchups": [{"opponent": "incineroar", "rating": 781, "opRating": 218}, {"opponent": "regirock", "rating": 673, "opRating": 326}, {"opponent": "melmetal", "rating": 662, "opRating": 337}, {"opponent": "genesect_douse", "rating": 504, "opRating": 495}, {"opponent": "genesect_shock", "rating": 504, "opRating": 495}], "counters": [{"opponent": "metagross", "rating": 180}, {"opponent": "gyarados", "rating": 190}, {"opponent": "dialga", "rating": 290}, {"opponent": "excadrill", "rating": 320}, {"opponent": "garcho<PERSON>", "rating": 384}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 49318}, {"moveId": "ZEN_HEADBUTT", "uses": 27182}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41227}, {"moveId": "FOCUS_BLAST", "uses": 18512}, {"moveId": "LOW_SWEEP", "uses": 16786}]}, "moveset": ["LOW_KICK", "BODY_SLAM", "FOCUS_BLAST"], "score": 37.4}, {"speciesId": "oricorio_sensu", "speciesName": "Oricorio (Sensu)", "rating": 444, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 868, "opRating": 131}, {"opponent": "zap<PERSON>_galarian", "rating": 859, "opRating": 140}, {"opponent": "pinsir", "rating": 856, "opRating": 143}, {"opponent": "virizion", "rating": 817, "opRating": 182}, {"opponent": "buzzwole", "rating": 801, "opRating": 198}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "grou<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66296}, {"moveId": "POUND", "uses": 10204}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35281}, {"moveId": "HURRICANE", "uses": 31960}, {"moveId": "AIR_CUTTER", "uses": 9257}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 37}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 393, "matchups": [{"opponent": "mismagius_shadow", "rating": 932, "opRating": 67}, {"opponent": "gengar", "rating": 905, "opRating": 94}, {"opponent": "exeggutor_shadow", "rating": 826, "opRating": 173}, {"opponent": "trevenant", "rating": 704, "opRating": 295}, {"opponent": "giratina_origin", "rating": 554}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "dialga", "rating": 247}, {"opponent": "metagross", "rating": 345}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39159}, {"moveId": "BITE", "uses": 26475}, {"moveId": "TAKE_DOWN", "uses": 10872}], "chargedMoves": [{"moveId": "SURF", "uses": 30824}, {"moveId": "DARK_PULSE", "uses": 24878}, {"moveId": "GRASS_KNOT", "uses": 20838}]}, "moveset": ["SUCKER_PUNCH", "SURF", "DARK_PULSE"], "score": 36.9}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 365, "matchups": [{"opponent": "mismagius_shadow", "rating": 920, "opRating": 79}, {"opponent": "dusknoir_shadow", "rating": 920, "opRating": 79}, {"opponent": "gengar", "rating": 881, "opRating": 118}, {"opponent": "giratina_origin", "rating": 673}, {"opponent": "trevenant", "rating": 673, "opRating": 326}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 214}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "excadrill", "rating": 362}, {"opponent": "metagross", "rating": 363}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 45528}, {"moveId": "ASTONISH", "uses": 30972}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34323}, {"moveId": "DISARMING_VOICE", "uses": 30056}, {"moveId": "FIRE_BLAST", "uses": 11975}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 36.7}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 450, "matchups": [{"opponent": "buzzwole", "rating": 740, "opRating": 259}, {"opponent": "virizion", "rating": 731, "opRating": 268}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 698, "opRating": 301}, {"opponent": "pangoro", "rating": 689, "opRating": 310}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "giratina_origin", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "zacian_hero", "rating": 283}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66269}, {"moveId": "POUND", "uses": 10231}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35281}, {"moveId": "HURRICANE", "uses": 31962}, {"moveId": "AIR_CUTTER", "uses": 9261}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 36.7}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 358, "matchups": [{"opponent": "gengar", "rating": 727, "opRating": 272}, {"opponent": "cofagrigus", "rating": 727, "opRating": 272}, {"opponent": "gourgeist_super", "rating": 640, "opRating": 359}, {"opponent": "giratina_origin", "rating": 601}, {"opponent": "trevenant", "rating": 579, "opRating": 420}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 288}, {"opponent": "excadrill", "rating": 300}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "metagross", "rating": 363}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 47288}, {"moveId": "ASTONISH", "uses": 29212}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28024}, {"moveId": "DISARMING_VOICE", "uses": 24147}, {"moveId": "RETURN", "uses": 14675}, {"moveId": "FIRE_BLAST", "uses": 9753}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 36.6}, {"speciesId": "reuniclus", "speciesName": "Reuniclus", "rating": 407, "matchups": [{"opponent": "flygon_shadow", "rating": 630, "opRating": 369}, {"opponent": "gallade", "rating": 590, "opRating": 409}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 555, "opRating": 444}, {"opponent": "virizion", "rating": 544, "opRating": 455}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 539, "opRating": 460}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 198}, {"opponent": "dragonite", "rating": 364}, {"opponent": "lugia", "rating": 395}, {"opponent": "garcho<PERSON>", "rating": 441}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5775}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5221}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 5182}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5110}, {"moveId": "HIDDEN_POWER_DARK", "uses": 5033}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4969}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4733}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4659}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4647}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4491}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4475}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4382}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4063}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4063}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3967}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3750}, {"moveId": "ZEN_HEADBUTT", "uses": 1784}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32330}, {"moveId": "FUTURE_SIGHT", "uses": 24817}, {"moveId": "THUNDER", "uses": 19306}]}, "moveset": ["HIDDEN_POWER_ICE", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 36.3}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 415, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 794, "opRating": 205}, {"opponent": "darmanitan_standard", "rating": 772, "opRating": 227}, {"opponent": "chandelure", "rating": 758, "opRating": 241}, {"opponent": "magmortar_shadow", "rating": 727, "opRating": 272}, {"opponent": "moltres_shadow", "rating": 719, "opRating": 280}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "metagross", "rating": 293}, {"opponent": "excadrill", "rating": 351}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 39076}, {"moveId": "WATER_GUN", "uses": 37424}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 30964}, {"moveId": "AQUA_JET", "uses": 29002}, {"moveId": "SWIFT", "uses": 16442}]}, "moveset": ["WATERFALL", "HYDRO_PUMP", "AQUA_JET"], "score": 36.1}, {"speciesId": "noivern", "speciesName": "Noivern", "rating": 429, "matchups": [{"opponent": "virizion", "rating": 691, "opRating": 308}, {"opponent": "grou<PERSON>", "rating": 609}, {"opponent": "buzzwole", "rating": 575, "opRating": 424}, {"opponent": "kyogre", "rating": 533, "opRating": 466}, {"opponent": "swampert", "rating": 508}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "gyarados", "rating": 167}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "mewtwo", "rating": 210}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 46805}, {"moveId": "BITE", "uses": 29695}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 24777}, {"moveId": "DRACO_METEOR", "uses": 22071}, {"moveId": "PSYCHIC", "uses": 20086}, {"moveId": "HEAT_WAVE", "uses": 9562}]}, "moveset": ["AIR_SLASH", "HURRICANE", "DRACO_METEOR"], "score": 35.6}, {"speciesId": "oricorio_pau", "speciesName": "Oricorio (Pa'u)", "rating": 422, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 850, "opRating": 149}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 826, "opRating": 173}, {"opponent": "virizion", "rating": 774, "opRating": 225}, {"opponent": "gallade", "rating": 759, "opRating": 240}, {"opponent": "buzzwole", "rating": 628, "opRating": 371}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "grou<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66286}, {"moveId": "POUND", "uses": 10214}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35281}, {"moveId": "HURRICANE", "uses": 31960}, {"moveId": "AIR_CUTTER", "uses": 9257}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 35.4}, {"speciesId": "alomomola", "speciesName": "Alomomola", "rating": 391, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 665, "opRating": 334}, {"opponent": "heatran", "rating": 646, "opRating": 353}, {"opponent": "darmanitan_standard", "rating": 635, "opRating": 364}, {"opponent": "entei_shadow", "rating": 613, "opRating": 386}, {"opponent": "moltres_shadow", "rating": 579, "opRating": 420}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 7648}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5636}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5173}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4825}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4634}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4593}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4546}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4290}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4192}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4165}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4160}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4130}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3705}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3687}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3670}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3623}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3418}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 27054}, {"moveId": "HYDRO_PUMP", "uses": 24800}, {"moveId": "PSYCHIC", "uses": 24626}]}, "moveset": ["WATERFALL", "BLIZZARD", "HYDRO_PUMP"], "score": 35}, {"speciesId": "mr_mime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 348, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 823, "opRating": 176}, {"opponent": "sneasler", "rating": 676, "opRating": 323}, {"opponent": "heracross", "rating": 646, "opRating": 353}, {"opponent": "kommo_o", "rating": 584, "opRating": 415}, {"opponent": "buzzwole", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "lugia", "rating": 280}, {"opponent": "dialga", "rating": 361}, {"opponent": "dragonite", "rating": 497}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 63705}, {"moveId": "ZEN_HEADBUTT", "uses": 12795}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37780}, {"moveId": "PSYCHIC", "uses": 32144}, {"moveId": "PSYBEAM", "uses": 6433}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 33.7}, {"speciesId": "accelgor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 402, "matchups": [{"opponent": "meganium_shadow", "rating": 857, "opRating": 142}, {"opponent": "zarude", "rating": 822, "opRating": 177}, {"opponent": "tangrowth_shadow", "rating": 822, "opRating": 177}, {"opponent": "tangrowth", "rating": 822, "opRating": 177}, {"opponent": "latios", "rating": 529, "opRating": 470}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "zacian_hero", "rating": 213}, {"opponent": "dialga", "rating": 228}, {"opponent": "metagross", "rating": 313}, {"opponent": "garcho<PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54986}, {"moveId": "ACID", "uses": 21514}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26683}, {"moveId": "SIGNAL_BEAM", "uses": 22922}, {"moveId": "FOCUS_BLAST", "uses": 20353}, {"moveId": "ACID_SPRAY", "uses": 6624}]}, "moveset": ["INFESTATION", "BUG_BUZZ", "SIGNAL_BEAM"], "score": 33.4}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 360, "matchups": [{"opponent": "absol_shadow", "rating": 826, "opRating": 173}, {"opponent": "staraptor_shadow", "rating": 704, "opRating": 295}, {"opponent": "salazzle", "rating": 637, "opRating": 362}, {"opponent": "lickilicky", "rating": 542, "opRating": 457}, {"opponent": "shiftry_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "gyarados", "rating": 260}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47560}, {"moveId": "ASTONISH", "uses": 28940}], "chargedMoves": [{"moveId": "RETURN", "uses": 23831}, {"moveId": "AERIAL_ACE", "uses": 22873}, {"moveId": "LOW_SWEEP", "uses": 20460}, {"moveId": "HYPER_BEAM", "uses": 9198}]}, "moveset": ["SCRATCH", "RETURN", "AERIAL_ACE"], "score": 33.3}, {"speciesId": "gorebyss", "speciesName": "<PERSON><PERSON>", "rating": 385, "matchups": [{"opponent": "golem", "rating": 862, "opRating": 137}, {"opponent": "chandelure", "rating": 792, "opRating": 207}, {"opponent": "delphox", "rating": 774, "opRating": 225}, {"opponent": "flareon", "rating": 774, "opRating": 225}, {"opponent": "entei", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "metagross", "rating": 273}, {"opponent": "excadrill", "rating": 351}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40683}, {"moveId": "CONFUSION", "uses": 35817}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 32074}, {"moveId": "WATER_PULSE", "uses": 23934}, {"moveId": "DRAINING_KISS", "uses": 20475}]}, "moveset": ["WATER_GUN", "PSYCHIC", "WATER_PULSE"], "score": 32.8}, {"speciesId": "xatu", "speciesName": "Xatu", "rating": 384, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 808, "opRating": 191}, {"opponent": "virizion", "rating": 755, "opRating": 244}, {"opponent": "gallade", "rating": 734, "opRating": 265}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 677, "opRating": 322}, {"opponent": "buzzwole", "rating": 593, "opRating": 406}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "metagross", "rating": 186}, {"opponent": "zacian_hero", "rating": 280}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 39791}, {"moveId": "FEINT_ATTACK", "uses": 36709}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 29236}, {"moveId": "FUTURE_SIGHT", "uses": 26802}, {"moveId": "OMINOUS_WIND", "uses": 20525}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "FUTURE_SIGHT"], "score": 31.5}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 365, "matchups": [{"opponent": "vaporeon", "rating": 629, "opRating": 370}, {"opponent": "swampert", "rating": 597}, {"opponent": "sneasler", "rating": 587, "opRating": 412}, {"opponent": "tapu_fini", "rating": 579, "opRating": 420}, {"opponent": "tapu_koko", "rating": 552, "opRating": 447}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "gyarados", "rating": 219}, {"opponent": "zacian_hero", "rating": 228}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6058}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5724}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5268}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5031}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4955}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4778}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4769}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4592}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4564}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4470}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4462}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4424}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4109}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4104}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3919}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3777}, {"moveId": "ZEN_HEADBUTT", "uses": 1435}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41157}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12209}, {"moveId": "SOLAR_BEAM", "uses": 8427}]}, "moveset": ["ZEN_HEADBUTT", "GRASS_KNOT", "SEED_FLARE"], "score": 31}, {"speciesId": "sliggoo", "speciesName": "Sliggoo", "rating": 380, "matchups": [{"opponent": "golem", "rating": 870, "opRating": 129}, {"opponent": "chandelure", "rating": 818, "opRating": 181}, {"opponent": "typhlosion", "rating": 769, "opRating": 230}, {"opponent": "manectric_shadow", "rating": 714, "opRating": 285}, {"opponent": "electivire_shadow", "rating": 561, "opRating": 438}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "metagross", "rating": 270}, {"opponent": "excadrill", "rating": 486}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40193}, {"moveId": "TACKLE", "uses": 36307}], "chargedMoves": [{"moveId": "DRAGON_PULSE", "uses": 27418}, {"moveId": "MUDDY_WATER", "uses": 27052}, {"moveId": "SLUDGE_WAVE", "uses": 16728}, {"moveId": "WATER_PULSE", "uses": 5281}]}, "moveset": ["WATER_GUN", "DRAGON_PULSE", "MUDDY_WATER"], "score": 30.4}, {"speciesId": "hit<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 317, "matchups": [{"opponent": "weavile_shadow", "rating": 917, "opRating": 82}, {"opponent": "weavile", "rating": 917, "opRating": 82}, {"opponent": "bisharp", "rating": 917, "opRating": 82}, {"opponent": "sandslash_alolan", "rating": 803, "opRating": 196}, {"opponent": "mamos<PERSON>_shadow", "rating": 602, "opRating": 397}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "metagross", "rating": 264}, {"opponent": "dialga", "rating": 285}, {"opponent": "excadrill", "rating": 376}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 46832}, {"moveId": "LOW_KICK", "uses": 29668}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30849}, {"moveId": "STONE_EDGE", "uses": 16573}, {"moveId": "BRICK_BREAK", "uses": 13696}, {"moveId": "STOMP", "uses": 11336}, {"moveId": "LOW_SWEEP", "uses": 3979}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_SMASH", "CLOSE_COMBAT", "STONE_EDGE"], "score": 29.9}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 346, "matchups": [{"opponent": "magnezone", "rating": 679, "opRating": 320}, {"opponent": "magnezone_shadow", "rating": 644, "opRating": 355}, {"opponent": "nihilego", "rating": 619, "opRating": 380}, {"opponent": "melmetal", "rating": 570, "opRating": 429}, {"opponent": "terrakion", "rating": 552, "opRating": 447}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "dialga", "rating": 133}, {"opponent": "gyarados", "rating": 157}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 190}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 28248}, {"moveId": "MUD_SLAP", "uses": 25194}, {"moveId": "EXTRASENSORY", "uses": 23050}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 16783}, {"moveId": "ICE_BEAM", "uses": 14072}, {"moveId": "SHADOW_BALL", "uses": 13681}, {"moveId": "PSYCHIC", "uses": 11892}, {"moveId": "ROCK_TOMB", "uses": 7921}, {"moveId": "EARTHQUAKE", "uses": 7234}, {"moveId": "GYRO_BALL", "uses": 4906}]}, "moveset": ["CONFUSION", "EARTH_POWER", "ICE_BEAM"], "score": 28.4}, {"speciesId": "meowstic_female", "speciesName": "<PERSON><PERSON><PERSON> (Female)", "rating": 340, "matchups": [{"opponent": "toxicroak", "rating": 874, "opRating": 125}, {"opponent": "hitmon<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "primeape", "rating": 616, "opRating": 383}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "gallade", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 173}, {"opponent": "zacian_hero", "rating": 184}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44370}, {"moveId": "CHARM", "uses": 32130}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 31197}, {"moveId": "PSYCHIC", "uses": 26606}, {"moveId": "ENERGY_BALL", "uses": 18704}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 28.3}, {"speciesId": "me<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON> (Male)", "rating": 331, "matchups": [{"opponent": "toxicroak", "rating": 874, "opRating": 125}, {"opponent": "hitmon<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "primeape", "rating": 616, "opRating": 383}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "gallade", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 173}, {"opponent": "zacian_hero", "rating": 184}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40886}, {"moveId": "SUCKER_PUNCH", "uses": 35614}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 30614}, {"moveId": "THUNDERBOLT", "uses": 24484}, {"moveId": "ENERGY_BALL", "uses": 21401}]}, "moveset": ["CONFUSION", "PSYCHIC", "THUNDERBOLT"], "score": 28.1}]