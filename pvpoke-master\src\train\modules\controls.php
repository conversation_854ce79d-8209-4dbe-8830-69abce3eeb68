<div class="controls">
	<div class="move-labels">
		<div class="label">Thunderbolt</div>
		<div class="label">Magnet Bomb</div>
	</div>

	<div class="move-bars">
		<div class="move-bar">
			<div class="label">T</div>
			<div class="bar"></div>
			<div class="bar"></div>
			<div class="bar"></div>
			<div class="bar-back electric"></div>
		</div>
		<div class="move-bar">
			<div class="label">MB</div>
			<div class="bar"></div>
			<div class="bar"></div>
			<div class="bar"></div>
			<div class="bar-back steel"></div>
		</div>
	</div>

	<div class="auto-tap-container">
		<div class="auto-tap">Autotap</div>
	</div>

	<div class="button-stack">
		<div class="pause-btn"></div>
		<div class="restart-btn"></div>
		<div class="quit-btn">&cross;</div>
	</div>

</div>

<div class="shield-window">
	<div class="container">
		<p>Attack incoming! Use Protect Shield?</p>
		<div class="shield"></div>
		<div class="shield-count"></div>
		<div class="close">Not Now</div>
	</div>
</div>

<div class="charge-window">
	<div class="container">
		<div class="rings">
			<div class="ring-container">
				<div class="ring"></div>
			</div>
			<div class="ring-container">
				<div class="ring"></div>
			</div>
			<div class="ring-container">
				<div class="ring"></div>
			</div>
			<div class="ring-container">
				<div class="move-bars">
				</div>
			</div>
		</div>
		<div class="charge">25%</div>
	</div>
</div>

<div class="switch-sidebar active">
	<div class="container">
		<div class="pokemon-container">
			<div class="pokemon">
				<div class="sprite-container">
					<div class="cp">cp 1500</div>
					<div class="main-sprite"></div>
					<div class="secondary-sprite"></div>
					<div class="health"></div>
				</div>
				<div class="name">Pokemon 1</div>
				<div class="switch-timer">60</div>
			</div>
			<div class="pokemon">
				<div class="sprite-container">
					<div class="cp">cp 1500</div>
					<div class="main-sprite"></div>
					<div class="secondary-sprite"></div>
					<div class="health"></div>
				</div>
				<div class="name">Pokemon 2</div>
				<div class="switch-timer">60</div>
			</div>
		</div>
	</div>
</div>

<div class="switch-window">
	<div class="container">
		<p>Switch in a new pokemon?</p>
		<div class="pokemon-container">
			<div class="pokemon">
				<div class="cp">cp 1500</div>
				<div class="sprite-container">
					<div class="main-sprite"></div>
					<div class="secondary-sprite"></div>
					<div class="health"></div>
				</div>
				<div class="name">Pokemon 1</div>
			</div>
			<div class="pokemon">
				<div class="cp">cp 1500</div>
				<div class="sprite-container">
					<div class="main-sprite"></div>
					<div class="secondary-sprite"></div>
					<div class="health"></div>
				</div>
				<div class="name">Pokemon 2</div>
			</div>
		</div>
	</div>
</div>

<div class="restart-confirm hide">
	<p>Restart the match?</p>

	<div class="center flex">
		<div class="button yes">Yes</div>
		<div class="button no">No</div>
	</div>
</div>

<div class="quit-confirm hide">
	<p>Quit the match? In Tournament Mode, you'll go back to the team selection screen.</p>

	<div class="center flex">
		<div class="button yes">Yes</div>
		<div class="button no">No</div>
	</div>
</div>
