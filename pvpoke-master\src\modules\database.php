<?php
require_once 'config.php';

class DatabaseConnection {
    private $connection;
    private $isPostgreSQL;
    
    public function __construct() {
        $this->isPostgreSQL = isset($_SERVER['RENDER']) || isset($_ENV['DATABASE_URL']);
        $this->connect();
    }
    
    private function connect() {
        if ($this->isPostgreSQL) {
            // PostgreSQL connection for Render
            $database_url = $_ENV['DATABASE_URL'] ?? '';
            if ($database_url) {
                $this->connection = new PDO($database_url);
            } else {
                $dsn = "pgsql:host={$GLOBALS['DB_HOST']};dbname={$GLOBALS['DB_NAME']}";
                $this->connection = new PDO($dsn, $GLOBALS['DB_USER'], $GLOBALS['DB_PASS']);
            }
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } else {
            // MySQL connection for local development
            $this->connection = new mysqli($GLOBALS['DB_HOST'], $GLOBALS['DB_USER'], $GLOBALS['DB_PASS'], $GLOBALS['DB_NAME']);
            $this->connection->set_charset('utf8');
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
        }
    }
    
    public function prepare($sql) {
        if ($this->isPostgreSQL) {
            // Convert MySQL syntax to PostgreSQL
            $sql = $this->convertSqlSyntax($sql);
            return $this->connection->prepare($sql);
        } else {
            return $this->connection->prepare($sql);
        }
    }
    
    private function convertSqlSyntax($sql) {
        // Convert MySQL column names to PostgreSQL snake_case
        $sql = str_replace('pokemonId', 'pokemon_id', $sql);
        $sql = str_replace('teamPosition', 'team_position', $sql);
        $sql = str_replace('playerType', 'player_type', $sql);
        $sql = str_replace('teamScore', 'team_score', $sql);
        $sql = str_replace('individualScore', 'individual_score', $sql);
        $sql = str_replace('teamStr', 'team_str', $sql);
        $sql = str_replace('postDatetime', 'post_datetime', $sql);
        
        return $sql;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function isPostgreSQL() {
        return $this->isPostgreSQL;
    }
    
    public function close() {
        if ($this->isPostgreSQL) {
            $this->connection = null;
        } else {
            $this->connection->close();
        }
    }
}
?>
