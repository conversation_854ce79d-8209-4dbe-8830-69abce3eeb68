/* VARIABLES */

$borderRed: #780900;
$borderDarkBlue: #0a0018;
$backgroundBlue: #1d0441;
$fontColor: #fff;
$linkColor: #990f44;
$linkColorHighlight: #ad104c;
$errorColor: #ce2c09;

// #ffb3b3
// #ff8686

/* MIXINS */

@mixin transition($property, $duration, $ease, $delay){
    -webkit-transition: $property $duration $ease $delay;
    -moz-transition: $property $duration $ease $delay;
    -o-transition: $property $duration $ease $delay;
    transition: $property $duration $ease $delay;
}

@mixin transform($property){
	-moz-transform: $property;
	-webkit-transform: $property;
	-o-transform: $property;
	-ms-transform: $property;
	transform: $property;
}

@mixin typegradient($light, $dark){
	background: -moz-linear-gradient(0deg, $dark 43%, $light 55%);
	background: -webkit-linear-gradient(0deg, $dark 43%, $light 55%);
	background: linear-gradient(0deg, $dark 43%, $light 55%);
}


@mixin teragradient($light, $dark){
	background: $dark;
	background: -moz-linear-gradient(117deg, $light 33%, $dark 100%);
	background: -webkit-linear-gradient(117deg, $light 33%, $dark 100%);
	background: linear-gradient(117deg, $light 33%, $dark 100%);
}

html, body{
	margin: 0;
	padding: 0;
	font-family: Arial, sans-serif;
	color: $fontColor;
	height:100%;
}

body{
	/*background: rgb(207,32,32);
	background: -moz-linear-gradient(167deg, rgba(207,32,32,1) 2%, rgba(171,42,9,1) 14%, rgba(82,8,59,1) 48%, rgba(29,4,65,1) 72%);
	background: -webkit-linear-gradient(167deg, rgba(207,32,32,1) 2%, rgba(171,42,9,1) 14%, rgba(82,8,59,1) 48%, rgba(29,4,65,1) 72%);
	background: linear-gradient(167deg, rgba(207,32,32,1) 2%, rgba(171,42,9,1) 14%, rgba(82,8,59,1) 48%, rgba(29,4,65,1) 72%);*/

	background: rgb(82,8,59);
	background: -moz-linear-gradient(167deg, rgba(82,8,59,1) 14%, rgba(29,4,65,1) 72%);
	background: -webkit-linear-gradient(167deg, rgba(82,8,59,1) 14%, rgba(29,4,65,1) 72%);
	background: linear-gradient(167deg, rgba(82,8,59,1) 14%, rgba(29,4,65,1) 72%);
	background-attachment: fixed;

	background: url('../img/bg.jpg');
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-size:cover;
	background-position: center bottom;
}

a{
	color: $linkColor;

	&:hover{
		color: $linkColorHighlight;
	}
}

header{
	padding: 5px;
	background: #1e0441;
	position: fixed;
	z-index: 15;
	width:100%;
	box-sizing: border-box;

	.header-wrap{
		max-width:1000px;
		margin:0 auto;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
	}

	a{
		color: #ffcf01;

		img{
			height:30px;
		}
	}
}

#main{
	max-width:1000px;
	margin: auto;
	padding:37px 10px 10px 10px;
    border-radius: 12px;

	& > h1{
		margin:0;
	}

	p{
		font-size: 16px;
		line-height: 22px;
	}
}

input[type="text"]{
	background: none;
    border: none;
    border-bottom: 1px solid rgba(255,255,255,0.5);
	color: $fontColor;
    outline: none;
    padding-bottom: 5px;
}

::-webkit-input-placeholder {
    color:   rgba(255,255,255,0.5);
}
:-moz-placeholder {
   color: rgba(255,255,255,0.5);
}
::-moz-placeholder {
   color: rgba(255,255,255,0.5);
}
:-ms-input-placeholder {
   color: rgba(255,255,255,0.5);
}
::-ms-input-placeholder {
   color: rgba(255,255,255,0.5);
}
::placeholder {
   color: rgba(255,255,255,0.5);
}

.flex{
	display: flex;
	flex-wrap: wrap;
}

.template{
	display: none;
}

.bordered-section{
	background: $backgroundBlue;
	border: 3px solid $borderDarkBlue;
	padding: 10px;
	border-bottom-left-radius: 10px;
	border-top-right-radius: 10px;
}

.bordered-section select, .modal select{
	background: rgba(0,0,0,0.5);
	border: 1px solid rgba(0,0,0,0.75);
    color: #fff;
    padding: 10px;
    box-sizing: border-box;
	border-bottom-left-radius: 10px;
	border-top-right-radius: 10px;

	&:hover{
		background: rgba(0,0,0,0.75);
	}

	option{
		background: rgba(29,4,65,1);
	}

	&.error{
		border: 1px solid $errorColor;
		color: $errorColor;

		option{
			color: #fff;
		}
	}
}

.section-header{
	margin: 10px 0;
	align-items: center;

	h3{
		font-size: 22px;
		margin: 0 20px 0 0;
	}

	&.margin-top{
		margin-top: 40px;
	}
}

.hr{
	flex-grow: 1;
	border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}

.poke-search-container{
	input, select{
		display: block;
		width: 225px;
		box-sizing: border-box;
	}

	select{
		margin: 10px 0 10px 0;
		font-size: 20px;
	}
}

.boss-section{
	@include teragradient(#a1417f, #6d1744);
	position: relative;
	overflow: hidden;


	& > .flex{
		position: relative;
		z-index: 10;
		justify-content: space-between;
		margin-bottom: 15px;

		&:last-of-type{
			margin-bottom: 0;
		}
	}

	h4{
		margin-top: 0;
		margin-bottom: 15px;

		&.sub-title{
			margin-bottom: 10px;
		}
	}

	.pattern{
		width:100%;
		height:100%;
		position:absolute;
		z-index: 0;
		left: 0;
		top: 0;
		background: url('../img/pattern.png');
		background-position: center center;
		opacity: 0.1;
	}

	.flash{
		width:100%;
		height:100%;
		position:absolute;
		z-index: 0;
		left: -1000px;
		top: 0;

		background: rgb(255,255,255);
		background: -moz-linear-gradient(123deg, rgba(255,255,255,0) 16%, rgba(255,255,255,1) 23%, rgba(255,255,255,1) 25%, rgba(255,255,255,0) 29%, rgba(255,255,255,1) 30%, rgba(255,255,255,0) 33%);
		background: -webkit-linear-gradient(123deg, rgba(255,255,255,0) 16%, rgba(255,255,255,1) 23%, rgba(255,255,255,1) 25%, rgba(255,255,255,0) 29%, rgba(255,255,255,1) 30%, rgba(255,255,255,0) 33%);
		background: linear-gradient(123deg, rgba(255,255,255,0) 16%, rgba(255,255,255,1) 23%, rgba(255,255,255,1) 25%, rgba(255,255,255,0) 29%, rgba(255,255,255,1) 30%, rgba(255,255,255,0) 33%);

		opacity: 0.2;
	}

	.traits-container{
		display: none;
		margin-top: 20px;

		.trait-item{
			padding: 5px 10px;
			width: auto;
			background: rgba(0,0,0,0.5);

			&.on{
				background: $linkColor;
			}
		}
	}

	#tera-select{
		font-size: 20px;
		min-width:100px;

		option{
			background: rgba(29,4,65,1);
		}
	}

	#poke-select{
		margin-bottom: 20px;
	}

	.item-list{
		display: flex;
		justify-content: flex-start;
		max-width: 480px;
		flex-wrap: wrap;

		#attack-type-select{
			margin: 10px 0 0 0;
			font-size: 14px;
		    padding: 5px;
		    width: 105px;
			border-radius: 10px;
		}
	}

	.tera-type-container .tera-icon{
		display: none;
	}

	&[tera-type="bug"]{ @include teragradient(#9ba141, #5b6c16); }
	&[tera-type="dark"]{ @include teragradient(#a68aa6, #6f536f); }
	&[tera-type="dragon"]{ @include teragradient(#808dff, #4654cd); }
	&[tera-type="electric"]{ @include teragradient(#c59515, #805d01); }
	&[tera-type="fairy"]{ @include teragradient(#d766de, #9f2ea6); }
	&[tera-type="fighting"]{ @include teragradient(#d16f1a, #904503 ); }
	&[tera-type="fire"]{ @include teragradient(#dc5750, #b22720 ); }
	&[tera-type="flying"]{ @include teragradient(#8095cd, #485d95); }
	&[tera-type="ghost"]{ @include teragradient(#b481b0, #7c4978); }
	&[tera-type="grass"]{ @include teragradient(#6ab755, #377b25); }
	&[tera-type="ground"]{ @include teragradient(#bf8967, #87512f); }
	&[tera-type="ice"]{ @include teragradient(#32b3d3, #046d80); }
	&[tera-type="normal"]{ @include teragradient(#959595, #5d5d5d); }
	&[tera-type="poison"]{ @include teragradient(#b379db, #79469c); }
	&[tera-type="psychic"]{ @include teragradient(#d67498, #964060); }
	&[tera-type="rock"]{ @include teragradient(#9c986c, #656135); }
	&[tera-type="steel"]{ @include teragradient(#70a69c, #386e64); }
	&[tera-type="water"]{ @include teragradient(#5794df, #2b578d); }
}

[tera-type="bug"] .tera-icon{ background-image: url('../img/types/bug.png'); }
[tera-type="dark"] .tera-icon{background-image: url('../img/types/dark.png'); }
[tera-type="dragon"] .tera-icon{ background-image: url('../img/types/dragon.png'); }
[tera-type="electric"] .tera-icon{ background-image: url('../img/types/electric.png'); }
[tera-type="fairy"] .tera-icon{ background-image: url('../img/types/fairy.png'); }
[tera-type="fighting"] .tera-icon{ background-image: url('../img/types/fighting.png'); }
[tera-type="fire"] .tera-icon{ background-image: url('../img/types/fire.png'); }
[tera-type="flying"] .tera-icon{ background-image: url('../img/types/flying.png'); }
[tera-type="ghost"] .tera-icon{ background-image: url('../img/types/ghost.png'); }
[tera-type="grass"] .tera-icon{ background-image: url('../img/types/grass.png'); }
[tera-type="ground"] .tera-icon{ background-image: url('../img/types/ground.png'); }
[tera-type="ice"] .tera-icon{ background-image: url('../img/types/ice.png'); }
[tera-type="normal"] .tera-icon{ background-image: url('../img/types/normal.png'); }
[tera-type="poison"] .tera-icon{ background-image: url('../img/types/poison.png'); }
[tera-type="psychic"] .tera-icon{ background-image: url('../img/types/psychic.png'); }
[tera-type="rock"] .tera-icon{ background-image: url('../img/types/rock.png'); }
[tera-type="steel"] .tera-icon{ background-image: url('../img/types/steel.png'); }
[tera-type="water"] .tera-icon{ background-image: url('../img/types/water.png'); }

.type-item[tera-type="bug"] .tera-icon{ background-image: url('../img/types/bug.png'); }
.type-item[tera-type="dark"] .tera-icon{background-image: url('../img/types/dark.png'); }
.type-item[tera-type="dragon"] .tera-icon{ background-image: url('../img/types/dragon.png'); }
.type-item[tera-type="electric"] .tera-icon{ background-image: url('../img/types/electric.png'); }
.type-item[tera-type="fairy"] .tera-icon{ background-image: url('../img/types/fairy.png'); }
.type-item[tera-type="fighting"] .tera-icon{ background-image: url('../img/types/fighting.png'); }
.type-item[tera-type="fire"] .tera-icon{ background-image: url('../img/types/fire.png'); }
.type-item[tera-type="flying"] .tera-icon{ background-image: url('../img/types/flying.png'); }
.type-item[tera-type="ghost"] .tera-icon{ background-image: url('../img/types/ghost.png'); }
.type-item[tera-type="grass"] .tera-icon{ background-image: url('../img/types/grass.png'); }
.type-item[tera-type="ground"] .tera-icon{ background-image: url('../img/types/ground.png'); }
.type-item[tera-type="ice"] .tera-icon{ background-image: url('../img/types/ice.png'); }
.type-item[tera-type="normal"] .tera-icon{ background-image: url('../img/types/normal.png'); }
.type-item[tera-type="poison"] .tera-icon{ background-image: url('../img/types/poison.png'); }
.type-item[tera-type="psychic"] .tera-icon{ background-image: url('../img/types/psychic.png'); }
.type-item[tera-type="rock"] .tera-icon{ background-image: url('../img/types/rock.png'); }
.type-item[tera-type="steel"] .tera-icon{ background-image: url('../img/types/steel.png'); }
.type-item[tera-type="water"] .tera-icon{ background-image: url('../img/types/water.png'); }

/* Typing colors and icons */

.bug, [type="bug"]{ @include typegradient(#bec720, #8b9f18) }
.dark, [type="dark"]{ @include typegradient(#715a5b, #4f3f3f) }
.dragon, [type="dragon"]{ @include typegradient(#7387f0, #444dcb) }
.electric, [type="electric"]{ @include typegradient(#ffcb03, #f6bc00) }
.fairy, [type="fairy"]{ @include typegradient(#f89af3, #ed6ce5) }
.fighting, [type="fighting"]{ @include typegradient(#fca606, #fc7c00) }
.fire, [type="fire"]{ @include typegradient(#f94043, #dd2727) }
.flying, [type="flying"]{ @include typegradient(#b3d4f4, #7fb4e9) }
.ghost, [type="ghost"]{ @include typegradient(#9e5a9c, #6d3d69) }
.grass, [type="grass"]{ @include typegradient(#5fca3d, #409f26) }
.ground, [type="ground"]{ @include typegradient(#bd7033, #8f4d24) }
.ice, [type="ice"]{ @include typegradient(#5bebff, #40d3f8) }
.normal, [type="normal"]{ @include typegradient(#c7c7c7, #9d9d9f) }
.poison, [type="poison"]{ @include typegradient(#bd5ce3, #8a3fc1) }
.psychic, [type="psychic"]{ @include typegradient(#fe64b1, #eb4481) }
.rock, [type="rock"]{ @include typegradient(#d0c9b0, #aba77b) }
.steel, [type="steel"]{ @include typegradient(#86c8d4, #5d9cb0) }
.water, [type="water"]{ @include typegradient(#35b5f7, #267ee7) }


.type-item, .trait-item{
	box-sizing: border-box;
	font-size: 14px;
	width: 105px;
	margin: 10px 15px 0 0;
	border: 1px solid rgba(0, 0, 0, 0.75);
	border-radius: 10px;
	overflow: hidden;

	&.tera{
		border-top-left-radius: 0px;
		border-bottom-right-radius: 0px;
	}

	.type-name-container{
		background: rgba(0,0,0,0.1);
		align-items: center;
		flex-grow: 1;

		.type-name{
			padding: 5px 10px 5px 0;
			text-align: center;
			flex-grow: 1;
		}
	}

	.tera-icon{
		width: 18px;
	    height: 18px;
	    margin: 0 2px 0 5px;
	}

	a{
		background:rgba(0,0,0,0.5);
		padding: 5px;
		text-decoration: none;
		color: $fontColor;

		&:hover{
			background:rgba(0,0,0,0.75);
		}
	}
}

.tera-icon{
	width:85px;
	height:85px;
	background-size:contain;
	background-repeat: no-repeat;
	margin-right: 15px;
}

.results-container{
	display: none;
}

#results{
	text-align: left;
	width: 100%;

	th{
		top:0px;
		position: sticky;
		background: #1d0441;
		border-bottom: 1px solid rgba(255,255,255,0.25);
		z-index: 5;
	}

	th, td{
		padding: 10px;
	}

	tbody tr:nth-of-type(odd){
		background:rgba(255,255,255,0.1);
	}

	tbody tr{
		position: relative;
		top: 0;
		opacity: 1;
		@include transition(all, 250ms, ease, 0ms);

		&.animate{
			top: 20px;
			opacity: 0;
		}
	}

	.type-item{
		margin-top: 0;
		margin-right: 3px;
	}

	.type-item:not(.tera){
		.type-name-container{
			background: #1d0441;
			margin: 2px;
			border-radius: 10px;
		}
	}

	a.score{
		color: #fff;
		text-decoration: none;
		border-bottom: 1px solid rgba(255,255,255,0.5);
	}
}

button#run, .modal button{
	margin-top: 20px;
    outline: none;
    border: none;
    padding: 10px;
    font-size: 20px;
    display: block;
    width: 230px;
	cursor: pointer;
	color: #fff;
	border: 2px solid #0a0018;
    border-top-right-radius: 10px;
    border-bottom-left-radius: 10px;
	box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.6);
	@include typegradient($linkColor, #46052c);

	&:hover{
		@include typegradient($linkColorHighlight , #4f0532);
		box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.8);
	}

	&:active{
		position: relative;
		top: 2px;
		box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
	}
}

.results-section{
	.table-container{
		max-height: 800px;
		overflow-y: auto;
	}

	.search-instructions{
		font-size: 12px;
	    margin: 5px 0 10px 0;
	    opacity: 0.5;
	}

	.results-controls{
		justify-content: space-between;
		flex-wrap: nowrap;

		a.results-options{
			display: block;
			width:30px;
			height:30px;
			margin-left: 20px;
			text-decoration: none;
			background:url('../img/options.png');
			background-repeat: no-repeat;
			background-size:contain;
		}
	}
}

.score-details{
	align-items: center;
	justify-content: center;
	text-align: center;

	.overall{
		.score{
			font-size: 44px;
		}
	}

	.offense .score, .defense .score{
		font-size: 26px;
	}

	.score{
		font-weight: bold;
	}

	.label{
		font-size: 12px;
	}

	span.multiply{
		margin: 0 10px;
	}

	.type-item{
		margin-top: 0;
	}

	.typings, .tera-type{
		flex-basis: 100%;
		justify-content: center;
	}

	.tera-type .type-item,
	.traits{
		margin-bottom: 3px;
		margin-right: 0;
	}

	.traits{
		justify-content: center;
	}

	.tera-type > .label{
		flex-basis: 100%;
	}

	.full-row{
		flex-basis: 100%;
	}

	.border-bottom{
		border-bottom: 1px solid rgba(255,255,255,0.1);
		margin-bottom: 10px;
		padding-bottom: 10px;
	}


}

footer{
	max-width:1000px;
	text-align: center;
	font-size:12px;
	margin:80px auto 0 auto;

	p:first-of-type{
		margin-top:0;
	}
}

/* MODAL WINDOW */

.modal{
	position:fixed;
	top:0;
	left:0;
	width:100%;
	height:100%;
	background:rgba(0,0,0,.5);
	z-index:30;
	padding:10px;
	box-sizing:border-box;
	overflow-y:scroll;
	display: flex;
    align-items: center;
    justify-content: center;

	.modal-container{
		position:relative;
		background: rgb(82,8,59);
		background: -moz-linear-gradient(167deg, rgba(82,8,59,1) 14%, rgba(29,4,65,1) 72%);
		background: -webkit-linear-gradient(167deg, rgba(82,8,59,1) 14%, rgba(29,4,65,1) 72%);
		background: linear-gradient(167deg, rgba(82,8,59,1) 14%, rgba(29,4,65,1) 72%);
		background-attachment: fixed;
		margin: 0 auto;
		max-width: 400px;
		vertical-align: middle;
		margin-top: 50px;
		border-radius: 8px;
		overflow:hidden;
		margin-bottom: 75px;
		flex-grow: 1;

		.modal-header{
			padding:10px;
			background:$borderDarkBlue;
			font-size:24px;
			font-weight: bold;
		}

		.modal-close{

			position: absolute;
			top:0;
			right:0;
			padding:10px;
			font-size: 30px;
			cursor:pointer;

			&:after{
				content:"×";
			}
		}

		.modal-content{
			padding:10px;
		}
	}

	h4{
		margin: 20px 0 10px 0;

		&:first-of-type{
			margin-top: 0;
		}
	}

	select{
		font-size: 18px;
		width: 200px;
		margin-bottom: 10px;
	}

	button{
		background: $linkColor !important;
		margin: 30px auto 0 auto;
		font-size: 18px;
		padding: 10px;

		&:hover{
			background: #ad104c !important;
		}
	}

	p{
		font-size: 14px;
		text-align: left;
	}
}

.share-link-container{

	text-align: center;
	margin-top:20px;

	p{
		margin:0;
	}
}

.share-link{
	display: inline-block;
	background: #000;
	background-image:url('../../img/link.png');
	background-repeat: no-repeat;
	background-position: 6px center;
	background-size: 20px;
	padding: 10px 10px 10px 35px;
	border-radius: 8px;
	margin:5px auto;

	input{
		width: auto;
		display: inline-block;
		border: none;
		background: $borderDarkBlue;
		color: $linkColor;
		padding: 5px;
		font-size: 18px;
	}

	.copy{
		display: inline-block;
		font-weight: bold;
		font-size:13px;
		cursor: pointer;
		color: #fff;
		margin: 5px;
		border-radius: 8px;
	}
}


.check{
	margin-top:10px;
	cursor: pointer;

	span{
		display: block;
		width: 17px;
		height: 17px;
		border-radius: 2px;
		background: #222;
		border: 2px solid #000;
		box-sizing:border-box;
		margin: 0px 10px 0 0;
		float: left;
	}

	&.on{
		span{
			font-size: 18px;
		    padding-left: 1px;
		    line-height: 10px;
		    font-weight: bold;
		    overflow: hidden;
			background: $linkColor;

			&:after{
				content: "✓";
			}
		}
	}

	&:hover:not(.on){
		span{
			background:#333;
		}
	}
}

.traits{
	flex-wrap: wrap;

	.trait{
		font-size: 12px;
	    background: $linkColor;
	    padding: 2px 6px;
	    border-radius: 10px;
	    margin: 4px 4px 0 0;
	}
}

/* Responsive styling */

@media only screen and (max-width: 820px) {
	h1{ font-size: 26px; }

	h3{ font-size: 20px; }

	#main p{
		font-size: 14px;
    	line-height: 20px;
	}

	.tera-type-container{
		margin-top: 30px;

		.flex{
			flex-direction: row-reverse;
		}

		.tera-icon{
			width: 75px;
			height: 75px;
			margin-right: 0;
			margin-left: 15px;
		}
	}

	#poke-select, .boss-section #tera-select{
		font-size: 18px;
		padding: 5px;
	}

	#poke-search, #poke-select, .boss-section #tera-select, {
		width: 175px;
	}

	.type-item{
		font-size: 12px;
		margin: 10px 10px 0 0;

		.type-name-container .type-name{
			padding: 3px 5px 3px 0;
		}
	}

	.boss-section .boss-attack-types #attack-type-select{
		padding: 3px;
	}

	#results{
		.type-item{
			width: 85px;
		}

		td, th{
			font-size: 14px;
			padding: 5px;
		}
	}

	header a.home-link{
		font-size: 12px;
	}
}


@media only screen and (max-width: 480px) {
	#results{
		td > .flex{
			display: block;
		}

		.type-item:nth-of-type(2){
			margin-top: 5px;
		}

		.type-item{
			width: 75px;

			.tera-icon{
				width: 12px;
				height: 12px;
			}
		}

		.results-section .table-container{
			max-height: 550px;
		}
	}
}


/* Ad styling and display */

#nitro-header-mobile,
#nitro-body-mobile,
#nitro-body-desktop,
#nitro-body-mobile,
#nitro-sidebar-right,
#nitro-sidebar-right-300,
#nitro-sidebar-left,
#nitro-sidebar-left-300{
	display:none;
}

#nitro-header-mobile{
	overflow:hidden;
}

.mobile-ad-container,
.desktop-ad-container{
	overflow: hidden;
	clip-path: inset(0 0 0 0);
	margin: 0 auto 5px auto;
	display:none;
}

.section.battle .mobile-ad-container{
	margin: 10px auto 5px auto;
}

// Bad ad fix

iframe#bh-div {
    display: none !important;
}

@media only screen and (min-width: 320px) and (max-width: 767px) {

	.nitro-pin-container{
		position: fixed;
		bottom:0;
		width:100%;
		z-index:25;
	}
	#nitro-header-mobile{
		display: block;
		margin: 0 auto;
	}

	#nitro-body-mobile{
		display: block;
	}

	.mobile-ad-container{
		display:block;
		width:320px;
		height:70px;
	}
}

@media only screen and (min-width: 768px) {
	#nitro-header-desktop{
		display: block;
	}

	#nitro-body-desktop{
		display: block;
	}

	.desktop-ad-container{
		display:block;
		width:728px;
		height:110px;
		margin:20px auto;

		&.nitro-header{
			margin:0 auto;
		}
	}
}

@media only screen and (min-width: 1390px) and (max-width:1649px) {
	#nitro-sidebar-left{
		display: block;
		position: fixed;
		top:95px;
		left:20px;
	}

	#nitro-sidebar-right{
		display: block;
		position: fixed;
		top:95px;
		right:20px;
	}
}

@media only screen and (min-width: 1650px) {
	#nitro-sidebar-left-300{
		display: block;
		position: fixed;
		top:95px;
		left:20px;
	}

	#nitro-sidebar-right-300{
		display: block;
		position: fixed;
		top:95px;
		right:20px;
	}
}
