[{"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 811, "matchups": [{"opponent": "<PERSON>ras", "rating": 675}, {"opponent": "bellibolt", "rating": 658}, {"opponent": "victree<PERSON>_shadow", "rating": 655}, {"opponent": "skeledirge", "rating": 627}, {"opponent": "tentacruel", "rating": 588}], "counters": [{"opponent": "pinsir_shadow", "rating": 212}, {"opponent": "s<PERSON><PERSON>", "rating": 328}, {"opponent": "genesect_chill", "rating": 334}, {"opponent": "crustle", "rating": 376}, {"opponent": "virizion", "rating": 460}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 25270}, {"moveId": "BULLET_SEED", "uses": 21092}, {"moveId": "INFESTATION", "uses": 16676}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 19678}, {"moveId": "GRASS_KNOT", "uses": 13777}, {"moveId": "ROCK_SLIDE", "uses": 12715}, {"moveId": "STONE_EDGE", "uses": 6679}, {"moveId": "BULLDOZE", "uses": 5852}, {"moveId": "RETURN", "uses": 4343}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 100}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 815, "matchups": [{"opponent": "bellibolt", "rating": 697}, {"opponent": "tentacruel", "rating": 638}, {"opponent": "<PERSON>ras", "rating": 611}, {"opponent": "cradily", "rating": 555}, {"opponent": "skeledirge", "rating": 552}], "counters": [{"opponent": "crustle_shadow", "rating": 113}, {"opponent": "kleavor", "rating": 169}, {"opponent": "pinsir", "rating": 212}, {"opponent": "virizion", "rating": 369}, {"opponent": "jellicent", "rating": 438}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 25352}, {"moveId": "BULLET_SEED", "uses": 21264}, {"moveId": "INFESTATION", "uses": 16362}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21062}, {"moveId": "GRASS_KNOT", "uses": 14742}, {"moveId": "ROCK_SLIDE", "uses": 13648}, {"moveId": "STONE_EDGE", "uses": 7194}, {"moveId": "BULLDOZE", "uses": 6294}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 95.2}, {"speciesId": "samu<PERSON>t_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 802, "matchups": [{"opponent": "skeledirge", "rating": 817}, {"opponent": "cradily", "rating": 808}, {"opponent": "feraligatr", "rating": 645}, {"opponent": "<PERSON>ras", "rating": 594}, {"opponent": "virizion", "rating": 585}], "counters": [{"opponent": "jellicent", "rating": 260}, {"opponent": "bellibolt", "rating": 372}, {"opponent": "tentacruel", "rating": 423}, {"opponent": "victree<PERSON>_shadow", "rating": 446}, {"opponent": "stunfisk", "rating": 481}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 41486}, {"moveId": "WATERFALL", "uses": 21514}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 24230}, {"moveId": "MEGAHORN", "uses": 12871}, {"moveId": "RAZOR_SHELL", "uses": 10185}, {"moveId": "LIQUIDATION", "uses": 6751}, {"moveId": "BLIZZARD", "uses": 6377}, {"moveId": "HYDRO_PUMP", "uses": 2660}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "MEGAHORN"], "score": 92.5}, {"speciesId": "lap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 782, "matchups": [{"opponent": "skeledirge", "rating": 813}, {"opponent": "cradily", "rating": 569}, {"opponent": "tentacruel", "rating": 567}, {"opponent": "bellibolt", "rating": 545}, {"opponent": "<PERSON>ras", "rating": 543}], "counters": [{"opponent": "serperior", "rating": 245}, {"opponent": "golisopod", "rating": 372}, {"opponent": "jellicent", "rating": 405}, {"opponent": "forretress", "rating": 411}, {"opponent": "stunfisk", "rating": 446}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 24086}, {"moveId": "ICE_SHARD", "uses": 15230}, {"moveId": "WATER_GUN", "uses": 13683}, {"moveId": "FROST_BREATH", "uses": 9993}], "chargedMoves": [{"moveId": "SPARKLING_ARIA", "uses": 18841}, {"moveId": "ICE_BEAM", "uses": 13548}, {"moveId": "SURF", "uses": 8278}, {"moveId": "SKULL_BASH", "uses": 7733}, {"moveId": "DRAGON_PULSE", "uses": 6589}, {"moveId": "BLIZZARD", "uses": 4844}, {"moveId": "HYDRO_PUMP", "uses": 3236}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYWAVE", "SPARKLING_ARIA", "ICE_BEAM"], "score": 92}, {"speciesId": "bellibolt", "speciesName": "Bellibolt", "rating": 789, "matchups": [{"opponent": "<PERSON>ras", "rating": 717}, {"opponent": "feraligatr", "rating": 705, "opRating": 295}, {"opponent": "golisopod", "rating": 687, "opRating": 312}, {"opponent": "tentacruel", "rating": 662}, {"opponent": "skeledirge", "rating": 602}], "counters": [{"opponent": "gastrodon", "rating": 278}, {"opponent": "stunfisk", "rating": 343}, {"opponent": "virizion", "rating": 378}, {"opponent": "cradily", "rating": 383}, {"opponent": "victree<PERSON>_shadow", "rating": 488}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 24638}, {"moveId": "SUCKER_PUNCH", "uses": 24116}, {"moveId": "WATER_GUN", "uses": 14169}], "chargedMoves": [{"moveId": "PARABOLIC_CHARGE", "uses": 34832}, {"moveId": "DISCHARGE", "uses": 22193}, {"moveId": "ZAP_CANNON", "uses": 5875}]}, "moveset": ["SUCKER_PUNCH", "PARABOLIC_CHARGE", "ZAP_CANNON"], "score": 91.5}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 764, "matchups": [{"opponent": "virizion", "rating": 645}, {"opponent": "cradily", "rating": 638}, {"opponent": "tentacruel", "rating": 627}, {"opponent": "skeledirge", "rating": 616}, {"opponent": "feraligatr", "rating": 508}], "counters": [{"opponent": "serperior_shadow", "rating": 245}, {"opponent": "forretress", "rating": 334}, {"opponent": "jellicent", "rating": 395}, {"opponent": "bellibolt", "rating": 417}, {"opponent": "samu<PERSON>t", "rating": 437}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 23118}, {"moveId": "ICE_SHARD", "uses": 15130}, {"moveId": "WATER_GUN", "uses": 14528}, {"moveId": "FROST_BREATH", "uses": 10215}], "chargedMoves": [{"moveId": "SPARKLING_ARIA", "uses": 17252}, {"moveId": "ICE_BEAM", "uses": 12405}, {"moveId": "SURF", "uses": 7566}, {"moveId": "SKULL_BASH", "uses": 6868}, {"moveId": "DRAGON_PULSE", "uses": 5844}, {"moveId": "RETURN", "uses": 5759}, {"moveId": "BLIZZARD", "uses": 4251}, {"moveId": "HYDRO_PUMP", "uses": 2932}]}, "moveset": ["PSYWAVE", "SPARKLING_ARIA", "ICE_BEAM"], "score": 90.3}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 800, "matchups": [{"opponent": "virizion", "rating": 889}, {"opponent": "feraligatr", "rating": 850}, {"opponent": "tentacruel", "rating": 724}, {"opponent": "bellibolt", "rating": 685}, {"opponent": "talonflame", "rating": 529}], "counters": [{"opponent": "skeledirge", "rating": 316}, {"opponent": "forretress", "rating": 319}, {"opponent": "toxtricity", "rating": 338}, {"opponent": "<PERSON>ras", "rating": 432}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 33320}, {"moveId": "MAGICAL_LEAF", "uses": 21231}, {"moveId": "RAZOR_LEAF", "uses": 8480}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33914}, {"moveId": "SLUDGE_BOMB", "uses": 16199}, {"moveId": "LEAF_TORNADO", "uses": 6421}, {"moveId": "ACID_SPRAY", "uses": 3565}, {"moveId": "SOLAR_BEAM", "uses": 2831}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 85.4}, {"speciesId": "golisopod", "speciesName": "Golisopod", "rating": 773, "matchups": [{"opponent": "virizion", "rating": 780}, {"opponent": "victree<PERSON>_shadow", "rating": 762, "opRating": 237}, {"opponent": "feraligatr", "rating": 680, "opRating": 319}, {"opponent": "<PERSON>ras", "rating": 609}, {"opponent": "cradily", "rating": 521}], "counters": [{"opponent": "bellibolt", "rating": 322}, {"opponent": "jellicent", "rating": 364}, {"opponent": "tentacruel", "rating": 387}, {"opponent": "skeledirge", "rating": 416}, {"opponent": "talonflame", "rating": 446}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 23685}, {"moveId": "SHADOW_CLAW", "uses": 18277}, {"moveId": "WATERFALL", "uses": 11420}, {"moveId": "METAL_CLAW", "uses": 9557}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18079}, {"moveId": "AQUA_JET", "uses": 17202}, {"moveId": "AERIAL_ACE", "uses": 12033}, {"moveId": "RAZOR_SHELL", "uses": 9434}, {"moveId": "LIQUIDATION", "uses": 6166}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "AQUA_JET"], "score": 84.4}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 785, "matchups": [{"opponent": "golisopod", "rating": 798, "opRating": 201}, {"opponent": "victree<PERSON>_shadow", "rating": 758, "opRating": 241}, {"opponent": "tentacruel", "rating": 738}, {"opponent": "bellibolt", "rating": 695}, {"opponent": "virizion", "rating": 642, "opRating": 357}], "counters": [{"opponent": "turtonator", "rating": 253}, {"opponent": "skeledirge", "rating": 327}, {"opponent": "<PERSON>ras", "rating": 378}, {"opponent": "feraligatr", "rating": 430}, {"opponent": "cradily", "rating": 469}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 28333}, {"moveId": "SHADOW_CLAW", "uses": 18139}, {"moveId": "EMBER", "uses": 16557}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 27737}, {"moveId": "THUNDER_PUNCH", "uses": 18527}, {"moveId": "SOLAR_BEAM", "uses": 7390}, {"moveId": "OVERHEAT", "uses": 5905}, {"moveId": "FIRE_BLAST", "uses": 3427}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 84.3}, {"speciesId": "crustle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 785, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 863, "opRating": 136}, {"opponent": "talonflame", "rating": 816, "opRating": 183}, {"opponent": "golisopod", "rating": 806, "opRating": 193}, {"opponent": "cradily", "rating": 540}, {"opponent": "bellibolt", "rating": 506}], "counters": [{"opponent": "tentacruel", "rating": 325}, {"opponent": "virizion", "rating": 381}, {"opponent": "feraligatr", "rating": 386}, {"opponent": "<PERSON>ras", "rating": 402}, {"opponent": "skeledirge", "rating": 438}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 39529}, {"moveId": "SMACK_DOWN", "uses": 23471}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24716}, {"moveId": "ROCK_SLIDE", "uses": 19530}, {"moveId": "ROCK_BLAST", "uses": 18694}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "ROCK_SLIDE"], "score": 83.4}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 806, "matchups": [{"opponent": "feraligatr", "rating": 834, "opRating": 165}, {"opponent": "<PERSON>ras", "rating": 790, "opRating": 209}, {"opponent": "golisopod", "rating": 790, "opRating": 209}, {"opponent": "virizion", "rating": 782, "opRating": 217}, {"opponent": "bellibolt", "rating": 681}], "counters": [{"opponent": "turtonator", "rating": 207}, {"opponent": "skeledirge", "rating": 247}, {"opponent": "talonflame", "rating": 324}, {"opponent": "tentacruel", "rating": 446}, {"opponent": "cradily", "rating": 477}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 17407}, {"moveId": "POISON_JAB", "uses": 15383}, {"moveId": "BULLET_SEED", "uses": 12590}, {"moveId": "MAGICAL_LEAF", "uses": 11737}, {"moveId": "RAZOR_LEAF", "uses": 5902}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 18357}, {"moveId": "GRASS_KNOT", "uses": 14056}, {"moveId": "SLUDGE_BOMB", "uses": 12471}, {"moveId": "LEAF_STORM", "uses": 8575}, {"moveId": "DAZZLING_GLEAM", "uses": 6612}, {"moveId": "SOLAR_BEAM", "uses": 2930}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 83}, {"speciesId": "feraligatr_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 756, "matchups": [{"opponent": "skeledirge", "rating": 883, "opRating": 116}, {"opponent": "talonflame", "rating": 874, "opRating": 125}, {"opponent": "typhlosion_shadow", "rating": 845, "opRating": 154}, {"opponent": "feraligatr", "rating": 638, "opRating": 361}, {"opponent": "virizion", "rating": 562, "opRating": 437}], "counters": [{"opponent": "bellibolt", "rating": 337}, {"opponent": "cradily", "rating": 380}, {"opponent": "<PERSON>ras", "rating": 402}, {"opponent": "victree<PERSON>_shadow", "rating": 413}, {"opponent": "tentacruel", "rating": 437}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 19546}, {"moveId": "ICE_FANG", "uses": 13952}, {"moveId": "WATER_GUN", "uses": 12056}, {"moveId": "WATERFALL", "uses": 11382}, {"moveId": "BITE", "uses": 6085}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 32102}, {"moveId": "CRUNCH", "uses": 15711}, {"moveId": "ICE_BEAM", "uses": 11740}, {"moveId": "HYDRO_PUMP", "uses": 3407}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "HYDRO_CANNON", "ICE_BEAM"], "score": 82.3}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 760, "matchups": [{"opponent": "feraligatr", "rating": 827, "opRating": 172}, {"opponent": "<PERSON>ras", "rating": 816, "opRating": 183}, {"opponent": "tentacruel", "rating": 799, "opRating": 200}, {"opponent": "victree<PERSON>_shadow", "rating": 750, "opRating": 250}, {"opponent": "skeledirge", "rating": 591, "opRating": 408}], "counters": [{"opponent": "gastrodon", "rating": 245}, {"opponent": "swampert", "rating": 279}, {"opponent": "virizion", "rating": 348}, {"opponent": "bellibolt", "rating": 352}, {"opponent": "cradily", "rating": 441}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 56929}, {"moveId": "LOW_KICK", "uses": 6071}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 23613}, {"moveId": "THUNDER_PUNCH", "uses": 13680}, {"moveId": "ICE_PUNCH", "uses": 13600}, {"moveId": "FLAMETHROWER", "uses": 8259}, {"moveId": "THUNDER", "uses": 3849}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 82}, {"speciesId": "zap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 779, "matchups": [{"opponent": "virizion", "rating": 897}, {"opponent": "tentacruel", "rating": 817}, {"opponent": "feraligatr", "rating": 817, "opRating": 182}, {"opponent": "<PERSON>ras", "rating": 807}, {"opponent": "skeledirge", "rating": 615}], "counters": [{"opponent": "stunfisk", "rating": 264}, {"opponent": "bellibolt", "rating": 282}, {"opponent": "toxtricity", "rating": 296}, {"opponent": "cradily", "rating": 358}, {"opponent": "lickilicky", "rating": 366}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 44523}, {"moveId": "CHARGE_BEAM", "uses": 18477}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 27355}, {"moveId": "ANCIENT_POWER", "uses": 13202}, {"moveId": "THUNDERBOLT", "uses": 12434}, {"moveId": "THUNDER", "uses": 5447}, {"moveId": "ZAP_CANNON", "uses": 4429}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 81.9}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 795, "matchups": [{"opponent": "golisopod", "rating": 858, "opRating": 141}, {"opponent": "<PERSON>ras", "rating": 851, "opRating": 148}, {"opponent": "feraligatr", "rating": 824, "opRating": 175}, {"opponent": "tentacruel", "rating": 772, "opRating": 227}, {"opponent": "virizion", "rating": 686, "opRating": 313}], "counters": [{"opponent": "gastrodon", "rating": 242}, {"opponent": "swampert", "rating": 279}, {"opponent": "cradily", "rating": 327}, {"opponent": "bellibolt", "rating": 455}, {"opponent": "skeledirge", "rating": 455}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 29277}, {"moveId": "EMBER", "uses": 16676}, {"moveId": "FIRE_SPIN", "uses": 14845}, {"moveId": "LOW_KICK", "uses": 2145}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20968}, {"moveId": "DRILL_RUN", "uses": 15245}, {"moveId": "FLAME_CHARGE", "uses": 13363}, {"moveId": "FIRE_BLAST", "uses": 6037}, {"moveId": "SCORCHING_SANDS", "uses": 5720}, {"moveId": "HEAT_WAVE", "uses": 1866}]}, "moveset": ["INCINERATE", "WILD_CHARGE", "FLAME_CHARGE"], "score": 81.2}, {"speciesId": "crustle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 740, "matchups": [{"opponent": "cradily", "rating": 636}, {"opponent": "victree<PERSON>_shadow", "rating": 630}, {"opponent": "<PERSON>ras", "rating": 536}, {"opponent": "virizion", "rating": 523}, {"opponent": "feraligatr", "rating": 516}], "counters": [{"opponent": "toxtricity", "rating": 290}, {"opponent": "skeledirge", "rating": 319}, {"opponent": "forretress", "rating": 371}, {"opponent": "tentacruel", "rating": 434}, {"opponent": "bellibolt", "rating": 490}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 39045}, {"moveId": "SMACK_DOWN", "uses": 23955}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 22398}, {"moveId": "ROCK_SLIDE", "uses": 17602}, {"moveId": "ROCK_BLAST", "uses": 17135}, {"moveId": "RETURN", "uses": 6018}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "ROCK_SLIDE"], "score": 81}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 780, "matchups": [{"opponent": "virizion", "rating": 907, "opRating": 92}, {"opponent": "victree<PERSON>_shadow", "rating": 670, "opRating": 329}, {"opponent": "<PERSON>ras", "rating": 568}, {"opponent": "bellibolt", "rating": 538}, {"opponent": "cradily", "rating": 529}], "counters": [{"opponent": "talonflame", "rating": 217}, {"opponent": "forretress", "rating": 260}, {"opponent": "skeledirge", "rating": 369}, {"opponent": "golisopod", "rating": 414}, {"opponent": "tentacruel", "rating": 426}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 32532}, {"moveId": "MAGICAL_LEAF", "uses": 20968}, {"moveId": "RAZOR_LEAF", "uses": 9488}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 31244}, {"moveId": "SLUDGE_BOMB", "uses": 14157}, {"moveId": "LEAF_TORNADO", "uses": 6014}, {"moveId": "RETURN", "uses": 5681}, {"moveId": "ACID_SPRAY", "uses": 3210}, {"moveId": "SOLAR_BEAM", "uses": 2682}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 80.7}, {"speciesId": "blastoise", "speciesName": "Blastoise", "rating": 702, "matchups": [{"opponent": "skeledirge", "rating": 771}, {"opponent": "virizion", "rating": 602}, {"opponent": "feraligatr", "rating": 569}, {"opponent": "cradily", "rating": 530}, {"opponent": "victree<PERSON>_shadow", "rating": 512}], "counters": [{"opponent": "venusaur", "rating": 242}, {"opponent": "bellibolt", "rating": 382}, {"opponent": "tentacruel", "rating": 455}, {"opponent": "<PERSON>ras", "rating": 476}, {"opponent": "golisopod", "rating": 485}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 32773}, {"moveId": "WATER_GUN", "uses": 19237}, {"moveId": "BITE", "uses": 10975}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 30417}, {"moveId": "ICE_BEAM", "uses": 10682}, {"moveId": "SKULL_BASH", "uses": 8062}, {"moveId": "RETURN", "uses": 6769}, {"moveId": "FLASH_CANNON", "uses": 3888}, {"moveId": "HYDRO_PUMP", "uses": 3330}]}, "moveset": ["ROLLOUT", "HYDRO_CANNON", "ICE_BEAM"], "score": 79.1}, {"speciesId": "kleavor", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 776, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 845, "opRating": 154}, {"opponent": "golisopod", "rating": 770, "opRating": 229}, {"opponent": "skeledirge", "rating": 725, "opRating": 274}, {"opponent": "talonflame", "rating": 710, "opRating": 289}, {"opponent": "typhlosion_shadow", "rating": 635, "opRating": 364}], "counters": [{"opponent": "jellicent", "rating": 114}, {"opponent": "bellibolt", "rating": 367}, {"opponent": "virizion", "rating": 381}, {"opponent": "tentacruel", "rating": 390}, {"opponent": "cradily", "rating": 483}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31108}, {"moveId": "QUICK_ATTACK", "uses": 20100}, {"moveId": "AIR_SLASH", "uses": 11760}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18327}, {"moveId": "STONE_EDGE", "uses": 16075}, {"moveId": "ROCK_SLIDE", "uses": 15226}, {"moveId": "TRAILBLAZE", "uses": 13364}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "STONE_EDGE"], "score": 78.9}, {"speciesId": "samu<PERSON>t", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 770, "matchups": [{"opponent": "typhlosion_shadow", "rating": 860, "opRating": 140}, {"opponent": "feraligatr", "rating": 674, "opRating": 325}, {"opponent": "<PERSON>ras", "rating": 668, "opRating": 331}, {"opponent": "talonflame", "rating": 605, "opRating": 394}, {"opponent": "golisopod", "rating": 560, "opRating": 440}], "counters": [{"opponent": "bellibolt", "rating": 335}, {"opponent": "cradily", "rating": 350}, {"opponent": "tentacruel", "rating": 381}, {"opponent": "virizion", "rating": 384}, {"opponent": "skeledirge", "rating": 397}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40924}, {"moveId": "WATERFALL", "uses": 22076}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 22256}, {"moveId": "MEGAHORN", "uses": 11583}, {"moveId": "RAZOR_SHELL", "uses": 9436}, {"moveId": "LIQUIDATION", "uses": 6204}, {"moveId": "BLIZZARD", "uses": 5862}, {"moveId": "RETURN", "uses": 5264}, {"moveId": "HYDRO_PUMP", "uses": 2312}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "MEGAHORN"], "score": 78.9}, {"speciesId": "mor<PERSON><PERSON>_full_belly", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Full Belly)", "rating": 743, "matchups": [{"opponent": "<PERSON>ras", "rating": 913, "opRating": 86}, {"opponent": "feraligatr", "rating": 895, "opRating": 104}, {"opponent": "tentacruel", "rating": 758, "opRating": 241}, {"opponent": "skeledirge", "rating": 679, "opRating": 320}, {"opponent": "bellibolt", "rating": 564}], "counters": [{"opponent": "gastrodon", "rating": 233}, {"opponent": "swampert", "rating": 268}, {"opponent": "victree<PERSON>_shadow", "rating": 329}, {"opponent": "virizion", "rating": 348}, {"opponent": "cradily", "rating": 441}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 42256}, {"moveId": "CHARGE_BEAM", "uses": 12887}, {"moveId": "BITE", "uses": 7895}], "chargedMoves": [{"moveId": "AURA_WHEEL_ELECTRIC", "uses": 34968}, {"moveId": "PSYCHIC_FANGS", "uses": 13622}, {"moveId": "SEED_BOMB", "uses": 7471}, {"moveId": "OUTRAGE", "uses": 6981}]}, "moveset": ["THUNDER_SHOCK", "AURA_WHEEL_ELECTRIC", "PSYCHIC_FANGS"], "score": 78.6}, {"speciesId": "toxtricity", "speciesName": "Toxtricity", "rating": 843, "matchups": [{"opponent": "tentacruel", "rating": 893, "opRating": 106}, {"opponent": "victree<PERSON>_shadow", "rating": 867, "opRating": 132}, {"opponent": "<PERSON>ras", "rating": 777, "opRating": 222}, {"opponent": "virizion", "rating": 738, "opRating": 261}, {"opponent": "cradily", "rating": 590, "opRating": 409}], "counters": [{"opponent": "gastrodon", "rating": 96}, {"opponent": "forretress", "rating": 168}, {"opponent": "stunfisk", "rating": 170}, {"opponent": "swampert", "rating": 220}, {"opponent": "bellibolt", "rating": 290}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 26160}, {"moveId": "POISON_JAB", "uses": 21042}, {"moveId": "SPARK", "uses": 15774}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34304}, {"moveId": "DISCHARGE", "uses": 16764}, {"moveId": "POWER_UP_PUNCH", "uses": 6892}, {"moveId": "ACID_SPRAY", "uses": 5010}]}, "moveset": ["ACID", "WILD_CHARGE", "POWER_UP_PUNCH"], "score": 78.3}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 758, "matchups": [{"opponent": "golisopod", "rating": 864, "opRating": 135}, {"opponent": "victree<PERSON>_shadow", "rating": 788, "opRating": 211}, {"opponent": "virizion", "rating": 695, "opRating": 304}, {"opponent": "bellibolt", "rating": 526}, {"opponent": "<PERSON>ras", "rating": 513}], "counters": [{"opponent": "gastrodon", "rating": 292}, {"opponent": "skeledirge", "rating": 316}, {"opponent": "feraligatr", "rating": 361}, {"opponent": "cradily", "rating": 397}, {"opponent": "tentacruel", "rating": 494}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27478}, {"moveId": "SHADOW_CLAW", "uses": 18284}, {"moveId": "EMBER", "uses": 17257}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 25329}, {"moveId": "THUNDER_PUNCH", "uses": 16530}, {"moveId": "SOLAR_BEAM", "uses": 6680}, {"moveId": "RETURN", "uses": 6039}, {"moveId": "OVERHEAT", "uses": 5388}, {"moveId": "FIRE_BLAST", "uses": 3127}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 78.2}, {"speciesId": "ampha<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 715, "matchups": [{"opponent": "feraligatr", "rating": 855, "opRating": 144}, {"opponent": "<PERSON>ras", "rating": 775}, {"opponent": "golisopod", "rating": 760, "opRating": 239}, {"opponent": "skeledirge", "rating": 671}, {"opponent": "tentacruel", "rating": 505}], "counters": [{"opponent": "virizion", "rating": 242}, {"opponent": "venusaur", "rating": 270}, {"opponent": "victree<PERSON>_shadow", "rating": 353}, {"opponent": "cradily", "rating": 455}, {"opponent": "bellibolt", "rating": 495}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 45426}, {"moveId": "CHARGE_BEAM", "uses": 17574}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 13556}, {"moveId": "THUNDER_PUNCH", "uses": 12670}, {"moveId": "POWER_GEM", "uses": 10358}, {"moveId": "TRAILBLAZE", "uses": 10237}, {"moveId": "FOCUS_BLAST", "uses": 5672}, {"moveId": "DRAGON_PULSE", "uses": 4150}, {"moveId": "THUNDER", "uses": 3430}, {"moveId": "ZAP_CANNON", "uses": 2846}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "BRUTAL_SWING", "TRAILBLAZE"], "score": 77.9}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 768, "matchups": [{"opponent": "talonflame", "rating": 836, "opRating": 163}, {"opponent": "feraligatr", "rating": 804, "opRating": 195}, {"opponent": "typhlosion_shadow", "rating": 804, "opRating": 195}, {"opponent": "golisopod", "rating": 794, "opRating": 205}, {"opponent": "victree<PERSON>_shadow", "rating": 773, "opRating": 226}], "counters": [{"opponent": "jellicent", "rating": 158}, {"opponent": "skeledirge", "rating": 322}, {"opponent": "tentacruel", "rating": 437}, {"opponent": "bellibolt", "rating": 457}, {"opponent": "cradily", "rating": 480}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 36224}, {"moveId": "FIRE_SPIN", "uses": 26776}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 18861}, {"moveId": "SCORCHING_SANDS", "uses": 14133}, {"moveId": "THUNDERBOLT", "uses": 10309}, {"moveId": "BRICK_BREAK", "uses": 10142}, {"moveId": "PSYCHIC", "uses": 6475}, {"moveId": "FIRE_BLAST", "uses": 3179}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 77.8}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 766, "matchups": [{"opponent": "virizion", "rating": 834, "opRating": 165}, {"opponent": "victree<PERSON>_shadow", "rating": 671, "opRating": 328}, {"opponent": "feraligatr", "rating": 618, "opRating": 381}, {"opponent": "skeledirge", "rating": 615}, {"opponent": "cradily", "rating": 571}], "counters": [{"opponent": "forretress", "rating": 223}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "stunfisk", "rating": 338}, {"opponent": "bellibolt", "rating": 360}, {"opponent": "<PERSON>ras", "rating": 383}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22322}, {"moveId": "POISON_STING", "uses": 21037}, {"moveId": "POISON_JAB", "uses": 19749}], "chargedMoves": [{"moveId": "SCALD", "uses": 22895}, {"moveId": "SLUDGE_WAVE", "uses": 13373}, {"moveId": "BLIZZARD", "uses": 9140}, {"moveId": "RETURN", "uses": 8589}, {"moveId": "ACID_SPRAY", "uses": 4565}, {"moveId": "HYDRO_PUMP", "uses": 4558}]}, "moveset": ["ACID", "SCALD", "SLUDGE_WAVE"], "score": 77.8}, {"speciesId": "barbara<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 780, "matchups": [{"opponent": "talonflame", "rating": 916, "opRating": 83}, {"opponent": "golisopod", "rating": 795, "opRating": 204}, {"opponent": "<PERSON>ras", "rating": 637, "opRating": 362}, {"opponent": "skeledirge", "rating": 634, "opRating": 365}, {"opponent": "tentacruel", "rating": 620, "opRating": 379}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 266}, {"opponent": "stunfisk", "rating": 301}, {"opponent": "virizion", "rating": 360}, {"opponent": "cradily", "rating": 375}, {"opponent": "bellibolt", "rating": 417}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 26750}, {"moveId": "MUD_SLAP", "uses": 21470}, {"moveId": "WATER_GUN", "uses": 14766}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 17609}, {"moveId": "STONE_EDGE", "uses": 17552}, {"moveId": "GRASS_KNOT", "uses": 11024}, {"moveId": "RAZOR_SHELL", "uses": 10890}, {"moveId": "SKULL_BASH", "uses": 5912}]}, "moveset": ["FURY_CUTTER", "CROSS_CHOP", "STONE_EDGE"], "score": 77.7}, {"speciesId": "drampa", "speciesName": "Drampa", "rating": 757, "matchups": [{"opponent": "feraligatr", "rating": 758}, {"opponent": "skeledirge", "rating": 640}, {"opponent": "golisopod", "rating": 560}, {"opponent": "bellibolt", "rating": 546}, {"opponent": "victree<PERSON>_shadow", "rating": 546}], "counters": [{"opponent": "virizion", "rating": 233}, {"opponent": "toxtricity", "rating": 325}, {"opponent": "<PERSON>ras", "rating": 329}, {"opponent": "tentacruel", "rating": 390}, {"opponent": "cradily", "rating": 441}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 39642}, {"moveId": "EXTRASENSORY", "uses": 23358}], "chargedMoves": [{"moveId": "SWIFT", "uses": 25117}, {"moveId": "FLY", "uses": 19590}, {"moveId": "OUTRAGE", "uses": 13558}, {"moveId": "DRAGON_PULSE", "uses": 4517}]}, "moveset": ["DRAGON_BREATH", "SWIFT", "FLY"], "score": 77.3}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 759, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 834, "opRating": 165}, {"opponent": "virizion", "rating": 817, "opRating": 182}, {"opponent": "golisopod", "rating": 798, "opRating": 201}, {"opponent": "tentacruel", "rating": 781, "opRating": 218}, {"opponent": "talonflame", "rating": 579, "opRating": 420}], "counters": [{"opponent": "jellicent", "rating": 265}, {"opponent": "skeledirge", "rating": 319}, {"opponent": "feraligatr", "rating": 330}, {"opponent": "cradily", "rating": 455}, {"opponent": "bellibolt", "rating": 495}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 37588}, {"moveId": "EMBER", "uses": 25412}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 35746}, {"moveId": "FLAME_CHARGE", "uses": 17600}, {"moveId": "FLAMETHROWER", "uses": 7298}, {"moveId": "HEAT_WAVE", "uses": 2327}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 77.1}, {"speciesId": "tentacruel_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 769, "matchups": [{"opponent": "talonflame", "rating": 866, "opRating": 133}, {"opponent": "skeledirge", "rating": 745, "opRating": 254}, {"opponent": "virizion", "rating": 718, "opRating": 281}, {"opponent": "cradily", "rating": 659}, {"opponent": "tentacruel", "rating": 588, "opRating": 411}], "counters": [{"opponent": "toxtricity", "rating": 164}, {"opponent": "zapdos", "rating": 223}, {"opponent": "forretress", "rating": 248}, {"opponent": "bellibolt", "rating": 377}, {"opponent": "<PERSON>ras", "rating": 432}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22580}, {"moveId": "POISON_STING", "uses": 21222}, {"moveId": "POISON_JAB", "uses": 19304}], "chargedMoves": [{"moveId": "SCALD", "uses": 26127}, {"moveId": "SLUDGE_WAVE", "uses": 15779}, {"moveId": "BLIZZARD", "uses": 10529}, {"moveId": "ACID_SPRAY", "uses": 5371}, {"moveId": "HYDRO_PUMP", "uses": 5202}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "SCALD", "SLUDGE_WAVE"], "score": 77}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 696, "matchups": [{"opponent": "feraligatr", "rating": 849, "opRating": 150}, {"opponent": "skeledirge", "rating": 730}, {"opponent": "talonflame", "rating": 715, "opRating": 284}, {"opponent": "<PERSON>ras", "rating": 644}, {"opponent": "tentacruel", "rating": 588}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 323}, {"opponent": "virizion", "rating": 327}, {"opponent": "forretress", "rating": 337}, {"opponent": "bellibolt", "rating": 397}, {"opponent": "cradily", "rating": 447}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 44442}, {"moveId": "CHARGE_BEAM", "uses": 18558}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 12664}, {"moveId": "THUNDER_PUNCH", "uses": 11965}, {"moveId": "POWER_GEM", "uses": 9703}, {"moveId": "TRAILBLAZE", "uses": 9689}, {"moveId": "FOCUS_BLAST", "uses": 5304}, {"moveId": "DRAGON_PULSE", "uses": 3902}, {"moveId": "RETURN", "uses": 3816}, {"moveId": "THUNDER", "uses": 3203}, {"moveId": "ZAP_CANNON", "uses": 2720}]}, "moveset": ["VOLT_SWITCH", "BRUTAL_SWING", "TRAILBLAZE"], "score": 76.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 728, "matchups": [{"opponent": "cradily", "rating": 815}, {"opponent": "samu<PERSON>t", "rating": 805, "opRating": 194}, {"opponent": "victree<PERSON>_shadow", "rating": 611, "opRating": 388}, {"opponent": "virizion", "rating": 592, "opRating": 407}, {"opponent": "bellibolt", "rating": 576}], "counters": [{"opponent": "skeledirge", "rating": 288}, {"opponent": "<PERSON>ras", "rating": 352}, {"opponent": "tentacruel", "rating": 357}, {"opponent": "typhlosion_shadow", "rating": 384}, {"opponent": "talonflame", "rating": 407}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 33078}, {"moveId": "VOLT_SWITCH", "uses": 29922}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 15394}, {"moveId": "CROSS_POISON", "uses": 13757}, {"moveId": "LUNGE", "uses": 13336}, {"moveId": "BUG_BUZZ", "uses": 8580}, {"moveId": "ENERGY_BALL", "uses": 7318}, {"moveId": "RETURN", "uses": 4599}]}, "moveset": ["FURY_CUTTER", "DISCHARGE", "LUNGE"], "score": 76.2}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 717, "matchups": [{"opponent": "golisopod", "rating": 652}, {"opponent": "skeledirge", "rating": 630}, {"opponent": "feraligatr", "rating": 621}, {"opponent": "victree<PERSON>_shadow", "rating": 592}, {"opponent": "<PERSON>ras", "rating": 540}], "counters": [{"opponent": "virizion", "rating": 236}, {"opponent": "bellibolt", "rating": 385}, {"opponent": "tentacruel", "rating": 402}, {"opponent": "cradily", "rating": 405}, {"opponent": "typhlosion_shadow", "rating": 493}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 37550}, {"moveId": "LICK", "uses": 19860}, {"moveId": "ZEN_HEADBUTT", "uses": 5592}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25704}, {"moveId": "SHADOW_BALL", "uses": 13027}, {"moveId": "EARTHQUAKE", "uses": 10453}, {"moveId": "SOLAR_BEAM", "uses": 7184}, {"moveId": "HYPER_BEAM", "uses": 6642}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "SHADOW_BALL"], "score": 76.1}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 734, "matchups": [{"opponent": "golisopod", "rating": 827, "opRating": 172}, {"opponent": "talonflame", "rating": 805, "opRating": 194}, {"opponent": "victree<PERSON>_shadow", "rating": 717, "opRating": 282}, {"opponent": "bellibolt", "rating": 666}, {"opponent": "virizion", "rating": 621, "opRating": 378}], "counters": [{"opponent": "swampert", "rating": 231}, {"opponent": "tentacruel", "rating": 363}, {"opponent": "skeledirge", "rating": 416}, {"opponent": "<PERSON>ras", "rating": 469}, {"opponent": "cradily", "rating": 480}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34189}, {"moveId": "FIRE_FANG", "uses": 17121}, {"moveId": "TACKLE", "uses": 11703}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 18619}, {"moveId": "OVERHEAT", "uses": 15920}, {"moveId": "FOCUS_BLAST", "uses": 10954}, {"moveId": "PSYCHIC", "uses": 9563}, {"moveId": "RETURN", "uses": 8015}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 75.5}, {"speciesId": "feraligatr", "speciesName": "Feraligatr", "rating": 727, "matchups": [{"opponent": "skeledirge", "rating": 902, "opRating": 97}, {"opponent": "typhlosion_shadow", "rating": 874, "opRating": 125}, {"opponent": "talonflame", "rating": 858, "opRating": 141}, {"opponent": "swampert", "rating": 685, "opRating": 314}, {"opponent": "stunfisk", "rating": 603, "opRating": 396}], "counters": [{"opponent": "serperior", "rating": 138}, {"opponent": "cradily", "rating": 325}, {"opponent": "victree<PERSON>_shadow", "rating": 353}, {"opponent": "tentacruel", "rating": 381}, {"opponent": "bellibolt", "rating": 412}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 18994}, {"moveId": "ICE_FANG", "uses": 13994}, {"moveId": "WATER_GUN", "uses": 12138}, {"moveId": "WATERFALL", "uses": 11578}, {"moveId": "BITE", "uses": 6378}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 28973}, {"moveId": "CRUNCH", "uses": 13915}, {"moveId": "ICE_BEAM", "uses": 10491}, {"moveId": "RETURN", "uses": 6623}, {"moveId": "HYDRO_PUMP", "uses": 3163}]}, "moveset": ["SHADOW_CLAW", "HYDRO_CANNON", "ICE_BEAM"], "score": 75.4}, {"speciesId": "gra<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 708, "matchups": [{"opponent": "bellibolt", "rating": 750}, {"opponent": "feraligatr", "rating": 664, "opRating": 335}, {"opponent": "skeledirge", "rating": 654, "opRating": 345}, {"opponent": "cradily", "rating": 592}, {"opponent": "tentacruel", "rating": 568}], "counters": [{"opponent": "<PERSON>ras", "rating": 261}, {"opponent": "gastrodon", "rating": 283}, {"opponent": "talonflame", "rating": 315}, {"opponent": "virizion", "rating": 339}, {"opponent": "golisopod", "rating": 368}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 33017}, {"moveId": "MUD_SLAP", "uses": 29983}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 32947}, {"moveId": "SLUDGE_BOMB", "uses": 27010}, {"moveId": "ACID_SPRAY", "uses": 3155}]}, "moveset": ["MUD_SLAP", "POISON_FANG", "SLUDGE_BOMB"], "score": 75}, {"speciesId": "poliwrath", "speciesName": "Poliwrath", "rating": 711, "matchups": [{"opponent": "cradily", "rating": 672}, {"opponent": "virizion", "rating": 641, "opRating": 358}, {"opponent": "talonflame", "rating": 638, "opRating": 361}, {"opponent": "feraligatr", "rating": 635, "opRating": 364}, {"opponent": "<PERSON>ras", "rating": 576}], "counters": [{"opponent": "toxtricity", "rating": 164}, {"opponent": "jellicent", "rating": 318}, {"opponent": "bellibolt", "rating": 357}, {"opponent": "tentacruel", "rating": 434}, {"opponent": "skeledirge", "rating": 488}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 19774}, {"moveId": "BUBBLE", "uses": 18594}, {"moveId": "MUD_SHOT", "uses": 18372}, {"moveId": "ROCK_SMASH", "uses": 6259}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 19917}, {"moveId": "SCALD", "uses": 12698}, {"moveId": "ICE_PUNCH", "uses": 10835}, {"moveId": "ICY_WIND", "uses": 5294}, {"moveId": "RETURN", "uses": 4462}, {"moveId": "POWER_UP_PUNCH", "uses": 4436}, {"moveId": "SUBMISSION", "uses": 3005}, {"moveId": "HYDRO_PUMP", "uses": 2505}]}, "moveset": ["COUNTER", "ICY_WIND", "SCALD"], "score": 74.9}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 721, "matchups": [{"opponent": "skeledirge", "rating": 887}, {"opponent": "talonflame", "rating": 838, "opRating": 161}, {"opponent": "virizion", "rating": 629}, {"opponent": "bellibolt", "rating": 618}, {"opponent": "tentacruel", "rating": 590}], "counters": [{"opponent": "cradily", "rating": 269}, {"opponent": "golisopod", "rating": 322}, {"opponent": "samu<PERSON>t", "rating": 362}, {"opponent": "<PERSON>ras", "rating": 383}, {"opponent": "victree<PERSON>_shadow", "rating": 398}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38176}, {"moveId": "WATER_GUN", "uses": 24824}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 21221}, {"moveId": "SLUDGE", "uses": 15983}, {"moveId": "EARTHQUAKE", "uses": 8912}, {"moveId": "MUDDY_WATER", "uses": 8191}, {"moveId": "SURF", "uses": 5895}, {"moveId": "SLUDGE_WAVE", "uses": 2842}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 74.6}, {"speciesId": "king<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 638, "matchups": [{"opponent": "skeledirge", "rating": 803}, {"opponent": "tentacruel", "rating": 712}, {"opponent": "bellibolt", "rating": 702}, {"opponent": "virizion", "rating": 594}, {"opponent": "<PERSON>ras", "rating": 581}], "counters": [{"opponent": "toxtricity", "rating": 274}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "victree<PERSON>_shadow", "rating": 407}, {"opponent": "golisopod", "rating": 414}, {"opponent": "cradily", "rating": 438}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 27721}, {"moveId": "WATER_GUN", "uses": 18354}, {"moveId": "WATERFALL", "uses": 16928}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 20331}, {"moveId": "OCTAZOOKA", "uses": 12174}, {"moveId": "BLIZZARD", "uses": 10620}, {"moveId": "HYDRO_PUMP", "uses": 10283}, {"moveId": "RETURN", "uses": 9546}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 74.2}, {"speciesId": "gal<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 744, "matchups": [{"opponent": "feraligatr", "rating": 818, "opRating": 181}, {"opponent": "samu<PERSON>t", "rating": 792, "opRating": 207}, {"opponent": "golisopod", "rating": 754, "opRating": 245}, {"opponent": "cradily", "rating": 748}, {"opponent": "virizion", "rating": 707, "opRating": 292}], "counters": [{"opponent": "skeledirge", "rating": 275}, {"opponent": "typhlosion_shadow", "rating": 344}, {"opponent": "tentacruel", "rating": 396}, {"opponent": "<PERSON>ras", "rating": 426}, {"opponent": "bellibolt", "rating": 440}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32831}, {"moveId": "VOLT_SWITCH", "uses": 30169}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 16564}, {"moveId": "CROSS_POISON", "uses": 14974}, {"moveId": "LUNGE", "uses": 14343}, {"moveId": "BUG_BUZZ", "uses": 9270}, {"moveId": "ENERGY_BALL", "uses": 7878}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "DISCHARGE", "CROSS_POISON"], "score": 74.1}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 703, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 688, "opRating": 311}, {"opponent": "talonflame", "rating": 659, "opRating": 340}, {"opponent": "feraligatr", "rating": 610, "opRating": 389}, {"opponent": "tentacruel", "rating": 600}, {"opponent": "<PERSON>ras", "rating": 507}], "counters": [{"opponent": "virizion", "rating": 303}, {"opponent": "skeledirge", "rating": 316}, {"opponent": "bellibolt", "rating": 362}, {"opponent": "cradily", "rating": 394}, {"opponent": "typhlosion_shadow", "rating": 394}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 34206}, {"moveId": "TACKLE", "uses": 23147}, {"moveId": "ZEN_HEADBUTT", "uses": 5640}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24495}, {"moveId": "THUNDERBOLT", "uses": 13039}, {"moveId": "ICE_BEAM", "uses": 12283}, {"moveId": "STOMP", "uses": 8620}, {"moveId": "GYRO_BALL", "uses": 4487}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 74.1}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "virizion", "rating": 679, "opRating": 320}, {"opponent": "skeledirge", "rating": 643}, {"opponent": "victree<PERSON>_shadow", "rating": 611, "opRating": 388}, {"opponent": "feraligatr", "rating": 607, "opRating": 392}, {"opponent": "<PERSON>ras", "rating": 503}], "counters": [{"opponent": "lickilicky", "rating": 290}, {"opponent": "bellibolt", "rating": 382}, {"opponent": "talonflame", "rating": 401}, {"opponent": "cradily", "rating": 472}, {"opponent": "tentacruel", "rating": 476}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 22816}, {"moveId": "LEAFAGE", "uses": 17760}, {"moveId": "MAGICAL_LEAF", "uses": 16412}, {"moveId": "RAZOR_LEAF", "uses": 6010}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 23778}, {"moveId": "BRAVE_BIRD", "uses": 19255}, {"moveId": "SPIRIT_SHACKLE", "uses": 12146}, {"moveId": "ENERGY_BALL", "uses": 4560}, {"moveId": "SHADOW_SNEAK", "uses": 3196}]}, "moveset": ["ASTONISH", "FRENZY_PLANT", "SPIRIT_SHACKLE"], "score": 73.8}, {"speciesId": "walrein_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 726, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 837, "opRating": 162}, {"opponent": "swampert", "rating": 720, "opRating": 280}, {"opponent": "talonflame", "rating": 530, "opRating": 470}, {"opponent": "cradily", "rating": 512}, {"opponent": "feraligatr", "rating": 502, "opRating": 497}], "counters": [{"opponent": "<PERSON>ras", "rating": 266}, {"opponent": "forretress", "rating": 273}, {"opponent": "typhlosion_shadow", "rating": 321}, {"opponent": "bellibolt", "rating": 442}, {"opponent": "tentacruel", "rating": 473}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 30075}, {"moveId": "WATERFALL", "uses": 19092}, {"moveId": "FROST_BREATH", "uses": 13812}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 30580}, {"moveId": "EARTHQUAKE", "uses": 13430}, {"moveId": "WATER_PULSE", "uses": 13162}, {"moveId": "BLIZZARD", "uses": 5693}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 73.4}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 700, "matchups": [{"opponent": "swampert", "rating": 907, "opRating": 92}, {"opponent": "virizion", "rating": 681, "opRating": 318}, {"opponent": "skeledirge", "rating": 651, "opRating": 348}, {"opponent": "golisopod", "rating": 586, "opRating": 413}, {"opponent": "cradily", "rating": 535}], "counters": [{"opponent": "drampa", "rating": 268}, {"opponent": "victree<PERSON>_shadow", "rating": 338}, {"opponent": "bellibolt", "rating": 350}, {"opponent": "<PERSON>ras", "rating": 378}, {"opponent": "tentacruel", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 32932}, {"moveId": "SUCKER_PUNCH", "uses": 30068}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 21382}, {"moveId": "SHADOW_BALL", "uses": 21178}, {"moveId": "SEED_BOMB", "uses": 20443}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SEED_BOMB"], "score": 73.2}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 701, "matchups": [{"opponent": "bellibolt", "rating": 679}, {"opponent": "tentacruel", "rating": 661}, {"opponent": "feraligatr", "rating": 647, "opRating": 352}, {"opponent": "<PERSON>ras", "rating": 637}, {"opponent": "skeledirge", "rating": 581}], "counters": [{"opponent": "serperior", "rating": 236}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "virizion", "rating": 330}, {"opponent": "swampert", "rating": 336}, {"opponent": "cradily", "rating": 397}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 35184}, {"moveId": "MUD_SHOT", "uses": 27816}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 28594}, {"moveId": "DISCHARGE", "uses": 23207}, {"moveId": "MUDDY_WATER", "uses": 11228}]}, "moveset": ["THUNDER_SHOCK", "MUD_BOMB", "DISCHARGE"], "score": 72.9}, {"speciesId": "turtonator", "speciesName": "Turtonator", "rating": 734, "matchups": [{"opponent": "typhlosion_shadow", "rating": 746, "opRating": 253}, {"opponent": "golisopod", "rating": 658, "opRating": 341}, {"opponent": "virizion", "rating": 577, "opRating": 422}, {"opponent": "skeledirge", "rating": 566, "opRating": 433}, {"opponent": "bellibolt", "rating": 528}], "counters": [{"opponent": "drampa", "rating": 187}, {"opponent": "<PERSON>ras", "rating": 324}, {"opponent": "tentacruel", "rating": 340}, {"opponent": "feraligatr", "rating": 408}, {"opponent": "cradily", "rating": 416}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27030}, {"moveId": "EMBER", "uses": 18869}, {"moveId": "FIRE_SPIN", "uses": 17074}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 19999}, {"moveId": "OVERHEAT", "uses": 18428}, {"moveId": "DRAGON_PULSE", "uses": 18196}, {"moveId": "FLASH_CANNON", "uses": 6417}]}, "moveset": ["INCINERATE", "DRAGON_PULSE", "OVERHEAT"], "score": 72.8}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 687, "matchups": [{"opponent": "feraligatr", "rating": 837, "opRating": 162}, {"opponent": "virizion", "rating": 805, "opRating": 194}, {"opponent": "golisopod", "rating": 757, "opRating": 242}, {"opponent": "skeledirge", "rating": 734, "opRating": 265}, {"opponent": "talonflame", "rating": 611, "opRating": 388}], "counters": [{"opponent": "zapdos", "rating": 243}, {"opponent": "tentacruel", "rating": 349}, {"opponent": "cradily", "rating": 394}, {"opponent": "<PERSON>ras", "rating": 406}, {"opponent": "bellibolt", "rating": 415}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 37701}, {"moveId": "SNARL", "uses": 25299}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 18730}, {"moveId": "SHADOW_BALL", "uses": 18355}, {"moveId": "SLUDGE_BOMB", "uses": 13642}, {"moveId": "FLAMETHROWER", "uses": 12340}]}, "moveset": ["SHADOW_CLAW", "FOUL_PLAY", "SHADOW_BALL"], "score": 72.8}, {"speciesId": "palkia", "speciesName": "Pa<PERSON><PERSON>", "rating": 708, "matchups": [{"opponent": "talonflame", "rating": 888, "opRating": 111}, {"opponent": "skeledirge", "rating": 880, "opRating": 119}, {"opponent": "typhlosion_shadow", "rating": 857, "opRating": 142}, {"opponent": "tentacruel", "rating": 607}, {"opponent": "<PERSON>ras", "rating": 515}], "counters": [{"opponent": "bellibolt", "rating": 310}, {"opponent": "victree<PERSON>_shadow", "rating": 344}, {"opponent": "cradily", "rating": 386}, {"opponent": "virizion", "rating": 403}, {"opponent": "feraligatr", "rating": 418}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 31821}, {"moveId": "DRAGON_TAIL", "uses": 31179}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 35443}, {"moveId": "DRACO_METEOR", "uses": 13533}, {"moveId": "FIRE_BLAST", "uses": 9410}, {"moveId": "HYDRO_PUMP", "uses": 4608}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "DRACO_METEOR"], "score": 72.6}, {"speciesId": "pawmot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 747, "matchups": [{"opponent": "tentacruel", "rating": 834, "opRating": 165}, {"opponent": "feraligatr", "rating": 834, "opRating": 165}, {"opponent": "<PERSON>ras", "rating": 766, "opRating": 233}, {"opponent": "skeledirge", "rating": 712, "opRating": 287}, {"opponent": "bellibolt", "rating": 611}], "counters": [{"opponent": "toxtricity", "rating": 193}, {"opponent": "victree<PERSON>_shadow", "rating": 206}, {"opponent": "swampert", "rating": 214}, {"opponent": "virizion", "rating": 363}, {"opponent": "cradily", "rating": 402}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34767}, {"moveId": "SPARK", "uses": 14781}, {"moveId": "CHARGE_BEAM", "uses": 10781}, {"moveId": "LOW_KICK", "uses": 2655}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 19933}, {"moveId": "WILD_CHARGE", "uses": 17409}, {"moveId": "THUNDER_PUNCH", "uses": 10074}, {"moveId": "BRICK_BREAK", "uses": 8042}, {"moveId": "DISCHARGE", "uses": 4170}, {"moveId": "THUNDERBOLT", "uses": 3079}]}, "moveset": ["THUNDER_SHOCK", "BRICK_BREAK", "WILD_CHARGE"], "score": 72.6}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 735, "matchups": [{"opponent": "tentacruel", "rating": 741, "opRating": 258}, {"opponent": "skeledirge", "rating": 686, "opRating": 313}, {"opponent": "feraligatr", "rating": 598, "opRating": 401}, {"opponent": "golisopod", "rating": 588, "opRating": 411}, {"opponent": "bellibolt", "rating": 532}], "counters": [{"opponent": "gastrodon", "rating": 273}, {"opponent": "victree<PERSON>_shadow", "rating": 347}, {"opponent": "cradily", "rating": 350}, {"opponent": "virizion", "rating": 369}, {"opponent": "<PERSON>ras", "rating": 476}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 8680}, {"moveId": "SNARL", "uses": 8615}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3910}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3497}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3281}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3228}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2987}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2978}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2886}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2855}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2768}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2690}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2634}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2631}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2596}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2565}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2502}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1953}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28094}, {"moveId": "PSYCHIC_FANGS", "uses": 15675}, {"moveId": "CRUNCH", "uses": 11947}, {"moveId": "RETURN", "uses": 5201}, {"moveId": "HYPER_BEAM", "uses": 2063}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 72.5}, {"speciesId": "darmanitan_standard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Standard) (Shadow)", "rating": 711, "matchups": [{"opponent": "cradily", "rating": 771}, {"opponent": "golisopod", "rating": 771, "opRating": 228}, {"opponent": "<PERSON>ras", "rating": 759, "opRating": 240}, {"opponent": "bellibolt", "rating": 714}, {"opponent": "virizion", "rating": 545, "opRating": 454}], "counters": [{"opponent": "gastrodon", "rating": 129}, {"opponent": "stunfisk", "rating": 170}, {"opponent": "swampert", "rating": 276}, {"opponent": "tentacruel", "rating": 431}, {"opponent": "skeledirge", "rating": 488}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36132}, {"moveId": "FIRE_FANG", "uses": 16089}, {"moveId": "TACKLE", "uses": 10798}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 21182}, {"moveId": "OVERHEAT", "uses": 18018}, {"moveId": "FOCUS_BLAST", "uses": 12635}, {"moveId": "PSYCHIC", "uses": 11136}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 72.4}, {"speciesId": "palkia_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 705, "matchups": [{"opponent": "talonflame", "rating": 885, "opRating": 114}, {"opponent": "skeledirge", "rating": 877, "opRating": 122}, {"opponent": "typhlosion_shadow", "rating": 854, "opRating": 145}, {"opponent": "tentacruel", "rating": 602}, {"opponent": "<PERSON>ras", "rating": 507}], "counters": [{"opponent": "bellibolt", "rating": 310}, {"opponent": "victree<PERSON>_shadow", "rating": 344}, {"opponent": "cradily", "rating": 386}, {"opponent": "virizion", "rating": 403}, {"opponent": "feraligatr", "rating": 418}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 31828}, {"moveId": "DRAGON_TAIL", "uses": 31172}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 25883}, {"moveId": "SPACIAL_REND", "uses": 22592}, {"moveId": "FIRE_BLAST", "uses": 6806}, {"moveId": "DRACO_METEOR", "uses": 4333}, {"moveId": "HYDRO_PUMP", "uses": 3420}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "DRACO_METEOR"], "score": 72.4}, {"speciesId": "skeledirge", "speciesName": "Skeledirge", "rating": 799, "matchups": [{"opponent": "virizion", "rating": 938, "opRating": 61}, {"opponent": "victree<PERSON>_shadow", "rating": 716, "opRating": 283}, {"opponent": "golisopod", "rating": 611, "opRating": 388}, {"opponent": "cradily", "rating": 580}, {"opponent": "<PERSON>ras", "rating": 561, "opRating": 438}], "counters": [{"opponent": "feraligatr", "rating": 128}, {"opponent": "jellicent", "rating": 191}, {"opponent": "swampert", "rating": 234}, {"opponent": "bellibolt", "rating": 340}, {"opponent": "tentacruel", "rating": 470}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34022}, {"moveId": "HEX", "uses": 23319}, {"moveId": "BITE", "uses": 5664}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 16012}, {"moveId": "TORCH_SONG", "uses": 15520}, {"moveId": "SHADOW_BALL", "uses": 10220}, {"moveId": "CRUNCH", "uses": 9473}, {"moveId": "DISARMING_VOICE", "uses": 8027}, {"moveId": "FLAMETHROWER", "uses": 3619}]}, "moveset": ["INCINERATE", "TORCH_SONG", "SHADOW_BALL"], "score": 72.1}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 761, "matchups": [{"opponent": "forretress", "rating": 863, "opRating": 136}, {"opponent": "typhlosion_shadow", "rating": 811, "opRating": 188}, {"opponent": "victree<PERSON>_shadow", "rating": 780, "opRating": 219}, {"opponent": "turtonator", "rating": 773, "opRating": 226}, {"opponent": "stunfisk", "rating": 523, "opRating": 476}], "counters": [{"opponent": "golisopod", "rating": 312}, {"opponent": "cradily", "rating": 341}, {"opponent": "<PERSON>ras", "rating": 350}, {"opponent": "bellibolt", "rating": 385}, {"opponent": "tentacruel", "rating": 390}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33756}, {"moveId": "EMBER", "uses": 29244}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26758}, {"moveId": "SCORCHING_SANDS", "uses": 23313}, {"moveId": "FLAMETHROWER", "uses": 8370}, {"moveId": "FIRE_BLAST", "uses": 4561}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 71.7}, {"speciesId": "jellicent", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "skeledirge", "rating": 895}, {"opponent": "tentacruel", "rating": 765}, {"opponent": "virizion", "rating": 752}, {"opponent": "cradily", "rating": 612}, {"opponent": "<PERSON>ras", "rating": 604}], "counters": [{"opponent": "mor<PERSON><PERSON>_full_belly", "rating": 111}, {"opponent": "toxtricity", "rating": 161}, {"opponent": "venusaur", "rating": 232}, {"opponent": "bellibolt", "rating": 257}, {"opponent": "lickilicky", "rating": 307}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 36077}, {"moveId": "BUBBLE", "uses": 26923}], "chargedMoves": [{"moveId": "SURF", "uses": 22587}, {"moveId": "SHADOW_BALL", "uses": 20243}, {"moveId": "ICE_BEAM", "uses": 13758}, {"moveId": "BUBBLE_BEAM", "uses": 6490}]}, "moveset": ["HEX", "SURF", "SHADOW_BALL"], "score": 71.6}, {"speciesId": "<PERSON><PERSON>_<PERSON>", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 744, "matchups": [{"opponent": "zapdos", "rating": 844, "opRating": 155}, {"opponent": "talonflame", "rating": 773, "opRating": 226}, {"opponent": "typhlosion_shadow", "rating": 712, "opRating": 287}, {"opponent": "turtonator", "rating": 658, "opRating": 341}, {"opponent": "stunfisk", "rating": 628, "opRating": 371}], "counters": [{"opponent": "tentacruel", "rating": 286}, {"opponent": "virizion", "rating": 321}, {"opponent": "bellibolt", "rating": 367}, {"opponent": "<PERSON>ras", "rating": 389}, {"opponent": "cradily", "rating": 433}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 46919}, {"moveId": "STRUGGLE_BUG", "uses": 16081}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 22667}, {"moveId": "ROCK_BLAST", "uses": 21725}, {"moveId": "LIQUIDATION", "uses": 14818}, {"moveId": "WATER_PULSE", "uses": 3720}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "LIQUIDATION"], "score": 71}, {"speciesId": "oinkologne_female", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Female)", "rating": 674, "matchups": [{"opponent": "feraligatr", "rating": 767, "opRating": 232}, {"opponent": "tentacruel", "rating": 637, "opRating": 362}, {"opponent": "victree<PERSON>_shadow", "rating": 586, "opRating": 413}, {"opponent": "talonflame", "rating": 586, "opRating": 413}, {"opponent": "<PERSON>ras", "rating": 532, "opRating": 467}], "counters": [{"opponent": "virizion", "rating": 266}, {"opponent": "skeledirge", "rating": 330}, {"opponent": "cradily", "rating": 372}, {"opponent": "bellibolt", "rating": 372}, {"opponent": "golisopod", "rating": 386}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 50502}, {"moveId": "TAKE_DOWN", "uses": 12498}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29206}, {"moveId": "TRAILBLAZE", "uses": 20127}, {"moveId": "DIG", "uses": 13714}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 71}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 746, "matchups": [{"opponent": "virizion", "rating": 803, "opRating": 196}, {"opponent": "skeledirge", "rating": 782, "opRating": 217}, {"opponent": "victree<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "talonflame", "rating": 766, "opRating": 233}, {"opponent": "tentacruel", "rating": 739, "opRating": 260}], "counters": [{"opponent": "feraligatr", "rating": 204}, {"opponent": "jellicent", "rating": 270}, {"opponent": "<PERSON>ras", "rating": 335}, {"opponent": "cradily", "rating": 372}, {"opponent": "bellibolt", "rating": 417}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 21441}, {"moveId": "FIRE_FANG", "uses": 20845}, {"moveId": "THUNDER_FANG", "uses": 16298}, {"moveId": "BITE", "uses": 4395}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 16739}, {"moveId": "PSYCHIC_FANGS", "uses": 11322}, {"moveId": "FLAMETHROWER", "uses": 9551}, {"moveId": "SCORCHING_SANDS", "uses": 9277}, {"moveId": "CRUNCH", "uses": 8447}, {"moveId": "BULLDOZE", "uses": 4948}, {"moveId": "FIRE_BLAST", "uses": 2563}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 70.8}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 683, "matchups": [{"opponent": "feraligatr", "rating": 728, "opRating": 271}, {"opponent": "tentacruel", "rating": 700}, {"opponent": "<PERSON>ras", "rating": 626, "opRating": 373}, {"opponent": "virizion", "rating": 579, "opRating": 420}, {"opponent": "skeledirge", "rating": 560, "opRating": 439}], "counters": [{"opponent": "samu<PERSON>t", "rating": 268}, {"opponent": "forretress", "rating": 303}, {"opponent": "bellibolt", "rating": 315}, {"opponent": "cradily", "rating": 369}, {"opponent": "golisopod", "rating": 382}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 50947}, {"moveId": "ZEN_HEADBUTT", "uses": 11151}, {"moveId": "YAWN", "uses": 895}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 20243}, {"moveId": "TRAILBLAZE", "uses": 15526}, {"moveId": "FUTURE_SIGHT", "uses": 10324}, {"moveId": "PSYCHIC", "uses": 9922}, {"moveId": "FOUL_PLAY", "uses": 7005}]}, "moveset": ["CONFUSION", "BRUTAL_SWING", "TRAILBLAZE"], "score": 70.6}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 778, "matchups": [{"opponent": "samu<PERSON>t", "rating": 753, "opRating": 246}, {"opponent": "typhlosion_shadow", "rating": 695, "opRating": 304}, {"opponent": "stunfisk", "rating": 603, "opRating": 396}, {"opponent": "talonflame", "rating": 600, "opRating": 399}, {"opponent": "feraligatr", "rating": 588, "opRating": 411}], "counters": [{"opponent": "cradily", "rating": 238}, {"opponent": "virizion", "rating": 296}, {"opponent": "bellibolt", "rating": 300}, {"opponent": "golisopod", "rating": 372}, {"opponent": "tentacruel", "rating": 476}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 29189}, {"moveId": "COUNTER", "uses": 19934}, {"moveId": "METAL_CLAW", "uses": 13809}], "chargedMoves": [{"moveId": "SWIFT", "uses": 21345}, {"moveId": "CLOSE_COMBAT", "uses": 20612}, {"moveId": "TRAILBLAZE", "uses": 13123}, {"moveId": "PLAY_ROUGH", "uses": 5050}, {"moveId": "HYPER_BEAM", "uses": 2888}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 70.6}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 748, "matchups": [{"opponent": "golisopod", "rating": 877, "opRating": 122}, {"opponent": "virizion", "rating": 836, "opRating": 163}, {"opponent": "victree<PERSON>_shadow", "rating": 693, "opRating": 306}, {"opponent": "skeledirge", "rating": 544}, {"opponent": "<PERSON>ras", "rating": 532}], "counters": [{"opponent": "tentacruel", "rating": 298}, {"opponent": "cradily", "rating": 322}, {"opponent": "stunfisk", "rating": 334}, {"opponent": "bellibolt", "rating": 335}, {"opponent": "feraligatr", "rating": 345}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31377}, {"moveId": "FIRE_SPIN", "uses": 16611}, {"moveId": "STEEL_WING", "uses": 8051}, {"moveId": "PECK", "uses": 6947}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22634}, {"moveId": "FLY", "uses": 19291}, {"moveId": "FLAME_CHARGE", "uses": 11648}, {"moveId": "FIRE_BLAST", "uses": 5184}, {"moveId": "HURRICANE", "uses": 4083}]}, "moveset": ["INCINERATE", "FLY", "FLAME_CHARGE"], "score": 70.1}, {"speciesId": "walrein", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 703, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 857, "opRating": 142}, {"opponent": "cradily", "rating": 630}, {"opponent": "feraligatr", "rating": 545, "opRating": 455}, {"opponent": "virizion", "rating": 515, "opRating": 485}, {"opponent": "tentacruel", "rating": 505}], "counters": [{"opponent": "skeledirge", "rating": 211}, {"opponent": "forretress", "rating": 300}, {"opponent": "golisopod", "rating": 354}, {"opponent": "<PERSON>ras", "rating": 404}, {"opponent": "bellibolt", "rating": 425}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 29447}, {"moveId": "WATERFALL", "uses": 19166}, {"moveId": "FROST_BREATH", "uses": 14390}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 27038}, {"moveId": "WATER_PULSE", "uses": 11529}, {"moveId": "EARTHQUAKE", "uses": 11457}, {"moveId": "RETURN", "uses": 7884}, {"moveId": "BLIZZARD", "uses": 4998}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 70.1}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 731, "matchups": [{"opponent": "swampert", "rating": 932, "opRating": 67}, {"opponent": "<PERSON>ras", "rating": 726, "opRating": 273}, {"opponent": "virizion", "rating": 717, "opRating": 282}, {"opponent": "feraligatr", "rating": 690, "opRating": 309}, {"opponent": "bellibolt", "rating": 570}], "counters": [{"opponent": "skeledirge", "rating": 258}, {"opponent": "talonflame", "rating": 306}, {"opponent": "victree<PERSON>_shadow", "rating": 326}, {"opponent": "cradily", "rating": 416}, {"opponent": "tentacruel", "rating": 437}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 21863}, {"moveId": "BULLET_SEED", "uses": 17526}, {"moveId": "MAGICAL_LEAF", "uses": 15971}, {"moveId": "RAZOR_LEAF", "uses": 7638}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 45674}, {"moveId": "DAZZLING_GLEAM", "uses": 12487}, {"moveId": "PETAL_BLIZZARD", "uses": 4720}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 69.4}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 788, "matchups": [{"opponent": "virizion", "rating": 912, "opRating": 87}, {"opponent": "cradily", "rating": 865, "opRating": 134}, {"opponent": "golisopod", "rating": 862, "opRating": 137}, {"opponent": "victree<PERSON>_shadow", "rating": 855, "opRating": 144}, {"opponent": "bellibolt", "rating": 691, "opRating": 308}], "counters": [{"opponent": "gastrodon", "rating": 73}, {"opponent": "stunfisk", "rating": 135}, {"opponent": "jellicent", "rating": 193}, {"opponent": "<PERSON>ras", "rating": 316}, {"opponent": "tentacruel", "rating": 328}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 37822}, {"moveId": "POISON_JAB", "uses": 25178}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 26182}, {"moveId": "SLUDGE_WAVE", "uses": 14237}, {"moveId": "FIRE_BLAST", "uses": 11880}, {"moveId": "DRAGON_PULSE", "uses": 10705}]}, "moveset": ["INCINERATE", "POISON_FANG", "DRAGON_PULSE"], "score": 69.4}, {"speciesId": "venomoth", "speciesName": "Venomoth", "rating": 645, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 869, "opRating": 130}, {"opponent": "virizion", "rating": 847, "opRating": 152}, {"opponent": "golisopod", "rating": 614, "opRating": 385}, {"opponent": "tentacruel", "rating": 592, "opRating": 407}, {"opponent": "feraligatr", "rating": 550, "opRating": 449}], "counters": [{"opponent": "talonflame", "rating": 354}, {"opponent": "<PERSON>ras", "rating": 361}, {"opponent": "skeledirge", "rating": 375}, {"opponent": "bellibolt", "rating": 440}, {"opponent": "cradily", "rating": 472}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 19894}, {"moveId": "BUG_BITE", "uses": 16663}, {"moveId": "INFESTATION", "uses": 13237}, {"moveId": "CONFUSION", "uses": 13181}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 19386}, {"moveId": "SILVER_WIND", "uses": 14292}, {"moveId": "BUG_BUZZ", "uses": 13010}, {"moveId": "PSYCHIC", "uses": 9158}, {"moveId": "RETURN", "uses": 7136}]}, "moveset": ["PSYWAVE", "POISON_FANG", "SILVER_WIND"], "score": 69.4}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 721, "matchups": [{"opponent": "feraligatr", "rating": 923, "opRating": 76}, {"opponent": "tentacruel", "rating": 791, "opRating": 208}, {"opponent": "golisopod", "rating": 777, "opRating": 222}, {"opponent": "<PERSON>ras", "rating": 649, "opRating": 350}, {"opponent": "skeledirge", "rating": 536, "opRating": 463}], "counters": [{"opponent": "stunfisk", "rating": 217}, {"opponent": "virizion", "rating": 233}, {"opponent": "cradily", "rating": 272}, {"opponent": "bellibolt", "rating": 327}, {"opponent": "victree<PERSON>_shadow", "rating": 344}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 23782}, {"moveId": "MUD_SLAP", "uses": 19746}, {"moveId": "QUICK_ATTACK", "uses": 19418}], "chargedMoves": [{"moveId": "PARABOLIC_CHARGE", "uses": 21748}, {"moveId": "BREAKING_SWIPE", "uses": 18524}, {"moveId": "GRASS_KNOT", "uses": 10383}, {"moveId": "BULLDOZE", "uses": 7205}, {"moveId": "THUNDERBOLT", "uses": 5146}]}, "moveset": ["VOLT_SWITCH", "BREAKING_SWIPE", "PARABOLIC_CHARGE"], "score": 69.3}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 773, "matchups": [{"opponent": "tentacruel", "rating": 935, "opRating": 64}, {"opponent": "<PERSON>ras", "rating": 867, "opRating": 132}, {"opponent": "golisopod", "rating": 867, "opRating": 132}, {"opponent": "feraligatr", "rating": 844, "opRating": 155}, {"opponent": "cradily", "rating": 549, "opRating": 450}], "counters": [{"opponent": "gastrodon", "rating": 73}, {"opponent": "stunfisk", "rating": 100}, {"opponent": "turtonator", "rating": 126}, {"opponent": "virizion", "rating": 260}, {"opponent": "bellibolt", "rating": 267}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 23042}, {"moveId": "METAL_SOUND", "uses": 17420}, {"moveId": "SPARK", "uses": 13032}, {"moveId": "CHARGE_BEAM", "uses": 9499}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 38061}, {"moveId": "MIRROR_SHOT", "uses": 13051}, {"moveId": "FLASH_CANNON", "uses": 6962}, {"moveId": "ZAP_CANNON", "uses": 5180}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 69.3}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 755, "matchups": [{"opponent": "golisopod", "rating": 870, "opRating": 130}, {"opponent": "feraligatr", "rating": 846, "opRating": 153}, {"opponent": "victree<PERSON>_shadow", "rating": 810, "opRating": 190}, {"opponent": "talonflame", "rating": 806, "opRating": 193}, {"opponent": "tentacruel", "rating": 546, "opRating": 453}], "counters": [{"opponent": "stunfisk", "rating": 207}, {"opponent": "bellibolt", "rating": 247}, {"opponent": "cradily", "rating": 297}, {"opponent": "skeledirge", "rating": 377}, {"opponent": "<PERSON>ras", "rating": 443}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 43314}, {"moveId": "CHARGE_BEAM", "uses": 19686}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 24984}, {"moveId": "ANCIENT_POWER", "uses": 11912}, {"moveId": "THUNDERBOLT", "uses": 11430}, {"moveId": "RETURN", "uses": 5557}, {"moveId": "THUNDER", "uses": 4945}, {"moveId": "ZAP_CANNON", "uses": 4117}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 69.3}, {"speciesId": "lokix", "speciesName": "<PERSON><PERSON>", "rating": 711, "matchups": [{"opponent": "<PERSON>ras", "rating": 689, "opRating": 310}, {"opponent": "feraligatr", "rating": 657, "opRating": 342}, {"opponent": "skeledirge", "rating": 612, "opRating": 387}, {"opponent": "bellibolt", "rating": 592}, {"opponent": "victree<PERSON>_shadow", "rating": 589, "opRating": 410}], "counters": [{"opponent": "golisopod", "rating": 269}, {"opponent": "virizion", "rating": 300}, {"opponent": "typhlosion_shadow", "rating": 301}, {"opponent": "cradily", "rating": 383}, {"opponent": "tentacruel", "rating": 431}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 27879}, {"moveId": "BUG_BITE", "uses": 19449}, {"moveId": "COUNTER", "uses": 15690}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 22553}, {"moveId": "DARK_PULSE", "uses": 17872}, {"moveId": "TRAILBLAZE", "uses": 16558}, {"moveId": "BUG_BUZZ", "uses": 6005}]}, "moveset": ["SUCKER_PUNCH", "X_SCISSOR", "TRAILBLAZE"], "score": 69.2}, {"speciesId": "poliwrath_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 705, "matchups": [{"opponent": "cradily", "rating": 833}, {"opponent": "skeledirge", "rating": 745, "opRating": 254}, {"opponent": "feraligatr", "rating": 728, "opRating": 271}, {"opponent": "<PERSON>ras", "rating": 612, "opRating": 387}, {"opponent": "virizion", "rating": 596, "opRating": 403}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "victree<PERSON>_shadow", "rating": 233}, {"opponent": "jellicent", "rating": 260}, {"opponent": "tentacruel", "rating": 405}, {"opponent": "bellibolt", "rating": 485}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 19576}, {"moveId": "BUBBLE", "uses": 18781}, {"moveId": "MUD_SHOT", "uses": 18639}, {"moveId": "ROCK_SMASH", "uses": 6008}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 21440}, {"moveId": "SCALD", "uses": 13574}, {"moveId": "ICE_PUNCH", "uses": 11621}, {"moveId": "ICY_WIND", "uses": 5678}, {"moveId": "POWER_UP_PUNCH", "uses": 4704}, {"moveId": "SUBMISSION", "uses": 3133}, {"moveId": "HYDRO_PUMP", "uses": 2709}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "ICY_WIND", "SCALD"], "score": 69.2}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 737, "matchups": [{"opponent": "swampert", "rating": 928, "opRating": 71}, {"opponent": "feraligatr", "rating": 818, "opRating": 181}, {"opponent": "<PERSON>ras", "rating": 727, "opRating": 272}, {"opponent": "bellibolt", "rating": 574, "opRating": 425}, {"opponent": "cradily", "rating": 558}], "counters": [{"opponent": "talonflame", "rating": 223}, {"opponent": "typhlosion_shadow", "rating": 288}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "golisopod", "rating": 329}, {"opponent": "victree<PERSON>_shadow", "rating": 344}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 30995}, {"moveId": "LEAFAGE", "uses": 22639}, {"moveId": "RAZOR_LEAF", "uses": 9342}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26568}, {"moveId": "SUPER_POWER", "uses": 14259}, {"moveId": "X_SCISSOR", "uses": 12213}, {"moveId": "TRAILBLAZE", "uses": 6591}, {"moveId": "LEAF_STORM", "uses": 3451}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 68.9}, {"speciesId": "palkia_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 713, "matchups": [{"opponent": "skeledirge", "rating": 864, "opRating": 135}, {"opponent": "talonflame", "rating": 856, "opRating": 143}, {"opponent": "typhlosion_shadow", "rating": 825, "opRating": 174}, {"opponent": "feraligatr", "rating": 717, "opRating": 282}, {"opponent": "tentacruel", "rating": 527}], "counters": [{"opponent": "<PERSON>ras", "rating": 283}, {"opponent": "cradily", "rating": 302}, {"opponent": "virizion", "rating": 306}, {"opponent": "bellibolt", "rating": 310}, {"opponent": "victree<PERSON>_shadow", "rating": 401}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31831}, {"moveId": "DRAGON_BREATH", "uses": 31169}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 35368}, {"moveId": "DRACO_METEOR", "uses": 13501}, {"moveId": "FIRE_BLAST", "uses": 9409}, {"moveId": "HYDRO_PUMP", "uses": 4571}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "DRACO_METEOR"], "score": 68.8}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 697, "matchups": [{"opponent": "talonflame", "rating": 848, "opRating": 151}, {"opponent": "skeledirge", "rating": 837, "opRating": 162}, {"opponent": "typhlosion_shadow", "rating": 803, "opRating": 196}, {"opponent": "virizion", "rating": 792, "opRating": 207}, {"opponent": "swampert", "rating": 603, "opRating": 396}], "counters": [{"opponent": "jellicent", "rating": 288}, {"opponent": "bellibolt", "rating": 340}, {"opponent": "golisopod", "rating": 347}, {"opponent": "cradily", "rating": 388}, {"opponent": "<PERSON>ras", "rating": 404}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 9499}, {"moveId": "QUICK_ATTACK", "uses": 5783}, {"moveId": "WATER_GUN", "uses": 4472}, {"moveId": "TACKLE", "uses": 3434}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3125}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2974}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2827}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2766}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2763}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2538}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2508}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2497}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2425}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2420}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2370}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2288}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2285}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2266}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2173}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1723}], "chargedMoves": [{"moveId": "SURF", "uses": 15738}, {"moveId": "POWER_GEM", "uses": 13806}, {"moveId": "ICE_BEAM", "uses": 9785}, {"moveId": "PSYCHIC", "uses": 9358}, {"moveId": "THUNDER", "uses": 8547}, {"moveId": "HYDRO_PUMP", "uses": 3100}, {"moveId": "PSYBEAM", "uses": 2750}]}, "moveset": ["PSYWAVE", "SURF", "ICE_BEAM"], "score": 68.8}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 738, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 845, "opRating": 154}, {"opponent": "virizion", "rating": 738, "opRating": 261}, {"opponent": "golisopod", "rating": 600, "opRating": 399}, {"opponent": "bellibolt", "rating": 547}, {"opponent": "<PERSON>ras", "rating": 540, "opRating": 459}], "counters": [{"opponent": "tentacruel", "rating": 284}, {"opponent": "stunfisk", "rating": 296}, {"opponent": "jellicent", "rating": 298}, {"opponent": "feraligatr", "rating": 349}, {"opponent": "cradily", "rating": 461}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25987}, {"moveId": "FIRE_SPIN", "uses": 23496}, {"moveId": "FEINT_ATTACK", "uses": 13572}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 16989}, {"moveId": "PSYSHOCK", "uses": 13219}, {"moveId": "SCORCHING_SANDS", "uses": 10362}, {"moveId": "OVERHEAT", "uses": 6807}, {"moveId": "SOLAR_BEAM", "uses": 4761}, {"moveId": "RETURN", "uses": 3949}, {"moveId": "FLAMETHROWER", "uses": 3704}, {"moveId": "FIRE_BLAST", "uses": 1979}, {"moveId": "HEAT_WAVE", "uses": 1190}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 68.5}, {"speciesId": "serperior_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 714, "matchups": [{"opponent": "swampert", "rating": 914, "opRating": 85}, {"opponent": "feraligatr", "rating": 858, "opRating": 141}, {"opponent": "<PERSON>ras", "rating": 855, "opRating": 144}, {"opponent": "bellibolt", "rating": 702}, {"opponent": "virizion", "rating": 638, "opRating": 361}], "counters": [{"opponent": "talonflame", "rating": 205}, {"opponent": "skeledirge", "rating": 225}, {"opponent": "golisopod", "rating": 351}, {"opponent": "tentacruel", "rating": 387}, {"opponent": "cradily", "rating": 494}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51881}, {"moveId": "IRON_TAIL", "uses": 11119}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25231}, {"moveId": "AERIAL_ACE", "uses": 18075}, {"moveId": "LEAF_TORNADO", "uses": 12998}, {"moveId": "GRASS_KNOT", "uses": 6797}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 68.3}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 673, "matchups": [{"opponent": "skeledirge", "rating": 723, "opRating": 276}, {"opponent": "virizion", "rating": 723, "opRating": 276}, {"opponent": "tentacruel", "rating": 702, "opRating": 297}, {"opponent": "victree<PERSON>_shadow", "rating": 693, "opRating": 306}, {"opponent": "<PERSON>ras", "rating": 511, "opRating": 488}], "counters": [{"opponent": "talonflame", "rating": 145}, {"opponent": "drampa", "rating": 211}, {"opponent": "lickilicky", "rating": 283}, {"opponent": "cradily", "rating": 394}, {"opponent": "bellibolt", "rating": 485}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43980}, {"moveId": "RAZOR_LEAF", "uses": 19020}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 17350}, {"moveId": "FOUL_PLAY", "uses": 16824}, {"moveId": "SHADOW_BALL", "uses": 16299}, {"moveId": "FIRE_BLAST", "uses": 7347}, {"moveId": "POLTERGEIST", "uses": 5247}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 68.1}, {"speciesId": "virizion", "speciesName": "Virizion", "rating": 751, "matchups": [{"opponent": "talonflame", "rating": 718, "opRating": 281}, {"opponent": "cradily", "rating": 675}, {"opponent": "bellibolt", "rating": 630}, {"opponent": "feraligatr", "rating": 560}, {"opponent": "<PERSON>ras", "rating": 506}], "counters": [{"opponent": "armarouge", "rating": 96}, {"opponent": "skeledirge", "rating": 122}, {"opponent": "tentacruel", "rating": 272}, {"opponent": "victree<PERSON>_shadow", "rating": 404}, {"opponent": "golisopod", "rating": 457}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 33405}, {"moveId": "QUICK_ATTACK", "uses": 26754}, {"moveId": "ZEN_HEADBUTT", "uses": 2815}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 21085}, {"moveId": "SACRED_SWORD", "uses": 17179}, {"moveId": "CLOSE_COMBAT", "uses": 14410}, {"moveId": "STONE_EDGE", "uses": 10353}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "STONE_EDGE"], "score": 68.1}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 670, "matchups": [{"opponent": "jellicent", "rating": 852, "opRating": 147}, {"opponent": "swampert", "rating": 710, "opRating": 289}, {"opponent": "skeledirge", "rating": 642, "opRating": 357}, {"opponent": "golisopod", "rating": 581, "opRating": 418}, {"opponent": "feraligatr", "rating": 520, "opRating": 479}], "counters": [{"opponent": "<PERSON>ras", "rating": 255}, {"opponent": "typhlosion_shadow", "rating": 281}, {"opponent": "tentacruel", "rating": 431}, {"opponent": "bellibolt", "rating": 437}, {"opponent": "cradily", "rating": 441}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38637}, {"moveId": "FEINT_ATTACK", "uses": 24363}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 22341}, {"moveId": "SLUDGE_BOMB", "uses": 20323}, {"moveId": "FOUL_PLAY", "uses": 20216}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 67.9}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 668, "matchups": [{"opponent": "virizion", "rating": 753, "opRating": 246}, {"opponent": "skeledirge", "rating": 718, "opRating": 281}, {"opponent": "tentacruel", "rating": 689, "opRating": 310}, {"opponent": "victree<PERSON>_shadow", "rating": 680, "opRating": 319}, {"opponent": "typhlosion_shadow", "rating": 537, "opRating": 462}], "counters": [{"opponent": "talonflame", "rating": 145}, {"opponent": "drampa", "rating": 214}, {"opponent": "lickilicky", "rating": 283}, {"opponent": "cradily", "rating": 394}, {"opponent": "bellibolt", "rating": 482}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43964}, {"moveId": "RAZOR_LEAF", "uses": 19036}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 17316}, {"moveId": "FOUL_PLAY", "uses": 16819}, {"moveId": "SHADOW_BALL", "uses": 16296}, {"moveId": "FIRE_BLAST", "uses": 7343}, {"moveId": "POLTERGEIST", "uses": 5230}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 67.7}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 661, "matchups": [{"opponent": "jellicent", "rating": 660, "opRating": 339}, {"opponent": "victree<PERSON>_shadow", "rating": 626, "opRating": 373}, {"opponent": "feraligatr", "rating": 619, "opRating": 380}, {"opponent": "skeledirge", "rating": 615, "opRating": 384}, {"opponent": "stunfisk", "rating": 580, "opRating": 419}], "counters": [{"opponent": "virizion", "rating": 257}, {"opponent": "bellibolt", "rating": 332}, {"opponent": "cradily", "rating": 358}, {"opponent": "golisopod", "rating": 368}, {"opponent": "<PERSON>ras", "rating": 456}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 18390}, {"moveId": "BULLET_SEED", "uses": 17146}, {"moveId": "TACKLE", "uses": 16894}, {"moveId": "BITE", "uses": 10563}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26609}, {"moveId": "TRAILBLAZE", "uses": 18636}, {"moveId": "CRUNCH", "uses": 17797}]}, "moveset": ["MUD_SHOT", "BODY_SLAM", "CRUNCH"], "score": 67.7}, {"speciesId": "manectric_shadow", "speciesName": "Man<PERSON><PERSON> (Shadow)", "rating": 717, "matchups": [{"opponent": "<PERSON>ras", "rating": 810, "opRating": 189}, {"opponent": "tentacruel", "rating": 790, "opRating": 209}, {"opponent": "feraligatr", "rating": 790, "opRating": 209}, {"opponent": "golisopod", "rating": 748, "opRating": 251}, {"opponent": "skeledirge", "rating": 741, "opRating": 258}], "counters": [{"opponent": "gastrodon", "rating": 186}, {"opponent": "swampert", "rating": 234}, {"opponent": "virizion", "rating": 242}, {"opponent": "cradily", "rating": 311}, {"opponent": "bellibolt", "rating": 342}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27394}, {"moveId": "THUNDER_FANG", "uses": 21624}, {"moveId": "CHARGE_BEAM", "uses": 13936}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 29279}, {"moveId": "PSYCHIC_FANGS", "uses": 16789}, {"moveId": "OVERHEAT", "uses": 9272}, {"moveId": "THUNDER", "uses": 4648}, {"moveId": "FLAME_BURST", "uses": 3067}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 67.7}, {"speciesId": "scyther_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 725, "matchups": [{"opponent": "virizion", "rating": 944, "opRating": 55}, {"opponent": "golisopod", "rating": 858, "opRating": 141}, {"opponent": "cradily", "rating": 772, "opRating": 227}, {"opponent": "victree<PERSON>_shadow", "rating": 748, "opRating": 251}, {"opponent": "swampert", "rating": 627, "opRating": 372}], "counters": [{"opponent": "talonflame", "rating": 116}, {"opponent": "skeledirge", "rating": 344}, {"opponent": "<PERSON>ras", "rating": 348}, {"opponent": "tentacruel", "rating": 369}, {"opponent": "bellibolt", "rating": 420}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 34480}, {"moveId": "AIR_SLASH", "uses": 17089}, {"moveId": "STEEL_WING", "uses": 11440}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 16112}, {"moveId": "X_SCISSOR", "uses": 15450}, {"moveId": "NIGHT_SLASH", "uses": 15117}, {"moveId": "TRAILBLAZE", "uses": 12193}, {"moveId": "BUG_BUZZ", "uses": 4194}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "NIGHT_SLASH"], "score": 67.7}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 714, "matchups": [{"opponent": "typhlosion_shadow", "rating": 740, "opRating": 259}, {"opponent": "talonflame", "rating": 661, "opRating": 338}, {"opponent": "feraligatr", "rating": 631, "opRating": 368}, {"opponent": "swampert", "rating": 542, "opRating": 457}, {"opponent": "<PERSON>ras", "rating": 515, "opRating": 484}], "counters": [{"opponent": "virizion", "rating": 242}, {"opponent": "cradily", "rating": 361}, {"opponent": "bellibolt", "rating": 367}, {"opponent": "skeledirge", "rating": 391}, {"opponent": "tentacruel", "rating": 405}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 28617}, {"moveId": "COUNTER", "uses": 20302}, {"moveId": "METAL_CLAW", "uses": 14035}], "chargedMoves": [{"moveId": "SWIFT", "uses": 19982}, {"moveId": "CLOSE_COMBAT", "uses": 19635}, {"moveId": "TRAILBLAZE", "uses": 12508}, {"moveId": "PLAY_ROUGH", "uses": 4749}, {"moveId": "RETURN", "uses": 3447}, {"moveId": "HYPER_BEAM", "uses": 2731}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 67.5}, {"speciesId": "qwilfish", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 695, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 862, "opRating": 137}, {"opponent": "skeledirge", "rating": 835, "opRating": 164}, {"opponent": "talonflame", "rating": 822, "opRating": 177}, {"opponent": "virizion", "rating": 580, "opRating": 419}, {"opponent": "cradily", "rating": 573}], "counters": [{"opponent": "<PERSON>ras", "rating": 235}, {"opponent": "jellicent", "rating": 250}, {"opponent": "forretress", "rating": 285}, {"opponent": "tentacruel", "rating": 325}, {"opponent": "bellibolt", "rating": 330}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 39515}, {"moveId": "WATER_GUN", "uses": 23485}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 25445}, {"moveId": "SLUDGE_WAVE", "uses": 10591}, {"moveId": "ICE_BEAM", "uses": 10072}, {"moveId": "SCALD", "uses": 8393}, {"moveId": "FELL_STINGER", "uses": 4975}, {"moveId": "ACID_SPRAY", "uses": 3581}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "ICE_BEAM"], "score": 67.2}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 682, "matchups": [{"opponent": "feraligatr", "rating": 842, "opRating": 157}, {"opponent": "tentacruel", "rating": 811, "opRating": 188}, {"opponent": "golisopod", "rating": 811, "opRating": 188}, {"opponent": "talonflame", "rating": 734, "opRating": 265}, {"opponent": "<PERSON>ras", "rating": 583, "opRating": 416}], "counters": [{"opponent": "stunfisk", "rating": 212}, {"opponent": "victree<PERSON>_shadow", "rating": 287}, {"opponent": "virizion", "rating": 306}, {"opponent": "cradily", "rating": 322}, {"opponent": "bellibolt", "rating": 377}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 24674}, {"moveId": "ACID", "uses": 22435}, {"moveId": "SPARK", "uses": 15874}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 17650}, {"moveId": "THUNDERBOLT", "uses": 14735}, {"moveId": "CRUNCH", "uses": 14149}, {"moveId": "LIQUIDATION", "uses": 13704}, {"moveId": "ACID_SPRAY", "uses": 2830}]}, "moveset": ["VOLT_SWITCH", "DRAGON_CLAW", "THUNDERBOLT"], "score": 67.1}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 691, "matchups": [{"opponent": "skeledirge", "rating": 872, "opRating": 127}, {"opponent": "typhlosion_shadow", "rating": 870, "opRating": 129}, {"opponent": "tentacruel", "rating": 646}, {"opponent": "talonflame", "rating": 627, "opRating": 372}, {"opponent": "virizion", "rating": 502, "opRating": 497}], "counters": [{"opponent": "cradily", "rating": 227}, {"opponent": "victree<PERSON>_shadow", "rating": 332}, {"opponent": "<PERSON>ras", "rating": 357}, {"opponent": "golisopod", "rating": 407}, {"opponent": "bellibolt", "rating": 497}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 37365}, {"moveId": "WATER_GUN", "uses": 25635}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 19860}, {"moveId": "SLUDGE", "uses": 14622}, {"moveId": "EARTHQUAKE", "uses": 8332}, {"moveId": "MUDDY_WATER", "uses": 7688}, {"moveId": "SURF", "uses": 5485}, {"moveId": "RETURN", "uses": 4411}, {"moveId": "SLUDGE_WAVE", "uses": 2551}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 67.1}, {"speciesId": "venomoth_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 674, "matchups": [{"opponent": "venusaur", "rating": 882, "opRating": 117}, {"opponent": "victree<PERSON>_shadow", "rating": 843, "opRating": 156}, {"opponent": "virizion", "rating": 831, "opRating": 168}, {"opponent": "tentacruel", "rating": 560, "opRating": 439}, {"opponent": "samu<PERSON>t", "rating": 525, "opRating": 474}], "counters": [{"opponent": "forretress", "rating": 254}, {"opponent": "skeledirge", "rating": 344}, {"opponent": "bellibolt", "rating": 360}, {"opponent": "cradily", "rating": 372}, {"opponent": "<PERSON>ras", "rating": 411}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 19961}, {"moveId": "BUG_BITE", "uses": 16254}, {"moveId": "INFESTATION", "uses": 13602}, {"moveId": "CONFUSION", "uses": 13166}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 21933}, {"moveId": "SILVER_WIND", "uses": 16019}, {"moveId": "BUG_BUZZ", "uses": 14512}, {"moveId": "PSYCHIC", "uses": 10567}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYWAVE", "POISON_FANG", "SILVER_WIND"], "score": 67.1}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 681, "matchups": [{"opponent": "virizion", "rating": 892, "opRating": 107}, {"opponent": "feraligatr", "rating": 788, "opRating": 211}, {"opponent": "golisopod", "rating": 718, "opRating": 281}, {"opponent": "victree<PERSON>_shadow", "rating": 545, "opRating": 454}, {"opponent": "talonflame", "rating": 538, "opRating": 461}], "counters": [{"opponent": "cradily", "rating": 297}, {"opponent": "skeledirge", "rating": 375}, {"opponent": "bellibolt", "rating": 377}, {"opponent": "<PERSON>ras", "rating": 389}, {"opponent": "tentacruel", "rating": 443}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 19563}, {"moveId": "MUD_SLAP", "uses": 15940}, {"moveId": "BUG_BITE", "uses": 15364}, {"moveId": "SPARK", "uses": 12095}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18913}, {"moveId": "DISCHARGE", "uses": 16188}, {"moveId": "FLY", "uses": 15938}, {"moveId": "CRUNCH", "uses": 11880}]}, "moveset": ["VOLT_SWITCH", "X_SCISSOR", "FLY"], "score": 67.1}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 732, "matchups": [{"opponent": "swampert", "rating": 912, "opRating": 87}, {"opponent": "feraligatr", "rating": 856, "opRating": 143}, {"opponent": "stunfisk", "rating": 814, "opRating": 185}, {"opponent": "samu<PERSON>t", "rating": 758, "opRating": 241}, {"opponent": "bellibolt", "rating": 576, "opRating": 423}], "counters": [{"opponent": "talonflame", "rating": 190}, {"opponent": "skeledirge", "rating": 238}, {"opponent": "virizion", "rating": 318}, {"opponent": "tentacruel", "rating": 375}, {"opponent": "cradily", "rating": 433}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 34517}, {"moveId": "BULLET_SEED", "uses": 28483}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 20894}, {"moveId": "BREAKING_SWIPE", "uses": 11583}, {"moveId": "AERIAL_ACE", "uses": 9715}, {"moveId": "FRENZY_PLANT", "uses": 7701}, {"moveId": "EARTHQUAKE", "uses": 5149}, {"moveId": "DRAGON_CLAW", "uses": 4593}, {"moveId": "RETURN", "uses": 3273}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 66.9}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 718, "matchups": [{"opponent": "feraligatr", "rating": 820, "opRating": 179}, {"opponent": "<PERSON>ras", "rating": 810, "opRating": 189}, {"opponent": "tentacruel", "rating": 790, "opRating": 209}, {"opponent": "skeledirge", "rating": 771, "opRating": 228}, {"opponent": "golisopod", "rating": 748, "opRating": 251}], "counters": [{"opponent": "gastrodon", "rating": 200}, {"opponent": "swampert", "rating": 220}, {"opponent": "cradily", "rating": 280}, {"opponent": "bellibolt", "rating": 322}, {"opponent": "virizion", "rating": 418}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 9317}, {"moveId": "SNARL", "uses": 9263}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3756}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3384}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3254}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3120}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2930}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2872}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2826}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2735}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2698}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2559}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2529}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2523}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2504}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2465}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2368}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1845}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 29408}, {"moveId": "PSYCHIC_FANGS", "uses": 16654}, {"moveId": "CRUNCH", "uses": 12675}, {"moveId": "HYPER_BEAM", "uses": 4358}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 66.8}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 726, "matchups": [{"opponent": "typhlosion_shadow", "rating": 842, "opRating": 157}, {"opponent": "forretress", "rating": 811, "opRating": 188}, {"opponent": "stunfisk", "rating": 606, "opRating": 393}, {"opponent": "victree<PERSON>_shadow", "rating": 582, "opRating": 417}, {"opponent": "bellibolt", "rating": 510, "opRating": 489}], "counters": [{"opponent": "golisopod", "rating": 290}, {"opponent": "feraligatr", "rating": 301}, {"opponent": "<PERSON>ras", "rating": 303}, {"opponent": "tentacruel", "rating": 307}, {"opponent": "cradily", "rating": 397}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33694}, {"moveId": "EMBER", "uses": 29306}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23446}, {"moveId": "SCORCHING_SANDS", "uses": 20040}, {"moveId": "RETURN", "uses": 8160}, {"moveId": "FLAMETHROWER", "uses": 7291}, {"moveId": "FIRE_BLAST", "uses": 3939}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 66.8}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 690, "matchups": [{"opponent": "typhlosion_shadow", "rating": 763, "opRating": 236}, {"opponent": "talonflame", "rating": 753, "opRating": 246}, {"opponent": "turtonator", "rating": 709, "opRating": 290}, {"opponent": "stunfisk", "rating": 540, "opRating": 459}, {"opponent": "victree<PERSON>_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "virizion", "rating": 293}, {"opponent": "<PERSON>ras", "rating": 326}, {"opponent": "skeledirge", "rating": 352}, {"opponent": "tentacruel", "rating": 375}, {"opponent": "bellibolt", "rating": 387}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 46529}, {"moveId": "STRUGGLE_BUG", "uses": 16471}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 20213}, {"moveId": "ROCK_BLAST", "uses": 19531}, {"moveId": "LIQUIDATION", "uses": 13329}, {"moveId": "RETURN", "uses": 6539}, {"moveId": "WATER_PULSE", "uses": 3350}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "LIQUIDATION"], "score": 66.7}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 664, "matchups": [{"opponent": "drampa", "rating": 721, "opRating": 278}, {"opponent": "lickilicky", "rating": 671, "opRating": 328}, {"opponent": "jellicent", "rating": 621, "opRating": 378}, {"opponent": "feraligatr", "rating": 546, "opRating": 453}, {"opponent": "swampert", "rating": 503, "opRating": 496}], "counters": [{"opponent": "tentacruel", "rating": 292}, {"opponent": "golisopod", "rating": 326}, {"opponent": "virizion", "rating": 333}, {"opponent": "cradily", "rating": 358}, {"opponent": "bellibolt", "rating": 385}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 33835}, {"moveId": "TACKLE", "uses": 23247}, {"moveId": "TAKE_DOWN", "uses": 5948}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26960}, {"moveId": "BODY_SLAM", "uses": 24367}, {"moveId": "PAYBACK", "uses": 11683}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "PAYBACK"], "score": 66.5}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 658, "matchups": [{"opponent": "jellicent", "rating": 889, "opRating": 110}, {"opponent": "feraligatr", "rating": 753, "opRating": 246}, {"opponent": "tentacruel", "rating": 554}, {"opponent": "bellibolt", "rating": 536}, {"opponent": "virizion", "rating": 509, "opRating": 490}], "counters": [{"opponent": "<PERSON>ras", "rating": 222}, {"opponent": "talonflame", "rating": 285}, {"opponent": "cradily", "rating": 377}, {"opponent": "golisopod", "rating": 393}, {"opponent": "skeledirge", "rating": 430}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38465}, {"moveId": "FEINT_ATTACK", "uses": 24535}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20080}, {"moveId": "FOUL_PLAY", "uses": 17902}, {"moveId": "SLUDGE_BOMB", "uses": 17838}, {"moveId": "RETURN", "uses": 7185}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 66.3}, {"speciesId": "blastoise_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 727, "matchups": [{"opponent": "talonflame", "rating": 897, "opRating": 102}, {"opponent": "typhlosion_shadow", "rating": 870, "opRating": 129}, {"opponent": "skeledirge", "rating": 864, "opRating": 135}, {"opponent": "<PERSON>ras", "rating": 656, "opRating": 343}, {"opponent": "golisopod", "rating": 581, "opRating": 418}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 146}, {"opponent": "bellibolt", "rating": 282}, {"opponent": "cradily", "rating": 333}, {"opponent": "tentacruel", "rating": 369}, {"opponent": "virizion", "rating": 378}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 34440}, {"moveId": "WATER_GUN", "uses": 18271}, {"moveId": "BITE", "uses": 10353}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 33702}, {"moveId": "ICE_BEAM", "uses": 11945}, {"moveId": "SKULL_BASH", "uses": 9268}, {"moveId": "FLASH_CANNON", "uses": 4446}, {"moveId": "HYDRO_PUMP", "uses": 3548}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "HYDRO_CANNON", "ICE_BEAM"], "score": 66.3}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 694, "matchups": [{"opponent": "swampert", "rating": 770, "opRating": 229}, {"opponent": "feraligatr", "rating": 646, "opRating": 353}, {"opponent": "golisopod", "rating": 643, "opRating": 356}, {"opponent": "victree<PERSON>_shadow", "rating": 544, "opRating": 455}, {"opponent": "<PERSON>ras", "rating": 512, "opRating": 487}], "counters": [{"opponent": "forretress", "rating": 242}, {"opponent": "virizion", "rating": 266}, {"opponent": "skeledirge", "rating": 308}, {"opponent": "cradily", "rating": 361}, {"opponent": "bellibolt", "rating": 382}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 20114}, {"moveId": "POISON_STING", "uses": 16154}, {"moveId": "BUG_BITE", "uses": 15339}, {"moveId": "AIR_SLASH", "uses": 11474}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24076}, {"moveId": "POWER_GEM", "uses": 20540}, {"moveId": "BUG_BUZZ", "uses": 6508}, {"moveId": "FELL_STINGER", "uses": 6369}, {"moveId": "SIGNAL_BEAM", "uses": 5544}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 66.3}, {"speciesId": "king<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 602, "matchups": [{"opponent": "skeledirge", "rating": 771, "opRating": 228}, {"opponent": "talonflame", "rating": 601, "opRating": 398}, {"opponent": "cradily", "rating": 565}, {"opponent": "<PERSON>ras", "rating": 555}, {"opponent": "feraligatr", "rating": 519, "opRating": 480}], "counters": [{"opponent": "lickilicky", "rating": 307}, {"opponent": "victree<PERSON>_shadow", "rating": 344}, {"opponent": "virizion", "rating": 354}, {"opponent": "tentacruel", "rating": 369}, {"opponent": "bellibolt", "rating": 427}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 28389}, {"moveId": "WATER_GUN", "uses": 17491}, {"moveId": "WATERFALL", "uses": 17095}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 24489}, {"moveId": "OCTAZOOKA", "uses": 14090}, {"moveId": "BLIZZARD", "uses": 12524}, {"moveId": "HYDRO_PUMP", "uses": 11829}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 65.9}, {"speciesId": "bruxish", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 665, "matchups": [{"opponent": "virizion", "rating": 898, "opRating": 101}, {"opponent": "skeledirge", "rating": 838}, {"opponent": "talonflame", "rating": 838, "opRating": 161}, {"opponent": "tentacruel", "rating": 799}, {"opponent": "<PERSON>ras", "rating": 648}], "counters": [{"opponent": "forretress", "rating": 245}, {"opponent": "cradily", "rating": 316}, {"opponent": "bellibolt", "rating": 350}, {"opponent": "golisopod", "rating": 379}, {"opponent": "feraligatr", "rating": 449}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31540}, {"moveId": "WATER_GUN", "uses": 21426}, {"moveId": "BITE", "uses": 10119}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 18961}, {"moveId": "AQUA_TAIL", "uses": 17789}, {"moveId": "PSYCHIC_FANGS", "uses": 16693}, {"moveId": "CRUNCH", "uses": 9448}]}, "moveset": ["CONFUSION", "AQUA_TAIL", "PSYCHIC_FANGS"], "score": 65.6}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 719, "matchups": [{"opponent": "tentacruel", "rating": 839, "opRating": 160}, {"opponent": "feraligatr", "rating": 820, "opRating": 179}, {"opponent": "<PERSON>ras", "rating": 781, "opRating": 218}, {"opponent": "golisopod", "rating": 748, "opRating": 251}, {"opponent": "skeledirge", "rating": 722, "opRating": 277}], "counters": [{"opponent": "crustle", "rating": 200}, {"opponent": "cradily", "rating": 272}, {"opponent": "bellibolt", "rating": 320}, {"opponent": "victree<PERSON>_shadow", "rating": 359}, {"opponent": "virizion", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 26695}, {"moveId": "THUNDER_FANG", "uses": 22049}, {"moveId": "CHARGE_BEAM", "uses": 14217}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 27200}, {"moveId": "PSYCHIC_FANGS", "uses": 15196}, {"moveId": "OVERHEAT", "uses": 8434}, {"moveId": "RETURN", "uses": 5004}, {"moveId": "THUNDER", "uses": 4261}, {"moveId": "FLAME_BURST", "uses": 2815}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 65.6}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 699, "matchups": [{"opponent": "typhlosion_shadow", "rating": 803, "opRating": 196}, {"opponent": "victree<PERSON>_shadow", "rating": 778, "opRating": 221}, {"opponent": "skeledirge", "rating": 536, "opRating": 463}, {"opponent": "golisopod", "rating": 530, "opRating": 469}, {"opponent": "virizion", "rating": 515, "opRating": 484}], "counters": [{"opponent": "tentacruel", "rating": 295}, {"opponent": "<PERSON>ras", "rating": 311}, {"opponent": "cradily", "rating": 325}, {"opponent": "feraligatr", "rating": 396}, {"opponent": "bellibolt", "rating": 455}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 29182}, {"moveId": "EMBER", "uses": 17302}, {"moveId": "FIRE_FANG", "uses": 13575}, {"moveId": "TAKE_DOWN", "uses": 2872}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 20446}, {"moveId": "DARK_PULSE", "uses": 16534}, {"moveId": "OVERHEAT", "uses": 15776}, {"moveId": "SOLAR_BEAM", "uses": 10345}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "DARK_PULSE"], "score": 65.6}, {"speciesId": "tauros_blaze", "speciesName": "<PERSON><PERSON> (Blaze)", "rating": 684, "matchups": [{"opponent": "forretress", "rating": 861, "opRating": 138}, {"opponent": "victree<PERSON>_shadow", "rating": 608, "opRating": 391}, {"opponent": "bellibolt", "rating": 604, "opRating": 395}, {"opponent": "cradily", "rating": 577, "opRating": 422}, {"opponent": "virizion", "rating": 570, "opRating": 429}], "counters": [{"opponent": "swampert", "rating": 163}, {"opponent": "talonflame", "rating": 238}, {"opponent": "skeledirge", "rating": 294}, {"opponent": "tentacruel", "rating": 325}, {"opponent": "<PERSON>ras", "rating": 354}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 40988}, {"moveId": "TACKLE", "uses": 17573}, {"moveId": "ZEN_HEADBUTT", "uses": 4393}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21978}, {"moveId": "TRAILBLAZE", "uses": 21079}, {"moveId": "EARTHQUAKE", "uses": 12395}, {"moveId": "IRON_HEAD", "uses": 7500}]}, "moveset": ["DOUBLE_KICK", "FLAME_CHARGE", "TRAILBLAZE"], "score": 65.3}, {"speciesId": "reshiram", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 656, "matchups": [{"opponent": "skeledirge", "rating": 782, "opRating": 217}, {"opponent": "talonflame", "rating": 615, "opRating": 384}, {"opponent": "golisopod", "rating": 583, "opRating": 416}, {"opponent": "<PERSON>ras", "rating": 565, "opRating": 434}, {"opponent": "bellibolt", "rating": 539}], "counters": [{"opponent": "cradily", "rating": 269}, {"opponent": "stunfisk", "rating": 282}, {"opponent": "swampert", "rating": 285}, {"opponent": "virizion", "rating": 306}, {"opponent": "tentacruel", "rating": 399}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33478}, {"moveId": "FIRE_FANG", "uses": 29522}], "chargedMoves": [{"moveId": "FUSION_FLARE", "uses": 23938}, {"moveId": "STONE_EDGE", "uses": 13917}, {"moveId": "CRUNCH", "uses": 12618}, {"moveId": "DRACO_METEOR", "uses": 7729}, {"moveId": "OVERHEAT", "uses": 4965}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "OVERHEAT"], "score": 65.2}, {"speciesId": "charizard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 717, "matchups": [{"opponent": "venusaur", "rating": 887, "opRating": 112}, {"opponent": "turtonator", "rating": 781, "opRating": 218}, {"opponent": "virizion", "rating": 758, "opRating": 241}, {"opponent": "victree<PERSON>_shadow", "rating": 758, "opRating": 241}, {"opponent": "forretress", "rating": 731, "opRating": 268}], "counters": [{"opponent": "swampert", "rating": 144}, {"opponent": "jellicent", "rating": 239}, {"opponent": "feraligatr", "rating": 283}, {"opponent": "tentacruel", "rating": 322}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15872}, {"moveId": "FIRE_SPIN", "uses": 14080}, {"moveId": "WING_ATTACK", "uses": 11973}, {"moveId": "DRAGON_BREATH", "uses": 11490}, {"moveId": "AIR_SLASH", "uses": 9464}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 26783}, {"moveId": "DRAGON_CLAW", "uses": 20993}, {"moveId": "FLAMETHROWER", "uses": 6131}, {"moveId": "OVERHEAT", "uses": 5811}, {"moveId": "FIRE_BLAST", "uses": 3403}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BLAST_BURN", "DRAGON_CLAW"], "score": 65.1}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 668, "matchups": [{"opponent": "<PERSON>ras", "rating": 621, "opRating": 378}, {"opponent": "typhlosion_shadow", "rating": 569, "opRating": 430}, {"opponent": "victree<PERSON>_shadow", "rating": 557, "opRating": 442}, {"opponent": "bellibolt", "rating": 524}, {"opponent": "feraligatr", "rating": 506, "opRating": 493}], "counters": [{"opponent": "skeledirge", "rating": 194}, {"opponent": "jellicent", "rating": 316}, {"opponent": "cradily", "rating": 341}, {"opponent": "virizion", "rating": 357}, {"opponent": "tentacruel", "rating": 485}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7636}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3954}, {"moveId": "CHARGE_BEAM", "uses": 3754}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3696}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3628}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3343}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3342}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3293}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3286}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3272}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3254}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3191}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3089}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3081}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3008}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2962}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2916}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2341}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 22188}, {"moveId": "RETURN", "uses": 13962}, {"moveId": "ZAP_CANNON", "uses": 12017}, {"moveId": "SOLAR_BEAM", "uses": 9398}, {"moveId": "HYPER_BEAM", "uses": 5384}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "RETURN"], "score": 65.1}, {"speciesId": "scolipede_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 709, "matchups": [{"opponent": "virizion", "rating": 931, "opRating": 68}, {"opponent": "cradily", "rating": 856}, {"opponent": "victree<PERSON>_shadow", "rating": 852, "opRating": 147}, {"opponent": "golisopod", "rating": 571, "opRating": 428}, {"opponent": "typhlosion_shadow", "rating": 564, "opRating": 435}], "counters": [{"opponent": "skeledirge", "rating": 244}, {"opponent": "forretress", "rating": 254}, {"opponent": "<PERSON>ras", "rating": 272}, {"opponent": "bellibolt", "rating": 285}, {"opponent": "tentacruel", "rating": 378}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 23044}, {"moveId": "POISON_JAB", "uses": 21251}, {"moveId": "BUG_BITE", "uses": 18767}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 27486}, {"moveId": "SLUDGE_BOMB", "uses": 19685}, {"moveId": "MEGAHORN", "uses": 10918}, {"moveId": "GYRO_BALL", "uses": 4848}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "X_SCISSOR", "SLUDGE_BOMB"], "score": 65.1}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 666, "matchups": [{"opponent": "swampert", "rating": 799, "opRating": 200}, {"opponent": "virizion", "rating": 744, "opRating": 255}, {"opponent": "skeledirge", "rating": 710, "opRating": 289}, {"opponent": "tentacruel", "rating": 673, "opRating": 326}, {"opponent": "victree<PERSON>_shadow", "rating": 663, "opRating": 336}], "counters": [{"opponent": "talonflame", "rating": 145}, {"opponent": "drampa", "rating": 214}, {"opponent": "bellibolt", "rating": 315}, {"opponent": "golisopod", "rating": 340}, {"opponent": "cradily", "rating": 400}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43992}, {"moveId": "RAZOR_LEAF", "uses": 19008}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 17344}, {"moveId": "FOUL_PLAY", "uses": 16803}, {"moveId": "SHADOW_BALL", "uses": 16276}, {"moveId": "FIRE_BLAST", "uses": 7360}, {"moveId": "POLTERGEIST", "uses": 5233}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 64.9}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 723, "matchups": [{"opponent": "samu<PERSON>t", "rating": 861, "opRating": 138}, {"opponent": "forretress", "rating": 804, "opRating": 195}, {"opponent": "typhlosion_shadow", "rating": 783, "opRating": 216}, {"opponent": "venusaur", "rating": 606, "opRating": 393}, {"opponent": "victree<PERSON>_shadow", "rating": 549, "opRating": 450}], "counters": [{"opponent": "jellicent", "rating": 140}, {"opponent": "skeledirge", "rating": 280}, {"opponent": "tentacruel", "rating": 349}, {"opponent": "bellibolt", "rating": 390}, {"opponent": "cradily", "rating": 402}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 35678}, {"moveId": "FIRE_SPIN", "uses": 27322}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 17456}, {"moveId": "SCORCHING_SANDS", "uses": 12950}, {"moveId": "BRICK_BREAK", "uses": 9383}, {"moveId": "THUNDERBOLT", "uses": 9343}, {"moveId": "PSYCHIC", "uses": 5891}, {"moveId": "RETURN", "uses": 5044}, {"moveId": "FIRE_BLAST", "uses": 2971}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 64.9}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 648, "matchups": [{"opponent": "skeledirge", "rating": 801, "opRating": 198}, {"opponent": "jellicent", "rating": 774, "opRating": 225}, {"opponent": "typhlosion_shadow", "rating": 740, "opRating": 259}, {"opponent": "feraligatr", "rating": 731, "opRating": 268}, {"opponent": "swampert", "rating": 664, "opRating": 335}], "counters": [{"opponent": "virizion", "rating": 215}, {"opponent": "victree<PERSON>_shadow", "rating": 266}, {"opponent": "bellibolt", "rating": 355}, {"opponent": "cradily", "rating": 394}, {"opponent": "tentacruel", "rating": 396}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 25860}, {"moveId": "SAND_ATTACK", "uses": 21852}, {"moveId": "BITE", "uses": 10346}, {"moveId": "TAKE_DOWN", "uses": 4916}], "chargedMoves": [{"moveId": "SURF", "uses": 22017}, {"moveId": "GRASS_KNOT", "uses": 21700}, {"moveId": "DARK_PULSE", "uses": 19243}]}, "moveset": ["SUCKER_PUNCH", "SURF", "GRASS_KNOT"], "score": 64.8}, {"speciesId": "gyarado<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 704, "matchups": [{"opponent": "skeledirge", "rating": 812, "opRating": 187}, {"opponent": "talonflame", "rating": 812, "opRating": 187}, {"opponent": "samu<PERSON>t", "rating": 678, "opRating": 321}, {"opponent": "golisopod", "rating": 631, "opRating": 368}, {"opponent": "feraligatr", "rating": 518, "opRating": 481}], "counters": [{"opponent": "stunfisk", "rating": 179}, {"opponent": "virizion", "rating": 315}, {"opponent": "cradily", "rating": 330}, {"opponent": "bellibolt", "rating": 355}, {"opponent": "<PERSON>ras", "rating": 361}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 19046}, {"moveId": "DRAGON_TAIL", "uses": 19002}, {"moveId": "WATERFALL", "uses": 16281}, {"moveId": "BITE", "uses": 8660}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 25797}, {"moveId": "CRUNCH", "uses": 14691}, {"moveId": "OUTRAGE", "uses": 10242}, {"moveId": "TWISTER", "uses": 5502}, {"moveId": "DRAGON_PULSE", "uses": 3446}, {"moveId": "HYDRO_PUMP", "uses": 3298}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 64.7}, {"speciesId": "gren<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 674, "matchups": [{"opponent": "typhlosion_shadow", "rating": 890, "opRating": 110}, {"opponent": "skeledirge", "rating": 876, "opRating": 123}, {"opponent": "talonflame", "rating": 866, "opRating": 133}, {"opponent": "feraligatr", "rating": 696, "opRating": 303}, {"opponent": "<PERSON>ras", "rating": 596, "opRating": 403}], "counters": [{"opponent": "virizion", "rating": 166}, {"opponent": "golisopod", "rating": 234}, {"opponent": "victree<PERSON>_shadow", "rating": 314}, {"opponent": "cradily", "rating": 391}, {"opponent": "bellibolt", "rating": 432}], "moves": {"fastMoves": [{"moveId": "WATER_SHURIKEN", "uses": 29050}, {"moveId": "BUBBLE", "uses": 18740}, {"moveId": "FEINT_ATTACK", "uses": 15147}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 21596}, {"moveId": "NIGHT_SLASH", "uses": 20587}, {"moveId": "AERIAL_ACE", "uses": 12434}, {"moveId": "SURF", "uses": 5931}, {"moveId": "HYDRO_PUMP", "uses": 2337}]}, "moveset": ["WATER_SHURIKEN", "HYDRO_CANNON", "NIGHT_SLASH"], "score": 64.5}, {"speciesId": "oinkologne", "speciesName": "Oinkologne", "rating": 648, "matchups": [{"opponent": "feraligatr", "rating": 740, "opRating": 259}, {"opponent": "swampert", "rating": 708, "opRating": 291}, {"opponent": "<PERSON>ras", "rating": 642}, {"opponent": "tentacruel", "rating": 602}, {"opponent": "victree<PERSON>_shadow", "rating": 502, "opRating": 497}], "counters": [{"opponent": "skeledirge", "rating": 211}, {"opponent": "virizion", "rating": 272}, {"opponent": "bellibolt", "rating": 342}, {"opponent": "talonflame", "rating": 395}, {"opponent": "cradily", "rating": 400}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 50077}, {"moveId": "TAKE_DOWN", "uses": 12923}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29201}, {"moveId": "TRAILBLAZE", "uses": 20119}, {"moveId": "DIG", "uses": 13667}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 64.5}, {"speciesId": "centiskorch", "speciesName": "Centiskorch", "rating": 673, "matchups": [{"opponent": "venusaur", "rating": 935, "opRating": 64}, {"opponent": "forretress", "rating": 878, "opRating": 121}, {"opponent": "stunfisk", "rating": 545, "opRating": 454}, {"opponent": "victree<PERSON>_shadow", "rating": 533, "opRating": 466}, {"opponent": "virizion", "rating": 528, "opRating": 471}], "counters": [{"opponent": "tentacruel", "rating": 266}, {"opponent": "talonflame", "rating": 306}, {"opponent": "<PERSON>ras", "rating": 374}, {"opponent": "cradily", "rating": 408}, {"opponent": "bellibolt", "rating": 412}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 32844}, {"moveId": "BUG_BITE", "uses": 30156}], "chargedMoves": [{"moveId": "LUNGE", "uses": 22586}, {"moveId": "CRUNCH", "uses": 19519}, {"moveId": "BUG_BUZZ", "uses": 14581}, {"moveId": "HEAT_WAVE", "uses": 6299}]}, "moveset": ["EMBER", "LUNGE", "CRUNCH"], "score": 64.3}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 738, "matchups": [{"opponent": "feraligatr", "rating": 897, "opRating": 102}, {"opponent": "<PERSON>ras", "rating": 834, "opRating": 165}, {"opponent": "golisopod", "rating": 785, "opRating": 214}, {"opponent": "talonflame", "rating": 732, "opRating": 267}, {"opponent": "tentacruel", "rating": 700, "opRating": 299}], "counters": [{"opponent": "cradily", "rating": 186}, {"opponent": "victree<PERSON>_shadow", "rating": 290}, {"opponent": "skeledirge", "rating": 361}, {"opponent": "bellibolt", "rating": 375}, {"opponent": "virizion", "rating": 387}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 44332}, {"moveId": "TACKLE", "uses": 18668}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32298}, {"moveId": "SWIFT", "uses": 18246}, {"moveId": "ENERGY_BALL", "uses": 12503}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 64}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 647, "matchups": [{"opponent": "stunfisk", "rating": 624, "opRating": 375}, {"opponent": "cradily", "rating": 615}, {"opponent": "feraligatr", "rating": 533, "opRating": 466}, {"opponent": "venusaur", "rating": 525, "opRating": 474}, {"opponent": "blastoise", "rating": 502, "opRating": 497}], "counters": [{"opponent": "virizion", "rating": 275}, {"opponent": "skeledirge", "rating": 297}, {"opponent": "golisopod", "rating": 336}, {"opponent": "victree<PERSON>_shadow", "rating": 350}, {"opponent": "<PERSON>ras", "rating": 367}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51385}, {"moveId": "ZEN_HEADBUTT", "uses": 11615}], "chargedMoves": [{"moveId": "STOMP", "uses": 22220}, {"moveId": "MEGAHORN", "uses": 19141}, {"moveId": "EARTHQUAKE", "uses": 13977}, {"moveId": "SKULL_BASH", "uses": 7704}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 63.9}, {"speciesId": "meloetta_aria", "speciesName": "<PERSON><PERSON><PERSON> (Aria)", "rating": 657, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 811, "opRating": 188}, {"opponent": "feraligatr", "rating": 645, "opRating": 354}, {"opponent": "swampert", "rating": 605, "opRating": 394}, {"opponent": "virizion", "rating": 579, "opRating": 420}, {"opponent": "tentacruel", "rating": 572, "opRating": 427}], "counters": [{"opponent": "skeledirge", "rating": 247}, {"opponent": "bellibolt", "rating": 265}, {"opponent": "golisopod", "rating": 333}, {"opponent": "cradily", "rating": 427}, {"opponent": "<PERSON>ras", "rating": 450}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 32955}, {"moveId": "CONFUSION", "uses": 30045}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 33251}, {"moveId": "THUNDERBOLT", "uses": 11734}, {"moveId": "DAZZLING_GLEAM", "uses": 10098}, {"moveId": "HYPER_BEAM", "uses": 7912}]}, "moveset": ["QUICK_ATTACK", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 63.7}, {"speciesId": "scyther", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 680, "matchups": [{"opponent": "virizion", "rating": 868, "opRating": 131}, {"opponent": "victree<PERSON>_shadow", "rating": 803, "opRating": 196}, {"opponent": "gastrodon", "rating": 796, "opRating": 203}, {"opponent": "swampert", "rating": 713, "opRating": 286}, {"opponent": "golisopod", "rating": 617, "opRating": 382}], "counters": [{"opponent": "<PERSON>ras", "rating": 272}, {"opponent": "talonflame", "rating": 300}, {"opponent": "skeledirge", "rating": 308}, {"opponent": "bellibolt", "rating": 350}, {"opponent": "cradily", "rating": 394}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 33584}, {"moveId": "AIR_SLASH", "uses": 17454}, {"moveId": "STEEL_WING", "uses": 11994}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 14926}, {"moveId": "X_SCISSOR", "uses": 14463}, {"moveId": "NIGHT_SLASH", "uses": 14049}, {"moveId": "TRAILBLAZE", "uses": 11426}, {"moveId": "RETURN", "uses": 4269}, {"moveId": "BUG_BUZZ", "uses": 3919}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "NIGHT_SLASH"], "score": 63.5}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 753, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 820, "opRating": 179}, {"opponent": "virizion", "rating": 691, "opRating": 308}, {"opponent": "talonflame", "rating": 566, "opRating": 433}, {"opponent": "golisopod", "rating": 547, "opRating": 452}, {"opponent": "bellibolt", "rating": 515, "opRating": 484}], "counters": [{"opponent": "tentacruel", "rating": 224}, {"opponent": "swampert", "rating": 228}, {"opponent": "feraligatr", "rating": 245}, {"opponent": "skeledirge", "rating": 266}, {"opponent": "cradily", "rating": 391}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 26377}, {"moveId": "FIRE_SPIN", "uses": 23502}, {"moveId": "FEINT_ATTACK", "uses": 13175}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17988}, {"moveId": "PSYSHOCK", "uses": 14189}, {"moveId": "SCORCHING_SANDS", "uses": 11085}, {"moveId": "OVERHEAT", "uses": 7200}, {"moveId": "SOLAR_BEAM", "uses": 5080}, {"moveId": "FLAMETHROWER", "uses": 3892}, {"moveId": "FIRE_BLAST", "uses": 2091}, {"moveId": "HEAT_WAVE", "uses": 1245}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 63.3}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 710, "matchups": [{"opponent": "swampert", "rating": 935, "opRating": 64}, {"opponent": "feraligatr", "rating": 837, "opRating": 162}, {"opponent": "<PERSON>ras", "rating": 739, "opRating": 260}, {"opponent": "virizion", "rating": 705, "opRating": 294}, {"opponent": "bellibolt", "rating": 641, "opRating": 358}], "counters": [{"opponent": "skeledirge", "rating": 205}, {"opponent": "talonflame", "rating": 282}, {"opponent": "golisopod", "rating": 319}, {"opponent": "tentacruel", "rating": 346}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 21727}, {"moveId": "BULLET_SEED", "uses": 17175}, {"moveId": "MAGICAL_LEAF", "uses": 15907}, {"moveId": "RAZOR_LEAF", "uses": 8180}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 40164}, {"moveId": "DAZZLING_GLEAM", "uses": 10379}, {"moveId": "RETURN", "uses": 8318}, {"moveId": "PETAL_BLIZZARD", "uses": 4081}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 63.1}, {"speciesId": "r<PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 754, "matchups": [{"opponent": "<PERSON>ras", "rating": 876, "opRating": 123}, {"opponent": "tentacruel", "rating": 855, "opRating": 144}, {"opponent": "feraligatr", "rating": 605, "opRating": 394}, {"opponent": "golisopod", "rating": 598, "opRating": 401}, {"opponent": "cradily", "rating": 521}], "counters": [{"opponent": "skeledirge", "rating": 166}, {"opponent": "toxtricity", "rating": 174}, {"opponent": "victree<PERSON>_shadow", "rating": 182}, {"opponent": "virizion", "rating": 284}, {"opponent": "bellibolt", "rating": 350}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 22876}, {"moveId": "VOLT_SWITCH", "uses": 22085}, {"moveId": "SPARK", "uses": 11804}, {"moveId": "CHARM", "uses": 6246}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19201}, {"moveId": "THUNDER_PUNCH", "uses": 11097}, {"moveId": "TRAILBLAZE", "uses": 9214}, {"moveId": "SURF", "uses": 8407}, {"moveId": "BRICK_BREAK", "uses": 7319}, {"moveId": "SKULL_BASH", "uses": 4749}, {"moveId": "THUNDER", "uses": 2984}]}, "moveset": ["THUNDER_SHOCK", "BRICK_BREAK", "WILD_CHARGE"], "score": 63.1}, {"speciesId": "sceptile_shadow", "speciesName": "<PERSON>eptile (Shadow)", "rating": 751, "matchups": [{"opponent": "stunfisk", "rating": 912, "opRating": 87}, {"opponent": "feraligatr", "rating": 828, "opRating": 171}, {"opponent": "<PERSON>ras", "rating": 797, "opRating": 202}, {"opponent": "golisopod", "rating": 639, "opRating": 360}, {"opponent": "bellibolt", "rating": 503}], "counters": [{"opponent": "skeledirge", "rating": 108}, {"opponent": "talonflame", "rating": 116}, {"opponent": "virizion", "rating": 396}, {"opponent": "tentacruel", "rating": 420}, {"opponent": "cradily", "rating": 466}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 34359}, {"moveId": "BULLET_SEED", "uses": 28641}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 21870}, {"moveId": "BREAKING_SWIPE", "uses": 12270}, {"moveId": "AERIAL_ACE", "uses": 10481}, {"moveId": "FRENZY_PLANT", "uses": 8158}, {"moveId": "EARTHQUAKE", "uses": 5417}, {"moveId": "DRAGON_CLAW", "uses": 4832}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 63.1}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 699, "matchups": [{"opponent": "swampert", "rating": 941, "opRating": 58}, {"opponent": "feraligatr", "rating": 886, "opRating": 113}, {"opponent": "virizion", "rating": 742, "opRating": 257}, {"opponent": "<PERSON>ras", "rating": 552, "opRating": 447}, {"opponent": "bellibolt", "rating": 521, "opRating": 478}], "counters": [{"opponent": "talonflame", "rating": 184}, {"opponent": "skeledirge", "rating": 233}, {"opponent": "victree<PERSON>_shadow", "rating": 335}, {"opponent": "tentacruel", "rating": 352}, {"opponent": "cradily", "rating": 433}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51357}, {"moveId": "IRON_TAIL", "uses": 11643}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 23170}, {"moveId": "AERIAL_ACE", "uses": 16048}, {"moveId": "LEAF_TORNADO", "uses": 11971}, {"moveId": "GRASS_KNOT", "uses": 6107}, {"moveId": "RETURN", "uses": 5735}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 63.1}, {"speciesId": "pinsir_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 752, "matchups": [{"opponent": "swampert", "rating": 882, "opRating": 117}, {"opponent": "cradily", "rating": 825, "opRating": 174}, {"opponent": "feraligatr", "rating": 791, "opRating": 208}, {"opponent": "<PERSON>ras", "rating": 757, "opRating": 242}, {"opponent": "victree<PERSON>_shadow", "rating": 723, "opRating": 276}], "counters": [{"opponent": "skeledirge", "rating": 177}, {"opponent": "talonflame", "rating": 190}, {"opponent": "jellicent", "rating": 237}, {"opponent": "tentacruel", "rating": 269}, {"opponent": "bellibolt", "rating": 390}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 34481}, {"moveId": "BUG_BITE", "uses": 22719}, {"moveId": "ROCK_SMASH", "uses": 5807}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20396}, {"moveId": "X_SCISSOR", "uses": 19422}, {"moveId": "SUPER_POWER", "uses": 15174}, {"moveId": "VICE_GRIP", "uses": 5476}, {"moveId": "SUBMISSION", "uses": 2440}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 62.9}, {"speciesId": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 679, "matchups": [{"opponent": "<PERSON>ras", "rating": 775, "opRating": 224}, {"opponent": "jellicent", "rating": 745, "opRating": 254}, {"opponent": "feraligatr", "rating": 674, "opRating": 325}, {"opponent": "skeledirge", "rating": 624, "opRating": 375}, {"opponent": "cradily", "rating": 573, "opRating": 426}], "counters": [{"opponent": "golisopod", "rating": 152}, {"opponent": "victree<PERSON>_shadow", "rating": 230}, {"opponent": "tentacruel", "rating": 352}, {"opponent": "virizion", "rating": 354}, {"opponent": "bellibolt", "rating": 425}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 25894}, {"moveId": "SNARL", "uses": 20443}, {"moveId": "WATERFALL", "uses": 16631}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 16668}, {"moveId": "DARK_PULSE", "uses": 16162}, {"moveId": "RAZOR_SHELL", "uses": 15306}, {"moveId": "ICY_WIND", "uses": 14929}]}, "moveset": ["FURY_CUTTER", "ICY_WIND", "DARK_PULSE"], "score": 62.8}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 673, "matchups": [{"opponent": "tentacruel", "rating": 884, "opRating": 115}, {"opponent": "bellibolt", "rating": 827}, {"opponent": "skeledirge", "rating": 759, "opRating": 240}, {"opponent": "feraligatr", "rating": 665, "opRating": 334}, {"opponent": "<PERSON>ras", "rating": 502}], "counters": [{"opponent": "cradily", "rating": 211}, {"opponent": "victree<PERSON>_shadow", "rating": 254}, {"opponent": "golisopod", "rating": 354}, {"opponent": "virizion", "rating": 381}, {"opponent": "talonflame", "rating": 392}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 10520}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4580}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3855}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3722}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3669}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3390}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3248}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3247}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3213}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3211}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3097}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3067}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3017}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3003}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2962}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2812}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2342}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 22314}, {"moveId": "EARTH_POWER", "uses": 20613}, {"moveId": "WATER_PULSE", "uses": 12572}, {"moveId": "EARTHQUAKE", "uses": 7543}]}, "moveset": ["MUD_SLAP", "EARTH_POWER", "BODY_SLAM"], "score": 62.4}, {"speciesId": "pinsir", "speciesName": "Pinsir", "rating": 724, "matchups": [{"opponent": "cradily", "rating": 814, "opRating": 185}, {"opponent": "victree<PERSON>_shadow", "rating": 784, "opRating": 215}, {"opponent": "swampert", "rating": 590, "opRating": 409}, {"opponent": "typhlosion_shadow", "rating": 587, "opRating": 412}, {"opponent": "<PERSON>ras", "rating": 530, "opRating": 469}], "counters": [{"opponent": "skeledirge", "rating": 208}, {"opponent": "talonflame", "rating": 223}, {"opponent": "jellicent", "rating": 229}, {"opponent": "bellibolt", "rating": 327}, {"opponent": "tentacruel", "rating": 340}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 33643}, {"moveId": "BUG_BITE", "uses": 23290}, {"moveId": "ROCK_SMASH", "uses": 6110}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18808}, {"moveId": "X_SCISSOR", "uses": 17816}, {"moveId": "SUPER_POWER", "uses": 13901}, {"moveId": "RETURN", "uses": 5441}, {"moveId": "VICE_GRIP", "uses": 4871}, {"moveId": "SUBMISSION", "uses": 2244}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 61.9}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 719, "matchups": [{"opponent": "<PERSON>ras", "rating": 876, "opRating": 123}, {"opponent": "tentacruel", "rating": 855, "opRating": 144}, {"opponent": "feraligatr", "rating": 771, "opRating": 228}, {"opponent": "golisopod", "rating": 697, "opRating": 302}, {"opponent": "cradily", "rating": 503}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 197}, {"opponent": "lickilicky", "rating": 223}, {"opponent": "skeledirge", "rating": 225}, {"opponent": "virizion", "rating": 318}, {"opponent": "bellibolt", "rating": 367}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 25326}, {"moveId": "VOLT_SWITCH", "uses": 24487}, {"moveId": "SPARK", "uses": 13142}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20716}, {"moveId": "THUNDER_PUNCH", "uses": 12024}, {"moveId": "TRAILBLAZE", "uses": 9651}, {"moveId": "SURF", "uses": 9140}, {"moveId": "PSYCHIC", "uses": 7558}, {"moveId": "GRASS_KNOT", "uses": 3904}]}, "moveset": ["THUNDER_SHOCK", "TRAILBLAZE", "WILD_CHARGE"], "score": 61.8}, {"speciesId": "zekrom", "speciesName": "Zekrom", "rating": 667, "matchups": [{"opponent": "tentacruel", "rating": 760, "opRating": 239}, {"opponent": "talonflame", "rating": 644, "opRating": 355}, {"opponent": "lickilicky", "rating": 590, "opRating": 409}, {"opponent": "jellicent", "rating": 539, "opRating": 460}, {"opponent": "golisopod", "rating": 507, "opRating": 492}], "counters": [{"opponent": "<PERSON>ras", "rating": 227}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "feraligatr", "rating": 317}, {"opponent": "bellibolt", "rating": 372}, {"opponent": "cradily", "rating": 450}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 39061}, {"moveId": "CHARGE_BEAM", "uses": 23939}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 25807}, {"moveId": "OUTRAGE", "uses": 11674}, {"moveId": "CRUNCH", "uses": 11653}, {"moveId": "FUSION_BOLT", "uses": 10456}, {"moveId": "FLASH_CANNON", "uses": 3471}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "WILD_CHARGE"], "score": 61.8}, {"speciesId": "golem_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 730, "matchups": [{"opponent": "<PERSON>ras", "rating": 871, "opRating": 128}, {"opponent": "tentacruel", "rating": 851, "opRating": 148}, {"opponent": "feraligatr", "rating": 848, "opRating": 151}, {"opponent": "skeledirge", "rating": 828, "opRating": 171}, {"opponent": "golisopod", "rating": 809, "opRating": 190}], "counters": [{"opponent": "gastrodon", "rating": 73}, {"opponent": "virizion", "rating": 169}, {"opponent": "victree<PERSON>_shadow", "rating": 200}, {"opponent": "cradily", "rating": 227}, {"opponent": "bellibolt", "rating": 440}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 26972}, {"moveId": "VOLT_SWITCH", "uses": 24016}, {"moveId": "ROCK_THROW", "uses": 12084}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28971}, {"moveId": "STONE_EDGE", "uses": 17797}, {"moveId": "ROCK_BLAST", "uses": 16178}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "WILD_CHARGE", "ROCK_BLAST"], "score": 61.6}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 659, "matchups": [{"opponent": "swampert", "rating": 929, "opRating": 70}, {"opponent": "virizion", "rating": 723, "opRating": 276}, {"opponent": "skeledirge", "rating": 697, "opRating": 302}, {"opponent": "victree<PERSON>_shadow", "rating": 671, "opRating": 328}, {"opponent": "tentacruel", "rating": 511, "opRating": 488}], "counters": [{"opponent": "talonflame", "rating": 145}, {"opponent": "<PERSON>ras", "rating": 300}, {"opponent": "bellibolt", "rating": 310}, {"opponent": "golisopod", "rating": 343}, {"opponent": "cradily", "rating": 405}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 44044}, {"moveId": "RAZOR_LEAF", "uses": 18956}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 17334}, {"moveId": "FOUL_PLAY", "uses": 16833}, {"moveId": "SHADOW_BALL", "uses": 16313}, {"moveId": "FIRE_BLAST", "uses": 7376}, {"moveId": "POLTERGEIST", "uses": 5252}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 61.6}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 669, "matchups": [{"opponent": "jellicent", "rating": 751, "opRating": 248}, {"opponent": "typhlosion_shadow", "rating": 706, "opRating": 293}, {"opponent": "feraligatr", "rating": 673, "opRating": 326}, {"opponent": "cradily", "rating": 582, "opRating": 417}, {"opponent": "<PERSON>ras", "rating": 507, "opRating": 492}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 233}, {"opponent": "virizion", "rating": 324}, {"opponent": "skeledirge", "rating": 355}, {"opponent": "bellibolt", "rating": 360}, {"opponent": "tentacruel", "rating": 360}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34486}, {"moveId": "TACKLE", "uses": 21814}, {"moveId": "LOW_KICK", "uses": 6678}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 29980}, {"moveId": "STOMP", "uses": 18100}, {"moveId": "PAYBACK", "uses": 12834}, {"moveId": "DRAIN_PUNCH", "uses": 2097}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "PAYBACK"], "score": 61.5}, {"speciesId": "milotic", "speciesName": "Milo<PERSON>", "rating": 628, "matchups": [{"opponent": "skeledirge", "rating": 774, "opRating": 225}, {"opponent": "talonflame", "rating": 678, "opRating": 321}, {"opponent": "<PERSON>ras", "rating": 570}, {"opponent": "feraligatr", "rating": 564, "opRating": 435}, {"opponent": "tentacruel", "rating": 543}], "counters": [{"opponent": "zapdos", "rating": 236}, {"opponent": "victree<PERSON>_shadow", "rating": 263}, {"opponent": "forretress", "rating": 309}, {"opponent": "bellibolt", "rating": 347}, {"opponent": "cradily", "rating": 391}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 35025}, {"moveId": "WATERFALL", "uses": 27975}], "chargedMoves": [{"moveId": "SURF", "uses": 26047}, {"moveId": "WRAP", "uses": 18649}, {"moveId": "BLIZZARD", "uses": 10889}, {"moveId": "HYPER_BEAM", "uses": 7398}]}, "moveset": ["DRAGON_TAIL", "SURF", "WRAP"], "score": 61.4}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 663, "matchups": [{"opponent": "virizion", "rating": 834, "opRating": 165}, {"opponent": "victree<PERSON>_shadow", "rating": 647, "opRating": 352}, {"opponent": "golisopod", "rating": 611, "opRating": 388}, {"opponent": "samu<PERSON>t", "rating": 575, "opRating": 424}, {"opponent": "feraligatr", "rating": 546, "opRating": 453}], "counters": [{"opponent": "forretress", "rating": 217}, {"opponent": "skeledirge", "rating": 233}, {"opponent": "<PERSON>ras", "rating": 357}, {"opponent": "bellibolt", "rating": 382}, {"opponent": "tentacruel", "rating": 387}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 22469}, {"moveId": "POISON_JAB", "uses": 21081}, {"moveId": "BUG_BITE", "uses": 19452}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24301}, {"moveId": "SLUDGE_BOMB", "uses": 17052}, {"moveId": "MEGAHORN", "uses": 9689}, {"moveId": "RETURN", "uses": 7704}, {"moveId": "GYRO_BALL", "uses": 4207}]}, "moveset": ["POISON_STING", "X_SCISSOR", "SLUDGE_BOMB"], "score": 61.1}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 703, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 811, "opRating": 188}, {"opponent": "forretress", "rating": 781, "opRating": 218}, {"opponent": "virizion", "rating": 778, "opRating": 221}, {"opponent": "golisopod", "rating": 526, "opRating": 473}, {"opponent": "talonflame", "rating": 519, "opRating": 480}], "counters": [{"opponent": "feraligatr", "rating": 128}, {"opponent": "tentacruel", "rating": 292}, {"opponent": "skeledirge", "rating": 350}, {"opponent": "cradily", "rating": 388}, {"opponent": "bellibolt", "rating": 407}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15668}, {"moveId": "FIRE_SPIN", "uses": 13903}, {"moveId": "DRAGON_BREATH", "uses": 11822}, {"moveId": "WING_ATTACK", "uses": 11819}, {"moveId": "AIR_SLASH", "uses": 9665}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24277}, {"moveId": "DRAGON_CLAW", "uses": 18524}, {"moveId": "RETURN", "uses": 6361}, {"moveId": "FLAMETHROWER", "uses": 5511}, {"moveId": "OVERHEAT", "uses": 5245}, {"moveId": "FIRE_BLAST", "uses": 2917}]}, "moveset": ["EMBER", "BLAST_BURN", "DRAGON_CLAW"], "score": 61}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 693, "matchups": [{"opponent": "jellicent", "rating": 886, "opRating": 113}, {"opponent": "feraligatr", "rating": 871, "opRating": 128}, {"opponent": "bellibolt", "rating": 613, "opRating": 386}, {"opponent": "virizion", "rating": 584, "opRating": 415}, {"opponent": "tentacruel", "rating": 518, "opRating": 481}], "counters": [{"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "victree<PERSON>_shadow", "rating": 233}, {"opponent": "skeledirge", "rating": 255}, {"opponent": "talonflame", "rating": 273}, {"opponent": "cradily", "rating": 455}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 47050}, {"moveId": "RAZOR_LEAF", "uses": 15950}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30188}, {"moveId": "SLUDGE_BOMB", "uses": 17959}, {"moveId": "RETURN", "uses": 7253}, {"moveId": "PETAL_BLIZZARD", "uses": 4126}, {"moveId": "SOLAR_BEAM", "uses": 3369}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 61}, {"speciesId": "girafarig_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 604, "matchups": [{"opponent": "swampert", "rating": 853, "opRating": 146}, {"opponent": "tentacruel", "rating": 707, "opRating": 292}, {"opponent": "stunfisk", "rating": 579, "opRating": 420}, {"opponent": "<PERSON>ras", "rating": 535, "opRating": 464}, {"opponent": "feraligatr", "rating": 525, "opRating": 474}], "counters": [{"opponent": "golisopod", "rating": 234}, {"opponent": "bellibolt", "rating": 292}, {"opponent": "virizion", "rating": 336}, {"opponent": "skeledirge", "rating": 361}, {"opponent": "cradily", "rating": 466}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 24206}, {"moveId": "CONFUSION", "uses": 24057}, {"moveId": "TACKLE", "uses": 14736}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 27182}, {"moveId": "TRAILBLAZE", "uses": 16634}, {"moveId": "THUNDERBOLT", "uses": 10745}, {"moveId": "PSYCHIC", "uses": 5223}, {"moveId": "MIRROR_COAT", "uses": 3227}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DOUBLE_KICK", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 60.7}, {"speciesId": "tauros_aqua", "speciesName": "Tauros (Aqua)", "rating": 661, "matchups": [{"opponent": "typhlosion_shadow", "rating": 864, "opRating": 135}, {"opponent": "feraligatr", "rating": 658, "opRating": 341}, {"opponent": "<PERSON>ras", "rating": 550, "opRating": 449}, {"opponent": "golisopod", "rating": 550, "opRating": 449}, {"opponent": "bellibolt", "rating": 503}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 128}, {"opponent": "cradily", "rating": 327}, {"opponent": "skeledirge", "rating": 350}, {"opponent": "tentacruel", "rating": 399}, {"opponent": "virizion", "rating": 433}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 40207}, {"moveId": "TACKLE", "uses": 18271}, {"moveId": "ZEN_HEADBUTT", "uses": 4505}], "chargedMoves": [{"moveId": "AQUA_JET", "uses": 27058}, {"moveId": "TRAILBLAZE", "uses": 18828}, {"moveId": "EARTHQUAKE", "uses": 9318}, {"moveId": "IRON_HEAD", "uses": 7860}]}, "moveset": ["DOUBLE_KICK", "AQUA_JET", "TRAILBLAZE"], "score": 60.6}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 676, "matchups": [{"opponent": "tentacruel", "rating": 789, "opRating": 210}, {"opponent": "talonflame", "rating": 710, "opRating": 289}, {"opponent": "feraligatr", "rating": 631, "opRating": 368}, {"opponent": "golisopod", "rating": 628, "opRating": 371}, {"opponent": "<PERSON>ras", "rating": 585, "opRating": 414}], "counters": [{"opponent": "stunfisk", "rating": 200}, {"opponent": "bellibolt", "rating": 257}, {"opponent": "virizion", "rating": 275}, {"opponent": "cradily", "rating": 358}, {"opponent": "victree<PERSON>_shadow", "rating": 362}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 31928}, {"moveId": "VOLT_SWITCH", "uses": 31072}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32521}, {"moveId": "SHADOW_BALL", "uses": 12217}, {"moveId": "RETURN", "uses": 7217}, {"moveId": "THUNDERBOLT", "uses": 5959}, {"moveId": "THUNDER", "uses": 5146}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SHADOW_BALL"], "score": 60.4}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 603, "matchups": [{"opponent": "jellicent", "rating": 781, "opRating": 218}, {"opponent": "feraligatr", "rating": 720, "opRating": 279}, {"opponent": "stunfisk", "rating": 646, "opRating": 353}, {"opponent": "tentacruel", "rating": 542, "opRating": 457}, {"opponent": "victree<PERSON>_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "cradily", "rating": 263}, {"opponent": "virizion", "rating": 284}, {"opponent": "<PERSON>ras", "rating": 324}, {"opponent": "skeledirge", "rating": 375}, {"opponent": "bellibolt", "rating": 490}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 50533}, {"moveId": "ZEN_HEADBUTT", "uses": 10989}, {"moveId": "YAWN", "uses": 1455}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 17915}, {"moveId": "SUPER_POWER", "uses": 16421}, {"moveId": "OUTRAGE", "uses": 8219}, {"moveId": "EARTHQUAKE", "uses": 7928}, {"moveId": "HEAVY_SLAM", "uses": 5104}, {"moveId": "SKULL_BASH", "uses": 4336}, {"moveId": "HYPER_BEAM", "uses": 2933}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "EARTHQUAKE"], "score": 60.2}, {"speciesId": "gyarados", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 674, "matchups": [{"opponent": "talonflame", "rating": 846, "opRating": 153}, {"opponent": "skeledirge", "rating": 840, "opRating": 159}, {"opponent": "swampert", "rating": 740, "opRating": 259}, {"opponent": "samu<PERSON>t", "rating": 668, "opRating": 331}, {"opponent": "golisopod", "rating": 553, "opRating": 446}], "counters": [{"opponent": "stunfisk", "rating": 135}, {"opponent": "cradily", "rating": 300}, {"opponent": "victree<PERSON>_shadow", "rating": 302}, {"opponent": "bellibolt", "rating": 342}, {"opponent": "<PERSON>ras", "rating": 361}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 19287}, {"moveId": "DRAGON_TAIL", "uses": 18514}, {"moveId": "WATERFALL", "uses": 15935}, {"moveId": "BITE", "uses": 9197}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 23498}, {"moveId": "CRUNCH", "uses": 13189}, {"moveId": "OUTRAGE", "uses": 9037}, {"moveId": "RETURN", "uses": 6278}, {"moveId": "TWISTER", "uses": 4891}, {"moveId": "HYDRO_PUMP", "uses": 3019}, {"moveId": "DRAGON_PULSE", "uses": 2989}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 59.9}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 712, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 729, "opRating": 270}, {"opponent": "feraligatr", "rating": 617, "opRating": 382}, {"opponent": "bellibolt", "rating": 609, "opRating": 390}, {"opponent": "cradily", "rating": 557, "opRating": 442}, {"opponent": "<PERSON>ras", "rating": 513, "opRating": 486}], "counters": [{"opponent": "golisopod", "rating": 109}, {"opponent": "samu<PERSON>t", "rating": 205}, {"opponent": "typhlosion_shadow", "rating": 248}, {"opponent": "skeledirge", "rating": 261}, {"opponent": "tentacruel", "rating": 298}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 29502}, {"moveId": "LEAFAGE", "uses": 21934}, {"moveId": "RAZOR_LEAF", "uses": 11530}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 23741}, {"moveId": "ENERGY_BALL", "uses": 13607}, {"moveId": "OUTRAGE", "uses": 8437}, {"moveId": "ICY_WIND", "uses": 8113}, {"moveId": "RETURN", "uses": 5488}, {"moveId": "BLIZZARD", "uses": 3650}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 59.6}, {"speciesId": "golem_alolan", "speciesName": "Golem (Alolan)", "rating": 708, "matchups": [{"opponent": "feraligatr", "rating": 878, "opRating": 121}, {"opponent": "talonflame", "rating": 796, "opRating": 203}, {"opponent": "skeledirge", "rating": 720, "opRating": 279}, {"opponent": "tentacruel", "rating": 546, "opRating": 453}, {"opponent": "<PERSON>ras", "rating": 532, "opRating": 467}], "counters": [{"opponent": "gastrodon", "rating": 73}, {"opponent": "victree<PERSON>_shadow", "rating": 164}, {"opponent": "virizion", "rating": 178}, {"opponent": "cradily", "rating": 341}, {"opponent": "bellibolt", "rating": 350}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 26398}, {"moveId": "VOLT_SWITCH", "uses": 23633}, {"moveId": "ROCK_THROW", "uses": 13033}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26629}, {"moveId": "STONE_EDGE", "uses": 16195}, {"moveId": "ROCK_BLAST", "uses": 14594}, {"moveId": "RETURN", "uses": 5683}]}, "moveset": ["ROLLOUT", "ROCK_BLAST", "WILD_CHARGE"], "score": 59.6}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 652, "matchups": [{"opponent": "feraligatr", "rating": 756, "opRating": 243}, {"opponent": "tentacruel", "rating": 667, "opRating": 332}, {"opponent": "bellibolt", "rating": 639, "opRating": 360}, {"opponent": "skeledirge", "rating": 591, "opRating": 408}, {"opponent": "victree<PERSON>_shadow", "rating": 565, "opRating": 434}], "counters": [{"opponent": "virizion", "rating": 187}, {"opponent": "talonflame", "rating": 190}, {"opponent": "golisopod", "rating": 205}, {"opponent": "cradily", "rating": 336}, {"opponent": "<PERSON>ras", "rating": 363}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 51707}, {"moveId": "LOW_KICK", "uses": 11293}], "chargedMoves": [{"moveId": "STOMP", "uses": 15214}, {"moveId": "CRUNCH", "uses": 14074}, {"moveId": "BRICK_BREAK", "uses": 11001}, {"moveId": "OUTRAGE", "uses": 9253}, {"moveId": "EARTHQUAKE", "uses": 8556}, {"moveId": "POWER_UP_PUNCH", "uses": 4816}]}, "moveset": ["MUD_SLAP", "POWER_UP_PUNCH", "OUTRAGE"], "score": 59.5}, {"speciesId": "gogoat", "speciesName": "Gogoat", "rating": 661, "matchups": [{"opponent": "swampert", "rating": 948, "opRating": 51}, {"opponent": "stunfisk", "rating": 838, "opRating": 161}, {"opponent": "feraligatr", "rating": 829, "opRating": 170}, {"opponent": "<PERSON>ras", "rating": 822, "opRating": 177}, {"opponent": "bellibolt", "rating": 623, "opRating": 376}], "counters": [{"opponent": "skeledirge", "rating": 205}, {"opponent": "talonflame", "rating": 205}, {"opponent": "victree<PERSON>_shadow", "rating": 263}, {"opponent": "typhlosion_shadow", "rating": 288}, {"opponent": "cradily", "rating": 458}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 43449}, {"moveId": "ROCK_SMASH", "uses": 11860}, {"moveId": "ZEN_HEADBUTT", "uses": 7674}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42189}, {"moveId": "BRICK_BREAK", "uses": 14203}, {"moveId": "SEED_BOMB", "uses": 6613}]}, "moveset": ["VINE_WHIP", "LEAF_BLADE", "BRICK_BREAK"], "score": 59.4}, {"speciesId": "electabuzz_shadow", "speciesName": "Electabuzz (Shadow)", "rating": 706, "matchups": [{"opponent": "tentacruel", "rating": 811, "opRating": 188}, {"opponent": "feraligatr", "rating": 811, "opRating": 188}, {"opponent": "golisopod", "rating": 791, "opRating": 208}, {"opponent": "<PERSON>ras", "rating": 760, "opRating": 239}, {"opponent": "skeledirge", "rating": 636, "opRating": 363}], "counters": [{"opponent": "stunfisk", "rating": 137}, {"opponent": "cradily", "rating": 202}, {"opponent": "bellibolt", "rating": 237}, {"opponent": "victree<PERSON>_shadow", "rating": 275}, {"opponent": "virizion", "rating": 327}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 51685}, {"moveId": "LOW_KICK", "uses": 11315}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 39652}, {"moveId": "THUNDERBOLT", "uses": 12522}, {"moveId": "THUNDER", "uses": 10703}, {"moveId": "FRUSTRATION", "uses": 30}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 59.2}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 751, "matchups": [{"opponent": "tentacruel", "rating": 935, "opRating": 64}, {"opponent": "golisopod", "rating": 867, "opRating": 132}, {"opponent": "<PERSON>ras", "rating": 837, "opRating": 162}, {"opponent": "talonflame", "rating": 700, "opRating": 299}, {"opponent": "feraligatr", "rating": 636, "opRating": 363}], "counters": [{"opponent": "gastrodon", "rating": 77}, {"opponent": "stunfisk", "rating": 133}, {"opponent": "virizion", "rating": 175}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "bellibolt", "rating": 220}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 22441}, {"moveId": "METAL_SOUND", "uses": 17350}, {"moveId": "SPARK", "uses": 13352}, {"moveId": "CHARGE_BEAM", "uses": 9862}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34196}, {"moveId": "MIRROR_SHOT", "uses": 11340}, {"moveId": "RETURN", "uses": 7117}, {"moveId": "FLASH_CANNON", "uses": 5901}, {"moveId": "ZAP_CANNON", "uses": 4465}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 59.2}, {"speciesId": "girafarig", "speciesName": "Girafarig", "rating": 563, "matchups": [{"opponent": "swampert", "rating": 882, "opRating": 117}, {"opponent": "jellicent", "rating": 656, "opRating": 343}, {"opponent": "virizion", "rating": 617, "opRating": 382}, {"opponent": "<PERSON>ras", "rating": 601, "opRating": 398}, {"opponent": "feraligatr", "rating": 563, "opRating": 436}], "counters": [{"opponent": "skeledirge", "rating": 302}, {"opponent": "golisopod", "rating": 333}, {"opponent": "bellibolt", "rating": 337}, {"opponent": "tentacruel", "rating": 408}, {"opponent": "cradily", "rating": 430}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 24136}, {"moveId": "CONFUSION", "uses": 23917}, {"moveId": "TACKLE", "uses": 14901}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 23594}, {"moveId": "TRAILBLAZE", "uses": 14564}, {"moveId": "THUNDERBOLT", "uses": 9246}, {"moveId": "RETURN", "uses": 8313}, {"moveId": "PSYCHIC", "uses": 4542}, {"moveId": "MIRROR_COAT", "uses": 2790}]}, "moveset": ["DOUBLE_KICK", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 59.1}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 678, "matchups": [{"opponent": "tentacruel", "rating": 778, "opRating": 221}, {"opponent": "talonflame", "rating": 681, "opRating": 318}, {"opponent": "<PERSON>ras", "rating": 640, "opRating": 359}, {"opponent": "feraligatr", "rating": 597, "opRating": 402}, {"opponent": "golisopod", "rating": 550, "opRating": 449}], "counters": [{"opponent": "gastrodon", "rating": 169}, {"opponent": "victree<PERSON>_shadow", "rating": 269}, {"opponent": "cradily", "rating": 277}, {"opponent": "bellibolt", "rating": 305}, {"opponent": "virizion", "rating": 330}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 51669}, {"moveId": "LOW_KICK", "uses": 11331}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 31711}, {"moveId": "RETURN", "uses": 12812}, {"moveId": "THUNDERBOLT", "uses": 9875}, {"moveId": "THUNDER", "uses": 8618}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 59}, {"speciesId": "tapu_koko", "speciesName": "<PERSON><PERSON>", "rating": 653, "matchups": [{"opponent": "skeledirge", "rating": 657}, {"opponent": "tentacruel", "rating": 635}, {"opponent": "<PERSON>ras", "rating": 590}, {"opponent": "virizion", "rating": 590}, {"opponent": "golisopod", "rating": 586, "opRating": 413}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 143}, {"opponent": "typhlosion_shadow", "rating": 251}, {"opponent": "cradily", "rating": 302}, {"opponent": "gastrodon", "rating": 318}, {"opponent": "bellibolt", "rating": 375}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 36830}, {"moveId": "QUICK_ATTACK", "uses": 26170}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20336}, {"moveId": "NATURES_MADNESS", "uses": 19147}, {"moveId": "THUNDERBOLT", "uses": 12626}, {"moveId": "THUNDER", "uses": 5532}, {"moveId": "DAZZLING_GLEAM", "uses": 5390}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "NATURES_MADNESS"], "score": 59}, {"speciesId": "seaking", "speciesName": "Seaking", "rating": 666, "matchups": [{"opponent": "typhlosion_shadow", "rating": 610, "opRating": 389}, {"opponent": "swampert", "rating": 604, "opRating": 395}, {"opponent": "virizion", "rating": 601, "opRating": 398}, {"opponent": "drampa", "rating": 569, "opRating": 430}, {"opponent": "cradily", "rating": 566, "opRating": 433}], "counters": [{"opponent": "toxtricity", "rating": 132}, {"opponent": "forretress", "rating": 208}, {"opponent": "victree<PERSON>_shadow", "rating": 218}, {"opponent": "bellibolt", "rating": 355}, {"opponent": "tentacruel", "rating": 366}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29059}, {"moveId": "WATERFALL", "uses": 21647}, {"moveId": "PECK", "uses": 12291}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20268}, {"moveId": "ICY_WIND", "uses": 14748}, {"moveId": "MEGAHORN", "uses": 13499}, {"moveId": "WATER_PULSE", "uses": 9736}, {"moveId": "ICE_BEAM", "uses": 4694}]}, "moveset": ["POISON_JAB", "DRILL_RUN", "ICY_WIND"], "score": 58.8}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 602, "matchups": [{"opponent": "jellicent", "rating": 651, "opRating": 348}, {"opponent": "toxtricity", "rating": 636, "opRating": 363}, {"opponent": "skeledirge", "rating": 591, "opRating": 408}, {"opponent": "turtonator", "rating": 538, "opRating": 461}, {"opponent": "zapdos", "rating": 514, "opRating": 485}], "counters": [{"opponent": "bellibolt", "rating": 265}, {"opponent": "virizion", "rating": 266}, {"opponent": "cradily", "rating": 347}, {"opponent": "talonflame", "rating": 348}, {"opponent": "<PERSON>ras", "rating": 361}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 50158}, {"moveId": "ZEN_HEADBUTT", "uses": 10987}, {"moveId": "YAWN", "uses": 1881}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16617}, {"moveId": "SUPER_POWER", "uses": 15384}, {"moveId": "OUTRAGE", "uses": 7655}, {"moveId": "EARTHQUAKE", "uses": 7446}, {"moveId": "HEAVY_SLAM", "uses": 4751}, {"moveId": "RETURN", "uses": 4352}, {"moveId": "SKULL_BASH", "uses": 4085}, {"moveId": "HYPER_BEAM", "uses": 2695}]}, "moveset": ["LICK", "BODY_SLAM", "EARTHQUAKE"], "score": 58.8}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 595, "matchups": [{"opponent": "feraligatr", "rating": 706, "opRating": 293}, {"opponent": "gastrodon", "rating": 661, "opRating": 338}, {"opponent": "swampert", "rating": 651, "opRating": 348}, {"opponent": "jellicent", "rating": 570, "opRating": 429}, {"opponent": "stunfisk", "rating": 567, "opRating": 432}], "counters": [{"opponent": "skeledirge", "rating": 225}, {"opponent": "virizion", "rating": 321}, {"opponent": "bellibolt", "rating": 370}, {"opponent": "tentacruel", "rating": 396}, {"opponent": "cradily", "rating": 408}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 51817}, {"moveId": "ZEN_HEADBUTT", "uses": 11207}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 20908}, {"moveId": "HORN_ATTACK", "uses": 20240}, {"moveId": "EARTHQUAKE", "uses": 12747}, {"moveId": "IRON_HEAD", "uses": 9163}]}, "moveset": ["TACKLE", "TRAILBLAZE", "HORN_ATTACK"], "score": 58.8}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 678, "matchups": [{"opponent": "virizion", "rating": 810, "opRating": 189}, {"opponent": "lickilicky", "rating": 583, "opRating": 416}, {"opponent": "bellibolt", "rating": 538, "opRating": 461}, {"opponent": "typhlosion_shadow", "rating": 535, "opRating": 464}, {"opponent": "golisopod", "rating": 528, "opRating": 471}], "counters": [{"opponent": "tentacruel", "rating": 230}, {"opponent": "victree<PERSON>_shadow", "rating": 266}, {"opponent": "skeledirge", "rating": 283}, {"opponent": "<PERSON>ras", "rating": 307}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 45040}, {"moveId": "RAZOR_LEAF", "uses": 17960}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 21561}, {"moveId": "PETAL_BLIZZARD", "uses": 13530}, {"moveId": "MOONBLAST", "uses": 13163}, {"moveId": "RETURN", "uses": 9249}, {"moveId": "SOLAR_BEAM", "uses": 5543}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 58.7}, {"speciesId": "dhelmise", "speciesName": "Dhelmise", "rating": 643, "matchups": [{"opponent": "stunfisk", "rating": 692, "opRating": 307}, {"opponent": "virizion", "rating": 626, "opRating": 373}, {"opponent": "feraligatr", "rating": 583, "opRating": 416}, {"opponent": "cradily", "rating": 528}, {"opponent": "golisopod", "rating": 528, "opRating": 471}], "counters": [{"opponent": "typhlosion_shadow", "rating": 241}, {"opponent": "victree<PERSON>_shadow", "rating": 299}, {"opponent": "talonflame", "rating": 300}, {"opponent": "bellibolt", "rating": 345}, {"opponent": "tentacruel", "rating": 411}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24903}, {"moveId": "ASTONISH", "uses": 21268}, {"moveId": "METAL_SOUND", "uses": 16828}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20974}, {"moveId": "SHADOW_BALL", "uses": 18793}, {"moveId": "WRAP", "uses": 15589}, {"moveId": "HEAVY_SLAM", "uses": 7617}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "POWER_WHIP"], "score": 58.6}, {"speciesId": "kabutops", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 694, "matchups": [{"opponent": "talonflame", "rating": 889, "opRating": 110}, {"opponent": "skeledirge", "rating": 862, "opRating": 137}, {"opponent": "typhlosion_shadow", "rating": 858, "opRating": 141}, {"opponent": "<PERSON>ras", "rating": 576, "opRating": 423}, {"opponent": "tentacruel", "rating": 572, "opRating": 427}], "counters": [{"opponent": "virizion", "rating": 118}, {"opponent": "samu<PERSON>t", "rating": 182}, {"opponent": "victree<PERSON>_shadow", "rating": 230}, {"opponent": "bellibolt", "rating": 360}, {"opponent": "cradily", "rating": 363}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 25196}, {"moveId": "MUD_SHOT", "uses": 17921}, {"moveId": "WATERFALL", "uses": 14009}, {"moveId": "ROCK_SMASH", "uses": 5844}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26367}, {"moveId": "ANCIENT_POWER", "uses": 23855}, {"moveId": "WATER_PULSE", "uses": 12738}]}, "moveset": ["FURY_CUTTER", "STONE_EDGE", "ANCIENT_POWER"], "score": 58.6}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 676, "matchups": [{"opponent": "golisopod", "rating": 852, "opRating": 147}, {"opponent": "feraligatr", "rating": 816, "opRating": 183}, {"opponent": "tentacruel", "rating": 780, "opRating": 219}, {"opponent": "<PERSON>ras", "rating": 748, "opRating": 251}, {"opponent": "talonflame", "rating": 690, "opRating": 309}], "counters": [{"opponent": "cradily", "rating": 197}, {"opponent": "bellibolt", "rating": 215}, {"opponent": "virizion", "rating": 269}, {"opponent": "stunfisk", "rating": 294}, {"opponent": "victree<PERSON>_shadow", "rating": 374}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 36220}, {"moveId": "ASTONISH", "uses": 26780}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 23961}, {"moveId": "CRUNCH", "uses": 17745}, {"moveId": "BRICK_BREAK", "uses": 14756}, {"moveId": "THUNDER", "uses": 6573}]}, "moveset": ["THUNDER_SHOCK", "CRUNCH", "THUNDER"], "score": 58.4}, {"speciesId": "crawdaunt_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 639, "matchups": [{"opponent": "skeledirge", "rating": 836, "opRating": 163}, {"opponent": "talonflame", "rating": 836, "opRating": 163}, {"opponent": "typhlosion_shadow", "rating": 794, "opRating": 205}, {"opponent": "<PERSON>ras", "rating": 684, "opRating": 315}, {"opponent": "feraligatr", "rating": 631, "opRating": 368}], "counters": [{"opponent": "virizion", "rating": 169}, {"opponent": "cradily", "rating": 297}, {"opponent": "golisopod", "rating": 312}, {"opponent": "bellibolt", "rating": 342}, {"opponent": "tentacruel", "rating": 399}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37304}, {"moveId": "WATERFALL", "uses": 25696}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 24763}, {"moveId": "SCALD", "uses": 13509}, {"moveId": "RAZOR_SHELL", "uses": 10703}, {"moveId": "CRABHAMMER", "uses": 6652}, {"moveId": "VICE_GRIP", "uses": 5390}, {"moveId": "BUBBLE_BEAM", "uses": 1985}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "NIGHT_SLASH", "SCALD"], "score": 58.3}, {"speciesId": "buzzwole", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 651, "matchups": [{"opponent": "virizion", "rating": 686, "opRating": 313}, {"opponent": "cradily", "rating": 642, "opRating": 357}, {"opponent": "feraligatr", "rating": 610, "opRating": 389}, {"opponent": "<PERSON>ras", "rating": 601, "opRating": 398}, {"opponent": "victree<PERSON>_shadow", "rating": 518, "opRating": 481}], "counters": [{"opponent": "zapdos", "rating": 153}, {"opponent": "skeledirge", "rating": 169}, {"opponent": "talonflame", "rating": 244}, {"opponent": "bellibolt", "rating": 307}, {"opponent": "tentacruel", "rating": 307}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 31544}, {"moveId": "POISON_JAB", "uses": 31456}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 28934}, {"moveId": "LUNGE", "uses": 20496}, {"moveId": "POWER_UP_PUNCH", "uses": 7004}, {"moveId": "FELL_STINGER", "uses": 6564}]}, "moveset": ["COUNTER", "SUPER_POWER", "LUNGE"], "score": 57.5}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 694, "matchups": [{"opponent": "typhlosion_shadow", "rating": 719, "opRating": 280}, {"opponent": "jellicent", "rating": 687, "opRating": 312}, {"opponent": "skeledirge", "rating": 630, "opRating": 369}, {"opponent": "feraligatr", "rating": 595, "opRating": 404}, {"opponent": "swampert", "rating": 512, "opRating": 487}], "counters": [{"opponent": "samu<PERSON>t", "rating": 191}, {"opponent": "virizion", "rating": 206}, {"opponent": "bellibolt", "rating": 255}, {"opponent": "tentacruel", "rating": 340}, {"opponent": "cradily", "rating": 422}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32858}, {"moveId": "SHADOW_CLAW", "uses": 30142}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28585}, {"moveId": "NIGHT_SLASH", "uses": 23322}, {"moveId": "DIG", "uses": 11125}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 57.5}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 652, "matchups": [{"opponent": "cradily", "rating": 686}, {"opponent": "victree<PERSON>_shadow", "rating": 670, "opRating": 329}, {"opponent": "tentacruel", "rating": 598, "opRating": 401}, {"opponent": "golisopod", "rating": 566, "opRating": 433}, {"opponent": "<PERSON>ras", "rating": 503, "opRating": 496}], "counters": [{"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "bellibolt", "rating": 237}, {"opponent": "forretress", "rating": 260}, {"opponent": "feraligatr", "rating": 286}, {"opponent": "skeledirge", "rating": 422}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 21459}, {"moveId": "WATERFALL", "uses": 21421}, {"moveId": "STEEL_WING", "uses": 20121}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 26242}, {"moveId": "DRILL_PECK", "uses": 18242}, {"moveId": "BLIZZARD", "uses": 5796}, {"moveId": "RETURN", "uses": 5498}, {"moveId": "FLASH_CANNON", "uses": 4309}, {"moveId": "HYDRO_PUMP", "uses": 2746}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 57.4}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 697, "matchups": [{"opponent": "tentacruel", "rating": 796, "opRating": 203}, {"opponent": "samu<PERSON>t", "rating": 787, "opRating": 212}, {"opponent": "talonflame", "rating": 754, "opRating": 245}, {"opponent": "feraligatr", "rating": 690, "opRating": 309}, {"opponent": "golisopod", "rating": 584, "opRating": 415}], "counters": [{"opponent": "skeledirge", "rating": 75}, {"opponent": "cradily", "rating": 230}, {"opponent": "virizion", "rating": 272}, {"opponent": "bellibolt", "rating": 440}, {"opponent": "<PERSON>ras", "rating": 493}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 8462}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3869}, {"moveId": "CHARGE_BEAM", "uses": 3769}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3659}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3555}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3285}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3256}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3233}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3222}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3219}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3194}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3089}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3012}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2962}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2936}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2898}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2852}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2215}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25929}, {"moveId": "ZAP_CANNON", "uses": 13655}, {"moveId": "HYPER_BEAM", "uses": 12735}, {"moveId": "SOLAR_BEAM", "uses": 10714}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 57.4}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 704, "matchups": [{"opponent": "feraligatr", "rating": 833, "opRating": 166}, {"opponent": "victree<PERSON>_shadow", "rating": 674, "opRating": 325}, {"opponent": "bellibolt", "rating": 647, "opRating": 352}, {"opponent": "virizion", "rating": 538, "opRating": 461}, {"opponent": "cradily", "rating": 530, "opRating": 469}], "counters": [{"opponent": "golisopod", "rating": 109}, {"opponent": "skeledirge", "rating": 205}, {"opponent": "typhlosion_shadow", "rating": 218}, {"opponent": "tentacruel", "rating": 334}, {"opponent": "<PERSON>ras", "rating": 374}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 29917}, {"moveId": "LEAFAGE", "uses": 22384}, {"moveId": "RAZOR_LEAF", "uses": 10718}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 25833}, {"moveId": "ENERGY_BALL", "uses": 15045}, {"moveId": "OUTRAGE", "uses": 9331}, {"moveId": "ICY_WIND", "uses": 8742}, {"moveId": "BLIZZARD", "uses": 3931}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 57.2}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 686, "matchups": [{"opponent": "skeledirge", "rating": 758, "opRating": 241}, {"opponent": "victree<PERSON>_shadow", "rating": 641, "opRating": 358}, {"opponent": "bellibolt", "rating": 614, "opRating": 385}, {"opponent": "<PERSON>ras", "rating": 614, "opRating": 385}, {"opponent": "talonflame", "rating": 573, "opRating": 426}], "counters": [{"opponent": "swampert", "rating": 101}, {"opponent": "samu<PERSON>t", "rating": 102}, {"opponent": "toxtricity", "rating": 138}, {"opponent": "tentacruel", "rating": 340}, {"opponent": "cradily", "rating": 405}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 22126}, {"moveId": "DOUBLE_KICK", "uses": 21681}, {"moveId": "FIRE_FANG", "uses": 19198}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 17976}, {"moveId": "BLAZE_KICK", "uses": 13226}, {"moveId": "DARKEST_LARIAT", "uses": 12882}, {"moveId": "DARK_PULSE", "uses": 11822}, {"moveId": "FLAME_CHARGE", "uses": 5006}, {"moveId": "FIRE_BLAST", "uses": 2209}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 57.1}, {"speciesId": "kecleon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 549, "matchups": [{"opponent": "trevenant", "rating": 926, "opRating": 73}, {"opponent": "pinsir_shadow", "rating": 774, "opRating": 225}, {"opponent": "jellicent", "rating": 746, "opRating": 253}, {"opponent": "serperior", "rating": 538, "opRating": 461}, {"opponent": "venusaur", "rating": 517, "opRating": 482}], "counters": [{"opponent": "talonflame", "rating": 321}, {"opponent": "cradily", "rating": 338}, {"opponent": "virizion", "rating": 351}, {"opponent": "bellibolt", "rating": 382}, {"opponent": "<PERSON>ras", "rating": 391}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 41213}, {"moveId": "LICK", "uses": 21787}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 14636}, {"moveId": "AERIAL_ACE", "uses": 14219}, {"moveId": "THUNDER", "uses": 9563}, {"moveId": "ICE_BEAM", "uses": 9533}, {"moveId": "FLAMETHROWER", "uses": 9290}, {"moveId": "SHADOW_SNEAK", "uses": 5742}]}, "moveset": ["SUCKER_PUNCH", "FOUL_PLAY", "AERIAL_ACE"], "score": 57.1}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 679, "matchups": [{"opponent": "jellicent", "rating": 888, "opRating": 111}, {"opponent": "feraligatr", "rating": 872, "opRating": 127}, {"opponent": "samu<PERSON>t", "rating": 866, "opRating": 133}, {"opponent": "stunfisk", "rating": 841, "opRating": 158}, {"opponent": "bellibolt", "rating": 714, "opRating": 285}], "counters": [{"opponent": "zapdos", "rating": 93}, {"opponent": "talonflame", "rating": 166}, {"opponent": "skeledirge", "rating": 241}, {"opponent": "victree<PERSON>_shadow", "rating": 278}, {"opponent": "tentacruel", "rating": 360}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 37112}, {"moveId": "SMACK_DOWN", "uses": 19394}, {"moveId": "LOW_KICK", "uses": 6520}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 20811}, {"moveId": "SUPER_POWER", "uses": 20415}, {"moveId": "THUNDER_PUNCH", "uses": 12224}, {"moveId": "ENERGY_BALL", "uses": 3960}, {"moveId": "GYRO_BALL", "uses": 3199}, {"moveId": "SOLAR_BEAM", "uses": 2363}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SUPER_POWER"], "score": 57}, {"speciesId": "dedenne", "speciesName": "Dedenne", "rating": 653, "matchups": [{"opponent": "golisopod", "rating": 671, "opRating": 328}, {"opponent": "<PERSON>ras", "rating": 648, "opRating": 351}, {"opponent": "feraligatr", "rating": 641, "opRating": 358}, {"opponent": "tentacruel", "rating": 526, "opRating": 473}, {"opponent": "talonflame", "rating": 519, "opRating": 480}], "counters": [{"opponent": "stunfisk", "rating": 135}, {"opponent": "victree<PERSON>_shadow", "rating": 224}, {"opponent": "cradily", "rating": 236}, {"opponent": "skeledirge", "rating": 297}, {"opponent": "bellibolt", "rating": 402}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40693}, {"moveId": "TACKLE", "uses": 22307}], "chargedMoves": [{"moveId": "PARABOLIC_CHARGE", "uses": 30425}, {"moveId": "DISCHARGE", "uses": 19329}, {"moveId": "PLAY_ROUGH", "uses": 13213}]}, "moveset": ["THUNDER_SHOCK", "PARABOLIC_CHARGE", "DISCHARGE"], "score": 57}, {"speciesId": "suicune", "speciesName": "Suicune", "rating": 589, "matchups": [{"opponent": "talonflame", "rating": 693, "opRating": 306}, {"opponent": "virizion", "rating": 648, "opRating": 351}, {"opponent": "skeledirge", "rating": 634, "opRating": 365}, {"opponent": "cradily", "rating": 632, "opRating": 367}, {"opponent": "typhlosion_shadow", "rating": 615, "opRating": 384}], "counters": [{"opponent": "<PERSON>ras", "rating": 270}, {"opponent": "golisopod", "rating": 280}, {"opponent": "tentacruel", "rating": 298}, {"opponent": "feraligatr", "rating": 317}, {"opponent": "bellibolt", "rating": 362}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 5669}, {"moveId": "SNARL", "uses": 5609}, {"moveId": "EXTRASENSORY", "uses": 4408}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3603}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3529}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3385}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3294}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3012}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3003}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2999}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2976}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2886}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2883}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2881}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2796}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2778}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2711}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2667}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2076}], "chargedMoves": [{"moveId": "SCALD", "uses": 21983}, {"moveId": "ICE_BEAM", "uses": 14599}, {"moveId": "RETURN", "uses": 9637}, {"moveId": "BUBBLE_BEAM", "uses": 6606}, {"moveId": "WATER_PULSE", "uses": 5748}, {"moveId": "HYDRO_PUMP", "uses": 4435}]}, "moveset": ["ICE_FANG", "ICE_BEAM", "SCALD"], "score": 57}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 622, "matchups": [{"opponent": "<PERSON>ras", "rating": 761, "opRating": 238}, {"opponent": "lickilicky", "rating": 699, "opRating": 300}, {"opponent": "feraligatr", "rating": 606, "opRating": 393}, {"opponent": "swampert", "rating": 533, "opRating": 466}, {"opponent": "bellibolt", "rating": 508, "opRating": 491}], "counters": [{"opponent": "virizion", "rating": 139}, {"opponent": "golisopod", "rating": 248}, {"opponent": "talonflame", "rating": 300}, {"opponent": "tentacruel", "rating": 346}, {"opponent": "cradily", "rating": 497}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 35083}, {"moveId": "LICK", "uses": 27917}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25704}, {"moveId": "CROSS_CHOP", "uses": 20047}, {"moveId": "RETURN", "uses": 8146}, {"moveId": "GUNK_SHOT", "uses": 5221}, {"moveId": "HYPER_BEAM", "uses": 3232}, {"moveId": "OBSTRUCT", "uses": 638}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "CROSS_CHOP"], "score": 56.4}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 672, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 836, "opRating": 163}, {"opponent": "skeledirge", "rating": 826, "opRating": 173}, {"opponent": "typhlosion_shadow", "rating": 783, "opRating": 216}, {"opponent": "jellicent", "rating": 748, "opRating": 251}, {"opponent": "virizion", "rating": 510, "opRating": 489}], "counters": [{"opponent": "lickilicky", "rating": 171}, {"opponent": "feraligatr", "rating": 242}, {"opponent": "cradily", "rating": 305}, {"opponent": "<PERSON>ras", "rating": 311}, {"opponent": "bellibolt", "rating": 332}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 33512}, {"moveId": "EMBER", "uses": 29488}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20491}, {"moveId": "FIRE_PUNCH", "uses": 15786}, {"moveId": "NIGHT_SHADE", "uses": 12002}, {"moveId": "OVERHEAT", "uses": 9235}, {"moveId": "SHADOW_BALL", "uses": 5551}]}, "moveset": ["HEX", "FIRE_PUNCH", "SHADOW_BALL"], "score": 56.4}, {"speciesId": "ursa<PERSON>na_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 632, "matchups": [{"opponent": "zapdos", "rating": 693, "opRating": 306}, {"opponent": "stunfisk", "rating": 594, "opRating": 405}, {"opponent": "typhlosion_shadow", "rating": 591, "opRating": 408}, {"opponent": "forretress", "rating": 591, "opRating": 408}, {"opponent": "bellibolt", "rating": 560, "opRating": 439}], "counters": [{"opponent": "skeledirge", "rating": 236}, {"opponent": "victree<PERSON>_shadow", "rating": 287}, {"opponent": "cradily", "rating": 302}, {"opponent": "<PERSON>ras", "rating": 322}, {"opponent": "virizion", "rating": 327}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 48086}, {"moveId": "ROCK_SMASH", "uses": 14914}], "chargedMoves": [{"moveId": "SWIFT", "uses": 12561}, {"moveId": "TRAILBLAZE", "uses": 9103}, {"moveId": "THUNDER_PUNCH", "uses": 8714}, {"moveId": "FIRE_PUNCH", "uses": 8355}, {"moveId": "ICE_PUNCH", "uses": 8291}, {"moveId": "HIGH_HORSEPOWER", "uses": 8105}, {"moveId": "AERIAL_ACE", "uses": 7839}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "SWIFT", "ICE_PUNCH"], "score": 56.4}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 699, "matchups": [{"opponent": "feraligatr", "rating": 839, "opRating": 160}, {"opponent": "<PERSON>ras", "rating": 732, "opRating": 267}, {"opponent": "golisopod", "rating": 603, "opRating": 396}, {"opponent": "bellibolt", "rating": 591}, {"opponent": "cradily", "rating": 550}], "counters": [{"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "skeledirge", "rating": 136}, {"opponent": "victree<PERSON>_shadow", "rating": 260}, {"opponent": "talonflame", "rating": 348}, {"opponent": "tentacruel", "rating": 405}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 48032}, {"moveId": "RAZOR_LEAF", "uses": 14968}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 33650}, {"moveId": "SLUDGE_BOMB", "uses": 20919}, {"moveId": "PETAL_BLIZZARD", "uses": 4631}, {"moveId": "SOLAR_BEAM", "uses": 3891}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 56.4}, {"speciesId": "flapple", "speciesName": "Flapple", "rating": 614, "matchups": [{"opponent": "jellicent", "rating": 753, "opRating": 246}, {"opponent": "stunfisk", "rating": 700, "opRating": 299}, {"opponent": "gastrodon", "rating": 687, "opRating": 312}, {"opponent": "virizion", "rating": 611, "opRating": 388}, {"opponent": "swampert", "rating": 575, "opRating": 424}], "counters": [{"opponent": "<PERSON>ras", "rating": 227}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "golisopod", "rating": 301}, {"opponent": "feraligatr", "rating": 317}, {"opponent": "cradily", "rating": 377}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33904}, {"moveId": "BULLET_SEED", "uses": 29096}], "chargedMoves": [{"moveId": "FLY", "uses": 23650}, {"moveId": "SEED_BOMB", "uses": 18005}, {"moveId": "OUTRAGE", "uses": 16041}, {"moveId": "DRAGON_PULSE", "uses": 5266}]}, "moveset": ["DRAGON_BREATH", "FLY", "SEED_BOMB"], "score": 56.3}, {"speciesId": "pidgeot_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 680, "matchups": [{"opponent": "virizion", "rating": 860, "opRating": 139}, {"opponent": "victree<PERSON>_shadow", "rating": 767, "opRating": 232}, {"opponent": "golisopod", "rating": 670, "opRating": 329}, {"opponent": "feraligatr", "rating": 647, "opRating": 352}, {"opponent": "tentacruel", "rating": 548, "opRating": 451}], "counters": [{"opponent": "bellibolt", "rating": 210}, {"opponent": "stunfisk", "rating": 226}, {"opponent": "typhlosion_shadow", "rating": 268}, {"opponent": "cradily", "rating": 286}, {"opponent": "<PERSON>ras", "rating": 303}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 20521}, {"moveId": "WING_ATTACK", "uses": 18533}, {"moveId": "AIR_SLASH", "uses": 13813}, {"moveId": "STEEL_WING", "uses": 10087}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 25232}, {"moveId": "AIR_CUTTER", "uses": 21671}, {"moveId": "AERIAL_ACE", "uses": 8214}, {"moveId": "HURRICANE", "uses": 4411}, {"moveId": "FEATHER_DANCE", "uses": 3150}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["GUST", "BRAVE_BIRD", "AIR_CUTTER"], "score": 56.3}, {"speciesId": "urshifu_rapid_strike", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Rapid Strike)", "rating": 614, "matchups": [{"opponent": "cradily", "rating": 791}, {"opponent": "talonflame", "rating": 791, "opRating": 208}, {"opponent": "samu<PERSON>t", "rating": 667, "opRating": 332}, {"opponent": "feraligatr", "rating": 648, "opRating": 351}, {"opponent": "<PERSON>ras", "rating": 518, "opRating": 481}], "counters": [{"opponent": "zapdos", "rating": 183}, {"opponent": "victree<PERSON>_shadow", "rating": 191}, {"opponent": "jellicent", "rating": 214}, {"opponent": "tentacruel", "rating": 292}, {"opponent": "bellibolt", "rating": 435}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 32286}, {"moveId": "WATERFALL", "uses": 21449}, {"moveId": "ROCK_SMASH", "uses": 9286}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24111}, {"moveId": "AQUA_JET", "uses": 19221}, {"moveId": "BRICK_BREAK", "uses": 9833}, {"moveId": "DYNAMIC_PUNCH", "uses": 9830}]}, "moveset": ["COUNTER", "AQUA_JET", "DYNAMIC_PUNCH"], "score": 56.2}, {"speciesId": "vaporeon", "speciesName": "Vaporeon", "rating": 607, "matchups": [{"opponent": "skeledirge", "rating": 883, "opRating": 116}, {"opponent": "talonflame", "rating": 843, "opRating": 156}, {"opponent": "typhlosion_shadow", "rating": 606, "opRating": 393}, {"opponent": "swampert", "rating": 585, "opRating": 414}, {"opponent": "feraligatr", "rating": 540, "opRating": 459}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "bellibolt", "rating": 302}, {"opponent": "<PERSON>ras", "rating": 305}, {"opponent": "virizion", "rating": 360}, {"opponent": "tentacruel", "rating": 434}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 63000}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 26450}, {"moveId": "LAST_RESORT", "uses": 10795}, {"moveId": "LIQUIDATION", "uses": 8934}, {"moveId": "SCALD", "uses": 8737}, {"moveId": "WATER_PULSE", "uses": 4499}, {"moveId": "HYDRO_PUMP", "uses": 3531}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "LAST_RESORT"], "score": 56.2}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 687, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 773, "opRating": 226}, {"opponent": "cradily", "rating": 682}, {"opponent": "tentacruel", "rating": 642, "opRating": 357}, {"opponent": "<PERSON>ras", "rating": 558, "opRating": 441}, {"opponent": "golisopod", "rating": 554, "opRating": 445}], "counters": [{"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "talonflame", "rating": 244}, {"opponent": "skeledirge", "rating": 294}, {"opponent": "bellibolt", "rating": 317}, {"opponent": "virizion", "rating": 406}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35738}, {"moveId": "BULLET_PUNCH", "uses": 27262}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18701}, {"moveId": "NIGHT_SLASH", "uses": 17876}, {"moveId": "TRAILBLAZE", "uses": 13764}, {"moveId": "IRON_HEAD", "uses": 7120}, {"moveId": "RETURN", "uses": 5629}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "TRAILBLAZE"], "score": 55.9}, {"speciesId": "whiscash_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 645, "matchups": [{"opponent": "skeledirge", "rating": 867, "opRating": 132}, {"opponent": "typhlosion_shadow", "rating": 865, "opRating": 134}, {"opponent": "bellibolt", "rating": 737}, {"opponent": "tentacruel", "rating": 676, "opRating": 323}, {"opponent": "feraligatr", "rating": 576, "opRating": 423}], "counters": [{"opponent": "virizion", "rating": 212}, {"opponent": "cradily", "rating": 244}, {"opponent": "golisopod", "rating": 255}, {"opponent": "victree<PERSON>_shadow", "rating": 377}, {"opponent": "<PERSON>ras", "rating": 378}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36690}, {"moveId": "WATER_GUN", "uses": 26310}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 28042}, {"moveId": "SCALD", "uses": 19360}, {"moveId": "BLIZZARD", "uses": 10457}, {"moveId": "WATER_PULSE", "uses": 5045}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 55.9}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 595, "matchups": [{"opponent": "talonflame", "rating": 714, "opRating": 285}, {"opponent": "<PERSON>ras", "rating": 605, "opRating": 394}, {"opponent": "feraligatr", "rating": 605, "opRating": 394}, {"opponent": "golisopod", "rating": 545, "opRating": 454}, {"opponent": "tentacruel", "rating": 538, "opRating": 461}], "counters": [{"opponent": "virizion", "rating": 184}, {"opponent": "stunfisk", "rating": 238}, {"opponent": "victree<PERSON>_shadow", "rating": 290}, {"opponent": "bellibolt", "rating": 305}, {"opponent": "cradily", "rating": 341}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 28689}, {"moveId": "SPARK", "uses": 19103}, {"moveId": "TACKLE", "uses": 15197}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 22006}, {"moveId": "FOUL_PLAY", "uses": 20513}, {"moveId": "RETURN", "uses": 8794}, {"moveId": "THUNDERBOLT", "uses": 8175}, {"moveId": "HYPER_BEAM", "uses": 3420}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "FOUL_PLAY"], "score": 55.8}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 568, "matchups": [{"opponent": "drampa", "rating": 701, "opRating": 298}, {"opponent": "forretress", "rating": 691, "opRating": 308}, {"opponent": "stunfisk", "rating": 604, "opRating": 395}, {"opponent": "cradily", "rating": 600, "opRating": 399}, {"opponent": "lickilicky", "rating": 523, "opRating": 476}], "counters": [{"opponent": "skeledirge", "rating": 244}, {"opponent": "virizion", "rating": 312}, {"opponent": "tentacruel", "rating": 349}, {"opponent": "<PERSON>ras", "rating": 357}, {"opponent": "bellibolt", "rating": 375}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 49157}, {"moveId": "LOW_KICK", "uses": 8308}, {"moveId": "POUND", "uses": 5571}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 19916}, {"moveId": "FIRE_PUNCH", "uses": 19844}, {"moveId": "FOCUS_BLAST", "uses": 12330}, {"moveId": "HYPER_BEAM", "uses": 10909}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FIRE_PUNCH"], "score": 55.7}, {"speciesId": "politoed", "speciesName": "Politoed", "rating": 638, "matchups": [{"opponent": "talonflame", "rating": 659, "opRating": 340}, {"opponent": "typhlosion_shadow", "rating": 631, "opRating": 368}, {"opponent": "skeledirge", "rating": 620, "opRating": 379}, {"opponent": "swampert", "rating": 546, "opRating": 453}, {"opponent": "cradily", "rating": 513}], "counters": [{"opponent": "serperior", "rating": 82}, {"opponent": "golisopod", "rating": 223}, {"opponent": "victree<PERSON>_shadow", "rating": 281}, {"opponent": "<PERSON>ras", "rating": 378}, {"opponent": "bellibolt", "rating": 422}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31751}, {"moveId": "MUD_SHOT", "uses": 31249}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 20422}, {"moveId": "ICE_BEAM", "uses": 9449}, {"moveId": "EARTHQUAKE", "uses": 7597}, {"moveId": "SURF", "uses": 6894}, {"moveId": "SCALD", "uses": 6676}, {"moveId": "RETURN", "uses": 6023}, {"moveId": "BLIZZARD", "uses": 3285}, {"moveId": "HYDRO_PUMP", "uses": 2630}]}, "moveset": ["MUD_SHOT", "SURF", "BLIZZARD"], "score": 55.7}, {"speciesId": "suicune_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 562, "matchups": [{"opponent": "talonflame", "rating": 627, "opRating": 372}, {"opponent": "skeledirge", "rating": 601, "opRating": 398}, {"opponent": "virizion", "rating": 573, "opRating": 426}, {"opponent": "cradily", "rating": 548}, {"opponent": "typhlosion_shadow", "rating": 522, "opRating": 477}], "counters": [{"opponent": "tentacruel", "rating": 292}, {"opponent": "<PERSON>ras", "rating": 350}, {"opponent": "golisopod", "rating": 358}, {"opponent": "victree<PERSON>_shadow", "rating": 380}, {"opponent": "bellibolt", "rating": 405}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 5824}, {"moveId": "ICE_FANG", "uses": 5682}, {"moveId": "EXTRASENSORY", "uses": 4386}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3630}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3526}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3393}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3282}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3068}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3005}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2984}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2948}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2899}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2867}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2855}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2779}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2768}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2688}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2645}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2080}], "chargedMoves": [{"moveId": "SCALD", "uses": 25962}, {"moveId": "ICE_BEAM", "uses": 17350}, {"moveId": "BUBBLE_BEAM", "uses": 7689}, {"moveId": "WATER_PULSE", "uses": 6846}, {"moveId": "HYDRO_PUMP", "uses": 5157}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_FANG", "SCALD", "ICE_BEAM"], "score": 55.5}, {"speciesId": "wailord", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 651, "matchups": [{"opponent": "talonflame", "rating": 903, "opRating": 96}, {"opponent": "typhlosion_shadow", "rating": 878, "opRating": 121}, {"opponent": "turtonator", "rating": 611, "opRating": 388}, {"opponent": "skeledirge", "rating": 579, "opRating": 420}, {"opponent": "swampert", "rating": 536, "opRating": 463}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 128}, {"opponent": "bellibolt", "rating": 247}, {"opponent": "tentacruel", "rating": 340}, {"opponent": "<PERSON>ras", "rating": 396}, {"opponent": "cradily", "rating": 427}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 35301}, {"moveId": "WATER_GUN", "uses": 22208}, {"moveId": "ZEN_HEADBUTT", "uses": 5529}], "chargedMoves": [{"moveId": "SURF", "uses": 24821}, {"moveId": "SCALD", "uses": 12343}, {"moveId": "BLIZZARD", "uses": 11461}, {"moveId": "RETURN", "uses": 10361}, {"moveId": "HYPER_BEAM", "uses": 3996}]}, "moveset": ["ROLLOUT", "SURF", "BLIZZARD"], "score": 55.5}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 700, "matchups": [{"opponent": "feraligatr", "rating": 855, "opRating": 144}, {"opponent": "golisopod", "rating": 816, "opRating": 183}, {"opponent": "tentacruel", "rating": 806, "opRating": 193}, {"opponent": "<PERSON>ras", "rating": 753, "opRating": 246}, {"opponent": "skeledirge", "rating": 644, "opRating": 355}], "counters": [{"opponent": "stunfisk", "rating": 98}, {"opponent": "swampert", "rating": 189}, {"opponent": "cradily", "rating": 225}, {"opponent": "victree<PERSON>_shadow", "rating": 257}, {"opponent": "bellibolt", "rating": 267}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 56059}, {"moveId": "LOW_KICK", "uses": 6941}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22148}, {"moveId": "THUNDER_PUNCH", "uses": 12820}, {"moveId": "ICE_PUNCH", "uses": 12487}, {"moveId": "FLAMETHROWER", "uses": 7667}, {"moveId": "RETURN", "uses": 4255}, {"moveId": "THUNDER", "uses": 3424}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 55.4}, {"speciesId": "electrode_shadow", "speciesName": "Electrode (Shadow)", "rating": 613, "matchups": [{"opponent": "feraligatr", "rating": 855, "opRating": 144}, {"opponent": "golisopod", "rating": 816, "opRating": 183}, {"opponent": "talonflame", "rating": 785, "opRating": 214}, {"opponent": "typhlosion_shadow", "rating": 644, "opRating": 355}, {"opponent": "<PERSON>ras", "rating": 566, "opRating": 433}], "counters": [{"opponent": "cradily", "rating": 205}, {"opponent": "virizion", "rating": 218}, {"opponent": "swampert", "rating": 265}, {"opponent": "bellibolt", "rating": 337}, {"opponent": "victree<PERSON>_shadow", "rating": 350}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 29111}, {"moveId": "SPARK", "uses": 18900}, {"moveId": "TACKLE", "uses": 14975}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 23850}, {"moveId": "FOUL_PLAY", "uses": 22558}, {"moveId": "THUNDERBOLT", "uses": 8908}, {"moveId": "HYPER_BEAM", "uses": 7761}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "FOUL_PLAY"], "score": 55.4}, {"speciesId": "keldeo_ordinary", "speciesName": "<PERSON><PERSON><PERSON> (Ordinary)", "rating": 658, "matchups": [{"opponent": "golisopod", "rating": 648, "opRating": 351}, {"opponent": "samu<PERSON>t", "rating": 644, "opRating": 355}, {"opponent": "swampert", "rating": 631, "opRating": 368}, {"opponent": "feraligatr", "rating": 610, "opRating": 389}, {"opponent": "virizion", "rating": 603, "opRating": 396}], "counters": [{"opponent": "skeledirge", "rating": 200}, {"opponent": "typhlosion_shadow", "rating": 235}, {"opponent": "bellibolt", "rating": 285}, {"opponent": "tentacruel", "rating": 289}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 52747}, {"moveId": "LOW_KICK", "uses": 10253}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 18802}, {"moveId": "CLOSE_COMBAT", "uses": 16622}, {"moveId": "AQUA_JET", "uses": 15503}, {"moveId": "X_SCISSOR", "uses": 9901}, {"moveId": "HYDRO_PUMP", "uses": 2232}]}, "moveset": ["POISON_JAB", "HYDRO_PUMP", "SACRED_SWORD"], "score": 55.4}, {"speciesId": "obstagoon_shadow", "speciesName": "Obstagoon (Shadow)", "rating": 648, "matchups": [{"opponent": "<PERSON>ras", "rating": 733, "opRating": 266}, {"opponent": "lickilicky", "rating": 651, "opRating": 348}, {"opponent": "cradily", "rating": 637}, {"opponent": "jellicent", "rating": 587, "opRating": 412}, {"opponent": "feraligatr", "rating": 502, "opRating": 497}], "counters": [{"opponent": "golisopod", "rating": 145}, {"opponent": "virizion", "rating": 166}, {"opponent": "talonflame", "rating": 276}, {"opponent": "skeledirge", "rating": 341}, {"opponent": "bellibolt", "rating": 452}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 35656}, {"moveId": "LICK", "uses": 27344}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 27861}, {"moveId": "CROSS_CHOP", "uses": 21618}, {"moveId": "HYPER_BEAM", "uses": 7073}, {"moveId": "GUNK_SHOT", "uses": 5828}, {"moveId": "OBSTRUCT", "uses": 684}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "NIGHT_SLASH"], "score": 55.4}, {"speciesId": "lanturn", "speciesName": "Lanturn", "rating": 621, "matchups": [{"opponent": "feraligatr", "rating": 767, "opRating": 232}, {"opponent": "talonflame", "rating": 755, "opRating": 244}, {"opponent": "skeledirge", "rating": 618, "opRating": 381}, {"opponent": "tentacruel", "rating": 582, "opRating": 417}, {"opponent": "<PERSON>ras", "rating": 559, "opRating": 440}], "counters": [{"opponent": "bellibolt", "rating": 215}, {"opponent": "gastrodon", "rating": 231}, {"opponent": "virizion", "rating": 281}, {"opponent": "victree<PERSON>_shadow", "rating": 293}, {"opponent": "cradily", "rating": 358}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 23177}, {"moveId": "SPARK", "uses": 22997}, {"moveId": "CHARGE_BEAM", "uses": 16807}], "chargedMoves": [{"moveId": "SURF", "uses": 27944}, {"moveId": "THUNDERBOLT", "uses": 20578}, {"moveId": "THUNDER", "uses": 8881}, {"moveId": "HYDRO_PUMP", "uses": 5349}]}, "moveset": ["SPARK", "SURF", "THUNDERBOLT"], "score": 55.1}, {"speciesId": "pelipper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 656, "matchups": [{"opponent": "typhlosion_shadow", "rating": 813, "opRating": 186}, {"opponent": "virizion", "rating": 788, "opRating": 211}, {"opponent": "skeledirge", "rating": 700, "opRating": 299}, {"opponent": "golisopod", "rating": 690, "opRating": 309}, {"opponent": "feraligatr", "rating": 658, "opRating": 341}], "counters": [{"opponent": "tentacruel", "rating": 180}, {"opponent": "bellibolt", "rating": 190}, {"opponent": "stunfisk", "rating": 224}, {"opponent": "cradily", "rating": 305}, {"opponent": "<PERSON>ras", "rating": 406}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 35017}, {"moveId": "WATER_GUN", "uses": 27983}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 32183}, {"moveId": "HURRICANE", "uses": 17503}, {"moveId": "BLIZZARD", "uses": 9122}, {"moveId": "HYDRO_PUMP", "uses": 4243}]}, "moveset": ["WING_ATTACK", "WEATHER_BALL_WATER", "HURRICANE"], "score": 54.9}, {"speciesId": "relicanth", "speciesName": "Relicanth", "rating": 612, "matchups": [{"opponent": "talonflame", "rating": 935, "opRating": 64}, {"opponent": "skeledirge", "rating": 871, "opRating": 128}, {"opponent": "typhlosion_shadow", "rating": 726, "opRating": 273}, {"opponent": "stunfisk", "rating": 548, "opRating": 451}, {"opponent": "feraligatr", "rating": 507, "opRating": 492}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 116}, {"opponent": "serperior", "rating": 144}, {"opponent": "virizion", "rating": 236}, {"opponent": "bellibolt", "rating": 377}, {"opponent": "cradily", "rating": 397}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51758}, {"moveId": "ZEN_HEADBUTT", "uses": 11242}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 32600}, {"moveId": "ANCIENT_POWER", "uses": 26135}, {"moveId": "HYDRO_PUMP", "uses": 4259}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "ANCIENT_POWER"], "score": 54.9}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 666, "matchups": [{"opponent": "virizion", "rating": 772, "opRating": 227}, {"opponent": "feraligatr", "rating": 637, "opRating": 362}, {"opponent": "drampa", "rating": 628, "opRating": 371}, {"opponent": "samu<PERSON>t", "rating": 612, "opRating": 387}, {"opponent": "swampert", "rating": 564, "opRating": 435}], "counters": [{"opponent": "golisopod", "rating": 237}, {"opponent": "typhlosion_shadow", "rating": 241}, {"opponent": "skeledirge", "rating": 308}, {"opponent": "tentacruel", "rating": 310}, {"opponent": "cradily", "rating": 400}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 46396}, {"moveId": "RAZOR_LEAF", "uses": 16604}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25597}, {"moveId": "PETAL_BLIZZARD", "uses": 15631}, {"moveId": "MOONBLAST", "uses": 15372}, {"moveId": "SOLAR_BEAM", "uses": 6239}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 54.7}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 715, "matchups": [{"opponent": "tentacruel", "rating": 769, "opRating": 230}, {"opponent": "victree<PERSON>_shadow", "rating": 763, "opRating": 236}, {"opponent": "golisopod", "rating": 717, "opRating": 282}, {"opponent": "<PERSON>ras", "rating": 680, "opRating": 319}, {"opponent": "cradily", "rating": 579, "opRating": 420}], "counters": [{"opponent": "turtonator", "rating": 73}, {"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "skeledirge", "rating": 136}, {"opponent": "talonflame", "rating": 235}, {"opponent": "bellibolt", "rating": 325}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 26024}, {"moveId": "BUG_BITE", "uses": 25300}, {"moveId": "STRUGGLE_BUG", "uses": 11659}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22401}, {"moveId": "MIRROR_SHOT", "uses": 10490}, {"moveId": "EARTHQUAKE", "uses": 9380}, {"moveId": "HEAVY_SLAM", "uses": 8449}, {"moveId": "RETURN", "uses": 6695}, {"moveId": "SAND_TOMB", "uses": 5539}]}, "moveset": ["VOLT_SWITCH", "ROCK_TOMB", "EARTHQUAKE"], "score": 54.6}, {"speciesId": "cacturne", "speciesName": "Cacturne", "rating": 643, "matchups": [{"opponent": "<PERSON>ras", "rating": 921, "opRating": 78}, {"opponent": "stunfisk", "rating": 777, "opRating": 222}, {"opponent": "feraligatr", "rating": 676, "opRating": 323}, {"opponent": "bellibolt", "rating": 630, "opRating": 369}, {"opponent": "skeledirge", "rating": 513, "opRating": 486}], "counters": [{"opponent": "virizion", "rating": 139}, {"opponent": "golisopod", "rating": 241}, {"opponent": "talonflame", "rating": 270}, {"opponent": "victree<PERSON>_shadow", "rating": 326}, {"opponent": "cradily", "rating": 450}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 26871}, {"moveId": "SAND_ATTACK", "uses": 18298}, {"moveId": "POISON_JAB", "uses": 17754}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 16838}, {"moveId": "TRAILBLAZE", "uses": 15778}, {"moveId": "DARK_PULSE", "uses": 12891}, {"moveId": "GRASS_KNOT", "uses": 6329}, {"moveId": "PAYBACK", "uses": 5853}, {"moveId": "RETURN", "uses": 5294}]}, "moveset": ["SUCKER_PUNCH", "DYNAMIC_PUNCH", "TRAILBLAZE"], "score": 54.5}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 638, "matchups": [{"opponent": "skeledirge", "rating": 853, "opRating": 146}, {"opponent": "typhlosion_shadow", "rating": 850, "opRating": 149}, {"opponent": "talonflame", "rating": 842, "opRating": 157}, {"opponent": "stunfisk", "rating": 757, "opRating": 242}, {"opponent": "tentacruel", "rating": 603, "opRating": 396}], "counters": [{"opponent": "serperior", "rating": 141}, {"opponent": "virizion", "rating": 233}, {"opponent": "victree<PERSON>_shadow", "rating": 278}, {"opponent": "cradily", "rating": 286}, {"opponent": "bellibolt", "rating": 422}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 37244}, {"moveId": "WATER_GUN", "uses": 25756}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 18244}, {"moveId": "MUD_BOMB", "uses": 17703}, {"moveId": "STONE_EDGE", "uses": 11558}, {"moveId": "SLUDGE_BOMB", "uses": 9149}, {"moveId": "EARTHQUAKE", "uses": 4184}, {"moveId": "ACID_SPRAY", "uses": 2061}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 54.5}, {"speciesId": "volcanion", "speciesName": "Volcanion", "rating": 620, "matchups": [{"opponent": "skeledirge", "rating": 865, "opRating": 134}, {"opponent": "forretress", "rating": 772, "opRating": 227}, {"opponent": "golisopod", "rating": 600, "opRating": 399}, {"opponent": "typhlosion_shadow", "rating": 544, "opRating": 455}, {"opponent": "<PERSON>ras", "rating": 533, "opRating": 466}], "counters": [{"opponent": "gastrodon", "rating": 134}, {"opponent": "swampert", "rating": 186}, {"opponent": "talonflame", "rating": 282}, {"opponent": "cradily", "rating": 433}, {"opponent": "bellibolt", "rating": 482}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 41348}, {"moveId": "WATER_GUN", "uses": 18431}, {"moveId": "TAKE_DOWN", "uses": 3191}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 18344}, {"moveId": "EARTH_POWER", "uses": 17899}, {"moveId": "SLUDGE_BOMB", "uses": 15995}, {"moveId": "HYDRO_PUMP", "uses": 10865}]}, "moveset": ["INCINERATE", "OVERHEAT", "EARTH_POWER"], "score": 54.5}, {"speciesId": "ceruledge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 700, "matchups": [{"opponent": "virizion", "rating": 921, "opRating": 78}, {"opponent": "victree<PERSON>_shadow", "rating": 836, "opRating": 163}, {"opponent": "talonflame", "rating": 801, "opRating": 198}, {"opponent": "skeledirge", "rating": 780, "opRating": 219}, {"opponent": "typhlosion_shadow", "rating": 599, "opRating": 400}], "counters": [{"opponent": "gastrodon", "rating": 134}, {"opponent": "swampert", "rating": 149}, {"opponent": "feraligatr", "rating": 154}, {"opponent": "golisopod", "rating": 230}, {"opponent": "<PERSON>ras", "rating": 313}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 38226}, {"moveId": "EMBER", "uses": 24774}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 25878}, {"moveId": "FLAME_CHARGE", "uses": 23945}, {"moveId": "FLAMETHROWER", "uses": 9897}, {"moveId": "HEAT_WAVE", "uses": 3193}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 54.3}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 579, "matchups": [{"opponent": "stunfisk", "rating": 804, "opRating": 195}, {"opponent": "gastrodon", "rating": 642, "opRating": 357}, {"opponent": "typhlosion_shadow", "rating": 618, "opRating": 381}, {"opponent": "bellibolt", "rating": 573, "opRating": 426}, {"opponent": "swampert", "rating": 508, "opRating": 491}], "counters": [{"opponent": "<PERSON>ras", "rating": 261}, {"opponent": "cradily", "rating": 294}, {"opponent": "tentacruel", "rating": 322}, {"opponent": "golisopod", "rating": 326}, {"opponent": "skeledirge", "rating": 336}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 34453}, {"moveId": "BULLET_SEED", "uses": 28547}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 23017}, {"moveId": "DRAGON_PULSE", "uses": 17445}, {"moveId": "DRACO_METEOR", "uses": 15438}, {"moveId": "SOLAR_BEAM", "uses": 6880}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 54.3}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 633, "matchups": [{"opponent": "feraligatr", "rating": 808, "opRating": 191}, {"opponent": "golisopod", "rating": 770, "opRating": 229}, {"opponent": "talonflame", "rating": 740, "opRating": 259}, {"opponent": "<PERSON>ras", "rating": 545, "opRating": 454}, {"opponent": "tentacruel", "rating": 511, "opRating": 488}], "counters": [{"opponent": "cradily", "rating": 230}, {"opponent": "victree<PERSON>_shadow", "rating": 275}, {"opponent": "bellibolt", "rating": 280}, {"opponent": "virizion", "rating": 290}, {"opponent": "skeledirge", "rating": 352}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 32102}, {"moveId": "THUNDER_SHOCK", "uses": 30898}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 24672}, {"moveId": "LAST_RESORT", "uses": 14504}, {"moveId": "THUNDERBOLT", "uses": 9089}, {"moveId": "THUNDER", "uses": 8015}, {"moveId": "ZAP_CANNON", "uses": 6561}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LAST_RESORT"], "score": 54.2}, {"speciesId": "marowak_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 597, "matchups": [{"opponent": "toxtricity", "rating": 876, "opRating": 123}, {"opponent": "skeledirge", "rating": 830, "opRating": 169}, {"opponent": "turtonator", "rating": 764, "opRating": 235}, {"opponent": "typhlosion_shadow", "rating": 732, "opRating": 267}, {"opponent": "victree<PERSON>_shadow", "rating": 605, "opRating": 394}], "counters": [{"opponent": "lickilicky", "rating": 130}, {"opponent": "<PERSON>ras", "rating": 238}, {"opponent": "cradily", "rating": 247}, {"opponent": "feraligatr", "rating": 320}, {"opponent": "bellibolt", "rating": 405}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 29621}, {"moveId": "FIRE_SPIN", "uses": 25822}, {"moveId": "ROCK_SMASH", "uses": 7531}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 22540}, {"moveId": "SHADOW_BONE", "uses": 20018}, {"moveId": "FIRE_BLAST", "uses": 7708}, {"moveId": "FLAME_WHEEL", "uses": 6443}, {"moveId": "SHADOW_BALL", "uses": 6355}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "BONE_CLUB", "SHADOW_BONE"], "score": 54.2}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 663, "matchups": [{"opponent": "swampert", "rating": 951, "opRating": 48}, {"opponent": "stunfisk", "rating": 839, "opRating": 160}, {"opponent": "<PERSON>ras", "rating": 717, "opRating": 282}, {"opponent": "bellibolt", "rating": 554, "opRating": 445}, {"opponent": "virizion", "rating": 516, "opRating": 483}], "counters": [{"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "talonflame", "rating": 181}, {"opponent": "skeledirge", "rating": 272}, {"opponent": "golisopod", "rating": 333}, {"opponent": "cradily", "rating": 352}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 28695}, {"moveId": "BUG_BITE", "uses": 24433}, {"moveId": "RAZOR_LEAF", "uses": 9898}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33302}, {"moveId": "X_SCISSOR", "uses": 19627}, {"moveId": "SILVER_WIND", "uses": 5808}, {"moveId": "LEAF_STORM", "uses": 4081}]}, "moveset": ["SHADOW_CLAW", "LEAF_BLADE", "X_SCISSOR"], "score": 54.1}, {"speciesId": "pignite", "speciesName": "Pignite", "rating": 583, "matchups": [{"opponent": "forretress", "rating": 803, "opRating": 196}, {"opponent": "virizion", "rating": 623, "opRating": 376}, {"opponent": "golisopod", "rating": 559, "opRating": 440}, {"opponent": "victree<PERSON>_shadow", "rating": 540, "opRating": 459}, {"opponent": "typhlosion_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "talonflame", "rating": 157}, {"opponent": "feraligatr", "rating": 295}, {"opponent": "<PERSON>ras", "rating": 346}, {"opponent": "cradily", "rating": 361}, {"opponent": "bellibolt", "rating": 390}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41137}, {"moveId": "TACKLE", "uses": 21863}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 27635}, {"moveId": "FLAME_CHARGE", "uses": 19079}, {"moveId": "RETURN", "uses": 8390}, {"moveId": "FLAMETHROWER", "uses": 7920}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 54.1}, {"speciesId": "blazi<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 630, "matchups": [{"opponent": "forretress", "rating": 840, "opRating": 159}, {"opponent": "bellibolt", "rating": 801, "opRating": 198}, {"opponent": "victree<PERSON>_shadow", "rating": 737, "opRating": 262}, {"opponent": "virizion", "rating": 574, "opRating": 425}, {"opponent": "lickilicky", "rating": 542, "opRating": 457}], "counters": [{"opponent": "gastrodon", "rating": 143}, {"opponent": "feraligatr", "rating": 179}, {"opponent": "swampert", "rating": 180}, {"opponent": "<PERSON>ras", "rating": 350}, {"opponent": "tentacruel", "rating": 360}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 34340}, {"moveId": "COUNTER", "uses": 28660}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15520}, {"moveId": "BRAVE_BIRD", "uses": 14132}, {"moveId": "BLAZE_KICK", "uses": 11352}, {"moveId": "STONE_EDGE", "uses": 10673}, {"moveId": "FOCUS_BLAST", "uses": 8075}, {"moveId": "OVERHEAT", "uses": 3261}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "BLAZE_KICK", "BLAST_BURN"], "score": 53.9}, {"speciesId": "empoleon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 682, "matchups": [{"opponent": "skeledirge", "rating": 743, "opRating": 256}, {"opponent": "talonflame", "rating": 727, "opRating": 272}, {"opponent": "cradily", "rating": 645}, {"opponent": "victree<PERSON>_shadow", "rating": 594, "opRating": 405}, {"opponent": "golisopod", "rating": 522, "opRating": 477}], "counters": [{"opponent": "toxtricity", "rating": 132}, {"opponent": "virizion", "rating": 139}, {"opponent": "typhlosion_shadow", "rating": 168}, {"opponent": "jellicent", "rating": 250}, {"opponent": "bellibolt", "rating": 280}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 21949}, {"moveId": "WATERFALL", "uses": 20998}, {"moveId": "STEEL_WING", "uses": 20059}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 28565}, {"moveId": "DRILL_PECK", "uses": 20140}, {"moveId": "BLIZZARD", "uses": 6305}, {"moveId": "FLASH_CANNON", "uses": 4811}, {"moveId": "HYDRO_PUMP", "uses": 3072}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 53.8}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 599, "matchups": [{"opponent": "jellicent", "rating": 723, "opRating": 276}, {"opponent": "feraligatr", "rating": 647, "opRating": 352}, {"opponent": "tentacruel", "rating": 592, "opRating": 407}, {"opponent": "typhlosion_shadow", "rating": 539, "opRating": 460}, {"opponent": "virizion", "rating": 513, "opRating": 486}], "counters": [{"opponent": "golisopod", "rating": 216}, {"opponent": "samu<PERSON>t", "rating": 231}, {"opponent": "bellibolt", "rating": 305}, {"opponent": "skeledirge", "rating": 308}, {"opponent": "cradily", "rating": 355}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 32843}, {"moveId": "TACKLE", "uses": 24389}, {"moveId": "ZEN_HEADBUTT", "uses": 5697}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 24688}, {"moveId": "STOMP", "uses": 14384}, {"moveId": "MEGAHORN", "uses": 13585}, {"moveId": "PSYCHIC", "uses": 10356}]}, "moveset": ["CONFUSION", "WILD_CHARGE", "STOMP"], "score": 53.8}, {"speciesId": "chandelure_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 664, "matchups": [{"opponent": "cradily", "rating": 833, "opRating": 166}, {"opponent": "golisopod", "rating": 829, "opRating": 170}, {"opponent": "virizion", "rating": 795, "opRating": 204}, {"opponent": "tentacruel", "rating": 779, "opRating": 220}, {"opponent": "skeledirge", "rating": 679, "opRating": 320}], "counters": [{"opponent": "gastrodon", "rating": 129}, {"opponent": "jellicent", "rating": 155}, {"opponent": "stunfisk", "rating": 170}, {"opponent": "feraligatr", "rating": 191}, {"opponent": "<PERSON>ras", "rating": 218}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 30239}, {"moveId": "HEX", "uses": 19713}, {"moveId": "FIRE_SPIN", "uses": 13077}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16665}, {"moveId": "SHADOW_BALL", "uses": 16059}, {"moveId": "OVERHEAT", "uses": 12851}, {"moveId": "ENERGY_BALL", "uses": 12180}, {"moveId": "POLTERGEIST", "uses": 5215}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 53.7}, {"speciesId": "marowak_alolan", "speciesName": "Marowak (Alolan)", "rating": 576, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 676, "opRating": 323}, {"opponent": "talonflame", "rating": 573, "opRating": 426}, {"opponent": "typhlosion_shadow", "rating": 552, "opRating": 447}, {"opponent": "samu<PERSON>t", "rating": 552, "opRating": 447}, {"opponent": "tentacruel", "rating": 503, "opRating": 496}], "counters": [{"opponent": "lickilicky", "rating": 188}, {"opponent": "<PERSON>ras", "rating": 231}, {"opponent": "feraligatr", "rating": 267}, {"opponent": "cradily", "rating": 341}, {"opponent": "bellibolt", "rating": 347}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 29475}, {"moveId": "FIRE_SPIN", "uses": 25740}, {"moveId": "ROCK_SMASH", "uses": 7718}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 20514}, {"moveId": "SHADOW_BONE", "uses": 18377}, {"moveId": "FIRE_BLAST", "uses": 6934}, {"moveId": "SHADOW_BALL", "uses": 5839}, {"moveId": "FLAME_WHEEL", "uses": 5751}, {"moveId": "RETURN", "uses": 5691}]}, "moveset": ["HEX", "BONE_CLUB", "SHADOW_BONE"], "score": 53.7}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 668, "matchups": [{"opponent": "golisopod", "rating": 628, "opRating": 371}, {"opponent": "talonflame", "rating": 625, "opRating": 375}, {"opponent": "skeledirge", "rating": 570, "opRating": 429}, {"opponent": "typhlosion_shadow", "rating": 516, "opRating": 483}, {"opponent": "feraligatr", "rating": 510, "opRating": 489}], "counters": [{"opponent": "jellicent", "rating": 104}, {"opponent": "blastoise", "rating": 111}, {"opponent": "virizion", "rating": 281}, {"opponent": "bellibolt", "rating": 350}, {"opponent": "cradily", "rating": 400}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 15239}, {"moveId": "EXTRASENSORY", "uses": 4503}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3537}, {"moveId": "STEEL_WING", "uses": 3350}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3046}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2943}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2903}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2672}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2655}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2510}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2414}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2374}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2308}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2281}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2222}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2222}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2211}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2153}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1591}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 26018}, {"moveId": "SACRED_FIRE", "uses": 14198}, {"moveId": "EARTHQUAKE", "uses": 8572}, {"moveId": "SOLAR_BEAM", "uses": 6126}, {"moveId": "RETURN", "uses": 5247}, {"moveId": "FIRE_BLAST", "uses": 2826}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 53.5}, {"speciesId": "magcargo", "speciesName": "Magcargo", "rating": 648, "matchups": [{"opponent": "talonflame", "rating": 909, "opRating": 90}, {"opponent": "forretress", "rating": 846, "opRating": 153}, {"opponent": "typhlosion_shadow", "rating": 736, "opRating": 263}, {"opponent": "skeledirge", "rating": 673, "opRating": 326}, {"opponent": "victree<PERSON>_shadow", "rating": 582, "opRating": 417}], "counters": [{"opponent": "stunfisk", "rating": 123}, {"opponent": "golisopod", "rating": 173}, {"opponent": "feraligatr", "rating": 279}, {"opponent": "<PERSON>ras", "rating": 311}, {"opponent": "bellibolt", "rating": 350}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 28003}, {"moveId": "EMBER", "uses": 19414}, {"moveId": "ROCK_THROW", "uses": 15602}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 33599}, {"moveId": "OVERHEAT", "uses": 15320}, {"moveId": "STONE_EDGE", "uses": 11511}, {"moveId": "HEAT_WAVE", "uses": 2695}]}, "moveset": ["INCINERATE", "ROCK_TOMB", "OVERHEAT"], "score": 53.5}, {"speciesId": "emboar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 647, "matchups": [{"opponent": "bellibolt", "rating": 807, "opRating": 192}, {"opponent": "talonflame", "rating": 807, "opRating": 192}, {"opponent": "victree<PERSON>_shadow", "rating": 744, "opRating": 255}, {"opponent": "typhlosion_shadow", "rating": 744, "opRating": 255}, {"opponent": "virizion", "rating": 557, "opRating": 442}], "counters": [{"opponent": "jellicent", "rating": 142}, {"opponent": "feraligatr", "rating": 160}, {"opponent": "samu<PERSON>t", "rating": 174}, {"opponent": "golisopod", "rating": 251}, {"opponent": "tentacruel", "rating": 328}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 53153}, {"moveId": "LOW_KICK", "uses": 9847}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 25499}, {"moveId": "ROCK_SLIDE", "uses": 15980}, {"moveId": "FOCUS_BLAST", "uses": 12691}, {"moveId": "FLAME_CHARGE", "uses": 6960}, {"moveId": "HEAT_WAVE", "uses": 1954}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 53.4}, {"speciesId": "raikou_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 708, "matchups": [{"opponent": "feraligatr", "rating": 831, "opRating": 168}, {"opponent": "tentacruel", "rating": 827, "opRating": 172}, {"opponent": "<PERSON>ras", "rating": 798, "opRating": 201}, {"opponent": "golisopod", "rating": 768, "opRating": 231}, {"opponent": "skeledirge", "rating": 718, "opRating": 281}], "counters": [{"opponent": "swampert", "rating": 87}, {"opponent": "victree<PERSON>_shadow", "rating": 110}, {"opponent": "cradily", "rating": 266}, {"opponent": "bellibolt", "rating": 300}, {"opponent": "virizion", "rating": 345}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 31759}, {"moveId": "VOLT_SWITCH", "uses": 31241}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 36281}, {"moveId": "SHADOW_BALL", "uses": 14185}, {"moveId": "THUNDERBOLT", "uses": 6536}, {"moveId": "THUNDER", "uses": 5727}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SHADOW_BALL"], "score": 53.4}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 619, "matchups": [{"opponent": "swampert", "rating": 930, "opRating": 69}, {"opponent": "<PERSON>ras", "rating": 928, "opRating": 71}, {"opponent": "gastrodon", "rating": 886, "opRating": 113}, {"opponent": "stunfisk", "rating": 801, "opRating": 198}, {"opponent": "bellibolt", "rating": 662, "opRating": 337}], "counters": [{"opponent": "virizion", "rating": 169}, {"opponent": "talonflame", "rating": 279}, {"opponent": "golisopod", "rating": 287}, {"opponent": "cradily", "rating": 336}, {"opponent": "tentacruel", "rating": 405}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 21097}, {"moveId": "BULLET_SEED", "uses": 20479}, {"moveId": "FEINT_ATTACK", "uses": 13930}, {"moveId": "RAZOR_LEAF", "uses": 7520}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28038}, {"moveId": "FOUL_PLAY", "uses": 16649}, {"moveId": "HURRICANE", "uses": 8139}, {"moveId": "LEAF_TORNADO", "uses": 5344}, {"moveId": "RETURN", "uses": 4861}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 53.4}, {"speciesId": "tapu_fini", "speciesName": "<PERSON><PERSON>", "rating": 624, "matchups": [{"opponent": "skeledirge", "rating": 758, "opRating": 241}, {"opponent": "talonflame", "rating": 677, "opRating": 322}, {"opponent": "virizion", "rating": 617, "opRating": 382}, {"opponent": "samu<PERSON>t", "rating": 617, "opRating": 382}, {"opponent": "typhlosion_shadow", "rating": 595, "opRating": 404}], "counters": [{"opponent": "cradily", "rating": 152}, {"opponent": "victree<PERSON>_shadow", "rating": 338}, {"opponent": "<PERSON>ras", "rating": 344}, {"opponent": "tentacruel", "rating": 384}, {"opponent": "bellibolt", "rating": 450}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 6505}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4244}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4168}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4038}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3917}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3653}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3645}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3604}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3573}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3557}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3440}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3344}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3334}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3313}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3296}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3100}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2477}], "chargedMoves": [{"moveId": "NATURES_MADNESS", "uses": 22758}, {"moveId": "SURF", "uses": 19212}, {"moveId": "ICE_BEAM", "uses": 10385}, {"moveId": "MOONBLAST", "uses": 6888}, {"moveId": "HYDRO_PUMP", "uses": 3679}]}, "moveset": ["WATER_GUN", "SURF", "NATURES_MADNESS"], "score": 53.4}, {"speciesId": "dewgong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 577, "matchups": [{"opponent": "drampa", "rating": 666, "opRating": 333}, {"opponent": "swampert", "rating": 642, "opRating": 357}, {"opponent": "lickilicky", "rating": 548, "opRating": 451}, {"opponent": "victree<PERSON>_shadow", "rating": 543, "opRating": 456}, {"opponent": "tentacruel", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jellicent", "rating": 244}, {"opponent": "<PERSON>ras", "rating": 294}, {"opponent": "skeledirge", "rating": 338}, {"opponent": "bellibolt", "rating": 347}, {"opponent": "cradily", "rating": 397}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 34562}, {"moveId": "FROST_BREATH", "uses": 20260}, {"moveId": "IRON_TAIL", "uses": 8113}], "chargedMoves": [{"moveId": "AQUA_JET", "uses": 16210}, {"moveId": "ICY_WIND", "uses": 14633}, {"moveId": "DRILL_RUN", "uses": 14226}, {"moveId": "BLIZZARD", "uses": 6580}, {"moveId": "LIQUIDATION", "uses": 5929}, {"moveId": "WATER_PULSE", "uses": 2949}, {"moveId": "AURORA_BEAM", "uses": 2645}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "DRILL_RUN"], "score": 53.3}, {"speciesId": "<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 603, "matchups": [{"opponent": "skeledirge", "rating": 833, "opRating": 166}, {"opponent": "talonflame", "rating": 821, "opRating": 178}, {"opponent": "turtonator", "rating": 764, "opRating": 235}, {"opponent": "swampert", "rating": 625, "opRating": 375}, {"opponent": "stunfisk", "rating": 571, "opRating": 428}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "tentacruel", "rating": 286}, {"opponent": "virizion", "rating": 290}, {"opponent": "cradily", "rating": 361}, {"opponent": "bellibolt", "rating": 382}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 33019}, {"moveId": "WATER_GUN", "uses": 29981}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 19020}, {"moveId": "LIQUIDATION", "uses": 14770}, {"moveId": "SYNCHRONOISE", "uses": 10056}, {"moveId": "ICE_BEAM", "uses": 8888}, {"moveId": "BUBBLE_BEAM", "uses": 4218}, {"moveId": "PSYCHIC", "uses": 3181}, {"moveId": "HYDRO_PUMP", "uses": 2820}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "LIQUIDATION"], "score": 53.3}, {"speciesId": "magneton_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 687, "matchups": [{"opponent": "tentacruel", "rating": 861, "opRating": 138}, {"opponent": "golisopod", "rating": 857, "opRating": 142}, {"opponent": "feraligatr", "rating": 800, "opRating": 199}, {"opponent": "cradily", "rating": 682, "opRating": 317}, {"opponent": "<PERSON>ras", "rating": 532, "opRating": 467}], "counters": [{"opponent": "stunfisk", "rating": 39}, {"opponent": "gastrodon", "rating": 73}, {"opponent": "virizion", "rating": 100}, {"opponent": "typhlosion_shadow", "rating": 182}, {"opponent": "bellibolt", "rating": 245}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 16825}, {"moveId": "THUNDER_SHOCK", "uses": 16272}, {"moveId": "METAL_SOUND", "uses": 12546}, {"moveId": "SPARK", "uses": 10144}, {"moveId": "CHARGE_BEAM", "uses": 7230}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 25104}, {"moveId": "MAGNET_BOMB", "uses": 20121}, {"moveId": "ZAP_CANNON", "uses": 13582}, {"moveId": "FLASH_CANNON", "uses": 4312}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "MAGNET_BOMB"], "score": 53.3}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 598, "matchups": [{"opponent": "feraligatr", "rating": 787, "opRating": 212}, {"opponent": "golisopod", "rating": 742, "opRating": 257}, {"opponent": "talonflame", "rating": 716, "opRating": 283}, {"opponent": "tentacruel", "rating": 656, "opRating": 343}, {"opponent": "skeledirge", "rating": 529, "opRating": 470}], "counters": [{"opponent": "gastrodon", "rating": 181}, {"opponent": "virizion", "rating": 245}, {"opponent": "cradily", "rating": 247}, {"opponent": "bellibolt", "rating": 270}, {"opponent": "victree<PERSON>_shadow", "rating": 290}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 38929}, {"moveId": "SPARK", "uses": 24071}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 24488}, {"moveId": "POWER_WHIP", "uses": 17004}, {"moveId": "DAZZLING_GLEAM", "uses": 13591}, {"moveId": "THUNDER", "uses": 7892}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "POWER_WHIP"], "score": 53.3}, {"speciesId": "golduck", "speciesName": "Gold<PERSON>", "rating": 602, "matchups": [{"opponent": "skeledirge", "rating": 866, "opRating": 133}, {"opponent": "talonflame", "rating": 866, "opRating": 133}, {"opponent": "typhlosion_shadow", "rating": 562, "opRating": 437}, {"opponent": "lickilicky", "rating": 553, "opRating": 446}, {"opponent": "swampert", "rating": 508, "opRating": 491}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "tentacruel", "rating": 292}, {"opponent": "bellibolt", "rating": 322}, {"opponent": "cradily", "rating": 325}, {"opponent": "golisopod", "rating": 336}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 32797}, {"moveId": "WATER_GUN", "uses": 30203}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 17527}, {"moveId": "LIQUIDATION", "uses": 13716}, {"moveId": "SYNCHRONOISE", "uses": 9234}, {"moveId": "ICE_BEAM", "uses": 8167}, {"moveId": "RETURN", "uses": 4860}, {"moveId": "BUBBLE_BEAM", "uses": 3904}, {"moveId": "PSYCHIC", "uses": 2922}, {"moveId": "HYDRO_PUMP", "uses": 2637}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "LIQUIDATION"], "score": 53.1}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 535, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 899, "opRating": 100}, {"opponent": "skeledirge", "rating": 800, "opRating": 199}, {"opponent": "jellicent", "rating": 758, "opRating": 241}, {"opponent": "victree<PERSON>_shadow", "rating": 522, "opRating": 477}, {"opponent": "feraligatr", "rating": 509, "opRating": 490}], "counters": [{"opponent": "talonflame", "rating": 217}, {"opponent": "cradily", "rating": 300}, {"opponent": "<PERSON>ras", "rating": 320}, {"opponent": "bellibolt", "rating": 340}, {"opponent": "tentacruel", "rating": 366}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 42149}, {"moveId": "BITE", "uses": 20851}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 18828}, {"moveId": "BOOMBURST", "uses": 15613}, {"moveId": "DISARMING_VOICE", "uses": 15273}, {"moveId": "FIRE_BLAST", "uses": 7531}, {"moveId": "RETURN", "uses": 5785}]}, "moveset": ["ASTONISH", "DISARMING_VOICE", "CRUNCH"], "score": 52.9}, {"speciesId": "furret", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 561, "matchups": [{"opponent": "jellicent", "rating": 713, "opRating": 286}, {"opponent": "gastrodon", "rating": 643, "opRating": 356}, {"opponent": "swampert", "rating": 620, "opRating": 379}, {"opponent": "zapdos", "rating": 601, "opRating": 398}, {"opponent": "stunfisk", "rating": 525, "opRating": 474}], "counters": [{"opponent": "virizion", "rating": 193}, {"opponent": "tentacruel", "rating": 298}, {"opponent": "<PERSON>ras", "rating": 333}, {"opponent": "bellibolt", "rating": 350}, {"opponent": "cradily", "rating": 441}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 32498}, {"moveId": "QUICK_ATTACK", "uses": 30502}], "chargedMoves": [{"moveId": "SWIFT", "uses": 24512}, {"moveId": "TRAILBLAZE", "uses": 14768}, {"moveId": "BRICK_BREAK", "uses": 10489}, {"moveId": "DIG", "uses": 9994}, {"moveId": "HYPER_BEAM", "uses": 3292}]}, "moveset": ["SUCKER_PUNCH", "SWIFT", "TRAILBLAZE"], "score": 52.9}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 674, "matchups": [{"opponent": "virizion", "rating": 857, "opRating": 142}, {"opponent": "feraligatr", "rating": 724, "opRating": 275}, {"opponent": "golisopod", "rating": 707, "opRating": 292}, {"opponent": "victree<PERSON>_shadow", "rating": 653, "opRating": 346}, {"opponent": "skeledirge", "rating": 502, "opRating": 497}], "counters": [{"opponent": "bellibolt", "rating": 170}, {"opponent": "stunfisk", "rating": 184}, {"opponent": "typhlosion_shadow", "rating": 228}, {"opponent": "cradily", "rating": 363}, {"opponent": "tentacruel", "rating": 381}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 20206}, {"moveId": "WING_ATTACK", "uses": 18145}, {"moveId": "AIR_SLASH", "uses": 14028}, {"moveId": "STEEL_WING", "uses": 10631}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22332}, {"moveId": "AIR_CUTTER", "uses": 19122}, {"moveId": "RETURN", "uses": 7495}, {"moveId": "AERIAL_ACE", "uses": 7125}, {"moveId": "HURRICANE", "uses": 3939}, {"moveId": "FEATHER_DANCE", "uses": 2714}]}, "moveset": ["GUST", "AIR_CUTTER", "BRAVE_BIRD"], "score": 52.9}, {"speciesId": "cacturne_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 618, "matchups": [{"opponent": "<PERSON>ras", "rating": 846, "opRating": 153}, {"opponent": "cradily", "rating": 637}, {"opponent": "tentacruel", "rating": 565, "opRating": 434}, {"opponent": "skeledirge", "rating": 565, "opRating": 434}, {"opponent": "bellibolt", "rating": 539, "opRating": 460}], "counters": [{"opponent": "virizion", "rating": 166}, {"opponent": "golisopod", "rating": 202}, {"opponent": "typhlosion_shadow", "rating": 218}, {"opponent": "samu<PERSON>t", "rating": 254}, {"opponent": "talonflame", "rating": 297}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 27399}, {"moveId": "SAND_ATTACK", "uses": 18089}, {"moveId": "POISON_JAB", "uses": 17451}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 18416}, {"moveId": "TRAILBLAZE", "uses": 16988}, {"moveId": "DARK_PULSE", "uses": 14276}, {"moveId": "GRASS_KNOT", "uses": 6806}, {"moveId": "PAYBACK", "uses": 6402}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SUCKER_PUNCH", "DYNAMIC_PUNCH", "TRAILBLAZE"], "score": 52.7}, {"speciesId": "beedrill", "speciesName": "Beedrill", "rating": 591, "matchups": [{"opponent": "toxtricity", "rating": 855, "opRating": 144}, {"opponent": "typhlosion_shadow", "rating": 563, "opRating": 436}, {"opponent": "tentacruel", "rating": 536, "opRating": 463}, {"opponent": "victree<PERSON>_shadow", "rating": 530, "opRating": 469}, {"opponent": "samu<PERSON>t", "rating": 530, "opRating": 469}], "counters": [{"opponent": "<PERSON>ras", "rating": 212}, {"opponent": "talonflame", "rating": 241}, {"opponent": "bellibolt", "rating": 290}, {"opponent": "virizion", "rating": 318}, {"opponent": "cradily", "rating": 425}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 18906}, {"moveId": "POISON_JAB", "uses": 16477}, {"moveId": "BUG_BITE", "uses": 14747}, {"moveId": "INFESTATION", "uses": 12904}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 16104}, {"moveId": "DRILL_RUN", "uses": 15901}, {"moveId": "AERIAL_ACE", "uses": 11549}, {"moveId": "SLUDGE_BOMB", "uses": 10618}, {"moveId": "RETURN", "uses": 4562}, {"moveId": "FELL_STINGER", "uses": 4296}]}, "moveset": ["POISON_STING", "DRILL_RUN", "X_SCISSOR"], "score": 52.5}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 629, "matchups": [{"opponent": "typhlosion_shadow", "rating": 848, "opRating": 151}, {"opponent": "skeledirge", "rating": 762, "opRating": 237}, {"opponent": "tentacruel", "rating": 690, "opRating": 309}, {"opponent": "talonflame", "rating": 623, "opRating": 376}, {"opponent": "bellibolt", "rating": 539}], "counters": [{"opponent": "cradily", "rating": 205}, {"opponent": "golisopod", "rating": 230}, {"opponent": "virizion", "rating": 306}, {"opponent": "victree<PERSON>_shadow", "rating": 314}, {"opponent": "<PERSON>ras", "rating": 411}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 35252}, {"moveId": "WATER_GUN", "uses": 27748}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 24849}, {"moveId": "SCALD", "uses": 17291}, {"moveId": "BLIZZARD", "uses": 9114}, {"moveId": "RETURN", "uses": 7225}, {"moveId": "WATER_PULSE", "uses": 4543}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 52.5}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 610, "matchups": [{"opponent": "virizion", "rating": 864, "opRating": 135}, {"opponent": "cradily", "rating": 792, "opRating": 207}, {"opponent": "swampert", "rating": 704, "opRating": 295}, {"opponent": "golisopod", "rating": 632, "opRating": 367}, {"opponent": "samu<PERSON>t", "rating": 588, "opRating": 411}], "counters": [{"opponent": "typhlosion_shadow", "rating": 192}, {"opponent": "skeledirge", "rating": 280}, {"opponent": "tentacruel", "rating": 289}, {"opponent": "<PERSON>ras", "rating": 329}, {"opponent": "bellibolt", "rating": 330}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 33031}, {"moveId": "WING_ATTACK", "uses": 29969}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 26765}, {"moveId": "ANCIENT_POWER", "uses": 19141}, {"moveId": "BUG_BUZZ", "uses": 17153}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "ANCIENT_POWER"], "score": 52.5}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 681, "matchups": [{"opponent": "feraligatr", "rating": 878, "opRating": 121}, {"opponent": "samu<PERSON>t", "rating": 810, "opRating": 189}, {"opponent": "typhlosion_shadow", "rating": 571, "opRating": 428}, {"opponent": "<PERSON>ras", "rating": 565, "opRating": 434}, {"opponent": "bellibolt", "rating": 550, "opRating": 449}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 101}, {"opponent": "talonflame", "rating": 160}, {"opponent": "skeledirge", "rating": 241}, {"opponent": "tentacruel", "rating": 349}, {"opponent": "cradily", "rating": 430}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 28978}, {"moveId": "MAGICAL_LEAF", "uses": 22892}, {"moveId": "RAZOR_LEAF", "uses": 11193}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 33572}, {"moveId": "EARTHQUAKE", "uses": 11605}, {"moveId": "RETURN", "uses": 9381}, {"moveId": "PETAL_BLIZZARD", "uses": 4657}, {"moveId": "SOLAR_BEAM", "uses": 3810}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 52.1}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 652, "matchups": [{"opponent": "talonflame", "rating": 839, "opRating": 160}, {"opponent": "typhlosion_shadow", "rating": 786, "opRating": 213}, {"opponent": "virizion", "rating": 634, "opRating": 365}, {"opponent": "victree<PERSON>_shadow", "rating": 578, "opRating": 421}, {"opponent": "golisopod", "rating": 515, "opRating": 484}], "counters": [{"opponent": "feraligatr", "rating": 128}, {"opponent": "gastrodon", "rating": 132}, {"opponent": "swampert", "rating": 144}, {"opponent": "samu<PERSON>t", "rating": 145}, {"opponent": "tentacruel", "rating": 298}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 52421}, {"moveId": "LOW_KICK", "uses": 10579}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 23297}, {"moveId": "ROCK_SLIDE", "uses": 14388}, {"moveId": "FOCUS_BLAST", "uses": 11254}, {"moveId": "FLAME_CHARGE", "uses": 6406}, {"moveId": "RETURN", "uses": 6028}, {"moveId": "HEAT_WAVE", "uses": 1719}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 51.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 808, "matchups": [{"opponent": "tentacruel", "rating": 949, "opRating": 50}, {"opponent": "golisopod", "rating": 895, "opRating": 104}, {"opponent": "<PERSON>ras", "rating": 882, "opRating": 117}, {"opponent": "feraligatr", "rating": 879, "opRating": 120}, {"opponent": "cradily", "rating": 587, "opRating": 412}], "counters": [{"opponent": "gastrodon", "rating": 58}, {"opponent": "bellibolt", "rating": 85}, {"opponent": "skeledirge", "rating": 122}, {"opponent": "swampert", "rating": 129}, {"opponent": "virizion", "rating": 200}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40748}, {"moveId": "SPARK", "uses": 22252}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 46160}, {"moveId": "GYRO_BALL", "uses": 8675}, {"moveId": "FELL_STINGER", "uses": 8136}]}, "moveset": ["THUNDER_SHOCK", "FELL_STINGER", "WILD_CHARGE"], "score": 51.8}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 658, "matchups": [{"opponent": "forretress", "rating": 859, "opRating": 140}, {"opponent": "victree<PERSON>_shadow", "rating": 791, "opRating": 208}, {"opponent": "golisopod", "rating": 582, "opRating": 417}, {"opponent": "virizion", "rating": 541, "opRating": 458}, {"opponent": "skeledirge", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gastrodon", "rating": 101}, {"opponent": "feraligatr", "rating": 135}, {"opponent": "swampert", "rating": 141}, {"opponent": "typhlosion_shadow", "rating": 215}, {"opponent": "cradily", "rating": 394}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 35102}, {"moveId": "BUG_BITE", "uses": 27898}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 18822}, {"moveId": "STONE_EDGE", "uses": 13141}, {"moveId": "EARTH_POWER", "uses": 11221}, {"moveId": "IRON_HEAD", "uses": 6695}, {"moveId": "RETURN", "uses": 5458}, {"moveId": "FLAMETHROWER", "uses": 5060}, {"moveId": "FIRE_BLAST", "uses": 2685}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 51.5}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 589, "matchups": [{"opponent": "gastrodon", "rating": 831, "opRating": 168}, {"opponent": "swampert", "rating": 773, "opRating": 226}, {"opponent": "jellicent", "rating": 645, "opRating": 354}, {"opponent": "virizion", "rating": 633, "opRating": 366}, {"opponent": "samu<PERSON>t", "rating": 542, "opRating": 457}], "counters": [{"opponent": "tentacruel", "rating": 263}, {"opponent": "<PERSON>ras", "rating": 266}, {"opponent": "skeledirge", "rating": 283}, {"opponent": "bellibolt", "rating": 312}, {"opponent": "cradily", "rating": 333}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 39051}, {"moveId": "RAZOR_LEAF", "uses": 23949}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26523}, {"moveId": "AERIAL_ACE", "uses": 15400}, {"moveId": "BRUTAL_SWING", "uses": 14225}, {"moveId": "STOMP", "uses": 6850}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "BRUTAL_SWING"], "score": 51.5}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 645, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 813, "opRating": 186}, {"opponent": "forretress", "rating": 777, "opRating": 222}, {"opponent": "venusaur", "rating": 725, "opRating": 274}, {"opponent": "virizion", "rating": 566, "opRating": 433}, {"opponent": "talonflame", "rating": 560, "opRating": 439}], "counters": [{"opponent": "samu<PERSON>t", "rating": 117}, {"opponent": "feraligatr", "rating": 128}, {"opponent": "skeledirge", "rating": 250}, {"opponent": "bellibolt", "rating": 262}, {"opponent": "cradily", "rating": 336}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 32823}, {"moveId": "CONFUSION", "uses": 30177}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 39501}, {"moveId": "PSYCHIC", "uses": 10672}, {"moveId": "FOCUS_BLAST", "uses": 8329}, {"moveId": "OVERHEAT", "uses": 4604}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 51.4}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 652, "matchups": [{"opponent": "golisopod", "rating": 857, "opRating": 142}, {"opponent": "cradily", "rating": 715, "opRating": 284}, {"opponent": "tentacruel", "rating": 630, "opRating": 369}, {"opponent": "victree<PERSON>_shadow", "rating": 630, "opRating": 369}, {"opponent": "<PERSON>ras", "rating": 528, "opRating": 471}], "counters": [{"opponent": "stunfisk", "rating": 30}, {"opponent": "gastrodon", "rating": 66}, {"opponent": "virizion", "rating": 87}, {"opponent": "bellibolt", "rating": 220}, {"opponent": "skeledirge", "rating": 338}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 16430}, {"moveId": "THUNDER_SHOCK", "uses": 15791}, {"moveId": "METAL_SOUND", "uses": 12854}, {"moveId": "SPARK", "uses": 10442}, {"moveId": "CHARGE_BEAM", "uses": 7485}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 21918}, {"moveId": "MAGNET_BOMB", "uses": 16922}, {"moveId": "ZAP_CANNON", "uses": 11742}, {"moveId": "RETURN", "uses": 8731}, {"moveId": "FLASH_CANNON", "uses": 3608}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "MAGNET_BOMB"], "score": 51.3}, {"speciesId": "beedrill_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 621, "matchups": [{"opponent": "toxtricity", "rating": 828, "opRating": 171}, {"opponent": "cradily", "rating": 822, "opRating": 177}, {"opponent": "venusaur", "rating": 671, "opRating": 328}, {"opponent": "serperior", "rating": 600, "opRating": 399}, {"opponent": "stunfisk", "rating": 570, "opRating": 429}], "counters": [{"opponent": "typhlosion_shadow", "rating": 86}, {"opponent": "<PERSON>ras", "rating": 233}, {"opponent": "talonflame", "rating": 250}, {"opponent": "tentacruel", "rating": 322}, {"opponent": "bellibolt", "rating": 357}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 19105}, {"moveId": "POISON_JAB", "uses": 16589}, {"moveId": "BUG_BITE", "uses": 14241}, {"moveId": "INFESTATION", "uses": 13070}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 17329}, {"moveId": "DRILL_RUN", "uses": 17099}, {"moveId": "AERIAL_ACE", "uses": 12462}, {"moveId": "SLUDGE_BOMB", "uses": 11473}, {"moveId": "FELL_STINGER", "uses": 4519}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "DRILL_RUN", "X_SCISSOR"], "score": 51.1}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 672, "matchups": [{"opponent": "jellicent", "rating": 872, "opRating": 127}, {"opponent": "<PERSON>ras", "rating": 860, "opRating": 139}, {"opponent": "stunfisk", "rating": 852, "opRating": 147}, {"opponent": "feraligatr", "rating": 849, "opRating": 150}, {"opponent": "bellibolt", "rating": 713, "opRating": 286}], "counters": [{"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "golisopod", "rating": 145}, {"opponent": "talonflame", "rating": 178}, {"opponent": "victree<PERSON>_shadow", "rating": 245}, {"opponent": "tentacruel", "rating": 384}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 29378}, {"moveId": "MAGICAL_LEAF", "uses": 23004}, {"moveId": "RAZOR_LEAF", "uses": 10644}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 39073}, {"moveId": "EARTHQUAKE", "uses": 13888}, {"moveId": "PETAL_BLIZZARD", "uses": 5502}, {"moveId": "SOLAR_BEAM", "uses": 4327}, {"moveId": "FRUSTRATION", "uses": 17}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 51.1}, {"speciesId": "crawdaunt", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 598, "matchups": [{"opponent": "skeledirge", "rating": 868, "opRating": 131}, {"opponent": "talonflame", "rating": 858, "opRating": 141}, {"opponent": "typhlosion_shadow", "rating": 826, "opRating": 173}, {"opponent": "jellicent", "rating": 691, "opRating": 308}, {"opponent": "<PERSON>ras", "rating": 560, "opRating": 439}], "counters": [{"opponent": "virizion", "rating": 136}, {"opponent": "cradily", "rating": 266}, {"opponent": "golisopod", "rating": 269}, {"opponent": "bellibolt", "rating": 292}, {"opponent": "tentacruel", "rating": 346}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36356}, {"moveId": "WATERFALL", "uses": 26644}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 22518}, {"moveId": "SCALD", "uses": 12540}, {"moveId": "RAZOR_SHELL", "uses": 9799}, {"moveId": "CRABHAMMER", "uses": 6125}, {"moveId": "RETURN", "uses": 5315}, {"moveId": "VICE_GRIP", "uses": 4832}, {"moveId": "BUBBLE_BEAM", "uses": 1840}]}, "moveset": ["SNARL", "NIGHT_SLASH", "CRABHAMMER"], "score": 51}, {"speciesId": "politoed_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 669, "matchups": [{"opponent": "typhlosion_shadow", "rating": 865, "opRating": 134}, {"opponent": "skeledirge", "rating": 719, "opRating": 280}, {"opponent": "virizion", "rating": 700, "opRating": 299}, {"opponent": "talonflame", "rating": 623, "opRating": 376}, {"opponent": "stunfisk", "rating": 623, "opRating": 376}], "counters": [{"opponent": "serperior", "rating": 70}, {"opponent": "cradily", "rating": 111}, {"opponent": "victree<PERSON>_shadow", "rating": 260}, {"opponent": "golisopod", "rating": 308}, {"opponent": "bellibolt", "rating": 375}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31851}, {"moveId": "MUD_SHOT", "uses": 31149}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 22435}, {"moveId": "ICE_BEAM", "uses": 10486}, {"moveId": "EARTHQUAKE", "uses": 8526}, {"moveId": "SURF", "uses": 7561}, {"moveId": "SCALD", "uses": 7385}, {"moveId": "BLIZZARD", "uses": 3721}, {"moveId": "HYDRO_PUMP", "uses": 2955}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "BLIZZARD"], "score": 51}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 557, "matchups": [{"opponent": "trevenant", "rating": 893, "opRating": 106}, {"opponent": "venusaur", "rating": 771, "opRating": 228}, {"opponent": "skeledirge", "rating": 690, "opRating": 309}, {"opponent": "jellicent", "rating": 671, "opRating": 328}, {"opponent": "feraligatr", "rating": 568, "opRating": 431}], "counters": [{"opponent": "virizion", "rating": 184}, {"opponent": "golisopod", "rating": 251}, {"opponent": "bellibolt", "rating": 310}, {"opponent": "<PERSON>ras", "rating": 318}, {"opponent": "cradily", "rating": 325}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 40235}, {"moveId": "SCRATCH", "uses": 22765}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 31348}, {"moveId": "LOW_SWEEP", "uses": 16271}, {"moveId": "HYPER_BEAM", "uses": 15224}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 50.9}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 586, "matchups": [{"opponent": "jellicent", "rating": 751, "opRating": 248}, {"opponent": "stunfisk", "rating": 713, "opRating": 286}, {"opponent": "gastrodon", "rating": 689, "opRating": 310}, {"opponent": "swampert", "rating": 603, "opRating": 396}, {"opponent": "bellibolt", "rating": 568, "opRating": 431}], "counters": [{"opponent": "<PERSON>ras", "rating": 209}, {"opponent": "cradily", "rating": 255}, {"opponent": "golisopod", "rating": 276}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "feraligatr", "rating": 305}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 35031}, {"moveId": "BULLET_SEED", "uses": 27969}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20088}, {"moveId": "DRAGON_PULSE", "uses": 14396}, {"moveId": "DRACO_METEOR", "uses": 12875}, {"moveId": "RETURN", "uses": 9891}, {"moveId": "SOLAR_BEAM", "uses": 5745}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 50.9}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 644, "matchups": [{"opponent": "skeledirge", "rating": 789, "opRating": 210}, {"opponent": "forretress", "rating": 775, "opRating": 224}, {"opponent": "victree<PERSON>_shadow", "rating": 751, "opRating": 248}, {"opponent": "talonflame", "rating": 547, "opRating": 452}, {"opponent": "<PERSON>ras", "rating": 503, "opRating": 496}], "counters": [{"opponent": "swampert", "rating": 161}, {"opponent": "feraligatr", "rating": 166}, {"opponent": "typhlosion_shadow", "rating": 215}, {"opponent": "golisopod", "rating": 258}, {"opponent": "virizion", "rating": 330}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 17311}, {"moveId": "EXTRASENSORY", "uses": 4488}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3383}, {"moveId": "STEEL_WING", "uses": 3180}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2984}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2856}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2746}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2661}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2527}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2375}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2299}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2274}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2188}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2174}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2133}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2107}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2052}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2039}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1470}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 28421}, {"moveId": "SACRED_FIRE", "uses": 15328}, {"moveId": "EARTHQUAKE", "uses": 9376}, {"moveId": "SOLAR_BEAM", "uses": 6708}, {"moveId": "FIRE_BLAST", "uses": 3045}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 50.7}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 592, "matchups": [{"opponent": "skeledirge", "rating": 845, "opRating": 154}, {"opponent": "typhlosion_shadow", "rating": 795, "opRating": 204}, {"opponent": "tentacruel", "rating": 666, "opRating": 333}, {"opponent": "virizion", "rating": 540, "opRating": 459}, {"opponent": "bellibolt", "rating": 517}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 137}, {"opponent": "cradily", "rating": 241}, {"opponent": "golisopod", "rating": 276}, {"opponent": "<PERSON>ras", "rating": 316}, {"opponent": "feraligatr", "rating": 355}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 33192}, {"moveId": "BUBBLE", "uses": 29808}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 24685}, {"moveId": "MUDDY_WATER", "uses": 19438}, {"moveId": "SLUDGE_BOMB", "uses": 18915}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "SLUDGE_BOMB"], "score": 50.7}, {"speciesId": "forretress_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 687, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 803, "opRating": 196}, {"opponent": "tentacruel", "rating": 763, "opRating": 236}, {"opponent": "feraligatr", "rating": 653, "opRating": 346}, {"opponent": "<PERSON>ras", "rating": 601, "opRating": 398}, {"opponent": "cradily", "rating": 518, "opRating": 481}], "counters": [{"opponent": "turtonator", "rating": 91}, {"opponent": "skeledirge", "rating": 113}, {"opponent": "typhlosion_shadow", "rating": 162}, {"opponent": "talonflame", "rating": 193}, {"opponent": "bellibolt", "rating": 397}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 26694}, {"moveId": "BUG_BITE", "uses": 24566}, {"moveId": "STRUGGLE_BUG", "uses": 11760}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 24920}, {"moveId": "MIRROR_SHOT", "uses": 11724}, {"moveId": "EARTHQUAKE", "uses": 10552}, {"moveId": "HEAVY_SLAM", "uses": 9620}, {"moveId": "SAND_TOMB", "uses": 6138}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "ROCK_TOMB", "EARTHQUAKE"], "score": 50.6}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 562, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 743, "opRating": 256}, {"opponent": "skeledirge", "rating": 693, "opRating": 306}, {"opponent": "talonflame", "rating": 680, "opRating": 319}, {"opponent": "typhlosion_shadow", "rating": 604, "opRating": 395}, {"opponent": "feraligatr", "rating": 598, "opRating": 401}], "counters": [{"opponent": "<PERSON>ras", "rating": 229}, {"opponent": "cradily", "rating": 238}, {"opponent": "lickilicky", "rating": 247}, {"opponent": "bellibolt", "rating": 277}, {"opponent": "stunfisk", "rating": 280}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 41979}, {"moveId": "SCRATCH", "uses": 21021}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 30518}, {"moveId": "THUNDER", "uses": 18850}, {"moveId": "PLAY_ROUGH", "uses": 13472}, {"moveId": "FRUSTRATION", "uses": 12}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 50.5}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 735, "matchups": [{"opponent": "golisopod", "rating": 810, "opRating": 189}, {"opponent": "virizion", "rating": 779, "opRating": 220}, {"opponent": "talonflame", "rating": 593, "opRating": 406}, {"opponent": "feraligatr", "rating": 586, "opRating": 413}, {"opponent": "typhlosion_shadow", "rating": 586, "opRating": 413}], "counters": [{"opponent": "cradily", "rating": 127}, {"opponent": "forretress", "rating": 131}, {"opponent": "stunfisk", "rating": 142}, {"opponent": "bellibolt", "rating": 182}, {"opponent": "victree<PERSON>_shadow", "rating": 245}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 18234}, {"moveId": "SAND_ATTACK", "uses": 16326}, {"moveId": "GUST", "uses": 14633}, {"moveId": "WING_ATTACK", "uses": 13781}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 21808}, {"moveId": "CLOSE_COMBAT", "uses": 20343}, {"moveId": "FLY", "uses": 18546}, {"moveId": "HEAT_WAVE", "uses": 2336}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 50.5}, {"speciesId": "vigoroth", "speciesName": "Vigoroth", "rating": 556, "matchups": [{"opponent": "porygon2_shadow", "rating": 851, "opRating": 148}, {"opponent": "rapidash", "rating": 691, "opRating": 308}, {"opponent": "drampa", "rating": 662, "opRating": 337}, {"opponent": "<PERSON>ras", "rating": 616, "opRating": 383}, {"opponent": "typhlosion_shadow", "rating": 543, "opRating": 456}], "counters": [{"opponent": "skeledirge", "rating": 147}, {"opponent": "tentacruel", "rating": 236}, {"opponent": "virizion", "rating": 296}, {"opponent": "victree<PERSON>_shadow", "rating": 320}, {"opponent": "bellibolt", "rating": 382}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 38718}, {"moveId": "SCRATCH", "uses": 24282}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21587}, {"moveId": "ROCK_SLIDE", "uses": 15378}, {"moveId": "BRICK_BREAK", "uses": 11895}, {"moveId": "BULLDOZE", "uses": 8721}, {"moveId": "RETURN", "uses": 5358}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 50.5}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 635, "matchups": [{"opponent": "samu<PERSON>t", "rating": 774, "opRating": 225}, {"opponent": "feraligatr", "rating": 716, "opRating": 283}, {"opponent": "<PERSON>ras", "rating": 589, "opRating": 410}, {"opponent": "typhlosion_shadow", "rating": 558, "opRating": 441}, {"opponent": "golisopod", "rating": 537, "opRating": 462}], "counters": [{"opponent": "talonflame", "rating": 80}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "virizion", "rating": 163}, {"opponent": "cradily", "rating": 319}, {"opponent": "bellibolt", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24046}, {"moveId": "ICE_FANG", "uses": 19586}, {"moveId": "LICK", "uses": 15002}, {"moveId": "TAKE_DOWN", "uses": 4387}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32859}, {"moveId": "CRUNCH", "uses": 20490}, {"moveId": "PLAY_ROUGH", "uses": 9638}]}, "moveset": ["SAND_ATTACK", "WILD_CHARGE", "CRUNCH"], "score": 50.3}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 628, "matchups": [{"opponent": "jellicent", "rating": 860, "opRating": 139}, {"opponent": "feraligatr", "rating": 855, "opRating": 144}, {"opponent": "stunfisk", "rating": 764, "opRating": 235}, {"opponent": "samu<PERSON>t", "rating": 758, "opRating": 241}, {"opponent": "<PERSON>ras", "rating": 525, "opRating": 474}], "counters": [{"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "golisopod", "rating": 145}, {"opponent": "virizion", "rating": 275}, {"opponent": "skeledirge", "rating": 302}, {"opponent": "tentacruel", "rating": 346}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 36537}, {"moveId": "INFESTATION", "uses": 26463}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18457}, {"moveId": "ROCK_SLIDE", "uses": 15367}, {"moveId": "SLUDGE_BOMB", "uses": 11478}, {"moveId": "ANCIENT_POWER", "uses": 7429}, {"moveId": "RETURN", "uses": 6396}, {"moveId": "SOLAR_BEAM", "uses": 3907}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 50.2}, {"speciesId": "heracross", "speciesName": "Heracross", "rating": 622, "matchups": [{"opponent": "cradily", "rating": 625, "opRating": 375}, {"opponent": "virizion", "rating": 614, "opRating": 385}, {"opponent": "golisopod", "rating": 594, "opRating": 405}, {"opponent": "bellibolt", "rating": 584, "opRating": 415}, {"opponent": "<PERSON>ras", "rating": 550, "opRating": 449}], "counters": [{"opponent": "skeledirge", "rating": 102}, {"opponent": "zapdos", "rating": 153}, {"opponent": "talonflame", "rating": 181}, {"opponent": "victree<PERSON>_shadow", "rating": 233}, {"opponent": "tentacruel", "rating": 319}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43934}, {"moveId": "STRUGGLE_BUG", "uses": 19066}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 19143}, {"moveId": "UPPER_HAND", "uses": 17381}, {"moveId": "MEGAHORN", "uses": 10779}, {"moveId": "ROCK_BLAST", "uses": 9952}, {"moveId": "EARTHQUAKE", "uses": 5687}]}, "moveset": ["COUNTER", "UPPER_HAND", "MEGAHORN"], "score": 50.1}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 628, "matchups": [{"opponent": "venusaur", "rating": 853, "opRating": 146}, {"opponent": "forretress", "rating": 834, "opRating": 165}, {"opponent": "virizion", "rating": 652, "opRating": 347}, {"opponent": "typhlosion_shadow", "rating": 538, "opRating": 461}, {"opponent": "victree<PERSON>_shadow", "rating": 535, "opRating": 464}], "counters": [{"opponent": "feraligatr", "rating": 157}, {"opponent": "samu<PERSON>t", "rating": 162}, {"opponent": "golisopod", "rating": 251}, {"opponent": "<PERSON>ras", "rating": 294}, {"opponent": "tentacruel", "rating": 304}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33761}, {"moveId": "COUNTER", "uses": 29239}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14686}, {"moveId": "BRAVE_BIRD", "uses": 13181}, {"moveId": "BLAZE_KICK", "uses": 10766}, {"moveId": "STONE_EDGE", "uses": 9939}, {"moveId": "FOCUS_BLAST", "uses": 7514}, {"moveId": "RETURN", "uses": 3785}, {"moveId": "OVERHEAT", "uses": 3108}]}, "moveset": ["FIRE_SPIN", "BLAZE_KICK", "BLAST_BURN"], "score": 50}, {"speciesId": "jumpluff_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 630, "matchups": [{"opponent": "virizion", "rating": 905, "opRating": 94}, {"opponent": "gastrodon", "rating": 795, "opRating": 204}, {"opponent": "victree<PERSON>_shadow", "rating": 667, "opRating": 332}, {"opponent": "golisopod", "rating": 615, "opRating": 384}, {"opponent": "jellicent", "rating": 551, "opRating": 448}], "counters": [{"opponent": "cradily", "rating": 216}, {"opponent": "<PERSON>ras", "rating": 246}, {"opponent": "bellibolt", "rating": 265}, {"opponent": "tentacruel", "rating": 272}, {"opponent": "skeledirge", "rating": 297}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 24995}, {"moveId": "BULLET_SEED", "uses": 21075}, {"moveId": "INFESTATION", "uses": 16926}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 20155}, {"moveId": "AERIAL_ACE", "uses": 18236}, {"moveId": "ENERGY_BALL", "uses": 13056}, {"moveId": "DAZZLING_GLEAM", "uses": 7573}, {"moveId": "SOLAR_BEAM", "uses": 3762}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FAIRY_WIND", "ACROBATICS", "AERIAL_ACE"], "score": 50}, {"speciesId": "wailord_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 619, "matchups": [{"opponent": "talonflame", "rating": 845, "opRating": 154}, {"opponent": "skeledirge", "rating": 843, "opRating": 156}, {"opponent": "virizion", "rating": 666, "opRating": 333}, {"opponent": "<PERSON>ras", "rating": 513, "opRating": 486}, {"opponent": "golisopod", "rating": 508, "opRating": 491}], "counters": [{"opponent": "toxtricity", "rating": 161}, {"opponent": "victree<PERSON>_shadow", "rating": 164}, {"opponent": "cradily", "rating": 263}, {"opponent": "tentacruel", "rating": 275}, {"opponent": "bellibolt", "rating": 292}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 36414}, {"moveId": "WATER_GUN", "uses": 21232}, {"moveId": "ZEN_HEADBUTT", "uses": 5424}], "chargedMoves": [{"moveId": "SURF", "uses": 27374}, {"moveId": "SCALD", "uses": 13526}, {"moveId": "BLIZZARD", "uses": 12705}, {"moveId": "HYPER_BEAM", "uses": 9269}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SCALD", "BLIZZARD"], "score": 49.8}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 554, "matchups": [{"opponent": "jellicent", "rating": 693, "opRating": 306}, {"opponent": "feraligatr", "rating": 662, "opRating": 337}, {"opponent": "swampert", "rating": 571, "opRating": 428}, {"opponent": "tentacruel", "rating": 509, "opRating": 490}, {"opponent": "golisopod", "rating": 503, "opRating": 496}], "counters": [{"opponent": "<PERSON>ras", "rating": 196}, {"opponent": "victree<PERSON>_shadow", "rating": 254}, {"opponent": "cradily", "rating": 313}, {"opponent": "talonflame", "rating": 372}, {"opponent": "bellibolt", "rating": 450}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 39228}, {"moveId": "SCRATCH", "uses": 23772}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 26954}, {"moveId": "RETURN", "uses": 15971}, {"moveId": "LOW_SWEEP", "uses": 13832}, {"moveId": "HYPER_BEAM", "uses": 6194}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 49.7}, {"speciesId": "omastar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "talonflame", "rating": 912, "opRating": 87}, {"opponent": "skeledirge", "rating": 898, "opRating": 101}, {"opponent": "typhlosion_shadow", "rating": 891, "opRating": 108}, {"opponent": "tentacruel", "rating": 601, "opRating": 398}, {"opponent": "<PERSON>ras", "rating": 580, "opRating": 419}], "counters": [{"opponent": "virizion", "rating": 139}, {"opponent": "gastrodon", "rating": 209}, {"opponent": "cradily", "rating": 227}, {"opponent": "swampert", "rating": 234}, {"opponent": "bellibolt", "rating": 307}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 22682}, {"moveId": "MUD_SHOT", "uses": 21642}, {"moveId": "WATER_GUN", "uses": 18645}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 22281}, {"moveId": "ROCK_BLAST", "uses": 21147}, {"moveId": "ANCIENT_POWER", "uses": 10537}, {"moveId": "HYDRO_PUMP", "uses": 8994}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 49.7}, {"speciesId": "tapu_bulu", "speciesName": "Tapu Bulu", "rating": 592, "matchups": [{"opponent": "gastrodon", "rating": 833, "opRating": 166}, {"opponent": "feraligatr", "rating": 686, "opRating": 313}, {"opponent": "stunfisk", "rating": 686, "opRating": 313}, {"opponent": "bellibolt", "rating": 620, "opRating": 379}, {"opponent": "virizion", "rating": 589, "opRating": 410}], "counters": [{"opponent": "skeledirge", "rating": 216}, {"opponent": "victree<PERSON>_shadow", "rating": 251}, {"opponent": "cradily", "rating": 275}, {"opponent": "tentacruel", "rating": 284}, {"opponent": "typhlosion_shadow", "rating": 284}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 49459}, {"moveId": "ROCK_SMASH", "uses": 13541}], "chargedMoves": [{"moveId": "NATURES_MADNESS", "uses": 22167}, {"moveId": "GRASS_KNOT", "uses": 17689}, {"moveId": "MEGAHORN", "uses": 13304}, {"moveId": "DAZZLING_GLEAM", "uses": 6108}, {"moveId": "SOLAR_BEAM", "uses": 3782}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "NATURES_MADNESS"], "score": 49.7}, {"speciesId": "camerupt_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 598, "matchups": [{"opponent": "cradily", "rating": 831, "opRating": 168}, {"opponent": "tentacruel", "rating": 831, "opRating": 168}, {"opponent": "talonflame", "rating": 757, "opRating": 242}, {"opponent": "skeledirge", "rating": 732, "opRating": 267}, {"opponent": "bellibolt", "rating": 665, "opRating": 334}], "counters": [{"opponent": "jellicent", "rating": 135}, {"opponent": "swampert", "rating": 172}, {"opponent": "feraligatr", "rating": 179}, {"opponent": "samu<PERSON>t", "rating": 185}, {"opponent": "<PERSON>ras", "rating": 192}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36938}, {"moveId": "EMBER", "uses": 21358}, {"moveId": "ROCK_SMASH", "uses": 4732}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 23046}, {"moveId": "OVERHEAT", "uses": 21553}, {"moveId": "SOLAR_BEAM", "uses": 9871}, {"moveId": "EARTHQUAKE", "uses": 8408}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 49.4}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 542, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 817, "opRating": 182}, {"opponent": "skeledirge", "rating": 761, "opRating": 238}, {"opponent": "jellicent", "rating": 748, "opRating": 251}, {"opponent": "zapdos", "rating": 541, "opRating": 458}, {"opponent": "forretress", "rating": 504, "opRating": 495}], "counters": [{"opponent": "virizion", "rating": 221}, {"opponent": "talonflame", "rating": 264}, {"opponent": "bellibolt", "rating": 350}, {"opponent": "cradily", "rating": 363}, {"opponent": "<PERSON>ras", "rating": 365}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 44013}, {"moveId": "BITE", "uses": 18987}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 20625}, {"moveId": "BOOMBURST", "uses": 17378}, {"moveId": "DISARMING_VOICE", "uses": 16791}, {"moveId": "FIRE_BLAST", "uses": 8293}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "DISARMING_VOICE", "CRUNCH"], "score": 49.4}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 559, "matchups": [{"opponent": "skeledirge", "rating": 813, "opRating": 186}, {"opponent": "talonflame", "rating": 813, "opRating": 186}, {"opponent": "typhlosion_shadow", "rating": 767, "opRating": 232}, {"opponent": "swampert", "rating": 612, "opRating": 387}, {"opponent": "stunfisk", "rating": 525, "opRating": 474}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 284}, {"opponent": "tentacruel", "rating": 286}, {"opponent": "cradily", "rating": 313}, {"opponent": "bellibolt", "rating": 375}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 31879}, {"moveId": "WATERFALL", "uses": 31121}], "chargedMoves": [{"moveId": "AQUA_JET", "uses": 26572}, {"moveId": "SWIFT", "uses": 23224}, {"moveId": "LIQUIDATION", "uses": 9585}, {"moveId": "HYDRO_PUMP", "uses": 3654}]}, "moveset": ["WATER_GUN", "AQUA_JET", "SWIFT"], "score": 49.4}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 626, "matchups": [{"opponent": "swampert", "rating": 908, "opRating": 91}, {"opponent": "<PERSON>ras", "rating": 704, "opRating": 295}, {"opponent": "feraligatr", "rating": 674, "opRating": 325}, {"opponent": "bellibolt", "rating": 596}, {"opponent": "skeledirge", "rating": 502, "opRating": 497}], "counters": [{"opponent": "golisopod", "rating": 92}, {"opponent": "virizion", "rating": 169}, {"opponent": "talonflame", "rating": 315}, {"opponent": "cradily", "rating": 402}, {"opponent": "tentacruel", "rating": 488}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 21553}, {"moveId": "BULLET_SEED", "uses": 21358}, {"moveId": "FEINT_ATTACK", "uses": 13464}, {"moveId": "RAZOR_LEAF", "uses": 6630}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30020}, {"moveId": "FOUL_PLAY", "uses": 18242}, {"moveId": "HURRICANE", "uses": 8992}, {"moveId": "LEAF_TORNADO", "uses": 5716}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 49.3}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 536, "matchups": [{"opponent": "samu<PERSON>t", "rating": 778, "opRating": 221}, {"opponent": "staraptor_shadow", "rating": 753, "opRating": 246}, {"opponent": "talonflame", "rating": 731, "opRating": 268}, {"opponent": "jellicent", "rating": 699, "opRating": 300}, {"opponent": "feraligatr", "rating": 661, "opRating": 338}], "counters": [{"opponent": "stunfisk", "rating": 231}, {"opponent": "bellibolt", "rating": 245}, {"opponent": "cradily", "rating": 294}, {"opponent": "virizion", "rating": 345}, {"opponent": "skeledirge", "rating": 347}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 41247}, {"moveId": "SCRATCH", "uses": 21753}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 23870}, {"moveId": "THUNDER", "uses": 14610}, {"moveId": "RETURN", "uses": 14523}, {"moveId": "PLAY_ROUGH", "uses": 9954}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 49.2}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 609, "matchups": [{"opponent": "talonflame", "rating": 873, "opRating": 126}, {"opponent": "typhlosion_shadow", "rating": 832, "opRating": 167}, {"opponent": "skeledirge", "rating": 737, "opRating": 262}, {"opponent": "stunfisk", "rating": 667, "opRating": 332}, {"opponent": "forretress", "rating": 654, "opRating": 345}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 137}, {"opponent": "cradily", "rating": 241}, {"opponent": "virizion", "rating": 303}, {"opponent": "<PERSON>ras", "rating": 380}, {"opponent": "bellibolt", "rating": 442}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36192}, {"moveId": "WATER_GUN", "uses": 26808}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17081}, {"moveId": "MUD_BOMB", "uses": 16537}, {"moveId": "STONE_EDGE", "uses": 10683}, {"moveId": "SLUDGE_BOMB", "uses": 8384}, {"moveId": "RETURN", "uses": 4430}, {"moveId": "EARTHQUAKE", "uses": 3966}, {"moveId": "ACID_SPRAY", "uses": 1899}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 48.9}, {"speciesId": "masquerain", "speciesName": "Masquerain", "rating": 566, "matchups": [{"opponent": "venusaur", "rating": 807, "opRating": 192}, {"opponent": "gastrodon", "rating": 724, "opRating": 275}, {"opponent": "samu<PERSON>t", "rating": 637, "opRating": 362}, {"opponent": "golisopod", "rating": 631, "opRating": 368}, {"opponent": "swampert", "rating": 608, "opRating": 391}], "counters": [{"opponent": "skeledirge", "rating": 180}, {"opponent": "talonflame", "rating": 190}, {"opponent": "tentacruel", "rating": 281}, {"opponent": "cradily", "rating": 372}, {"opponent": "bellibolt", "rating": 412}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 32633}, {"moveId": "AIR_SLASH", "uses": 30367}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 27237}, {"moveId": "LUNGE", "uses": 18493}, {"moveId": "SILVER_WIND", "uses": 6667}, {"moveId": "OMINOUS_WIND", "uses": 5854}, {"moveId": "BUBBLE_BEAM", "uses": 4792}]}, "moveset": ["INFESTATION", "AIR_CUTTER", "LUNGE"], "score": 48.6}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 528, "matchups": [{"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 923, "opRating": 76}, {"opponent": "zangoose", "rating": 917, "opRating": 82}, {"opponent": "ursaring", "rating": 911, "opRating": 88}, {"opponent": "salazzle", "rating": 739, "opRating": 260}, {"opponent": "jellicent", "rating": 521, "opRating": 478}], "counters": [{"opponent": "virizion", "rating": 181}, {"opponent": "talonflame", "rating": 205}, {"opponent": "cradily", "rating": 277}, {"opponent": "victree<PERSON>_shadow", "rating": 305}, {"opponent": "bellibolt", "rating": 395}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 28695}, {"moveId": "BULLET_SEED", "uses": 26929}, {"moveId": "TAKE_DOWN", "uses": 7299}], "chargedMoves": [{"moveId": "SWIFT", "uses": 34664}, {"moveId": "CRUNCH", "uses": 19850}, {"moveId": "PLAY_ROUGH", "uses": 8509}]}, "moveset": ["MUD_SHOT", "SWIFT", "CRUNCH"], "score": 48.5}, {"speciesId": "meowscarada", "speciesName": "Meowscarada", "rating": 631, "matchups": [{"opponent": "<PERSON>ras", "rating": 926, "opRating": 73}, {"opponent": "swampert", "rating": 916, "opRating": 83}, {"opponent": "jellicent", "rating": 903, "opRating": 96}, {"opponent": "feraligatr", "rating": 863, "opRating": 136}, {"opponent": "bellibolt", "rating": 650, "opRating": 350}], "counters": [{"opponent": "virizion", "rating": 84}, {"opponent": "talonflame", "rating": 250}, {"opponent": "golisopod", "rating": 290}, {"opponent": "cradily", "rating": 333}, {"opponent": "skeledirge", "rating": 347}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 44612}, {"moveId": "CHARM", "uses": 18388}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 22127}, {"moveId": "FRENZY_PLANT", "uses": 19531}, {"moveId": "FLOWER_TRICK", "uses": 7705}, {"moveId": "GRASS_KNOT", "uses": 5237}, {"moveId": "PLAY_ROUGH", "uses": 4684}, {"moveId": "ENERGY_BALL", "uses": 3824}]}, "moveset": ["LEAFAGE", "NIGHT_SLASH", "FRENZY_PLANT"], "score": 48.4}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 731, "matchups": [{"opponent": "forretress", "rating": 912, "opRating": 87}, {"opponent": "talonflame", "rating": 845, "opRating": 154}, {"opponent": "typhlosion_shadow", "rating": 812, "opRating": 187}, {"opponent": "virizion", "rating": 695, "opRating": 304}, {"opponent": "golisopod", "rating": 508, "opRating": 491}], "counters": [{"opponent": "gastrodon", "rating": 58}, {"opponent": "swampert", "rating": 87}, {"opponent": "victree<PERSON>_shadow", "rating": 134}, {"opponent": "cradily", "rating": 180}, {"opponent": "feraligatr", "rating": 191}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 38473}, {"moveId": "ASTONISH", "uses": 24527}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 26503}, {"moveId": "THUNDERBOLT", "uses": 25416}, {"moveId": "THUNDER", "uses": 11082}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OVERHEAT"], "score": 48.4}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 644, "matchups": [{"opponent": "venusaur", "rating": 922, "opRating": 77}, {"opponent": "victree<PERSON>_shadow", "rating": 888, "opRating": 111}, {"opponent": "forretress", "rating": 831, "opRating": 168}, {"opponent": "cradily", "rating": 638, "opRating": 361}, {"opponent": "lickilicky", "rating": 527, "opRating": 472}], "counters": [{"opponent": "gastrodon", "rating": 87}, {"opponent": "feraligatr", "rating": 179}, {"opponent": "tentacruel", "rating": 248}, {"opponent": "virizion", "rating": 293}, {"opponent": "skeledirge", "rating": 308}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 36598}, {"moveId": "BUG_BITE", "uses": 26402}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 20426}, {"moveId": "STONE_EDGE", "uses": 14529}, {"moveId": "EARTH_POWER", "uses": 12344}, {"moveId": "IRON_HEAD", "uses": 7245}, {"moveId": "FLAMETHROWER", "uses": 5565}, {"moveId": "FIRE_BLAST", "uses": 2966}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 48.2}, {"speciesId": "hunt<PERSON>", "speciesName": "Huntail", "rating": 558, "matchups": [{"opponent": "talonflame", "rating": 844, "opRating": 155}, {"opponent": "skeledirge", "rating": 837, "opRating": 162}, {"opponent": "typhlosion_shadow", "rating": 799, "opRating": 200}, {"opponent": "crustle", "rating": 768, "opRating": 231}, {"opponent": "stunfisk", "rating": 511, "opRating": 488}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 251}, {"opponent": "<PERSON>ras", "rating": 268}, {"opponent": "cradily", "rating": 280}, {"opponent": "bellibolt", "rating": 310}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39580}, {"moveId": "BITE", "uses": 23420}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 31306}, {"moveId": "CRUNCH", "uses": 18294}, {"moveId": "ICE_BEAM", "uses": 13363}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "CRUNCH"], "score": 48.2}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 599, "matchups": [{"opponent": "feraligatr", "rating": 829, "opRating": 170}, {"opponent": "golisopod", "rating": 662, "opRating": 337}, {"opponent": "talonflame", "rating": 608, "opRating": 391}, {"opponent": "typhlosion_shadow", "rating": 529, "opRating": 470}, {"opponent": "tentacruel", "rating": 512, "opRating": 487}], "counters": [{"opponent": "cradily", "rating": 180}, {"opponent": "virizion", "rating": 212}, {"opponent": "victree<PERSON>_shadow", "rating": 242}, {"opponent": "bellibolt", "rating": 312}, {"opponent": "skeledirge", "rating": 366}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34373}, {"moveId": "ASTONISH", "uses": 28627}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 32042}, {"moveId": "OMINOUS_WIND", "uses": 17038}, {"moveId": "THUNDER", "uses": 13912}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 48.2}, {"speciesId": "carracosta_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 539, "matchups": [{"opponent": "talonflame", "rating": 931, "opRating": 68}, {"opponent": "typhlosion_shadow", "rating": 908, "opRating": 91}, {"opponent": "skeledirge", "rating": 882, "opRating": 117}, {"opponent": "tentacruel", "rating": 601, "opRating": 398}, {"opponent": "<PERSON>ras", "rating": 591, "opRating": 408}], "counters": [{"opponent": "virizion", "rating": 169}, {"opponent": "gastrodon", "rating": 172}, {"opponent": "stunfisk", "rating": 177}, {"opponent": "cradily", "rating": 238}, {"opponent": "bellibolt", "rating": 265}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 34653}, {"moveId": "WATER_GUN", "uses": 28347}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 19882}, {"moveId": "BODY_SLAM", "uses": 16680}, {"moveId": "LIQUIDATION", "uses": 15028}, {"moveId": "SURF", "uses": 11355}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ANCIENT_POWER", "LIQUIDATION"], "score": 47.8}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 688, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 771, "opRating": 228}, {"opponent": "cradily", "rating": 682, "opRating": 317}, {"opponent": "golisopod", "rating": 596, "opRating": 403}, {"opponent": "samu<PERSON>t", "rating": 589, "opRating": 410}, {"opponent": "feraligatr", "rating": 546, "opRating": 453}], "counters": [{"opponent": "typhlosion_shadow", "rating": 112}, {"opponent": "skeledirge", "rating": 113}, {"opponent": "talonflame", "rating": 122}, {"opponent": "turtonator", "rating": 137}, {"opponent": "virizion", "rating": 351}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 35334}, {"moveId": "COUNTER", "uses": 27666}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 16995}, {"moveId": "MEGAHORN", "uses": 16055}, {"moveId": "AERIAL_ACE", "uses": 13694}, {"moveId": "RAZOR_SHELL", "uses": 8908}, {"moveId": "RETURN", "uses": 5384}, {"moveId": "ACID_SPRAY", "uses": 2033}]}, "moveset": ["BUG_BITE", "RAZOR_SHELL", "DRILL_RUN"], "score": 47.8}, {"speciesId": "quaquaval", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 672, "matchups": [{"opponent": "skeledirge", "rating": 842, "opRating": 157}, {"opponent": "talonflame", "rating": 830, "opRating": 169}, {"opponent": "typhlosion_shadow", "rating": 791, "opRating": 208}, {"opponent": "golisopod", "rating": 666, "opRating": 333}, {"opponent": "virizion", "rating": 644, "opRating": 355}], "counters": [{"opponent": "<PERSON>ras", "rating": 123}, {"opponent": "victree<PERSON>_shadow", "rating": 245}, {"opponent": "bellibolt", "rating": 260}, {"opponent": "cradily", "rating": 261}, {"opponent": "tentacruel", "rating": 281}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34499}, {"moveId": "WATER_GUN", "uses": 28501}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20617}, {"moveId": "HYDRO_CANNON", "uses": 15902}, {"moveId": "AERIAL_ACE", "uses": 10058}, {"moveId": "AQUA_JET", "uses": 6084}, {"moveId": "AQUA_STEP", "uses": 5966}, {"moveId": "LIQUIDATION", "uses": 4376}]}, "moveset": ["WING_ATTACK", "CLOSE_COMBAT", "HYDRO_CANNON"], "score": 47.8}, {"speciesId": "rotom", "speciesName": "Rotom", "rating": 546, "matchups": [{"opponent": "skeledirge", "rating": 728, "opRating": 271}, {"opponent": "tentacruel", "rating": 586, "opRating": 413}, {"opponent": "virizion", "rating": 586, "opRating": 413}, {"opponent": "golisopod", "rating": 547, "opRating": 452}, {"opponent": "<PERSON>ras", "rating": 503, "opRating": 496}], "counters": [{"opponent": "lickilicky", "rating": 69}, {"opponent": "drampa", "rating": 154}, {"opponent": "feraligatr", "rating": 245}, {"opponent": "bellibolt", "rating": 305}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 31867}, {"moveId": "ASTONISH", "uses": 31133}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 29296}, {"moveId": "OMINOUS_WIND", "uses": 20937}, {"moveId": "THUNDER", "uses": 12726}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 47.8}, {"speciesId": "appletun", "speciesName": "Appletun", "rating": 604, "matchups": [{"opponent": "gastrodon", "rating": 791, "opRating": 208}, {"opponent": "swampert", "rating": 703, "opRating": 296}, {"opponent": "stunfisk", "rating": 699, "opRating": 300}, {"opponent": "jellicent", "rating": 668, "opRating": 331}, {"opponent": "bellibolt", "rating": 575, "opRating": 424}], "counters": [{"opponent": "golisopod", "rating": 177}, {"opponent": "feraligatr", "rating": 204}, {"opponent": "talonflame", "rating": 217}, {"opponent": "victree<PERSON>_shadow", "rating": 254}, {"opponent": "cradily", "rating": 358}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 31750}, {"moveId": "BULLET_SEED", "uses": 31250}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 24187}, {"moveId": "SEED_BOMB", "uses": 21213}, {"moveId": "ENERGY_BALL", "uses": 9527}, {"moveId": "DRAGON_PULSE", "uses": 8059}]}, "moveset": ["ASTONISH", "OUTRAGE", "SEED_BOMB"], "score": 47.6}, {"speciesId": "graveler_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 636, "matchups": [{"opponent": "skeledirge", "rating": 792, "opRating": 207}, {"opponent": "talonflame", "rating": 792, "opRating": 207}, {"opponent": "golisopod", "rating": 774, "opRating": 225}, {"opponent": "<PERSON>ras", "rating": 762, "opRating": 237}, {"opponent": "typhlosion_shadow", "rating": 744, "opRating": 255}], "counters": [{"opponent": "stunfisk", "rating": 30}, {"opponent": "gastrodon", "rating": 66}, {"opponent": "virizion", "rating": 75}, {"opponent": "cradily", "rating": 283}, {"opponent": "bellibolt", "rating": 307}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 38646}, {"moveId": "ROCK_THROW", "uses": 24354}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 24445}, {"moveId": "ROCK_BLAST", "uses": 22089}, {"moveId": "THUNDERBOLT", "uses": 16378}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "STONE_EDGE", "ROCK_BLAST"], "score": 47.6}, {"speciesId": "pignite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 555, "matchups": [{"opponent": "genesect_shock", "rating": 922, "opRating": 77}, {"opponent": "forretress", "rating": 809, "opRating": 190}, {"opponent": "crustle", "rating": 655, "opRating": 344}, {"opponent": "serperior", "rating": 572, "opRating": 427}, {"opponent": "venusaur", "rating": 524, "opRating": 475}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "skeledirge", "rating": 200}, {"opponent": "typhlosion_shadow", "rating": 261}, {"opponent": "tentacruel", "rating": 316}, {"opponent": "cradily", "rating": 402}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 42002}, {"moveId": "TACKLE", "uses": 20998}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 32076}, {"moveId": "FLAME_CHARGE", "uses": 21732}, {"moveId": "FLAMETHROWER", "uses": 9076}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 47.6}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 633, "matchups": [{"opponent": "jellicent", "rating": 857, "opRating": 142}, {"opponent": "feraligatr", "rating": 826, "opRating": 173}, {"opponent": "stunfisk", "rating": 826, "opRating": 173}, {"opponent": "<PERSON>ras", "rating": 678, "opRating": 321}, {"opponent": "bellibolt", "rating": 539, "opRating": 460}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 83}, {"opponent": "golisopod", "rating": 180}, {"opponent": "virizion", "rating": 269}, {"opponent": "cradily", "rating": 319}, {"opponent": "tentacruel", "rating": 378}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 36733}, {"moveId": "INFESTATION", "uses": 26267}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20365}, {"moveId": "ROCK_SLIDE", "uses": 17147}, {"moveId": "SLUDGE_BOMB", "uses": 12974}, {"moveId": "ANCIENT_POWER", "uses": 8146}, {"moveId": "SOLAR_BEAM", "uses": 4263}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 47.4}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 596, "matchups": [{"opponent": "stunfisk", "rating": 790, "opRating": 209}, {"opponent": "typhlosion_shadow", "rating": 657, "opRating": 342}, {"opponent": "feraligatr", "rating": 589, "opRating": 410}, {"opponent": "bellibolt", "rating": 581, "opRating": 418}, {"opponent": "tentacruel", "rating": 536, "opRating": 463}], "counters": [{"opponent": "jellicent", "rating": 91}, {"opponent": "skeledirge", "rating": 147}, {"opponent": "<PERSON>ras", "rating": 170}, {"opponent": "samu<PERSON>t", "rating": 174}, {"opponent": "victree<PERSON>_shadow", "rating": 230}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 47526}, {"moveId": "ROCK_SMASH", "uses": 15474}], "chargedMoves": [{"moveId": "SWIFT", "uses": 12127}, {"moveId": "TRAILBLAZE", "uses": 8851}, {"moveId": "THUNDER_PUNCH", "uses": 8380}, {"moveId": "FIRE_PUNCH", "uses": 8101}, {"moveId": "ICE_PUNCH", "uses": 8024}, {"moveId": "HIGH_HORSEPOWER", "uses": 7893}, {"moveId": "AERIAL_ACE", "uses": 7561}, {"moveId": "RETURN", "uses": 2038}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 47.3}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 618, "matchups": [{"opponent": "forretress", "rating": 855, "opRating": 144}, {"opponent": "zapdos", "rating": 599, "opRating": 400}, {"opponent": "victree<PERSON>_shadow", "rating": 571, "opRating": 428}, {"opponent": "talonflame", "rating": 517, "opRating": 482}, {"opponent": "golisopod", "rating": 508, "opRating": 491}], "counters": [{"opponent": "feraligatr", "rating": 135}, {"opponent": "cradily", "rating": 205}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "tentacruel", "rating": 295}, {"opponent": "<PERSON>ras", "rating": 309}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39486}, {"moveId": "LICK", "uses": 23514}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 27983}, {"moveId": "FLAMETHROWER", "uses": 25174}, {"moveId": "POWER_UP_PUNCH", "uses": 9872}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 47.2}, {"speciesId": "thundurus_therian", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Therian)", "rating": 658, "matchups": [{"opponent": "golisopod", "rating": 813, "opRating": 186}, {"opponent": "feraligatr", "rating": 794, "opRating": 205}, {"opponent": "tentacruel", "rating": 750, "opRating": 250}, {"opponent": "talonflame", "rating": 712, "opRating": 287}, {"opponent": "typhlosion_shadow", "rating": 634, "opRating": 365}], "counters": [{"opponent": "stunfisk", "rating": 86}, {"opponent": "bellibolt", "rating": 122}, {"opponent": "lickilicky", "rating": 145}, {"opponent": "cradily", "rating": 186}, {"opponent": "virizion", "rating": 230}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 47412}, {"moveId": "BITE", "uses": 15588}], "chargedMoves": [{"moveId": "WILDBOLT_STORM", "uses": 24939}, {"moveId": "FOCUS_BLAST", "uses": 12159}, {"moveId": "SLUDGE_WAVE", "uses": 10906}, {"moveId": "THUNDERBOLT", "uses": 7985}, {"moveId": "THUNDER", "uses": 6999}]}, "moveset": ["VOLT_SWITCH", "WILDBOLT_STORM", "FOCUS_BLAST"], "score": 47.2}, {"speciesId": "toxapex", "speciesName": "Toxapex", "rating": 556, "matchups": [{"opponent": "virizion", "rating": 724, "opRating": 275}, {"opponent": "typhlosion_shadow", "rating": 606, "opRating": 393}, {"opponent": "samu<PERSON>t", "rating": 606, "opRating": 393}, {"opponent": "crustle", "rating": 578, "opRating": 421}, {"opponent": "golisopod", "rating": 559, "opRating": 440}], "counters": [{"opponent": "forretress", "rating": 165}, {"opponent": "<PERSON>ras", "rating": 311}, {"opponent": "bellibolt", "rating": 332}, {"opponent": "tentacruel", "rating": 334}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 42863}, {"moveId": "BITE", "uses": 20137}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 28046}, {"moveId": "BRINE", "uses": 24018}, {"moveId": "GUNK_SHOT", "uses": 11023}]}, "moveset": ["POISON_JAB", "BRINE", "SLUDGE_WAVE"], "score": 47.2}, {"speciesId": "genesect_shock", "speciesName": "Genesect (Shock)", "rating": 756, "matchups": [{"opponent": "tentacruel", "rating": 901, "opRating": 98}, {"opponent": "victree<PERSON>_shadow", "rating": 901, "opRating": 98}, {"opponent": "feraligatr", "rating": 799, "opRating": 200}, {"opponent": "cradily", "rating": 657}, {"opponent": "<PERSON>ras", "rating": 566, "opRating": 433}], "counters": [{"opponent": "skeledirge", "rating": 58}, {"opponent": "talonflame", "rating": 62}, {"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "bellibolt", "rating": 365}, {"opponent": "virizion", "rating": 396}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 42746}, {"moveId": "METAL_CLAW", "uses": 20254}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25245}, {"moveId": "TECHNO_BLAST_SHOCK", "uses": 20102}, {"moveId": "MAGNET_BOMB", "uses": 13600}, {"moveId": "ZAP_CANNON", "uses": 4047}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_SHOCK"], "score": 47}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 589, "matchups": [{"opponent": "venusaur", "rating": 884, "opRating": 115}, {"opponent": "virizion", "rating": 800, "opRating": 200}, {"opponent": "golisopod", "rating": 656, "opRating": 343}, {"opponent": "victree<PERSON>_shadow", "rating": 578, "opRating": 421}, {"opponent": "gastrodon", "rating": 578, "opRating": 421}], "counters": [{"opponent": "bellibolt", "rating": 202}, {"opponent": "cradily", "rating": 244}, {"opponent": "<PERSON>ras", "rating": 266}, {"opponent": "skeledirge", "rating": 322}, {"opponent": "tentacruel", "rating": 328}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 55793}, {"moveId": "POUND", "uses": 7207}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 39602}, {"moveId": "AERIAL_ACE", "uses": 14976}, {"moveId": "HURRICANE", "uses": 8494}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 47}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 629, "matchups": [{"opponent": "talonflame", "rating": 629, "opRating": 370}, {"opponent": "feraligatr", "rating": 604, "opRating": 395}, {"opponent": "tentacruel", "rating": 554, "opRating": 445}, {"opponent": "golisopod", "rating": 537, "opRating": 462}, {"opponent": "<PERSON>ras", "rating": 525, "opRating": 475}], "counters": [{"opponent": "gastrodon", "rating": 87}, {"opponent": "victree<PERSON>_shadow", "rating": 134}, {"opponent": "virizion", "rating": 200}, {"opponent": "bellibolt", "rating": 362}, {"opponent": "cradily", "rating": 455}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 36596}, {"moveId": "ASTONISH", "uses": 26404}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27377}, {"moveId": "BLIZZARD", "uses": 23826}, {"moveId": "THUNDER", "uses": 11803}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 47}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 663, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 759, "opRating": 240}, {"opponent": "cradily", "rating": 686, "opRating": 313}, {"opponent": "golisopod", "rating": 604, "opRating": 395}, {"opponent": "<PERSON>ras", "rating": 573, "opRating": 426}, {"opponent": "feraligatr", "rating": 565, "opRating": 434}], "counters": [{"opponent": "turtonator", "rating": 109}, {"opponent": "skeledirge", "rating": 113}, {"opponent": "talonflame", "rating": 122}, {"opponent": "typhlosion_shadow", "rating": 132}, {"opponent": "bellibolt", "rating": 415}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 37419}, {"moveId": "METAL_CLAW", "uses": 25581}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 30335}, {"moveId": "STONE_EDGE", "uses": 22019}, {"moveId": "IRON_HEAD", "uses": 10677}]}, "moveset": ["BUG_BITE", "X_SCISSOR", "STONE_EDGE"], "score": 46.8}, {"speciesId": "escavalier_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 696, "matchups": [{"opponent": "tentacruel", "rating": 857, "opRating": 142}, {"opponent": "victree<PERSON>_shadow", "rating": 725, "opRating": 275}, {"opponent": "bellibolt", "rating": 696, "opRating": 303}, {"opponent": "cradily", "rating": 664, "opRating": 335}, {"opponent": "<PERSON>ras", "rating": 532, "opRating": 467}], "counters": [{"opponent": "skeledirge", "rating": 63}, {"opponent": "talonflame", "rating": 86}, {"opponent": "typhlosion_shadow", "rating": 149}, {"opponent": "jellicent", "rating": 193}, {"opponent": "virizion", "rating": 306}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 35169}, {"moveId": "COUNTER", "uses": 27831}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 18471}, {"moveId": "MEGAHORN", "uses": 17582}, {"moveId": "AERIAL_ACE", "uses": 14945}, {"moveId": "RAZOR_SHELL", "uses": 9719}, {"moveId": "ACID_SPRAY", "uses": 2216}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUG_BITE", "DRILL_RUN", "RAZOR_SHELL"], "score": 46.8}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 587, "matchups": [{"opponent": "golisopod", "rating": 716, "opRating": 283}, {"opponent": "jellicent", "rating": 619, "opRating": 380}, {"opponent": "virizion", "rating": 594, "opRating": 405}, {"opponent": "swampert", "rating": 564, "opRating": 435}, {"opponent": "victree<PERSON>_shadow", "rating": 562, "opRating": 437}], "counters": [{"opponent": "zapdos", "rating": 113}, {"opponent": "bellibolt", "rating": 190}, {"opponent": "cradily", "rating": 238}, {"opponent": "<PERSON>ras", "rating": 296}, {"opponent": "tentacruel", "rating": 316}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 36696}, {"moveId": "EXTRASENSORY", "uses": 26304}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 23908}, {"moveId": "NIGHT_SHADE", "uses": 18491}, {"moveId": "PSYCHIC", "uses": 12078}, {"moveId": "SHADOW_BALL", "uses": 8355}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "NIGHT_SHADE"], "score": 46.6}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 712, "matchups": [{"opponent": "golisopod", "rating": 872, "opRating": 127}, {"opponent": "virizion", "rating": 822, "opRating": 177}, {"opponent": "typhlosion_shadow", "rating": 649, "opRating": 350}, {"opponent": "swampert", "rating": 642, "opRating": 357}, {"opponent": "victree<PERSON>_shadow", "rating": 555, "opRating": 444}], "counters": [{"opponent": "cradily", "rating": 111}, {"opponent": "bellibolt", "rating": 150}, {"opponent": "feraligatr", "rating": 191}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "tentacruel", "rating": 352}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 17784}, {"moveId": "SAND_ATTACK", "uses": 16118}, {"moveId": "GUST", "uses": 15096}, {"moveId": "WING_ATTACK", "uses": 14004}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 19820}, {"moveId": "CLOSE_COMBAT", "uses": 18324}, {"moveId": "FLY", "uses": 16873}, {"moveId": "RETURN", "uses": 5927}, {"moveId": "HEAT_WAVE", "uses": 2056}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 46.2}, {"speciesId": "vigoroth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 577, "matchups": [{"opponent": "luxray_shadow", "rating": 854, "opRating": 145}, {"opponent": "drampa", "rating": 691, "opRating": 308}, {"opponent": "talonflame", "rating": 662, "opRating": 337}, {"opponent": "lickilicky", "rating": 543, "opRating": 456}, {"opponent": "cradily", "rating": 505, "opRating": 494}], "counters": [{"opponent": "skeledirge", "rating": 155}, {"opponent": "victree<PERSON>_shadow", "rating": 164}, {"opponent": "virizion", "rating": 166}, {"opponent": "tentacruel", "rating": 278}, {"opponent": "bellibolt", "rating": 327}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 39016}, {"moveId": "SCRATCH", "uses": 23984}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 23797}, {"moveId": "ROCK_SLIDE", "uses": 16728}, {"moveId": "BRICK_BREAK", "uses": 12934}, {"moveId": "BULLDOZE", "uses": 9507}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 46.1}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 552, "matchups": [{"opponent": "forretress", "rating": 845, "opRating": 154}, {"opponent": "roserade", "rating": 828, "opRating": 171}, {"opponent": "serperior", "rating": 691, "opRating": 308}, {"opponent": "venusaur", "rating": 677, "opRating": 322}, {"opponent": "victree<PERSON>_shadow", "rating": 570, "opRating": 429}], "counters": [{"opponent": "golisopod", "rating": 258}, {"opponent": "tentacruel", "rating": 260}, {"opponent": "bellibolt", "rating": 270}, {"opponent": "skeledirge", "rating": 280}, {"opponent": "cradily", "rating": 338}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 41193}, {"moveId": "TACKLE", "uses": 21807}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 30820}, {"moveId": "FOCUS_BLAST", "uses": 19282}, {"moveId": "FLAMETHROWER", "uses": 12822}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 46}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 598, "matchups": [{"opponent": "gastrodon", "rating": 847, "opRating": 152}, {"opponent": "stunfisk", "rating": 740, "opRating": 259}, {"opponent": "swampert", "rating": 655, "opRating": 344}, {"opponent": "jellicent", "rating": 554, "opRating": 445}, {"opponent": "bellibolt", "rating": 506, "opRating": 493}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 107}, {"opponent": "talonflame", "rating": 223}, {"opponent": "skeledirge", "rating": 252}, {"opponent": "<PERSON>ras", "rating": 331}, {"opponent": "tentacruel", "rating": 340}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 33554}, {"moveId": "MAGICAL_LEAF", "uses": 29446}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 17652}, {"moveId": "AURA_SPHERE", "uses": 14948}, {"moveId": "AERIAL_ACE", "uses": 14643}, {"moveId": "NIGHT_SHADE", "uses": 10633}, {"moveId": "ENERGY_BALL", "uses": 5120}]}, "moveset": ["MAGICAL_LEAF", "AURA_SPHERE", "AERIAL_ACE"], "score": 46}, {"speciesId": "rotom_fan", "speciesName": "<PERSON><PERSON><PERSON> (Fan)", "rating": 554, "matchups": [{"opponent": "jellicent", "rating": 641, "opRating": 358}, {"opponent": "golisopod", "rating": 575, "opRating": 425}, {"opponent": "tentacruel", "rating": 570, "opRating": 429}, {"opponent": "samu<PERSON>t", "rating": 537, "opRating": 462}, {"opponent": "skeledirge", "rating": 512, "opRating": 487}], "counters": [{"opponent": "drampa", "rating": 124}, {"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "cradily", "rating": 258}, {"opponent": "victree<PERSON>_shadow", "rating": 275}, {"opponent": "bellibolt", "rating": 295}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33694}, {"moveId": "AIR_SLASH", "uses": 29306}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 32040}, {"moveId": "OMINOUS_WIND", "uses": 17015}, {"moveId": "THUNDER", "uses": 13913}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 46}, {"speciesId": "sci<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 707, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 879, "opRating": 120}, {"opponent": "<PERSON>ras", "rating": 828, "opRating": 171}, {"opponent": "cradily", "rating": 638, "opRating": 361}, {"opponent": "tentacruel", "rating": 547, "opRating": 452}, {"opponent": "golisopod", "rating": 525, "opRating": 474}], "counters": [{"opponent": "typhlosion_shadow", "rating": 82}, {"opponent": "skeledirge", "rating": 86}, {"opponent": "talonflame", "rating": 92}, {"opponent": "feraligatr", "rating": 207}, {"opponent": "bellibolt", "rating": 300}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35744}, {"moveId": "BULLET_PUNCH", "uses": 27256}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 20457}, {"moveId": "NIGHT_SLASH", "uses": 19714}, {"moveId": "TRAILBLAZE", "uses": 14997}, {"moveId": "IRON_HEAD", "uses": 7805}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "TRAILBLAZE"], "score": 46}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 598, "matchups": [{"opponent": "tentacruel", "rating": 789, "opRating": 210}, {"opponent": "<PERSON>ras", "rating": 779, "opRating": 220}, {"opponent": "golisopod", "rating": 745, "opRating": 254}, {"opponent": "talonflame", "rating": 732, "opRating": 267}, {"opponent": "feraligatr", "rating": 591, "opRating": 408}], "counters": [{"opponent": "stunfisk", "rating": 95}, {"opponent": "victree<PERSON>_shadow", "rating": 137}, {"opponent": "virizion", "rating": 212}, {"opponent": "cradily", "rating": 227}, {"opponent": "bellibolt", "rating": 307}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 52049}, {"moveId": "LOW_KICK", "uses": 10951}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 29033}, {"moveId": "DISCHARGE", "uses": 14213}, {"moveId": "FLAME_CHARGE", "uses": 13519}, {"moveId": "RETURN", "uses": 6225}]}, "moveset": ["SPARK", "WILD_CHARGE", "DISCHARGE"], "score": 46}, {"speciesId": "camerupt", "speciesName": "Camerupt", "rating": 628, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 853, "opRating": 146}, {"opponent": "skeledirge", "rating": 770, "opRating": 229}, {"opponent": "typhlosion_shadow", "rating": 713, "opRating": 286}, {"opponent": "bellibolt", "rating": 707, "opRating": 292}, {"opponent": "virizion", "rating": 621, "opRating": 378}], "counters": [{"opponent": "jellicent", "rating": 114}, {"opponent": "swampert", "rating": 149}, {"opponent": "feraligatr", "rating": 154}, {"opponent": "<PERSON>ras", "rating": 166}, {"opponent": "golisopod", "rating": 230}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 35685}, {"moveId": "EMBER", "uses": 22087}, {"moveId": "ROCK_SMASH", "uses": 5266}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 20207}, {"moveId": "OVERHEAT", "uses": 18470}, {"moveId": "SOLAR_BEAM", "uses": 8537}, {"moveId": "RETURN", "uses": 8534}, {"moveId": "EARTHQUAKE", "uses": 7378}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 45.8}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 496, "matchups": [{"opponent": "manectric", "rating": 688, "opRating": 311}, {"opponent": "roserade", "rating": 656, "opRating": 343}, {"opponent": "ursaring_shadow", "rating": 593, "opRating": 406}, {"opponent": "jellicent", "rating": 586, "opRating": 413}, {"opponent": "drampa", "rating": 584, "opRating": 415}], "counters": [{"opponent": "typhlosion_shadow", "rating": 201}, {"opponent": "bellibolt", "rating": 247}, {"opponent": "cradily", "rating": 250}, {"opponent": "skeledirge", "rating": 302}, {"opponent": "<PERSON>ras", "rating": 333}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 35212}, {"moveId": "POUND", "uses": 27788}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 31545}, {"moveId": "DAZZLING_GLEAM", "uses": 11999}, {"moveId": "PSYCHIC", "uses": 10072}, {"moveId": "HYPER_BEAM", "uses": 9345}]}, "moveset": ["ZEN_HEADBUTT", "WILD_CHARGE", "DAZZLING_GLEAM"], "score": 45.7}, {"speciesId": "sealeo_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 520, "matchups": [{"opponent": "kleavor", "rating": 768, "opRating": 231}, {"opponent": "salazzle", "rating": 763, "opRating": 236}, {"opponent": "typhlosion_shadow", "rating": 739, "opRating": 260}, {"opponent": "turtonator", "rating": 626, "opRating": 373}, {"opponent": "swampert", "rating": 620, "opRating": 379}], "counters": [{"opponent": "virizion", "rating": 251}, {"opponent": "bellibolt", "rating": 265}, {"opponent": "tentacruel", "rating": 278}, {"opponent": "<PERSON>ras", "rating": 292}, {"opponent": "cradily", "rating": 300}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37058}, {"moveId": "WATER_GUN", "uses": 25942}], "chargedMoves": [{"moveId": "SURF", "uses": 23617}, {"moveId": "BODY_SLAM", "uses": 21703}, {"moveId": "AURORA_BEAM", "uses": 11606}, {"moveId": "WATER_PULSE", "uses": 5940}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "SURF", "BODY_SLAM"], "score": 45.7}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 600, "matchups": [{"opponent": "stunfisk", "rating": 731, "opRating": 268}, {"opponent": "cradily", "rating": 638, "opRating": 361}, {"opponent": "bellibolt", "rating": 634, "opRating": 365}, {"opponent": "virizion", "rating": 582, "opRating": 417}, {"opponent": "feraligatr", "rating": 559, "opRating": 440}], "counters": [{"opponent": "skeledirge", "rating": 119}, {"opponent": "talonflame", "rating": 190}, {"opponent": "golisopod", "rating": 202}, {"opponent": "victree<PERSON>_shadow", "rating": 218}, {"opponent": "tentacruel", "rating": 304}], "moves": {"fastMoves": [{"moveId": "FORCE_PALM", "uses": 24937}, {"moveId": "BULLET_SEED", "uses": 20258}, {"moveId": "COUNTER", "uses": 17771}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25821}, {"moveId": "GRASS_KNOT", "uses": 14400}, {"moveId": "SEED_BOMB", "uses": 11550}, {"moveId": "SLUDGE_BOMB", "uses": 11223}]}, "moveset": ["FORCE_PALM", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 45.6}, {"speciesId": "graveler_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 606, "matchups": [{"opponent": "zapdos", "rating": 885, "opRating": 114}, {"opponent": "skeledirge", "rating": 825, "opRating": 174}, {"opponent": "talonflame", "rating": 700, "opRating": 300}, {"opponent": "typhlosion_shadow", "rating": 662, "opRating": 337}, {"opponent": "crustle", "rating": 577, "opRating": 422}], "counters": [{"opponent": "gastrodon", "rating": 68}, {"opponent": "virizion", "rating": 78}, {"opponent": "swampert", "rating": 155}, {"opponent": "cradily", "rating": 244}, {"opponent": "bellibolt", "rating": 355}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 37828}, {"moveId": "ROCK_THROW", "uses": 25172}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 21585}, {"moveId": "ROCK_BLAST", "uses": 19376}, {"moveId": "THUNDERBOLT", "uses": 14578}, {"moveId": "RETURN", "uses": 7469}]}, "moveset": ["VOLT_SWITCH", "STONE_EDGE", "ROCK_BLAST"], "score": 45.6}, {"speciesId": "mantine", "speciesName": "<PERSON><PERSON>", "rating": 642, "matchups": [{"opponent": "virizion", "rating": 926, "opRating": 73}, {"opponent": "swampert", "rating": 687, "opRating": 312}, {"opponent": "samu<PERSON>t", "rating": 667, "opRating": 332}, {"opponent": "victree<PERSON>_shadow", "rating": 654, "opRating": 345}, {"opponent": "golisopod", "rating": 593, "opRating": 406}], "counters": [{"opponent": "zapdos", "rating": 113}, {"opponent": "bellibolt", "rating": 130}, {"opponent": "stunfisk", "rating": 149}, {"opponent": "cradily", "rating": 313}, {"opponent": "<PERSON>ras", "rating": 339}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 21812}, {"moveId": "BUBBLE", "uses": 21269}, {"moveId": "BULLET_SEED", "uses": 19881}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 29003}, {"moveId": "WATER_PULSE", "uses": 13481}, {"moveId": "ICE_BEAM", "uses": 12834}, {"moveId": "BUBBLE_BEAM", "uses": 7636}]}, "moveset": ["WING_ATTACK", "AERIAL_ACE", "ICE_BEAM"], "score": 45.4}, {"speciesId": "wigglytuff", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 544, "matchups": [{"opponent": "drampa", "rating": 865, "opRating": 134}, {"opponent": "jellicent", "rating": 625, "opRating": 375}, {"opponent": "virizion", "rating": 550, "opRating": 450}, {"opponent": "lickilicky", "rating": 542, "opRating": 457}, {"opponent": "blastoise", "rating": 521, "opRating": 478}], "counters": [{"opponent": "skeledirge", "rating": 180}, {"opponent": "victree<PERSON>_shadow", "rating": 197}, {"opponent": "tentacruel", "rating": 233}, {"opponent": "cradily", "rating": 319}, {"opponent": "bellibolt", "rating": 327}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 29914}, {"moveId": "FEINT_ATTACK", "uses": 28213}, {"moveId": "POUND", "uses": 4854}], "chargedMoves": [{"moveId": "SWIFT", "uses": 21697}, {"moveId": "DISARMING_VOICE", "uses": 13576}, {"moveId": "ICY_WIND", "uses": 12262}, {"moveId": "DAZZLING_GLEAM", "uses": 4997}, {"moveId": "ICE_BEAM", "uses": 4005}, {"moveId": "PLAY_ROUGH", "uses": 3618}, {"moveId": "HYPER_BEAM", "uses": 3005}]}, "moveset": ["CHARM", "DISARMING_VOICE", "ICY_WIND"], "score": 45.4}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "stunfisk", "rating": 756, "opRating": 243}, {"opponent": "gastrodon", "rating": 737, "opRating": 262}, {"opponent": "swampert", "rating": 692, "opRating": 307}, {"opponent": "virizion", "rating": 680, "opRating": 319}, {"opponent": "<PERSON>ras", "rating": 509, "opRating": 490}], "counters": [{"opponent": "golisopod", "rating": 216}, {"opponent": "typhlosion_shadow", "rating": 228}, {"opponent": "skeledirge", "rating": 236}, {"opponent": "feraligatr", "rating": 248}, {"opponent": "bellibolt", "rating": 335}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 26234}, {"moveId": "MAGICAL_LEAF", "uses": 23943}, {"moveId": "CHARGE_BEAM", "uses": 12810}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 16637}, {"moveId": "PSYCHIC", "uses": 14926}, {"moveId": "LEAF_STORM", "uses": 13075}, {"moveId": "DAZZLING_GLEAM", "uses": 11558}, {"moveId": "HYPER_BEAM", "uses": 6820}]}, "moveset": ["CONFUSION", "SEED_BOMB", "LEAF_STORM"], "score": 45.3}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 585, "matchups": [{"opponent": "forretress", "rating": 852, "opRating": 147}, {"opponent": "serperior", "rating": 702, "opRating": 297}, {"opponent": "venusaur", "rating": 630, "opRating": 369}, {"opponent": "victree<PERSON>_shadow", "rating": 601, "opRating": 398}, {"opponent": "crustle", "rating": 526, "opRating": 473}], "counters": [{"opponent": "swampert", "rating": 127}, {"opponent": "feraligatr", "rating": 128}, {"opponent": "tentacruel", "rating": 230}, {"opponent": "<PERSON>ras", "rating": 240}, {"opponent": "cradily", "rating": 266}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 47705}, {"moveId": "BITE", "uses": 15295}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 23267}, {"moveId": "FLAMETHROWER", "uses": 20864}, {"moveId": "DISARMING_VOICE", "uses": 18991}]}, "moveset": ["INCINERATE", "DISARMING_VOICE", "CRUNCH"], "score": 45.3}, {"speciesId": "genesect_chill", "speciesName": "Genesect (Chill)", "rating": 738, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 901, "opRating": 98}, {"opponent": "cradily", "rating": 685, "opRating": 314}, {"opponent": "virizion", "rating": 645, "opRating": 354}, {"opponent": "tentacruel", "rating": 602, "opRating": 397}, {"opponent": "golisopod", "rating": 547, "opRating": 452}], "counters": [{"opponent": "skeledirge", "rating": 58}, {"opponent": "talonflame", "rating": 62}, {"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "turtonator", "rating": 137}, {"opponent": "bellibolt", "rating": 365}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 42511}, {"moveId": "METAL_CLAW", "uses": 20489}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25498}, {"moveId": "TECHNO_BLAST_CHILL", "uses": 18206}, {"moveId": "MAGNET_BOMB", "uses": 14189}, {"moveId": "ICE_BEAM", "uses": 5123}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_CHILL"], "score": 45.3}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 585, "matchups": [{"opponent": "swampert", "rating": 935, "opRating": 64}, {"opponent": "stunfisk", "rating": 818, "opRating": 181}, {"opponent": "jellicent", "rating": 731, "opRating": 268}, {"opponent": "<PERSON>ras", "rating": 689, "opRating": 310}, {"opponent": "bellibolt", "rating": 541, "opRating": 458}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 161}, {"opponent": "skeledirge", "rating": 180}, {"opponent": "virizion", "rating": 248}, {"opponent": "talonflame", "rating": 252}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 25729}, {"moveId": "BULLET_SEED", "uses": 25444}, {"moveId": "RAZOR_LEAF", "uses": 11792}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 41967}, {"moveId": "LAST_RESORT", "uses": 11650}, {"moveId": "ENERGY_BALL", "uses": 5911}, {"moveId": "SOLAR_BEAM", "uses": 3444}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "LAST_RESORT"], "score": 45.3}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 595, "matchups": [{"opponent": "talonflame", "rating": 798, "opRating": 201}, {"opponent": "forretress", "rating": 771, "opRating": 228}, {"opponent": "victree<PERSON>_shadow", "rating": 758, "opRating": 241}, {"opponent": "virizion", "rating": 728, "opRating": 271}, {"opponent": "skeledirge", "rating": 602, "opRating": 397}], "counters": [{"opponent": "feraligatr", "rating": 179}, {"opponent": "<PERSON>ras", "rating": 227}, {"opponent": "tentacruel", "rating": 301}, {"opponent": "cradily", "rating": 336}, {"opponent": "bellibolt", "rating": 365}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33948}, {"moveId": "WING_ATTACK", "uses": 29052}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 20155}, {"moveId": "SKY_ATTACK", "uses": 19355}, {"moveId": "OVERHEAT", "uses": 16009}, {"moveId": "FIRE_BLAST", "uses": 4696}, {"moveId": "HEAT_WAVE", "uses": 2748}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 45.3}, {"speciesId": "genesect_burn", "speciesName": "Genesect (Burn)", "rating": 687, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 901, "opRating": 98}, {"opponent": "cradily", "rating": 657, "opRating": 342}, {"opponent": "tentacruel", "rating": 594, "opRating": 405}, {"opponent": "golisopod", "rating": 547, "opRating": 452}, {"opponent": "<PERSON>ras", "rating": 511, "opRating": 488}], "counters": [{"opponent": "skeledirge", "rating": 80}, {"opponent": "talonflame", "rating": 86}, {"opponent": "typhlosion_shadow", "rating": 142}, {"opponent": "jellicent", "rating": 229}, {"opponent": "bellibolt", "rating": 365}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 42496}, {"moveId": "METAL_CLAW", "uses": 20504}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25576}, {"moveId": "TECHNO_BLAST_BURN", "uses": 18055}, {"moveId": "MAGNET_BOMB", "uses": 14333}, {"moveId": "FLAMETHROWER", "uses": 5091}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "MAGNET_BOMB"], "score": 45.2}, {"speciesId": "omastar", "speciesName": "Omastar", "rating": 549, "matchups": [{"opponent": "talonflame", "rating": 912, "opRating": 87}, {"opponent": "typhlosion_shadow", "rating": 912, "opRating": 87}, {"opponent": "turtonator", "rating": 891, "opRating": 108}, {"opponent": "skeledirge", "rating": 818, "opRating": 181}, {"opponent": "golisopod", "rating": 527, "opRating": 472}], "counters": [{"opponent": "virizion", "rating": 169}, {"opponent": "cradily", "rating": 191}, {"opponent": "stunfisk", "rating": 242}, {"opponent": "bellibolt", "rating": 255}, {"opponent": "victree<PERSON>_shadow", "rating": 308}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 22923}, {"moveId": "MUD_SHOT", "uses": 21371}, {"moveId": "WATER_GUN", "uses": 18712}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 19508}, {"moveId": "ROCK_BLAST", "uses": 18798}, {"moveId": "ANCIENT_POWER", "uses": 9394}, {"moveId": "HYDRO_PUMP", "uses": 7819}, {"moveId": "RETURN", "uses": 7406}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 45.2}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 553, "matchups": [{"opponent": "gastrodon", "rating": 729, "opRating": 270}, {"opponent": "swampert", "rating": 660, "opRating": 339}, {"opponent": "feraligatr", "rating": 657, "opRating": 342}, {"opponent": "golisopod", "rating": 597, "opRating": 402}, {"opponent": "samu<PERSON>t", "rating": 559, "opRating": 440}], "counters": [{"opponent": "bellibolt", "rating": 180}, {"opponent": "cradily", "rating": 200}, {"opponent": "stunfisk", "rating": 212}, {"opponent": "skeledirge", "rating": 252}, {"opponent": "<PERSON>ras", "rating": 257}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 31225}, {"moveId": "PECK", "uses": 20733}, {"moveId": "ROCK_SMASH", "uses": 11044}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 38024}, {"moveId": "ROCK_BLAST", "uses": 19811}, {"moveId": "FLASH_CANNON", "uses": 5218}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 45.2}, {"speciesId": "primarina", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 527, "matchups": [{"opponent": "drampa", "rating": 859, "opRating": 140}, {"opponent": "virizion", "rating": 667, "opRating": 332}, {"opponent": "blastoise", "rating": 547, "opRating": 452}, {"opponent": "turtonator", "rating": 547, "opRating": 452}, {"opponent": "golisopod", "rating": 517, "opRating": 482}], "counters": [{"opponent": "tentacruel", "rating": 189}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "victree<PERSON>_shadow", "rating": 236}, {"opponent": "cradily", "rating": 280}, {"opponent": "bellibolt", "rating": 322}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 34378}, {"moveId": "CHARM", "uses": 28622}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 24956}, {"moveId": "DISARMING_VOICE", "uses": 14963}, {"moveId": "SPARKLING_ARIA", "uses": 7921}, {"moveId": "PSYCHIC", "uses": 6692}, {"moveId": "MOONBLAST", "uses": 6192}, {"moveId": "HYDRO_PUMP", "uses": 2747}]}, "moveset": ["CHARM", "DISARMING_VOICE", "HYDRO_CANNON"], "score": 45}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 739, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 901, "opRating": 98}, {"opponent": "golisopod", "rating": 838, "opRating": 161}, {"opponent": "cradily", "rating": 657, "opRating": 342}, {"opponent": "tentacruel", "rating": 610, "opRating": 389}, {"opponent": "<PERSON>ras", "rating": 527, "opRating": 472}], "counters": [{"opponent": "skeledirge", "rating": 58}, {"opponent": "talonflame", "rating": 62}, {"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "turtonator", "rating": 137}, {"opponent": "bellibolt", "rating": 365}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 42070}, {"moveId": "METAL_CLAW", "uses": 20930}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26610}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 17962}, {"moveId": "MAGNET_BOMB", "uses": 15261}, {"moveId": "HYPER_BEAM", "uses": 3261}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 44.8}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 556, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 643, "opRating": 356}, {"opponent": "forretress", "rating": 630, "opRating": 369}, {"opponent": "skeledirge", "rating": 611, "opRating": 388}, {"opponent": "typhlosion_shadow", "rating": 592, "opRating": 407}, {"opponent": "virizion", "rating": 573, "opRating": 426}], "counters": [{"opponent": "gastrodon", "rating": 134}, {"opponent": "swampert", "rating": 172}, {"opponent": "samu<PERSON>t", "rating": 185}, {"opponent": "<PERSON>ras", "rating": 272}, {"opponent": "cradily", "rating": 308}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 33343}, {"moveId": "FIRE_SPIN", "uses": 29657}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 28496}, {"moveId": "EARTHQUAKE", "uses": 20105}, {"moveId": "SOLAR_BEAM", "uses": 14381}]}, "moveset": ["EMBER", "OVERHEAT", "EARTHQUAKE"], "score": 44.8}, {"speciesId": "stant<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 546, "matchups": [{"opponent": "tentacruel", "rating": 714, "opRating": 285}, {"opponent": "jellicent", "rating": 661, "opRating": 338}, {"opponent": "talonflame", "rating": 608, "opRating": 391}, {"opponent": "feraligatr", "rating": 580, "opRating": 419}, {"opponent": "<PERSON>ras", "rating": 509, "opRating": 490}], "counters": [{"opponent": "virizion", "rating": 175}, {"opponent": "cradily", "rating": 219}, {"opponent": "golisopod", "rating": 226}, {"opponent": "victree<PERSON>_shadow", "rating": 287}, {"opponent": "bellibolt", "rating": 360}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 54796}, {"moveId": "ZEN_HEADBUTT", "uses": 8204}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 29145}, {"moveId": "STOMP", "uses": 17529}, {"moveId": "MEGAHORN", "uses": 16206}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 44.6}, {"speciesId": "basculin", "speciesName": "Bas<PERSON>lin", "rating": 525, "matchups": [{"opponent": "skeledirge", "rating": 821, "opRating": 178}, {"opponent": "talonflame", "rating": 818, "opRating": 181}, {"opponent": "rapidash", "rating": 818, "opRating": 181}, {"opponent": "crustle", "rating": 805, "opRating": 194}, {"opponent": "stunfisk", "rating": 525, "opRating": 474}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "<PERSON>ras", "rating": 240}, {"opponent": "tentacruel", "rating": 275}, {"opponent": "cradily", "rating": 305}, {"opponent": "bellibolt", "rating": 330}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 34557}, {"moveId": "TACKLE", "uses": 28443}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 36782}, {"moveId": "AQUA_JET", "uses": 17476}, {"moveId": "MUDDY_WATER", "uses": 8817}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "AQUA_JET"], "score": 44.2}, {"speciesId": "dunsparce", "speciesName": "Dunsparce", "rating": 473, "matchups": [{"opponent": "magnezone_shadow", "rating": 828, "opRating": 171}, {"opponent": "salazzle", "rating": 810, "opRating": 189}, {"opponent": "talonflame", "rating": 773, "opRating": 226}, {"opponent": "toxtricity", "rating": 711, "opRating": 288}, {"opponent": "typhlosion_shadow", "rating": 661, "opRating": 338}], "counters": [{"opponent": "virizion", "rating": 127}, {"opponent": "feraligatr", "rating": 261}, {"opponent": "cradily", "rating": 272}, {"opponent": "skeledirge", "rating": 358}, {"opponent": "bellibolt", "rating": 402}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 28906}, {"moveId": "ASTONISH", "uses": 22433}, {"moveId": "BITE", "uses": 11659}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 30186}, {"moveId": "ROCK_SLIDE", "uses": 25190}, {"moveId": "DIG", "uses": 7630}]}, "moveset": ["ROLLOUT", "DRILL_RUN", "ROCK_SLIDE"], "score": 44.2}, {"speciesId": "genesect_douse", "speciesName": "Genesect (Douse)", "rating": 739, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 901, "opRating": 98}, {"opponent": "swampert", "rating": 838, "opRating": 161}, {"opponent": "cradily", "rating": 657, "opRating": 342}, {"opponent": "tentacruel", "rating": 602, "opRating": 397}, {"opponent": "golisopod", "rating": 547, "opRating": 452}], "counters": [{"opponent": "skeledirge", "rating": 58}, {"opponent": "talonflame", "rating": 62}, {"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "turtonator", "rating": 137}, {"opponent": "bellibolt", "rating": 365}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 42595}, {"moveId": "METAL_CLAW", "uses": 20405}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25348}, {"moveId": "TECHNO_BLAST_DOUSE", "uses": 18085}, {"moveId": "MAGNET_BOMB", "uses": 13399}, {"moveId": "GUNK_SHOT", "uses": 6149}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_DOUSE"], "score": 44.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 601, "matchups": [{"opponent": "virizion", "rating": 923, "opRating": 76}, {"opponent": "victree<PERSON>_shadow", "rating": 722, "opRating": 277}, {"opponent": "gastrodon", "rating": 685, "opRating": 314}, {"opponent": "golisopod", "rating": 600, "opRating": 399}, {"opponent": "samu<PERSON>t", "rating": 591, "opRating": 408}], "counters": [{"opponent": "cradily", "rating": 191}, {"opponent": "<PERSON>ras", "rating": 192}, {"opponent": "tentacruel", "rating": 198}, {"opponent": "bellibolt", "rating": 225}, {"opponent": "skeledirge", "rating": 252}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 24846}, {"moveId": "BULLET_SEED", "uses": 21255}, {"moveId": "INFESTATION", "uses": 16888}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 18542}, {"moveId": "AERIAL_ACE", "uses": 16786}, {"moveId": "ENERGY_BALL", "uses": 12048}, {"moveId": "DAZZLING_GLEAM", "uses": 6844}, {"moveId": "RETURN", "uses": 5175}, {"moveId": "SOLAR_BEAM", "uses": 3491}]}, "moveset": ["FAIRY_WIND", "ACROBATICS", "AERIAL_ACE"], "score": 44.1}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 530, "matchups": [{"opponent": "ursaring_shadow", "rating": 914, "opRating": 85}, {"opponent": "stoutland", "rating": 867, "opRating": 132}, {"opponent": "magneton_shadow", "rating": 638, "opRating": 361}, {"opponent": "salazzle", "rating": 605, "opRating": 394}, {"opponent": "drampa", "rating": 515, "opRating": 484}], "counters": [{"opponent": "skeledirge", "rating": 86}, {"opponent": "cradily", "rating": 222}, {"opponent": "virizion", "rating": 257}, {"opponent": "<PERSON>ras", "rating": 296}, {"opponent": "bellibolt", "rating": 357}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 34473}, {"moveId": "LICK", "uses": 28527}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 37354}, {"moveId": "BULLDOZE", "uses": 15485}, {"moveId": "GUNK_SHOT", "uses": 10168}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 43.7}, {"speciesId": "zebst<PERSON>_shadow", "speciesName": "<PERSON>eb<PERSON><PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "feraligatr", "rating": 808, "opRating": 191}, {"opponent": "<PERSON>ras", "rating": 779, "opRating": 220}, {"opponent": "tentacruel", "rating": 754, "opRating": 245}, {"opponent": "golisopod", "rating": 745, "opRating": 254}, {"opponent": "skeledirge", "rating": 694, "opRating": 305}], "counters": [{"opponent": "stunfisk", "rating": 102}, {"opponent": "victree<PERSON>_shadow", "rating": 164}, {"opponent": "cradily", "rating": 177}, {"opponent": "bellibolt", "rating": 205}, {"opponent": "virizion", "rating": 209}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 52240}, {"moveId": "LOW_KICK", "uses": 10760}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 31899}, {"moveId": "DISCHARGE", "uses": 15537}, {"moveId": "FLAME_CHARGE", "uses": 15437}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "DISCHARGE"], "score": 43.7}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 584, "matchups": [{"opponent": "venusaur", "rating": 853, "opRating": 146}, {"opponent": "forretress", "rating": 834, "opRating": 165}, {"opponent": "victree<PERSON>_shadow", "rating": 551, "opRating": 448}, {"opponent": "jellicent", "rating": 535, "opRating": 464}, {"opponent": "skeledirge", "rating": 516, "opRating": 483}], "counters": [{"opponent": "virizion", "rating": 57}, {"opponent": "tentacruel", "rating": 233}, {"opponent": "cradily", "rating": 252}, {"opponent": "feraligatr", "rating": 270}, {"opponent": "golisopod", "rating": 283}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 31559}, {"moveId": "FIRE_FANG", "uses": 31441}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 24237}, {"moveId": "FLAMETHROWER", "uses": 15529}, {"moveId": "CRUNCH", "uses": 11351}, {"moveId": "RETURN", "uses": 7670}, {"moveId": "FIRE_BLAST", "uses": 4083}]}, "moveset": ["SNARL", "FOUL_PLAY", "FLAMETHROWER"], "score": 43.6}, {"speciesId": "carracosta", "speciesName": "Carracosta", "rating": 522, "matchups": [{"opponent": "talonflame", "rating": 937, "opRating": 62}, {"opponent": "typhlosion_shadow", "rating": 892, "opRating": 107}, {"opponent": "skeledirge", "rating": 640, "opRating": 359}, {"opponent": "<PERSON>ras", "rating": 624, "opRating": 375}, {"opponent": "golisopod", "rating": 558, "opRating": 441}], "counters": [{"opponent": "stunfisk", "rating": 142}, {"opponent": "virizion", "rating": 172}, {"opponent": "cradily", "rating": 202}, {"opponent": "bellibolt", "rating": 252}, {"opponent": "victree<PERSON>_shadow", "rating": 272}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 34525}, {"moveId": "WATER_GUN", "uses": 28475}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 18744}, {"moveId": "BODY_SLAM", "uses": 15484}, {"moveId": "LIQUIDATION", "uses": 13633}, {"moveId": "SURF", "uses": 11379}, {"moveId": "RETURN", "uses": 3720}]}, "moveset": ["ROCK_THROW", "ANCIENT_POWER", "LIQUIDATION"], "score": 43.3}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 542, "matchups": [{"opponent": "forretress", "rating": 858, "opRating": 141}, {"opponent": "venusaur", "rating": 696, "opRating": 303}, {"opponent": "victree<PERSON>_shadow", "rating": 585, "opRating": 414}, {"opponent": "serperior", "rating": 561, "opRating": 438}, {"opponent": "jellicent", "rating": 503, "opRating": 496}], "counters": [{"opponent": "tentacruel", "rating": 198}, {"opponent": "cradily", "rating": 236}, {"opponent": "feraligatr", "rating": 238}, {"opponent": "<PERSON>ras", "rating": 261}, {"opponent": "bellibolt", "rating": 280}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 20642}, {"moveId": "SNARL", "uses": 19223}, {"moveId": "THUNDER_FANG", "uses": 16591}, {"moveId": "BITE", "uses": 6531}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15807}, {"moveId": "PSYCHIC_FANGS", "uses": 10670}, {"moveId": "FLAMETHROWER", "uses": 9007}, {"moveId": "SCORCHING_SANDS", "uses": 8808}, {"moveId": "CRUNCH", "uses": 8007}, {"moveId": "BULLDOZE", "uses": 4738}, {"moveId": "RETURN", "uses": 3551}, {"moveId": "FIRE_BLAST", "uses": 2444}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 43.2}, {"speciesId": "marshtomp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 537, "matchups": [{"opponent": "skeledirge", "rating": 805, "opRating": 194}, {"opponent": "typhlosion_shadow", "rating": 805, "opRating": 194}, {"opponent": "stunfisk", "rating": 678, "opRating": 321}, {"opponent": "tentacruel", "rating": 541, "opRating": 458}, {"opponent": "bellibolt", "rating": 509, "opRating": 490}], "counters": [{"opponent": "golisopod", "rating": 180}, {"opponent": "virizion", "rating": 196}, {"opponent": "<PERSON>ras", "rating": 222}, {"opponent": "cradily", "rating": 247}, {"opponent": "feraligatr", "rating": 283}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 37388}, {"moveId": "WATER_GUN", "uses": 25612}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 23867}, {"moveId": "SLUDGE", "uses": 21477}, {"moveId": "SURF", "uses": 17608}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 43.2}, {"speciesId": "tsar<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 587, "matchups": [{"opponent": "stunfisk", "rating": 812, "opRating": 187}, {"opponent": "lickilicky", "rating": 758, "opRating": 241}, {"opponent": "gastrodon", "rating": 734, "opRating": 265}, {"opponent": "swampert", "rating": 656, "opRating": 343}, {"opponent": "jellicent", "rating": 549, "opRating": 450}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 113}, {"opponent": "typhlosion_shadow", "rating": 172}, {"opponent": "talonflame", "rating": 241}, {"opponent": "tentacruel", "rating": 328}, {"opponent": "<PERSON>ras", "rating": 329}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 34576}, {"moveId": "CHARM", "uses": 15135}, {"moveId": "RAZOR_LEAF", "uses": 13299}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 16146}, {"moveId": "TRIPLE_AXEL", "uses": 15353}, {"moveId": "HIGH_JUMP_KICK", "uses": 12227}, {"moveId": "STOMP", "uses": 10018}, {"moveId": "ENERGY_BALL", "uses": 5791}, {"moveId": "DRAINING_KISS", "uses": 3466}]}, "moveset": ["MAGICAL_LEAF", "TRIPLE_AXEL", "HIGH_JUMP_KICK"], "score": 43.1}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "forretress", "rating": 821, "opRating": 178}, {"opponent": "cradily", "rating": 762, "opRating": 237}, {"opponent": "victree<PERSON>_shadow", "rating": 737, "opRating": 262}, {"opponent": "typhlosion_shadow", "rating": 659, "opRating": 340}, {"opponent": "bellibolt", "rating": 506, "opRating": 493}], "counters": [{"opponent": "feraligatr", "rating": 179}, {"opponent": "tentacruel", "rating": 189}, {"opponent": "talonflame", "rating": 193}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "<PERSON>ras", "rating": 227}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 50549}, {"moveId": "ROCK_SMASH", "uses": 12451}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29234}, {"moveId": "BLAST_BURN", "uses": 22630}, {"moveId": "SOLAR_BEAM", "uses": 5790}, {"moveId": "FLAMETHROWER", "uses": 5232}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 42.9}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 626, "matchups": [{"opponent": "lickilicky", "rating": 778, "opRating": 221}, {"opponent": "stunfisk", "rating": 718, "opRating": 281}, {"opponent": "forretress", "rating": 705, "opRating": 294}, {"opponent": "samu<PERSON>t", "rating": 602, "opRating": 397}, {"opponent": "cradily", "rating": 546, "opRating": 453}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 56}, {"opponent": "zapdos", "rating": 83}, {"opponent": "skeledirge", "rating": 133}, {"opponent": "talonflame", "rating": 169}, {"opponent": "tentacruel", "rating": 340}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 34072}, {"moveId": "MAGICAL_LEAF", "uses": 28928}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 26739}, {"moveId": "UPPER_HAND", "uses": 24417}, {"moveId": "PETAL_BLIZZARD", "uses": 8272}, {"moveId": "SOLAR_BEAM", "uses": 3394}]}, "moveset": ["BULLET_SEED", "CLOSE_COMBAT", "UPPER_HAND"], "score": 42.8}, {"speciesId": "octillery", "speciesName": "Octillery", "rating": 505, "matchups": [{"opponent": "typhlosion_shadow", "rating": 846, "opRating": 153}, {"opponent": "swampert", "rating": 646, "opRating": 353}, {"opponent": "talonflame", "rating": 643, "opRating": 356}, {"opponent": "skeledirge", "rating": 568, "opRating": 431}, {"opponent": "stunfisk", "rating": 512, "opRating": 487}], "counters": [{"opponent": "virizion", "rating": 203}, {"opponent": "cradily", "rating": 213}, {"opponent": "bellibolt", "rating": 222}, {"opponent": "<PERSON>ras", "rating": 274}, {"opponent": "tentacruel", "rating": 307}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 23675}, {"moveId": "MUD_SHOT", "uses": 20697}, {"moveId": "WATER_GUN", "uses": 18690}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 18082}, {"moveId": "OCTAZOOKA", "uses": 16254}, {"moveId": "GUNK_SHOT", "uses": 12011}, {"moveId": "AURORA_BEAM", "uses": 11528}, {"moveId": "ACID_SPRAY", "uses": 5203}]}, "moveset": ["LOCK_ON", "WATER_PULSE", "OCTAZOOKA"], "score": 42.8}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 504, "matchups": [{"opponent": "swampert", "rating": 639, "opRating": 360}, {"opponent": "virizion", "rating": 629, "opRating": 370}, {"opponent": "cradily", "rating": 587, "opRating": 412}, {"opponent": "feraligatr", "rating": 568, "opRating": 431}, {"opponent": "golisopod", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 157}, {"opponent": "skeledirge", "rating": 175}, {"opponent": "typhlosion_shadow", "rating": 175}, {"opponent": "tentacruel", "rating": 369}, {"opponent": "bellibolt", "rating": 405}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 36056}, {"moveId": "INFESTATION", "uses": 26944}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 25399}, {"moveId": "WATER_PULSE", "uses": 17738}, {"moveId": "BUBBLE_BEAM", "uses": 10211}, {"moveId": "MIRROR_COAT", "uses": 9572}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "WATER_PULSE"], "score": 42.7}, {"speciesId": "kingler", "speciesName": "<PERSON><PERSON>", "rating": 522, "matchups": [{"opponent": "skeledirge", "rating": 814, "opRating": 185}, {"opponent": "talonflame", "rating": 814, "opRating": 185}, {"opponent": "darmanitan_standard", "rating": 780, "opRating": 219}, {"opponent": "rapidash", "rating": 747, "opRating": 252}, {"opponent": "swampert", "rating": 545, "opRating": 454}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 146}, {"opponent": "virizion", "rating": 242}, {"opponent": "tentacruel", "rating": 245}, {"opponent": "cradily", "rating": 313}, {"opponent": "bellibolt", "rating": 342}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 23762}, {"moveId": "MUD_SHOT", "uses": 23267}, {"moveId": "METAL_CLAW", "uses": 15955}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 19963}, {"moveId": "CRABHAMMER", "uses": 17443}, {"moveId": "RAZOR_SHELL", "uses": 14112}, {"moveId": "VICE_GRIP", "uses": 6732}, {"moveId": "WATER_PULSE", "uses": 4658}]}, "moveset": ["BUBBLE", "CRABHAMMER", "X_SCISSOR"], "score": 42.7}, {"speciesId": "sharpedo", "speciesName": "<PERSON><PERSON>", "rating": 479, "matchups": [{"opponent": "typhlosion_shadow", "rating": 757, "opRating": 242}, {"opponent": "skeledirge", "rating": 754, "opRating": 245}, {"opponent": "talonflame", "rating": 742, "opRating": 257}, {"opponent": "jellicent", "rating": 624, "opRating": 375}, {"opponent": "feraligatr", "rating": 515, "opRating": 484}], "counters": [{"opponent": "golisopod", "rating": 180}, {"opponent": "tentacruel", "rating": 189}, {"opponent": "victree<PERSON>_shadow", "rating": 236}, {"opponent": "cradily", "rating": 297}, {"opponent": "bellibolt", "rating": 332}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 37297}, {"moveId": "BITE", "uses": 25703}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 26676}, {"moveId": "POISON_FANG", "uses": 17489}, {"moveId": "HYDRO_PUMP", "uses": 10272}, {"moveId": "RETURN", "uses": 8539}]}, "moveset": ["WATERFALL", "CRUNCH", "POISON_FANG"], "score": 42.5}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 570, "matchups": [{"opponent": "venusaur", "rating": 848, "opRating": 151}, {"opponent": "forretress", "rating": 811, "opRating": 188}, {"opponent": "victree<PERSON>_shadow", "rating": 595, "opRating": 404}, {"opponent": "zapdos", "rating": 518, "opRating": 481}, {"opponent": "crustle", "rating": 515, "opRating": 484}], "counters": [{"opponent": "swampert", "rating": 141}, {"opponent": "feraligatr", "rating": 157}, {"opponent": "<PERSON>ras", "rating": 192}, {"opponent": "tentacruel", "rating": 275}, {"opponent": "cradily", "rating": 288}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 43528}, {"moveId": "BITE", "uses": 19472}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30563}, {"moveId": "FLAMETHROWER", "uses": 25562}, {"moveId": "FIRE_BLAST", "uses": 6960}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 42.5}, {"speciesId": "swanna", "speciesName": "<PERSON><PERSON>", "rating": 549, "matchups": [{"opponent": "turtonator", "rating": 777, "opRating": 222}, {"opponent": "golisopod", "rating": 701, "opRating": 298}, {"opponent": "gastrodon", "rating": 554, "opRating": 445}, {"opponent": "talonflame", "rating": 548, "opRating": 451}, {"opponent": "swampert", "rating": 539, "opRating": 460}], "counters": [{"opponent": "virizion", "rating": 154}, {"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "<PERSON>ras", "rating": 277}, {"opponent": "bellibolt", "rating": 290}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32735}, {"moveId": "WATER_GUN", "uses": 30265}], "chargedMoves": [{"moveId": "FLY", "uses": 30301}, {"moveId": "ICE_BEAM", "uses": 11006}, {"moveId": "RETURN", "uses": 7550}, {"moveId": "BUBBLE_BEAM", "uses": 7521}, {"moveId": "HURRICANE", "uses": 6522}]}, "moveset": ["WATER_GUN", "FLY", "ICE_BEAM"], "score": 42.3}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 629, "matchups": [{"opponent": "<PERSON>ras", "rating": 737, "opRating": 262}, {"opponent": "feraligatr", "rating": 659, "opRating": 340}, {"opponent": "cradily", "rating": 606, "opRating": 393}, {"opponent": "tentacruel", "rating": 593, "opRating": 406}, {"opponent": "bellibolt", "rating": 534, "opRating": 465}], "counters": [{"opponent": "talonflame", "rating": 44}, {"opponent": "typhlosion_shadow", "rating": 62}, {"opponent": "turtonator", "rating": 66}, {"opponent": "skeledirge", "rating": 183}, {"opponent": "virizion", "rating": 263}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35009}, {"moveId": "METAL_CLAW", "uses": 27991}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20996}, {"moveId": "MIRROR_SHOT", "uses": 12875}, {"moveId": "THUNDER", "uses": 11213}, {"moveId": "RETURN", "uses": 7996}, {"moveId": "FLASH_CANNON", "uses": 6625}, {"moveId": "ACID_SPRAY", "uses": 3325}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "FLASH_CANNON"], "score": 42.1}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 587, "matchups": [{"opponent": "venusaur", "rating": 853, "opRating": 146}, {"opponent": "forretress", "rating": 818, "opRating": 181}, {"opponent": "skeledirge", "rating": 788, "opRating": 211}, {"opponent": "turtonator", "rating": 704, "opRating": 295}, {"opponent": "zapdos", "rating": 522, "opRating": 477}], "counters": [{"opponent": "virizion", "rating": 57}, {"opponent": "victree<PERSON>_shadow", "rating": 164}, {"opponent": "tentacruel", "rating": 284}, {"opponent": "cradily", "rating": 297}, {"opponent": "bellibolt", "rating": 355}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 32059}, {"moveId": "FIRE_FANG", "uses": 30941}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 27753}, {"moveId": "FLAMETHROWER", "uses": 17553}, {"moveId": "CRUNCH", "uses": 12940}, {"moveId": "FIRE_BLAST", "uses": 4659}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "FOUL_PLAY", "FLAMETHROWER"], "score": 42}, {"speciesId": "komala", "speciesName": "Komala", "rating": 545, "matchups": [{"opponent": "salazzle", "rating": 773, "opRating": 226}, {"opponent": "skeledirge", "rating": 741, "opRating": 258}, {"opponent": "talonflame", "rating": 741, "opRating": 258}, {"opponent": "jellicent", "rating": 673, "opRating": 326}, {"opponent": "zapdos", "rating": 535, "opRating": 464}], "counters": [{"opponent": "virizion", "rating": 57}, {"opponent": "victree<PERSON>_shadow", "rating": 146}, {"opponent": "samu<PERSON>t", "rating": 162}, {"opponent": "bellibolt", "rating": 277}, {"opponent": "cradily", "rating": 338}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 62081}, {"moveId": "YAWN", "uses": 919}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 25867}, {"moveId": "BULLDOZE", "uses": 20105}, {"moveId": "PLAY_ROUGH", "uses": 17065}]}, "moveset": ["ROLLOUT", "PAYBACK", "BULLDOZE"], "score": 42}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 580, "matchups": [{"opponent": "venusaur", "rating": 921, "opRating": 78}, {"opponent": "virizion", "rating": 800, "opRating": 200}, {"opponent": "victree<PERSON>_shadow", "rating": 640, "opRating": 359}, {"opponent": "gastrodon", "rating": 578, "opRating": 421}, {"opponent": "golisopod", "rating": 531, "opRating": 468}], "counters": [{"opponent": "feraligatr", "rating": 179}, {"opponent": "bellibolt", "rating": 180}, {"opponent": "cradily", "rating": 244}, {"opponent": "<PERSON>ras", "rating": 285}, {"opponent": "tentacruel", "rating": 286}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 55820}, {"moveId": "POUND", "uses": 7180}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 39598}, {"moveId": "AERIAL_ACE", "uses": 14982}, {"moveId": "HURRICANE", "uses": 8503}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 42}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 571, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 858, "opRating": 141}, {"opponent": "forretress", "rating": 858, "opRating": 141}, {"opponent": "talonflame", "rating": 726, "opRating": 273}, {"opponent": "typhlosion_shadow", "rating": 653, "opRating": 346}, {"opponent": "zapdos", "rating": 521, "opRating": 478}], "counters": [{"opponent": "jellicent", "rating": 142}, {"opponent": "swampert", "rating": 144}, {"opponent": "feraligatr", "rating": 160}, {"opponent": "<PERSON>ras", "rating": 209}, {"opponent": "tentacruel", "rating": 295}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28503}, {"moveId": "FIRE_FANG", "uses": 27252}, {"moveId": "ROCK_SMASH", "uses": 7314}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 21067}, {"moveId": "ROCK_SLIDE", "uses": 16531}, {"moveId": "FLAMETHROWER", "uses": 13415}, {"moveId": "CRUNCH", "uses": 11848}]}, "moveset": ["FIRE_FANG", "ROCK_SLIDE", "CRUNCH"], "score": 41.9}, {"speciesId": "sharpedo_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 484, "matchups": [{"opponent": "salazzle", "rating": 824, "opRating": 175}, {"opponent": "typhlosion_shadow", "rating": 805, "opRating": 194}, {"opponent": "skeledirge", "rating": 777, "opRating": 222}, {"opponent": "talonflame", "rating": 777, "opRating": 222}, {"opponent": "turtonator", "rating": 646, "opRating": 353}], "counters": [{"opponent": "golisopod", "rating": 163}, {"opponent": "virizion", "rating": 178}, {"opponent": "feraligatr", "rating": 232}, {"opponent": "tentacruel", "rating": 266}, {"opponent": "cradily", "rating": 347}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 38371}, {"moveId": "BITE", "uses": 24629}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30749}, {"moveId": "POISON_FANG", "uses": 20378}, {"moveId": "HYDRO_PUMP", "uses": 11796}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATERFALL", "CRUNCH", "POISON_FANG"], "score": 41.7}, {"speciesId": "stantler", "speciesName": "<PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "tentacruel", "rating": 754, "opRating": 245}, {"opponent": "feraligatr", "rating": 670, "opRating": 329}, {"opponent": "talonflame", "rating": 670, "opRating": 329}, {"opponent": "drampa", "rating": 624, "opRating": 375}, {"opponent": "blastoise", "rating": 618, "opRating": 381}], "counters": [{"opponent": "skeledirge", "rating": 147}, {"opponent": "virizion", "rating": 175}, {"opponent": "victree<PERSON>_shadow", "rating": 230}, {"opponent": "cradily", "rating": 236}, {"opponent": "bellibolt", "rating": 315}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 54258}, {"moveId": "ZEN_HEADBUTT", "uses": 8742}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 25647}, {"moveId": "STOMP", "uses": 14783}, {"moveId": "MEGAHORN", "uses": 13934}, {"moveId": "RETURN", "uses": 8709}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 41.7}, {"speciesId": "whimsicott", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 630, "matchups": [{"opponent": "gastrodon", "rating": 848, "opRating": 151}, {"opponent": "virizion", "rating": 802, "opRating": 197}, {"opponent": "stunfisk", "rating": 721, "opRating": 278}, {"opponent": "samu<PERSON>t", "rating": 630, "opRating": 369}, {"opponent": "bellibolt", "rating": 559, "opRating": 440}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 74}, {"opponent": "typhlosion_shadow", "rating": 102}, {"opponent": "skeledirge", "rating": 225}, {"opponent": "cradily", "rating": 238}, {"opponent": "tentacruel", "rating": 239}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 33694}, {"moveId": "CHARM", "uses": 15874}, {"moveId": "RAZOR_LEAF", "uses": 13441}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 18883}, {"moveId": "MOONBLAST", "uses": 16104}, {"moveId": "SEED_BOMB", "uses": 15043}, {"moveId": "HURRICANE", "uses": 12956}]}, "moveset": ["FAIRY_WIND", "MOONBLAST", "GRASS_KNOT"], "score": 41.7}, {"speciesId": "<PERSON><PERSON>", "speciesName": "Boltund", "rating": 537, "matchups": [{"opponent": "tentacruel", "rating": 745, "opRating": 254}, {"opponent": "zapdos", "rating": 732, "opRating": 267}, {"opponent": "blastoise", "rating": 603, "opRating": 396}, {"opponent": "jellicent", "rating": 525, "opRating": 474}, {"opponent": "feraligatr", "rating": 503, "opRating": 496}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 137}, {"opponent": "cradily", "rating": 205}, {"opponent": "<PERSON>ras", "rating": 227}, {"opponent": "bellibolt", "rating": 252}, {"opponent": "virizion", "rating": 269}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39920}, {"moveId": "BITE", "uses": 23080}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 35076}, {"moveId": "THUNDER", "uses": 27924}]}, "moveset": ["SPARK", "CRUNCH", "THUNDER"], "score": 41.5}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 538, "matchups": [{"opponent": "stunfisk", "rating": 783, "opRating": 216}, {"opponent": "jellicent", "rating": 712, "opRating": 287}, {"opponent": "feraligatr", "rating": 632, "opRating": 367}, {"opponent": "virizion", "rating": 598, "opRating": 401}, {"opponent": "swampert", "rating": 592, "opRating": 407}], "counters": [{"opponent": "typhlosion_shadow", "rating": 82}, {"opponent": "talonflame", "rating": 178}, {"opponent": "skeledirge", "rating": 205}, {"opponent": "cradily", "rating": 286}, {"opponent": "bellibolt", "rating": 305}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 32181}, {"moveId": "BULLET_SEED", "uses": 30819}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 34082}, {"moveId": "PETAL_BLIZZARD", "uses": 20644}, {"moveId": "SOLAR_BEAM", "uses": 8221}]}, "moveset": ["BULLET_SEED", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 41.3}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 577, "matchups": [{"opponent": "forretress", "rating": 845, "opRating": 154}, {"opponent": "skeledirge", "rating": 649, "opRating": 350}, {"opponent": "venusaur", "rating": 607, "opRating": 392}, {"opponent": "stunfisk", "rating": 591, "opRating": 408}, {"opponent": "victree<PERSON>_shadow", "rating": 560, "opRating": 439}], "counters": [{"opponent": "jellicent", "rating": 127}, {"opponent": "feraligatr", "rating": 157}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "bellibolt", "rating": 267}, {"opponent": "cradily", "rating": 372}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 34071}, {"moveId": "FIRE_FANG", "uses": 28929}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 19073}, {"moveId": "FLAME_CHARGE", "uses": 15530}, {"moveId": "OVERHEAT", "uses": 12027}, {"moveId": "FLAMETHROWER", "uses": 6375}, {"moveId": "IRON_HEAD", "uses": 6317}, {"moveId": "FIRE_BLAST", "uses": 3535}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SCORCHING_SANDS", "FLAME_CHARGE"], "score": 41.2}, {"speciesId": "gumshoos", "speciesName": "<PERSON>ums<PERSON><PERSON>", "rating": 414, "matchups": [{"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 910, "opRating": 89}, {"opponent": "dhelmise", "rating": 877, "opRating": 122}, {"opponent": "gourgeist_super", "rating": 755, "opRating": 244}, {"opponent": "trevenant", "rating": 720, "opRating": 279}, {"opponent": "jellicent", "rating": 562, "opRating": 437}], "counters": [{"opponent": "virizion", "rating": 121}, {"opponent": "tentacruel", "rating": 278}, {"opponent": "cradily", "rating": 283}, {"opponent": "bellibolt", "rating": 302}, {"opponent": "<PERSON>ras", "rating": 313}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 42557}, {"moveId": "TAKE_DOWN", "uses": 20443}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 27124}, {"moveId": "CRUNCH", "uses": 18169}, {"moveId": "HYPER_FANG", "uses": 17640}]}, "moveset": ["BITE", "ROCK_TOMB", "CRUNCH"], "score": 41.2}, {"speciesId": "marshtomp", "speciesName": "Marshtom<PERSON>", "rating": 514, "matchups": [{"opponent": "toxtricity", "rating": 856, "opRating": 143}, {"opponent": "typhlosion_shadow", "rating": 834, "opRating": 165}, {"opponent": "stunfisk", "rating": 700, "opRating": 299}, {"opponent": "skeledirge", "rating": 652, "opRating": 347}, {"opponent": "tentacruel", "rating": 589, "opRating": 410}], "counters": [{"opponent": "virizion", "rating": 178}, {"opponent": "<PERSON>ras", "rating": 183}, {"opponent": "golisopod", "rating": 191}, {"opponent": "cradily", "rating": 211}, {"opponent": "victree<PERSON>_shadow", "rating": 320}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36017}, {"moveId": "WATER_GUN", "uses": 26983}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 21832}, {"moveId": "SLUDGE", "uses": 19285}, {"moveId": "SURF", "uses": 16113}, {"moveId": "RETURN", "uses": 5766}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 40.8}, {"speciesId": "arboliva", "speciesName": "Arboliva", "rating": 553, "matchups": [{"opponent": "feraligatr", "rating": 916, "opRating": 83}, {"opponent": "stunfisk", "rating": 826, "opRating": 173}, {"opponent": "<PERSON>ras", "rating": 720, "opRating": 280}, {"opponent": "bellibolt", "rating": 603, "opRating": 396}, {"opponent": "tentacruel", "rating": 520, "opRating": 480}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 107}, {"opponent": "typhlosion_shadow", "rating": 172}, {"opponent": "golisopod", "rating": 223}, {"opponent": "skeledirge", "rating": 277}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 27964}, {"moveId": "TACKLE", "uses": 22303}, {"moveId": "RAZOR_LEAF", "uses": 12805}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 27780}, {"moveId": "EARTH_POWER", "uses": 18513}, {"moveId": "SEED_BOMB", "uses": 8872}, {"moveId": "ENERGY_BALL", "uses": 7872}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 40.7}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 604, "matchups": [{"opponent": "forretress", "rating": 873, "opRating": 126}, {"opponent": "venusaur", "rating": 697, "opRating": 302}, {"opponent": "zapdos", "rating": 623, "opRating": 376}, {"opponent": "golisopod", "rating": 543, "opRating": 456}, {"opponent": "typhlosion_shadow", "rating": 535, "opRating": 464}], "counters": [{"opponent": "feraligatr", "rating": 135}, {"opponent": "tentacruel", "rating": 136}, {"opponent": "skeledirge", "rating": 158}, {"opponent": "<PERSON>ras", "rating": 311}, {"opponent": "cradily", "rating": 327}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33646}, {"moveId": "FIRE_FANG", "uses": 29354}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 16880}, {"moveId": "FLAME_CHARGE", "uses": 13919}, {"moveId": "OVERHEAT", "uses": 10777}, {"moveId": "RETURN", "uses": 6710}, {"moveId": "FLAMETHROWER", "uses": 5807}, {"moveId": "IRON_HEAD", "uses": 5638}, {"moveId": "FIRE_BLAST", "uses": 3117}]}, "moveset": ["FIRE_SPIN", "SCORCHING_SANDS", "FLAME_CHARGE"], "score": 40.7}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 574, "matchups": [{"opponent": "stunfisk", "rating": 770, "opRating": 229}, {"opponent": "feraligatr", "rating": 767, "opRating": 232}, {"opponent": "swampert", "rating": 702, "opRating": 297}, {"opponent": "jellicent", "rating": 654, "opRating": 345}, {"opponent": "<PERSON>ras", "rating": 505, "opRating": 494}], "counters": [{"opponent": "golisopod", "rating": 102}, {"opponent": "victree<PERSON>_shadow", "rating": 128}, {"opponent": "virizion", "rating": 245}, {"opponent": "skeledirge", "rating": 247}, {"opponent": "cradily", "rating": 283}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 41710}, {"moveId": "RAZOR_LEAF", "uses": 21290}], "chargedMoves": [{"moveId": "SCALD", "uses": 17843}, {"moveId": "ENERGY_BALL", "uses": 12174}, {"moveId": "ICE_BEAM", "uses": 11294}, {"moveId": "LEAF_STORM", "uses": 10617}, {"moveId": "BLIZZARD", "uses": 3936}, {"moveId": "HYDRO_PUMP", "uses": 3602}, {"moveId": "SOLAR_BEAM", "uses": 3514}]}, "moveset": ["BUBBLE", "SCALD", "LEAF_STORM"], "score": 40.7}, {"speciesId": "brionne", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 466, "matchups": [{"opponent": "skeledirge", "rating": 816, "opRating": 183}, {"opponent": "talonflame", "rating": 816, "opRating": 183}, {"opponent": "rapidash", "rating": 813, "opRating": 186}, {"opponent": "darmanitan_standard", "rating": 785, "opRating": 214}, {"opponent": "crustle", "rating": 545, "opRating": 454}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "<PERSON>ras", "rating": 255}, {"opponent": "tentacruel", "rating": 266}, {"opponent": "cradily", "rating": 294}, {"opponent": "bellibolt", "rating": 317}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 38253}, {"moveId": "CHARM", "uses": 24747}], "chargedMoves": [{"moveId": "AQUA_JET", "uses": 35870}, {"moveId": "DISARMING_VOICE", "uses": 20602}, {"moveId": "WATER_PULSE", "uses": 6499}]}, "moveset": ["WATER_GUN", "AQUA_JET", "DISARMING_VOICE"], "score": 40.5}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 598, "matchups": [{"opponent": "lickilicky", "rating": 746, "opRating": 253}, {"opponent": "typhlosion_shadow", "rating": 711, "opRating": 288}, {"opponent": "virizion", "rating": 616, "opRating": 383}, {"opponent": "victree<PERSON>_shadow", "rating": 584, "opRating": 415}, {"opponent": "bellibolt", "rating": 571, "opRating": 428}], "counters": [{"opponent": "feraligatr", "rating": 157}, {"opponent": "skeledirge", "rating": 158}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "talonflame", "rating": 169}, {"opponent": "<PERSON>ras", "rating": 192}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 49589}, {"moveId": "ROCK_SMASH", "uses": 13411}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 26949}, {"moveId": "BLAST_BURN", "uses": 20980}, {"moveId": "SOLAR_BEAM", "uses": 5391}, {"moveId": "RETURN", "uses": 4920}, {"moveId": "FLAMETHROWER", "uses": 4810}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 40.5}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 578, "matchups": [{"opponent": "swampert", "rating": 904, "opRating": 95}, {"opponent": "jellicent", "rating": 845, "opRating": 154}, {"opponent": "feraligatr", "rating": 811, "opRating": 188}, {"opponent": "stunfisk", "rating": 796, "opRating": 203}, {"opponent": "bellibolt", "rating": 524, "opRating": 475}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 83}, {"opponent": "typhlosion_shadow", "rating": 122}, {"opponent": "skeledirge", "rating": 136}, {"opponent": "virizion", "rating": 263}, {"opponent": "cradily", "rating": 294}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 43765}, {"moveId": "BITE", "uses": 19235}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28811}, {"moveId": "CRUNCH", "uses": 28136}, {"moveId": "SOLAR_BEAM", "uses": 6114}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "CRUNCH"], "score": 40.5}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Ribombee", "rating": 585, "matchups": [{"opponent": "virizion", "rating": 852, "opRating": 147}, {"opponent": "golisopod", "rating": 556, "opRating": 443}, {"opponent": "bellibolt", "rating": 545, "opRating": 454}, {"opponent": "<PERSON>ras", "rating": 538, "opRating": 461}, {"opponent": "samu<PERSON>t", "rating": 528, "opRating": 471}], "counters": [{"opponent": "typhlosion_shadow", "rating": 92}, {"opponent": "victree<PERSON>_shadow", "rating": 110}, {"opponent": "tentacruel", "rating": 201}, {"opponent": "skeledirge", "rating": 250}, {"opponent": "cradily", "rating": 402}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 30526}, {"moveId": "CHARM", "uses": 18353}, {"moveId": "STRUGGLE_BUG", "uses": 14107}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 28673}, {"moveId": "BUG_BUZZ", "uses": 27967}, {"moveId": "DRAINING_KISS", "uses": 6426}]}, "moveset": ["FAIRY_WIND", "DAZZLING_GLEAM", "BUG_BUZZ"], "score": 40.4}, {"speciesId": "sealeo", "speciesName": "<PERSON><PERSON>", "rating": 468, "matchups": [{"opponent": "magcargo", "rating": 860, "opRating": 139}, {"opponent": "staraptor_shadow", "rating": 790, "opRating": 209}, {"opponent": "<PERSON><PERSON>_<PERSON>", "rating": 782, "opRating": 217}, {"opponent": "darmanitan_standard_shadow", "rating": 755, "opRating": 244}, {"opponent": "darmanitan_standard", "rating": 728, "opRating": 271}], "counters": [{"opponent": "bellibolt", "rating": 215}, {"opponent": "tentacruel", "rating": 215}, {"opponent": "cradily", "rating": 250}, {"opponent": "<PERSON>ras", "rating": 253}, {"opponent": "virizion", "rating": 257}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 35913}, {"moveId": "WATER_GUN", "uses": 27087}], "chargedMoves": [{"moveId": "SURF", "uses": 22044}, {"moveId": "BODY_SLAM", "uses": 19717}, {"moveId": "AURORA_BEAM", "uses": 10750}, {"moveId": "WATER_PULSE", "uses": 5645}, {"moveId": "RETURN", "uses": 4940}]}, "moveset": ["POWDER_SNOW", "SURF", "BODY_SLAM"], "score": 40.4}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 586, "matchups": [{"opponent": "jellicent", "rating": 835, "opRating": 164}, {"opponent": "stunfisk", "rating": 815, "opRating": 184}, {"opponent": "feraligatr", "rating": 691, "opRating": 308}, {"opponent": "samu<PERSON>t", "rating": 687, "opRating": 312}, {"opponent": "bellibolt", "rating": 503, "opRating": 496}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 101}, {"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "golisopod", "rating": 145}, {"opponent": "skeledirge", "rating": 244}, {"opponent": "cradily", "rating": 300}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 36853}, {"moveId": "INFESTATION", "uses": 26147}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21503}, {"moveId": "SLUDGE_BOMB", "uses": 16575}, {"moveId": "POWER_WHIP", "uses": 10775}, {"moveId": "RETURN", "uses": 9474}, {"moveId": "SOLAR_BEAM", "uses": 4521}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 40.1}, {"speciesId": "simipour", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 477, "matchups": [{"opponent": "turtonator", "rating": 774, "opRating": 225}, {"opponent": "rotom_heat", "rating": 716, "opRating": 283}, {"opponent": "crustle", "rating": 558, "opRating": 441}, {"opponent": "talonflame", "rating": 546, "opRating": 453}, {"opponent": "stunfisk", "rating": 527, "opRating": 472}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "golisopod", "rating": 180}, {"opponent": "<PERSON>ras", "rating": 270}, {"opponent": "cradily", "rating": 316}, {"opponent": "bellibolt", "rating": 345}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 38573}, {"moveId": "BITE", "uses": 24427}], "chargedMoves": [{"moveId": "SURF", "uses": 30399}, {"moveId": "CRUNCH", "uses": 26751}, {"moveId": "HYDRO_PUMP", "uses": 5832}]}, "moveset": ["WATER_GUN", "SURF", "CRUNCH"], "score": 39.9}, {"speciesId": "slowbro", "speciesName": "Slowbro", "rating": 520, "matchups": [{"opponent": "talonflame", "rating": 674, "opRating": 325}, {"opponent": "typhlosion_shadow", "rating": 599, "opRating": 400}, {"opponent": "turtonator", "rating": 599, "opRating": 400}, {"opponent": "swampert", "rating": 545, "opRating": 454}, {"opponent": "virizion", "rating": 518, "opRating": 481}], "counters": [{"opponent": "bellibolt", "rating": 167}, {"opponent": "golisopod", "rating": 187}, {"opponent": "victree<PERSON>_shadow", "rating": 191}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "cradily", "rating": 355}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 35583}, {"moveId": "WATER_GUN", "uses": 27417}], "chargedMoves": [{"moveId": "SURF", "uses": 18415}, {"moveId": "PSYCHIC", "uses": 12061}, {"moveId": "ICE_BEAM", "uses": 11458}, {"moveId": "SCALD", "uses": 9058}, {"moveId": "RETURN", "uses": 7385}, {"moveId": "WATER_PULSE", "uses": 4686}]}, "moveset": ["WATER_GUN", "SURF", "ICE_BEAM"], "score": 39.9}, {"speciesId": "slowking", "speciesName": "Slowking", "rating": 521, "matchups": [{"opponent": "talonflame", "rating": 674, "opRating": 325}, {"opponent": "typhlosion_shadow", "rating": 599, "opRating": 400}, {"opponent": "turtonator", "rating": 599, "opRating": 400}, {"opponent": "swampert", "rating": 545, "opRating": 454}, {"opponent": "virizion", "rating": 518, "opRating": 481}], "counters": [{"opponent": "bellibolt", "rating": 167}, {"opponent": "golisopod", "rating": 187}, {"opponent": "victree<PERSON>_shadow", "rating": 191}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "cradily", "rating": 355}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 35597}, {"moveId": "WATER_GUN", "uses": 27403}], "chargedMoves": [{"moveId": "SURF", "uses": 19172}, {"moveId": "PSYCHIC", "uses": 12184}, {"moveId": "SCALD", "uses": 9407}, {"moveId": "BLIZZARD", "uses": 8126}, {"moveId": "RETURN", "uses": 7433}, {"moveId": "FIRE_BLAST", "uses": 6581}]}, "moveset": ["WATER_GUN", "SURF", "PSYCHIC"], "score": 39.7}, {"speciesId": "zarude", "speciesName": "Zarude", "rating": 585, "matchups": [{"opponent": "feraligatr", "rating": 891, "opRating": 108}, {"opponent": "stunfisk", "rating": 821, "opRating": 178}, {"opponent": "jellicent", "rating": 780, "opRating": 219}, {"opponent": "bellibolt", "rating": 630, "opRating": 369}, {"opponent": "<PERSON>ras", "rating": 566, "opRating": 433}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 101}, {"opponent": "typhlosion_shadow", "rating": 135}, {"opponent": "golisopod", "rating": 145}, {"opponent": "virizion", "rating": 245}, {"opponent": "cradily", "rating": 300}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 41375}, {"moveId": "BITE", "uses": 21625}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 28050}, {"moveId": "POWER_WHIP", "uses": 25793}, {"moveId": "ENERGY_BALL", "uses": 9323}]}, "moveset": ["VINE_WHIP", "DARK_PULSE", "POWER_WHIP"], "score": 39.7}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 502, "matchups": [{"opponent": "toxtricity", "rating": 778, "opRating": 221}, {"opponent": "stunfisk", "rating": 672, "opRating": 327}, {"opponent": "cradily", "rating": 626, "opRating": 373}, {"opponent": "virizion", "rating": 623, "opRating": 376}, {"opponent": "gastrodon", "rating": 619, "opRating": 380}], "counters": [{"opponent": "skeledirge", "rating": 147}, {"opponent": "talonflame", "rating": 157}, {"opponent": "<PERSON>ras", "rating": 170}, {"opponent": "feraligatr", "rating": 220}, {"opponent": "golisopod", "rating": 226}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 35424}, {"moveId": "CONFUSION", "uses": 27576}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 27769}, {"moveId": "BULLDOZE", "uses": 24766}, {"moveId": "PSYBEAM", "uses": 10536}]}, "moveset": ["BUG_BITE", "BULLDOZE", "BUG_BUZZ"], "score": 39.6}, {"speciesId": "cloyster_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 535, "matchups": [{"opponent": "cradily_shadow", "rating": 811, "opRating": 188}, {"opponent": "stunfisk", "rating": 631, "opRating": 368}, {"opponent": "virizion", "rating": 586, "opRating": 413}, {"opponent": "swampert", "rating": 549, "opRating": 450}, {"opponent": "drampa", "rating": 508, "opRating": 491}], "counters": [{"opponent": "skeledirge", "rating": 136}, {"opponent": "<PERSON>ras", "rating": 147}, {"opponent": "typhlosion_shadow", "rating": 188}, {"opponent": "bellibolt", "rating": 310}, {"opponent": "tentacruel", "rating": 313}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 40210}, {"moveId": "FROST_BREATH", "uses": 22790}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 21313}, {"moveId": "LIQUIDATION", "uses": 14409}, {"moveId": "RAZOR_SHELL", "uses": 10976}, {"moveId": "ICY_WIND", "uses": 7518}, {"moveId": "BLIZZARD", "uses": 3236}, {"moveId": "HYDRO_PUMP", "uses": 2697}, {"moveId": "AURORA_BEAM", "uses": 2620}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "HYDRO_PUMP"], "score": 39.5}, {"speciesId": "seadra_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 390, "matchups": [{"opponent": "turtonator", "rating": 755, "opRating": 244}, {"opponent": "palkia_shadow", "rating": 644, "opRating": 355}, {"opponent": "darmanitan_standard_shadow", "rating": 644, "opRating": 355}, {"opponent": "palkia", "rating": 611, "opRating": 388}, {"opponent": "drampa", "rating": 607, "opRating": 392}], "counters": [{"opponent": "virizion", "rating": 230}, {"opponent": "bellibolt", "rating": 242}, {"opponent": "<PERSON>ras", "rating": 253}, {"opponent": "cradily", "rating": 269}, {"opponent": "tentacruel", "rating": 278}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 35462}, {"moveId": "WATER_GUN", "uses": 27538}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 18587}, {"moveId": "DRAGON_PULSE", "uses": 16896}, {"moveId": "BLIZZARD", "uses": 15371}, {"moveId": "AURORA_BEAM", "uses": 12208}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["DRAGON_BREATH", "HYDRO_PUMP", "BLIZZARD"], "score": 39.5}, {"speciesId": "alomomola", "speciesName": "Alomomola", "rating": 450, "matchups": [{"opponent": "talonflame", "rating": 728, "opRating": 271}, {"opponent": "typhlosion_shadow", "rating": 706, "opRating": 293}, {"opponent": "skeledirge", "rating": 675, "opRating": 324}, {"opponent": "crustle", "rating": 596, "opRating": 403}, {"opponent": "lickilicky", "rating": 515, "opRating": 484}], "counters": [{"opponent": "<PERSON>ras", "rating": 235}, {"opponent": "bellibolt", "rating": 237}, {"opponent": "virizion", "rating": 245}, {"opponent": "tentacruel", "rating": 254}, {"opponent": "cradily", "rating": 255}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 6062}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4260}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4147}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4041}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3906}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3662}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3640}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3594}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3580}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3561}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3472}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3466}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3399}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3378}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3308}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3218}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2558}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 21862}, {"moveId": "HYDRO_PUMP", "uses": 21539}, {"moveId": "BLIZZARD", "uses": 19669}]}, "moveset": ["WATERFALL", "HYDRO_PUMP", "BLIZZARD"], "score": 39.3}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Regieleki", "rating": 603, "matchups": [{"opponent": "feraligatr", "rating": 803, "opRating": 196}, {"opponent": "golisopod", "rating": 738, "opRating": 261}, {"opponent": "samu<PERSON>t", "rating": 738, "opRating": 261}, {"opponent": "talonflame", "rating": 667, "opRating": 332}, {"opponent": "typhlosion_shadow", "rating": 596, "opRating": 403}], "counters": [{"opponent": "gastrodon", "rating": 49}, {"opponent": "swampert", "rating": 62}, {"opponent": "victree<PERSON>_shadow", "rating": 116}, {"opponent": "cradily", "rating": 197}, {"opponent": "bellibolt", "rating": 340}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 23046}, {"moveId": "THUNDER_SHOCK", "uses": 22182}, {"moveId": "LOCK_ON", "uses": 17807}], "chargedMoves": [{"moveId": "THUNDER_CAGE", "uses": 41115}, {"moveId": "HYPER_BEAM", "uses": 8848}, {"moveId": "THUNDER", "uses": 7249}, {"moveId": "ZAP_CANNON", "uses": 5972}]}, "moveset": ["LOCK_ON", "THUNDER", "HYPER_BEAM"], "score": 39.3}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 512, "matchups": [{"opponent": "tentacruel", "rating": 683, "opRating": 316}, {"opponent": "jellicent", "rating": 683, "opRating": 316}, {"opponent": "swampert", "rating": 629, "opRating": 370}, {"opponent": "stunfisk", "rating": 611, "opRating": 388}, {"opponent": "feraligatr", "rating": 587, "opRating": 412}], "counters": [{"opponent": "golisopod", "rating": 145}, {"opponent": "talonflame", "rating": 181}, {"opponent": "virizion", "rating": 200}, {"opponent": "skeledirge", "rating": 291}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 49499}, {"moveId": "TAKE_DOWN", "uses": 13501}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22806}, {"moveId": "TRAILBLAZE", "uses": 17726}, {"moveId": "MEGAHORN", "uses": 12768}, {"moveId": "HYPER_BEAM", "uses": 6455}, {"moveId": "SOLAR_BEAM", "uses": 3296}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "TRAILBLAZE"], "score": 39.3}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 584, "matchups": [{"opponent": "stunfisk", "rating": 791, "opRating": 208}, {"opponent": "<PERSON>ras", "rating": 765, "opRating": 234}, {"opponent": "jellicent", "rating": 694, "opRating": 305}, {"opponent": "feraligatr", "rating": 664, "opRating": 335}, {"opponent": "swampert", "rating": 593, "opRating": 406}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 83}, {"opponent": "typhlosion_shadow", "rating": 152}, {"opponent": "golisopod", "rating": 180}, {"opponent": "skeledirge", "rating": 297}, {"opponent": "cradily", "rating": 330}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 37012}, {"moveId": "INFESTATION", "uses": 25988}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 24811}, {"moveId": "SLUDGE_BOMB", "uses": 20384}, {"moveId": "POWER_WHIP", "uses": 12415}, {"moveId": "SOLAR_BEAM", "uses": 5216}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 39.3}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 479, "matchups": [{"opponent": "swampert", "rating": 935, "opRating": 64}, {"opponent": "gastrodon", "rating": 894, "opRating": 105}, {"opponent": "stunfisk", "rating": 864, "opRating": 135}, {"opponent": "feraligatr", "rating": 620, "opRating": 379}, {"opponent": "jellicent", "rating": 576, "opRating": 423}], "counters": [{"opponent": "talonflame", "rating": 145}, {"opponent": "victree<PERSON>_shadow", "rating": 167}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "virizion", "rating": 215}, {"opponent": "typhlosion_shadow", "rating": 235}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 34367}, {"moveId": "BITE", "uses": 28633}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 26993}, {"moveId": "STONE_EDGE", "uses": 15980}, {"moveId": "EARTHQUAKE", "uses": 10835}, {"moveId": "SAND_TOMB", "uses": 6229}, {"moveId": "SOLAR_BEAM", "uses": 3011}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 39.2}, {"speciesId": "accelgor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "incineroar", "rating": 853, "opRating": 146}, {"opponent": "meganium_shadow", "rating": 853, "opRating": 146}, {"opponent": "gastrodon", "rating": 829, "opRating": 170}, {"opponent": "barbara<PERSON>", "rating": 712, "opRating": 287}, {"opponent": "swampert", "rating": 637, "opRating": 362}], "counters": [{"opponent": "typhlosion_shadow", "rating": 168}, {"opponent": "victree<PERSON>_shadow", "rating": 230}, {"opponent": "tentacruel", "rating": 248}, {"opponent": "bellibolt", "rating": 297}, {"opponent": "<PERSON>ras", "rating": 311}], "moves": {"fastMoves": [{"moveId": "WATER_SHURIKEN", "uses": 22863}, {"moveId": "ACID", "uses": 22701}, {"moveId": "INFESTATION", "uses": 17476}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 14845}, {"moveId": "SIGNAL_BEAM", "uses": 12707}, {"moveId": "ENERGY_BALL", "uses": 12660}, {"moveId": "FOCUS_BLAST", "uses": 10333}, {"moveId": "RETURN", "uses": 8965}, {"moveId": "ACID_SPRAY", "uses": 3433}]}, "moveset": ["WATER_SHURIKEN", "BUG_BUZZ", "ENERGY_BALL"], "score": 39.1}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 529, "matchups": [{"opponent": "talonflame", "rating": 847, "opRating": 152}, {"opponent": "typhlosion_shadow", "rating": 802, "opRating": 197}, {"opponent": "skeledirge", "rating": 785, "opRating": 214}, {"opponent": "<PERSON>ras", "rating": 532, "opRating": 467}, {"opponent": "jellicent", "rating": 514, "opRating": 485}], "counters": [{"opponent": "virizion", "rating": 57}, {"opponent": "victree<PERSON>_shadow", "rating": 146}, {"opponent": "golisopod", "rating": 202}, {"opponent": "cradily", "rating": 241}, {"opponent": "bellibolt", "rating": 265}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 35778}, {"moveId": "WATER_GUN", "uses": 21162}, {"moveId": "TAKE_DOWN", "uses": 6074}], "chargedMoves": [{"moveId": "SURF", "uses": 31289}, {"moveId": "HYPER_FANG", "uses": 25004}, {"moveId": "HYPER_BEAM", "uses": 6703}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 38.9}, {"speciesId": "slowbro_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 505, "matchups": [{"opponent": "talonflame", "rating": 615, "opRating": 384}, {"opponent": "stunfisk", "rating": 596, "opRating": 403}, {"opponent": "virizion", "rating": 564, "opRating": 435}, {"opponent": "typhlosion_shadow", "rating": 559, "opRating": 440}, {"opponent": "swampert", "rating": 513, "opRating": 486}], "counters": [{"opponent": "golisopod", "rating": 152}, {"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "bellibolt", "rating": 197}, {"opponent": "tentacruel", "rating": 295}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 36775}, {"moveId": "WATER_GUN", "uses": 26225}], "chargedMoves": [{"moveId": "SURF", "uses": 20613}, {"moveId": "PSYCHIC", "uses": 13879}, {"moveId": "ICE_BEAM", "uses": 12940}, {"moveId": "SCALD", "uses": 10289}, {"moveId": "WATER_PULSE", "uses": 5283}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "SURF", "ICE_BEAM"], "score": 38.8}, {"speciesId": "slowking_shadow", "speciesName": "Slowking (Shadow)", "rating": 510, "matchups": [{"opponent": "talonflame", "rating": 615, "opRating": 384}, {"opponent": "stunfisk", "rating": 596, "opRating": 403}, {"opponent": "virizion", "rating": 564, "opRating": 435}, {"opponent": "typhlosion_shadow", "rating": 559, "opRating": 440}, {"opponent": "swampert", "rating": 513, "opRating": 486}], "counters": [{"opponent": "golisopod", "rating": 152}, {"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "bellibolt", "rating": 197}, {"opponent": "tentacruel", "rating": 289}, {"opponent": "cradily", "rating": 316}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 36827}, {"moveId": "WATER_GUN", "uses": 26173}], "chargedMoves": [{"moveId": "SURF", "uses": 21435}, {"moveId": "PSYCHIC", "uses": 14101}, {"moveId": "SCALD", "uses": 10832}, {"moveId": "BLIZZARD", "uses": 9177}, {"moveId": "FIRE_BLAST", "uses": 7487}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "SURF", "PSYCHIC"], "score": 38.8}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 596, "matchups": [{"opponent": "forretress", "rating": 811, "opRating": 188}, {"opponent": "typhlosion_shadow", "rating": 707, "opRating": 292}, {"opponent": "drampa", "rating": 630, "opRating": 369}, {"opponent": "virizion", "rating": 596, "opRating": 403}, {"opponent": "victree<PERSON>_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "tentacruel", "rating": 156}, {"opponent": "feraligatr", "rating": 160}, {"opponent": "cradily", "rating": 200}, {"opponent": "bellibolt", "rating": 227}, {"opponent": "skeledirge", "rating": 247}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 33044}, {"moveId": "FIRE_SPIN", "uses": 29956}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 22159}, {"moveId": "FLAMETHROWER", "uses": 13343}, {"moveId": "OVERHEAT", "uses": 12410}, {"moveId": "LAST_RESORT", "uses": 9429}, {"moveId": "FIRE_BLAST", "uses": 3505}, {"moveId": "HEAT_WAVE", "uses": 2158}]}, "moveset": ["EMBER", "SUPER_POWER", "FLAMETHROWER"], "score": 38.7}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 554, "matchups": [{"opponent": "manectric", "rating": 811, "opRating": 188}, {"opponent": "drampa", "rating": 688, "opRating": 311}, {"opponent": "swampert", "rating": 614, "opRating": 385}, {"opponent": "gastrodon", "rating": 545, "opRating": 454}, {"opponent": "turtonator", "rating": 516, "opRating": 483}], "counters": [{"opponent": "skeledirge", "rating": 113}, {"opponent": "<PERSON>ras", "rating": 121}, {"opponent": "typhlosion_shadow", "rating": 162}, {"opponent": "talonflame", "rating": 193}, {"opponent": "bellibolt", "rating": 327}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 39110}, {"moveId": "FROST_BREATH", "uses": 23890}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19730}, {"moveId": "LIQUIDATION", "uses": 13191}, {"moveId": "RAZOR_SHELL", "uses": 10023}, {"moveId": "ICY_WIND", "uses": 6945}, {"moveId": "RETURN", "uses": 5209}, {"moveId": "BLIZZARD", "uses": 3028}, {"moveId": "HYDRO_PUMP", "uses": 2487}, {"moveId": "AURORA_BEAM", "uses": 2394}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "HYDRO_PUMP"], "score": 38.4}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 436, "matchups": [{"opponent": "bewear", "rating": 896, "opRating": 103}, {"opponent": "mor<PERSON><PERSON>_full_belly", "rating": 871, "opRating": 128}, {"opponent": "quaquaval", "rating": 829, "opRating": 170}, {"opponent": "keldeo_ordinary", "rating": 783, "opRating": 216}, {"opponent": "drampa", "rating": 646, "opRating": 353}], "counters": [{"opponent": "tentacruel", "rating": 180}, {"opponent": "skeledirge", "rating": 180}, {"opponent": "<PERSON>ras", "rating": 261}, {"opponent": "bellibolt", "rating": 265}, {"opponent": "cradily", "rating": 336}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 50963}, {"moveId": "POUND", "uses": 12037}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 31599}, {"moveId": "THUNDERBOLT", "uses": 17966}, {"moveId": "HYPER_BEAM", "uses": 13416}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 38.3}, {"speciesId": "kyogre_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 448, "matchups": [{"opponent": "rapidash", "rating": 843, "opRating": 156}, {"opponent": "typhlosion_shadow", "rating": 821, "opRating": 178}, {"opponent": "salazzle", "rating": 817, "opRating": 182}, {"opponent": "talonflame", "rating": 806, "opRating": 193}, {"opponent": "lickilicky", "rating": 554, "opRating": 445}], "counters": [{"opponent": "virizion", "rating": 172}, {"opponent": "<PERSON>ras", "rating": 196}, {"opponent": "tentacruel", "rating": 210}, {"opponent": "cradily", "rating": 269}, {"opponent": "bellibolt", "rating": 287}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 63000}], "chargedMoves": [{"moveId": "SURF", "uses": 21352}, {"moveId": "THUNDER", "uses": 13591}, {"moveId": "ORIGIN_PULSE", "uses": 12806}, {"moveId": "BLIZZARD", "uses": 11122}, {"moveId": "HYDRO_PUMP", "uses": 4071}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATERFALL", "SURF", "ORIGIN_PULSE"], "score": 38}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 502, "matchups": [{"opponent": "talonflame", "rating": 873, "opRating": 126}, {"opponent": "rapidash", "rating": 864, "opRating": 135}, {"opponent": "typhlosion_shadow", "rating": 838, "opRating": 161}, {"opponent": "skeledirge", "rating": 570, "opRating": 429}, {"opponent": "feraligatr", "rating": 526, "opRating": 473}], "counters": [{"opponent": "virizion", "rating": 57}, {"opponent": "victree<PERSON>_shadow", "rating": 128}, {"opponent": "cradily", "rating": 202}, {"opponent": "bellibolt", "rating": 222}, {"opponent": "tentacruel", "rating": 272}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 34593}, {"moveId": "WATER_GUN", "uses": 22225}, {"moveId": "TAKE_DOWN", "uses": 6220}], "chargedMoves": [{"moveId": "SURF", "uses": 28208}, {"moveId": "HYPER_FANG", "uses": 21629}, {"moveId": "RETURN", "uses": 7472}, {"moveId": "HYPER_BEAM", "uses": 5722}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 37.4}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 575, "matchups": [{"opponent": "swampert", "rating": 904, "opRating": 95}, {"opponent": "feraligatr", "rating": 843, "opRating": 156}, {"opponent": "stunfisk", "rating": 782, "opRating": 217}, {"opponent": "samu<PERSON>t", "rating": 690, "opRating": 309}, {"opponent": "bellibolt", "rating": 509, "opRating": 490}], "counters": [{"opponent": "talonflame", "rating": 74}, {"opponent": "victree<PERSON>_shadow", "rating": 83}, {"opponent": "skeledirge", "rating": 102}, {"opponent": "typhlosion_shadow", "rating": 122}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 43639}, {"moveId": "BITE", "uses": 19361}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 26740}, {"moveId": "CRUNCH", "uses": 26633}, {"moveId": "ENERGY_BALL", "uses": 9459}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 37.4}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 458, "matchups": [{"opponent": "swampert", "rating": 926, "opRating": 73}, {"opponent": "gastrodon", "rating": 891, "opRating": 108}, {"opponent": "stunfisk", "rating": 800, "opRating": 200}, {"opponent": "lickilicky", "rating": 608, "opRating": 391}, {"opponent": "bellibolt", "rating": 561, "opRating": 438}], "counters": [{"opponent": "talonflame", "rating": 110}, {"opponent": "skeledirge", "rating": 169}, {"opponent": "victree<PERSON>_shadow", "rating": 197}, {"opponent": "virizion", "rating": 233}, {"opponent": "cradily", "rating": 391}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 33526}, {"moveId": "BITE", "uses": 29474}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 24843}, {"moveId": "STONE_EDGE", "uses": 14250}, {"moveId": "EARTHQUAKE", "uses": 9843}, {"moveId": "SAND_TOMB", "uses": 5777}, {"moveId": "RETURN", "uses": 5448}, {"moveId": "SOLAR_BEAM", "uses": 2838}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 37.2}, {"speciesId": "ferroth<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 630, "matchups": [{"opponent": "feraligatr", "rating": 750, "opRating": 250}, {"opponent": "tentacruel", "rating": 690, "opRating": 309}, {"opponent": "victree<PERSON>_shadow", "rating": 678, "opRating": 321}, {"opponent": "<PERSON>ras", "rating": 603, "opRating": 396}, {"opponent": "cradily", "rating": 587, "opRating": 412}], "counters": [{"opponent": "talonflame", "rating": 32}, {"opponent": "skeledirge", "rating": 52}, {"opponent": "turtonator", "rating": 52}, {"opponent": "typhlosion_shadow", "rating": 82}, {"opponent": "virizion", "rating": 333}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35402}, {"moveId": "METAL_CLAW", "uses": 27598}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 23722}, {"moveId": "MIRROR_SHOT", "uses": 14763}, {"moveId": "THUNDER", "uses": 12852}, {"moveId": "FLASH_CANNON", "uses": 7743}, {"moveId": "ACID_SPRAY", "uses": 3877}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "FLASH_CANNON"], "score": 36.7}, {"speciesId": "seadra", "speciesName": "<PERSON>dra", "rating": 357, "matchups": [{"opponent": "drampa", "rating": 640, "opRating": 359}, {"opponent": "palkia_shadow", "rating": 611, "opRating": 388}, {"opponent": "zekrom", "rating": 596, "opRating": 403}, {"opponent": "reshiram", "rating": 596, "opRating": 403}, {"opponent": "crawdaunt_shadow", "rating": 537, "opRating": 462}], "counters": [{"opponent": "cradily", "rating": 227}, {"opponent": "<PERSON>ras", "rating": 235}, {"opponent": "bellibolt", "rating": 242}, {"opponent": "virizion", "rating": 266}, {"opponent": "tentacruel", "rating": 278}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 35295}, {"moveId": "WATER_GUN", "uses": 27705}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 14996}, {"moveId": "DRAGON_PULSE", "uses": 12836}, {"moveId": "RETURN", "uses": 12815}, {"moveId": "BLIZZARD", "uses": 12421}, {"moveId": "AURORA_BEAM", "uses": 9919}]}, "moveset": ["DRAGON_BREATH", "HYDRO_PUMP", "BLIZZARD"], "score": 36.6}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 606, "matchups": [{"opponent": "talonflame", "rating": 845, "opRating": 154}, {"opponent": "feraligatr", "rating": 687, "opRating": 312}, {"opponent": "tentacruel", "rating": 629, "opRating": 370}, {"opponent": "<PERSON>ras", "rating": 595, "opRating": 404}, {"opponent": "golisopod", "rating": 550, "opRating": 450}], "counters": [{"opponent": "cradily", "rating": 75}, {"opponent": "gastrodon", "rating": 87}, {"opponent": "victree<PERSON>_shadow", "rating": 110}, {"opponent": "virizion", "rating": 260}, {"opponent": "bellibolt", "rating": 295}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 35462}, {"moveId": "ASTONISH", "uses": 27538}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 30004}, {"moveId": "HYDRO_PUMP", "uses": 19933}, {"moveId": "THUNDER", "uses": 13065}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 36.4}, {"speciesId": "shiinotic", "speciesName": "Shiinotic", "rating": 491, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 774, "opRating": 225}, {"opponent": "gastrodon", "rating": 714, "opRating": 285}, {"opponent": "virizion", "rating": 651, "opRating": 348}, {"opponent": "stunfisk", "rating": 595, "opRating": 404}, {"opponent": "jellicent", "rating": 552, "opRating": 447}], "counters": [{"opponent": "tentacruel", "rating": 144}, {"opponent": "victree<PERSON>_shadow", "rating": 167}, {"opponent": "talonflame", "rating": 193}, {"opponent": "feraligatr", "rating": 204}, {"opponent": "cradily", "rating": 241}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 63000}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 22984}, {"moveId": "MOONBLAST", "uses": 21749}, {"moveId": "SLUDGE_BOMB", "uses": 18290}]}, "moveset": ["ASTONISH", "MOONBLAST", "SEED_BOMB"], "score": 36.3}, {"speciesId": "tatsugiri_curly", "speciesName": "<PERSON><PERSON><PERSON> (Curly)", "rating": 495, "matchups": [{"opponent": "skeledirge", "rating": 783, "opRating": 216}, {"opponent": "typhlosion_shadow", "rating": 648, "opRating": 351}, {"opponent": "stunfisk", "rating": 648, "opRating": 351}, {"opponent": "swampert", "rating": 599, "opRating": 400}, {"opponent": "talonflame", "rating": 595, "opRating": 404}], "counters": [{"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 178}, {"opponent": "tentacruel", "rating": 186}, {"opponent": "golisopod", "rating": 195}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51707}, {"moveId": "TAKE_DOWN", "uses": 11293}], "chargedMoves": [{"moveId": "SURF", "uses": 22345}, {"moveId": "OUTRAGE", "uses": 20737}, {"moveId": "MUDDY_WATER", "uses": 15599}, {"moveId": "HYDRO_PUMP", "uses": 4286}]}, "moveset": ["WATER_GUN", "SURF", "OUTRAGE"], "score": 36.3}, {"speciesId": "tatsugiri_droopy", "speciesName": "<PERSON><PERSON><PERSON> (Droopy)", "rating": 495, "matchups": [{"opponent": "skeledirge", "rating": 783, "opRating": 216}, {"opponent": "typhlosion_shadow", "rating": 648, "opRating": 351}, {"opponent": "stunfisk", "rating": 648, "opRating": 351}, {"opponent": "swampert", "rating": 599, "opRating": 400}, {"opponent": "talonflame", "rating": 595, "opRating": 404}], "counters": [{"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 178}, {"opponent": "tentacruel", "rating": 186}, {"opponent": "golisopod", "rating": 195}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51707}, {"moveId": "TAKE_DOWN", "uses": 11293}], "chargedMoves": [{"moveId": "SURF", "uses": 22345}, {"moveId": "OUTRAGE", "uses": 20737}, {"moveId": "MUDDY_WATER", "uses": 15599}, {"moveId": "HYDRO_PUMP", "uses": 4286}]}, "moveset": ["WATER_GUN", "SURF", "OUTRAGE"], "score": 36.3}, {"speciesId": "tatsugiri_stretchy", "speciesName": "Tatsugiri (Stretchy)", "rating": 495, "matchups": [{"opponent": "skeledirge", "rating": 783, "opRating": 216}, {"opponent": "typhlosion_shadow", "rating": 648, "opRating": 351}, {"opponent": "stunfisk", "rating": 648, "opRating": 351}, {"opponent": "swampert", "rating": 599, "opRating": 400}, {"opponent": "talonflame", "rating": 595, "opRating": 404}], "counters": [{"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 178}, {"opponent": "tentacruel", "rating": 186}, {"opponent": "golisopod", "rating": 195}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51707}, {"moveId": "TAKE_DOWN", "uses": 11293}], "chargedMoves": [{"moveId": "SURF", "uses": 22345}, {"moveId": "OUTRAGE", "uses": 20737}, {"moveId": "MUDDY_WATER", "uses": 15599}, {"moveId": "HYDRO_PUMP", "uses": 4286}]}, "moveset": ["WATER_GUN", "SURF", "OUTRAGE"], "score": 36.3}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 543, "matchups": [{"opponent": "gastrodon", "rating": 702, "opRating": 297}, {"opponent": "swampert", "rating": 631, "opRating": 368}, {"opponent": "virizion", "rating": 581, "opRating": 418}, {"opponent": "golisopod", "rating": 570, "opRating": 429}, {"opponent": "samu<PERSON>t", "rating": 514, "opRating": 485}], "counters": [{"opponent": "tentacruel", "rating": 118}, {"opponent": "feraligatr", "rating": 154}, {"opponent": "typhlosion_shadow", "rating": 168}, {"opponent": "bellibolt", "rating": 192}, {"opponent": "cradily", "rating": 275}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 32558}, {"moveId": "STEEL_WING", "uses": 30442}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20653}, {"moveId": "DRILL_PECK", "uses": 18029}, {"moveId": "AIR_CUTTER", "uses": 17706}, {"moveId": "AERIAL_ACE", "uses": 6588}]}, "moveset": ["STEEL_WING", "BRAVE_BIRD", "DRILL_PECK"], "score": 36.2}, {"speciesId": "rilla<PERSON>m", "speciesName": "R<PERSON>boom", "rating": 451, "matchups": [{"opponent": "swampert", "rating": 925, "opRating": 74}, {"opponent": "gastrodon", "rating": 892, "opRating": 107}, {"opponent": "feraligatr", "rating": 687, "opRating": 312}, {"opponent": "stunfisk", "rating": 687, "opRating": 312}, {"opponent": "jellicent", "rating": 550, "opRating": 449}], "counters": [{"opponent": "talonflame", "rating": 145}, {"opponent": "victree<PERSON>_shadow", "rating": 149}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "virizion", "rating": 215}, {"opponent": "cradily", "rating": 266}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 36123}, {"moveId": "SCRATCH", "uses": 26877}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 29670}, {"moveId": "EARTH_POWER", "uses": 22715}, {"moveId": "ENERGY_BALL", "uses": 10630}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "EARTH_POWER"], "score": 36.2}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 434, "matchups": [{"opponent": "bewear", "rating": 812, "opRating": 187}, {"opponent": "mor<PERSON><PERSON>_full_belly", "rating": 791, "opRating": 208}, {"opponent": "pawmot", "rating": 755, "opRating": 244}, {"opponent": "virizion", "rating": 617, "opRating": 382}, {"opponent": "drampa", "rating": 610, "opRating": 389}], "counters": [{"opponent": "tentacruel", "rating": 177}, {"opponent": "skeledirge", "rating": 177}, {"opponent": "victree<PERSON>_shadow", "rating": 197}, {"opponent": "cradily", "rating": 227}, {"opponent": "<PERSON>ras", "rating": 233}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 8113}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4082}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3862}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3589}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3588}, {"moveId": "CHARM", "uses": 3583}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3320}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3279}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3225}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3217}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3216}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3063}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3001}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2978}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2894}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2892}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2828}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2345}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 29763}, {"moveId": "HYPER_BEAM", "uses": 21352}, {"moveId": "SOLAR_BEAM", "uses": 11997}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 36}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 594, "matchups": [{"opponent": "virizion", "rating": 882, "opRating": 117}, {"opponent": "venusaur", "rating": 868, "opRating": 131}, {"opponent": "forretress", "rating": 833, "opRating": 166}, {"opponent": "zapdos", "rating": 656, "opRating": 343}, {"opponent": "typhlosion_shadow", "rating": 560, "opRating": 439}], "counters": [{"opponent": "tentacruel", "rating": 136}, {"opponent": "skeledirge", "rating": 158}, {"opponent": "<PERSON>ras", "rating": 175}, {"opponent": "cradily", "rating": 205}, {"opponent": "bellibolt", "rating": 230}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 41261}, {"moveId": "SCRATCH", "uses": 13196}, {"moveId": "ZEN_HEADBUTT", "uses": 8527}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 22014}, {"moveId": "MYSTICAL_FIRE", "uses": 15764}, {"moveId": "PSYCHIC", "uses": 11392}, {"moveId": "FLAME_CHARGE", "uses": 6064}, {"moveId": "FLAMETHROWER", "uses": 5079}, {"moveId": "FIRE_BLAST", "uses": 2855}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 35.8}, {"speciesId": "kyogre", "speciesName": "Kyogre", "rating": 437, "matchups": [{"opponent": "typhlosion_shadow", "rating": 844, "opRating": 155}, {"opponent": "salazzle", "rating": 844, "opRating": 155}, {"opponent": "rapidash", "rating": 822, "opRating": 177}, {"opponent": "talonflame", "rating": 797, "opRating": 202}, {"opponent": "turtonator", "rating": 521, "opRating": 478}], "counters": [{"opponent": "tentacruel", "rating": 180}, {"opponent": "victree<PERSON>_shadow", "rating": 182}, {"opponent": "virizion", "rating": 184}, {"opponent": "cradily", "rating": 230}, {"opponent": "bellibolt", "rating": 252}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 63000}], "chargedMoves": [{"moveId": "SURF", "uses": 21556}, {"moveId": "THUNDER", "uses": 13690}, {"moveId": "ORIGIN_PULSE", "uses": 12463}, {"moveId": "BLIZZARD", "uses": 11198}, {"moveId": "HYDRO_PUMP", "uses": 4102}]}, "moveset": ["WATERFALL", "SURF", "ORIGIN_PULSE"], "score": 35.8}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 577, "matchups": [{"opponent": "jellicent", "rating": 843, "opRating": 156}, {"opponent": "stunfisk", "rating": 831, "opRating": 168}, {"opponent": "<PERSON>ras", "rating": 722, "opRating": 277}, {"opponent": "forretress", "rating": 691, "opRating": 308}, {"opponent": "swampert", "rating": 621, "opRating": 378}], "counters": [{"opponent": "skeledirge", "rating": 69}, {"opponent": "golisopod", "rating": 102}, {"opponent": "talonflame", "rating": 119}, {"opponent": "tentacruel", "rating": 165}, {"opponent": "cradily", "rating": 286}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 41807}, {"moveId": "RAZOR_LEAF", "uses": 21193}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 29638}, {"moveId": "SOLAR_BEAM", "uses": 13423}, {"moveId": "DAZZLING_GLEAM", "uses": 12473}, {"moveId": "HYPER_BEAM", "uses": 7536}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 35.6}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 550, "matchups": [{"opponent": "venusaur", "rating": 675, "opRating": 324}, {"opponent": "golisopod", "rating": 632, "opRating": 367}, {"opponent": "samu<PERSON>t", "rating": 594, "opRating": 405}, {"opponent": "gastrodon", "rating": 589, "opRating": 410}, {"opponent": "crustle", "rating": 511, "opRating": 488}], "counters": [{"opponent": "bellibolt", "rating": 127}, {"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "cradily", "rating": 166}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "talonflame", "rating": 217}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37604}, {"moveId": "STEEL_WING", "uses": 25396}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18405}, {"moveId": "CLOSE_COMBAT", "uses": 17462}, {"moveId": "FLY", "uses": 15613}, {"moveId": "ROCK_SLIDE", "uses": 9488}, {"moveId": "HEAT_WAVE", "uses": 1925}]}, "moveset": ["AIR_SLASH", "FLY", "CLOSE_COMBAT"], "score": 35.2}, {"speciesId": "raticate_alolan", "speciesName": "Raticate (Alolan)", "rating": 439, "matchups": [{"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 807, "opRating": 192}, {"opponent": "gourgeist_average", "rating": 771, "opRating": 228}, {"opponent": "trevenant", "rating": 722, "opRating": 277}, {"opponent": "oranguru", "rating": 698, "opRating": 301}, {"opponent": "darmanitan_standard_shadow", "rating": 679, "opRating": 320}], "counters": [{"opponent": "virizion", "rating": 84}, {"opponent": "golisopod", "rating": 109}, {"opponent": "bellibolt", "rating": 257}, {"opponent": "cradily", "rating": 266}, {"opponent": "skeledirge", "rating": 280}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 38379}, {"moveId": "BITE", "uses": 24621}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30518}, {"moveId": "HYPER_FANG", "uses": 20234}, {"moveId": "RETURN", "uses": 6997}, {"moveId": "HYPER_BEAM", "uses": 5286}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_FANG"], "score": 35.2}, {"speciesId": "raticate_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Al<PERSON><PERSON>) (Shadow)", "rating": 456, "matchups": [{"opponent": "luxray_shadow", "rating": 911, "opRating": 88}, {"opponent": "manectric_shadow", "rating": 911, "opRating": 88}, {"opponent": "bruxish", "rating": 875, "opRating": 125}, {"opponent": "staraptor_shadow", "rating": 753, "opRating": 246}, {"opponent": "jellicent", "rating": 722, "opRating": 277}], "counters": [{"opponent": "virizion", "rating": 112}, {"opponent": "golisopod", "rating": 145}, {"opponent": "victree<PERSON>_shadow", "rating": 164}, {"opponent": "typhlosion_shadow", "rating": 201}, {"opponent": "cradily", "rating": 308}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39739}, {"moveId": "BITE", "uses": 23261}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 33870}, {"moveId": "HYPER_FANG", "uses": 22910}, {"moveId": "HYPER_BEAM", "uses": 5970}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_FANG"], "score": 35.2}, {"speciesId": "kartana", "speciesName": "Kartana", "rating": 483, "matchups": [{"opponent": "swampert", "rating": 877, "opRating": 122}, {"opponent": "gastrodon", "rating": 779, "opRating": 220}, {"opponent": "feraligatr", "rating": 774, "opRating": 225}, {"opponent": "samu<PERSON>t", "rating": 715, "opRating": 284}, {"opponent": "cradily", "rating": 519, "opRating": 480}], "counters": [{"opponent": "talonflame", "rating": 104}, {"opponent": "skeledirge", "rating": 136}, {"opponent": "typhlosion_shadow", "rating": 149}, {"opponent": "virizion", "rating": 166}, {"opponent": "victree<PERSON>_shadow", "rating": 200}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37478}, {"moveId": "RAZOR_LEAF", "uses": 25522}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26904}, {"moveId": "NIGHT_SLASH", "uses": 13996}, {"moveId": "AERIAL_ACE", "uses": 11483}, {"moveId": "X_SCISSOR", "uses": 10568}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "NIGHT_SLASH"], "score": 35.1}, {"speciesId": "parasect", "speciesName": "Parasect", "rating": 580, "matchups": [{"opponent": "electivire_shadow", "rating": 869, "opRating": 130}, {"opponent": "gastrodon", "rating": 693, "opRating": 306}, {"opponent": "stunfisk", "rating": 679, "opRating": 320}, {"opponent": "swampert", "rating": 605, "opRating": 394}, {"opponent": "serperior", "rating": 563, "opRating": 436}], "counters": [{"opponent": "skeledirge", "rating": 41}, {"opponent": "talonflame", "rating": 44}, {"opponent": "typhlosion_shadow", "rating": 96}, {"opponent": "tentacruel", "rating": 233}, {"opponent": "<PERSON>ras", "rating": 277}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 30052}, {"moveId": "BUG_BITE", "uses": 22879}, {"moveId": "STRUGGLE_BUG", "uses": 10058}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 27159}, {"moveId": "CROSS_POISON", "uses": 24635}, {"moveId": "SOLAR_BEAM", "uses": 11263}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "CROSS_POISON"], "score": 34.8}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 515, "matchups": [{"opponent": "genesect_shock", "rating": 879, "opRating": 120}, {"opponent": "forretress", "rating": 808, "opRating": 191}, {"opponent": "serperior", "rating": 675, "opRating": 325}, {"opponent": "venusaur", "rating": 612, "opRating": 387}, {"opponent": "victree<PERSON>_shadow", "rating": 591, "opRating": 408}], "counters": [{"opponent": "tentacruel", "rating": 162}, {"opponent": "skeledirge", "rating": 180}, {"opponent": "<PERSON>ras", "rating": 227}, {"opponent": "cradily", "rating": 247}, {"opponent": "bellibolt", "rating": 267}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 29060}, {"moveId": "HEX", "uses": 19587}, {"moveId": "FIRE_SPIN", "uses": 14352}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 15068}, {"moveId": "SHADOW_BALL", "uses": 14438}, {"moveId": "OVERHEAT", "uses": 11582}, {"moveId": "ENERGY_BALL", "uses": 10881}, {"moveId": "RETURN", "uses": 6420}, {"moveId": "POLTERGEIST", "uses": 4687}]}, "moveset": ["FIRE_SPIN", "SHADOW_BALL", "FLAME_CHARGE"], "score": 34.6}, {"speciesId": "clawitzer", "speciesName": "Clawitzer", "rating": 428, "matchups": [{"opponent": "darmanitan_standard_shadow", "rating": 831, "opRating": 168}, {"opponent": "charizard_shadow", "rating": 789, "opRating": 210}, {"opponent": "turtonator", "rating": 768, "opRating": 231}, {"opponent": "darmanitan_standard", "rating": 755, "opRating": 244}, {"opponent": "talonflame", "rating": 524, "opRating": 475}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 178}, {"opponent": "cradily", "rating": 191}, {"opponent": "bellibolt", "rating": 205}, {"opponent": "golisopod", "rating": 216}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 32862}, {"moveId": "SMACK_DOWN", "uses": 30138}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 24101}, {"moveId": "DARK_PULSE", "uses": 16995}, {"moveId": "ICE_BEAM", "uses": 15465}, {"moveId": "WATER_PULSE", "uses": 6507}]}, "moveset": ["WATER_GUN", "CRABHAMMER", "DARK_PULSE"], "score": 34.3}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 437, "matchups": [{"opponent": "swampert", "rating": 932, "opRating": 67}, {"opponent": "swampert_shadow", "rating": 923, "opRating": 76}, {"opponent": "gastrodon", "rating": 875, "opRating": 125}, {"opponent": "stunfisk", "rating": 649, "opRating": 350}, {"opponent": "feraligatr", "rating": 603, "opRating": 396}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 137}, {"opponent": "talonflame", "rating": 145}, {"opponent": "skeledirge", "rating": 202}, {"opponent": "virizion", "rating": 215}, {"opponent": "cradily", "rating": 238}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 33954}, {"moveId": "BITE", "uses": 29046}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 32146}, {"moveId": "ENERGY_BALL", "uses": 23774}, {"moveId": "SOLAR_BEAM", "uses": 6940}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 34.3}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 578, "matchups": [{"opponent": "venusaur", "rating": 775, "opRating": 224}, {"opponent": "talonflame", "rating": 701, "opRating": 298}, {"opponent": "drampa", "rating": 627, "opRating": 372}, {"opponent": "zapdos", "rating": 570, "opRating": 429}, {"opponent": "swampert", "rating": 563, "opRating": 436}], "counters": [{"opponent": "virizion", "rating": 60}, {"opponent": "skeledirge", "rating": 75}, {"opponent": "victree<PERSON>_shadow", "rating": 116}, {"opponent": "tentacruel", "rating": 286}, {"opponent": "bellibolt", "rating": 347}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9268}, {"moveId": "CHARGE_BEAM", "uses": 3836}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3810}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3596}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3530}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3251}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3215}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3215}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3189}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3172}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3165}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3043}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2940}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2939}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2915}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2840}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2823}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2206}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18231}, {"moveId": "RETURN", "uses": 11507}, {"moveId": "ZAP_CANNON", "uses": 10430}, {"moveId": "BLIZZARD", "uses": 10341}, {"moveId": "SOLAR_BEAM", "uses": 7999}, {"moveId": "HYPER_BEAM", "uses": 4433}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 34}, {"speciesId": "lumineon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 417, "matchups": [{"opponent": "salazzle", "rating": 625, "opRating": 374}, {"opponent": "talonflame", "rating": 574, "opRating": 425}, {"opponent": "skeledirge", "rating": 548, "opRating": 451}, {"opponent": "crustle", "rating": 541, "opRating": 458}, {"opponent": "typhlosion_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 116}, {"opponent": "cradily", "rating": 147}, {"opponent": "bellibolt", "rating": 237}, {"opponent": "tentacruel", "rating": 239}, {"opponent": "<PERSON>ras", "rating": 251}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 33319}, {"moveId": "WATERFALL", "uses": 29681}], "chargedMoves": [{"moveId": "SILVER_WIND", "uses": 23814}, {"moveId": "WATER_PULSE", "uses": 23537}, {"moveId": "BLIZZARD", "uses": 15652}]}, "moveset": ["WATER_GUN", "WATER_PULSE", "SILVER_WIND"], "score": 33.9}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 567, "matchups": [{"opponent": "venusaur", "rating": 738, "opRating": 261}, {"opponent": "talonflame", "rating": 647, "opRating": 352}, {"opponent": "stunfisk", "rating": 610, "opRating": 389}, {"opponent": "turtonator", "rating": 593, "opRating": 406}, {"opponent": "feraligatr", "rating": 587, "opRating": 412}], "counters": [{"opponent": "virizion", "rating": 60}, {"opponent": "victree<PERSON>_shadow", "rating": 116}, {"opponent": "skeledirge", "rating": 161}, {"opponent": "<PERSON>ras", "rating": 279}, {"opponent": "tentacruel", "rating": 292}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 10133}, {"moveId": "CHARGE_BEAM", "uses": 4073}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3774}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3564}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3495}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3213}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3159}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3158}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3140}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3126}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3091}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2948}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2865}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2853}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2821}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2784}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2727}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2111}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 20605}, {"moveId": "ZAP_CANNON", "uses": 11694}, {"moveId": "BLIZZARD", "uses": 11611}, {"moveId": "HYPER_BEAM", "uses": 10177}, {"moveId": "SOLAR_BEAM", "uses": 8978}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 33.8}, {"speciesId": "swanna_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 489, "matchups": [{"opponent": "virizion", "rating": 740, "opRating": 259}, {"opponent": "swampert", "rating": 704, "opRating": 295}, {"opponent": "gastrodon", "rating": 649, "opRating": 350}, {"opponent": "samu<PERSON>t", "rating": 640, "opRating": 359}, {"opponent": "golisopod", "rating": 585, "opRating": 414}], "counters": [{"opponent": "stunfisk", "rating": 86}, {"opponent": "bellibolt", "rating": 140}, {"opponent": "<PERSON>ras", "rating": 175}, {"opponent": "cradily", "rating": 191}, {"opponent": "skeledirge", "rating": 225}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 33321}, {"moveId": "WATER_GUN", "uses": 29679}], "chargedMoves": [{"moveId": "FLY", "uses": 34430}, {"moveId": "ICE_BEAM", "uses": 12530}, {"moveId": "BUBBLE_BEAM", "uses": 8564}, {"moveId": "HURRICANE", "uses": 7552}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "FLY", "ICE_BEAM"], "score": 33.4}, {"speciesId": "unfezant_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 415, "matchups": [{"opponent": "bewear", "rating": 873, "opRating": 126}, {"opponent": "jellicent", "rating": 639, "opRating": 360}, {"opponent": "gastrodon", "rating": 639, "opRating": 360}, {"opponent": "virizion", "rating": 617, "opRating": 382}, {"opponent": "venusaur", "rating": 531, "opRating": 468}], "counters": [{"opponent": "bellibolt", "rating": 150}, {"opponent": "<PERSON>ras", "rating": 175}, {"opponent": "cradily", "rating": 205}, {"opponent": "tentacruel", "rating": 242}, {"opponent": "skeledirge", "rating": 247}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37494}, {"moveId": "STEEL_WING", "uses": 25506}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 35251}, {"moveId": "HYPER_BEAM", "uses": 19282}, {"moveId": "HEAT_WAVE", "uses": 8359}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 33.4}, {"speciesId": "inteleon", "speciesName": "Inteleon", "rating": 428, "matchups": [{"opponent": "rapidash", "rating": 780, "opRating": 219}, {"opponent": "turtonator", "rating": 762, "opRating": 237}, {"opponent": "typhlosion_shadow", "rating": 737, "opRating": 262}, {"opponent": "salazzle", "rating": 723, "opRating": 276}, {"opponent": "swampert", "rating": 535, "opRating": 464}], "counters": [{"opponent": "<PERSON>ras", "rating": 127}, {"opponent": "golisopod", "rating": 152}, {"opponent": "virizion", "rating": 154}, {"opponent": "cradily", "rating": 227}, {"opponent": "bellibolt", "rating": 240}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 56232}, {"moveId": "POUND", "uses": 6768}], "chargedMoves": [{"moveId": "SURF", "uses": 32530}, {"moveId": "SHADOW_BALL", "uses": 22161}, {"moveId": "WATER_PULSE", "uses": 8249}]}, "moveset": ["WATER_GUN", "SURF", "SHADOW_BALL"], "score": 32.8}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 494, "matchups": [{"opponent": "genesect_shock", "rating": 909, "opRating": 90}, {"opponent": "serperior", "rating": 766, "opRating": 233}, {"opponent": "forretress", "rating": 716, "opRating": 283}, {"opponent": "venusaur", "rating": 666, "opRating": 333}, {"opponent": "victree<PERSON>_shadow", "rating": 646, "opRating": 353}], "counters": [{"opponent": "skeledirge", "rating": 158}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "<PERSON>ras", "rating": 192}, {"opponent": "cradily", "rating": 205}, {"opponent": "bellibolt", "rating": 230}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33753}, {"moveId": "WING_ATTACK", "uses": 29247}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 17534}, {"moveId": "SKY_ATTACK", "uses": 16852}, {"moveId": "OVERHEAT", "uses": 14050}, {"moveId": "RETURN", "uses": 7923}, {"moveId": "FIRE_BLAST", "uses": 4203}, {"moveId": "HEAT_WAVE", "uses": 2469}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 32.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Eldegoss", "rating": 472, "matchups": [{"opponent": "gastrodon", "rating": 781, "opRating": 218}, {"opponent": "stunfisk", "rating": 742, "opRating": 257}, {"opponent": "swampert", "rating": 711, "opRating": 288}, {"opponent": "samu<PERSON>t", "rating": 633, "opRating": 366}, {"opponent": "feraligatr", "rating": 507, "opRating": 492}], "counters": [{"opponent": "golisopod", "rating": 102}, {"opponent": "talonflame", "rating": 125}, {"opponent": "virizion", "rating": 172}, {"opponent": "skeledirge", "rating": 180}, {"opponent": "tentacruel", "rating": 245}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 37951}, {"moveId": "RAZOR_LEAF", "uses": 25049}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 46620}, {"moveId": "ENERGY_BALL", "uses": 16380}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "ENERGY_BALL"], "score": 32.6}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 427, "matchups": [{"opponent": "swampert", "rating": 914, "opRating": 85}, {"opponent": "gastrodon", "rating": 847, "opRating": 152}, {"opponent": "stunfisk", "rating": 716, "opRating": 283}, {"opponent": "jellicent", "rating": 539, "opRating": 460}, {"opponent": "blastoise", "rating": 533, "opRating": 466}], "counters": [{"opponent": "talonflame", "rating": 119}, {"opponent": "victree<PERSON>_shadow", "rating": 134}, {"opponent": "skeledirge", "rating": 169}, {"opponent": "virizion", "rating": 172}, {"opponent": "cradily", "rating": 225}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 32890}, {"moveId": "BITE", "uses": 30110}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27914}, {"moveId": "ENERGY_BALL", "uses": 21614}, {"moveId": "RETURN", "uses": 7015}, {"moveId": "SOLAR_BEAM", "uses": 6356}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 31.9}, {"speciesId": "gorebyss", "speciesName": "<PERSON><PERSON>", "rating": 379, "matchups": [{"opponent": "darmanitan_standard_shadow", "rating": 823, "opRating": 176}, {"opponent": "charizard_shadow", "rating": 765, "opRating": 234}, {"opponent": "darmanitan_standard", "rating": 746, "opRating": 253}, {"opponent": "talonflame", "rating": 519, "opRating": 480}, {"opponent": "turtonator", "rating": 503, "opRating": 496}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 173}, {"opponent": "virizion", "rating": 178}, {"opponent": "tentacruel", "rating": 186}, {"opponent": "cradily", "rating": 194}, {"opponent": "bellibolt", "rating": 205}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 33375}, {"moveId": "WATER_GUN", "uses": 29625}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 27432}, {"moveId": "PSYCHIC", "uses": 22470}, {"moveId": "DRAINING_KISS", "uses": 13079}]}, "moveset": ["WATER_GUN", "WATER_PULSE", "PSYCHIC"], "score": 31.5}, {"speciesId": "fearow", "speciesName": "<PERSON>ow", "rating": 442, "matchups": [{"opponent": "zangoose", "rating": 865, "opRating": 134}, {"opponent": "decid<PERSON><PERSON>", "rating": 845, "opRating": 154}, {"opponent": "serperior", "rating": 654, "opRating": 345}, {"opponent": "venusaur", "rating": 570, "opRating": 429}, {"opponent": "golisopod", "rating": 557, "opRating": 442}], "counters": [{"opponent": "skeledirge", "rating": 147}, {"opponent": "talonflame", "rating": 157}, {"opponent": "<PERSON>ras", "rating": 170}, {"opponent": "virizion", "rating": 184}, {"opponent": "bellibolt", "rating": 310}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 32790}, {"moveId": "PECK", "uses": 30210}], "chargedMoves": [{"moveId": "FLY", "uses": 20646}, {"moveId": "DRILL_RUN", "uses": 17353}, {"moveId": "AERIAL_ACE", "uses": 15700}, {"moveId": "SKY_ATTACK", "uses": 5287}, {"moveId": "TWISTER", "uses": 4196}]}, "moveset": ["STEEL_WING", "FLY", "DRILL_RUN"], "score": 31.4}, {"speciesId": "volcarona", "speciesName": "Volcarona", "rating": 437, "matchups": [{"opponent": "genesect_shock", "rating": 904, "opRating": 95}, {"opponent": "genesect", "rating": 904, "opRating": 95}, {"opponent": "forretress", "rating": 838, "opRating": 161}, {"opponent": "venusaur", "rating": 647, "opRating": 352}, {"opponent": "victree<PERSON>_shadow", "rating": 626, "opRating": 373}], "counters": [{"opponent": "skeledirge", "rating": 158}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "<PERSON>ras", "rating": 192}, {"opponent": "cradily", "rating": 216}, {"opponent": "bellibolt", "rating": 230}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 32941}, {"moveId": "BUG_BITE", "uses": 30059}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 19231}, {"moveId": "BUG_BUZZ", "uses": 18768}, {"moveId": "HURRICANE", "uses": 14302}, {"moveId": "SOLAR_BEAM", "uses": 10793}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "BUG_BUZZ"], "score": 31.1}, {"speciesId": "accelgor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 494, "matchups": [{"opponent": "gastrodon", "rating": 787, "opRating": 212}, {"opponent": "cradily_shadow", "rating": 709, "opRating": 290}, {"opponent": "stunfisk", "rating": 613, "opRating": 386}, {"opponent": "serperior", "rating": 580, "opRating": 419}, {"opponent": "swampert", "rating": 553, "opRating": 446}], "counters": [{"opponent": "feraligatr", "rating": 91}, {"opponent": "golisopod", "rating": 102}, {"opponent": "victree<PERSON>_shadow", "rating": 110}, {"opponent": "talonflame", "rating": 151}, {"opponent": "skeledirge", "rating": 169}], "moves": {"fastMoves": [{"moveId": "WATER_SHURIKEN", "uses": 23144}, {"moveId": "ACID", "uses": 22710}, {"moveId": "INFESTATION", "uses": 17180}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 17228}, {"moveId": "SIGNAL_BEAM", "uses": 14751}, {"moveId": "ENERGY_BALL", "uses": 14626}, {"moveId": "FOCUS_BLAST", "uses": 12242}, {"moveId": "ACID_SPRAY", "uses": 4066}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["WATER_SHURIKEN", "BUG_BUZZ", "ENERGY_BALL"], "score": 30.9}, {"speciesId": "dart<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 523, "matchups": [{"opponent": "gastrodon", "rating": 821, "opRating": 178}, {"opponent": "swampert", "rating": 702, "opRating": 297}, {"opponent": "samu<PERSON>t", "rating": 642, "opRating": 357}, {"opponent": "golisopod", "rating": 577, "opRating": 422}, {"opponent": "virizion", "rating": 529, "opRating": 470}], "counters": [{"opponent": "talonflame", "rating": 74}, {"opponent": "victree<PERSON>_shadow", "rating": 101}, {"opponent": "skeledirge", "rating": 102}, {"opponent": "cradily", "rating": 205}, {"opponent": "bellibolt", "rating": 295}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 21346}, {"moveId": "MAGICAL_LEAF", "uses": 19617}, {"moveId": "PECK", "uses": 13147}, {"moveId": "RAZOR_LEAF", "uses": 8954}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37748}, {"moveId": "SEED_BOMB", "uses": 17455}, {"moveId": "ENERGY_BALL", "uses": 7806}]}, "moveset": ["LEAFAGE", "BRAVE_BIRD", "SEED_BOMB"], "score": 30.9}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 443, "matchups": [{"opponent": "meganium", "rating": 781, "opRating": 218}, {"opponent": "mor<PERSON><PERSON>_full_belly", "rating": 774, "opRating": 225}, {"opponent": "gastrodon", "rating": 735, "opRating": 264}, {"opponent": "stunfisk", "rating": 630, "opRating": 369}, {"opponent": "venusaur", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 98}, {"opponent": "skeledirge", "rating": 113}, {"opponent": "<PERSON>ras", "rating": 170}, {"opponent": "tentacruel", "rating": 186}, {"opponent": "cradily", "rating": 269}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 35829}, {"moveId": "CONFUSION", "uses": 27171}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 25849}, {"moveId": "BUG_BUZZ", "uses": 25819}, {"moveId": "PSYBEAM", "uses": 11299}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 30.6}, {"speciesId": "thwackey", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 385, "matchups": [{"opponent": "swampert_shadow", "rating": 929, "opRating": 70}, {"opponent": "swampert", "rating": 920, "opRating": 79}, {"opponent": "gastrodon", "rating": 818, "opRating": 181}, {"opponent": "barbara<PERSON>", "rating": 767, "opRating": 232}, {"opponent": "stunfisk", "rating": 630, "opRating": 369}], "counters": [{"opponent": "talonflame", "rating": 101}, {"opponent": "victree<PERSON>_shadow", "rating": 149}, {"opponent": "skeledirge", "rating": 155}, {"opponent": "virizion", "rating": 172}, {"opponent": "cradily", "rating": 222}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 36381}, {"moveId": "SCRATCH", "uses": 26619}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 46620}, {"moveId": "ENERGY_BALL", "uses": 16380}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 30.5}, {"speciesId": "sunflora", "speciesName": "Sunflora", "rating": 523, "matchups": [{"opponent": "jellicent", "rating": 826, "opRating": 173}, {"opponent": "stunfisk", "rating": 789, "opRating": 210}, {"opponent": "feraligatr", "rating": 676, "opRating": 323}, {"opponent": "<PERSON>ras", "rating": 649, "opRating": 350}, {"opponent": "swampert", "rating": 606, "opRating": 393}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 56}, {"opponent": "typhlosion_shadow", "rating": 82}, {"opponent": "golisopod", "rating": 102}, {"opponent": "skeledirge", "rating": 169}, {"opponent": "cradily", "rating": 205}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 43121}, {"moveId": "RAZOR_LEAF", "uses": 19879}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25350}, {"moveId": "LEAF_STORM", "uses": 21523}, {"moveId": "PETAL_BLIZZARD", "uses": 9002}, {"moveId": "SOLAR_BEAM", "uses": 7231}]}, "moveset": ["BULLET_SEED", "LEAF_STORM", "SLUDGE_BOMB"], "score": 30.1}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 395, "matchups": [{"opponent": "gourgeist_super", "rating": 772, "opRating": 227}, {"opponent": "gourgeist_large", "rating": 772, "opRating": 227}, {"opponent": "gourgeist_average", "rating": 768, "opRating": 231}, {"opponent": "gourgeist_small", "rating": 765, "opRating": 234}, {"opponent": "gastrodon", "rating": 696, "opRating": 303}], "counters": [{"opponent": "bellibolt", "rating": 127}, {"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "cradily", "rating": 180}, {"opponent": "tentacruel", "rating": 189}, {"opponent": "skeledirge", "rating": 202}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37105}, {"moveId": "STEEL_WING", "uses": 25895}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 29256}, {"moveId": "RETURN", "uses": 19299}, {"moveId": "HYPER_BEAM", "uses": 7562}, {"moveId": "HEAT_WAVE", "uses": 6864}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "RETURN"], "score": 29.8}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 470, "matchups": [{"opponent": "swampert", "rating": 917, "opRating": 82}, {"opponent": "swampert_shadow", "rating": 890, "opRating": 109}, {"opponent": "gastrodon", "rating": 855, "opRating": 144}, {"opponent": "golem_alolan_shadow", "rating": 837, "opRating": 162}, {"opponent": "stunfisk", "rating": 677, "opRating": 322}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 56}, {"opponent": "typhlosion_shadow", "rating": 69}, {"opponent": "talonflame", "rating": 119}, {"opponent": "cradily", "rating": 205}, {"opponent": "bellibolt", "rating": 225}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 21228}, {"moveId": "BULLET_SEED", "uses": 20175}, {"moveId": "EXTRASENSORY", "uses": 16359}, {"moveId": "ZEN_HEADBUTT", "uses": 5265}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 23635}, {"moveId": "PSYCHIC", "uses": 20245}, {"moveId": "RETURN", "uses": 12164}, {"moveId": "SOLAR_BEAM", "uses": 7035}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 29.5}, {"speciesId": "illumise", "speciesName": "Illumise", "rating": 363, "matchups": [{"opponent": "lilligant_hisuian", "rating": 765, "opRating": 234}, {"opponent": "shiftry", "rating": 681, "opRating": 318}, {"opponent": "meowscarada", "rating": 664, "opRating": 335}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 520, "opRating": 479}, {"opponent": "sceptile_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "skeledirge", "rating": 75}, {"opponent": "cradily", "rating": 186}, {"opponent": "bellibolt", "rating": 237}, {"opponent": "tentacruel", "rating": 242}, {"opponent": "<PERSON>ras", "rating": 244}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 34331}, {"moveId": "STRUGGLE_BUG", "uses": 28669}], "chargedMoves": [{"moveId": "SILVER_WIND", "uses": 24038}, {"moveId": "BUG_BUZZ", "uses": 21651}, {"moveId": "DAZZLING_GLEAM", "uses": 17345}]}, "moveset": ["TACKLE", "SILVER_WIND", "DAZZLING_GLEAM"], "score": 29.3}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 436, "matchups": [{"opponent": "swampert", "rating": 965, "opRating": 34}, {"opponent": "swampert_shadow", "rating": 947, "opRating": 52}, {"opponent": "gastrodon", "rating": 931, "opRating": 68}, {"opponent": "stunfisk", "rating": 686, "opRating": 313}, {"opponent": "jellicent", "rating": 537, "opRating": 462}], "counters": [{"opponent": "talonflame", "rating": 86}, {"opponent": "virizion", "rating": 124}, {"opponent": "skeledirge", "rating": 138}, {"opponent": "cradily", "rating": 191}, {"opponent": "tentacruel", "rating": 215}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 9202}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4590}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4143}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3871}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3611}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3361}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3278}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3271}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3193}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3188}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3033}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2977}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2942}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2890}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2887}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2788}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2299}, {"moveId": "ZEN_HEADBUTT", "uses": 1350}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 33905}, {"moveId": "ENERGY_BALL", "uses": 11970}, {"moveId": "SEED_FLARE", "uses": 10069}, {"moveId": "SOLAR_BEAM", "uses": 6933}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 28.9}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 306, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 852, "opRating": 147}, {"opponent": "rotom_heat", "rating": 820, "opRating": 179}, {"opponent": "magnezone", "rating": 785, "opRating": 214}, {"opponent": "toxtricity", "rating": 764, "opRating": 235}, {"opponent": "magnezone_shadow", "rating": 742, "opRating": 257}], "counters": [{"opponent": "virizion", "rating": 109}, {"opponent": "golisopod", "rating": 127}, {"opponent": "talonflame", "rating": 145}, {"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "cradily", "rating": 180}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ROCK", "uses": 4791}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4549}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4380}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4147}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4141}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3920}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3908}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3873}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3837}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3668}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3585}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3569}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3562}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3502}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3373}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2664}, {"moveId": "ZEN_HEADBUTT", "uses": 1101}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 33377}, {"moveId": "THUNDER", "uses": 12942}, {"moveId": "FOCUS_BLAST", "uses": 11779}, {"moveId": "GIGA_IMPACT", "uses": 4677}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HIDDEN_POWER_GROUND", "THUNDER", "FOCUS_BLAST"], "score": 27.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 408, "matchups": [{"opponent": "palkia_shadow", "rating": 743, "opRating": 256}, {"opponent": "charizard_shadow", "rating": 741, "opRating": 258}, {"opponent": "turtonator", "rating": 597, "opRating": 402}, {"opponent": "kleavor", "rating": 589, "opRating": 410}, {"opponent": "crustle", "rating": 504, "opRating": 495}], "counters": [{"opponent": "tentacruel", "rating": 91}, {"opponent": "victree<PERSON>_shadow", "rating": 92}, {"opponent": "cradily", "rating": 102}, {"opponent": "bellibolt", "rating": 242}, {"opponent": "<PERSON>ras", "rating": 244}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 45621}, {"moveId": "ROCK_SMASH", "uses": 17379}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 22274}, {"moveId": "PLAY_ROUGH", "uses": 21939}, {"moveId": "HYDRO_PUMP", "uses": 18760}]}, "moveset": ["BUBBLE", "PLAY_ROUGH", "ICE_BEAM"], "score": 27.7}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 475, "matchups": [{"opponent": "swampert", "rating": 890, "opRating": 109}, {"opponent": "gastrodon", "rating": 837, "opRating": 162}, {"opponent": "feraligatr", "rating": 760, "opRating": 239}, {"opponent": "stunfisk", "rating": 642, "opRating": 357}, {"opponent": "virizion", "rating": 523, "opRating": 476}], "counters": [{"opponent": "victree<PERSON>_shadow", "rating": 74}, {"opponent": "typhlosion_shadow", "rating": 86}, {"opponent": "golisopod", "rating": 92}, {"opponent": "skeledirge", "rating": 211}, {"opponent": "cradily", "rating": 244}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 21024}, {"moveId": "BULLET_SEED", "uses": 20917}, {"moveId": "EXTRASENSORY", "uses": 16069}, {"moveId": "ZEN_HEADBUTT", "uses": 5002}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 28588}, {"moveId": "PSYCHIC", "uses": 25953}, {"moveId": "SOLAR_BEAM", "uses": 8321}, {"moveId": "FRUSTRATION", "uses": 4}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 27.1}, {"speciesId": "audino", "speciesName": "Audino", "rating": 259, "matchups": [{"opponent": "slaking", "rating": 739, "opRating": 260}, {"opponent": "slaking_shadow", "rating": 695, "opRating": 304}, {"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 582, "opRating": 417}, {"opponent": "purugly", "rating": 529, "opRating": 470}, {"opponent": "beedrill_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "skeledirge", "rating": 125}, {"opponent": "cradily", "rating": 155}, {"opponent": "<PERSON>ras", "rating": 177}, {"opponent": "bellibolt", "rating": 182}, {"opponent": "virizion", "rating": 184}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 36770}, {"moveId": "POUND", "uses": 26230}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 32102}, {"moveId": "DISARMING_VOICE", "uses": 17571}, {"moveId": "HYPER_BEAM", "uses": 6868}, {"moveId": "DAZZLING_GLEAM", "uses": 6495}]}, "moveset": ["ZEN_HEADBUTT", "BODY_SLAM", "DISARMING_VOICE"], "score": 26.1}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 281, "matchups": [{"opponent": "rotom_heat", "rating": 842, "opRating": 157}, {"opponent": "golem_alolan_shadow", "rating": 800, "opRating": 199}, {"opponent": "magnezone_shadow", "rating": 786, "opRating": 213}, {"opponent": "magneton_shadow", "rating": 772, "opRating": 227}, {"opponent": "salazzle", "rating": 748, "opRating": 251}], "counters": [{"opponent": "virizion", "rating": 87}, {"opponent": "golisopod", "rating": 131}, {"opponent": "<PERSON>ras", "rating": 183}, {"opponent": "cradily", "rating": 188}, {"opponent": "bellibolt", "rating": 252}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ROCK", "uses": 4754}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4508}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4373}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4168}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4095}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3932}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3922}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3896}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3843}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3719}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3627}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3619}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3573}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3496}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3420}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2712}, {"moveId": "ZEN_HEADBUTT", "uses": 1199}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 33427}, {"moveId": "THUNDER", "uses": 12995}, {"moveId": "FOCUS_BLAST", "uses": 11756}, {"moveId": "GIGA_IMPACT", "uses": 4707}]}, "moveset": ["HIDDEN_POWER_GROUND", "THUNDER", "FOCUS_BLAST"], "score": 25.9}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 258, "matchups": [{"opponent": "whiscash", "rating": 674, "opRating": 325}, {"opponent": "lanturn", "rating": 603, "opRating": 396}, {"opponent": "whiscash_shadow", "rating": 571, "opRating": 428}, {"opponent": "gastrodon", "rating": 565, "opRating": 434}, {"opponent": "stunfisk", "rating": 553, "opRating": 446}], "counters": [{"opponent": "skeledirge", "rating": 136}, {"opponent": "<PERSON>ras", "rating": 158}, {"opponent": "cradily", "rating": 161}, {"opponent": "bellibolt", "rating": 177}, {"opponent": "tentacruel", "rating": 210}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 8390}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4270}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4008}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3707}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3684}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3472}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3357}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3346}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3277}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3258}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3185}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3076}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3050}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3017}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2981}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2910}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2347}, {"moveId": "ZEN_HEADBUTT", "uses": 1426}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 33893}, {"moveId": "ENERGY_BALL", "uses": 11970}, {"moveId": "SEED_FLARE", "uses": 10048}, {"moveId": "SOLAR_BEAM", "uses": 6945}]}, "moveset": ["ZEN_HEADBUTT", "GRASS_KNOT", "SEED_FLARE"], "score": 25.4}, {"speciesId": "volbeat", "speciesName": "Volbeat", "rating": 306, "matchups": [{"opponent": "tangela", "rating": 634, "opRating": 365}, {"opponent": "obstagoon_shadow", "rating": 617, "opRating": 382}, {"opponent": "lilligant_hisuian", "rating": 607, "opRating": 392}, {"opponent": "gogoat", "rating": 590, "opRating": 409}, {"opponent": "meganium", "rating": 506, "opRating": 493}], "counters": [{"opponent": "skeledirge", "rating": 75}, {"opponent": "cradily", "rating": 83}, {"opponent": "talonflame", "rating": 157}, {"opponent": "bellibolt", "rating": 167}, {"opponent": "tentacruel", "rating": 186}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 34922}, {"moveId": "STRUGGLE_BUG", "uses": 28078}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 22214}, {"moveId": "THUNDERBOLT", "uses": 21724}, {"moveId": "SIGNAL_BEAM", "uses": 19113}]}, "moveset": ["TACKLE", "THUNDERBOLT", "BUG_BUZZ"], "score": 24.9}, {"speciesId": "chansey", "speciesName": "<PERSON><PERSON>", "rating": 227, "matchups": [{"opponent": "slaking", "rating": 631, "opRating": 368}, {"opponent": "bewear", "rating": 571, "opRating": 428}, {"opponent": "slaking_shadow", "rating": 561, "opRating": 438}, {"opponent": "audino", "rating": 541, "opRating": 458}], "counters": [{"opponent": "skeledirge", "rating": 127}, {"opponent": "virizion", "rating": 130}, {"opponent": "cradily", "rating": 147}, {"opponent": "bellibolt", "rating": 177}, {"opponent": "<PERSON>ras", "rating": 186}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 34055}, {"moveId": "POUND", "uses": 28945}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 21744}, {"moveId": "PSYCHIC", "uses": 18399}, {"moveId": "HYPER_BEAM", "uses": 17403}, {"moveId": "PSYBEAM", "uses": 5449}]}, "moveset": ["ZEN_HEADBUTT", "DAZZLING_GLEAM", "PSYCHIC"], "score": 23.4}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 419, "matchups": [{"opponent": "stunfisk", "rating": 729, "opRating": 270}, {"opponent": "gastrodon", "rating": 646, "opRating": 353}, {"opponent": "swampert", "rating": 592, "opRating": 407}, {"opponent": "jellicent", "rating": 573, "opRating": 426}, {"opponent": "blastoise", "rating": 547, "opRating": 452}], "counters": [{"opponent": "talonflame", "rating": 50}, {"opponent": "victree<PERSON>_shadow", "rating": 56}, {"opponent": "skeledirge", "rating": 69}, {"opponent": "tentacruel", "rating": 121}, {"opponent": "cradily", "rating": 188}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38390}, {"moveId": "RAZOR_LEAF", "uses": 24610}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 25447}, {"moveId": "SOLAR_BEAM", "uses": 21848}, {"moveId": "HYPER_BEAM", "uses": 15759}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 22.2}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 187, "matchups": [{"opponent": "arcanine", "rating": 613, "opRating": 386}, {"opponent": "omastar_shadow", "rating": 594, "opRating": 405}, {"opponent": "marowak_alolan_shadow", "rating": 591, "opRating": 408}, {"opponent": "rotom", "rating": 527, "opRating": 472}, {"opponent": "infernape_shadow", "rating": 525, "opRating": 475}], "counters": [{"opponent": "virizion", "rating": 15}, {"opponent": "bellibolt", "rating": 17}, {"opponent": "skeledirge", "rating": 19}, {"opponent": "tentacruel", "rating": 23}, {"opponent": "cradily", "rating": 25}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 63000}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 31088}, {"moveId": "EARTHQUAKE", "uses": 13823}, {"moveId": "PLAY_ROUGH", "uses": 9532}, {"moveId": "HYPER_BEAM", "uses": 8452}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 11.6}, {"speciesId": "slaking_shadow", "speciesName": "Slaking (Shadow)", "rating": 188, "matchups": [{"opponent": "omastar", "rating": 594, "opRating": 405}, {"opponent": "marowak_alolan", "rating": 591, "opRating": 408}, {"opponent": "marowak_alolan_shadow", "rating": 530, "opRating": 469}, {"opponent": "infernape", "rating": 525, "opRating": 475}, {"opponent": "ambipom_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "virizion", "rating": 15}, {"opponent": "bellibolt", "rating": 17}, {"opponent": "cradily", "rating": 19}, {"opponent": "tentacruel", "rating": 20}, {"opponent": "skeledirge", "rating": 86}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 63000}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 31034}, {"moveId": "EARTHQUAKE", "uses": 13844}, {"moveId": "PLAY_ROUGH", "uses": 9519}, {"moveId": "HYPER_BEAM", "uses": 8481}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 11.5}]