<!DOCTYPE html>
<html>
<head>
    <title>Matrix State Compression Test</title>
</head>
<body>
    <h1>Matrix State Compression Test</h1>
    <div id="results"></div>

    <script>
        // Mock GameMaster for testing
        var GameMaster = {
            getInstance: function() {
                return {
                    data: {
                        pokemon: [
                            { speciesId: "charizard" },
                            { speciesId: "blastoise" },
                            { speciesId: "venusaur" }
                        ],
                        moves: [
                            { moveId: "FIRE_SPIN" },
                            { moveId: "BLAST_BURN" },
                            { moveId: "DRAGON_CLAW" },
                            { moveId: "WATER_GUN" },
                            { moveId: "HYDRO_CANNON" }
                        ]
                    }
                };
            }
        };

        // Include the MatrixStateManager
    </script>
    <script src="pvpoke-master/src/js/interface/MatrixStateManager.js"></script>
    
    <script>
        function runTests() {
            var results = document.getElementById('results');
            var manager = new MatrixStateManager();
            
            // Test data
            var testTeamA = [
                {
                    speciesId: "charizard",
                    level: 20,
                    ivs: { attack: 0, defense: 15, hp: 14 },
                    moves: {
                        fastMove: "FIRE_SPIN",
                        chargedMoves: ["BLAST_BURN", "DRAGON_CLAW"]
                    },
                    nickname: "Test Char"
                }
            ];
            
            var testTeamB = [
                {
                    speciesId: "blastoise",
                    level: 25,
                    ivs: { attack: 15, defense: 15, hp: 15 },
                    moves: {
                        fastMove: "WATER_GUN",
                        chargedMoves: ["HYDRO_CANNON"]
                    }
                }
            ];
            
            var testSettings = {
                league: 1500,
                matrixMode: 'battle',
                shields: [1, 1]
            };
            
            results.innerHTML += '<h2>Test Results:</h2>';
            
            // Test 1: Pokemon ID Mapping
            try {
                var pokemonMapping = manager.generatePokemonIdMapping();
                results.innerHTML += '<p>✓ Pokemon ID mapping generated: ' + Object.keys(pokemonMapping.pokemonToId).length + ' Pokemon</p>';
            } catch (e) {
                results.innerHTML += '<p>✗ Pokemon ID mapping failed: ' + e.message + '</p>';
            }
            
            // Test 2: Move ID Mapping
            try {
                var moveMapping = manager.generateMoveIdMapping();
                results.innerHTML += '<p>✓ Move ID mapping generated: ' + Object.keys(moveMapping.moveToId).length + ' moves</p>';
            } catch (e) {
                results.innerHTML += '<p>✗ Move ID mapping failed: ' + e.message + '</p>';
            }
            
            // Test 3: State Compression
            try {
                var matrixState = {
                    version: '1.0',
                    timestamp: Date.now(),
                    league: 1500,
                    settings: testSettings,
                    teams: { A: testTeamA, B: testTeamB }
                };
                
                var compressed = manager.compressStateData(matrixState);
                if (compressed) {
                    results.innerHTML += '<p>✓ State compression successful</p>';
                    results.innerHTML += '<p>Compressed data: <code>' + JSON.stringify(compressed).substring(0, 100) + '...</code></p>';
                } else {
                    results.innerHTML += '<p>✗ State compression failed</p>';
                }
            } catch (e) {
                results.innerHTML += '<p>✗ State compression error: ' + e.message + '</p>';
            }
            
            // Test 4: URL Generation
            try {
                var shareableUrl = manager.generateShareableURL(testTeamA, testTeamB, testSettings);
                if (shareableUrl) {
                    results.innerHTML += '<p>✓ Shareable URL generated</p>';
                    results.innerHTML += '<p>URL length: ' + shareableUrl.length + ' characters</p>';
                    results.innerHTML += '<p>URL: <code>' + shareableUrl.substring(0, 100) + '...</code></p>';
                } else {
                    results.innerHTML += '<p>✗ Shareable URL generation failed</p>';
                }
            } catch (e) {
                results.innerHTML += '<p>✗ URL generation error: ' + e.message + '</p>';
            }
            
            // Test 5: Base64 Encoding/Decoding
            try {
                var testString = '{"test": "data", "number": 123}';
                var encoded = manager.encodeBase64(testString);
                var decoded = manager.decodeBase64(encoded);
                
                if (decoded === testString) {
                    results.innerHTML += '<p>✓ Base64 encoding/decoding works correctly</p>';
                } else {
                    results.innerHTML += '<p>✗ Base64 encoding/decoding failed</p>';
                }
            } catch (e) {
                results.innerHTML += '<p>✗ Base64 test error: ' + e.message + '</p>';
            }
        }
        
        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>