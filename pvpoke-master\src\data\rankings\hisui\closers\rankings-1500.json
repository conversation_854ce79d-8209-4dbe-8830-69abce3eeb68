[{"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "gliscor", "rating": 861}, {"opponent": "spiritomb", "rating": 791}, {"opponent": "dusknoir_shadow", "rating": 763}, {"opponent": "bastiodon", "rating": 708}, {"opponent": "drapion_shadow", "rating": 606}], "counters": [{"opponent": "gallade_shadow", "rating": 115}, {"opponent": "gastrodon", "rating": 252}, {"opponent": "abomasnow_shadow", "rating": 321}, {"opponent": "hippo<PERSON><PERSON>", "rating": 366}, {"opponent": "lickilicky", "rating": 443}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34233}, {"moveId": "LOW_KICK", "uses": 3267}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13184}, {"moveId": "ICE_PUNCH", "uses": 9440}, {"moveId": "THUNDER_PUNCH", "uses": 7593}, {"moveId": "FLAMETHROWER", "uses": 5190}, {"moveId": "THUNDER", "uses": 2138}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 100}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 648, "matchups": [{"opponent": "dusknoir_shadow", "rating": 805}, {"opponent": "spiritomb", "rating": 740}, {"opponent": "drapion_shadow", "rating": 671}, {"opponent": "gliscor", "rating": 648}, {"opponent": "gallade_shadow", "rating": 583}], "counters": [{"opponent": "gastrodon", "rating": 264}, {"opponent": "abomasnow_shadow", "rating": 272}, {"opponent": "hippo<PERSON><PERSON>", "rating": 313}, {"opponent": "lickilicky", "rating": 368}, {"opponent": "bastiodon", "rating": 402}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 33664}, {"moveId": "LOW_KICK", "uses": 3836}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13178}, {"moveId": "ICE_PUNCH", "uses": 9451}, {"moveId": "THUNDER_PUNCH", "uses": 7628}, {"moveId": "FLAMETHROWER", "uses": 5134}, {"moveId": "THUNDER", "uses": 2138}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 96.2}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 674, "matchups": [{"opponent": "bibarel", "rating": 942, "opRating": 57}, {"opponent": "bastiodon", "rating": 903}, {"opponent": "abomasnow_shadow", "rating": 855, "opRating": 144}, {"opponent": "gastrodon", "rating": 841}, {"opponent": "drapion_shadow", "rating": 649}], "counters": [{"opponent": "drifb<PERSON>", "rating": 248}, {"opponent": "gliscor", "rating": 288}, {"opponent": "spiritomb", "rating": 312}, {"opponent": "dusknoir_shadow", "rating": 388}, {"opponent": "sneasler", "rating": 419}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 18388}, {"moveId": "CONFUSION", "uses": 11436}, {"moveId": "CHARM", "uses": 5415}, {"moveId": "LOW_KICK", "uses": 2289}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 14527}, {"moveId": "LEAF_BLADE", "uses": 13508}, {"moveId": "SYNCHRONOISE", "uses": 7153}, {"moveId": "PSYCHIC", "uses": 2232}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 92.7}, {"speciesId": "snea<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 663, "matchups": [{"opponent": "bibarel", "rating": 910, "opRating": 89}, {"opponent": "bastiodon", "rating": 892}, {"opponent": "abomasnow_shadow", "rating": 843, "opRating": 156}, {"opponent": "drapion_shadow", "rating": 620}, {"opponent": "gastrodon", "rating": 607}], "counters": [{"opponent": "spiritomb", "rating": 192}, {"opponent": "gallade_shadow", "rating": 230}, {"opponent": "gliscor", "rating": 297}, {"opponent": "drifb<PERSON>", "rating": 449}, {"opponent": "dusknoir_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 19038}, {"moveId": "POISON_JAB", "uses": 14690}, {"moveId": "ROCK_SMASH", "uses": 3803}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18498}, {"moveId": "AERIAL_ACE", "uses": 10912}, {"moveId": "X_SCISSOR", "uses": 8122}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 88.8}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 596, "matchups": [{"opponent": "drifb<PERSON>", "rating": 902}, {"opponent": "gliscor", "rating": 855}, {"opponent": "bastiodon", "rating": 818}, {"opponent": "spiritomb", "rating": 681}, {"opponent": "gastrodon", "rating": 543}], "counters": [{"opponent": "gallade_shadow", "rating": 134}, {"opponent": "qwilfish_his<PERSON>an", "rating": 173}, {"opponent": "abomasnow_shadow", "rating": 458}, {"opponent": "dusknoir_shadow", "rating": 461}, {"opponent": "drapion_shadow", "rating": 487}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 20317}, {"moveId": "MUD_SLAP", "uses": 17183}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 10979}, {"moveId": "ICICLE_SPEAR", "uses": 9205}, {"moveId": "HIGH_HORSEPOWER", "uses": 5464}, {"moveId": "BULLDOZE", "uses": 4461}, {"moveId": "STONE_EDGE", "uses": 3949}, {"moveId": "ANCIENT_POWER", "uses": 3534}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 88.1}, {"speciesId": "wormadam_trash", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Trash)", "rating": 611, "matchups": [{"opponent": "abomasnow_shadow", "rating": 814, "opRating": 185}, {"opponent": "gallade_shadow", "rating": 625}, {"opponent": "drapion_shadow", "rating": 614}, {"opponent": "gastrodon", "rating": 596}, {"opponent": "spiritomb", "rating": 581}], "counters": [{"opponent": "bastiodon", "rating": 205}, {"opponent": "drifb<PERSON>", "rating": 315}, {"opponent": "gliscor", "rating": 465}, {"opponent": "dusknoir_shadow", "rating": 466}, {"opponent": "sneasler", "rating": 477}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 13915}, {"moveId": "BUG_BITE", "uses": 13505}, {"moveId": "CONFUSION", "uses": 9992}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 16198}, {"moveId": "BUG_BUZZ", "uses": 15141}, {"moveId": "PSYBEAM", "uses": 6137}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "IRON_HEAD"], "score": 85.8}, {"speciesId": "toxicroak_shadow", "speciesName": "To<PERSON>croa<PERSON> (Shadow)", "rating": 681, "matchups": [{"opponent": "bibarel", "rating": 941, "opRating": 58}, {"opponent": "abomasnow_shadow", "rating": 844, "opRating": 155}, {"opponent": "bastiodon", "rating": 837}, {"opponent": "drapion_shadow", "rating": 655}, {"opponent": "drifb<PERSON>", "rating": 627, "opRating": 372}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "gliscor", "rating": 306}, {"opponent": "spiritomb", "rating": 336}, {"opponent": "gastrodon", "rating": 389}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 11957}, {"moveId": "MUD_SHOT", "uses": 8683}, {"moveId": "POISON_JAB", "uses": 8658}, {"moveId": "COUNTER", "uses": 8162}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 13976}, {"moveId": "MUD_BOMB", "uses": 9058}, {"moveId": "SHADOW_BALL", "uses": 8247}, {"moveId": "SLUDGE_BOMB", "uses": 6248}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 85.3}, {"speciesId": "empoleon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 619, "matchups": [{"opponent": "abomasnow_shadow", "rating": 836, "opRating": 163}, {"opponent": "gliscor", "rating": 770}, {"opponent": "dusknoir_shadow", "rating": 770}, {"opponent": "bastiodon", "rating": 635}, {"opponent": "drapion_shadow", "rating": 631}], "counters": [{"opponent": "gallade_shadow", "rating": 168}, {"opponent": "sneasler", "rating": 187}, {"opponent": "gastrodon", "rating": 377}, {"opponent": "drifb<PERSON>", "rating": 430}, {"opponent": "spiritomb", "rating": 442}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 13162}, {"moveId": "WATERFALL", "uses": 12280}, {"moveId": "STEEL_WING", "uses": 12059}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 18487}, {"moveId": "DRILL_PECK", "uses": 9486}, {"moveId": "BLIZZARD", "uses": 4221}, {"moveId": "FLASH_CANNON", "uses": 3255}, {"moveId": "HYDRO_PUMP", "uses": 1974}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 85}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 567, "matchups": [{"opponent": "drifb<PERSON>", "rating": 576}, {"opponent": "dusknoir_shadow", "rating": 557}, {"opponent": "bastiodon", "rating": 528}, {"opponent": "gliscor", "rating": 509}, {"opponent": "gastrodon", "rating": 509}], "counters": [{"opponent": "electivire_shadow", "rating": 208}, {"opponent": "magnezone_shadow", "rating": 235}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 405}, {"opponent": "drapion_shadow", "rating": 449}, {"opponent": "qwilfish_his<PERSON>an", "rating": 479}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 24826}, {"moveId": "FEINT_ATTACK", "uses": 12674}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 14838}, {"moveId": "SHADOW_BALL", "uses": 12443}, {"moveId": "SHADOW_SNEAK", "uses": 7048}, {"moveId": "OMINOUS_WIND", "uses": 3175}]}, "moveset": ["SUCKER_PUNCH", "ROCK_TOMB", "SHADOW_BALL"], "score": 84.7}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 641, "matchups": [{"opponent": "bastiodon", "rating": 888}, {"opponent": "qwilfish_his<PERSON>an", "rating": 714, "opRating": 285}, {"opponent": "drapion_shadow", "rating": 691}, {"opponent": "gallade_shadow", "rating": 580, "opRating": 419}, {"opponent": "abomasnow_shadow", "rating": 575, "opRating": 424}], "counters": [{"opponent": "gliscor", "rating": 293}, {"opponent": "spiritomb", "rating": 307}, {"opponent": "drifb<PERSON>", "rating": 366}, {"opponent": "dusknoir_shadow", "rating": 394}, {"opponent": "gastrodon", "rating": 452}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 18725}, {"moveId": "POISON_JAB", "uses": 14548}, {"moveId": "ROCK_SMASH", "uses": 4204}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18517}, {"moveId": "AERIAL_ACE", "uses": 10854}, {"moveId": "X_SCISSOR", "uses": 8124}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 84.5}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 593, "matchups": [{"opponent": "gliscor", "rating": 891}, {"opponent": "drifb<PERSON>", "rating": 884}, {"opponent": "gliscor_shadow", "rating": 855, "opRating": 144}, {"opponent": "spiritomb", "rating": 739}, {"opponent": "drapion_shadow", "rating": 561}], "counters": [{"opponent": "gallade_shadow", "rating": 115}, {"opponent": "gastrodon", "rating": 360}, {"opponent": "abomasnow_shadow", "rating": 384}, {"opponent": "dusknoir_shadow", "rating": 422}, {"opponent": "bastiodon", "rating": 467}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 20177}, {"moveId": "MUD_SLAP", "uses": 17323}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 10972}, {"moveId": "ICICLE_SPEAR", "uses": 9182}, {"moveId": "HIGH_HORSEPOWER", "uses": 5463}, {"moveId": "BULLDOZE", "uses": 4478}, {"moveId": "STONE_EDGE", "uses": 3948}, {"moveId": "ANCIENT_POWER", "uses": 3530}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 84.4}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 605, "matchups": [{"opponent": "dusknoir_shadow", "rating": 898}, {"opponent": "drifb<PERSON>", "rating": 682}, {"opponent": "gliscor", "rating": 669}, {"opponent": "abomasnow_shadow", "rating": 601}, {"opponent": "spiritomb", "rating": 550}], "counters": [{"opponent": "lickilicky", "rating": 281}, {"opponent": "gastrodon", "rating": 303}, {"opponent": "sneasler", "rating": 308}, {"opponent": "gallade_shadow", "rating": 350}, {"opponent": "bastiodon", "rating": 399}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 12401}, {"moveId": "ICE_FANG", "uses": 10994}, {"moveId": "INFESTATION", "uses": 7986}, {"moveId": "BITE", "uses": 6133}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 15279}, {"moveId": "CRUNCH", "uses": 12820}, {"moveId": "SLUDGE_BOMB", "uses": 6911}, {"moveId": "FELL_STINGER", "uses": 2477}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 83.9}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 601, "matchups": [{"opponent": "bastiodon", "rating": 844}, {"opponent": "gastrodon", "rating": 762}, {"opponent": "sneasler", "rating": 702}, {"opponent": "gallade_shadow", "rating": 646}, {"opponent": "dusknoir_shadow", "rating": 517}], "counters": [{"opponent": "abomasnow_shadow", "rating": 174}, {"opponent": "drifb<PERSON>", "rating": 241}, {"opponent": "spiritomb", "rating": 350}, {"opponent": "qwilfish_his<PERSON>an", "rating": 429}, {"opponent": "drapion_shadow", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 13530}, {"moveId": "FURY_CUTTER", "uses": 13439}, {"moveId": "WING_ATTACK", "uses": 10503}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 12814}, {"moveId": "AERIAL_ACE", "uses": 12744}, {"moveId": "EARTHQUAKE", "uses": 7540}, {"moveId": "SAND_TOMB", "uses": 4323}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 82.7}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 658, "matchups": [{"opponent": "bibarel", "rating": 953, "opRating": 46}, {"opponent": "abomasnow_shadow", "rating": 883, "opRating": 116}, {"opponent": "bastiodon", "rating": 864}, {"opponent": "empoleon_shadow", "rating": 864, "opRating": 135}, {"opponent": "dusknoir_shadow", "rating": 511}], "counters": [{"opponent": "gallade_shadow", "rating": 120}, {"opponent": "spiritomb", "rating": 264}, {"opponent": "gliscor", "rating": 336}, {"opponent": "gastrodon", "rating": 339}, {"opponent": "drapion_shadow", "rating": 457}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 11233}, {"moveId": "POISON_JAB", "uses": 9183}, {"moveId": "MUD_SHOT", "uses": 8634}, {"moveId": "COUNTER", "uses": 8463}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 14020}, {"moveId": "MUD_BOMB", "uses": 9050}, {"moveId": "SHADOW_BALL", "uses": 8227}, {"moveId": "SLUDGE_BOMB", "uses": 6221}]}, "moveset": ["POISON_STING", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 82.7}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 571, "matchups": [{"opponent": "sneasler", "rating": 885}, {"opponent": "gallade_shadow", "rating": 775}, {"opponent": "drifb<PERSON>", "rating": 741}, {"opponent": "spiritomb", "rating": 567}, {"opponent": "dusknoir_shadow", "rating": 559}], "counters": [{"opponent": "gastrodon", "rating": 267}, {"opponent": "bastiodon", "rating": 460}, {"opponent": "abomasnow_shadow", "rating": 461}, {"opponent": "qwilfish_his<PERSON>an", "rating": 462}, {"opponent": "gliscor", "rating": 474}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 12380}, {"moveId": "ICE_FANG", "uses": 10856}, {"moveId": "INFESTATION", "uses": 7942}, {"moveId": "BITE", "uses": 6377}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 13995}, {"moveId": "CRUNCH", "uses": 11785}, {"moveId": "SLUDGE_BOMB", "uses": 6230}, {"moveId": "RETURN", "uses": 3213}, {"moveId": "FELL_STINGER", "uses": 2244}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 82}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "gliscor", "rating": 860}, {"opponent": "gliscor_shadow", "rating": 825, "opRating": 174}, {"opponent": "gastrodon", "rating": 748}, {"opponent": "drifb<PERSON>", "rating": 678}, {"opponent": "dusknoir_shadow", "rating": 555}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "bastiodon", "rating": 219}, {"opponent": "drapion_shadow", "rating": 398}, {"opponent": "sneasler", "rating": 424}, {"opponent": "spiritomb", "rating": 461}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 19154}, {"moveId": "LEAFAGE", "uses": 12612}, {"moveId": "RAZOR_LEAF", "uses": 5739}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 17033}, {"moveId": "ENERGY_BALL", "uses": 7434}, {"moveId": "ICY_WIND", "uses": 5772}, {"moveId": "OUTRAGE", "uses": 4521}, {"moveId": "BLIZZARD", "uses": 2619}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 81.9}, {"speciesId": "froslass", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 587, "matchups": [{"opponent": "drifb<PERSON>", "rating": 846}, {"opponent": "gliscor", "rating": 738}, {"opponent": "dusknoir_shadow", "rating": 707}, {"opponent": "gallade_shadow", "rating": 657}, {"opponent": "abomasnow_shadow", "rating": 557}], "counters": [{"opponent": "bastiodon", "rating": 205}, {"opponent": "gastrodon", "rating": 354}, {"opponent": "qwilfish_his<PERSON>an", "rating": 384}, {"opponent": "drapion_shadow", "rating": 406}, {"opponent": "spiritomb", "rating": 432}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18916}, {"moveId": "HEX", "uses": 18584}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 15435}, {"moveId": "SHADOW_BALL", "uses": 7560}, {"moveId": "CRUNCH", "uses": 6549}, {"moveId": "TRIPLE_AXEL", "uses": 5424}, {"moveId": "RETURN", "uses": 2590}]}, "moveset": ["HEX", "AVALANCHE", "SHADOW_BALL"], "score": 81.4}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 610, "matchups": [{"opponent": "empoleon_shadow", "rating": 934, "opRating": 65}, {"opponent": "drifb<PERSON>", "rating": 921, "opRating": 78}, {"opponent": "sneasler", "rating": 891, "opRating": 108}, {"opponent": "vespiquen", "rating": 826, "opRating": 173}, {"opponent": "drapion_shadow", "rating": 530}], "counters": [{"opponent": "spiritomb", "rating": 115}, {"opponent": "gastrodon", "rating": 327}, {"opponent": "gliscor", "rating": 336}, {"opponent": "bastiodon", "rating": 359}, {"opponent": "dusknoir_shadow", "rating": 488}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 26951}, {"moveId": "TACKLE", "uses": 10549}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19359}, {"moveId": "SWIFT", "uses": 9776}, {"moveId": "ENERGY_BALL", "uses": 8346}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 80.8}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 579, "matchups": [{"opponent": "bastiodon", "rating": 742}, {"opponent": "qwilfish_his<PERSON>an", "rating": 714, "opRating": 285}, {"opponent": "drapion_shadow", "rating": 690}, {"opponent": "sneasler", "rating": 549, "opRating": 450}, {"opponent": "gallade_shadow", "rating": 538, "opRating": 461}], "counters": [{"opponent": "dusknoir_shadow", "rating": 361}, {"opponent": "gliscor", "rating": 366}, {"opponent": "spiritomb", "rating": 413}, {"opponent": "gastrodon", "rating": 419}, {"opponent": "drifb<PERSON>", "rating": 428}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 10569}, {"moveId": "ICE_FANG", "uses": 8650}, {"moveId": "FIRE_FANG", "uses": 7757}, {"moveId": "THUNDER_FANG", "uses": 6836}, {"moveId": "BITE", "uses": 3724}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 11555}, {"moveId": "SCORCHING_SANDS", "uses": 9687}, {"moveId": "BODY_SLAM", "uses": 6599}, {"moveId": "EARTH_POWER", "uses": 3798}, {"moveId": "STONE_EDGE", "uses": 3153}, {"moveId": "EARTHQUAKE", "uses": 2803}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 80.8}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 554, "matchups": [{"opponent": "bastiodon", "rating": 910}, {"opponent": "drapion_shadow", "rating": 696}, {"opponent": "qwilfish_his<PERSON>an", "rating": 660, "opRating": 339}, {"opponent": "dusknoir_shadow", "rating": 547}, {"opponent": "sneasler", "rating": 547, "opRating": 452}], "counters": [{"opponent": "gallade_shadow", "rating": 158}, {"opponent": "drifb<PERSON>", "rating": 212}, {"opponent": "abomasnow_shadow", "rating": 251}, {"opponent": "gliscor", "rating": 314}, {"opponent": "spiritomb", "rating": 490}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 6492}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2824}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2665}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2514}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2069}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2016}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2007}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1912}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1875}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1807}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1780}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1772}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1751}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1738}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1712}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1490}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1312}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 11973}, {"moveId": "BODY_SLAM", "uses": 11088}, {"moveId": "WATER_PULSE", "uses": 10027}, {"moveId": "EARTHQUAKE", "uses": 4410}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 80.5}, {"speciesId": "cress<PERSON>a_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 563, "matchups": [{"opponent": "electivire_shadow", "rating": 891, "opRating": 108}, {"opponent": "gastrodon", "rating": 814}, {"opponent": "gallade_shadow", "rating": 790, "opRating": 209}, {"opponent": "sneasler", "rating": 746, "opRating": 253}, {"opponent": "gliscor", "rating": 537}], "counters": [{"opponent": "drifb<PERSON>", "rating": 349}, {"opponent": "dusknoir_shadow", "rating": 355}, {"opponent": "bastiodon", "rating": 363}, {"opponent": "drapion_shadow", "rating": 377}, {"opponent": "spiritomb", "rating": 451}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 19958}, {"moveId": "CONFUSION", "uses": 17542}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 11645}, {"moveId": "MOONBLAST", "uses": 9914}, {"moveId": "FUTURE_SIGHT", "uses": 8714}, {"moveId": "AURORA_BEAM", "uses": 7175}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 80}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 614, "matchups": [{"opponent": "bibarel", "rating": 942, "opRating": 57}, {"opponent": "abomasnow_shadow", "rating": 879, "opRating": 120}, {"opponent": "gastrodon", "rating": 855}, {"opponent": "lickilicky", "rating": 754, "opRating": 245}, {"opponent": "bastiodon", "rating": 668}], "counters": [{"opponent": "drifb<PERSON>", "rating": 208}, {"opponent": "drapion_shadow", "rating": 224}, {"opponent": "spiritomb", "rating": 240}, {"opponent": "dusknoir_shadow", "rating": 338}, {"opponent": "gliscor", "rating": 482}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 17895}, {"moveId": "CONFUSION", "uses": 11499}, {"moveId": "CHARM", "uses": 5735}, {"moveId": "LOW_KICK", "uses": 2370}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 14505}, {"moveId": "LEAF_BLADE", "uses": 13540}, {"moveId": "SYNCHRONOISE", "uses": 7181}, {"moveId": "PSYCHIC", "uses": 2217}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 80}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 541, "matchups": [{"opponent": "gastrodon", "rating": 761}, {"opponent": "qwilfish_his<PERSON>an", "rating": 738, "opRating": 261}, {"opponent": "drapion_shadow", "rating": 718}, {"opponent": "gallade_shadow", "rating": 703}, {"opponent": "spiritomb", "rating": 507}], "counters": [{"opponent": "drifb<PERSON>", "rating": 186}, {"opponent": "gliscor", "rating": 344}, {"opponent": "dusknoir_shadow", "rating": 438}, {"opponent": "abomasnow_shadow", "rating": 447}, {"opponent": "bastiodon", "rating": 474}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 21789}, {"moveId": "CONFUSION", "uses": 15711}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 15516}, {"moveId": "BULLDOZE", "uses": 14970}, {"moveId": "PSYBEAM", "uses": 6992}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BULLDOZE"], "score": 80}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 583, "matchups": [{"opponent": "gallade_shadow", "rating": 711}, {"opponent": "sneasler", "rating": 706}, {"opponent": "gastrodon", "rating": 685}, {"opponent": "bastiodon", "rating": 616}, {"opponent": "dusknoir_shadow", "rating": 512}], "counters": [{"opponent": "abomasnow_shadow", "rating": 139}, {"opponent": "drifb<PERSON>", "rating": 234}, {"opponent": "drapion_shadow", "rating": 330}, {"opponent": "qwilfish_his<PERSON>an", "rating": 396}, {"opponent": "spiritomb", "rating": 490}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 13618}, {"moveId": "FURY_CUTTER", "uses": 12890}, {"moveId": "WING_ATTACK", "uses": 10975}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 12861}, {"moveId": "AERIAL_ACE", "uses": 12753}, {"moveId": "EARTHQUAKE", "uses": 7535}, {"moveId": "SAND_TOMB", "uses": 4373}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 79.9}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 554, "matchups": [{"opponent": "dusknoir_shadow", "rating": 756}, {"opponent": "drifb<PERSON>", "rating": 677}, {"opponent": "gastrodon", "rating": 591}, {"opponent": "spiritomb", "rating": 585}, {"opponent": "drapion_shadow", "rating": 544}], "counters": [{"opponent": "bastiodon", "rating": 230}, {"opponent": "lickilicky", "rating": 296}, {"opponent": "gliscor", "rating": 306}, {"opponent": "qwilfish_his<PERSON>an", "rating": 367}, {"opponent": "sneasler", "rating": 397}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 23356}, {"moveId": "BITE", "uses": 14144}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 11980}, {"moveId": "TRAILBLAZE", "uses": 9505}, {"moveId": "FLAMETHROWER", "uses": 6720}, {"moveId": "SLUDGE_BOMB", "uses": 5931}, {"moveId": "RETURN", "uses": 3358}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 79.6}, {"speciesId": "hippopotas", "speciesName": "Hippopotas", "rating": 561, "matchups": [{"opponent": "bastiodon", "rating": 753}, {"opponent": "qwilfish_his<PERSON>an", "rating": 723, "opRating": 276}, {"opponent": "drapion_shadow", "rating": 700}, {"opponent": "sneasler", "rating": 546, "opRating": 453}, {"opponent": "lickilicky", "rating": 546, "opRating": 453}], "counters": [{"opponent": "drifb<PERSON>", "rating": 296}, {"opponent": "gastrodon", "rating": 324}, {"opponent": "spiritomb", "rating": 399}, {"opponent": "dusknoir_shadow", "rating": 444}, {"opponent": "gliscor", "rating": 478}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 19060}, {"moveId": "TACKLE", "uses": 9713}, {"moveId": "BITE", "uses": 8701}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 13889}, {"moveId": "DIG", "uses": 11315}, {"moveId": "BODY_SLAM", "uses": 9549}, {"moveId": "RETURN", "uses": 2707}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 79.3}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 595, "matchups": [{"opponent": "electivire_shadow", "rating": 947, "opRating": 52}, {"opponent": "bastiodon", "rating": 894}, {"opponent": "qwilfish_his<PERSON>an", "rating": 669, "opRating": 330}, {"opponent": "drapion_shadow", "rating": 640}, {"opponent": "drifb<PERSON>", "rating": 514, "opRating": 485}], "counters": [{"opponent": "gliscor", "rating": 224}, {"opponent": "gallade_shadow", "rating": 288}, {"opponent": "spiritomb", "rating": 317}, {"opponent": "gastrodon", "rating": 413}, {"opponent": "dusknoir_shadow", "rating": 488}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 11195}, {"moveId": "ICE_FANG", "uses": 8637}, {"moveId": "FIRE_FANG", "uses": 7685}, {"moveId": "THUNDER_FANG", "uses": 6759}, {"moveId": "BITE", "uses": 3230}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 11552}, {"moveId": "SCORCHING_SANDS", "uses": 9718}, {"moveId": "BODY_SLAM", "uses": 6551}, {"moveId": "EARTH_POWER", "uses": 3795}, {"moveId": "STONE_EDGE", "uses": 3133}, {"moveId": "EARTHQUAKE", "uses": 2832}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 79.3}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 562, "matchups": [{"opponent": "drifb<PERSON>", "rating": 683}, {"opponent": "gallade_shadow", "rating": 661}, {"opponent": "bastiodon", "rating": 594}, {"opponent": "gliscor", "rating": 561}, {"opponent": "abomasnow_shadow", "rating": 527}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 235}, {"opponent": "spiritomb", "rating": 360}, {"opponent": "gastrodon", "rating": 377}, {"opponent": "drapion_shadow", "rating": 440}, {"opponent": "gliscor_shadow", "rating": 487}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 20049}, {"moveId": "ASTONISH", "uses": 17451}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 11586}, {"moveId": "DYNAMIC_PUNCH", "uses": 9646}, {"moveId": "DARK_PULSE", "uses": 4265}, {"moveId": "SHADOW_BALL", "uses": 3138}, {"moveId": "PSYCHIC", "uses": 2725}, {"moveId": "RETURN", "uses": 2465}, {"moveId": "POLTERGEIST", "uses": 2085}, {"moveId": "OMINOUS_WIND", "uses": 1599}]}, "moveset": ["ASTONISH", "DYNAMIC_PUNCH", "SHADOW_PUNCH"], "score": 79.1}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 560, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 796, "opRating": 203}, {"opponent": "drapion_shadow", "rating": 718}, {"opponent": "bastiodon", "rating": 653}, {"opponent": "abomasnow_shadow", "rating": 553, "opRating": 446}, {"opponent": "sneasler", "rating": 503, "opRating": 496}], "counters": [{"opponent": "gallade_shadow", "rating": 264}, {"opponent": "gastrodon", "rating": 309}, {"opponent": "gliscor", "rating": 405}, {"opponent": "spiritomb", "rating": 466}, {"opponent": "dusknoir_shadow", "rating": 472}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 21975}, {"moveId": "LICK", "uses": 12959}, {"moveId": "ZEN_HEADBUTT", "uses": 2602}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 13461}, {"moveId": "SHADOW_BALL", "uses": 9624}, {"moveId": "EARTHQUAKE", "uses": 7016}, {"moveId": "SOLAR_BEAM", "uses": 4344}, {"moveId": "HYPER_BEAM", "uses": 3050}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "EARTHQUAKE"], "score": 79}, {"speciesId": "staravia", "speciesName": "Staravia", "rating": 570, "matchups": [{"opponent": "dusknoir_shadow", "rating": 811}, {"opponent": "spiritomb", "rating": 766}, {"opponent": "gastrodon", "rating": 755}, {"opponent": "gliscor", "rating": 692}, {"opponent": "drapion_shadow", "rating": 681}], "counters": [{"opponent": "bastiodon", "rating": 82}, {"opponent": "abomasnow_shadow", "rating": 104}, {"opponent": "gallade_shadow", "rating": 144}, {"opponent": "sneasler", "rating": 160}, {"opponent": "drifb<PERSON>", "rating": 287}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 13227}, {"moveId": "QUICK_ATTACK", "uses": 13092}, {"moveId": "WING_ATTACK", "uses": 11174}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 12750}, {"moveId": "FLY", "uses": 10832}, {"moveId": "AERIAL_ACE", "uses": 8286}, {"moveId": "RETURN", "uses": 3683}, {"moveId": "HEAT_WAVE", "uses": 2012}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 78.7}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 591, "matchups": [{"opponent": "gastrodon", "rating": 848}, {"opponent": "gallade_shadow", "rating": 829, "opRating": 170}, {"opponent": "qwilfish_his<PERSON>an", "rating": 643, "opRating": 356}, {"opponent": "abomasnow_shadow", "rating": 609, "opRating": 390}, {"opponent": "dusknoir_shadow", "rating": 571}], "counters": [{"opponent": "bastiodon", "rating": 172}, {"opponent": "spiritomb", "rating": 336}, {"opponent": "drifb<PERSON>", "rating": 349}, {"opponent": "gliscor", "rating": 362}, {"opponent": "drapion_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 12503}, {"moveId": "BUG_BITE", "uses": 9039}, {"moveId": "POISON_STING", "uses": 8774}, {"moveId": "AIR_SLASH", "uses": 7199}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 14399}, {"moveId": "POWER_GEM", "uses": 11958}, {"moveId": "FELL_STINGER", "uses": 3955}, {"moveId": "BUG_BUZZ", "uses": 3927}, {"moveId": "SIGNAL_BEAM", "uses": 3342}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 78.5}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 580, "matchups": [{"opponent": "abomasnow_shadow", "rating": 872, "opRating": 127}, {"opponent": "gliscor_shadow", "rating": 770, "opRating": 229}, {"opponent": "gliscor", "rating": 766}, {"opponent": "bastiodon", "rating": 635}, {"opponent": "drapion_shadow", "rating": 540}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "gastrodon", "rating": 306}, {"opponent": "spiritomb", "rating": 389}, {"opponent": "drifb<PERSON>", "rating": 416}, {"opponent": "dusknoir_shadow", "rating": 455}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 12903}, {"moveId": "WATERFALL", "uses": 12542}, {"moveId": "STEEL_WING", "uses": 12094}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 18471}, {"moveId": "DRILL_PECK", "uses": 9532}, {"moveId": "BLIZZARD", "uses": 4223}, {"moveId": "FLASH_CANNON", "uses": 3252}, {"moveId": "HYDRO_PUMP", "uses": 1967}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 78.3}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 576, "matchups": [{"opponent": "gallade_shadow", "rating": 787, "opRating": 212}, {"opponent": "gliscor_shadow", "rating": 715, "opRating": 284}, {"opponent": "sneasler", "rating": 660, "opRating": 339}, {"opponent": "drifb<PERSON>", "rating": 633, "opRating": 366}, {"opponent": "gliscor", "rating": 519}], "counters": [{"opponent": "dusknoir_shadow", "rating": 233}, {"opponent": "drapion_shadow", "rating": 368}, {"opponent": "spiritomb", "rating": 437}, {"opponent": "gastrodon", "rating": 446}, {"opponent": "bastiodon", "rating": 489}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 20530}, {"moveId": "DRAGON_TAIL", "uses": 16970}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16578}, {"moveId": "DRAGON_PULSE", "uses": 10753}, {"moveId": "OMINOUS_WIND", "uses": 8475}, {"moveId": "SHADOW_FORCE", "uses": 1703}]}, "moveset": ["SHADOW_CLAW", "OMINOUS_WIND", "SHADOW_BALL"], "score": 78.3}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 558, "matchups": [{"opponent": "gallade_shadow", "rating": 817, "opRating": 182}, {"opponent": "sneasler", "rating": 787, "opRating": 212}, {"opponent": "gastrodon", "rating": 694}, {"opponent": "abomasnow_shadow", "rating": 648, "opRating": 351}, {"opponent": "lickilicky", "rating": 577, "opRating": 422}], "counters": [{"opponent": "dusknoir_shadow", "rating": 333}, {"opponent": "drapion_shadow", "rating": 334}, {"opponent": "spiritomb", "rating": 389}, {"opponent": "bastiodon", "rating": 402}, {"opponent": "gliscor", "rating": 474}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 20219}, {"moveId": "CONFUSION", "uses": 17281}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 11689}, {"moveId": "MOONBLAST", "uses": 9910}, {"moveId": "FUTURE_SIGHT", "uses": 8734}, {"moveId": "AURORA_BEAM", "uses": 7177}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 78.2}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 549, "matchups": [{"opponent": "dusknoir_shadow", "rating": 820}, {"opponent": "spiritomb", "rating": 806}, {"opponent": "gastrodon", "rating": 717}, {"opponent": "drapion_shadow", "rating": 575}, {"opponent": "gliscor", "rating": 544}], "counters": [{"opponent": "abomasnow_shadow", "rating": 73}, {"opponent": "gallade_shadow", "rating": 100}, {"opponent": "gliscor_shadow", "rating": 107}, {"opponent": "bastiodon", "rating": 201}, {"opponent": "sneasler", "rating": 348}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27999}, {"moveId": "PECK", "uses": 9501}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18253}, {"moveId": "DARK_PULSE", "uses": 11328}, {"moveId": "PSYCHIC", "uses": 3962}, {"moveId": "SKY_ATTACK", "uses": 3945}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 78}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 583, "matchups": [{"opponent": "empoleon_shadow", "rating": 920, "opRating": 79}, {"opponent": "abomasnow_shadow", "rating": 823, "opRating": 176}, {"opponent": "dusknoir_shadow", "rating": 752}, {"opponent": "spiritomb", "rating": 716}, {"opponent": "drapion_shadow", "rating": 570}], "counters": [{"opponent": "gastrodon", "rating": 107}, {"opponent": "gallade_shadow", "rating": 201}, {"opponent": "bastiodon", "rating": 248}, {"opponent": "gliscor", "rating": 456}, {"opponent": "drifb<PERSON>", "rating": 466}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 23948}, {"moveId": "BUG_BITE", "uses": 13552}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 13140}, {"moveId": "EARTH_POWER", "uses": 7066}, {"moveId": "STONE_EDGE", "uses": 6389}, {"moveId": "IRON_HEAD", "uses": 5495}, {"moveId": "FLAMETHROWER", "uses": 3570}, {"moveId": "FIRE_BLAST", "uses": 1891}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 77.9}, {"speciesId": "mantyke", "speciesName": "Mantyke", "rating": 509, "matchups": [{"opponent": "gliscor", "rating": 787}, {"opponent": "gallade_shadow", "rating": 720, "opRating": 279}, {"opponent": "gliscor_shadow", "rating": 720, "opRating": 279}, {"opponent": "sneasler", "rating": 716, "opRating": 283}, {"opponent": "gastrodon", "rating": 695}], "counters": [{"opponent": "bastiodon", "rating": 309}, {"opponent": "dusknoir_shadow", "rating": 361}, {"opponent": "spiritomb", "rating": 389}, {"opponent": "drifb<PERSON>", "rating": 394}, {"opponent": "drapion_shadow", "rating": 415}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 23701}, {"moveId": "TACKLE", "uses": 13799}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 16483}, {"moveId": "WATER_PULSE", "uses": 10737}, {"moveId": "ICE_BEAM", "uses": 10325}]}, "moveset": ["BUBBLE", "AERIAL_ACE", "WATER_PULSE"], "score": 77.6}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 505, "matchups": [{"opponent": "spiritomb", "rating": 831}, {"opponent": "bastiodon", "rating": 823}, {"opponent": "gliscor", "rating": 600}, {"opponent": "drapion_shadow", "rating": 512}, {"opponent": "dusknoir_shadow", "rating": 504}], "counters": [{"opponent": "gliscor_shadow", "rating": 172}, {"opponent": "gallade_shadow", "rating": 187}, {"opponent": "empoleon_shadow", "rating": 213}, {"opponent": "sneasler", "rating": 232}, {"opponent": "gastrodon", "rating": 428}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31187}, {"moveId": "ROCK_SMASH", "uses": 6313}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15837}, {"moveId": "CLOSE_COMBAT", "uses": 14850}, {"moveId": "FLAMETHROWER", "uses": 3638}, {"moveId": "SOLAR_BEAM", "uses": 3159}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 77.4}, {"speciesId": "froslass_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "drifb<PERSON>", "rating": 830}, {"opponent": "gliscor", "rating": 707}, {"opponent": "dusknoir_shadow", "rating": 646}, {"opponent": "spiritomb", "rating": 600}, {"opponent": "gallade_shadow", "rating": 584}], "counters": [{"opponent": "drapion_shadow", "rating": 63}, {"opponent": "sneasler", "rating": 160}, {"opponent": "bastiodon", "rating": 255}, {"opponent": "gastrodon", "rating": 416}, {"opponent": "qwilfish_his<PERSON>an", "rating": 462}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18779}, {"moveId": "HEX", "uses": 18721}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 16583}, {"moveId": "SHADOW_BALL", "uses": 8027}, {"moveId": "CRUNCH", "uses": 7034}, {"moveId": "TRIPLE_AXEL", "uses": 5822}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "AVALANCHE", "SHADOW_BALL"], "score": 77.1}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 578, "matchups": [{"opponent": "drifb<PERSON>", "rating": 882}, {"opponent": "dusknoir_shadow", "rating": 764}, {"opponent": "spiritomb", "rating": 725}, {"opponent": "drapion_shadow", "rating": 637}, {"opponent": "abomasnow_shadow", "rating": 598, "opRating": 401}], "counters": [{"opponent": "gallade_shadow", "rating": 134}, {"opponent": "sneasler", "rating": 187}, {"opponent": "gliscor", "rating": 215}, {"opponent": "gastrodon", "rating": 261}, {"opponent": "bastiodon", "rating": 305}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12580}, {"moveId": "METAL_SOUND", "uses": 11724}, {"moveId": "SPARK", "uses": 7587}, {"moveId": "CHARGE_BEAM", "uses": 5571}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20369}, {"moveId": "MIRROR_SHOT", "uses": 9487}, {"moveId": "FLASH_CANNON", "uses": 4985}, {"moveId": "ZAP_CANNON", "uses": 2785}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 77.1}, {"speciesId": "bastiodon", "speciesName": "Bastiodon", "rating": 612, "matchups": [{"opponent": "vespiquen", "rating": 827, "opRating": 172}, {"opponent": "drifb<PERSON>", "rating": 798, "opRating": 201}, {"opponent": "abomasnow_shadow", "rating": 780, "opRating": 219}, {"opponent": "qwilfish_his<PERSON>an", "rating": 629, "opRating": 370}, {"opponent": "drapion_shadow", "rating": 600}], "counters": [{"opponent": "gastrodon", "rating": 89}, {"opponent": "gallade_shadow", "rating": 96}, {"opponent": "gliscor", "rating": 383}, {"opponent": "dusknoir_shadow", "rating": 466}, {"opponent": "spiritomb", "rating": 471}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 23138}, {"moveId": "IRON_TAIL", "uses": 14362}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15504}, {"moveId": "FLAMETHROWER", "uses": 9972}, {"moveId": "FLASH_CANNON", "uses": 6640}, {"moveId": "RETURN", "uses": 5407}]}, "moveset": ["SMACK_DOWN", "STONE_EDGE", "FLAMETHROWER"], "score": 76.6}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 579, "matchups": [{"opponent": "empoleon_shadow", "rating": 911, "opRating": 88}, {"opponent": "drifb<PERSON>", "rating": 862}, {"opponent": "spiritomb", "rating": 764}, {"opponent": "dusknoir_shadow", "rating": 725}, {"opponent": "drapion_shadow", "rating": 573}], "counters": [{"opponent": "gastrodon", "rating": 89}, {"opponent": "gallade_shadow", "rating": 153}, {"opponent": "gliscor", "rating": 254}, {"opponent": "bastiodon", "rating": 374}, {"opponent": "abomasnow_shadow", "rating": 419}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 13069}, {"moveId": "METAL_SOUND", "uses": 11859}, {"moveId": "SPARK", "uses": 7294}, {"moveId": "CHARGE_BEAM", "uses": 5287}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20403}, {"moveId": "MIRROR_SHOT", "uses": 9441}, {"moveId": "FLASH_CANNON", "uses": 5017}, {"moveId": "ZAP_CANNON", "uses": 2791}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 76.6}, {"speciesId": "staravia_shadow", "speciesName": "Staravia (Shadow)", "rating": 566, "matchups": [{"opponent": "dusknoir_shadow", "rating": 777}, {"opponent": "spiritomb", "rating": 740}, {"opponent": "gastrodon", "rating": 729}, {"opponent": "gliscor", "rating": 651}, {"opponent": "drapion_shadow", "rating": 511}], "counters": [{"opponent": "bastiodon", "rating": 93}, {"opponent": "empoleon_shadow", "rating": 98}, {"opponent": "abomasnow_shadow", "rating": 122}, {"opponent": "gallade_shadow", "rating": 168}, {"opponent": "sneasler", "rating": 187}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13244}, {"moveId": "SAND_ATTACK", "uses": 12942}, {"moveId": "WING_ATTACK", "uses": 11271}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 14093}, {"moveId": "FLY", "uses": 12019}, {"moveId": "AERIAL_ACE", "uses": 9137}, {"moveId": "HEAT_WAVE", "uses": 2228}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 76.6}, {"speciesId": "qwilfish_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 542, "matchups": [{"opponent": "dusknoir_shadow", "rating": 723}, {"opponent": "drifb<PERSON>", "rating": 702}, {"opponent": "abomasnow_shadow", "rating": 611}, {"opponent": "gliscor", "rating": 603}, {"opponent": "spiritomb", "rating": 520}], "counters": [{"opponent": "lickilicky", "rating": 203}, {"opponent": "sneasler", "rating": 285}, {"opponent": "gallade_shadow", "rating": 326}, {"opponent": "gastrodon", "rating": 339}, {"opponent": "bastiodon", "rating": 370}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 20968}, {"moveId": "POISON_JAB", "uses": 16532}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 11952}, {"moveId": "DARK_PULSE", "uses": 7829}, {"moveId": "ICE_BEAM", "uses": 6414}, {"moveId": "SHADOW_BALL", "uses": 5682}, {"moveId": "SLUDGE_BOMB", "uses": 5649}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 76.3}, {"speciesId": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 558, "matchups": [{"opponent": "abomasnow_shadow", "rating": 757, "opRating": 242}, {"opponent": "dusknoir_shadow", "rating": 712}, {"opponent": "drifb<PERSON>", "rating": 674}, {"opponent": "gliscor", "rating": 621}, {"opponent": "spiritomb", "rating": 594}], "counters": [{"opponent": "gallade_shadow", "rating": 129}, {"opponent": "bastiodon", "rating": 334}, {"opponent": "gastrodon", "rating": 339}, {"opponent": "sneasler", "rating": 361}, {"opponent": "drapion_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 14303}, {"moveId": "SNARL", "uses": 12416}, {"moveId": "WATERFALL", "uses": 10833}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 10538}, {"moveId": "RAZOR_SHELL", "uses": 9881}, {"moveId": "DARK_PULSE", "uses": 8930}, {"moveId": "X_SCISSOR", "uses": 8134}]}, "moveset": ["FURY_CUTTER", "DARK_PULSE", "ICY_WIND"], "score": 76.2}, {"speciesId": "kricketune", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 572, "matchups": [{"opponent": "abomasnow_shadow", "rating": 885, "opRating": 114}, {"opponent": "gallade_shadow", "rating": 745, "opRating": 254}, {"opponent": "gastrodon", "rating": 633}, {"opponent": "lickilicky", "rating": 516, "opRating": 483}, {"opponent": "sneasler", "rating": 513, "opRating": 486}], "counters": [{"opponent": "bastiodon", "rating": 194}, {"opponent": "dusknoir_shadow", "rating": 377}, {"opponent": "spiritomb", "rating": 442}, {"opponent": "gliscor", "rating": 448}, {"opponent": "drapion_shadow", "rating": 457}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28194}, {"moveId": "STRUGGLE_BUG", "uses": 9306}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18107}, {"moveId": "AERIAL_ACE", "uses": 14539}, {"moveId": "BUG_BUZZ", "uses": 4839}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "AERIAL_ACE"], "score": 75.9}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 587, "matchups": [{"opponent": "drifb<PERSON>", "rating": 779}, {"opponent": "spiritomb", "rating": 774}, {"opponent": "abomasnow_shadow", "rating": 758, "opRating": 241}, {"opponent": "drapion_shadow", "rating": 569}, {"opponent": "dusknoir_shadow", "rating": 553}], "counters": [{"opponent": "gastrodon", "rating": 53}, {"opponent": "gallade_shadow", "rating": 120}, {"opponent": "sneasler", "rating": 133}, {"opponent": "bastiodon", "rating": 205}, {"opponent": "gliscor", "rating": 461}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 21545}, {"moveId": "ASTONISH", "uses": 15955}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17130}, {"moveId": "THUNDERBOLT", "uses": 14189}, {"moveId": "THUNDER", "uses": 6138}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 75.9}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 616, "matchups": [{"opponent": "empoleon_shadow", "rating": 808, "opRating": 192}, {"opponent": "bibarel", "rating": 808, "opRating": 192}, {"opponent": "gastrodon", "rating": 660}, {"opponent": "qwilfish_his<PERSON>an", "rating": 636, "opRating": 364}, {"opponent": "gliscor", "rating": 592}], "counters": [{"opponent": "drapion_shadow", "rating": 127}, {"opponent": "gallade_shadow", "rating": 168}, {"opponent": "spiritomb", "rating": 413}, {"opponent": "bastiodon", "rating": 482}, {"opponent": "dusknoir_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 10340}, {"moveId": "SAND_ATTACK", "uses": 10321}, {"moveId": "GUST", "uses": 8686}, {"moveId": "WING_ATTACK", "uses": 8141}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 13161}, {"moveId": "CLOSE_COMBAT", "uses": 11598}, {"moveId": "FLY", "uses": 11199}, {"moveId": "HEAT_WAVE", "uses": 1567}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 75.7}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 595, "matchups": [{"opponent": "drifb<PERSON>", "rating": 814, "opRating": 185}, {"opponent": "abomasnow_shadow", "rating": 740, "opRating": 259}, {"opponent": "gallade_shadow", "rating": 685, "opRating": 314}, {"opponent": "dusknoir_shadow", "rating": 611}, {"opponent": "sneasler", "rating": 564, "opRating": 435}], "counters": [{"opponent": "drapion_shadow", "rating": 63}, {"opponent": "bastiodon", "rating": 183}, {"opponent": "gastrodon", "rating": 238}, {"opponent": "gliscor", "rating": 327}, {"opponent": "spiritomb", "rating": 442}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19428}, {"moveId": "EMBER", "uses": 18072}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10398}, {"moveId": "FIRE_PUNCH", "uses": 9999}, {"moveId": "NIGHT_SHADE", "uses": 7783}, {"moveId": "OVERHEAT", "uses": 5782}, {"moveId": "SHADOW_BALL", "uses": 3613}]}, "moveset": ["HEX", "FIRE_PUNCH", "WILD_CHARGE"], "score": 75.4}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "drifb<PERSON>", "rating": 822}, {"opponent": "gallade_shadow", "rating": 611}, {"opponent": "sneasler", "rating": 605, "opRating": 394}, {"opponent": "bastiodon", "rating": 533}, {"opponent": "lickilicky", "rating": 527, "opRating": 472}], "counters": [{"opponent": "drapion_shadow", "rating": 101}, {"opponent": "spiritomb", "rating": 442}, {"opponent": "abomasnow_shadow", "rating": 444}, {"opponent": "gastrodon", "rating": 452}, {"opponent": "gliscor", "rating": 487}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 20376}, {"moveId": "ASTONISH", "uses": 17124}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 12341}, {"moveId": "DYNAMIC_PUNCH", "uses": 10398}, {"moveId": "DARK_PULSE", "uses": 4507}, {"moveId": "SHADOW_BALL", "uses": 3336}, {"moveId": "PSYCHIC", "uses": 3013}, {"moveId": "POLTERGEIST", "uses": 2171}, {"moveId": "OMINOUS_WIND", "uses": 1761}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "DYNAMIC_PUNCH", "SHADOW_PUNCH"], "score": 75.3}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 584, "matchups": [{"opponent": "gallade_shadow", "rating": 966, "opRating": 33}, {"opponent": "sneasler", "rating": 875, "opRating": 125}, {"opponent": "abomasnow_shadow", "rating": 741, "opRating": 258}, {"opponent": "bibarel", "rating": 712, "opRating": 287}, {"opponent": "toxicroak", "rating": 695, "opRating": 304}], "counters": [{"opponent": "gliscor", "rating": 284}, {"opponent": "gastrodon", "rating": 300}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "drapion_shadow", "rating": 338}, {"opponent": "bastiodon", "rating": 438}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 15760}, {"moveId": "CONFUSION", "uses": 13004}, {"moveId": "FEINT_ATTACK", "uses": 8749}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 14316}, {"moveId": "HEAVY_SLAM", "uses": 6741}, {"moveId": "PAYBACK", "uses": 6271}, {"moveId": "BULLDOZE", "uses": 5670}, {"moveId": "PSYCHIC", "uses": 2392}, {"moveId": "FLASH_CANNON", "uses": 2159}]}, "moveset": ["METAL_SOUND", "PSYSHOCK", "PAYBACK"], "score": 75}, {"speciesId": "rhyperior_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 544, "matchups": [{"opponent": "lickilicky", "rating": 828, "opRating": 171}, {"opponent": "empoleon_shadow", "rating": 753, "opRating": 246}, {"opponent": "spiritomb", "rating": 731, "opRating": 268}, {"opponent": "bastiodon", "rating": 679}, {"opponent": "drifb<PERSON>", "rating": 559, "opRating": 440}], "counters": [{"opponent": "gallade_shadow", "rating": 216}, {"opponent": "drapion_shadow", "rating": 254}, {"opponent": "dusknoir_shadow", "rating": 416}, {"opponent": "gastrodon", "rating": 443}, {"opponent": "gliscor", "rating": 478}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 24032}, {"moveId": "SMACK_DOWN", "uses": 13468}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 9910}, {"moveId": "BREAKING_SWIPE", "uses": 6787}, {"moveId": "SUPER_POWER", "uses": 6299}, {"moveId": "SURF", "uses": 5293}, {"moveId": "EARTHQUAKE", "uses": 4314}, {"moveId": "STONE_EDGE", "uses": 2762}, {"moveId": "SKULL_BASH", "uses": 2084}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 75}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 607, "matchups": [{"opponent": "bibarel", "rating": 840, "opRating": 160}, {"opponent": "empoleon_shadow", "rating": 832, "opRating": 168}, {"opponent": "gastrodon", "rating": 675}, {"opponent": "qwilfish_his<PERSON>an", "rating": 656, "opRating": 344}, {"opponent": "lickilicky", "rating": 600, "opRating": 400}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "spiritomb", "rating": 355}, {"opponent": "bastiodon", "rating": 377}, {"opponent": "gliscor", "rating": 435}, {"opponent": "dusknoir_shadow", "rating": 438}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 10188}, {"moveId": "SAND_ATTACK", "uses": 10170}, {"moveId": "GUST", "uses": 8986}, {"moveId": "WING_ATTACK", "uses": 8210}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 13175}, {"moveId": "CLOSE_COMBAT", "uses": 11600}, {"moveId": "FLY", "uses": 11158}, {"moveId": "HEAT_WAVE", "uses": 1597}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 74.5}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 549, "matchups": [{"opponent": "drifb<PERSON>", "rating": 785, "opRating": 214}, {"opponent": "abomasnow_shadow", "rating": 761, "opRating": 238}, {"opponent": "gliscor", "rating": 674}, {"opponent": "qwilfish_his<PERSON>an", "rating": 614, "opRating": 385}, {"opponent": "gliscor_shadow", "rating": 593, "opRating": 406}], "counters": [{"opponent": "gastrodon", "rating": 324}, {"opponent": "spiritomb", "rating": 403}, {"opponent": "dusknoir_shadow", "rating": 444}, {"opponent": "bastiodon", "rating": 453}, {"opponent": "drapion_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 19701}, {"moveId": "WATER_GUN", "uses": 14871}, {"moveId": "TAKE_DOWN", "uses": 2906}], "chargedMoves": [{"moveId": "SURF", "uses": 22844}, {"moveId": "HYPER_FANG", "uses": 11489}, {"moveId": "HYPER_BEAM", "uses": 3085}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 74.3}, {"speciesId": "palkia_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 582, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 750, "opRating": 250}, {"opponent": "dusknoir_shadow", "rating": 700}, {"opponent": "spiritomb", "rating": 685}, {"opponent": "hippo<PERSON><PERSON>", "rating": 655, "opRating": 345}, {"opponent": "gliscor", "rating": 555}], "counters": [{"opponent": "drapion_shadow", "rating": 233}, {"opponent": "gallade_shadow", "rating": 259}, {"opponent": "bastiodon", "rating": 323}, {"opponent": "gastrodon", "rating": 443}, {"opponent": "drifb<PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 18831}, {"moveId": "DRAGON_BREATH", "uses": 18669}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 23261}, {"moveId": "DRACO_METEOR", "uses": 6084}, {"moveId": "FIRE_BLAST", "uses": 5022}, {"moveId": "HYDRO_PUMP", "uses": 2984}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "DRACO_METEOR"], "score": 74}, {"speciesId": "lumineon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 491, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 667, "opRating": 332}, {"opponent": "gliscor", "rating": 594}, {"opponent": "drapion_shadow", "rating": 576}, {"opponent": "bastiodon", "rating": 569}, {"opponent": "sneasler", "rating": 532, "opRating": 467}], "counters": [{"opponent": "gastrodon", "rating": 375}, {"opponent": "spiritomb", "rating": 456}, {"opponent": "gallade_shadow", "rating": 461}, {"opponent": "drifb<PERSON>", "rating": 483}, {"opponent": "dusknoir_shadow", "rating": 494}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 18826}, {"moveId": "WATERFALL", "uses": 18674}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 15197}, {"moveId": "BLIZZARD", "uses": 11130}, {"moveId": "SILVER_WIND", "uses": 11114}]}, "moveset": ["WATER_GUN", "WATER_PULSE", "SILVER_WIND"], "score": 73.7}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 537, "matchups": [{"opponent": "dusknoir_shadow", "rating": 717}, {"opponent": "drifb<PERSON>", "rating": 692}, {"opponent": "abomasnow_shadow", "rating": 618}, {"opponent": "gliscor", "rating": 598}, {"opponent": "spiritomb", "rating": 553}], "counters": [{"opponent": "sneasler", "rating": 290}, {"opponent": "drapion_shadow", "rating": 326}, {"opponent": "gallade_shadow", "rating": 331}, {"opponent": "gastrodon", "rating": 345}, {"opponent": "bastiodon", "rating": 377}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 20967}, {"moveId": "POISON_JAB", "uses": 16533}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 11979}, {"moveId": "DARK_PULSE", "uses": 7839}, {"moveId": "ICE_BEAM", "uses": 6422}, {"moveId": "SHADOW_BALL", "uses": 5691}, {"moveId": "SLUDGE_BOMB", "uses": 5631}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 73.7}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 503, "matchups": [{"opponent": "gastrodon", "rating": 787}, {"opponent": "gliscor", "rating": 765}, {"opponent": "gliscor_shadow", "rating": 758, "opRating": 241}, {"opponent": "gallade_shadow", "rating": 751}, {"opponent": "sneasler", "rating": 633, "opRating": 366}], "counters": [{"opponent": "dusknoir_shadow", "rating": 177}, {"opponent": "bastiodon", "rating": 201}, {"opponent": "drapion_shadow", "rating": 317}, {"opponent": "abomasnow_shadow", "rating": 321}, {"opponent": "spiritomb", "rating": 423}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19223}, {"moveId": "ASTONISH", "uses": 18277}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 10909}, {"moveId": "MYSTICAL_FIRE", "uses": 9494}, {"moveId": "SHADOW_BALL", "uses": 9105}, {"moveId": "OMINOUS_WIND", "uses": 4642}, {"moveId": "RETURN", "uses": 3341}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 73.6}, {"speciesId": "kleavor", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 560, "matchups": [{"opponent": "abomasnow", "rating": 879, "opRating": 120}, {"opponent": "abomasnow_shadow", "rating": 855, "opRating": 144}, {"opponent": "electivire_shadow", "rating": 855, "opRating": 144}, {"opponent": "gastrodon", "rating": 788}, {"opponent": "gallade_shadow", "rating": 557, "opRating": 442}], "counters": [{"opponent": "bastiodon", "rating": 190}, {"opponent": "gliscor", "rating": 331}, {"opponent": "dusknoir_shadow", "rating": 366}, {"opponent": "spiritomb", "rating": 471}, {"opponent": "drapion_shadow", "rating": 487}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 18308}, {"moveId": "QUICK_ATTACK", "uses": 11409}, {"moveId": "AIR_SLASH", "uses": 7749}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10183}, {"moveId": "TRAILBLAZE", "uses": 9318}, {"moveId": "STONE_EDGE", "uses": 9259}, {"moveId": "ROCK_SLIDE", "uses": 8726}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TRAILBLAZE"], "score": 72.9}, {"speciesId": "snea<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 576, "matchups": [{"opponent": "lickilicky", "rating": 779, "opRating": 220}, {"opponent": "qwilfish_his<PERSON>an", "rating": 734, "opRating": 265}, {"opponent": "drapion_shadow", "rating": 711}, {"opponent": "abomasnow_shadow", "rating": 639, "opRating": 360}, {"opponent": "bastiodon", "rating": 617}], "counters": [{"opponent": "gallade_shadow", "rating": 192}, {"opponent": "gliscor", "rating": 284}, {"opponent": "spiritomb", "rating": 293}, {"opponent": "dusknoir_shadow", "rating": 338}, {"opponent": "gastrodon", "rating": 407}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 28660}, {"moveId": "ROCK_SMASH", "uses": 8840}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 17133}, {"moveId": "AERIAL_ACE", "uses": 9796}, {"moveId": "X_SCISSOR", "uses": 7492}, {"moveId": "RETURN", "uses": 3112}]}, "moveset": ["POISON_JAB", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 72.9}, {"speciesId": "hippopotas_shadow", "speciesName": "Hip<PERSON><PERSON><PERSON> (Shadow)", "rating": 556, "matchups": [{"opponent": "bastiodon", "rating": 884, "opRating": 115}, {"opponent": "qwilfish_his<PERSON>an", "rating": 680, "opRating": 319}, {"opponent": "vespiquen", "rating": 674, "opRating": 325}, {"opponent": "drapion_shadow", "rating": 654}, {"opponent": "toxicroak", "rating": 601, "opRating": 398}], "counters": [{"opponent": "gallade_shadow", "rating": 158}, {"opponent": "gliscor", "rating": 323}, {"opponent": "gastrodon", "rating": 377}, {"opponent": "spiritomb", "rating": 403}, {"opponent": "dusknoir_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 19855}, {"moveId": "TACKLE", "uses": 9529}, {"moveId": "BITE", "uses": 8076}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 14830}, {"moveId": "DIG", "uses": 12033}, {"moveId": "BODY_SLAM", "uses": 10583}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 72.6}, {"speciesId": "p<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537, "matchups": [{"opponent": "drifb<PERSON>", "rating": 700, "opRating": 299}, {"opponent": "qwilfish_his<PERSON>an", "rating": 686, "opRating": 313}, {"opponent": "sneasler", "rating": 602, "opRating": 397}, {"opponent": "drapion_shadow", "rating": 591}, {"opponent": "spiritomb", "rating": 577}], "counters": [{"opponent": "gastrodon", "rating": 187}, {"opponent": "gliscor", "rating": 206}, {"opponent": "bastiodon", "rating": 323}, {"opponent": "dusknoir_shadow", "rating": 416}, {"opponent": "gallade_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 21906}, {"moveId": "SPARK", "uses": 15594}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 23712}, {"moveId": "THUNDERBOLT", "uses": 7400}, {"moveId": "THUNDER", "uses": 6410}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 72.6}, {"speciesId": "bastiodon_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 591, "matchups": [{"opponent": "vespiquen", "rating": 805, "opRating": 194}, {"opponent": "abomasnow_shadow", "rating": 751, "opRating": 248}, {"opponent": "drifb<PERSON>", "rating": 748}, {"opponent": "drapion_shadow", "rating": 676}, {"opponent": "spiritomb", "rating": 647}], "counters": [{"opponent": "gallade_shadow", "rating": 86}, {"opponent": "gastrodon", "rating": 107}, {"opponent": "sneasler", "rating": 107}, {"opponent": "gliscor", "rating": 155}, {"opponent": "dusknoir_shadow", "rating": 166}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 23270}, {"moveId": "IRON_TAIL", "uses": 14230}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 18042}, {"moveId": "FLAMETHROWER", "uses": 11595}, {"moveId": "FLASH_CANNON", "uses": 7824}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "STONE_EDGE", "FLAMETHROWER"], "score": 72.5}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 573, "matchups": [{"opponent": "drifb<PERSON>", "rating": 889}, {"opponent": "dusknoir_shadow", "rating": 793}, {"opponent": "spiritomb", "rating": 751}, {"opponent": "gastrodon", "rating": 665}, {"opponent": "drapion_shadow", "rating": 503}], "counters": [{"opponent": "abomasnow_shadow", "rating": 83}, {"opponent": "gliscor", "rating": 107}, {"opponent": "gallade_shadow", "rating": 115}, {"opponent": "bastiodon", "rating": 230}, {"opponent": "sneasler", "rating": 424}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28837}, {"moveId": "PECK", "uses": 8663}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18233}, {"moveId": "DARK_PULSE", "uses": 11317}, {"moveId": "PSYCHIC", "uses": 3974}, {"moveId": "SKY_ATTACK", "uses": 3958}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 72.5}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 544, "matchups": [{"opponent": "spiritomb", "rating": 761}, {"opponent": "bastiodon", "rating": 690}, {"opponent": "vespiquen", "rating": 634, "opRating": 365}, {"opponent": "drifb<PERSON>", "rating": 626}, {"opponent": "dusknoir_shadow", "rating": 582}], "counters": [{"opponent": "abomasnow_shadow", "rating": 83}, {"opponent": "gallade_shadow", "rating": 187}, {"opponent": "drapion_shadow", "rating": 216}, {"opponent": "gastrodon", "rating": 297}, {"opponent": "gliscor", "rating": 413}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23867}, {"moveId": "SMACK_DOWN", "uses": 13633}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 9959}, {"moveId": "BREAKING_SWIPE", "uses": 6760}, {"moveId": "SUPER_POWER", "uses": 6297}, {"moveId": "SURF", "uses": 5269}, {"moveId": "EARTHQUAKE", "uses": 4313}, {"moveId": "STONE_EDGE", "uses": 2757}, {"moveId": "SKULL_BASH", "uses": 2105}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 72.5}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 540, "matchups": [{"opponent": "gastrodon", "rating": 800}, {"opponent": "electivire_shadow", "rating": 727, "opRating": 272}, {"opponent": "drifb<PERSON>", "rating": 643, "opRating": 356}, {"opponent": "gliscor", "rating": 597}, {"opponent": "qwilfish_his<PERSON>an", "rating": 503, "opRating": 496}], "counters": [{"opponent": "gallade_shadow", "rating": 120}, {"opponent": "bastiodon", "rating": 194}, {"opponent": "drapion_shadow", "rating": 398}, {"opponent": "spiritomb", "rating": 403}, {"opponent": "dusknoir_shadow", "rating": 472}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18959}, {"moveId": "LEAFAGE", "uses": 12579}, {"moveId": "RAZOR_LEAF", "uses": 6046}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 16022}, {"moveId": "ENERGY_BALL", "uses": 6955}, {"moveId": "ICY_WIND", "uses": 5428}, {"moveId": "OUTRAGE", "uses": 4189}, {"moveId": "RETURN", "uses": 2489}, {"moveId": "BLIZZARD", "uses": 2468}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 72.3}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 578, "matchups": [{"opponent": "bibarel", "rating": 946, "opRating": 53}, {"opponent": "empoleon_shadow", "rating": 893, "opRating": 106}, {"opponent": "abomasnow_shadow", "rating": 730, "opRating": 269}, {"opponent": "qwilfish_his<PERSON>an", "rating": 721, "opRating": 278}, {"opponent": "bastiodon", "rating": 548}], "counters": [{"opponent": "gliscor", "rating": 258}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "gastrodon", "rating": 327}, {"opponent": "spiritomb", "rating": 355}, {"opponent": "drapion_shadow", "rating": 487}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 15664}, {"moveId": "FORCE_PALM", "uses": 13049}, {"moveId": "COUNTER", "uses": 8792}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 12112}, {"moveId": "BLAZE_KICK", "uses": 6711}, {"moveId": "THUNDER_PUNCH", "uses": 5742}, {"moveId": "SHADOW_BALL", "uses": 5336}, {"moveId": "FLASH_CANNON", "uses": 2799}, {"moveId": "AURA_SPHERE", "uses": 2742}, {"moveId": "POWER_UP_PUNCH", "uses": 2131}]}, "moveset": ["FORCE_PALM", "THUNDER_PUNCH", "SHADOW_BALL"], "score": 72.3}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 531, "matchups": [{"opponent": "empoleon_shadow", "rating": 873, "opRating": 126}, {"opponent": "drifb<PERSON>", "rating": 865, "opRating": 134}, {"opponent": "sneasler", "rating": 852, "opRating": 147}, {"opponent": "spiritomb", "rating": 747, "opRating": 252}, {"opponent": "dusknoir_shadow", "rating": 714}], "counters": [{"opponent": "drapion_shadow", "rating": 139}, {"opponent": "bastiodon", "rating": 154}, {"opponent": "gastrodon", "rating": 178}, {"opponent": "gliscor", "rating": 224}, {"opponent": "gallade_shadow", "rating": 302}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 4995}, {"moveId": "SPARK", "uses": 4643}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2194}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2150}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2121}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1892}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1878}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1824}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1806}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1750}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1666}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1646}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1635}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1624}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1564}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1527}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1387}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1158}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17398}, {"moveId": "PSYCHIC_FANGS", "uses": 9264}, {"moveId": "CRUNCH", "uses": 8297}, {"moveId": "HYPER_BEAM", "uses": 2563}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 72.3}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 552, "matchups": [{"opponent": "drifb<PERSON>", "rating": 921, "opRating": 78}, {"opponent": "dusknoir_shadow", "rating": 843}, {"opponent": "electivire_shadow", "rating": 817, "opRating": 182}, {"opponent": "gastrodon", "rating": 640}, {"opponent": "gliscor", "rating": 557}], "counters": [{"opponent": "drapion_shadow", "rating": 105}, {"opponent": "abomasnow_shadow", "rating": 157}, {"opponent": "gallade_shadow", "rating": 250}, {"opponent": "bastiodon", "rating": 330}, {"opponent": "spiritomb", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 23193}, {"moveId": "SNARL", "uses": 14307}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 12196}, {"moveId": "FOUL_PLAY", "uses": 11302}, {"moveId": "FLAMETHROWER", "uses": 8387}, {"moveId": "SLUDGE_BOMB", "uses": 5686}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "FOUL_PLAY"], "score": 72.3}, {"speciesId": "snea<PERSON>_<PERSON><PERSON>an_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) (<PERSON>)", "rating": 559, "matchups": [{"opponent": "bastiodon", "rating": 864}, {"opponent": "lickilicky", "rating": 725, "opRating": 274}, {"opponent": "qwilfish_his<PERSON>an", "rating": 693, "opRating": 306}, {"opponent": "drapion_shadow", "rating": 671}, {"opponent": "abomasnow_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "gallade_shadow", "rating": 240}, {"opponent": "gastrodon", "rating": 273}, {"opponent": "gliscor", "rating": 310}, {"opponent": "spiritomb", "rating": 322}, {"opponent": "dusknoir_shadow", "rating": 355}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29183}, {"moveId": "ROCK_SMASH", "uses": 8317}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18500}, {"moveId": "AERIAL_ACE", "uses": 10816}, {"moveId": "X_SCISSOR", "uses": 8133}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 71.6}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 506, "matchups": [{"opponent": "gliscor", "rating": 620}, {"opponent": "gastrodon", "rating": 615}, {"opponent": "dusknoir_shadow", "rating": 596}, {"opponent": "spiritomb", "rating": 538}, {"opponent": "gallade_shadow", "rating": 533}], "counters": [{"opponent": "drapion_shadow", "rating": 63}, {"opponent": "sneasler", "rating": 178}, {"opponent": "bastiodon", "rating": 266}, {"opponent": "qwilfish_his<PERSON>an", "rating": 272}, {"opponent": "abomasnow_shadow", "rating": 447}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 10828}, {"moveId": "PSYWAVE", "uses": 10193}, {"moveId": "SUCKER_PUNCH", "uses": 9724}, {"moveId": "MAGICAL_LEAF", "uses": 6657}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 15918}, {"moveId": "DARK_PULSE", "uses": 11033}, {"moveId": "DAZZLING_GLEAM", "uses": 10403}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 70.3}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 564, "matchups": [{"opponent": "abomasnow", "rating": 945, "opRating": 54}, {"opponent": "froslass", "rating": 891, "opRating": 108}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 864, "opRating": 135}, {"opponent": "abomasnow_shadow", "rating": 860, "opRating": 139}, {"opponent": "vespiquen", "rating": 707, "opRating": 292}], "counters": [{"opponent": "gastrodon", "rating": 205}, {"opponent": "bastiodon", "rating": 363}, {"opponent": "gliscor", "rating": 375}, {"opponent": "drapion_shadow", "rating": 461}, {"opponent": "dusknoir_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 23207}, {"moveId": "BUG_BITE", "uses": 14293}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 13201}, {"moveId": "EARTH_POWER", "uses": 7054}, {"moveId": "STONE_EDGE", "uses": 6385}, {"moveId": "IRON_HEAD", "uses": 5473}, {"moveId": "FLAMETHROWER", "uses": 3570}, {"moveId": "FIRE_BLAST", "uses": 1887}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 70}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 509, "matchups": [{"opponent": "abomasnow_shadow", "rating": 784, "opRating": 215}, {"opponent": "vespiquen", "rating": 658, "opRating": 341}, {"opponent": "qwilfish_his<PERSON>an", "rating": 597, "opRating": 402}, {"opponent": "drapion_shadow", "rating": 568}, {"opponent": "gliscor_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "gliscor", "rating": 443}, {"opponent": "bastiodon", "rating": 453}, {"opponent": "spiritomb", "rating": 490}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 28834}, {"moveId": "ROCK_SMASH", "uses": 8666}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 16877}, {"moveId": "LOW_SWEEP", "uses": 10572}, {"moveId": "RETURN", "uses": 6309}, {"moveId": "FLAME_WHEEL", "uses": 3788}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 70}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 590, "matchups": [{"opponent": "abomasnow_shadow", "rating": 907, "opRating": 92}, {"opponent": "abomasnow", "rating": 907, "opRating": 92}, {"opponent": "empoleon_shadow", "rating": 884, "opRating": 115}, {"opponent": "magnezone_shadow", "rating": 833, "opRating": 166}, {"opponent": "drifb<PERSON>", "rating": 583, "opRating": 416}], "counters": [{"opponent": "drapion_shadow", "rating": 84}, {"opponent": "gastrodon", "rating": 250}, {"opponent": "gliscor", "rating": 340}, {"opponent": "bastiodon", "rating": 359}, {"opponent": "dusknoir_shadow", "rating": 361}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 19941}, {"moveId": "FIRE_SPIN", "uses": 17559}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 12808}, {"moveId": "SCORCHING_SANDS", "uses": 8123}, {"moveId": "BRICK_BREAK", "uses": 5499}, {"moveId": "THUNDERBOLT", "uses": 5235}, {"moveId": "PSYCHIC", "uses": 3750}, {"moveId": "FIRE_BLAST", "uses": 2194}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 69.9}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 528, "matchups": [{"opponent": "electivire_shadow", "rating": 729, "opRating": 270}, {"opponent": "qwilfish_his<PERSON>an", "rating": 642, "opRating": 357}, {"opponent": "hippo<PERSON><PERSON>", "rating": 614, "opRating": 385}, {"opponent": "gliscor_shadow", "rating": 543, "opRating": 456}, {"opponent": "bibarel", "rating": 522, "opRating": 477}], "counters": [{"opponent": "dusknoir_shadow", "rating": 266}, {"opponent": "bastiodon", "rating": 381}, {"opponent": "drapion_shadow", "rating": 457}, {"opponent": "gastrodon", "rating": 464}, {"opponent": "gliscor", "rating": 474}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 18843}, {"moveId": "TACKLE", "uses": 18657}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21367}, {"moveId": "BULLDOZE", "uses": 11046}, {"moveId": "GUNK_SHOT", "uses": 5059}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 69.4}, {"speciesId": "prin<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 436, "matchups": [{"opponent": "gliscor", "rating": 731}, {"opponent": "bastiodon", "rating": 558}, {"opponent": "gastrodon", "rating": 529}, {"opponent": "drapion_shadow", "rating": 525}, {"opponent": "sneasler", "rating": 522, "opRating": 477}], "counters": [{"opponent": "abomasnow_shadow", "rating": 87}, {"opponent": "spiritomb", "rating": 355}, {"opponent": "dusknoir_shadow", "rating": 377}, {"opponent": "drifb<PERSON>", "rating": 425}, {"opponent": "gallade_shadow", "rating": 427}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 22326}, {"moveId": "METAL_CLAW", "uses": 15174}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 17215}, {"moveId": "HYDRO_PUMP", "uses": 8487}, {"moveId": "BUBBLE_BEAM", "uses": 6276}, {"moveId": "RETURN", "uses": 5480}]}, "moveset": ["BUBBLE", "ICY_WIND", "HYDRO_PUMP"], "score": 69.2}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 545, "matchups": [{"opponent": "drifb<PERSON>", "rating": 924, "opRating": 75}, {"opponent": "dusknoir_shadow", "rating": 849}, {"opponent": "froslass", "rating": 660, "opRating": 339}, {"opponent": "hippo<PERSON><PERSON>", "rating": 594, "opRating": 405}, {"opponent": "gliscor", "rating": 566}], "counters": [{"opponent": "gallade_shadow", "rating": 86}, {"opponent": "bastiodon", "rating": 161}, {"opponent": "gastrodon", "rating": 377}, {"opponent": "spiritomb", "rating": 423}, {"opponent": "drapion_shadow", "rating": 457}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15144}, {"moveId": "ICE_SHARD", "uses": 13688}, {"moveId": "FEINT_ATTACK", "uses": 8672}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 16392}, {"moveId": "FOUL_PLAY", "uses": 10352}, {"moveId": "TRIPLE_AXEL", "uses": 5740}, {"moveId": "FOCUS_BLAST", "uses": 5039}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 69.2}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 544, "matchups": [{"opponent": "drifb<PERSON>", "rating": 905}, {"opponent": "dusknoir_shadow", "rating": 811}, {"opponent": "spiritomb", "rating": 773}, {"opponent": "abomasnow_shadow", "rating": 698, "opRating": 301}, {"opponent": "drapion_shadow", "rating": 556}], "counters": [{"opponent": "gliscor", "rating": 64}, {"opponent": "sneasler", "rating": 89}, {"opponent": "gallade_shadow", "rating": 100}, {"opponent": "bastiodon", "rating": 183}, {"opponent": "gastrodon", "rating": 449}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15436}, {"moveId": "ICE_SHARD", "uses": 13835}, {"moveId": "FEINT_ATTACK", "uses": 8216}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 16390}, {"moveId": "FOUL_PLAY", "uses": 10368}, {"moveId": "TRIPLE_AXEL", "uses": 5711}, {"moveId": "FOCUS_BLAST", "uses": 5009}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 69.2}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 506, "matchups": [{"opponent": "drifb<PERSON>", "rating": 828, "opRating": 171}, {"opponent": "gliscor_shadow", "rating": 674, "opRating": 325}, {"opponent": "qwilfish_his<PERSON>an", "rating": 624, "opRating": 375}, {"opponent": "lickilicky", "rating": 600, "opRating": 399}, {"opponent": "bastiodon", "rating": 503}], "counters": [{"opponent": "gastrodon", "rating": 291}, {"opponent": "dusknoir_shadow", "rating": 366}, {"opponent": "spiritomb", "rating": 370}, {"opponent": "drapion_shadow", "rating": 415}, {"opponent": "gliscor", "rating": 431}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 19472}, {"moveId": "WATER_GUN", "uses": 14861}, {"moveId": "TAKE_DOWN", "uses": 3152}], "chargedMoves": [{"moveId": "SURF", "uses": 21100}, {"moveId": "HYPER_FANG", "uses": 10234}, {"moveId": "RETURN", "uses": 3504}, {"moveId": "HYPER_BEAM", "uses": 2702}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 68.8}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 498, "matchups": [{"opponent": "sneasler", "rating": 944, "opRating": 55}, {"opponent": "drifb<PERSON>", "rating": 746, "opRating": 253}, {"opponent": "vespiquen", "rating": 585, "opRating": 414}, {"opponent": "bibarel", "rating": 541, "opRating": 458}, {"opponent": "drapion_shadow", "rating": 514}], "counters": [{"opponent": "bastiodon", "rating": 273}, {"opponent": "gastrodon", "rating": 318}, {"opponent": "gliscor", "rating": 336}, {"opponent": "spiritomb", "rating": 475}, {"opponent": "dusknoir_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 26358}, {"moveId": "SCRATCH", "uses": 11142}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 17558}, {"moveId": "THUNDER", "uses": 10675}, {"moveId": "PLAY_ROUGH", "uses": 9197}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 68.3}, {"speciesId": "drifb<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 463, "matchups": [{"opponent": "gastrodon", "rating": 861}, {"opponent": "gliscor", "rating": 739}, {"opponent": "gallade_shadow", "rating": 705}, {"opponent": "gliscor_shadow", "rating": 700, "opRating": 299}, {"opponent": "sneasler", "rating": 550, "opRating": 449}], "counters": [{"opponent": "drapion_shadow", "rating": 101}, {"opponent": "abomasnow_shadow", "rating": 157}, {"opponent": "bastiodon", "rating": 294}, {"opponent": "dusknoir_shadow", "rating": 333}, {"opponent": "spiritomb", "rating": 423}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19458}, {"moveId": "ASTONISH", "uses": 18042}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 12028}, {"moveId": "MYSTICAL_FIRE", "uses": 10495}, {"moveId": "SHADOW_BALL", "uses": 9941}, {"moveId": "OMINOUS_WIND", "uses": 5033}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "ICY_WIND", "SHADOW_BALL"], "score": 68.2}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 493, "matchups": [{"opponent": "bastiodon", "rating": 858}, {"opponent": "lickilicky", "rating": 786, "opRating": 213}, {"opponent": "gliscor_shadow", "rating": 685, "opRating": 314}, {"opponent": "gliscor", "rating": 516}, {"opponent": "drifb<PERSON>", "rating": 512, "opRating": 487}], "counters": [{"opponent": "gallade_shadow", "rating": 57}, {"opponent": "dusknoir_shadow", "rating": 261}, {"opponent": "drapion_shadow", "rating": 317}, {"opponent": "spiritomb", "rating": 408}, {"opponent": "gastrodon", "rating": 476}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 30384}, {"moveId": "LOW_KICK", "uses": 4324}, {"moveId": "POUND", "uses": 2769}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 14308}, {"moveId": "FIRE_PUNCH", "uses": 12188}, {"moveId": "FOCUS_BLAST", "uses": 6541}, {"moveId": "HYPER_BEAM", "uses": 4460}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FOCUS_BLAST"], "score": 68}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 522, "matchups": [{"opponent": "electrode_hisuian", "rating": 889, "opRating": 110}, {"opponent": "dusknoir_shadow", "rating": 705}, {"opponent": "abomasnow_shadow", "rating": 623, "opRating": 376}, {"opponent": "bibarel", "rating": 591, "opRating": 408}, {"opponent": "gastrodon", "rating": 572}], "counters": [{"opponent": "bastiodon", "rating": 136}, {"opponent": "gallade_shadow", "rating": 216}, {"opponent": "drapion_shadow", "rating": 355}, {"opponent": "gliscor", "rating": 379}, {"opponent": "spiritomb", "rating": 480}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 23979}, {"moveId": "BITE", "uses": 13521}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 12935}, {"moveId": "TRAILBLAZE", "uses": 10389}, {"moveId": "FLAMETHROWER", "uses": 7471}, {"moveId": "SLUDGE_BOMB", "uses": 6621}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 67.9}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 435, "matchups": [{"opponent": "gliscor", "rating": 739}, {"opponent": "gallade_shadow", "rating": 713}, {"opponent": "gastrodon", "rating": 617}, {"opponent": "sneasler", "rating": 600, "opRating": 400}, {"opponent": "drifb<PERSON>", "rating": 521}], "counters": [{"opponent": "bastiodon", "rating": 154}, {"opponent": "abomasnow_shadow", "rating": 167}, {"opponent": "drapion_shadow", "rating": 330}, {"opponent": "dusknoir_shadow", "rating": 427}, {"opponent": "spiritomb", "rating": 461}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 3545}, {"moveId": "CHARM", "uses": 2977}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2582}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2402}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2322}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2125}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2115}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2085}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1975}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1867}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1849}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1817}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1789}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1753}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1750}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1697}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1508}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1316}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 8021}, {"moveId": "PSYSHOCK", "uses": 7754}, {"moveId": "DAZZLING_GLEAM", "uses": 6059}, {"moveId": "AURA_SPHERE", "uses": 5902}, {"moveId": "ANCIENT_POWER", "uses": 5050}, {"moveId": "FLAMETHROWER", "uses": 4604}]}, "moveset": ["CHARM", "PSYSHOCK", "AURA_SPHERE"], "score": 67.2}, {"speciesId": "avalugg_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 512, "matchups": [{"opponent": "gliscor", "rating": 807}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 774, "opRating": 225}, {"opponent": "vespiquen", "rating": 741, "opRating": 258}, {"opponent": "gliscor_shadow", "rating": 737, "opRating": 262}, {"opponent": "drifb<PERSON>", "rating": 614, "opRating": 385}], "counters": [{"opponent": "bastiodon", "rating": 136}, {"opponent": "gastrodon", "rating": 291}, {"opponent": "spiritomb", "rating": 326}, {"opponent": "drapion_shadow", "rating": 355}, {"opponent": "dusknoir_shadow", "rating": 361}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 21557}, {"moveId": "TACKLE", "uses": 8869}, {"moveId": "BITE", "uses": 7039}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 13655}, {"moveId": "ROCK_SLIDE", "uses": 9288}, {"moveId": "CRUNCH", "uses": 8414}, {"moveId": "BLIZZARD", "uses": 6127}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ROCK_SLIDE"], "score": 66.2}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 482, "matchups": [{"opponent": "sneasler", "rating": 873, "opRating": 126}, {"opponent": "spiritomb", "rating": 731, "opRating": 268}, {"opponent": "drifb<PERSON>", "rating": 684, "opRating": 315}, {"opponent": "qwilfish_his<PERSON>an", "rating": 676, "opRating": 323}, {"opponent": "drapion_shadow", "rating": 542}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "gliscor", "rating": 211}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "bastiodon", "rating": 276}, {"opponent": "gastrodon", "rating": 279}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 4541}, {"moveId": "SPARK", "uses": 4287}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2278}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2149}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2096}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1952}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1940}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1867}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1843}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1818}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1702}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1693}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1681}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1651}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1618}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1594}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1460}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1182}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17359}, {"moveId": "PSYCHIC_FANGS", "uses": 9296}, {"moveId": "CRUNCH", "uses": 8340}, {"moveId": "HYPER_BEAM", "uses": 2564}]}, "moveset": ["SPARK", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 66.2}, {"speciesId": "monferno_shadow", "speciesName": "Mon<PERSON> (Shadow)", "rating": 501, "matchups": [{"opponent": "empoleon_shadow", "rating": 892, "opRating": 107}, {"opponent": "spiritomb", "rating": 827, "opRating": 172}, {"opponent": "abomasnow_shadow", "rating": 737, "opRating": 262}, {"opponent": "vespiquen", "rating": 611, "opRating": 388}, {"opponent": "gliscor", "rating": 525}], "counters": [{"opponent": "gallade_shadow", "rating": 216}, {"opponent": "gastrodon", "rating": 264}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "bastiodon", "rating": 327}, {"opponent": "drapion_shadow", "rating": 419}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 28914}, {"moveId": "ROCK_SMASH", "uses": 8586}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 20183}, {"moveId": "LOW_SWEEP", "uses": 12757}, {"moveId": "FLAME_WHEEL", "uses": 4547}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 66.2}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 463, "matchups": [{"opponent": "gastrodon", "rating": 828}, {"opponent": "dusknoir_shadow", "rating": 597}, {"opponent": "gallade_shadow", "rating": 593}, {"opponent": "qwilfish_his<PERSON>an", "rating": 578, "opRating": 421}, {"opponent": "drapion_shadow", "rating": 527}], "counters": [{"opponent": "gliscor", "rating": 129}, {"opponent": "bastiodon", "rating": 190}, {"opponent": "drifb<PERSON>", "rating": 222}, {"opponent": "abomasnow_shadow", "rating": 398}, {"opponent": "spiritomb", "rating": 466}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 21671}, {"moveId": "CONFUSION", "uses": 15829}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 17077}, {"moveId": "BUG_BUZZ", "uses": 14156}, {"moveId": "PSYBEAM", "uses": 6290}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 66}, {"speciesId": "drifloon", "speciesName": "Drifloon", "rating": 460, "matchups": [{"opponent": "gastrodon", "rating": 766}, {"opponent": "gallade_shadow", "rating": 747, "opRating": 252}, {"opponent": "gliscor_shadow", "rating": 733, "opRating": 266}, {"opponent": "gliscor", "rating": 607}, {"opponent": "sneasler", "rating": 596, "opRating": 403}], "counters": [{"opponent": "dusknoir_shadow", "rating": 155}, {"opponent": "bastiodon", "rating": 190}, {"opponent": "drapion_shadow", "rating": 288}, {"opponent": "drifb<PERSON>", "rating": 344}, {"opponent": "spiritomb", "rating": 403}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19075}, {"moveId": "ASTONISH", "uses": 18425}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 14360}, {"moveId": "SHADOW_BALL", "uses": 12182}, {"moveId": "OMINOUS_WIND", "uses": 6268}, {"moveId": "RETURN", "uses": 4766}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 65.8}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 495, "matchups": [{"opponent": "abomasnow_shadow", "rating": 684, "opRating": 315}, {"opponent": "drifb<PERSON>", "rating": 672}, {"opponent": "drapion_shadow", "rating": 621}, {"opponent": "spiritomb", "rating": 584}, {"opponent": "bastiodon", "rating": 546}], "counters": [{"opponent": "gallade_shadow", "rating": 96}, {"opponent": "sneasler", "rating": 107}, {"opponent": "gastrodon", "rating": 136}, {"opponent": "gliscor", "rating": 215}, {"opponent": "dusknoir_shadow", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 20142}, {"moveId": "SPARK", "uses": 17358}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 12572}, {"moveId": "ROCK_SLIDE", "uses": 11734}, {"moveId": "THUNDERBOLT", "uses": 6465}, {"moveId": "RETURN", "uses": 4421}, {"moveId": "ZAP_CANNON", "uses": 2321}]}, "moveset": ["SPARK", "ROCK_SLIDE", "ZAP_CANNON"], "score": 65.7}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 482, "matchups": [{"opponent": "gastrodon", "rating": 790}, {"opponent": "hippo<PERSON><PERSON>", "rating": 761, "opRating": 238}, {"opponent": "bibarel", "rating": 672, "opRating": 327}, {"opponent": "lickilicky", "rating": 580, "opRating": 419}, {"opponent": "dusknoir_shadow", "rating": 503}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "drapion_shadow", "rating": 313}, {"opponent": "bastiodon", "rating": 352}, {"opponent": "gliscor", "rating": 392}, {"opponent": "spiritomb", "rating": 413}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 22632}, {"moveId": "INFESTATION", "uses": 14868}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 14265}, {"moveId": "ROCK_SLIDE", "uses": 9696}, {"moveId": "SLUDGE_BOMB", "uses": 5855}, {"moveId": "ANCIENT_POWER", "uses": 4673}, {"moveId": "SOLAR_BEAM", "uses": 2948}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 65.5}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 508, "matchups": [{"opponent": "toxicroak", "rating": 790, "opRating": 209}, {"opponent": "sneasler", "rating": 758, "opRating": 241}, {"opponent": "abomasnow_shadow", "rating": 592, "opRating": 407}, {"opponent": "gallade_shadow", "rating": 588, "opRating": 411}, {"opponent": "gastrodon", "rating": 560}], "counters": [{"opponent": "spiritomb", "rating": 105}, {"opponent": "bastiodon", "rating": 244}, {"opponent": "drapion_shadow", "rating": 254}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 306}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 21029}, {"moveId": "EXTRASENSORY", "uses": 16471}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17018}, {"moveId": "FUTURE_SIGHT", "uses": 10592}, {"moveId": "THUNDER", "uses": 9885}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 65.5}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 482, "matchups": [{"opponent": "dusknoir_shadow", "rating": 887, "opRating": 112}, {"opponent": "dusknoir", "rating": 782, "opRating": 217}, {"opponent": "drifb<PERSON>", "rating": 677, "opRating": 322}, {"opponent": "abomasnow_shadow", "rating": 564, "opRating": 435}, {"opponent": "froslass", "rating": 504, "opRating": 495}], "counters": [{"opponent": "bastiodon", "rating": 287}, {"opponent": "drapion_shadow", "rating": 334}, {"opponent": "gastrodon", "rating": 360}, {"opponent": "gliscor", "rating": 366}, {"opponent": "spiritomb", "rating": 403}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 25379}, {"moveId": "SCRATCH", "uses": 12121}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 19569}, {"moveId": "LOW_SWEEP", "uses": 9873}, {"moveId": "HYPER_BEAM", "uses": 8028}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 65.2}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "drifb<PERSON>", "rating": 826, "opRating": 173}, {"opponent": "dusknoir_shadow", "rating": 653}, {"opponent": "gliscor_shadow", "rating": 620, "opRating": 379}, {"opponent": "gallade_shadow", "rating": 591, "opRating": 408}, {"opponent": "sneasler", "rating": 524, "opRating": 475}], "counters": [{"opponent": "drapion_shadow", "rating": 50}, {"opponent": "bastiodon", "rating": 248}, {"opponent": "gastrodon", "rating": 303}, {"opponent": "gliscor", "rating": 314}, {"opponent": "spiritomb", "rating": 456}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 10557}, {"moveId": "PSYWAVE", "uses": 10339}, {"moveId": "SUCKER_PUNCH", "uses": 9854}, {"moveId": "MAGICAL_LEAF", "uses": 6767}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 15946}, {"moveId": "DARK_PULSE", "uses": 11083}, {"moveId": "DAZZLING_GLEAM", "uses": 10413}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 64.9}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 498, "matchups": [{"opponent": "vespiquen", "rating": 852, "opRating": 147}, {"opponent": "drifb<PERSON>", "rating": 659}, {"opponent": "bastiodon", "rating": 567}, {"opponent": "drapion_shadow", "rating": 546}, {"opponent": "spiritomb", "rating": 529}], "counters": [{"opponent": "gallade_shadow", "rating": 96}, {"opponent": "gastrodon", "rating": 187}, {"opponent": "gliscor", "rating": 250}, {"opponent": "dusknoir_shadow", "rating": 344}, {"opponent": "abomasnow_shadow", "rating": 482}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 19999}, {"moveId": "SPARK", "uses": 17501}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 14289}, {"moveId": "ROCK_SLIDE", "uses": 13307}, {"moveId": "THUNDERBOLT", "uses": 7248}, {"moveId": "ZAP_CANNON", "uses": 2574}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "ZAP_CANNON"], "score": 64.9}, {"speciesId": "dialga_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 480, "matchups": [{"opponent": "gliscor", "rating": 682}, {"opponent": "bibarel", "rating": 672, "opRating": 327}, {"opponent": "vespiquen", "rating": 654, "opRating": 345}, {"opponent": "gliscor_shadow", "rating": 649, "opRating": 350}, {"opponent": "drapion_shadow", "rating": 528}], "counters": [{"opponent": "gastrodon", "rating": 238}, {"opponent": "dusknoir_shadow", "rating": 288}, {"opponent": "gallade_shadow", "rating": 317}, {"opponent": "spiritomb", "rating": 326}, {"opponent": "bastiodon", "rating": 370}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21451}, {"moveId": "METAL_CLAW", "uses": 16049}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 15308}, {"moveId": "DRACO_METEOR", "uses": 11368}, {"moveId": "THUNDER", "uses": 10792}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 64.3}, {"speciesId": "snover", "speciesName": "Snover", "rating": 461, "matchups": [{"opponent": "gastrodon", "rating": 771}, {"opponent": "drifb<PERSON>", "rating": 686, "opRating": 313}, {"opponent": "bibarel", "rating": 683, "opRating": 316}, {"opponent": "electivire_shadow", "rating": 679, "opRating": 320}, {"opponent": "gliscor", "rating": 524}], "counters": [{"opponent": "gallade_shadow", "rating": 120}, {"opponent": "bastiodon", "rating": 183}, {"opponent": "spiritomb", "rating": 350}, {"opponent": "dusknoir_shadow", "rating": 411}, {"opponent": "drapion_shadow", "rating": 440}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 14659}, {"moveId": "LEAFAGE", "uses": 11580}, {"moveId": "ICE_SHARD", "uses": 11282}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 14641}, {"moveId": "ENERGY_BALL", "uses": 11416}, {"moveId": "STOMP", "uses": 7356}, {"moveId": "RETURN", "uses": 4112}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 64}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "staraptor_shadow", "rating": 796, "opRating": 203}, {"opponent": "gliscor", "rating": 612}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 593, "opRating": 406}, {"opponent": "gliscor_shadow", "rating": 515, "opRating": 484}, {"opponent": "hippo<PERSON><PERSON>", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dusknoir_shadow", "rating": 216}, {"opponent": "drapion_shadow", "rating": 360}, {"opponent": "bastiodon", "rating": 410}, {"opponent": "gastrodon", "rating": 440}, {"opponent": "spiritomb", "rating": 490}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 18818}, {"moveId": "WATERFALL", "uses": 18682}], "chargedMoves": [{"moveId": "AQUA_JET", "uses": 18286}, {"moveId": "SWIFT", "uses": 10101}, {"moveId": "LIQUIDATION", "uses": 6546}, {"moveId": "HYDRO_PUMP", "uses": 2501}]}, "moveset": ["WATER_GUN", "AQUA_JET", "SWIFT"], "score": 63.8}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 440, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 814, "opRating": 185}, {"opponent": "gastrodon", "rating": 791}, {"opponent": "bibarel", "rating": 685, "opRating": 314}, {"opponent": "lickilicky", "rating": 651, "opRating": 348}, {"opponent": "dusknoir_shadow", "rating": 511}], "counters": [{"opponent": "gallade_shadow", "rating": 240}, {"opponent": "drapion_shadow", "rating": 347}, {"opponent": "bastiodon", "rating": 402}, {"opponent": "spiritomb", "rating": 437}, {"opponent": "gliscor", "rating": 461}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 5123}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2531}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2528}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2289}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2190}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2167}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2068}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2053}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1996}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1935}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1918}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1867}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1840}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1786}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1741}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1551}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1326}, {"moveId": "ZEN_HEADBUTT", "uses": 734}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20155}, {"moveId": "ENERGY_BALL", "uses": 7125}, {"moveId": "SEED_FLARE", "uses": 5995}, {"moveId": "SOLAR_BEAM", "uses": 4126}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "SEED_FLARE"], "score": 63.8}, {"speciesId": "luxio", "speciesName": "Luxio", "rating": 475, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 708, "opRating": 291}, {"opponent": "drifb<PERSON>", "rating": 697, "opRating": 302}, {"opponent": "drapion_shadow", "rating": 690}, {"opponent": "vespiquen", "rating": 629, "opRating": 370}, {"opponent": "dusknoir_shadow", "rating": 539}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "spiritomb", "rating": 153}, {"opponent": "bastiodon", "rating": 291}, {"opponent": "gastrodon", "rating": 294}, {"opponent": "gliscor", "rating": 297}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 25898}, {"moveId": "BITE", "uses": 11602}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19633}, {"moveId": "CRUNCH", "uses": 10109}, {"moveId": "RETURN", "uses": 4185}, {"moveId": "THUNDERBOLT", "uses": 3573}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 63.7}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 537, "matchups": [{"opponent": "abomasnow", "rating": 930, "opRating": 69}, {"opponent": "abomasnow_shadow", "rating": 907, "opRating": 92}, {"opponent": "magnezone_shadow", "rating": 861, "opRating": 138}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 791, "opRating": 208}, {"opponent": "bronzong", "rating": 606, "opRating": 393}], "counters": [{"opponent": "gastrodon", "rating": 252}, {"opponent": "gliscor", "rating": 267}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "bastiodon", "rating": 356}, {"opponent": "drapion_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 19762}, {"moveId": "FIRE_SPIN", "uses": 17738}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 12831}, {"moveId": "SCORCHING_SANDS", "uses": 8113}, {"moveId": "BRICK_BREAK", "uses": 5507}, {"moveId": "THUNDERBOLT", "uses": 5226}, {"moveId": "PSYCHIC", "uses": 3768}, {"moveId": "FIRE_BLAST", "uses": 2184}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 63.2}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 471, "matchups": [{"opponent": "drifb<PERSON>", "rating": 776, "opRating": 223}, {"opponent": "froslass", "rating": 626, "opRating": 373}, {"opponent": "bibarel", "rating": 593, "opRating": 406}, {"opponent": "vespiquen", "rating": 578, "opRating": 421}, {"opponent": "empoleon_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "gastrodon", "rating": 267}, {"opponent": "gliscor", "rating": 293}, {"opponent": "bastiodon", "rating": 345}, {"opponent": "dusknoir_shadow", "rating": 405}, {"opponent": "drapion_shadow", "rating": 406}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 26071}, {"moveId": "SCRATCH", "uses": 11429}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 13751}, {"moveId": "THUNDER", "uses": 8666}, {"moveId": "RETURN", "uses": 7957}, {"moveId": "PLAY_ROUGH", "uses": 7133}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 63.1}, {"speciesId": "drifloon_shadow", "speciesName": "Dr<PERSON><PERSON><PERSON> (Shadow)", "rating": 449, "matchups": [{"opponent": "gliscor", "rating": 733}, {"opponent": "gastrodon", "rating": 723}, {"opponent": "gallade_shadow", "rating": 712, "opRating": 287}, {"opponent": "gliscor_shadow", "rating": 693, "opRating": 306}, {"opponent": "sneasler", "rating": 524, "opRating": 475}], "counters": [{"opponent": "drapion_shadow", "rating": 50}, {"opponent": "dusknoir_shadow", "rating": 200}, {"opponent": "bastiodon", "rating": 215}, {"opponent": "drifb<PERSON>", "rating": 416}, {"opponent": "spiritomb", "rating": 461}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19186}, {"moveId": "ASTONISH", "uses": 18314}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 16745}, {"moveId": "SHADOW_BALL", "uses": 13701}, {"moveId": "OMINOUS_WIND", "uses": 6997}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 62.3}, {"speciesId": "rotom_fan", "speciesName": "<PERSON><PERSON><PERSON> (Fan)", "rating": 412, "matchups": [{"opponent": "gallade_shadow", "rating": 709}, {"opponent": "gliscor", "rating": 623}, {"opponent": "dusknoir_shadow", "rating": 553}, {"opponent": "sneasler", "rating": 543, "opRating": 456}, {"opponent": "drifb<PERSON>", "rating": 516}], "counters": [{"opponent": "abomasnow_shadow", "rating": 115}, {"opponent": "drapion_shadow", "rating": 216}, {"opponent": "bastiodon", "rating": 241}, {"opponent": "spiritomb", "rating": 293}, {"opponent": "gastrodon", "rating": 431}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 21081}, {"moveId": "AIR_SLASH", "uses": 16419}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18201}, {"moveId": "OMINOUS_WIND", "uses": 11369}, {"moveId": "THUNDER", "uses": 7906}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 62.3}, {"speciesId": "shellos", "speciesName": "<PERSON><PERSON>", "rating": 438, "matchups": [{"opponent": "empoleon_shadow", "rating": 725, "opRating": 274}, {"opponent": "bastiodon", "rating": 641}, {"opponent": "qwilfish_his<PERSON>an", "rating": 641, "opRating": 358}, {"opponent": "drapion_shadow", "rating": 632}, {"opponent": "sneasler", "rating": 551, "opRating": 448}], "counters": [{"opponent": "drifb<PERSON>", "rating": 148}, {"opponent": "gliscor", "rating": 288}, {"opponent": "spiritomb", "rating": 302}, {"opponent": "gastrodon", "rating": 303}, {"opponent": "dusknoir_shadow", "rating": 350}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 5237}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2756}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2568}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2201}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2185}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2095}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2076}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2044}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2029}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1936}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1919}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1885}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1875}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1853}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1842}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1566}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1416}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 14552}, {"moveId": "BODY_SLAM", "uses": 12040}, {"moveId": "WATER_PULSE", "uses": 10883}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "BODY_SLAM"], "score": 62.3}, {"speciesId": "gabite", "speciesName": "Gabite", "rating": 492, "matchups": [{"opponent": "magnezone_shadow", "rating": 791, "opRating": 208}, {"opponent": "qwilfish_his<PERSON>an", "rating": 761, "opRating": 238}, {"opponent": "bastiodon", "rating": 681}, {"opponent": "drapion_shadow", "rating": 643}, {"opponent": "empoleon_shadow", "rating": 515, "opRating": 484}], "counters": [{"opponent": "gallade_shadow", "rating": 120}, {"opponent": "gliscor", "rating": 297}, {"opponent": "gastrodon", "rating": 312}, {"opponent": "spiritomb", "rating": 331}, {"opponent": "dusknoir_shadow", "rating": 405}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32764}, {"moveId": "TAKE_DOWN", "uses": 4736}], "chargedMoves": [{"moveId": "DIG", "uses": 13494}, {"moveId": "FLAMETHROWER", "uses": 11103}, {"moveId": "TWISTER", "uses": 7386}, {"moveId": "RETURN", "uses": 5552}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 62}, {"speciesId": "buneary", "speciesName": "Buneary", "rating": 459, "matchups": [{"opponent": "abomasnow_shadow", "rating": 888, "opRating": 111}, {"opponent": "bibarel", "rating": 581, "opRating": 418}, {"opponent": "vespiquen", "rating": 574, "opRating": 425}, {"opponent": "hippo<PERSON><PERSON>", "rating": 570, "opRating": 429}, {"opponent": "qwilfish_his<PERSON>an", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dusknoir_shadow", "rating": 261}, {"opponent": "bastiodon", "rating": 269}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "gliscor", "rating": 297}, {"opponent": "drapion_shadow", "rating": 364}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 34416}, {"moveId": "POUND", "uses": 3084}], "chargedMoves": [{"moveId": "SWIFT", "uses": 20382}, {"moveId": "FIRE_PUNCH", "uses": 17118}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "FIRE_PUNCH"], "score": 61.8}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 414, "matchups": [{"opponent": "drifb<PERSON>", "rating": 757}, {"opponent": "drapion_shadow", "rating": 646}, {"opponent": "gliscor", "rating": 641}, {"opponent": "dusknoir_shadow", "rating": 520}, {"opponent": "qwilfish_his<PERSON>an", "rating": 510, "opRating": 489}], "counters": [{"opponent": "bastiodon", "rating": 125}, {"opponent": "gallade_shadow", "rating": 144}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "gastrodon", "rating": 425}, {"opponent": "spiritomb", "rating": 480}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 23620}, {"moveId": "FROST_BREATH", "uses": 13880}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 18077}, {"moveId": "ICY_WIND", "uses": 6278}, {"moveId": "WATER_PULSE", "uses": 5108}, {"moveId": "ICE_BEAM", "uses": 4099}, {"moveId": "LAST_RESORT", "uses": 4005}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 61.7}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 460, "matchups": [{"opponent": "drifb<PERSON>", "rating": 779, "opRating": 220}, {"opponent": "empoleon_shadow", "rating": 715, "opRating": 284}, {"opponent": "vespiquen", "rating": 639, "opRating": 360}, {"opponent": "qwilfish_his<PERSON>an", "rating": 559, "opRating": 440}, {"opponent": "drapion_shadow", "rating": 537}], "counters": [{"opponent": "spiritomb", "rating": 115}, {"opponent": "gliscor", "rating": 241}, {"opponent": "bastiodon", "rating": 413}, {"opponent": "gastrodon", "rating": 458}, {"opponent": "dusknoir_shadow", "rating": 466}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20417}, {"moveId": "ASTONISH", "uses": 17083}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16325}, {"moveId": "HYDRO_PUMP", "uses": 14048}, {"moveId": "THUNDER", "uses": 7103}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 61.5}, {"speciesId": "growl<PERSON>e_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 504, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 806, "opRating": 193}, {"opponent": "abomasnow_shadow", "rating": 764, "opRating": 235}, {"opponent": "gliscor_shadow", "rating": 739, "opRating": 260}, {"opponent": "drifb<PERSON>", "rating": 686, "opRating": 313}, {"opponent": "vespiquen", "rating": 521, "opRating": 478}], "counters": [{"opponent": "gastrodon", "rating": 89}, {"opponent": "drapion_shadow", "rating": 118}, {"opponent": "bastiodon", "rating": 244}, {"opponent": "gliscor", "rating": 452}, {"opponent": "dusknoir_shadow", "rating": 455}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 27987}, {"moveId": "BITE", "uses": 9513}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 13106}, {"moveId": "FLAMETHROWER", "uses": 12363}, {"moveId": "CRUNCH", "uses": 12008}]}, "moveset": ["EMBER", "ROCK_SLIDE", "FLAMETHROWER"], "score": 61.2}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 477, "matchups": [{"opponent": "gastrodon", "rating": 926}, {"opponent": "hippo<PERSON><PERSON>", "rating": 754, "opRating": 245}, {"opponent": "bibarel", "rating": 710, "opRating": 289}, {"opponent": "lickilicky", "rating": 539, "opRating": 460}, {"opponent": "electivire_shadow", "rating": 529, "opRating": 470}], "counters": [{"opponent": "bastiodon", "rating": 248}, {"opponent": "spiritomb", "rating": 298}, {"opponent": "drapion_shadow", "rating": 300}, {"opponent": "gliscor", "rating": 301}, {"opponent": "dusknoir_shadow", "rating": 350}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 15835}, {"moveId": "QUICK_ATTACK", "uses": 14206}, {"moveId": "RAZOR_LEAF", "uses": 7464}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26032}, {"moveId": "LAST_RESORT", "uses": 5619}, {"moveId": "ENERGY_BALL", "uses": 3636}, {"moveId": "SOLAR_BEAM", "uses": 2174}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 61.2}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 464, "matchups": [{"opponent": "bastiodon", "rating": 810}, {"opponent": "spiritomb", "rating": 798, "opRating": 201}, {"opponent": "drifb<PERSON>", "rating": 718, "opRating": 281}, {"opponent": "abomasnow_shadow", "rating": 701, "opRating": 298}, {"opponent": "lickilicky", "rating": 689, "opRating": 310}], "counters": [{"opponent": "gastrodon", "rating": 107}, {"opponent": "drapion_shadow", "rating": 165}, {"opponent": "gliscor", "rating": 172}, {"opponent": "gallade_shadow", "rating": 216}, {"opponent": "dusknoir_shadow", "rating": 222}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31871}, {"moveId": "ROCK_SMASH", "uses": 5629}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15818}, {"moveId": "CLOSE_COMBAT", "uses": 14829}, {"moveId": "FLAMETHROWER", "uses": 3636}, {"moveId": "SOLAR_BEAM", "uses": 3159}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 60.6}, {"speciesId": "prinplup_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 414, "matchups": [{"opponent": "lickilicky", "rating": 731, "opRating": 268}, {"opponent": "gliscor", "rating": 647}, {"opponent": "empoleon_shadow", "rating": 610, "opRating": 389}, {"opponent": "gliscor_shadow", "rating": 606, "opRating": 393}, {"opponent": "bastiodon", "rating": 525}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "dusknoir_shadow", "rating": 194}, {"opponent": "gastrodon", "rating": 348}, {"opponent": "spiritomb", "rating": 350}, {"opponent": "drapion_shadow", "rating": 411}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 22577}, {"moveId": "METAL_CLAW", "uses": 14923}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 20223}, {"moveId": "HYDRO_PUMP", "uses": 9945}, {"moveId": "BUBBLE_BEAM", "uses": 7245}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUBBLE", "ICY_WIND", "HYDRO_PUMP"], "score": 60.6}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 463, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 687, "opRating": 312}, {"opponent": "gliscor", "rating": 677}, {"opponent": "gliscor_shadow", "rating": 642, "opRating": 357}, {"opponent": "magnezone_shadow", "rating": 562, "opRating": 437}, {"opponent": "electivire_shadow", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "spiritomb", "rating": 283}, {"opponent": "bastiodon", "rating": 287}, {"opponent": "gastrodon", "rating": 318}, {"opponent": "drapion_shadow", "rating": 419}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 28502}, {"moveId": "ROCK_SMASH", "uses": 8998}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 6587}, {"moveId": "SWIFT", "uses": 6171}, {"moveId": "FIRE_PUNCH", "uses": 5407}, {"moveId": "TRAILBLAZE", "uses": 5314}, {"moveId": "HIGH_HORSEPOWER", "uses": 5261}, {"moveId": "THUNDER_PUNCH", "uses": 4600}, {"moveId": "AERIAL_ACE", "uses": 4169}]}, "moveset": ["TACKLE", "ICE_PUNCH", "SWIFT"], "score": 60.6}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 468, "matchups": [{"opponent": "dusknoir_shadow", "rating": 784}, {"opponent": "gastrodon", "rating": 750}, {"opponent": "hippo<PERSON><PERSON>", "rating": 719, "opRating": 280}, {"opponent": "bibarel", "rating": 615, "opRating": 384}, {"opponent": "lickilicky", "rating": 530, "opRating": 469}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "bastiodon", "rating": 262}, {"opponent": "gliscor", "rating": 284}, {"opponent": "drapion_shadow", "rating": 317}, {"opponent": "spiritomb", "rating": 432}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 26297}, {"moveId": "BITE", "uses": 11203}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 16296}, {"moveId": "CRUNCH", "uses": 15298}, {"moveId": "ENERGY_BALL", "uses": 5771}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 60.4}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 459, "matchups": [{"opponent": "electivire_shadow", "rating": 854, "opRating": 145}, {"opponent": "drifb<PERSON>", "rating": 779, "opRating": 220}, {"opponent": "drapion_shadow", "rating": 639}, {"opponent": "bibarel", "rating": 564, "opRating": 435}, {"opponent": "qwilfish_his<PERSON>an", "rating": 559, "opRating": 440}], "counters": [{"opponent": "spiritomb", "rating": 115}, {"opponent": "bastiodon", "rating": 205}, {"opponent": "gliscor", "rating": 241}, {"opponent": "dusknoir_shadow", "rating": 466}, {"opponent": "gastrodon", "rating": 488}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20999}, {"moveId": "ASTONISH", "uses": 16501}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 15867}, {"moveId": "THUNDERBOLT", "uses": 15131}, {"moveId": "THUNDER", "uses": 6519}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 60.3}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 498, "matchups": [{"opponent": "abomasnow", "rating": 855, "opRating": 144}, {"opponent": "abomasnow_shadow", "rating": 835, "opRating": 164}, {"opponent": "spiritomb", "rating": 747, "opRating": 252}, {"opponent": "gastrodon", "rating": 623}, {"opponent": "bibarel", "rating": 546, "opRating": 453}], "counters": [{"opponent": "gliscor", "rating": 60}, {"opponent": "gallade_shadow", "rating": 120}, {"opponent": "bastiodon", "rating": 187}, {"opponent": "dusknoir_shadow", "rating": 261}, {"opponent": "drapion_shadow", "rating": 292}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 9340}, {"moveId": "BULLET_SEED", "uses": 8215}, {"moveId": "POISON_JAB", "uses": 8080}, {"moveId": "MAGICAL_LEAF", "uses": 7864}, {"moveId": "RAZOR_LEAF", "uses": 3957}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 12534}, {"moveId": "GRASS_KNOT", "uses": 8342}, {"moveId": "LEAF_STORM", "uses": 5164}, {"moveId": "SLUDGE_BOMB", "uses": 4900}, {"moveId": "DAZZLING_GLEAM", "uses": 4777}, {"moveId": "SOLAR_BEAM", "uses": 1757}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 59.8}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 450, "matchups": [{"opponent": "gallade_shadow", "rating": 764, "opRating": 236}, {"opponent": "toxicroak", "rating": 704, "opRating": 296}, {"opponent": "drapion_shadow", "rating": 628}, {"opponent": "gastrodon", "rating": 604}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 540, "opRating": 460}], "counters": [{"opponent": "dusknoir_shadow", "rating": 144}, {"opponent": "bastiodon", "rating": 194}, {"opponent": "drifb<PERSON>", "rating": 294}, {"opponent": "gliscor", "rating": 357}, {"opponent": "spiritomb", "rating": 423}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 19394}, {"moveId": "WING_ATTACK", "uses": 18106}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 17028}, {"moveId": "ANCIENT_POWER", "uses": 10565}, {"moveId": "BUG_BUZZ", "uses": 9928}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "ANCIENT_POWER"], "score": 59.8}, {"speciesId": "gabite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 461, "matchups": [{"opponent": "electrode_hisuian", "rating": 757, "opRating": 242}, {"opponent": "magnezone_shadow", "rating": 746, "opRating": 253}, {"opponent": "qwilfish_his<PERSON>an", "rating": 727, "opRating": 272}, {"opponent": "drapion_shadow", "rating": 681}, {"opponent": "bastiodon", "rating": 662}], "counters": [{"opponent": "dusknoir_shadow", "rating": 133}, {"opponent": "gallade_shadow", "rating": 144}, {"opponent": "gliscor", "rating": 340}, {"opponent": "gastrodon", "rating": 377}, {"opponent": "spiritomb", "rating": 432}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32588}, {"moveId": "TAKE_DOWN", "uses": 4912}], "chargedMoves": [{"moveId": "DIG", "uses": 15416}, {"moveId": "FLAMETHROWER", "uses": 13169}, {"moveId": "TWISTER", "uses": 8847}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 59.5}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 475, "matchups": [{"opponent": "gastrodon", "rating": 735}, {"opponent": "hippo<PERSON><PERSON>", "rating": 716, "opRating": 283}, {"opponent": "lickilicky", "rating": 636, "opRating": 363}, {"opponent": "bibarel", "rating": 610, "opRating": 389}, {"opponent": "dusknoir_shadow", "rating": 536}], "counters": [{"opponent": "gallade_shadow", "rating": 144}, {"opponent": "bastiodon", "rating": 258}, {"opponent": "gliscor", "rating": 288}, {"opponent": "drapion_shadow", "rating": 355}, {"opponent": "spiritomb", "rating": 466}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 23041}, {"moveId": "INFESTATION", "uses": 14459}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 14291}, {"moveId": "ROCK_SLIDE", "uses": 9739}, {"moveId": "SLUDGE_BOMB", "uses": 5867}, {"moveId": "ANCIENT_POWER", "uses": 4623}, {"moveId": "SOLAR_BEAM", "uses": 2945}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 59.4}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 450, "matchups": [{"opponent": "dusknoir_shadow", "rating": 782, "opRating": 217}, {"opponent": "bibarel", "rating": 564, "opRating": 435}, {"opponent": "drifb<PERSON>", "rating": 536, "opRating": 463}, {"opponent": "vespiquen", "rating": 536, "opRating": 463}, {"opponent": "hippo<PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "gastrodon", "rating": 196}, {"opponent": "bastiodon", "rating": 241}, {"opponent": "drapion_shadow", "rating": 305}, {"opponent": "gliscor", "rating": 318}, {"opponent": "spiritomb", "rating": 350}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 25346}, {"moveId": "SCRATCH", "uses": 12154}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 16953}, {"moveId": "LOW_SWEEP", "uses": 8615}, {"moveId": "RETURN", "uses": 8557}, {"moveId": "HYPER_BEAM", "uses": 3302}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 59.2}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 424, "matchups": [{"opponent": "gastrodon", "rating": 761}, {"opponent": "gallade_shadow", "rating": 758, "opRating": 241}, {"opponent": "lickilicky", "rating": 668, "opRating": 331}, {"opponent": "gliscor", "rating": 615}, {"opponent": "sneasler", "rating": 552, "opRating": 447}], "counters": [{"opponent": "bastiodon", "rating": 89}, {"opponent": "drapion_shadow", "rating": 101}, {"opponent": "dusknoir_shadow", "rating": 155}, {"opponent": "drifb<PERSON>", "rating": 397}, {"opponent": "spiritomb", "rating": 432}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 31121}, {"moveId": "ZEN_HEADBUTT", "uses": 6379}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 13361}, {"moveId": "FLY", "uses": 11368}, {"moveId": "DAZZLING_GLEAM", "uses": 4820}, {"moveId": "PSYCHIC", "uses": 4428}, {"moveId": "OMINOUS_WIND", "uses": 3513}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "FLY"], "score": 58.9}, {"speciesId": "rotom", "speciesName": "Rotom", "rating": 456, "matchups": [{"opponent": "drifb<PERSON>", "rating": 603, "opRating": 396}, {"opponent": "bibarel", "rating": 599, "opRating": 400}, {"opponent": "vespiquen", "rating": 594, "opRating": 405}, {"opponent": "gallade_shadow", "rating": 575, "opRating": 424}, {"opponent": "sneasler", "rating": 561, "opRating": 438}], "counters": [{"opponent": "drapion_shadow", "rating": 88}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "gastrodon", "rating": 312}, {"opponent": "bastiodon", "rating": 330}, {"opponent": "gliscor", "rating": 336}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 19600}, {"moveId": "THUNDER_SHOCK", "uses": 17900}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16537}, {"moveId": "OMINOUS_WIND", "uses": 13811}, {"moveId": "THUNDER", "uses": 7153}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 58.9}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 464, "matchups": [{"opponent": "bibarel", "rating": 931, "opRating": 68}, {"opponent": "empoleon_shadow", "rating": 846, "opRating": 153}, {"opponent": "gastrodon", "rating": 730}, {"opponent": "bastiodon", "rating": 547}, {"opponent": "dusknoir_shadow", "rating": 529}], "counters": [{"opponent": "gliscor", "rating": 51}, {"opponent": "gallade_shadow", "rating": 86}, {"opponent": "drifb<PERSON>", "rating": 287}, {"opponent": "drapion_shadow", "rating": 347}, {"opponent": "spiritomb", "rating": 471}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 20040}, {"moveId": "MAGICAL_LEAF", "uses": 17460}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 15020}, {"moveId": "UPPER_HAND", "uses": 13597}, {"moveId": "PETAL_BLIZZARD", "uses": 6273}, {"moveId": "SOLAR_BEAM", "uses": 2558}]}, "moveset": ["BULLET_SEED", "PETAL_BLIZZARD", "UPPER_HAND"], "score": 58.6}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 449, "matchups": [{"opponent": "sneasler", "rating": 743, "opRating": 256}, {"opponent": "drifb<PERSON>", "rating": 705, "opRating": 294}, {"opponent": "bibarel", "rating": 657, "opRating": 342}, {"opponent": "toxicroak", "rating": 568, "opRating": 431}, {"opponent": "dusknoir_shadow", "rating": 544}], "counters": [{"opponent": "drapion_shadow", "rating": 76}, {"opponent": "bastiodon", "rating": 223}, {"opponent": "gastrodon", "rating": 348}, {"opponent": "gliscor", "rating": 353}, {"opponent": "spiritomb", "rating": 365}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19615}, {"moveId": "TACKLE", "uses": 14751}, {"moveId": "ZEN_HEADBUTT", "uses": 3117}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15391}, {"moveId": "STOMP", "uses": 8393}, {"moveId": "MEGAHORN", "uses": 7594}, {"moveId": "PSYCHIC", "uses": 6119}]}, "moveset": ["CONFUSION", "WILD_CHARGE", "STOMP"], "score": 58.6}, {"speciesId": "garcho<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 423, "matchups": [{"opponent": "magnezone_shadow", "rating": 730, "opRating": 269}, {"opponent": "lickilicky", "rating": 710, "opRating": 289}, {"opponent": "drapion_shadow", "rating": 650}, {"opponent": "bibarel", "rating": 519, "opRating": 480}, {"opponent": "bastiodon", "rating": 503}], "counters": [{"opponent": "gallade_shadow", "rating": 245}, {"opponent": "dusknoir_shadow", "rating": 266}, {"opponent": "gliscor", "rating": 297}, {"opponent": "spiritomb", "rating": 365}, {"opponent": "gastrodon", "rating": 375}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 18942}, {"moveId": "MUD_SHOT", "uses": 18558}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 11739}, {"moveId": "EARTH_POWER", "uses": 11159}, {"moveId": "FIRE_BLAST", "uses": 5839}, {"moveId": "SAND_TOMB", "uses": 4677}, {"moveId": "EARTHQUAKE", "uses": 4062}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 58.3}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 479, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "bibarel", "rating": 645, "opRating": 354}, {"opponent": "lickilicky", "rating": 612, "opRating": 387}, {"opponent": "bastiodon", "rating": 532}, {"opponent": "gastrodon", "rating": 520}], "counters": [{"opponent": "gliscor", "rating": 185}, {"opponent": "spiritomb", "rating": 187}, {"opponent": "drifb<PERSON>", "rating": 234}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "drapion_shadow", "rating": 453}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 19310}, {"moveId": "MAGICAL_LEAF", "uses": 18190}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 10530}, {"moveId": "AURA_SPHERE", "uses": 8723}, {"moveId": "AERIAL_ACE", "uses": 7953}, {"moveId": "NIGHT_SHADE", "uses": 7209}, {"moveId": "ENERGY_BALL", "uses": 3052}]}, "moveset": ["PSYCHO_CUT", "AERIAL_ACE", "AURA_SPHERE"], "score": 57.8}, {"speciesId": "<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 433, "matchups": [{"opponent": "gastrodon", "rating": 750}, {"opponent": "gallade_shadow", "rating": 742, "opRating": 257}, {"opponent": "toxicroak", "rating": 681, "opRating": 318}, {"opponent": "munchlax", "rating": 594, "opRating": 405}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 536, "opRating": 463}], "counters": [{"opponent": "dusknoir_shadow", "rating": 144}, {"opponent": "bastiodon", "rating": 172}, {"opponent": "spiritomb", "rating": 245}, {"opponent": "gliscor", "rating": 362}, {"opponent": "drapion_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 20763}, {"moveId": "AIR_SLASH", "uses": 16737}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 20878}, {"moveId": "BUG_BUZZ", "uses": 12207}, {"moveId": "PSYBEAM", "uses": 4454}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "BUG_BUZZ"], "score": 57.2}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 429, "matchups": [{"opponent": "magnezone_shadow", "rating": 777, "opRating": 222}, {"opponent": "lickilicky", "rating": 765, "opRating": 234}, {"opponent": "bibarel", "rating": 623, "opRating": 376}, {"opponent": "qwilfish_his<PERSON>an", "rating": 551, "opRating": 448}, {"opponent": "vespiquen", "rating": 543, "opRating": 456}], "counters": [{"opponent": "gliscor", "rating": 245}, {"opponent": "gastrodon", "rating": 306}, {"opponent": "dusknoir_shadow", "rating": 377}, {"opponent": "bastiodon", "rating": 449}, {"opponent": "drapion_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 19293}, {"moveId": "MUD_SHOT", "uses": 18207}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 11741}, {"moveId": "EARTH_POWER", "uses": 11154}, {"moveId": "FIRE_BLAST", "uses": 5824}, {"moveId": "SAND_TOMB", "uses": 4717}, {"moveId": "EARTHQUAKE", "uses": 4047}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 57}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 423, "matchups": [{"opponent": "gastrodon", "rating": 696}, {"opponent": "hippo<PERSON><PERSON>", "rating": 650, "opRating": 350}, {"opponent": "bibarel", "rating": 630, "opRating": 369}, {"opponent": "magnezone_shadow", "rating": 600, "opRating": 400}, {"opponent": "lickilicky", "rating": 569, "opRating": 430}], "counters": [{"opponent": "gliscor", "rating": 219}, {"opponent": "spiritomb", "rating": 240}, {"opponent": "drapion_shadow", "rating": 241}, {"opponent": "dusknoir_shadow", "rating": 283}, {"opponent": "bastiodon", "rating": 345}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 25191}, {"moveId": "RAZOR_LEAF", "uses": 12309}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 19127}, {"moveId": "DAZZLING_GLEAM", "uses": 7854}, {"moveId": "SOLAR_BEAM", "uses": 7097}, {"moveId": "HYPER_BEAM", "uses": 3397}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 56.9}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 413, "matchups": [{"opponent": "gastrodon", "rating": 797}, {"opponent": "hippo<PERSON><PERSON>", "rating": 673, "opRating": 326}, {"opponent": "bibarel", "rating": 656, "opRating": 343}, {"opponent": "lickilicky", "rating": 585, "opRating": 414}, {"opponent": "electivire_shadow", "rating": 581, "opRating": 418}], "counters": [{"opponent": "gliscor", "rating": 232}, {"opponent": "dusknoir_shadow", "rating": 300}, {"opponent": "spiritomb", "rating": 312}, {"opponent": "drapion_shadow", "rating": 334}, {"opponent": "bastiodon", "rating": 352}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20062}, {"moveId": "BITE", "uses": 17438}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 14995}, {"moveId": "ENERGY_BALL", "uses": 14686}, {"moveId": "SOLAR_BEAM", "uses": 4314}, {"moveId": "RETURN", "uses": 3536}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 56.7}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 435, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 704, "opRating": 295}, {"opponent": "empoleon_shadow", "rating": 666, "opRating": 333}, {"opponent": "bibarel", "rating": 629, "opRating": 370}, {"opponent": "drifb<PERSON>", "rating": 612, "opRating": 387}, {"opponent": "magnezone_shadow", "rating": 580, "opRating": 419}], "counters": [{"opponent": "bastiodon", "rating": 219}, {"opponent": "gliscor", "rating": 241}, {"opponent": "gastrodon", "rating": 244}, {"opponent": "dusknoir_shadow", "rating": 466}, {"opponent": "drapion_shadow", "rating": 474}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 19484}, {"moveId": "ASTONISH", "uses": 18016}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18209}, {"moveId": "OMINOUS_WIND", "uses": 11363}, {"moveId": "THUNDER", "uses": 7907}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 55.7}, {"speciesId": "s<PERSON><PERSON><PERSON>", "speciesName": "Skorupi", "rating": 408, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 814, "opRating": 185}, {"opponent": "electrode_hisuian", "rating": 752, "opRating": 247}, {"opponent": "abomasnow_shadow", "rating": 663, "opRating": 336}, {"opponent": "gallade", "rating": 628, "opRating": 371}, {"opponent": "gallade_shadow", "rating": 526, "opRating": 473}], "counters": [{"opponent": "bastiodon", "rating": 187}, {"opponent": "gastrodon", "rating": 250}, {"opponent": "drapion_shadow", "rating": 322}, {"opponent": "dusknoir_shadow", "rating": 355}, {"opponent": "gliscor", "rating": 396}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21413}, {"moveId": "INFESTATION", "uses": 16087}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17849}, {"moveId": "CROSS_POISON", "uses": 12538}, {"moveId": "RETURN", "uses": 3666}, {"moveId": "SLUDGE_BOMB", "uses": 3371}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 54.9}, {"speciesId": "skorup<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 411, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 787, "opRating": 212}, {"opponent": "gallade_shadow", "rating": 672, "opRating": 327}, {"opponent": "kleavor", "rating": 672, "opRating": 327}, {"opponent": "abomasnow", "rating": 646, "opRating": 353}, {"opponent": "abomasnow_shadow", "rating": 588, "opRating": 411}], "counters": [{"opponent": "bastiodon", "rating": 223}, {"opponent": "dusknoir_shadow", "rating": 227}, {"opponent": "gliscor", "rating": 258}, {"opponent": "gastrodon", "rating": 297}, {"opponent": "drapion_shadow", "rating": 364}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21218}, {"moveId": "INFESTATION", "uses": 16282}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 19758}, {"moveId": "CROSS_POISON", "uses": 14011}, {"moveId": "SLUDGE_BOMB", "uses": 3730}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 54.7}, {"speciesId": "snover_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 424, "matchups": [{"opponent": "gastrodon", "rating": 732}, {"opponent": "drifb<PERSON>", "rating": 690, "opRating": 309}, {"opponent": "electivire_shadow", "rating": 633, "opRating": 366}, {"opponent": "hippo<PERSON><PERSON>", "rating": 619, "opRating": 380}, {"opponent": "abomasnow_shadow", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dusknoir_shadow", "rating": 133}, {"opponent": "drapion_shadow", "rating": 148}, {"opponent": "bastiodon", "rating": 208}, {"opponent": "gliscor", "rating": 215}, {"opponent": "spiritomb", "rating": 437}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 14730}, {"moveId": "LEAFAGE", "uses": 11641}, {"moveId": "ICE_SHARD", "uses": 11123}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 16337}, {"moveId": "ENERGY_BALL", "uses": 12618}, {"moveId": "STOMP", "uses": 8550}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 53.7}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 430, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 806, "opRating": 193}, {"opponent": "abomasnow_shadow", "rating": 755, "opRating": 244}, {"opponent": "spiritomb", "rating": 751, "opRating": 248}, {"opponent": "bronzong", "rating": 627, "opRating": 372}, {"opponent": "abomasnow", "rating": 593, "opRating": 406}], "counters": [{"opponent": "gastrodon", "rating": 89}, {"opponent": "drapion_shadow", "rating": 135}, {"opponent": "dusknoir_shadow", "rating": 266}, {"opponent": "bastiodon", "rating": 269}, {"opponent": "gliscor", "rating": 409}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 17596}, {"moveId": "SNARL", "uses": 15475}, {"moveId": "ROCK_SMASH", "uses": 4405}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12378}, {"moveId": "FLAMETHROWER", "uses": 8623}, {"moveId": "ROCK_SLIDE", "uses": 8450}, {"moveId": "CRUNCH", "uses": 8002}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "ROCK_SLIDE"], "score": 53}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 428, "matchups": [{"opponent": "toxicroak", "rating": 693, "opRating": 306}, {"opponent": "gallade_shadow", "rating": 681, "opRating": 318}, {"opponent": "sneasler", "rating": 650, "opRating": 349}, {"opponent": "abomasnow", "rating": 625, "opRating": 375}, {"opponent": "lickilicky", "rating": 594, "opRating": 405}], "counters": [{"opponent": "drapion_shadow", "rating": 76}, {"opponent": "bastiodon", "rating": 212}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "gastrodon", "rating": 306}, {"opponent": "gliscor", "rating": 323}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 21017}, {"moveId": "EXTRASENSORY", "uses": 16483}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16425}, {"moveId": "FUTURE_SIGHT", "uses": 10654}, {"moveId": "BLIZZARD", "uses": 10404}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 52.9}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 376, "matchups": [{"opponent": "gastrodon", "rating": 887}, {"opponent": "hippo<PERSON><PERSON>", "rating": 770, "opRating": 229}, {"opponent": "bibarel", "rating": 687, "opRating": 312}, {"opponent": "lickilicky", "rating": 639, "opRating": 360}, {"opponent": "magnezone_shadow", "rating": 639, "opRating": 360}], "counters": [{"opponent": "drapion_shadow", "rating": 211}, {"opponent": "spiritomb", "rating": 230}, {"opponent": "gliscor", "rating": 232}, {"opponent": "dusknoir_shadow", "rating": 300}, {"opponent": "bastiodon", "rating": 438}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20309}, {"moveId": "BITE", "uses": 17191}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 16467}, {"moveId": "STONE_EDGE", "uses": 8519}, {"moveId": "EARTHQUAKE", "uses": 6792}, {"moveId": "SAND_TOMB", "uses": 3930}, {"moveId": "SOLAR_BEAM", "uses": 1844}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 52.3}, {"speciesId": "luxio_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 430, "matchups": [{"opponent": "spiritomb", "rating": 723, "opRating": 276}, {"opponent": "qwilfish_his<PERSON>an", "rating": 651, "opRating": 348}, {"opponent": "drifb<PERSON>", "rating": 647, "opRating": 352}, {"opponent": "bibarel", "rating": 514, "opRating": 485}, {"opponent": "vespiquen", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gastrodon", "rating": 71}, {"opponent": "dusknoir_shadow", "rating": 166}, {"opponent": "drapion_shadow", "rating": 177}, {"opponent": "bastiodon", "rating": 291}, {"opponent": "gliscor", "rating": 340}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 26313}, {"moveId": "BITE", "uses": 11187}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 21836}, {"moveId": "CRUNCH", "uses": 11642}, {"moveId": "THUNDERBOLT", "uses": 4019}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 52.1}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 372, "matchups": [{"opponent": "electivire_shadow", "rating": 850, "opRating": 149}, {"opponent": "drifb<PERSON>", "rating": 709, "opRating": 290}, {"opponent": "spiritomb", "rating": 692, "opRating": 307}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 636, "opRating": 363}, {"opponent": "qwilfish_his<PERSON>an", "rating": 606, "opRating": 393}], "counters": [{"opponent": "dusknoir_shadow", "rating": 88}, {"opponent": "drapion_shadow", "rating": 93}, {"opponent": "bastiodon", "rating": 287}, {"opponent": "gliscor", "rating": 297}, {"opponent": "gastrodon", "rating": 324}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 5611}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2538}, {"moveId": "CHARGE_BEAM", "uses": 2201}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2170}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2053}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2040}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1947}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1924}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1885}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1878}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1866}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1848}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1757}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1754}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1722}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1696}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1494}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1259}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 10645}, {"moveId": "BLIZZARD", "uses": 8689}, {"moveId": "ZAP_CANNON", "uses": 7077}, {"moveId": "SOLAR_BEAM", "uses": 5896}, {"moveId": "HYPER_BEAM", "uses": 5284}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 51.6}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 382, "matchups": [{"opponent": "gastrodon", "rating": 696}, {"opponent": "hippo<PERSON><PERSON>", "rating": 650, "opRating": 350}, {"opponent": "bibarel", "rating": 630, "opRating": 369}, {"opponent": "electivire_shadow", "rating": 561, "opRating": 438}, {"opponent": "dusknoir", "rating": 534, "opRating": 465}], "counters": [{"opponent": "bastiodon", "rating": 287}, {"opponent": "gliscor", "rating": 301}, {"opponent": "drapion_shadow", "rating": 330}, {"opponent": "dusknoir_shadow", "rating": 388}, {"opponent": "spiritomb", "rating": 471}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 22987}, {"moveId": "RAZOR_LEAF", "uses": 14513}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 16924}, {"moveId": "SOLAR_BEAM", "uses": 13037}, {"moveId": "HYPER_BEAM", "uses": 7582}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 51.5}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 407, "matchups": [{"opponent": "rhyperior_shadow", "rating": 893, "opRating": 106}, {"opponent": "rhyperior", "rating": 886, "opRating": 113}, {"opponent": "gastrodon", "rating": 762}, {"opponent": "hippo<PERSON><PERSON>", "rating": 702, "opRating": 297}, {"opponent": "bibarel", "rating": 585, "opRating": 414}], "counters": [{"opponent": "bastiodon", "rating": 226}, {"opponent": "drapion_shadow", "rating": 228}, {"opponent": "gliscor", "rating": 241}, {"opponent": "spiritomb", "rating": 269}, {"opponent": "dusknoir_shadow", "rating": 311}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20539}, {"moveId": "BITE", "uses": 16961}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16897}, {"moveId": "ENERGY_BALL", "uses": 15900}, {"moveId": "SOLAR_BEAM", "uses": 4682}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 51.2}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 378, "matchups": [{"opponent": "gastrodon", "rating": 751}, {"opponent": "hippo<PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "bibarel", "rating": 646, "opRating": 353}, {"opponent": "magnezone_shadow", "rating": 578, "opRating": 421}, {"opponent": "lickilicky", "rating": 571, "opRating": 428}], "counters": [{"opponent": "gliscor", "rating": 211}, {"opponent": "drapion_shadow", "rating": 228}, {"opponent": "spiritomb", "rating": 269}, {"opponent": "dusknoir_shadow", "rating": 311}, {"opponent": "bastiodon", "rating": 453}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20885}, {"moveId": "BITE", "uses": 16615}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 16490}, {"moveId": "STONE_EDGE", "uses": 8510}, {"moveId": "EARTHQUAKE", "uses": 6800}, {"moveId": "SAND_TOMB", "uses": 3917}, {"moveId": "SOLAR_BEAM", "uses": 1838}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 50.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 423, "matchups": [{"opponent": "staravia_shadow", "rating": 803, "opRating": 196}, {"opponent": "snea<PERSON>_shadow", "rating": 775, "opRating": 224}, {"opponent": "staraptor_shadow", "rating": 775, "opRating": 224}, {"opponent": "toxicroak", "rating": 574, "opRating": 425}, {"opponent": "gallade_shadow", "rating": 518, "opRating": 481}], "counters": [{"opponent": "drapion_shadow", "rating": 93}, {"opponent": "bastiodon", "rating": 122}, {"opponent": "dusknoir_shadow", "rating": 300}, {"opponent": "gliscor", "rating": 344}, {"opponent": "gastrodon", "rating": 383}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 20929}, {"moveId": "EXTRASENSORY", "uses": 16571}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17821}, {"moveId": "FUTURE_SIGHT", "uses": 11561}, {"moveId": "FIRE_BLAST", "uses": 8158}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 50.1}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 402, "matchups": [{"opponent": "sneasler", "rating": 914, "opRating": 85}, {"opponent": "electivire_shadow", "rating": 829, "opRating": 170}, {"opponent": "drifb<PERSON>", "rating": 641, "opRating": 358}, {"opponent": "spiritomb", "rating": 623, "opRating": 376}, {"opponent": "qwilfish_his<PERSON>an", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dusknoir_shadow", "rating": 88}, {"opponent": "drapion_shadow", "rating": 93}, {"opponent": "gliscor", "rating": 327}, {"opponent": "bastiodon", "rating": 327}, {"opponent": "gastrodon", "rating": 348}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 5755}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2538}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2180}, {"moveId": "CHARGE_BEAM", "uses": 2166}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2048}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2024}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1944}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1941}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1880}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1851}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1846}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1840}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1786}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1713}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1686}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1664}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1470}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1263}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 10640}, {"moveId": "BLIZZARD", "uses": 8734}, {"moveId": "ZAP_CANNON", "uses": 7025}, {"moveId": "SOLAR_BEAM", "uses": 5878}, {"moveId": "HYPER_BEAM", "uses": 5271}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 50.1}, {"speciesId": "pip<PERSON>p", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 360, "matchups": [{"opponent": "infernape_shadow", "rating": 862, "opRating": 137}, {"opponent": "gliscor_shadow", "rating": 816, "opRating": 183}, {"opponent": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 748, "opRating": 251}, {"opponent": "monferno", "rating": 667, "opRating": 332}, {"opponent": "hippopotas", "rating": 564, "opRating": 435}], "counters": [{"opponent": "gastrodon", "rating": 235}, {"opponent": "bastiodon", "rating": 276}, {"opponent": "dusknoir_shadow", "rating": 288}, {"opponent": "drapion_shadow", "rating": 322}, {"opponent": "gliscor", "rating": 491}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 35284}, {"moveId": "POUND", "uses": 2216}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 14004}, {"moveId": "ICY_WIND", "uses": 13627}, {"moveId": "BUBBLE_BEAM", "uses": 5834}, {"moveId": "RETURN", "uses": 4019}]}, "moveset": ["BUBBLE", "DRILL_PECK", "ICY_WIND"], "score": 49.3}, {"speciesId": "stunky", "speciesName": "Stunky", "rating": 374, "matchups": [{"opponent": "typhlosion_hisuian", "rating": 877, "opRating": 122}, {"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 721, "opRating": 278}, {"opponent": "drifb<PERSON>", "rating": 656, "opRating": 343}, {"opponent": "bronzong", "rating": 619, "opRating": 380}, {"opponent": "dusknoir", "rating": 537, "opRating": 462}], "counters": [{"opponent": "drapion_shadow", "rating": 152}, {"opponent": "gastrodon", "rating": 226}, {"opponent": "gliscor", "rating": 232}, {"opponent": "bastiodon", "rating": 309}, {"opponent": "dusknoir_shadow", "rating": 422}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 23699}, {"moveId": "SCRATCH", "uses": 13801}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 11975}, {"moveId": "TRAILBLAZE", "uses": 9567}, {"moveId": "FLAMETHROWER", "uses": 6693}, {"moveId": "SLUDGE_BOMB", "uses": 5946}, {"moveId": "RETURN", "uses": 3313}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 48.7}, {"speciesId": "bonsly", "speciesName": "Bon<PERSON><PERSON>", "rating": 403, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 669, "opRating": 330}, {"opponent": "vespiquen", "rating": 598, "opRating": 401}, {"opponent": "froslass", "rating": 582, "opRating": 417}, {"opponent": "drifb<PERSON>", "rating": 562, "opRating": 437}, {"opponent": "bibarel", "rating": 515, "opRating": 484}], "counters": [{"opponent": "gliscor", "rating": 232}, {"opponent": "gastrodon", "rating": 241}, {"opponent": "drapion_shadow", "rating": 266}, {"opponent": "dusknoir_shadow", "rating": 266}, {"opponent": "bastiodon", "rating": 280}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 19110}, {"moveId": "COUNTER", "uses": 18390}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 17730}, {"moveId": "ROCK_SLIDE", "uses": 11402}, {"moveId": "EARTHQUAKE", "uses": 8449}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 48.4}, {"speciesId": "piplup_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 359, "matchups": [{"opponent": "infernape", "rating": 862, "opRating": 137}, {"opponent": "infernape_shadow", "rating": 832, "opRating": 167}, {"opponent": "gliscor", "rating": 816}, {"opponent": "mamos<PERSON>_shadow", "rating": 641, "opRating": 358}, {"opponent": "magmortar_shadow", "rating": 606, "opRating": 393}], "counters": [{"opponent": "dusknoir_shadow", "rating": 133}, {"opponent": "drapion_shadow", "rating": 135}, {"opponent": "bastiodon", "rating": 251}, {"opponent": "gastrodon", "rating": 276}, {"opponent": "spiritomb", "rating": 322}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 35405}, {"moveId": "POUND", "uses": 2095}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 15855}, {"moveId": "ICY_WIND", "uses": 15138}, {"moveId": "BUBBLE_BEAM", "uses": 6481}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUBBLE", "DRILL_PECK", "ICY_WIND"], "score": 48.4}, {"speciesId": "chatot", "speciesName": "Chatot", "rating": 345, "matchups": [{"opponent": "giratina_origin", "rating": 768, "opRating": 231}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 676, "opRating": 323}, {"opponent": "dusknoir", "rating": 591, "opRating": 408}, {"opponent": "gliscor_shadow", "rating": 537, "opRating": 462}, {"opponent": "dusknoir_shadow", "rating": 513}], "counters": [{"opponent": "spiritomb", "rating": 192}, {"opponent": "bastiodon", "rating": 201}, {"opponent": "drapion_shadow", "rating": 351}, {"opponent": "gastrodon", "rating": 351}, {"opponent": "gliscor", "rating": 495}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 20876}, {"moveId": "PECK", "uses": 16624}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 16651}, {"moveId": "NIGHT_SHADE", "uses": 16143}, {"moveId": "HEAT_WAVE", "uses": 4698}]}, "moveset": ["STEEL_WING", "SKY_ATTACK", "NIGHT_SHADE"], "score": 47.9}, {"speciesId": "turtwig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 356, "matchups": [{"opponent": "gastrodon", "rating": 737}, {"opponent": "hippow<PERSON>_shadow", "rating": 703, "opRating": 296}, {"opponent": "bibarel", "rating": 592, "opRating": 407}, {"opponent": "electivire", "rating": 592, "opRating": 407}, {"opponent": "electivire_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dusknoir_shadow", "rating": 138}, {"opponent": "bastiodon", "rating": 183}, {"opponent": "gliscor", "rating": 241}, {"opponent": "spiritomb", "rating": 264}, {"opponent": "drapion_shadow", "rating": 292}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 19116}, {"moveId": "TACKLE", "uses": 18384}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 14139}, {"moveId": "BODY_SLAM", "uses": 13598}, {"moveId": "ENERGY_BALL", "uses": 6327}, {"moveId": "RETURN", "uses": 3450}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 46.1}, {"speciesId": "stunky_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 359, "matchups": [{"opponent": "mismagius_shadow", "rating": 863, "opRating": 136}, {"opponent": "cress<PERSON>a_shadow", "rating": 697, "opRating": 302}, {"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 693, "opRating": 306}, {"opponent": "drifb<PERSON>", "rating": 639, "opRating": 360}, {"opponent": "cresselia", "rating": 615, "opRating": 384}], "counters": [{"opponent": "bastiodon", "rating": 158}, {"opponent": "gastrodon", "rating": 193}, {"opponent": "drapion_shadow", "rating": 228}, {"opponent": "gliscor", "rating": 232}, {"opponent": "dusknoir_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 24156}, {"moveId": "SCRATCH", "uses": 13344}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 12936}, {"moveId": "TRAILBLAZE", "uses": 10409}, {"moveId": "FLAMETHROWER", "uses": 7475}, {"moveId": "SLUDGE_BOMB", "uses": 6634}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 45.6}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 342, "matchups": [{"opponent": "gastrodon", "rating": 788}, {"opponent": "spiritomb", "rating": 707}, {"opponent": "hippo<PERSON><PERSON>", "rating": 650, "opRating": 349}, {"opponent": "gallade_shadow", "rating": 512, "opRating": 487}, {"opponent": "bibarel", "rating": 504, "opRating": 495}], "counters": [{"opponent": "drapion_shadow", "rating": 76}, {"opponent": "drifb<PERSON>", "rating": 86}, {"opponent": "gliscor", "rating": 137}, {"opponent": "dusknoir_shadow", "rating": 177}, {"opponent": "bastiodon", "rating": 251}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 5483}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2543}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2449}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2405}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2200}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2098}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2041}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2012}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1902}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1901}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1866}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1812}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1761}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1707}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1700}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1500}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1327}, {"moveId": "ZEN_HEADBUTT", "uses": 662}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20216}, {"moveId": "ENERGY_BALL", "uses": 7125}, {"moveId": "SEED_FLARE", "uses": 5997}, {"moveId": "SOLAR_BEAM", "uses": 4125}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 44.7}, {"speciesId": "rampardos_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 340, "matchups": [{"opponent": "staravia_shadow", "rating": 833, "opRating": 166}, {"opponent": "honch<PERSON><PERSON>", "rating": 825, "opRating": 174}, {"opponent": "staraptor_shadow", "rating": 809, "opRating": 190}, {"opponent": "honchk<PERSON>_shadow", "rating": 793, "opRating": 206}, {"opponent": "rotom_heat", "rating": 777, "opRating": 222}], "counters": [{"opponent": "gastrodon", "rating": 130}, {"opponent": "drapion_shadow", "rating": 190}, {"opponent": "gliscor", "rating": 258}, {"opponent": "dusknoir_shadow", "rating": 266}, {"opponent": "bastiodon", "rating": 291}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 31544}, {"moveId": "ZEN_HEADBUTT", "uses": 5956}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 16320}, {"moveId": "FLAMETHROWER", "uses": 11192}, {"moveId": "OUTRAGE", "uses": 10013}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "FLAMETHROWER"], "score": 41}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 288, "matchups": [{"opponent": "drifb<PERSON>", "rating": 707, "opRating": 292}, {"opponent": "giratina_origin", "rating": 680, "opRating": 319}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 558, "opRating": 441}, {"opponent": "hippopotas", "rating": 540, "opRating": 459}, {"opponent": "bibarel", "rating": 509, "opRating": 490}], "counters": [{"opponent": "gastrodon", "rating": 142}, {"opponent": "dusknoir_shadow", "rating": 166}, {"opponent": "drapion_shadow", "rating": 169}, {"opponent": "bastiodon", "rating": 219}, {"opponent": "gliscor", "rating": 387}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 2984}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2815}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2540}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2499}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2425}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2406}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2405}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2278}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2251}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2228}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2201}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2143}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2113}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2093}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1865}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1593}, {"moveId": "ZEN_HEADBUTT", "uses": 523}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 18654}, {"moveId": "THUNDER", "uses": 8670}, {"moveId": "FOCUS_BLAST", "uses": 7469}, {"moveId": "GIGA_IMPACT", "uses": 2615}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "THUNDER"], "score": 39.1}, {"speciesId": "turtwig_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 341, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 703, "opRating": 296}, {"opponent": "gastrodon", "rating": 692}, {"opponent": "hippow<PERSON>_shadow", "rating": 677, "opRating": 322}, {"opponent": "bibarel", "rating": 544, "opRating": 455}, {"opponent": "electivire", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dusknoir_shadow", "rating": 66}, {"opponent": "gliscor", "rating": 94}, {"opponent": "drapion_shadow", "rating": 152}, {"opponent": "bastiodon", "rating": 176}, {"opponent": "spiritomb", "rating": 302}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 19307}, {"moveId": "TACKLE", "uses": 18193}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 15324}, {"moveId": "SEED_BOMB", "uses": 15322}, {"moveId": "ENERGY_BALL", "uses": 6878}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 39.1}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 305, "matchups": [{"opponent": "honchk<PERSON>_shadow", "rating": 825, "opRating": 174}, {"opponent": "yanmega", "rating": 666, "opRating": 333}, {"opponent": "<PERSON><PERSON>", "rating": 666, "opRating": 333}, {"opponent": "buneary", "rating": 626, "opRating": 373}, {"opponent": "heatran", "rating": 531, "opRating": 468}], "counters": [{"opponent": "gastrodon", "rating": 107}, {"opponent": "drapion_shadow", "rating": 165}, {"opponent": "dusknoir_shadow", "rating": 222}, {"opponent": "bastiodon", "rating": 241}, {"opponent": "gliscor", "rating": 258}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 31322}, {"moveId": "ZEN_HEADBUTT", "uses": 6178}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 16296}, {"moveId": "FLAMETHROWER", "uses": 11217}, {"moveId": "OUTRAGE", "uses": 10028}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "FLAMETHROWER"], "score": 36.7}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 260, "matchups": [{"opponent": "gabite_shadow", "rating": 809, "opRating": 190}, {"opponent": "drifloon", "rating": 681, "opRating": 318}, {"opponent": "giratina_origin", "rating": 677, "opRating": 322}, {"opponent": "drifb<PERSON>", "rating": 663, "opRating": 336}, {"opponent": "munchlax", "rating": 504, "opRating": 495}], "counters": [{"opponent": "drapion_shadow", "rating": 114}, {"opponent": "bastiodon", "rating": 158}, {"opponent": "gastrodon", "rating": 178}, {"opponent": "dusknoir_shadow", "rating": 194}, {"opponent": "gliscor", "rating": 465}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 2963}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2924}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2526}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2516}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2509}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2432}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2382}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2262}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2223}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2198}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2185}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2161}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2076}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2069}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1870}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1570}, {"moveId": "ZEN_HEADBUTT", "uses": 519}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 18637}, {"moveId": "THUNDER", "uses": 8700}, {"moveId": "FOCUS_BLAST", "uses": 7469}, {"moveId": "GIGA_IMPACT", "uses": 2590}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "THUNDER"], "score": 35.8}, {"speciesId": "cranidos_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 121, "matchups": [{"opponent": "regigigas", "rating": 639, "opRating": 360}, {"opponent": "regigigas_shadow", "rating": 571, "opRating": 428}], "counters": [{"opponent": "dusknoir_shadow", "rating": 44}, {"opponent": "drapion_shadow", "rating": 76}, {"opponent": "gastrodon", "rating": 83}, {"opponent": "gliscor", "rating": 107}, {"opponent": "bastiodon", "rating": 258}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 19322}, {"moveId": "ZEN_HEADBUTT", "uses": 18178}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18108}, {"moveId": "ANCIENT_POWER", "uses": 11160}, {"moveId": "BULLDOZE", "uses": 8169}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TAKE_DOWN", "ANCIENT_POWER", "BULLDOZE"], "score": 14.8}, {"speciesId": "cranidos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 123, "matchups": [{"opponent": "regigigas_shadow", "rating": 639, "opRating": 360}, {"opponent": "braviary_hisuian", "rating": 530, "opRating": 469}, {"opponent": "heatran", "rating": 522, "opRating": 477}], "counters": [{"opponent": "dusknoir_shadow", "rating": 44}, {"opponent": "drapion_shadow", "rating": 63}, {"opponent": "gastrodon", "rating": 71}, {"opponent": "gliscor", "rating": 103}, {"opponent": "bastiodon", "rating": 201}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 19313}, {"moveId": "ZEN_HEADBUTT", "uses": 18187}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 16179}, {"moveId": "ANCIENT_POWER", "uses": 9969}, {"moveId": "BULLDOZE", "uses": 7237}, {"moveId": "RETURN", "uses": 4159}]}, "moveset": ["TAKE_DOWN", "ANCIENT_POWER", "BULLDOZE"], "score": 13.8}]