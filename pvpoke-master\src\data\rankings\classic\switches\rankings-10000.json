[{"speciesId": "mewtwo_shadow", "speciesName": "<PERSON><PERSON>t<PERSON> (Shadow)", "rating": 870, "matchups": [{"opponent": "metagross", "rating": 789}, {"opponent": "zacian_hero", "rating": 778}, {"opponent": "gyarados", "rating": 747}, {"opponent": "mewtwo", "rating": 697}, {"opponent": "dialga", "rating": 559}], "counters": [{"opponent": "yveltal", "rating": 232}, {"opponent": "lugia", "rating": 366}, {"opponent": "grou<PERSON>", "rating": 421}, {"opponent": "garcho<PERSON>", "rating": 443}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 47810}, {"moveId": "CONFUSION", "uses": 28690}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19750}, {"moveId": "SHADOW_BALL", "uses": 11890}, {"moveId": "ICE_BEAM", "uses": 11522}, {"moveId": "FLAMETHROWER", "uses": 8763}, {"moveId": "THUNDERBOLT", "uses": 8480}, {"moveId": "FOCUS_BLAST", "uses": 7597}, {"moveId": "PSYCHIC", "uses": 4615}, {"moveId": "HYPER_BEAM", "uses": 3739}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 100}, {"speciesId": "giratina_altered", "speciesName": "Giratina (Altered)", "rating": 849, "matchups": [{"opponent": "mewtwo", "rating": 693}, {"opponent": "gyarados", "rating": 657}, {"opponent": "metagross", "rating": 617}, {"opponent": "zacian_hero", "rating": 601}, {"opponent": "garcho<PERSON>", "rating": 581}], "counters": [{"opponent": "dialga", "rating": 410}, {"opponent": "lugia", "rating": 426}, {"opponent": "grou<PERSON>", "rating": 489}, {"opponent": "giratina_origin", "rating": 492}, {"opponent": "zekrom", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 42485}, {"moveId": "DRAGON_BREATH", "uses": 34015}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 38955}, {"moveId": "ANCIENT_POWER", "uses": 18980}, {"moveId": "SHADOW_SNEAK", "uses": 18522}]}, "moveset": ["SHADOW_CLAW", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 99.2}, {"speciesId": "lugia_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 810, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "zacian_hero", "rating": 650}, {"opponent": "mewtwo", "rating": 633}, {"opponent": "gyarados", "rating": 600}, {"opponent": "metagross", "rating": 547}], "counters": [{"opponent": "magnezone_shadow", "rating": 207}, {"opponent": "raikou_shadow", "rating": 282}, {"opponent": "dialga", "rating": 440}, {"opponent": "giratina_origin", "rating": 452}, {"opponent": "yveltal", "rating": 472}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42604}, {"moveId": "EXTRASENSORY", "uses": 33896}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 29440}, {"moveId": "AEROBLAST", "uses": 21931}, {"moveId": "FUTURE_SIGHT", "uses": 15152}, {"moveId": "HYDRO_PUMP", "uses": 9791}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 98}, {"speciesId": "gyarados", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 796, "matchups": [{"opponent": "garcho<PERSON>", "rating": 930}, {"opponent": "giratina_origin", "rating": 688}, {"opponent": "metagross", "rating": 641}, {"opponent": "mewtwo", "rating": 587}, {"opponent": "lugia", "rating": 538}], "counters": [{"opponent": "dialga", "rating": 418}, {"opponent": "sylveon", "rating": 440}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 452}, {"opponent": "zacian_hero", "rating": 453}, {"opponent": "excadrill", "rating": 474}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22462}, {"moveId": "DRAGON_TAIL", "uses": 21784}, {"moveId": "WATERFALL", "uses": 20909}, {"moveId": "BITE", "uses": 11365}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 26291}, {"moveId": "CRUNCH", "uses": 18448}, {"moveId": "OUTRAGE", "uses": 11086}, {"moveId": "RETURN", "uses": 6833}, {"moveId": "TWISTER", "uses": 5978}, {"moveId": "HYDRO_PUMP", "uses": 4155}, {"moveId": "DRAGON_PULSE", "uses": 3733}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 95.3}, {"speciesId": "meloetta_aria", "speciesName": "<PERSON><PERSON><PERSON> (Aria)", "rating": 835, "matchups": [{"opponent": "giratina_origin", "rating": 741}, {"opponent": "garcho<PERSON>", "rating": 646}, {"opponent": "zacian_hero", "rating": 641}, {"opponent": "mewtwo", "rating": 634}, {"opponent": "gyarados", "rating": 574}], "counters": [{"opponent": "genesect_douse", "rating": 357}, {"opponent": "genesect_shock", "rating": 357}, {"opponent": "dialga", "rating": 369}, {"opponent": "metagross", "rating": 372}, {"opponent": "lugia", "rating": 400}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 41454}, {"moveId": "CONFUSION", "uses": 35046}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30116}, {"moveId": "THUNDERBOLT", "uses": 19603}, {"moveId": "HYPER_BEAM", "uses": 13752}, {"moveId": "DAZZLING_GLEAM", "uses": 13218}]}, "moveset": ["QUICK_ATTACK", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 95.1}, {"speciesId": "gyarado<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 805, "matchups": [{"opponent": "garcho<PERSON>", "rating": 935}, {"opponent": "metagross", "rating": 817}, {"opponent": "giratina_origin", "rating": 780}, {"opponent": "mewtwo", "rating": 512}, {"opponent": "dialga", "rating": 502}], "counters": [{"opponent": "mewtwo_shadow", "rating": 263}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 320}, {"opponent": "lugia", "rating": 400}, {"opponent": "sylveon", "rating": 456}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22646}, {"moveId": "DRAGON_TAIL", "uses": 22287}, {"moveId": "WATERFALL", "uses": 20980}, {"moveId": "BITE", "uses": 10568}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 28830}, {"moveId": "CRUNCH", "uses": 20183}, {"moveId": "OUTRAGE", "uses": 12243}, {"moveId": "TWISTER", "uses": 6540}, {"moveId": "HYDRO_PUMP", "uses": 4663}, {"moveId": "DRAGON_PULSE", "uses": 3984}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 94}, {"speciesId": "lugia", "speciesName": "Lugia", "rating": 816, "matchups": [{"opponent": "garcho<PERSON>", "rating": 785}, {"opponent": "dragonite", "rating": 702}, {"opponent": "gyarados", "rating": 583}, {"opponent": "zacian_hero", "rating": 523}, {"opponent": "giratina_origin", "rating": 504}], "counters": [{"opponent": "dialga", "rating": 377}, {"opponent": "metagross", "rating": 427}, {"opponent": "mewtwo", "rating": 468}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 471}, {"opponent": "zekrom", "rating": 483}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42203}, {"moveId": "EXTRASENSORY", "uses": 34297}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26334}, {"moveId": "AEROBLAST", "uses": 19552}, {"moveId": "FUTURE_SIGHT", "uses": 13649}, {"moveId": "HYDRO_PUMP", "uses": 8800}, {"moveId": "RETURN", "uses": 8238}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 93.5}, {"speciesId": "mewtwo", "speciesName": "Mewtwo", "rating": 848, "matchups": [{"opponent": "zacian_hero", "rating": 815}, {"opponent": "gyarados", "rating": 585}, {"opponent": "dialga", "rating": 580}, {"opponent": "lugia", "rating": 578}, {"opponent": "garcho<PERSON>", "rating": 546}], "counters": [{"opponent": "yveltal", "rating": 198}, {"opponent": "giratina_origin", "rating": 316}, {"opponent": "giratina_altered", "rating": 386}, {"opponent": "dragonite", "rating": 417}, {"opponent": "metagross", "rating": 476}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 46738}, {"moveId": "CONFUSION", "uses": 29762}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19057}, {"moveId": "SHADOW_BALL", "uses": 11492}, {"moveId": "ICE_BEAM", "uses": 11102}, {"moveId": "FLAMETHROWER", "uses": 8442}, {"moveId": "THUNDERBOLT", "uses": 8119}, {"moveId": "FOCUS_BLAST", "uses": 7342}, {"moveId": "RETURN", "uses": 4640}, {"moveId": "PSYCHIC", "uses": 4399}, {"moveId": "HYPER_BEAM", "uses": 1741}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 93.3}, {"speciesId": "zacian_hero", "speciesName": "<PERSON><PERSON><PERSON> (Hero)", "rating": 801, "matchups": [{"opponent": "dragonite", "rating": 806}, {"opponent": "dialga", "rating": 777}, {"opponent": "gyarados", "rating": 679}, {"opponent": "garcho<PERSON>", "rating": 650}, {"opponent": "lugia", "rating": 523}], "counters": [{"opponent": "metagross", "rating": 334}, {"opponent": "grou<PERSON>", "rating": 366}, {"opponent": "swampert", "rating": 437}, {"opponent": "giratina_origin", "rating": 444}, {"opponent": "mewtwo", "rating": 447}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27363}, {"moveId": "QUICK_ATTACK", "uses": 26463}, {"moveId": "FIRE_FANG", "uses": 12486}, {"moveId": "METAL_CLAW", "uses": 10190}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27796}, {"moveId": "WILD_CHARGE", "uses": 27136}, {"moveId": "PLAY_ROUGH", "uses": 12799}, {"moveId": "IRON_HEAD", "uses": 8946}]}, "moveset": ["QUICK_ATTACK", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 93.3}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 846, "matchups": [{"opponent": "metagross", "rating": 844}, {"opponent": "excadrill", "rating": 750}, {"opponent": "mewtwo", "rating": 699}, {"opponent": "zacian_hero", "rating": 627}, {"opponent": "lugia", "rating": 541}], "counters": [{"opponent": "dragonite", "rating": 300}, {"opponent": "dialga", "rating": 432}, {"opponent": "zekrom", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 441}, {"opponent": "gyarados", "rating": 445}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 43369}, {"moveId": "DRAGON_TAIL", "uses": 33131}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 36703}, {"moveId": "DRAGON_PULSE", "uses": 21111}, {"moveId": "OMINOUS_WIND", "uses": 18682}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "OMINOUS_WIND"], "score": 92.7}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 795, "matchups": [{"opponent": "giratina_origin", "rating": 712}, {"opponent": "dialga", "rating": 686}, {"opponent": "gyarados", "rating": 608}, {"opponent": "garcho<PERSON>", "rating": 583}, {"opponent": "zacian_hero", "rating": 515}], "counters": [{"opponent": "dragonite", "rating": 348}, {"opponent": "zekrom", "rating": 385}, {"opponent": "lugia", "rating": 466}, {"opponent": "grou<PERSON>", "rating": 470}, {"opponent": "mewtwo", "rating": 479}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66290}, {"moveId": "ZEN_HEADBUTT", "uses": 8634}, {"moveId": "YAWN", "uses": 1620}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26072}, {"moveId": "SUPER_POWER", "uses": 16868}, {"moveId": "OUTRAGE", "uses": 9582}, {"moveId": "EARTHQUAKE", "uses": 9351}, {"moveId": "HEAVY_SLAM", "uses": 7185}, {"moveId": "SKULL_BASH", "uses": 4464}, {"moveId": "HYPER_BEAM", "uses": 2959}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 91.3}, {"speciesId": "grou<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 861, "matchups": [{"opponent": "metagross", "rating": 834}, {"opponent": "dialga", "rating": 769}, {"opponent": "zacian_hero", "rating": 671}, {"opponent": "garcho<PERSON>", "rating": 608}, {"opponent": "mewtwo", "rating": 595}], "counters": [{"opponent": "dragonite", "rating": 236}, {"opponent": "ho_oh", "rating": 244}, {"opponent": "lugia", "rating": 278}, {"opponent": "gyarados", "rating": 337}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43305}, {"moveId": "DRAGON_TAIL", "uses": 33195}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 29653}, {"moveId": "FIRE_PUNCH", "uses": 28457}, {"moveId": "SOLAR_BEAM", "uses": 12756}, {"moveId": "FIRE_BLAST", "uses": 5731}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "FIRE_PUNCH"], "score": 91.1}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 797, "matchups": [{"opponent": "excadrill", "rating": 942}, {"opponent": "metagross", "rating": 807}, {"opponent": "dialga", "rating": 711}, {"opponent": "garcho<PERSON>", "rating": 576}, {"opponent": "mewtwo", "rating": 527}], "counters": [{"opponent": "giratina_origin", "rating": 312}, {"opponent": "kyogre", "rating": 326}, {"opponent": "dragonite", "rating": 343}, {"opponent": "gyarados", "rating": 347}, {"opponent": "swampert", "rating": 395}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51723}, {"moveId": "ROCK_THROW", "uses": 24777}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 25399}, {"moveId": "EARTH_POWER", "uses": 24576}, {"moveId": "OUTRAGE", "uses": 15210}, {"moveId": "FOCUS_BLAST", "uses": 11275}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTH_POWER"], "score": 89.2}, {"speciesId": "latios_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 836, "matchups": [{"opponent": "garcho<PERSON>", "rating": 880}, {"opponent": "zacian_hero", "rating": 793}, {"opponent": "giratina_origin", "rating": 706}, {"opponent": "mewtwo", "rating": 694}, {"opponent": "gyarados", "rating": 595}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "metagross", "rating": 366}, {"opponent": "lugia", "rating": 385}, {"opponent": "dialga", "rating": 448}, {"opponent": "grou<PERSON>", "rating": 478}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 67511}, {"moveId": "ZEN_HEADBUTT", "uses": 8989}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 33216}, {"moveId": "LUSTER_PURGE", "uses": 21563}, {"moveId": "PSYCHIC", "uses": 14247}, {"moveId": "SOLAR_BEAM", "uses": 7469}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 87.8}, {"speciesId": "palkia", "speciesName": "Pa<PERSON><PERSON>", "rating": 844, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "giratina_origin", "rating": 698}, {"opponent": "excadrill", "rating": 692}, {"opponent": "metagross", "rating": 640}, {"opponent": "mewtwo", "rating": 561}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 261}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "lugia", "rating": 326}, {"opponent": "dialga", "rating": 394}, {"opponent": "gyarados", "rating": 443}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 38484}, {"moveId": "DRAGON_BREATH", "uses": 38016}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 41130}, {"moveId": "DRACO_METEOR", "uses": 18156}, {"moveId": "FIRE_BLAST", "uses": 10859}, {"moveId": "HYDRO_PUMP", "uses": 6410}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "DRACO_METEOR"], "score": 87.8}, {"speciesId": "dragonite", "speciesName": "Dragonite", "rating": 860, "matchups": [{"opponent": "garcho<PERSON>", "rating": 944}, {"opponent": "giratina_origin", "rating": 805}, {"opponent": "excadrill", "rating": 680}, {"opponent": "dialga", "rating": 587}, {"opponent": "mewtwo", "rating": 582}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 196}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "lugia", "rating": 297}, {"opponent": "metagross", "rating": 450}, {"opponent": "gyarados", "rating": 489}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 32790}, {"moveId": "DRAGON_TAIL", "uses": 32319}, {"moveId": "STEEL_WING", "uses": 11386}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 23280}, {"moveId": "SUPER_POWER", "uses": 19236}, {"moveId": "HURRICANE", "uses": 10844}, {"moveId": "OUTRAGE", "uses": 6403}, {"moveId": "RETURN", "uses": 6271}, {"moveId": "DRAGON_PULSE", "uses": 4339}, {"moveId": "DRACO_METEOR", "uses": 3768}, {"moveId": "HYPER_BEAM", "uses": 2454}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 87.1}, {"speciesId": "reshiram", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 835, "matchups": [{"opponent": "metagross", "rating": 845}, {"opponent": "garcho<PERSON>", "rating": 820}, {"opponent": "giratina_origin", "rating": 820}, {"opponent": "zacian_hero", "rating": 605}, {"opponent": "mewtwo", "rating": 589}], "counters": [{"opponent": "excadrill", "rating": 244}, {"opponent": "grou<PERSON>", "rating": 274}, {"opponent": "swampert", "rating": 313}, {"opponent": "dialga", "rating": 355}, {"opponent": "lugia", "rating": 371}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48516}, {"moveId": "FIRE_FANG", "uses": 27984}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24039}, {"moveId": "OVERHEAT", "uses": 19654}, {"moveId": "STONE_EDGE", "uses": 19288}, {"moveId": "DRACO_METEOR", "uses": 13361}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "OVERHEAT"], "score": 86.5}, {"speciesId": "genesect_chill", "speciesName": "Genesect (Chill)", "rating": 836, "matchups": [{"opponent": "garcho<PERSON>", "rating": 870}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "gyarados", "rating": 636}, {"opponent": "zacian_hero", "rating": 623}, {"opponent": "dialga", "rating": 525}], "counters": [{"opponent": "ho_oh", "rating": 101}, {"opponent": "grou<PERSON>", "rating": 206}, {"opponent": "kyogre", "rating": 372}, {"opponent": "giratina_altered", "rating": 414}, {"opponent": "giratina_origin", "rating": 434}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 52047}, {"moveId": "METAL_CLAW", "uses": 24453}], "chargedMoves": [{"moveId": "TECHNO_BLAST_CHILL", "uses": 26649}, {"moveId": "X_SCISSOR", "uses": 22707}, {"moveId": "MAGNET_BOMB", "uses": 19737}, {"moveId": "ICE_BEAM", "uses": 7541}]}, "moveset": ["FURY_CUTTER", "TECHNO_BLAST_CHILL", "X_SCISSOR"], "score": 86}, {"speciesId": "avalugg", "speciesName": "Avalugg", "rating": 732, "matchups": [{"opponent": "garcho<PERSON>", "rating": 904}, {"opponent": "dragonite", "rating": 750}, {"opponent": "giratina_origin", "rating": 626}, {"opponent": "gyarados", "rating": 618}, {"opponent": "zacian_hero", "rating": 569}], "counters": [{"opponent": "metagross", "rating": 279}, {"opponent": "kyogre", "rating": 301}, {"opponent": "ho_oh", "rating": 419}, {"opponent": "dialga", "rating": 459}, {"opponent": "mewtwo", "rating": 473}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 52530}, {"moveId": "BITE", "uses": 23970}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 31692}, {"moveId": "BODY_SLAM", "uses": 17221}, {"moveId": "CRUNCH", "uses": 14426}, {"moveId": "EARTHQUAKE", "uses": 10124}, {"moveId": "MIRROR_COAT", "uses": 3078}]}, "moveset": ["ICE_FANG", "AVALANCHE", "BODY_SLAM"], "score": 85.5}, {"speciesId": "florges", "speciesName": "Florges", "rating": 798, "matchups": [{"opponent": "dragonite", "rating": 872}, {"opponent": "dialga", "rating": 806}, {"opponent": "gyarados", "rating": 791}, {"opponent": "garcho<PERSON>", "rating": 657}, {"opponent": "giratina_origin", "rating": 571}], "counters": [{"opponent": "ho_oh", "rating": 203}, {"opponent": "metagross", "rating": 232}, {"opponent": "excadrill", "rating": 304}, {"opponent": "kyogre", "rating": 353}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 28079}, {"moveId": "VINE_WHIP", "uses": 22196}, {"moveId": "TACKLE", "uses": 15711}, {"moveId": "RAZOR_LEAF", "uses": 10522}], "chargedMoves": [{"moveId": "DISARMING_VOICE", "uses": 34866}, {"moveId": "PSYCHIC", "uses": 15469}, {"moveId": "MOONBLAST", "uses": 14204}, {"moveId": "PETAL_BLIZZARD", "uses": 11984}]}, "moveset": ["FAIRY_WIND", "DISARMING_VOICE", "MOONBLAST"], "score": 85.3}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 765, "matchups": [{"opponent": "garcho<PERSON>", "rating": 881}, {"opponent": "dragonite", "rating": 704}, {"opponent": "giratina_origin", "rating": 679}, {"opponent": "dialga", "rating": 606}, {"opponent": "lugia", "rating": 565}], "counters": [{"opponent": "zacian_hero", "rating": 381}, {"opponent": "gyarados", "rating": 425}, {"opponent": "metagross", "rating": 438}, {"opponent": "swampert", "rating": 452}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 51475}, {"moveId": "MUD_SLAP", "uses": 25025}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36176}, {"moveId": "STONE_EDGE", "uses": 12258}, {"moveId": "BULLDOZE", "uses": 11118}, {"moveId": "ANCIENT_POWER", "uses": 11080}, {"moveId": "RETURN", "uses": 5756}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 85}, {"speciesId": "dialga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 802, "matchups": [{"opponent": "lugia", "rating": 665}, {"opponent": "mewtwo", "rating": 627}, {"opponent": "gyarados", "rating": 614}, {"opponent": "giratina_origin", "rating": 567}, {"opponent": "zacian_hero", "rating": 538}], "counters": [{"opponent": "excadrill", "rating": 253}, {"opponent": "grou<PERSON>", "rating": 274}, {"opponent": "swampert", "rating": 313}, {"opponent": "metagross", "rating": 418}, {"opponent": "garcho<PERSON>", "rating": 446}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52355}, {"moveId": "METAL_CLAW", "uses": 24145}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 28857}, {"moveId": "DRACO_METEOR", "uses": 25272}, {"moveId": "THUNDER", "uses": 22481}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 84.3}, {"speciesId": "buzzwole", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 830, "matchups": [{"opponent": "excadrill", "rating": 817}, {"opponent": "swampert", "rating": 798, "opRating": 201}, {"opponent": "dialga", "rating": 706}, {"opponent": "garcho<PERSON>", "rating": 682}, {"opponent": "metagross", "rating": 559, "opRating": 440}], "counters": [{"opponent": "gyarados", "rating": 342}, {"opponent": "lugia", "rating": 350}, {"opponent": "giratina_origin", "rating": 380}, {"opponent": "zacian_hero", "rating": 384}, {"opponent": "mewtwo", "rating": 395}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45492}, {"moveId": "POISON_JAB", "uses": 31008}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34408}, {"moveId": "LUNGE", "uses": 25659}, {"moveId": "FELL_STINGER", "uses": 8188}, {"moveId": "POWER_UP_PUNCH", "uses": 8174}]}, "moveset": ["COUNTER", "SUPER_POWER", "LUNGE"], "score": 83.8}, {"speciesId": "zap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 842, "matchups": [{"opponent": "gyarados", "rating": 793}, {"opponent": "swampert", "rating": 706, "opRating": 293}, {"opponent": "zacian_hero", "rating": 594}, {"opponent": "metagross", "rating": 580}, {"opponent": "lugia", "rating": 577, "opRating": 422}], "counters": [{"opponent": "mewtwo", "rating": 333}, {"opponent": "dragonite", "rating": 335}, {"opponent": "dialga", "rating": 366}, {"opponent": "giratina_origin", "rating": 396}, {"opponent": "garcho<PERSON>", "rating": 403}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50303}, {"moveId": "CHARGE_BEAM", "uses": 26197}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 33047}, {"moveId": "THUNDERBOLT", "uses": 15596}, {"moveId": "ANCIENT_POWER", "uses": 14479}, {"moveId": "THUNDER", "uses": 6915}, {"moveId": "ZAP_CANNON", "uses": 6440}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 82.3}, {"speciesId": "x<PERSON><PERSON>", "speciesName": "Xerneas", "rating": 763, "matchups": [{"opponent": "dialga", "rating": 837}, {"opponent": "dragonite", "rating": 737}, {"opponent": "gyarados", "rating": 723}, {"opponent": "garcho<PERSON>", "rating": 611}, {"opponent": "zacian_hero", "rating": 575}], "counters": [{"opponent": "ho_oh", "rating": 164}, {"opponent": "mewtwo", "rating": 244}, {"opponent": "metagross", "rating": 383}, {"opponent": "giratina_origin", "rating": 444}, {"opponent": "swampert", "rating": 445}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 68423}, {"moveId": "ZEN_HEADBUTT", "uses": 8077}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27205}, {"moveId": "MOONBLAST", "uses": 19191}, {"moveId": "MEGAHORN", "uses": 14039}, {"moveId": "THUNDER", "uses": 10856}, {"moveId": "GIGA_IMPACT", "uses": 5376}]}, "moveset": ["TACKLE", "CLOSE_COMBAT", "MOONBLAST"], "score": 81.6}, {"speciesId": "kyogre", "speciesName": "Kyogre", "rating": 769, "matchups": [{"opponent": "metagross", "rating": 673}, {"opponent": "zacian_hero", "rating": 660}, {"opponent": "mewtwo", "rating": 605}, {"opponent": "garcho<PERSON>", "rating": 600}, {"opponent": "lugia", "rating": 576}], "counters": [{"opponent": "giratina_origin", "rating": 324}, {"opponent": "zekrom", "rating": 328}, {"opponent": "dragonite", "rating": 329}, {"opponent": "dialga", "rating": 372}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 76500}], "chargedMoves": [{"moveId": "SURF", "uses": 37729}, {"moveId": "BLIZZARD", "uses": 17476}, {"moveId": "THUNDER", "uses": 15182}, {"moveId": "HYDRO_PUMP", "uses": 6017}]}, "moveset": ["WATERFALL", "SURF", "BLIZZARD"], "score": 81}, {"speciesId": "zekrom", "speciesName": "Zekrom", "rating": 834, "matchups": [{"opponent": "metagross", "rating": 855}, {"opponent": "garcho<PERSON>", "rating": 820}, {"opponent": "giratina_origin", "rating": 820}, {"opponent": "mewtwo", "rating": 589}, {"opponent": "gyarados", "rating": 567}], "counters": [{"opponent": "excadrill", "rating": 244}, {"opponent": "grou<PERSON>", "rating": 274}, {"opponent": "swampert", "rating": 313}, {"opponent": "dialga", "rating": 355}, {"opponent": "zacian_hero", "rating": 439}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47053}, {"moveId": "CHARGE_BEAM", "uses": 29447}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34901}, {"moveId": "CRUNCH", "uses": 18869}, {"moveId": "OUTRAGE", "uses": 16900}, {"moveId": "FLASH_CANNON", "uses": 5848}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "WILD_CHARGE"], "score": 80.9}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 768, "matchups": [{"opponent": "zacian_hero", "rating": 851}, {"opponent": "excadrill", "rating": 735, "opRating": 264}, {"opponent": "giratina_origin", "rating": 730}, {"opponent": "swampert", "rating": 652, "opRating": 347}, {"opponent": "metagross", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dragonite", "rating": 289}, {"opponent": "lugia", "rating": 340}, {"opponent": "garcho<PERSON>", "rating": 401}, {"opponent": "dialga", "rating": 413}, {"opponent": "mewtwo", "rating": 453}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 65336}, {"moveId": "ZEN_HEADBUTT", "uses": 9216}, {"moveId": "YAWN", "uses": 2055}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24709}, {"moveId": "SUPER_POWER", "uses": 16225}, {"moveId": "OUTRAGE", "uses": 9122}, {"moveId": "EARTHQUAKE", "uses": 8982}, {"moveId": "HEAVY_SLAM", "uses": 6834}, {"moveId": "SKULL_BASH", "uses": 4252}, {"moveId": "RETURN", "uses": 3601}, {"moveId": "HYPER_BEAM", "uses": 2779}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 80.3}, {"speciesId": "yveltal", "speciesName": "Y<PERSON><PERSON>", "rating": 794, "matchups": [{"opponent": "mewtwo", "rating": 947}, {"opponent": "metagross", "rating": 773}, {"opponent": "giratina_origin", "rating": 707}, {"opponent": "dialga", "rating": 678}, {"opponent": "garcho<PERSON>", "rating": 648}], "counters": [{"opponent": "sylveon", "rating": 185}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 205}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "dragonite", "rating": 329}, {"opponent": "zekrom", "rating": 339}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27716}, {"moveId": "GUST", "uses": 25662}, {"moveId": "SUCKER_PUNCH", "uses": 23052}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27439}, {"moveId": "HURRICANE", "uses": 15663}, {"moveId": "FOCUS_BLAST", "uses": 13290}, {"moveId": "PSYCHIC", "uses": 12873}, {"moveId": "HYPER_BEAM", "uses": 7254}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 80.3}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 823, "matchups": [{"opponent": "gyarados", "rating": 793}, {"opponent": "metagross", "rating": 634}, {"opponent": "mewtwo", "rating": 559}, {"opponent": "garcho<PERSON>", "rating": 540}, {"opponent": "zacian_hero", "rating": 540}], "counters": [{"opponent": "zekrom", "rating": 285}, {"opponent": "dialga", "rating": 296}, {"opponent": "giratina_origin", "rating": 332}, {"opponent": "excadrill", "rating": 341}, {"opponent": "dragonite", "rating": 438}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 49298}, {"moveId": "CHARGE_BEAM", "uses": 27202}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 29501}, {"moveId": "THUNDERBOLT", "uses": 14293}, {"moveId": "ANCIENT_POWER", "uses": 13144}, {"moveId": "RETURN", "uses": 7340}, {"moveId": "THUNDER", "uses": 6270}, {"moveId": "ZAP_CANNON", "uses": 6004}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 80}, {"speciesId": "dragonite_shadow", "speciesName": "Dragonite (Shadow)", "rating": 860, "matchups": [{"opponent": "garcho<PERSON>", "rating": 944}, {"opponent": "grou<PERSON>", "rating": 843}, {"opponent": "giratina_origin", "rating": 811}, {"opponent": "gyarados", "rating": 630}, {"opponent": "mewtwo", "rating": 531}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "dialga", "rating": 274}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "lugia", "rating": 385}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33104}, {"moveId": "DRAGON_BREATH", "uses": 33053}, {"moveId": "STEEL_WING", "uses": 10430}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 24292}, {"moveId": "SUPER_POWER", "uses": 20049}, {"moveId": "HURRICANE", "uses": 11514}, {"moveId": "OUTRAGE", "uses": 6819}, {"moveId": "HYPER_BEAM", "uses": 5140}, {"moveId": "DRAGON_PULSE", "uses": 4488}, {"moveId": "DRACO_METEOR", "uses": 4065}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 79.6}, {"speciesId": "regirock", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 801, "matchups": [{"opponent": "zacian_hero", "rating": 656}, {"opponent": "excadrill", "rating": 625, "opRating": 375}, {"opponent": "lugia", "rating": 595}, {"opponent": "gyarados", "rating": 593}, {"opponent": "giratina_origin", "rating": 517}], "counters": [{"opponent": "grou<PERSON>", "rating": 277}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "metagross", "rating": 316}, {"opponent": "dialga", "rating": 459}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 40986}, {"moveId": "ROCK_THROW", "uses": 25560}, {"moveId": "ROCK_SMASH", "uses": 9950}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 30548}, {"moveId": "EARTHQUAKE", "uses": 18967}, {"moveId": "FOCUS_BLAST", "uses": 14040}, {"moveId": "ZAP_CANNON", "uses": 12845}]}, "moveset": ["LOCK_ON", "STONE_EDGE", "FOCUS_BLAST"], "score": 79.2}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 779, "matchups": [{"opponent": "metagross", "rating": 873}, {"opponent": "dialga", "rating": 736}, {"opponent": "excadrill", "rating": 676}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "garcho<PERSON>", "rating": 537}], "counters": [{"opponent": "dragonite", "rating": 252}, {"opponent": "lugia", "rating": 357}, {"opponent": "giratina_origin", "rating": 392}, {"opponent": "mewtwo", "rating": 395}, {"opponent": "gyarados", "rating": 425}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 46778}, {"moveId": "WATER_GUN", "uses": 29722}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 29388}, {"moveId": "EARTHQUAKE", "uses": 13459}, {"moveId": "MUDDY_WATER", "uses": 11273}, {"moveId": "SURF", "uses": 9728}, {"moveId": "SLUDGE_WAVE", "uses": 6691}, {"moveId": "RETURN", "uses": 5862}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 79}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 814, "matchups": [{"opponent": "metagross", "rating": 816}, {"opponent": "excadrill", "rating": 711}, {"opponent": "dialga", "rating": 619}, {"opponent": "dragonite", "rating": 619}, {"opponent": "giratina_origin", "rating": 558}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 182}, {"opponent": "lugia", "rating": 214}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "gyarados", "rating": 373}, {"opponent": "mewtwo", "rating": 473}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39919}, {"moveId": "DRAGON_TAIL", "uses": 36581}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 24656}, {"moveId": "EARTH_POWER", "uses": 22569}, {"moveId": "FIRE_BLAST", "uses": 9839}, {"moveId": "EARTHQUAKE", "uses": 9670}, {"moveId": "SAND_TOMB", "uses": 9510}]}, "moveset": ["MUD_SHOT", "OUTRAGE", "EARTH_POWER"], "score": 78.5}, {"speciesId": "genesect_douse", "speciesName": "Genesect (Douse)", "rating": 846, "matchups": [{"opponent": "metagross", "rating": 806}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "gyarados", "rating": 636}, {"opponent": "garcho<PERSON>", "rating": 531}, {"opponent": "dialga", "rating": 515}], "counters": [{"opponent": "ho_oh", "rating": 101}, {"opponent": "grou<PERSON>", "rating": 206}, {"opponent": "giratina_origin", "rating": 235}, {"opponent": "dragonite", "rating": 316}, {"opponent": "zekrom", "rating": 388}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50358}, {"moveId": "METAL_CLAW", "uses": 26142}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24952}, {"moveId": "TECHNO_BLAST_DOUSE", "uses": 22968}, {"moveId": "MAGNET_BOMB", "uses": 22125}, {"moveId": "GUNK_SHOT", "uses": 6554}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_DOUSE"], "score": 78.5}, {"speciesId": "genesect_shock", "speciesName": "Genesect (Shock)", "rating": 838, "matchups": [{"opponent": "gyarados", "rating": 870}, {"opponent": "metagross", "rating": 806}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "dialga", "rating": 515}, {"opponent": "garcho<PERSON>", "rating": 506}], "counters": [{"opponent": "grou<PERSON>", "rating": 206}, {"opponent": "giratina_origin", "rating": 235}, {"opponent": "excadrill", "rating": 332}, {"opponent": "zekrom", "rating": 388}, {"opponent": "dragonite", "rating": 417}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50674}, {"moveId": "METAL_CLAW", "uses": 25826}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25922}, {"moveId": "MAGNET_BOMB", "uses": 23199}, {"moveId": "TECHNO_BLAST_SHOCK", "uses": 22200}, {"moveId": "ZAP_CANNON", "uses": 5208}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_SHOCK"], "score": 77.4}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 803, "matchups": [{"opponent": "excadrill", "rating": 857, "opRating": 142}, {"opponent": "metagross", "rating": 741}, {"opponent": "dialga", "rating": 700}, {"opponent": "yveltal", "rating": 601, "opRating": 398}, {"opponent": "zekrom", "rating": 526, "opRating": 473}], "counters": [{"opponent": "giratina_origin", "rating": 318}, {"opponent": "lugia", "rating": 335}, {"opponent": "gyarados", "rating": 360}, {"opponent": "mewtwo", "rating": 385}, {"opponent": "garcho<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 38390}, {"moveId": "POISON_JAB", "uses": 30029}, {"moveId": "ROCK_SMASH", "uses": 8041}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 44040}, {"moveId": "X_SCISSOR", "uses": 18550}, {"moveId": "AERIAL_ACE", "uses": 13894}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "X_SCISSOR"], "score": 77.1}, {"speciesId": "articuno_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 706, "matchups": [{"opponent": "garcho<PERSON>", "rating": 946}, {"opponent": "grou<PERSON>", "rating": 768, "opRating": 231}, {"opponent": "dragonite", "rating": 591}, {"opponent": "giratina_origin", "rating": 580}, {"opponent": "swampert", "rating": 561, "opRating": 438}], "counters": [{"opponent": "metagross", "rating": 319}, {"opponent": "mewtwo", "rating": 393}, {"opponent": "dialga", "rating": 404}, {"opponent": "gyarados", "rating": 430}, {"opponent": "zacian_hero", "rating": 436}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46445}, {"moveId": "FROST_BREATH", "uses": 30055}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31070}, {"moveId": "ANCIENT_POWER", "uses": 15601}, {"moveId": "HURRICANE", "uses": 12992}, {"moveId": "ICE_BEAM", "uses": 10013}, {"moveId": "BLIZZARD", "uses": 7020}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 76.8}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 756, "matchups": [{"opponent": "garcho<PERSON>", "rating": 881}, {"opponent": "giratina_origin", "rating": 788}, {"opponent": "gyarados", "rating": 718}, {"opponent": "lugia", "rating": 672}, {"opponent": "dialga", "rating": 606}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "kyogre", "rating": 266}, {"opponent": "snorlax", "rating": 366}, {"opponent": "ho_oh", "rating": 408}, {"opponent": "zacian_hero", "rating": 424}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52586}, {"moveId": "MUD_SLAP", "uses": 23914}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38956}, {"moveId": "STONE_EDGE", "uses": 13265}, {"moveId": "BULLDOZE", "uses": 12156}, {"moveId": "ANCIENT_POWER", "uses": 12014}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 76.6}, {"speciesId": "latios", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 847, "matchups": [{"opponent": "garcho<PERSON>", "rating": 909}, {"opponent": "grou<PERSON>", "rating": 790}, {"opponent": "giratina_origin", "rating": 718}, {"opponent": "excadrill", "rating": 656}, {"opponent": "dragonite", "rating": 569}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 297}, {"opponent": "metagross", "rating": 348}, {"opponent": "mewtwo", "rating": 463}, {"opponent": "zacian_hero", "rating": 482}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 66665}, {"moveId": "ZEN_HEADBUTT", "uses": 9835}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30047}, {"moveId": "LUSTER_PURGE", "uses": 19612}, {"moveId": "PSYCHIC", "uses": 12995}, {"moveId": "RETURN", "uses": 7312}, {"moveId": "SOLAR_BEAM", "uses": 6720}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 76.5}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 786, "matchups": [{"opponent": "metagross", "rating": 816}, {"opponent": "dialga", "rating": 746}, {"opponent": "mewtwo", "rating": 653}, {"opponent": "lugia", "rating": 651}, {"opponent": "gyarados", "rating": 553}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "grou<PERSON>", "rating": 282}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "dragonite", "rating": 329}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38848}, {"moveId": "MUD_SLAP", "uses": 20163}, {"moveId": "METAL_CLAW", "uses": 17482}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32017}, {"moveId": "ROCK_SLIDE", "uses": 22231}, {"moveId": "IRON_HEAD", "uses": 13983}, {"moveId": "EARTHQUAKE", "uses": 8238}]}, "moveset": ["MUD_SHOT", "DRILL_RUN", "ROCK_SLIDE"], "score": 76.1}, {"speciesId": "genesect_burn", "speciesName": "Genesect (Burn)", "rating": 825, "matchups": [{"opponent": "metagross", "rating": 870}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "gyarados", "rating": 636}, {"opponent": "dialga", "rating": 525}, {"opponent": "garcho<PERSON>", "rating": 506}], "counters": [{"opponent": "ho_oh", "rating": 101}, {"opponent": "grou<PERSON>", "rating": 206}, {"opponent": "giratina_origin", "rating": 235}, {"opponent": "dragonite", "rating": 316}, {"opponent": "zekrom", "rating": 388}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50422}, {"moveId": "METAL_CLAW", "uses": 26078}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24640}, {"moveId": "MAGNET_BOMB", "uses": 22846}, {"moveId": "TECHNO_BLAST_BURN", "uses": 22710}, {"moveId": "FLAMETHROWER", "uses": 6414}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_BURN"], "score": 75.9}, {"speciesId": "articuno", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 698, "matchups": [{"opponent": "garcho<PERSON>", "rating": 940}, {"opponent": "giratina_origin", "rating": 639}, {"opponent": "gyarados", "rating": 575}, {"opponent": "zacian_hero", "rating": 553}, {"opponent": "mewtwo", "rating": 513}], "counters": [{"opponent": "metagross", "rating": 261}, {"opponent": "kyogre", "rating": 279}, {"opponent": "dialga", "rating": 328}, {"opponent": "excadrill", "rating": 416}, {"opponent": "zekrom", "rating": 489}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 45523}, {"moveId": "FROST_BREATH", "uses": 30977}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 28473}, {"moveId": "ANCIENT_POWER", "uses": 13981}, {"moveId": "HURRICANE", "uses": 11581}, {"moveId": "ICE_BEAM", "uses": 9187}, {"moveId": "RETURN", "uses": 6863}, {"moveId": "BLIZZARD", "uses": 6413}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 75.4}, {"speciesId": "cobalion", "speciesName": "Cobalion", "rating": 805, "matchups": [{"opponent": "magnezone_shadow", "rating": 901, "opRating": 98}, {"opponent": "dialga", "rating": 789}, {"opponent": "snorlax", "rating": 638, "opRating": 361}, {"opponent": "gyarados", "rating": 587}, {"opponent": "excadrill", "rating": 555, "opRating": 444}], "counters": [{"opponent": "grou<PERSON>", "rating": 241}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "giratina_origin", "rating": 346}, {"opponent": "garcho<PERSON>", "rating": 354}, {"opponent": "zacian_hero", "rating": 387}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 50495}, {"moveId": "METAL_CLAW", "uses": 21789}, {"moveId": "ZEN_HEADBUTT", "uses": 4179}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 26276}, {"moveId": "CLOSE_COMBAT", "uses": 24110}, {"moveId": "STONE_EDGE", "uses": 14767}, {"moveId": "IRON_HEAD", "uses": 11226}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "STONE_EDGE"], "score": 75}, {"speciesId": "terrakion", "speciesName": "Terrakion", "rating": 812, "matchups": [{"opponent": "ho_oh", "rating": 901, "opRating": 98}, {"opponent": "excadrill", "rating": 890}, {"opponent": "dialga", "rating": 731}, {"opponent": "metagross", "rating": 651}, {"opponent": "zekrom", "rating": 611, "opRating": 388}], "counters": [{"opponent": "giratina_origin", "rating": 290}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "gyarados", "rating": 474}, {"opponent": "garcho<PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47088}, {"moveId": "SMACK_DOWN", "uses": 26748}, {"moveId": "ZEN_HEADBUTT", "uses": 2635}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 24125}, {"moveId": "ROCK_SLIDE", "uses": 22735}, {"moveId": "CLOSE_COMBAT", "uses": 21396}, {"moveId": "EARTHQUAKE", "uses": 8214}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "ROCK_SLIDE"], "score": 75}, {"speciesId": "kyurem", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 799, "matchups": [{"opponent": "garcho<PERSON>", "rating": 905}, {"opponent": "giratina_origin", "rating": 788}, {"opponent": "swampert", "rating": 635, "opRating": 364}, {"opponent": "dragonite", "rating": 612}, {"opponent": "mewtwo", "rating": 594}], "counters": [{"opponent": "zacian_hero", "rating": 222}, {"opponent": "lugia", "rating": 292}, {"opponent": "metagross", "rating": 343}, {"opponent": "dialga", "rating": 421}, {"opponent": "gyarados", "rating": 481}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 56554}, {"moveId": "STEEL_WING", "uses": 19946}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 40958}, {"moveId": "BLIZZARD", "uses": 22094}, {"moveId": "DRACO_METEOR", "uses": 13658}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "BLIZZARD"], "score": 74.7}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 812, "matchups": [{"opponent": "mewtwo", "rating": 686}, {"opponent": "gyarados", "rating": 636}, {"opponent": "zacian_hero", "rating": 623}, {"opponent": "garcho<PERSON>", "rating": 531}, {"opponent": "dialga", "rating": 515}], "counters": [{"opponent": "ho_oh", "rating": 101}, {"opponent": "grou<PERSON>", "rating": 206}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "dragonite", "rating": 417}, {"opponent": "excadrill", "rating": 448}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 49248}, {"moveId": "METAL_CLAW", "uses": 27252}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26835}, {"moveId": "MAGNET_BOMB", "uses": 25109}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 20823}, {"moveId": "HYPER_BEAM", "uses": 3605}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 74.1}, {"speciesId": "mew", "speciesName": "Mew", "rating": 794, "matchups": [{"opponent": "gyarados", "rating": 855}, {"opponent": "lugia", "rating": 738}, {"opponent": "zacian_hero", "rating": 624}, {"opponent": "mewtwo", "rating": 537}, {"opponent": "metagross", "rating": 537}], "counters": [{"opponent": "dragonite", "rating": 188}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "zekrom", "rating": 301}, {"opponent": "dialga", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 429}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 12075}, {"moveId": "VOLT_SWITCH", "uses": 10834}, {"moveId": "SNARL", "uses": 9100}, {"moveId": "POISON_JAB", "uses": 7738}, {"moveId": "DRAGON_TAIL", "uses": 7172}, {"moveId": "INFESTATION", "uses": 7064}, {"moveId": "CHARGE_BEAM", "uses": 4685}, {"moveId": "WATERFALL", "uses": 4594}, {"moveId": "FROST_BREATH", "uses": 4402}, {"moveId": "STEEL_WING", "uses": 2911}, {"moveId": "STRUGGLE_BUG", "uses": 2604}, {"moveId": "ROCK_SMASH", "uses": 2008}, {"moveId": "CUT", "uses": 1052}, {"moveId": "POUND", "uses": 100}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8457}, {"moveId": "DRAGON_CLAW", "uses": 5894}, {"moveId": "SURF", "uses": 5599}, {"moveId": "ROCK_SLIDE", "uses": 5280}, {"moveId": "PSYSHOCK", "uses": 4897}, {"moveId": "ICE_BEAM", "uses": 4743}, {"moveId": "FLAME_CHARGE", "uses": 4341}, {"moveId": "DARK_PULSE", "uses": 4288}, {"moveId": "GRASS_KNOT", "uses": 4185}, {"moveId": "FOCUS_BLAST", "uses": 2850}, {"moveId": "LOW_SWEEP", "uses": 2582}, {"moveId": "BULLDOZE", "uses": 2313}, {"moveId": "STONE_EDGE", "uses": 2144}, {"moveId": "DAZZLING_GLEAM", "uses": 2021}, {"moveId": "PSYCHIC", "uses": 1890}, {"moveId": "ANCIENT_POWER", "uses": 1803}, {"moveId": "OVERHEAT", "uses": 1717}, {"moveId": "BLIZZARD", "uses": 1668}, {"moveId": "GYRO_BALL", "uses": 1530}, {"moveId": "FLASH_CANNON", "uses": 1489}, {"moveId": "ENERGY_BALL", "uses": 1481}, {"moveId": "THUNDERBOLT", "uses": 1468}, {"moveId": "HYPER_BEAM", "uses": 1416}, {"moveId": "THUNDER", "uses": 1359}, {"moveId": "SOLAR_BEAM", "uses": 815}]}, "moveset": ["SHADOW_CLAW", "SURF", "WILD_CHARGE"], "score": 74}, {"speciesId": "gardevoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "dragonite", "rating": 866}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "zekrom", "rating": 801, "opRating": 198}, {"opponent": "dialga", "rating": 675}, {"opponent": "gyarados", "rating": 603}], "counters": [{"opponent": "metagross", "rating": 261}, {"opponent": "excadrill", "rating": 358}, {"opponent": "lugia", "rating": 402}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "giratina_origin", "rating": 480}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31061}, {"moveId": "CHARM", "uses": 27001}, {"moveId": "CHARGE_BEAM", "uses": 18475}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26130}, {"moveId": "SYNCHRONOISE", "uses": 23219}, {"moveId": "DAZZLING_GLEAM", "uses": 16483}, {"moveId": "PSYCHIC", "uses": 10569}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 73.7}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 697, "matchups": [{"opponent": "garcho<PERSON>", "rating": 924}, {"opponent": "dragonite", "rating": 808}, {"opponent": "gyarados", "rating": 558}, {"opponent": "giratina_origin", "rating": 544}, {"opponent": "zacian_hero", "rating": 530}], "counters": [{"opponent": "metagross", "rating": 235}, {"opponent": "excadrill", "rating": 304}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "dialga", "rating": 421}, {"opponent": "swampert", "rating": 480}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 7539}, {"moveId": "AIR_SLASH", "uses": 6333}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4942}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4865}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4404}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4251}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4202}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4201}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4168}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3891}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3833}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3781}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3741}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3363}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3362}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3321}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3206}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3111}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 21444}, {"moveId": "AERIAL_ACE", "uses": 19697}, {"moveId": "FLAMETHROWER", "uses": 18665}, {"moveId": "DAZZLING_GLEAM", "uses": 16758}]}, "moveset": ["CHARM", "ANCIENT_POWER", "FLAMETHROWER"], "score": 73.4}, {"speciesId": "zarude", "speciesName": "Zarude", "rating": 770, "matchups": [{"opponent": "mewtwo", "rating": 944}, {"opponent": "excadrill", "rating": 795}, {"opponent": "giratina_origin", "rating": 711}, {"opponent": "gyarados", "rating": 680}, {"opponent": "garcho<PERSON>", "rating": 668}], "counters": [{"opponent": "ho_oh", "rating": 242}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 266}, {"opponent": "dragonite", "rating": 268}, {"opponent": "dialga", "rating": 288}, {"opponent": "lugia", "rating": 473}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 49341}, {"moveId": "BITE", "uses": 27159}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37162}, {"moveId": "POWER_WHIP", "uses": 29005}, {"moveId": "ENERGY_BALL", "uses": 10565}]}, "moveset": ["VINE_WHIP", "DARK_PULSE", "POWER_WHIP"], "score": 73.3}, {"speciesId": "goodra", "speciesName": "<PERSON><PERSON>", "rating": 801, "matchups": [{"opponent": "excadrill", "rating": 698, "opRating": 301}, {"opponent": "swampert", "rating": 655, "opRating": 344}, {"opponent": "mewtwo", "rating": 642}, {"opponent": "giratina_origin", "rating": 599, "opRating": 400}, {"opponent": "grou<PERSON>", "rating": 596, "opRating": 403}], "counters": [{"opponent": "zacian_hero", "rating": 257}, {"opponent": "metagross", "rating": 284}, {"opponent": "dialga", "rating": 328}, {"opponent": "gyarados", "rating": 335}, {"opponent": "garcho<PERSON>", "rating": 429}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48753}, {"moveId": "WATER_GUN", "uses": 27747}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22410}, {"moveId": "MUDDY_WATER", "uses": 21396}, {"moveId": "DRACO_METEOR", "uses": 19651}, {"moveId": "SLUDGE_WAVE", "uses": 13006}]}, "moveset": ["DRAGON_BREATH", "MUDDY_WATER", "DRACO_METEOR"], "score": 71.8}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 672, "matchups": [{"opponent": "garcho<PERSON>", "rating": 865}, {"opponent": "giratina_origin", "rating": 765}, {"opponent": "excadrill", "rating": 617}, {"opponent": "lugia", "rating": 567}, {"opponent": "mewtwo", "rating": 543}], "counters": [{"opponent": "kyogre", "rating": 250}, {"opponent": "metagross", "rating": 281}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "dialga", "rating": 385}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47797}, {"moveId": "FROST_BREATH", "uses": 28703}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38906}, {"moveId": "ICY_WIND", "uses": 13524}, {"moveId": "LAST_RESORT", "uses": 9566}, {"moveId": "ICE_BEAM", "uses": 8742}, {"moveId": "WATER_PULSE", "uses": 5669}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 71.4}, {"speciesId": "golisopod", "speciesName": "Golisopod", "rating": 694, "matchups": [{"opponent": "kyogre", "rating": 698, "opRating": 301}, {"opponent": "swampert", "rating": 673, "opRating": 326}, {"opponent": "metagross", "rating": 618}, {"opponent": "garcho<PERSON>", "rating": 579}, {"opponent": "mewtwo", "rating": 570}], "counters": [{"opponent": "lugia", "rating": 316}, {"opponent": "dialga", "rating": 345}, {"opponent": "dragonite", "rating": 359}, {"opponent": "zacian_hero", "rating": 413}, {"opponent": "giratina_origin", "rating": 424}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 26027}, {"moveId": "FURY_CUTTER", "uses": 21253}, {"moveId": "WATERFALL", "uses": 18911}, {"moveId": "METAL_CLAW", "uses": 10307}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 38592}, {"moveId": "AERIAL_ACE", "uses": 19143}, {"moveId": "AQUA_JET", "uses": 18771}]}, "moveset": ["SHADOW_CLAW", "X_SCISSOR", "AERIAL_ACE"], "score": 70.7}, {"speciesId": "hydreigon", "speciesName": "Hydreigon", "rating": 810, "matchups": [{"opponent": "mewtwo", "rating": 933}, {"opponent": "garcho<PERSON>", "rating": 870}, {"opponent": "giratina_origin", "rating": 870}, {"opponent": "metagross", "rating": 738}, {"opponent": "gyarados", "rating": 600}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 75}, {"opponent": "dialga", "rating": 252}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "lugia", "rating": 378}, {"opponent": "grou<PERSON>", "rating": 461}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 50295}, {"moveId": "BITE", "uses": 26205}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 40277}, {"moveId": "DRAGON_PULSE", "uses": 15077}, {"moveId": "DARK_PULSE", "uses": 12390}, {"moveId": "FLASH_CANNON", "uses": 8711}]}, "moveset": ["DRAGON_BREATH", "BRUTAL_SWING", "FLASH_CANNON"], "score": 70.6}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 687, "matchups": [{"opponent": "garcho<PERSON>", "rating": 884}, {"opponent": "dragonite", "rating": 687}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 659, "opRating": 340}, {"opponent": "dialga", "rating": 532}, {"opponent": "zacian_hero", "rating": 525}], "counters": [{"opponent": "metagross", "rating": 235}, {"opponent": "mewtwo", "rating": 401}, {"opponent": "giratina_origin", "rating": 422}, {"opponent": "lugia", "rating": 428}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23852}, {"moveId": "FIRE_FANG", "uses": 19767}, {"moveId": "THUNDER_FANG", "uses": 19388}, {"moveId": "BITE", "uses": 13431}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 23533}, {"moveId": "BODY_SLAM", "uses": 20725}, {"moveId": "EARTH_POWER", "uses": 18017}, {"moveId": "EARTHQUAKE", "uses": 7804}, {"moveId": "STONE_EDGE", "uses": 6337}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 70.1}, {"speciesId": "primarina", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 670, "matchups": [{"opponent": "dragonite", "rating": 869}, {"opponent": "garcho<PERSON>", "rating": 845}, {"opponent": "zekrom", "rating": 845, "opRating": 154}, {"opponent": "gyarados", "rating": 619}, {"opponent": "dialga", "rating": 537}], "counters": [{"opponent": "metagross", "rating": 235}, {"opponent": "excadrill", "rating": 327}, {"opponent": "lugia", "rating": 335}, {"opponent": "zacian_hero", "rating": 416}, {"opponent": "mewtwo", "rating": 419}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 38520}, {"moveId": "WATERFALL", "uses": 37980}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 37947}, {"moveId": "HYDRO_PUMP", "uses": 19677}, {"moveId": "PSYCHIC", "uses": 18770}]}, "moveset": ["CHARM", "MOONBLAST", "HYDRO_PUMP"], "score": 69.7}, {"speciesId": "walrein_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 725, "matchups": [{"opponent": "garcho<PERSON>", "rating": 904}, {"opponent": "dragonite", "rating": 760}, {"opponent": "dialga", "rating": 648}, {"opponent": "zekrom", "rating": 648, "opRating": 351}, {"opponent": "excadrill", "rating": 586}], "counters": [{"opponent": "metagross", "rating": 209}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "giratina_origin", "rating": 368}, {"opponent": "gyarados", "rating": 471}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 38108}, {"moveId": "WATERFALL", "uses": 21138}, {"moveId": "FROST_BREATH", "uses": 17249}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 40042}, {"moveId": "EARTHQUAKE", "uses": 18402}, {"moveId": "WATER_PULSE", "uses": 9250}, {"moveId": "BLIZZARD", "uses": 8829}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 69.7}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 788, "matchups": [{"opponent": "excadrill", "rating": 927, "opRating": 72}, {"opponent": "metagross", "rating": 848}, {"opponent": "dialga", "rating": 684}, {"opponent": "swampert", "rating": 659, "opRating": 340}, {"opponent": "zacian_hero", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dragonite", "rating": 196}, {"opponent": "lugia", "rating": 288}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "giratina_origin", "rating": 344}, {"opponent": "mewtwo", "rating": 348}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 48551}, {"moveId": "WATER_GUN", "uses": 27949}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 31753}, {"moveId": "EARTHQUAKE", "uses": 14538}, {"moveId": "MUDDY_WATER", "uses": 12203}, {"moveId": "SURF", "uses": 10539}, {"moveId": "SLUDGE_WAVE", "uses": 7447}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 69.6}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 758, "matchups": [{"opponent": "excadrill", "rating": 890}, {"opponent": "metagross", "rating": 810}, {"opponent": "zacian_hero", "rating": 567}, {"opponent": "dialga", "rating": 564}, {"opponent": "gyarados", "rating": 562}], "counters": [{"opponent": "giratina_origin", "rating": 207}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "grou<PERSON>", "rating": 309}, {"opponent": "mewtwo", "rating": 348}, {"opponent": "lugia", "rating": 357}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 43206}, {"moveId": "CONFUSION", "uses": 33294}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 48094}, {"moveId": "PSYCHIC", "uses": 14167}, {"moveId": "FOCUS_BLAST", "uses": 8651}, {"moveId": "OVERHEAT", "uses": 5512}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 69.6}, {"speciesId": "sylveon", "speciesName": "Sylveon", "rating": 694, "matchups": [{"opponent": "dragonite", "rating": 822}, {"opponent": "garcho<PERSON>", "rating": 644}, {"opponent": "zacian_hero", "rating": 559}, {"opponent": "gyarados", "rating": 559}, {"opponent": "lugia", "rating": 507}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "excadrill", "rating": 281}, {"opponent": "mewtwo", "rating": 367}, {"opponent": "grou<PERSON>", "rating": 394}, {"opponent": "dialga", "rating": 461}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39624}, {"moveId": "CHARM", "uses": 36876}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 27112}, {"moveId": "PSYSHOCK", "uses": 18295}, {"moveId": "LAST_RESORT", "uses": 13215}, {"moveId": "DRAINING_KISS", "uses": 11004}, {"moveId": "DAZZLING_GLEAM", "uses": 6884}]}, "moveset": ["CHARM", "MOONBLAST", "PSYSHOCK"], "score": 68.9}, {"speciesId": "milotic", "speciesName": "Milo<PERSON>", "rating": 689, "matchups": [{"opponent": "garcho<PERSON>", "rating": 595}, {"opponent": "gyarados", "rating": 590}, {"opponent": "grou<PERSON>", "rating": 564, "opRating": 435}, {"opponent": "giratina_origin", "rating": 556}, {"opponent": "swampert", "rating": 536, "opRating": 463}], "counters": [{"opponent": "lugia", "rating": 266}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "metagross", "rating": 363}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 40701}, {"moveId": "WATERFALL", "uses": 35799}], "chargedMoves": [{"moveId": "SURF", "uses": 46232}, {"moveId": "BLIZZARD", "uses": 20006}, {"moveId": "HYPER_BEAM", "uses": 10146}]}, "moveset": ["DRAGON_TAIL", "SURF", "BLIZZARD"], "score": 68.6}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 795, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 776, "opRating": 223}, {"opponent": "zacian_hero", "rating": 713}, {"opponent": "dragonite", "rating": 703}, {"opponent": "gyarados", "rating": 614}, {"opponent": "mewtwo", "rating": 549}], "counters": [{"opponent": "grou<PERSON>", "rating": 198}, {"opponent": "metagross", "rating": 218}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "dialga", "rating": 317}, {"opponent": "garcho<PERSON>", "rating": 349}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 46834}, {"moveId": "CHARGE_BEAM", "uses": 29666}], "chargedMoves": [{"moveId": "DOOM_DESIRE", "uses": 45658}, {"moveId": "PSYCHIC", "uses": 20203}, {"moveId": "DAZZLING_GLEAM", "uses": 10555}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 68.2}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 706, "matchups": [{"opponent": "garcho<PERSON>", "rating": 873}, {"opponent": "giratina_origin", "rating": 719}, {"opponent": "grou<PERSON>", "rating": 682, "opRating": 317}, {"opponent": "dragonite", "rating": 626, "opRating": 373}, {"opponent": "gyarados", "rating": 525}], "counters": [{"opponent": "metagross", "rating": 281}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "lugia", "rating": 371}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 11830}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5035}, {"moveId": "CHARGE_BEAM", "uses": 4544}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4315}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4128}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4045}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4021}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3927}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3811}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3747}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3702}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3698}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3663}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3390}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3351}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3323}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3170}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3068}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 20563}, {"moveId": "BLIZZARD", "uses": 16049}, {"moveId": "ZAP_CANNON", "uses": 13209}, {"moveId": "RETURN", "uses": 12988}, {"moveId": "SOLAR_BEAM", "uses": 8601}, {"moveId": "HYPER_BEAM", "uses": 5018}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 68.2}, {"speciesId": "tapu_fini", "speciesName": "<PERSON><PERSON>", "rating": 731, "matchups": [{"opponent": "grou<PERSON>", "rating": 853}, {"opponent": "dragonite", "rating": 691}, {"opponent": "excadrill", "rating": 691}, {"opponent": "garcho<PERSON>", "rating": 621}, {"opponent": "gyarados", "rating": 621}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "metagross", "rating": 322}, {"opponent": "zacian_hero", "rating": 433}, {"opponent": "mewtwo", "rating": 476}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 8494}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5685}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5166}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4727}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4570}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4570}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4467}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4265}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4217}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4177}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4150}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4131}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3678}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3665}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3628}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3575}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3409}], "chargedMoves": [{"moveId": "SURF", "uses": 31416}, {"moveId": "MOONBLAST", "uses": 22736}, {"moveId": "ICE_BEAM", "uses": 17307}, {"moveId": "HYDRO_PUMP", "uses": 4985}]}, "moveset": ["WATER_GUN", "SURF", "MOONBLAST"], "score": 67.7}, {"speciesId": "beartic", "speciesName": "Bear<PERSON>", "rating": 673, "matchups": [{"opponent": "garcho<PERSON>", "rating": 914}, {"opponent": "dragonite", "rating": 734}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 579, "opRating": 420}, {"opponent": "zekrom", "rating": 564, "opRating": 435}, {"opponent": "excadrill", "rating": 536, "opRating": 463}], "counters": [{"opponent": "zacian_hero", "rating": 260}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "giratina_origin", "rating": 362}, {"opponent": "metagross", "rating": 372}, {"opponent": "dialga", "rating": 467}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52480}, {"moveId": "CHARM", "uses": 24020}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 36206}, {"moveId": "SURF", "uses": 27988}, {"moveId": "PLAY_ROUGH", "uses": 12288}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "SURF"], "score": 67.3}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 731, "matchups": [{"opponent": "metagross", "rating": 809}, {"opponent": "zacian_hero", "rating": 672}, {"opponent": "dialga", "rating": 620}, {"opponent": "mewtwo", "rating": 553}, {"opponent": "garcho<PERSON>", "rating": 510}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "gyarados", "rating": 327}, {"opponent": "dragonite", "rating": 327}, {"opponent": "excadrill", "rating": 344}, {"opponent": "lugia", "rating": 416}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39106}, {"moveId": "WING_ATTACK", "uses": 37394}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26934}, {"moveId": "OVERHEAT", "uses": 17005}, {"moveId": "ANCIENT_POWER", "uses": 16523}, {"moveId": "RETURN", "uses": 8183}, {"moveId": "FIRE_BLAST", "uses": 4883}, {"moveId": "HEAT_WAVE", "uses": 2996}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 67.3}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 713, "matchups": [{"opponent": "swampert", "rating": 716, "opRating": 283}, {"opponent": "garcho<PERSON>", "rating": 692}, {"opponent": "sylveon", "rating": 601, "opRating": 398}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 541, "opRating": 458}, {"opponent": "dragonite", "rating": 512, "opRating": 487}], "counters": [{"opponent": "metagross", "rating": 215}, {"opponent": "giratina_origin", "rating": 320}, {"opponent": "lugia", "rating": 330}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "dialga", "rating": 394}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 40785}, {"moveId": "CONFUSION", "uses": 35715}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21704}, {"moveId": "MOONBLAST", "uses": 21480}, {"moveId": "FUTURE_SIGHT", "uses": 18978}, {"moveId": "AURORA_BEAM", "uses": 14320}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 67.2}, {"speciesId": "haxorus", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 792, "matchups": [{"opponent": "excadrill", "rating": 876, "opRating": 123}, {"opponent": "kyogre", "rating": 753, "opRating": 246}, {"opponent": "metagross", "rating": 701, "opRating": 298}, {"opponent": "yveltal", "rating": 566, "opRating": 433}, {"opponent": "swampert", "rating": 542, "opRating": 457}], "counters": [{"opponent": "giratina_origin", "rating": 282}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "gyarados", "rating": 309}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 38293}, {"moveId": "DRAGON_TAIL", "uses": 38207}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 25655}, {"moveId": "NIGHT_SLASH", "uses": 22515}, {"moveId": "SURF", "uses": 17707}, {"moveId": "EARTHQUAKE", "uses": 10636}]}, "moveset": ["COUNTER", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 66.9}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 748, "matchups": [{"opponent": "zacian_hero", "rating": 828}, {"opponent": "mewtwo", "rating": 702}, {"opponent": "mewtwo_shadow", "rating": 660, "opRating": 339}, {"opponent": "sylveon", "rating": 620, "opRating": 379}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}], "counters": [{"opponent": "excadrill", "rating": 265}, {"opponent": "dragonite", "rating": 287}, {"opponent": "garcho<PERSON>", "rating": 291}, {"opponent": "dialga", "rating": 328}, {"opponent": "lugia", "rating": 354}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 39180}, {"moveId": "POISON_STING", "uses": 37320}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17992}, {"moveId": "DARK_PULSE", "uses": 17808}, {"moveId": "ICE_BEAM", "uses": 13689}, {"moveId": "SHADOW_BALL", "uses": 13482}, {"moveId": "SLUDGE_BOMB", "uses": 13433}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 66.8}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "dialga", "rating": 610}, {"opponent": "garcho<PERSON>", "rating": 596}, {"opponent": "zacian_hero", "rating": 593}, {"opponent": "dragonite", "rating": 591}, {"opponent": "giratina_origin", "rating": 502}], "counters": [{"opponent": "metagross", "rating": 200}, {"opponent": "swampert", "rating": 276}, {"opponent": "excadrill", "rating": 330}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "gyarados", "rating": 425}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23337}, {"moveId": "THUNDER_FANG", "uses": 19465}, {"moveId": "FIRE_FANG", "uses": 19406}, {"moveId": "BITE", "uses": 14253}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 22787}, {"moveId": "BODY_SLAM", "uses": 19792}, {"moveId": "EARTH_POWER", "uses": 17378}, {"moveId": "EARTHQUAKE", "uses": 7524}, {"moveId": "STONE_EDGE", "uses": 6070}, {"moveId": "RETURN", "uses": 2889}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 66.6}, {"speciesId": "walrein", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 705, "matchups": [{"opponent": "garcho<PERSON>", "rating": 895}, {"opponent": "dragonite", "rating": 800}, {"opponent": "excadrill", "rating": 637, "opRating": 362}, {"opponent": "lugia", "rating": 569}, {"opponent": "giratina_origin", "rating": 544}], "counters": [{"opponent": "metagross", "rating": 154}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "gyarados", "rating": 425}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 36636}, {"moveId": "WATERFALL", "uses": 21580}, {"moveId": "FROST_BREATH", "uses": 18276}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 35781}, {"moveId": "EARTHQUAKE", "uses": 16216}, {"moveId": "RETURN", "uses": 8628}, {"moveId": "WATER_PULSE", "uses": 8083}, {"moveId": "BLIZZARD", "uses": 7843}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 66.5}, {"speciesId": "kommo_o", "speciesName": "Kommo-o", "rating": 812, "matchups": [{"opponent": "garcho<PERSON>", "rating": 887}, {"opponent": "giratina_origin", "rating": 695}, {"opponent": "excadrill", "rating": 682, "opRating": 317}, {"opponent": "dragonite", "rating": 594, "opRating": 405}, {"opponent": "dialga", "rating": 557}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "zacian_hero", "rating": 242}, {"opponent": "lugia", "rating": 242}, {"opponent": "mewtwo", "rating": 361}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42105}, {"moveId": "POISON_JAB", "uses": 34395}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35292}, {"moveId": "DRAGON_CLAW", "uses": 29137}, {"moveId": "FLAMETHROWER", "uses": 12131}]}, "moveset": ["DRAGON_TAIL", "CLOSE_COMBAT", "DRAGON_CLAW"], "score": 66.3}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 816, "matchups": [{"opponent": "dialga", "rating": 829}, {"opponent": "gyarados", "rating": 779}, {"opponent": "lugia", "rating": 762}, {"opponent": "excadrill", "rating": 583, "opRating": 416}, {"opponent": "dragonite", "rating": 568, "opRating": 431}], "counters": [{"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "swampert", "rating": 199}, {"opponent": "metagross", "rating": 340}, {"opponent": "giratina_origin", "rating": 426}, {"opponent": "mewtwo", "rating": 447}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 76500}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 26421}, {"moveId": "ROCK_SLIDE", "uses": 21507}, {"moveId": "THUNDERBOLT", "uses": 12342}, {"moveId": "FLASH_CANNON", "uses": 9908}, {"moveId": "HYPER_BEAM", "uses": 6477}]}, "moveset": ["THUNDER_SHOCK", "SUPER_POWER", "ROCK_SLIDE"], "score": 66.3}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 784, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "gyarados", "rating": 768}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 717, "opRating": 282}, {"opponent": "lugia", "rating": 677}, {"opponent": "mewtwo", "rating": 604}], "counters": [{"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "dialga", "rating": 355}, {"opponent": "excadrill", "rating": 369}, {"opponent": "giratina_origin", "rating": 408}, {"opponent": "zacian_hero", "rating": 421}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41517}, {"moveId": "THUNDER_SHOCK", "uses": 34983}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 36350}, {"moveId": "SHADOW_BALL", "uses": 19381}, {"moveId": "RETURN", "uses": 8531}, {"moveId": "THUNDERBOLT", "uses": 6654}, {"moveId": "THUNDER", "uses": 5808}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 66.3}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 698, "matchups": [{"opponent": "swampert", "rating": 778, "opRating": 221}, {"opponent": "metagross", "rating": 772}, {"opponent": "excadrill", "rating": 676, "opRating": 323}, {"opponent": "grou<PERSON>", "rating": 615, "opRating": 384}, {"opponent": "zacian_hero", "rating": 556}], "counters": [{"opponent": "dragonite", "rating": 207}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "lugia", "rating": 378}, {"opponent": "dialga", "rating": 410}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 50303}, {"moveId": "SUCKER_PUNCH", "uses": 26197}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 29960}, {"moveId": "SEED_BOMB", "uses": 24023}, {"moveId": "FOUL_PLAY", "uses": 22536}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SEED_BOMB"], "score": 66.3}, {"speciesId": "raikou_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 793, "matchups": [{"opponent": "metagross", "rating": 836}, {"opponent": "zacian_hero", "rating": 814}, {"opponent": "gyarados", "rating": 766}, {"opponent": "lugia", "rating": 704}, {"opponent": "mewtwo", "rating": 521}], "counters": [{"opponent": "grou<PERSON>", "rating": 84}, {"opponent": "excadrill", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "dialga", "rating": 418}, {"opponent": "giratina_origin", "rating": 426}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41884}, {"moveId": "THUNDER_SHOCK", "uses": 34616}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 40233}, {"moveId": "SHADOW_BALL", "uses": 22541}, {"moveId": "THUNDERBOLT", "uses": 7196}, {"moveId": "THUNDER", "uses": 6356}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 65.3}, {"speciesId": "z<PERSON><PERSON><PERSON>_hero", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hero)", "rating": 789, "matchups": [{"opponent": "excadrill", "rating": 919}, {"opponent": "snorlax", "rating": 705, "opRating": 294}, {"opponent": "dialga", "rating": 673}, {"opponent": "yveltal", "rating": 658, "opRating": 341}, {"opponent": "giratina_origin", "rating": 578}], "counters": [{"opponent": "zacian_hero", "rating": 179}, {"opponent": "dragonite", "rating": 287}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "metagross", "rating": 430}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 26737}, {"moveId": "QUICK_ATTACK", "uses": 25409}, {"moveId": "ICE_FANG", "uses": 14902}, {"moveId": "METAL_CLAW", "uses": 9488}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35182}, {"moveId": "CRUNCH", "uses": 19518}, {"moveId": "MOONBLAST", "uses": 13094}, {"moveId": "IRON_HEAD", "uses": 8855}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 65.3}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 723, "matchups": [{"opponent": "metagross", "rating": 803}, {"opponent": "grou<PERSON>", "rating": 589, "opRating": 410}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 580, "opRating": 419}, {"opponent": "dialga", "rating": 520}, {"opponent": "mewtwo", "rating": 502}], "counters": [{"opponent": "excadrill", "rating": 141}, {"opponent": "lugia", "rating": 300}, {"opponent": "gyarados", "rating": 350}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "garcho<PERSON>", "rating": 476}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16896}, {"moveId": "DRAGON_BREATH", "uses": 16478}, {"moveId": "EMBER", "uses": 15616}, {"moveId": "WING_ATTACK", "uses": 15419}, {"moveId": "AIR_SLASH", "uses": 12129}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 29951}, {"moveId": "DRAGON_CLAW", "uses": 22481}, {"moveId": "RETURN", "uses": 6966}, {"moveId": "FLAMETHROWER", "uses": 6803}, {"moveId": "OVERHEAT", "uses": 6383}, {"moveId": "FIRE_BLAST", "uses": 3587}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 64.9}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 784, "matchups": [{"opponent": "dialga", "rating": 726}, {"opponent": "lugia", "rating": 715}, {"opponent": "zacian_hero", "rating": 683}, {"opponent": "dragonite", "rating": 555, "opRating": 444}, {"opponent": "mewtwo", "rating": 529}], "counters": [{"opponent": "excadrill", "rating": 183}, {"opponent": "grou<PERSON>", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "gyarados", "rating": 378}, {"opponent": "giratina_origin", "rating": 394}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 67141}, {"moveId": "ZEN_HEADBUTT", "uses": 9359}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 29562}, {"moveId": "PSYCHIC", "uses": 17033}, {"moveId": "EARTHQUAKE", "uses": 16341}, {"moveId": "RETURN", "uses": 8923}, {"moveId": "FLASH_CANNON", "uses": 4750}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 64.8}, {"speciesId": "mewtwo_armored", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Armored)", "rating": 762, "matchups": [{"opponent": "excadrill", "rating": 729, "opRating": 270}, {"opponent": "swampert", "rating": 585, "opRating": 414}, {"opponent": "zacian_hero", "rating": 565, "opRating": 434}, {"opponent": "gyarados", "rating": 528, "opRating": 471}, {"opponent": "grou<PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "giratina_origin", "rating": 183}, {"opponent": "lugia", "rating": 261}, {"opponent": "metagross", "rating": 270}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "dialga", "rating": 377}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66224}, {"moveId": "IRON_TAIL", "uses": 10276}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 25579}, {"moveId": "ROCK_SLIDE", "uses": 18840}, {"moveId": "DYNAMIC_PUNCH", "uses": 15517}, {"moveId": "EARTHQUAKE", "uses": 11433}, {"moveId": "FUTURE_SIGHT", "uses": 5243}]}, "moveset": ["CONFUSION", "PSYSTRIKE", "DYNAMIC_PUNCH"], "score": 64.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Ray<PERSON><PERSON>", "rating": 738, "matchups": [{"opponent": "garcho<PERSON>", "rating": 929}, {"opponent": "ho_oh", "rating": 853, "opRating": 146}, {"opponent": "grou<PERSON>", "rating": 730, "opRating": 269}, {"opponent": "swampert", "rating": 662, "opRating": 337}, {"opponent": "mewtwo", "rating": 547}], "counters": [{"opponent": "zacian_hero", "rating": 283}, {"opponent": "dialga", "rating": 307}, {"opponent": "metagross", "rating": 322}, {"opponent": "gyarados", "rating": 373}, {"opponent": "lugia", "rating": 378}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 46888}, {"moveId": "AIR_SLASH", "uses": 29612}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23882}, {"moveId": "ANCIENT_POWER", "uses": 19218}, {"moveId": "AERIAL_ACE", "uses": 17519}, {"moveId": "HURRICANE", "uses": 15937}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "ANCIENT_POWER"], "score": 64.6}, {"speciesId": "aurorus", "speciesName": "Au<PERSON><PERSON>", "rating": 673, "matchups": [{"opponent": "garcho<PERSON>", "rating": 844}, {"opponent": "gyarados", "rating": 802}, {"opponent": "dragonite", "rating": 795}, {"opponent": "lugia", "rating": 651}, {"opponent": "giratina_origin", "rating": 519}], "counters": [{"opponent": "zacian_hero", "rating": 234}, {"opponent": "metagross", "rating": 250}, {"opponent": "swampert", "rating": 253}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37802}, {"moveId": "ROCK_THROW", "uses": 20908}, {"moveId": "FROST_BREATH", "uses": 17806}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 38367}, {"moveId": "ANCIENT_POWER", "uses": 16823}, {"moveId": "THUNDERBOLT", "uses": 10716}, {"moveId": "BLIZZARD", "uses": 5853}, {"moveId": "HYPER_BEAM", "uses": 4817}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 64.5}, {"speciesId": "suicune", "speciesName": "Suicune", "rating": 676, "matchups": [{"opponent": "excadrill", "rating": 731}, {"opponent": "garcho<PERSON>", "rating": 626}, {"opponent": "grou<PERSON>", "rating": 597, "opRating": 402}, {"opponent": "giratina_origin", "rating": 587}, {"opponent": "metagross", "rating": 509}], "counters": [{"opponent": "mewtwo", "rating": 309}, {"opponent": "dialga", "rating": 377}, {"opponent": "lugia", "rating": 388}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "gyarados", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7180}, {"moveId": "ICE_FANG", "uses": 6327}, {"moveId": "EXTRASENSORY", "uses": 4683}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4567}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4492}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4129}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4010}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3947}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3864}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3684}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3644}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3572}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3487}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3445}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3151}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3138}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3135}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3013}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2905}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26455}, {"moveId": "HYDRO_PUMP", "uses": 15224}, {"moveId": "RETURN", "uses": 12644}, {"moveId": "BUBBLE_BEAM", "uses": 11306}, {"moveId": "WATER_PULSE", "uses": 10904}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 64.5}, {"speciesId": "zap<PERSON>_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 824, "matchups": [{"opponent": "garcho<PERSON>", "rating": 825}, {"opponent": "zacian_hero", "rating": 822}, {"opponent": "excadrill", "rating": 768}, {"opponent": "dialga", "rating": 634}, {"opponent": "gyarados", "rating": 596}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 95}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "lugia", "rating": 233}, {"opponent": "metagross", "rating": 366}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 76500}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34243}, {"moveId": "BRAVE_BIRD", "uses": 28894}, {"moveId": "ANCIENT_POWER", "uses": 13368}]}, "moveset": ["COUNTER", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 64.4}, {"speciesId": "charizard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 725, "matchups": [{"opponent": "garcho<PERSON>", "rating": 889}, {"opponent": "metagross", "rating": 764}, {"opponent": "dragonite", "rating": 622}, {"opponent": "zacian_hero", "rating": 574}, {"opponent": "dialga", "rating": 532}], "counters": [{"opponent": "excadrill", "rating": 188}, {"opponent": "swampert", "rating": 191}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 371}, {"opponent": "giratina_origin", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17195}, {"moveId": "DRAGON_BREATH", "uses": 16285}, {"moveId": "WING_ATTACK", "uses": 15743}, {"moveId": "EMBER", "uses": 15373}, {"moveId": "AIR_SLASH", "uses": 11897}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 32755}, {"moveId": "DRAGON_CLAW", "uses": 25115}, {"moveId": "FLAMETHROWER", "uses": 7494}, {"moveId": "OVERHEAT", "uses": 7114}, {"moveId": "FIRE_BLAST", "uses": 4099}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 63.8}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 639, "matchups": [{"opponent": "gliscor_shadow", "rating": 908, "opRating": 91}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 638, "opRating": 361}, {"opponent": "garcho<PERSON>", "rating": 634}, {"opponent": "grou<PERSON>", "rating": 577, "opRating": 422}, {"opponent": "zacian_hero", "rating": 520}], "counters": [{"opponent": "dialga", "rating": 334}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "metagross", "rating": 363}, {"opponent": "lugia", "rating": 369}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 30417}, {"moveId": "WATER_GUN", "uses": 25996}, {"moveId": "FROST_BREATH", "uses": 20068}], "chargedMoves": [{"moveId": "SURF", "uses": 25535}, {"moveId": "ICE_BEAM", "uses": 18999}, {"moveId": "SKULL_BASH", "uses": 7696}, {"moveId": "DRAGON_PULSE", "uses": 7155}, {"moveId": "BLIZZARD", "uses": 6645}, {"moveId": "RETURN", "uses": 6502}, {"moveId": "HYDRO_PUMP", "uses": 4131}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 63.8}, {"speciesId": "sci<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 717, "matchups": [{"opponent": "nihilego", "rating": 856, "opRating": 143}, {"opponent": "zacian_hero", "rating": 843}, {"opponent": "metagross", "rating": 773}, {"opponent": "sylveon", "rating": 719, "opRating": 280}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 694, "opRating": 305}], "counters": [{"opponent": "giratina_origin", "rating": 324}, {"opponent": "dragonite", "rating": 348}, {"opponent": "garcho<PERSON>", "rating": 387}, {"opponent": "dialga", "rating": 415}, {"opponent": "mewtwo", "rating": 440}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38472}, {"moveId": "BULLET_PUNCH", "uses": 38028}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 34176}, {"moveId": "X_SCISSOR", "uses": 25197}, {"moveId": "IRON_HEAD", "uses": 17051}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "IRON_HEAD"], "score": 63.8}, {"speciesId": "lap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 654, "matchups": [{"opponent": "excadrill", "rating": 591, "opRating": 408}, {"opponent": "swampert", "rating": 587, "opRating": 412}, {"opponent": "garcho<PERSON>", "rating": 557}, {"opponent": "giratina_origin", "rating": 526}, {"opponent": "grou<PERSON>", "rating": 518, "opRating": 481}], "counters": [{"opponent": "metagross", "rating": 252}, {"opponent": "zacian_hero", "rating": 300}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "dialga", "rating": 396}, {"opponent": "gyarados", "rating": 425}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 31261}, {"moveId": "WATER_GUN", "uses": 25108}, {"moveId": "FROST_BREATH", "uses": 20200}], "chargedMoves": [{"moveId": "SURF", "uses": 27769}, {"moveId": "ICE_BEAM", "uses": 20569}, {"moveId": "SKULL_BASH", "uses": 8560}, {"moveId": "DRAGON_PULSE", "uses": 7895}, {"moveId": "BLIZZARD", "uses": 7115}, {"moveId": "HYDRO_PUMP", "uses": 4377}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 63.7}, {"speciesId": "suicune_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 690, "matchups": [{"opponent": "metagross", "rating": 649}, {"opponent": "mewtwo", "rating": 582}, {"opponent": "garcho<PERSON>", "rating": 554}, {"opponent": "zacian_hero", "rating": 547}, {"opponent": "giratina_origin", "rating": 504}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "gyarados", "rating": 317}, {"opponent": "yveltal", "rating": 335}, {"opponent": "zekrom", "rating": 375}, {"opponent": "lugia", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7348}, {"moveId": "ICE_FANG", "uses": 6261}, {"moveId": "EXTRASENSORY", "uses": 4743}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4628}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4589}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4098}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3986}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3934}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3874}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3753}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3704}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3561}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3511}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3436}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3179}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3178}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3162}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3004}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2960}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31532}, {"moveId": "HYDRO_PUMP", "uses": 18281}, {"moveId": "BUBBLE_BEAM", "uses": 13562}, {"moveId": "WATER_PULSE", "uses": 13015}, {"moveId": "FRUSTRATION", "uses": 4}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 63.7}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 740, "matchups": [{"opponent": "yveltal", "rating": 873, "opRating": 126}, {"opponent": "metagross", "rating": 791}, {"opponent": "gyarados", "rating": 750}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 650, "opRating": 350}, {"opponent": "lugia", "rating": 576, "opRating": 423}], "counters": [{"opponent": "garcho<PERSON>", "rating": 176}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "mewtwo", "rating": 367}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "dialga", "rating": 383}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58301}, {"moveId": "ASTONISH", "uses": 18199}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25636}, {"moveId": "THUNDER_PUNCH", "uses": 23883}, {"moveId": "BRICK_BREAK", "uses": 19321}, {"moveId": "THUNDER", "uses": 7717}]}, "moveset": ["THUNDER_SHOCK", "CRUNCH", "THUNDER"], "score": 63.4}, {"speciesId": "regice", "speciesName": "Regice", "rating": 713, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 677, "opRating": 322}, {"opponent": "gyarados", "rating": 627, "opRating": 372}, {"opponent": "kyogre", "rating": 578, "opRating": 421}, {"opponent": "swampert", "rating": 566, "opRating": 433}, {"opponent": "excadrill", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dragonite", "rating": 247}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "dialga", "rating": 388}, {"opponent": "garcho<PERSON>", "rating": 434}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42376}, {"moveId": "FROST_BREATH", "uses": 24607}, {"moveId": "ROCK_SMASH", "uses": 9529}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 25650}, {"moveId": "EARTHQUAKE", "uses": 19604}, {"moveId": "THUNDER", "uses": 16944}, {"moveId": "FOCUS_BLAST", "uses": 14222}]}, "moveset": ["LOCK_ON", "THUNDER", "EARTHQUAKE"], "score": 62.9}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 754, "matchups": [{"opponent": "yveltal", "rating": 868, "opRating": 131}, {"opponent": "gyarados", "rating": 789}, {"opponent": "metagross", "rating": 740}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 689, "opRating": 310}, {"opponent": "lugia", "rating": 631, "opRating": 368}], "counters": [{"opponent": "excadrill", "rating": 197}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "dialga", "rating": 342}, {"opponent": "giratina_origin", "rating": 348}, {"opponent": "mewtwo", "rating": 369}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 67011}, {"moveId": "LOW_KICK", "uses": 9489}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26489}, {"moveId": "ICE_PUNCH", "uses": 17903}, {"moveId": "THUNDER_PUNCH", "uses": 12999}, {"moveId": "FLAMETHROWER", "uses": 9810}, {"moveId": "RETURN", "uses": 5123}, {"moveId": "THUNDER", "uses": 4191}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 62.8}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 745, "matchups": [{"opponent": "swampert", "rating": 909, "opRating": 90}, {"opponent": "swampert_shadow", "rating": 909, "opRating": 90}, {"opponent": "dialga", "rating": 639}, {"opponent": "excadrill", "rating": 603, "opRating": 396}, {"opponent": "zekrom", "rating": 542, "opRating": 457}], "counters": [{"opponent": "lugia", "rating": 190}, {"opponent": "giratina_origin", "rating": 264}, {"opponent": "dragonite", "rating": 300}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 450}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42891}, {"moveId": "CHARM", "uses": 23005}, {"moveId": "LOW_KICK", "uses": 10556}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29158}, {"moveId": "LEAF_BLADE", "uses": 23879}, {"moveId": "SYNCHRONOISE", "uses": 12178}, {"moveId": "RETURN", "uses": 5618}, {"moveId": "PSYCHIC", "uses": 5512}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 62.8}, {"speciesId": "gardevoir", "speciesName": "Gardevoir", "rating": 630, "matchups": [{"opponent": "yveltal", "rating": 866, "opRating": 133}, {"opponent": "palkia", "rating": 860, "opRating": 139}, {"opponent": "dragonite", "rating": 847}, {"opponent": "zekrom", "rating": 847, "opRating": 152}, {"opponent": "garcho<PERSON>", "rating": 827}], "counters": [{"opponent": "metagross", "rating": 264}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "lugia", "rating": 335}, {"opponent": "giratina_origin", "rating": 400}, {"opponent": "dialga", "rating": 421}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30257}, {"moveId": "CHARM", "uses": 27821}, {"moveId": "CHARGE_BEAM", "uses": 18484}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 23031}, {"moveId": "SYNCHRONOISE", "uses": 20240}, {"moveId": "DAZZLING_GLEAM", "uses": 14317}, {"moveId": "RETURN", "uses": 9641}, {"moveId": "PSYCHIC", "uses": 9174}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 62.5}, {"speciesId": "virizion", "speciesName": "Virizion", "rating": 750, "matchups": [{"opponent": "swampert", "rating": 848, "opRating": 151}, {"opponent": "excadrill", "rating": 821}, {"opponent": "dialga", "rating": 720}, {"opponent": "garcho<PERSON>", "rating": 646}, {"opponent": "gyarados", "rating": 638}], "counters": [{"opponent": "lugia", "rating": 145}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "dragonite", "rating": 263}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "zacian_hero", "rating": 384}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 41019}, {"moveId": "QUICK_ATTACK", "uses": 32541}, {"moveId": "ZEN_HEADBUTT", "uses": 2894}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26368}, {"moveId": "SACRED_SWORD", "uses": 20090}, {"moveId": "CLOSE_COMBAT", "uses": 18249}, {"moveId": "STONE_EDGE", "uses": 11895}]}, "moveset": ["DOUBLE_KICK", "LEAF_BLADE", "SACRED_SWORD"], "score": 62.5}, {"speciesId": "mr_rime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 600, "matchups": [{"opponent": "garcho<PERSON>", "rating": 869}, {"opponent": "dragonite", "rating": 677, "opRating": 322}, {"opponent": "zekrom", "rating": 633, "opRating": 366}, {"opponent": "zacian_hero", "rating": 575, "opRating": 424}, {"opponent": "lugia", "rating": 561, "opRating": 438}], "counters": [{"opponent": "metagross", "rating": 308}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "gyarados", "rating": 324}, {"opponent": "giratina_origin", "rating": 364}, {"opponent": "dialga", "rating": 372}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38570}, {"moveId": "CONFUSION", "uses": 31218}, {"moveId": "ZEN_HEADBUTT", "uses": 6706}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 30900}, {"moveId": "ICE_PUNCH", "uses": 26751}, {"moveId": "PSYCHIC", "uses": 15574}, {"moveId": "PSYBEAM", "uses": 3094}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ICE_PUNCH"], "score": 62.4}, {"speciesId": "vaporeon", "speciesName": "Vaporeon", "rating": 708, "matchups": [{"opponent": "excadrill", "rating": 720, "opRating": 279}, {"opponent": "metagross", "rating": 669}, {"opponent": "swampert", "rating": 644, "opRating": 355}, {"opponent": "zacian_hero", "rating": 614, "opRating": 385}, {"opponent": "grou<PERSON>", "rating": 557, "opRating": 442}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "dialga", "rating": 312}, {"opponent": "lugia", "rating": 326}, {"opponent": "mewtwo", "rating": 359}, {"opponent": "garcho<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 76500}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38170}, {"moveId": "LAST_RESORT", "uses": 14534}, {"moveId": "SCALD", "uses": 13516}, {"moveId": "HYDRO_PUMP", "uses": 6023}, {"moveId": "WATER_PULSE", "uses": 4344}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "LAST_RESORT"], "score": 62.2}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 703, "matchups": [{"opponent": "swampert", "rating": 843, "opRating": 156}, {"opponent": "excadrill", "rating": 756, "opRating": 243}, {"opponent": "gyarados", "rating": 611, "opRating": 388}, {"opponent": "garcho<PERSON>", "rating": 574}, {"opponent": "zacian_hero", "rating": 549, "opRating": 450}], "counters": [{"opponent": "metagross", "rating": 244}, {"opponent": "dialga", "rating": 252}, {"opponent": "dragonite", "rating": 284}, {"opponent": "giratina_origin", "rating": 318}, {"opponent": "mewtwo", "rating": 406}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44372}, {"moveId": "INFESTATION", "uses": 32128}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22318}, {"moveId": "ROCK_SLIDE", "uses": 21911}, {"moveId": "SLUDGE_BOMB", "uses": 11545}, {"moveId": "RETURN", "uses": 8111}, {"moveId": "ANCIENT_POWER", "uses": 7841}, {"moveId": "SOLAR_BEAM", "uses": 4753}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 61.8}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 724, "matchups": [{"opponent": "swampert", "rating": 820, "opRating": 179}, {"opponent": "excadrill", "rating": 716, "opRating": 283}, {"opponent": "grou<PERSON>", "rating": 684, "opRating": 315}, {"opponent": "gyarados", "rating": 524, "opRating": 475}, {"opponent": "garcho<PERSON>", "rating": 514}], "counters": [{"opponent": "giratina_origin", "rating": 227}, {"opponent": "metagross", "rating": 267}, {"opponent": "dialga", "rating": 285}, {"opponent": "dragonite", "rating": 327}, {"opponent": "mewtwo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44507}, {"moveId": "INFESTATION", "uses": 31993}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 24866}, {"moveId": "ROCK_SLIDE", "uses": 24486}, {"moveId": "SLUDGE_BOMB", "uses": 13074}, {"moveId": "ANCIENT_POWER", "uses": 8879}, {"moveId": "SOLAR_BEAM", "uses": 5344}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 61.8}, {"speciesId": "gengar", "speciesName": "Gengar", "rating": 730, "matchups": [{"opponent": "excadrill", "rating": 750}, {"opponent": "metagross", "rating": 714}, {"opponent": "giratina_origin", "rating": 609}, {"opponent": "lugia", "rating": 609}, {"opponent": "zacian_hero", "rating": 584}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "grou<PERSON>", "rating": 279}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "garcho<PERSON>", "rating": 443}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 28936}, {"moveId": "HEX", "uses": 18806}, {"moveId": "LICK", "uses": 14995}, {"moveId": "SUCKER_PUNCH", "uses": 13838}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16631}, {"moveId": "SHADOW_PUNCH", "uses": 16496}, {"moveId": "SLUDGE_BOMB", "uses": 11755}, {"moveId": "DARK_PULSE", "uses": 10841}, {"moveId": "FOCUS_BLAST", "uses": 8834}, {"moveId": "PSYCHIC", "uses": 8084}, {"moveId": "SLUDGE_WAVE", "uses": 3962}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SHADOW_PUNCH"], "score": 61.7}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 757, "matchups": [{"opponent": "swampert", "rating": 909}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "dialga", "rating": 568}, {"opponent": "excadrill", "rating": 551, "opRating": 448}, {"opponent": "zekrom", "rating": 529, "opRating": 470}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "lugia", "rating": 200}, {"opponent": "giratina_origin", "rating": 314}, {"opponent": "dragonite", "rating": 359}, {"opponent": "metagross", "rating": 430}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44736}, {"moveId": "CHARM", "uses": 21977}, {"moveId": "LOW_KICK", "uses": 9787}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31138}, {"moveId": "LEAF_BLADE", "uses": 25796}, {"moveId": "SYNCHRONOISE", "uses": 13430}, {"moveId": "PSYCHIC", "uses": 6189}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 61.5}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 686, "matchups": [{"opponent": "zarude", "rating": 926, "opRating": 73}, {"opponent": "hydreigon", "rating": 757, "opRating": 242}, {"opponent": "latios_shadow", "rating": 691, "opRating": 308}, {"opponent": "mewtwo", "rating": 656}, {"opponent": "metagross", "rating": 525, "opRating": 474}], "counters": [{"opponent": "garcho<PERSON>", "rating": 340}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "gyarados", "rating": 373}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38893}, {"moveId": "BULLET_PUNCH", "uses": 37607}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30570}, {"moveId": "X_SCISSOR", "uses": 22599}, {"moveId": "IRON_HEAD", "uses": 15052}, {"moveId": "RETURN", "uses": 8424}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 61.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 728, "matchups": [{"opponent": "metagross", "rating": 752}, {"opponent": "excadrill", "rating": 639, "opRating": 360}, {"opponent": "dialga", "rating": 634}, {"opponent": "swampert", "rating": 599, "opRating": 400}, {"opponent": "zacian_hero", "rating": 586, "opRating": 413}], "counters": [{"opponent": "giratina_origin", "rating": 133}, {"opponent": "dragonite", "rating": 242}, {"opponent": "gyarados", "rating": 252}, {"opponent": "lugia", "rating": 297}, {"opponent": "mewtwo", "rating": 442}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 27062}, {"moveId": "MUD_SLAP", "uses": 18412}, {"moveId": "CHARM", "uses": 15831}, {"moveId": "TACKLE", "uses": 15189}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30806}, {"moveId": "EARTHQUAKE", "uses": 22411}, {"moveId": "PLAY_ROUGH", "uses": 11854}, {"moveId": "HEAVY_SLAM", "uses": 11564}]}, "moveset": ["COUNTER", "BODY_SLAM", "EARTHQUAKE"], "score": 60.7}, {"speciesId": "land<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 814, "matchups": [{"opponent": "excadrill", "rating": 948}, {"opponent": "metagross", "rating": 725}, {"opponent": "dialga", "rating": 698}, {"opponent": "swampert", "rating": 614, "opRating": 385}, {"opponent": "lugia", "rating": 527, "opRating": 472}], "counters": [{"opponent": "gyarados", "rating": 69}, {"opponent": "dragonite", "rating": 71}, {"opponent": "giratina_origin", "rating": 316}, {"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "mewtwo", "rating": 421}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51646}, {"moveId": "EXTRASENSORY", "uses": 24854}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24359}, {"moveId": "STONE_EDGE", "uses": 22017}, {"moveId": "EARTHQUAKE", "uses": 18634}, {"moveId": "BULLDOZE", "uses": 11395}]}, "moveset": ["MUD_SHOT", "SUPER_POWER", "STONE_EDGE"], "score": 60.5}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 722, "matchups": [{"opponent": "giratina_origin", "rating": 798}, {"opponent": "metagross", "rating": 744}, {"opponent": "giratina_altered", "rating": 666, "opRating": 333}, {"opponent": "excadrill", "rating": 653, "opRating": 346}, {"opponent": "dialga", "rating": 584}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "dragonite", "rating": 287}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "lugia", "rating": 383}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 44931}, {"moveId": "TACKLE", "uses": 23961}, {"moveId": "LOW_KICK", "uses": 7543}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34083}, {"moveId": "STOMP", "uses": 21992}, {"moveId": "PAYBACK", "uses": 18051}, {"moveId": "DRAIN_PUNCH", "uses": 2383}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "PAYBACK"], "score": 60.3}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 721, "matchups": [{"opponent": "mewtwo", "rating": 802}, {"opponent": "metagross", "rating": 786}, {"opponent": "giratina_origin", "rating": 617}, {"opponent": "excadrill", "rating": 617, "opRating": 382}, {"opponent": "dialga", "rating": 566}], "counters": [{"opponent": "zacian_hero", "rating": 274}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "dragonite", "rating": 364}, {"opponent": "lugia", "rating": 380}, {"opponent": "gyarados", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45989}, {"moveId": "FEINT_ATTACK", "uses": 30511}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 26754}, {"moveId": "SHADOW_BALL", "uses": 20461}, {"moveId": "SLUDGE_BOMB", "uses": 15038}, {"moveId": "FOCUS_BLAST", "uses": 14140}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 60.1}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 676, "matchups": [{"opponent": "raikou_shadow", "rating": 850, "opRating": 149}, {"opponent": "excadrill", "rating": 753, "opRating": 246}, {"opponent": "cobalion", "rating": 701, "opRating": 298}, {"opponent": "reshiram", "rating": 667, "opRating": 332}, {"opponent": "dialga", "rating": 582}], "counters": [{"opponent": "dragonite", "rating": 316}, {"opponent": "gyarados", "rating": 327}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "garcho<PERSON>", "rating": 403}, {"opponent": "mewtwo", "rating": 424}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38928}, {"moveId": "WING_ATTACK", "uses": 37572}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 27748}, {"moveId": "EARTHQUAKE", "uses": 17198}, {"moveId": "AERIAL_ACE", "uses": 15281}, {"moveId": "SAND_TOMB", "uses": 8366}, {"moveId": "RETURN", "uses": 7863}]}, "moveset": ["WING_ATTACK", "NIGHT_SLASH", "EARTHQUAKE"], "score": 60.1}, {"speciesId": "pinsir_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 734, "matchups": [{"opponent": "excadrill", "rating": 916, "opRating": 83}, {"opponent": "metagross", "rating": 647, "opRating": 352}, {"opponent": "swampert", "rating": 624, "opRating": 375}, {"opponent": "snorlax", "rating": 536, "opRating": 463}, {"opponent": "dialga", "rating": 533}], "counters": [{"opponent": "dragonite", "rating": 210}, {"opponent": "giratina_origin", "rating": 233}, {"opponent": "gyarados", "rating": 296}, {"opponent": "garcho<PERSON>", "rating": 401}, {"opponent": "mewtwo", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40131}, {"moveId": "BUG_BITE", "uses": 26811}, {"moveId": "ROCK_SMASH", "uses": 9569}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25015}, {"moveId": "X_SCISSOR", "uses": 22159}, {"moveId": "SUPER_POWER", "uses": 18627}, {"moveId": "VICE_GRIP", "uses": 7704}, {"moveId": "SUBMISSION", "uses": 3015}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 59.8}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 704, "matchups": [{"opponent": "gyarados", "rating": 755}, {"opponent": "lugia", "rating": 703}, {"opponent": "metagross", "rating": 604}, {"opponent": "mewtwo", "rating": 575}, {"opponent": "dialga", "rating": 546}], "counters": [{"opponent": "grou<PERSON>", "rating": 70}, {"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "swampert", "rating": 293}, {"opponent": "dragonite", "rating": 303}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42514}, {"moveId": "METAL_CLAW", "uses": 21807}, {"moveId": "ROCK_SMASH", "uses": 12178}], "chargedMoves": [{"moveId": "FLASH_CANNON", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "ZAP_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 59.8}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 712, "matchups": [{"opponent": "metagross", "rating": 809}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 735, "opRating": 264}, {"opponent": "dialga", "rating": 621}, {"opponent": "zacian_hero", "rating": 569}, {"opponent": "mewtwo", "rating": 531}], "counters": [{"opponent": "giratina_origin", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "dragonite", "rating": 276}, {"opponent": "gyarados", "rating": 283}, {"opponent": "lugia", "rating": 352}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46818}, {"moveId": "FIRE_FANG", "uses": 29682}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25488}, {"moveId": "OVERHEAT", "uses": 19689}, {"moveId": "IRON_HEAD", "uses": 14848}, {"moveId": "FLAMETHROWER", "uses": 10559}, {"moveId": "FIRE_BLAST", "uses": 5914}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 59.1}, {"speciesId": "machamp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 782, "matchups": [{"opponent": "yveltal", "rating": 903, "opRating": 96}, {"opponent": "excadrill", "rating": 889}, {"opponent": "ho_oh", "rating": 771, "opRating": 228}, {"opponent": "dialga", "rating": 677}, {"opponent": "metagross", "rating": 658}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 243}, {"opponent": "lugia", "rating": 280}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 389}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29980}, {"moveId": "KARATE_CHOP", "uses": 27818}, {"moveId": "BULLET_PUNCH", "uses": 18682}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 21204}, {"moveId": "CROSS_CHOP", "uses": 14602}, {"moveId": "ROCK_SLIDE", "uses": 12553}, {"moveId": "PAYBACK", "uses": 9131}, {"moveId": "HEAVY_SLAM", "uses": 5823}, {"moveId": "DYNAMIC_PUNCH", "uses": 5706}, {"moveId": "STONE_EDGE", "uses": 4921}, {"moveId": "SUBMISSION", "uses": 2507}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 59.1}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 729, "matchups": [{"opponent": "metagross", "rating": 840}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 751, "opRating": 248}, {"opponent": "dialga", "rating": 650}, {"opponent": "zacian_hero", "rating": 616, "opRating": 383}, {"opponent": "mewtwo", "rating": 596}], "counters": [{"opponent": "giratina_origin", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "dragonite", "rating": 257}, {"opponent": "gyarados", "rating": 296}, {"opponent": "lugia", "rating": 316}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45259}, {"moveId": "FIRE_FANG", "uses": 31241}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 22026}, {"moveId": "OVERHEAT", "uses": 17102}, {"moveId": "IRON_HEAD", "uses": 12544}, {"moveId": "RETURN", "uses": 10691}, {"moveId": "FLAMETHROWER", "uses": 9138}, {"moveId": "FIRE_BLAST", "uses": 5007}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 58.7}, {"speciesId": "sir<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 756, "matchups": [{"opponent": "swampert", "rating": 913, "opRating": 86}, {"opponent": "excadrill", "rating": 886, "opRating": 113}, {"opponent": "metagross", "rating": 682, "opRating": 317}, {"opponent": "dialga", "rating": 651}, {"opponent": "zekrom", "rating": 582, "opRating": 417}], "counters": [{"opponent": "giratina_origin", "rating": 173}, {"opponent": "lugia", "rating": 188}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "zacian_hero", "rating": 329}, {"opponent": "garcho<PERSON>", "rating": 363}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44521}, {"moveId": "FURY_CUTTER", "uses": 31979}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24575}, {"moveId": "LEAF_BLADE", "uses": 19525}, {"moveId": "NIGHT_SLASH", "uses": 17403}, {"moveId": "BRAVE_BIRD", "uses": 14971}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 58.7}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 731, "matchups": [{"opponent": "dialga", "rating": 777}, {"opponent": "mewtwo", "rating": 662}, {"opponent": "swampert", "rating": 643, "opRating": 356}, {"opponent": "excadrill", "rating": 601, "opRating": 398}, {"opponent": "metagross", "rating": 595, "opRating": 404}], "counters": [{"opponent": "giratina_origin", "rating": 197}, {"opponent": "dragonite", "rating": 202}, {"opponent": "lugia", "rating": 252}, {"opponent": "gyarados", "rating": 332}, {"opponent": "garcho<PERSON>", "rating": 354}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46073}, {"moveId": "BUG_BITE", "uses": 30427}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 30577}, {"moveId": "MEGAHORN", "uses": 27414}, {"moveId": "AERIAL_ACE", "uses": 14673}, {"moveId": "ACID_SPRAY", "uses": 3960}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 58}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 733, "matchups": [{"opponent": "zacian_hero", "rating": 721}, {"opponent": "metagross", "rating": 674}, {"opponent": "mewtwo", "rating": 656}, {"opponent": "gyarados", "rating": 539}, {"opponent": "dialga", "rating": 531}], "counters": [{"opponent": "giratina_origin", "rating": 101}, {"opponent": "zekrom", "rating": 149}, {"opponent": "swampert", "rating": 151}, {"opponent": "lugia", "rating": 383}, {"opponent": "garcho<PERSON>", "rating": 462}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16261}, {"moveId": "EXTRASENSORY", "uses": 5264}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4049}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4043}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3944}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3690}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3481}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3431}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3413}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3221}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3151}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3081}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3019}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2951}, {"moveId": "STEEL_WING", "uses": 2862}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2791}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2692}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2610}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2473}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 29075}, {"moveId": "SACRED_FIRE", "uses": 18812}, {"moveId": "EARTHQUAKE", "uses": 11937}, {"moveId": "RETURN", "uses": 6426}, {"moveId": "SOLAR_BEAM", "uses": 6366}, {"moveId": "FIRE_BLAST", "uses": 3749}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 57.7}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 624, "matchups": [{"opponent": "articuno_galarian", "rating": 878, "opRating": 121}, {"opponent": "zekrom", "rating": 649, "opRating": 350}, {"opponent": "giratina_altered", "rating": 564, "opRating": 435}, {"opponent": "palkia", "rating": 564, "opRating": 435}, {"opponent": "dragonite", "rating": 527, "opRating": 472}], "counters": [{"opponent": "excadrill", "rating": 262}, {"opponent": "metagross", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "dialga", "rating": 448}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 41300}, {"moveId": "THUNDER_FANG", "uses": 24296}, {"moveId": "IRON_TAIL", "uses": 10853}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22556}, {"moveId": "PSYCHIC_FANGS", "uses": 20139}, {"moveId": "EARTHQUAKE", "uses": 19188}, {"moveId": "HEAVY_SLAM", "uses": 14636}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "PSYCHIC_FANGS"], "score": 57.7}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 755, "matchups": [{"opponent": "zacian_hero", "rating": 785}, {"opponent": "swampert", "rating": 735, "opRating": 264}, {"opponent": "gyarados", "rating": 672}, {"opponent": "excadrill", "rating": 556, "opRating": 443}, {"opponent": "grou<PERSON>", "rating": 552, "opRating": 447}], "counters": [{"opponent": "giratina_origin", "rating": 137}, {"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "lugia", "rating": 319}, {"opponent": "dialga", "rating": 331}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 32652}, {"moveId": "BULLET_SEED", "uses": 28557}, {"moveId": "RAZOR_LEAF", "uses": 15291}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 25174}, {"moveId": "GRASS_KNOT", "uses": 16426}, {"moveId": "SLUDGE_BOMB", "uses": 14385}, {"moveId": "LEAF_STORM", "uses": 9568}, {"moveId": "DAZZLING_GLEAM", "uses": 7478}, {"moveId": "SOLAR_BEAM", "uses": 3420}]}, "moveset": ["POISON_JAB", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 57.6}, {"speciesId": "tapu_lele", "speciesName": "<PERSON><PERSON>", "rating": 707, "matchups": [{"opponent": "dialga", "rating": 786}, {"opponent": "dragonite", "rating": 757}, {"opponent": "gyarados", "rating": 681}, {"opponent": "garcho<PERSON>", "rating": 576}, {"opponent": "zacian_hero", "rating": 535}], "counters": [{"opponent": "metagross", "rating": 125}, {"opponent": "mewtwo", "rating": 174}, {"opponent": "giratina_origin", "rating": 205}, {"opponent": "grou<PERSON>", "rating": 247}, {"opponent": "lugia", "rating": 342}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56923}, {"moveId": "ASTONISH", "uses": 19577}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28466}, {"moveId": "PSYSHOCK", "uses": 24971}, {"moveId": "FOCUS_BLAST", "uses": 14679}, {"moveId": "FUTURE_SIGHT", "uses": 8382}]}, "moveset": ["CONFUSION", "MOONBLAST", "PSYSHOCK"], "score": 57.3}, {"speciesId": "metagross_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 745, "matchups": [{"opponent": "dialga", "rating": 726}, {"opponent": "lugia", "rating": 659}, {"opponent": "zacian_hero", "rating": 639}, {"opponent": "metagross", "rating": 630}, {"opponent": "dragonite", "rating": 549, "opRating": 450}], "counters": [{"opponent": "excadrill", "rating": 165}, {"opponent": "giratina_origin", "rating": 193}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "gyarados", "rating": 376}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 68274}, {"moveId": "ZEN_HEADBUTT", "uses": 8226}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 33440}, {"moveId": "PSYCHIC", "uses": 19550}, {"moveId": "EARTHQUAKE", "uses": 18247}, {"moveId": "FLASH_CANNON", "uses": 5202}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 57}, {"speciesId": "feraligatr", "speciesName": "Feraligatr", "rating": 660, "matchups": [{"opponent": "ho_oh", "rating": 856, "opRating": 143}, {"opponent": "grou<PERSON>", "rating": 783, "opRating": 216}, {"opponent": "excadrill", "rating": 629, "opRating": 370}, {"opponent": "sylveon", "rating": 525, "opRating": 474}, {"opponent": "metagross", "rating": 511, "opRating": 488}], "counters": [{"opponent": "giratina_origin", "rating": 272}, {"opponent": "gyarados", "rating": 286}, {"opponent": "dialga", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 370}, {"opponent": "mewtwo", "rating": 427}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22921}, {"moveId": "WATERFALL", "uses": 21447}, {"moveId": "ICE_FANG", "uses": 20244}, {"moveId": "BITE", "uses": 11917}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 34406}, {"moveId": "CRUNCH", "uses": 17780}, {"moveId": "ICE_BEAM", "uses": 14294}, {"moveId": "RETURN", "uses": 6366}, {"moveId": "HYDRO_PUMP", "uses": 3694}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 56.9}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 712, "matchups": [{"opponent": "metagross", "rating": 762}, {"opponent": "excadrill", "rating": 731, "opRating": 268}, {"opponent": "dialga", "rating": 631}, {"opponent": "garcho<PERSON>", "rating": 515}, {"opponent": "mewtwo", "rating": 509}], "counters": [{"opponent": "gyarados", "rating": 162}, {"opponent": "dragonite", "rating": 162}, {"opponent": "giratina_origin", "rating": 296}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "lugia", "rating": 361}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38328}, {"moveId": "WING_ATTACK", "uses": 38172}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30912}, {"moveId": "EARTHQUAKE", "uses": 18791}, {"moveId": "AERIAL_ACE", "uses": 17399}, {"moveId": "SAND_TOMB", "uses": 9256}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "EARTHQUAKE"], "score": 56.9}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 672, "matchups": [{"opponent": "giratina_origin", "rating": 813}, {"opponent": "mewtwo", "rating": 681}, {"opponent": "dialga", "rating": 663}, {"opponent": "excadrill", "rating": 647, "opRating": 352}, {"opponent": "metagross", "rating": 536, "opRating": 463}], "counters": [{"opponent": "zacian_hero", "rating": 161}, {"opponent": "dragonite", "rating": 188}, {"opponent": "lugia", "rating": 307}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "gyarados", "rating": 376}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45371}, {"moveId": "LICK", "uses": 31129}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 37958}, {"moveId": "CROSS_CHOP", "uses": 22262}, {"moveId": "HYPER_BEAM", "uses": 8614}, {"moveId": "GUNK_SHOT", "uses": 6642}, {"moveId": "OBSTRUCT", "uses": 912}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "OBSTRUCT"], "score": 56.9}, {"speciesId": "samu<PERSON>t", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 696, "matchups": [{"opponent": "ho_oh", "rating": 855, "opRating": 144}, {"opponent": "metagross", "rating": 817}, {"opponent": "zacian_hero", "rating": 780}, {"opponent": "excadrill", "rating": 621, "opRating": 378}, {"opponent": "swampert", "rating": 548, "opRating": 451}], "counters": [{"opponent": "giratina_origin", "rating": 139}, {"opponent": "dragonite", "rating": 178}, {"opponent": "dialga", "rating": 304}, {"opponent": "gyarados", "rating": 314}, {"opponent": "mewtwo", "rating": 385}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 43388}, {"moveId": "WATERFALL", "uses": 33112}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 33885}, {"moveId": "RAZOR_SHELL", "uses": 14302}, {"moveId": "MEGAHORN", "uses": 13757}, {"moveId": "BLIZZARD", "uses": 10946}, {"moveId": "HYDRO_PUMP", "uses": 3551}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "RAZOR_SHELL"], "score": 56.7}, {"speciesId": "nihilego", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 812, "matchups": [{"opponent": "zacian_hero", "rating": 913}, {"opponent": "gyarados", "rating": 836}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 831, "opRating": 168}, {"opponent": "dragonite", "rating": 782}, {"opponent": "lugia", "rating": 693}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "grou<PERSON>", "rating": 230}, {"opponent": "metagross", "rating": 241}, {"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 260}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54815}, {"moveId": "ACID", "uses": 18712}, {"moveId": "POUND", "uses": 3085}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 41283}, {"moveId": "SLUDGE_BOMB", "uses": 22022}, {"moveId": "POWER_GEM", "uses": 7509}, {"moveId": "GUNK_SHOT", "uses": 5709}]}, "moveset": ["POISON_JAB", "ROCK_SLIDE", "SLUDGE_BOMB"], "score": 56.2}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 649, "matchups": [{"opponent": "mewtwo_shadow", "rating": 836, "opRating": 163}, {"opponent": "gyarados", "rating": 741, "opRating": 258}, {"opponent": "sylveon", "rating": 559, "opRating": 440}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 532, "opRating": 467}, {"opponent": "metagross", "rating": 518, "opRating": 481}], "counters": [{"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "giratina_origin", "rating": 338}, {"opponent": "mewtwo", "rating": 408}, {"opponent": "dialga", "rating": 459}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30633}, {"moveId": "FIRE_FANG", "uses": 20513}, {"moveId": "THUNDER_FANG", "uses": 14473}, {"moveId": "BITE", "uses": 10791}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22453}, {"moveId": "CRUNCH", "uses": 16068}, {"moveId": "PSYCHIC_FANGS", "uses": 14507}, {"moveId": "FLAMETHROWER", "uses": 13272}, {"moveId": "BULLDOZE", "uses": 6694}, {"moveId": "FIRE_BLAST", "uses": 3568}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 56}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 700, "matchups": [{"opponent": "grou<PERSON>", "rating": 851}, {"opponent": "zacian_hero", "rating": 671}, {"opponent": "metagross", "rating": 648}, {"opponent": "garcho<PERSON>", "rating": 638}, {"opponent": "mewtwo", "rating": 572}], "counters": [{"opponent": "dragonite", "rating": 148}, {"opponent": "swampert", "rating": 181}, {"opponent": "gyarados", "rating": 188}, {"opponent": "giratina_origin", "rating": 476}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 18698}, {"moveId": "EXTRASENSORY", "uses": 5405}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3974}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3851}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3721}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3572}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3292}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3286}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3251}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3069}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3023}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2969}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2844}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2822}, {"moveId": "STEEL_WING", "uses": 2675}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2552}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2494}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2369}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2332}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 32098}, {"moveId": "SACRED_FIRE", "uses": 20406}, {"moveId": "EARTHQUAKE", "uses": 12862}, {"moveId": "SOLAR_BEAM", "uses": 6981}, {"moveId": "FIRE_BLAST", "uses": 4143}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 55.7}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 729, "matchups": [{"opponent": "metagross", "rating": 809}, {"opponent": "grou<PERSON>", "rating": 787, "opRating": 212}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 706, "opRating": 293}, {"opponent": "zacian_hero", "rating": 623}, {"opponent": "dialga", "rating": 545}], "counters": [{"opponent": "dragonite", "rating": 151}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "gyarados", "rating": 386}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38794}, {"moveId": "WING_ATTACK", "uses": 37706}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30393}, {"moveId": "OVERHEAT", "uses": 18816}, {"moveId": "ANCIENT_POWER", "uses": 18450}, {"moveId": "FIRE_BLAST", "uses": 5460}, {"moveId": "HEAT_WAVE", "uses": 3208}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 55.6}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 594, "matchups": [{"opponent": "gengar", "rating": 865, "opRating": 134}, {"opponent": "electivire_shadow", "rating": 732, "opRating": 267}, {"opponent": "giratina_origin", "rating": 653}, {"opponent": "latios", "rating": 648, "opRating": 351}, {"opponent": "giratina_altered", "rating": 504, "opRating": 495}], "counters": [{"opponent": "dragonite", "rating": 239}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dialga", "rating": 326}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "mewtwo", "rating": 486}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66106}, {"moveId": "ZEN_HEADBUTT", "uses": 10394}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35305}, {"moveId": "SHADOW_BALL", "uses": 17284}, {"moveId": "EARTHQUAKE", "uses": 13378}, {"moveId": "SOLAR_BEAM", "uses": 6581}, {"moveId": "HYPER_BEAM", "uses": 3914}]}, "moveset": ["LICK", "BODY_SLAM", "SHADOW_BALL"], "score": 55.2}, {"speciesId": "pangoro", "speciesName": "Pangoro", "rating": 706, "matchups": [{"opponent": "excadrill", "rating": 904}, {"opponent": "metagross", "rating": 750}, {"opponent": "mewtwo", "rating": 682}, {"opponent": "dialga", "rating": 610}, {"opponent": "giratina_origin", "rating": 603}], "counters": [{"opponent": "dragonite", "rating": 228}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "lugia", "rating": 321}, {"opponent": "gyarados", "rating": 378}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42470}, {"moveId": "BULLET_PUNCH", "uses": 27998}, {"moveId": "LOW_KICK", "uses": 6056}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29034}, {"moveId": "NIGHT_SLASH", "uses": 26573}, {"moveId": "ROCK_SLIDE", "uses": 14193}, {"moveId": "IRON_HEAD", "uses": 6620}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 55.2}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 676, "matchups": [{"opponent": "swampert", "rating": 936, "opRating": 63}, {"opponent": "excadrill", "rating": 724, "opRating": 275}, {"opponent": "grou<PERSON>", "rating": 681, "opRating": 318}, {"opponent": "garcho<PERSON>", "rating": 540}, {"opponent": "zacian_hero", "rating": 536, "opRating": 463}], "counters": [{"opponent": "lugia", "rating": 209}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "dialga", "rating": 244}, {"opponent": "metagross", "rating": 270}, {"opponent": "mewtwo", "rating": 328}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30515}, {"moveId": "BULLET_SEED", "uses": 29440}, {"moveId": "RAZOR_LEAF", "uses": 16541}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 51120}, {"moveId": "LAST_RESORT", "uses": 13854}, {"moveId": "ENERGY_BALL", "uses": 7222}, {"moveId": "SOLAR_BEAM", "uses": 4256}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 54.6}, {"speciesId": "al<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 691, "matchups": [{"opponent": "excadrill", "rating": 811}, {"opponent": "garcho<PERSON>", "rating": 774}, {"opponent": "metagross", "rating": 596}, {"opponent": "dialga", "rating": 551}, {"opponent": "grou<PERSON>", "rating": 540, "opRating": 459}], "counters": [{"opponent": "giratina_origin", "rating": 165}, {"opponent": "dragonite", "rating": 178}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "lugia", "rating": 371}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29467}, {"moveId": "PSYCHO_CUT", "uses": 26443}, {"moveId": "CONFUSION", "uses": 20548}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18317}, {"moveId": "FIRE_PUNCH", "uses": 16663}, {"moveId": "PSYCHIC", "uses": 15555}, {"moveId": "FOCUS_BLAST", "uses": 10929}, {"moveId": "DAZZLING_GLEAM", "uses": 8264}, {"moveId": "FUTURE_SIGHT", "uses": 6697}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 54.5}, {"speciesId": "con<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 735, "matchups": [{"opponent": "metagross", "rating": 709, "opRating": 290}, {"opponent": "excadrill", "rating": 656, "opRating": 343}, {"opponent": "gyarados", "rating": 620, "opRating": 379}, {"opponent": "dialga", "rating": 608}, {"opponent": "garcho<PERSON>", "rating": 514}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dragonite", "rating": 162}, {"opponent": "lugia", "rating": 271}, {"opponent": "giratina_origin", "rating": 280}, {"opponent": "zacian_hero", "rating": 338}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45947}, {"moveId": "POISON_JAB", "uses": 30553}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 36171}, {"moveId": "STONE_EDGE", "uses": 30453}, {"moveId": "FOCUS_BLAST", "uses": 9869}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "STONE_EDGE"], "score": 54.5}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 724, "matchups": [{"opponent": "mewtwo", "rating": 784}, {"opponent": "giratina_origin", "rating": 715}, {"opponent": "yveltal", "rating": 607, "opRating": 392}, {"opponent": "lugia", "rating": 583, "opRating": 416}, {"opponent": "swampert", "rating": 524, "opRating": 475}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "zacian_hero", "rating": 213}, {"opponent": "dragonite", "rating": 332}, {"opponent": "dialga", "rating": 342}, {"opponent": "metagross", "rating": 363}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39061}, {"moveId": "WING_ATTACK", "uses": 37439}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36310}, {"moveId": "PAYBACK", "uses": 23939}, {"moveId": "ANCIENT_POWER", "uses": 16407}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 54.5}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 812, "matchups": [{"opponent": "zacian_hero", "rating": 882}, {"opponent": "metagross", "rating": 863}, {"opponent": "gyarados", "rating": 834}, {"opponent": "lugia", "rating": 805}, {"opponent": "mewtwo", "rating": 515}], "counters": [{"opponent": "grou<PERSON>", "rating": 141}, {"opponent": "excadrill", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "dialga", "rating": 312}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47937}, {"moveId": "CHARGE_BEAM", "uses": 28563}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 41025}, {"moveId": "MIRROR_SHOT", "uses": 19027}, {"moveId": "FLASH_CANNON", "uses": 10173}, {"moveId": "ZAP_CANNON", "uses": 6381}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 54.3}, {"speciesId": "salamence_shadow", "speciesName": "Salamence (Shadow)", "rating": 674, "matchups": [{"opponent": "garcho<PERSON>", "rating": 935}, {"opponent": "giratina_origin", "rating": 729}, {"opponent": "grou<PERSON>", "rating": 688, "opRating": 311}, {"opponent": "swampert", "rating": 621, "opRating": 378}, {"opponent": "dragonite", "rating": 533, "opRating": 466}], "counters": [{"opponent": "zacian_hero", "rating": 176}, {"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 264}, {"opponent": "metagross", "rating": 293}, {"opponent": "mewtwo", "rating": 348}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 44942}, {"moveId": "FIRE_FANG", "uses": 19548}, {"moveId": "BITE", "uses": 11996}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 35118}, {"moveId": "FIRE_BLAST", "uses": 15428}, {"moveId": "HYDRO_PUMP", "uses": 15292}, {"moveId": "DRACO_METEOR", "uses": 10542}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "FIRE_BLAST"], "score": 54.3}, {"speciesId": "machamp", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 762, "matchups": [{"opponent": "excadrill", "rating": 911, "opRating": 88}, {"opponent": "metagross", "rating": 696, "opRating": 303}, {"opponent": "swampert", "rating": 677, "opRating": 322}, {"opponent": "dialga", "rating": 658}, {"opponent": "zekrom", "rating": 513, "opRating": 486}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 216}, {"opponent": "giratina_origin", "rating": 219}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "garcho<PERSON>", "rating": 338}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 30044}, {"moveId": "KARATE_CHOP", "uses": 27382}, {"moveId": "BULLET_PUNCH", "uses": 19154}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20214}, {"moveId": "CROSS_CHOP", "uses": 13900}, {"moveId": "ROCK_SLIDE", "uses": 11842}, {"moveId": "PAYBACK", "uses": 8599}, {"moveId": "HEAVY_SLAM", "uses": 5476}, {"moveId": "DYNAMIC_PUNCH", "uses": 5340}, {"moveId": "STONE_EDGE", "uses": 4671}, {"moveId": "RETURN", "uses": 4027}, {"moveId": "SUBMISSION", "uses": 2455}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 53.9}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 672, "matchups": [{"opponent": "garcho<PERSON>", "rating": 882}, {"opponent": "dragonite", "rating": 694}, {"opponent": "mewtwo", "rating": 681}, {"opponent": "giratina_origin", "rating": 617}, {"opponent": "dialga", "rating": 566}], "counters": [{"opponent": "zacian_hero", "rating": 286}, {"opponent": "lugia", "rating": 323}, {"opponent": "gyarados", "rating": 360}, {"opponent": "swampert", "rating": 375}, {"opponent": "excadrill", "rating": 409}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30698}, {"moveId": "ICE_SHARD", "uses": 27704}, {"moveId": "FEINT_ATTACK", "uses": 18125}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38245}, {"moveId": "FOUL_PLAY", "uses": 21882}, {"moveId": "FOCUS_BLAST", "uses": 9841}, {"moveId": "RETURN", "uses": 6568}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 53.9}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 682, "matchups": [{"opponent": "mewtwo", "rating": 920}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "excadrill", "rating": 837}, {"opponent": "giratina_origin", "rating": 805}, {"opponent": "dragonite", "rating": 633}], "counters": [{"opponent": "zacian_hero", "rating": 349}, {"opponent": "metagross", "rating": 351}, {"opponent": "lugia", "rating": 385}, {"opponent": "dialga", "rating": 396}, {"opponent": "gyarados", "rating": 427}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 31527}, {"moveId": "ICE_SHARD", "uses": 27724}, {"moveId": "FEINT_ATTACK", "uses": 17224}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41686}, {"moveId": "FOUL_PLAY", "uses": 23972}, {"moveId": "FOCUS_BLAST", "uses": 10745}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 53.9}, {"speciesId": "cofagrigus", "speciesName": "<PERSON><PERSON>g<PERSON><PERSON>", "rating": 579, "matchups": [{"opponent": "lucario", "rating": 823, "opRating": 176}, {"opponent": "x<PERSON><PERSON>", "rating": 758, "opRating": 241}, {"opponent": "metagross", "rating": 672, "opRating": 327}, {"opponent": "zacian_hero", "rating": 589, "opRating": 410}, {"opponent": "lugia", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dragonite", "rating": 247}, {"opponent": "giratina_origin", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "dialga", "rating": 355}, {"opponent": "mewtwo", "rating": 401}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 56143}, {"moveId": "ASTONISH", "uses": 14608}, {"moveId": "ZEN_HEADBUTT", "uses": 5776}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 35646}, {"moveId": "DARK_PULSE", "uses": 23271}, {"moveId": "PSYCHIC", "uses": 17493}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "DARK_PULSE"], "score": 53.8}, {"speciesId": "cryogonal", "speciesName": "Cryogonal", "rating": 594, "matchups": [{"opponent": "land<PERSON><PERSON>_therian", "rating": 857, "opRating": 142}, {"opponent": "zekrom", "rating": 671, "opRating": 328}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 656, "opRating": 343}, {"opponent": "dragonite_shadow", "rating": 648, "opRating": 351}, {"opponent": "garcho<PERSON>", "rating": 587}], "counters": [{"opponent": "zacian_hero", "rating": 167}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "dialga", "rating": 366}, {"opponent": "metagross", "rating": 375}, {"opponent": "gyarados", "rating": 414}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 44968}, {"moveId": "FROST_BREATH", "uses": 31532}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39080}, {"moveId": "AURORA_BEAM", "uses": 19593}, {"moveId": "SOLAR_BEAM", "uses": 10048}, {"moveId": "WATER_PULSE", "uses": 7783}]}, "moveset": ["ICE_SHARD", "NIGHT_SLASH", "AURORA_BEAM"], "score": 53.8}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 782, "matchups": [{"opponent": "metagross", "rating": 759}, {"opponent": "zacian_hero", "rating": 740}, {"opponent": "gyarados", "rating": 737}, {"opponent": "lugia", "rating": 667}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "excadrill", "rating": 248}, {"opponent": "garcho<PERSON>", "rating": 359}, {"opponent": "mewtwo", "rating": 424}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 68146}, {"moveId": "LOW_KICK", "uses": 8354}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28344}, {"moveId": "ICE_PUNCH", "uses": 19259}, {"moveId": "THUNDER_PUNCH", "uses": 13793}, {"moveId": "FLAMETHROWER", "uses": 10505}, {"moveId": "THUNDER", "uses": 4506}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 53.8}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 711, "matchups": [{"opponent": "metagross", "rating": 779}, {"opponent": "dialga", "rating": 643}, {"opponent": "mewtwo", "rating": 638}, {"opponent": "lugia", "rating": 569, "opRating": 430}, {"opponent": "zacian_hero", "rating": 566}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "grou<PERSON>", "rating": 176}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "dragonite", "rating": 329}, {"opponent": "gyarados", "rating": 340}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46678}, {"moveId": "BUG_BITE", "uses": 29822}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 24926}, {"moveId": "STONE_EDGE", "uses": 24488}, {"moveId": "IRON_HEAD", "uses": 20254}, {"moveId": "FIRE_BLAST", "uses": 6751}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "IRON_HEAD"], "score": 53.3}, {"speciesId": "sandslash_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 633, "matchups": [{"opponent": "garcho<PERSON>", "rating": 850}, {"opponent": "dragonite", "rating": 789}, {"opponent": "lugia", "rating": 628}, {"opponent": "gyarados", "rating": 548}, {"opponent": "mewtwo", "rating": 527}], "counters": [{"opponent": "zacian_hero", "rating": 303}, {"opponent": "excadrill", "rating": 309}, {"opponent": "metagross", "rating": 313}, {"opponent": "giratina_origin", "rating": 350}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 33199}, {"moveId": "SHADOW_CLAW", "uses": 30445}, {"moveId": "METAL_CLAW", "uses": 12863}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 34255}, {"moveId": "BLIZZARD", "uses": 17750}, {"moveId": "BULLDOZE", "uses": 13216}, {"moveId": "GYRO_BALL", "uses": 11203}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 53.3}, {"speciesId": "tapu_bulu", "speciesName": "Tapu Bulu", "rating": 701, "matchups": [{"opponent": "swampert", "rating": 818, "opRating": 181}, {"opponent": "garcho<PERSON>", "rating": 745}, {"opponent": "dragonite", "rating": 716, "opRating": 283}, {"opponent": "gyarados", "rating": 694, "opRating": 305}, {"opponent": "zacian_hero", "rating": 515, "opRating": 484}], "counters": [{"opponent": "giratina_origin", "rating": 81}, {"opponent": "metagross", "rating": 229}, {"opponent": "lugia", "rating": 230}, {"opponent": "dialga", "rating": 336}, {"opponent": "mewtwo", "rating": 406}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 59617}, {"moveId": "ROCK_SMASH", "uses": 16883}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28649}, {"moveId": "MEGAHORN", "uses": 23258}, {"moveId": "DAZZLING_GLEAM", "uses": 18633}, {"moveId": "SOLAR_BEAM", "uses": 5973}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "DAZZLING_GLEAM"], "score": 53.3}, {"speciesId": "feraligatr_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 664, "matchups": [{"opponent": "ho_oh", "rating": 873, "opRating": 126}, {"opponent": "swampert_shadow", "rating": 862, "opRating": 137}, {"opponent": "metagross", "rating": 772, "opRating": 227}, {"opponent": "grou<PERSON>", "rating": 766, "opRating": 233}, {"opponent": "excadrill", "rating": 592, "opRating": 407}], "counters": [{"opponent": "giratina_origin", "rating": 207}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "gyarados", "rating": 301}, {"opponent": "dialga", "rating": 307}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22444}, {"moveId": "WATERFALL", "uses": 21928}, {"moveId": "ICE_FANG", "uses": 20331}, {"moveId": "BITE", "uses": 11904}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37397}, {"moveId": "CRUNCH", "uses": 19331}, {"moveId": "ICE_BEAM", "uses": 15639}, {"moveId": "HYDRO_PUMP", "uses": 4020}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 53.1}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 639, "matchups": [{"opponent": "porygon_z", "rating": 923, "opRating": 76}, {"opponent": "pinsir_shadow", "rating": 893, "opRating": 106}, {"opponent": "roserade", "rating": 814, "opRating": 185}, {"opponent": "grou<PERSON>", "rating": 518, "opRating": 481}, {"opponent": "zekrom", "rating": 503, "opRating": 496}], "counters": [{"opponent": "zacian_hero", "rating": 286}, {"opponent": "mewtwo", "rating": 299}, {"opponent": "dialga", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "giratina_origin", "rating": 366}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 51929}, {"moveId": "STEEL_WING", "uses": 24571}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 26457}, {"moveId": "CRUNCH", "uses": 25148}, {"moveId": "ANCIENT_POWER", "uses": 24874}]}, "moveset": ["WING_ATTACK", "DRAGON_CLAW", "CRUNCH"], "score": 52.8}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 614, "matchups": [{"opponent": "swampert", "rating": 803, "opRating": 196}, {"opponent": "metagross", "rating": 744, "opRating": 255}, {"opponent": "zacian_hero", "rating": 648, "opRating": 351}, {"opponent": "garcho<PERSON>", "rating": 573}, {"opponent": "excadrill", "rating": 558, "opRating": 441}], "counters": [{"opponent": "dragonite", "rating": 151}, {"opponent": "giratina_origin", "rating": 183}, {"opponent": "gyarados", "rating": 317}, {"opponent": "dialga", "rating": 336}, {"opponent": "mewtwo", "rating": 494}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49504}, {"moveId": "RAZOR_LEAF", "uses": 26996}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26370}, {"moveId": "SEED_BOMB", "uses": 21687}, {"moveId": "FOUL_PLAY", "uses": 19765}, {"moveId": "FIRE_BLAST", "uses": 8624}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 52.8}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 628, "matchups": [{"opponent": "garcho<PERSON>", "rating": 924}, {"opponent": "swampert", "rating": 760, "opRating": 239}, {"opponent": "dragonite", "rating": 715, "opRating": 284}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 545, "opRating": 454}, {"opponent": "excadrill", "rating": 505, "opRating": 494}], "counters": [{"opponent": "metagross", "rating": 183}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "giratina_origin", "rating": 350}, {"opponent": "dialga", "rating": 372}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 55076}, {"moveId": "RAZOR_LEAF", "uses": 21424}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 42675}, {"moveId": "ENERGY_BALL", "uses": 16051}, {"moveId": "OUTRAGE", "uses": 11234}, {"moveId": "BLIZZARD", "uses": 6489}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 52.4}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 734, "matchups": [{"opponent": "excadrill", "rating": 826, "opRating": 173}, {"opponent": "yveltal", "rating": 826, "opRating": 173}, {"opponent": "metagross", "rating": 740, "opRating": 259}, {"opponent": "gyarados", "rating": 676, "opRating": 323}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "opRating": 478}], "counters": [{"opponent": "giratina_origin", "rating": 133}, {"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "dialga", "rating": 448}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 40062}, {"moveId": "FIRE_SPIN", "uses": 36438}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 25615}, {"moveId": "BRICK_BREAK", "uses": 17296}, {"moveId": "THUNDERBOLT", "uses": 15009}, {"moveId": "PSYCHIC", "uses": 13388}, {"moveId": "FIRE_BLAST", "uses": 5124}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 52.4}, {"speciesId": "pinsir", "speciesName": "Pinsir", "rating": 703, "matchups": [{"opponent": "excadrill", "rating": 916, "opRating": 83}, {"opponent": "magnezone_shadow", "rating": 862, "opRating": 137}, {"opponent": "swampert", "rating": 671, "opRating": 328}, {"opponent": "dialga", "rating": 543}, {"opponent": "yveltal", "rating": 506, "opRating": 493}], "counters": [{"opponent": "gyarados", "rating": 198}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "mewtwo", "rating": 401}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40175}, {"moveId": "BUG_BITE", "uses": 26452}, {"moveId": "ROCK_SMASH", "uses": 9866}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 22932}, {"moveId": "X_SCISSOR", "uses": 20037}, {"moveId": "SUPER_POWER", "uses": 17062}, {"moveId": "RETURN", "uses": 7259}, {"moveId": "VICE_GRIP", "uses": 6542}, {"moveId": "SUBMISSION", "uses": 2758}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 52.2}, {"speciesId": "gran<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 586, "matchups": [{"opponent": "pangoro", "rating": 903, "opRating": 96}, {"opponent": "porygon_z", "rating": 895, "opRating": 104}, {"opponent": "excadrill", "rating": 852}, {"opponent": "dialga", "rating": 747}, {"opponent": "latios", "rating": 650, "opRating": 349}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "giratina_origin", "rating": 326}, {"opponent": "metagross", "rating": 363}, {"opponent": "mewtwo", "rating": 411}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37183}, {"moveId": "CHARM", "uses": 25973}, {"moveId": "BITE", "uses": 13367}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34082}, {"moveId": "CRUNCH", "uses": 25747}, {"moveId": "PLAY_ROUGH", "uses": 16574}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 52.1}, {"speciesId": "latias", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 654, "matchups": [{"opponent": "kyogre", "rating": 651, "opRating": 348}, {"opponent": "swampert", "rating": 566, "opRating": 433}, {"opponent": "ho_oh", "rating": 546, "opRating": 453}, {"opponent": "grou<PERSON>", "rating": 540, "opRating": 459}, {"opponent": "mewtwo", "rating": 526}], "counters": [{"opponent": "zacian_hero", "rating": 182}, {"opponent": "metagross", "rating": 212}, {"opponent": "gyarados", "rating": 291}, {"opponent": "dialga", "rating": 317}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 46241}, {"moveId": "CHARM", "uses": 23605}, {"moveId": "ZEN_HEADBUTT", "uses": 6663}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 21902}, {"moveId": "OUTRAGE", "uses": 19624}, {"moveId": "PSYCHIC", "uses": 14566}, {"moveId": "THUNDER", "uses": 12166}, {"moveId": "RETURN", "uses": 8025}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 52.1}, {"speciesId": "ninetales_alolan", "speciesName": "Ninetales (Alolan)", "rating": 587, "matchups": [{"opponent": "dragonite", "rating": 854}, {"opponent": "zekrom", "rating": 835, "opRating": 164}, {"opponent": "palkia", "rating": 599, "opRating": 400}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 577, "opRating": 422}, {"opponent": "garcho<PERSON>", "rating": 543}], "counters": [{"opponent": "metagross", "rating": 116}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "mewtwo", "rating": 351}, {"opponent": "dialga", "rating": 372}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37364}, {"moveId": "CHARM", "uses": 21187}, {"moveId": "FEINT_ATTACK", "uses": 18005}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39776}, {"moveId": "PSYSHOCK", "uses": 12572}, {"moveId": "DAZZLING_GLEAM", "uses": 9502}, {"moveId": "ICE_BEAM", "uses": 8583}, {"moveId": "BLIZZARD", "uses": 5942}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "PSYSHOCK"], "score": 52.1}, {"speciesId": "espeon", "speciesName": "Espeon", "rating": 722, "matchups": [{"opponent": "zacian_hero", "rating": 761}, {"opponent": "metagross", "rating": 667, "opRating": 332}, {"opponent": "excadrill", "rating": 536, "opRating": 463}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 513, "opRating": 486}, {"opponent": "swampert", "rating": 506, "opRating": 493}], "counters": [{"opponent": "lugia", "rating": 159}, {"opponent": "dialga", "rating": 220}, {"opponent": "mewtwo", "rating": 244}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "garcho<PERSON>", "rating": 387}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 67188}, {"moveId": "ZEN_HEADBUTT", "uses": 9312}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 24781}, {"moveId": "SHADOW_BALL", "uses": 19353}, {"moveId": "PSYCHIC", "uses": 13641}, {"moveId": "LAST_RESORT", "uses": 9978}, {"moveId": "FUTURE_SIGHT", "uses": 5998}, {"moveId": "PSYBEAM", "uses": 2738}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "SHADOW_BALL"], "score": 51.9}, {"speciesId": "tyranitar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 671, "matchups": [{"opponent": "ho_oh", "rating": 915, "opRating": 84}, {"opponent": "mewtwo", "rating": 738}, {"opponent": "giratina_origin", "rating": 689}, {"opponent": "lugia", "rating": 644, "opRating": 355}, {"opponent": "gyarados", "rating": 547, "opRating": 452}], "counters": [{"opponent": "excadrill", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "metagross", "rating": 258}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43737}, {"moveId": "BITE", "uses": 25798}, {"moveId": "IRON_TAIL", "uses": 6923}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37791}, {"moveId": "STONE_EDGE", "uses": 29087}, {"moveId": "FIRE_BLAST", "uses": 9627}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 51.9}, {"speciesId": "heracross", "speciesName": "Heracross", "rating": 751, "matchups": [{"opponent": "metagross", "rating": 732}, {"opponent": "swampert", "rating": 729, "opRating": 270}, {"opponent": "excadrill", "rating": 712, "opRating": 287}, {"opponent": "dialga", "rating": 662}, {"opponent": "garcho<PERSON>", "rating": 517}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 169}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "gyarados", "rating": 363}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 58093}, {"moveId": "STRUGGLE_BUG", "uses": 18407}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32821}, {"moveId": "MEGAHORN", "uses": 18335}, {"moveId": "ROCK_BLAST", "uses": 15062}, {"moveId": "EARTHQUAKE", "uses": 10227}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ROCK_BLAST"], "score": 51.8}, {"speciesId": "hoopa_unbound", "speciesName": "<PERSON><PERSON><PERSON> (Unbound)", "rating": 633, "matchups": [{"opponent": "ho_oh", "rating": 710, "opRating": 289}, {"opponent": "giratina_altered", "rating": 649, "opRating": 350}, {"opponent": "mewtwo", "rating": 630}, {"opponent": "excadrill", "rating": 557, "opRating": 442}, {"opponent": "dialga", "rating": 519}], "counters": [{"opponent": "metagross", "rating": 145}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dragonite", "rating": 321}, {"opponent": "zacian_hero", "rating": 346}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56626}, {"moveId": "ASTONISH", "uses": 19874}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 30848}, {"moveId": "SHADOW_BALL", "uses": 23159}, {"moveId": "PSYCHIC", "uses": 22437}]}, "moveset": ["CONFUSION", "DARK_PULSE", "SHADOW_BALL"], "score": 51.8}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 577, "matchups": [{"opponent": "garcho<PERSON>", "rating": 873}, {"opponent": "dragonite", "rating": 684, "opRating": 315}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 549, "opRating": 450}, {"opponent": "zekrom", "rating": 549, "opRating": 450}, {"opponent": "dialga", "rating": 504}], "counters": [{"opponent": "metagross", "rating": 188}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "mewtwo", "rating": 299}, {"opponent": "gyarados", "rating": 304}, {"opponent": "lugia", "rating": 307}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44606}, {"moveId": "ICE_SHARD", "uses": 31894}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41410}, {"moveId": "STONE_EDGE", "uses": 15296}, {"moveId": "BULLDOZE", "uses": 12861}, {"moveId": "RETURN", "uses": 6833}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 51.8}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 583, "matchups": [{"opponent": "swampert", "rating": 789, "opRating": 210}, {"opponent": "swampert_shadow", "rating": 765, "opRating": 234}, {"opponent": "latios_shadow", "rating": 661, "opRating": 338}, {"opponent": "zacian_hero", "rating": 612, "opRating": 387}, {"opponent": "excadrill", "rating": 533, "opRating": 466}], "counters": [{"opponent": "giratina_origin", "rating": 181}, {"opponent": "lugia", "rating": 288}, {"opponent": "gyarados", "rating": 314}, {"opponent": "dialga", "rating": 334}, {"opponent": "mewtwo", "rating": 489}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49421}, {"moveId": "RAZOR_LEAF", "uses": 27079}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26375}, {"moveId": "SEED_BOMB", "uses": 21784}, {"moveId": "FOUL_PLAY", "uses": 19814}, {"moveId": "FIRE_BLAST", "uses": 8612}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 51.6}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 570, "matchups": [{"opponent": "garcho<PERSON>", "rating": 866}, {"opponent": "grou<PERSON>", "rating": 740, "opRating": 259}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "zekrom", "rating": 614, "opRating": 385}, {"opponent": "dragonite", "rating": 531, "opRating": 468}], "counters": [{"opponent": "metagross", "rating": 191}, {"opponent": "zacian_hero", "rating": 248}, {"opponent": "dialga", "rating": 326}, {"opponent": "lugia", "rating": 330}, {"opponent": "mewtwo", "rating": 335}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47638}, {"moveId": "FROST_BREATH", "uses": 28862}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36469}, {"moveId": "ICY_WIND", "uses": 12695}, {"moveId": "HYDRO_PUMP", "uses": 10122}, {"moveId": "RETURN", "uses": 7063}, {"moveId": "BLIZZARD", "uses": 5642}, {"moveId": "AURORA_BEAM", "uses": 4557}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 51.4}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 625, "matchups": [{"opponent": "bewear", "rating": 918, "opRating": 81}, {"opponent": "porygon_z", "rating": 867, "opRating": 132}, {"opponent": "victini", "rating": 851, "opRating": 148}, {"opponent": "mew", "rating": 688, "opRating": 311}, {"opponent": "sylveon", "rating": 525, "opRating": 474}], "counters": [{"opponent": "metagross", "rating": 252}, {"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "mewtwo", "rating": 367}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 8588}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5110}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4473}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4356}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4301}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4294}, {"moveId": "CHARGE_BEAM", "uses": 4255}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4076}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4039}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3936}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3921}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3856}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3842}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3584}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3577}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3527}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3409}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3307}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 26315}, {"moveId": "RETURN", "uses": 16644}, {"moveId": "ZAP_CANNON", "uses": 16191}, {"moveId": "SOLAR_BEAM", "uses": 10797}, {"moveId": "HYPER_BEAM", "uses": 6445}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "RETURN"], "score": 51.2}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 769, "matchups": [{"opponent": "metagross", "rating": 758}, {"opponent": "gyarados", "rating": 755}, {"opponent": "lugia", "rating": 690, "opRating": 309}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622, "opRating": 377}, {"opponent": "dragonite", "rating": 605, "opRating": 394}], "counters": [{"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "excadrill", "rating": 183}, {"opponent": "giratina_origin", "rating": 189}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "dialga", "rating": 423}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40768}, {"moveId": "SPARK", "uses": 35732}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 28546}, {"moveId": "POWER_WHIP", "uses": 23104}, {"moveId": "DAZZLING_GLEAM", "uses": 14287}, {"moveId": "THUNDER", "uses": 10541}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "DAZZLING_GLEAM"], "score": 51.1}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 571, "matchups": [{"opponent": "ursaring_shadow", "rating": 900, "opRating": 99}, {"opponent": "snorlax", "rating": 803, "opRating": 196}, {"opponent": "gengar", "rating": 716, "opRating": 283}, {"opponent": "landorus_incarnate", "rating": 603, "opRating": 396}, {"opponent": "giratina_origin", "rating": 530, "opRating": 469}], "counters": [{"opponent": "zacian_hero", "rating": 239}, {"opponent": "dragonite", "rating": 239}, {"opponent": "dialga", "rating": 285}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "mewtwo", "rating": 414}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 28586}, {"moveId": "TACKLE", "uses": 28579}, {"moveId": "BITE", "uses": 19305}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 47795}, {"moveId": "CRUNCH", "uses": 28705}]}, "moveset": ["TACKLE", "BODY_SLAM", "CRUNCH"], "score": 50.9}, {"speciesId": "latias_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 658, "matchups": [{"opponent": "haxorus", "rating": 851, "opRating": 148}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 793, "opRating": 206}, {"opponent": "machamp_shadow", "rating": 770, "opRating": 229}, {"opponent": "kyogre", "rating": 595, "opRating": 404}, {"opponent": "snorlax", "rating": 561, "opRating": 438}], "counters": [{"opponent": "zacian_hero", "rating": 182}, {"opponent": "lugia", "rating": 223}, {"opponent": "metagross", "rating": 273}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "dialga", "rating": 342}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47363}, {"moveId": "CHARM", "uses": 22869}, {"moveId": "ZEN_HEADBUTT", "uses": 6253}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 24405}, {"moveId": "OUTRAGE", "uses": 22055}, {"moveId": "PSYCHIC", "uses": 16223}, {"moveId": "THUNDER", "uses": 13739}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 50.9}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 736, "matchups": [{"opponent": "genesect_chill", "rating": 875, "opRating": 125}, {"opponent": "metagross", "rating": 805, "opRating": 194}, {"opponent": "yveltal", "rating": 671, "opRating": 328}, {"opponent": "dialga", "rating": 595}, {"opponent": "snorlax", "rating": 566, "opRating": 433}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "lugia", "rating": 235}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "gyarados", "rating": 247}, {"opponent": "garcho<PERSON>", "rating": 305}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46293}, {"moveId": "FIRE_SPIN", "uses": 30207}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 21226}, {"moveId": "BRAVE_BIRD", "uses": 16005}, {"moveId": "BLAZE_KICK", "uses": 13113}, {"moveId": "STONE_EDGE", "uses": 11839}, {"moveId": "FOCUS_BLAST", "uses": 9829}, {"moveId": "OVERHEAT", "uses": 4524}]}, "moveset": ["COUNTER", "BLAZE_KICK", "BLAST_BURN"], "score": 50.8}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 597, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "swampert", "rating": 811, "opRating": 188}, {"opponent": "dragonite", "rating": 690, "opRating": 309}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 610, "opRating": 389}, {"opponent": "excadrill", "rating": 556, "opRating": 443}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "zacian_hero", "rating": 242}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "dialga", "rating": 355}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 54283}, {"moveId": "RAZOR_LEAF", "uses": 22217}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39299}, {"moveId": "ENERGY_BALL", "uses": 14452}, {"moveId": "OUTRAGE", "uses": 10294}, {"moveId": "RETURN", "uses": 6598}, {"moveId": "BLIZZARD", "uses": 5986}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 50.7}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 633, "matchups": [{"opponent": "weavile", "rating": 904, "opRating": 95}, {"opponent": "weavile_shadow", "rating": 889, "opRating": 110}, {"opponent": "gengar", "rating": 739, "opRating": 260}, {"opponent": "espeon", "rating": 690, "opRating": 309}, {"opponent": "swampert", "rating": 608, "opRating": 391}], "counters": [{"opponent": "zacian_hero", "rating": 173}, {"opponent": "lugia", "rating": 273}, {"opponent": "gyarados", "rating": 275}, {"opponent": "dialga", "rating": 315}, {"opponent": "mewtwo", "rating": 437}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 65875}, {"moveId": "ZEN_HEADBUTT", "uses": 10625}], "chargedMoves": [{"moveId": "STOMP", "uses": 26193}, {"moveId": "MEGAHORN", "uses": 21399}, {"moveId": "EARTHQUAKE", "uses": 19749}, {"moveId": "SKULL_BASH", "uses": 9108}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 50.4}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 656, "matchups": [{"opponent": "electivire_shadow", "rating": 973, "opRating": 26}, {"opponent": "magnezone_shadow", "rating": 950, "opRating": 49}, {"opponent": "raikou_shadow", "rating": 938, "opRating": 61}, {"opponent": "excadrill", "rating": 904, "opRating": 95}, {"opponent": "metagross", "rating": 732, "opRating": 267}], "counters": [{"opponent": "lugia", "rating": 228}, {"opponent": "mewtwo", "rating": 278}, {"opponent": "gyarados", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 394}, {"opponent": "dialga", "rating": 410}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39909}, {"moveId": "DRAGON_TAIL", "uses": 36591}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27581}, {"moveId": "EARTH_POWER", "uses": 18515}, {"moveId": "STONE_EDGE", "uses": 15667}, {"moveId": "EARTHQUAKE", "uses": 7868}, {"moveId": "RETURN", "uses": 6734}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 50.1}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 774, "matchups": [{"opponent": "gyarados", "rating": 888}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 828, "opRating": 171}, {"opponent": "lugia", "rating": 783}, {"opponent": "mewtwo", "rating": 671}, {"opponent": "metagross", "rating": 656}], "counters": [{"opponent": "garcho<PERSON>", "rating": 110}, {"opponent": "excadrill", "rating": 134}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "dialga", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47597}, {"moveId": "CHARGE_BEAM", "uses": 28903}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37217}, {"moveId": "MIRROR_SHOT", "uses": 16867}, {"moveId": "FLASH_CANNON", "uses": 8817}, {"moveId": "RETURN", "uses": 7888}, {"moveId": "ZAP_CANNON", "uses": 5722}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 50.1}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 695, "matchups": [{"opponent": "metagross", "rating": 706}, {"opponent": "dialga", "rating": 661}, {"opponent": "zacian_hero", "rating": 582}, {"opponent": "excadrill", "rating": 556, "opRating": 443}, {"opponent": "giratina_origin", "rating": 535}], "counters": [{"opponent": "gyarados", "rating": 105}, {"opponent": "dragonite", "rating": 109}, {"opponent": "swampert", "rating": 211}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "mewtwo", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 40291}, {"moveId": "MUD_SLAP", "uses": 36209}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 24524}, {"moveId": "SUPER_POWER", "uses": 15091}, {"moveId": "SURF", "uses": 12716}, {"moveId": "EARTHQUAKE", "uses": 11750}, {"moveId": "STONE_EDGE", "uses": 6953}, {"moveId": "SKULL_BASH", "uses": 5457}]}, "moveset": ["MUD_SLAP", "ROCK_WRECKER", "SURF"], "score": 50.1}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 612, "matchups": [{"opponent": "moltres_shadow", "rating": 868, "opRating": 131}, {"opponent": "gyarados", "rating": 793, "opRating": 206}, {"opponent": "gyarado<PERSON>_shadow", "rating": 741, "opRating": 258}, {"opponent": "metagross", "rating": 596, "opRating": 403}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 591, "opRating": 408}], "counters": [{"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "mewtwo", "rating": 382}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28938}, {"moveId": "FIRE_FANG", "uses": 20611}, {"moveId": "THUNDER_FANG", "uses": 15075}, {"moveId": "BITE", "uses": 11842}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20993}, {"moveId": "CRUNCH", "uses": 14871}, {"moveId": "PSYCHIC_FANGS", "uses": 13487}, {"moveId": "FLAMETHROWER", "uses": 12384}, {"moveId": "BULLDOZE", "uses": 6328}, {"moveId": "RETURN", "uses": 5109}, {"moveId": "FIRE_BLAST", "uses": 3339}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 50}, {"speciesId": "clefable", "speciesName": "Clefable", "rating": 546, "matchups": [{"opponent": "dragonite", "rating": 739, "opRating": 260}, {"opponent": "palkia", "rating": 664, "opRating": 335}, {"opponent": "yveltal", "rating": 657, "opRating": 342}, {"opponent": "garcho<PERSON>", "rating": 538}, {"opponent": "zekrom", "rating": 507, "opRating": 492}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "lugia", "rating": 269}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "dialga", "rating": 331}, {"opponent": "giratina_origin", "rating": 360}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 41425}, {"moveId": "CHARGE_BEAM", "uses": 26339}, {"moveId": "ZEN_HEADBUTT", "uses": 5634}, {"moveId": "POUND", "uses": 3090}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28514}, {"moveId": "METEOR_MASH", "uses": 25673}, {"moveId": "PSYCHIC", "uses": 14999}, {"moveId": "DAZZLING_GLEAM", "uses": 7275}]}, "moveset": ["CHARM", "MOONBLAST", "METEOR_MASH"], "score": 50}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 651, "matchups": [{"opponent": "yveltal", "rating": 859, "opRating": 140}, {"opponent": "gyarados", "rating": 674}, {"opponent": "kyogre", "rating": 662, "opRating": 337}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 573, "opRating": 426}, {"opponent": "giratina_origin", "rating": 522}], "counters": [{"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "grou<PERSON>", "rating": 214}, {"opponent": "mewtwo", "rating": 278}, {"opponent": "dialga", "rating": 279}, {"opponent": "zacian_hero", "rating": 364}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9291}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4990}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4453}, {"moveId": "CHARGE_BEAM", "uses": 4426}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4295}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4230}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4215}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4041}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3977}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3915}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3849}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3829}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3796}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3555}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3539}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3488}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3360}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3243}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 30718}, {"moveId": "ZAP_CANNON", "uses": 18352}, {"moveId": "HYPER_BEAM", "uses": 14999}, {"moveId": "SOLAR_BEAM", "uses": 12289}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 49.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 677, "matchups": [{"opponent": "excadrill", "rating": 848, "opRating": 151}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 774, "opRating": 225}, {"opponent": "cobalion", "rating": 722, "opRating": 277}, {"opponent": "metagross", "rating": 685, "opRating": 314}, {"opponent": "dialga", "rating": 551}], "counters": [{"opponent": "giratina_origin", "rating": 137}, {"opponent": "gyarados", "rating": 239}, {"opponent": "dragonite", "rating": 244}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "garcho<PERSON>", "rating": 319}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29140}, {"moveId": "PSYCHO_CUT", "uses": 26105}, {"moveId": "CONFUSION", "uses": 21260}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16628}, {"moveId": "FIRE_PUNCH", "uses": 15304}, {"moveId": "PSYCHIC", "uses": 13970}, {"moveId": "FOCUS_BLAST", "uses": 10001}, {"moveId": "DAZZLING_GLEAM", "uses": 7449}, {"moveId": "RETURN", "uses": 7103}, {"moveId": "FUTURE_SIGHT", "uses": 6031}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 49.5}, {"speciesId": "blastoise", "speciesName": "Blastoise", "rating": 622, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 891, "opRating": 108}, {"opponent": "mamos<PERSON>_shadow", "rating": 855, "opRating": 144}, {"opponent": "excadrill", "rating": 676, "opRating": 323}, {"opponent": "garcho<PERSON>", "rating": 558}, {"opponent": "zacian_hero", "rating": 532, "opRating": 467}], "counters": [{"opponent": "giratina_origin", "rating": 113}, {"opponent": "dragonite", "rating": 228}, {"opponent": "dialga", "rating": 315}, {"opponent": "gyarados", "rating": 345}, {"opponent": "mewtwo", "rating": 398}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 50846}, {"moveId": "BITE", "uses": 25654}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37011}, {"moveId": "ICE_BEAM", "uses": 15605}, {"moveId": "SKULL_BASH", "uses": 8003}, {"moveId": "RETURN", "uses": 6686}, {"moveId": "FLASH_CANNON", "uses": 5259}, {"moveId": "HYDRO_PUMP", "uses": 4036}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 49.5}, {"speciesId": "poliwrath_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 639, "matchups": [{"opponent": "excadrill", "rating": 911, "opRating": 88}, {"opponent": "snorlax", "rating": 591, "opRating": 408}, {"opponent": "metagross", "rating": 567, "opRating": 432}, {"opponent": "dialga", "rating": 545}, {"opponent": "zekrom", "rating": 545, "opRating": 454}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "giratina_origin", "rating": 207}, {"opponent": "lugia", "rating": 233}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "garcho<PERSON>", "rating": 333}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36021}, {"moveId": "BUBBLE", "uses": 29936}, {"moveId": "ROCK_SMASH", "uses": 10545}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 21672}, {"moveId": "DYNAMIC_PUNCH", "uses": 20057}, {"moveId": "SCALD", "uses": 19224}, {"moveId": "POWER_UP_PUNCH", "uses": 6755}, {"moveId": "SUBMISSION", "uses": 4552}, {"moveId": "HYDRO_PUMP", "uses": 4412}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 49.5}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 713, "matchups": [{"opponent": "swampert", "rating": 845, "opRating": 154}, {"opponent": "excadrill", "rating": 791, "opRating": 208}, {"opponent": "zacian_hero", "rating": 736}, {"opponent": "gyarados", "rating": 718}, {"opponent": "garcho<PERSON>", "rating": 664}], "counters": [{"opponent": "giratina_origin", "rating": 153}, {"opponent": "metagross", "rating": 168}, {"opponent": "dialga", "rating": 187}, {"opponent": "lugia", "rating": 235}, {"opponent": "dragonite", "rating": 303}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 29871}, {"moveId": "CONFUSION", "uses": 29617}, {"moveId": "CHARGE_BEAM", "uses": 16969}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 22118}, {"moveId": "PSYCHIC", "uses": 20069}, {"moveId": "LEAF_STORM", "uses": 15090}, {"moveId": "DAZZLING_GLEAM", "uses": 11043}, {"moveId": "HYPER_BEAM", "uses": 8082}]}, "moveset": ["CONFUSION", "SEED_BOMB", "LEAF_STORM"], "score": 49.4}, {"speciesId": "tyranitar", "speciesName": "Tyranitar", "rating": 673, "matchups": [{"opponent": "ho_oh", "rating": 907, "opRating": 92}, {"opponent": "mewtwo", "rating": 773}, {"opponent": "giratina_origin", "rating": 716}, {"opponent": "lugia", "rating": 711, "opRating": 288}, {"opponent": "gyarados", "rating": 604, "opRating": 395}], "counters": [{"opponent": "excadrill", "rating": 165}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "metagross", "rating": 226}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 41948}, {"moveId": "BITE", "uses": 27251}, {"moveId": "IRON_TAIL", "uses": 7290}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 33528}, {"moveId": "STONE_EDGE", "uses": 25488}, {"moveId": "RETURN", "uses": 9051}, {"moveId": "FIRE_BLAST", "uses": 8420}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 49.4}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 647, "matchups": [{"opponent": "garcho<PERSON>", "rating": 896}, {"opponent": "kyogre", "rating": 811, "opRating": 188}, {"opponent": "grou<PERSON>", "rating": 716, "opRating": 283}, {"opponent": "swampert", "rating": 662, "opRating": 337}, {"opponent": "excadrill", "rating": 636, "opRating": 363}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "lugia", "rating": 276}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "gyarados", "rating": 311}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42436}, {"moveId": "BULLET_SEED", "uses": 34064}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26941}, {"moveId": "DRAGON_PULSE", "uses": 19511}, {"moveId": "DRACO_METEOR", "uses": 17435}, {"moveId": "SOLAR_BEAM", "uses": 12506}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 49.2}, {"speciesId": "rapidash_galarian", "speciesName": "Rapidash (Galarian)", "rating": 583, "matchups": [{"opponent": "hydreigon", "rating": 872, "opRating": 127}, {"opponent": "mewtwo_shadow", "rating": 862, "opRating": 137}, {"opponent": "latios_shadow", "rating": 825, "opRating": 174}, {"opponent": "dragonite", "rating": 687, "opRating": 312}, {"opponent": "palkia", "rating": 540, "opRating": 459}], "counters": [{"opponent": "giratina_origin", "rating": 211}, {"opponent": "metagross", "rating": 264}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "dialga", "rating": 326}, {"opponent": "mewtwo", "rating": 424}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 36364}, {"moveId": "PSYCHO_CUT", "uses": 32401}, {"moveId": "LOW_KICK", "uses": 7719}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27416}, {"moveId": "MEGAHORN", "uses": 17179}, {"moveId": "PSYCHIC", "uses": 16832}, {"moveId": "PLAY_ROUGH", "uses": 15033}]}, "moveset": ["FAIRY_WIND", "BODY_SLAM", "MEGAHORN"], "score": 49.2}, {"speciesId": "salamence", "speciesName": "Salamence", "rating": 643, "matchups": [{"opponent": "haxorus", "rating": 842, "opRating": 157}, {"opponent": "swampert", "rating": 657, "opRating": 342}, {"opponent": "grou<PERSON>", "rating": 634, "opRating": 365}, {"opponent": "yveltal", "rating": 577, "opRating": 422}, {"opponent": "kyogre", "rating": 559, "opRating": 440}], "counters": [{"opponent": "zacian_hero", "rating": 147}, {"opponent": "metagross", "rating": 235}, {"opponent": "lugia", "rating": 240}, {"opponent": "dialga", "rating": 307}, {"opponent": "mewtwo", "rating": 419}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43025}, {"moveId": "FIRE_FANG", "uses": 19985}, {"moveId": "BITE", "uses": 13411}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 29308}, {"moveId": "RETURN", "uses": 13027}, {"moveId": "FIRE_BLAST", "uses": 12802}, {"moveId": "HYDRO_PUMP", "uses": 12566}, {"moveId": "DRACO_METEOR", "uses": 8800}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "RETURN"], "score": 49.2}, {"speciesId": "scyther_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 587, "matchups": [{"opponent": "zarude", "rating": 901, "opRating": 98}, {"opponent": "tangrowth_shadow", "rating": 891, "opRating": 108}, {"opponent": "tangrowth", "rating": 882, "opRating": 117}, {"opponent": "mewtwo_shadow", "rating": 805, "opRating": 194}, {"opponent": "metagross", "rating": 595, "opRating": 404}], "counters": [{"opponent": "gyarados", "rating": 239}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "dialga", "rating": 355}, {"opponent": "garcho<PERSON>", "rating": 396}, {"opponent": "mewtwo", "rating": 419}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35952}, {"moveId": "AIR_SLASH", "uses": 25064}, {"moveId": "STEEL_WING", "uses": 15410}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28329}, {"moveId": "X_SCISSOR", "uses": 20071}, {"moveId": "AERIAL_ACE", "uses": 14973}, {"moveId": "BUG_BUZZ", "uses": 13072}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 49.2}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 667, "matchups": [{"opponent": "ho_oh", "rating": 786, "opRating": 213}, {"opponent": "nihilego", "rating": 750, "opRating": 250}, {"opponent": "sylveon", "rating": 668, "opRating": 331}, {"opponent": "mewtwo_shadow", "rating": 654, "opRating": 345}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 632, "opRating": 367}], "counters": [{"opponent": "dragonite", "rating": 151}, {"opponent": "giratina_origin", "rating": 215}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "zacian_hero", "rating": 248}, {"opponent": "dialga", "rating": 320}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 43816}, {"moveId": "METAL_CLAW", "uses": 32684}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 35918}, {"moveId": "DRILL_PECK", "uses": 19059}, {"moveId": "BLIZZARD", "uses": 10615}, {"moveId": "FLASH_CANNON", "uses": 7105}, {"moveId": "HYDRO_PUMP", "uses": 3853}]}, "moveset": ["WATERFALL", "HYDRO_CANNON", "FLASH_CANNON"], "score": 49.1}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 694, "matchups": [{"opponent": "excadrill", "rating": 814, "opRating": 185}, {"opponent": "metagross", "rating": 783, "opRating": 216}, {"opponent": "gyarados", "rating": 740, "opRating": 259}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 594, "opRating": 405}, {"opponent": "dialga", "rating": 570}], "counters": [{"opponent": "giratina_origin", "rating": 119}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "lugia", "rating": 254}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39819}, {"moveId": "FIRE_SPIN", "uses": 36681}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23057}, {"moveId": "BRICK_BREAK", "uses": 15635}, {"moveId": "THUNDERBOLT", "uses": 13331}, {"moveId": "PSYCHIC", "uses": 11749}, {"moveId": "RETURN", "uses": 8208}, {"moveId": "FIRE_BLAST", "uses": 4693}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 49}, {"speciesId": "seaking", "speciesName": "Seaking", "rating": 509, "matchups": [{"opponent": "darmanitan_standard", "rating": 819, "opRating": 180}, {"opponent": "tapu_bulu", "rating": 784, "opRating": 215}, {"opponent": "tapu_koko", "rating": 677, "opRating": 322}, {"opponent": "magmortar_shadow", "rating": 668, "opRating": 331}, {"opponent": "garcho<PERSON>", "rating": 523}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "metagross", "rating": 287}, {"opponent": "giratina_origin", "rating": 330}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "gyarados", "rating": 384}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 33514}, {"moveId": "WATERFALL", "uses": 29360}, {"moveId": "PECK", "uses": 13633}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 23993}, {"moveId": "ICY_WIND", "uses": 23338}, {"moveId": "MEGAHORN", "uses": 14863}, {"moveId": "ICE_BEAM", "uses": 7525}, {"moveId": "WATER_PULSE", "uses": 6845}]}, "moveset": ["POISON_JAB", "DRILL_RUN", "ICY_WIND"], "score": 49}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 613, "matchups": [{"opponent": "gengar", "rating": 751, "opRating": 248}, {"opponent": "mewtwo", "rating": 668}, {"opponent": "gallade_shadow", "rating": 659, "opRating": 340}, {"opponent": "mewtwo_shadow", "rating": 627, "opRating": 372}, {"opponent": "giratina_origin", "rating": 582, "opRating": 417}], "counters": [{"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "excadrill", "rating": 188}, {"opponent": "dragonite", "rating": 231}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "dialga", "rating": 304}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24549}, {"moveId": "INFESTATION", "uses": 18920}, {"moveId": "ICE_FANG", "uses": 17811}, {"moveId": "BITE", "uses": 15175}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28372}, {"moveId": "AQUA_TAIL", "uses": 20670}, {"moveId": "SLUDGE_BOMB", "uses": 15416}, {"moveId": "RETURN", "uses": 7092}, {"moveId": "FELL_STINGER", "uses": 5019}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 48.8}, {"speciesId": "darmanitan_galarian_standard", "speciesName": "Dar<PERSON><PERSON> (Galarian)", "rating": 565, "matchups": [{"opponent": "land<PERSON><PERSON>_therian", "rating": 887, "opRating": 112}, {"opponent": "landorus_incarnate", "rating": 882, "opRating": 117}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "dragonite", "rating": 694, "opRating": 305}, {"opponent": "lugia", "rating": 661, "opRating": 338}], "counters": [{"opponent": "metagross", "rating": 191}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "gyarados", "rating": 280}, {"opponent": "dialga", "rating": 285}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 43351}, {"moveId": "TACKLE", "uses": 33149}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30935}, {"moveId": "ICE_PUNCH", "uses": 18577}, {"moveId": "SUPER_POWER", "uses": 18555}, {"moveId": "OVERHEAT", "uses": 8407}]}, "moveset": ["ICE_FANG", "AVALANCHE", "ICE_PUNCH"], "score": 48.4}, {"speciesId": "articuno_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 785, "matchups": [{"opponent": "ho_oh", "rating": 795, "opRating": 204}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 634, "opRating": 365}, {"opponent": "gyarados", "rating": 580, "opRating": 419}, {"opponent": "zacian_hero", "rating": 553, "opRating": 446}, {"opponent": "lugia", "rating": 502, "opRating": 497}], "counters": [{"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "metagross", "rating": 247}, {"opponent": "dialga", "rating": 277}, {"opponent": "giratina_origin", "rating": 356}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 43761}, {"moveId": "CONFUSION", "uses": 32739}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 41522}, {"moveId": "ANCIENT_POWER", "uses": 18983}, {"moveId": "FUTURE_SIGHT", "uses": 16003}]}, "moveset": ["PSYCHO_CUT", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 48.3}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 652, "matchups": [{"opponent": "raikou_shadow", "rating": 889, "opRating": 110}, {"opponent": "swampert", "rating": 855, "opRating": 144}, {"opponent": "kyogre", "rating": 711, "opRating": 288}, {"opponent": "snorlax", "rating": 592, "opRating": 407}, {"opponent": "grou<PERSON>", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "lugia", "rating": 242}, {"opponent": "metagross", "rating": 287}, {"opponent": "mewtwo", "rating": 414}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42748}, {"moveId": "BULLET_SEED", "uses": 33752}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 23496}, {"moveId": "DRAGON_PULSE", "uses": 16620}, {"moveId": "DRACO_METEOR", "uses": 14874}, {"moveId": "SOLAR_BEAM", "uses": 10705}, {"moveId": "RETURN", "uses": 10621}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 48.3}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 638, "matchups": [{"opponent": "giratina_origin", "rating": 857}, {"opponent": "metagross", "rating": 634, "opRating": 365}, {"opponent": "dialga", "rating": 591}, {"opponent": "giratina_altered", "rating": 545, "opRating": 454}, {"opponent": "zekrom", "rating": 508, "opRating": 491}], "counters": [{"opponent": "zacian_hero", "rating": 184}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dragonite", "rating": 194}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34821}, {"moveId": "COUNTER", "uses": 30643}, {"moveId": "METAL_CLAW", "uses": 11027}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 43704}, {"moveId": "PLAY_ROUGH", "uses": 17087}, {"moveId": "HYPER_BEAM", "uses": 15434}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 48.1}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 640, "matchups": [{"opponent": "gengar", "rating": 853, "opRating": 146}, {"opponent": "metagross", "rating": 640, "opRating": 359}, {"opponent": "mewtwo", "rating": 627}, {"opponent": "mewtwo_shadow", "rating": 563, "opRating": 436}, {"opponent": "giratina_origin", "rating": 560, "opRating": 439}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "dragonite", "rating": 257}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "gyarados", "rating": 322}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24832}, {"moveId": "INFESTATION", "uses": 18837}, {"moveId": "ICE_FANG", "uses": 17707}, {"moveId": "BITE", "uses": 15047}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31070}, {"moveId": "AQUA_TAIL", "uses": 22765}, {"moveId": "SLUDGE_BOMB", "uses": 17247}, {"moveId": "FELL_STINGER", "uses": 5422}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 47.7}, {"speciesId": "d<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 644, "matchups": [{"opponent": "xurkitree", "rating": 889, "opRating": 110}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "thundurus_therian", "rating": 817, "opRating": 182}, {"opponent": "kyogre", "rating": 520, "opRating": 479}, {"opponent": "swampert", "rating": 505, "opRating": 494}], "counters": [{"opponent": "zacian_hero", "rating": 173}, {"opponent": "dialga", "rating": 211}, {"opponent": "lugia", "rating": 261}, {"opponent": "metagross", "rating": 319}, {"opponent": "mewtwo", "rating": 380}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 52854}, {"moveId": "BITE", "uses": 23646}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 35911}, {"moveId": "NIGHT_SLASH", "uses": 32832}, {"moveId": "HYPER_BEAM", "uses": 7698}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 47.7}, {"speciesId": "poliwrath", "speciesName": "Poliwrath", "rating": 625, "matchups": [{"opponent": "magnezone_shadow", "rating": 825, "opRating": 174}, {"opponent": "excadrill", "rating": 639, "opRating": 360}, {"opponent": "snorlax", "rating": 599, "opRating": 400}, {"opponent": "dialga", "rating": 567}, {"opponent": "kyogre", "rating": 534, "opRating": 465}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "lugia", "rating": 202}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "giratina_origin", "rating": 280}, {"opponent": "gyarados", "rating": 324}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 35241}, {"moveId": "BUBBLE", "uses": 30062}, {"moveId": "ROCK_SMASH", "uses": 11259}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 19771}, {"moveId": "DYNAMIC_PUNCH", "uses": 18483}, {"moveId": "SCALD", "uses": 17532}, {"moveId": "RETURN", "uses": 6447}, {"moveId": "POWER_UP_PUNCH", "uses": 6222}, {"moveId": "SUBMISSION", "uses": 4074}, {"moveId": "HYDRO_PUMP", "uses": 3960}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 47.7}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 592, "matchups": [{"opponent": "gengar", "rating": 909, "opRating": 90}, {"opponent": "excadrill", "rating": 872, "opRating": 127}, {"opponent": "magnezone_shadow", "rating": 822, "opRating": 177}, {"opponent": "giratina_origin", "rating": 621, "opRating": 378}, {"opponent": "metagross", "rating": 593, "opRating": 406}], "counters": [{"opponent": "zacian_hero", "rating": 193}, {"opponent": "gyarados", "rating": 257}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "dialga", "rating": 451}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45505}, {"moveId": "FURY_CUTTER", "uses": 30995}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35969}, {"moveId": "NIGHT_SLASH", "uses": 35923}, {"moveId": "DIG", "uses": 4513}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 47.5}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 619, "matchups": [{"opponent": "metagross", "rating": 761}, {"opponent": "mewtwo", "rating": 728}, {"opponent": "giratina_altered", "rating": 620, "opRating": 379}, {"opponent": "lugia", "rating": 600, "opRating": 399}, {"opponent": "giratina_origin", "rating": 560}], "counters": [{"opponent": "zacian_hero", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dragonite", "rating": 303}, {"opponent": "gyarados", "rating": 389}, {"opponent": "dialga", "rating": 394}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 51419}, {"moveId": "METAL_CLAW", "uses": 25081}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27648}, {"moveId": "X_SCISSOR", "uses": 18497}, {"moveId": "IRON_HEAD", "uses": 17263}, {"moveId": "FOCUS_BLAST", "uses": 13126}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 47.4}, {"speciesId": "blastoise_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 623, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 855, "opRating": 144}, {"opponent": "entei", "rating": 844, "opRating": 155}, {"opponent": "ho_oh", "rating": 835, "opRating": 164}, {"opponent": "grou<PERSON>", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 597, "opRating": 402}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "gyarados", "rating": 255}, {"opponent": "dialga", "rating": 339}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "mewtwo", "rating": 390}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51274}, {"moveId": "BITE", "uses": 25226}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 40309}, {"moveId": "ICE_BEAM", "uses": 16991}, {"moveId": "SKULL_BASH", "uses": 8985}, {"moveId": "FLASH_CANNON", "uses": 5884}, {"moveId": "HYDRO_PUMP", "uses": 4204}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 47.3}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 668, "matchups": [{"opponent": "metagross", "rating": 750}, {"opponent": "mewtwo", "rating": 729}, {"opponent": "giratina_origin", "rating": 641}, {"opponent": "dialga", "rating": 626}, {"opponent": "excadrill", "rating": 621, "opRating": 378}], "counters": [{"opponent": "zacian_hero", "rating": 72}, {"opponent": "grou<PERSON>", "rating": 239}, {"opponent": "dragonite", "rating": 276}, {"opponent": "gyarados", "rating": 278}, {"opponent": "garcho<PERSON>", "rating": 366}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46577}, {"moveId": "MUD_SLAP", "uses": 29923}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36924}, {"moveId": "EARTHQUAKE", "uses": 23061}, {"moveId": "OUTRAGE", "uses": 16506}]}, "moveset": ["SNARL", "CRUNCH", "EARTHQUAKE"], "score": 47.3}, {"speciesId": "primeape", "speciesName": "Primeape", "rating": 625, "matchups": [{"opponent": "snorlax", "rating": 765, "opRating": 234}, {"opponent": "genesect_douse", "rating": 718, "opRating": 281}, {"opponent": "genesect_shock", "rating": 718, "opRating": 281}, {"opponent": "dialga", "rating": 593}, {"opponent": "metagross", "rating": 573, "opRating": 426}], "counters": [{"opponent": "giratina_origin", "rating": 183}, {"opponent": "lugia", "rating": 245}, {"opponent": "mewtwo", "rating": 273}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 396}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37345}, {"moveId": "KARATE_CHOP", "uses": 34460}, {"moveId": "LOW_KICK", "uses": 4690}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23347}, {"moveId": "NIGHT_SLASH", "uses": 18723}, {"moveId": "CROSS_CHOP", "uses": 16155}, {"moveId": "ICE_PUNCH", "uses": 15214}, {"moveId": "LOW_SWEEP", "uses": 3024}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 47.3}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 627, "matchups": [{"opponent": "garcho<PERSON>", "rating": 814}, {"opponent": "dragonite", "rating": 804, "opRating": 195}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "zekrom", "rating": 750, "opRating": 250}, {"opponent": "lugia", "rating": 664, "opRating": 335}], "counters": [{"opponent": "metagross", "rating": 148}, {"opponent": "zacian_hero", "rating": 242}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "mewtwo", "rating": 437}, {"opponent": "dialga", "rating": 440}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32479}, {"moveId": "SHADOW_CLAW", "uses": 30535}, {"moveId": "METAL_CLAW", "uses": 13524}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 30526}, {"moveId": "BLIZZARD", "uses": 15865}, {"moveId": "BULLDOZE", "uses": 11472}, {"moveId": "GYRO_BALL", "uses": 9804}, {"moveId": "RETURN", "uses": 8984}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 47.3}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 605, "matchups": [{"opponent": "tapu_bulu", "rating": 895, "opRating": 104}, {"opponent": "zarude", "rating": 821, "opRating": 178}, {"opponent": "mewtwo", "rating": 595}, {"opponent": "sylveon", "rating": 548, "opRating": 451}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509, "opRating": 490}], "counters": [{"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "dialga", "rating": 266}, {"opponent": "lugia", "rating": 335}, {"opponent": "metagross", "rating": 375}, {"opponent": "giratina_origin", "rating": 388}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 48134}, {"moveId": "BITE", "uses": 28366}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38953}, {"moveId": "SLUDGE_BOMB", "uses": 21343}, {"moveId": "FLAMETHROWER", "uses": 16107}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 47.3}, {"speciesId": "aromatisse", "speciesName": "Aromatisse", "rating": 515, "matchups": [{"opponent": "kommo_o", "rating": 908, "opRating": 91}, {"opponent": "dragonite", "rating": 653, "opRating": 346}, {"opponent": "yveltal", "rating": 646, "opRating": 353}, {"opponent": "palkia", "rating": 646, "opRating": 353}, {"opponent": "garcho<PERSON>", "rating": 537, "opRating": 462}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "lugia", "rating": 269}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "dialga", "rating": 331}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46608}, {"moveId": "CHARGE_BEAM", "uses": 29892}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 30010}, {"moveId": "THUNDERBOLT", "uses": 18546}, {"moveId": "PSYCHIC", "uses": 15747}, {"moveId": "DRAINING_KISS", "uses": 12175}]}, "moveset": ["CHARM", "MOONBLAST", "THUNDERBOLT"], "score": 47.1}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 611, "matchups": [{"opponent": "pinsir_shadow", "rating": 823, "opRating": 176}, {"opponent": "zarude", "rating": 777, "opRating": 222}, {"opponent": "ho_oh", "rating": 753, "opRating": 246}, {"opponent": "zapdos", "rating": 618, "opRating": 381}, {"opponent": "zacian_hero", "rating": 545, "opRating": 454}], "counters": [{"opponent": "giratina_origin", "rating": 193}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "metagross", "rating": 305}, {"opponent": "dialga", "rating": 312}, {"opponent": "mewtwo", "rating": 375}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51347}, {"moveId": "STRUGGLE_BUG", "uses": 25153}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 29059}, {"moveId": "CROSS_POISON", "uses": 26920}, {"moveId": "RETURN", "uses": 11289}, {"moveId": "WATER_PULSE", "uses": 9272}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 47}, {"speciesId": "relicanth", "speciesName": "Relicanth", "rating": 599, "matchups": [{"opponent": "ho_oh", "rating": 937, "opRating": 62}, {"opponent": "ma<PERSON><PERSON>", "rating": 885, "opRating": 114}, {"opponent": "mamos<PERSON>_shadow", "rating": 858, "opRating": 141}, {"opponent": "excadrill", "rating": 514, "opRating": 485}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dragonite", "rating": 191}, {"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "giratina_origin", "rating": 237}, {"opponent": "dialga", "rating": 290}, {"opponent": "mewtwo", "rating": 356}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66186}, {"moveId": "ZEN_HEADBUTT", "uses": 10314}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 40572}, {"moveId": "ANCIENT_POWER", "uses": 29638}, {"moveId": "HYDRO_PUMP", "uses": 6318}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "ANCIENT_POWER"], "score": 47}, {"speciesId": "thundurus_therian", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Therian)", "rating": 721, "matchups": [{"opponent": "metagross", "rating": 776}, {"opponent": "lugia", "rating": 732}, {"opponent": "gyarados", "rating": 714}, {"opponent": "dragonite", "rating": 644}, {"opponent": "excadrill", "rating": 520, "opRating": 479}], "counters": [{"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "giratina_origin", "rating": 245}, {"opponent": "dialga", "rating": 339}, {"opponent": "zacian_hero", "rating": 497}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 57091}, {"moveId": "BITE", "uses": 19409}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27780}, {"moveId": "FOCUS_BLAST", "uses": 21844}, {"moveId": "SLUDGE_WAVE", "uses": 14847}, {"moveId": "THUNDER", "uses": 12036}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "FOCUS_BLAST"], "score": 47}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 700, "matchups": [{"opponent": "giratina_origin", "rating": 873}, {"opponent": "garcho<PERSON>", "rating": 842}, {"opponent": "gyarados", "rating": 688}, {"opponent": "dragonite", "rating": 564}, {"opponent": "excadrill", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "metagross", "rating": 319}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "lugia", "rating": 430}, {"opponent": "zacian_hero", "rating": 494}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 12974}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4974}, {"moveId": "CHARGE_BEAM", "uses": 4700}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4295}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4034}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3961}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3947}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3863}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3787}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3673}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3616}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3609}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3574}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3353}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3286}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3270}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3105}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3002}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 23077}, {"moveId": "BLIZZARD", "uses": 17612}, {"moveId": "ZAP_CANNON", "uses": 14701}, {"moveId": "HYPER_BEAM", "uses": 11273}, {"moveId": "SOLAR_BEAM", "uses": 9648}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 46.8}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 660, "matchups": [{"opponent": "swampert", "rating": 834, "opRating": 165}, {"opponent": "excadrill", "rating": 809, "opRating": 190}, {"opponent": "gyarados", "rating": 622, "opRating": 377}, {"opponent": "garcho<PERSON>", "rating": 608}, {"opponent": "dialga", "rating": 559}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 145}, {"opponent": "dragonite", "rating": 156}, {"opponent": "giratina_origin", "rating": 197}, {"opponent": "zacian_hero", "rating": 341}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42753}, {"moveId": "SMACK_DOWN", "uses": 24971}, {"moveId": "LOW_KICK", "uses": 8705}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 40600}, {"moveId": "ENERGY_BALL", "uses": 19893}, {"moveId": "GYRO_BALL", "uses": 10243}, {"moveId": "SOLAR_BEAM", "uses": 5821}]}, "moveset": ["VINE_WHIP", "SUPER_POWER", "ENERGY_BALL"], "score": 46.7}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 640, "matchups": [{"opponent": "ho_oh", "rating": 943, "opRating": 56}, {"opponent": "moltres", "rating": 920, "opRating": 79}, {"opponent": "gyarados", "rating": 801, "opRating": 198}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 685, "opRating": 314}, {"opponent": "sylveon", "rating": 610, "opRating": 389}], "counters": [{"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "dialga", "rating": 342}, {"opponent": "mewtwo", "rating": 359}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41576}, {"moveId": "FIRE_FANG", "uses": 25083}, {"moveId": "ROCK_SMASH", "uses": 9768}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22272}, {"moveId": "ROCK_SLIDE", "uses": 21161}, {"moveId": "CRUNCH", "uses": 17821}, {"moveId": "FLAMETHROWER", "uses": 15278}]}, "moveset": ["SNARL", "WILD_CHARGE", "ROCK_SLIDE"], "score": 46.6}, {"speciesId": "cloyster_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 575, "matchups": [{"opponent": "garcho<PERSON>", "rating": 842}, {"opponent": "yveltal", "rating": 622, "opRating": 377}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 602, "opRating": 397}, {"opponent": "zekrom", "rating": 566, "opRating": 433}, {"opponent": "excadrill", "rating": 559, "opRating": 440}], "counters": [{"opponent": "zacian_hero", "rating": 164}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "metagross", "rating": 276}, {"opponent": "dialga", "rating": 355}, {"opponent": "gyarados", "rating": 404}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 48334}, {"moveId": "FROST_BREATH", "uses": 28166}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 40010}, {"moveId": "ICY_WIND", "uses": 13925}, {"moveId": "HYDRO_PUMP", "uses": 11231}, {"moveId": "BLIZZARD", "uses": 6187}, {"moveId": "AURORA_BEAM", "uses": 5049}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 46.6}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 646, "matchups": [{"opponent": "zarude", "rating": 841, "opRating": 158}, {"opponent": "gyarados", "rating": 709, "opRating": 290}, {"opponent": "kyogre", "rating": 565, "opRating": 434}, {"opponent": "metagross", "rating": 541, "opRating": 458}, {"opponent": "yveltal", "rating": 511, "opRating": 488}], "counters": [{"opponent": "garcho<PERSON>", "rating": 138}, {"opponent": "dialga", "rating": 266}, {"opponent": "giratina_origin", "rating": 266}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "mewtwo", "rating": 320}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 31589}, {"moveId": "BUG_BITE", "uses": 23888}, {"moveId": "MUD_SLAP", "uses": 20973}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26875}, {"moveId": "CRUNCH", "uses": 25608}, {"moveId": "DISCHARGE", "uses": 24101}]}, "moveset": ["SPARK", "X_SCISSOR", "CRUNCH"], "score": 46.6}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 733, "matchups": [{"opponent": "metagross", "rating": 786, "opRating": 213}, {"opponent": "excadrill", "rating": 773, "opRating": 226}, {"opponent": "palkia", "rating": 729, "opRating": 270}, {"opponent": "dialga", "rating": 703}, {"opponent": "swampert", "rating": 525, "opRating": 474}], "counters": [{"opponent": "giratina_origin", "rating": 121}, {"opponent": "zacian_hero", "rating": 173}, {"opponent": "mewtwo", "rating": 177}, {"opponent": "dragonite", "rating": 194}, {"opponent": "garcho<PERSON>", "rating": 272}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45052}, {"moveId": "BULLET_PUNCH", "uses": 31448}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34750}, {"moveId": "SHADOW_BALL", "uses": 18785}, {"moveId": "FLASH_CANNON", "uses": 8960}, {"moveId": "AURA_SPHERE", "uses": 7790}, {"moveId": "POWER_UP_PUNCH", "uses": 6110}]}, "moveset": ["COUNTER", "POWER_UP_PUNCH", "SHADOW_BALL"], "score": 46}, {"speciesId": "scyther", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "tangrowth", "rating": 920, "opRating": 79}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 907, "opRating": 92}, {"opponent": "zarude", "rating": 882, "opRating": 117}, {"opponent": "tangrowth_shadow", "rating": 882, "opRating": 117}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 780, "opRating": 219}], "counters": [{"opponent": "zacian_hero", "rating": 208}, {"opponent": "dragonite", "rating": 220}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "dialga", "rating": 350}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35721}, {"moveId": "AIR_SLASH", "uses": 25141}, {"moveId": "STEEL_WING", "uses": 15667}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25838}, {"moveId": "X_SCISSOR", "uses": 18294}, {"moveId": "AERIAL_ACE", "uses": 13367}, {"moveId": "BUG_BUZZ", "uses": 11886}, {"moveId": "RETURN", "uses": 7044}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 46}, {"speciesId": "<PERSON><PERSON>_<PERSON>", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 637, "matchups": [{"opponent": "pinsir_shadow", "rating": 801, "opRating": 198}, {"opponent": "charizard", "rating": 801, "opRating": 198}, {"opponent": "zarude", "rating": 740, "opRating": 259}, {"opponent": "ho_oh", "rating": 707, "opRating": 292}, {"opponent": "zacian_hero", "rating": 509, "opRating": 490}], "counters": [{"opponent": "giratina_origin", "rating": 211}, {"opponent": "dialga", "rating": 228}, {"opponent": "metagross", "rating": 232}, {"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "mewtwo", "rating": 359}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50876}, {"moveId": "STRUGGLE_BUG", "uses": 25624}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 33869}, {"moveId": "CROSS_POISON", "uses": 31389}, {"moveId": "WATER_PULSE", "uses": 11122}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 45.9}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 553, "matchups": [{"opponent": "swampert", "rating": 771, "opRating": 228}, {"opponent": "gallade", "rating": 761, "opRating": 238}, {"opponent": "swampert_shadow", "rating": 748, "opRating": 251}, {"opponent": "latios_shadow", "rating": 627, "opRating": 372}, {"opponent": "zacian_hero", "rating": 583, "opRating": 416}], "counters": [{"opponent": "dragonite", "rating": 148}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "lugia", "rating": 283}, {"opponent": "dialga", "rating": 301}, {"opponent": "gyarados", "rating": 309}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49372}, {"moveId": "RAZOR_LEAF", "uses": 27128}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26353}, {"moveId": "SEED_BOMB", "uses": 21717}, {"moveId": "FOUL_PLAY", "uses": 19749}, {"moveId": "FIRE_BLAST", "uses": 8558}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 45.9}, {"speciesId": "scrafty", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 604, "matchups": [{"opponent": "weavile_shadow", "rating": 963, "opRating": 36}, {"opponent": "darkrai", "rating": 936, "opRating": 63}, {"opponent": "mamos<PERSON>_shadow", "rating": 795, "opRating": 204}, {"opponent": "excadrill", "rating": 654, "opRating": 345}, {"opponent": "dialga", "rating": 553}], "counters": [{"opponent": "zacian_hero", "rating": 187}, {"opponent": "lugia", "rating": 188}, {"opponent": "dragonite", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "mewtwo", "rating": 364}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43410}, {"moveId": "SNARL", "uses": 33090}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 50675}, {"moveId": "POWER_UP_PUNCH", "uses": 19155}, {"moveId": "ACID_SPRAY", "uses": 6709}]}, "moveset": ["COUNTER", "FOUL_PLAY", "POWER_UP_PUNCH"], "score": 45.9}, {"speciesId": "barbara<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 635, "matchups": [{"opponent": "moltres", "rating": 921, "opRating": 78}, {"opponent": "moltres_shadow", "rating": 921, "opRating": 78}, {"opponent": "ho_oh", "rating": 903, "opRating": 96}, {"opponent": "gyarado<PERSON>_shadow", "rating": 746, "opRating": 253}, {"opponent": "zap<PERSON>_shadow", "rating": 681, "opRating": 318}], "counters": [{"opponent": "zacian_hero", "rating": 150}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "dialga", "rating": 350}, {"opponent": "mewtwo", "rating": 453}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31130}, {"moveId": "WATER_GUN", "uses": 26736}, {"moveId": "MUD_SLAP", "uses": 18659}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26408}, {"moveId": "CROSS_CHOP", "uses": 24117}, {"moveId": "GRASS_KNOT", "uses": 16312}, {"moveId": "SKULL_BASH", "uses": 9642}]}, "moveset": ["FURY_CUTTER", "STONE_EDGE", "CROSS_CHOP"], "score": 45.7}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 672, "matchups": [{"opponent": "snorlax", "rating": 823, "opRating": 176}, {"opponent": "genesect_douse", "rating": 744, "opRating": 255}, {"opponent": "dialga", "rating": 661}, {"opponent": "swampert", "rating": 657, "opRating": 342}, {"opponent": "excadrill", "rating": 642, "opRating": 357}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 150}, {"opponent": "giratina_origin", "rating": 187}, {"opponent": "gyarados", "rating": 298}, {"opponent": "garcho<PERSON>", "rating": 399}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46943}, {"moveId": "BULLET_PUNCH", "uses": 29557}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29273}, {"moveId": "SUPER_POWER", "uses": 21761}, {"moveId": "HEAVY_SLAM", "uses": 10186}, {"moveId": "DYNAMIC_PUNCH", "uses": 7861}, {"moveId": "RETURN", "uses": 7385}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "HEAVY_SLAM"], "score": 45.7}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 566, "matchups": [{"opponent": "lucario", "rating": 901, "opRating": 98}, {"opponent": "metagross", "rating": 722}, {"opponent": "garcho<PERSON>", "rating": 680}, {"opponent": "zacian_hero", "rating": 593, "opRating": 406}, {"opponent": "grou<PERSON>", "rating": 585, "opRating": 414}], "counters": [{"opponent": "giratina_origin", "rating": 129}, {"opponent": "gyarados", "rating": 216}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "lugia", "rating": 311}, {"opponent": "dialga", "rating": 336}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55643}, {"moveId": "ASTONISH", "uses": 20857}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31417}, {"moveId": "SHADOW_BALL", "uses": 29829}, {"moveId": "OMINOUS_WIND", "uses": 15294}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 45.6}, {"speciesId": "<PERSON><PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 661, "matchups": [{"opponent": "metagross", "rating": 704}, {"opponent": "dialga", "rating": 601}, {"opponent": "excadrill", "rating": 590, "opRating": 409}, {"opponent": "zekrom", "rating": 552, "opRating": 447}, {"opponent": "swampert", "rating": 537, "opRating": 462}], "counters": [{"opponent": "lugia", "rating": 126}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 171}, {"opponent": "zacian_hero", "rating": 343}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 47095}, {"moveId": "BULLET_PUNCH", "uses": 29405}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32311}, {"moveId": "SUPER_POWER", "uses": 23841}, {"moveId": "HEAVY_SLAM", "uses": 11712}, {"moveId": "DYNAMIC_PUNCH", "uses": 8644}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "SUPER_POWER"], "score": 45.6}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 556, "matchups": [{"opponent": "giratina_origin", "rating": 775}, {"opponent": "garcho<PERSON>", "rating": 631}, {"opponent": "lugia", "rating": 595, "opRating": 404}, {"opponent": "sylveon", "rating": 537, "opRating": 462}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "metagross", "rating": 162}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "dialga", "rating": 241}, {"opponent": "gyarados", "rating": 304}, {"opponent": "zacian_hero", "rating": 315}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5901}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5573}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 5211}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 5079}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5070}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4990}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4912}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4825}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4504}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4496}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4440}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4141}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4085}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4056}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3899}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3798}, {"moveId": "ZEN_HEADBUTT", "uses": 1265}], "chargedMoves": [{"moveId": "THUNDER", "uses": 27031}, {"moveId": "FOCUS_BLAST", "uses": 26083}, {"moveId": "GIGA_IMPACT", "uses": 23493}]}, "moveset": ["HIDDEN_POWER_ICE", "THUNDER", "FOCUS_BLAST"], "score": 45.6}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 571, "matchups": [{"opponent": "swampert", "rating": 945, "opRating": 54}, {"opponent": "swampert_shadow", "rating": 920, "opRating": 79}, {"opponent": "kyogre", "rating": 678, "opRating": 321}, {"opponent": "excadrill", "rating": 652, "opRating": 347}, {"opponent": "grou<PERSON>", "rating": 598, "opRating": 401}], "counters": [{"opponent": "zacian_hero", "rating": 251}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "giratina_origin", "rating": 296}, {"opponent": "dialga", "rating": 342}, {"opponent": "garcho<PERSON>", "rating": 392}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 40542}, {"moveId": "FURY_CUTTER", "uses": 35958}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30198}, {"moveId": "DRAGON_CLAW", "uses": 17084}, {"moveId": "FRENZY_PLANT", "uses": 11193}, {"moveId": "EARTHQUAKE", "uses": 10021}, {"moveId": "AERIAL_ACE", "uses": 8024}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DRAGON_CLAW"], "score": 45.6}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 722, "matchups": [{"opponent": "zacian_hero", "rating": 803}, {"opponent": "sylveon", "rating": 760, "opRating": 239}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 741, "opRating": 258}, {"opponent": "zekrom", "rating": 642, "opRating": 357}, {"opponent": "yveltal", "rating": 524, "opRating": 475}], "counters": [{"opponent": "giratina_origin", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 166}, {"opponent": "mewtwo", "rating": 299}, {"opponent": "gyarados", "rating": 358}, {"opponent": "dialga", "rating": 442}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57571}, {"moveId": "BITE", "uses": 18929}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22453}, {"moveId": "EARTH_POWER", "uses": 20733}, {"moveId": "STONE_EDGE", "uses": 15251}, {"moveId": "SLUDGE_WAVE", "uses": 8966}, {"moveId": "EARTHQUAKE", "uses": 8907}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 45.4}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 573, "matchups": [{"opponent": "mew", "rating": 907, "opRating": 92}, {"opponent": "giratina_origin", "rating": 674}, {"opponent": "gyarados", "rating": 564}, {"opponent": "giratina_altered", "rating": 547, "opRating": 452}, {"opponent": "mewtwo", "rating": 522}], "counters": [{"opponent": "grou<PERSON>", "rating": 138}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "dragonite", "rating": 210}, {"opponent": "dialga", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 314}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 35915}, {"moveId": "ICE_FANG", "uses": 32017}, {"moveId": "TAKE_DOWN", "uses": 8604}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35402}, {"moveId": "CRUNCH", "uses": 27741}, {"moveId": "PLAY_ROUGH", "uses": 13436}]}, "moveset": ["LICK", "WILD_CHARGE", "CRUNCH"], "score": 45.4}, {"speciesId": "omastar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 632, "matchups": [{"opponent": "moltres", "rating": 917, "opRating": 82}, {"opponent": "ho_oh", "rating": 885, "opRating": 114}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 563, "opRating": 436}, {"opponent": "yveltal", "rating": 550, "opRating": 449}, {"opponent": "gyarados", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "dialga", "rating": 252}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "zacian_hero", "rating": 283}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30062}, {"moveId": "ROCK_THROW", "uses": 23734}, {"moveId": "WATER_GUN", "uses": 22731}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30176}, {"moveId": "ROCK_BLAST", "uses": 21565}, {"moveId": "HYDRO_PUMP", "uses": 14073}, {"moveId": "ANCIENT_POWER", "uses": 10874}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 45.3}, {"speciesId": "crustle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 633, "matchups": [{"opponent": "pinsir_shadow", "rating": 834, "opRating": 165}, {"opponent": "magmortar_shadow", "rating": 821, "opRating": 178}, {"opponent": "zarude", "rating": 805, "opRating": 194}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550, "opRating": 449}, {"opponent": "mewtwo", "rating": 503}], "counters": [{"opponent": "giratina_origin", "rating": 171}, {"opponent": "dialga", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "gyarados", "rating": 309}, {"opponent": "metagross", "rating": 348}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40709}, {"moveId": "SMACK_DOWN", "uses": 35791}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28871}, {"moveId": "X_SCISSOR", "uses": 26869}, {"moveId": "ROCK_BLAST", "uses": 20692}]}, "moveset": ["FURY_CUTTER", "ROCK_SLIDE", "X_SCISSOR"], "score": 45.1}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 602, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 837, "opRating": 162}, {"opponent": "espeon", "rating": 789, "opRating": 210}, {"opponent": "dialga", "rating": 589}, {"opponent": "genesect_chill", "rating": 563, "opRating": 436}, {"opponent": "metagross", "rating": 503, "opRating": 496}], "counters": [{"opponent": "zacian_hero", "rating": 202}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "gyarados", "rating": 319}, {"opponent": "giratina_origin", "rating": 320}, {"opponent": "mewtwo", "rating": 460}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 53746}, {"moveId": "METAL_CLAW", "uses": 22754}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31201}, {"moveId": "FOUL_PLAY", "uses": 20457}, {"moveId": "IRON_HEAD", "uses": 14576}, {"moveId": "PLAY_ROUGH", "uses": 10224}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "FOUL_PLAY"], "score": 45.1}, {"speciesId": "mandibuzz", "speciesName": "Mandibuzz", "rating": 514, "matchups": [{"opponent": "espeon", "rating": 883, "opRating": 116}, {"opponent": "mewtwo", "rating": 781}, {"opponent": "mewtwo_shadow", "rating": 765, "opRating": 234}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 753, "opRating": 246}, {"opponent": "giratina_origin", "rating": 630}], "counters": [{"opponent": "excadrill", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "lugia", "rating": 273}, {"opponent": "dialga", "rating": 282}, {"opponent": "zacian_hero", "rating": 291}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44544}, {"moveId": "AIR_SLASH", "uses": 31956}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 28257}, {"moveId": "SHADOW_BALL", "uses": 18643}, {"moveId": "AERIAL_ACE", "uses": 17595}, {"moveId": "DARK_PULSE", "uses": 12093}]}, "moveset": ["SNARL", "FOUL_PLAY", "SHADOW_BALL"], "score": 44.9}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 653, "matchups": [{"opponent": "metagross", "rating": 817}, {"opponent": "mewtwo_shadow", "rating": 804, "opRating": 195}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 775, "opRating": 224}, {"opponent": "mewtwo", "rating": 755}, {"opponent": "giratina_origin", "rating": 662}], "counters": [{"opponent": "zacian_hero", "rating": 75}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "dragonite", "rating": 289}, {"opponent": "gyarados", "rating": 304}, {"opponent": "dialga", "rating": 423}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28440}, {"moveId": "DOUBLE_KICK", "uses": 27411}, {"moveId": "FIRE_FANG", "uses": 20642}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37379}, {"moveId": "FLAME_CHARGE", "uses": 31959}, {"moveId": "FIRE_BLAST", "uses": 7096}]}, "moveset": ["SNARL", "DARK_PULSE", "FIRE_BLAST"], "score": 44.6}, {"speciesId": "magneton_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 603, "matchups": [{"opponent": "gyarados", "rating": 795}, {"opponent": "gyarado<PERSON>_shadow", "rating": 728, "opRating": 271}, {"opponent": "sylveon", "rating": 570, "opRating": 429}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 535, "opRating": 464}, {"opponent": "lugia", "rating": 527, "opRating": 472}], "counters": [{"opponent": "excadrill", "rating": 202}, {"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "giratina_origin", "rating": 223}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "dialga", "rating": 323}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 31038}, {"moveId": "SPARK", "uses": 28128}, {"moveId": "CHARGE_BEAM", "uses": 17372}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 32058}, {"moveId": "DISCHARGE", "uses": 27055}, {"moveId": "ZAP_CANNON", "uses": 10577}, {"moveId": "FLASH_CANNON", "uses": 6798}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 44.6}, {"speciesId": "dewgong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 493, "matchups": [{"opponent": "garcho<PERSON>", "rating": 602}, {"opponent": "dragonite", "rating": 580, "opRating": 419}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 580, "opRating": 419}, {"opponent": "grou<PERSON>", "rating": 545, "opRating": 454}, {"opponent": "giratina_origin", "rating": 505, "opRating": 494}], "counters": [{"opponent": "metagross", "rating": 215}, {"opponent": "dialga", "rating": 239}, {"opponent": "mewtwo", "rating": 255}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "gyarados", "rating": 296}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38777}, {"moveId": "FROST_BREATH", "uses": 29664}, {"moveId": "IRON_TAIL", "uses": 8058}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 36268}, {"moveId": "BLIZZARD", "uses": 16155}, {"moveId": "AQUA_JET", "uses": 12693}, {"moveId": "AURORA_BEAM", "uses": 6482}, {"moveId": "WATER_PULSE", "uses": 4746}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "BLIZZARD"], "score": 44.4}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 585, "matchups": [{"opponent": "garcho<PERSON>", "rating": 848}, {"opponent": "dragonite", "rating": 631, "opRating": 368}, {"opponent": "zekrom", "rating": 599, "opRating": 400}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 582, "opRating": 417}, {"opponent": "grou<PERSON>", "rating": 514, "opRating": 485}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "metagross", "rating": 232}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "dialga", "rating": 366}, {"opponent": "lugia", "rating": 376}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44996}, {"moveId": "ICE_SHARD", "uses": 31504}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 45134}, {"moveId": "STONE_EDGE", "uses": 17119}, {"moveId": "BULLDOZE", "uses": 14142}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 44.3}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 564, "matchups": [{"opponent": "swampert", "rating": 932, "opRating": 67}, {"opponent": "mewtwo", "rating": 693}, {"opponent": "kyogre", "rating": 634, "opRating": 365}, {"opponent": "metagross", "rating": 577, "opRating": 422}, {"opponent": "excadrill", "rating": 553, "opRating": 446}], "counters": [{"opponent": "dragonite", "rating": 273}, {"opponent": "dialga", "rating": 282}, {"opponent": "gyarados", "rating": 309}, {"opponent": "giratina_origin", "rating": 352}, {"opponent": "garcho<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25718}, {"moveId": "BULLET_SEED", "uses": 23305}, {"moveId": "FEINT_ATTACK", "uses": 17604}, {"moveId": "RAZOR_LEAF", "uses": 9978}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36521}, {"moveId": "FOUL_PLAY", "uses": 24110}, {"moveId": "HURRICANE", "uses": 8834}, {"moveId": "LEAF_TORNADO", "uses": 6932}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 44.3}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 666, "matchups": [{"opponent": "swampert", "rating": 784, "opRating": 215}, {"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "sylveon", "rating": 735, "opRating": 264}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "zacian_hero", "rating": 595, "opRating": 404}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "giratina_origin", "rating": 189}, {"opponent": "metagross", "rating": 250}, {"opponent": "dialga", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 321}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 52942}, {"moveId": "RAZOR_LEAF", "uses": 23558}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38001}, {"moveId": "SLUDGE_BOMB", "uses": 19392}, {"moveId": "RETURN", "uses": 9514}, {"moveId": "PETAL_BLIZZARD", "uses": 5232}, {"moveId": "SOLAR_BEAM", "uses": 4237}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 44.3}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 678, "matchups": [{"opponent": "swampert", "rating": 729, "opRating": 270}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 613, "opRating": 386}, {"opponent": "grou<PERSON>", "rating": 601, "opRating": 398}, {"opponent": "excadrill", "rating": 578, "opRating": 421}, {"opponent": "zacian_hero", "rating": 531, "opRating": 468}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 173}, {"opponent": "lugia", "rating": 192}, {"opponent": "giratina_origin", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 368}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53983}, {"moveId": "RAZOR_LEAF", "uses": 22517}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 42972}, {"moveId": "SLUDGE_BOMB", "uses": 22731}, {"moveId": "PETAL_BLIZZARD", "uses": 5924}, {"moveId": "SOLAR_BEAM", "uses": 4967}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 44.2}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 635, "matchups": [{"opponent": "nihilego", "rating": 946, "opRating": 53}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 663, "opRating": 336}, {"opponent": "dialga", "rating": 633}, {"opponent": "sylveon", "rating": 521, "opRating": 478}, {"opponent": "excadrill", "rating": 518, "opRating": 481}], "counters": [{"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "gyarados", "rating": 365}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "mewtwo", "rating": 388}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 50457}, {"moveId": "METAL_CLAW", "uses": 26043}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26258}, {"moveId": "EARTHQUAKE", "uses": 23730}, {"moveId": "MUDDY_WATER", "uses": 15305}, {"moveId": "FLASH_CANNON", "uses": 11206}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 44}, {"speciesId": "politoed", "speciesName": "Politoed", "rating": 560, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 879, "opRating": 120}, {"opponent": "moltres_shadow", "rating": 825, "opRating": 174}, {"opponent": "excadrill", "rating": 655, "opRating": 344}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 647, "opRating": 352}, {"opponent": "zacian_hero", "rating": 505, "opRating": 494}], "counters": [{"opponent": "giratina_origin", "rating": 161}, {"opponent": "gyarados", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "dialga", "rating": 320}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41319}, {"moveId": "BUBBLE", "uses": 35181}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 29640}, {"moveId": "SURF", "uses": 12079}, {"moveId": "BLIZZARD", "uses": 11972}, {"moveId": "EARTHQUAKE", "uses": 11740}, {"moveId": "RETURN", "uses": 7223}, {"moveId": "HYDRO_PUMP", "uses": 3918}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "SURF"], "score": 43.9}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 570, "matchups": [{"opponent": "cobalion", "rating": 733, "opRating": 266}, {"opponent": "melmetal", "rating": 708, "opRating": 291}, {"opponent": "metagross", "rating": 672, "opRating": 327}, {"opponent": "zacian_hero", "rating": 589, "opRating": 410}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dragonite", "rating": 130}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "dialga", "rating": 355}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 60590}, {"moveId": "ASTONISH", "uses": 15910}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 41478}, {"moveId": "ROCK_TOMB", "uses": 17591}, {"moveId": "SAND_TOMB", "uses": 17433}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "ROCK_TOMB"], "score": 43.9}, {"speciesId": "carracosta", "speciesName": "Carracosta", "rating": 577, "matchups": [{"opponent": "ho_oh", "rating": 923, "opRating": 76}, {"opponent": "moltres", "rating": 898, "opRating": 101}, {"opponent": "ma<PERSON><PERSON>", "rating": 861, "opRating": 138}, {"opponent": "mamos<PERSON>_shadow", "rating": 861, "opRating": 138}, {"opponent": "sylveon", "rating": 518, "opRating": 481}], "counters": [{"opponent": "giratina_origin", "rating": 171}, {"opponent": "gyarados", "rating": 247}, {"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39361}, {"moveId": "ROCK_THROW", "uses": 37139}], "chargedMoves": [{"moveId": "SURF", "uses": 29814}, {"moveId": "BODY_SLAM", "uses": 25690}, {"moveId": "ANCIENT_POWER", "uses": 21026}]}, "moveset": ["WATER_GUN", "SURF", "BODY_SLAM"], "score": 43.7}, {"speciesId": "kingler", "speciesName": "<PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "excadrill", "rating": 877, "opRating": 122}, {"opponent": "mamos<PERSON>_shadow", "rating": 877, "opRating": 122}, {"opponent": "entei_shadow", "rating": 866, "opRating": 133}, {"opponent": "magmortar_shadow", "rating": 818, "opRating": 181}, {"opponent": "moltres_shadow", "rating": 818, "opRating": 181}], "counters": [{"opponent": "gyarados", "rating": 172}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "dialga", "rating": 271}, {"opponent": "garcho<PERSON>", "rating": 279}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32017}, {"moveId": "BUBBLE", "uses": 28571}, {"moveId": "METAL_CLAW", "uses": 15896}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 35511}, {"moveId": "X_SCISSOR", "uses": 24812}, {"moveId": "VICE_GRIP", "uses": 11046}, {"moveId": "WATER_PULSE", "uses": 5145}]}, "moveset": ["MUD_SHOT", "CRABHAMMER", "X_SCISSOR"], "score": 43.7}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 602, "matchups": [{"opponent": "swampert_shadow", "rating": 929, "opRating": 70}, {"opponent": "swampert", "rating": 767, "opRating": 232}, {"opponent": "kyogre", "rating": 742, "opRating": 257}, {"opponent": "excadrill", "rating": 652, "opRating": 347}, {"opponent": "grou<PERSON>", "rating": 598, "opRating": 401}], "counters": [{"opponent": "lugia", "rating": 176}, {"opponent": "giratina_origin", "rating": 189}, {"opponent": "dialga", "rating": 247}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51163}, {"moveId": "RAZOR_LEAF", "uses": 25337}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37388}, {"moveId": "SUPER_POWER", "uses": 20540}, {"moveId": "X_SCISSOR", "uses": 13992}, {"moveId": "LEAF_STORM", "uses": 4639}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 43.7}, {"speciesId": "deoxys_defense", "speciesName": "<PERSON><PERSON><PERSON> (Defense)", "rating": 577, "matchups": [{"opponent": "lucario", "rating": 803, "opRating": 196}, {"opponent": "magmortar_shadow", "rating": 728, "opRating": 271}, {"opponent": "blaziken", "rating": 704, "opRating": 295}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 692, "opRating": 307}, {"opponent": "sneasler", "rating": 590, "opRating": 409}], "counters": [{"opponent": "giratina_origin", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "metagross", "rating": 247}, {"opponent": "dialga", "rating": 391}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 67118}, {"moveId": "ZEN_HEADBUTT", "uses": 9382}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 32939}, {"moveId": "ROCK_SLIDE", "uses": 27419}, {"moveId": "THUNDERBOLT", "uses": 16172}]}, "moveset": ["COUNTER", "PSYCHO_BOOST", "ROCK_SLIDE"], "score": 43.6}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "electivire_shadow", "rating": 701, "opRating": 298}, {"opponent": "zap<PERSON>_shadow", "rating": 649, "opRating": 350}, {"opponent": "giratina_origin", "rating": 626}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "garcho<PERSON>", "rating": 518}], "counters": [{"opponent": "metagross", "rating": 177}, {"opponent": "dialga", "rating": 184}, {"opponent": "excadrill", "rating": 211}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "mewtwo", "rating": 322}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 36092}, {"moveId": "TACKLE", "uses": 35271}, {"moveId": "ZEN_HEADBUTT", "uses": 5157}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33456}, {"moveId": "ICE_BEAM", "uses": 16633}, {"moveId": "THUNDERBOLT", "uses": 12095}, {"moveId": "STOMP", "uses": 8258}, {"moveId": "GYRO_BALL", "uses": 6027}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 43.6}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "rai<PERSON>u", "rating": 916, "opRating": 83}, {"opponent": "raikou_shadow", "rating": 900, "opRating": 99}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 755, "opRating": 244}, {"opponent": "sylveon", "rating": 755, "opRating": 244}, {"opponent": "zacian_hero", "rating": 663}], "counters": [{"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "mewtwo", "rating": 244}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "metagross", "rating": 348}, {"opponent": "dialga", "rating": 358}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54535}, {"moveId": "BITE", "uses": 21965}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 20443}, {"moveId": "EARTH_POWER", "uses": 19282}, {"moveId": "STONE_EDGE", "uses": 14085}, {"moveId": "EARTHQUAKE", "uses": 8280}, {"moveId": "SLUDGE_WAVE", "uses": 8265}, {"moveId": "RETURN", "uses": 6270}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 43.6}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 547, "matchups": [{"opponent": "mewtwo", "rating": 645}, {"opponent": "swampert", "rating": 642, "opRating": 357}, {"opponent": "excadrill", "rating": 623, "opRating": 376}, {"opponent": "metagross", "rating": 594}, {"opponent": "giratina_origin", "rating": 521}], "counters": [{"opponent": "dragonite", "rating": 236}, {"opponent": "dialga", "rating": 241}, {"opponent": "lugia", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "gyarados", "rating": 409}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25127}, {"moveId": "BULLET_SEED", "uses": 22789}, {"moveId": "FEINT_ATTACK", "uses": 17827}, {"moveId": "RAZOR_LEAF", "uses": 10851}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33770}, {"moveId": "FOUL_PLAY", "uses": 22103}, {"moveId": "HURRICANE", "uses": 7999}, {"moveId": "LEAF_TORNADO", "uses": 6487}, {"moveId": "RETURN", "uses": 6170}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 43.6}, {"speciesId": "tyrantrum", "speciesName": "Tyrantrum", "rating": 649, "matchups": [{"opponent": "raikou_shadow", "rating": 841, "opRating": 158}, {"opponent": "zap<PERSON>_shadow", "rating": 741, "opRating": 258}, {"opponent": "ho_oh", "rating": 666, "opRating": 333}, {"opponent": "mewtwo", "rating": 537}, {"opponent": "giratina_origin", "rating": 511, "opRating": 488}], "counters": [{"opponent": "zacian_hero", "rating": 112}, {"opponent": "excadrill", "rating": 181}, {"opponent": "dialga", "rating": 211}, {"opponent": "gyarados", "rating": 270}, {"opponent": "garcho<PERSON>", "rating": 403}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 35554}, {"moveId": "ROCK_THROW", "uses": 24033}, {"moveId": "CHARM", "uses": 16886}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23604}, {"moveId": "CRUNCH", "uses": 20680}, {"moveId": "OUTRAGE", "uses": 17972}, {"moveId": "EARTHQUAKE", "uses": 14370}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "CRUNCH"], "score": 43.6}, {"speciesId": "gigalith", "speciesName": "Gigalith", "rating": 625, "matchups": [{"opponent": "moltres", "rating": 879, "opRating": 120}, {"opponent": "ho_oh", "rating": 845, "opRating": 154}, {"opponent": "lugia", "rating": 592, "opRating": 407}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 564, "opRating": 435}, {"opponent": "sylveon", "rating": 522, "opRating": 477}], "counters": [{"opponent": "garcho<PERSON>", "rating": 147}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "metagross", "rating": 302}, {"opponent": "mewtwo", "rating": 427}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 44052}, {"moveId": "MUD_SLAP", "uses": 32448}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 31616}, {"moveId": "SUPER_POWER", "uses": 26317}, {"moveId": "HEAVY_SLAM", "uses": 10685}, {"moveId": "SOLAR_BEAM", "uses": 7794}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "SUPER_POWER"], "score": 43.5}, {"speciesId": "gran<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 539, "matchups": [{"opponent": "dialga", "rating": 701}, {"opponent": "latios", "rating": 666, "opRating": 333}, {"opponent": "latios_shadow", "rating": 650, "opRating": 349}, {"opponent": "palkia", "rating": 532, "opRating": 467}, {"opponent": "excadrill", "rating": 510, "opRating": 489}], "counters": [{"opponent": "zacian_hero", "rating": 135}, {"opponent": "garcho<PERSON>", "rating": 194}, {"opponent": "giratina_origin", "rating": 260}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "metagross", "rating": 334}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 35515}, {"moveId": "CHARM", "uses": 26952}, {"moveId": "BITE", "uses": 13969}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30805}, {"moveId": "CRUNCH", "uses": 22902}, {"moveId": "PLAY_ROUGH", "uses": 14508}, {"moveId": "RETURN", "uses": 8451}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 43.5}, {"speciesId": "gren<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 569, "matchups": [{"opponent": "excadrill", "rating": 884, "opRating": 115}, {"opponent": "landorus_incarnate", "rating": 846, "opRating": 153}, {"opponent": "metagross", "rating": 778}, {"opponent": "mewtwo", "rating": 668}, {"opponent": "mewtwo_shadow", "rating": 668, "opRating": 331}], "counters": [{"opponent": "dragonite", "rating": 204}, {"opponent": "dialga", "rating": 214}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "lugia", "rating": 307}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 40084}, {"moveId": "FEINT_ATTACK", "uses": 36416}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 36096}, {"moveId": "SURF", "uses": 26385}, {"moveId": "AERIAL_ACE", "uses": 9828}, {"moveId": "HYDRO_PUMP", "uses": 4262}]}, "moveset": ["BUBBLE", "NIGHT_SLASH", "SURF"], "score": 43.5}, {"speciesId": "omastar", "speciesName": "Omastar", "rating": 601, "matchups": [{"opponent": "moltres", "rating": 933, "opRating": 66}, {"opponent": "ho_oh", "rating": 901, "opRating": 98}, {"opponent": "yveltal", "rating": 608, "opRating": 391}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 595, "opRating": 404}, {"opponent": "lugia", "rating": 538, "opRating": 461}], "counters": [{"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "giratina_origin", "rating": 203}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "dialga", "rating": 361}, {"opponent": "mewtwo", "rating": 447}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29323}, {"moveId": "ROCK_THROW", "uses": 23572}, {"moveId": "WATER_GUN", "uses": 23571}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26862}, {"moveId": "ROCK_BLAST", "uses": 19273}, {"moveId": "HYDRO_PUMP", "uses": 12268}, {"moveId": "ANCIENT_POWER", "uses": 9729}, {"moveId": "RETURN", "uses": 8427}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 43.5}, {"speciesId": "golem", "speciesName": "Golem", "rating": 647, "matchups": [{"opponent": "ho_oh", "rating": 892, "opRating": 107}, {"opponent": "dialga", "rating": 648}, {"opponent": "metagross", "rating": 648, "opRating": 351}, {"opponent": "zekrom", "rating": 633, "opRating": 366}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 517, "opRating": 482}], "counters": [{"opponent": "excadrill", "rating": 141}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "giratina_origin", "rating": 294}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "mewtwo", "rating": 354}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 33330}, {"moveId": "ROCK_THROW", "uses": 22644}, {"moveId": "MUD_SLAP", "uses": 20491}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 20160}, {"moveId": "STONE_EDGE", "uses": 18950}, {"moveId": "ROCK_BLAST", "uses": 17128}, {"moveId": "ANCIENT_POWER", "uses": 13022}, {"moveId": "RETURN", "uses": 7268}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 43.3}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 665, "matchups": [{"opponent": "yveltal", "rating": 834, "opRating": 165}, {"opponent": "zacian_hero", "rating": 752}, {"opponent": "ho_oh", "rating": 752, "opRating": 247}, {"opponent": "gyarados", "rating": 691}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 552, "opRating": 447}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "grou<PERSON>", "rating": 214}, {"opponent": "dialga", "rating": 274}, {"opponent": "giratina_origin", "rating": 286}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11794}, {"moveId": "SNARL", "uses": 9572}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4403}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4177}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4090}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3768}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3645}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3613}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3530}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3524}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3284}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3276}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3245}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3013}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2989}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2905}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2876}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2684}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34302}, {"moveId": "CRUNCH", "uses": 19563}, {"moveId": "PSYCHIC_FANGS", "uses": 17332}, {"moveId": "HYPER_BEAM", "uses": 5296}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 43.3}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 590, "matchups": [{"opponent": "electivire_shadow", "rating": 939, "opRating": 60}, {"opponent": "magnezone_shadow", "rating": 911, "opRating": 88}, {"opponent": "nihilego", "rating": 853, "opRating": 146}, {"opponent": "excadrill", "rating": 653, "opRating": 346}, {"opponent": "metagross", "rating": 610, "opRating": 389}], "counters": [{"opponent": "gyarados", "rating": 182}, {"opponent": "giratina_origin", "rating": 227}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "dialga", "rating": 494}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41928}, {"moveId": "BUBBLE", "uses": 34572}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 30085}, {"moveId": "MUDDY_WATER", "uses": 26523}, {"moveId": "SLUDGE_BOMB", "uses": 19916}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "MUDDY_WATER"], "score": 43.3}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 529, "matchups": [{"opponent": "gengar", "rating": 728, "opRating": 271}, {"opponent": "muk_alolan", "rating": 722, "opRating": 277}, {"opponent": "over<PERSON><PERSON>l", "rating": 557, "opRating": 442}, {"opponent": "victini", "rating": 554, "opRating": 445}, {"opponent": "excadrill", "rating": 551, "opRating": 448}], "counters": [{"opponent": "mewtwo", "rating": 205}, {"opponent": "metagross", "rating": 229}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 63795}, {"moveId": "ZEN_HEADBUTT", "uses": 12705}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 32816}, {"moveId": "EARTHQUAKE", "uses": 25019}, {"moveId": "IRON_HEAD", "uses": 18716}]}, "moveset": ["TACKLE", "HORN_ATTACK", "EARTHQUAKE"], "score": 43.3}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 513, "matchups": [{"opponent": "mewtwo_shadow", "rating": 762, "opRating": 237}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "mewtwo", "rating": 636}, {"opponent": "giratina_origin", "rating": 597, "opRating": 402}, {"opponent": "metagross", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dragonite", "rating": 164}, {"opponent": "garcho<PERSON>", "rating": 265}, {"opponent": "lugia", "rating": 269}, {"opponent": "dialga", "rating": 282}, {"opponent": "gyarados", "rating": 304}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42110}, {"moveId": "FEINT_ATTACK", "uses": 34390}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 33524}, {"moveId": "PSYCHIC", "uses": 15068}, {"moveId": "DARK_PULSE", "uses": 14253}, {"moveId": "LAST_RESORT", "uses": 13651}]}, "moveset": ["SNARL", "FOUL_PLAY", "PSYCHIC"], "score": 43.2}, {"speciesId": "torn<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 561, "matchups": [{"opponent": "grou<PERSON>", "rating": 608, "opRating": 391}, {"opponent": "garcho<PERSON>", "rating": 558}, {"opponent": "swampert", "rating": 544, "opRating": 455}, {"opponent": "zacian_hero", "rating": 502, "opRating": 497}, {"opponent": "sylveon", "rating": 502, "opRating": 497}], "counters": [{"opponent": "giratina_origin", "rating": 193}, {"opponent": "dialga", "rating": 198}, {"opponent": "metagross", "rating": 212}, {"opponent": "gyarados", "rating": 291}, {"opponent": "mewtwo", "rating": 356}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 58448}, {"moveId": "ASTONISH", "uses": 18052}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 26520}, {"moveId": "PSYCHIC", "uses": 21116}, {"moveId": "FOCUS_BLAST", "uses": 20280}, {"moveId": "HEAT_WAVE", "uses": 8544}]}, "moveset": ["GUST", "HURRICANE", "PSYCHIC"], "score": 43}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 606, "matchups": [{"opponent": "swampert", "rating": 825, "opRating": 174}, {"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 755, "opRating": 244}, {"opponent": "garcho<PERSON>", "rating": 561}, {"opponent": "zacian_hero", "rating": 523, "opRating": 476}], "counters": [{"opponent": "lugia", "rating": 140}, {"opponent": "giratina_origin", "rating": 175}, {"opponent": "metagross", "rating": 191}, {"opponent": "mewtwo", "rating": 348}, {"opponent": "dialga", "rating": 372}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53246}, {"moveId": "RAZOR_LEAF", "uses": 23254}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38999}, {"moveId": "EARTHQUAKE", "uses": 17012}, {"moveId": "RETURN", "uses": 10786}, {"moveId": "PETAL_BLIZZARD", "uses": 5389}, {"moveId": "SOLAR_BEAM", "uses": 4433}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 42.9}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 585, "matchups": [{"opponent": "mewtwo_shadow", "rating": 595, "opRating": 404}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 587, "opRating": 412}, {"opponent": "sylveon", "rating": 580, "opRating": 419}, {"opponent": "giratina_origin", "rating": 558, "opRating": 441}, {"opponent": "zacian_hero", "rating": 531, "opRating": 468}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "lugia", "rating": 283}, {"opponent": "metagross", "rating": 293}, {"opponent": "mewtwo", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 46990}, {"moveId": "BITE", "uses": 29510}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34625}, {"moveId": "SLUDGE_BOMB", "uses": 18494}, {"moveId": "FLAMETHROWER", "uses": 14464}, {"moveId": "RETURN", "uses": 8917}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 42.9}, {"speciesId": "tapu_koko", "speciesName": "<PERSON><PERSON>", "rating": 730, "matchups": [{"opponent": "gyarados", "rating": 843}, {"opponent": "zekrom", "rating": 812, "opRating": 187}, {"opponent": "lugia", "rating": 719}, {"opponent": "dragonite", "rating": 691}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 589, "opRating": 410}], "counters": [{"opponent": "garcho<PERSON>", "rating": 44}, {"opponent": "excadrill", "rating": 83}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "dialga", "rating": 448}, {"opponent": "mewtwo", "rating": 453}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43952}, {"moveId": "QUICK_ATTACK", "uses": 32548}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31128}, {"moveId": "THUNDERBOLT", "uses": 20730}, {"moveId": "DAZZLING_GLEAM", "uses": 15532}, {"moveId": "THUNDER", "uses": 9180}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "DAZZLING_GLEAM"], "score": 42.7}, {"speciesId": "muk_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 673, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "gyarados", "rating": 709}, {"opponent": "sylveon", "rating": 706, "opRating": 293}, {"opponent": "zacian_hero", "rating": 625}, {"opponent": "yveltal", "rating": 555, "opRating": 444}], "counters": [{"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "dialga", "rating": 296}, {"opponent": "lugia", "rating": 342}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 31941}, {"moveId": "LICK", "uses": 23235}, {"moveId": "INFESTATION", "uses": 21337}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27101}, {"moveId": "THUNDER_PUNCH", "uses": 22901}, {"moveId": "SLUDGE_WAVE", "uses": 15304}, {"moveId": "GUNK_SHOT", "uses": 6016}, {"moveId": "ACID_SPRAY", "uses": 5133}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 42.6}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 536, "matchups": [{"opponent": "gengar", "rating": 795, "opRating": 204}, {"opponent": "sneasler", "rating": 733, "opRating": 266}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 661, "opRating": 338}, {"opponent": "xurkitree", "rating": 618, "opRating": 381}, {"opponent": "giratina_origin", "rating": 559, "opRating": 440}], "counters": [{"opponent": "gyarados", "rating": 219}, {"opponent": "metagross", "rating": 244}, {"opponent": "dialga", "rating": 274}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "mewtwo", "rating": 388}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 61767}, {"moveId": "ZEN_HEADBUTT", "uses": 12847}, {"moveId": "YAWN", "uses": 1947}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 36397}, {"moveId": "PSYCHIC", "uses": 27933}, {"moveId": "FUTURE_SIGHT", "uses": 12192}]}, "moveset": ["CONFUSION", "FOUL_PLAY", "PSYCHIC"], "score": 42.6}, {"speciesId": "politoed_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 647, "matchups": [{"opponent": "garcho<PERSON>", "rating": 879}, {"opponent": "ho_oh", "rating": 803, "opRating": 196}, {"opponent": "dragonite", "rating": 626, "opRating": 373}, {"opponent": "zekrom", "rating": 626, "opRating": 373}, {"opponent": "excadrill", "rating": 607, "opRating": 392}], "counters": [{"opponent": "gyarados", "rating": 115}, {"opponent": "giratina_origin", "rating": 135}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "dialga", "rating": 415}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41617}, {"moveId": "BUBBLE", "uses": 34883}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 32484}, {"moveId": "BLIZZARD", "uses": 13362}, {"moveId": "SURF", "uses": 13215}, {"moveId": "EARTHQUAKE", "uses": 13076}, {"moveId": "HYDRO_PUMP", "uses": 4213}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "BLIZZARD"], "score": 42.6}, {"speciesId": "crawdaunt", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "excadrill", "rating": 843, "opRating": 156}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 829, "opRating": 170}, {"opponent": "metagross", "rating": 731}, {"opponent": "mewtwo_shadow", "rating": 646, "opRating": 353}, {"opponent": "mewtwo", "rating": 636}], "counters": [{"opponent": "gyarados", "rating": 226}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 288}, {"opponent": "lugia", "rating": 316}, {"opponent": "dialga", "rating": 339}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44501}, {"moveId": "WATERFALL", "uses": 31999}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39307}, {"moveId": "CRABHAMMER", "uses": 23016}, {"moveId": "VICE_GRIP", "uses": 7339}, {"moveId": "BUBBLE_BEAM", "uses": 6932}]}, "moveset": ["SNARL", "NIGHT_SLASH", "CRABHAMMER"], "score": 42.3}, {"speciesId": "king<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 582, "matchups": [{"opponent": "vaporeon", "rating": 887, "opRating": 112}, {"opponent": "entei", "rating": 774, "opRating": 225}, {"opponent": "kyogre", "rating": 728, "opRating": 271}, {"opponent": "swampert", "rating": 545, "opRating": 454}, {"opponent": "ho_oh", "rating": 509, "opRating": 490}], "counters": [{"opponent": "zacian_hero", "rating": 176}, {"opponent": "dialga", "rating": 198}, {"opponent": "gyarados", "rating": 260}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 32955}, {"moveId": "WATER_GUN", "uses": 22305}, {"moveId": "WATERFALL", "uses": 21299}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23149}, {"moveId": "OCTAZOOKA", "uses": 15356}, {"moveId": "BLIZZARD", "uses": 14865}, {"moveId": "HYDRO_PUMP", "uses": 12946}, {"moveId": "RETURN", "uses": 10018}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 42.3}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 671, "matchups": [{"opponent": "mewtwo", "rating": 711}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 588, "opRating": 411}, {"opponent": "giratina_origin", "rating": 584}, {"opponent": "zacian_hero", "rating": 574}, {"opponent": "gyarados", "rating": 533}], "counters": [{"opponent": "swampert", "rating": 121}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dragonite", "rating": 210}, {"opponent": "dialga", "rating": 250}, {"opponent": "metagross", "rating": 331}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29599}, {"moveId": "SNARL", "uses": 28413}, {"moveId": "BITE", "uses": 18479}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 43641}, {"moveId": "SLUDGE_WAVE", "uses": 19058}, {"moveId": "GUNK_SHOT", "uses": 7380}, {"moveId": "ACID_SPRAY", "uses": 6432}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "SLUDGE_WAVE"], "score": 42.3}, {"speciesId": "hitmon<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 614, "matchups": [{"opponent": "regirock", "rating": 909, "opRating": 90}, {"opponent": "snorlax", "rating": 724, "opRating": 275}, {"opponent": "genesect_douse", "rating": 688, "opRating": 311}, {"opponent": "metagross", "rating": 586, "opRating": 413}, {"opponent": "swampert", "rating": 555, "opRating": 444}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 178}, {"opponent": "giratina_origin", "rating": 231}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "dialga", "rating": 437}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42658}, {"moveId": "BULLET_PUNCH", "uses": 26340}, {"moveId": "ROCK_SMASH", "uses": 7520}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24709}, {"moveId": "ICE_PUNCH", "uses": 16078}, {"moveId": "THUNDER_PUNCH", "uses": 11851}, {"moveId": "BRICK_BREAK", "uses": 10973}, {"moveId": "FIRE_PUNCH", "uses": 10644}, {"moveId": "POWER_UP_PUNCH", "uses": 2163}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 42}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 566, "matchups": [{"opponent": "mewtwo_armored", "rating": 847, "opRating": 152}, {"opponent": "metagross", "rating": 740, "opRating": 259}, {"opponent": "mewtwo_shadow", "rating": 704, "opRating": 295}, {"opponent": "mewtwo", "rating": 658}, {"opponent": "giratina_origin", "rating": 542, "opRating": 457}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "lugia", "rating": 264}, {"opponent": "gyarados", "rating": 273}, {"opponent": "dialga", "rating": 328}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45864}, {"moveId": "FIRE_FANG", "uses": 30636}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30708}, {"moveId": "FLAMETHROWER", "uses": 18523}, {"moveId": "FOUL_PLAY", "uses": 13334}, {"moveId": "RETURN", "uses": 8943}, {"moveId": "FIRE_BLAST", "uses": 5026}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 42}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 629, "matchups": [{"opponent": "gyarados", "rating": 714}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}, {"opponent": "lugia", "rating": 563, "opRating": 436}, {"opponent": "yveltal", "rating": 553, "opRating": 446}, {"opponent": "metagross", "rating": 516, "opRating": 483}], "counters": [{"opponent": "giratina_origin", "rating": 175}, {"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "dialga", "rating": 230}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "zacian_hero", "rating": 332}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43368}, {"moveId": "THUNDER_SHOCK", "uses": 33132}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 26609}, {"moveId": "LAST_RESORT", "uses": 19099}, {"moveId": "THUNDERBOLT", "uses": 11483}, {"moveId": "THUNDER", "uses": 9978}, {"moveId": "ZAP_CANNON", "uses": 9570}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LAST_RESORT"], "score": 41.9}, {"speciesId": "aggron_shadow", "speciesName": "A<PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 681, "opRating": 318}, {"opponent": "lugia", "rating": 589, "opRating": 410}, {"opponent": "ho_oh", "rating": 585, "opRating": 414}, {"opponent": "giratina_altered", "rating": 585, "opRating": 414}, {"opponent": "gyarados", "rating": 563, "opRating": 436}], "counters": [{"opponent": "zacian_hero", "rating": 95}, {"opponent": "metagross", "rating": 177}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "dialga", "rating": 453}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 34072}, {"moveId": "SMACK_DOWN", "uses": 33093}, {"moveId": "IRON_TAIL", "uses": 9443}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 31427}, {"moveId": "HEAVY_SLAM", "uses": 21916}, {"moveId": "THUNDER", "uses": 14627}, {"moveId": "ROCK_TOMB", "uses": 8481}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 41.8}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 608, "matchups": [{"opponent": "swampert", "rating": 927, "opRating": 72}, {"opponent": "swampert_shadow", "rating": 927, "opRating": 72}, {"opponent": "zap<PERSON>_galarian", "rating": 770, "opRating": 229}, {"opponent": "excadrill", "rating": 639, "opRating": 360}, {"opponent": "grou<PERSON>", "rating": 543, "opRating": 456}], "counters": [{"opponent": "lugia", "rating": 164}, {"opponent": "dialga", "rating": 233}, {"opponent": "giratina_origin", "rating": 272}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 352}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 26981}, {"moveId": "CONFUSION", "uses": 24954}, {"moveId": "EXTRASENSORY", "uses": 19426}, {"moveId": "ZEN_HEADBUTT", "uses": 5288}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 31779}, {"moveId": "PSYCHIC", "uses": 29998}, {"moveId": "SOLAR_BEAM", "uses": 14537}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 41.8}, {"speciesId": "kabutops", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 574, "matchups": [{"opponent": "ho_oh", "rating": 911, "opRating": 88}, {"opponent": "moltres_shadow", "rating": 908, "opRating": 91}, {"opponent": "moltres", "rating": 897, "opRating": 102}, {"opponent": "walrein_shadow", "rating": 890, "opRating": 109}, {"opponent": "entei", "rating": 778, "opRating": 221}], "counters": [{"opponent": "mewtwo", "rating": 218}, {"opponent": "garcho<PERSON>", "rating": 218}, {"opponent": "giratina_origin", "rating": 254}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "dialga", "rating": 293}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 25561}, {"moveId": "FURY_CUTTER", "uses": 22819}, {"moveId": "WATERFALL", "uses": 20463}, {"moveId": "ROCK_SMASH", "uses": 7617}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 32982}, {"moveId": "ANCIENT_POWER", "uses": 29722}, {"moveId": "WATER_PULSE", "uses": 13790}]}, "moveset": ["MUD_SHOT", "STONE_EDGE", "ANCIENT_POWER"], "score": 41.8}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 681, "matchups": [{"opponent": "magnezone_shadow", "rating": 950, "opRating": 49}, {"opponent": "electivire_shadow", "rating": 950, "opRating": 49}, {"opponent": "raikou_shadow", "rating": 938, "opRating": 61}, {"opponent": "excadrill", "rating": 880, "opRating": 119}, {"opponent": "metagross", "rating": 709, "opRating": 290}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "lugia", "rating": 185}, {"opponent": "dragonite", "rating": 252}, {"opponent": "zacian_hero", "rating": 329}, {"opponent": "mewtwo", "rating": 343}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40539}, {"moveId": "DRAGON_TAIL", "uses": 35961}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30282}, {"moveId": "EARTH_POWER", "uses": 20162}, {"moveId": "STONE_EDGE", "uses": 17386}, {"moveId": "EARTHQUAKE", "uses": 8760}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 41.6}, {"speciesId": "malamar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 563, "matchups": [{"opponent": "espeon", "rating": 900, "opRating": 100}, {"opponent": "gengar", "rating": 819, "opRating": 180}, {"opponent": "mewtwo", "rating": 644}, {"opponent": "excadrill", "rating": 600, "opRating": 400}, {"opponent": "mewtwo_shadow", "rating": 544, "opRating": 455}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "dragonite", "rating": 231}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "giratina_origin", "rating": 241}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 57776}, {"moveId": "PECK", "uses": 18724}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 32011}, {"moveId": "SUPER_POWER", "uses": 28279}, {"moveId": "PSYBEAM", "uses": 8547}, {"moveId": "HYPER_BEAM", "uses": 7693}]}, "moveset": ["PSYCHO_CUT", "FOUL_PLAY", "SUPER_POWER"], "score": 41.6}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "matchups": [{"opponent": "articuno_galarian", "rating": 907, "opRating": 92}, {"opponent": "mewtwo", "rating": 738}, {"opponent": "mewtwo_shadow", "rating": 703, "opRating": 296}, {"opponent": "metagross", "rating": 614}, {"opponent": "snorlax", "rating": 539, "opRating": 460}], "counters": [{"opponent": "excadrill", "rating": 116}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "lugia", "rating": 300}, {"opponent": "dialga", "rating": 307}, {"opponent": "zacian_hero", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 56919}, {"moveId": "PECK", "uses": 19581}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 24820}, {"moveId": "DARK_PULSE", "uses": 19092}, {"moveId": "SKY_ATTACK", "uses": 18707}, {"moveId": "PSYCHIC", "uses": 8076}, {"moveId": "RETURN", "uses": 5795}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 41.5}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 587, "matchups": [{"opponent": "articuno_galarian", "rating": 923, "opRating": 76}, {"opponent": "mewtwo_shadow", "rating": 914, "opRating": 85}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 859, "opRating": 140}, {"opponent": "metagross", "rating": 762}, {"opponent": "mewtwo", "rating": 704}], "counters": [{"opponent": "zacian_hero", "rating": 190}, {"opponent": "dialga", "rating": 301}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "lugia", "rating": 316}, {"opponent": "gyarados", "rating": 327}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46791}, {"moveId": "FIRE_FANG", "uses": 29709}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34740}, {"moveId": "FLAMETHROWER", "uses": 20970}, {"moveId": "FOUL_PLAY", "uses": 15014}, {"moveId": "FIRE_BLAST", "uses": 5843}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 41.5}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 595, "matchups": [{"opponent": "gyarados", "rating": 795}, {"opponent": "gyarado<PERSON>_shadow", "rating": 795, "opRating": 204}, {"opponent": "sylveon", "rating": 641, "opRating": 358}, {"opponent": "lugia", "rating": 610, "opRating": 389}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 606, "opRating": 393}], "counters": [{"opponent": "giratina_origin", "rating": 175}, {"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "dragonite", "rating": 263}, {"opponent": "dialga", "rating": 279}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30712}, {"moveId": "SPARK", "uses": 28186}, {"moveId": "CHARGE_BEAM", "uses": 17592}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 27307}, {"moveId": "DISCHARGE", "uses": 23730}, {"moveId": "RETURN", "uses": 10731}, {"moveId": "ZAP_CANNON", "uses": 9093}, {"moveId": "FLASH_CANNON", "uses": 5827}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 41.5}, {"speciesId": "hitmonchan", "speciesName": "Hitmonchan", "rating": 610, "matchups": [{"opponent": "zarude", "rating": 779, "opRating": 220}, {"opponent": "regirock", "rating": 716, "opRating": 283}, {"opponent": "snorlax", "rating": 618, "opRating": 381}, {"opponent": "dialga", "rating": 562}, {"opponent": "excadrill", "rating": 562, "opRating": 437}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 161}, {"opponent": "giratina_origin", "rating": 191}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "gyarados", "rating": 288}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 41550}, {"moveId": "BULLET_PUNCH", "uses": 26915}, {"moveId": "ROCK_SMASH", "uses": 8027}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23363}, {"moveId": "ICE_PUNCH", "uses": 15109}, {"moveId": "THUNDER_PUNCH", "uses": 11039}, {"moveId": "BRICK_BREAK", "uses": 10387}, {"moveId": "FIRE_PUNCH", "uses": 10011}, {"moveId": "RETURN", "uses": 4624}, {"moveId": "POWER_UP_PUNCH", "uses": 2077}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 41.1}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "zarude", "rating": 924, "opRating": 75}, {"opponent": "tangrowth_shadow", "rating": 917, "opRating": 82}, {"opponent": "hydreigon", "rating": 715, "opRating": 284}, {"opponent": "latios_shadow", "rating": 679, "opRating": 320}, {"opponent": "mewtwo", "rating": 503}], "counters": [{"opponent": "giratina_origin", "rating": 105}, {"opponent": "dragonite", "rating": 199}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "excadrill", "rating": 281}, {"opponent": "dialga", "rating": 301}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 43381}, {"moveId": "METAL_CLAW", "uses": 33119}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 32678}, {"moveId": "STONE_EDGE", "uses": 23842}, {"moveId": "IRON_HEAD", "uses": 19962}]}, "moveset": ["BUG_BITE", "X_SCISSOR", "STONE_EDGE"], "score": 40.9}, {"speciesId": "aggron", "speciesName": "Aggron", "rating": 598, "matchups": [{"opponent": "ho_oh", "rating": 668, "opRating": 331}, {"opponent": "mewtwo", "rating": 652}, {"opponent": "gyarados", "rating": 601, "opRating": 398}, {"opponent": "lugia", "rating": 598, "opRating": 401}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570, "opRating": 429}], "counters": [{"opponent": "zacian_hero", "rating": 72}, {"opponent": "excadrill", "rating": 132}, {"opponent": "metagross", "rating": 162}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "dialga", "rating": 366}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33828}, {"moveId": "SMACK_DOWN", "uses": 32670}, {"moveId": "IRON_TAIL", "uses": 9975}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 27235}, {"moveId": "HEAVY_SLAM", "uses": 18648}, {"moveId": "THUNDER", "uses": 12763}, {"moveId": "RETURN", "uses": 10407}, {"moveId": "ROCK_TOMB", "uses": 7336}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 40.8}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 433, "matchups": [{"opponent": "pangoro", "rating": 901, "opRating": 98}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "kommo_o", "rating": 710, "opRating": 289}, {"opponent": "gallade_shadow", "rating": 703, "opRating": 296}, {"opponent": "hydreigon", "rating": 678, "opRating": 321}], "counters": [{"opponent": "metagross", "rating": 206}, {"opponent": "lugia", "rating": 211}, {"opponent": "dialga", "rating": 269}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "zacian_hero", "rating": 274}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 6474}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 5472}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5349}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4876}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4714}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4642}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4549}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4396}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4387}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4287}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4217}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4185}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4152}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3848}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3823}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3707}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3588}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 35993}, {"moveId": "HYPER_BEAM", "uses": 26094}, {"moveId": "SOLAR_BEAM", "uses": 14577}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 40.8}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 565, "matchups": [{"opponent": "raikou_shadow", "rating": 905, "opRating": 94}, {"opponent": "metagross", "rating": 698, "opRating": 301}, {"opponent": "excadrill", "rating": 603, "opRating": 396}, {"opponent": "dialga", "rating": 600}, {"opponent": "zekrom", "rating": 585, "opRating": 414}], "counters": [{"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "lugia", "rating": 188}, {"opponent": "gyarados", "rating": 195}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "mewtwo", "rating": 364}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53140}, {"moveId": "METAL_CLAW", "uses": 23360}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28715}, {"moveId": "EARTHQUAKE", "uses": 17840}, {"moveId": "BULLDOZE", "uses": 10935}, {"moveId": "ROCK_TOMB", "uses": 10632}, {"moveId": "RETURN", "uses": 8497}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 40.8}, {"speciesId": "forretress_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 571, "matchups": [{"opponent": "mewtwo", "rating": 628}, {"opponent": "dialga", "rating": 582}, {"opponent": "sylveon", "rating": 570, "opRating": 429}, {"opponent": "metagross", "rating": 545, "opRating": 454}, {"opponent": "excadrill", "rating": 533, "opRating": 466}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "dragonite", "rating": 220}, {"opponent": "gyarados", "rating": 265}, {"opponent": "lugia", "rating": 283}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47043}, {"moveId": "STRUGGLE_BUG", "uses": 29457}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 20497}, {"moveId": "EARTHQUAKE", "uses": 17499}, {"moveId": "HEAVY_SLAM", "uses": 16806}, {"moveId": "ROCK_TOMB", "uses": 13157}, {"moveId": "SAND_TOMB", "uses": 8572}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 40.6}, {"speciesId": "king<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 548, "matchups": [{"opponent": "entei", "rating": 774, "opRating": 225}, {"opponent": "vaporeon", "rating": 734, "opRating": 265}, {"opponent": "kyogre", "rating": 695, "opRating": 304}, {"opponent": "excadrill", "rating": 518, "opRating": 481}, {"opponent": "snorlax", "rating": 506, "opRating": 493}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "dialga", "rating": 209}, {"opponent": "lugia", "rating": 216}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "gyarados", "rating": 250}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33719}, {"moveId": "WATERFALL", "uses": 21735}, {"moveId": "WATER_GUN", "uses": 21150}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 26760}, {"moveId": "OCTAZOOKA", "uses": 17672}, {"moveId": "BLIZZARD", "uses": 17066}, {"moveId": "HYDRO_PUMP", "uses": 14798}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 40.6}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 601, "matchups": [{"opponent": "rai<PERSON>u", "rating": 905, "opRating": 94}, {"opponent": "metagross", "rating": 722, "opRating": 277}, {"opponent": "dialga", "rating": 631}, {"opponent": "zekrom", "rating": 631, "opRating": 368}, {"opponent": "excadrill", "rating": 548, "opRating": 451}], "counters": [{"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "zacian_hero", "rating": 182}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "gyarados", "rating": 260}, {"opponent": "giratina_origin", "rating": 310}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53797}, {"moveId": "METAL_CLAW", "uses": 22703}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 32036}, {"moveId": "EARTHQUAKE", "uses": 19908}, {"moveId": "ROCK_TOMB", "uses": 12243}, {"moveId": "BULLDOZE", "uses": 12220}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 40.6}, {"speciesId": "ampha<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 674, "matchups": [{"opponent": "metagross", "rating": 771}, {"opponent": "gyarados", "rating": 768}, {"opponent": "lugia", "rating": 674, "opRating": 325}, {"opponent": "dialga", "rating": 577}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}], "counters": [{"opponent": "garcho<PERSON>", "rating": 44}, {"opponent": "excadrill", "rating": 141}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "zacian_hero", "rating": 343}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 53344}, {"moveId": "CHARGE_BEAM", "uses": 23156}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 24287}, {"moveId": "FOCUS_BLAST", "uses": 14823}, {"moveId": "DRAGON_PULSE", "uses": 12872}, {"moveId": "POWER_GEM", "uses": 9080}, {"moveId": "THUNDER", "uses": 7857}, {"moveId": "ZAP_CANNON", "uses": 7598}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 40.5}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 572, "matchups": [{"opponent": "porygon_z_shadow", "rating": 889, "opRating": 110}, {"opponent": "yveltal", "rating": 879, "opRating": 120}, {"opponent": "ho_oh", "rating": 714, "opRating": 285}, {"opponent": "zacian_hero", "rating": 667, "opRating": 332}, {"opponent": "gyarados", "rating": 610, "opRating": 389}], "counters": [{"opponent": "giratina_origin", "rating": 131}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "dialga", "rating": 250}, {"opponent": "metagross", "rating": 287}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 58836}, {"moveId": "LOW_KICK", "uses": 17664}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 28763}, {"moveId": "STONE_EDGE", "uses": 26234}, {"moveId": "GRASS_KNOT", "uses": 21421}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 40.5}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 600, "matchups": [{"opponent": "gengar", "rating": 820, "opRating": 179}, {"opponent": "excadrill", "rating": 699, "opRating": 300}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 649, "opRating": 350}, {"opponent": "giratina_origin", "rating": 585, "opRating": 414}, {"opponent": "zacian_hero", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "metagross", "rating": 136}, {"opponent": "lugia", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "mewtwo", "rating": 403}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 76500}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 42568}, {"moveId": "EARTHQUAKE", "uses": 17056}, {"moveId": "PLAY_ROUGH", "uses": 12079}, {"moveId": "HYPER_BEAM", "uses": 4742}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 40.5}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 556, "matchups": [{"opponent": "swampert", "rating": 961, "opRating": 38}, {"opponent": "swampert_shadow", "rating": 951, "opRating": 48}, {"opponent": "excadrill", "rating": 726, "opRating": 273}, {"opponent": "grou<PERSON>", "rating": 646, "opRating": 353}, {"opponent": "kyogre", "rating": 626, "opRating": 373}], "counters": [{"opponent": "lugia", "rating": 154}, {"opponent": "dialga", "rating": 176}, {"opponent": "giratina_origin", "rating": 183}, {"opponent": "metagross", "rating": 264}, {"opponent": "mewtwo", "rating": 403}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41814}, {"moveId": "BITE", "uses": 34686}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30944}, {"moveId": "STONE_EDGE", "uses": 17848}, {"moveId": "EARTHQUAKE", "uses": 16340}, {"moveId": "SAND_TOMB", "uses": 7884}, {"moveId": "SOLAR_BEAM", "uses": 3524}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 40.3}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 630, "matchups": [{"opponent": "genesect_douse", "rating": 909, "opRating": 90}, {"opponent": "metagross", "rating": 802, "opRating": 197}, {"opponent": "sylveon", "rating": 579, "opRating": 420}, {"opponent": "dialga", "rating": 541}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537, "opRating": 462}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "gyarados", "rating": 157}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "giratina_origin", "rating": 252}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 61891}, {"moveId": "LOW_KICK", "uses": 14609}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 31121}, {"moveId": "ROCK_SLIDE", "uses": 20748}, {"moveId": "FOCUS_BLAST", "uses": 13638}, {"moveId": "FLAME_CHARGE", "uses": 8563}, {"moveId": "HEAT_WAVE", "uses": 2362}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 40.2}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 656, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 733, "opRating": 266}, {"opponent": "sylveon", "rating": 733, "opRating": 266}, {"opponent": "zacian_hero", "rating": 663, "opRating": 336}, {"opponent": "yveltal", "rating": 625, "opRating": 375}, {"opponent": "gyarados", "rating": 552, "opRating": 447}], "counters": [{"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 225}, {"opponent": "metagross", "rating": 290}, {"opponent": "giratina_origin", "rating": 298}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26706}, {"moveId": "LICK", "uses": 20157}, {"moveId": "INFESTATION", "uses": 19385}, {"moveId": "ACID", "uses": 10305}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 23324}, {"moveId": "THUNDER_PUNCH", "uses": 20187}, {"moveId": "SLUDGE_WAVE", "uses": 13034}, {"moveId": "RETURN", "uses": 10272}, {"moveId": "GUNK_SHOT", "uses": 5124}, {"moveId": "ACID_SPRAY", "uses": 4431}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 40.1}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 517, "matchups": [{"opponent": "honch<PERSON><PERSON>", "rating": 903, "opRating": 96}, {"opponent": "weavile_shadow", "rating": 884, "opRating": 115}, {"opponent": "gyarado<PERSON>_shadow", "rating": 759, "opRating": 240}, {"opponent": "gengar", "rating": 628, "opRating": 371}, {"opponent": "bewear", "rating": 606, "opRating": 393}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "mewtwo", "rating": 182}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dialga", "rating": 342}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 39798}, {"moveId": "TACKLE", "uses": 30040}, {"moveId": "TAKE_DOWN", "uses": 6608}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34428}, {"moveId": "WILD_CHARGE", "uses": 27243}, {"moveId": "PAYBACK", "uses": 14953}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "WILD_CHARGE"], "score": 39.8}, {"speciesId": "golem_alolan", "speciesName": "Golem (Alolan)", "rating": 671, "matchups": [{"opponent": "ho_oh", "rating": 866, "opRating": 133}, {"opponent": "gyarados", "rating": 750}, {"opponent": "yveltal", "rating": 654, "opRating": 345}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "lugia", "rating": 578, "opRating": 421}], "counters": [{"opponent": "dragonite", "rating": 162}, {"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "dialga", "rating": 266}, {"opponent": "giratina_origin", "rating": 274}, {"opponent": "mewtwo", "rating": 466}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 34317}, {"moveId": "ROLLOUT", "uses": 24148}, {"moveId": "ROCK_THROW", "uses": 18065}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37054}, {"moveId": "STONE_EDGE", "uses": 20633}, {"moveId": "ROCK_BLAST", "uses": 18799}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "STONE_EDGE"], "score": 39.8}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 610, "matchups": [{"opponent": "ho_oh", "rating": 644, "opRating": 355}, {"opponent": "metagross", "rating": 627, "opRating": 372}, {"opponent": "sylveon", "rating": 600, "opRating": 399}, {"opponent": "dialga", "rating": 588}, {"opponent": "zacian_hero", "rating": 519, "opRating": 480}], "counters": [{"opponent": "gyarados", "rating": 85}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "giratina_origin", "rating": 243}, {"opponent": "lugia", "rating": 314}, {"opponent": "mewtwo", "rating": 421}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 58405}, {"moveId": "ROCK_SMASH", "uses": 18095}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23007}, {"moveId": "SURF", "uses": 19570}, {"moveId": "EARTHQUAKE", "uses": 18352}, {"moveId": "MEGAHORN", "uses": 15514}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "SURF"], "score": 39.8}, {"speciesId": "lycanroc_midnight", "speciesName": "Lycanroc (Midnight)", "rating": 620, "matchups": [{"opponent": "weavile_shadow", "rating": 865, "opRating": 134}, {"opponent": "ho_oh", "rating": 828, "opRating": 171}, {"opponent": "moltres", "rating": 820, "opRating": 179}, {"opponent": "mamos<PERSON>_shadow", "rating": 750, "opRating": 250}, {"opponent": "yveltal", "rating": 558, "opRating": 441}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "zacian_hero", "rating": 144}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "gyarados", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 291}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43983}, {"moveId": "ROCK_THROW", "uses": 32517}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26955}, {"moveId": "CRUNCH", "uses": 26472}, {"moveId": "PSYCHIC_FANGS", "uses": 23120}]}, "moveset": ["COUNTER", "STONE_EDGE", "CRUNCH"], "score": 39.6}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 651, "matchups": [{"opponent": "yveltal", "rating": 854, "opRating": 145}, {"opponent": "gyarados", "rating": 752}, {"opponent": "lugia", "rating": 633, "opRating": 366}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 633, "opRating": 366}, {"opponent": "metagross", "rating": 552, "opRating": 447}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "dialga", "rating": 252}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "zacian_hero", "rating": 367}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 10957}, {"moveId": "SNARL", "uses": 9083}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4572}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4330}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4062}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3839}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3739}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3618}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3614}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3587}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3454}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3300}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3268}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3051}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2993}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2961}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2926}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2749}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32793}, {"moveId": "CRUNCH", "uses": 18668}, {"moveId": "PSYCHIC_FANGS", "uses": 16249}, {"moveId": "RETURN", "uses": 6283}, {"moveId": "HYPER_BEAM", "uses": 2493}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 39.5}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 580, "matchups": [{"opponent": "genesect_douse", "rating": 882, "opRating": 117}, {"opponent": "genesect_shock", "rating": 882, "opRating": 117}, {"opponent": "metagross", "rating": 734, "opRating": 265}, {"opponent": "excadrill", "rating": 728, "opRating": 271}, {"opponent": "sylveon", "rating": 516, "opRating": 483}], "counters": [{"opponent": "giratina_origin", "rating": 117}, {"opponent": "gyarados", "rating": 188}, {"opponent": "mewtwo", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "dialga", "rating": 331}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38536}, {"moveId": "EMBER", "uses": 37964}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 48592}, {"moveId": "FLAMETHROWER", "uses": 18118}, {"moveId": "FIRE_BLAST", "uses": 9887}, {"moveId": "FRUSTRATION", "uses": 7}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "FLAMETHROWER"], "score": 39.2}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 617, "matchups": [{"opponent": "zarude", "rating": 842, "opRating": 157}, {"opponent": "metagross", "rating": 803}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 669, "opRating": 330}, {"opponent": "sylveon", "rating": 669, "opRating": 330}, {"opponent": "zacian_hero", "rating": 538, "opRating": 461}], "counters": [{"opponent": "gyarados", "rating": 118}, {"opponent": "giratina_origin", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "lugia", "rating": 280}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 30270}, {"moveId": "SHADOW_CLAW", "uses": 29099}, {"moveId": "EMBER", "uses": 17061}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 41688}, {"moveId": "RETURN", "uses": 10666}, {"moveId": "SOLAR_BEAM", "uses": 10117}, {"moveId": "OVERHEAT", "uses": 8872}, {"moveId": "FIRE_BLAST", "uses": 5149}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 39.2}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 637, "matchups": [{"opponent": "zacian_hero", "rating": 795}, {"opponent": "metagross", "rating": 778}, {"opponent": "sylveon", "rating": 728, "opRating": 271}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 709, "opRating": 290}, {"opponent": "dialga", "rating": 555}], "counters": [{"opponent": "dragonite", "rating": 135}, {"opponent": "gyarados", "rating": 144}, {"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "mewtwo", "rating": 179}, {"opponent": "giratina_origin", "rating": 286}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 42156}, {"moveId": "FIRE_FANG", "uses": 17867}, {"moveId": "TACKLE", "uses": 16447}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27027}, {"moveId": "OVERHEAT", "uses": 21181}, {"moveId": "PSYCHIC", "uses": 14542}, {"moveId": "FOCUS_BLAST", "uses": 13759}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 39.1}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 561, "matchups": [{"opponent": "gengar", "rating": 817, "opRating": 182}, {"opponent": "gyarado<PERSON>_shadow", "rating": 674, "opRating": 325}, {"opponent": "zap<PERSON>_shadow", "rating": 640, "opRating": 359}, {"opponent": "gyarados", "rating": 550, "opRating": 449}, {"opponent": "metagross", "rating": 547, "opRating": 452}], "counters": [{"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "zacian_hero", "rating": 182}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "dialga", "rating": 244}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 57652}, {"moveId": "ACID", "uses": 18848}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27197}, {"moveId": "CRUNCH", "uses": 25145}, {"moveId": "THUNDERBOLT", "uses": 20580}, {"moveId": "ACID_SPRAY", "uses": 3582}]}, "moveset": ["SPARK", "DRAGON_CLAW", "CRUNCH"], "score": 39.1}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 540, "matchups": [{"opponent": "swampert", "rating": 820, "opRating": 179}, {"opponent": "kyogre", "rating": 753, "opRating": 246}, {"opponent": "gyarados", "rating": 625, "opRating": 375}, {"opponent": "zacian_hero", "rating": 536, "opRating": 463}, {"opponent": "excadrill", "rating": 533, "opRating": 466}], "counters": [{"opponent": "lugia", "rating": 138}, {"opponent": "giratina_origin", "rating": 159}, {"opponent": "metagross", "rating": 188}, {"opponent": "dialga", "rating": 220}, {"opponent": "mewtwo", "rating": 341}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 65296}, {"moveId": "IRON_TAIL", "uses": 11204}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 34941}, {"moveId": "LEAF_TORNADO", "uses": 17954}, {"moveId": "AERIAL_ACE", "uses": 14224}, {"moveId": "GRASS_KNOT", "uses": 9213}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "LEAF_TORNADO"], "score": 38.9}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 547, "matchups": [{"opponent": "magnezone_shadow", "rating": 885, "opRating": 114}, {"opponent": "magnezone", "rating": 885, "opRating": 114}, {"opponent": "xurkitree", "rating": 883, "opRating": 116}, {"opponent": "gyarados", "rating": 546, "opRating": 453}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "opRating": 478}], "counters": [{"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "dragonite", "rating": 138}, {"opponent": "giratina_origin", "rating": 197}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "dialga", "rating": 399}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38620}, {"moveId": "THUNDER_SHOCK", "uses": 37880}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 33841}, {"moveId": "DISCHARGE", "uses": 26059}, {"moveId": "MUDDY_WATER", "uses": 16591}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "DISCHARGE"], "score": 38.9}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 466, "matchups": [{"opponent": "pinsir", "rating": 878, "opRating": 121}, {"opponent": "gengar", "rating": 850, "opRating": 149}, {"opponent": "trevenant", "rating": 823, "opRating": 176}, {"opponent": "heracross", "rating": 723, "opRating": 276}, {"opponent": "giratina_origin", "rating": 626, "opRating": 373}], "counters": [{"opponent": "lugia", "rating": 211}, {"opponent": "dialga", "rating": 217}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "mewtwo", "rating": 369}, {"opponent": "garcho<PERSON>", "rating": 396}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 45375}, {"moveId": "EXTRASENSORY", "uses": 31125}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33870}, {"moveId": "SHADOW_BALL", "uses": 23973}, {"moveId": "PSYCHIC", "uses": 14221}, {"moveId": "NIGHT_SHADE", "uses": 4428}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "SHADOW_BALL"], "score": 38.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 522, "matchups": [{"opponent": "celebi", "rating": 775, "opRating": 224}, {"opponent": "gallade", "rating": 751, "opRating": 248}, {"opponent": "tangrowth_shadow", "rating": 728, "opRating": 271}, {"opponent": "virizion", "rating": 697, "opRating": 302}, {"opponent": "zarude", "rating": 651, "opRating": 348}], "counters": [{"opponent": "giratina_origin", "rating": 205}, {"opponent": "dialga", "rating": 206}, {"opponent": "metagross", "rating": 270}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 43665}, {"moveId": "STEEL_WING", "uses": 32835}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33563}, {"moveId": "SKY_ATTACK", "uses": 25158}, {"moveId": "FLASH_CANNON", "uses": 9651}, {"moveId": "RETURN", "uses": 8187}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 38.8}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 507, "matchups": [{"opponent": "trevenant", "rating": 770, "opRating": 229}, {"opponent": "heracross", "rating": 735, "opRating": 264}, {"opponent": "swampert", "rating": 662, "opRating": 337}, {"opponent": "swampert_shadow", "rating": 613, "opRating": 386}, {"opponent": "giratina_origin", "rating": 529, "opRating": 470}], "counters": [{"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "mewtwo", "rating": 223}, {"opponent": "dialga", "rating": 236}, {"opponent": "zacian_hero", "rating": 236}, {"opponent": "gyarados", "rating": 244}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 37773}, {"moveId": "PECK", "uses": 24296}, {"moveId": "ROCK_SMASH", "uses": 14406}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 44807}, {"moveId": "ROCK_BLAST", "uses": 22133}, {"moveId": "FLASH_CANNON", "uses": 9598}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 38.8}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 596, "matchups": [{"opponent": "genesect_douse", "rating": 889, "opRating": 110}, {"opponent": "metagross", "rating": 764, "opRating": 235}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 711, "opRating": 288}, {"opponent": "sylveon", "rating": 669, "opRating": 330}, {"opponent": "dialga", "rating": 568}], "counters": [{"opponent": "gyarados", "rating": 144}, {"opponent": "mewtwo", "rating": 179}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "giratina_origin", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 305}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31378}, {"moveId": "SHADOW_CLAW", "uses": 29078}, {"moveId": "EMBER", "uses": 15984}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 48148}, {"moveId": "SOLAR_BEAM", "uses": 12026}, {"moveId": "OVERHEAT", "uses": 10319}, {"moveId": "FIRE_BLAST", "uses": 6014}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 38.8}, {"speciesId": "dragalge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 593, "matchups": [{"opponent": "magmortar_shadow", "rating": 748, "opRating": 251}, {"opponent": "over<PERSON><PERSON>l", "rating": 721, "opRating": 278}, {"opponent": "roserade", "rating": 708, "opRating": 291}, {"opponent": "thundurus_incarnate", "rating": 701, "opRating": 298}, {"opponent": "kyogre", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 190}, {"opponent": "giratina_origin", "rating": 296}, {"opponent": "garcho<PERSON>", "rating": 356}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 37629}, {"moveId": "WATER_GUN", "uses": 24357}, {"moveId": "ACID", "uses": 14412}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 31005}, {"moveId": "OUTRAGE", "uses": 27305}, {"moveId": "GUNK_SHOT", "uses": 13272}, {"moveId": "HYDRO_PUMP", "uses": 4929}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "OUTRAGE"], "score": 38.7}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 512, "matchups": [{"opponent": "mewtwo_shadow", "rating": 848, "opRating": 151}, {"opponent": "swampert", "rating": 751, "opRating": 248}, {"opponent": "swampert_shadow", "rating": 725, "opRating": 274}, {"opponent": "latios_shadow", "rating": 625, "opRating": 374}, {"opponent": "zacian_hero", "rating": 540, "opRating": 459}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "giratina_origin", "rating": 163}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dialga", "rating": 298}, {"opponent": "gyarados", "rating": 306}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49296}, {"moveId": "RAZOR_LEAF", "uses": 27204}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26411}, {"moveId": "SEED_BOMB", "uses": 21651}, {"moveId": "FOUL_PLAY", "uses": 19824}, {"moveId": "FIRE_BLAST", "uses": 8561}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 38.5}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 574, "matchups": [{"opponent": "zarude", "rating": 896, "opRating": 103}, {"opponent": "xurkitree", "rating": 878, "opRating": 121}, {"opponent": "swampert", "rating": 786, "opRating": 213}, {"opponent": "swampert_shadow", "rating": 759, "opRating": 240}, {"opponent": "kyogre", "rating": 536, "opRating": 463}], "counters": [{"opponent": "giratina_origin", "rating": 181}, {"opponent": "lugia", "rating": 202}, {"opponent": "dialga", "rating": 209}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 356}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 44802}, {"moveId": "RAZOR_LEAF", "uses": 31698}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42050}, {"moveId": "X_SCISSOR", "uses": 21424}, {"moveId": "SILVER_WIND", "uses": 7735}, {"moveId": "LEAF_STORM", "uses": 5158}]}, "moveset": ["BUG_BITE", "LEAF_BLADE", "X_SCISSOR"], "score": 38.5}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 594, "matchups": [{"opponent": "zarude", "rating": 880, "opRating": 119}, {"opponent": "tangrowth_shadow", "rating": 880, "opRating": 119}, {"opponent": "virizion", "rating": 816, "opRating": 183}, {"opponent": "sylveon", "rating": 647, "opRating": 352}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609, "opRating": 390}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "gyarados", "rating": 250}, {"opponent": "dialga", "rating": 258}, {"opponent": "mewtwo", "rating": 330}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 43125}, {"moveId": "BUG_BITE", "uses": 33375}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25305}, {"moveId": "MEGAHORN", "uses": 24147}, {"moveId": "SLUDGE_BOMB", "uses": 19147}, {"moveId": "GYRO_BALL", "uses": 7855}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "MEGAHORN"], "score": 38.4}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 610, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 801, "opRating": 198}, {"opponent": "mamos<PERSON>_shadow", "rating": 771, "opRating": 228}, {"opponent": "magnezone", "rating": 758, "opRating": 241}, {"opponent": "magnezone_shadow", "rating": 709, "opRating": 290}, {"opponent": "giratina_origin", "rating": 580, "opRating": 419}], "counters": [{"opponent": "zacian_hero", "rating": 147}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "gyarados", "rating": 170}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "dialga", "rating": 494}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34179}, {"moveId": "COUNTER", "uses": 30616}, {"moveId": "METAL_CLAW", "uses": 11711}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 38933}, {"moveId": "RETURN", "uses": 16502}, {"moveId": "PLAY_ROUGH", "uses": 14693}, {"moveId": "HYPER_BEAM", "uses": 6382}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "RETURN"], "score": 38.4}, {"speciesId": "glalie", "speciesName": "G<PERSON><PERSON>", "rating": 456, "matchups": [{"opponent": "garcho<PERSON>", "rating": 845}, {"opponent": "thundurus_therian", "rating": 750, "opRating": 250}, {"opponent": "zapdos", "rating": 622, "opRating": 377}, {"opponent": "hydreigon", "rating": 619, "opRating": 380}, {"opponent": "latios_shadow", "rating": 578, "opRating": 421}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "dialga", "rating": 298}, {"opponent": "gyarados", "rating": 324}, {"opponent": "zacian_hero", "rating": 335}, {"opponent": "giratina_origin", "rating": 358}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46705}, {"moveId": "FROST_BREATH", "uses": 29795}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 49165}, {"moveId": "SHADOW_BALL", "uses": 20397}, {"moveId": "GYRO_BALL", "uses": 6982}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "SHADOW_BALL"], "score": 38.2}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 537, "matchups": [{"opponent": "dragonite", "rating": 708, "opRating": 291}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 664, "opRating": 335}, {"opponent": "sylveon", "rating": 664, "opRating": 335}, {"opponent": "zacian_hero", "rating": 523, "opRating": 476}, {"opponent": "yveltal", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 171}, {"opponent": "metagross", "rating": 186}, {"opponent": "dialga", "rating": 290}, {"opponent": "giratina_origin", "rating": 308}, {"opponent": "garcho<PERSON>", "rating": 356}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 48745}, {"moveId": "TACKLE", "uses": 27755}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 23788}, {"moveId": "PLAY_ROUGH", "uses": 23137}, {"moveId": "OVERHEAT", "uses": 19883}, {"moveId": "HYPER_BEAM", "uses": 9659}]}, "moveset": ["FAIRY_WIND", "SLUDGE", "PLAY_ROUGH"], "score": 38.2}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 576, "matchups": [{"opponent": "mewtwo", "rating": 667}, {"opponent": "nihilego", "rating": 643, "opRating": 356}, {"opponent": "mewtwo_shadow", "rating": 628, "opRating": 371}, {"opponent": "excadrill", "rating": 625, "opRating": 375}, {"opponent": "sylveon", "rating": 542, "opRating": 457}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "dialga", "rating": 252}, {"opponent": "lugia", "rating": 269}, {"opponent": "gyarados", "rating": 270}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47806}, {"moveId": "STRUGGLE_BUG", "uses": 28694}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 18203}, {"moveId": "EARTHQUAKE", "uses": 15505}, {"moveId": "HEAVY_SLAM", "uses": 14781}, {"moveId": "ROCK_TOMB", "uses": 11353}, {"moveId": "RETURN", "uses": 8910}, {"moveId": "SAND_TOMB", "uses": 7655}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 38.1}, {"speciesId": "jynx", "speciesName": "Jynx", "rating": 510, "matchups": [{"opponent": "garcho<PERSON>", "rating": 848}, {"opponent": "sneasler", "rating": 775, "opRating": 224}, {"opponent": "tangrowth_shadow", "rating": 714, "opRating": 285}, {"opponent": "zap<PERSON>_galarian", "rating": 590, "opRating": 409}, {"opponent": "dragonite", "rating": 533, "opRating": 466}], "counters": [{"opponent": "zacian_hero", "rating": 228}, {"opponent": "gyarados", "rating": 234}, {"opponent": "dialga", "rating": 274}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "giratina_origin", "rating": 342}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 41880}, {"moveId": "FROST_BREATH", "uses": 32953}, {"moveId": "POUND", "uses": 1604}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30933}, {"moveId": "ICE_PUNCH", "uses": 18640}, {"moveId": "PSYSHOCK", "uses": 13822}, {"moveId": "FOCUS_BLAST", "uses": 8970}, {"moveId": "DRAINING_KISS", "uses": 4110}]}, "moveset": ["CONFUSION", "AVALANCHE", "ICE_PUNCH"], "score": 38.1}, {"speciesId": "altaria", "speciesName": "Altaria", "rating": 547, "matchups": [{"opponent": "magmortar_shadow", "rating": 756, "opRating": 243}, {"opponent": "sneasler", "rating": 609, "opRating": 390}, {"opponent": "grou<PERSON>", "rating": 600, "opRating": 399}, {"opponent": "swampert", "rating": 560, "opRating": 439}, {"opponent": "kyogre", "rating": 527, "opRating": 472}], "counters": [{"opponent": "zacian_hero", "rating": 196}, {"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "mewtwo", "rating": 359}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52649}, {"moveId": "PECK", "uses": 23851}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 34979}, {"moveId": "MOONBLAST", "uses": 19789}, {"moveId": "DRAGON_PULSE", "uses": 16747}, {"moveId": "DAZZLING_GLEAM", "uses": 5108}]}, "moveset": ["DRAGON_BREATH", "SKY_ATTACK", "MOONBLAST"], "score": 37.9}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 586, "matchups": [{"opponent": "swampert", "rating": 951, "opRating": 48}, {"opponent": "swampert_shadow", "rating": 927, "opRating": 72}, {"opponent": "terrakion", "rating": 742, "opRating": 257}, {"opponent": "grou<PERSON>", "rating": 631, "opRating": 368}, {"opponent": "kyogre", "rating": 569, "opRating": 430}], "counters": [{"opponent": "lugia", "rating": 126}, {"opponent": "dialga", "rating": 195}, {"opponent": "giratina_origin", "rating": 223}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "metagross", "rating": 252}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 26177}, {"moveId": "CONFUSION", "uses": 25132}, {"moveId": "EXTRASENSORY", "uses": 19705}, {"moveId": "ZEN_HEADBUTT", "uses": 5513}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26828}, {"moveId": "PSYCHIC", "uses": 24514}, {"moveId": "RETURN", "uses": 12889}, {"moveId": "SOLAR_BEAM", "uses": 12220}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 37.9}, {"speciesId": "vanilluxe", "speciesName": "Vanilluxe", "rating": 429, "matchups": [{"opponent": "flygon_shadow", "rating": 908, "opRating": 91}, {"opponent": "gliscor_shadow", "rating": 882, "opRating": 117}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 857, "opRating": 142}, {"opponent": "dragonite_shadow", "rating": 636, "opRating": 363}, {"opponent": "dragonite", "rating": 617, "opRating": 382}], "counters": [{"opponent": "metagross", "rating": 177}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "dialga", "rating": 263}, {"opponent": "giratina_origin", "rating": 286}], "moves": {"fastMoves": [{"moveId": "FROST_BREATH", "uses": 54145}, {"moveId": "ASTONISH", "uses": 22355}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 38173}, {"moveId": "SIGNAL_BEAM", "uses": 23023}, {"moveId": "FLASH_CANNON", "uses": 15326}]}, "moveset": ["FROST_BREATH", "BLIZZARD", "SIGNAL_BEAM"], "score": 37.9}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "gyarados", "rating": 814}, {"opponent": "dialga", "rating": 607}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 586, "opRating": 413}, {"opponent": "lugia", "rating": 508, "opRating": 491}, {"opponent": "zacian_hero", "rating": 505, "opRating": 494}], "counters": [{"opponent": "garcho<PERSON>", "rating": 44}, {"opponent": "excadrill", "rating": 120}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "dragonite", "rating": 284}, {"opponent": "mewtwo", "rating": 385}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 51990}, {"moveId": "CHARGE_BEAM", "uses": 24510}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 21742}, {"moveId": "FOCUS_BLAST", "uses": 13038}, {"moveId": "DRAGON_PULSE", "uses": 11066}, {"moveId": "RETURN", "uses": 8789}, {"moveId": "POWER_GEM", "uses": 8015}, {"moveId": "THUNDER", "uses": 7001}, {"moveId": "ZAP_CANNON", "uses": 6805}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 37.8}, {"speciesId": "electrode_shadow", "speciesName": "Electrode (Shadow)", "rating": 494, "matchups": [{"opponent": "suicune_shadow", "rating": 855, "opRating": 144}, {"opponent": "walrein_shadow", "rating": 806, "opRating": 193}, {"opponent": "moltres", "rating": 700, "opRating": 299}, {"opponent": "gyarados", "rating": 626, "opRating": 373}, {"opponent": "zap<PERSON>_galarian", "rating": 531, "opRating": 468}], "counters": [{"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "dialga", "rating": 225}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "mewtwo", "rating": 364}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33529}, {"moveId": "SPARK", "uses": 24403}, {"moveId": "TACKLE", "uses": 18573}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 29664}, {"moveId": "DISCHARGE", "uses": 25933}, {"moveId": "THUNDERBOLT", "uses": 11083}, {"moveId": "HYPER_BEAM", "uses": 9731}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 37.8}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 522, "matchups": [{"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "moltres_shadow", "rating": 846, "opRating": 153}, {"opponent": "ho_oh", "rating": 844, "opRating": 155}, {"opponent": "gyarados", "rating": 630, "opRating": 369}, {"opponent": "zap<PERSON>_shadow", "rating": 607, "opRating": 392}], "counters": [{"opponent": "metagross", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "mewtwo", "rating": 276}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 68014}, {"moveId": "ZEN_HEADBUTT", "uses": 8486}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 38190}, {"moveId": "OUTRAGE", "uses": 19215}, {"moveId": "FLAMETHROWER", "uses": 19179}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "OUTRAGE"], "score": 37.8}, {"speciesId": "skar<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 520, "matchups": [{"opponent": "tapu_bulu", "rating": 781, "opRating": 218}, {"opponent": "genesect", "rating": 667, "opRating": 332}, {"opponent": "zacian_hero", "rating": 540, "opRating": 459}, {"opponent": "genesect_douse", "rating": 536, "opRating": 463}, {"opponent": "genesect_chill", "rating": 536, "opRating": 463}], "counters": [{"opponent": "giratina_origin", "rating": 127}, {"opponent": "metagross", "rating": 252}, {"opponent": "dialga", "rating": 271}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "garcho<PERSON>", "rating": 347}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 44014}, {"moveId": "STEEL_WING", "uses": 32486}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37533}, {"moveId": "SKY_ATTACK", "uses": 28085}, {"moveId": "FLASH_CANNON", "uses": 10844}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 37.8}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 538, "matchups": [{"opponent": "swampert_shadow", "rating": 961, "opRating": 38}, {"opponent": "swampert", "rating": 956, "opRating": 43}, {"opponent": "excadrill", "rating": 646, "opRating": 353}, {"opponent": "kyogre", "rating": 636, "opRating": 363}, {"opponent": "snorlax", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "lugia", "rating": 164}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "metagross", "rating": 220}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41238}, {"moveId": "BITE", "uses": 35262}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 28463}, {"moveId": "STONE_EDGE", "uses": 16015}, {"moveId": "EARTHQUAKE", "uses": 15183}, {"moveId": "SAND_TOMB", "uses": 7378}, {"moveId": "RETURN", "uses": 6268}, {"moveId": "SOLAR_BEAM", "uses": 3141}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 37.8}, {"speciesId": "lanturn", "speciesName": "Lanturn", "rating": 545, "matchups": [{"opponent": "gyarados", "rating": 776}, {"opponent": "gyarado<PERSON>_shadow", "rating": 732, "opRating": 267}, {"opponent": "ho_oh", "rating": 609, "opRating": 390}, {"opponent": "kyogre", "rating": 569, "opRating": 430}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "dialga", "rating": 214}, {"opponent": "dragonite", "rating": 242}, {"opponent": "mewtwo", "rating": 330}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 29309}, {"moveId": "WATER_GUN", "uses": 28601}, {"moveId": "CHARGE_BEAM", "uses": 18554}], "chargedMoves": [{"moveId": "SURF", "uses": 39492}, {"moveId": "THUNDERBOLT", "uses": 21460}, {"moveId": "THUNDER", "uses": 9271}, {"moveId": "HYDRO_PUMP", "uses": 6175}]}, "moveset": ["SPARK", "SURF", "THUNDERBOLT"], "score": 37.7}, {"speciesId": "masquerain", "speciesName": "Masquerain", "rating": 458, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 875, "opRating": 124}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 840, "opRating": 159}, {"opponent": "obstagoon", "rating": 764, "opRating": 235}, {"opponent": "zarude", "rating": 691, "opRating": 308}, {"opponent": "virizion", "rating": 630, "opRating": 369}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "dialga", "rating": 228}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "garcho<PERSON>", "rating": 375}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 38651}, {"moveId": "AIR_SLASH", "uses": 37849}], "chargedMoves": [{"moveId": "LUNGE", "uses": 30544}, {"moveId": "OMINOUS_WIND", "uses": 14000}, {"moveId": "AIR_CUTTER", "uses": 11991}, {"moveId": "SILVER_WIND", "uses": 10940}, {"moveId": "BUBBLE_BEAM", "uses": 9100}]}, "moveset": ["INFESTATION", "LUNGE", "OMINOUS_WIND"], "score": 37.7}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 546, "matchups": [{"opponent": "excadrill", "rating": 847, "opRating": 152}, {"opponent": "genesect_douse", "rating": 776, "opRating": 223}, {"opponent": "genesect_shock", "rating": 776, "opRating": 223}, {"opponent": "metagross", "rating": 763, "opRating": 236}, {"opponent": "sylveon", "rating": 531, "opRating": 468}], "counters": [{"opponent": "giratina_origin", "rating": 181}, {"opponent": "gyarados", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 269}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 28347}, {"moveId": "EMBER", "uses": 27227}, {"moveId": "FEINT_ATTACK", "uses": 20935}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 30703}, {"moveId": "PSYSHOCK", "uses": 13537}, {"moveId": "OVERHEAT", "uses": 12344}, {"moveId": "SOLAR_BEAM", "uses": 7435}, {"moveId": "FLAMETHROWER", "uses": 6635}, {"moveId": "FIRE_BLAST", "uses": 3553}, {"moveId": "HEAT_WAVE", "uses": 2152}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 37.7}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 483, "matchups": [{"opponent": "gengar", "rating": 858, "opRating": 141}, {"opponent": "golem_alolan", "rating": 824, "opRating": 175}, {"opponent": "metagross_shadow", "rating": 694, "opRating": 305}, {"opponent": "giratina_origin", "rating": 627, "opRating": 372}, {"opponent": "excadrill", "rating": 627, "opRating": 372}], "counters": [{"opponent": "zacian_hero", "rating": 141}, {"opponent": "gyarados", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "mewtwo", "rating": 322}, {"opponent": "dialga", "rating": 366}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 57418}, {"moveId": "LOW_KICK", "uses": 19082}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19634}, {"moveId": "STOMP", "uses": 17812}, {"moveId": "BRICK_BREAK", "uses": 13317}, {"moveId": "OUTRAGE", "uses": 11603}, {"moveId": "EARTHQUAKE", "uses": 11452}, {"moveId": "POWER_UP_PUNCH", "uses": 2744}]}, "moveset": ["MUD_SLAP", "CRUNCH", "STOMP"], "score": 37.5}, {"speciesId": "crobat_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 589, "matchups": [{"opponent": "machamp", "rating": 862, "opRating": 137}, {"opponent": "buzzwole", "rating": 783, "opRating": 216}, {"opponent": "sylveon", "rating": 626, "opRating": 373}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 592, "opRating": 407}, {"opponent": "zacian_hero", "rating": 502, "opRating": 497}], "counters": [{"opponent": "giratina_origin", "rating": 163}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "dialga", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 219}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45737}, {"moveId": "BITE", "uses": 30763}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 26955}, {"moveId": "SHADOW_BALL", "uses": 21746}, {"moveId": "POISON_FANG", "uses": 12066}, {"moveId": "AIR_CUTTER", "uses": 8269}, {"moveId": "SLUDGE_BOMB", "uses": 7312}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 37.4}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 382, "matchups": [{"opponent": "quagsire_shadow", "rating": 826, "opRating": 173}, {"opponent": "honchk<PERSON>_shadow", "rating": 753, "opRating": 246}, {"opponent": "staraptor_shadow", "rating": 722, "opRating": 277}, {"opponent": "staraptor", "rating": 704, "opRating": 295}, {"opponent": "lickilicky", "rating": 591, "opRating": 408}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "lugia", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "giratina_origin", "rating": 262}, {"opponent": "mewtwo", "rating": 263}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 48059}, {"moveId": "ASTONISH", "uses": 28441}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28289}, {"moveId": "LOW_SWEEP", "uses": 24296}, {"moveId": "HYPER_BEAM", "uses": 23678}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SCRATCH", "AERIAL_ACE", "LOW_SWEEP"], "score": 37.2}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 549, "matchups": [{"opponent": "swampert", "rating": 914, "opRating": 85}, {"opponent": "swampert_shadow", "rating": 914, "opRating": 85}, {"opponent": "mamos<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "kyogre", "rating": 585, "opRating": 414}, {"opponent": "sylveon", "rating": 546, "opRating": 453}], "counters": [{"opponent": "giratina_origin", "rating": 143}, {"opponent": "lugia", "rating": 173}, {"opponent": "dialga", "rating": 182}, {"opponent": "metagross", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 392}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 46309}, {"moveId": "METAL_CLAW", "uses": 30191}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 25477}, {"moveId": "MIRROR_SHOT", "uses": 21645}, {"moveId": "THUNDER", "uses": 14620}, {"moveId": "FLASH_CANNON", "uses": 11292}, {"moveId": "ACID_SPRAY", "uses": 3525}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "MIRROR_SHOT"], "score": 37.2}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 512, "matchups": [{"opponent": "magnezone_shadow", "rating": 914, "opRating": 85}, {"opponent": "magnezone", "rating": 896, "opRating": 103}, {"opponent": "melmetal", "rating": 635, "opRating": 364}, {"opponent": "metagross", "rating": 582, "opRating": 417}, {"opponent": "dialga", "rating": 527}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "dragonite", "rating": 156}, {"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "mewtwo", "rating": 333}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8190}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 6206}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5292}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5133}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4523}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4452}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4426}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4238}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4199}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4040}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4027}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3997}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3621}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3539}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3532}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3509}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3390}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33692}, {"moveId": "EARTH_POWER", "uses": 23723}, {"moveId": "EARTHQUAKE", "uses": 10305}, {"moveId": "WATER_PULSE", "uses": 8846}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 37.2}, {"speciesId": "be<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 531, "matchups": [{"opponent": "sneasler", "rating": 783, "opRating": 216}, {"opponent": "blaziken", "rating": 777, "opRating": 222}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 740, "opRating": 259}, {"opponent": "ho_oh", "rating": 737, "opRating": 262}, {"opponent": "zapdos", "rating": 628, "opRating": 371}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "metagross", "rating": 191}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "dialga", "rating": 260}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 55680}, {"moveId": "ASTONISH", "uses": 20820}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29086}, {"moveId": "DARK_PULSE", "uses": 24809}, {"moveId": "PSYCHIC", "uses": 22751}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "DARK_PULSE"], "score": 37}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 567, "matchups": [{"opponent": "magnezone_shadow", "rating": 923, "opRating": 76}, {"opponent": "metagross", "rating": 714, "opRating": 285}, {"opponent": "excadrill", "rating": 595, "opRating": 404}, {"opponent": "zacian_hero", "rating": 546, "opRating": 453}, {"opponent": "zekrom", "rating": 524, "opRating": 475}], "counters": [{"opponent": "gyarados", "rating": 85}, {"opponent": "lugia", "rating": 154}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 274}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 49444}, {"moveId": "ASTONISH", "uses": 27056}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 32162}, {"moveId": "EARTH_POWER", "uses": 23032}, {"moveId": "DYNAMIC_PUNCH", "uses": 21377}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "EARTH_POWER"], "score": 37}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 399, "matchups": [{"opponent": "gengar", "rating": 759, "opRating": 240}, {"opponent": "magneton", "rating": 662, "opRating": 337}, {"opponent": "victini", "rating": 567, "opRating": 432}, {"opponent": "giratina_origin", "rating": 553, "opRating": 446}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "metagross", "rating": 232}, {"opponent": "dialga", "rating": 241}, {"opponent": "mewtwo", "rating": 317}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 39507}, {"moveId": "TACKLE", "uses": 36993}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 53110}, {"moveId": "BULLDOZE", "uses": 14677}, {"moveId": "GUNK_SHOT", "uses": 8773}]}, "moveset": ["LICK", "BODY_SLAM", "BULLDOZE"], "score": 37}, {"speciesId": "slurpuff", "speciesName": "Slurpuff", "rating": 495, "matchups": [{"opponent": "kommo_o", "rating": 876, "opRating": 123}, {"opponent": "hydreigon", "rating": 864, "opRating": 135}, {"opponent": "latios_shadow", "rating": 721, "opRating": 278}, {"opponent": "dragonite", "rating": 658, "opRating": 341}, {"opponent": "yveltal", "rating": 597, "opRating": 402}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "dialga", "rating": 250}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 384}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 32104}, {"moveId": "CHARM", "uses": 25926}, {"moveId": "TACKLE", "uses": 18434}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22384}, {"moveId": "PLAY_ROUGH", "uses": 22007}, {"moveId": "ENERGY_BALL", "uses": 18113}, {"moveId": "DRAINING_KISS", "uses": 13928}]}, "moveset": ["FAIRY_WIND", "FLAMETHROWER", "PLAY_ROUGH"], "score": 37}, {"speciesId": "aerodactyl", "speciesName": "Aerodactyl", "rating": 527, "matchups": [{"opponent": "pinsir_shadow", "rating": 886, "opRating": 113}, {"opponent": "moltres", "rating": 875, "opRating": 125}, {"opponent": "ho_oh", "rating": 837, "opRating": 162}, {"opponent": "lugia", "rating": 517, "opRating": 482}, {"opponent": "zacian_hero", "rating": 505, "opRating": 494}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "metagross", "rating": 177}, {"opponent": "dialga", "rating": 192}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "mewtwo", "rating": 210}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36307}, {"moveId": "BITE", "uses": 20846}, {"moveId": "STEEL_WING", "uses": 19391}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27654}, {"moveId": "EARTH_POWER", "uses": 18025}, {"moveId": "IRON_HEAD", "uses": 10019}, {"moveId": "ANCIENT_POWER", "uses": 9899}, {"moveId": "RETURN", "uses": 7913}, {"moveId": "HYPER_BEAM", "uses": 3063}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 36.7}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 576, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 828, "opRating": 171}, {"opponent": "buzzwole", "rating": 783, "opRating": 216}, {"opponent": "sylveon", "rating": 693, "opRating": 306}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 660, "opRating": 339}, {"opponent": "zacian_hero", "rating": 570, "opRating": 429}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "metagross", "rating": 159}, {"opponent": "giratina_origin", "rating": 197}, {"opponent": "dialga", "rating": 206}, {"opponent": "garcho<PERSON>", "rating": 227}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45260}, {"moveId": "BITE", "uses": 31240}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 24411}, {"moveId": "SHADOW_BALL", "uses": 19583}, {"moveId": "POISON_FANG", "uses": 11068}, {"moveId": "AIR_CUTTER", "uses": 7447}, {"moveId": "RETURN", "uses": 7402}, {"moveId": "SLUDGE_BOMB", "uses": 6517}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 36.7}, {"speciesId": "golduck", "speciesName": "Gold<PERSON>", "rating": 562, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 822, "opRating": 177}, {"opponent": "magmortar_shadow", "rating": 787, "opRating": 212}, {"opponent": "moltres_shadow", "rating": 787, "opRating": 212}, {"opponent": "excadrill", "rating": 578, "opRating": 421}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "gyarados", "rating": 195}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "dialga", "rating": 301}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41583}, {"moveId": "CONFUSION", "uses": 34917}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 21098}, {"moveId": "ICE_BEAM", "uses": 16448}, {"moveId": "SYNCHRONOISE", "uses": 10707}, {"moveId": "HYDRO_PUMP", "uses": 9229}, {"moveId": "RETURN", "uses": 7189}, {"moveId": "BUBBLE_BEAM", "uses": 6870}, {"moveId": "PSYCHIC", "uses": 4865}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 36.5}, {"speciesId": "sawk", "speciesName": "Sawk", "rating": 598, "matchups": [{"opponent": "registeel", "rating": 908, "opRating": 91}, {"opponent": "yveltal", "rating": 698, "opRating": 301}, {"opponent": "snorlax", "rating": 564, "opRating": 435}, {"opponent": "kyogre", "rating": 557, "opRating": 442}, {"opponent": "excadrill", "rating": 530, "opRating": 469}], "counters": [{"opponent": "giratina_origin", "rating": 105}, {"opponent": "dialga", "rating": 146}, {"opponent": "metagross", "rating": 156}, {"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "mewtwo", "rating": 270}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57490}, {"moveId": "LOW_KICK", "uses": 19010}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41155}, {"moveId": "FOCUS_BLAST", "uses": 18635}, {"moveId": "LOW_SWEEP", "uses": 16702}]}, "moveset": ["POISON_JAB", "BODY_SLAM", "FOCUS_BLAST"], "score": 36.5}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 502, "matchups": [{"opponent": "swampert", "rating": 911, "opRating": 88}, {"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "tapu_fini", "rating": 850, "opRating": 149}, {"opponent": "kyogre", "rating": 664, "opRating": 335}, {"opponent": "sylveon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "giratina_origin", "rating": 183}, {"opponent": "metagross", "rating": 264}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "mewtwo", "rating": 315}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42825}, {"moveId": "ACID", "uses": 33675}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25853}, {"moveId": "MOONBLAST", "uses": 25576}, {"moveId": "PETAL_BLIZZARD", "uses": 17835}, {"moveId": "SOLAR_BEAM", "uses": 7064}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "MOONBLAST"], "score": 36.5}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 604, "matchups": [{"opponent": "gyarados", "rating": 834, "opRating": 165}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 746, "opRating": 253}, {"opponent": "sylveon", "rating": 630, "opRating": 369}, {"opponent": "yveltal", "rating": 598, "opRating": 401}, {"opponent": "lugia", "rating": 573, "opRating": 426}], "counters": [{"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "giratina_origin", "rating": 199}, {"opponent": "dialga", "rating": 263}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "mewtwo", "rating": 411}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 48972}, {"moveId": "CHARGE_BEAM", "uses": 27528}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 29932}, {"moveId": "ZAP_CANNON", "uses": 19264}, {"moveId": "FLASH_CANNON", "uses": 15565}, {"moveId": "HYPER_BEAM", "uses": 11764}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 36.4}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 503, "matchups": [{"opponent": "swampert", "rating": 949, "opRating": 50}, {"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "tapu_fini", "rating": 824, "opRating": 175}, {"opponent": "terrakion", "rating": 654, "opRating": 345}, {"opponent": "kyogre", "rating": 589, "opRating": 410}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "dialga", "rating": 187}, {"opponent": "metagross", "rating": 220}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "garcho<PERSON>", "rating": 340}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42741}, {"moveId": "ASTONISH", "uses": 33759}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34127}, {"moveId": "ENERGY_BALL", "uses": 21335}, {"moveId": "SHADOW_SNEAK", "uses": 21080}]}, "moveset": ["RAZOR_LEAF", "BRAVE_BIRD", "ENERGY_BALL"], "score": 36.2}, {"speciesId": "hypno_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 470, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 761, "opRating": 238}, {"opponent": "sneasler", "rating": 744, "opRating": 255}, {"opponent": "machamp_shadow", "rating": 660, "opRating": 339}, {"opponent": "landorus_incarnate", "rating": 609, "opRating": 390}, {"opponent": "zap<PERSON>_galarian", "rating": 550, "opRating": 449}], "counters": [{"opponent": "metagross", "rating": 136}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "dialga", "rating": 239}, {"opponent": "giratina_origin", "rating": 254}, {"opponent": "garcho<PERSON>", "rating": 368}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66997}, {"moveId": "ZEN_HEADBUTT", "uses": 9503}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14860}, {"moveId": "PSYSHOCK", "uses": 11934}, {"moveId": "SHADOW_BALL", "uses": 11547}, {"moveId": "FIRE_PUNCH", "uses": 11226}, {"moveId": "THUNDER_PUNCH", "uses": 11031}, {"moveId": "FOCUS_BLAST", "uses": 7316}, {"moveId": "PSYCHIC", "uses": 4605}, {"moveId": "FUTURE_SIGHT", "uses": 3952}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 36.2}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 520, "matchups": [{"opponent": "genesect_douse", "rating": 795, "opRating": 204}, {"opponent": "genesect_shock", "rating": 795, "opRating": 204}, {"opponent": "genesect_chill", "rating": 795, "opRating": 204}, {"opponent": "genesect_burn", "rating": 795, "opRating": 204}, {"opponent": "metagross", "rating": 608, "opRating": 391}], "counters": [{"opponent": "giratina_origin", "rating": 149}, {"opponent": "gyarados", "rating": 170}, {"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 355}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 27791}, {"moveId": "EMBER", "uses": 27703}, {"moveId": "FEINT_ATTACK", "uses": 21011}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28171}, {"moveId": "PSYSHOCK", "uses": 12270}, {"moveId": "OVERHEAT", "uses": 11271}, {"moveId": "SOLAR_BEAM", "uses": 6623}, {"moveId": "RETURN", "uses": 6570}, {"moveId": "FLAMETHROWER", "uses": 6142}, {"moveId": "FIRE_BLAST", "uses": 3306}, {"moveId": "HEAT_WAVE", "uses": 2000}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 36.2}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 599, "matchups": [{"opponent": "yveltal", "rating": 847, "opRating": 152}, {"opponent": "ho_oh", "rating": 753, "opRating": 246}, {"opponent": "gyarados", "rating": 676, "opRating": 323}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 573, "opRating": 426}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "zacian_hero", "rating": 213}, {"opponent": "dialga", "rating": 361}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 63257}, {"moveId": "LOW_KICK", "uses": 13243}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 45145}, {"moveId": "FLAME_CHARGE", "uses": 21794}, {"moveId": "DISCHARGE", "uses": 9662}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 36.2}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 465, "matchups": [{"opponent": "gyarados", "rating": 700, "opRating": 299}, {"opponent": "moltres_shadow", "rating": 700, "opRating": 299}, {"opponent": "charizard_shadow", "rating": 700, "opRating": 299}, {"opponent": "gyarado<PERSON>_shadow", "rating": 626, "opRating": 373}, {"opponent": "magnezone", "rating": 584, "opRating": 415}], "counters": [{"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "dialga", "rating": 201}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "mewtwo", "rating": 302}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33356}, {"moveId": "SPARK", "uses": 24389}, {"moveId": "TACKLE", "uses": 18760}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 26921}, {"moveId": "DISCHARGE", "uses": 23894}, {"moveId": "RETURN", "uses": 11023}, {"moveId": "THUNDERBOLT", "uses": 10163}, {"moveId": "HYPER_BEAM", "uses": 4338}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 36.1}, {"speciesId": "aerodactyl_shadow", "speciesName": "Aerodactyl (Shadow)", "rating": 525, "matchups": [{"opponent": "moltres_shadow", "rating": 883, "opRating": 116}, {"opponent": "moltres", "rating": 869, "opRating": 130}, {"opponent": "ho_oh", "rating": 845, "opRating": 154}, {"opponent": "pinsir_shadow", "rating": 840, "opRating": 159}, {"opponent": "ho_oh_shadow", "rating": 813, "opRating": 186}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "metagross", "rating": 177}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "giratina_origin", "rating": 235}, {"opponent": "mewtwo", "rating": 236}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36439}, {"moveId": "BITE", "uses": 20412}, {"moveId": "STEEL_WING", "uses": 19672}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29254}, {"moveId": "EARTH_POWER", "uses": 19282}, {"moveId": "IRON_HEAD", "uses": 10733}, {"moveId": "ANCIENT_POWER", "uses": 10481}, {"moveId": "HYPER_BEAM", "uses": 6619}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 36}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 524, "matchups": [{"opponent": "garcho<PERSON>", "rating": 890}, {"opponent": "registeel", "rating": 876, "opRating": 123}, {"opponent": "snor<PERSON>_shadow", "rating": 834, "opRating": 165}, {"opponent": "zarude", "rating": 750, "opRating": 250}, {"opponent": "yveltal", "rating": 744, "opRating": 255}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dragonite", "rating": 162}, {"opponent": "gyarados", "rating": 329}, {"opponent": "metagross", "rating": 331}, {"opponent": "dialga", "rating": 486}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31903}, {"moveId": "GUST", "uses": 22771}, {"moveId": "WING_ATTACK", "uses": 21775}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 39297}, {"moveId": "CLOSE_COMBAT", "uses": 32705}, {"moveId": "HEAT_WAVE", "uses": 4414}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 36}, {"speciesId": "hoopa", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 518, "matchups": [{"opponent": "heracross", "rating": 926, "opRating": 73}, {"opponent": "machamp", "rating": 920, "opRating": 79}, {"opponent": "machamp_shadow", "rating": 910, "opRating": 89}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 907, "opRating": 92}, {"opponent": "zacian_hero", "rating": 589, "opRating": 410}], "counters": [{"opponent": "metagross", "rating": 125}, {"opponent": "mewtwo", "rating": 174}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "dialga", "rating": 220}, {"opponent": "garcho<PERSON>", "rating": 241}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 52600}, {"moveId": "ASTONISH", "uses": 23900}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 43481}, {"moveId": "PSYCHIC", "uses": 27344}, {"moveId": "PSYBEAM", "uses": 5670}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 35.8}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 621, "matchups": [{"opponent": "swampert", "rating": 802, "opRating": 197}, {"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 688, "opRating": 311}, {"opponent": "grou<PERSON>", "rating": 627, "opRating": 372}, {"opponent": "sylveon", "rating": 531, "opRating": 468}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "dialga", "rating": 160}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "metagross", "rating": 255}, {"opponent": "garcho<PERSON>", "rating": 361}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54063}, {"moveId": "RAZOR_LEAF", "uses": 22437}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 45469}, {"moveId": "EARTHQUAKE", "uses": 19573}, {"moveId": "PETAL_BLIZZARD", "uses": 6230}, {"moveId": "SOLAR_BEAM", "uses": 5138}, {"moveId": "FRUSTRATION", "uses": 28}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 35.8}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 529, "matchups": [{"opponent": "metagross", "rating": 778, "opRating": 221}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 674, "opRating": 325}, {"opponent": "sylveon", "rating": 593, "opRating": 406}, {"opponent": "genesect_chill", "rating": 593, "opRating": 406}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "giratina_origin", "rating": 207}, {"opponent": "gyarados", "rating": 216}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31917}, {"moveId": "FIRE_SPIN", "uses": 20185}, {"moveId": "EMBER", "uses": 19675}, {"moveId": "LOW_KICK", "uses": 4765}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32920}, {"moveId": "FLAME_CHARGE", "uses": 27468}, {"moveId": "FIRE_BLAST", "uses": 12373}, {"moveId": "HEAT_WAVE", "uses": 3688}]}, "moveset": ["INCINERATE", "DRILL_RUN", "FLAME_CHARGE"], "score": 35.8}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 601, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 848, "opRating": 151}, {"opponent": "mewtwo", "rating": 703}, {"opponent": "metagross", "rating": 674}, {"opponent": "gyarados", "rating": 579}, {"opponent": "swampert", "rating": 554, "opRating": 445}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "dragonite", "rating": 356}, {"opponent": "lugia", "rating": 359}, {"opponent": "giratina_origin", "rating": 468}, {"opponent": "garcho<PERSON>", "rating": 469}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 58831}, {"moveId": "PECK", "uses": 17669}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 26974}, {"moveId": "DARK_PULSE", "uses": 20532}, {"moveId": "SKY_ATTACK", "uses": 20113}, {"moveId": "PSYCHIC", "uses": 8753}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 35.7}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 522, "matchups": [{"opponent": "genesect_douse", "rating": 897, "opRating": 102}, {"opponent": "genesect_shock", "rating": 897, "opRating": 102}, {"opponent": "metagross", "rating": 788, "opRating": 211}, {"opponent": "sylveon", "rating": 630, "opRating": 369}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 597, "opRating": 402}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "gyarados", "rating": 157}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "dialga", "rating": 326}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 60548}, {"moveId": "TAKE_DOWN", "uses": 15952}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25090}, {"moveId": "DARK_PULSE", "uses": 21831}, {"moveId": "OVERHEAT", "uses": 19310}, {"moveId": "SOLAR_BEAM", "uses": 10305}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "DARK_PULSE"], "score": 35.7}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 509, "matchups": [{"opponent": "golem", "rating": 905, "opRating": 94}, {"opponent": "moltres", "rating": 797, "opRating": 202}, {"opponent": "moltres_shadow", "rating": 752, "opRating": 247}, {"opponent": "swampert", "rating": 727, "opRating": 272}, {"opponent": "swampert_shadow", "rating": 691, "opRating": 308}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "giratina_origin", "rating": 185}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "mewtwo", "rating": 263}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36220}, {"moveId": "INFESTATION", "uses": 28985}, {"moveId": "ACID", "uses": 11289}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 29603}, {"moveId": "GRASS_KNOT", "uses": 25419}, {"moveId": "BULLDOZE", "uses": 12147}, {"moveId": "RETURN", "uses": 9384}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 35.5}, {"speciesId": "<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 571, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 822, "opRating": 177}, {"opponent": "mamos<PERSON>_shadow", "rating": 822, "opRating": 177}, {"opponent": "moltres", "rating": 787, "opRating": 212}, {"opponent": "heatran", "rating": 787, "opRating": 212}, {"opponent": "excadrill", "rating": 517, "opRating": 482}], "counters": [{"opponent": "gyarados", "rating": 136}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "dialga", "rating": 288}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39901}, {"moveId": "CONFUSION", "uses": 36599}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 22971}, {"moveId": "ICE_BEAM", "uses": 18260}, {"moveId": "SYNCHRONOISE", "uses": 11949}, {"moveId": "HYDRO_PUMP", "uses": 10214}, {"moveId": "BUBBLE_BEAM", "uses": 7545}, {"moveId": "PSYCHIC", "uses": 5450}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 35.5}, {"speciesId": "sudowoodo_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 525, "matchups": [{"opponent": "registeel", "rating": 904, "opRating": 95}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "moltres", "rating": 792, "opRating": 207}, {"opponent": "entei", "rating": 777, "opRating": 222}, {"opponent": "mamos<PERSON>_shadow", "rating": 716, "opRating": 283}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "giratina_origin", "rating": 201}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "dialga", "rating": 320}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44956}, {"moveId": "ROCK_THROW", "uses": 31544}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34376}, {"moveId": "EARTHQUAKE", "uses": 20983}, {"moveId": "STONE_EDGE", "uses": 13664}, {"moveId": "ROCK_TOMB", "uses": 7452}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 35.5}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 540, "matchups": [{"opponent": "magnezone_shadow", "rating": 904, "opRating": 95}, {"opponent": "landorus_incarnate", "rating": 744, "opRating": 255}, {"opponent": "dragonite", "rating": 676, "opRating": 323}, {"opponent": "dragonite_shadow", "rating": 623, "opRating": 376}, {"opponent": "excadrill", "rating": 604, "opRating": 395}], "counters": [{"opponent": "giratina_origin", "rating": 121}, {"opponent": "lugia", "rating": 223}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "dialga", "rating": 410}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41981}, {"moveId": "WATER_GUN", "uses": 34519}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 41465}, {"moveId": "BLIZZARD", "uses": 22842}, {"moveId": "WATER_PULSE", "uses": 12196}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "BLIZZARD"], "score": 35.3}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 383, "matchups": [{"opponent": "pangoro", "rating": 890, "opRating": 109}, {"opponent": "weavile_shadow", "rating": 804, "opRating": 195}, {"opponent": "kommo_o", "rating": 634, "opRating": 365}, {"opponent": "hydreigon", "rating": 631, "opRating": 368}, {"opponent": "giratina_origin", "rating": 609, "opRating": 390}], "counters": [{"opponent": "metagross", "rating": 177}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "dialga", "rating": 217}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "gyarados", "rating": 250}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 64591}, {"moveId": "POUND", "uses": 11909}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38733}, {"moveId": "THUNDERBOLT", "uses": 21569}, {"moveId": "HYPER_BEAM", "uses": 16162}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 35.1}, {"speciesId": "sudowoodo", "speciesName": "Sudowoodo", "rating": 515, "matchups": [{"opponent": "porygon_z", "rating": 920, "opRating": 79}, {"opponent": "moltres", "rating": 818, "opRating": 181}, {"opponent": "moltres_shadow", "rating": 792, "opRating": 207}, {"opponent": "dialga", "rating": 550}, {"opponent": "ho_oh", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "giratina_origin", "rating": 155}, {"opponent": "lugia", "rating": 214}, {"opponent": "garcho<PERSON>", "rating": 218}, {"opponent": "zacian_hero", "rating": 234}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44252}, {"moveId": "ROCK_THROW", "uses": 32248}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30330}, {"moveId": "EARTHQUAKE", "uses": 18329}, {"moveId": "STONE_EDGE", "uses": 12085}, {"moveId": "RETURN", "uses": 9235}, {"moveId": "ROCK_TOMB", "uses": 6527}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 35}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 606, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 832, "opRating": 167}, {"opponent": "metagross", "rating": 826, "opRating": 173}, {"opponent": "sylveon", "rating": 585, "opRating": 414}, {"opponent": "zacian_hero", "rating": 573, "opRating": 426}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570, "opRating": 429}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "gyarados", "rating": 126}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "dialga", "rating": 440}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48333}, {"moveId": "SCRATCH", "uses": 17829}, {"moveId": "ZEN_HEADBUTT", "uses": 10417}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 30367}, {"moveId": "PSYCHIC", "uses": 26640}, {"moveId": "FLAMETHROWER", "uses": 12825}, {"moveId": "FIRE_BLAST", "uses": 6827}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "PSYCHIC"], "score": 34.8}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 488, "matchups": [{"opponent": "metagross_shadow", "rating": 720, "opRating": 279}, {"opponent": "gallade", "rating": 695, "opRating": 304}, {"opponent": "metagross", "rating": 683, "opRating": 316}, {"opponent": "genesect", "rating": 575, "opRating": 425}, {"opponent": "zacian_hero", "rating": 533, "opRating": 466}], "counters": [{"opponent": "giratina_origin", "rating": 129}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "gyarados", "rating": 314}, {"opponent": "dialga", "rating": 323}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55387}, {"moveId": "ASTONISH", "uses": 21113}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 25254}, {"moveId": "DARK_PULSE", "uses": 16500}, {"moveId": "OMINOUS_WIND", "uses": 12990}, {"moveId": "PSYCHIC", "uses": 12569}, {"moveId": "RETURN", "uses": 9112}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 34.8}, {"speciesId": "hypno", "speciesName": "Hypno", "rating": 451, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 800, "opRating": 199}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "machamp", "rating": 648, "opRating": 351}, {"opponent": "heracross", "rating": 648, "opRating": 351}, {"opponent": "terrakion", "rating": 592, "opRating": 407}], "counters": [{"opponent": "mewtwo", "rating": 187}, {"opponent": "giratina_origin", "rating": 213}, {"opponent": "dialga", "rating": 220}, {"opponent": "gyarados", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 354}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66888}, {"moveId": "ZEN_HEADBUTT", "uses": 9612}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14023}, {"moveId": "PSYSHOCK", "uses": 11242}, {"moveId": "SHADOW_BALL", "uses": 10881}, {"moveId": "FIRE_PUNCH", "uses": 10627}, {"moveId": "THUNDER_PUNCH", "uses": 10368}, {"moveId": "FOCUS_BLAST", "uses": 6921}, {"moveId": "RETURN", "uses": 4404}, {"moveId": "PSYCHIC", "uses": 4387}, {"moveId": "FUTURE_SIGHT", "uses": 3793}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 34.8}, {"speciesId": "falinks", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 575, "matchups": [{"opponent": "darkrai", "rating": 902, "opRating": 97}, {"opponent": "ma<PERSON><PERSON>", "rating": 791, "opRating": 208}, {"opponent": "snor<PERSON>_shadow", "rating": 755, "opRating": 244}, {"opponent": "dialga", "rating": 590}, {"opponent": "snorlax", "rating": 510, "opRating": 489}], "counters": [{"opponent": "giratina_origin", "rating": 127}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "gyarados", "rating": 157}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "garcho<PERSON>", "rating": 326}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 62538}, {"moveId": "ROCK_SMASH", "uses": 13962}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34639}, {"moveId": "MEGAHORN", "uses": 20942}, {"moveId": "BRICK_BREAK", "uses": 20830}]}, "moveset": ["COUNTER", "SUPER_POWER", "MEGAHORN"], "score": 34.6}, {"speciesId": "tornadus_incarnate", "speciesName": "Tornadus (Incarnate)", "rating": 504, "matchups": [{"opponent": "heracross", "rating": 802, "opRating": 197}, {"opponent": "buzzwole", "rating": 767, "opRating": 232}, {"opponent": "virizion", "rating": 761, "opRating": 238}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 632, "opRating": 367}, {"opponent": "grou<PERSON>", "rating": 567, "opRating": 432}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "metagross", "rating": 194}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "zacian_hero", "rating": 234}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47764}, {"moveId": "BITE", "uses": 28736}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25205}, {"moveId": "GRASS_KNOT", "uses": 21792}, {"moveId": "HURRICANE", "uses": 20322}, {"moveId": "HYPER_BEAM", "uses": 9202}]}, "moveset": ["AIR_SLASH", "DARK_PULSE", "GRASS_KNOT"], "score": 34.6}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 480, "matchups": [{"opponent": "magnezone", "rating": 950, "opRating": 49}, {"opponent": "magnezone_shadow", "rating": 944, "opRating": 55}, {"opponent": "xurkitree", "rating": 927, "opRating": 72}, {"opponent": "nihilego", "rating": 857, "opRating": 142}, {"opponent": "raikou_shadow", "rating": 854, "opRating": 145}], "counters": [{"opponent": "giratina_origin", "rating": 217}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "dialga", "rating": 418}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38359}, {"moveId": "ROCK_THROW", "uses": 38141}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 22408}, {"moveId": "STONE_EDGE", "uses": 20721}, {"moveId": "ROCK_BLAST", "uses": 18707}, {"moveId": "ANCIENT_POWER", "uses": 14607}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "EARTHQUAKE", "STONE_EDGE"], "score": 34.4}, {"speciesId": "jellicent", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 506, "matchups": [{"opponent": "metagross_shadow", "rating": 823, "opRating": 176}, {"opponent": "escavalier", "rating": 681, "opRating": 318}, {"opponent": "landorus_incarnate", "rating": 674, "opRating": 325}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 592, "opRating": 407}, {"opponent": "genesect", "rating": 577, "opRating": 422}], "counters": [{"opponent": "gyarados", "rating": 118}, {"opponent": "giratina_origin", "rating": 127}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 288}, {"opponent": "garcho<PERSON>", "rating": 420}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 41461}, {"moveId": "BUBBLE", "uses": 35039}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37809}, {"moveId": "ICE_BEAM", "uses": 25800}, {"moveId": "BUBBLE_BEAM", "uses": 12932}]}, "moveset": ["HEX", "SHADOW_BALL", "ICE_BEAM"], "score": 34.4}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 590, "matchups": [{"opponent": "articuno_galarian", "rating": 853, "opRating": 146}, {"opponent": "mewtwo_shadow", "rating": 764, "opRating": 235}, {"opponent": "metagross", "rating": 710, "opRating": 289}, {"opponent": "gyarados", "rating": 694, "opRating": 305}, {"opponent": "ho_oh", "rating": 662, "opRating": 337}], "counters": [{"opponent": "zacian_hero", "rating": 127}, {"opponent": "mewtwo", "rating": 161}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "dialga", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 347}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 38584}, {"moveId": "CHARGE_BEAM", "uses": 20504}, {"moveId": "THUNDER_FANG", "uses": 17430}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 33042}, {"moveId": "PSYCHIC_FANGS", "uses": 17211}, {"moveId": "OVERHEAT", "uses": 10830}, {"moveId": "RETURN", "uses": 6614}, {"moveId": "THUNDER", "uses": 5174}, {"moveId": "FLAME_BURST", "uses": 3610}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 34.4}, {"speciesId": "wailord", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 516, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 860, "opRating": 139}, {"opponent": "mamos<PERSON>_shadow", "rating": 840, "opRating": 159}, {"opponent": "moltres", "rating": 820, "opRating": 179}, {"opponent": "moltres_shadow", "rating": 814, "opRating": 185}, {"opponent": "excadrill", "rating": 598, "opRating": 401}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "gyarados", "rating": 152}, {"opponent": "mewtwo", "rating": 182}, {"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 295}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66853}, {"moveId": "ZEN_HEADBUTT", "uses": 9648}], "chargedMoves": [{"moveId": "SURF", "uses": 46202}, {"moveId": "BLIZZARD", "uses": 20065}, {"moveId": "HYPER_BEAM", "uses": 10202}]}, "moveset": ["WATER_GUN", "SURF", "BLIZZARD"], "score": 34.4}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 521, "matchups": [{"opponent": "victini", "rating": 911, "opRating": 88}, {"opponent": "lucario", "rating": 792, "opRating": 207}, {"opponent": "machamp_shadow", "rating": 746, "opRating": 253}, {"opponent": "metagross", "rating": 725, "opRating": 274}, {"opponent": "zacian_hero", "rating": 524, "opRating": 475}], "counters": [{"opponent": "gyarados", "rating": 126}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "dialga", "rating": 339}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43307}, {"moveId": "SUCKER_PUNCH", "uses": 33193}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32451}, {"moveId": "DARK_PULSE", "uses": 21039}, {"moveId": "DAZZLING_GLEAM", "uses": 11576}, {"moveId": "RETURN", "uses": 11311}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 34.3}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 491, "matchups": [{"opponent": "tapu_fini", "rating": 786, "opRating": 213}, {"opponent": "swampert", "rating": 734, "opRating": 265}, {"opponent": "swampert_shadow", "rating": 698, "opRating": 301}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "grou<PERSON>", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "dialga", "rating": 190}, {"opponent": "dragonite", "rating": 194}, {"opponent": "giratina_origin", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 319}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51797}, {"moveId": "BITE", "uses": 24703}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37987}, {"moveId": "GRASS_KNOT", "uses": 31726}, {"moveId": "SOLAR_BEAM", "uses": 6736}]}, "moveset": ["VINE_WHIP", "CRUNCH", "GRASS_KNOT"], "score": 34.3}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 517, "matchups": [{"opponent": "articuno_galarian", "rating": 786, "opRating": 213}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 698, "opRating": 301}, {"opponent": "sneasler", "rating": 658, "opRating": 341}, {"opponent": "machamp", "rating": 658, "opRating": 341}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 560, "opRating": 439}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "giratina_origin", "rating": 133}, {"opponent": "gyarados", "rating": 229}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "mewtwo", "rating": 315}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42752}, {"moveId": "EXTRASENSORY", "uses": 33748}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 35878}, {"moveId": "THUNDER", "uses": 28079}, {"moveId": "SWIFT", "uses": 12531}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "THUNDER"], "score": 34.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 453, "matchups": [{"opponent": "blaziken", "rating": 850, "opRating": 149}, {"opponent": "machamp", "rating": 795, "opRating": 204}, {"opponent": "machamp_shadow", "rating": 777, "opRating": 222}, {"opponent": "sneasler", "rating": 762, "opRating": 237}, {"opponent": "zap<PERSON>_galarian", "rating": 719, "opRating": 280}], "counters": [{"opponent": "metagross", "rating": 142}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 192}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 255}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43161}, {"moveId": "EXTRASENSORY", "uses": 33339}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 39685}, {"moveId": "FIRE_BLAST", "uses": 22578}, {"moveId": "SWIFT", "uses": 14269}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "FIRE_BLAST"], "score": 34.1}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 637, "matchups": [{"opponent": "genesect_shock", "rating": 926, "opRating": 73}, {"opponent": "zacian_hero", "rating": 774, "opRating": 225}, {"opponent": "metagross", "rating": 767, "opRating": 232}, {"opponent": "sylveon", "rating": 679, "opRating": 320}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 644, "opRating": 355}], "counters": [{"opponent": "giratina_origin", "rating": 115}, {"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "gyarados", "rating": 159}, {"opponent": "mewtwo", "rating": 179}, {"opponent": "dialga", "rating": 247}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34956}, {"moveId": "HEX", "uses": 23799}, {"moveId": "FIRE_SPIN", "uses": 17781}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26519}, {"moveId": "FLAME_CHARGE", "uses": 20815}, {"moveId": "OVERHEAT", "uses": 16107}, {"moveId": "ENERGY_BALL", "uses": 13060}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 34.1}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 352, "matchups": [{"opponent": "gengar", "rating": 727, "opRating": 272}, {"opponent": "cofagrigus", "rating": 727, "opRating": 272}, {"opponent": "gourgeist_super", "rating": 632, "opRating": 367}, {"opponent": "giratina_origin", "rating": 601, "opRating": 398}, {"opponent": "trevenant", "rating": 579, "opRating": 420}], "counters": [{"opponent": "zacian_hero", "rating": 147}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dialga", "rating": 288}, {"opponent": "mewtwo", "rating": 315}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 47301}, {"moveId": "ASTONISH", "uses": 29199}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28024}, {"moveId": "DISARMING_VOICE", "uses": 24146}, {"moveId": "RETURN", "uses": 14676}, {"moveId": "FIRE_BLAST", "uses": 9753}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 34.1}, {"speciesId": "mantine", "speciesName": "<PERSON><PERSON>", "rating": 448, "matchups": [{"opponent": "heracross", "rating": 812, "opRating": 187}, {"opponent": "gliscor_shadow", "rating": 798, "opRating": 201}, {"opponent": "buzzwole", "rating": 610, "opRating": 389}, {"opponent": "landorus_incarnate", "rating": 593, "opRating": 406}, {"opponent": "grou<PERSON>", "rating": 590, "opRating": 409}], "counters": [{"opponent": "giratina_origin", "rating": 183}, {"opponent": "dialga", "rating": 211}, {"opponent": "metagross", "rating": 250}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "mewtwo", "rating": 276}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 26884}, {"moveId": "BUBBLE", "uses": 26162}, {"moveId": "BULLET_SEED", "uses": 23475}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26866}, {"moveId": "AERIAL_ACE", "uses": 24172}, {"moveId": "BUBBLE_BEAM", "uses": 13000}, {"moveId": "WATER_PULSE", "uses": 12440}]}, "moveset": ["WING_ATTACK", "ICE_BEAM", "AERIAL_ACE"], "score": 34}, {"speciesId": "octillery", "speciesName": "Octillery", "rating": 382, "matchups": [{"opponent": "gliscor_shadow", "rating": 814, "opRating": 185}, {"opponent": "chandelure", "rating": 783, "opRating": 216}, {"opponent": "flygon_shadow", "rating": 682, "opRating": 317}, {"opponent": "salamence_shadow", "rating": 579, "opRating": 420}, {"opponent": "mamos<PERSON>_shadow", "rating": 527, "opRating": 472}], "counters": [{"opponent": "mewtwo", "rating": 164}, {"opponent": "zacian_hero", "rating": 199}, {"opponent": "gyarados", "rating": 208}, {"opponent": "dialga", "rating": 209}, {"opponent": "giratina_origin", "rating": 229}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 27008}, {"moveId": "WATER_GUN", "uses": 24917}, {"moveId": "MUD_SHOT", "uses": 24565}], "chargedMoves": [{"moveId": "OCTAZOOKA", "uses": 26802}, {"moveId": "AURORA_BEAM", "uses": 21827}, {"moveId": "GUNK_SHOT", "uses": 13787}, {"moveId": "WATER_PULSE", "uses": 8014}, {"moveId": "ACID_SPRAY", "uses": 5952}]}, "moveset": ["LOCK_ON", "OCTAZOOKA", "AURORA_BEAM"], "score": 33.8}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 495, "matchups": [{"opponent": "tapu_koko", "rating": 842, "opRating": 157}, {"opponent": "nihilego", "rating": 811, "opRating": 188}, {"opponent": "raikou_shadow", "rating": 811, "opRating": 188}, {"opponent": "melmetal", "rating": 760, "opRating": 239}, {"opponent": "excadrill", "rating": 608, "opRating": 391}], "counters": [{"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "gyarados", "rating": 260}, {"opponent": "dialga", "rating": 434}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43196}, {"moveId": "WATER_GUN", "uses": 33304}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 26774}, {"moveId": "STONE_EDGE", "uses": 21723}, {"moveId": "SLUDGE_BOMB", "uses": 14704}, {"moveId": "RETURN", "uses": 9966}, {"moveId": "ACID_SPRAY", "uses": 3320}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 33.8}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 505, "matchups": [{"opponent": "machamp_shadow", "rating": 805, "opRating": 194}, {"opponent": "sneasler", "rating": 787, "opRating": 212}, {"opponent": "sylveon", "rating": 543, "opRating": 456}, {"opponent": "zacian_hero", "rating": 531, "opRating": 468}, {"opponent": "grou<PERSON>", "rating": 505, "opRating": 494}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "dialga", "rating": 176}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "gyarados", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 330}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42833}, {"moveId": "EXTRASENSORY", "uses": 33667}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 33687}, {"moveId": "BLIZZARD", "uses": 30331}, {"moveId": "SWIFT", "uses": 12395}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "BLIZZARD"], "score": 33.7}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 496, "matchups": [{"opponent": "empoleon", "rating": 830, "opRating": 169}, {"opponent": "gyarados", "rating": 748, "opRating": 251}, {"opponent": "charizard", "rating": 748, "opRating": 251}, {"opponent": "walrein_shadow", "rating": 720, "opRating": 279}, {"opponent": "gyarado<PERSON>_shadow", "rating": 665, "opRating": 334}], "counters": [{"opponent": "garcho<PERSON>", "rating": 103}, {"opponent": "giratina_origin", "rating": 151}, {"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 289}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 55470}, {"moveId": "ASTONISH", "uses": 21030}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34968}, {"moveId": "OMINOUS_WIND", "uses": 26253}, {"moveId": "THUNDER", "uses": 15165}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 33.7}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 497, "matchups": [{"opponent": "swampert_shadow", "rating": 909, "opRating": 90}, {"opponent": "swampert", "rating": 895, "opRating": 104}, {"opponent": "terrakion", "rating": 813, "opRating": 186}, {"opponent": "kyogre", "rating": 630, "opRating": 369}, {"opponent": "sylveon", "rating": 549, "opRating": 450}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "metagross", "rating": 264}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "garcho<PERSON>", "rating": 307}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43471}, {"moveId": "ACID", "uses": 33029}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 43202}, {"moveId": "SLUDGE_BOMB", "uses": 17470}, {"moveId": "LEAF_TORNADO", "uses": 8223}, {"moveId": "ACID_SPRAY", "uses": 3901}, {"moveId": "SOLAR_BEAM", "uses": 3617}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 33.7}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 478, "matchups": [{"opponent": "swampert", "rating": 923, "opRating": 76}, {"opponent": "swampert_shadow", "rating": 911, "opRating": 88}, {"opponent": "vaporeon", "rating": 807, "opRating": 192}, {"opponent": "xurkitree", "rating": 673, "opRating": 326}, {"opponent": "kyogre", "rating": 539, "opRating": 460}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "metagross", "rating": 220}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "garcho<PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43379}, {"moveId": "ACID", "uses": 33121}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 21931}, {"moveId": "SLUDGE_BOMB", "uses": 21802}, {"moveId": "PETAL_BLIZZARD", "uses": 15470}, {"moveId": "RETURN", "uses": 10997}, {"moveId": "SOLAR_BEAM", "uses": 6381}]}, "moveset": ["RAZOR_LEAF", "MOONBLAST", "SLUDGE_BOMB"], "score": 33.7}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 442, "matchups": [{"opponent": "pinsir_shadow", "rating": 700, "opRating": 299}, {"opponent": "barbara<PERSON>", "rating": 648, "opRating": 351}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 619, "opRating": 380}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 552, "opRating": 447}, {"opponent": "zarude", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dialga", "rating": 195}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "metagross", "rating": 235}, {"opponent": "garcho<PERSON>", "rating": 251}, {"opponent": "gyarados", "rating": 265}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 63165}, {"moveId": "TAKE_DOWN", "uses": 13335}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 38572}, {"moveId": "SEED_BOMB", "uses": 20676}, {"moveId": "GUNK_SHOT", "uses": 12004}, {"moveId": "ACID_SPRAY", "uses": 5240}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 33.6}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 543, "matchups": [{"opponent": "ho_oh", "rating": 703, "opRating": 296}, {"opponent": "kyogre", "rating": 582, "opRating": 417}, {"opponent": "swampert", "rating": 541, "opRating": 458}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 527, "opRating": 472}, {"opponent": "yveltal", "rating": 517, "opRating": 482}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 173}, {"opponent": "dragonite", "rating": 178}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "dialga", "rating": 260}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 32311}, {"moveId": "QUICK_ATTACK", "uses": 26805}, {"moveId": "MUD_SLAP", "uses": 17373}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27646}, {"moveId": "GRASS_KNOT", "uses": 24981}, {"moveId": "BULLDOZE", "uses": 16515}, {"moveId": "PARABOLIC_CHARGE", "uses": 7255}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "GRASS_KNOT"], "score": 33.6}, {"speciesId": "hunt<PERSON>", "speciesName": "Huntail", "rating": 510, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 848, "opRating": 151}, {"opponent": "landorus_incarnate", "rating": 818, "opRating": 181}, {"opponent": "ma<PERSON><PERSON>", "rating": 788, "opRating": 211}, {"opponent": "moltres", "rating": 788, "opRating": 211}, {"opponent": "excadrill", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "garcho<PERSON>", "rating": 295}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 49353}, {"moveId": "BITE", "uses": 27147}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 33339}, {"moveId": "CRUNCH", "uses": 24017}, {"moveId": "ICE_BEAM", "uses": 19103}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "CRUNCH"], "score": 33.6}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 363, "matchups": [{"opponent": "mismagius_shadow", "rating": 920, "opRating": 79}, {"opponent": "gengar", "rating": 881, "opRating": 118}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 765, "opRating": 234}, {"opponent": "trevenant", "rating": 673, "opRating": 326}, {"opponent": "giratina_origin", "rating": 601, "opRating": 398}], "counters": [{"opponent": "zacian_hero", "rating": 150}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 214}, {"opponent": "gyarados", "rating": 260}, {"opponent": "mewtwo", "rating": 276}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 45530}, {"moveId": "ASTONISH", "uses": 30970}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34323}, {"moveId": "DISARMING_VOICE", "uses": 30056}, {"moveId": "FIRE_BLAST", "uses": 11975}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 33.4}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 584, "matchups": [{"opponent": "metagross", "rating": 761, "opRating": 238}, {"opponent": "genesect_douse", "rating": 744, "opRating": 255}, {"opponent": "sylveon", "rating": 573, "opRating": 426}, {"opponent": "dialga", "rating": 543}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "gyarados", "rating": 126}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 187}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39044}, {"moveId": "EMBER", "uses": 37456}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24760}, {"moveId": "FLAMETHROWER", "uses": 17074}, {"moveId": "OVERHEAT", "uses": 15875}, {"moveId": "LAST_RESORT", "uses": 11590}, {"moveId": "FIRE_BLAST", "uses": 4520}, {"moveId": "HEAT_WAVE", "uses": 2734}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 33.4}, {"speciesId": "lycanroc_midday", "speciesName": "Lycanroc (Midday)", "rating": 513, "matchups": [{"opponent": "heatran", "rating": 826, "opRating": 173}, {"opponent": "ho_oh", "rating": 814, "opRating": 185}, {"opponent": "moltres", "rating": 777, "opRating": 222}, {"opponent": "rai<PERSON>u", "rating": 704, "opRating": 295}, {"opponent": "raikou_shadow", "rating": 649, "opRating": 350}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "giratina_origin", "rating": 296}, {"opponent": "dialga", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40163}, {"moveId": "ROCK_THROW", "uses": 36337}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27471}, {"moveId": "STONE_EDGE", "uses": 26229}, {"moveId": "CRUNCH", "uses": 22758}]}, "moveset": ["SUCKER_PUNCH", "DRILL_RUN", "STONE_EDGE"], "score": 33.4}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 483, "matchups": [{"opponent": "gengar", "rating": 784, "opRating": 215}, {"opponent": "trevenant", "rating": 770, "opRating": 229}, {"opponent": "golisopod", "rating": 741, "opRating": 258}, {"opponent": "heracross", "rating": 648, "opRating": 351}, {"opponent": "giratina_origin", "rating": 627, "opRating": 372}], "counters": [{"opponent": "zacian_hero", "rating": 164}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 230}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47290}, {"moveId": "STEEL_WING", "uses": 29210}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 49780}, {"moveId": "HYPER_BEAM", "uses": 17444}, {"moveId": "HEAT_WAVE", "uses": 9297}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 33.4}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 597, "matchups": [{"opponent": "tapu_bulu", "rating": 922, "opRating": 77}, {"opponent": "heracross", "rating": 875, "opRating": 125}, {"opponent": "blaziken", "rating": 875, "opRating": 125}, {"opponent": "virizion", "rating": 858, "opRating": 141}, {"opponent": "buzzwole", "rating": 594, "opRating": 405}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "metagross", "rating": 197}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "lugia", "rating": 223}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 40811}, {"moveId": "BUG_BITE", "uses": 35689}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26368}, {"moveId": "ANCIENT_POWER", "uses": 25648}, {"moveId": "AERIAL_ACE", "uses": 24431}]}, "moveset": ["WING_ATTACK", "BUG_BUZZ", "ANCIENT_POWER"], "score": 33.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "sneasler", "rating": 767, "opRating": 232}, {"opponent": "electivire", "rating": 726, "opRating": 273}, {"opponent": "magmortar_shadow", "rating": 684, "opRating": 315}, {"opponent": "terrakion", "rating": 585, "opRating": 414}, {"opponent": "zap<PERSON>_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dialga", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 293}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43546}, {"moveId": "CHARM", "uses": 32954}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 37382}, {"moveId": "PSYCHIC", "uses": 27302}, {"moveId": "FUTURE_SIGHT", "uses": 11793}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 33.1}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 559, "matchups": [{"opponent": "genesect_shock", "rating": 927, "opRating": 72}, {"opponent": "genesect_chill", "rating": 927, "opRating": 72}, {"opponent": "genesect_douse", "rating": 825, "opRating": 174}, {"opponent": "metagross", "rating": 786, "opRating": 213}, {"opponent": "dialga", "rating": 572}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "gyarados", "rating": 126}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 305}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 59033}, {"moveId": "ROCK_SMASH", "uses": 17467}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 33667}, {"moveId": "BLAST_BURN", "uses": 29732}, {"moveId": "FLAMETHROWER", "uses": 6852}, {"moveId": "SOLAR_BEAM", "uses": 6250}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 33.1}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 448, "matchups": [{"opponent": "rhyperior", "rating": 787, "opRating": 212}, {"opponent": "swampert", "rating": 715, "opRating": 284}, {"opponent": "landorus_incarnate", "rating": 677, "opRating": 322}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 654, "opRating": 345}, {"opponent": "swampert_shadow", "rating": 636, "opRating": 363}], "counters": [{"opponent": "lugia", "rating": 219}, {"opponent": "dialga", "rating": 225}, {"opponent": "gyarados", "rating": 265}, {"opponent": "metagross", "rating": 305}, {"opponent": "mewtwo", "rating": 317}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 48157}, {"moveId": "RAZOR_LEAF", "uses": 28343}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 21131}, {"moveId": "ENERGY_BALL", "uses": 16145}, {"moveId": "LEAF_STORM", "uses": 14059}, {"moveId": "HYDRO_PUMP", "uses": 13101}, {"moveId": "BLIZZARD", "uses": 7304}, {"moveId": "SOLAR_BEAM", "uses": 4631}]}, "moveset": ["BUBBLE", "ICE_BEAM", "ENERGY_BALL"], "score": 33.1}, {"speciesId": "pheromosa", "speciesName": "Pheromosa", "rating": 469, "matchups": [{"opponent": "registeel", "rating": 860, "opRating": 139}, {"opponent": "garcho<PERSON>", "rating": 813}, {"opponent": "zarude", "rating": 768, "opRating": 231}, {"opponent": "yveltal", "rating": 569, "opRating": 430}, {"opponent": "metagross", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "gyarados", "rating": 180}, {"opponent": "giratina_origin", "rating": 245}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "mewtwo", "rating": 367}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 60214}, {"moveId": "LOW_KICK", "uses": 16286}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 37071}, {"moveId": "LUNGE", "uses": 20642}, {"moveId": "BUG_BUZZ", "uses": 13410}, {"moveId": "FOCUS_BLAST", "uses": 5420}]}, "moveset": ["BUG_BITE", "CLOSE_COMBAT", "LUNGE"], "score": 33}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 429, "matchups": [{"opponent": "relicanth", "rating": 861, "opRating": 138}, {"opponent": "tapu_fini", "rating": 622, "opRating": 377}, {"opponent": "rhyperior", "rating": 590, "opRating": 409}, {"opponent": "swampert", "rating": 572, "opRating": 427}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 554, "opRating": 445}], "counters": [{"opponent": "giratina_origin", "rating": 157}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "dialga", "rating": 236}, {"opponent": "lugia", "rating": 238}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52410}, {"moveId": "ASTONISH", "uses": 24090}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 24236}, {"moveId": "GRASS_KNOT", "uses": 23831}, {"moveId": "SLUDGE_BOMB", "uses": 19279}, {"moveId": "RETURN", "uses": 9141}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 32.9}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 510, "matchups": [{"opponent": "thundurus_therian", "rating": 813, "opRating": 186}, {"opponent": "moltres", "rating": 752, "opRating": 247}, {"opponent": "moltres_shadow", "rating": 708, "opRating": 291}, {"opponent": "swampert", "rating": 691, "opRating": 308}, {"opponent": "swampert_shadow", "rating": 619, "opRating": 380}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "metagross", "rating": 197}, {"opponent": "giratina_origin", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "mewtwo", "rating": 263}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36502}, {"moveId": "INFESTATION", "uses": 29461}, {"moveId": "ACID", "uses": 10581}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 33818}, {"moveId": "GRASS_KNOT", "uses": 28903}, {"moveId": "BULLDOZE", "uses": 13790}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 32.7}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 354, "matchups": [{"opponent": "absol", "rating": 804, "opRating": 195}, {"opponent": "absol_shadow", "rating": 801, "opRating": 198}, {"opponent": "staraptor_shadow", "rating": 704, "opRating": 295}, {"opponent": "lickilicky", "rating": 542, "opRating": 457}, {"opponent": "shiftry_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "giratina_origin", "rating": 189}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 255}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47560}, {"moveId": "ASTONISH", "uses": 28940}], "chargedMoves": [{"moveId": "RETURN", "uses": 23831}, {"moveId": "AERIAL_ACE", "uses": 22873}, {"moveId": "LOW_SWEEP", "uses": 20460}, {"moveId": "HYPER_BEAM", "uses": 9198}]}, "moveset": ["SCRATCH", "RETURN", "AERIAL_ACE"], "score": 32.6}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 508, "matchups": [{"opponent": "swampert_shadow", "rating": 948, "opRating": 51}, {"opponent": "tapu_fini", "rating": 838, "opRating": 161}, {"opponent": "swampert", "rating": 801, "opRating": 198}, {"opponent": "kyogre", "rating": 557, "opRating": 442}, {"opponent": "excadrill", "rating": 503, "opRating": 496}], "counters": [{"opponent": "giratina_origin", "rating": 113}, {"opponent": "metagross", "rating": 206}, {"opponent": "dialga", "rating": 222}, {"opponent": "garcho<PERSON>", "rating": 326}, {"opponent": "mewtwo", "rating": 351}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38639}, {"moveId": "RAZOR_LEAF", "uses": 22651}, {"moveId": "ACID", "uses": 15223}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 50305}, {"moveId": "DAZZLING_GLEAM", "uses": 10890}, {"moveId": "RETURN", "uses": 10297}, {"moveId": "PETAL_BLIZZARD", "uses": 5283}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 32.6}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 513, "matchups": [{"opponent": "regirock", "rating": 693, "opRating": 306}, {"opponent": "swampert", "rating": 676, "opRating": 323}, {"opponent": "kyogre", "rating": 647, "opRating": 352}, {"opponent": "excadrill", "rating": 644, "opRating": 355}, {"opponent": "metagross", "rating": 521, "opRating": 478}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "gyarados", "rating": 157}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "dialga", "rating": 394}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44218}, {"moveId": "BULLET_SEED", "uses": 32282}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25553}, {"moveId": "GRASS_KNOT", "uses": 19205}, {"moveId": "SEED_BOMB", "uses": 17643}, {"moveId": "SLUDGE_BOMB", "uses": 14205}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 32.6}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 556, "matchups": [{"opponent": "cobalion", "rating": 744, "opRating": 255}, {"opponent": "regirock", "rating": 735, "opRating": 264}, {"opponent": "melmetal", "rating": 718, "opRating": 281}, {"opponent": "yveltal", "rating": 627, "opRating": 372}, {"opponent": "snorlax", "rating": 599, "opRating": 400}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "gyarados", "rating": 157}, {"opponent": "lugia", "rating": 178}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "dialga", "rating": 355}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42192}, {"moveId": "POISON_JAB", "uses": 34308}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 28886}, {"moveId": "SLUDGE_BOMB", "uses": 24635}, {"moveId": "MUD_BOMB", "uses": 23009}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "SLUDGE_BOMB"], "score": 32.6}, {"speciesId": "manectric_shadow", "speciesName": "Man<PERSON><PERSON> (Shadow)", "rating": 608, "matchups": [{"opponent": "mewtwo", "rating": 764}, {"opponent": "ho_oh", "rating": 748, "opRating": 251}, {"opponent": "metagross", "rating": 707, "opRating": 292}, {"opponent": "gyarados", "rating": 652, "opRating": 347}, {"opponent": "lugia", "rating": 566, "opRating": 433}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "zacian_hero", "rating": 158}, {"opponent": "dragonite", "rating": 191}, {"opponent": "giratina_origin", "rating": 211}, {"opponent": "garcho<PERSON>", "rating": 417}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 39480}, {"moveId": "CHARGE_BEAM", "uses": 20398}, {"moveId": "THUNDER_FANG", "uses": 16588}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35730}, {"moveId": "PSYCHIC_FANGS", "uses": 19226}, {"moveId": "OVERHEAT", "uses": 12003}, {"moveId": "THUNDER", "uses": 5717}, {"moveId": "FLAME_BURST", "uses": 3980}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 32.4}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 512, "matchups": [{"opponent": "genesect_chill", "rating": 857, "opRating": 142}, {"opponent": "genesect_burn", "rating": 857, "opRating": 142}, {"opponent": "metagross", "rating": 773, "opRating": 226}, {"opponent": "sylveon", "rating": 571, "opRating": 428}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 532, "opRating": 467}], "counters": [{"opponent": "mewtwo", "rating": 135}, {"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "gyarados", "rating": 180}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "dialga", "rating": 271}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 35998}, {"moveId": "FIRE_SPIN", "uses": 21191}, {"moveId": "PECK", "uses": 10025}, {"moveId": "STEEL_WING", "uses": 9327}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37712}, {"moveId": "FLAME_CHARGE", "uses": 21953}, {"moveId": "FIRE_BLAST", "uses": 9996}, {"moveId": "HURRICANE", "uses": 6863}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "FLAME_CHARGE"], "score": 32.3}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 540, "matchups": [{"opponent": "victini", "rating": 919, "opRating": 80}, {"opponent": "escavalier", "rating": 869, "opRating": 130}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 697, "opRating": 302}, {"opponent": "metagross", "rating": 686, "opRating": 313}, {"opponent": "giratina_origin", "rating": 570, "opRating": 429}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "gyarados", "rating": 146}, {"opponent": "excadrill", "rating": 148}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43859}, {"moveId": "SUCKER_PUNCH", "uses": 32641}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37783}, {"moveId": "DARK_PULSE", "uses": 24661}, {"moveId": "DAZZLING_GLEAM", "uses": 13882}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 32.2}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 456, "matchups": [{"opponent": "suicune_shadow", "rating": 849, "opRating": 150}, {"opponent": "tapu_fini", "rating": 812, "opRating": 187}, {"opponent": "swampert", "rating": 736, "opRating": 263}, {"opponent": "swampert_shadow", "rating": 699, "opRating": 300}, {"opponent": "kyogre", "rating": 628, "opRating": 371}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "lugia", "rating": 183}, {"opponent": "dialga", "rating": 184}, {"opponent": "giratina_origin", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 50694}, {"moveId": "BITE", "uses": 25806}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36252}, {"moveId": "POWER_WHIP", "uses": 29638}, {"moveId": "ENERGY_BALL", "uses": 10665}]}, "moveset": ["VINE_WHIP", "CRUNCH", "POWER_WHIP"], "score": 32}, {"speciesId": "hitmontop", "speciesName": "Hitmontop", "rating": 537, "matchups": [{"opponent": "darkrai", "rating": 870, "opRating": 129}, {"opponent": "ma<PERSON><PERSON>", "rating": 771, "opRating": 228}, {"opponent": "snor<PERSON>_shadow", "rating": 720, "opRating": 279}, {"opponent": "excadrill", "rating": 559, "opRating": 440}, {"opponent": "dialga", "rating": 535}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "lugia", "rating": 214}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "gyarados", "rating": 255}, {"opponent": "garcho<PERSON>", "rating": 338}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 63355}, {"moveId": "ROCK_SMASH", "uses": 13145}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 46268}, {"moveId": "STONE_EDGE", "uses": 22109}, {"moveId": "GYRO_BALL", "uses": 8122}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "STONE_EDGE"], "score": 32}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 503, "matchups": [{"opponent": "metagross", "rating": 780, "opRating": 219}, {"opponent": "nihilego", "rating": 780, "opRating": 219}, {"opponent": "dialga", "rating": 528}, {"opponent": "zekrom", "rating": 528, "opRating": 471}, {"opponent": "excadrill", "rating": 525, "opRating": 474}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "lugia", "rating": 192}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "zacian_hero", "rating": 291}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 44590}, {"moveId": "WATER_GUN", "uses": 31910}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 30421}, {"moveId": "STONE_EDGE", "uses": 24938}, {"moveId": "SLUDGE_BOMB", "uses": 17149}, {"moveId": "ACID_SPRAY", "uses": 3861}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 32}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 448, "matchups": [{"opponent": "swampert", "rating": 711, "opRating": 288}, {"opponent": "virizion", "rating": 708, "opRating": 291}, {"opponent": "swampert_shadow", "rating": 665, "opRating": 334}, {"opponent": "heracross", "rating": 633, "opRating": 366}, {"opponent": "buzzwole", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "metagross", "rating": 154}, {"opponent": "giratina_origin", "rating": 161}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 41704}, {"moveId": "RAZOR_LEAF", "uses": 34796}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 45795}, {"moveId": "AERIAL_ACE", "uses": 16485}, {"moveId": "STOMP", "uses": 14209}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 32}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "swampert_shadow", "rating": 895, "opRating": 104}, {"opponent": "swampert", "rating": 892, "opRating": 107}, {"opponent": "sylveon", "rating": 578, "opRating": 421}, {"opponent": "zacian_hero", "rating": 505, "opRating": 494}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "metagross", "rating": 220}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 44133}, {"moveId": "ACID", "uses": 32367}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 39424}, {"moveId": "SLUDGE_BOMB", "uses": 15212}, {"moveId": "LEAF_TORNADO", "uses": 7599}, {"moveId": "RETURN", "uses": 7450}, {"moveId": "ACID_SPRAY", "uses": 3460}, {"moveId": "SOLAR_BEAM", "uses": 3313}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 32}, {"speciesId": "simipour", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 488, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 814, "opRating": 185}, {"opponent": "moltres", "rating": 801, "opRating": 198}, {"opponent": "entei", "rating": 789, "opRating": 210}, {"opponent": "mamos<PERSON>_shadow", "rating": 777, "opRating": 222}, {"opponent": "moltres_shadow", "rating": 753, "opRating": 246}], "counters": [{"opponent": "gyarados", "rating": 131}, {"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "giratina_origin", "rating": 175}, {"opponent": "garcho<PERSON>", "rating": 312}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 48600}, {"moveId": "BITE", "uses": 27900}], "chargedMoves": [{"moveId": "SURF", "uses": 39486}, {"moveId": "CRUNCH", "uses": 30676}, {"moveId": "HYDRO_PUMP", "uses": 6311}]}, "moveset": ["WATER_GUN", "SURF", "CRUNCH"], "score": 31.6}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 520, "matchups": [{"opponent": "florges", "rating": 737, "opRating": 262}, {"opponent": "sylveon", "rating": 719, "opRating": 280}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 688, "opRating": 311}, {"opponent": "buzzwole", "rating": 664, "opRating": 335}, {"opponent": "zacian_hero", "rating": 646, "opRating": 353}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 198}, {"opponent": "gyarados", "rating": 237}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31704}, {"moveId": "HEX", "uses": 30457}, {"moveId": "ACID", "uses": 14365}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32813}, {"moveId": "FUTURE_SIGHT", "uses": 24288}, {"moveId": "SLUDGE_WAVE", "uses": 19394}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 31.6}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 499, "matchups": [{"opponent": "sylveon", "rating": 679, "opRating": 320}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 676, "opRating": 323}, {"opponent": "ho_oh", "rating": 640, "opRating": 359}, {"opponent": "lugia", "rating": 595, "opRating": 404}, {"opponent": "gyarados", "rating": 584, "opRating": 415}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "dialga", "rating": 241}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "mewtwo", "rating": 320}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39776}, {"moveId": "ROCK_THROW", "uses": 36724}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30476}, {"moveId": "MAGNET_BOMB", "uses": 24114}, {"moveId": "THUNDERBOLT", "uses": 12937}, {"moveId": "RETURN", "uses": 9006}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 31.4}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 436, "matchups": [{"opponent": "xurkitree", "rating": 685, "opRating": 314}, {"opponent": "samu<PERSON>t", "rating": 651, "opRating": 348}, {"opponent": "rhyperior", "rating": 631, "opRating": 368}, {"opponent": "metagross_shadow", "rating": 590, "opRating": 409}, {"opponent": "electivire_shadow", "rating": 558, "opRating": 441}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "giratina_origin", "rating": 183}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 257}, {"opponent": "dialga", "rating": 269}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52217}, {"moveId": "ASTONISH", "uses": 24283}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 27411}, {"moveId": "GRASS_KNOT", "uses": 26753}, {"moveId": "SLUDGE_BOMB", "uses": 22287}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 31.3}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 520, "matchups": [{"opponent": "nidoqueen", "rating": 868, "opRating": 131}, {"opponent": "articuno_galarian", "rating": 809, "opRating": 190}, {"opponent": "nihilego", "rating": 687, "opRating": 312}, {"opponent": "roserade", "rating": 664, "opRating": 335}, {"opponent": "sylveon", "rating": 546, "opRating": 453}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "giratina_origin", "rating": 133}, {"opponent": "metagross", "rating": 142}, {"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "dialga", "rating": 247}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42980}, {"moveId": "FEINT_ATTACK", "uses": 33520}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 20984}, {"moveId": "PAYBACK", "uses": 17236}, {"moveId": "HEAVY_SLAM", "uses": 15112}, {"moveId": "BULLDOZE", "uses": 10084}, {"moveId": "PSYCHIC", "uses": 8141}, {"moveId": "FLASH_CANNON", "uses": 4836}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 31.3}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 376, "matchups": [{"opponent": "mismagius_shadow", "rating": 932, "opRating": 67}, {"opponent": "gengar", "rating": 896, "opRating": 103}, {"opponent": "trevenant", "rating": 704, "opRating": 295}, {"opponent": "giratina_origin", "rating": 554, "opRating": 445}, {"opponent": "victini", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "zacian_hero", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "gyarados", "rating": 224}, {"opponent": "dialga", "rating": 247}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39111}, {"moveId": "BITE", "uses": 26517}, {"moveId": "TAKE_DOWN", "uses": 10882}], "chargedMoves": [{"moveId": "SURF", "uses": 30824}, {"moveId": "DARK_PULSE", "uses": 24878}, {"moveId": "GRASS_KNOT", "uses": 20838}]}, "moveset": ["SUCKER_PUNCH", "SURF", "DARK_PULSE"], "score": 31.3}, {"speciesId": "absol", "speciesName": "Absol", "rating": 451, "matchups": [{"opponent": "articuno_galarian", "rating": 916, "opRating": 83}, {"opponent": "gallade_shadow", "rating": 775, "opRating": 224}, {"opponent": "mewtwo", "rating": 681}, {"opponent": "mewtwo_shadow", "rating": 627, "opRating": 372}, {"opponent": "metagross", "rating": 560, "opRating": 439}], "counters": [{"opponent": "zacian_hero", "rating": 274}, {"opponent": "dialga", "rating": 296}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "lugia", "rating": 323}, {"opponent": "gyarados", "rating": 337}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41608}, {"moveId": "PSYCHO_CUT", "uses": 34892}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25230}, {"moveId": "MEGAHORN", "uses": 17503}, {"moveId": "THUNDER", "uses": 13135}, {"moveId": "PAYBACK", "uses": 11473}, {"moveId": "RETURN", "uses": 9300}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 31.2}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 596, "matchups": [{"opponent": "moltres_shadow", "rating": 813, "opRating": 186}, {"opponent": "florges", "rating": 750, "opRating": 250}, {"opponent": "sylveon", "rating": 741, "opRating": 258}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 709, "opRating": 290}, {"opponent": "zacian_hero", "rating": 680, "opRating": 319}], "counters": [{"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "giratina_origin", "rating": 129}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 182}, {"opponent": "metagross", "rating": 232}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56280}, {"moveId": "ACID", "uses": 20220}], "chargedMoves": [{"moveId": "SCALD", "uses": 31023}, {"moveId": "BLIZZARD", "uses": 17582}, {"moveId": "SLUDGE_WAVE", "uses": 15557}, {"moveId": "HYDRO_PUMP", "uses": 6927}, {"moveId": "ACID_SPRAY", "uses": 5309}]}, "moveset": ["POISON_JAB", "SCALD", "BLIZZARD"], "score": 31}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 402, "matchups": [{"opponent": "heracross", "rating": 723, "opRating": 276}, {"opponent": "trevenant", "rating": 671, "opRating": 328}, {"opponent": "bewear", "rating": 666, "opRating": 333}, {"opponent": "buzzwole", "rating": 661, "opRating": 338}, {"opponent": "excadrill", "rating": 616, "opRating": 383}], "counters": [{"opponent": "zacian_hero", "rating": 164}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 448}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 51831}, {"moveId": "STEEL_WING", "uses": 24669}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 28722}, {"moveId": "CLOSE_COMBAT", "uses": 26574}, {"moveId": "ROCK_SLIDE", "uses": 17802}, {"moveId": "HEAT_WAVE", "uses": 3403}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 30.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 540, "matchups": [{"opponent": "darkrai", "rating": 877, "opRating": 122}, {"opponent": "ma<PERSON><PERSON>", "rating": 751, "opRating": 248}, {"opponent": "snor<PERSON>_shadow", "rating": 732, "opRating": 267}, {"opponent": "zarude", "rating": 728, "opRating": 271}, {"opponent": "dialga", "rating": 555}], "counters": [{"opponent": "mewtwo", "rating": 93}, {"opponent": "giratina_origin", "rating": 123}, {"opponent": "lugia", "rating": 228}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 363}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 57032}, {"moveId": "ROCK_SMASH", "uses": 11611}, {"moveId": "LOW_KICK", "uses": 7861}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28855}, {"moveId": "STONE_EDGE", "uses": 15001}, {"moveId": "BRICK_BREAK", "uses": 12763}, {"moveId": "STOMP", "uses": 10195}, {"moveId": "RETURN", "uses": 6017}, {"moveId": "LOW_SWEEP", "uses": 3744}]}, "moveset": ["DOUBLE_KICK", "CLOSE_COMBAT", "STONE_EDGE"], "score": 30.9}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 529, "matchups": [{"opponent": "magnezone", "rating": 803, "opRating": 196}, {"opponent": "magnezone_shadow", "rating": 742, "opRating": 257}, {"opponent": "sylveon", "rating": 736, "opRating": 263}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 647, "opRating": 352}, {"opponent": "zacian_hero", "rating": 552, "opRating": 447}], "counters": [{"opponent": "giratina_origin", "rating": 105}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 413}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 25902}, {"moveId": "DOUBLE_KICK", "uses": 25248}, {"moveId": "FURY_CUTTER", "uses": 21418}, {"moveId": "IRON_TAIL", "uses": 3934}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 20307}, {"moveId": "MEGAHORN", "uses": 16721}, {"moveId": "SLUDGE_WAVE", "uses": 13215}, {"moveId": "EARTHQUAKE", "uses": 8793}, {"moveId": "RETURN", "uses": 8776}, {"moveId": "SAND_TOMB", "uses": 8637}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 30.9}, {"speciesId": "pelipper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 503, "matchups": [{"opponent": "rhyperior", "rating": 838, "opRating": 161}, {"opponent": "blaziken", "rating": 742, "opRating": 257}, {"opponent": "grou<PERSON>", "rating": 637, "opRating": 362}, {"opponent": "swampert", "rating": 545, "opRating": 454}, {"opponent": "excadrill", "rating": 510, "opRating": 489}], "counters": [{"opponent": "giratina_origin", "rating": 105}, {"opponent": "dialga", "rating": 116}, {"opponent": "gyarados", "rating": 203}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 208}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 39032}, {"moveId": "WATER_GUN", "uses": 37468}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 40553}, {"moveId": "HURRICANE", "uses": 16154}, {"moveId": "BLIZZARD", "uses": 14580}, {"moveId": "HYDRO_PUMP", "uses": 5375}]}, "moveset": ["WING_ATTACK", "WEATHER_BALL_WATER", "HURRICANE"], "score": 30.9}, {"speciesId": "clawitzer", "speciesName": "Clawitzer", "rating": 478, "matchups": [{"opponent": "golem", "rating": 882, "opRating": 117}, {"opponent": "hippo<PERSON><PERSON>", "rating": 674, "opRating": 325}, {"opponent": "regirock", "rating": 639, "opRating": 360}, {"opponent": "ma<PERSON><PERSON>", "rating": 610, "opRating": 389}, {"opponent": "entei", "rating": 610, "opRating": 389}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "gyarados", "rating": 146}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "dialga", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 192}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41697}, {"moveId": "SMACK_DOWN", "uses": 34803}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 29817}, {"moveId": "ICE_BEAM", "uses": 21618}, {"moveId": "DARK_PULSE", "uses": 20824}, {"moveId": "WATER_PULSE", "uses": 4381}]}, "moveset": ["WATER_GUN", "CRABHAMMER", "ICE_BEAM"], "score": 30.7}, {"speciesId": "slowking_shadow", "speciesName": "Slowking (Shadow)", "rating": 451, "matchups": [{"opponent": "sneasler", "rating": 798, "opRating": 201}, {"opponent": "blaziken", "rating": 780, "opRating": 219}, {"opponent": "machamp_shadow", "rating": 654, "opRating": 345}, {"opponent": "ma<PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "sylveon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 176}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 245}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40036}, {"moveId": "WATER_GUN", "uses": 36464}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 33184}, {"moveId": "BLIZZARD", "uses": 26051}, {"moveId": "FIRE_BLAST", "uses": 17151}, {"moveId": "FRUSTRATION", "uses": 8}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 30.7}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 482, "matchups": [{"opponent": "tapu_fini", "rating": 842, "opRating": 157}, {"opponent": "kyogre", "rating": 738, "opRating": 261}, {"opponent": "swampert", "rating": 704, "opRating": 295}, {"opponent": "excadrill", "rating": 567, "opRating": 432}, {"opponent": "grou<PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "dialga", "rating": 146}, {"opponent": "giratina_origin", "rating": 171}, {"opponent": "metagross", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 333}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44713}, {"moveId": "INFESTATION", "uses": 31787}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31624}, {"moveId": "SLUDGE_BOMB", "uses": 22199}, {"moveId": "POWER_WHIP", "uses": 15839}, {"moveId": "SOLAR_BEAM", "uses": 6649}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 30.7}, {"speciesId": "comfey", "speciesName": "Comfey", "rating": 414, "matchups": [{"opponent": "barbara<PERSON>", "rating": 906, "opRating": 93}, {"opponent": "golem", "rating": 868, "opRating": 131}, {"opponent": "swampert", "rating": 631, "opRating": 368}, {"opponent": "swampert_shadow", "rating": 585, "opRating": 414}, {"opponent": "kommo_o", "rating": 546, "opRating": 453}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "metagross", "rating": 165}, {"opponent": "giratina_origin", "rating": 183}, {"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 260}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 8301}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5382}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4942}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4724}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4593}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4511}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4433}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4371}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4233}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4183}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4173}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4059}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3795}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3727}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3693}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3685}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3487}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 38044}, {"moveId": "DRAINING_KISS", "uses": 28555}, {"moveId": "PETAL_BLIZZARD", "uses": 9817}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "DRAINING_KISS"], "score": 30.5}, {"speciesId": "gur<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "porygon_z_shadow", "rating": 862, "opRating": 137}, {"opponent": "tapu_bulu", "rating": 859, "opRating": 140}, {"opponent": "moltres", "rating": 741, "opRating": 258}, {"opponent": "thundurus_incarnate", "rating": 691, "opRating": 308}, {"opponent": "gyarado<PERSON>_shadow", "rating": 592, "opRating": 407}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 187}, {"opponent": "gyarados", "rating": 206}, {"opponent": "metagross", "rating": 229}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56926}, {"moveId": "LOW_KICK", "uses": 19574}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 34044}, {"moveId": "STONE_EDGE", "uses": 32500}, {"moveId": "LOW_SWEEP", "uses": 10103}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 30.5}, {"speciesId": "slowbro_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 448, "matchups": [{"opponent": "sneasler", "rating": 798, "opRating": 201}, {"opponent": "blaziken", "rating": 780, "opRating": 219}, {"opponent": "machamp_shadow", "rating": 654, "opRating": 345}, {"opponent": "ma<PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "sylveon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 176}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 245}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40017}, {"moveId": "WATER_GUN", "uses": 36483}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31497}, {"moveId": "PSYCHIC", "uses": 28904}, {"moveId": "WATER_PULSE", "uses": 15911}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 30.5}, {"speciesId": "lunatone", "speciesName": "Lunatone", "rating": 443, "matchups": [{"opponent": "ho_oh", "rating": 803, "opRating": 196}, {"opponent": "entei_shadow", "rating": 766, "opRating": 233}, {"opponent": "sneasler", "rating": 760, "opRating": 239}, {"opponent": "zapdos", "rating": 545, "opRating": 454}, {"opponent": "zacian_hero", "rating": 521, "opRating": 478}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 323}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 39955}, {"moveId": "ROCK_THROW", "uses": 36545}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 36346}, {"moveId": "MOONBLAST", "uses": 20450}, {"moveId": "PSYCHIC", "uses": 19659}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "MOONBLAST"], "score": 30.3}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 465, "matchups": [{"opponent": "tapu_fini", "rating": 795, "opRating": 204}, {"opponent": "vaporeon", "rating": 795, "opRating": 204}, {"opponent": "swampert", "rating": 768, "opRating": 231}, {"opponent": "swampert_shadow", "rating": 704, "opRating": 295}, {"opponent": "ma<PERSON><PERSON>", "rating": 664, "opRating": 335}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "dialga", "rating": 152}, {"opponent": "giratina_origin", "rating": 161}, {"opponent": "metagross", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 272}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44256}, {"moveId": "INFESTATION", "uses": 32244}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 26994}, {"moveId": "SLUDGE_BOMB", "uses": 17769}, {"moveId": "POWER_WHIP", "uses": 13499}, {"moveId": "RETURN", "uses": 12434}, {"moveId": "SOLAR_BEAM", "uses": 5685}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 30.3}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 480, "matchups": [{"opponent": "gyarados", "rating": 750, "opRating": 250}, {"opponent": "swampert", "rating": 718, "opRating": 281}, {"opponent": "gyarado<PERSON>_shadow", "rating": 690, "opRating": 309}, {"opponent": "swampert_shadow", "rating": 647, "opRating": 352}, {"opponent": "kyogre", "rating": 630, "opRating": 369}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "giratina_origin", "rating": 133}, {"opponent": "dialga", "rating": 165}, {"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "zacian_hero", "rating": 248}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50367}, {"moveId": "TACKLE", "uses": 26133}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 49837}, {"moveId": "ENERGY_BALL", "uses": 20555}, {"moveId": "SWIFT", "uses": 6136}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ENERGY_BALL"], "score": 30.2}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 474, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 742, "opRating": 257}, {"opponent": "machamp", "rating": 676, "opRating": 323}, {"opponent": "blaziken", "rating": 661, "opRating": 338}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 640, "opRating": 359}, {"opponent": "heracross", "rating": 640, "opRating": 359}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "metagross", "rating": 194}, {"opponent": "lugia", "rating": 264}, {"opponent": "dialga", "rating": 277}, {"opponent": "zacian_hero", "rating": 306}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 7970}, {"moveId": "WATER_GUN", "uses": 6699}, {"moveId": "TACKLE", "uses": 4952}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4571}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4263}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3944}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3861}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3821}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3735}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3717}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3510}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3504}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3483}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3407}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3340}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3061}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3046}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3011}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2851}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 20419}, {"moveId": "PSYCHIC", "uses": 17235}, {"moveId": "THUNDER", "uses": 13364}, {"moveId": "HYDRO_PUMP", "uses": 12939}, {"moveId": "POWER_GEM", "uses": 8964}, {"moveId": "PSYBEAM", "uses": 3534}]}, "moveset": ["QUICK_ATTACK", "ICE_BEAM", "PSYCHIC"], "score": 30.2}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 431, "matchups": [{"opponent": "exeggutor_shadow", "rating": 939, "opRating": 60}, {"opponent": "celebi", "rating": 729, "opRating": 270}, {"opponent": "obstagoon", "rating": 671, "opRating": 328}, {"opponent": "zarude", "rating": 662, "opRating": 337}, {"opponent": "pinsir_shadow", "rating": 601, "opRating": 398}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "gyarados", "rating": 247}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 22468}, {"moveId": "BUG_BITE", "uses": 19352}, {"moveId": "POISON_STING", "uses": 17585}, {"moveId": "AIR_SLASH", "uses": 17119}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 29863}, {"moveId": "BUG_BUZZ", "uses": 19500}, {"moveId": "POWER_GEM", "uses": 14029}, {"moveId": "SIGNAL_BEAM", "uses": 8403}, {"moveId": "FELL_STINGER", "uses": 4907}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 30.2}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 525, "matchups": [{"opponent": "genesect_douse", "rating": 848, "opRating": 151}, {"opponent": "genesect_shock", "rating": 848, "opRating": 151}, {"opponent": "genesect_chill", "rating": 848, "opRating": 151}, {"opponent": "genesect_burn", "rating": 848, "opRating": 151}, {"opponent": "genesect", "rating": 848, "opRating": 151}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "dialga", "rating": 339}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38902}, {"moveId": "EMBER", "uses": 37598}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38244}, {"moveId": "RETURN", "uses": 16380}, {"moveId": "FLAMETHROWER", "uses": 14257}, {"moveId": "FIRE_BLAST", "uses": 7700}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "RETURN"], "score": 30}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 504, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 714, "opRating": 285}, {"opponent": "sylveon", "rating": 686, "opRating": 313}, {"opponent": "lugia", "rating": 591, "opRating": 408}, {"opponent": "ho_oh", "rating": 573, "opRating": 426}, {"opponent": "gyarados", "rating": 545, "opRating": 454}], "counters": [{"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "giratina_origin", "rating": 155}, {"opponent": "metagross", "rating": 165}, {"opponent": "dialga", "rating": 252}, {"opponent": "mewtwo", "rating": 260}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39489}, {"moveId": "ROCK_THROW", "uses": 37011}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34331}, {"moveId": "MAGNET_BOMB", "uses": 27635}, {"moveId": "THUNDERBOLT", "uses": 14426}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 30}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 468, "matchups": [{"opponent": "swampert", "rating": 848, "opRating": 151}, {"opponent": "garcho<PERSON>", "rating": 626}, {"opponent": "kyogre", "rating": 574, "opRating": 425}, {"opponent": "zacian_hero", "rating": 512, "opRating": 487}, {"opponent": "grou<PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "dialga", "rating": 133}, {"opponent": "metagross", "rating": 148}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "gyarados", "rating": 286}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6261}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5710}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 5635}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5058}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4971}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4899}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4704}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4592}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4523}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4465}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4396}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4347}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3981}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3937}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3838}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3648}, {"moveId": "ZEN_HEADBUTT", "uses": 1358}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41200}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12214}, {"moveId": "SOLAR_BEAM", "uses": 8438}]}, "moveset": ["HIDDEN_POWER_GRASS", "GRASS_KNOT", "ENERGY_BALL"], "score": 30}, {"speciesId": "slowbro", "speciesName": "Slowbro", "rating": 458, "matchups": [{"opponent": "sneasler", "rating": 788, "opRating": 211}, {"opponent": "blaziken", "rating": 703, "opRating": 296}, {"opponent": "mamos<PERSON>_shadow", "rating": 649, "opRating": 350}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 543, "opRating": 456}, {"opponent": "garcho<PERSON>", "rating": 507}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "lugia", "rating": 242}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38606}, {"moveId": "WATER_GUN", "uses": 37894}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26532}, {"moveId": "PSYCHIC", "uses": 23954}, {"moveId": "WATER_PULSE", "uses": 13233}, {"moveId": "RETURN", "uses": 12785}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 30}, {"speciesId": "solrock", "speciesName": "Solrock", "rating": 439, "matchups": [{"opponent": "ho_oh", "rating": 803, "opRating": 196}, {"opponent": "entei_shadow", "rating": 766, "opRating": 233}, {"opponent": "sneasler", "rating": 760, "opRating": 239}, {"opponent": "zapdos", "rating": 545, "opRating": 454}, {"opponent": "zacian_hero", "rating": 521, "opRating": 478}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 241}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 39991}, {"moveId": "ROCK_THROW", "uses": 36509}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 42226}, {"moveId": "PSYCHIC", "uses": 22905}, {"moveId": "SOLAR_BEAM", "uses": 11363}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 29.9}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 389, "matchups": [{"opponent": "arcanine_<PERSON><PERSON>an", "rating": 859, "opRating": 140}, {"opponent": "golem", "rating": 856, "opRating": 143}, {"opponent": "mamos<PERSON>_shadow", "rating": 794, "opRating": 205}, {"opponent": "magmortar_shadow", "rating": 727, "opRating": 272}, {"opponent": "moltres_shadow", "rating": 719, "opRating": 280}], "counters": [{"opponent": "giratina_origin", "rating": 127}, {"opponent": "dialga", "rating": 135}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 234}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 39076}, {"moveId": "WATER_GUN", "uses": 37424}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 30964}, {"moveId": "AQUA_JET", "uses": 29002}, {"moveId": "SWIFT", "uses": 16442}]}, "moveset": ["WATERFALL", "HYDRO_PUMP", "AQUA_JET"], "score": 29.6}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 461, "matchups": [{"opponent": "trevenant", "rating": 795, "opRating": 204}, {"opponent": "gengar", "rating": 735, "opRating": 264}, {"opponent": "heracross", "rating": 684, "opRating": 315}, {"opponent": "landorus_incarnate", "rating": 553, "opRating": 446}, {"opponent": "giratina_origin", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 173}, {"opponent": "gyarados", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 298}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 25054}, {"moveId": "WING_ATTACK", "uses": 22213}, {"moveId": "AIR_SLASH", "uses": 17701}, {"moveId": "STEEL_WING", "uses": 11376}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 42064}, {"moveId": "AERIAL_ACE", "uses": 16981}, {"moveId": "HURRICANE", "uses": 7650}, {"moveId": "FEATHER_DANCE", "uses": 5343}, {"moveId": "AIR_CUTTER", "uses": 4590}]}, "moveset": ["GUST", "BRAVE_BIRD", "AERIAL_ACE"], "score": 29.5}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 539, "matchups": [{"opponent": "florges", "rating": 811, "opRating": 188}, {"opponent": "sylveon", "rating": 765, "opRating": 234}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 742, "opRating": 257}, {"opponent": "zap<PERSON>_galarian", "rating": 706, "opRating": 293}, {"opponent": "zacian_hero", "rating": 677, "opRating": 322}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "metagross", "rating": 142}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 195}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 41263}, {"moveId": "CONFUSION", "uses": 35237}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 28880}, {"moveId": "PSYCHIC", "uses": 27227}, {"moveId": "FOCUS_BLAST", "uses": 20348}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PSYCHIC"], "score": 29.5}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 547, "matchups": [{"opponent": "registeel", "rating": 910, "opRating": 89}, {"opponent": "darkrai", "rating": 806, "opRating": 193}, {"opponent": "zarude", "rating": 769, "opRating": 230}, {"opponent": "tangrowth_shadow", "rating": 769, "opRating": 230}, {"opponent": "giratina_origin", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "dragonite", "rating": 148}, {"opponent": "gyarados", "rating": 288}, {"opponent": "zacian_hero", "rating": 306}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31017}, {"moveId": "GUST", "uses": 23480}, {"moveId": "WING_ATTACK", "uses": 21960}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33305}, {"moveId": "CLOSE_COMBAT", "uses": 28785}, {"moveId": "RETURN", "uses": 10487}, {"moveId": "HEAT_WAVE", "uses": 3916}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 29.5}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 420, "matchups": [{"opponent": "weavile", "rating": 720, "opRating": 279}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 672, "opRating": 327}, {"opponent": "darkrai", "rating": 603, "opRating": 396}, {"opponent": "zarude", "rating": 574, "opRating": 425}, {"opponent": "mewtwo_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "giratina_origin", "rating": 121}, {"opponent": "gyarados", "rating": 182}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "garcho<PERSON>", "rating": 319}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 39535}, {"moveId": "INFESTATION", "uses": 36965}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 39575}, {"moveId": "BUBBLE_BEAM", "uses": 22425}, {"moveId": "MIRROR_COAT", "uses": 14550}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 29.3}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 514, "matchups": [{"opponent": "swampert", "rating": 948, "opRating": 51}, {"opponent": "swampert_shadow", "rating": 923, "opRating": 76}, {"opponent": "kyogre", "rating": 692, "opRating": 307}, {"opponent": "excadrill", "rating": 646, "opRating": 353}, {"opponent": "grou<PERSON>", "rating": 588, "opRating": 411}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "giratina_origin", "rating": 125}, {"opponent": "mewtwo", "rating": 242}, {"opponent": "zacian_hero", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 39815}, {"moveId": "RAZOR_LEAF", "uses": 22077}, {"moveId": "ACID", "uses": 14603}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 57475}, {"moveId": "DAZZLING_GLEAM", "uses": 13109}, {"moveId": "PETAL_BLIZZARD", "uses": 5942}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 29.2}, {"speciesId": "mr_mime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 348, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 823, "opRating": 176}, {"opponent": "sneasler", "rating": 676, "opRating": 323}, {"opponent": "heracross", "rating": 646, "opRating": 353}, {"opponent": "kommo_o", "rating": 584, "opRating": 415}, {"opponent": "buzzwole", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "dialga", "rating": 361}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 63705}, {"moveId": "ZEN_HEADBUTT", "uses": 12795}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37780}, {"moveId": "PSYCHIC", "uses": 32144}, {"moveId": "PSYBEAM", "uses": 6433}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 29.2}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 566, "matchups": [{"opponent": "gyarados", "rating": 757, "opRating": 242}, {"opponent": "yveltal", "rating": 750, "opRating": 250}, {"opponent": "ho_oh", "rating": 679, "opRating": 320}, {"opponent": "lugia", "rating": 556, "opRating": 443}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 556, "opRating": 443}], "counters": [{"opponent": "garcho<PERSON>", "rating": 70}, {"opponent": "giratina_origin", "rating": 129}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "dialga", "rating": 154}, {"opponent": "dragonite", "rating": 162}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 29130}, {"moveId": "THUNDER_SHOCK", "uses": 25718}, {"moveId": "SPARK", "uses": 21545}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32415}, {"moveId": "THUNDER_PUNCH", "uses": 15861}, {"moveId": "PSYCHIC", "uses": 14309}, {"moveId": "GRASS_KNOT", "uses": 13874}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 29.2}, {"speciesId": "reuniclus", "speciesName": "Reuniclus", "rating": 376, "matchups": [{"opponent": "flygon", "rating": 639, "opRating": 360}, {"opponent": "flygon_shadow", "rating": 630, "opRating": 369}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 555, "opRating": 444}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 539, "opRating": 460}, {"opponent": "virizion", "rating": 532, "opRating": 467}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "gyarados", "rating": 164}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "zacian_hero", "rating": 193}, {"opponent": "dialga", "rating": 198}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5778}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5219}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 5179}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5109}, {"moveId": "HIDDEN_POWER_DARK", "uses": 5031}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4972}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4738}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4662}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4646}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4489}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4474}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4382}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4062}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4061}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3968}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3750}, {"moveId": "ZEN_HEADBUTT", "uses": 1782}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32329}, {"moveId": "FUTURE_SIGHT", "uses": 24817}, {"moveId": "THUNDER", "uses": 19306}]}, "moveset": ["HIDDEN_POWER_ICE", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 29.2}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 467, "matchups": [{"opponent": "genesect_chill", "rating": 782, "opRating": 217}, {"opponent": "genesect_burn", "rating": 782, "opRating": 217}, {"opponent": "sylveon", "rating": 737, "opRating": 262}, {"opponent": "metagross", "rating": 704, "opRating": 295}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 704, "opRating": 295}], "counters": [{"opponent": "giratina_origin", "rating": 91}, {"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "dialga", "rating": 144}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "gyarados", "rating": 244}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 42471}, {"moveId": "POISON_JAB", "uses": 34029}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 32832}, {"moveId": "FIRE_BLAST", "uses": 16160}, {"moveId": "DRAGON_PULSE", "uses": 14355}, {"moveId": "SLUDGE_WAVE", "uses": 13183}]}, "moveset": ["INCINERATE", "POISON_FANG", "FIRE_BLAST"], "score": 29.2}, {"speciesId": "musharna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 451, "matchups": [{"opponent": "gallade", "rating": 660, "opRating": 340}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "kommo_o", "rating": 602, "opRating": 397}, {"opponent": "machamp_shadow", "rating": 584, "opRating": 415}, {"opponent": "sneasler", "rating": 573, "opRating": 426}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "giratina_origin", "rating": 141}, {"opponent": "dialga", "rating": 144}, {"opponent": "lugia", "rating": 230}, {"opponent": "garcho<PERSON>", "rating": 288}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 51862}, {"moveId": "ZEN_HEADBUTT", "uses": 24638}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 42380}, {"moveId": "DAZZLING_GLEAM", "uses": 19893}, {"moveId": "FUTURE_SIGHT", "uses": 14237}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 29}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 503, "matchups": [{"opponent": "tapu_koko", "rating": 855, "opRating": 144}, {"opponent": "magnezone", "rating": 742, "opRating": 257}, {"opponent": "magnezone_shadow", "rating": 713, "opRating": 286}, {"opponent": "sylveon", "rating": 684, "opRating": 315}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 592, "opRating": 407}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "giratina_origin", "rating": 131}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 270}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 25985}, {"moveId": "DOUBLE_KICK", "uses": 25809}, {"moveId": "FURY_CUTTER", "uses": 21145}, {"moveId": "IRON_TAIL", "uses": 3505}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 22481}, {"moveId": "MEGAHORN", "uses": 19147}, {"moveId": "SLUDGE_WAVE", "uses": 15598}, {"moveId": "EARTHQUAKE", "uses": 9686}, {"moveId": "SAND_TOMB", "uses": 9430}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 29}, {"speciesId": "alomomola", "speciesName": "Alomomola", "rating": 378, "matchups": [{"opponent": "heatran", "rating": 646, "opRating": 353}, {"opponent": "entei_shadow", "rating": 613, "opRating": 386}, {"opponent": "moltres_shadow", "rating": 579, "opRating": 420}, {"opponent": "articuno_shadow", "rating": 555, "opRating": 444}, {"opponent": "mamos<PERSON>_shadow", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 141}, {"opponent": "gyarados", "rating": 170}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 215}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 7651}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5636}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5172}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4825}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4634}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4596}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4546}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4290}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4192}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4165}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4160}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4130}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3704}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3687}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3669}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3623}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3417}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 27056}, {"moveId": "HYDRO_PUMP", "uses": 24800}, {"moveId": "PSYCHIC", "uses": 24626}]}, "moveset": ["WATERFALL", "BLIZZARD", "HYDRO_PUMP"], "score": 28.9}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 444, "matchups": [{"opponent": "venusaur_shadow", "rating": 785, "opRating": 214}, {"opponent": "gengar", "rating": 750, "opRating": 250}, {"opponent": "trevenant", "rating": 746, "opRating": 253}, {"opponent": "giratina_origin", "rating": 637, "opRating": 362}, {"opponent": "swampert_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "zacian_hero", "rating": 109}, {"opponent": "gyarados", "rating": 157}, {"opponent": "dialga", "rating": 165}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 47980}, {"moveId": "STEEL_WING", "uses": 28520}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34427}, {"moveId": "DRILL_PECK", "uses": 31300}, {"moveId": "AERIAL_ACE", "uses": 6889}, {"moveId": "AIR_CUTTER", "uses": 3825}]}, "moveset": ["FEINT_ATTACK", "BRAVE_BIRD", "DRILL_PECK"], "score": 28.9}, {"speciesId": "slowking", "speciesName": "Slowking", "rating": 459, "matchups": [{"opponent": "sneasler", "rating": 788, "opRating": 211}, {"opponent": "blaziken", "rating": 703, "opRating": 296}, {"opponent": "mamos<PERSON>_shadow", "rating": 649, "opRating": 350}, {"opponent": "ma<PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "machamp_shadow", "rating": 603, "opRating": 396}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 408}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38615}, {"moveId": "WATER_GUN", "uses": 37885}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 26851}, {"moveId": "BLIZZARD", "uses": 21445}, {"moveId": "RETURN", "uses": 14137}, {"moveId": "FIRE_BLAST", "uses": 14101}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 28.9}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 414, "matchups": [{"opponent": "heracross", "rating": 886, "opRating": 113}, {"opponent": "virizion", "rating": 695, "opRating": 304}, {"opponent": "buzzwole", "rating": 618, "opRating": 381}, {"opponent": "gallade", "rating": 613, "opRating": 386}, {"opponent": "garcho<PERSON>", "rating": 544, "opRating": 455}], "counters": [{"opponent": "giratina_origin", "rating": 129}, {"opponent": "dialga", "rating": 165}, {"opponent": "gyarados", "rating": 188}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "zacian_hero", "rating": 205}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 64011}, {"moveId": "ZEN_HEADBUTT", "uses": 12489}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 38298}, {"moveId": "PSYCHIC", "uses": 17147}, {"moveId": "OMINOUS_WIND", "uses": 11680}, {"moveId": "DAZZLING_GLEAM", "uses": 9351}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "PSYCHIC"], "score": 28.6}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 497, "matchups": [{"opponent": "lucario", "rating": 795, "opRating": 204}, {"opponent": "metagross", "rating": 720, "opRating": 279}, {"opponent": "metagross_shadow", "rating": 675, "opRating": 325}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 670, "opRating": 329}, {"opponent": "gallade_shadow", "rating": 641, "opRating": 358}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "dragonite", "rating": 130}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 321}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55741}, {"moveId": "ASTONISH", "uses": 20759}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 28439}, {"moveId": "DARK_PULSE", "uses": 18923}, {"moveId": "OMINOUS_WIND", "uses": 14564}, {"moveId": "PSYCHIC", "uses": 14533}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 28.6}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 487, "matchups": [{"opponent": "genesect_douse", "rating": 764, "opRating": 235}, {"opponent": "genesect_shock", "rating": 764, "opRating": 235}, {"opponent": "metagross", "rating": 761, "opRating": 238}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 558, "opRating": 441}, {"opponent": "sylveon", "rating": 525, "opRating": 474}], "counters": [{"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 173}, {"opponent": "lugia", "rating": 202}, {"opponent": "zacian_hero", "rating": 265}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 41529}, {"moveId": "LICK", "uses": 34971}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 33507}, {"moveId": "THUNDER_PUNCH", "uses": 29707}, {"moveId": "POWER_UP_PUNCH", "uses": 13272}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "THUNDER_PUNCH"], "score": 28.5}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 441, "matchups": [{"opponent": "gengar", "rating": 800, "opRating": 199}, {"opponent": "roserade", "rating": 603, "opRating": 396}, {"opponent": "nidoqueen_shadow", "rating": 576, "opRating": 423}, {"opponent": "giratina_origin", "rating": 537, "opRating": 462}, {"opponent": "sneasler", "rating": 505, "opRating": 494}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "dialga", "rating": 141}, {"opponent": "lugia", "rating": 147}, {"opponent": "zacian_hero", "rating": 167}, {"opponent": "garcho<PERSON>", "rating": 171}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 38987}, {"moveId": "POUND", "uses": 37513}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 29324}, {"moveId": "HYPER_BEAM", "uses": 24670}, {"moveId": "DAZZLING_GLEAM", "uses": 22497}]}, "moveset": ["ZEN_HEADBUTT", "PSYCHIC", "HYPER_BEAM"], "score": 27.9}, {"speciesId": "lair<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 428, "matchups": [{"opponent": "porygon_z_shadow", "rating": 890, "opRating": 109}, {"opponent": "articuno_shadow", "rating": 658, "opRating": 341}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609, "opRating": 390}, {"opponent": "kyurem", "rating": 584, "opRating": 415}, {"opponent": "sylveon", "rating": 573, "opRating": 426}], "counters": [{"opponent": "giratina_origin", "rating": 153}, {"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "metagross", "rating": 218}, {"opponent": "dialga", "rating": 233}, {"opponent": "mewtwo", "rating": 255}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54756}, {"moveId": "IRON_TAIL", "uses": 21744}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28340}, {"moveId": "BODY_SLAM", "uses": 27334}, {"moveId": "HEAVY_SLAM", "uses": 14736}, {"moveId": "ROCK_TOMB", "uses": 6083}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 27.8}, {"speciesId": "chimecho", "speciesName": "Chi<PERSON><PERSON>", "rating": 409, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 777, "opRating": 222}, {"opponent": "blaziken", "rating": 594, "opRating": 405}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 579, "opRating": 420}, {"opponent": "machamp", "rating": 557, "opRating": 442}, {"opponent": "terrakion", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 85}, {"opponent": "giratina_origin", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "dialga", "rating": 271}, {"opponent": "gyarados", "rating": 275}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 52808}, {"moveId": "ASTONISH", "uses": 23692}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30432}, {"moveId": "SHADOW_BALL", "uses": 28767}, {"moveId": "ENERGY_BALL", "uses": 17330}]}, "moveset": ["EXTRASENSORY", "PSYSHOCK", "SHADOW_BALL"], "score": 27.6}, {"speciesId": "sigilyph", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 412, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 703, "opRating": 296}, {"opponent": "virizion", "rating": 668, "opRating": 331}, {"opponent": "gallade", "rating": 631, "opRating": 368}, {"opponent": "buzzwole", "rating": 606, "opRating": 393}, {"opponent": "gallade_shadow", "rating": 603, "opRating": 396}], "counters": [{"opponent": "giratina_origin", "rating": 129}, {"opponent": "dialga", "rating": 138}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 58158}, {"moveId": "ZEN_HEADBUTT", "uses": 18342}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 28862}, {"moveId": "SIGNAL_BEAM", "uses": 19480}, {"moveId": "AIR_CUTTER", "uses": 15117}, {"moveId": "PSYBEAM", "uses": 13061}]}, "moveset": ["AIR_SLASH", "ANCIENT_POWER", "SIGNAL_BEAM"], "score": 27.5}, {"speciesId": "gorebyss", "speciesName": "<PERSON><PERSON>", "rating": 362, "matchups": [{"opponent": "golem", "rating": 862, "opRating": 137}, {"opponent": "chandelure", "rating": 792, "opRating": 207}, {"opponent": "magmar_shadow", "rating": 792, "opRating": 207}, {"opponent": "flareon", "rating": 774, "opRating": 225}, {"opponent": "hippo<PERSON><PERSON>", "rating": 611, "opRating": 388}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 222}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40675}, {"moveId": "CONFUSION", "uses": 35825}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 32074}, {"moveId": "WATER_PULSE", "uses": 23934}, {"moveId": "DRAINING_KISS", "uses": 20476}]}, "moveset": ["WATER_GUN", "PSYCHIC", "WATER_PULSE"], "score": 27.4}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 410, "matchups": [{"opponent": "charizard", "rating": 746, "opRating": 253}, {"opponent": "moltres_shadow", "rating": 686, "opRating": 313}, {"opponent": "sylveon", "rating": 602, "opRating": 397}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 584, "opRating": 415}, {"opponent": "articuno_galarian", "rating": 584, "opRating": 415}], "counters": [{"opponent": "giratina_origin", "rating": 125}, {"opponent": "metagross", "rating": 162}, {"opponent": "dialga", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "mewtwo", "rating": 294}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54005}, {"moveId": "IRON_TAIL", "uses": 22495}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27026}, {"moveId": "BODY_SLAM", "uses": 25837}, {"moveId": "HEAVY_SLAM", "uses": 14066}, {"moveId": "ROCK_TOMB", "uses": 5904}, {"moveId": "RETURN", "uses": 3729}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 27.4}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 459, "matchups": [{"opponent": "metagross_shadow", "rating": 762, "opRating": 237}, {"opponent": "metagross", "rating": 740, "opRating": 259}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "genesect_chill", "rating": 582, "opRating": 417}, {"opponent": "genesect_burn", "rating": 582, "opRating": 417}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 154}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "giratina_origin", "rating": 241}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48824}, {"moveId": "BITE", "uses": 27676}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38709}, {"moveId": "FLAMETHROWER", "uses": 29805}, {"moveId": "FIRE_BLAST", "uses": 8063}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 27.1}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 398, "matchups": [{"opponent": "gardevoir_shadow", "rating": 634, "opRating": 365}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 630, "opRating": 369}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 560, "opRating": 439}, {"opponent": "espeon", "rating": 536, "opRating": 463}, {"opponent": "gallade_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "gyarados", "rating": 190}, {"opponent": "giratina_origin", "rating": 205}, {"opponent": "dialga", "rating": 233}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 33126}, {"moveId": "TACKLE", "uses": 26500}, {"moveId": "ACID", "uses": 16861}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 19305}, {"moveId": "DARK_PULSE", "uses": 17904}, {"moveId": "SLUDGE_BOMB", "uses": 17425}, {"moveId": "THUNDERBOLT", "uses": 13275}, {"moveId": "RETURN", "uses": 8580}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 27.1}, {"speciesId": "deoxys_speed", "speciesName": "<PERSON><PERSON><PERSON> (Speed)", "rating": 415, "matchups": [{"opponent": "poliwrath_shadow", "rating": 755, "opRating": 244}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 740, "opRating": 259}, {"opponent": "walrein_shadow", "rating": 602, "opRating": 397}, {"opponent": "blaziken", "rating": 574, "opRating": 425}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 555, "opRating": 444}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 122}, {"opponent": "giratina_origin", "rating": 129}, {"opponent": "dialga", "rating": 192}, {"opponent": "zacian_hero", "rating": 286}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 59389}, {"moveId": "ZEN_HEADBUTT", "uses": 17111}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 43901}, {"moveId": "THUNDERBOLT", "uses": 23701}, {"moveId": "SWIFT", "uses": 8868}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 26.8}, {"speciesId": "oricorio_sensu", "speciesName": "Oricorio (Sensu)", "rating": 400, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 868, "opRating": 131}, {"opponent": "zap<PERSON>_galarian", "rating": 859, "opRating": 140}, {"opponent": "virizion", "rating": 810, "opRating": 189}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 734, "opRating": 265}, {"opponent": "buzzwole", "rating": 704, "opRating": 295}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "giratina_origin", "rating": 129}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "gyarados", "rating": 167}, {"opponent": "garcho<PERSON>", "rating": 171}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66296}, {"moveId": "POUND", "uses": 10204}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35282}, {"moveId": "HURRICANE", "uses": 31959}, {"moveId": "AIR_CUTTER", "uses": 9257}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 26.6}, {"speciesId": "oricorio_pau", "speciesName": "Oricorio (Pa'u)", "rating": 378, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 826, "opRating": 173}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 826, "opRating": 173}, {"opponent": "virizion", "rating": 762, "opRating": 237}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 655, "opRating": 344}, {"opponent": "buzzwole", "rating": 612, "opRating": 387}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "giratina_origin", "rating": 129}, {"opponent": "gyarados", "rating": 167}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 171}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66272}, {"moveId": "POUND", "uses": 10228}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35282}, {"moveId": "HURRICANE", "uses": 31959}, {"moveId": "AIR_CUTTER", "uses": 9257}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 26.5}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 406, "matchups": [{"opponent": "virizion", "rating": 713, "opRating": 286}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 698, "opRating": 301}, {"opponent": "pangoro", "rating": 670, "opRating": 329}, {"opponent": "pinsir", "rating": 634, "opRating": 365}, {"opponent": "pinsir_shadow", "rating": 594, "opRating": 405}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "gyarados", "rating": 167}, {"opponent": "garcho<PERSON>", "rating": 171}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66260}, {"moveId": "POUND", "uses": 10240}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35281}, {"moveId": "HURRICANE", "uses": 31962}, {"moveId": "AIR_CUTTER", "uses": 9261}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 26.5}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 414, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 779, "opRating": 220}, {"opponent": "espeon", "rating": 716, "opRating": 283}, {"opponent": "mewtwo_armored", "rating": 645, "opRating": 354}, {"opponent": "gallade", "rating": 641, "opRating": 358}, {"opponent": "mewtwo", "rating": 515}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "gyarados", "rating": 126}, {"opponent": "dialga", "rating": 144}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "zacian_hero", "rating": 251}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40469}, {"moveId": "FEINT_ATTACK", "uses": 36031}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 42019}, {"moveId": "SHADOW_SNEAK", "uses": 23730}, {"moveId": "OMINOUS_WIND", "uses": 10762}]}, "moveset": ["SUCKER_PUNCH", "SHADOW_BALL", "SHADOW_SNEAK"], "score": 26.5}, {"speciesId": "electabuzz_shadow", "speciesName": "Electabuzz (Shadow)", "rating": 537, "matchups": [{"opponent": "gyarados", "rating": 744, "opRating": 255}, {"opponent": "gyarado<PERSON>_shadow", "rating": 694, "opRating": 305}, {"opponent": "ho_oh", "rating": 644, "opRating": 355}, {"opponent": "zap<PERSON>_galarian", "rating": 634, "opRating": 365}, {"opponent": "lugia", "rating": 560, "opRating": 439}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "mewtwo", "rating": 335}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58071}, {"moveId": "LOW_KICK", "uses": 18429}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 44950}, {"moveId": "THUNDERBOLT", "uses": 16800}, {"moveId": "THUNDER", "uses": 14524}, {"moveId": "FRUSTRATION", "uses": 109}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 26.2}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 391, "matchups": [{"opponent": "empoleon", "rating": 761, "opRating": 238}, {"opponent": "feraligatr_shadow", "rating": 758, "opRating": 241}, {"opponent": "gengar", "rating": 750, "opRating": 250}, {"opponent": "mew", "rating": 683, "opRating": 316}, {"opponent": "giratina_origin", "rating": 529, "opRating": 470}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dialga", "rating": 165}, {"opponent": "gyarados", "rating": 170}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "metagross", "rating": 264}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 59852}, {"moveId": "TAKE_DOWN", "uses": 16648}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35228}, {"moveId": "MEGAHORN", "uses": 19473}, {"moveId": "SOLAR_BEAM", "uses": 11482}, {"moveId": "HYPER_BEAM", "uses": 10238}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "MEGAHORN"], "score": 26.2}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 410, "matchups": [{"opponent": "tapu_bulu", "rating": 774, "opRating": 225}, {"opponent": "virizion", "rating": 734, "opRating": 265}, {"opponent": "pinsir", "rating": 716, "opRating": 283}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 698, "opRating": 301}, {"opponent": "genesect_burn", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "gyarados", "rating": 167}, {"opponent": "garcho<PERSON>", "rating": 171}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66392}, {"moveId": "POUND", "uses": 10108}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35281}, {"moveId": "HURRICANE", "uses": 31981}, {"moveId": "AIR_CUTTER", "uses": 9257}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 25.9}, {"speciesId": "throh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 398, "matchups": [{"opponent": "bisharp", "rating": 790, "opRating": 209}, {"opponent": "weavile", "rating": 694, "opRating": 305}, {"opponent": "regirock", "rating": 673, "opRating": 326}, {"opponent": "weavile_shadow", "rating": 640, "opRating": 359}, {"opponent": "melmetal", "rating": 562, "opRating": 437}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "metagross", "rating": 180}, {"opponent": "gyarados", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "dialga", "rating": 290}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 49320}, {"moveId": "ZEN_HEADBUTT", "uses": 27180}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41229}, {"moveId": "FOCUS_BLAST", "uses": 18509}, {"moveId": "LOW_SWEEP", "uses": 16787}]}, "moveset": ["LOW_KICK", "BODY_SLAM", "FOCUS_BLAST"], "score": 25.8}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 418, "matchups": [{"opponent": "tapu_bulu", "rating": 890, "opRating": 109}, {"opponent": "rhyperior", "rating": 722, "opRating": 277}, {"opponent": "swampert", "rating": 695, "opRating": 304}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "swampert_shadow", "rating": 658, "opRating": 341}], "counters": [{"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "dialga", "rating": 127}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "gyarados", "rating": 157}, {"opponent": "giratina_origin", "rating": 175}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 38439}, {"moveId": "BULLET_SEED", "uses": 38061}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33813}, {"moveId": "PETAL_BLIZZARD", "uses": 30458}, {"moveId": "SOLAR_BEAM", "uses": 12287}]}, "moveset": ["POISON_JAB", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 25.7}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 479, "matchups": [{"opponent": "porygon_z_shadow", "rating": 835, "opRating": 164}, {"opponent": "gyarado<PERSON>_shadow", "rating": 744, "opRating": 255}, {"opponent": "moltres", "rating": 701, "opRating": 298}, {"opponent": "gyarados", "rating": 687, "opRating": 312}, {"opponent": "moltres_shadow", "rating": 647, "opRating": 352}], "counters": [{"opponent": "garcho<PERSON>", "rating": 56}, {"opponent": "giratina_origin", "rating": 107}, {"opponent": "dialga", "rating": 138}, {"opponent": "mewtwo", "rating": 195}, {"opponent": "zacian_hero", "rating": 283}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 60387}, {"moveId": "LOW_KICK", "uses": 16113}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 34819}, {"moveId": "RETURN", "uses": 17570}, {"moveId": "THUNDERBOLT", "uses": 12960}, {"moveId": "THUNDER", "uses": 11164}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 25.4}, {"speciesId": "noivern", "speciesName": "Noivern", "rating": 391, "matchups": [{"opponent": "virizion", "rating": 691, "opRating": 308}, {"opponent": "pinsir_shadow", "rating": 657, "opRating": 342}, {"opponent": "buzzwole", "rating": 634, "opRating": 365}, {"opponent": "grou<PERSON>", "rating": 598, "opRating": 401}, {"opponent": "kyogre", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "giratina_origin", "rating": 141}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "mewtwo", "rating": 210}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 46197}, {"moveId": "BITE", "uses": 30303}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 24771}, {"moveId": "DRACO_METEOR", "uses": 22046}, {"moveId": "PSYCHIC", "uses": 20089}, {"moveId": "HEAT_WAVE", "uses": 9563}]}, "moveset": ["AIR_SLASH", "HURRICANE", "DRACO_METEOR"], "score": 25.4}, {"speciesId": "weezing_shadow", "speciesName": "Weez<PERSON> (Shadow)", "rating": 393, "matchups": [{"opponent": "gardevoir_shadow", "rating": 573, "opRating": 426}, {"opponent": "pinsir_shadow", "rating": 567, "opRating": 432}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 560, "opRating": 439}, {"opponent": "latios_shadow", "rating": 533, "opRating": 466}, {"opponent": "gallade", "rating": 510, "opRating": 489}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 211}, {"opponent": "lugia", "rating": 240}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "giratina_origin", "rating": 282}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 42083}, {"moveId": "TACKLE", "uses": 34417}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 21611}, {"moveId": "DARK_PULSE", "uses": 20126}, {"moveId": "SLUDGE_BOMB", "uses": 19848}, {"moveId": "THUNDERBOLT", "uses": 14938}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 25.4}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 416, "matchups": [{"opponent": "sandslash_alolan_shadow", "rating": 845, "opRating": 154}, {"opponent": "golem_alolan", "rating": 775, "opRating": 224}, {"opponent": "snor<PERSON>_shadow", "rating": 691, "opRating": 308}, {"opponent": "walrein", "rating": 644, "opRating": 355}, {"opponent": "excadrill", "rating": 550, "opRating": 449}], "counters": [{"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "mewtwo", "rating": 140}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 54577}, {"moveId": "LOW_KICK", "uses": 11936}, {"moveId": "POUND", "uses": 9955}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 34745}, {"moveId": "FOCUS_BLAST", "uses": 20943}, {"moveId": "HYPER_BEAM", "uses": 20785}]}, "moveset": ["DOUBLE_KICK", "FIRE_PUNCH", "FOCUS_BLAST"], "score": 25.1}, {"speciesId": "absol_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 470, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 828, "opRating": 171}, {"opponent": "mewtwo_armored", "rating": 785, "opRating": 214}, {"opponent": "victini", "rating": 761, "opRating": 238}, {"opponent": "mewtwo", "rating": 627}, {"opponent": "metagross", "rating": 593}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "dragonite", "rating": 98}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42289}, {"moveId": "PSYCHO_CUT", "uses": 34211}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 28417}, {"moveId": "MEGAHORN", "uses": 19975}, {"moveId": "THUNDER", "uses": 15045}, {"moveId": "PAYBACK", "uses": 12921}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 24.8}, {"speciesId": "meowstic_female", "speciesName": "<PERSON><PERSON><PERSON> (Female)", "rating": 340, "matchups": [{"opponent": "hitmon<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "primeape", "rating": 616, "opRating": 383}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "gallade", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 173}, {"opponent": "zacian_hero", "rating": 184}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44370}, {"moveId": "CHARM", "uses": 32130}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 31197}, {"moveId": "PSYCHIC", "uses": 26606}, {"moveId": "ENERGY_BALL", "uses": 18704}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 24.8}, {"speciesId": "me<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON> (Male)", "rating": 331, "matchups": [{"opponent": "hitmon<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "primeape", "rating": 616, "opRating": 383}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "gallade", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 173}, {"opponent": "zacian_hero", "rating": 184}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40886}, {"moveId": "SUCKER_PUNCH", "uses": 35614}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 30614}, {"moveId": "THUNDERBOLT", "uses": 24484}, {"moveId": "ENERGY_BALL", "uses": 21401}]}, "moveset": ["CONFUSION", "PSYCHIC", "THUNDERBOLT"], "score": 24.8}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 379, "matchups": [{"opponent": "primarina", "rating": 609, "opRating": 390}, {"opponent": "pinsir", "rating": 589, "opRating": 410}, {"opponent": "zarude", "rating": 587, "opRating": 412}, {"opponent": "zacian_hero", "rating": 559, "opRating": 440}, {"opponent": "x<PERSON><PERSON>", "rating": 517, "opRating": 482}], "counters": [{"opponent": "mewtwo", "rating": 93}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "dialga", "rating": 173}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "gyarados", "rating": 185}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54750}, {"moveId": "ROCK_SMASH", "uses": 21750}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 33587}, {"moveId": "SLUDGE_BOMB", "uses": 28776}, {"moveId": "GUNK_SHOT", "uses": 7474}, {"moveId": "ACID_SPRAY", "uses": 6610}]}, "moveset": ["INFESTATION", "ICE_BEAM", "SLUDGE_BOMB"], "score": 24.7}, {"speciesId": "hit<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 314, "matchups": [{"opponent": "weavile_shadow", "rating": 917, "opRating": 82}, {"opponent": "weavile", "rating": 917, "opRating": 82}, {"opponent": "bisharp", "rating": 917, "opRating": 82}, {"opponent": "sandslash_alolan", "rating": 803, "opRating": 196}, {"opponent": "mamos<PERSON>_shadow", "rating": 602, "opRating": 397}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "gyarados", "rating": 146}, {"opponent": "zacian_hero", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "dialga", "rating": 285}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 46834}, {"moveId": "LOW_KICK", "uses": 29666}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30849}, {"moveId": "STONE_EDGE", "uses": 16573}, {"moveId": "BRICK_BREAK", "uses": 13696}, {"moveId": "STOMP", "uses": 11335}, {"moveId": "LOW_SWEEP", "uses": 3979}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_SMASH", "CLOSE_COMBAT", "STONE_EDGE"], "score": 24.4}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 340, "matchups": [{"opponent": "vaporeon", "rating": 614, "opRating": 385}, {"opponent": "swampert", "rating": 592, "opRating": 407}, {"opponent": "sneasler", "rating": 587, "opRating": 412}, {"opponent": "tapu_fini", "rating": 564, "opRating": 435}, {"opponent": "tapu_koko", "rating": 552, "opRating": 447}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 219}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6057}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5725}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5266}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5032}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4955}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4779}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4769}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4593}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4565}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4471}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4463}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4424}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4109}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4105}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3918}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3778}, {"moveId": "ZEN_HEADBUTT", "uses": 1436}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41158}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12209}, {"moveId": "SOLAR_BEAM", "uses": 8427}]}, "moveset": ["ZEN_HEADBUTT", "GRASS_KNOT", "SEED_FLARE"], "score": 24.2}, {"speciesId": "grumpig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 395, "matchups": [{"opponent": "gallade", "rating": 627, "opRating": 372}, {"opponent": "magmortar_shadow", "rating": 627, "opRating": 372}, {"opponent": "terrakion", "rating": 607, "opRating": 392}, {"opponent": "machamp", "rating": 604, "opRating": 395}, {"opponent": "heracross", "rating": 604, "opRating": 395}], "counters": [{"opponent": "mewtwo", "rating": 85}, {"opponent": "giratina_origin", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "gyarados", "rating": 141}, {"opponent": "dialga", "rating": 279}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 44502}, {"moveId": "CHARGE_BEAM", "uses": 31998}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37606}, {"moveId": "PSYCHIC", "uses": 31989}, {"moveId": "MIRROR_COAT", "uses": 6790}]}, "moveset": ["EXTRASENSORY", "SHADOW_BALL", "PSYCHIC"], "score": 24}, {"speciesId": "xatu", "speciesName": "Xatu", "rating": 335, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 808, "opRating": 191}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 808, "opRating": 191}, {"opponent": "virizion", "rating": 741, "opRating": 258}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 664, "opRating": 335}, {"opponent": "buzzwole", "rating": 577, "opRating": 422}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "gyarados", "rating": 167}, {"opponent": "mewtwo", "rating": 169}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 39796}, {"moveId": "FEINT_ATTACK", "uses": 36704}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 29234}, {"moveId": "FUTURE_SIGHT", "uses": 26802}, {"moveId": "OMINOUS_WIND", "uses": 20526}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "FUTURE_SIGHT"], "score": 24}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 560, "matchups": [{"opponent": "ho_oh", "rating": 838, "opRating": 161}, {"opponent": "moltres", "rating": 838, "opRating": 161}, {"opponent": "entei", "rating": 807, "opRating": 192}, {"opponent": "moltres_shadow", "rating": 807, "opRating": 192}, {"opponent": "gyarados", "rating": 724}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "dialga", "rating": 258}, {"opponent": "dragonite", "rating": 292}, {"opponent": "metagross", "rating": 328}, {"opponent": "zacian_hero", "rating": 375}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 57229}, {"moveId": "ASTONISH", "uses": 19271}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34720}, {"moveId": "HYDRO_PUMP", "uses": 26674}, {"moveId": "THUNDER", "uses": 15106}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 23.1}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 346, "matchups": [{"opponent": "thundurus_therian", "rating": 714, "opRating": 285}, {"opponent": "magnezone", "rating": 679, "opRating": 320}, {"opponent": "magnezone_shadow", "rating": 644, "opRating": 355}, {"opponent": "nihilego", "rating": 619, "opRating": 380}, {"opponent": "melmetal", "rating": 570, "opRating": 429}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "giratina_origin", "rating": 121}, {"opponent": "dialga", "rating": 133}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 190}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 28248}, {"moveId": "MUD_SLAP", "uses": 25194}, {"moveId": "EXTRASENSORY", "uses": 23050}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 16783}, {"moveId": "ICE_BEAM", "uses": 14072}, {"moveId": "SHADOW_BALL", "uses": 13681}, {"moveId": "PSYCHIC", "uses": 11892}, {"moveId": "ROCK_TOMB", "uses": 7921}, {"moveId": "EARTHQUAKE", "uses": 7234}, {"moveId": "GYRO_BALL", "uses": 4906}]}, "moveset": ["CONFUSION", "EARTH_POWER", "ICE_BEAM"], "score": 22.8}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 402, "matchups": [{"opponent": "avalugg", "rating": 656, "opRating": 343}, {"opponent": "zarude", "rating": 608, "opRating": 391}, {"opponent": "metagross", "rating": 598, "opRating": 401}, {"opponent": "genesect_chill", "rating": 515, "opRating": 484}, {"opponent": "genesect_burn", "rating": 515, "opRating": 484}], "counters": [{"opponent": "giratina_origin", "rating": 73}, {"opponent": "garcho<PERSON>", "rating": 86}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "lugia", "rating": 178}, {"opponent": "dialga", "rating": 182}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38590}, {"moveId": "EMBER", "uses": 37910}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 34901}, {"moveId": "EARTHQUAKE", "uses": 26322}, {"moveId": "SOLAR_BEAM", "uses": 15338}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 22}, {"speciesId": "sliggoo", "speciesName": "Sliggoo", "rating": 357, "matchups": [{"opponent": "golem", "rating": 870, "opRating": 129}, {"opponent": "chandelure", "rating": 818, "opRating": 181}, {"opponent": "typhlosion", "rating": 769, "opRating": 230}, {"opponent": "magmortar_shadow", "rating": 577, "opRating": 422}, {"opponent": "electivire_shadow", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "giratina_origin", "rating": 109}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "zacian_hero", "rating": 210}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40197}, {"moveId": "TACKLE", "uses": 36303}], "chargedMoves": [{"moveId": "DRAGON_PULSE", "uses": 27418}, {"moveId": "MUDDY_WATER", "uses": 27051}, {"moveId": "SLUDGE_WAVE", "uses": 16728}, {"moveId": "WATER_PULSE", "uses": 5281}]}, "moveset": ["WATER_GUN", "DRAGON_PULSE", "MUDDY_WATER"], "score": 20.6}, {"speciesId": "accelgor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 331, "matchups": [{"opponent": "rotom_wash", "rating": 822, "opRating": 177}, {"opponent": "lycanroc_midnight", "rating": 735, "opRating": 264}, {"opponent": "lura<PERSON>s", "rating": 686, "opRating": 313}, {"opponent": "ursaring_shadow", "rating": 630, "opRating": 369}, {"opponent": "pinsir", "rating": 569, "opRating": 430}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "zacian_hero", "rating": 213}, {"opponent": "lugia", "rating": 223}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54985}, {"moveId": "ACID", "uses": 21515}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26682}, {"moveId": "SIGNAL_BEAM", "uses": 22924}, {"moveId": "FOCUS_BLAST", "uses": 20353}, {"moveId": "ACID_SPRAY", "uses": 6623}]}, "moveset": ["INFESTATION", "BUG_BUZZ", "SIGNAL_BEAM"], "score": 18.9}, {"speciesId": "deoxys", "speciesName": "Deoxys", "rating": 385, "matchups": [{"opponent": "blaziken", "rating": 744, "opRating": 255}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 681, "opRating": 318}, {"opponent": "machamp_shadow", "rating": 677, "opRating": 322}, {"opponent": "zap<PERSON>_galarian", "rating": 570, "opRating": 429}, {"opponent": "ho_oh", "rating": 543, "opRating": 456}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "metagross", "rating": 133}, {"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "gyarados", "rating": 273}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 60312}, {"moveId": "ZEN_HEADBUTT", "uses": 16188}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 42173}, {"moveId": "THUNDERBOLT", "uses": 22800}, {"moveId": "HYPER_BEAM", "uses": 11542}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 18.3}]