{"name": "catch", "title": "Catch Cup", "include": [], "exclude": [{"filterType": "id", "values": ["deoxys_defense", "regirock", "regice", "mew", "victini", "genesect", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "latios", "latias", "rai<PERSON>u", "suicune", "entei", "celebi", "diancie", "regidrago", "<PERSON><PERSON><PERSON><PERSON>", "ho_oh", "buzzwole", "marowak_alolan_shadow", "lap<PERSON>_shadow", "hitmontop_shadow", "meganium_shadow", "jumpluff_shadow", "steelix_shadow", "walrein_shadow", "poliwrath_shadow", "politoed_shadow", "sealeo_shadow", "guzzlord", "zap<PERSON>_shadow", "sableye_shadow", "feraligatr_shadow", "golurk_shadow", "quagsire_shadow", "diggersby_shadow", "zap<PERSON>_shadow", "bastiodon_shadow", "typhlosion_shadow", "registeel_shadow", "dusknoir_shadow", "dusclops_shadow"]}, {"filterType": "tag", "values": ["mega"]}]}