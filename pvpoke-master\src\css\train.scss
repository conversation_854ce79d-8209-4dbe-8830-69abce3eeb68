/* MIXINS */

@mixin transition($property, $duration, $ease, $delay){
    -webkit-transition: $property $duration $ease $delay;
    -moz-transition: $property $duration $ease $delay;
    -o-transition: $property $duration $ease $delay;
    transition: $property $duration $ease $delay;
}

@mixin gradient($light, $dark){
	background: $dark; /* Old browsers */
	background: -moz-linear-gradient(top, $light 30%, $dark 99%) !important; /* FF3.6-15 */
	background: -webkit-linear-gradient(top, $light 30%,$dark 99%) !important; /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, $light 30%,$dark 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

@mixin transform($property){
	-moz-transform: $property;
	-webkit-transform: $property;
	-o-transform: $property;
	-ms-transform: $property;
	transform: $property;
}

.multi{
	.quick-fill-select option.multi-battle{
		display:none !important;
	}

	.cup-select, .format-select{
		display:none;
	}
}

.train{
	.poke{
		display:block;
		width:100%;

		&:first-of-type{
			float:left;
		}

		&:last-of-type{
			float:right;
		}

		&.ai-options{
			float:right;
		}

		h3:first-of-type{
			margin:0;
		}

		.options{
			display:none;
		}

		.custom-options,
		.poke-stats{
			display:block;
		}

		.section-title,
		.rank{
			text-align: left;
		}

		p{
			text-align: left;
		    font-size: 12px;
		}
	}

	.featured-team-section{
		margin-top:10px;
		display:none;
	}

	.custom-team-section{
		display:none;

		.team-import{
			width:95%;
			display:block;
		}

		.custom-team-validation{
			border-radius:8px;
			padding: 8px;
			font-size:12px;
			text-align: left;
			display: none;
			margin-top:5px;

			&.true{
				border: 1px solid #0eb084;
				color:#0eb084;
			}

			&.false{
				border: 1px solid #ff5115;
				color:#ff5115;
			}
		}
	}
}


.section.battle{
		height:auto !important;
}

.editor{
	.poke.multi{
		float:none;

		.poke-stats{
			background:none;
		}
	}

	.multi-selector{
		display:none;
	    border-top: 1px solid;
	    padding-top: 20px;
	    margin-top: 20px;

		&[mode="new"]{
			.button.add-team{
				display:block;
			}

			.button.save-changes{
				display:none;
			}
		}

		&[mode="edit"]{
			.button.add-team{
				display:none;
			}

			.button.save-changes{
				display:block;
			}
		}

		.poke{
			h3:nth-of-type(2){ display:none; }
			.quick-fill-select{ display:none; }
			.quick-fill-buttons{ display:none; }
		}

		.pokebox{
			display:none !important;
		}
	}

	.button.new-team,
	.button.add-team,
	.button.save-changes{
		margin:10px 0;
	}

	.table-container{
		max-height: 800px;
	}
}

.training-editor-import{
	h3{
		margin-top:0;
	}

	textarea.import {
	    width: 100%;
	    height: 100px;
	}

	.copy {
	    display: inline-block;
	    cursor: pointer;
	    font-weight: bold;
	    background: #004c66;
	    color: #eee;
	    padding: 5px;
	    border-radius: 8px;
	    margin: 5px 0;
	}

	.team-fill-select{
		max-width:200px;
		margin-bottom:10px;
	}

	.team-fill-buttons button{
		margin-right:10px;
	}
}

.featured-team-description{
	display: none;
	text-align: center;

	img{
		max-width:75px;
		margin: 0 auto;
	}

	a h3{
		color:#003748;
		margin-top: 0;
		font-size: 16px;
		font-weight:bold !important;
		text-decoration: underline;
	}

	h5{
		font-size:14px;
		text-align: left;
		margin: 10px 0 5px 0;
	}

	.featured-team-preview{
		padding: 5px;
		border: 1px solid;
		border-radius: 12px;

		.preview-poke{
			display:flex;
			margin-bottom:5px;

			&:last-of-type{
				margin-bottom: 0;
			}

			.sprite-container{
				width: 32px;
				height: 32px;
				background: none;
				border: none;
				padding: 0;
				margin-right:10px;
			}

			h6, p{
				margin: 0;
			}

			h6{
				text-align: left;
				font-size:14px;
			}

			p{
				font-size:12px;
			}
		}
	}
}

.ai-options .poke.multi{
	float:none;
	display:none;

	.poke-stats{
		background:none;
		padding:0;
	}
}

.poke.ai-options a.inline-link{
	font-size:12px;
	font-weight:bold;
	text-decoration: underline;
	margin:0;
	display:inline;


	&.train-editor-link{
		display:block;
		text-align: left;
		margin-top:10px;
	}
}

.autotap-toggle{
	font-size:14px;
	text-align: left;
}

a.random{
	margin-top:5px;
}

.section.battle{
	text-align: center;
}

.battle-active{

	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -moz-tap-highlight-color: rgba(0, 0, 0, 0);
  	-webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
     -khtml-user-select: none; /* Konqueror HTML */
       -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently
                                  supported by Chrome and Opera */

	.battle-window{
		display:inline-block;
	}
	header{
		display: none;
	}

	#main{
		padding-top:0;
	}
}

.battle-window{
	display:none;
	background:rgba(255,255,255,0.75);
	margin:0 auto;
	overflow: hidden;
	position:relative;
    box-shadow: 0px 5px 15px;
	cursor:pointer;
	touch-action: manipulation;

	.img-block{
		visibility: hidden;
		max-width:100%;
	}

	.top{
		padding-top:5px;
		text-align:center;
		position:absolute;
		width:100%;
		top:0;
		z-index:10;

		.timer{
			display:none;
			color:#eee;
			width:60px;
			height:60px;
			border-radius:60px;
			background:rgba(0,0,0,0.75);
			margin: 0 auto;

			.text{
				display:table-cell;
				vertical-align: middle;
				font-size:28px;
			}
		}

		.shields{
			background-size:contain;
			background-image:url('../img/shield.png');
			background-repeat:no-repeat;
			padding-left: 24px;
		    font-size: 14px;
		    height: 15px;
			margin-top:2px;

			&.left{
				left:6%;
			}

			&.right{
				right:6%;
			}

			&.no-shields{
				color:#ff0000;
			}
		}

		.team-indicator{
			position:absolute;
			background: rgba(255,255,255,.75);
			border-radius:8px;
			display:flex;
			flex-wrap:wrap;
			top:25px;
			padding: 3px;
			max-width:35%;
			justify-content: space-between;

			&.left{
				left:2.5%;

				.cmp, .types{
					left:0;
				}

				.type{
					margin-left:5px;
				}
			}

			&.right{
				right:2.5%;

				.cp{
					text-align: left;
				}

				.name{
					text-align: right;
				}

				.cmp, .types{
					right:0;
				}

				.type{
					margin-right:5px;
				}
			}

			.name, .cp, .balls{
				width:50%;
				font-size:12px;
				font-weight:bold;
				text-align: left;
			}

			.cp{
				text-align: right;
			}

			.balls{
				display:flex;
			}

			.ball{
				width:14px;
				height:14px;
				border-radius:40px;
				background:#ff0000;
				margin: 3px 3px;

				&.fainted{
					background:#000;
				}
			}

			.cmp{
				position: absolute;
				top: 110%;
				font-weight: bold;
				font-size: 12px;
				padding: 5px;
				background: rgba(0,0,0,0.25);
				color: #eee;
				border-radius: 12px;
			}

			.types{
				position: absolute;
			    display: flex;
			    top: -20px;

				.type{
					width: 18px;
				    height: 18px;
				    border-radius: 18px;
				    font-weight: bold;
					color:#fff;
				    font-size: 12px;
				    line-height: 20px;
					text-transform: capitalize;

					&.none{
						display:none;
					}
				}
			}

			.featured-team-description{
				position: absolute;
				top: 190%;
				right: 0;

				img{
					max-width:45px;
				}
			}
		}
	}

	.scene{
		position:absolute;
		width: 150%;
		height: 150%;
		left: -25%;
		top: -25%;

		@include transition(all, 0.5s, ease, 0s);

		.background{
			position: absolute;
			top:0;
			left:0;
			width:100%;
			height:100%;
			background-image: url('../img/train/pvp-bg.jpg');
			background-repeat: no-repeat;
			background-position: center bottom;
		}

		.pokemon-container{
			position:absolute;
			width:37.5%;

			&.self{
				bottom: 30%;
				left: 20%;
				z-index: 10;

				.pokemon{
					@include transform(scaleX(-1));

					.main-sprite, .secondary-sprite{
						z-index:10;
					}

					.shadow{
						z-index:20;
					}
				}

				.fast-move-circle-container{
					top: -10px;
					left: -70px;
				}
			}

			&.opponent{
				bottom: 52%;
				right: 25%;

				.shield-sprite{
					z-index:20;
				}

				.fast-move-circle-container{
					top: 25px;
					left: -60px;
					z-index:25;
				}
			}

			h3{
				display: inline-block;
			    padding: 5px;
			    margin: 0 0 5px 0;
    			line-height: 18px;
			    border-radius: 12px;
				text-align: center;

			}

			.messages{
				text-align: center;
			}

			.pokemon{
				@include transition(all, 1s, ease, 0s);

				position:relative;
				width:100px;
				height:100px;
				margin: 0 auto;

				.fast-move-circle-container{
					position: absolute;
					width:100px;
					height:100px;
				    display: flex;
				    justify-content: center;
				    align-items: center;
				}

				.fast-move-circle{
					width: 100px;
				    height: 100px;
				    border-radius: 300px;
					opacity: 0;
				}

				.shadow{
					width:151px;
					height:62px;
					background:url('../img/train/sprite-shadow.png');
					background-size:100%;
					background-repeat: no-repeat;
					position: absolute;
					z-index: 5;
					top: 65px;
					left: -25px;
					opacity: 0.45;
				}

				.shield-sprite-container{
					position:absolute;
					top:0;
					left:0;

					&.active{
						.shield-sprite{
							opacity:.85;
						}
					}

					.shield-sprite{
						width:65px;
						height:55px;
						background:url('../img/train/shield.png');
						background-size:cover;
						background-repeat: no-repeat;
						position:absolute;
						opacity:0;

						&:nth-of-type(1){
							@include transition(all, .75s, ease, 0s);
							top:-15px;
						}
						&:nth-of-type(2){
							@include transition(all, .75s, ease, .05s);
							top: 15px;
							left: 52px;
						}
						&:nth-of-type(3){
							@include transition(all, .75s, ease, .15s);
							top: 45px;
						}
						&:nth-of-type(4){
							@include transition(all, .75s, ease, .2s);
							top: 15px;
						    left: -52px;
						}
					}
				}
			}

			.hp.bar-back{
				outline:2px solid #ccc;
				border:none;
				height:15px;
				width: 90%;
				margin: 0 auto 20px auto;

				@include transition(all, 0.4s, ease, 0s);

				&.super-effective, &.effective, &.not-very-effective{
					@include transition(all, 0.05s, ease, 0s);
				}

				&.super-effective{
					outline: 4px solid #ea0404;
				}

				&.effective{
					outline: 2px solid #f57a23;
				}

				&.not-very-effective{
					outline: 2px solid #2172ea;
				}

				.bar{
					position:relative;
					top:-11px;
					background-color:#31e8b7;

					@include transition(width, 0.2s, ease, 0s);

					&.damage{
						background:#ff5115;
						position: relative;
						top: 0px;

						@include transition(width, 0.2s, ease, 0.5s);
					}

					&[color="yellow"]{
						background:#eabd49;
					}

					&[color="red"]{
						background:#e11414;
					}
				}
			}
		}
	}

	.controls{
		position:absolute;
		bottom:0;
		width:100%;
		height:40%;

		@include gradient(rgba(0,0,0,0), rgba(0,0,0,.85));

		.move-bars{
			position:absolute;
			bottom:25%;
			width:100%;
			z-index:15;

			.move-bar{
				display:block;
				width:70px;
				height:70px;

				@include transition(all, 0.2s, ease, 0s);

				.bar{
					@include transition(top, 0.2s, ease-out, 0s);

					bottom:0;
					background: url(../img/train/type-gradients.jpg);
					background-repeat: no-repeat;
				}

				.bar-back{
					border-radius:40px;
				}

				.label{
					font-size:24px;
					padding-top:18px;
					pointer-events: none;
				}

				&.active{
					border:3px solid #000;
					box-shadow: 0px 5px 10px rgba(0,0,0,.5);

					@include transform(scale(1.25, 1.25));


					&:active{
						@include transform(scale(1.15, 1.15));
					}
				}
			}
		}

		.move-labels{
			position: absolute;
		    bottom: 62%;
		    width: 100%;
		    display: flex;
		    justify-content: space-around;
			z-index: 15;

			.label{
				@include transition(all, 0.2s, ease, 0s);

				width:50%;
				font-weight: bold;
			    color: rgba(0,0,0,0.75);
			    text-transform: uppercase;
			    font-size: 12px;
				padding:1px 0;

				&.active{
					color: rgba(255,255,255,1);
					text-shadow: 3px 3px 5px #000;
					font-size:14px;
					padding:0;
				}
			}
		}

		.switch-btn{

			width:40px;
			height:35px;
			border-radius:40px;
			background:rgba(0,0,0,0.5);
			border:2px solid #000;
			text-align:center;
			padding-top:5px;
			font-size:24px;
			font-weight:bold;
			position:absolute;
			z-index: 25;
			bottom:5%;
			right:5%;
			-webkit-text-fill-color: #eee;
			-webkit-text-stroke-width: 1px;
			-webkit-text-stroke-color: #000;

			&.active{
				background:#eee;
			}
		}

		.auto-tap-container{
			position: absolute;
			width: 100%;
			bottom: 10px;

			.auto-tap{
				@include transition(all, 0.2s, ease, 0s);

				display: inline-block;
				background: #888;
				padding: 5px;
				border-radius: 12px;
				font-weight: bold;

				&.active{
					background:#ee2222;
					color:#eee;
				}
			}
		}

		.button-stack{
			@include transition(all, 0.2s, ease, 0s);

			position:absolute;
			background: rgba(255, 255, 255, 0.85);
			padding: 5px 2px;
			border-radius: 8px;
			left: 2.5%;
			top: -80%;
			z-index: 15;

			div{
				width: 16px;
			    height: 19px;
				margin:10px 0;
			    background-position: center center;
			    background-repeat: no-repeat;

				&:first-of-type{
					margin-top:0;
				}

				&:last-of-type{
					margin-bottom:0;
				}
			}

			.pause-btn{
				background-image: url('../img/playback_pause.png');

				&.active{
					background-image: url('../img/playback_play.png');
				}
			}

			.restart-btn{
				background-image: url('../img/playback_replay.png');
			}
		}
	}

	.shield-window{
		width:100%;
		height:40%;
		position:absolute;
		left:0;
		bottom:0;
		opacity: 0;
		pointer-events: none;

		@include transition(all, 0.25s, ease, 0s);

		&.closed{
			pointer-events: none;
			bottom:-50%;
		}

		.container{

			.close{
				font-weight: bold;
				text-transform: uppercase;
				cursor:pointer;
			}

			.shield{
				width:109px;
				height:94px;
				margin: 1em auto;
				cursor:pointer;
				background:url('../img/train/shield.png');
				background-size:contain;
			}
		}
	}

	.shield-window, .switch-window, .charge-window{
		position:absolute;
		height:50%;
		bottom:0;
		width:100%;
		text-align: center;
		color:#eee;
		opacity: 0;
		pointer-events: none;
	}

	.shield-window .container,
	.switch-window .container{
		width:90%;
		margin:0 auto;
		height:100%;
		border-radius:12px;
		background:rgba(0,0,0,0.5);
		color:#eee;
		text-align: center;
		padding:30px;
		box-sizing:border-box;
	}

	.charge-window{

		@include transition(all, 0.25s, ease, 0.25s);

		.container{

			p{
				padding:10px;
				border-radius:8px;
				background:rgba(0,0,0,0.5);
			}

			.move-bar{
				display:block;
				width:120px;
				height:120px;
				border-radius:120px;
				border: 6px solid #eee;

				.label{
					font-size:60px;
					padding-top:22px;
				}

				.bar:nth-child(3),
				.bar:nth-child(4){
					display: none;
				}
			}
		}

		.rings{
			position:absolute;
			width:500px;
			height:500px;
			left:50%;
			bottom:-25%;
			margin-left:-250px;

			.ring-container{
				position:absolute;
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;

				.ring{
					border: 4px solid #eee;
					border-radius: 500px;
				}
			}

			.ring-container:nth-of-type(1) .ring{
				max-width:90%;
				max-height:90%;
			}

			.ring-container:nth-of-type(2) .ring{
				max-width:95%;
				max-height:95%;
			}

			.ring-container:nth-of-type(3) .ring{
				max-width:100%;
				max-height:100%;
			}
		}

		.charge{
			font-weight: bold;
			font-size:18px;
			padding:10px;
			border-radius: 12px;
			background:rgba(0,0,0,0.5);
			display:inline-block;
			position: relative;
			top: 170px;
		}
	}

	.switch-sidebar{
		position: absolute;
		text-align: center;
		z-index:20;
		bottom: 27%;
		right:0;
		pointer-events: all;

		.pokemon{
			position: relative;
		}

		.sprite-container{
			padding:0;
			width:45px;
			height:45px;
			text-align: center;
			color:#eee;

			.main-sprite,
			.secondary-sprite{
				width:75%;
				height:75%;
				left:6px;
				top:6px;
			}

			.cp{
				position: absolute;
			    z-index: 5;
			    font-size: 10px;
			    text-align: center;
			    width: 100%;
			}
		}

		.switch-timer{
			color: #eee;
		    font-weight: bold;
		    position: absolute;
		    top: 12px;
		    width: 100%;
		    text-align: center;
		    font-size: 24px;
			display:none;
		}

		.name{
			font-size:11px;
			margin-bottom:5px;
			width:60px;
			min-height:24px;
		}

		&:not(.active){
			.sprite-container{
				opacity: 0.25;
			}

			.switch-timer{
				display: block;
			}
		}

		.container{
			padding: 10px;
		}
	}

	.switch-window{
		height:30%;
		opacity:1;
		text-align: center;
		z-index:20;
		bottom:-30%;

		@include transition(all, 0.25s, ease, 0s);

		&.active{
			bottom:0;
			pointer-events: all;
		}

		p{
			margin-top:0;
		}

		.container{
			padding: 15px 30px;
		}

		.pokemon{
			position:relative;
		}

		.pokemon-container{
			display: flex;
			justify-content: space-around;
		}

		.main-sprite,
		.secondary-sprite{
			width:85%;
			height:85%;
			left:4px;
			top:4px;
		}

	}

	.switch-window .health,
	.switch-sidebar .health{
		width:100%;
		height:2px;
		background-color:#31e8b7;
		position:absolute;
		bottom:3px;
		left:5px;
		width:75%;

		&[color="yellow"]{
			background:#eabd49;
		}

		&[color="red"]{
			background:#e11414;
		}
	}

	.animate-message{
		position: absolute;
		width: 100%;
		text-align: center;
		top: 37%;
		opacity:0;
		pointer-events: none;
		z-index:25;

		@include transition(all, 0.25s, ease, 0.25s);

		.text{
			margin: 0 auto;
			display: inline-block;
			background: rgba(0,0,0,0.65);
			color: #eee;
			padding: 10px;
			border-radius: 12px;
			font-size: 18px;
		}
	}

	.countdown{
		width:100%;
		height:100%;
		position:absolute;
		z-index:20;
		top:0;
		left:0;
		display:none;
		justify-content: center;
		align-items: center;
		pointer-events: none;

		.text{
			font-size:300px;
			font-weight:bold;
			text-transform: uppercase;
			color:#eee;

		}

		&.animate .text{
			font-size:72px;
			@include transition(all, 0.9s, ease, 0s);
		}
	}

	// Phases

	&[phase="suspend_charged_shield"]{
		.timer{
			display:table;
		}

		.shield-window{
			opacity: 1;
			pointer-events: all;
		}

		.controls .move-bars,
		.controls .move-labels{
			display:none;
		}

		.controls .button-stack,
		.controls .move-bar,
		.controls .switch-btn,
		.switch-sidebar{
			display:none !important;
		}

		.team-indicator{
			display:none;
		}

		.scene{
			@include transform(scale(0.8));
		}
	}

	&[phase="suspend_charged_no_shields"]{
		.animate-message{
			opacity:1;
		}

		.controls .move-bars{
			display:none;
		}

		.controls .button-stack,
		.controls .move-bar,
		.controls .switch-btn,
		.controls .move-labels,
		.switch-sidebar{
			display:none !important;
		}

		.team-indicator{
			display:none;
		}

		.scene{
			@include transform(scale(0.8));
		}
	}

	&[phase="suspend_charged_attack"]{
		.timer{
			display:table;
		}

		.animate-message{
			opacity:1;
		}

		.charge-window{
			opacity: 1;
			pointer-events: all;
		}

		.controls .move-bars,
		.controls .move-labels{
			display:none;
		}

		.controls .button-stack,
		.controls .move-bar,
		.switch-sidebar{
			display:none !important;
		}

		.team-indicator{
			display:none;
		}

		.scene{
			@include transform(scale(1.1));
		}
	}

	&[phase="suspend_switch_self"]{
		.timer{
			display:table;
		}

		.switch-window{
			bottom:0;
			pointer-events: all;
		}

		.controls .button-stack,
		.controls .move-bar,
		.controls .move-labels,
		.switch-sidebar{
			display:none !important;
		}
	}

	&[phase="suspend_switch"]{
		.animate-message{
			opacity:1;
		}

		.controls .button-stack,
		.controls .move-bar,
		.switch-sidebar{
			display:none !important;
		}
	}

	&[phase="animating"]{
		.animate-message{
			opacity:1;
		}

		.team-indicator{
			display:none;
		}

		.controls .button-stack{
			display:none !important;
		}
	}

	&[phase="countdown"]{
		.top, .controls{
			display:none !important;
		}

		.countdown{
			display:flex;
		}
	}

	&[phase="game_over_screen"]{
		display:inline-block;

		.end-screen-container{
			bottom:-10px;
		}
	}

	&[phase="game_paused"]{
		.countdown{
			display:flex;
		}
	}

	&[mode="single"]{
		.button.next-round{
			display:none;
		}
	}

}

.team-select .pokemon-container{

	.sprite-container{
		opacity:0.25;
		overflow:hidden;
	}

	.name{
		padding:5px;
		border-radius:12px;
		margin-top:5px;
		max-width:100px;
		font-weight:bold;
	}

	.name, .cp{
		text-align:center;
	}

	.pokemon.active .sprite-container{
		opacity:1;
	}
}

.team-select{
	display:none;

	.pokemon-container{
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.pokemon{
			width:100px;
			margin-bottom:10px;

			.number{
				width:100%;
				height:100%;
				position:absolute;
				top:0;
				left:0;
				background:#0088a6;
				color:#eee;
				font-weight: bold;
				display:none;
			}

			&.selected{
				.number{
					display:block;
					opacity: 0.9;
					font-size: 40px;
					text-align: center;
					padding-top: 5px;
				}
			}
		}
	}

	.self, .opponent{
		background:rgba(255,255,255,0.75);
		border-radius:12px;
		padding: 10px 0;

		h3{
			margin-top:0;
		}

		.pokemon{
			cursor:pointer;
		}
	}

	.lets-go-btn{
		display:none;
	}

	a.return-to-setup{
		background: rgba(255, 255, 255, 0.75);
	    color: #000;
	    border-radius: 8px;
	    font-size: 14px;
	    padding: 4px 12px;
	    margin-bottom: 10px;
	    display: inline-block;
	}
}

/* END SCREEN */

.end-screen-container{
	@include transition(all, 0.5s, ease, 0s);

	position:absolute;
	z-index:50;
	width:100%;
	bottom:-150%;

	.end-screen{
		width:90%;
		margin: 0 auto;
		padding-bottom: 20px;
		min-height:450px;
		box-shadow: #000 0px 0px 15px;
		background: rgba(255,255,255,.95);
	}

	.buttons{
		display: flex;

		.button{
			font-size:14px;
		}
	}

	.tabs{
		display:flex;

		a.tab{
			display: block;
			width: 33%;
			text-align: center;
			text-decoration: none;
			font-weight: bold;
			text-transform: uppercase;
			font-size: 14px;
			padding: 5px;

			&[href="damage"]{
				color:#ed4f34;

				&.active{
					background:#ed4f34;
					color:#eee;
				}
			}

			&[href="shields"]{
				color:#cb28c1;

				&.active{
					background:#cb28c1;
					color:#eee;
				}
			}

			&[href="energy"]{
				color:#aecb28;

				&.active{
					background:#aecb28;
					color:#eee;
				}
			}
		}
	}

	h3.result{
		margin:0;
	}

	.tab-section{
		display:none;
	}

	.tab-content-container{
		height: 300px;
		overflow-y: scroll;
		margin-top:10px;
	}

	p.description{
		font-size:12px;

		&:first-of-type{
			margin-top:0;
		}
	}

	.damage{
		h3{
			font-size:14px;
			margin: 0;
			text-align: left;

			&.center{
				text-align: center;
			}
		}

		.damage-section{
			padding:10px;
			border-radius:12px;
			background:#eee;
			position:relative;

			.avg-line{
				height: 100%;
				width: 0px;
				border: 1px dotted rgba(0,0,0,.5);
				position: absolute;
				top: 0;
				left: 49.75%;
			}
		}

		.avg-label{
			display:inline-block;
			font-size:12px;
		}
	}

	.pokemon-entry{
		display:flex;
		margin-bottom:15px;

		&:last-of-type{
			margin-bottom:0;
		}

		.name{
			text-align: left;
			font-size:12px;
		}

		.poke-icon{
			width:25%;
		}

		.damage-container{
			width:75%;
			display:flex;

			.damage-bar,
			.shield-bar{
				width:100%;
				height:12px;
				border-radius:12px;
				position: relative;
				z-index:5px;
			}

			.damage-bar{
				background: url('../img/train/damage-bar-bg.jpg');
   		 		background-position: -40px 0;
			}

			.shield-bar{
				background: url(../img/train/shield-bar-bg.jpg);
				background-size: 200%;
				background-position: 70% 0;
				margin-left:2px;
			}
		}
	}

	.stats-table{
		width:100%;
	}
}

/* POKEMON SPRITES */
.sprite-container{
	width:36px;
	height:36px;
	border:1px solid #0088a6;
	border-radius:12px;
	padding:10px;
	background:#333;
	margin: 0 auto;
	position:relative;
}

.main-sprite,
.secondary-sprite,
.white-sprite{
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;
	background-size:600% 300%;
}

.main-sprite{
	background-image:url('../img/train/substitute-sprite-main.png');
}

.secondary-sprite{
	background-image:url('../img/train/substitute-sprite-secondary.png');
}

.white-sprite{
	@include transition(opacity, .75s, ease, 0s);
	background-image:url('../img/train/substitute-sprite-white.png');
	background-size:100%;
	background-repeat: no-repeat;
	opacity:0;
	z-index:25;
}

.pokemon[type-1="bug"] .main-sprite,
.pokemon[type-2="bug"] .secondary-sprite{
	background-position: 0 0;
}

.pokemon[type-1="dark"] .main-sprite,
.pokemon[type-2="dark"] .secondary-sprite{
	background-position: -100% 0;
}

.pokemon[type-1="dragon"] .main-sprite,
.pokemon[type-2="dragon"] .secondary-sprite{
	background-position: -200% 0;
}

.pokemon[type-1="electric"] .main-sprite,
.pokemon[type-2="electric"] .secondary-sprite{
	background-position: -300% 0;
}

.pokemon[type-1="fairy"] .main-sprite,
.pokemon[type-2="fairy"] .secondary-sprite{
	background-position: -400% 0;
}

.pokemon[type-1="fighting"] .main-sprite,
.pokemon[type-2="fighting"] .secondary-sprite{
	background-position: -500% 0;
}

.pokemon[type-1="fire"] .main-sprite,
.pokemon[type-2="fire"] .secondary-sprite{
	background-position: 0% -100%;
}

.pokemon[type-1="flying"] .main-sprite,
.pokemon[type-2="flying"] .secondary-sprite{
	background-position: -100% -100%;
}

.pokemon[type-1="ghost"] .main-sprite,
.pokemon[type-2="ghost"] .secondary-sprite{
	background-position: -200% -100%;
}

.pokemon[type-1="grass"] .main-sprite,
.pokemon[type-2="grass"] .secondary-sprite{
	background-position: -300% -100%;
}

.pokemon[type-1="ground"] .main-sprite,
.pokemon[type-2="ground"] .secondary-sprite{
	background-position: -400% -100%;
}

.pokemon[type-1="ice"] .main-sprite,
.pokemon[type-2="ice"] .secondary-sprite{
	background-position: -500% -100%;
}

.pokemon[type-1="normal"] .main-sprite,
.pokemon[type-2="normal"] .secondary-sprite{
	background-position: 0% -200%;
}

.pokemon[type-1="poison"] .main-sprite,
.pokemon[type-2="poison"] .secondary-sprite{
	background-position: -100% -200%;
}

.pokemon[type-1="psychic"] .main-sprite,
.pokemon[type-2="psychic"] .secondary-sprite{
	background-position: -200% -200%;
}

.pokemon[type-1="rock"] .main-sprite,
.pokemon[type-2="rock"] .secondary-sprite{
	background-position: -300% -200%;
}

.pokemon[type-1="steel"] .main-sprite,
.pokemon[type-2="steel"] .secondary-sprite{
	background-position: -400% -200%;
}

.pokemon[type-1="water"] .main-sprite,
.pokemon[type-2="water"] .secondary-sprite{
	background-position: -500% -200%;
}

/* ANIMATIONS */

.battle-window .scene .pokemon-container{

	&[cooldown="500"]{
		@include transition(all, 0.25s, ease-out, 0s);

		.fast-move-circle{
			@include transition(all, 0.25s, ease-out, 0s);
		}
	}

	&[cooldown="1000"]{
		@include transition(all, 0.75s, ease-out, 0s);

		.fast-move-circle{
			@include transition(all, 0.5s, ease-out, 0s);
		}
	}

	&[cooldown="1500"]{
		@include transition(all, 1.25s, ease-out, 0s);

		.fast-move-circle{
			@include transition(all, 1s, ease-out, 0s);
		}
	}

	&[cooldown="2000"]{
		@include transition(all, 1.75s, ease-out, 0s);

		.fast-move-circle{
			@include transition(all, 1.5s, ease-out, 0s);
		}
	}

	&[cooldown="2500"]{
		@include transition(all, 2.25s, ease-out, 0s);

		.fast-move-circle{
			@include transition(all, 2s, ease-out, 0s);
		}
	}

	&.animate-fast{
		@include transition(all, 0.25s, ease-out, 0s);

		&.self{
			bottom:31%;
			left:21%;
		}

		&.opponent{
			bottom:51%;
			right:26%;
		}

		.fast-move-circle{
			@include transition(all, 0s, ease-out, 0s);
			width:20px;
			height:20px;
			opacity:1;
		}
	}

	&.animate-switch{
		.pokemon{
			@include transform(scale(0, 0));

			.white-sprite{
				opacity: 1;
			}

			.shadow{
				display:none;
			}
		}

		.hp, .name, .messages{
			display:none;
		}
	}

}

.move-bar .bar{
	&[type="bug"]{ background-position-x: 0px !important; }
	&[type="dark"]{ background-position-x: -200px !important; }
	&[type="dragon"]{ background-position-x: -400px !important; }
	&[type="electric"]{ background-position-x: -600px !important; }
	&[type="fairy"]{ background-position-x: -800px !important; }
	&[type="fighting"]{ background-position-x: -1000px !important; }
	&[type="fire"]{ background-position-x: -1200px !important; }
	&[type="flying"]{ background-position-x: -1400px !important; }
	&[type="ghost"]{ background-position-x: -1600px !important; }
	&[type="grass"]{ background-position-x: -1800px !important; }
	&[type="ground"]{ background-position-x: -2000px !important; }
	&[type="ice"]{ background-position-x: -2200px !important; }
	&[type="normal"]{ background-position-x: -2400px !important; }
	&[type="poison"]{ background-position-x: -2600px !important; }
	&[type="psychic"]{ background-position-x: -2800px !important; }
	&[type="rock"]{ background-position-x: -3000px !important; }
	&[type="steel"]{ background-position-x: -3200px !important; }
	&[type="water"]{ background-position-x: -3400px !important; }
}

@media only screen and (max-width: 720px) {
	.train > .poke{
		width:49%;
	}

	.team-select{

		&.self, &.opponent{
			padding: 5px 0;
		}

		h3.center{
			margin:5px 0;
			font-size:16px;
		}

		h4{
			margin-bottom:0;
		}

		.roster .pokemon{
			.sprite-container{
				width:25px;
				height:25px;
			}

			.cp{
				font-size:12px;
			}

			.name{
				margin-top:0;
				font-size:14px;
				line-height:14px;
			}
		}

		.number{
			padding-top:2px !important;
		}
	}

}

@media only screen and (max-width: 350px) {
	.move-labels .label{
		display:none !important;
	}
}
