<?php
// Detect if we're running on Render or locally
if (isset($_SERVER['RENDER'])) {
    // Production on Render
    $WEB_ROOT = '/';
    $WEB_HOST = 'https://' . $_SERVER['HTTP_HOST'] . $WEB_ROOT;

    // Database configuration for Render PostgreSQL
    $DB_NAME = $_ENV['DATABASE_URL'] ?? 'pvpoke_training';
    $DB_HOST = $_ENV['DB_HOST'] ?? 'localhost';
    $DB_USER = $_ENV['DB_USER'] ?? 'pvpoke_user';
    $DB_PASS = $_ENV['DB_PASS'] ?? '';
} else {
    // Local development
    $WEB_ROOT = '/pvpoke/src/';
    $WEB_HOST = 'http://' . $_SERVER['HTTP_HOST'] . $WEB_ROOT;

    $DB_NAME = 'pvpoke_training';
    $DB_HOST = 'localhost';
    $DB_USER = 'root';
    $DB_PASS = '';
}

$UA = '';
$GOOGLE_AD_CLIENT = '';
?>
