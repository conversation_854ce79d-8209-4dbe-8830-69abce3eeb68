// JavaScript Document

var InterfaceMaster = (function () {
    var instance;

    function createInstance() {


        var object = new interfaceObject();

		function interfaceObject(){

			var self = this;
			var data;
			var gm = GameMaster.getInstance();
			var jumpToPoke = false;
			var limitedPokemon = [];
			var context = "rankings"; // Used for internal reference
			var battle = new Battle();
			var customRankingInterface;
			var metaGroup = [];
			var metaGroupData = [];
			var csv = '';
			var showMoveCounts = false;
			var rankingDisplayInterval;

			// Show move counts if previously set
			if(window.localStorage.getItem("rankingsShowMoveCounts") == "true"){
				$(".check.move-counts").addClass("on");
				$(".rankings-container").toggleClass("show-move-counts");
				showMoveCounts = true;
			}

			// The scenario generated by the rankings,
			// This really should be embedded in the ranking data but this is a quick fix for now
			var scenario = {
				slug: "custom",
				shields: [1,1],
				energy: [0,0]
			};

			var scenarios = [];

			this.context = "rankings"; // Used for external reference

			this.init = function(){
				scenarios = GameMaster.getInstance().data.rankingScenarios;

				if(! get){
					this.displayRankings("overall","1500","all");
				} else{
					this.loadGetData();
				}

				$(".format-select").on("change", selectFormat);
				$(".league-select").on("change", selectLeague);
				$(".category-select").on("change", selectCategory);
				$("body").on("click", ".check", checkBox);
				$("body").on("click", ".check.limited", toggleLimitedPokemon);
				$("body").on("click", ".check.xl", toggleXLPokemon);
				$("body").on("click", ".continentals .check", toggleSlots);
				$("body").on("click", ".detail-section .trait-info, .detail-section .traits > div", openTraitPopup);
				$("body").on("click", ".detail-tab-nav a", toggleDetailTab);
				$("body").on("click", ".detail-section a.show-move-stats", toggleMoveStats);
				$("body").on("click", ".check.move-counts", toggleMoveCounts);
				$("body").on("click", ".detail-section.similar-pokemon a", jumpToSimilarPokemon);
				$("body").on("click", ".moveset.fast .move-detail-template", recalculateMoveCounts);
				$("body").on("click", "button.ranking-compare", addPokemonToCompare);

				pokeSearch.setBattle(battle);

				window.addEventListener('popstate', function(e) {
					get = e.state;
					self.loadGetData();
				});
			};

			// Grabs ranking data from the Game Master

			this.displayRankings = function(category, league, cup){

				var gm = GameMaster.getInstance();

				clearInterval(rankingDisplayInterval);
				$(".rankings-container").html('');
				$(".loading").show();

				var format = gm.getFormat(cup, league);

				if(format && format.rules){
					$("a.format-rules").show();
				} else{
					$("a.format-rules").hide();
				}

				// Force 1500 if not general

				if((cup == "premier")&&(league == 1500)){
					league = 10000;

					$(".league-select option[value=\"10000\"]").prop("selected","selected");
				}

				if((cup == "classic")&&(league != 10000)){
					league = 10000;

					$(".league-select option[value=\"10000\"]").prop("selected","selected");
				}

				if(cup == "little"){
					league = 500;

					$(".league-select option[value=\"500\"]").prop("selected","selected");
				}

				if(cup == "littlejungle"){
					$(".little-jungle").show();
				} else{
					$(".little-jungle").hide();
				}

				battle.setCup(cup);
				battle.setCP(league);

				if(! battle.getCup().levelCap){
					battle.setLevelCap(50);
				}

				if(battle.getCup().link){
					$(".description.link").show();
					$(".description.link a").attr("href", battle.getCup().link);
					$(".description.link a").html(battle.getCup().link);
				} else{
					$(".description.link").hide();
				}

				// Check ranking details settings to show ranking details in one page or tabs

				if(settings.rankingDetails == "tabs"){
					$(".rankings-container").addClass("detail-tabs-on");
				}

				/* This timeout allows the interface to display the loading message before
				being thrown into the data loading loop */

				setTimeout(function(){
					gm.loadRankingData(self, category, league, cup);
				}, 50);

			}

			// Displays the grabbed data. Showoff.

			this.displayRankingData = function(rankings){

				var gm = GameMaster.getInstance();

				data = rankings;

				// Load meta group
				if(context != "custom"){
					var metaKey = $(".format-select option:selected").attr("meta-group");

					if(! gm.groups[metaKey]){
						runningResults = true;
						gm.loadGroupData(self, metaKey, data);
						return false;
					} else{
						metaGroupData = gm.groups[metaKey];
					}
				}

				// Sort rankings by selected sort method
				var sort = $(".category-select option:selected").attr("sort");

				switch(sort){
					case "score":
						data.sort((a,b) => (a.score > b.score) ? -1 : ((b.score > a.score) ? 1 : 0));
						$(".league-select-container > .ranking-header.right").html("Score");
						break;

					case "statproduct":
						data.sort((a,b) => (a.stats.product > b.stats.product) ? -1 : ((b.stats.product > a.stats.product) ? 1 : 0));
						$(".league-select-container > .ranking-header.right").html("Stat Product");
						break;

					case "attack":
						data.sort((a,b) => (a.stats.atk > b.stats.atk) ? -1 : ((b.stats.atk > a.stats.atk) ? 1 : 0));
						$(".league-select-container > .ranking-header.right").html("Attack");
						break;

					case "defense":
						data.sort((a,b) => (a.stats.def > b.stats.def) ? -1 : ((b.stats.def > a.stats.def) ? 1 : 0));
						$(".league-select-container > .ranking-header.right").html("Defense");
						break;

					case "stamina":
						data.sort((a,b) => (a.stats.hp > b.stats.hp) ? -1 : ((b.stats.hp > a.stats.hp) ? 1 : 0));
						$(".league-select-container > .ranking-header.right").html("Stamina");
						break;
				}

				// Pass this along to the custom ranking interface to fill in movesets
				if(context == "custom"){
					customRankingInterface.importMovesetsFromRankings(data);
				}

				// Show any restrictions
				var cup = battle.getCup().name;
				$(".limited").hide();
				limitedPokemon = [];

				if((gm.getCupById(cup))&&(gm.getCupById(cup).restrictedPokemon)){
					$(".limited").show();
					$(".check.limited").addClass("on");

					limitedPokemon = gm.getCupById(cup).restrictedPokemon;
				}

				if(battle.getCup().slots){
					$(".continentals").show();
				} else{
					$(".continentals").hide();
				}

				$(".section.white > .rankings-container").html('');

				// Initialize csv data

				csv = 'Pokemon,Score,Dex,Type 1,Type 2,Attack,Defense,Stamina,Stat Product,Level,CP,Fast Move,Charged Move 1,Charged Move 2,Charged Move 1 Count,Charged Move 2 Count,Buddy Distance,Charged Move Cost\n';


				// Create an element for each ranked Pokemon

				metaGroup = [];


				$(".loading").hide();

				var i = 0;
				var rankingDisplayIncrement = 15;

				if(settings.performanceMode){
					rankingDisplayIncrement = 5;
				}

				rankingDisplayInterval = setInterval(function(){
					for(var index = i; index < rankings.length && index < i + rankingDisplayIncrement; index++){
						try {
							self.displayRankingEntry(rankings[index], index);
						} catch{
							console.error(rankings[index].speciesId + " could not be displayed");
						}

					}

					i = index;

					if(i >= rankings.length){
						clearInterval(rankingDisplayInterval);
						self.completeRankingDisplay();
					}
				}, i);
			}

			this.displayRankingEntry = function(r, index){
				var pokemon = new Pokemon(r.speciesId, 0, battle);

				pokemon.initialize(true);
				pokemon.selectMove("fast", r.moveset[0]);
				pokemon.selectMove("charged", r.moveset[1], 0);

				if(r.moveset.length > 2){
					pokemon.selectMove("charged", r.moveset[2],1);
				} else{
					pokemon.selectMove("charged", "none", 1);
				}

				if(! pokemon.speciesId){
					return;
				}

				// Construct meta group from ranked Pokemon
				if((index < 100)&&(context == "custom")){
					metaGroup.push(pokemon);
				}

				if(context != "custom"){
					for(var n = 0; n < metaGroupData.length; n++){
						if(metaGroupData[n].speciesId == pokemon.speciesId){
							pokemon.score = r.score;
							metaGroup.push(pokemon);
							break;
						}
					}
				}

				// Get names of of ranking moves

				var moveNameStr = "";

				// Put together the recommended moveset string
				for(var n = 0; n < r.moveset.length; n++){
					if(n == 0){
						for(var j = 0; j < pokemon.fastMovePool.length; j++){
							if(r.moveset[n] == pokemon.fastMovePool[j].moveId){
								moveNameStr += pokemon.fastMovePool[j].displayName;

								moveNameStr += "<span class=\"count fast\">"+(pokemon.fastMovePool[j].cooldown / 500)+"</span>";
								break;
							}
						}
					} else{
						for(var j = 0; j < pokemon.chargedMovePool.length; j++){
							if(r.moveset[n] == pokemon.chargedMovePool[j].moveId){
								moveNameStr += pokemon.chargedMovePool[j].displayName;

								var moveCounts = Pokemon.calculateMoveCounts(pokemon.fastMove, pokemon.chargedMovePool[j]);
								var moveCount = moveCounts[0];

								if(moveCounts[0] > moveCounts[1]){
									moveCount+="-";
								}

								if(moveCounts[2] < moveCounts[1] && moveCounts[1] == moveCounts[0]){
									moveCount+=".";
								}

								moveNameStr += "<span class=\"count\">"+moveCount+"</span>";
								break;
							}
						}
					}

					if(n < r.moveset.length - 1){
						moveNameStr += ", "
					}
				}

				// Is this the best way to add HTML content? I'm gonna go with no here. But does it work? Yes!
				var $el = $("<div class=\"rank typed-ranking " + pokemon.types[0] + "\" type-1=\""+pokemon.types[0]+"\" type-2=\""+pokemon.types[1]+"\" data=\""+pokemon.speciesId+"\">" +
					"<div class=\"expand-label\"></div>" +
					"<div class=\"pokemon-info\">" +
						"<div class=\"name-container\">" +
							"<span class=\"number\">#"+(index+1)+"</span>" +
							"<span class=\"name\">"+pokemon.speciesName+"</span>" +
							"<div class=\"moves\">"+moveNameStr+"</div>" +
						"</div>" +
						"<div class=\"type-container\"></div>" +
					"</div>" +
					"<div class=\"rating-container\">" +
						"<div class=\"rating score-rating\">"+r.score+"</div>" +
						"<div class=\"clear\"></div>" +
					"</div>" +
					"<div class=\"details\"></div>" +
				"</div>");

				for(var i = 0; i < pokemon.types.length; i++){

					var typeStr = pokemon.types[i].charAt(0).toUpperCase() + pokemon.types[i].slice(1);
					if(pokemon.types[i] != "none"){
						$el.find(".type-container").append("<div class=\"type-info "+pokemon.types[i]+"\">"+typeStr+"</div>");
					}
				}

				var sort = $(".category-select option:selected").attr("sort");

				switch(sort){
					case "statproduct":
						$el.find(".rating").html(r.stats.product);
						break;

					case "attack":
						$el.find(".rating").html(r.stats.atk);
						break;

					case "defense":
						$el.find(".rating").html(r.stats.def);
						break;

					case "stamina":
						$el.find(".rating").html(r.stats.hp);
						break;
				}

				if(showMoveCounts){
					$el.find(".count").removeClass("hide");
				}

				// Match both shadow and non shadow versions on restricted list

				if(limitedPokemon.indexOf(pokemon.speciesId.replace("_shadow", "")) > -1){
					$el.addClass("limited-rank");
				}

				// Show points if applicable
				if(battle.getCup().tierRules){
					var points = gm.getPokemonTier(pokemon.speciesId, battle.getCup());
					var ptStr = " pts";

					if(points == 1){
						ptStr = " pt";
					}

					$el.find(".moves").prepend("<span class=\"cliffhanger-points\">"+points+ptStr+"</span>");
				}

				if(battle.getCup().slots){
					let slotNumbers = [];

					for(var n = 0; n < battle.getCup().slots.length; n++){
						let slot = battle.getCup().slots[n];

						if((slot.pokemon.indexOf(pokemon.speciesId) > -1)||(slot.pokemon.indexOf(pokemon.speciesId.replace("_shadow","")) > -1)){
							slotNumbers.push(n+1);
						}
					}

					for(let i = 0; i < slotNumbers.length; i++){
						$el.attr("slot"+slotNumbers[i], '');
					}
				}

				$(".section.white > .rankings-container").append($el);

				if(settings.performanceMode){
					var list = pokeSearch.getSearchList();
					if(list.indexOf(pokemon.speciesId) > -1 || list.length == 0){
						$el.show();
					} else{
						$el.hide();
					}
				}

				// Determine XL category

				if(pokemon.needsXLCandy()){
					$el.attr("needs-xls", "true");
					$el.find(".name").append("<span class=\"xl-info-icon\"></span>");
				}


				// For metas with species specific slots, show slot label

				if(battle.getCup().slots){
					let includedSlots = pokemon.getSlotNumbers(battle.getCup());

					if(includedSlots.length > 0){
						let slotStr = "<b>Slot " + includedSlots.join(", ") + ".</b>&nbsp;";
						$el.find(".moves").prepend(slotStr);
					}
				}

				var chargedMove2Name = '';
				var chargedMove1Count = Math.ceil(pokemon.chargedMoves[0].energy / pokemon.fastMove.energyGain);
				var chargedMove2Count = 0;

				if(pokemon.chargedMoves.length > 1){
					chargedMove2Name = pokemon.chargedMoves[1].name;
					chargedMove2Count = Math.ceil(pokemon.chargedMoves[1].energy / pokemon.fastMove.energyGain);
				}

				$el.on("click", selectPokemon);

				csv += pokemon.speciesName+','+r.score+','+pokemon.dex+','+pokemon.types[0]+','+pokemon.types[1]+','+(Math.round(pokemon.stats.atk*10)/10)+','+(Math.round(pokemon.stats.def*10)/10)+','+Math.round(pokemon.stats.hp)+','+Math.round(pokemon.stats.atk*pokemon.stats.def*pokemon.stats.hp)+','+pokemon.level+','+pokemon.cp+','+pokemon.fastMove.name+','+pokemon.chargedMoves[0].name+','+chargedMove2Name+','+chargedMove1Count+','+chargedMove2Count+','+pokemon.buddyDistance+','+pokemon.thirdMoveCost+'\n';


				// If a Pokemon has been selected via URL parameters, jump to it

				if(jumpToPoke && jumpToPoke == pokemon.speciesId){
					setTimeout(function(){
						self.jumpToPokemon(jumpToPoke);

						jumpToPoke = false;
					}, 50);
				}
			}

			this.completeRankingDisplay = function(){

				// Update download link with new data
				const cupName = battle.getCup().name;
				const category = $(".category-select option:selected").val() || scenario.slug;
				const cpString = `cp${battle.getCP()}`

				const fileNameParts = [cpString,  cupName,  category];

				// var filename = battle.getCup().name + " Rankings.csv";
				var filedata = '';

				if(context == "custom"){
					fileNameParts.unshift('custom');
				}

				const filename = 		fileNameParts.concat( 'rankings.csv').filter(Boolean).join('_');

				if (!csv.match(/^data:text\/csv/i)) {
					filedata = [csv];
					filedata = new Blob(filedata, { type: 'text/csv'});
				}

				$(".button.download-csv").attr("href", window.URL.createObjectURL(filedata));
				$(".button.download-csv").attr("download", filename);


				// If search string exists, process it

				if($(".poke-search[context='ranking-search']").first().val() != ''){
					$(".poke-search[context='ranking-search']").first().trigger("keyup");
				}

				if(context == "custom"){
					customRankingInterface.setMetaGroup(metaGroup);
				}
			}

			// Given JSON of get parameters, load these settings

			this.loadGetData = function(){

				if(! get){
					return false;
				}

				// Cycle through parameters and set them

				for(var key in get){
					if(get.hasOwnProperty(key)){

						var val = get[key];

						// Process each type of parameter

						switch(key){

							// Don't process default values so data doesn't needlessly reload

							case "cp":
								battle.setCP(val);
								break;

							case "cat":
								// Select by sort first if it exists
								if($(".category-select option[sort=\""+val+"\"]").length > 0){
									$(".category-select option[sort=\""+val+"\"]").first().prop("selected", "selected");
								} else{
									$(".category-select option[value=\""+val+"\"]").first().prop("selected", "selected");
								}

								// Show relevant description

								var category = $(".category-select option:selected").val();
								var sort = $(".category-select option:selected").attr("sort");

								$(".description").hide();
								if(sort == "score"){
									$(".description."+category).show();
								} else{
									$(".description."+sort).show();
								}
								break;

							case "cup":
								battle.setCup(val);
								break;

							case "p":
								// We have to wait for the data to load before we can jump to a Pokemon, so store this for later
								jumpToPoke = val;
								break;

						}
					}
				}

				// Load data via existing change function

				var cp = battle.getCP();
				var category = $(".category-select option:selected").val();
				var cup = battle.getCup().name;

				$(".format-select option[value=\""+cp+"\"][cup=\""+cup+"\"]").prop("selected","selected");

				self.displayRankings(category, cp, cup, null);
			}

			// When the view state changes, push to browser history so it can be navigated forward or back

			this.pushHistoryState = function(cup, cp, category, speciesId){
				if(context == "custom"){
					return false;
				}

				if(cup == "little"){
					cp = 500;
				}

				// Use the sort method for the non-score based categories
				var sort = $(".category-select option:selected").attr("sort");

				if(sort != "score"){
					category = sort;
				}

				var rankStr = "rankings/"+cup+"/"+cp+"/"+category+"/"

				if(speciesId){
					rankStr += speciesId+"/";
				}

				var url = webRoot+rankStr;

				var data = {cup: cup, cp: cp, cat: category, p: speciesId };

				window.history.pushState(data, "Rankings", url);

				// Send Google Analytics pageview
				gtag('event', 'Lookup', {
				  'category': 'Rankings',
				  'speciesId' : speciesId
				});

				gtag('event', 'page_view', {
				  page_title: speciesId + ' ' + document.title,
				  page_location: (host+rankStr),
				  pageview_type: 'virtual'
				});
			}

			// Set a context so this interface can add or skip functionality

			this.setContext = function(value){
				context = value;

				if(context == "custom"){
					$(".league-select option[value='500']").show();
				}
			}

			// Set a ranking scenario to be displayed

			this.setScenario = function(value){
				scenario = value;
			}

			// Link the custom ranking interface so these two can talk

			this.setCustomRankingInterface = function(obj){
				customRankingInterface = obj;
			}

			// Return a custom group of the top 100 Pokemon

			this.getMetaGroup = function(){
				return metaGroup;
			}

			// Open and scroll to a specified Pokemon

			this.jumpToPokemon = function(id){
				var $el = $(".rank[data=\""+id+"\"]")
				$el.show();

				// Close all ranking details
				$(".rankings-container > .rank").removeClass("selected");
				$(".rankings-container > .rank > .details").removeClass("active");

				$(".rankings-container").scrollTop(0); // This is dumb but it works for reasons I do not understand

				// Scroll to element
				var elTop = $el.position().top;
				var containerTop = $(".rankings-container").position().top;
				var gotoTop = elTop - containerTop - 20;

				$("html, body").animate({ scrollTop: 350}, 500);
				$(".rankings-container").scrollTop(gotoTop);

				$el.trigger("click");
			}

			// Event handler for changing the league select

			function selectLeague(e){
				var cp = battle.getCP();
				var levelCap = parseInt($(".league-select option:selected").attr("level-cap"));

				if(context != "custom"){
					var category = $(".category-select option:selected").val();
					var cup = battle.getCup().name;

					if(cp == 500){
						$(".format-select option[cup=\"little\"]").prop("selected","selected");
						cup = "little";
					} else if(cup == "little"){
						$(".format-select option[cup=\"all\"]").prop("selected","selected");
						cup = "all";
					}

					battle.setCup(cup);

					self.displayRankings(category, cp, cup);
					self.pushHistoryState(cup, cp, category, null);
				} else{
					cp = $(".league-select option:selected").val();
				}

				battle.setCP(cp);
				battle.setLevelCap(levelCap);
			}

			// Event handler for changing the cup select

			function selectFormat(e){
				var cp = $(".format-select option:selected").val();
				var cup = $(".format-select option:selected").attr("cup");
				var category = $(".category-select option:selected").val();
				var sort = $(".category-select option:selected").attr("sort");

				if(! category){
					category = "overall";
				}

				if(cup == "custom"){
					window.location.href = webRoot+'custom-rankings/';
					return;
				}

				self.displayRankings(category, cp, cup);
				self.pushHistoryState(cup, cp, category, null);
			}


			// Event handler for selecting ranking category

			function selectCategory(e){

				var cp = $(".format-select option:selected").val();
				var category = $(".category-select option:selected").val();
				var sort = $(".category-select option:selected").attr("sort");
				var scenarioStr = $(".category-select option:selected").attr("scenario");
				var cup = $(".format-select option:selected").attr("cup");

				$(".description").hide();
				if(sort == "score"){
					$(".description."+category).show();
				} else{
					$(".description."+sort).show();
				}

				// Set the corresponding scenario

				for(var i = 0; i < scenarios.length; i++){
					if(scenarios[i].slug == scenarioStr){
						scenario = scenarios[i];
						break;
					}
				}

				self.displayRankings(category, cp, cup);

				self.pushHistoryState(cup, cp, category, null);
			}

			// Event handler clicking on a Pokemon item, load detail data

			function selectPokemon(e){

				if($(e.target).parents(".details").length > 0 || $(e.target).is(".details")){
					return;
				}

				var cup = $(".format-select option:selected").attr("cup");
				var category = $(".category-select option:selected").val();
				var $rank = $(this).closest(".rank");

				$rank.toggleClass("selected");
				$rank.find(".details").toggleClass("active");

				var speciesId = $rank.attr("data");

				// Only execute if this was a direct action and not loaded from URL parameters, otherwise pushes infinite states when the user navigates back
				if($rank.hasClass("selected")){
					if(get && get.p == speciesId){
						get.p = false; // Unset this so URL properly sets if reselected
					} else{
						self.pushHistoryState(cup, battle.getCP(), category, speciesId);
					}
				}

				var $details = $rank.find(".details");

				if($details.html() != ''){
					return;
				}

				var r = data.find(r => r.speciesId == speciesId)
				var pokemon = new Pokemon(r.speciesId, 0, battle);
				pokemon.initialize(battle.getCP(), "gamemaster");
				pokemon.selectMove("fast", r.moveset[0]);
				pokemon.selectMove("charged", r.moveset[1], 0);

				if(r.moveset.length > 2){
					pokemon.selectMove("charged", r.moveset[2],1);
				} else{
					pokemon.selectMove("charged", "none", 1);
				}

				var pokeMoveStr = pokemon.generateURLMoveStr();

				// Display move data

				var fastMoves = pokemon.fastMovePool;
				var chargedMoves = pokemon.chargedMovePool;

				for(var j = 0; j < fastMoves.length; j++){
					fastMoves[j].uses = 0;

					for(var n = 0; n < r.moves.fastMoves.length; n++){
						var move = r.moves.fastMoves[n];

						if(move.moveId == fastMoves[j].moveId){
							fastMoves[j].uses = move.uses;
						}
					}
				}

				for(var j = 0; j < chargedMoves.length; j++){
					chargedMoves[j].uses = 0;

					for(var n = 0; n < r.moves.chargedMoves.length; n++){
						var move = r.moves.chargedMoves[n];

						if(move.moveId == chargedMoves[j].moveId){
							chargedMoves[j].uses = move.uses;
						}
					}
				}

				fastMoves.sort((a,b) => (a.uses > b.uses) ? -1 : ((b.uses > a.uses) ? 1 : 0));
				chargedMoves.sort((a,b) => (a.uses > b.uses) ? -1 : ((b.uses > a.uses) ? 1 : 0));

				// Buckle up, this is gonna get messy. This is the main detail HTML.

				$details.append($(".details-template.hide").html());


				// Display Pokemon stats
				var overall = Math.round((pokemon.stats.hp * pokemon.stats.atk * pokemon.stats.def) / 1000);

				$details.find(".stat-details .stat-row .value").eq(0).html(Math.floor(pokemon.stats.atk*10)/10);
				$details.find(".stat-details .stat-row .value").eq(1).html(Math.floor(pokemon.stats.def*10)/10);
				$details.find(".stat-details .stat-row .value").eq(2).html(pokemon.stats.hp);
				$details.find(".stat-details .stat-row .value").eq(3).html(overall);

				// Display bars
				$details.find(".stat-details .stat-row .bar:first-of-type").addClass(pokemon.types[0]);

				/*
				* Explanation for the following section: Instead of displaying stats proportionally,
				* I'm shifting the baseline so the scale goes from e.g. 100-350 instead of 0-350.
				* This has the effect of making high or low stats appear more extreme, giving the
				* stat bars a stronger visual impact.
				*
				* For example, Haunter is one of the squishiest Great League Pokemon. Its stat
				* product (1400) is half that Bastiodon (2800). If Bastiodon represents a full stat
				* bar, representing Haunter as half of that length doesn't truly convey the difference
				* between the two. Thus, the adjustments here make for a more striking scale.
				*
				* Thanks for coming to by TED talk.
				*/

				var statCeiling = 230;
				var statSubtraction = 80;
				var statProductCeiling = 4500;
				var statProductSubstraction = 4500;

				if(battle.getCP() == 500){
					statProductCeiling = 700;
					statProductSubstraction = 100;
					statCeiling = 150;
					statSubtraction = 20;
				} else if(battle.getCP() == 1500){
					statProductCeiling = 2000;
					statProductSubstraction = 1000;
					statCeiling = 250;
					statSubtraction = 50;
				} else if(battle.getCP() == 2500){
					statProductCeiling = 3000;
					statProductSubstraction = 2600;
					statCeiling = 300;
					statSubtraction = 50;
				}

				$details.find(".stat-details .stat-row .bar:first-of-type").eq(0).css("width", (( (pokemon.stats.atk - statSubtraction) / statCeiling) * 100) + "%");
				$details.find(".stat-details .stat-row .bar:first-of-type").eq(1).css("width", (((pokemon.stats.def - statSubtraction) / statCeiling) * 100) + "%");
				$details.find(".stat-details .stat-row .bar:first-of-type").eq(2).css("width", (((pokemon.stats.hp - statSubtraction) / statCeiling) * 100) + "%");
				$details.find(".stat-details .stat-row .bar:first-of-type").eq(3).css("width", (((overall - statProductSubstraction) / statProductCeiling) * 100) + "%");

				if(pokemon.hasTag("shadow")){
					$details.find(".shadow-mult").show();
				}

				// Need to calculate percentages

				var totalFastUses = 0;

				for(var n = 0; n < fastMoves.length; n++){
					totalFastUses += fastMoves[n].uses;
				}

				// Display fast moves

				for(var n = 0; n < fastMoves.length; n++){
					var percentStr = (Math.floor((fastMoves[n].uses / totalFastUses) * 1000) / 10) + "%";
					var displayWidth = (Math.floor((fastMoves[n].uses / totalFastUses) * 1000) / 20);

					if(displayWidth < 14){
						displayWidth = "14%";
					} else{
						displayWidth = displayWidth + "%";
					}

					var $moveDetails = $details.find(".moveset.fast .move-detail-template.hide").clone();
					$moveDetails.removeClass("hide");

					// Contextualize the move archetype for this Pokemon
					var archetype = fastMoves[n].archetype;
					var archetypeClass = 'general'; // For CSS

					if(fastMoves[n].archetype == "Fast Charge"){
						archetypeClass = "spam";
					} else if(fastMoves[n].archetype == "Heavy Damage"){
						archetypeClass = "nuke";
					} else if(fastMoves[n].archetype == "Multipurpose"){
   						archetypeClass = "high-energy";
					} else if(fastMoves[n].archetype == "Low Quality"){
						archetypeClass = "low-quality";
					}

					$moveDetails.addClass(fastMoves[n].type);
					$moveDetails.find(".name").html(fastMoves[n].displayName);
					$moveDetails.find(".archetype .name").html(archetype);
					$moveDetails.find(".archetype .icon").addClass(archetypeClass);
					$moveDetails.find(".dpt .value").html(Math.round( ((fastMoves[n].power * fastMoves[n].stab * pokemon.shadowAtkMult) / (fastMoves[n].cooldown / 500)) * 100) / 100);
					$moveDetails.find(".ept .value").html(Math.round( (fastMoves[n].energyGain / (fastMoves[n].cooldown / 500)) * 100) / 100);
					$moveDetails.find(".turns .value").html( fastMoves[n].cooldown / 500 );
					$moveDetails.attr("data", fastMoves[n].moveId);

					// Highlight this move if it's in the recommended moveset

					if(fastMoves[n] == pokemon.fastMove){
						$moveDetails.addClass("selected");
					}

					$details.find(".moveset.fast").append($moveDetails);
				}

				// Display charged moves

				var totalChargedUses = 0;

				for(var n = 0; n < chargedMoves.length; n++){
					totalChargedUses += chargedMoves[n].uses;
				}

				for(var n = 0; n < chargedMoves.length; n++){
					percentStr = (Math.floor((chargedMoves[n].uses / totalChargedUses) * 1000) / 10) + "%";
					displayWidth = (Math.floor((chargedMoves[n].uses / totalChargedUses) * 1000) / 20);

					if(displayWidth < 14){
						displayWidth = "14%";
					} else{
						displayWidth = displayWidth + "%";
					}

					var $moveDetails = $details.find(".moveset.charged .move-detail-template.hide").clone();
					$moveDetails.removeClass("hide");

					// Contextualize the move archetype for this Pokemon
					var archetype = chargedMoves[n].archetype;
					var archetypeClass = 'general'; // For CSS

					if(chargedMoves[n].stab == 1){
						var descriptor = "Coverage";

						if(chargedMoves[n].type == "normal"){
							descriptor = "Neutral"
						}

						switch(archetype){
							case "General":
								archetype = descriptor;
								break;

							case "High Energy":
								if(descriptor == "Coverage"){
									archetype = "High Energy Coverage";
								}
								break;

							case "Spam/Bait":
								archetype = descriptor + " Spam/Bait";
								break;

							case "Nuke":
								archetype = descriptor + " Nuke";
								break;

						}
					}

					if(chargedMoves[n].archetype.indexOf("Boost") > -1){
					  archetypeClass = "boost";
				  	} else if(chargedMoves[n].archetype.indexOf("Self-Debuff") > -1){
						archetypeClass = "self-debuff";
					} else if(chargedMoves[n].archetype.indexOf("Spam") > -1){
						archetypeClass = "spam";
					} else if(chargedMoves[n].archetype.indexOf("High Energy") > -1){
						archetypeClass = "high-energy";
					} else if(chargedMoves[n].archetype.indexOf("Nuke") > -1){
						archetypeClass = "nuke";
					} else if(chargedMoves[n].archetype.indexOf("Debuff") > -1){
						archetypeClass = "debuff";
					}

					if(chargedMoves[n].archetype == "Debuff Spam/Bait"){
						archetypeClass = "debuff";
					}

					$moveDetails.addClass(chargedMoves[n].type);
					$moveDetails.find(".name").html(chargedMoves[n].displayName);
					$moveDetails.find(".archetype .name").html(archetype);
					$moveDetails.find(".archetype .icon").addClass(archetypeClass);
					$moveDetails.find(".damage .value").html(Math.round((chargedMoves[n].power * chargedMoves[n].stab * pokemon.shadowAtkMult) * 100) / 100);
					$moveDetails.find(".energy .value").html(chargedMoves[n].energy);
					$moveDetails.find(".dpe .value").html( Math.round( ((chargedMoves[n].power * chargedMoves[n].stab * pokemon.shadowAtkMult) / chargedMoves[n].energy) * 100) / 100);
					$moveDetails.attr("data", chargedMoves[n].moveId);

					if(chargedMoves[n].buffs){
						$moveDetails.find(".move-effect").html(gm.getStatusEffectString(chargedMoves[n]));
					}

					// Add move counts
					var moveCounts = Pokemon.calculateMoveCounts(pokemon.fastMove, chargedMoves[n]);

					$moveDetails.find(".move-count span").html(moveCounts[0] + " - " + moveCounts[1] + " - " + moveCounts[2] + " - " + moveCounts[3]);

					// Highlight this move if it's in the recommended moveset

					for(var j = 0; j < pokemon.chargedMoves.length; j++){
						if(chargedMoves[n] == pokemon.chargedMoves[j]){
							$moveDetails.addClass("selected");
						}
					}

					$details.find(".moveset.charged").append($moveDetails);
				}

				// Helper variables for displaying matchups and link URL

				var cp = battle.getCP();

				if(context == "custom"){
					category = context;
				}

				// Display key matchups

				for(var n = 0; n < r.matchups.length; n++){
					var m = r.matchups[n];
					var opponent = new Pokemon(m.opponent, 1, battle);
					opponent.initialize(battle.getCP(), "gamemaster");
					opponent.selectRecommendedMoveset(category);

					var battleLink = host+"battle/"+battle.getCP(true)+"/"+pokemon.aliasId+"/"+opponent.aliasId+"/"+scenario.shields[0]+""+scenario.shields[1]+"/"+pokeMoveStr+"/"+opponent.generateURLMoveStr()+"/";

					// Append energy settings
					battleLink += pokemon.stats.hp + "-" + opponent.stats.hp + "/";

					if(scenario.energy[0] == 0){
						battleLink += "0";
					} else{
						var fastMoveCount = Math.floor((scenario.energy[0] * 500) / pokemon.fastMove.cooldown);
						if(fastMoveCount == 0){
							fastMoveCount = 1;
						}

						battleLink += Math.min(pokemon.fastMove.energyGain * fastMoveCount, 100);
					}

					battleLink += "-";

					if(scenario.energy[1] == 0){
						battleLink += "0";
					} else{
						battleLink += Math.min(opponent.fastMove.energyGain * (Math.floor((scenario.energy[1] * 500) / opponent.fastMove.cooldown)), 100);
					}

					battleLink += "/";

					var $item = $("<div class=\"rank " + opponent.types[0] + "\"><div class=\"name-container\"><span class=\"number\">#"+(n+1)+"</span><span class=\"name\">"+opponent.speciesName+"</span></div><div class=\"rating-container\"><a target=\"_blank\" href=\""+battleLink+"\" class=\"rating\"><span></span>"+m.rating+"<i></i></a><div class=\"clear\"></div></div>");
					var color = battle.getRatingColor(m.rating);

					$item.find(".rating").addClass(battle.getRatingClass(m.rating));
					$item.find(".rating").css("background", "rgb("+color[0]+","+color[1]+","+color[2]+")");

					$details.find(".matchups").append($item);
				}

				// Display top counters

				for(var n = 0; n < r.counters.length; n++){
					var c = r.counters[n];
					var opponent = new Pokemon(c.opponent, 1, battle);
					opponent.initialize(battle.getCP(), "gamemaster");
					opponent.selectRecommendedMoveset(category);
					var battleLink = host+"battle/"+battle.getCP(true)+"/"+pokemon.aliasId+"/"+opponent.aliasId+"/"+scenario.shields[0]+""+scenario.shields[1]+"/"+pokeMoveStr+"/"+opponent.generateURLMoveStr()+"/";

					// Append energy settings
					battleLink += pokemon.stats.hp + "-" + opponent.stats.hp + "/";

					if(scenario.energy[0] == 0){
						battleLink += "0";
					} else{
						battleLink += Math.min(pokemon.fastMove.energyGain * (Math.floor((scenario.energy[0] * 500) / pokemon.fastMove.cooldown)), 100);
					}

					battleLink += "-";

					if(scenario.energy[1] == 0){
						battleLink += "0";
					} else{
						battleLink += Math.min(opponent.fastMove.energyGain * (Math.floor((scenario.energy[1] * 500) / opponent.fastMove.cooldown)), 100);
					}

					var $item = $("<div class=\"rank " + opponent.types[0] + "\"><div class=\"name-container\"><span class=\"number\">#"+(n+1)+"</span><span class=\"name\">"+opponent.speciesName+"</span></div><div class=\"rating-container\"><a target=\"_blank\" href=\""+battleLink+"\" class=\"rating\"><span></span>"+c.rating+"</span><i></i></a><div class=\"clear\"></div></div>");
					var color = battle.getRatingColor(c.rating);

					$item.find(".rating").addClass(battle.getRatingClass(c.rating));
					$item.find(".rating").css("background", "rgb("+color[0]+","+color[1]+","+color[2]+")");

					$details.find(".counters").append($item);
				}

				// Display Pokemon's type information

				$details.find(".typing .type").eq(0).addClass(pokemon.types[0]);
				$details.find(".typing .type").eq(0).html(pokemon.types[0]);

				if(pokemon.types[1] != "none"){
					$details.find(".typing .type").eq(1).addClass(pokemon.types[1]);
					$details.find(".typing .type").eq(1).html(pokemon.types[1]);
				} else{
					$details.find(".typing .rating-container").eq(1).hide();
				}

				// Display weaknesses and resistances
				var effectivenessArr = []; // First we need to push the values into a sortable array, the original is key indexed (essentially an object)
				for(var type in pokemon.typeEffectiveness) {
					if (pokemon.typeEffectiveness.hasOwnProperty(type)) {
						effectivenessArr.push({
	 					   type: type,
	 					   val: pokemon.typeEffectiveness[type],
						   displayVal: Math.floor(pokemon.typeEffectiveness[type] * 1000) / 1000
	 				   });
					}
				}

				//effectivenessArr.sort((a,b) => (a.val > b.val) ? -1 : ((b.val > a.val) ? 1 : 0));

				var weaknessArr = effectivenessArr.filter(type => type.displayVal > 1);
				var resistanceArr = effectivenessArr.filter(type => type.displayVal < 1);

				weaknessArr.sort((a,b) => (a.val > b.val) ? -1 : ((b.val > a.val) ? 1 : 0));
				resistanceArr.sort((a,b) => (a.val > b.val) ? 1 : ((b.val > a.val) ? -1 : 0));

				for(var i = 0; i < weaknessArr.length; i++){
					$details.find(".detail-section .weaknesses").append("<div class=\"type "+weaknessArr[i].type+"\"><div class=\"multiplier\">x"+weaknessArr[i].displayVal+"</div><div>"+weaknessArr[i].type+"</div></div>");
				}

				for(var i = 0; i < resistanceArr.length; i++){
					$details.find(".detail-section .resistances").append("<div class=\"type "+resistanceArr[i].type+"\"><div class=\"multiplier\">x"+resistanceArr[i].displayVal+"</div><div>"+resistanceArr[i].type+"</div></div>");
				}

				// Display Pokemon's stat ranges

				/*var statRanges = {
					atk: {
						min: pokemon.generateIVCombinations("atk", -1, 1)[0].atk,
						max: pokemon.generateIVCombinations("atk", 1, 1)[0].atk,
					},
					def: {
						min: pokemon.generateIVCombinations("def", -1, 1)[0].def,
						max: pokemon.generateIVCombinations("def", 1, 1)[0].def,
					},
					hp: {
						min: pokemon.generateIVCombinations("hp", -1, 1)[0].hp,
						max: pokemon.generateIVCombinations("hp", 1, 1)[0].hp,
					}
				};*/

				// Show share link
				var cup = battle.getCup().name;
				var cupName = $(".format-select option:selected").html();

				if(cup == "classic"){
					cup = "all";
				}

				var link = host + "rankings/"+cup+"/"+cp+"/"+category+"/"+pokemon.aliasId+"/";

				$details.find(".share-link input").val(link);

				// Add multi-battle link
				if(context != "custom"){
					var multiBattleLink = host+"battle/multi/"+battle.getCP(true)+"/"+cup+"/"+pokemon.aliasId+"/"+scenario.shields[0]+""+scenario.shields[1]+"/"+pokeMoveStr+"/2-1/";

					// Append energy settings
					multiBattleLink += pokemon.stats.hp + "/";

					if(scenario.energy[0] == 0){
						multiBattleLink += "0";
					} else{
						multiBattleLink += Math.min(pokemon.fastMove.energyGain * (Math.floor((scenario.energy[0] * 500) / pokemon.fastMove.cooldown)), 100);
					}

					multiBattleLink += "/";

					$details.find(".multi-battle-link a").attr("href", multiBattleLink);
					$details.find(".multi-battle-link a").html(pokemon.speciesName+" vs. " + cupName);
					$details.find(".multi-battle-link .name").html(pokemon.speciesName+"'s");
				} else{
					$details.find(".share-link").remove();
					$details.find(".multi-battle-link").remove();
				}

				var scores;
				var key = battle.getCup().name + "overall" + battle.getCP();

				if(r.scores){
					scores = r.scores;
				} else{
					// If overall rankings have been loaded, grab overall scores from there

					if(gm.rankings[key]){
						for(var i = 0; i < gm.rankings[key].length; i++){
							if(gm.rankings[key][i].speciesId == pokemon.speciesId){
								scores = gm.rankings[key][i].scores;
								break;
							}
						}
					}
				}

				if(scores && category == "overall" && context != "custom"){
					// Display rating hexagon
					drawRatingHexagon($rank, r);
				} else{
					$details.find(".detail-section.performance").remove();
					$details.find(".detail-section.poke-stats").css("width", "100%");
					$details.find(".detail-section.poke-stats").css("float", "none");
				}

				// Display buddy distance and second move cost
				var moveCostStr = pokemon.thirdMoveCost.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","); // Ugh regex

				$details.find(".buddy-distance").html(pokemon.buddyDistance + " km");
				$details.find(".third-move-cost").html(moveCostStr + " Stardust");

				// Display Pokemon's highest IV's

				var rank1Combo = pokemon.generateIVCombinations("overall", 1, 1)[0];
				var highestLevelCombo = pokemon.generateIVCombinations("level", 1, 1)[0];
				$details.find(".stat-row.rank-1 .value").html("Lvl " + rank1Combo.level + " " + rank1Combo.ivs.atk + "/" + rank1Combo.ivs.def + "/" + rank1Combo.ivs.hp + "<br>CP " + rank1Combo.cp);

				var level41CP = pokemon.calculateCP(0.795300006866455, 15, 15, 15);

				pokemon.autoLevel = true;
				pokemon.setIV("atk", 15);
				pokemon.setIV("def", 15);
				pokemon.setIV("hp", 15);

				var hundoLevel = pokemon.level; // Getting the lowest possible level

				// Can this Pokemon get close the CP limit at level 41?

				if(pokemon.hasTag("xs")){
					$details.find(".xl-info-container").addClass("xs");
				} else	if(level41CP >= battle.getCP() - 20){

					// This Pokemon can get close to the CP limit at level 41
					if(rank1Combo.level <= 41){
						$details.find(".xl-info-container").addClass("regular");
					} else{
						$details.find(".xl-info-container").addClass("mixed");
					}
				} else{
					if(pokemon.levelCap == 40){
						$details.find(".xl-info-container").addClass("unavailable");
					} else{
						if(level41CP >= battle.getCP() - 75){
							$details.find(".xl-info-container").addClass("mixed");
						} else{
							$details.find(".xl-info-container").addClass("xl");
						}

					}

				}

				// Display level range

				if(rank1Combo.level > hundoLevel){
					$details.find(".stat-row.level .value").html(hundoLevel + " - " + highestLevelCombo.level);
				} else{
					$details.find(".stat-row.level .value").html(rank1Combo.level);
				}

				// Display Pokemon traits
				pokemon.isCustom = false;
				pokemon.initialize(battle.getCP()); // Reset to default IVs
				var traits = pokemon.generateTraits();

				for(var i = 0; i < traits.pros.length; i++){
					$details.find(".traits").append("<div class=\"pro\" title=\""+traits.pros[i].desc+"\">+ "+traits.pros[i].trait+"</div>");
				}

				for(var i = 0; i < traits.cons.length; i++){
					$details.find(".traits").append("<div class=\"con\" title=\""+traits.cons[i].desc+"\">- "+traits.cons[i].trait+"</div>");
				}

				// Display partner pokemon
				if(gm.rankings[key] && context != "custom" && ! settings.performanceMode){

					setTimeout(function(){
						var partnerPokemonMax = 5;
						var partnerPokemonCount = 0;
						var usedPartnerSpecies = [];
						var partnerPokemon = generatePartnerPokemon(pokemon, r.counters);

						for(var i = 0; i < partnerPokemon.length; i++){

							if((usedPartnerSpecies.indexOf(partnerPokemon[i].dex) == -1)&&(partnerPokemon[i].dex != pokemon.dex)){

								// Build team builder link

								var teamURL = host + "team-builder/" + battle.getCup().name + "/" + battle.getCP(true) + "/" + pokemon.speciesId + "-m-" + pokeMoveStr + "," + partnerPokemon[i].speciesId + "-m-" + partnerPokemon[i].generateURLMoveStr();

								$details.find(".partner-pokemon .list").append("<a href=\""+teamURL+"\" target=\"blank\" class=\""+partnerPokemon[i].types[0]+"\" data=\""+partnerPokemon[i].speciesId+"\">"+partnerPokemon[i].speciesName+" &rarr;</a>");
								usedPartnerSpecies.push(partnerPokemon[i].dex);
								partnerPokemonCount++;
							}

							if(partnerPokemonCount >= partnerPokemonMax){
								break;
							}
						}
					}, 100);

				} else{
					$details.find(".partner-pokemon").remove();
				}

				// Display similar pokemon
				if(gm.rankings[key] && context != "custom" && ! settings.performanceMode){
					// Delay this to avoid tying up the interface
					setTimeout(function(){
						var similarPokemon = pokemon.generateSimilarPokemon(traits);

						for(var i = 0; i < 8; i++){
							$details.find(".similar-pokemon .list").append("<a href=\"#\" class=\""+similarPokemon[i].types[0]+"\" data=\""+similarPokemon[i].speciesId+"\">"+similarPokemon[i].speciesName+"</a>");
						}
					}, 50);
				} else{
					$details.find(".similar-pokemon").remove();
				}
			}

			function drawRatingHexagon($rank, source, compare){
				// This is really dumb but we're pulling the type color out of the background gradient
				var bgArr = $rank.css("background").split("linear-gradient(");

				if(bgArr.length < 2){
					bgArr = $rank.css("background-image").split("linear-gradient(");
				}

				bgArr = bgArr[1].split(" 30%");
				var bgStr = bgArr[0]

				var $details = $rank.find(".details");
				var scores = source.scores;

				hexagon.init($details.find(".hexagon"), 80);
                hexagon.draw([
					Math.max( (scores[0] - 30) / 70 , .05 ),
					Math.max( (scores[2] - 30) / 70 , .05 ),
					Math.max( (scores[3] - 30) / 70 , .05 ),
					Math.max( (scores[1] - 30) / 70 , .05 ),
					Math.max( (scores[5] - 30) / 70 , .05 ),
					Math.max( (scores[4] - 30) / 70 , .05 ),
				], bgStr, false);

				// Draw comparison scores
				if(compare){
					var compareStr = "rgba(0,0,0,.6)";

					hexagon.draw([
						Math.max( (compare.scores[0] - 30) / 70 , .05 ),
						Math.max( (compare.scores[2] - 30) / 70 , .05 ),
						Math.max( (compare.scores[3] - 30) / 70 , .05 ),
						Math.max( (compare.scores[1] - 30) / 70 , .05 ),
						Math.max( (compare.scores[5] - 30) / 70 , .05 ),
						Math.max( (compare.scores[4] - 30) / 70 , .05 ),
					], compareStr, true);
				}

				for(var i = 0; i < scores.length; i++){
					$details.find(".hexagon-container .value").eq(i).html(scores[i]);
				}
			}

			// Use the team ranker to generate a list of partner Pokemon
			function generatePartnerPokemon(pokemon, counters){
				var ranker = RankerMaster.getInstance();

				// First, get ratings of source Pokemon vs meta

				ranker.setTargets(metaGroup);

				var sourceRankings = ranker.rank([pokemon], battle.getCP(true), battle.getCup()).rankings;

				sourceRankings.sort((a,b) => (a.speciesId > b.speciesId) ? 1 : ((b.speciesId > a.speciesId) ? -1 : 0));

				// Check matchups for each eligible Pokemon
				for(var i = 0; i < metaGroup.length; i++){
					var partner = metaGroup[i];
					var partnerScore = 0;
					var partnerRankings = ranker.rank([partner], battle.getCP(true), battle.getCup()).rankings;
					partnerRankings.sort((a,b) => (a.speciesId > b.speciesId) ? 1 : ((b.speciesId > a.speciesId) ? -1 : 0));

					for(var n = 0; n < partnerRankings.length; n++){
						var score = Math.min(800, sourceRankings[n].opRating + partnerRankings[n].opRating);
						var matchupWeight = 1;

						// If this Pokemon is a key loss, weigh this matchup more
						for(var j = 0; j < counters.length; j++){
							if(counters[j].opponent == partnerRankings[n].speciesId){
								matchupWeight = 3;
							}
						}

						partnerScore += score * matchupWeight;
					}

					partner.partnerScore = (0.75 * partnerScore) + (0.25 * partnerScore * (partner.score / 100)); // Multiply by overall ranking to show higher ranked Pokemon first

					// Exclude Megas from the partners of other Megas
					if(pokemon.hasTag("mega") && partner.hasTag("mega")){
						partner.partnerScore = 0;
					}

					// Exclude partners in point based metas which exceed the point limit
					if(battle.getCup().tierRules){
						var remainingPoints = battle.getCup().tierRules.max - gm.getPokemonTier(pokemon.speciesId, battle.getCup());
						if(gm.getPokemonTier(partner.speciesId, battle.getCup()) > remainingPoints){
							partner.partnerScore = 0;
						}
					}
				}

				metaGroup.sort((a,b) => (a.partnerScore > b.partnerScore) ? -1 : ((b.partnerScore > a.partnerScore) ? 1 : 0));

				return metaGroup;
			}

			// When clicking a Fast Move in a Pokemon's list, recalculate move counts based on that Fast Move

			function recalculateMoveCounts(e){
				var $template = $(e.target).closest(".move-detail-template")
				var $movesTab = $(e.target).closest(".detail-tab");
				var fastMove = gm.getMoveById($template.attr("data"));

				// For each listed Charged Move, update the move counts
				$movesTab.find(".moveset.charged .move-detail-template:not(.hide)").each(function(index, value){
					var chargedMove = gm.getMoveById($(this).attr("data"));
					var moveCounts = Pokemon.calculateMoveCounts(fastMove, chargedMove);
					$(this).find(".move-count span").html(moveCounts[0] + " - " + moveCounts[1] + " - " + moveCounts[2] + " - " + moveCounts[3]);
				});

				// Display this Fast Move as the selected move
				$movesTab.find(".moveset.fast .move-detail-template").removeClass("selected");
				$template.addClass("selected");
			}

			// Turn checkboxes on and off

			function checkBox(e){
				$(this).toggleClass("on");
				$(this).trigger("stateChange");
			}

			// Toggle the limited Pokemon from the Rankings

			function toggleLimitedPokemon(e){
				for(var i = 0; i < limitedPokemon.length; i++){
					$(".rank[data='"+limitedPokemon[i]+"']").toggleClass("hide");
					$(".rank[data='"+limitedPokemon[i]+"_shadow']").toggleClass("hide");
				}
			}

			// Toggle move count info

			function toggleMoveCounts(e){
				$(".rankings-container").toggleClass("show-move-counts");

				showMoveCounts = (! showMoveCounts);

				window.localStorage.setItem("rankingsShowMoveCounts", showMoveCounts)
			}

			// Toggle XL Pokemon from the Rankings

			function toggleXLPokemon(e){
				$(".rankings-container > .rank").each(function(index, value){
					if($(this).attr("needs-xls") == "true"){
						$(this).toggleClass("hide");
					}
				});
			}

			// Show or hide cup slots

			function toggleSlots(e){
				var selectedSlots = [];
				var cup = battle.getCup();

				if(! cup?.slots){
					return;
				}

				$(".continentals .check").each(function(index, value){
					if($(this).hasClass("on")){
						selectedSlots.push(parseInt($(this).attr("value")));
					}
				});

				if(selectedSlots.length == 0){
					$(".rank").removeClass("hide");
				} else if(selectedSlots.length > 0){
					$(".rank").addClass("hide");

					for(var i = 1; i <= cup.slots.length; i++){
						if(selectedSlots.includes(i)){
							$(".rank[slot"+i+"]").removeClass("hide");
						}
					}
				}
			}

			// Display trait details in the modal window

			function openTraitPopup(e){
				e.preventDefault();

				var $rank = $(e.target).closest(".rank")
				var $traits = $rank.find(".traits")

				modalWindow("Traits", $(".trait-modal"));

				$(".modal .name").first().html($rank.find(".name-container .name").first().html().replace("XL",""));

				$traits.find("div").each(function(index, value){
					$(".modal .traits").append("<div class=\""+$(this).attr("class")+"\"><div class=\"name\">"+$(this).html()+"</div><div class=\"desc\">"+$(this).attr("title")+"</div></div>");
				});
			}

			// Jump to a Pokemon entry from the similar Pokemon section

			function jumpToSimilarPokemon(e){
				e.preventDefault();

				self.jumpToPokemon($(e.target).attr("data"));
			}

			// Toggle move stats in the ranking details

			function toggleMoveStats(e){
				e.preventDefault();

				var $rank = $(e.target).closest(".rank")
				$(e.target).toggleClass("on");
				$rank.find(".moveset").toggleClass("show-stats");
			}

			// Switch to a different detail tab

			function toggleDetailTab(e){
				e.preventDefault();

				var $rank = $(e.target).closest(".rank")
				var tab = $(e.target).closest("a").attr("tab");

				// Display selected tab nav
				$rank.find(".detail-tab-nav a").removeClass("active");
				$(e.target).closest("a").addClass("active");

				// Display selected tab items
				$rank.find(".detail-tab").hide();
				$rank.find(".detail-tab[tab=\""+tab+"\"]").css("display", "flex");
			}

			// Select a Pokemon to compare an existing Pokemon's ranking data in the radar chart

			function addPokemonToCompare(e){
				var $rank = $(e.target).closest(".rank");

				modalWindow("Select Pokemon", $(".poke.single").first());

				pokeSelector = new PokeSelect($(".modal .poke"), 1);
				pokeSelector.setContext("modalrankcompare");
				pokeSelector.init(gm.data.pokemon, battle, data);
				pokeSelector.removePokemonFromOptions([{speciesId: $rank.attr("data")}]);

				$(".modal-content").append("<div class=\"center\"><div class=\"save-poke button\">Compare Pokemon</div></div>");
				$(".modal .poke-search").focus();

				// Add or save a Pokemon in the Pokemon list

				$(".modal .save-poke").on("click", function(e){

					// Make sure something's selected
					if(! pokeSelector){
						return false;
					}

					var pokemon = pokeSelector.getPokemon();

					if(! pokemon){
						return false;
					}

					var originalData = data.filter(r => r.speciesId == $rank.attr("data"))[0];
					var compareData = data.filter(r => r.speciesId == pokemon.speciesId)[0];

					drawRatingHexagon($rank, originalData, compareData);

					$rank.find(".details .ranking-compare").html("Compare: " + pokemon.speciesName);
					$rank.find(".details .ranking-compare").attr("class", "ranking-compare " + pokemon.types[0]);

					// Add comparison numbers
					for(var i = 0; i < 6; i++){
						var diff = Math.round( (originalData.scores[i] - compareData.scores[i]) * 10 ) / 10;

						if(diff >= 0){
							diff = "+" + diff;
						}

						var color = battle.getRatingColor( 500 + (25 * diff));

						$rank.find(".chart-label .comparison").eq(i).css("background", "rgb("+color[0]+","+color[1]+","+color[2]+")");
						$rank.find(".chart-label .comparison").eq(i).html(diff);
					}

					$rank.find(".chart-label .comparison").show();

					closeModalWindow();
				});
			}

			// Open format rules modal

			$("a.format-rules").click(function(e){
				e.preventDefault();

				var format = gm.getFormat(battle.getCup().name, battle.getCP());
				var $modalContent = $("<ul></ul>");

				for(var i = 0; i < format.rules.length; i++){
					$modalContent.append("<li>" + format.rules[i] + "</li>");
				}

				modalWindow(format.title + " Rules", $modalContent);

			});
		};

        return object;
    }

    return {
        getInstance: function () {
            if (!instance) {
                instance = createInstance();
            }
            return instance;
        }
    };
})();
