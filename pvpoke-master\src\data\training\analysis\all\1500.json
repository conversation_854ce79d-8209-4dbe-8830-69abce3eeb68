{"properties": {"lastUpdated": "June 26, 2025", "totalPerformers": 71034, "totalTeams": 23678}, "performers": [{"pokemon": "gligar_shadow FC+AA/Dg", "individualScore": 126.42, "teamScore": 567.27, "games": 516}, {"pokemon": "typhlosion_shadow I+BB/TP", "individualScore": 152.59, "teamScore": 539.01, "games": 377}, {"pokemon": "araquanid BBi+BuB/WP", "individualScore": 118.11, "teamScore": 538.73, "games": 374}, {"pokemon": "golisopod FC+AJ/XS", "individualScore": 134.51, "teamScore": 533.77, "games": 392}, {"pokemon": "scizor_shadow BP+NS/TBl", "individualScore": 179.77, "teamScore": 530.43, "games": 319}, {"pokemon": "dedenne TS+PR/PbC", "individualScore": 130.97, "teamScore": 526.45, "games": 663}, {"pokemon": "tinkaton FW+Bd/PR", "individualScore": 117.85, "teamScore": 522.91, "games": 328}, {"pokemon": "jellicent H+S/SB", "individualScore": 114.8, "teamScore": 519.92, "games": 1284}, {"pokemon": "jumpluff_shadow FW+AA/Acr", "individualScore": 117.95, "teamScore": 515.72, "games": 436}, {"pokemon": "corviknight SdA+IH/SA", "individualScore": 115.63, "teamScore": 510.38, "games": 372}, {"pokemon": "primeape KC+CC/RF", "individualScore": 127.83, "teamScore": 503.42, "games": 748}, {"pokemon": "talonflame I+BrB/Fl", "individualScore": 117.94, "teamScore": 498.52, "games": 924}, {"pokemon": "gastrodon MSl+BS/EP", "individualScore": 119.91, "teamScore": 498.21, "games": 1355}, {"pokemon": "drapion_shadow PSt+AT/Cr", "individualScore": 118.18, "teamScore": 497.87, "games": 2181, "usageTrend": [8.16, 7.37, 10.8, 10.6, 9.06, 10.13, 9.11, 10.67, 9.74, 7.05]}, {"pokemon": "talonflame I+Fl/FmC", "individualScore": 138.45, "teamScore": 497.75, "games": 578}, {"pokemon": "marowak_shadow MSl+BC/RS", "individualScore": 137.45, "teamScore": 497, "games": 1477}, {"pokemon": "dusclops H+IP/ShP", "individualScore": 129.7, "teamScore": 494.75, "games": 1808}, {"pokemon": "diggersby QA+FiP/ScS", "individualScore": 120.59, "teamScore": 494.41, "games": 2511, "usageTrend": [8.54, 5.92, 11.46, 11.28, 10.32, 9.76, 12.35, 13.46, 16.33, 18.37]}, {"pokemon": "cradily BS+GK/RT", "individualScore": 118.97, "teamScore": 494.09, "games": 1348}, {"pokemon": "talonflame I+BrB/FmC", "individualScore": 119.18, "teamScore": 494.04, "games": 610}, {"pokemon": "lapras Psy+IB/SpA", "individualScore": 125.58, "teamScore": 493.85, "games": 1420}, {"pokemon": "clodsire PSt+Eq/SlB", "individualScore": 124.17, "teamScore": 492.9, "games": 1683}, {"pokemon": "furret SkP+Sw/TBl", "individualScore": 118.79, "teamScore": 491.18, "games": 1950}, {"pokemon": "pangoro KC+CC/NS", "individualScore": 139.6, "teamScore": 491.02, "games": 470}, {"pokemon": "steelix TF+BkS/PsF", "individualScore": 106.66, "teamScore": 490.94, "games": 480}, {"pokemon": "dewgong IS+DR/IW", "individualScore": 106.01, "teamScore": 490.59, "games": 1238}, {"pokemon": "mandibuzz Sn+AA/DP", "individualScore": 119.13, "teamScore": 490.36, "games": 720}, {"pokemon": "dunsparce RO+DR/RS", "individualScore": 117.53, "teamScore": 489.78, "games": 1309}, {"pokemon": "blastoise RO+HC/SkB", "individualScore": 113.29, "teamScore": 482.96, "games": 1329}, {"pokemon": "morpeko_full_belly TS+AuW/PsF", "individualScore": 124.71, "teamScore": 482.2, "games": 1812}, {"pokemon": "grumpig Psy+DyP/SB", "individualScore": 109.26, "teamScore": 481.96, "games": 1452}, {"pokemon": "skeledirge I+SB/TSo", "individualScore": 104.56, "teamScore": 481.51, "games": 413}, {"pokemon": "toxapex PJ+Bri/SW", "individualScore": 117.51, "teamScore": 480.78, "games": 1839}, {"pokemon": "azumarill B+IB/PR", "individualScore": 128.39, "teamScore": 480.13, "games": 1622}, {"pokemon": "serperior VW+AA/FP", "individualScore": 116.85, "teamScore": 479.33, "games": 2165, "usageTrend": [10.1, 8.93, 7.18, 7.81, 8.96, 8.05, 7.53, 7.95, 7.7, 6.66]}, {"pokemon": "weezing_galarian FW+BrS/Sl", "individualScore": 121.44, "teamScore": 479.28, "games": 713}, {"pokemon": "jumpluff FW+AA/EB", "individualScore": 122.26, "teamScore": 476.35, "games": 1137}, {"pokemon": "feraligatr_shadow SC+HC/IB", "individualScore": 105.36, "teamScore": 475.88, "games": 2973, "usageTrend": [13.91, 12.83, 11.89, 11.96, 11.48, 14.83, 13.61, 11.66, 12.11, 11.13]}, {"pokemon": "clodsire PSt+Eq/SE", "individualScore": 122.21, "teamScore": 475.2, "games": 2873, "usageTrend": [11.44, 10.04, 12.84, 13.12, 11.33, 9.84, 10.9, 14.04, 14.29, 14.1]}, {"pokemon": "wigglytuff Ch+IW/Sw", "individualScore": 112.68, "teamScore": 474.92, "games": 1291}, {"pokemon": "sandslash_alolan PS+DR/IP", "individualScore": 114.47, "teamScore": 473.03, "games": 1038}, {"pokemon": "feraligatr SC+HC/IB", "individualScore": 111.84, "teamScore": 472.49, "games": 1645}, {"pokemon": "sableye SC+FoP/PG", "individualScore": 129.8, "teamScore": 471.59, "games": 988}, {"pokemon": "malamar Psy+FoP/SP", "individualScore": 133.58, "teamScore": 471.32, "games": 2136, "usageTrend": [8.97, 8.71, 8.09, 7.87, 7.5, 7.45, 8.39, 9.34, 8.72, 7.7]}, {"pokemon": "guzzlord DT+BrS/SlB", "individualScore": 120.23, "teamScore": 463.97, "games": 422}, {"pokemon": "ariados PSt+Lu/TBl", "individualScore": 121.64, "teamScore": 456.95, "games": 373}], "teams": [{"team": "gligar_shadow FC/AA/Dg|drapion_shadow PSt/AT/Cr|lapras Psy/SkB/SpA", "teamScore": 600.7, "games": 46}, {"team": "furret SkP/Sw/TBl|jumpluff_shadow FW/AA/Acr|typhlosion_shadow I/BB/TP", "teamScore": 572.03, "games": 31}, {"team": "dragonite_shadow DB/DC/SP|blastoise RO/HC/SkB|clodsire PSt/Eq/SlB", "teamScore": 565.45, "games": 31}, {"team": "marowak_shadow MSl/BC/RS|clodsire PSt/Eq/SlB|dunsparce RO/DR/RS", "teamScore": 565.38, "games": 53}, {"team": "dunsparce RO/DR/RS|golisopod FC/AJ/XS|talonflame I/BrB/FmC", "teamScore": 564.45, "games": 42}, {"team": "dusclops H/IP/ShP|araquanid BBi/BuB/WP|diggersby QA/FiP/ScS", "teamScore": 558.25, "games": 177}, {"team": "dunsparce RO/DR/RS|feraligatr_shadow SC/HC/IB|talonflame I/BrB/FmC", "teamScore": 540.16, "games": 82}, {"team": "clodsire PSt/Eq/SlB|mandibuzz Sn/AA/DP|primeape KC/CC/RF", "teamScore": 539.87, "games": 31}, {"team": "dedenne TS/PR/PbC|gligar FC/AA/Dg|primeape_shadow KC/CC/RF", "teamScore": 535.45, "games": 33}, {"team": "talonflame I/BrB/Fl|cradily Ac/GK/RT|quagsire MS/AT/SE", "teamScore": 534.72, "games": 32}, {"team": "talonflame I/BrB/Fl|gastrodon MSl/BS/EP|marowak_shadow MSl/BC/RS", "teamScore": 533.5, "games": 46}, {"team": "drapion_shadow PSt/AT/Cr|azumarill B/IB/PR|diggersby QA/FiP/ScS", "teamScore": 533.26, "games": 38}, {"team": "gastrodon MSl/BS/EP|clodsire PSt/Eq/SE|talonflame I/Fl/FmC", "teamScore": 529.79, "games": 48}, {"team": "dusclops H/IP/Po|annihilape C/CC/RF|primeape KC/CC/RF", "teamScore": 507.39, "games": 31}, {"team": "furret SkP/Sw/TBl|grumpig Psy/DyP/SB|primeape KC/CC/RF", "teamScore": 499.4, "games": 40}, {"team": "primeape KC/CC/RF|cradily BS/GK/RT|feraligatr SC/Cr/HC", "teamScore": 492.65, "games": 63}]}