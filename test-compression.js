// Simple test for matrix state compression functionality
// This tests the core compression logic without browser dependencies

// Mock btoa/atob for Node.js environment
if (typeof btoa === 'undefined') {
    global.btoa = function(str) {
        return Buffer.from(str, 'binary').toString('base64');
    };
}

if (typeof atob === 'undefined') {
    global.atob = function(str) {
        return Buffer.from(str, 'base64').toString('binary');
    };
}

// Mock GameMaster
global.GameMaster = {
    getInstance: function() {
        return {
            data: {
                pokemon: [
                    { speciesId: "charizard" },
                    { speciesId: "blastoise" },
                    { speciesId: "venusaur" },
                    { speciesId: "pikachu" }
                ],
                moves: [
                    { moveId: "FIRE_SPIN" },
                    { moveId: "BLAST_BURN" },
                    { moveId: "DRAGON_CLAW" },
                    { moveId: "WATER_GUN" },
                    { moveId: "HYDRO_CANNON" },
                    { moveId: "THUNDER_SHOCK" },
                    { moveId: "THUNDERBOLT" }
                ]
            }
        };
    }
};

// Mock window object
global.window = {
    location: {
        origin: 'https://pvpoke.com',
        pathname: '/battle/'
    }
};

// Load the MatrixStateManager (simplified version for testing)
function MatrixStateManager() {
    var self = this;
    var STATE_VERSION = '1.0';
    
    // Include the compression methods from the implementation
    this.generatePokemonIdMapping = function() {
        var pokemonToId = {};
        var idToPokemon = {};
        var currentId = 1;
        
        if (typeof GameMaster !== 'undefined' && GameMaster.getInstance && GameMaster.getInstance().data) {
            var gm = GameMaster.getInstance();
            if (gm.data && gm.data.pokemon) {
                gm.data.pokemon.forEach(function(pokemon) {
                    if (pokemon.speciesId && !pokemonToId[pokemon.speciesId]) {
                        pokemonToId[pokemon.speciesId] = currentId;
                        idToPokemon[currentId] = pokemon.speciesId;
                        currentId++;
                    }
                });
            }
        }
        
        return {
            pokemonToId: pokemonToId,
            idToPokemon: idToPokemon
        };
    };
    
    this.generateMoveIdMapping = function() {
        var moveToId = {};
        var idToMove = {};
        var currentId = 1;
        
        if (typeof GameMaster !== 'undefined' && GameMaster.getInstance && GameMaster.getInstance().data) {
            var gm = GameMaster.getInstance();
            if (gm.data && gm.data.moves) {
                gm.data.moves.forEach(function(move) {
                    if (move.moveId && !moveToId[move.moveId]) {
                        moveToId[move.moveId] = currentId;
                        idToMove[currentId] = move.moveId;
                        currentId++;
                    }
                });
            }
        }
        
        return {
            moveToId: moveToId,
            idToMove: idToMove
        };
    };
    
    this.encodeBase64 = function(str) {
        try {
            var base64 = btoa(unescape(encodeURIComponent(str)));
            return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        } catch (error) {
            console.error('Failed to encode Base64:', error);
            return '';
        }
    };
    
    this.decodeBase64 = function(base64) {
        try {
            var restored = base64.replace(/-/g, '+').replace(/_/g, '/');
            while (restored.length % 4) {
                restored += '=';
            }
            return decodeURIComponent(escape(atob(restored)));
        } catch (error) {
            console.error('Failed to decode Base64:', error);
            return '';
        }
    };
    
    this.compressPokemon = function(pokemon, pokemonMapping, moveMapping) {
        if (!pokemon || !pokemon.speciesId) {
            return null;
        }
        
        try {
            var compressed = {};
            
            var pokemonId = pokemonMapping.pokemonToId[pokemon.speciesId];
            if (!pokemonId) {
                console.warn('Unknown Pokemon species:', pokemon.speciesId);
                return null;
            }
            compressed.id = pokemonId;
            
            if (pokemon.level && pokemon.level !== 20) {
                compressed.l = pokemon.level;
            }
            
            if (pokemon.ivs && (pokemon.ivs.attack !== 15 || pokemon.ivs.defense !== 15 || pokemon.ivs.hp !== 15)) {
                compressed.i = [pokemon.ivs.attack || 15, pokemon.ivs.defense || 15, pokemon.ivs.hp || 15];
            }
            
            if (pokemon.moves) {
                var moves = [];
                
                if (pokemon.moves.fastMove) {
                    var fastMoveId = moveMapping.moveToId[pokemon.moves.fastMove];
                    if (fastMoveId) {
                        moves.push(fastMoveId);
                    }
                }
                
                if (pokemon.moves.chargedMoves && Array.isArray(pokemon.moves.chargedMoves)) {
                    var chargedMoveIds = pokemon.moves.chargedMoves.map(function(moveName) {
                        return moveMapping.moveToId[moveName];
                    }).filter(function(id) {
                        return id !== undefined;
                    });
                    
                    if (chargedMoveIds.length > 0) {
                        moves.push(chargedMoveIds);
                    }
                }
                
                if (moves.length > 0) {
                    compressed.m = moves;
                }
            }
            
            if (pokemon.nickname) {
                compressed.n = pokemon.nickname;
            }
            
            if (pokemon.shadow) {
                compressed.sh = 1;
            }
            if (pokemon.purified) {
                compressed.p = 1;
            }
            
            return compressed;
        } catch (error) {
            console.error('Failed to compress Pokemon:', error);
            return null;
        }
    };
}

// Run tests
console.log('Testing Matrix State Compression...\n');

var manager = new MatrixStateManager();

// Test 1: Pokemon ID Mapping
console.log('1. Testing Pokemon ID Mapping:');
var pokemonMapping = manager.generatePokemonIdMapping();
console.log('   Pokemon to ID mapping:', pokemonMapping.pokemonToId);
console.log('   ID to Pokemon mapping:', pokemonMapping.idToPokemon);
console.log('   ✓ Generated mappings for', Object.keys(pokemonMapping.pokemonToId).length, 'Pokemon\n');

// Test 2: Move ID Mapping
console.log('2. Testing Move ID Mapping:');
var moveMapping = manager.generateMoveIdMapping();
console.log('   Move to ID mapping:', moveMapping.moveToId);
console.log('   ID to Move mapping:', moveMapping.idToMove);
console.log('   ✓ Generated mappings for', Object.keys(moveMapping.moveToId).length, 'moves\n');

// Test 3: Pokemon Compression
console.log('3. Testing Pokemon Compression:');
var testPokemon = {
    speciesId: "charizard",
    level: 25,
    ivs: { attack: 0, defense: 15, hp: 14 },
    moves: {
        fastMove: "FIRE_SPIN",
        chargedMoves: ["BLAST_BURN", "DRAGON_CLAW"]
    },
    nickname: "Test Char",
    shadow: false
};

console.log('   Original Pokemon:', JSON.stringify(testPokemon, null, 2));
var compressedPokemon = manager.compressPokemon(testPokemon, pokemonMapping, moveMapping);
console.log('   Compressed Pokemon:', JSON.stringify(compressedPokemon));

// Calculate compression ratio
var originalSize = JSON.stringify(testPokemon).length;
var compressedSize = JSON.stringify(compressedPokemon).length;
var compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
console.log('   ✓ Compression ratio:', compressionRatio + '% reduction\n');

// Test 4: Base64 Encoding
console.log('4. Testing Base64 Encoding:');
var testData = { test: "data", number: 123, array: [1, 2, 3] };
var jsonString = JSON.stringify(testData);
var encoded = manager.encodeBase64(jsonString);
var decoded = manager.decodeBase64(encoded);

console.log('   Original:', jsonString);
console.log('   Encoded:', encoded);
console.log('   Decoded:', decoded);
console.log('   ✓ Encoding/decoding', decoded === jsonString ? 'successful' : 'failed');

console.log('\n✓ All compression tests completed successfully!');