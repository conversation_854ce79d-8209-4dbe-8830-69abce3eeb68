{"name": "knockout", "title": "Devon Knockout Cup", "restrictedPicks": 2, "restrictedPokemon": ["gastrodon", "whiscash", "diggersby", "run<PERSON><PERSON>", "golisopod", "araquanid", "tentacruel", "a<PERSON><PERSON>", "charjabug", "vikavolt", "<PERSON><PERSON><PERSON>"], "include": [{"filterType": "id", "name": "Species", "values": ["sandslash", "machamp", "sir<PERSON><PERSON><PERSON>", "hitmontop", "primeape", "poliwrath", "gastrodon", "whiscash", "diggersby", "run<PERSON><PERSON>", "golisopod", "araquanid", "tentacruel", "a<PERSON><PERSON>", "charjabug", "vikavolt", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "escavalier", "politoed", "dewgong", "walrein", "sealeo", "<PERSON>ras", "samu<PERSON>t", "blastoise", "milotic", "gren<PERSON><PERSON>", "castform_rainy", "wailmer", "seaking", "suicune", "qwilfish", "gengar", "haunter", "beedrill", "swalot", "weezing_galarian", "grimer", "muk", "grimer_alolan", "muk_alolan", "drapion", "skuntank", "qwilfish_his<PERSON>an", "over<PERSON><PERSON>l", "seviper", "scolipede", "marshtomp", "seismitoad", "stunfisk", "sanslash", "golurk", "palossand", "piloswine", "marowak", "hippo<PERSON><PERSON>", "rhyperior", "rhydon", "ampha<PERSON>", "heliolisk", "dedenne", "r<PERSON><PERSON>_alolan", "minun", "graveler_alolan", "golem_alolan", "r<PERSON><PERSON>", "minun", "luxray"]}], "exclude": [{"filterType": "tag", "name": "Tag", "values": ["shadow", "mega"]}], "link": "https://discord.com/invite/MtGA34QM5N"}