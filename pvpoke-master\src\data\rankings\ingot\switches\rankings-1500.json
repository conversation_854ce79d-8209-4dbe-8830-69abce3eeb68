[{"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 861, "matchups": [{"opponent": "rotom_heat", "rating": 937, "opRating": 62}, {"opponent": "victini", "rating": 937, "opRating": 62}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 931, "opRating": 68}, {"opponent": "nidoqueen", "rating": 926, "opRating": 73}, {"opponent": "magnezone", "rating": 881, "opRating": 118}], "counters": [{"opponent": "nidoking", "rating": 322}, {"opponent": "<PERSON>rserker", "rating": 341}, {"opponent": "cacturne", "rating": 367}, {"opponent": "obstagoon", "rating": 416}, {"opponent": "talonflame", "rating": 454}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 15915}, {"moveId": "METAL_CLAW", "uses": 9085}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 9100}, {"moveId": "ROCK_SLIDE", "uses": 7427}, {"moveId": "MUDDY_WATER", "uses": 5742}, {"moveId": "FLASH_CANNON", "uses": 2761}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 100}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 863, "matchups": [{"opponent": "stunfisk_galarian", "rating": 803, "opRating": 196}, {"opponent": "magnezone", "rating": 790, "opRating": 209}, {"opponent": "arcanine", "rating": 782, "opRating": 217}, {"opponent": "pyroar", "rating": 773, "opRating": 226}, {"opponent": "armarouge", "rating": 773, "opRating": 226}], "counters": [{"opponent": "tyrunt", "rating": 290}, {"opponent": "electrode_hisuian", "rating": 349}, {"opponent": "litleo", "rating": 356}, {"opponent": "magmar", "rating": 379}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 400}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 12754}, {"moveId": "SHADOW_CLAW", "uses": 7070}, {"moveId": "EMBER", "uses": 5189}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 13142}, {"moveId": "THUNDER_PUNCH", "uses": 5948}, {"moveId": "OVERHEAT", "uses": 2771}, {"moveId": "FIRE_BLAST", "uses": 1611}, {"moveId": "SOLAR_BEAM", "uses": 1531}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 99}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 865, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 861, "opRating": 138}, {"opponent": "litleo", "rating": 848, "opRating": 151}, {"opponent": "typhlosion", "rating": 834, "opRating": 165}, {"opponent": "ninetales", "rating": 834, "opRating": 165}, {"opponent": "rapidash", "rating": 821, "opRating": 178}], "counters": [{"opponent": "golbat", "rating": 285}, {"opponent": "victini", "rating": 286}, {"opponent": "slowbro_galarian", "rating": 286}, {"opponent": "dedenne", "rating": 308}, {"opponent": "nidoqueen", "rating": 320}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 15289}, {"moveId": "EMBER", "uses": 9711}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 9121}, {"moveId": "SCORCHING_SANDS", "uses": 8880}, {"moveId": "FLAMETHROWER", "uses": 2848}, {"moveId": "RETURN", "uses": 2528}, {"moveId": "FIRE_BLAST", "uses": 1551}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 96.1}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 849, "matchups": [{"opponent": "ninetales", "rating": 833, "opRating": 166}, {"opponent": "r<PERSON><PERSON>", "rating": 815, "opRating": 184}, {"opponent": "stunfisk_galarian", "rating": 792, "opRating": 207}, {"opponent": "litleo", "rating": 783, "opRating": 216}, {"opponent": "typhlosion", "rating": 779, "opRating": 220}], "counters": [{"opponent": "tyrunt", "rating": 228}, {"opponent": "magmar", "rating": 236}, {"opponent": "archen", "rating": 317}, {"opponent": "electrode_hisuian", "rating": 349}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 400}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 13189}, {"moveId": "FIRE_SPIN", "uses": 6555}, {"moveId": "EMBER", "uses": 4484}, {"moveId": "LOW_KICK", "uses": 766}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 7799}, {"moveId": "WILD_CHARGE", "uses": 5835}, {"moveId": "FLAME_CHARGE", "uses": 5339}, {"moveId": "SCORCHING_SANDS", "uses": 2955}, {"moveId": "FIRE_BLAST", "uses": 2408}, {"moveId": "HEAT_WAVE", "uses": 722}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "WILD_CHARGE"], "score": 94.9}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 834, "matchups": [{"opponent": "incineroar", "rating": 831, "opRating": 168}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 799, "opRating": 200}, {"opponent": "litleo", "rating": 750, "opRating": 250}, {"opponent": "typhlosion", "rating": 729, "opRating": 270}, {"opponent": "rapidash", "rating": 729, "opRating": 270}], "counters": [{"opponent": "magmar", "rating": 263}, {"opponent": "toxapex", "rating": 296}, {"opponent": "tyrunt", "rating": 325}, {"opponent": "umbreon", "rating": 338}, {"opponent": "ninetales", "rating": 349}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 9500}, {"moveId": "SNARL", "uses": 8047}, {"moveId": "THUNDER_FANG", "uses": 5620}, {"moveId": "BITE", "uses": 1829}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 5173}, {"moveId": "WILD_CHARGE", "uses": 5074}, {"moveId": "FLAMETHROWER", "uses": 4110}, {"moveId": "PSYCHIC_FANGS", "uses": 3843}, {"moveId": "CRUNCH", "uses": 2938}, {"moveId": "BULLDOZE", "uses": 2769}, {"moveId": "FIRE_BLAST", "uses": 1079}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 92.7}, {"speciesId": "litleo", "speciesName": "Litleo", "rating": 842, "matchups": [{"opponent": "victini", "rating": 856, "opRating": 143}, {"opponent": "stunfisk_galarian", "rating": 839, "opRating": 160}, {"opponent": "pyroar", "rating": 804, "opRating": 195}, {"opponent": "armarouge", "rating": 804, "opRating": 195}, {"opponent": "rapidash", "rating": 769, "opRating": 230}], "counters": [{"opponent": "magmar", "rating": 214}, {"opponent": "magby", "rating": 233}, {"opponent": "archen", "rating": 282}, {"opponent": "tyrunt", "rating": 313}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 319}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 10203}, {"moveId": "FIRE_FANG", "uses": 5837}, {"moveId": "EMBER", "uses": 4882}, {"moveId": "TACKLE", "uses": 4044}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11270}, {"moveId": "CRUNCH", "uses": 8987}, {"moveId": "FLAMETHROWER", "uses": 4719}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "CRUNCH"], "score": 90.1}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 826, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 856, "opRating": 143}, {"opponent": "talonflame", "rating": 828, "opRating": 171}, {"opponent": "typhlosion", "rating": 814, "opRating": 185}, {"opponent": "rapidash", "rating": 814, "opRating": 185}, {"opponent": "magmar", "rating": 787, "opRating": 212}], "counters": [{"opponent": "swalot", "rating": 270}, {"opponent": "slowbro_galarian", "rating": 279}, {"opponent": "victini", "rating": 282}, {"opponent": "dedenne", "rating": 312}, {"opponent": "nidoqueen", "rating": 327}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 13830}, {"moveId": "FIRE_SPIN", "uses": 11170}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 7808}, {"moveId": "SCORCHING_SANDS", "uses": 7217}, {"moveId": "BRICK_BREAK", "uses": 3777}, {"moveId": "THUNDERBOLT", "uses": 2718}, {"moveId": "PSYCHIC", "uses": 2182}, {"moveId": "FIRE_BLAST", "uses": 1337}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 90}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 820, "matchups": [{"opponent": "nidoking", "rating": 864, "opRating": 135}, {"opponent": "rotom_heat", "rating": 820, "opRating": 179}, {"opponent": "stunfisk_galarian", "rating": 798, "opRating": 201}, {"opponent": "arcanine", "rating": 798, "opRating": 201}, {"opponent": "typhlosion", "rating": 785, "opRating": 214}], "counters": [{"opponent": "magmar", "rating": 236}, {"opponent": "umbreon", "rating": 303}, {"opponent": "ampha<PERSON>", "rating": 313}, {"opponent": "r<PERSON><PERSON>", "rating": 340}, {"opponent": "electrode_hisuian", "rating": 349}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 17200}, {"moveId": "EMBER", "uses": 7800}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 9911}, {"moveId": "PSYSHOCK", "uses": 9584}, {"moveId": "FLAMETHROWER", "uses": 4154}, {"moveId": "HEAT_WAVE", "uses": 1339}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 89.9}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 887, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 938, "opRating": 61}, {"opponent": "victini", "rating": 909, "opRating": 90}, {"opponent": "over<PERSON><PERSON>l", "rating": 909, "opRating": 90}, {"opponent": "rotom_heat", "rating": 847, "opRating": 152}, {"opponent": "magmar", "rating": 793, "opRating": 206}], "counters": [{"opponent": "camerupt", "rating": 77}, {"opponent": "stunfisk_galarian", "rating": 168}, {"opponent": "nidoqueen", "rating": 230}, {"opponent": "nidoking", "rating": 236}, {"opponent": "steelix", "rating": 254}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15920}, {"moveId": "SPARK", "uses": 9080}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 18481}, {"moveId": "GYRO_BALL", "uses": 3480}, {"moveId": "FELL_STINGER", "uses": 3004}]}, "moveset": ["THUNDER_SHOCK", "FELL_STINGER", "WILD_CHARGE"], "score": 89.6}, {"speciesId": "qwilfish_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 823, "matchups": [{"opponent": "incineroar", "rating": 908, "opRating": 91}, {"opponent": "magmar", "rating": 862, "opRating": 137}, {"opponent": "camerupt", "rating": 841, "opRating": 158}, {"opponent": "magmortar", "rating": 829, "opRating": 170}, {"opponent": "darmanitan_standard", "rating": 816, "opRating": 183}], "counters": [{"opponent": "registeel", "rating": 242}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 252}, {"opponent": "<PERSON>rserker", "rating": 273}, {"opponent": "magnezone", "rating": 284}, {"opponent": "tentacruel", "rating": 333}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13455}, {"moveId": "POISON_JAB", "uses": 11545}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 9204}, {"moveId": "DARK_PULSE", "uses": 4950}, {"moveId": "SLUDGE_BOMB", "uses": 3974}, {"moveId": "SHADOW_BALL", "uses": 3623}, {"moveId": "ICE_BEAM", "uses": 3277}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 89.4}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 804, "matchups": [{"opponent": "ninetales", "rating": 799, "opRating": 200}, {"opponent": "r<PERSON><PERSON>", "rating": 777, "opRating": 222}, {"opponent": "litleo", "rating": 748, "opRating": 251}, {"opponent": "stunfisk_galarian", "rating": 733, "opRating": 266}, {"opponent": "typhlosion", "rating": 718, "opRating": 281}], "counters": [{"opponent": "magby", "rating": 204}, {"opponent": "archen", "rating": 265}, {"opponent": "qwilfish_his<PERSON>an", "rating": 279}, {"opponent": "zapdos", "rating": 304}, {"opponent": "toxapex", "rating": 305}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 14790}, {"moveId": "FIRE_FANG", "uses": 6473}, {"moveId": "TACKLE", "uses": 3742}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 8321}, {"moveId": "OVERHEAT", "uses": 8179}, {"moveId": "FOCUS_BLAST", "uses": 4792}, {"moveId": "PSYCHIC", "uses": 3694}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 89.3}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 823, "matchups": [{"opponent": "stunfisk_galarian", "rating": 842, "opRating": 157}, {"opponent": "nidoking", "rating": 821, "opRating": 178}, {"opponent": "armarouge", "rating": 808, "opRating": 191}, {"opponent": "darmanitan_standard", "rating": 804, "opRating": 195}, {"opponent": "nidoqueen", "rating": 780, "opRating": 219}], "counters": [{"opponent": "magmar", "rating": 200}, {"opponent": "archen", "rating": 282}, {"opponent": "r<PERSON><PERSON>", "rating": 305}, {"opponent": "tyrunt", "rating": 313}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 319}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 20130}, {"moveId": "BITE", "uses": 4870}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 9969}, {"moveId": "CRUNCH", "uses": 8912}, {"moveId": "DISARMING_VOICE", "uses": 6109}]}, "moveset": ["INCINERATE", "CRUNCH", "FLAMETHROWER"], "score": 88.3}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 804, "matchups": [{"opponent": "incineroar", "rating": 921, "opRating": 78}, {"opponent": "rotom_heat", "rating": 863, "opRating": 136}, {"opponent": "magmar", "rating": 830, "opRating": 169}, {"opponent": "magmortar", "rating": 830, "opRating": 169}, {"opponent": "typhlosion", "rating": 747, "opRating": 252}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 289}, {"opponent": "registeel", "rating": 303}, {"opponent": "<PERSON>rserker", "rating": 324}, {"opponent": "magnezone", "rating": 333}, {"opponent": "<PERSON>on", "rating": 333}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13358}, {"moveId": "POISON_JAB", "uses": 11642}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 9205}, {"moveId": "DARK_PULSE", "uses": 4951}, {"moveId": "SLUDGE_BOMB", "uses": 3958}, {"moveId": "SHADOW_BALL", "uses": 3623}, {"moveId": "ICE_BEAM", "uses": 3281}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 88.2}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 823, "matchups": [{"opponent": "victini", "rating": 836, "opRating": 164}, {"opponent": "stunfisk_galarian", "rating": 780, "opRating": 220}, {"opponent": "arcanine", "rating": 780, "opRating": 220}, {"opponent": "typhlosion", "rating": 772, "opRating": 228}, {"opponent": "rapidash", "rating": 772, "opRating": 228}], "counters": [{"opponent": "magby", "rating": 179}, {"opponent": "magmar", "rating": 236}, {"opponent": "toxapex", "rating": 271}, {"opponent": "ampha<PERSON>", "rating": 313}, {"opponent": "qwilfish_his<PERSON>an", "rating": 320}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 12939}, {"moveId": "FIRE_FANG", "uses": 5962}, {"moveId": "EMBER", "uses": 4946}, {"moveId": "TAKE_DOWN", "uses": 1155}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 9640}, {"moveId": "OVERHEAT", "uses": 7432}, {"moveId": "DARK_PULSE", "uses": 5983}, {"moveId": "SOLAR_BEAM", "uses": 1969}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "DARK_PULSE"], "score": 87.5}, {"speciesId": "r<PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 824, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 853, "opRating": 146}, {"opponent": "magmar", "rating": 818, "opRating": 181}, {"opponent": "magmortar", "rating": 818, "opRating": 181}, {"opponent": "ninetales", "rating": 796, "opRating": 203}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 756, "opRating": 243}], "counters": [{"opponent": "victreebel", "rating": 199}, {"opponent": "venusaur", "rating": 225}, {"opponent": "nidoqueen", "rating": 248}, {"opponent": "dedenne", "rating": 266}, {"opponent": "nidoking", "rating": 299}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 8963}, {"moveId": "VOLT_SWITCH", "uses": 8788}, {"moveId": "SPARK", "uses": 4952}, {"moveId": "CHARM", "uses": 2308}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8832}, {"moveId": "THUNDER_PUNCH", "uses": 5123}, {"moveId": "BRICK_BREAK", "uses": 4044}, {"moveId": "TRAILBLAZE", "uses": 3112}, {"moveId": "SKULL_BASH", "uses": 2546}, {"moveId": "THUNDER", "uses": 1397}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "BRICK_BREAK"], "score": 87}, {"speciesId": "arctibax", "speciesName": "Arctibax", "rating": 795, "matchups": [{"opponent": "rotom_heat", "rating": 897, "opRating": 102}, {"opponent": "incineroar", "rating": 857, "opRating": 142}, {"opponent": "victini", "rating": 831, "opRating": 168}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 824, "opRating": 175}, {"opponent": "litleo", "rating": 731, "opRating": 268}], "counters": [{"opponent": "ma<PERSON>le", "rating": 149}, {"opponent": "weezing_galarian", "rating": 204}, {"opponent": "klefki", "rating": 305}, {"opponent": "magneton", "rating": 321}, {"opponent": "magmar", "rating": 361}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 13083}, {"moveId": "ICE_FANG", "uses": 11917}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 9990}, {"moveId": "DRAGON_CLAW", "uses": 9011}, {"moveId": "ICY_WIND", "uses": 3485}, {"moveId": "OUTRAGE", "uses": 2532}]}, "moveset": ["DRAGON_BREATH", "AVALANCHE", "DRAGON_CLAW"], "score": 86.8}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 820, "matchups": [{"opponent": "ninetales", "rating": 858, "opRating": 141}, {"opponent": "litleo", "rating": 828, "opRating": 171}, {"opponent": "crocalor", "rating": 828, "opRating": 171}, {"opponent": "arcanine", "rating": 805, "opRating": 194}, {"opponent": "rapidash", "rating": 767, "opRating": 232}], "counters": [{"opponent": "electabuzz", "rating": 226}, {"opponent": "elekid", "rating": 254}, {"opponent": "em<PERSON>ga", "rating": 296}, {"opponent": "over<PERSON><PERSON>l", "rating": 301}, {"opponent": "r<PERSON><PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 12165}, {"moveId": "FIRE_SPIN", "uses": 7267}, {"moveId": "STEEL_WING", "uses": 3167}, {"moveId": "PECK", "uses": 2427}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 7992}, {"moveId": "FLAME_CHARGE", "uses": 7113}, {"moveId": "FLY", "uses": 6818}, {"moveId": "FIRE_BLAST", "uses": 1647}, {"moveId": "HURRICANE", "uses": 1519}]}, "moveset": ["INCINERATE", "FLY", "BRAVE_BIRD"], "score": 86.5}, {"speciesId": "magby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 805, "matchups": [{"opponent": "incineroar", "rating": 870, "opRating": 129}, {"opponent": "electrode_hisuian", "rating": 850, "opRating": 150}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 841, "opRating": 158}, {"opponent": "pyroar", "rating": 825, "opRating": 175}, {"opponent": "litleo", "rating": 779, "opRating": 220}], "counters": [{"opponent": "toxapex", "rating": 283}, {"opponent": "slowbro_galarian", "rating": 289}, {"opponent": "golbat", "rating": 289}, {"opponent": "dedenne", "rating": 312}, {"opponent": "nidoqueen", "rating": 327}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 14570}, {"moveId": "EMBER", "uses": 10430}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 12195}, {"moveId": "BRICK_BREAK", "uses": 6633}, {"moveId": "FLAMETHROWER", "uses": 3839}, {"moveId": "FLAME_BURST", "uses": 2334}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "BRICK_BREAK"], "score": 86.2}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 824, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 910, "opRating": 89}, {"opponent": "rotom_heat", "rating": 910, "opRating": 89}, {"opponent": "victini", "rating": 852, "opRating": 147}, {"opponent": "r<PERSON><PERSON>", "rating": 780, "opRating": 219}, {"opponent": "typhlosion", "rating": 737, "opRating": 262}], "counters": [{"opponent": "sandslash_alolan", "rating": 100}, {"opponent": "metang", "rating": 189}, {"opponent": "nidoking", "rating": 228}, {"opponent": "amaura", "rating": 234}, {"opponent": "heatran", "rating": 236}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 20160}, {"moveId": "BITE", "uses": 4840}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 8790}, {"moveId": "STONE_EDGE", "uses": 5870}, {"moveId": "POISON_FANG", "uses": 4617}, {"moveId": "EARTHQUAKE", "uses": 3228}, {"moveId": "SLUDGE_WAVE", "uses": 2500}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "POISON_FANG"], "score": 85.6}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 843, "matchups": [{"opponent": "piloswine", "rating": 888, "opRating": 111}, {"opponent": "sandslash_alolan", "rating": 888, "opRating": 111}, {"opponent": "magnezone", "rating": 837, "opRating": 162}, {"opponent": "stunfisk_galarian", "rating": 805, "opRating": 194}, {"opponent": "steelix", "rating": 710, "opRating": 289}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "tyrunt", "rating": 201}, {"opponent": "tentacruel", "rating": 261}, {"opponent": "r<PERSON><PERSON>", "rating": 269}, {"opponent": "talonflame", "rating": 290}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 11323}, {"moveId": "EMBER", "uses": 8509}, {"moveId": "FEINT_ATTACK", "uses": 5165}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 7768}, {"moveId": "SCORCHING_SANDS", "uses": 5636}, {"moveId": "OVERHEAT", "uses": 3111}, {"moveId": "PSYSHOCK", "uses": 2928}, {"moveId": "FLAMETHROWER", "uses": 1694}, {"moveId": "RETURN", "uses": 1606}, {"moveId": "FIRE_BLAST", "uses": 911}, {"moveId": "SOLAR_BEAM", "uses": 774}, {"moveId": "HEAT_WAVE", "uses": 556}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 85.5}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 819, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 844, "opRating": 155}, {"opponent": "typhlosion", "rating": 801, "opRating": 198}, {"opponent": "talonflame", "rating": 801, "opRating": 198}, {"opponent": "rapidash", "rating": 779, "opRating": 220}, {"opponent": "r<PERSON><PERSON>", "rating": 715, "opRating": 284}], "counters": [{"opponent": "nidoking", "rating": 90}, {"opponent": "linoone_galarian", "rating": 148}, {"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "magmar", "rating": 183}, {"opponent": "tyrunt", "rating": 341}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 14831}, {"moveId": "ASTONISH", "uses": 10169}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 12269}, {"moveId": "THUNDERBOLT", "uses": 8845}, {"moveId": "THUNDER", "uses": 3851}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 85.3}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 756, "matchups": [{"opponent": "rotom_heat", "rating": 860, "opRating": 139}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 823, "opRating": 176}, {"opponent": "armarouge", "rating": 782, "opRating": 217}, {"opponent": "rapidash", "rating": 775, "opRating": 224}, {"opponent": "qwilfish_his<PERSON>an", "rating": 747, "opRating": 252}], "counters": [{"opponent": "ferrothorn", "rating": 284}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 286}, {"opponent": "obstagoon", "rating": 340}, {"opponent": "ninetales", "rating": 341}, {"opponent": "stunfisk_galarian", "rating": 360}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 13898}, {"moveId": "LICK", "uses": 11102}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 14091}, {"moveId": "BULLDOZE", "uses": 8220}, {"moveId": "GUNK_SHOT", "uses": 2708}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 84.2}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 812, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 916, "opRating": 83}, {"opponent": "victini", "rating": 877, "opRating": 122}, {"opponent": "over<PERSON><PERSON>l", "rating": 877, "opRating": 122}, {"opponent": "incineroar", "rating": 872, "opRating": 127}, {"opponent": "magmar", "rating": 759, "opRating": 240}], "counters": [{"opponent": "stunfisk_galarian", "rating": 118}, {"opponent": "houndoom", "rating": 239}, {"opponent": "nidoking", "rating": 244}, {"opponent": "camerupt", "rating": 254}, {"opponent": "electrode_hisuian", "rating": 269}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 9575}, {"moveId": "SPARK", "uses": 5825}, {"moveId": "METAL_SOUND", "uses": 5283}, {"moveId": "CHARGE_BEAM", "uses": 4338}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15065}, {"moveId": "MIRROR_SHOT", "uses": 5232}, {"moveId": "FLASH_CANNON", "uses": 2752}, {"moveId": "ZAP_CANNON", "uses": 2057}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 83.4}, {"speciesId": "camerupt", "speciesName": "Camerupt", "rating": 775, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 922, "opRating": 77}, {"opponent": "magnezone", "rating": 913, "opRating": 86}, {"opponent": "rotom_heat", "rating": 889, "opRating": 110}, {"opponent": "litleo", "rating": 725, "opRating": 274}, {"opponent": "typhlosion", "rating": 700, "opRating": 299}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "magmar", "rating": 236}, {"opponent": "qwilfish_his<PERSON>an", "rating": 241}, {"opponent": "pyroar", "rating": 284}, {"opponent": "over<PERSON><PERSON>l", "rating": 334}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16837}, {"moveId": "EMBER", "uses": 6332}, {"moveId": "ROCK_SMASH", "uses": 1831}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 9997}, {"moveId": "OVERHEAT", "uses": 7126}, {"moveId": "EARTHQUAKE", "uses": 3658}, {"moveId": "RETURN", "uses": 2881}, {"moveId": "SOLAR_BEAM", "uses": 1442}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 82.5}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 780, "matchups": [{"opponent": "stunfisk_galarian", "rating": 893, "opRating": 106}, {"opponent": "swalot", "rating": 893, "opRating": 106}, {"opponent": "registeel", "rating": 888, "opRating": 111}, {"opponent": "nidoking", "rating": 858, "opRating": 141}, {"opponent": "magmar", "rating": 683, "opRating": 316}], "counters": [{"opponent": "manectric", "rating": 275}, {"opponent": "electabuzz", "rating": 291}, {"opponent": "zapdos", "rating": 291}, {"opponent": "ninetales", "rating": 325}, {"opponent": "victini", "rating": 329}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 7297}, {"moveId": "EMBER", "uses": 5277}, {"moveId": "DRAGON_BREATH", "uses": 4621}, {"moveId": "WING_ATTACK", "uses": 4442}, {"moveId": "AIR_SLASH", "uses": 3350}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 11658}, {"moveId": "DRAGON_CLAW", "uses": 6740}, {"moveId": "FLAMETHROWER", "uses": 2694}, {"moveId": "OVERHEAT", "uses": 2523}, {"moveId": "FIRE_BLAST", "uses": 1473}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "DRAGON_CLAW"], "score": 82}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 779, "matchups": [{"opponent": "tentacruel", "rating": 927, "opRating": 72}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 842, "opRating": 157}, {"opponent": "zapdos", "rating": 842, "opRating": 157}, {"opponent": "magnezone", "rating": 782, "opRating": 217}, {"opponent": "manectric", "rating": 713, "opRating": 286}], "counters": [{"opponent": "stunfisk_galarian", "rating": 198}, {"opponent": "nidoking", "rating": 287}, {"opponent": "rotom_heat", "rating": 295}, {"opponent": "crocalor", "rating": 304}, {"opponent": "litleo", "rating": 311}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 11212}, {"moveId": "THUNDER_FANG", "uses": 9881}, {"moveId": "IRON_TAIL", "uses": 3901}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 5826}, {"moveId": "EARTHQUAKE", "uses": 5785}, {"moveId": "PSYCHIC_FANGS", "uses": 4872}, {"moveId": "CRUNCH", "uses": 4059}, {"moveId": "HEAVY_SLAM", "uses": 2553}, {"moveId": "RETURN", "uses": 1911}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 81.4}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 796, "matchups": [{"opponent": "victini", "rating": 831, "opRating": 168}, {"opponent": "rotom_heat", "rating": 818, "opRating": 181}, {"opponent": "magmar", "rating": 813, "opRating": 186}, {"opponent": "magmortar", "rating": 813, "opRating": 186}, {"opponent": "qwilfish_his<PERSON>an", "rating": 813, "opRating": 186}], "counters": [{"opponent": "stunfisk_galarian", "rating": 198}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 243}, {"opponent": "avalugg_his<PERSON>an", "rating": 260}, {"opponent": "piloswine", "rating": 261}, {"opponent": "magnezone", "rating": 284}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 21706}, {"moveId": "LOW_KICK", "uses": 3294}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 9139}, {"moveId": "THUNDER_PUNCH", "uses": 5310}, {"moveId": "ICE_PUNCH", "uses": 4819}, {"moveId": "FLAMETHROWER", "uses": 4289}, {"moveId": "THUNDER", "uses": 1482}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 80.9}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 742, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 909, "opRating": 90}, {"opponent": "arcanine", "rating": 729, "opRating": 270}, {"opponent": "incineroar", "rating": 729, "opRating": 270}, {"opponent": "armarouge", "rating": 709, "opRating": 290}, {"opponent": "litleo", "rating": 641, "opRating": 358}], "counters": [{"opponent": "pawniard", "rating": 254}, {"opponent": "a<PERSON><PERSON>", "rating": 262}, {"opponent": "araquanid", "rating": 278}, {"opponent": "obstagoon", "rating": 333}, {"opponent": "r<PERSON><PERSON>", "rating": 362}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 13785}, {"moveId": "FEINT_ATTACK", "uses": 11215}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 10687}, {"moveId": "LAST_RESORT", "uses": 5569}, {"moveId": "DARK_PULSE", "uses": 4584}, {"moveId": "PSYCHIC", "uses": 4150}]}, "moveset": ["SNARL", "FOUL_PLAY", "LAST_RESORT"], "score": 80.7}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 797, "matchups": [{"opponent": "magnezone", "rating": 917, "opRating": 82}, {"opponent": "rotom_heat", "rating": 909, "opRating": 90}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 881, "opRating": 118}, {"opponent": "nidoqueen", "rating": 858, "opRating": 141}, {"opponent": "stunfisk_galarian", "rating": 779, "opRating": 220}], "counters": [{"opponent": "beedrill", "rating": 189}, {"opponent": "em<PERSON>ga", "rating": 195}, {"opponent": "charizard", "rating": 209}, {"opponent": "magmar", "rating": 223}, {"opponent": "moltres", "rating": 243}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 9163}, {"moveId": "POISON_JAB", "uses": 7809}, {"moveId": "FURY_CUTTER", "uses": 6592}, {"moveId": "IRON_TAIL", "uses": 1444}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 9501}, {"moveId": "MEGAHORN", "uses": 4851}, {"moveId": "SAND_TOMB", "uses": 3977}, {"moveId": "EARTHQUAKE", "uses": 3450}, {"moveId": "SLUDGE_WAVE", "uses": 3277}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 79.9}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 755, "matchups": [{"opponent": "tyrunt", "rating": 891, "opRating": 108}, {"opponent": "victini", "rating": 852, "opRating": 147}, {"opponent": "electivire", "rating": 839, "opRating": 160}, {"opponent": "arctibax", "rating": 830, "opRating": 169}, {"opponent": "r<PERSON><PERSON>", "rating": 821, "opRating": 178}], "counters": [{"opponent": "magnezone", "rating": 269}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 272}, {"opponent": "stunfisk_galarian", "rating": 275}, {"opponent": "nidoking", "rating": 342}, {"opponent": "arcanine", "rating": 344}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 16299}, {"moveId": "TACKLE", "uses": 8701}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 8462}, {"moveId": "OVERHEAT", "uses": 4980}, {"moveId": "SLUDGE", "uses": 4637}, {"moveId": "PLAY_ROUGH", "uses": 3131}, {"moveId": "RETURN", "uses": 2692}, {"moveId": "HYPER_BEAM", "uses": 1017}]}, "moveset": ["FAIRY_WIND", "PLAY_ROUGH", "BRUTAL_SWING"], "score": 79.6}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 752, "matchups": [{"opponent": "rotom_heat", "rating": 845, "opRating": 154}, {"opponent": "electivire", "rating": 829, "opRating": 170}, {"opponent": "incineroar", "rating": 795, "opRating": 204}, {"opponent": "victini", "rating": 787, "opRating": 212}, {"opponent": "talonflame", "rating": 733, "opRating": 266}], "counters": [{"opponent": "stunfisk_galarian", "rating": 245}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 268}, {"opponent": "arctibax", "rating": 268}, {"opponent": "piloswine", "rating": 293}, {"opponent": "steelix", "rating": 314}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 10867}, {"moveId": "THUNDER_FANG", "uses": 8618}, {"moveId": "CHARGE_BEAM", "uses": 5517}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 9910}, {"moveId": "PSYCHIC_FANGS", "uses": 5860}, {"moveId": "OVERHEAT", "uses": 4084}, {"moveId": "RETURN", "uses": 2218}, {"moveId": "THUNDER", "uses": 1544}, {"moveId": "FLAME_BURST", "uses": 1350}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "WILD_CHARGE"], "score": 79.4}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 806, "matchups": [{"opponent": "rotom_heat", "rating": 889, "opRating": 110}, {"opponent": "magmar", "rating": 871, "opRating": 128}, {"opponent": "magmortar", "rating": 871, "opRating": 128}, {"opponent": "magby", "rating": 871, "opRating": 128}, {"opponent": "qwilfish_his<PERSON>an", "rating": 818, "opRating": 181}], "counters": [{"opponent": "amaura", "rating": 112}, {"opponent": "sandslash_alolan", "rating": 125}, {"opponent": "avalugg_his<PERSON>an", "rating": 152}, {"opponent": "stunfisk_galarian", "rating": 215}, {"opponent": "swalot", "rating": 252}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 16516}, {"moveId": "TACKLE", "uses": 8484}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13009}, {"moveId": "SWIFT", "uses": 8375}, {"moveId": "ENERGY_BALL", "uses": 3653}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 79.3}, {"speciesId": "darum<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 784, "matchups": [{"opponent": "sandslash_alolan", "rating": 892, "opRating": 107}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 792, "opRating": 207}, {"opponent": "magnezone", "rating": 788, "opRating": 211}, {"opponent": "rotom_heat", "rating": 785, "opRating": 214}, {"opponent": "stunfisk_galarian", "rating": 724, "opRating": 275}], "counters": [{"opponent": "magmar", "rating": 241}, {"opponent": "litleo", "rating": 272}, {"opponent": "talonflame", "rating": 274}, {"opponent": "tyrunt", "rating": 282}, {"opponent": "typhlosion", "rating": 307}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 16268}, {"moveId": "TACKLE", "uses": 8732}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 14611}, {"moveId": "FLAME_CHARGE", "uses": 5509}, {"moveId": "RETURN", "uses": 4902}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 78.8}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 805, "matchups": [{"opponent": "electrode_hisuian", "rating": 864, "opRating": 135}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 848, "opRating": 151}, {"opponent": "r<PERSON><PERSON>", "rating": 848, "opRating": 151}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 848, "opRating": 151}, {"opponent": "magmortar", "rating": 724, "opRating": 275}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "raticate_alolan", "rating": 125}, {"opponent": "qwilfish_his<PERSON>an", "rating": 137}, {"opponent": "slowbro_galarian", "rating": 141}, {"opponent": "tyrunt", "rating": 259}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 14448}, {"moveId": "CONFUSION", "uses": 10552}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 16748}, {"moveId": "PSYCHIC", "uses": 3349}, {"moveId": "FOCUS_BLAST", "uses": 2984}, {"moveId": "OVERHEAT", "uses": 1956}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 78.1}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 744, "matchups": [{"opponent": "rotom_heat", "rating": 866, "opRating": 133}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 833, "opRating": 166}, {"opponent": "electrode_hisuian", "rating": 770, "opRating": 229}, {"opponent": "magmortar", "rating": 747, "opRating": 252}, {"opponent": "pyroar", "rating": 729, "opRating": 270}], "counters": [{"opponent": "forretress", "rating": 246}, {"opponent": "moltres_galarian", "rating": 258}, {"opponent": "stunfisk_galarian", "rating": 298}, {"opponent": "talonflame", "rating": 305}, {"opponent": "nidoking", "rating": 318}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 12333}, {"moveId": "INFESTATION", "uses": 8235}, {"moveId": "ROCK_SMASH", "uses": 4433}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 10656}, {"moveId": "ICE_BEAM", "uses": 9168}, {"moveId": "GUNK_SHOT", "uses": 2762}, {"moveId": "ACID_SPRAY", "uses": 2435}]}, "moveset": ["MUD_SHOT", "SLUDGE_BOMB", "ICE_BEAM"], "score": 78}, {"speciesId": "tyrunt", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 781, "matchups": [{"opponent": "litleo", "rating": 887, "opRating": 112}, {"opponent": "crocalor", "rating": 887, "opRating": 112}, {"opponent": "talonflame", "rating": 887, "opRating": 112}, {"opponent": "rapidash", "rating": 872, "opRating": 127}, {"opponent": "pyroar", "rating": 872, "opRating": 127}], "counters": [{"opponent": "weezing_galarian", "rating": 126}, {"opponent": "ma<PERSON>le", "rating": 153}, {"opponent": "dedenne", "rating": 199}, {"opponent": "sandslash_alolan", "rating": 201}, {"opponent": "stunfisk_galarian", "rating": 263}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 15478}, {"moveId": "TACKLE", "uses": 9522}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 10384}, {"moveId": "ANCIENT_POWER", "uses": 9499}, {"moveId": "STOMP", "uses": 5100}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 77.9}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 735, "matchups": [{"opponent": "registeel", "rating": 893, "opRating": 106}, {"opponent": "piloswine", "rating": 834, "opRating": 165}, {"opponent": "charizard", "rating": 790, "opRating": 209}, {"opponent": "heatran", "rating": 790, "opRating": 209}, {"opponent": "darmanitan_standard", "rating": 746, "opRating": 253}], "counters": [{"opponent": "magmar", "rating": 223}, {"opponent": "tyrunt", "rating": 279}, {"opponent": "over<PERSON><PERSON>l", "rating": 322}, {"opponent": "qwilfish_his<PERSON>an", "rating": 325}, {"opponent": "ninetales", "rating": 349}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16629}, {"moveId": "LICK", "uses": 8371}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 11722}, {"moveId": "THUNDER_PUNCH", "uses": 9232}, {"moveId": "POWER_UP_PUNCH", "uses": 4060}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 76.9}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 756, "matchups": [{"opponent": "rotom_heat", "rating": 770, "opRating": 229}, {"opponent": "litleo", "rating": 766, "opRating": 233}, {"opponent": "crocalor", "rating": 766, "opRating": 233}, {"opponent": "nidoking", "rating": 741, "opRating": 258}, {"opponent": "rapidash", "rating": 733, "opRating": 266}], "counters": [{"opponent": "weezing_galarian", "rating": 195}, {"opponent": "electrode_hisuian", "rating": 243}, {"opponent": "sandslash_alolan", "rating": 245}, {"opponent": "umbreon", "rating": 261}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 15580}, {"moveId": "WING_ATTACK", "uses": 9420}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 11299}, {"moveId": "ANCIENT_POWER", "uses": 6955}, {"moveId": "PAYBACK", "uses": 6785}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 76.8}, {"speciesId": "raticate_alolan", "speciesName": "Raticate (Alolan)", "rating": 757, "matchups": [{"opponent": "victini", "rating": 895, "opRating": 104}, {"opponent": "electivire", "rating": 861, "opRating": 138}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 858, "opRating": 141}, {"opponent": "armarouge", "rating": 780, "opRating": 219}, {"opponent": "rapidash", "rating": 709, "opRating": 290}], "counters": [{"opponent": "escavalier", "rating": 144}, {"opponent": "genesect", "rating": 155}, {"opponent": "magby", "rating": 191}, {"opponent": "registeel", "rating": 238}, {"opponent": "melmetal", "rating": 290}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 16072}, {"moveId": "BITE", "uses": 8928}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 11868}, {"moveId": "HYPER_FANG", "uses": 8188}, {"moveId": "RETURN", "uses": 2822}, {"moveId": "HYPER_BEAM", "uses": 2167}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "RETURN"], "score": 76.4}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 739, "matchups": [{"opponent": "em<PERSON>ga", "rating": 877, "opRating": 122}, {"opponent": "rotom_heat", "rating": 842, "opRating": 157}, {"opponent": "incineroar", "rating": 830, "opRating": 169}, {"opponent": "litleo", "rating": 681, "opRating": 318}, {"opponent": "typhlosion", "rating": 649, "opRating": 350}], "counters": [{"opponent": "stunfisk_galarian", "rating": 204}, {"opponent": "nidoqueen", "rating": 230}, {"opponent": "piloswine", "rating": 245}, {"opponent": "steelix", "rating": 282}, {"opponent": "nidoking", "rating": 287}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 13079}, {"moveId": "SPARK", "uses": 8374}, {"moveId": "ACID", "uses": 3548}], "chargedMoves": [{"moveId": "LIQUIDATION", "uses": 7406}, {"moveId": "DRAGON_CLAW", "uses": 6449}, {"moveId": "CRUNCH", "uses": 5674}, {"moveId": "THUNDERBOLT", "uses": 4714}, {"moveId": "ACID_SPRAY", "uses": 795}]}, "moveset": ["VOLT_SWITCH", "DRAGON_CLAW", "THUNDERBOLT"], "score": 76.2}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 740, "matchups": [{"opponent": "incineroar", "rating": 847, "opRating": 152}, {"opponent": "victini", "rating": 826, "opRating": 173}, {"opponent": "ninetales", "rating": 711, "opRating": 288}, {"opponent": "crocalor", "rating": 673, "opRating": 326}, {"opponent": "typhlosion", "rating": 639, "opRating": 360}], "counters": [{"opponent": "stunfisk_galarian", "rating": 224}, {"opponent": "raticate_alolan", "rating": 250}, {"opponent": "cradily", "rating": 262}, {"opponent": "nidoqueen", "rating": 291}, {"opponent": "registeel", "rating": 303}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 3108}, {"moveId": "SPARK", "uses": 2879}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1973}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1567}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1486}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1400}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1377}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1348}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1121}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1078}, {"moveId": "HIDDEN_POWER_ICE", "uses": 1074}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1066}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1064}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1009}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1008}, {"moveId": "HIDDEN_POWER_POISON", "uses": 870}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 816}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 687}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 11303}, {"moveId": "PSYCHIC_FANGS", "uses": 6443}, {"moveId": "CRUNCH", "uses": 5262}, {"moveId": "HYPER_BEAM", "uses": 1965}]}, "moveset": ["SPARK", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 76.2}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 751, "matchups": [{"opponent": "victini", "rating": 858, "opRating": 141}, {"opponent": "rotom_heat", "rating": 844, "opRating": 155}, {"opponent": "litleo", "rating": 748, "opRating": 251}, {"opponent": "magmar", "rating": 720, "opRating": 279}, {"opponent": "typhlosion", "rating": 720, "opRating": 279}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "grimer_alolan", "rating": 269}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 272}, {"opponent": "skuntank", "rating": 283}, {"opponent": "over<PERSON><PERSON>l", "rating": 305}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 13329}, {"moveId": "CONFUSION", "uses": 11671}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 6325}, {"moveId": "SURF", "uses": 5666}, {"moveId": "SLUDGE_BOMB", "uses": 3719}, {"moveId": "PSYCHIC", "uses": 3296}, {"moveId": "FOCUS_BLAST", "uses": 3228}, {"moveId": "SCALD", "uses": 2776}]}, "moveset": ["POISON_JAB", "SCALD", "BRUTAL_SWING"], "score": 76}, {"speciesId": "charjabug", "speciesName": "Charjabug", "rating": 738, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 888, "opRating": 111}, {"opponent": "electabuzz", "rating": 876, "opRating": 123}, {"opponent": "rai<PERSON>u", "rating": 876, "opRating": 123}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 742, "opRating": 257}, {"opponent": "r<PERSON><PERSON>", "rating": 726, "opRating": 273}], "counters": [{"opponent": "nidoqueen", "rating": 205}, {"opponent": "typhlosion", "rating": 239}, {"opponent": "camerupt", "rating": 258}, {"opponent": "stunfisk_galarian", "rating": 269}, {"opponent": "rotom_heat", "rating": 306}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 10988}, {"moveId": "SPARK", "uses": 7161}, {"moveId": "BUG_BITE", "uses": 6865}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10114}, {"moveId": "CRUNCH", "uses": 7582}, {"moveId": "DISCHARGE", "uses": 7323}]}, "moveset": ["VOLT_SWITCH", "X_SCISSOR", "DISCHARGE"], "score": 75.9}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 717, "matchups": [{"opponent": "magmortar", "rating": 818, "opRating": 181}, {"opponent": "magby", "rating": 818, "opRating": 181}, {"opponent": "magmar", "rating": 796, "opRating": 203}, {"opponent": "typhlosion", "rating": 743, "opRating": 256}, {"opponent": "rapidash", "rating": 743, "opRating": 256}], "counters": [{"opponent": "forretress", "rating": 253}, {"opponent": "sandslash_alolan", "rating": 262}, {"opponent": "weezing_galarian", "rating": 265}, {"opponent": "dedenne", "rating": 323}, {"opponent": "ninetales", "rating": 349}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 17099}, {"moveId": "STRUGGLE_BUG", "uses": 7901}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 8704}, {"moveId": "LIQUIDATION", "uses": 7969}, {"moveId": "CROSS_POISON", "uses": 6309}, {"moveId": "WATER_PULSE", "uses": 2018}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "LIQUIDATION"], "score": 75.1}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 766, "matchups": [{"opponent": "rotom_heat", "rating": 852, "opRating": 147}, {"opponent": "incineroar", "rating": 843, "opRating": 156}, {"opponent": "victini", "rating": 821, "opRating": 178}, {"opponent": "talonflame", "rating": 773, "opRating": 226}, {"opponent": "typhlosion", "rating": 665, "opRating": 334}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "steelix", "rating": 229}, {"opponent": "stunfisk_galarian", "rating": 248}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 19993}, {"moveId": "LOW_KICK", "uses": 5007}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 12328}, {"moveId": "RETURN", "uses": 5488}, {"moveId": "THUNDERBOLT", "uses": 3839}, {"moveId": "THUNDER", "uses": 3346}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 75}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 740, "matchups": [{"opponent": "rotom_heat", "rating": 821, "opRating": 178}, {"opponent": "litleo", "rating": 742, "opRating": 257}, {"opponent": "crocalor", "rating": 742, "opRating": 257}, {"opponent": "typhlosion", "rating": 716, "opRating": 283}, {"opponent": "rapidash", "rating": 703, "opRating": 296}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 282}, {"opponent": "nidoqueen", "rating": 305}, {"opponent": "r<PERSON><PERSON>", "rating": 314}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 16375}, {"moveId": "BITE", "uses": 8625}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 8110}, {"moveId": "FLAMETHROWER", "uses": 5396}, {"moveId": "SLUDGE_BOMB", "uses": 4871}, {"moveId": "TRAILBLAZE", "uses": 3911}, {"moveId": "RETURN", "uses": 2734}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 75}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 721, "matchups": [{"opponent": "darmanitan_standard", "rating": 728, "opRating": 271}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 689, "opRating": 310}, {"opponent": "armarouge", "rating": 686, "opRating": 313}, {"opponent": "r<PERSON><PERSON>", "rating": 674, "opRating": 325}, {"opponent": "litleo", "rating": 647, "opRating": 352}], "counters": [{"opponent": "stunfisk_galarian", "rating": 213}, {"opponent": "nidoqueen", "rating": 255}, {"opponent": "arctibax", "rating": 317}, {"opponent": "tyrunt", "rating": 325}, {"opponent": "sandslash_alolan", "rating": 326}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 17534}, {"moveId": "CHARGE_BEAM", "uses": 7466}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 5762}, {"moveId": "POWER_GEM", "uses": 4907}, {"moveId": "THUNDER_PUNCH", "uses": 4814}, {"moveId": "FOCUS_BLAST", "uses": 2781}, {"moveId": "TRAILBLAZE", "uses": 2712}, {"moveId": "DRAGON_PULSE", "uses": 1641}, {"moveId": "THUNDER", "uses": 1326}, {"moveId": "ZAP_CANNON", "uses": 1088}]}, "moveset": ["VOLT_SWITCH", "BRUTAL_SWING", "TRAILBLAZE"], "score": 74.6}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 720, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 777, "opRating": 222}, {"opponent": "pyroar", "rating": 773, "opRating": 226}, {"opponent": "armarouge", "rating": 773, "opRating": 226}, {"opponent": "rapidash", "rating": 718, "opRating": 281}, {"opponent": "stunfisk_galarian", "rating": 710, "opRating": 289}], "counters": [{"opponent": "magmar", "rating": 223}, {"opponent": "magmortar", "rating": 263}, {"opponent": "toxapex", "rating": 296}, {"opponent": "slowbro_galarian", "rating": 306}, {"opponent": "qwilfish_his<PERSON>an", "rating": 325}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 18773}, {"moveId": "BITE", "uses": 6227}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 11447}, {"moveId": "CRUNCH", "uses": 10476}, {"moveId": "FIRE_BLAST", "uses": 3090}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 74.6}, {"speciesId": "castform_sunny", "speciesName": "Castform (Sunny)", "rating": 767, "matchups": [{"opponent": "piloswine", "rating": 893, "opRating": 106}, {"opponent": "sandslash_alolan", "rating": 893, "opRating": 106}, {"opponent": "electivire", "rating": 859, "opRating": 140}, {"opponent": "heatran", "rating": 832, "opRating": 167}, {"opponent": "electrode_hisuian", "rating": 746, "opRating": 253}], "counters": [{"opponent": "toxapex", "rating": 148}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "tyrunt", "rating": 224}, {"opponent": "camerupt", "rating": 233}, {"opponent": "r<PERSON><PERSON>", "rating": 269}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15046}, {"moveId": "TACKLE", "uses": 9954}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 20235}, {"moveId": "SOLAR_BEAM", "uses": 2383}, {"moveId": "FIRE_BLAST", "uses": 2362}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 74.5}, {"speciesId": "grimer_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 734, "matchups": [{"opponent": "rotom_heat", "rating": 836, "opRating": 163}, {"opponent": "electabuzz", "rating": 836, "opRating": 163}, {"opponent": "talonflame", "rating": 728, "opRating": 271}, {"opponent": "typhlosion", "rating": 716, "opRating": 283}, {"opponent": "rapidash", "rating": 704, "opRating": 295}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "stunfisk_galarian", "rating": 236}, {"opponent": "sandslash_alolan", "rating": 298}, {"opponent": "r<PERSON><PERSON>", "rating": 314}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 12440}, {"moveId": "BITE", "uses": 7709}, {"moveId": "ACID", "uses": 4850}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 12384}, {"moveId": "SLUDGE_BOMB", "uses": 6852}, {"moveId": "RETURN", "uses": 3959}, {"moveId": "GUNK_SHOT", "uses": 1708}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 74.3}, {"speciesId": "em<PERSON>ga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 756, "matchups": [{"opponent": "nidoking", "rating": 882, "opRating": 117}, {"opponent": "incineroar", "rating": 853, "opRating": 146}, {"opponent": "victini", "rating": 833, "opRating": 166}, {"opponent": "talonflame", "rating": 776, "opRating": 223}, {"opponent": "litleo", "rating": 699, "opRating": 300}], "counters": [{"opponent": "steelix", "rating": 225}, {"opponent": "stunfisk_galarian", "rating": 263}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264}, {"opponent": "rai<PERSON>u", "rating": 275}, {"opponent": "r<PERSON><PERSON>", "rating": 287}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13776}, {"moveId": "QUICK_ATTACK", "uses": 11224}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 8275}, {"moveId": "DISCHARGE", "uses": 7211}, {"moveId": "ACROBATICS", "uses": 6496}, {"moveId": "THUNDERBOLT", "uses": 3028}]}, "moveset": ["THUNDER_SHOCK", "ACROBATICS", "DISCHARGE"], "score": 74.2}, {"speciesId": "fuecoco", "speciesName": "Fuecoco", "rating": 749, "matchups": [{"opponent": "registeel", "rating": 911, "opRating": 88}, {"opponent": "piloswine", "rating": 865, "opRating": 134}, {"opponent": "stunfisk_galarian", "rating": 819, "opRating": 180}, {"opponent": "nidoking", "rating": 799, "opRating": 200}, {"opponent": "steelix", "rating": 700, "opRating": 299}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "toxapex", "rating": 224}, {"opponent": "archen", "rating": 247}, {"opponent": "over<PERSON><PERSON>l", "rating": 268}, {"opponent": "r<PERSON><PERSON>", "rating": 287}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 20043}, {"moveId": "BITE", "uses": 4957}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 9962}, {"moveId": "CRUNCH", "uses": 8915}, {"moveId": "DISARMING_VOICE", "uses": 6109}]}, "moveset": ["INCINERATE", "CRUNCH", "FLAMETHROWER"], "score": 74}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 766, "matchups": [{"opponent": "piloswine", "rating": 895, "opRating": 104}, {"opponent": "avalugg_his<PERSON>an", "rating": 886, "opRating": 113}, {"opponent": "registeel", "rating": 881, "opRating": 118}, {"opponent": "nidoqueen", "rating": 831, "opRating": 168}, {"opponent": "stunfisk_galarian", "rating": 645, "opRating": 354}], "counters": [{"opponent": "magmar", "rating": 191}, {"opponent": "magmortar", "rating": 199}, {"opponent": "magby", "rating": 204}, {"opponent": "typhlosion", "rating": 243}, {"opponent": "rapidash", "rating": 256}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17629}, {"moveId": "BUG_BITE", "uses": 7371}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 8376}, {"moveId": "EARTH_POWER", "uses": 5580}, {"moveId": "STONE_EDGE", "uses": 5258}, {"moveId": "IRON_HEAD", "uses": 2341}, {"moveId": "FLAMETHROWER", "uses": 2276}, {"moveId": "FIRE_BLAST", "uses": 1199}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 73.4}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 747, "matchups": [{"opponent": "magmortar", "rating": 878, "opRating": 121}, {"opponent": "incineroar", "rating": 843, "opRating": 156}, {"opponent": "victini", "rating": 821, "opRating": 178}, {"opponent": "em<PERSON>ga", "rating": 821, "opRating": 178}, {"opponent": "typhlosion", "rating": 647, "opRating": 352}], "counters": [{"opponent": "amaura", "rating": 140}, {"opponent": "steelix", "rating": 254}, {"opponent": "nidoqueen", "rating": 298}, {"opponent": "r<PERSON><PERSON>", "rating": 314}, {"opponent": "electrode_hisuian", "rating": 314}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 16973}, {"moveId": "CHARGE_BEAM", "uses": 8027}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 9708}, {"moveId": "ANCIENT_POWER", "uses": 6443}, {"moveId": "THUNDERBOLT", "uses": 4929}, {"moveId": "THUNDER", "uses": 2176}, {"moveId": "ZAP_CANNON", "uses": 1733}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDER"], "score": 73.4}, {"speciesId": "avalugg_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 764, "matchups": [{"opponent": "nidoqueen", "rating": 871, "opRating": 128}, {"opponent": "electrode_hisuian", "rating": 871, "opRating": 128}, {"opponent": "electivire", "rating": 855, "opRating": 144}, {"opponent": "typhlosion", "rating": 743, "opRating": 256}, {"opponent": "rapidash", "rating": 743, "opRating": 256}], "counters": [{"opponent": "magby", "rating": 116}, {"opponent": "escavalier", "rating": 116}, {"opponent": "magmar", "rating": 138}, {"opponent": "durant", "rating": 155}, {"opponent": "melmetal", "rating": 209}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 12959}, {"moveId": "TACKLE", "uses": 7395}, {"moveId": "BITE", "uses": 4644}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 8782}, {"moveId": "ICY_WIND", "uses": 7302}, {"moveId": "CRUNCH", "uses": 5640}, {"moveId": "BLIZZARD", "uses": 3259}]}, "moveset": ["POWDER_SNOW", "ROCK_SLIDE", "ICY_WIND"], "score": 73.3}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 738, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 905, "opRating": 94}, {"opponent": "nidoqueen", "rating": 844, "opRating": 155}, {"opponent": "litleo", "rating": 825, "opRating": 174}, {"opponent": "crocalor", "rating": 825, "opRating": 174}, {"opponent": "rapidash", "rating": 806, "opRating": 193}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "klinklang", "rating": 147}, {"opponent": "magneton", "rating": 184}, {"opponent": "nidoking", "rating": 188}, {"opponent": "registeel", "rating": 276}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 19680}, {"moveId": "ACID", "uses": 5320}], "chargedMoves": [{"moveId": "SCALD", "uses": 11752}, {"moveId": "SLUDGE_WAVE", "uses": 3518}, {"moveId": "BLIZZARD", "uses": 3193}, {"moveId": "RETURN", "uses": 3044}, {"moveId": "HYDRO_PUMP", "uses": 2343}, {"moveId": "ACID_SPRAY", "uses": 1179}]}, "moveset": ["POISON_JAB", "ACID_SPRAY", "SCALD"], "score": 72.8}, {"speciesId": "dedenne", "speciesName": "Dedenne", "rating": 735, "matchups": [{"opponent": "electabuzz", "rating": 879, "opRating": 120}, {"opponent": "tyrunt", "rating": 872, "opRating": 127}, {"opponent": "arctibax", "rating": 823, "opRating": 176}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 789, "opRating": 210}, {"opponent": "r<PERSON><PERSON>", "rating": 714, "opRating": 285}], "counters": [{"opponent": "nidoqueen", "rating": 165}, {"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "nidoking", "rating": 192}, {"opponent": "stunfisk_galarian", "rating": 207}, {"opponent": "amoon<PERSON>s", "rating": 243}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15346}, {"moveId": "TACKLE", "uses": 9654}], "chargedMoves": [{"moveId": "PARABOLIC_CHARGE", "uses": 9456}, {"moveId": "DISCHARGE", "uses": 9381}, {"moveId": "PLAY_ROUGH", "uses": 6129}]}, "moveset": ["THUNDER_SHOCK", "PARABOLIC_CHARGE", "PLAY_ROUGH"], "score": 72.5}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 700, "matchups": [{"opponent": "victini", "rating": 823, "opRating": 176}, {"opponent": "talonflame", "rating": 737, "opRating": 262}, {"opponent": "rapidash", "rating": 702, "opRating": 297}, {"opponent": "pyroar", "rating": 702, "opRating": 297}, {"opponent": "armarouge", "rating": 702, "opRating": 297}], "counters": [{"opponent": "stunfisk_galarian", "rating": 204}, {"opponent": "magmar", "rating": 250}, {"opponent": "nidoqueen", "rating": 251}, {"opponent": "piloswine", "rating": 270}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 306}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 10998}, {"moveId": "SPARK", "uses": 7337}, {"moveId": "TACKLE", "uses": 6683}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 7972}, {"moveId": "FOUL_PLAY", "uses": 7771}, {"moveId": "RETURN", "uses": 4177}, {"moveId": "THUNDERBOLT", "uses": 3402}, {"moveId": "HYPER_BEAM", "uses": 1627}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "FOUL_PLAY"], "score": 71.7}, {"speciesId": "charmeleon", "speciesName": "Charmeleon", "rating": 722, "matchups": [{"opponent": "registeel", "rating": 897, "opRating": 102}, {"opponent": "electabuzz", "rating": 807, "opRating": 192}, {"opponent": "magnezone", "rating": 807, "opRating": 192}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "stunfisk_galarian", "rating": 759, "opRating": 240}], "counters": [{"opponent": "tyrunt", "rating": 131}, {"opponent": "toxapex", "rating": 194}, {"opponent": "tentacruel", "rating": 215}, {"opponent": "magmar", "rating": 227}, {"opponent": "magmortar", "rating": 259}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 11203}, {"moveId": "EMBER", "uses": 9507}, {"moveId": "SCRATCH", "uses": 4284}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 13564}, {"moveId": "RETURN", "uses": 4591}, {"moveId": "FLAMETHROWER", "uses": 4246}, {"moveId": "FLAME_BURST", "uses": 2556}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "RETURN"], "score": 71.2}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 710, "matchups": [{"opponent": "victini", "rating": 807, "opRating": 192}, {"opponent": "talonflame", "rating": 790, "opRating": 209}, {"opponent": "pyroar", "rating": 773, "opRating": 226}, {"opponent": "armarouge", "rating": 773, "opRating": 226}, {"opponent": "rapidash", "rating": 717, "opRating": 282}], "counters": [{"opponent": "lileep", "rating": 211}, {"opponent": "typhlosion", "rating": 213}, {"opponent": "cradily", "rating": 223}, {"opponent": "magmortar", "rating": 263}, {"opponent": "slowbro_galarian", "rating": 268}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 15230}, {"moveId": "WING_ATTACK", "uses": 9770}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 8080}, {"moveId": "OVERHEAT", "uses": 7886}, {"moveId": "SKY_ATTACK", "uses": 5359}, {"moveId": "FIRE_BLAST", "uses": 2318}, {"moveId": "HEAT_WAVE", "uses": 1354}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 70.8}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 766, "matchups": [{"opponent": "magmar", "rating": 884, "opRating": 115}, {"opponent": "qwilfish_his<PERSON>an", "rating": 847, "opRating": 152}, {"opponent": "victini", "rating": 847, "opRating": 152}, {"opponent": "ninetales", "rating": 787, "opRating": 212}, {"opponent": "litleo", "rating": 759, "opRating": 240}], "counters": [{"opponent": "cacturne", "rating": 119}, {"opponent": "stunky", "rating": 139}, {"opponent": "amoon<PERSON>s", "rating": 162}, {"opponent": "lokix", "rating": 169}, {"opponent": "umbreon", "rating": 209}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 9920}, {"moveId": "VOLT_SWITCH", "uses": 9679}, {"moveId": "SPARK", "uses": 5399}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10210}, {"moveId": "THUNDER_PUNCH", "uses": 5928}, {"moveId": "PSYCHIC", "uses": 3799}, {"moveId": "TRAILBLAZE", "uses": 3632}, {"moveId": "GRASS_KNOT", "uses": 1443}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "TRAILBLAZE"], "score": 70.7}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 791, "matchups": [{"opponent": "crocalor", "rating": 829, "opRating": 170}, {"opponent": "litleo", "rating": 814, "opRating": 185}, {"opponent": "talonflame", "rating": 814, "opRating": 185}, {"opponent": "rotom_heat", "rating": 814, "opRating": 185}, {"opponent": "stunfisk_galarian", "rating": 768, "opRating": 231}], "counters": [{"opponent": "over<PERSON><PERSON>l", "rating": 90}, {"opponent": "melmetal", "rating": 127}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "r<PERSON><PERSON>", "rating": 159}, {"opponent": "magmar", "rating": 160}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 8611}, {"moveId": "SNARL", "uses": 8234}, {"moveId": "FIRE_FANG", "uses": 8149}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 7989}, {"moveId": "BLAZE_KICK", "uses": 5870}, {"moveId": "DARKEST_LARIAT", "uses": 4193}, {"moveId": "DARK_PULSE", "uses": 3799}, {"moveId": "FLAME_CHARGE", "uses": 2221}, {"moveId": "FIRE_BLAST", "uses": 995}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 70.3}, {"speciesId": "linoone_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 703, "matchups": [{"opponent": "rotom_heat", "rating": 878, "opRating": 121}, {"opponent": "magnezone", "rating": 838, "opRating": 161}, {"opponent": "armarouge", "rating": 809, "opRating": 190}, {"opponent": "rapidash", "rating": 746, "opRating": 253}, {"opponent": "typhlosion", "rating": 684, "opRating": 315}], "counters": [{"opponent": "escavalier", "rating": 116}, {"opponent": "obstagoon", "rating": 199}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 239}, {"opponent": "registeel", "rating": 288}, {"opponent": "magmar", "rating": 325}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 14713}, {"moveId": "LICK", "uses": 10287}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 11550}, {"moveId": "DIG", "uses": 8334}, {"moveId": "RETURN", "uses": 2896}, {"moveId": "GUNK_SHOT", "uses": 2227}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 69.6}, {"speciesId": "minun", "speciesName": "<PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 724, "opRating": 275}, {"opponent": "over<PERSON><PERSON>l", "rating": 717, "opRating": 282}, {"opponent": "talonflame", "rating": 686, "opRating": 313}, {"opponent": "rapidash", "rating": 651, "opRating": 348}, {"opponent": "typhlosion", "rating": 631, "opRating": 368}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "roserade", "rating": 236}, {"opponent": "stunfisk_galarian", "rating": 239}, {"opponent": "arctibax", "rating": 307}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 334}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13519}, {"moveId": "SPARK", "uses": 11481}], "chargedMoves": [{"moveId": "SWIFT", "uses": 10401}, {"moveId": "DISCHARGE", "uses": 7215}, {"moveId": "GRASS_KNOT", "uses": 4298}, {"moveId": "THUNDERBOLT", "uses": 3102}]}, "moveset": ["QUICK_ATTACK", "THUNDERBOLT", "GRASS_KNOT"], "score": 69.5}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 738, "matchups": [{"opponent": "registeel", "rating": 880, "opRating": 119}, {"opponent": "piloswine", "rating": 830, "opRating": 169}, {"opponent": "magnezone", "rating": 752, "opRating": 247}, {"opponent": "stunfisk_galarian", "rating": 738, "opRating": 261}, {"opponent": "magby", "rating": 701, "opRating": 298}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "crocalor", "rating": 195}, {"opponent": "litleo", "rating": 199}, {"opponent": "umbreon", "rating": 206}, {"opponent": "magmar", "rating": 223}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 18074}, {"moveId": "SCRATCH", "uses": 4440}, {"moveId": "ZEN_HEADBUTT", "uses": 2471}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 9197}, {"moveId": "MYSTICAL_FIRE", "uses": 6600}, {"moveId": "PSYCHIC", "uses": 3413}, {"moveId": "FLAME_CHARGE", "uses": 2535}, {"moveId": "FLAMETHROWER", "uses": 2144}, {"moveId": "FIRE_BLAST", "uses": 1170}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 69.4}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 710, "matchups": [{"opponent": "<PERSON>rserker", "rating": 960, "opRating": 39}, {"opponent": "incineroar", "rating": 920, "opRating": 79}, {"opponent": "litleo", "rating": 778, "opRating": 221}, {"opponent": "pyroar", "rating": 750, "opRating": 250}, {"opponent": "rapidash", "rating": 688, "opRating": 311}], "counters": [{"opponent": "weezing_galarian", "rating": 178}, {"opponent": "dedenne", "rating": 240}, {"opponent": "magmortar", "rating": 254}, {"opponent": "magby", "rating": 266}, {"opponent": "magmar", "rating": 272}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 14018}, {"moveId": "LICK", "uses": 10982}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 10860}, {"moveId": "CROSS_CHOP", "uses": 9196}, {"moveId": "HYPER_BEAM", "uses": 2993}, {"moveId": "GUNK_SHOT", "uses": 1742}, {"moveId": "OBSTRUCT", "uses": 250}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "CROSS_CHOP"], "score": 69.4}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 753, "matchups": [{"opponent": "incineroar", "rating": 868, "opRating": 131}, {"opponent": "qwilfish_his<PERSON>an", "rating": 851, "opRating": 148}, {"opponent": "victini", "rating": 826, "opRating": 173}, {"opponent": "magmar", "rating": 817, "opRating": 182}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 665, "opRating": 334}], "counters": [{"opponent": "weezing_galarian", "rating": 147}, {"opponent": "raticate_alolan", "rating": 165}, {"opponent": "sandslash_alolan", "rating": 165}, {"opponent": "castform_sunny", "rating": 167}, {"opponent": "avalugg_his<PERSON>an", "rating": 177}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12605}, {"moveId": "VOLT_SWITCH", "uses": 12395}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 14188}, {"moveId": "SHADOW_BALL", "uses": 5977}, {"moveId": "THUNDERBOLT", "uses": 2560}, {"moveId": "THUNDER", "uses": 2263}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SHADOW_BALL"], "score": 69.2}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 693, "matchups": [{"opponent": "electivire", "rating": 849, "opRating": 150}, {"opponent": "incineroar", "rating": 825, "opRating": 174}, {"opponent": "magmortar", "rating": 800, "opRating": 199}, {"opponent": "talonflame", "rating": 747, "opRating": 252}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 616, "opRating": 383}], "counters": [{"opponent": "stunfisk_galarian", "rating": 198}, {"opponent": "cradily", "rating": 206}, {"opponent": "ferrothorn", "rating": 224}, {"opponent": "steelix", "rating": 298}, {"opponent": "rotom_heat", "rating": 301}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12806}, {"moveId": "THUNDER_SHOCK", "uses": 12194}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 8775}, {"moveId": "LAST_RESORT", "uses": 6534}, {"moveId": "THUNDERBOLT", "uses": 3761}, {"moveId": "THUNDER", "uses": 3272}, {"moveId": "ZAP_CANNON", "uses": 2666}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "LAST_RESORT"], "score": 69}, {"speciesId": "ponyta", "speciesName": "Ponyta", "rating": 728, "matchups": [{"opponent": "sandslash_alolan", "rating": 889, "opRating": 110}, {"opponent": "registeel", "rating": 876, "opRating": 123}, {"opponent": "rotom_heat", "rating": 765, "opRating": 234}, {"opponent": "magnezone", "rating": 730, "opRating": 269}, {"opponent": "rapidash", "rating": 707, "opRating": 292}], "counters": [{"opponent": "magmar", "rating": 241}, {"opponent": "magmortar", "rating": 259}, {"opponent": "tyrunt", "rating": 259}, {"opponent": "talonflame", "rating": 274}, {"opponent": "pyroar", "rating": 288}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15563}, {"moveId": "TACKLE", "uses": 9437}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 10667}, {"moveId": "STOMP", "uses": 7542}, {"moveId": "FIRE_BLAST", "uses": 4817}, {"moveId": "FLAME_WHEEL", "uses": 2031}]}, "moveset": ["EMBER", "FLAME_CHARGE", "STOMP"], "score": 68.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 722, "matchups": [{"opponent": "em<PERSON>ga", "rating": 875, "opRating": 125}, {"opponent": "incineroar", "rating": 842, "opRating": 157}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 754, "opRating": 245}, {"opponent": "r<PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 685, "opRating": 314}], "counters": [{"opponent": "armarouge", "rating": 214}, {"opponent": "arcanine", "rating": 217}, {"opponent": "rapidash", "rating": 220}, {"opponent": "typhlosion", "rating": 226}, {"opponent": "pyroar", "rating": 228}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 13802}, {"moveId": "FURY_CUTTER", "uses": 11198}], "chargedMoves": [{"moveId": "LUNGE", "uses": 5825}, {"moveId": "DISCHARGE", "uses": 5709}, {"moveId": "CROSS_POISON", "uses": 5231}, {"moveId": "BUG_BUZZ", "uses": 3755}, {"moveId": "RETURN", "uses": 2448}, {"moveId": "ENERGY_BALL", "uses": 2018}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LUNGE"], "score": 68.8}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 755, "matchups": [{"opponent": "over<PERSON><PERSON>l", "rating": 879, "opRating": 120}, {"opponent": "incineroar", "rating": 872, "opRating": 127}, {"opponent": "magnezone", "rating": 868, "opRating": 131}, {"opponent": "rotom_heat", "rating": 822, "opRating": 177}, {"opponent": "avalugg_his<PERSON>an", "rating": 812, "opRating": 187}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 144}, {"opponent": "r<PERSON><PERSON>", "rating": 203}, {"opponent": "magmar", "rating": 236}, {"opponent": "stunfisk_galarian", "rating": 242}, {"opponent": "electrode_hisuian", "rating": 256}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 25000}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 7784}, {"moveId": "DOUBLE_IRON_BASH", "uses": 5898}, {"moveId": "ROCK_SLIDE", "uses": 5808}, {"moveId": "THUNDERBOLT", "uses": 2789}, {"moveId": "HYPER_BEAM", "uses": 1844}, {"moveId": "FLASH_CANNON", "uses": 880}]}, "moveset": ["THUNDER_SHOCK", "DOUBLE_IRON_BASH", "SUPER_POWER"], "score": 68.8}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 727, "matchups": [{"opponent": "victini", "rating": 859, "opRating": 140}, {"opponent": "rotom_heat", "rating": 845, "opRating": 154}, {"opponent": "electrode_hisuian", "rating": 845, "opRating": 154}, {"opponent": "typhlosion", "rating": 736, "opRating": 263}, {"opponent": "rapidash", "rating": 736, "opRating": 263}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magnezone", "rating": 151}, {"opponent": "nidoking", "rating": 204}, {"opponent": "steelix", "rating": 209}, {"opponent": "stunfisk_galarian", "rating": 221}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 8863}, {"moveId": "LICK", "uses": 6403}, {"moveId": "INFESTATION", "uses": 6101}, {"moveId": "ACID", "uses": 3649}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 8762}, {"moveId": "DARK_PULSE", "uses": 7354}, {"moveId": "SLUDGE_WAVE", "uses": 5122}, {"moveId": "GUNK_SHOT", "uses": 2028}, {"moveId": "ACID_SPRAY", "uses": 1753}]}, "moveset": ["POISON_JAB", "THUNDER_PUNCH", "DARK_PULSE"], "score": 68.8}, {"speciesId": "lokix", "speciesName": "<PERSON><PERSON>", "rating": 711, "matchups": [{"opponent": "victini", "rating": 830, "opRating": 169}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 830, "opRating": 169}, {"opponent": "rai<PERSON>u", "rating": 830, "opRating": 169}, {"opponent": "rotom_heat", "rating": 814, "opRating": 185}, {"opponent": "nidoqueen", "rating": 657, "opRating": 342}], "counters": [{"opponent": "houndoom", "rating": 205}, {"opponent": "electrode_hisuian", "rating": 283}, {"opponent": "arcanine", "rating": 299}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 301}, {"opponent": "litleo", "rating": 304}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 11806}, {"moveId": "COUNTER", "uses": 7414}, {"moveId": "BUG_BITE", "uses": 5813}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 9324}, {"moveId": "DARK_PULSE", "uses": 8564}, {"moveId": "TRAILBLAZE", "uses": 4600}, {"moveId": "BUG_BUZZ", "uses": 2534}]}, "moveset": ["SUCKER_PUNCH", "X_SCISSOR", "TRAILBLAZE"], "score": 68.6}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 684, "matchups": [{"opponent": "victini", "rating": 842, "opRating": 157}, {"opponent": "umbreon", "rating": 834, "opRating": 165}, {"opponent": "nidoking", "rating": 808, "opRating": 191}, {"opponent": "qwilfish_his<PERSON>an", "rating": 706, "opRating": 293}, {"opponent": "magmar", "rating": 654, "opRating": 345}], "counters": [{"opponent": "magnezone", "rating": 210}, {"opponent": "golbat", "rating": 250}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 252}, {"opponent": "r<PERSON><PERSON>", "rating": 283}, {"opponent": "electivire", "rating": 318}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 13154}, {"moveId": "BUG_BITE", "uses": 11846}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 8983}, {"moveId": "BUG_BUZZ", "uses": 7717}, {"moveId": "BUBBLE_BEAM", "uses": 5067}, {"moveId": "MIRROR_COAT", "uses": 3212}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 68.5}, {"speciesId": "amaura", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 721, "matchups": [{"opponent": "zapdos", "rating": 890, "opRating": 109}, {"opponent": "em<PERSON>ga", "rating": 887, "opRating": 112}, {"opponent": "piloswine", "rating": 878, "opRating": 121}, {"opponent": "nidoqueen", "rating": 853, "opRating": 146}, {"opponent": "talonflame", "rating": 765, "opRating": 234}], "counters": [{"opponent": "melmetal", "rating": 191}, {"opponent": "magby", "rating": 237}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 239}, {"opponent": "magmar", "rating": 254}, {"opponent": "magmortar", "rating": 254}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 16985}, {"moveId": "FROST_BREATH", "uses": 8015}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 11259}, {"moveId": "ANCIENT_POWER", "uses": 8823}, {"moveId": "THUNDERBOLT", "uses": 3534}, {"moveId": "AURORA_BEAM", "uses": 1327}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 68.2}, {"speciesId": "growlithe", "speciesName": "Grow<PERSON>he", "rating": 713, "matchups": [{"opponent": "registeel", "rating": 896, "opRating": 103}, {"opponent": "sandslash_alolan", "rating": 877, "opRating": 122}, {"opponent": "piloswine", "rating": 848, "opRating": 151}, {"opponent": "bisharp", "rating": 840, "opRating": 159}, {"opponent": "camerupt", "rating": 718, "opRating": 281}], "counters": [{"opponent": "toxapex", "rating": 203}, {"opponent": "magmar", "rating": 227}, {"opponent": "tyrunt", "rating": 240}, {"opponent": "<PERSON>on", "rating": 254}, {"opponent": "nidoqueen", "rating": 262}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17419}, {"moveId": "BITE", "uses": 7581}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 10188}, {"moveId": "FLAMETHROWER", "uses": 10023}, {"moveId": "RETURN", "uses": 2580}, {"moveId": "FLAME_WHEEL", "uses": 2229}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 67.1}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 739, "matchups": [{"opponent": "rotom_heat", "rating": 927, "opRating": 72}, {"opponent": "electabuzz", "rating": 920, "opRating": 79}, {"opponent": "victini", "rating": 824, "opRating": 175}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 734, "opRating": 265}, {"opponent": "stunfisk_galarian", "rating": 706, "opRating": 293}], "counters": [{"opponent": "amaura", "rating": 118}, {"opponent": "registeel", "rating": 123}, {"opponent": "sandslash_alolan", "rating": 173}, {"opponent": "melmetal", "rating": 205}, {"opponent": "avalugg_his<PERSON>an", "rating": 210}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 18396}, {"moveId": "ROCK_SMASH", "uses": 6604}], "chargedMoves": [{"moveId": "SWIFT", "uses": 5516}, {"moveId": "HIGH_HORSEPOWER", "uses": 4916}, {"moveId": "FIRE_PUNCH", "uses": 4295}, {"moveId": "ICE_PUNCH", "uses": 2965}, {"moveId": "THUNDER_PUNCH", "uses": 2810}, {"moveId": "AERIAL_ACE", "uses": 2455}, {"moveId": "TRAILBLAZE", "uses": 2045}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 66.5}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 706, "matchups": [{"opponent": "sandslash_alolan", "rating": 907, "opRating": 92}, {"opponent": "registeel", "rating": 882, "opRating": 117}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 815, "opRating": 184}, {"opponent": "rotom_heat", "rating": 722, "opRating": 277}, {"opponent": "rapidash", "rating": 701, "opRating": 298}], "counters": [{"opponent": "tyrunt", "rating": 189}, {"opponent": "magby", "rating": 229}, {"opponent": "talonflame", "rating": 251}, {"opponent": "magmar", "rating": 272}, {"opponent": "litleo", "rating": 276}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 13488}, {"moveId": "SNARL", "uses": 11512}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 9972}, {"moveId": "FLAMETHROWER", "uses": 8447}, {"moveId": "FOUL_PLAY", "uses": 4275}, {"moveId": "FIRE_BLAST", "uses": 2335}]}, "moveset": ["FIRE_FANG", "CRUNCH", "FLAMETHROWER"], "score": 66.3}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 732, "matchups": [{"opponent": "rotom_heat", "rating": 832, "opRating": 167}, {"opponent": "victini", "rating": 832, "opRating": 167}, {"opponent": "electabuzz", "rating": 832, "opRating": 167}, {"opponent": "litleo", "rating": 688, "opRating": 311}, {"opponent": "rapidash", "rating": 671, "opRating": 328}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "sandslash_alolan", "rating": 125}, {"opponent": "nidoking", "rating": 145}, {"opponent": "magnezone", "rating": 161}, {"opponent": "heatran", "rating": 168}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 10499}, {"moveId": "SNARL", "uses": 9546}, {"moveId": "BITE", "uses": 4955}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 13592}, {"moveId": "SLUDGE_WAVE", "uses": 6631}, {"moveId": "GUNK_SHOT", "uses": 2568}, {"moveId": "ACID_SPRAY", "uses": 2213}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "ACID_SPRAY"], "score": 66.1}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 681, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 820, "opRating": 179}, {"opponent": "electivire", "rating": 820, "opRating": 179}, {"opponent": "magmar", "rating": 762, "opRating": 237}, {"opponent": "magmortar", "rating": 762, "opRating": 237}, {"opponent": "magby", "rating": 762, "opRating": 237}], "counters": [{"opponent": "camerupt", "rating": 77}, {"opponent": "gloom", "rating": 96}, {"opponent": "vileplume", "rating": 103}, {"opponent": "electrode_hisuian", "rating": 287}, {"opponent": "stunfisk_galarian", "rating": 292}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15086}, {"moveId": "SPARK", "uses": 9914}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 10927}, {"moveId": "POWER_WHIP", "uses": 6481}, {"moveId": "THUNDER", "uses": 4038}, {"moveId": "DAZZLING_GLEAM", "uses": 3505}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "POWER_WHIP"], "score": 66.1}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 671, "matchups": [{"opponent": "talonflame", "rating": 849, "opRating": 150}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 842, "opRating": 157}, {"opponent": "darmanitan_standard", "rating": 818, "opRating": 181}, {"opponent": "charizard", "rating": 807, "opRating": 192}, {"opponent": "manectric", "rating": 716, "opRating": 283}], "counters": [{"opponent": "registeel", "rating": 184}, {"opponent": "metang", "rating": 209}, {"opponent": "revavroom", "rating": 232}, {"opponent": "sandslash_alolan", "rating": 241}, {"opponent": "melmetal", "rating": 255}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 11372}, {"moveId": "INFESTATION", "uses": 10152}, {"moveId": "ACID", "uses": 3466}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 7356}, {"moveId": "ROCK_SLIDE", "uses": 6963}, {"moveId": "BULLDOZE", "uses": 4751}, {"moveId": "GRASS_KNOT", "uses": 3830}, {"moveId": "RETURN", "uses": 2135}]}, "moveset": ["BULLET_SEED", "ROCK_SLIDE", "GRASS_KNOT"], "score": 65.8}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 695, "matchups": [{"opponent": "incineroar", "rating": 815, "opRating": 185}, {"opponent": "litleo", "rating": 705, "opRating": 295}, {"opponent": "stunfisk_galarian", "rating": 695, "opRating": 305}, {"opponent": "typhlosion", "rating": 679, "opRating": 320}, {"opponent": "rapidash", "rating": 679, "opRating": 320}], "counters": [{"opponent": "talonflame", "rating": 217}, {"opponent": "victini", "rating": 220}, {"opponent": "crocalor", "rating": 222}, {"opponent": "qwilfish_his<PERSON>an", "rating": 233}, {"opponent": "magmar", "rating": 254}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 14565}, {"moveId": "EMBER", "uses": 10435}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 8019}, {"moveId": "FLAMETHROWER", "uses": 5733}, {"moveId": "OVERHEAT", "uses": 5329}, {"moveId": "LAST_RESORT", "uses": 3494}, {"moveId": "FIRE_BLAST", "uses": 1513}, {"moveId": "HEAT_WAVE", "uses": 923}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 65.8}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 715, "matchups": [{"opponent": "arctibax", "rating": 820, "opRating": 179}, {"opponent": "avalugg_his<PERSON>an", "rating": 816, "opRating": 183}, {"opponent": "talonflame", "rating": 762, "opRating": 237}, {"opponent": "typhlosion", "rating": 745, "opRating": 254}, {"opponent": "rapidash", "rating": 745, "opRating": 254}], "counters": [{"opponent": "escavalier", "rating": 130}, {"opponent": "registeel", "rating": 196}, {"opponent": "melmetal", "rating": 216}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 219}, {"opponent": "stunfisk_galarian", "rating": 230}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 16840}, {"moveId": "IRON_TAIL", "uses": 8160}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 9450}, {"moveId": "BODY_SLAM", "uses": 7058}, {"moveId": "HEAVY_SLAM", "uses": 3922}, {"moveId": "ROCK_TOMB", "uses": 2714}, {"moveId": "RETURN", "uses": 1834}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 65.7}, {"speciesId": "seviper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 692, "matchups": [{"opponent": "magmortar", "rating": 842, "opRating": 157}, {"opponent": "incineroar", "rating": 834, "opRating": 165}, {"opponent": "r<PERSON><PERSON>", "rating": 803, "opRating": 196}, {"opponent": "rotom_heat", "rating": 803, "opRating": 196}, {"opponent": "typhlosion", "rating": 626, "opRating": 373}], "counters": [{"opponent": "bronzong", "rating": 96}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 124}, {"opponent": "bisharp", "rating": 165}, {"opponent": "steelix", "rating": 189}, {"opponent": "stunfisk_galarian", "rating": 227}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 18965}, {"moveId": "IRON_TAIL", "uses": 6035}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 10048}, {"moveId": "POISON_FANG", "uses": 9299}, {"moveId": "WRAP", "uses": 5667}]}, "moveset": ["POISON_JAB", "POISON_FANG", "CRUNCH"], "score": 65.4}, {"speciesId": "toxapex", "speciesName": "Toxapex", "rating": 727, "matchups": [{"opponent": "magby", "rating": 796, "opRating": 203}, {"opponent": "ninetales", "rating": 796, "opRating": 203}, {"opponent": "litleo", "rating": 758, "opRating": 241}, {"opponent": "arcanine", "rating": 728, "opRating": 271}, {"opponent": "typhlosion", "rating": 707, "opRating": 292}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magnezone", "rating": 102}, {"opponent": "klinklang", "rating": 132}, {"opponent": "stunfisk_galarian", "rating": 218}, {"opponent": "r<PERSON><PERSON>", "rating": 225}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 16155}, {"moveId": "BITE", "uses": 8845}], "chargedMoves": [{"moveId": "BRINE", "uses": 13297}, {"moveId": "SLUDGE_WAVE", "uses": 8391}, {"moveId": "GUNK_SHOT", "uses": 3308}]}, "moveset": ["POISON_JAB", "BRINE", "SLUDGE_WAVE"], "score": 65.4}, {"speciesId": "a<PERSON><PERSON>", "speciesName": "Ariados", "rating": 672, "matchups": [{"opponent": "electrode_hisuian", "rating": 882, "opRating": 117}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 865, "opRating": 134}, {"opponent": "qwilfish_his<PERSON>an", "rating": 762, "opRating": 237}, {"opponent": "electivire", "rating": 687, "opRating": 312}, {"opponent": "magmar", "rating": 617, "opRating": 382}], "counters": [{"opponent": "crocalor", "rating": 280}, {"opponent": "heatran", "rating": 286}, {"opponent": "litleo", "rating": 290}, {"opponent": "charizard", "rating": 294}, {"opponent": "steelix", "rating": 310}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14219}, {"moveId": "INFESTATION", "uses": 10781}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 7652}, {"moveId": "LUNGE", "uses": 5657}, {"moveId": "MEGAHORN", "uses": 5441}, {"moveId": "TRAILBLAZE", "uses": 3446}, {"moveId": "SHADOW_SNEAK", "uses": 2777}]}, "moveset": ["POISON_STING", "LUNGE", "TRAILBLAZE"], "score": 65.1}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 749, "matchups": [{"opponent": "electabuzz", "rating": 932, "opRating": 67}, {"opponent": "rotom_heat", "rating": 919, "opRating": 80}, {"opponent": "over<PERSON><PERSON>l", "rating": 835, "opRating": 164}, {"opponent": "nidoqueen", "rating": 829, "opRating": 170}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 777, "opRating": 222}], "counters": [{"opponent": "magmar", "rating": 125}, {"opponent": "victini", "rating": 127}, {"opponent": "magmortar", "rating": 129}, {"opponent": "typhlosion", "rating": 132}, {"opponent": "magby", "rating": 154}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 15137}, {"moveId": "ICE_SHARD", "uses": 9863}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 7684}, {"moveId": "HIGH_HORSEPOWER", "uses": 5989}, {"moveId": "BULLDOZE", "uses": 4902}, {"moveId": "STONE_EDGE", "uses": 4740}, {"moveId": "RETURN", "uses": 1656}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "HIGH_HORSEPOWER"], "score": 65.1}, {"speciesId": "fletchinder", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 699, "matchups": [{"opponent": "nidoking", "rating": 799, "opRating": 200}, {"opponent": "rapidash", "rating": 767, "opRating": 232}, {"opponent": "magmar", "rating": 732, "opRating": 267}, {"opponent": "darmanitan_standard", "rating": 732, "opRating": 267}, {"opponent": "arcanine", "rating": 704, "opRating": 295}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "cradily", "rating": 195}, {"opponent": "avalugg_his<PERSON>an", "rating": 227}, {"opponent": "electabuzz", "rating": 239}, {"opponent": "magmortar", "rating": 259}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 12673}, {"moveId": "STEEL_WING", "uses": 6723}, {"moveId": "PECK", "uses": 5592}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 8652}, {"moveId": "FLY", "uses": 8631}, {"moveId": "AERIAL_ACE", "uses": 6568}, {"moveId": "HEAT_WAVE", "uses": 1205}]}, "moveset": ["EMBER", "FLY", "AERIAL_ACE"], "score": 65}, {"speciesId": "archen", "speciesName": "<PERSON><PERSON>", "rating": 702, "matchups": [{"opponent": "victini", "rating": 856, "opRating": 143}, {"opponent": "magmar", "rating": 786, "opRating": 213}, {"opponent": "litleo", "rating": 786, "opRating": 213}, {"opponent": "typhlosion", "rating": 773, "opRating": 226}, {"opponent": "rapidash", "rating": 760, "opRating": 239}], "counters": [{"opponent": "magnemite", "rating": 123}, {"opponent": "steelix", "rating": 149}, {"opponent": "manectric", "rating": 154}, {"opponent": "stunfisk_galarian", "rating": 177}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13531}, {"moveId": "WING_ATTACK", "uses": 11469}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 9005}, {"moveId": "DRAGON_CLAW", "uses": 6818}, {"moveId": "CRUNCH", "uses": 6379}, {"moveId": "RETURN", "uses": 2834}]}, "moveset": ["QUICK_ATTACK", "ANCIENT_POWER", "DRAGON_CLAW"], "score": 64.8}, {"speciesId": "beedrill", "speciesName": "Beedrill", "rating": 707, "matchups": [{"opponent": "nidoking", "rating": 914, "opRating": 85}, {"opponent": "incineroar", "rating": 860, "opRating": 139}, {"opponent": "victini", "rating": 841, "opRating": 158}, {"opponent": "rotom_heat", "rating": 825, "opRating": 174}, {"opponent": "over<PERSON><PERSON>l", "rating": 782, "opRating": 217}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magnezone", "rating": 151}, {"opponent": "camerupt", "rating": 209}, {"opponent": "heatran", "rating": 254}, {"opponent": "arcanine", "rating": 290}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 9885}, {"moveId": "INFESTATION", "uses": 8793}, {"moveId": "BUG_BITE", "uses": 6301}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 9203}, {"moveId": "X_SCISSOR", "uses": 5599}, {"moveId": "AERIAL_ACE", "uses": 3649}, {"moveId": "SLUDGE_BOMB", "uses": 3210}, {"moveId": "RETURN", "uses": 1843}, {"moveId": "FELL_STINGER", "uses": 1459}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "DRILL_RUN"], "score": 64.4}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 700, "matchups": [{"opponent": "registeel", "rating": 859, "opRating": 140}, {"opponent": "talonflame", "rating": 776, "opRating": 223}, {"opponent": "camerupt", "rating": 754, "opRating": 245}, {"opponent": "ninetales", "rating": 710, "opRating": 289}, {"opponent": "magmar", "rating": 548, "opRating": 451}], "counters": [{"opponent": "tyrunt", "rating": 127}, {"opponent": "heatmor", "rating": 209}, {"opponent": "magmortar", "rating": 226}, {"opponent": "armarouge", "rating": 245}, {"opponent": "rapidash", "rating": 252}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 6721}, {"moveId": "EXTRASENSORY", "uses": 1614}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1579}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1445}, {"moveId": "STEEL_WING", "uses": 1296}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1270}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1162}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1105}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1058}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 877}, {"moveId": "HIDDEN_POWER_DARK", "uses": 859}, {"moveId": "HIDDEN_POWER_ICE", "uses": 846}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 839}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 816}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 812}, {"moveId": "HIDDEN_POWER_BUG", "uses": 774}, {"moveId": "HIDDEN_POWER_POISON", "uses": 705}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 626}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 555}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 9023}, {"moveId": "SACRED_FIRE", "uses": 7784}, {"moveId": "EARTHQUAKE", "uses": 5308}, {"moveId": "FIRE_BLAST", "uses": 1550}, {"moveId": "SOLAR_BEAM", "uses": 1308}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 64.4}, {"speciesId": "eelektrik", "speciesName": "Eelektrik", "rating": 654, "matchups": [{"opponent": "zapdos", "rating": 872, "opRating": 127}, {"opponent": "talonflame", "rating": 725, "opRating": 274}, {"opponent": "rapidash", "rating": 710, "opRating": 289}, {"opponent": "armarouge", "rating": 710, "opRating": 289}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 680, "opRating": 319}], "counters": [{"opponent": "victreebel", "rating": 175}, {"opponent": "roserade", "rating": 178}, {"opponent": "stunfisk_galarian", "rating": 210}, {"opponent": "magmar", "rating": 227}, {"opponent": "nidoqueen", "rating": 273}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 17542}, {"moveId": "ACID", "uses": 7458}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 11107}, {"moveId": "DISCHARGE", "uses": 9715}, {"moveId": "THUNDERBOLT", "uses": 4132}]}, "moveset": ["SPARK", "CRUNCH", "DISCHARGE"], "score": 63.4}, {"speciesId": "braixen", "speciesName": "Braixen", "rating": 692, "matchups": [{"opponent": "sandslash_alolan", "rating": 886, "opRating": 113}, {"opponent": "piloswine", "rating": 849, "opRating": 150}, {"opponent": "rotom_heat", "rating": 784, "opRating": 215}, {"opponent": "magnezone", "rating": 752, "opRating": 247}, {"opponent": "arcanine", "rating": 691, "opRating": 308}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 227}, {"opponent": "litleo", "rating": 230}, {"opponent": "magmortar", "rating": 259}, {"opponent": "pyroar", "rating": 264}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 18062}, {"moveId": "SCRATCH", "uses": 6938}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11714}, {"moveId": "PSYSHOCK", "uses": 8390}, {"moveId": "FLAMETHROWER", "uses": 4878}]}, "moveset": ["EMBER", "FLAME_CHARGE", "PSYSHOCK"], "score": 63.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 710, "matchups": [{"opponent": "rotom_heat", "rating": 853, "opRating": 146}, {"opponent": "incineroar", "rating": 853, "opRating": 146}, {"opponent": "electabuzz", "rating": 853, "opRating": 146}, {"opponent": "typhlosion", "rating": 710, "opRating": 289}, {"opponent": "rapidash", "rating": 696, "opRating": 303}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magnezone", "rating": 102}, {"opponent": "r<PERSON><PERSON>", "rating": 181}, {"opponent": "magmar", "rating": 183}, {"opponent": "electivire", "rating": 186}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13350}, {"moveId": "POISON_JAB", "uses": 11650}], "chargedMoves": [{"moveId": "DIG", "uses": 7844}, {"moveId": "SLUDGE_BOMB", "uses": 5227}, {"moveId": "HORN_ATTACK", "uses": 4633}, {"moveId": "ICE_BEAM", "uses": 4327}, {"moveId": "RETURN", "uses": 2923}]}, "moveset": ["POISON_STING", "SLUDGE_BOMB", "DIG"], "score": 63.3}, {"speciesId": "elekid", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 644, "matchups": [{"opponent": "electivire", "rating": 833, "opRating": 166}, {"opponent": "talonflame", "rating": 750, "opRating": 250}, {"opponent": "charizard", "rating": 745, "opRating": 254}, {"opponent": "darmanitan_standard", "rating": 683, "opRating": 316}, {"opponent": "r<PERSON><PERSON>", "rating": 654, "opRating": 345}], "counters": [{"opponent": "nidoqueen", "rating": 223}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 266}, {"opponent": "nidoking", "rating": 287}, {"opponent": "ninetales", "rating": 301}, {"opponent": "rotom_heat", "rating": 306}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20207}, {"moveId": "LOW_KICK", "uses": 4793}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 10237}, {"moveId": "BRICK_BREAK", "uses": 7811}, {"moveId": "DISCHARGE", "uses": 3781}, {"moveId": "THUNDERBOLT", "uses": 3222}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "BRICK_BREAK"], "score": 62.9}, {"speciesId": "raboot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 693, "matchups": [{"opponent": "sandslash_alolan", "rating": 903, "opRating": 96}, {"opponent": "registeel", "rating": 888, "opRating": 111}, {"opponent": "piloswine", "rating": 857, "opRating": 142}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 796, "opRating": 203}, {"opponent": "magnezone", "rating": 746, "opRating": 253}], "counters": [{"opponent": "magmar", "rating": 223}, {"opponent": "talonflame", "rating": 225}, {"opponent": "magmortar", "rating": 240}, {"opponent": "typhlosion", "rating": 260}, {"opponent": "rapidash", "rating": 265}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16783}, {"moveId": "TACKLE", "uses": 8217}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 17646}, {"moveId": "FLAMETHROWER", "uses": 7354}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 62.9}, {"speciesId": "toxtricity", "speciesName": "Toxtricity", "rating": 727, "matchups": [{"opponent": "rotom_heat", "rating": 857, "opRating": 142}, {"opponent": "rai<PERSON>u", "rating": 857, "opRating": 142}, {"opponent": "incineroar", "rating": 819, "opRating": 180}, {"opponent": "litleo", "rating": 642, "opRating": 357}, {"opponent": "typhlosion", "rating": 626, "opRating": 373}], "counters": [{"opponent": "stunfisk_galarian", "rating": 133}, {"opponent": "nidoking", "rating": 133}, {"opponent": "sandslash_alolan", "rating": 137}, {"opponent": "nidoqueen", "rating": 154}, {"opponent": "avalugg_his<PERSON>an", "rating": 173}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 11658}, {"moveId": "SPARK", "uses": 9536}, {"moveId": "ACID", "uses": 3832}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 16124}, {"moveId": "POWER_UP_PUNCH", "uses": 3639}, {"moveId": "DISCHARGE", "uses": 3432}, {"moveId": "ACID_SPRAY", "uses": 1834}]}, "moveset": ["POISON_JAB", "WILD_CHARGE", "DISCHARGE"], "score": 62.6}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 695, "matchups": [{"opponent": "sandslash_alolan", "rating": 911, "opRating": 88}, {"opponent": "registeel", "rating": 886, "opRating": 113}, {"opponent": "piloswine", "rating": 840, "opRating": 159}, {"opponent": "magnezone", "rating": 797, "opRating": 202}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 794, "opRating": 205}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "tentacruel", "rating": 215}, {"opponent": "magmar", "rating": 227}, {"opponent": "litleo", "rating": 230}, {"opponent": "ninetales", "rating": 242}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 13557}, {"moveId": "FIRE_FANG", "uses": 11443}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 8346}, {"moveId": "FLAME_CHARGE", "uses": 6054}, {"moveId": "OVERHEAT", "uses": 4700}, {"moveId": "FLAMETHROWER", "uses": 2495}, {"moveId": "IRON_HEAD", "uses": 2018}, {"moveId": "FIRE_BLAST", "uses": 1393}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 62.1}, {"speciesId": "plusle", "speciesName": "<PERSON><PERSON>", "rating": 667, "matchups": [{"opponent": "incineroar", "rating": 836, "opRating": 164}, {"opponent": "piloswine", "rating": 776, "opRating": 224}, {"opponent": "r<PERSON><PERSON>", "rating": 712, "opRating": 288}, {"opponent": "talonflame", "rating": 636, "opRating": 364}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 628, "opRating": 372}], "counters": [{"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "genesect", "rating": 155}, {"opponent": "victini", "rating": 170}, {"opponent": "magmar", "rating": 183}, {"opponent": "ninetales", "rating": 194}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13591}, {"moveId": "SPARK", "uses": 11409}], "chargedMoves": [{"moveId": "SWIFT", "uses": 10406}, {"moveId": "DISCHARGE", "uses": 7225}, {"moveId": "GRASS_KNOT", "uses": 4291}, {"moveId": "THUNDERBOLT", "uses": 3092}]}, "moveset": ["QUICK_ATTACK", "THUNDERBOLT", "GRASS_KNOT"], "score": 62.1}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 665, "matchups": [{"opponent": "sandslash_alolan", "rating": 917, "opRating": 82}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 811, "opRating": 188}, {"opponent": "piloswine", "rating": 744, "opRating": 255}, {"opponent": "castform_sunny", "rating": 700, "opRating": 299}, {"opponent": "ninetales", "rating": 614, "opRating": 385}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "tentacruel", "rating": 174}, {"opponent": "arcanine", "rating": 225}, {"opponent": "talonflame", "rating": 232}, {"opponent": "nidoqueen", "rating": 262}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 14228}, {"moveId": "EMBER", "uses": 10772}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 11882}, {"moveId": "EARTHQUAKE", "uses": 10469}, {"moveId": "SOLAR_BEAM", "uses": 2660}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 61.7}, {"speciesId": "lileep", "speciesName": "<PERSON><PERSON>", "rating": 640, "matchups": [{"opponent": "u<PERSON><PERSON><PERSON>", "rating": 850, "opRating": 149}, {"opponent": "charizard", "rating": 817, "opRating": 182}, {"opponent": "piloswine", "rating": 778, "opRating": 221}, {"opponent": "darmanitan_standard", "rating": 698, "opRating": 301}, {"opponent": "electrode_hisuian", "rating": 672, "opRating": 327}], "counters": [{"opponent": "metang", "rating": 201}, {"opponent": "revavroom", "rating": 214}, {"opponent": "sandslash_alolan", "rating": 229}, {"opponent": "melmetal", "rating": 248}, {"opponent": "ninetales", "rating": 293}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 10878}, {"moveId": "INFESTATION", "uses": 9998}, {"moveId": "ACID", "uses": 4136}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 12006}, {"moveId": "GRASS_KNOT", "uses": 6650}, {"moveId": "RETURN", "uses": 3760}, {"moveId": "MIRROR_COAT", "uses": 2561}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "ANCIENT_POWER"], "score": 61.6}, {"speciesId": "torracat", "speciesName": "Torracat", "rating": 686, "matchups": [{"opponent": "registeel", "rating": 880, "opRating": 119}, {"opponent": "sandslash_alolan", "rating": 876, "opRating": 123}, {"opponent": "rotom_heat", "rating": 753, "opRating": 246}, {"opponent": "rapidash", "rating": 716, "opRating": 283}, {"opponent": "magnezone", "rating": 716, "opRating": 283}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "victini", "rating": 224}, {"opponent": "magmar", "rating": 227}, {"opponent": "litleo", "rating": 230}, {"opponent": "magmortar", "rating": 259}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17884}, {"moveId": "BITE", "uses": 7116}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11275}, {"moveId": "CRUNCH", "uses": 8990}, {"moveId": "FLAMETHROWER", "uses": 4723}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 61.2}, {"speciesId": "poipole", "speciesName": "Po<PERSON><PERSON>", "rating": 694, "matchups": [{"opponent": "incineroar", "rating": 868, "opRating": 131}, {"opponent": "rotom_heat", "rating": 835, "opRating": 164}, {"opponent": "victini", "rating": 835, "opRating": 164}, {"opponent": "litleo", "rating": 748, "opRating": 251}, {"opponent": "typhlosion", "rating": 718, "opRating": 281}], "counters": [{"opponent": "stunfisk_galarian", "rating": 165}, {"opponent": "heatran", "rating": 181}, {"opponent": "sandslash_alolan", "rating": 181}, {"opponent": "steelix", "rating": 201}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 210}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 17579}, {"moveId": "PECK", "uses": 7421}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 14559}, {"moveId": "FELL_STINGER", "uses": 5647}, {"moveId": "SLUDGE_WAVE", "uses": 4792}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "FELL_STINGER"], "score": 61.1}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 673, "matchups": [{"opponent": "sandslash_alolan", "rating": 889, "opRating": 110}, {"opponent": "registeel", "rating": 871, "opRating": 128}, {"opponent": "piloswine", "rating": 836, "opRating": 163}, {"opponent": "magnezone", "rating": 707, "opRating": 292}, {"opponent": "camerupt", "rating": 707, "opRating": 292}], "counters": [{"opponent": "crocalor", "rating": 229}, {"opponent": "magmar", "rating": 232}, {"opponent": "talonflame", "rating": 232}, {"opponent": "victini", "rating": 232}, {"opponent": "litleo", "rating": 234}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17208}, {"moveId": "TACKLE", "uses": 7792}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 12898}, {"moveId": "FOCUS_BLAST", "uses": 6698}, {"moveId": "FLAMETHROWER", "uses": 5369}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 60.3}, {"speciesId": "luxio", "speciesName": "Luxio", "rating": 685, "matchups": [{"opponent": "incineroar", "rating": 843, "opRating": 156}, {"opponent": "litleo", "rating": 689, "opRating": 310}, {"opponent": "crocalor", "rating": 689, "opRating": 310}, {"opponent": "typhlosion", "rating": 660, "opRating": 339}, {"opponent": "rapidash", "rating": 645, "opRating": 354}], "counters": [{"opponent": "electabuzz", "rating": 173}, {"opponent": "victini", "rating": 209}, {"opponent": "qwilfish_his<PERSON>an", "rating": 212}, {"opponent": "stunfisk_galarian", "rating": 221}, {"opponent": "magmar", "rating": 227}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 17032}, {"moveId": "BITE", "uses": 7968}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12898}, {"moveId": "CRUNCH", "uses": 6603}, {"moveId": "RETURN", "uses": 3112}, {"moveId": "THUNDERBOLT", "uses": 2364}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 60}, {"speciesId": "pawniard", "speciesName": "Pawniard", "rating": 683, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 877, "opRating": 122}, {"opponent": "electrode_hisuian", "rating": 860, "opRating": 139}, {"opponent": "slowbro_galarian", "rating": 843, "opRating": 156}, {"opponent": "rotom_heat", "rating": 792, "opRating": 207}, {"opponent": "qwilfish_his<PERSON>an", "rating": 677, "opRating": 322}], "counters": [{"opponent": "escavalier", "rating": 65}, {"opponent": "litleo", "rating": 136}, {"opponent": "weezing_galarian", "rating": 204}, {"opponent": "castform_sunny", "rating": 229}, {"opponent": "ninetales", "rating": 242}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 16884}, {"moveId": "SCRATCH", "uses": 8116}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 13906}, {"moveId": "X_SCISSOR", "uses": 6958}, {"moveId": "IRON_HEAD", "uses": 4132}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 59.5}, {"speciesId": "klefki", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 678, "matchups": [{"opponent": "tyrunt", "rating": 838, "opRating": 161}, {"opponent": "rotom_heat", "rating": 792, "opRating": 207}, {"opponent": "slowbro_galarian", "rating": 792, "opRating": 207}, {"opponent": "toxtricity", "rating": 792, "opRating": 207}, {"opponent": "arctibax", "rating": 694, "opRating": 305}], "counters": [{"opponent": "litleo", "rating": 115}, {"opponent": "pyroar", "rating": 116}, {"opponent": "incineroar", "rating": 185}, {"opponent": "ninetales", "rating": 198}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 227}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 14557}, {"moveId": "TACKLE", "uses": 10443}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 11045}, {"moveId": "PLAY_ROUGH", "uses": 5803}, {"moveId": "FLASH_CANNON", "uses": 4475}, {"moveId": "DRAINING_KISS", "uses": 3690}]}, "moveset": ["ASTONISH", "FOUL_PLAY", "PLAY_ROUGH"], "score": 57.2}, {"speciesId": "saland<PERSON>", "speciesName": "Salandit", "rating": 639, "matchups": [{"opponent": "registeel", "rating": 880, "opRating": 120}, {"opponent": "sandslash_alolan", "rating": 852, "opRating": 148}, {"opponent": "piloswine", "rating": 804, "opRating": 196}, {"opponent": "magnezone", "rating": 675, "opRating": 324}, {"opponent": "rapidash", "rating": 656, "opRating": 344}], "counters": [{"opponent": "typhlosion", "rating": 188}, {"opponent": "charizard", "rating": 230}, {"opponent": "qwilfish_his<PERSON>an", "rating": 237}, {"opponent": "tyrunt", "rating": 248}, {"opponent": "magmar", "rating": 250}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 12516}, {"moveId": "POISON_JAB", "uses": 12484}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 11290}, {"moveId": "POISON_FANG", "uses": 9542}, {"moveId": "DRAGON_PULSE", "uses": 4157}]}, "moveset": ["EMBER", "FLAMETHROWER", "POISON_FANG"], "score": 57.1}, {"speciesId": "quilava", "speciesName": "Quilava", "rating": 656, "matchups": [{"opponent": "sandslash_alolan", "rating": 889, "opRating": 110}, {"opponent": "registeel", "rating": 874, "opRating": 125}, {"opponent": "piloswine", "rating": 854, "opRating": 145}, {"opponent": "magnezone", "rating": 759, "opRating": 240}, {"opponent": "ninetales", "rating": 645, "opRating": 354}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "tentacruel", "rating": 215}, {"opponent": "magmar", "rating": 227}, {"opponent": "armarouge", "rating": 232}, {"opponent": "rapidash", "rating": 238}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15525}, {"moveId": "TACKLE", "uses": 9475}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 9407}, {"moveId": "DIG", "uses": 8144}, {"moveId": "FLAMETHROWER", "uses": 3914}, {"moveId": "RETURN", "uses": 3509}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 56.9}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 656, "matchups": [{"opponent": "rotom_heat", "rating": 849, "opRating": 150}, {"opponent": "rai<PERSON>u", "rating": 849, "opRating": 150}, {"opponent": "incineroar", "rating": 800, "opRating": 199}, {"opponent": "r<PERSON><PERSON>", "rating": 686, "opRating": 313}, {"opponent": "ninetales", "rating": 654, "opRating": 345}], "counters": [{"opponent": "linoone_galarian", "rating": 184}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 189}, {"opponent": "typhlosion", "rating": 217}, {"opponent": "victini", "rating": 228}, {"opponent": "qwilfish_his<PERSON>an", "rating": 233}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 20276}, {"moveId": "LOW_KICK", "uses": 4724}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12423}, {"moveId": "FLAME_CHARGE", "uses": 6905}, {"moveId": "RETURN", "uses": 3037}, {"moveId": "DISCHARGE", "uses": 2642}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 56.6}, {"speciesId": "s<PERSON><PERSON><PERSON>", "speciesName": "Skorupi", "rating": 593, "matchups": [{"opponent": "nidoking", "rating": 915, "opRating": 84}, {"opponent": "rotom_heat", "rating": 849, "opRating": 150}, {"opponent": "electrode_hisuian", "rating": 849, "opRating": 150}, {"opponent": "magmar", "rating": 579, "opRating": 420}, {"opponent": "typhlosion", "rating": 570, "opRating": 429}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 198}, {"opponent": "zapdos", "rating": 256}, {"opponent": "sandslash_alolan", "rating": 262}, {"opponent": "r<PERSON><PERSON>", "rating": 265}, {"opponent": "ninetales", "rating": 277}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14287}, {"moveId": "INFESTATION", "uses": 10713}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 11647}, {"moveId": "CROSS_POISON", "uses": 8369}, {"moveId": "RETURN", "uses": 2719}, {"moveId": "SLUDGE_BOMB", "uses": 2266}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 56.4}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 641, "matchups": [{"opponent": "nidoking", "rating": 877, "opRating": 122}, {"opponent": "electabuzz", "rating": 872, "opRating": 127}, {"opponent": "r<PERSON><PERSON>", "rating": 799, "opRating": 200}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 799, "opRating": 200}, {"opponent": "electrode_hisuian", "rating": 770, "opRating": 229}], "counters": [{"opponent": "arcanine", "rating": 127}, {"opponent": "typhlosion", "rating": 132}, {"opponent": "darmanitan_standard", "rating": 167}, {"opponent": "houndoom", "rating": 172}, {"opponent": "charizard", "rating": 239}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 17879}, {"moveId": "RAZOR_LEAF", "uses": 7121}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 13128}, {"moveId": "SLUDGE_BOMB", "uses": 8579}, {"moveId": "PETAL_BLIZZARD", "uses": 1808}, {"moveId": "SOLAR_BEAM", "uses": 1525}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 56}, {"speciesId": "cacturne", "speciesName": "Cacturne", "rating": 642, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 880, "opRating": 119}, {"opponent": "incineroar", "rating": 863, "opRating": 136}, {"opponent": "stunfisk_galarian", "rating": 859, "opRating": 140}, {"opponent": "rotom_heat", "rating": 814, "opRating": 185}, {"opponent": "victini", "rating": 809, "opRating": 190}], "counters": [{"opponent": "araquanid", "rating": 169}, {"opponent": "camerupt", "rating": 209}, {"opponent": "over<PERSON><PERSON>l", "rating": 214}, {"opponent": "skuntank", "rating": 216}, {"opponent": "rapidash", "rating": 247}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 10186}, {"moveId": "SAND_ATTACK", "uses": 8760}, {"moveId": "POISON_JAB", "uses": 6044}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 6299}, {"moveId": "DARK_PULSE", "uses": 6221}, {"moveId": "TRAILBLAZE", "uses": 4897}, {"moveId": "PAYBACK", "uses": 2802}, {"moveId": "RETURN", "uses": 2787}, {"moveId": "GRASS_KNOT", "uses": 1982}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DARK_PULSE"], "score": 55.9}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 684, "matchups": [{"opponent": "weezing_galarian", "rating": 868, "opRating": 131}, {"opponent": "over<PERSON><PERSON>l", "rating": 836, "opRating": 163}, {"opponent": "tyrunt", "rating": 784, "opRating": 215}, {"opponent": "magmortar", "rating": 700, "opRating": 300}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 657, "opRating": 342}], "counters": [{"opponent": "darmanitan_standard", "rating": 131}, {"opponent": "darum<PERSON>", "rating": 139}, {"opponent": "charmeleon", "rating": 141}, {"opponent": "heatran", "rating": 150}, {"opponent": "stunfisk_galarian", "rating": 177}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 6935}, {"moveId": "THUNDER_SHOCK", "uses": 6708}, {"moveId": "SPARK", "uses": 4468}, {"moveId": "METAL_SOUND", "uses": 3689}, {"moveId": "CHARGE_BEAM", "uses": 3200}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 10211}, {"moveId": "MAGNET_BOMB", "uses": 9424}, {"moveId": "ZAP_CANNON", "uses": 3403}, {"moveId": "FLASH_CANNON", "uses": 1965}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "MAGNET_BOMB"], "score": 55.9}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 718, "matchups": [{"opponent": "victini", "rating": 867, "opRating": 132}, {"opponent": "over<PERSON><PERSON>l", "rating": 867, "opRating": 132}, {"opponent": "incineroar", "rating": 846, "opRating": 153}, {"opponent": "rotom_heat", "rating": 824, "opRating": 175}, {"opponent": "stunfisk_galarian", "rating": 782, "opRating": 217}], "counters": [{"opponent": "pyroar", "rating": 112}, {"opponent": "arcanine", "rating": 188}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 190}, {"opponent": "typhlosion", "rating": 196}, {"opponent": "magmar", "rating": 205}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 15780}, {"moveId": "METAL_CLAW", "uses": 9220}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 10617}, {"moveId": "FOUL_PLAY", "uses": 5447}, {"moveId": "TRAILBLAZE", "uses": 3509}, {"moveId": "IRON_HEAD", "uses": 3488}, {"moveId": "PLAY_ROUGH", "uses": 1956}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "TRAILBLAZE"], "score": 55}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 645, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 869, "opRating": 130}, {"opponent": "rotom_heat", "rating": 859, "opRating": 140}, {"opponent": "victini", "rating": 838, "opRating": 161}, {"opponent": "magnezone", "rating": 785, "opRating": 214}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 654, "opRating": 345}], "counters": [{"opponent": "muk_alolan", "rating": 75}, {"opponent": "charmeleon", "rating": 122}, {"opponent": "charizard", "rating": 132}, {"opponent": "heatran", "rating": 140}, {"opponent": "toxtricity", "rating": 155}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 8591}, {"moveId": "BULLET_SEED", "uses": 6898}, {"moveId": "FEINT_ATTACK", "uses": 6439}, {"moveId": "RAZOR_LEAF", "uses": 3057}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 9979}, {"moveId": "FOUL_PLAY", "uses": 7288}, {"moveId": "HURRICANE", "uses": 3155}, {"moveId": "RETURN", "uses": 2664}, {"moveId": "LEAF_TORNADO", "uses": 1934}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 54.6}, {"speciesId": "stunky", "speciesName": "Stunky", "rating": 582, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 860, "opRating": 139}, {"opponent": "armarouge", "rating": 751, "opRating": 248}, {"opponent": "slowbro_galarian", "rating": 704, "opRating": 295}, {"opponent": "delphox", "rating": 687, "opRating": 312}, {"opponent": "rapidash", "rating": 588, "opRating": 411}], "counters": [{"opponent": "weezing_galarian", "rating": 217}, {"opponent": "raticate_alolan", "rating": 250}, {"opponent": "electrode_hisuian", "rating": 252}, {"opponent": "stunfisk_galarian", "rating": 260}, {"opponent": "castform_sunny", "rating": 260}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 14865}, {"moveId": "SCRATCH", "uses": 10135}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 8113}, {"moveId": "FLAMETHROWER", "uses": 5396}, {"moveId": "SLUDGE_BOMB", "uses": 4871}, {"moveId": "TRAILBLAZE", "uses": 3907}, {"moveId": "RETURN", "uses": 2746}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 54.6}, {"speciesId": "tepig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 636, "matchups": [{"opponent": "registeel", "rating": 899, "opRating": 100}, {"opponent": "genesect", "rating": 889, "opRating": 110}, {"opponent": "sandslash_alolan", "rating": 862, "opRating": 137}, {"opponent": "bisharp", "rating": 832, "opRating": 167}, {"opponent": "<PERSON>rserker", "rating": 755, "opRating": 244}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "toxapex", "rating": 190}, {"opponent": "tyrunt", "rating": 224}, {"opponent": "ninetales", "rating": 265}, {"opponent": "qwilfish_his<PERSON>an", "rating": 266}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15071}, {"moveId": "TACKLE", "uses": 9929}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 10653}, {"moveId": "BODY_SLAM", "uses": 9921}, {"moveId": "FLAMETHROWER", "uses": 4458}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BODY_SLAM"], "score": 54.6}, {"speciesId": "pikachu_libre", "speciesName": "<PERSON><PERSON><PERSON> (Libre)", "rating": 593, "matchups": [{"opponent": "registeel", "rating": 871, "opRating": 128}, {"opponent": "sandslash_alolan", "rating": 828, "opRating": 171}, {"opponent": "incineroar", "rating": 795, "opRating": 204}, {"opponent": "magnezone", "rating": 780, "opRating": 219}, {"opponent": "pyroar", "rating": 666, "opRating": 333}], "counters": [{"opponent": "charjabug", "rating": 194}, {"opponent": "dedenne", "rating": 203}, {"opponent": "nidoqueen", "rating": 205}, {"opponent": "nidoking", "rating": 232}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 250}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20013}, {"moveId": "CHARM", "uses": 4987}], "chargedMoves": [{"moveId": "FLYING_PRESS", "uses": 14407}, {"moveId": "THUNDER_PUNCH", "uses": 8440}, {"moveId": "PLAY_ROUGH", "uses": 2192}]}, "moveset": ["THUNDER_SHOCK", "FLYING_PRESS", "THUNDER_PUNCH"], "score": 54.2}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 736, "matchups": [{"opponent": "piloswine", "rating": 896, "opRating": 103}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 792, "opRating": 207}, {"opponent": "arctibax", "rating": 788, "opRating": 211}, {"opponent": "qwilfish_his<PERSON>an", "rating": 757, "opRating": 242}, {"opponent": "avalugg_his<PERSON>an", "rating": 753, "opRating": 246}], "counters": [{"opponent": "crocalor", "rating": 92}, {"opponent": "litleo", "rating": 94}, {"opponent": "darmanitan_standard", "rating": 98}, {"opponent": "pyroar", "rating": 108}, {"opponent": "typhlosion", "rating": 115}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 12296}, {"moveId": "METAL_CLAW", "uses": 8340}, {"moveId": "ROCK_SMASH", "uses": 4359}], "chargedMoves": [{"moveId": "FLASH_CANNON", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "RETURN", "uses": null}, {"moveId": "ZAP_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 54.2}, {"speciesId": "revavroom", "speciesName": "Revavroom", "rating": 689, "matchups": [{"opponent": "skuntank", "rating": 881, "opRating": 118}, {"opponent": "grimer_alolan", "rating": 881, "opRating": 118}, {"opponent": "registeel", "rating": 877, "opRating": 122}, {"opponent": "nidoqueen", "rating": 837, "opRating": 162}, {"opponent": "rotom_heat", "rating": 802, "opRating": 197}], "counters": [{"opponent": "stunfisk_galarian", "rating": 91}, {"opponent": "steelix", "rating": 104}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 140}, {"opponent": "magnezone", "rating": 161}, {"opponent": "nidoking", "rating": 165}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 16736}, {"moveId": "LICK", "uses": 8264}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 10760}, {"moveId": "GUNK_SHOT", "uses": 6406}, {"moveId": "GYRO_BALL", "uses": 5104}, {"moveId": "ACID_SPRAY", "uses": 2714}]}, "moveset": ["POISON_JAB", "OVERHEAT", "ACID_SPRAY"], "score": 54}, {"speciesId": "metang", "speciesName": "Metang", "rating": 622, "matchups": [{"opponent": "amaura", "rating": 910, "opRating": 89}, {"opponent": "piloswine", "rating": 844, "opRating": 155}, {"opponent": "tyrunt", "rating": 833, "opRating": 166}, {"opponent": "avalugg_his<PERSON>an", "rating": 821, "opRating": 178}, {"opponent": "arctibax", "rating": 786, "opRating": 213}], "counters": [{"opponent": "ninetales", "rating": 91}, {"opponent": "typhlosion", "rating": 132}, {"opponent": "bisharp", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 207}, {"opponent": "incineroar", "rating": 212}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 18642}, {"moveId": "ZEN_HEADBUTT", "uses": 6358}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 11910}, {"moveId": "RETURN", "uses": 5400}, {"moveId": "GYRO_BALL", "uses": 4471}, {"moveId": "PSYCHIC", "uses": 3246}]}, "moveset": ["METAL_CLAW", "PSYSHOCK", "GYRO_BALL"], "score": 53.6}, {"speciesId": "larve<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 628, "matchups": [{"opponent": "sandslash_alolan", "rating": 892, "opRating": 107}, {"opponent": "registeel", "rating": 876, "opRating": 123}, {"opponent": "bisharp", "rating": 861, "opRating": 138}, {"opponent": "piloswine", "rating": 857, "opRating": 142}, {"opponent": "magnezone", "rating": 746, "opRating": 253}], "counters": [{"opponent": "tyrunt", "rating": 155}, {"opponent": "victini", "rating": 182}, {"opponent": "ninetales", "rating": 214}, {"opponent": "typhlosion", "rating": 230}, {"opponent": "litleo", "rating": 251}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 14726}, {"moveId": "BUG_BITE", "uses": 10274}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 14029}, {"moveId": "BUG_BUZZ", "uses": 8360}, {"moveId": "FLAME_WHEEL", "uses": 2583}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BUG_BUZZ"], "score": 53.1}, {"speciesId": "zoroark", "speciesName": "Zoroark", "rating": 636, "matchups": [{"opponent": "rotom_heat", "rating": 752, "opRating": 247}, {"opponent": "victini", "rating": 752, "opRating": 247}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 752, "opRating": 247}, {"opponent": "em<PERSON>ga", "rating": 752, "opRating": 247}, {"opponent": "rai<PERSON>u", "rating": 752, "opRating": 247}], "counters": [{"opponent": "obstagoon", "rating": 68}, {"opponent": "araquanid", "rating": 154}, {"opponent": "amaura", "rating": 167}, {"opponent": "electrode_hisuian", "rating": 203}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 227}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 13430}, {"moveId": "SNARL", "uses": 11570}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 7862}, {"moveId": "NIGHT_SHADE", "uses": 6927}, {"moveId": "FLAMETHROWER", "uses": 6000}, {"moveId": "SLUDGE_BOMB", "uses": 4219}]}, "moveset": ["SHADOW_CLAW", "FOUL_PLAY", "SLUDGE_BOMB"], "score": 52.9}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 639, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 848, "opRating": 151}, {"opponent": "r<PERSON><PERSON>", "rating": 834, "opRating": 165}, {"opponent": "nidoking", "rating": 803, "opRating": 196}, {"opponent": "electabuzz", "rating": 784, "opRating": 215}, {"opponent": "magmar", "rating": 610, "opRating": 389}], "counters": [{"opponent": "litleo", "rating": 115}, {"opponent": "pyroar", "rating": 132}, {"opponent": "incineroar", "rating": 185}, {"opponent": "ninetales", "rating": 198}, {"opponent": "over<PERSON><PERSON>l", "rating": 227}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 15409}, {"moveId": "FEINT_ATTACK", "uses": 9591}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 7803}, {"moveId": "SLUDGE_BOMB", "uses": 6854}, {"moveId": "GRASS_KNOT", "uses": 6348}, {"moveId": "RETURN", "uses": 3994}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 52.7}, {"speciesId": "<PERSON>ay", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 605, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 805, "opRating": 194}, {"opponent": "rotom_heat", "rating": 767, "opRating": 232}, {"opponent": "nidoking", "rating": 767, "opRating": 232}, {"opponent": "nidoqueen", "rating": 698, "opRating": 301}, {"opponent": "rapidash", "rating": 645, "opRating": 354}], "counters": [{"opponent": "obstagoon", "rating": 155}, {"opponent": "pawniard", "rating": 173}, {"opponent": "incineroar", "rating": 174}, {"opponent": "lokix", "rating": 194}, {"opponent": "raticate_alolan", "rating": 216}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 14231}, {"moveId": "TACKLE", "uses": 6763}, {"moveId": "PECK", "uses": 4028}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 20699}, {"moveId": "PSYBEAM", "uses": 4301}]}, "moveset": ["PSYWAVE", "NIGHT_SLASH", "PSYBEAM"], "score": 52}, {"speciesId": "houndour", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 626, "matchups": [{"opponent": "registeel", "rating": 883, "opRating": 116}, {"opponent": "sandslash_alolan", "rating": 862, "opRating": 137}, {"opponent": "<PERSON>rserker", "rating": 845, "opRating": 154}, {"opponent": "rotom_heat", "rating": 725, "opRating": 275}, {"opponent": "rapidash", "rating": 662, "opRating": 337}], "counters": [{"opponent": "tyrunt", "rating": 131}, {"opponent": "magby", "rating": 191}, {"opponent": "magmar", "rating": 205}, {"opponent": "magmortar", "rating": 212}, {"opponent": "typhlosion", "rating": 217}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 14587}, {"moveId": "FEINT_ATTACK", "uses": 10413}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 9547}, {"moveId": "FLAMETHROWER", "uses": 8504}, {"moveId": "DARK_PULSE", "uses": 3568}, {"moveId": "RETURN", "uses": 3445}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 51.8}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 616, "matchups": [{"opponent": "em<PERSON>ga", "rating": 887, "opRating": 112}, {"opponent": "incineroar", "rating": 822, "opRating": 177}, {"opponent": "magnezone", "rating": 774, "opRating": 225}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 709, "opRating": 290}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 607, "opRating": 392}], "counters": [{"opponent": "amaura", "rating": 112}, {"opponent": "raboot", "rating": 157}, {"opponent": "sandslash_alolan", "rating": 165}, {"opponent": "nidoqueen", "rating": 194}, {"opponent": "piloswine", "rating": 200}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12942}, {"moveId": "ASTONISH", "uses": 12058}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 12406}, {"moveId": "OMINOUS_WIND", "uses": 7195}, {"moveId": "THUNDER", "uses": 5385}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 51.8}, {"speciesId": "pikachu", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 570, "matchups": [{"opponent": "zapdos", "rating": 838, "opRating": 161}, {"opponent": "magmortar", "rating": 804, "opRating": 195}, {"opponent": "magby", "rating": 804, "opRating": 195}, {"opponent": "talonflame", "rating": 695, "opRating": 304}, {"opponent": "rapidash", "rating": 666, "opRating": 333}], "counters": [{"opponent": "charjabug", "rating": 234}, {"opponent": "dedenne", "rating": 240}, {"opponent": "stunfisk_galarian", "rating": 263}, {"opponent": "electrode_hisuian", "rating": 274}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11463}, {"moveId": "QUICK_ATTACK", "uses": 9022}, {"moveId": "PRESENT", "uses": 4531}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 11272}, {"moveId": "SURF", "uses": 7442}, {"moveId": "DISCHARGE", "uses": 2389}, {"moveId": "THUNDERBOLT", "uses": 2003}, {"moveId": "THUNDER", "uses": 1748}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SURF"], "score": 50.3}, {"speciesId": "pika<PERSON>_shaymin", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>)", "rating": 570, "matchups": [{"opponent": "zapdos", "rating": 838, "opRating": 161}, {"opponent": "magmortar", "rating": 804, "opRating": 195}, {"opponent": "magby", "rating": 804, "opRating": 195}, {"opponent": "talonflame", "rating": 695, "opRating": 304}, {"opponent": "rapidash", "rating": 666, "opRating": 333}], "counters": [{"opponent": "charjabug", "rating": 234}, {"opponent": "dedenne", "rating": 240}, {"opponent": "stunfisk_galarian", "rating": 263}, {"opponent": "electrode_hisuian", "rating": 274}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11464}, {"moveId": "QUICK_ATTACK", "uses": 9024}, {"moveId": "PRESENT", "uses": 4531}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10059}, {"moveId": "SURF", "uses": 6438}, {"moveId": "GRASS_KNOT", "uses": 2951}, {"moveId": "DISCHARGE", "uses": 2126}, {"moveId": "THUNDERBOLT", "uses": 1846}, {"moveId": "THUNDER", "uses": 1640}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SURF"], "score": 50.3}, {"speciesId": "ivysaur", "speciesName": "Ivysaur", "rating": 609, "matchups": [{"opponent": "electabuzz", "rating": 869, "opRating": 130}, {"opponent": "r<PERSON><PERSON>", "rating": 811, "opRating": 188}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 800, "opRating": 200}, {"opponent": "nidoking", "rating": 780, "opRating": 219}, {"opponent": "electrode_hisuian", "rating": 776, "opRating": 223}], "counters": [{"opponent": "charizard", "rating": 98}, {"opponent": "sandslash_alolan", "rating": 125}, {"opponent": "typhlosion", "rating": 132}, {"opponent": "ninetales", "rating": 134}, {"opponent": "arcanine", "rating": 139}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 17291}, {"moveId": "RAZOR_LEAF", "uses": 7709}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 9096}, {"moveId": "POWER_WHIP", "uses": 8450}, {"moveId": "RETURN", "uses": 5686}, {"moveId": "SOLAR_BEAM", "uses": 1775}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 50.2}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 614, "matchups": [{"opponent": "obstagoon", "rating": 900, "opRating": 100}, {"opponent": "incineroar", "rating": 828, "opRating": 171}, {"opponent": "victini", "rating": 804, "opRating": 195}, {"opponent": "rai<PERSON>u", "rating": 804, "opRating": 195}, {"opponent": "rotom_heat", "rating": 785, "opRating": 214}], "counters": [{"opponent": "heatran", "rating": 140}, {"opponent": "steelix", "rating": 141}, {"opponent": "magneton", "rating": 163}, {"opponent": "stunfisk_galarian", "rating": 177}, {"opponent": "camerupt", "rating": 188}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 14570}, {"moveId": "BUG_BITE", "uses": 10430}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 9561}, {"moveId": "SLUDGE_BOMB", "uses": 5846}, {"moveId": "MEGAHORN", "uses": 3813}, {"moveId": "RETURN", "uses": 3666}, {"moveId": "GYRO_BALL", "uses": 2087}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "SLUDGE_BOMB"], "score": 50.1}, {"speciesId": "murkrow", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 583, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 772, "opRating": 227}, {"opponent": "nidoking", "rating": 645, "opRating": 354}, {"opponent": "rotom_heat", "rating": 638, "opRating": 361}, {"opponent": "litleo", "rating": 548, "opRating": 451}, {"opponent": "typhlosion", "rating": 511, "opRating": 488}], "counters": [{"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "weezing_galarian", "rating": 195}, {"opponent": "amaura", "rating": 195}, {"opponent": "steelix", "rating": 197}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 15352}, {"moveId": "PECK", "uses": 9648}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 10412}, {"moveId": "FOUL_PLAY", "uses": 8096}, {"moveId": "DARK_PULSE", "uses": 3426}, {"moveId": "RETURN", "uses": 3086}]}, "moveset": ["FEINT_ATTACK", "DRILL_PECK", "FOUL_PLAY"], "score": 49.6}, {"speciesId": "golbat", "speciesName": "Golbat", "rating": 667, "matchups": [{"opponent": "magmortar", "rating": 925, "opRating": 75}, {"opponent": "nidoking", "rating": 860, "opRating": 139}, {"opponent": "magmar", "rating": 714, "opRating": 285}, {"opponent": "crocalor", "rating": 692, "opRating": 307}, {"opponent": "rapidash", "rating": 657, "opRating": 342}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "steelix", "rating": 100}, {"opponent": "amaura", "rating": 103}, {"opponent": "sandslash_alolan", "rating": 125}, {"opponent": "avalugg_his<PERSON>an", "rating": 128}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 15907}, {"moveId": "BITE", "uses": 9093}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 7983}, {"moveId": "SHADOW_BALL", "uses": 6484}, {"moveId": "RETURN", "uses": 3800}, {"moveId": "AIR_CUTTER", "uses": 3388}, {"moveId": "OMINOUS_WIND", "uses": 3344}]}, "moveset": ["WING_ATTACK", "POISON_FANG", "SHADOW_BALL"], "score": 49.3}, {"speciesId": "klang", "speciesName": "Klang", "rating": 628, "matchups": [{"opponent": "archen", "rating": 888, "opRating": 112}, {"opponent": "tentacruel", "rating": 864, "opRating": 136}, {"opponent": "talonflame", "rating": 720, "opRating": 280}, {"opponent": "moltres_galarian", "rating": 708, "opRating": 292}, {"opponent": "darmanitan_standard", "rating": 660, "opRating": 340}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magmar", "rating": 125}, {"opponent": "stunfisk_galarian", "rating": 127}, {"opponent": "magmortar", "rating": 129}, {"opponent": "typhlosion", "rating": 132}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11479}, {"moveId": "METAL_SOUND", "uses": 7687}, {"moveId": "CHARGE_BEAM", "uses": 5840}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 11331}, {"moveId": "VICE_GRIP", "uses": 9579}, {"moveId": "ZAP_CANNON", "uses": 4058}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "VICE_GRIP"], "score": 49.2}, {"speciesId": "mareep", "speciesName": "<PERSON><PERSON>", "rating": 562, "matchups": [{"opponent": "charizard", "rating": 718, "opRating": 281}, {"opponent": "talonflame", "rating": 625, "opRating": 374}, {"opponent": "typhlosion", "rating": 596, "opRating": 403}, {"opponent": "rapidash", "rating": 596, "opRating": 403}, {"opponent": "pyroar", "rating": 596, "opRating": 403}], "counters": [{"opponent": "stunfisk_galarian", "rating": 168}, {"opponent": "steelix", "rating": 209}, {"opponent": "tyrunt", "rating": 217}, {"opponent": "r<PERSON><PERSON>", "rating": 225}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 239}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15521}, {"moveId": "TACKLE", "uses": 9479}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 8161}, {"moveId": "DISCHARGE", "uses": 6899}, {"moveId": "TRAILBLAZE", "uses": 4973}, {"moveId": "THUNDERBOLT", "uses": 2933}, {"moveId": "RETURN", "uses": 2006}]}, "moveset": ["THUNDER_SHOCK", "BODY_SLAM", "DISCHARGE"], "score": 49.2}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 648, "matchups": [{"opponent": "victini", "rating": 806, "opRating": 193}, {"opponent": "magmortar", "rating": 768, "opRating": 231}, {"opponent": "ninetales", "rating": 740, "opRating": 259}, {"opponent": "litleo", "rating": 712, "opRating": 287}, {"opponent": "typhlosion", "rating": 693, "opRating": 306}], "counters": [{"opponent": "<PERSON>on", "rating": 87}, {"opponent": "manectric", "rating": 108}, {"opponent": "amaura", "rating": 112}, {"opponent": "melmetal", "rating": 117}, {"opponent": "sandslash_alolan", "rating": 125}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 14970}, {"moveId": "STEEL_WING", "uses": 10030}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 10128}, {"moveId": "DRAGON_CLAW", "uses": 7737}, {"moveId": "CRUNCH", "uses": 7145}]}, "moveset": ["WING_ATTACK", "ANCIENT_POWER", "DRAGON_CLAW"], "score": 49.1}, {"speciesId": "<PERSON><PERSON>", "speciesName": "Joltik", "rating": 569, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 877, "opRating": 122}, {"opponent": "xurkitree", "rating": 838, "opRating": 161}, {"opponent": "rotom_wash", "rating": 830, "opRating": 169}, {"opponent": "luxio", "rating": 775, "opRating": 224}, {"opponent": "r<PERSON><PERSON>", "rating": 728, "opRating": 271}], "counters": [{"opponent": "houndoom", "rating": 138}, {"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "entei", "rating": 173}, {"opponent": "darum<PERSON>", "rating": 185}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 16645}, {"moveId": "CHARGE_BEAM", "uses": 8355}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 7850}, {"moveId": "CROSS_POISON", "uses": 7607}, {"moveId": "BUG_BUZZ", "uses": 5983}, {"moveId": "RETURN", "uses": 3573}]}, "moveset": ["SUCKER_PUNCH", "DISCHARGE", "CROSS_POISON"], "score": 49.1}, {"speciesId": "<PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 624, "matchups": [{"opponent": "incineroar", "rating": 906, "opRating": 93}, {"opponent": "moltres_galarian", "rating": 772, "opRating": 227}, {"opponent": "talonflame", "rating": 712, "opRating": 287}, {"opponent": "charizard", "rating": 708, "opRating": 291}, {"opponent": "rapidash", "rating": 604, "opRating": 395}], "counters": [{"opponent": "piloswine", "rating": 119}, {"opponent": "nidoqueen", "rating": 133}, {"opponent": "nidoking", "rating": 145}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 165}, {"opponent": "r<PERSON><PERSON>", "rating": 181}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 7817}, {"moveId": "ICE_FANG", "uses": 6440}, {"moveId": "THUNDER_FANG", "uses": 6314}, {"moveId": "BITE", "uses": 4390}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 12186}, {"moveId": "POISON_FANG", "uses": 5727}, {"moveId": "RETURN", "uses": 3864}, {"moveId": "PLAY_ROUGH", "uses": 3228}]}, "moveset": ["THUNDER_FANG", "CRUNCH", "POISON_FANG"], "score": 49}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 680, "matchups": [{"opponent": "skuntank", "rating": 887, "opRating": 112}, {"opponent": "tentacruel", "rating": 887, "opRating": 112}, {"opponent": "grimer_alolan", "rating": 887, "opRating": 112}, {"opponent": "incineroar", "rating": 857, "opRating": 142}, {"opponent": "over<PERSON><PERSON>l", "rating": 833, "opRating": 166}], "counters": [{"opponent": "stunfisk_galarian", "rating": 44}, {"opponent": "electrode_hisuian", "rating": 53}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "victini", "rating": 127}, {"opponent": "melmetal", "rating": 131}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11712}, {"moveId": "METAL_SOUND", "uses": 7487}, {"moveId": "CHARGE_BEAM", "uses": 5766}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 9021}, {"moveId": "ZAP_CANNON", "uses": 5946}, {"moveId": "HYPER_BEAM", "uses": 5349}, {"moveId": "FLASH_CANNON", "uses": 4692}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 48.6}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 620, "matchups": [{"opponent": "rotom_heat", "rating": 838, "opRating": 161}, {"opponent": "rapidash", "rating": 723, "opRating": 276}, {"opponent": "armarouge", "rating": 723, "opRating": 276}, {"opponent": "magmortar", "rating": 715, "opRating": 284}, {"opponent": "arcanine", "rating": 672, "opRating": 327}], "counters": [{"opponent": "lokix", "rating": 90}, {"opponent": "qwilfish_his<PERSON>an", "rating": 91}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "arctibax", "rating": 135}, {"opponent": "magnezone", "rating": 142}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 10829}, {"moveId": "HEX", "uses": 9465}, {"moveId": "ACID", "uses": 4693}], "chargedMoves": [{"moveId": "SURF", "uses": 8051}, {"moveId": "SHADOW_BALL", "uses": 4807}, {"moveId": "FUTURE_SIGHT", "uses": 4570}, {"moveId": "SCALD", "uses": 3924}, {"moveId": "SLUDGE_WAVE", "uses": 3601}]}, "moveset": ["HEX", "SURF", "SHADOW_BALL"], "score": 48.3}, {"speciesId": "celesteela", "speciesName": "<PERSON><PERSON>", "rating": 596, "matchups": [{"opponent": "beedrill", "rating": 870, "opRating": 129}, {"opponent": "nidoking", "rating": 736, "opRating": 263}, {"opponent": "over<PERSON><PERSON>l", "rating": 694, "opRating": 305}, {"opponent": "nidoqueen", "rating": 645, "opRating": 354}, {"opponent": "qwilfish_his<PERSON>an", "rating": 625, "opRating": 374}], "counters": [{"opponent": "magneton", "rating": 115}, {"opponent": "steelix", "rating": 125}, {"opponent": "electabuzz", "rating": 134}, {"opponent": "em<PERSON>ga", "rating": 146}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 198}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 13079}, {"moveId": "AIR_SLASH", "uses": 11921}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 9590}, {"moveId": "BULLDOZE", "uses": 7987}, {"moveId": "HEAVY_SLAM", "uses": 4922}, {"moveId": "IRON_HEAD", "uses": 2497}]}, "moveset": ["AIR_SLASH", "BODY_SLAM", "IRON_HEAD"], "score": 48}, {"speciesId": "venomoth", "speciesName": "Venomoth", "rating": 616, "matchups": [{"opponent": "incineroar", "rating": 843, "opRating": 156}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 824, "opRating": 175}, {"opponent": "skuntank", "rating": 812, "opRating": 187}, {"opponent": "raticate_alolan", "rating": 767, "opRating": 232}, {"opponent": "electrode_hisuian", "rating": 709, "opRating": 290}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "<PERSON>on", "rating": 137}, {"opponent": "darmanitan_standard", "rating": 145}, {"opponent": "armarouge", "rating": 175}, {"opponent": "melmetal", "rating": 177}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 8998}, {"moveId": "INFESTATION", "uses": 8660}, {"moveId": "BUG_BITE", "uses": 7350}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 7354}, {"moveId": "SILVER_WIND", "uses": 6542}, {"moveId": "PSYCHIC", "uses": 4398}, {"moveId": "RETURN", "uses": 3723}, {"moveId": "BUG_BUZZ", "uses": 2931}]}, "moveset": ["INFESTATION", "POISON_FANG", "BUG_BUZZ"], "score": 48}, {"speciesId": "cynda<PERSON><PERSON>", "speciesName": "Cyndaquil", "rating": 581, "matchups": [{"opponent": "registeel", "rating": 864, "opRating": 135}, {"opponent": "genesect", "rating": 851, "opRating": 148}, {"opponent": "sandslash_alolan", "rating": 815, "opRating": 184}, {"opponent": "bisharp", "rating": 806, "opRating": 193}, {"opponent": "magneton", "rating": 770, "opRating": 229}], "counters": [{"opponent": "magby", "rating": 154}, {"opponent": "typhlosion", "rating": 175}, {"opponent": "magmar", "rating": 183}, {"opponent": "qwilfish_his<PERSON>an", "rating": 204}, {"opponent": "tyrunt", "rating": 213}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 15065}, {"moveId": "TACKLE", "uses": 9935}], "chargedMoves": [{"moveId": "SWIFT", "uses": 9985}, {"moveId": "FLAME_CHARGE", "uses": 9400}, {"moveId": "FLAMETHROWER", "uses": 3942}, {"moveId": "RETURN", "uses": 1732}]}, "moveset": ["EMBER", "SWIFT", "FLAME_CHARGE"], "score": 47.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "tentacruel", "rating": 903, "opRating": 96}, {"opponent": "toxapex", "rating": 887, "opRating": 112}, {"opponent": "nidoqueen", "rating": 856, "opRating": 143}, {"opponent": "avalugg_his<PERSON>an", "rating": 748, "opRating": 251}, {"opponent": "over<PERSON><PERSON>l", "rating": 701, "opRating": 298}], "counters": [{"opponent": "bisharp", "rating": 101}, {"opponent": "lokix", "rating": 128}, {"opponent": "incineroar", "rating": 140}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "qwilfish_his<PERSON>an", "rating": 191}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 15993}, {"moveId": "CHARGE_BEAM", "uses": 9007}], "chargedMoves": [{"moveId": "DOOM_DESIRE", "uses": 14745}, {"moveId": "PSYCHIC", "uses": 7125}, {"moveId": "DAZZLING_GLEAM", "uses": 3106}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 47.4}, {"speciesId": "pikachu_5th_anniversary", "speciesName": "<PERSON><PERSON><PERSON> (5th Anniversary)", "rating": 577, "matchups": [{"opponent": "zapdos", "rating": 838, "opRating": 161}, {"opponent": "magmortar", "rating": 804, "opRating": 195}, {"opponent": "magby", "rating": 804, "opRating": 195}, {"opponent": "talonflame", "rating": 695, "opRating": 304}, {"opponent": "rapidash", "rating": 666, "opRating": 333}], "counters": [{"opponent": "stunfisk_galarian", "rating": 150}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 157}, {"opponent": "magnezone", "rating": 186}, {"opponent": "dedenne", "rating": 191}, {"opponent": "r<PERSON><PERSON>", "rating": 225}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11504}, {"moveId": "QUICK_ATTACK", "uses": 8974}, {"moveId": "PRESENT", "uses": 4543}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12867}, {"moveId": "FLY", "uses": 7036}, {"moveId": "DISCHARGE", "uses": 2747}, {"moveId": "THUNDERBOLT", "uses": 2358}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "FLY"], "score": 47.3}, {"speciesId": "pikachu_flying", "speciesName": "<PERSON><PERSON><PERSON> (Flying)", "rating": 577, "matchups": [{"opponent": "zapdos", "rating": 838, "opRating": 161}, {"opponent": "magmortar", "rating": 804, "opRating": 195}, {"opponent": "magby", "rating": 804, "opRating": 195}, {"opponent": "talonflame", "rating": 695, "opRating": 304}, {"opponent": "rapidash", "rating": 666, "opRating": 333}], "counters": [{"opponent": "stunfisk_galarian", "rating": 150}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 157}, {"opponent": "magnezone", "rating": 186}, {"opponent": "dedenne", "rating": 191}, {"opponent": "r<PERSON><PERSON>", "rating": 225}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13974}, {"moveId": "QUICK_ATTACK", "uses": 11026}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12867}, {"moveId": "FLY", "uses": 7036}, {"moveId": "DISCHARGE", "uses": 2747}, {"moveId": "THUNDERBOLT", "uses": 2358}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "FLY"], "score": 47.3}, {"speciesId": "scorbunny", "speciesName": "Scorbunny", "rating": 584, "matchups": [{"opponent": "registeel", "rating": 885, "opRating": 114}, {"opponent": "sandslash_alolan", "rating": 870, "opRating": 129}, {"opponent": "bisharp", "rating": 830, "opRating": 169}, {"opponent": "piloswine", "rating": 807, "opRating": 192}, {"opponent": "magnezone", "rating": 681, "opRating": 318}], "counters": [{"opponent": "talonflame", "rating": 202}, {"opponent": "litleo", "rating": 206}, {"opponent": "magby", "rating": 208}, {"opponent": "qwilfish_his<PERSON>an", "rating": 220}, {"opponent": "magmar", "rating": 223}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16604}, {"moveId": "TACKLE", "uses": 8396}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 17652}, {"moveId": "FLAMETHROWER", "uses": 7348}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 47.3}, {"speciesId": "whirlipede", "speciesName": "Whirlipede", "rating": 568, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 893, "opRating": 106}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 778, "opRating": 221}, {"opponent": "electrode_hisuian", "rating": 752, "opRating": 247}, {"opponent": "obstagoon", "rating": 738, "opRating": 261}, {"opponent": "magby", "rating": 575, "opRating": 424}], "counters": [{"opponent": "typhlosion", "rating": 132}, {"opponent": "archen", "rating": 134}, {"opponent": "stunfisk_galarian", "rating": 168}, {"opponent": "sandslash_alolan", "rating": 173}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 223}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14181}, {"moveId": "BUG_BITE", "uses": 10819}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8792}, {"moveId": "SIGNAL_BEAM", "uses": 7409}, {"moveId": "RETURN", "uses": 5522}, {"moveId": "GYRO_BALL", "uses": 3271}]}, "moveset": ["POISON_STING", "SLUDGE_BOMB", "SIGNAL_BEAM"], "score": 46.9}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 606, "matchups": [{"opponent": "piloswine", "rating": 888, "opRating": 111}, {"opponent": "fuecoco", "rating": 771, "opRating": 228}, {"opponent": "rotom_heat", "rating": 739, "opRating": 260}, {"opponent": "nidoqueen", "rating": 728, "opRating": 271}, {"opponent": "darum<PERSON>", "rating": 691, "opRating": 308}], "counters": [{"opponent": "melmetal", "rating": 109}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "crocalor", "rating": 140}, {"opponent": "litleo", "rating": 143}, {"opponent": "magmar", "rating": 160}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 16043}, {"moveId": "FROST_BREATH", "uses": 8957}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 7048}, {"moveId": "LIQUIDATION", "uses": 6898}, {"moveId": "RAZOR_SHELL", "uses": 5266}, {"moveId": "ICY_WIND", "uses": 2483}, {"moveId": "HYDRO_PUMP", "uses": 1304}, {"moveId": "BLIZZARD", "uses": 1058}, {"moveId": "AURORA_BEAM", "uses": 849}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "LIQUIDATION"], "score": 46.4}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550, "matchups": [{"opponent": "xurkitree", "rating": 825, "opRating": 174}, {"opponent": "luxio", "rating": 750, "opRating": 250}, {"opponent": "magby", "rating": 621, "opRating": 378}, {"opponent": "avalugg_his<PERSON>an", "rating": 556, "opRating": 443}, {"opponent": "over<PERSON><PERSON>l", "rating": 515, "opRating": 484}], "counters": [{"opponent": "heatran", "rating": 168}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 177}, {"opponent": "sandslash_alolan", "rating": 177}, {"opponent": "stunfisk_galarian", "rating": 204}, {"opponent": "litleo", "rating": 220}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 19309}, {"moveId": "TAKE_DOWN", "uses": 5691}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 13126}, {"moveId": "SEED_BOMB", "uses": 5125}, {"moveId": "GUNK_SHOT", "uses": 4696}, {"moveId": "ACID_SPRAY", "uses": 2030}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 46.3}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 690, "matchups": [{"opponent": "slowbro_galarian", "rating": 878, "opRating": 121}, {"opponent": "victini", "rating": 864, "opRating": 135}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 820, "opRating": 179}, {"opponent": "rotom_heat", "rating": 762, "opRating": 237}, {"opponent": "magnezone", "rating": 762, "opRating": 237}], "counters": [{"opponent": "obstagoon", "rating": 57}, {"opponent": "darum<PERSON>", "rating": 116}, {"opponent": "ninetales", "rating": 123}, {"opponent": "crocalor", "rating": 126}, {"opponent": "arcanine", "rating": 127}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 14604}, {"moveId": "METAL_CLAW", "uses": 10396}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 8619}, {"moveId": "X_SCISSOR", "uses": 7192}, {"moveId": "FOCUS_BLAST", "uses": 4747}, {"moveId": "IRON_HEAD", "uses": 4454}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 46.1}, {"speciesId": "ma<PERSON>le", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 635, "matchups": [{"opponent": "arctibax", "rating": 905, "opRating": 94}, {"opponent": "tyrunt", "rating": 905, "opRating": 94}, {"opponent": "raticate_alolan", "rating": 854, "opRating": 145}, {"opponent": "over<PERSON><PERSON>l", "rating": 807, "opRating": 192}, {"opponent": "avalugg_his<PERSON>an", "rating": 747, "opRating": 252}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magby", "rating": 116}, {"opponent": "magmar", "rating": 125}, {"opponent": "magmortar", "rating": 129}, {"opponent": "typhlosion", "rating": 132}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 6103}, {"moveId": "FIRE_FANG", "uses": 5722}, {"moveId": "ASTONISH", "uses": 5616}, {"moveId": "ICE_FANG", "uses": 4652}, {"moveId": "BITE", "uses": 2902}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 5943}, {"moveId": "PLAY_ROUGH", "uses": 5104}, {"moveId": "RETURN", "uses": 5040}, {"moveId": "VICE_GRIP", "uses": 4579}, {"moveId": "POWER_UP_PUNCH", "uses": 4326}]}, "moveset": ["FAIRY_WIND", "IRON_HEAD", "PLAY_ROUGH"], "score": 46.1}, {"speciesId": "vulpix", "speciesName": "Vulpix", "rating": 566, "matchups": [{"opponent": "piloswine", "rating": 859, "opRating": 140}, {"opponent": "sandslash_alolan", "rating": 859, "opRating": 140}, {"opponent": "melmetal", "rating": 813, "opRating": 186}, {"opponent": "<PERSON>rserker", "rating": 750, "opRating": 250}, {"opponent": "heatran", "rating": 677, "opRating": 322}], "counters": [{"opponent": "magmar", "rating": 138}, {"opponent": "tyrunt", "rating": 166}, {"opponent": "slowbro_galarian", "rating": 175}, {"opponent": "qwilfish_his<PERSON>an", "rating": 212}, {"opponent": "crocalor", "rating": 215}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 12521}, {"moveId": "QUICK_ATTACK", "uses": 12479}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 11733}, {"moveId": "BODY_SLAM", "uses": 6088}, {"moveId": "FLAME_CHARGE", "uses": 3074}, {"moveId": "FLAMETHROWER", "uses": 2562}, {"moveId": "RETURN", "uses": 1528}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "BODY_SLAM"], "score": 45.6}, {"speciesId": "magnemite", "speciesName": "Magnemite", "rating": 637, "matchups": [{"opponent": "victini", "rating": 825, "opRating": 174}, {"opponent": "over<PERSON><PERSON>l", "rating": 825, "opRating": 174}, {"opponent": "em<PERSON>ga", "rating": 825, "opRating": 174}, {"opponent": "piloswine", "rating": 825, "opRating": 174}, {"opponent": "incineroar", "rating": 797, "opRating": 202}], "counters": [{"opponent": "camerupt", "rating": 77}, {"opponent": "r<PERSON><PERSON>", "rating": 146}, {"opponent": "typhlosion", "rating": 170}, {"opponent": "arcanine", "rating": 176}, {"opponent": "rapidash", "rating": 180}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 7912}, {"moveId": "THUNDER_SHOCK", "uses": 7660}, {"moveId": "SPARK", "uses": 5113}, {"moveId": "METAL_SOUND", "uses": 4327}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 8577}, {"moveId": "MAGNET_BOMB", "uses": 8078}, {"moveId": "RETURN", "uses": 4676}, {"moveId": "THUNDERBOLT", "uses": 3660}]}, "moveset": ["VOLT_SWITCH", "MAGNET_BOMB", "DISCHARGE"], "score": 45.5}, {"speciesId": "meowscarada", "speciesName": "Meowscarada", "rating": 587, "matchups": [{"opponent": "electivire", "rating": 894, "opRating": 105}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 889, "opRating": 110}, {"opponent": "rai<PERSON>u", "rating": 868, "opRating": 131}, {"opponent": "dedenne", "rating": 855, "opRating": 144}, {"opponent": "stunfisk_galarian", "rating": 610, "opRating": 389}], "counters": [{"opponent": "charizard", "rating": 106}, {"opponent": "moltres", "rating": 106}, {"opponent": "darmanitan_standard", "rating": 113}, {"opponent": "heatran", "rating": 113}, {"opponent": "darum<PERSON>", "rating": 133}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 16250}, {"moveId": "CHARM", "uses": 8750}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 14749}, {"moveId": "GRASS_KNOT", "uses": 5429}, {"moveId": "PLAY_ROUGH", "uses": 2887}, {"moveId": "ENERGY_BALL", "uses": 1941}]}, "moveset": ["LEAFAGE", "NIGHT_SLASH", "GRASS_KNOT"], "score": 45.2}, {"speciesId": "persian_alolan", "speciesName": "Persian (Alolan)", "rating": 572, "matchups": [{"opponent": "plusle", "rating": 797, "opRating": 202}, {"opponent": "camerupt", "rating": 633, "opRating": 366}, {"opponent": "slowbro_galarian", "rating": 629, "opRating": 370}, {"opponent": "darum<PERSON>", "rating": 599, "opRating": 400}, {"opponent": "arcanine", "rating": 541, "opRating": 458}], "counters": [{"opponent": "obstagoon", "rating": 134}, {"opponent": "weezing_galarian", "rating": 147}, {"opponent": "raticate_alolan", "rating": 155}, {"opponent": "skuntank", "rating": 168}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 227}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 16026}, {"moveId": "SCRATCH", "uses": 8974}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 9299}, {"moveId": "TRAILBLAZE", "uses": 5027}, {"moveId": "DARK_PULSE", "uses": 3968}, {"moveId": "PAYBACK", "uses": 3624}, {"moveId": "PLAY_ROUGH", "uses": 3096}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "PLAY_ROUGH"], "score": 45.1}, {"speciesId": "purrloin", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 506, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 763, "opRating": 236}, {"opponent": "victini", "rating": 732, "opRating": 267}, {"opponent": "rotom_heat", "rating": 706, "opRating": 293}, {"opponent": "armarouge", "rating": 614, "opRating": 385}, {"opponent": "typhlosion", "rating": 504, "opRating": 495}], "counters": [{"opponent": "obstagoon", "rating": 90}, {"opponent": "araquanid", "rating": 165}, {"opponent": "raticate_alolan", "rating": 175}, {"opponent": "weezing_galarian", "rating": 208}, {"opponent": "qwilfish_his<PERSON>an", "rating": 241}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 18517}, {"moveId": "SCRATCH", "uses": 6483}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 14323}, {"moveId": "DARK_PULSE", "uses": 3867}, {"moveId": "RETURN", "uses": 3621}, {"moveId": "PLAY_ROUGH", "uses": 3224}]}, "moveset": ["SUCKER_PUNCH", "NIGHT_SLASH", "DARK_PULSE"], "score": 45.1}, {"speciesId": "lie<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 593, "matchups": [{"opponent": "victini", "rating": 797, "opRating": 202}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 797, "opRating": 202}, {"opponent": "rotom_heat", "rating": 757, "opRating": 242}, {"opponent": "magnezone", "rating": 698, "opRating": 301}, {"opponent": "rapidash", "rating": 591, "opRating": 408}], "counters": [{"opponent": "obstagoon", "rating": 79}, {"opponent": "over<PERSON><PERSON>l", "rating": 119}, {"opponent": "r<PERSON><PERSON>", "rating": 137}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 17489}, {"moveId": "CHARM", "uses": 7511}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 9815}, {"moveId": "PAYBACK", "uses": 4446}, {"moveId": "RETURN", "uses": 4312}, {"moveId": "PLAY_ROUGH", "uses": 3654}, {"moveId": "GUNK_SHOT", "uses": 2797}]}, "moveset": ["SNARL", "DARK_PULSE", "PLAY_ROUGH"], "score": 45}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 657, "matchups": [{"opponent": "talonflame", "rating": 801, "opRating": 198}, {"opponent": "rapidash", "rating": 779, "opRating": 220}, {"opponent": "pyroar", "rating": 779, "opRating": 220}, {"opponent": "armarouge", "rating": 779, "opRating": 220}, {"opponent": "arcanine", "rating": 741, "opRating": 258}], "counters": [{"opponent": "electrode_hisuian", "rating": 84}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "r<PERSON><PERSON>", "rating": 137}, {"opponent": "electivire", "rating": 140}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13579}, {"moveId": "ASTONISH", "uses": 11421}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 10424}, {"moveId": "HYDRO_PUMP", "uses": 10070}, {"moveId": "THUNDER", "uses": 4542}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 44.7}, {"speciesId": "nuzleaf", "speciesName": "Nuz<PERSON>", "rating": 590, "matchups": [{"opponent": "electabuzz", "rating": 869, "opRating": 130}, {"opponent": "rotom_heat", "rating": 843, "opRating": 156}, {"opponent": "victini", "rating": 805, "opRating": 194}, {"opponent": "stunfisk_galarian", "rating": 665, "opRating": 334}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 652, "opRating": 347}], "counters": [{"opponent": "houndoom", "rating": 138}, {"opponent": "muk_alolan", "rating": 140}, {"opponent": "weezing_galarian", "rating": 147}, {"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 16977}, {"moveId": "RAZOR_LEAF", "uses": 8023}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 11104}, {"moveId": "FOUL_PLAY", "uses": 8490}, {"moveId": "RETURN", "uses": 3197}, {"moveId": "GRASS_KNOT", "uses": 2194}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 44.5}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 610, "matchups": [{"opponent": "nidoking", "rating": 845, "opRating": 154}, {"opponent": "magnezone", "rating": 797, "opRating": 202}, {"opponent": "r<PERSON><PERSON>", "rating": 761, "opRating": 238}, {"opponent": "nidoqueen", "rating": 750, "opRating": 250}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 726, "opRating": 273}], "counters": [{"opponent": "pawniard", "rating": 93}, {"opponent": "incineroar", "rating": 117}, {"opponent": "victini", "rating": 139}, {"opponent": "qwilfish_his<PERSON>an", "rating": 179}, {"opponent": "ninetales", "rating": 182}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12711}, {"moveId": "BUG_BITE", "uses": 12289}], "chargedMoves": [{"moveId": "BULLDOZE", "uses": 13098}, {"moveId": "BUG_BUZZ", "uses": 8357}, {"moveId": "PSYBEAM", "uses": 3553}]}, "moveset": ["CONFUSION", "BUG_BUZZ", "BULLDOZE"], "score": 44.4}, {"speciesId": "fennekin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 545, "matchups": [{"opponent": "sandslash_alolan", "rating": 862, "opRating": 137}, {"opponent": "registeel", "rating": 858, "opRating": 141}, {"opponent": "genesect", "rating": 853, "opRating": 146}, {"opponent": "bisharp", "rating": 809, "opRating": 190}, {"opponent": "magneton", "rating": 774, "opRating": 225}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "tentacruel", "rating": 151}, {"opponent": "crocalor", "rating": 181}, {"opponent": "magmar", "rating": 183}, {"opponent": "litleo", "rating": 185}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17630}, {"moveId": "SCRATCH", "uses": 7370}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11715}, {"moveId": "PSYSHOCK", "uses": 8382}, {"moveId": "FLAMETHROWER", "uses": 4887}]}, "moveset": ["EMBER", "FLAME_CHARGE", "PSYSHOCK"], "score": 43.8}, {"speciesId": "pansear", "speciesName": "Pansear", "rating": 545, "matchups": [{"opponent": "registeel", "rating": 885, "opRating": 114}, {"opponent": "genesect", "rating": 885, "opRating": 114}, {"opponent": "s<PERSON><PERSON>", "rating": 877, "opRating": 122}, {"opponent": "sandslash_alolan", "rating": 838, "opRating": 161}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 700, "opRating": 299}], "counters": [{"opponent": "tyrunt", "rating": 127}, {"opponent": "magmar", "rating": 160}, {"opponent": "litleo", "rating": 171}, {"opponent": "typhlosion", "rating": 175}, {"opponent": "rapidash", "rating": 184}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 18165}, {"moveId": "SCRATCH", "uses": 6835}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 12260}, {"moveId": "CRUNCH", "uses": 9621}, {"moveId": "FLAME_BURST", "uses": 3141}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "CRUNCH"], "score": 43.8}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 595, "matchups": [{"opponent": "toxtricity", "rating": 920, "opRating": 79}, {"opponent": "tentacruel", "rating": 878, "opRating": 121}, {"opponent": "toxapex", "rating": 878, "opRating": 121}, {"opponent": "nidoqueen", "rating": 823, "opRating": 176}, {"opponent": "swalot", "rating": 819, "opRating": 180}], "counters": [{"opponent": "bisharp", "rating": 101}, {"opponent": "incineroar", "rating": 140}, {"opponent": "stunfisk_galarian", "rating": 147}, {"opponent": "umbreon", "rating": 151}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 190}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 10473}, {"moveId": "METAL_SOUND", "uses": 7711}, {"moveId": "FEINT_ATTACK", "uses": 6815}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 7347}, {"moveId": "BULLDOZE", "uses": 6015}, {"moveId": "PAYBACK", "uses": 4339}, {"moveId": "HEAVY_SLAM", "uses": 4022}, {"moveId": "PSYCHIC", "uses": 2005}, {"moveId": "FLASH_CANNON", "uses": 1324}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 43.4}, {"speciesId": "torchic", "speciesName": "Torchic", "rating": 541, "matchups": [{"opponent": "roserade", "rating": 875, "opRating": 125}, {"opponent": "registeel", "rating": 866, "opRating": 133}, {"opponent": "sandslash_alolan", "rating": 845, "opRating": 154}, {"opponent": "bisharp", "rating": 820, "opRating": 179}, {"opponent": "<PERSON>rserker", "rating": 720, "opRating": 279}], "counters": [{"opponent": "typhlosion", "rating": 158}, {"opponent": "magmar", "rating": 191}, {"opponent": "crocalor", "rating": 191}, {"opponent": "litleo", "rating": 195}, {"opponent": "incineroar", "rating": 196}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17859}, {"moveId": "SCRATCH", "uses": 7141}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 10635}, {"moveId": "ROCK_TOMB", "uses": 5957}, {"moveId": "FLAMETHROWER", "uses": 4434}, {"moveId": "RETURN", "uses": 3919}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_TOMB"], "score": 43.3}, {"speciesId": "dustox", "speciesName": "Dustox", "rating": 583, "matchups": [{"opponent": "nidoking", "rating": 869, "opRating": 130}, {"opponent": "r<PERSON><PERSON>", "rating": 806, "opRating": 193}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 764, "opRating": 235}, {"opponent": "magmar", "rating": 661, "opRating": 338}, {"opponent": "magmortar", "rating": 651, "opRating": 348}], "counters": [{"opponent": "incineroar", "rating": 94}, {"opponent": "armarouge", "rating": 162}, {"opponent": "heatran", "rating": 168}, {"opponent": "stunfisk_galarian", "rating": 186}, {"opponent": "ninetales", "rating": 194}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 14893}, {"moveId": "STRUGGLE_BUG", "uses": 10107}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8789}, {"moveId": "SILVER_WIND", "uses": 8523}, {"moveId": "BUG_BUZZ", "uses": 7680}]}, "moveset": ["CONFUSION", "SLUDGE_BOMB", "SILVER_WIND"], "score": 43}, {"speciesId": "pikachu_rock_star", "speciesName": "<PERSON><PERSON><PERSON> (Rock Star)", "rating": 499, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 804, "opRating": 195}, {"opponent": "amaura", "rating": 804, "opRating": 195}, {"opponent": "archen", "rating": 795, "opRating": 204}, {"opponent": "arm<PERSON>", "rating": 752, "opRating": 247}, {"opponent": "charizard", "rating": 623, "opRating": 376}], "counters": [{"opponent": "stunfisk_galarian", "rating": 168}, {"opponent": "charjabug", "rating": 170}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 194}, {"opponent": "arctibax", "rating": 198}, {"opponent": "electrode_hisuian", "rating": 207}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 17903}, {"moveId": "CHARM", "uses": 7097}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 12917}, {"moveId": "METEOR_MASH", "uses": 8147}, {"moveId": "PLAY_ROUGH", "uses": 3929}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "METEOR_MASH"], "score": 42.9}, {"speciesId": "pikachu_kari<PERSON>shi", "speciesName": "<PERSON><PERSON><PERSON> (Kariyushi)", "rating": 548, "matchups": [{"opponent": "zapdos", "rating": 838, "opRating": 161}, {"opponent": "magmortar", "rating": 804, "opRating": 195}, {"opponent": "magby", "rating": 804, "opRating": 195}, {"opponent": "talonflame", "rating": 695, "opRating": 304}, {"opponent": "rapidash", "rating": 666, "opRating": 333}], "counters": [{"opponent": "stunfisk_galarian", "rating": 118}, {"opponent": "piloswine", "rating": 145}, {"opponent": "nidoqueen", "rating": 151}, {"opponent": "steelix", "rating": 161}, {"opponent": "nidoking", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13865}, {"moveId": "QUICK_ATTACK", "uses": 11135}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17946}, {"moveId": "DISCHARGE", "uses": 3784}, {"moveId": "THUNDERBOLT", "uses": 3250}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "DISCHARGE"], "score": 42.7}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 748, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 923, "opRating": 76}, {"opponent": "nidoqueen", "rating": 919, "opRating": 80}, {"opponent": "electrode_hisuian", "rating": 899, "opRating": 100}, {"opponent": "over<PERSON><PERSON>l", "rating": 887, "opRating": 112}, {"opponent": "r<PERSON><PERSON>", "rating": 850, "opRating": 149}], "counters": [{"opponent": "darmanitan_standard", "rating": 76}, {"opponent": "pyroar", "rating": 88}, {"opponent": "arcanine", "rating": 90}, {"opponent": "litleo", "rating": 97}, {"opponent": "rapidash", "rating": 99}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 9735}, {"moveId": "POWDER_SNOW", "uses": 9544}, {"moveId": "METAL_CLAW", "uses": 5696}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 9144}, {"moveId": "ICE_PUNCH", "uses": 5802}, {"moveId": "AERIAL_ACE", "uses": 3518}, {"moveId": "RETURN", "uses": 1851}, {"moveId": "BULLDOZE", "uses": 1845}, {"moveId": "GYRO_BALL", "uses": 1534}, {"moveId": "BLIZZARD", "uses": 1251}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "DRILL_RUN"], "score": 42.4}, {"speciesId": "litten", "speciesName": "Litten", "rating": 535, "matchups": [{"opponent": "roserade", "rating": 875, "opRating": 125}, {"opponent": "registeel", "rating": 866, "opRating": 133}, {"opponent": "sandslash_alolan", "rating": 845, "opRating": 154}, {"opponent": "bisharp", "rating": 820, "opRating": 179}, {"opponent": "camerupt", "rating": 641, "opRating": 358}], "counters": [{"opponent": "tyrunt", "rating": 120}, {"opponent": "typhlosion", "rating": 175}, {"opponent": "crocalor", "rating": 181}, {"opponent": "litleo", "rating": 185}, {"opponent": "magmar", "rating": 196}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17695}, {"moveId": "SCRATCH", "uses": 7305}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11279}, {"moveId": "CRUNCH", "uses": 9003}, {"moveId": "FLAMETHROWER", "uses": 4708}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 42.2}, {"speciesId": "foongus", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 516, "matchups": [{"opponent": "luxio", "rating": 841, "opRating": 158}, {"opponent": "r<PERSON><PERSON>", "rating": 809, "opRating": 190}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 806, "opRating": 193}, {"opponent": "electabuzz", "rating": 741, "opRating": 258}, {"opponent": "magnezone", "rating": 658, "opRating": 341}], "counters": [{"opponent": "u<PERSON><PERSON><PERSON>", "rating": 86}, {"opponent": "houndoom", "rating": 130}, {"opponent": "amaura", "rating": 149}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "litleo", "rating": 178}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 15312}, {"moveId": "FEINT_ATTACK", "uses": 9688}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 11905}, {"moveId": "GRASS_KNOT", "uses": 7470}, {"moveId": "RETURN", "uses": 2951}, {"moveId": "ENERGY_BALL", "uses": 2718}]}, "moveset": ["ASTONISH", "BODY_SLAM", "GRASS_KNOT"], "score": 41.9}, {"speciesId": "arbok", "speciesName": "<PERSON>rbok", "rating": 498, "matchups": [{"opponent": "lileep", "rating": 694, "opRating": 305}, {"opponent": "cradily", "rating": 673, "opRating": 326}, {"opponent": "tyrunt", "rating": 632, "opRating": 367}, {"opponent": "magmortar", "rating": 590, "opRating": 409}, {"opponent": "poipole", "rating": 586, "opRating": 413}], "counters": [{"opponent": "klefki", "rating": 97}, {"opponent": "sandslash_alolan", "rating": 145}, {"opponent": "stunfisk_galarian", "rating": 162}, {"opponent": "steelix", "rating": 165}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 177}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 12057}, {"moveId": "BITE", "uses": 6990}, {"moveId": "ACID", "uses": 5943}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 9163}, {"moveId": "SLUDGE_WAVE", "uses": 6008}, {"moveId": "RETURN", "uses": 5448}, {"moveId": "GUNK_SHOT", "uses": 2344}, {"moveId": "ACID_SPRAY", "uses": 2039}]}, "moveset": ["DRAGON_TAIL", "DARK_PULSE", "SLUDGE_WAVE"], "score": 41.7}, {"speciesId": "flaaffy", "speciesName": "Flaaffy", "rating": 524, "matchups": [{"opponent": "rotom_wash", "rating": 877, "opRating": 122}, {"opponent": "golbat", "rating": 725, "opRating": 274}, {"opponent": "moltres_galarian", "rating": 678, "opRating": 321}, {"opponent": "magby", "rating": 579, "opRating": 420}, {"opponent": "magmortar", "rating": 576, "opRating": 423}], "counters": [{"opponent": "sandslash_alolan", "rating": 116}, {"opponent": "magmar", "rating": 160}, {"opponent": "pyroar", "rating": 164}, {"opponent": "arcanine", "rating": 168}, {"opponent": "typhlosion", "rating": 175}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 13260}, {"moveId": "CHARGE_BEAM", "uses": 11740}], "chargedMoves": [{"moveId": "POWER_GEM", "uses": 8133}, {"moveId": "DISCHARGE", "uses": 6186}, {"moveId": "TRAILBLAZE", "uses": 4871}, {"moveId": "RETURN", "uses": 3184}, {"moveId": "THUNDERBOLT", "uses": 2663}]}, "moveset": ["CHARGE_BEAM", "TRAILBLAZE", "DISCHARGE"], "score": 41.7}, {"speciesId": "helioptile", "speciesName": "Helioptile", "rating": 481, "matchups": [{"opponent": "bombirdier", "rating": 844, "opRating": 155}, {"opponent": "zoroark", "rating": 844, "opRating": 155}, {"opponent": "fletchinder", "rating": 621, "opRating": 378}, {"opponent": "charizard", "rating": 617, "opRating": 382}, {"opponent": "talonflame", "rating": 575, "opRating": 424}], "counters": [{"opponent": "escavalier", "rating": 116}, {"opponent": "wormadam_sandy", "rating": 126}, {"opponent": "piloswine", "rating": 180}, {"opponent": "stunfisk_galarian", "rating": 186}, {"opponent": "electrode_hisuian", "rating": 203}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12644}, {"moveId": "QUICK_ATTACK", "uses": 12356}], "chargedMoves": [{"moveId": "BULLDOZE", "uses": 10451}, {"moveId": "PARABOLIC_CHARGE", "uses": 10283}, {"moveId": "THUNDERBOLT", "uses": 4275}]}, "moveset": ["THUNDER_SHOCK", "BULLDOZE", "PARABOLIC_CHARGE"], "score": 41.6}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 585, "matchups": [{"opponent": "electabuzz", "rating": 902, "opRating": 97}, {"opponent": "r<PERSON><PERSON>", "rating": 890, "opRating": 109}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 890, "opRating": 109}, {"opponent": "nidoking", "rating": 875, "opRating": 125}, {"opponent": "rotom_heat", "rating": 855, "opRating": 144}], "counters": [{"opponent": "charizard", "rating": 106}, {"opponent": "crocalor", "rating": 123}, {"opponent": "litleo", "rating": 125}, {"opponent": "rapidash", "rating": 139}, {"opponent": "typhlosion", "rating": 153}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 11772}, {"moveId": "ACID", "uses": 7143}, {"moveId": "RAZOR_LEAF", "uses": 6083}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 11607}, {"moveId": "SLUDGE_BOMB", "uses": 5527}, {"moveId": "RETURN", "uses": 3364}, {"moveId": "LEAF_TORNADO", "uses": 2223}, {"moveId": "ACID_SPRAY", "uses": 1272}, {"moveId": "SOLAR_BEAM", "uses": 983}]}, "moveset": ["MAGICAL_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 41.6}, {"speciesId": "vullaby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 594, "matchups": [{"opponent": "nidoking", "rating": 716, "opRating": 283}, {"opponent": "magmar", "rating": 671, "opRating": 328}, {"opponent": "magby", "rating": 668, "opRating": 331}, {"opponent": "incineroar", "rating": 633, "opRating": 366}, {"opponent": "victini", "rating": 611, "opRating": 388}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magneton", "rating": 115}, {"opponent": "electivire", "rating": 140}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 152}, {"opponent": "electrode_hisuian", "rating": 163}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 13020}, {"moveId": "AIR_SLASH", "uses": 11980}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 12405}, {"moveId": "FOUL_PLAY", "uses": 8832}, {"moveId": "DARK_PULSE", "uses": 3762}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "FOUL_PLAY"], "score": 41.2}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 673, "matchups": [{"opponent": "skuntank", "rating": 903, "opRating": 96}, {"opponent": "rotom_heat", "rating": 857, "opRating": 142}, {"opponent": "tyrunt", "rating": 811, "opRating": 188}, {"opponent": "qwilfish_his<PERSON>an", "rating": 784, "opRating": 215}, {"opponent": "over<PERSON><PERSON>l", "rating": 773, "opRating": 226}], "counters": [{"opponent": "camerupt", "rating": 69}, {"opponent": "crocalor", "rating": 126}, {"opponent": "litleo", "rating": 129}, {"opponent": "typhlosion", "rating": 141}, {"opponent": "magmar", "rating": 147}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 11875}, {"moveId": "BUG_BITE", "uses": 8047}, {"moveId": "STRUGGLE_BUG", "uses": 5080}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 6133}, {"moveId": "MIRROR_SHOT", "uses": 4450}, {"moveId": "ROCK_TOMB", "uses": 4334}, {"moveId": "HEAVY_SLAM", "uses": 3606}, {"moveId": "SAND_TOMB", "uses": 3571}, {"moveId": "RETURN", "uses": 2900}]}, "moveset": ["VOLT_SWITCH", "EARTHQUAKE", "MIRROR_SHOT"], "score": 41.1}, {"speciesId": "bombirdier", "speciesName": "Bombirdier", "rating": 637, "matchups": [{"opponent": "nidoking", "rating": 817, "opRating": 182}, {"opponent": "talonflame", "rating": 707, "opRating": 292}, {"opponent": "rapidash", "rating": 673, "opRating": 326}, {"opponent": "pyroar", "rating": 673, "opRating": 326}, {"opponent": "armarouge", "rating": 673, "opRating": 326}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "amaura", "rating": 85}, {"opponent": "steelix", "rating": 100}, {"opponent": "magnezone", "rating": 102}, {"opponent": "r<PERSON><PERSON>", "rating": 137}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 13479}, {"moveId": "ROCK_THROW", "uses": 11521}], "chargedMoves": [{"moveId": "FLY", "uses": 9993}, {"moveId": "AERIAL_ACE", "uses": 7586}, {"moveId": "PAYBACK", "uses": 7404}]}, "moveset": ["WING_ATTACK", "FLY", "PAYBACK"], "score": 40.7}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 544, "matchups": [{"opponent": "venusaur", "rating": 855, "opRating": 144}, {"opponent": "nidoking", "rating": 839, "opRating": 160}, {"opponent": "rapidash", "rating": 585, "opRating": 414}, {"opponent": "camerupt", "rating": 585, "opRating": 414}, {"opponent": "darum<PERSON>", "rating": 558, "opRating": 441}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "magnezone", "rating": 107}, {"opponent": "amaura", "rating": 112}, {"opponent": "sandslash_alolan", "rating": 145}, {"opponent": "electivire", "rating": 168}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 21474}, {"moveId": "POUND", "uses": 3526}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 17267}, {"moveId": "HURRICANE", "uses": 4911}, {"moveId": "AIR_CUTTER", "uses": 2851}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 40.7}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 618, "matchups": [{"opponent": "dedenne", "rating": 854, "opRating": 145}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 790, "opRating": 209}, {"opponent": "electivire", "rating": 731, "opRating": 268}, {"opponent": "electrode_hisuian", "rating": 698, "opRating": 301}, {"opponent": "r<PERSON><PERSON>", "rating": 607, "opRating": 392}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "camerupt", "rating": 77}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 90}, {"opponent": "typhlosion", "rating": 158}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13600}, {"moveId": "ASTONISH", "uses": 11400}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 11036}, {"moveId": "BLIZZARD", "uses": 9184}, {"moveId": "THUNDER", "uses": 4787}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 40.7}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 608, "matchups": [{"opponent": "registeel", "rating": 869, "opRating": 130}, {"opponent": "rotom_heat", "rating": 752, "opRating": 247}, {"opponent": "nidoqueen", "rating": 742, "opRating": 257}, {"opponent": "qwilfish_his<PERSON>an", "rating": 696, "opRating": 303}, {"opponent": "stunfisk_galarian", "rating": 654, "opRating": 345}], "counters": [{"opponent": "armarouge", "rating": 109}, {"opponent": "delphox", "rating": 114}, {"opponent": "charizard", "rating": 132}, {"opponent": "ninetales", "rating": 146}, {"opponent": "crocalor", "rating": 147}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 13759}, {"moveId": "BUG_BITE", "uses": 11241}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 12415}, {"moveId": "MEGAHORN", "uses": 6579}, {"moveId": "AERIAL_ACE", "uses": 5228}, {"moveId": "ACID_SPRAY", "uses": 783}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 40.6}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Regieleki", "rating": 653, "matchups": [{"opponent": "dedenne", "rating": 841, "opRating": 158}, {"opponent": "em<PERSON>ga", "rating": 824, "opRating": 175}, {"opponent": "incineroar", "rating": 816, "opRating": 183}, {"opponent": "rotom_heat", "rating": 790, "opRating": 209}, {"opponent": "victini", "rating": 782, "opRating": 217}], "counters": [{"opponent": "arcanine", "rating": 86}, {"opponent": "magmar", "rating": 89}, {"opponent": "ninetales", "rating": 91}, {"opponent": "magmortar", "rating": 92}, {"opponent": "r<PERSON><PERSON>", "rating": 97}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 9047}, {"moveId": "THUNDER_SHOCK", "uses": 8580}, {"moveId": "LOCK_ON", "uses": 7354}], "chargedMoves": [{"moveId": "THUNDER", "uses": 12330}, {"moveId": "HYPER_BEAM", "uses": 7513}, {"moveId": "ZAP_CANNON", "uses": 5188}]}, "moveset": ["LOCK_ON", "THUNDER", "HYPER_BEAM"], "score": 40.4}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 642, "matchups": [{"opponent": "electabuzz", "rating": 844, "opRating": 155}, {"opponent": "em<PERSON>ga", "rating": 844, "opRating": 155}, {"opponent": "victini", "rating": 825, "opRating": 174}, {"opponent": "r<PERSON><PERSON>", "rating": 768, "opRating": 231}, {"opponent": "nidoqueen", "rating": 683, "opRating": 316}], "counters": [{"opponent": "obstagoon", "rating": 47}, {"opponent": "talonflame", "rating": 118}, {"opponent": "ninetales", "rating": 123}, {"opponent": "crocalor", "rating": 126}, {"opponent": "arcanine", "rating": 127}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 10308}, {"moveId": "ICE_SHARD", "uses": 8215}, {"moveId": "FEINT_ATTACK", "uses": 6480}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 10115}, {"moveId": "FOUL_PLAY", "uses": 7244}, {"moveId": "FOCUS_BLAST", "uses": 4102}, {"moveId": "TRIPLE_AXEL", "uses": 3564}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 40.2}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 607, "matchups": [{"opponent": "incineroar", "rating": 858, "opRating": 141}, {"opponent": "victini", "rating": 789, "opRating": 210}, {"opponent": "nidoking", "rating": 734, "opRating": 265}, {"opponent": "litleo", "rating": 610, "opRating": 389}, {"opponent": "typhlosion", "rating": 568, "opRating": 431}], "counters": [{"opponent": "electrode_hisuian", "rating": 115}, {"opponent": "steelix", "rating": 116}, {"opponent": "over<PERSON><PERSON>l", "rating": 119}, {"opponent": "ninetales", "rating": 142}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 18670}, {"moveId": "PECK", "uses": 6330}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 11665}, {"moveId": "DARK_PULSE", "uses": 7641}, {"moveId": "PSYCHIC", "uses": 3222}, {"moveId": "SKY_ATTACK", "uses": 2543}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 40.1}, {"speciesId": "electrike", "speciesName": "Electrike", "rating": 464, "matchups": [{"opponent": "bombirdier", "rating": 818, "opRating": 181}, {"opponent": "archeops", "rating": 783, "opRating": 216}, {"opponent": "archen", "rating": 747, "opRating": 252}, {"opponent": "charizard", "rating": 663, "opRating": 336}, {"opponent": "moltres", "rating": 663, "opRating": 336}], "counters": [{"opponent": "amaura", "rating": 85}, {"opponent": "stunfisk_galarian", "rating": 130}, {"opponent": "steelix", "rating": 149}, {"opponent": "sandslash_alolan", "rating": 157}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 247}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13532}, {"moveId": "SPARK", "uses": 11468}], "chargedMoves": [{"moveId": "SWIFT", "uses": 11679}, {"moveId": "DISCHARGE", "uses": 7923}, {"moveId": "THUNDERBOLT", "uses": 3404}, {"moveId": "RETURN", "uses": 2038}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "DISCHARGE"], "score": 39.9}, {"speciesId": "sandshrew_alolan", "speciesName": "Sandshrew (Alolan)", "rating": 664, "matchups": [{"opponent": "piloswine", "rating": 901, "opRating": 98}, {"opponent": "nidoqueen", "rating": 854, "opRating": 145}, {"opponent": "raticate_alolan", "rating": 854, "opRating": 145}, {"opponent": "slowbro_galarian", "rating": 854, "opRating": 145}, {"opponent": "rotom_heat", "rating": 807, "opRating": 192}], "counters": [{"opponent": "pyroar", "rating": 64}, {"opponent": "typhlosion", "rating": 68}, {"opponent": "armarouge", "rating": 70}, {"opponent": "rapidash", "rating": 72}, {"opponent": "darum<PERSON>", "rating": 81}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 15268}, {"moveId": "METAL_CLAW", "uses": 9732}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 11983}, {"moveId": "BLIZZARD", "uses": 5538}, {"moveId": "RETURN", "uses": 4175}, {"moveId": "GYRO_BALL", "uses": 3337}]}, "moveset": ["POWDER_SNOW", "NIGHT_SLASH", "BLIZZARD"], "score": 39.6}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 577, "matchups": [{"opponent": "tentacruel", "rating": 742, "opRating": 257}, {"opponent": "magmar", "rating": 688, "opRating": 311}, {"opponent": "nidoking", "rating": 688, "opRating": 311}, {"opponent": "magby", "rating": 684, "opRating": 315}, {"opponent": "nidoqueen", "rating": 638, "opRating": 361}], "counters": [{"opponent": "magnezone", "rating": 93}, {"opponent": "steelix", "rating": 100}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "<PERSON>on", "rating": 104}, {"opponent": "amaura", "rating": 112}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 15439}, {"moveId": "BITE", "uses": 9561}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 9780}, {"moveId": "SHADOW_BALL", "uses": 6299}, {"moveId": "POISON_FANG", "uses": 3240}, {"moveId": "AIR_CUTTER", "uses": 3043}, {"moveId": "SLUDGE_BOMB", "uses": 2652}]}, "moveset": ["AIR_SLASH", "POISON_FANG", "SHADOW_BALL"], "score": 39.5}, {"speciesId": "pikachu_pop_star", "speciesName": "<PERSON><PERSON><PERSON> (Pop Star)", "rating": 487, "matchups": [{"opponent": "rotom_wash", "rating": 809, "opRating": 190}, {"opponent": "xurkitree", "rating": 804, "opRating": 195}, {"opponent": "archen", "rating": 795, "opRating": 204}, {"opponent": "moltres", "rating": 685, "opRating": 314}, {"opponent": "charizard", "rating": 623, "opRating": 376}], "counters": [{"opponent": "arctibax", "rating": 102}, {"opponent": "nidoqueen", "rating": 143}, {"opponent": "nidoking", "rating": 157}, {"opponent": "stunfisk_galarian", "rating": 165}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 194}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 17490}, {"moveId": "CHARM", "uses": 7510}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 16699}, {"moveId": "PLAY_ROUGH", "uses": 5064}, {"moveId": "DRAINING_KISS", "uses": 3232}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "PLAY_ROUGH"], "score": 39.1}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 546, "matchups": [{"opponent": "nidoking", "rating": 839, "opRating": 160}, {"opponent": "camerupt", "rating": 742, "opRating": 257}, {"opponent": "darum<PERSON>", "rating": 714, "opRating": 285}, {"opponent": "magby", "rating": 683, "opRating": 316}, {"opponent": "magmar", "rating": 671, "opRating": 328}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "magnezone", "rating": 107}, {"opponent": "steelix", "rating": 133}, {"opponent": "avalugg_his<PERSON>an", "rating": 157}, {"opponent": "electivire", "rating": 168}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 21472}, {"moveId": "POUND", "uses": 3528}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 17266}, {"moveId": "HURRICANE", "uses": 4913}, {"moveId": "AIR_CUTTER", "uses": 2850}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 39}, {"speciesId": "wormadam_trash", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Trash)", "rating": 571, "matchups": [{"opponent": "toxtricity", "rating": 899, "opRating": 100}, {"opponent": "swalot", "rating": 787, "opRating": 212}, {"opponent": "nidoking", "rating": 764, "opRating": 235}, {"opponent": "avalugg_his<PERSON>an", "rating": 735, "opRating": 264}, {"opponent": "nidoqueen", "rating": 671, "opRating": 328}], "counters": [{"opponent": "incineroar", "rating": 94}, {"opponent": "heatran", "rating": 113}, {"opponent": "armarouge", "rating": 127}, {"opponent": "victini", "rating": 139}, {"opponent": "litleo", "rating": 157}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 9148}, {"moveId": "BUG_BITE", "uses": 8871}, {"moveId": "METAL_SOUND", "uses": 6976}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 11404}, {"moveId": "IRON_HEAD", "uses": 8567}, {"moveId": "PSYBEAM", "uses": 5017}]}, "moveset": ["CONFUSION", "BUG_BUZZ", "IRON_HEAD"], "score": 38.9}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 559, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 888, "opRating": 111}, {"opponent": "slowbro_galarian", "rating": 709, "opRating": 290}, {"opponent": "arm<PERSON>", "rating": 692, "opRating": 307}, {"opponent": "delphox", "rating": 688, "opRating": 311}, {"opponent": "electrode_hisuian", "rating": 542, "opRating": 457}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "crocalor", "rating": 85}, {"opponent": "litleo", "rating": 87}, {"opponent": "arcanine", "rating": 102}, {"opponent": "typhlosion", "rating": 106}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 9836}, {"moveId": "TACKLE", "uses": 9582}, {"moveId": "ACID", "uses": 5578}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6089}, {"moveId": "SHADOW_BALL", "uses": 5643}, {"moveId": "DARK_PULSE", "uses": 5410}, {"moveId": "THUNDERBOLT", "uses": 4428}, {"moveId": "RETURN", "uses": 3417}]}, "moveset": ["INFESTATION", "SLUDGE_BOMB", "SHADOW_BALL"], "score": 38.4}, {"speciesId": "ekans", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 484, "matchups": [{"opponent": "electrode_hisuian", "rating": 804, "opRating": 195}, {"opponent": "electivire", "rating": 804, "opRating": 195}, {"opponent": "dedenne", "rating": 804, "opRating": 195}, {"opponent": "darmanitan_standard", "rating": 623, "opRating": 376}, {"opponent": "pyroar", "rating": 557, "opRating": 442}], "counters": [{"opponent": "stunfisk_galarian", "rating": 82}, {"opponent": "steelix", "rating": 92}, {"opponent": "<PERSON>on", "rating": 108}, {"opponent": "sandslash_alolan", "rating": 161}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 198}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 17221}, {"moveId": "ACID", "uses": 7779}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 7681}, {"moveId": "SLUDGE_BOMB", "uses": 6263}, {"moveId": "WRAP", "uses": 5247}, {"moveId": "RETURN", "uses": 4091}, {"moveId": "GUNK_SHOT", "uses": 1590}]}, "moveset": ["POISON_STING", "POISON_FANG", "SLUDGE_BOMB"], "score": 37.9}, {"speciesId": "charcadet", "speciesName": "Charcadet", "rating": 508, "matchups": [{"opponent": "registeel", "rating": 880, "opRating": 119}, {"opponent": "genesect", "rating": 871, "opRating": 128}, {"opponent": "piloswine", "rating": 818, "opRating": 181}, {"opponent": "klinklang", "rating": 774, "opRating": 225}, {"opponent": "bisharp", "rating": 747, "opRating": 252}], "counters": [{"opponent": "magmortar", "rating": 129}, {"opponent": "magby", "rating": 129}, {"opponent": "tyrunt", "rating": 139}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "qwilfish_his<PERSON>an", "rating": 166}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16050}, {"moveId": "EMBER", "uses": 8950}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16095}, {"moveId": "FLAMETHROWER", "uses": 6736}, {"moveId": "HEAT_WAVE", "uses": 2235}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "FLAMETHROWER"], "score": 37.6}, {"speciesId": "slugma", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 481, "matchups": [{"opponent": "s<PERSON><PERSON>", "rating": 871, "opRating": 128}, {"opponent": "registeel", "rating": 858, "opRating": 141}, {"opponent": "genesect", "rating": 853, "opRating": 146}, {"opponent": "sandslash_alolan", "rating": 818, "opRating": 181}, {"opponent": "bisharp", "rating": 747, "opRating": 252}], "counters": [{"opponent": "tyrunt", "rating": 120}, {"opponent": "ninetales", "rating": 146}, {"opponent": "magby", "rating": 154}, {"opponent": "magmar", "rating": 165}, {"opponent": "typhlosion", "rating": 175}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 14237}, {"moveId": "ROCK_THROW", "uses": 10763}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11778}, {"moveId": "ROCK_SLIDE", "uses": 10273}, {"moveId": "FLAME_BURST", "uses": 2981}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 37.6}, {"speciesId": "charmander", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 483, "matchups": [{"opponent": "registeel", "rating": 855, "opRating": 144}, {"opponent": "genesect", "rating": 851, "opRating": 148}, {"opponent": "sandslash_alolan", "rating": 815, "opRating": 184}, {"opponent": "bisharp", "rating": 806, "opRating": 193}, {"opponent": "magneton", "rating": 770, "opRating": 229}], "counters": [{"opponent": "tyrunt", "rating": 131}, {"opponent": "magby", "rating": 154}, {"opponent": "victini", "rating": 158}, {"opponent": "typhlosion", "rating": 175}, {"opponent": "magmar", "rating": 183}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17596}, {"moveId": "SCRATCH", "uses": 7404}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11840}, {"moveId": "RETURN", "uses": 5255}, {"moveId": "FLAMETHROWER", "uses": 4985}, {"moveId": "FLAME_BURST", "uses": 3029}]}, "moveset": ["EMBER", "FLAME_CHARGE", "RETURN"], "score": 37.5}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 453, "matchups": [{"opponent": "xurkitree", "rating": 896, "opRating": 103}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 785, "opRating": 214}, {"opponent": "r<PERSON><PERSON>", "rating": 772, "opRating": 227}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 772, "opRating": 227}, {"opponent": "electabuzz", "rating": 714, "opRating": 285}], "counters": [{"opponent": "ho_oh", "rating": 149}, {"opponent": "talonflame", "rating": 171}, {"opponent": "fletchinder", "rating": 172}, {"opponent": "charizard", "rating": 188}, {"opponent": "ninetales", "rating": 222}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 12989}, {"moveId": "RAZOR_LEAF", "uses": 12011}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 10587}, {"moveId": "MOONBLAST", "uses": 7138}, {"moveId": "PETAL_BLIZZARD", "uses": 5176}, {"moveId": "SOLAR_BEAM", "uses": 2047}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 37.2}, {"speciesId": "chim<PERSON>r", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 479, "matchups": [{"opponent": "registeel", "rating": 865, "opRating": 134}, {"opponent": "genesect", "rating": 861, "opRating": 138}, {"opponent": "sandslash_alolan", "rating": 827, "opRating": 172}, {"opponent": "bisharp", "rating": 789, "opRating": 210}, {"opponent": "<PERSON>rserker", "rating": 693, "opRating": 306}], "counters": [{"opponent": "tyrunt", "rating": 131}, {"opponent": "magby", "rating": 154}, {"opponent": "typhlosion", "rating": 175}, {"opponent": "magmar", "rating": 183}, {"opponent": "ninetales", "rating": 186}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17552}, {"moveId": "SCRATCH", "uses": 7448}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 12232}, {"moveId": "RETURN", "uses": 5347}, {"moveId": "FLAMETHROWER", "uses": 5080}, {"moveId": "FLAME_WHEEL", "uses": 2270}]}, "moveset": ["EMBER", "FLAME_CHARGE", "RETURN"], "score": 37.1}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 632, "matchups": [{"opponent": "electabuzz", "rating": 868, "opRating": 131}, {"opponent": "nidoking", "rating": 863, "opRating": 136}, {"opponent": "r<PERSON><PERSON>", "rating": 852, "opRating": 147}, {"opponent": "magnezone", "rating": 773, "opRating": 226}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 710, "opRating": 289}], "counters": [{"opponent": "heatran", "rating": 50}, {"opponent": "arcanine", "rating": 69}, {"opponent": "pyroar", "rating": 84}, {"opponent": "typhlosion", "rating": 89}, {"opponent": "rapidash", "rating": 94}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 9135}, {"moveId": "BULLET_SEED", "uses": 7083}, {"moveId": "MAGICAL_LEAF", "uses": 6021}, {"moveId": "RAZOR_LEAF", "uses": 2759}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 10805}, {"moveId": "SLUDGE_BOMB", "uses": 5018}, {"moveId": "GRASS_KNOT", "uses": 4190}, {"moveId": "LEAF_STORM", "uses": 2493}, {"moveId": "DAZZLING_GLEAM", "uses": 1611}, {"moveId": "SOLAR_BEAM", "uses": 871}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 37.1}, {"speciesId": "gloom", "speciesName": "Gloom", "rating": 455, "matchups": [{"opponent": "xurkitree", "rating": 903, "opRating": 96}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 792, "opRating": 207}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 788, "opRating": 211}, {"opponent": "r<PERSON><PERSON>", "rating": 761, "opRating": 238}, {"opponent": "manectric", "rating": 746, "opRating": 253}], "counters": [{"opponent": "talonflame", "rating": 129}, {"opponent": "golbat", "rating": 132}, {"opponent": "ho_oh", "rating": 149}, {"opponent": "heatran", "rating": 168}, {"opponent": "charizard", "rating": 188}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 13076}, {"moveId": "RAZOR_LEAF", "uses": 11924}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8785}, {"moveId": "MOONBLAST", "uses": 6122}, {"moveId": "RETURN", "uses": 5535}, {"moveId": "PETAL_BLIZZARD", "uses": 4554}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 37}, {"speciesId": "tadbulb", "speciesName": "Tadbulb", "rating": 460, "matchups": [{"opponent": "archeops", "rating": 828, "opRating": 171}, {"opponent": "archen", "rating": 772, "opRating": 227}, {"opponent": "moltres", "rating": 716, "opRating": 283}, {"opponent": "charizard", "rating": 660, "opRating": 339}, {"opponent": "talonflame", "rating": 632, "opRating": 367}], "counters": [{"opponent": "stunfisk_galarian", "rating": 82}, {"opponent": "piloswine", "rating": 141}, {"opponent": "nidoqueen", "rating": 143}, {"opponent": "steelix", "rating": 161}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 198}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13688}, {"moveId": "WATER_GUN", "uses": 11312}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 10634}, {"moveId": "PARABOLIC_CHARGE", "uses": 10540}, {"moveId": "ZAP_CANNON", "uses": 3800}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "PARABOLIC_CHARGE"], "score": 36.5}, {"speciesId": "sneasel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 565, "matchups": [{"opponent": "piloswine", "rating": 831, "opRating": 168}, {"opponent": "rai<PERSON>u", "rating": 813, "opRating": 186}, {"opponent": "moltres_galarian", "rating": 745, "opRating": 254}, {"opponent": "nidoqueen", "rating": 659, "opRating": 340}, {"opponent": "qwilfish_his<PERSON>an", "rating": 640, "opRating": 359}], "counters": [{"opponent": "crocalor", "rating": 106}, {"opponent": "litleo", "rating": 108}, {"opponent": "typhlosion", "rating": 132}, {"opponent": "rapidash", "rating": 139}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 13747}, {"moveId": "FEINT_ATTACK", "uses": 11253}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 8037}, {"moveId": "FOUL_PLAY", "uses": 6178}, {"moveId": "ICE_PUNCH", "uses": 5766}, {"moveId": "TRIPLE_AXEL", "uses": 2813}, {"moveId": "RETURN", "uses": 2194}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICE_PUNCH"], "score": 36.4}, {"speciesId": "voltorb", "speciesName": "Voltorb", "rating": 468, "matchups": [{"opponent": "golbat", "rating": 827, "opRating": 172}, {"opponent": "talonflame", "rating": 676, "opRating": 323}, {"opponent": "moltres_galarian", "rating": 592, "opRating": 407}, {"opponent": "darmanitan_standard", "rating": 588, "opRating": 411}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 548, "opRating": 451}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 86}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 98}, {"opponent": "electrode_hisuian", "rating": 119}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 13394}, {"moveId": "TACKLE", "uses": 11606}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 8949}, {"moveId": "SIGNAL_BEAM", "uses": 4969}, {"moveId": "RETURN", "uses": 4679}, {"moveId": "THUNDERBOLT", "uses": 3810}, {"moveId": "GYRO_BALL", "uses": 2564}]}, "moveset": ["SPARK", "DISCHARGE", "SIGNAL_BEAM"], "score": 36.1}, {"speciesId": "nidoran_male", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 460, "matchups": [{"opponent": "shiftry", "rating": 851, "opRating": 148}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 822, "opRating": 177}, {"opponent": "rotom_mow", "rating": 797, "opRating": 202}, {"opponent": "xurkitree", "rating": 764, "opRating": 235}, {"opponent": "zoroark", "rating": 698, "opRating": 301}], "counters": [{"opponent": "sandslash_alolan", "rating": 104}, {"opponent": "<PERSON>on", "rating": 120}, {"opponent": "stunfisk_galarian", "rating": 133}, {"opponent": "steelix", "rating": 141}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 17395}, {"moveId": "PECK", "uses": 7605}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 11066}, {"moveId": "SLUDGE_BOMB", "uses": 7574}, {"moveId": "HORN_ATTACK", "uses": 3572}, {"moveId": "RETURN", "uses": 2834}]}, "moveset": ["POISON_STING", "BODY_SLAM", "SLUDGE_BOMB"], "score": 36}, {"speciesId": "nidoran_female", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 460, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 818, "opRating": 181}, {"opponent": "darmanitan_standard", "rating": 581, "opRating": 418}, {"opponent": "simisear", "rating": 581, "opRating": 418}, {"opponent": "moltres", "rating": 581, "opRating": 418}, {"opponent": "magby", "rating": 555, "opRating": 444}], "counters": [{"opponent": "stunfisk_galarian", "rating": 79}, {"opponent": "steelix", "rating": 80}, {"opponent": "<PERSON>on", "rating": 100}, {"opponent": "sandslash_alolan", "rating": 133}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 161}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 15996}, {"moveId": "BITE", "uses": 9004}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 9329}, {"moveId": "POISON_FANG", "uses": 7362}, {"moveId": "SLUDGE_BOMB", "uses": 6022}, {"moveId": "RETURN", "uses": 2335}]}, "moveset": ["POISON_STING", "BODY_SLAM", "POISON_FANG"], "score": 35.5}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537, "matchups": [{"opponent": "munchlax", "rating": 790, "opRating": 209}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 754, "opRating": 245}, {"opponent": "nidoqueen", "rating": 733, "opRating": 266}, {"opponent": "qwilfish_his<PERSON>an", "rating": 696, "opRating": 303}, {"opponent": "over<PERSON><PERSON>l", "rating": 631, "opRating": 368}], "counters": [{"opponent": "magnezone", "rating": 102}, {"opponent": "heatran", "rating": 113}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "electrode_hisuian", "rating": 123}, {"opponent": "ninetales", "rating": 146}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 12659}, {"moveId": "AIR_SLASH", "uses": 12341}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 14326}, {"moveId": "RETURN", "uses": 4242}, {"moveId": "FLASH_CANNON", "uses": 3363}, {"moveId": "SKY_ATTACK", "uses": 3072}]}, "moveset": ["STEEL_WING", "BRAVE_BIRD", "SKY_ATTACK"], "score": 35.3}, {"speciesId": "zorua", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 419, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 730, "opRating": 269}, {"opponent": "minun", "rating": 707, "opRating": 292}, {"opponent": "rotom_heat", "rating": 650, "opRating": 349}, {"opponent": "plusle", "rating": 650, "opRating": 349}], "counters": [{"opponent": "obstagoon", "rating": 134}, {"opponent": "weezing_galarian", "rating": 147}, {"opponent": "raticate_alolan", "rating": 165}, {"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 16075}, {"moveId": "SCRATCH", "uses": 8925}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 8130}, {"moveId": "NIGHT_SHADE", "uses": 7234}, {"moveId": "AERIAL_ACE", "uses": 6124}, {"moveId": "DARK_PULSE", "uses": 3458}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "NIGHT_SHADE"], "score": 34.8}, {"speciesId": "meowth_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 372, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "bronzong", "rating": 778, "opRating": 221}, {"opponent": "pikachu_horizons", "rating": 730, "opRating": 269}, {"opponent": "wormadam_sandy", "rating": 561, "opRating": 438}, {"opponent": "slowking_galarian", "rating": 508, "opRating": 491}], "counters": [{"opponent": "obstagoon", "rating": 134}, {"opponent": "qwilfish_his<PERSON>an", "rating": 154}, {"opponent": "houndoom", "rating": 155}, {"opponent": "over<PERSON><PERSON>l", "rating": 173}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 239}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 14283}, {"moveId": "SCRATCH", "uses": 10717}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 12753}, {"moveId": "TRAILBLAZE", "uses": 4799}, {"moveId": "FOUL_PLAY", "uses": 4000}, {"moveId": "DARK_PULSE", "uses": 3426}]}, "moveset": ["BITE", "NIGHT_SLASH", "TRAILBLAZE"], "score": 34.3}, {"speciesId": "roselia", "speciesName": "Roselia", "rating": 487, "matchups": [{"opponent": "rotom_heat", "rating": 813, "opRating": 186}, {"opponent": "luxio", "rating": 752, "opRating": 247}, {"opponent": "electabuzz", "rating": 658, "opRating": 341}, {"opponent": "electrode_hisuian", "rating": 658, "opRating": 341}, {"opponent": "weezing_galarian", "rating": 640, "opRating": 359}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "heatran", "rating": 127}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "nidoqueen", "rating": 133}, {"opponent": "nidoking", "rating": 145}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 12043}, {"moveId": "MAGICAL_LEAF", "uses": 8146}, {"moveId": "RAZOR_LEAF", "uses": 4830}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 13255}, {"moveId": "PETAL_BLIZZARD", "uses": 6872}, {"moveId": "DAZZLING_GLEAM", "uses": 4880}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 34.2}, {"speciesId": "shinx", "speciesName": "Shinx", "rating": 411, "matchups": [{"opponent": "bombirdier", "rating": 787, "opRating": 212}, {"opponent": "golbat", "rating": 770, "opRating": 229}, {"opponent": "archeops", "rating": 745, "opRating": 254}, {"opponent": "weavile", "rating": 733, "opRating": 266}, {"opponent": "talonflame", "rating": 512, "opRating": 487}], "counters": [{"opponent": "tyrunt", "rating": 96}, {"opponent": "steelix", "rating": 104}, {"opponent": "stunfisk_galarian", "rating": 127}, {"opponent": "qwilfish_his<PERSON>an", "rating": 137}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 202}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 13364}, {"moveId": "TACKLE", "uses": 11636}], "chargedMoves": [{"moveId": "SWIFT", "uses": 11694}, {"moveId": "DISCHARGE", "uses": 7915}, {"moveId": "THUNDERBOLT", "uses": 3393}, {"moveId": "RETURN", "uses": 2038}]}, "moveset": ["SPARK", "SWIFT", "DISCHARGE"], "score": 34.2}, {"speciesId": "oddish", "speciesName": "<PERSON><PERSON>", "rating": 429, "matchups": [{"opponent": "xurkitree", "rating": 862, "opRating": 137}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 770, "opRating": 229}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 762, "opRating": 237}, {"opponent": "r<PERSON><PERSON>", "rating": 733, "opRating": 266}, {"opponent": "avalugg_his<PERSON>an", "rating": 645, "opRating": 354}], "counters": [{"opponent": "talonflame", "rating": 129}, {"opponent": "heatran", "rating": 140}, {"opponent": "charizard", "rating": 170}, {"opponent": "litleo", "rating": 209}, {"opponent": "typhlosion", "rating": 213}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 13065}, {"moveId": "RAZOR_LEAF", "uses": 11935}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8135}, {"moveId": "SEED_BOMB", "uses": 6214}, {"moveId": "MOONBLAST", "uses": 5557}, {"moveId": "RETURN", "uses": 5051}]}, "moveset": ["RAZOR_LEAF", "SEED_BOMB", "SLUDGE_BOMB"], "score": 33.8}, {"speciesId": "absol", "speciesName": "Absol", "rating": 541, "matchups": [{"opponent": "incineroar", "rating": 848, "opRating": 151}, {"opponent": "victini", "rating": 766, "opRating": 233}, {"opponent": "rai<PERSON>u", "rating": 766, "opRating": 233}, {"opponent": "rotom_heat", "rating": 743, "opRating": 256}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 720, "opRating": 279}], "counters": [{"opponent": "obstagoon", "rating": 76}, {"opponent": "weezing_galarian", "rating": 95}, {"opponent": "over<PERSON><PERSON>l", "rating": 119}, {"opponent": "qwilfish_his<PERSON>an", "rating": 120}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 13119}, {"moveId": "PSYCHO_CUT", "uses": 11881}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 9177}, {"moveId": "MEGAHORN", "uses": 7061}, {"moveId": "THUNDER", "uses": 4584}, {"moveId": "PAYBACK", "uses": 4193}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 33.7}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 640, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 863, "opRating": 136}, {"opponent": "piloswine", "rating": 853, "opRating": 146}, {"opponent": "avalugg_his<PERSON>an", "rating": 825, "opRating": 174}, {"opponent": "electivire", "rating": 783, "opRating": 216}, {"opponent": "nidoqueen", "rating": 716, "opRating": 283}], "counters": [{"opponent": "darmanitan_standard", "rating": 40}, {"opponent": "charizard", "rating": 51}, {"opponent": "heatran", "rating": 54}, {"opponent": "darum<PERSON>", "rating": 74}, {"opponent": "litleo", "rating": 101}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 13439}, {"moveId": "BULLET_PUNCH", "uses": 11561}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 9076}, {"moveId": "X_SCISSOR", "uses": 8311}, {"moveId": "TRAILBLAZE", "uses": 3940}, {"moveId": "IRON_HEAD", "uses": 3668}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "TRAILBLAZE"], "score": 33.4}, {"speciesId": "klink", "speciesName": "Klink", "rating": 421, "matchups": [{"opponent": "bombirdier", "rating": 823, "opRating": 176}, {"opponent": "honch<PERSON><PERSON>", "rating": 809, "opRating": 190}, {"opponent": "archen", "rating": 681, "opRating": 318}, {"opponent": "toxapex", "rating": 641, "opRating": 358}, {"opponent": "poipole", "rating": 606, "opRating": 393}], "counters": [{"opponent": "stunfisk_galarian", "rating": 94}, {"opponent": "electrode_hisuian", "rating": 141}, {"opponent": "steelix", "rating": 141}, {"opponent": "nidoqueen", "rating": 147}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 181}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 11643}, {"moveId": "METAL_SOUND", "uses": 7719}, {"moveId": "CHARGE_BEAM", "uses": 5663}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 12003}, {"moveId": "VICE_GRIP", "uses": 8803}, {"moveId": "ZAP_CANNON", "uses": 4199}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "VICE_GRIP"], "score": 33}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 678, "matchups": [{"opponent": "over<PERSON><PERSON>l", "rating": 860, "opRating": 140}, {"opponent": "victini", "rating": 845, "opRating": 155}, {"opponent": "electrode_hisuian", "rating": 835, "opRating": 165}, {"opponent": "nidoqueen", "rating": 830, "opRating": 170}, {"opponent": "r<PERSON><PERSON>", "rating": 794, "opRating": 205}], "counters": [{"opponent": "charizard", "rating": 47}, {"opponent": "darmanitan_standard", "rating": 62}, {"opponent": "crocalor", "rating": 71}, {"opponent": "litleo", "rating": 73}, {"opponent": "typhlosion", "rating": 89}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 14511}, {"moveId": "METAL_CLAW", "uses": 10489}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 9659}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 7705}, {"moveId": "MAGNET_BOMB", "uses": 6274}, {"moveId": "HYPER_BEAM", "uses": 1389}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 32.5}, {"speciesId": "varoom", "speciesName": "Varoom", "rating": 452, "matchups": [{"opponent": "cradily", "rating": 775, "opRating": 225}, {"opponent": "poipole", "rating": 745, "opRating": 254}, {"opponent": "avalugg_his<PERSON>an", "rating": 666, "opRating": 333}, {"opponent": "skuntank", "rating": 600, "opRating": 400}, {"opponent": "tyrunt", "rating": 533, "opRating": 466}], "counters": [{"opponent": "steelix", "rating": 68}, {"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "nidoking", "rating": 133}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 14511}, {"moveId": "LICK", "uses": 10489}], "chargedMoves": [{"moveId": "GUNK_SHOT", "uses": 10442}, {"moveId": "GYRO_BALL", "uses": 9986}, {"moveId": "ACID_SPRAY", "uses": 4554}]}, "moveset": ["POISON_JAB", "GYRO_BALL", "GUNK_SHOT"], "score": 32.4}, {"speciesId": "koffing", "speciesName": "<PERSON><PERSON>", "rating": 473, "matchups": [{"opponent": "roserade", "rating": 725, "opRating": 274}, {"opponent": "houndour", "rating": 676, "opRating": 323}, {"opponent": "shiftry", "rating": 623, "opRating": 376}, {"opponent": "houndoom", "rating": 561, "opRating": 438}, {"opponent": "electrode_hisuian", "rating": 508, "opRating": 491}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "crocalor", "rating": 85}, {"opponent": "litleo", "rating": 87}, {"opponent": "magmar", "rating": 98}, {"opponent": "typhlosion", "rating": 106}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 9643}, {"moveId": "TACKLE", "uses": 9560}, {"moveId": "ACID", "uses": 5782}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 7461}, {"moveId": "SLUDGE_BOMB", "uses": 6679}, {"moveId": "SLUDGE", "uses": 6611}, {"moveId": "RETURN", "uses": 4216}]}, "moveset": ["INFESTATION", "DARK_PULSE", "SLUDGE_BOMB"], "score": 31.7}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 503, "matchups": [{"opponent": "tentacruel", "rating": 828, "opRating": 171}, {"opponent": "skuntank", "rating": 803, "opRating": 196}, {"opponent": "grimer_alolan", "rating": 803, "opRating": 196}, {"opponent": "nidoqueen", "rating": 735, "opRating": 264}, {"opponent": "avalugg_his<PERSON>an", "rating": 700, "opRating": 299}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "electrode_hisuian", "rating": 123}, {"opponent": "ninetales", "rating": 134}, {"opponent": "arcanine", "rating": 151}, {"opponent": "typhlosion", "rating": 158}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 20530}, {"moveId": "ZEN_HEADBUTT", "uses": 4470}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 9400}, {"moveId": "EARTHQUAKE", "uses": 8487}, {"moveId": "PSYCHIC", "uses": 5621}, {"moveId": "FLASH_CANNON", "uses": 1474}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "PSYCHIC"], "score": 31.7}, {"speciesId": "venonat", "speciesName": "Venonat", "rating": 465, "matchups": [{"opponent": "nidoking", "rating": 806, "opRating": 193}, {"opponent": "toxtricity", "rating": 785, "opRating": 214}, {"opponent": "beedrill", "rating": 785, "opRating": 214}, {"opponent": "magmortar", "rating": 531, "opRating": 468}, {"opponent": "magmar", "rating": 524, "opRating": 475}], "counters": [{"opponent": "bisharp", "rating": 92}, {"opponent": "stunfisk_galarian", "rating": 106}, {"opponent": "armarouge", "rating": 109}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 161}, {"opponent": "typhlosion", "rating": 175}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12784}, {"moveId": "BUG_BITE", "uses": 12216}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 9598}, {"moveId": "SIGNAL_BEAM", "uses": 7018}, {"moveId": "RETURN", "uses": 4914}, {"moveId": "PSYBEAM", "uses": 3456}]}, "moveset": ["CONFUSION", "POISON_FANG", "SIGNAL_BEAM"], "score": 31.4}, {"speciesId": "meowth_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 498, "matchups": [{"opponent": "toxtricity", "rating": 822, "opRating": 177}, {"opponent": "rotom_heat", "rating": 779, "opRating": 220}, {"opponent": "tyrunt", "rating": 748, "opRating": 251}, {"opponent": "qwilfish_his<PERSON>an", "rating": 696, "opRating": 303}, {"opponent": "avalugg_his<PERSON>an", "rating": 673, "opRating": 326}], "counters": [{"opponent": "ninetales", "rating": 83}, {"opponent": "darmanitan_standard", "rating": 91}, {"opponent": "crocalor", "rating": 95}, {"opponent": "litleo", "rating": 97}, {"opponent": "arcanine", "rating": 114}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 15690}, {"moveId": "SCRATCH", "uses": 9310}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 9673}, {"moveId": "DIG", "uses": 7741}, {"moveId": "TRAILBLAZE", "uses": 4680}, {"moveId": "GYRO_BALL", "uses": 2933}]}, "moveset": ["METAL_CLAW", "NIGHT_SLASH", "DIG"], "score": 31.3}, {"speciesId": "spinarak", "speciesName": "Spinarak", "rating": 393, "matchups": [{"opponent": "meowscarada", "rating": 818, "opRating": 181}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 800, "opRating": 199}, {"opponent": "honch<PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "lie<PERSON>", "rating": 747, "opRating": 252}, {"opponent": "dedenne", "rating": 703, "opRating": 296}], "counters": [{"opponent": "charmeleon", "rating": 74}, {"opponent": "darum<PERSON>", "rating": 81}, {"opponent": "ponyta", "rating": 84}, {"opponent": "houndoom", "rating": 88}, {"opponent": "entei", "rating": 88}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14680}, {"moveId": "BUG_BITE", "uses": 10320}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 10255}, {"moveId": "CROSS_POISON", "uses": 10062}, {"moveId": "SIGNAL_BEAM", "uses": 4690}]}, "moveset": ["POISON_STING", "NIGHT_SLASH", "CROSS_POISON"], "score": 30.7}, {"speciesId": "pikachu_horizons", "speciesName": "<PERSON><PERSON><PERSON> (Horizons)", "rating": 476, "matchups": [{"opponent": "zapdos", "rating": 847, "opRating": 152}, {"opponent": "talonflame", "rating": 595, "opRating": 404}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "armarouge", "rating": 533, "opRating": 466}, {"opponent": "pyroar", "rating": 528, "opRating": 471}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "charjabug", "rating": 75}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 97}, {"opponent": "arctibax", "rating": 99}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13881}, {"moveId": "QUICK_ATTACK", "uses": 11119}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15037}, {"moveId": "VOLT_TACKLE", "uses": 4000}, {"moveId": "DISCHARGE", "uses": 3243}, {"moveId": "THUNDERBOLT", "uses": 2749}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "VOLT_TACKLE"], "score": 30.4}, {"speciesId": "voltorb_his<PERSON>an", "speciesName": "Voltorb (<PERSON><PERSON>an)", "rating": 457, "matchups": [{"opponent": "xurkitree", "rating": 907, "opRating": 92}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 889, "opRating": 110}, {"opponent": "luxray", "rating": 694, "opRating": 305}, {"opponent": "eelektross", "rating": 676, "opRating": 323}, {"opponent": "magnezone", "rating": 623, "opRating": 376}], "counters": [{"opponent": "sandslash_alolan", "rating": 88}, {"opponent": "crocalor", "rating": 106}, {"opponent": "talonflame", "rating": 118}, {"opponent": "typhlosion", "rating": 132}, {"opponent": "stunfisk_galarian", "rating": 142}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 13517}, {"moveId": "CHARGE_BEAM", "uses": 11483}], "chargedMoves": [{"moveId": "SWIFT", "uses": 12254}, {"moveId": "THUNDERBOLT", "uses": 7651}, {"moveId": "ENERGY_BALL", "uses": 5115}]}, "moveset": ["TACKLE", "SWIFT", "THUNDERBOLT"], "score": 30.4}, {"speciesId": "bulbasaur", "speciesName": "Bulbasaur", "rating": 490, "matchups": [{"opponent": "electabuzz", "rating": 858, "opRating": 141}, {"opponent": "dedenne", "rating": 858, "opRating": 141}, {"opponent": "r<PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 766, "opRating": 233}, {"opponent": "nidoking", "rating": 750, "opRating": 250}], "counters": [{"opponent": "charizard", "rating": 89}, {"opponent": "pyroar", "rating": 112}, {"opponent": "arcanine", "rating": 114}, {"opponent": "typhlosion", "rating": 119}, {"opponent": "magmar", "rating": 125}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 13843}, {"moveId": "TACKLE", "uses": 11157}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7825}, {"moveId": "POWER_WHIP", "uses": 6877}, {"moveId": "SEED_BOMB", "uses": 5504}, {"moveId": "RETURN", "uses": 4827}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 30.3}, {"speciesId": "kartana", "speciesName": "Kartana", "rating": 463, "matchups": [{"opponent": "electivire", "rating": 839, "opRating": 160}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 820, "opRating": 179}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 814, "opRating": 185}, {"opponent": "rai<PERSON>u", "rating": 801, "opRating": 198}, {"opponent": "luxray", "rating": 750, "opRating": 250}], "counters": [{"opponent": "charizard", "rating": 85}, {"opponent": "heatran", "rating": 90}, {"opponent": "ninetales", "rating": 95}, {"opponent": "talonflame", "rating": 95}, {"opponent": "arcanine", "rating": 131}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 15353}, {"moveId": "RAZOR_LEAF", "uses": 9647}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 8783}, {"moveId": "NIGHT_SLASH", "uses": 7018}, {"moveId": "X_SCISSOR", "uses": 4738}, {"moveId": "AERIAL_ACE", "uses": 4467}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "NIGHT_SLASH"], "score": 30}, {"speciesId": "blitzle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 421, "matchups": [{"opponent": "rotom_frost", "rating": 812, "opRating": 187}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 804, "opRating": 195}, {"opponent": "bombirdier", "rating": 787, "opRating": 212}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 725, "opRating": 275}, {"opponent": "talonflame", "rating": 512, "opRating": 487}], "counters": [{"opponent": "amaura", "rating": 85}, {"opponent": "heatran", "rating": 95}, {"opponent": "tyrunt", "rating": 96}, {"opponent": "sandslash_alolan", "rating": 104}, {"opponent": "typhlosion", "rating": 158}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13709}, {"moveId": "SPARK", "uses": 11291}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 9241}, {"moveId": "DISCHARGE", "uses": 8035}, {"moveId": "RETURN", "uses": 4315}, {"moveId": "THUNDERBOLT", "uses": 3451}]}, "moveset": ["QUICK_ATTACK", "FLAME_CHARGE", "DISCHARGE"], "score": 29.9}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 596, "matchups": [{"opponent": "piloswine", "rating": 820, "opRating": 180}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 820, "opRating": 180}, {"opponent": "manectric", "rating": 804, "opRating": 196}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 756, "opRating": 244}, {"opponent": "electivire", "rating": 732, "opRating": 268}], "counters": [{"opponent": "litleo", "rating": 55}, {"opponent": "ninetales", "rating": 59}, {"opponent": "typhlosion", "rating": 68}, {"opponent": "rapidash", "rating": 72}, {"opponent": "magmar", "rating": 84}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 12600}, {"moveId": "METAL_CLAW", "uses": 12400}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 6390}, {"moveId": "MIRROR_SHOT", "uses": 5804}, {"moveId": "THUNDER", "uses": 4603}, {"moveId": "RETURN", "uses": 4021}, {"moveId": "FLASH_CANNON", "uses": 3008}, {"moveId": "ACID_SPRAY", "uses": 1173}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "THUNDER"], "score": 29.7}, {"speciesId": "zubat", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 335, "matchups": [{"opponent": "roselia", "rating": 592, "opRating": 407}, {"opponent": "gloom", "rating": 557, "opRating": 442}, {"opponent": "weepinbell", "rating": 553, "opRating": 446}, {"opponent": "roserade", "rating": 548, "opRating": 451}, {"opponent": "bulbasaur", "rating": 522, "opRating": 477}], "counters": [{"opponent": "steelix", "rating": 72}, {"opponent": "melmetal", "rating": 95}, {"opponent": "stunfisk_galarian", "rating": 103}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magnezone", "rating": 127}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 15948}, {"moveId": "BITE", "uses": 9052}], "chargedMoves": [{"moveId": "SWIFT", "uses": 9254}, {"moveId": "POISON_FANG", "uses": 6212}, {"moveId": "SLUDGE_BOMB", "uses": 5082}, {"moveId": "AIR_CUTTER", "uses": 2889}, {"moveId": "RETURN", "uses": 1613}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "POISON_FANG"], "score": 29}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 614, "matchups": [{"opponent": "victini", "rating": 845, "opRating": 155}, {"opponent": "avalugg_his<PERSON>an", "rating": 845, "opRating": 155}, {"opponent": "incineroar", "rating": 794, "opRating": 205}, {"opponent": "arctibax", "rating": 784, "opRating": 215}, {"opponent": "rotom_heat", "rating": 755, "opRating": 245}], "counters": [{"opponent": "talonflame", "rating": 72}, {"opponent": "ninetales", "rating": 75}, {"opponent": "rapidash", "rating": 85}, {"opponent": "arcanine", "rating": 86}, {"opponent": "litleo", "rating": 87}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 12797}, {"moveId": "BUG_BITE", "uses": 12203}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10873}, {"moveId": "STONE_EDGE", "uses": 9568}, {"moveId": "IRON_HEAD", "uses": 4596}]}, "moveset": ["METAL_CLAW", "X_SCISSOR", "STONE_EDGE"], "score": 28.7}, {"speciesId": "toxel", "speciesName": "Toxel", "rating": 288, "matchups": [{"opponent": "pawmi", "rating": 783, "opRating": 216}, {"opponent": "pichu", "rating": 725, "opRating": 274}, {"opponent": "zigzagoon_galarian", "rating": 676, "opRating": 323}, {"opponent": "blitzle", "rating": 654, "opRating": 345}, {"opponent": "tynamo", "rating": 654, "opRating": 345}], "counters": [{"opponent": "stunfisk_galarian", "rating": 79}, {"opponent": "steelix", "rating": 92}, {"opponent": "nidoqueen", "rating": 111}, {"opponent": "nidoking", "rating": 133}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 169}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 25000}], "chargedMoves": [{"moveId": "POWER_UP_PUNCH", "uses": 25000}]}, "moveset": ["ACID", "POWER_UP_PUNCH"], "score": 26.8}, {"speciesId": "meltan", "speciesName": "Meltan", "rating": 466, "matchups": [{"opponent": "over<PERSON><PERSON>l", "rating": 847, "opRating": 152}, {"opponent": "piloswine", "rating": 797, "opRating": 202}, {"opponent": "qwilfish_his<PERSON>an", "rating": 702, "opRating": 297}, {"opponent": "avalugg_his<PERSON>an", "rating": 698, "opRating": 301}, {"opponent": "arctibax", "rating": 685, "opRating": 314}], "counters": [{"opponent": "electrode_hisuian", "rating": 44}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "crocalor", "rating": 95}, {"opponent": "litleo", "rating": 97}, {"opponent": "typhlosion", "rating": 119}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 25000}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 14936}, {"moveId": "FLASH_CANNON", "uses": 10064}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "FLASH_CANNON"], "score": 26.5}, {"speciesId": "weepinbell", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 475, "matchups": [{"opponent": "dedenne", "rating": 877, "opRating": 122}, {"opponent": "nidoking", "rating": 838, "opRating": 161}, {"opponent": "rai<PERSON>u", "rating": 838, "opRating": 161}, {"opponent": "eelektross", "rating": 816, "opRating": 183}, {"opponent": "electrode_hisuian", "rating": 708, "opRating": 291}], "counters": [{"opponent": "charizard", "rating": 55}, {"opponent": "crocalor", "rating": 85}, {"opponent": "litleo", "rating": 87}, {"opponent": "typhlosion", "rating": 106}, {"opponent": "magmar", "rating": 111}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 11742}, {"moveId": "ACID", "uses": 7011}, {"moveId": "RAZOR_LEAF", "uses": 6248}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7836}, {"moveId": "POWER_WHIP", "uses": 6871}, {"moveId": "SEED_BOMB", "uses": 5511}, {"moveId": "RETURN", "uses": 4827}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "SLUDGE_BOMB"], "score": 25.9}, {"speciesId": "budew", "speciesName": "<PERSON><PERSON>", "rating": 306, "matchups": [{"opponent": "magnemite", "rating": 796, "opRating": 203}, {"opponent": "toxtricity", "rating": 606, "opRating": 393}, {"opponent": "magnezone", "rating": 535, "opRating": 464}, {"opponent": "eelektrik", "rating": 517, "opRating": 482}, {"opponent": "electrode", "rating": 508, "opRating": 491}], "counters": [{"opponent": "moltres_galarian", "rating": 77}, {"opponent": "em<PERSON>ga", "rating": 101}, {"opponent": "charizard", "rating": 106}, {"opponent": "raticate_alolan", "rating": 111}, {"opponent": "munchlax", "rating": 112}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GROUND", "uses": 2257}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1856}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1792}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1709}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1642}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1461}, {"moveId": "RAZOR_LEAF", "uses": 1389}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1379}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1372}, {"moveId": "HIDDEN_POWER_ICE", "uses": 1369}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1358}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1337}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1300}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1291}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1239}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1186}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1082}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 18500}, {"moveId": "ENERGY_BALL", "uses": 6500}]}, "moveset": ["HIDDEN_POWER_GROUND", "GRASS_KNOT", "ENERGY_BALL"], "score": 24.8}, {"speciesId": "bron<PERSON>", "speciesName": "Bronzor", "rating": 374, "matchups": [{"opponent": "archen", "rating": 711, "opRating": 288}, {"opponent": "poipole", "rating": 624, "opRating": 375}, {"opponent": "beedrill", "rating": 591, "opRating": 408}, {"opponent": "toxapex", "rating": 565, "opRating": 434}, {"opponent": "toxtricity", "rating": 558, "opRating": 441}], "counters": [{"opponent": "stunfisk_galarian", "rating": 65}, {"opponent": "incineroar", "rating": 71}, {"opponent": "umbreon", "rating": 74}, {"opponent": "ninetales", "rating": 103}, {"opponent": "armarouge", "rating": 109}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 13517}, {"moveId": "TACKLE", "uses": 11483}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 10900}, {"moveId": "PAYBACK", "uses": 6600}, {"moveId": "HEAVY_SLAM", "uses": 5707}, {"moveId": "GYRO_BALL", "uses": 1763}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 23.9}, {"speciesId": "ferroseed", "speciesName": "Ferroseed", "rating": 454, "matchups": [{"opponent": "arctibax", "rating": 768, "opRating": 231}, {"opponent": "luxray", "rating": 731, "opRating": 268}, {"opponent": "toxtricity", "rating": 726, "opRating": 273}, {"opponent": "tyrunt", "rating": 701, "opRating": 298}, {"opponent": "avalugg_his<PERSON>an", "rating": 630, "opRating": 369}], "counters": [{"opponent": "crocalor", "rating": 58}, {"opponent": "litleo", "rating": 59}, {"opponent": "typhlosion", "rating": 72}, {"opponent": "rapidash", "rating": 76}, {"opponent": "magmar", "rating": 84}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 12723}, {"moveId": "METAL_CLAW", "uses": 12277}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 9772}, {"moveId": "RETURN", "uses": 9039}, {"moveId": "FLASH_CANNON", "uses": 3107}, {"moveId": "GYRO_BALL", "uses": 3089}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "RETURN"], "score": 23.4}, {"speciesId": "gulpin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 284, "matchups": [{"opponent": "obstagoon", "rating": 621, "opRating": 378}, {"opponent": "bisharp", "rating": 554, "opRating": 445}, {"opponent": "weavile", "rating": 528, "opRating": 471}, {"opponent": "pikachu_horizons", "rating": 515, "opRating": 484}, {"opponent": "lie<PERSON>", "rating": 512, "opRating": 487}], "counters": [{"opponent": "slowbro_galarian", "rating": 72}, {"opponent": "talonflame", "rating": 95}, {"opponent": "nidoking", "rating": 98}, {"opponent": "nidoqueen", "rating": 100}, {"opponent": "armarouge", "rating": 109}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 18219}, {"moveId": "POUND", "uses": 6781}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 10363}, {"moveId": "ICE_BEAM", "uses": 9194}, {"moveId": "GUNK_SHOT", "uses": 5456}]}, "moveset": ["ROCK_SMASH", "SLUDGE", "ICE_BEAM"], "score": 23.3}, {"speciesId": "tynamo", "speciesName": "Tynamo", "rating": 246, "matchups": [{"opponent": "darkrai", "rating": 1000, "opRating": 0}, {"opponent": "volcarona", "rating": 990, "opRating": 9}, {"opponent": "pawmi", "rating": 795, "opRating": 204}, {"opponent": "weedle", "rating": 795, "opRating": 204}, {"opponent": "pichu", "rating": 771, "opRating": 228}], "counters": [{"opponent": "stunfisk_galarian", "rating": 76}, {"opponent": "sandslash_alolan", "rating": 88}, {"opponent": "steelix", "rating": 104}, {"opponent": "avalugg_his<PERSON>an", "rating": 107}, {"opponent": "litleo", "rating": 146}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 12797}, {"moveId": "SPARK", "uses": 12203}], "chargedMoves": [{"moveId": "STRUGGLE", "uses": 25000}]}, "moveset": ["TACKLE", "STRUGGLE"], "score": 23.3}, {"speciesId": "zigzagoon_galarian", "speciesName": "Zigzagoon (Galarian)", "rating": 276, "matchups": [{"opponent": "rattata_alolan", "rating": 768, "opRating": 231}, {"opponent": "budew", "rating": 645, "opRating": 354}, {"opponent": "rotom_heat", "rating": 640, "opRating": 359}, {"opponent": "meowth_alolan", "rating": 545, "opRating": 454}, {"opponent": "bron<PERSON>", "rating": 536, "opRating": 463}], "counters": [{"opponent": "amaura", "rating": 60}, {"opponent": "tyrunt", "rating": 81}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 82}, {"opponent": "magnezone", "rating": 107}, {"opponent": "stunfisk_galarian", "rating": 124}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 20175}, {"moveId": "TAKE_DOWN", "uses": 4825}], "chargedMoves": [{"moveId": "SWIFT", "uses": 11357}, {"moveId": "DIG", "uses": 6959}, {"moveId": "BODY_SLAM", "uses": 4746}, {"moveId": "RETURN", "uses": 1950}]}, "moveset": ["TACKLE", "SWIFT", "DIG"], "score": 23}, {"speciesId": "rattata_alolan", "speciesName": "Rattata (Alolan)", "rating": 322, "matchups": [{"opponent": "zoroark", "rating": 841, "opRating": 158}, {"opponent": "lie<PERSON>", "rating": 816, "opRating": 183}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 780, "opRating": 219}, {"opponent": "honch<PERSON><PERSON>", "rating": 780, "opRating": 219}, {"opponent": "absol", "rating": 780, "opRating": 219}], "counters": [{"opponent": "nidoking", "rating": 82}, {"opponent": "magmortar", "rating": 97}, {"opponent": "electrode_hisuian", "rating": 110}, {"opponent": "magmar", "rating": 111}, {"opponent": "ninetales", "rating": 123}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 14048}, {"moveId": "TACKLE", "uses": 10952}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 9968}, {"moveId": "HYPER_FANG", "uses": 7195}, {"moveId": "SHADOW_BALL", "uses": 5378}, {"moveId": "RETURN", "uses": 2465}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_FANG"], "score": 21.7}, {"speciesId": "bellsprout", "speciesName": "Bellsprout", "rating": 349, "matchups": [{"opponent": "electabuzz", "rating": 822, "opRating": 177}, {"opponent": "rai<PERSON>u", "rating": 822, "opRating": 177}, {"opponent": "dedenne", "rating": 822, "opRating": 177}, {"opponent": "rotom_heat", "rating": 791, "opRating": 208}, {"opponent": "nidoking", "rating": 598, "opRating": 401}], "counters": [{"opponent": "crocalor", "rating": 54}, {"opponent": "litleo", "rating": 55}, {"opponent": "typhlosion", "rating": 68}, {"opponent": "armarouge", "rating": 70}, {"opponent": "rapidash", "rating": 72}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 16519}, {"moveId": "ACID", "uses": 8481}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7484}, {"moveId": "POWER_WHIP", "uses": 7264}, {"moveId": "WRAP", "uses": 5723}, {"moveId": "RETURN", "uses": 4519}]}, "moveset": ["VINE_WHIP", "SLUDGE_BOMB", "POWER_WHIP"], "score": 19.4}, {"speciesId": "venipede", "speciesName": "Venipede", "rating": 292, "matchups": [{"opponent": "weavile", "rating": 780, "opRating": 219}, {"opponent": "shiftry", "rating": 658, "opRating": 341}, {"opponent": "venusaur", "rating": 642, "opRating": 357}, {"opponent": "zoroark", "rating": 637, "opRating": 362}, {"opponent": "obstagoon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 41}, {"opponent": "pyroar", "rating": 64}, {"opponent": "ninetales", "rating": 67}, {"opponent": "typhlosion", "rating": 68}, {"opponent": "rapidash", "rating": 72}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14127}, {"moveId": "BUG_BITE", "uses": 10873}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8796}, {"moveId": "SIGNAL_BEAM", "uses": 7425}, {"moveId": "RETURN", "uses": 5524}, {"moveId": "GYRO_BALL", "uses": 3270}]}, "moveset": ["POISON_STING", "SLUDGE_BOMB", "SIGNAL_BEAM"], "score": 17.7}, {"speciesId": "pawmi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 243, "matchups": [{"opponent": "golbat", "rating": 679, "opRating": 320}, {"opponent": "bombirdier", "rating": 675, "opRating": 325}, {"opponent": "celesteela", "rating": 675, "opRating": 325}, {"opponent": "lie<PERSON>", "rating": 675, "opRating": 325}, {"opponent": "honch<PERSON><PERSON>", "rating": 645, "opRating": 354}], "counters": [{"opponent": "camerupt", "rating": 45}, {"opponent": "stunfisk_galarian", "rating": 62}, {"opponent": "nidoqueen", "rating": 68}, {"opponent": "typhlosion", "rating": 89}, {"opponent": "rapidash", "rating": 94}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 14359}, {"moveId": "CHARGE_BEAM", "uses": 10641}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17940}, {"moveId": "DISCHARGE", "uses": 3792}, {"moveId": "THUNDERBOLT", "uses": 3251}]}, "moveset": ["SPARK", "WILD_CHARGE", "DISCHARGE"], "score": 17.4}, {"speciesId": "poochyena", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 266, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 785, "opRating": 214}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 695, "opRating": 304}, {"opponent": "bronzong", "rating": 695, "opRating": 304}, {"opponent": "rotom_heat", "rating": 628, "opRating": 371}, {"opponent": "charcadet", "rating": 547, "opRating": 452}], "counters": [{"opponent": "arctibax", "rating": 56}, {"opponent": "qwilfish_his<PERSON>an", "rating": 66}, {"opponent": "ninetales", "rating": 75}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 86}, {"opponent": "magmar", "rating": 93}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 14741}, {"moveId": "TACKLE", "uses": 10259}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 9403}, {"moveId": "DIG", "uses": 7448}, {"moveId": "POISON_FANG", "uses": 4977}, {"moveId": "RETURN", "uses": 3192}]}, "moveset": ["SNARL", "CRUNCH", "DIG"], "score": 17.2}, {"speciesId": "pichu", "speciesName": "<PERSON><PERSON>", "rating": 220, "matchups": [{"opponent": "zubat", "rating": 785, "opRating": 214}, {"opponent": "archeops", "rating": 702, "opRating": 297}, {"opponent": "honch<PERSON><PERSON>", "rating": 541, "opRating": 458}, {"opponent": "archen", "rating": 523, "opRating": 476}, {"opponent": "crobat", "rating": 505, "opRating": 494}], "counters": [{"opponent": "camerupt", "rating": 24}, {"opponent": "nidoqueen", "rating": 25}, {"opponent": "litleo", "rating": 55}, {"opponent": "ninetales", "rating": 59}, {"opponent": "typhlosion", "rating": 68}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13605}, {"moveId": "QUICK_ATTACK", "uses": 11395}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 13095}, {"moveId": "DISARMING_VOICE", "uses": 7820}, {"moveId": "THUNDERBOLT", "uses": 4066}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "DISARMING_VOICE"], "score": 15.6}, {"speciesId": "trubbish", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 192, "matchups": [{"opponent": "vileplume", "rating": 582, "opRating": 417}, {"opponent": "oddish", "rating": 582, "opRating": 417}, {"opponent": "blitzle", "rating": 547, "opRating": 452}, {"opponent": "gloom", "rating": 523, "opRating": 476}, {"opponent": "pikachu_horizons", "rating": 519, "opRating": 480}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 53}, {"opponent": "stunfisk_galarian", "rating": 56}, {"opponent": "crocalor", "rating": 85}, {"opponent": "litleo", "rating": 87}, {"opponent": "magmar", "rating": 98}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 14346}, {"moveId": "POUND", "uses": 10654}], "chargedMoves": [{"moveId": "GUNK_SHOT", "uses": 12566}, {"moveId": "SEED_BOMB", "uses": 12434}]}, "moveset": ["TAKE_DOWN", "GUNK_SHOT", "SEED_BOMB"], "score": 15.2}, {"speciesId": "beldum", "speciesName": "Beldum", "rating": 203, "matchups": [{"opponent": "nidoran_female", "rating": 685, "opRating": 314}, {"opponent": "nidoran_male", "rating": 681, "opRating": 318}, {"opponent": "ekans", "rating": 637, "opRating": 362}, {"opponent": "sneasel", "rating": 606, "opRating": 393}, {"opponent": "venonat", "rating": 597, "opRating": 402}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 53}, {"opponent": "stunfisk_galarian", "rating": 56}, {"opponent": "magmar", "rating": 84}, {"opponent": "ninetales", "rating": 87}, {"opponent": "typhlosion", "rating": 89}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 25000}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 13228}, {"moveId": "RETURN", "uses": 11772}]}, "moveset": ["TAKE_DOWN", "IRON_HEAD", "RETURN"], "score": 14.4}, {"speciesId": "kakuna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 139, "matchups": [{"opponent": "pichu", "rating": 579, "opRating": 420}, {"opponent": "tynamo", "rating": 575, "opRating": 425}, {"opponent": "zigzagoon_galarian", "rating": 558, "opRating": 441}, {"opponent": "weedle", "rating": 554, "opRating": 445}, {"opponent": "poochyena", "rating": 525, "opRating": 475}], "counters": [{"opponent": "stunfisk_galarian", "rating": 38}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 41}, {"opponent": "litleo", "rating": 45}, {"opponent": "qwilfish_his<PERSON>an", "rating": 45}, {"opponent": "typhlosion", "rating": 55}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13138}, {"moveId": "BUG_BITE", "uses": 11862}], "chargedMoves": [{"moveId": "RETURN", "uses": 24750}, {"moveId": "STRUGGLE", "uses": 250}]}, "moveset": ["POISON_STING", "RETURN", "STRUGGLE"], "score": 9.9}, {"speciesId": "weedle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 120, "matchups": [{"opponent": "darkrai", "rating": 991, "opRating": 8}, {"opponent": "volcarona", "rating": 982, "opRating": 17}, {"opponent": "toxel", "rating": 517, "opRating": 482}], "counters": [{"opponent": "stunfisk_galarian", "rating": 38}, {"opponent": "arcanine", "rating": 40}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 41}, {"opponent": "typhlosion", "rating": 42}, {"opponent": "rapidash", "rating": 45}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13413}, {"moveId": "BUG_BITE", "uses": 11587}], "chargedMoves": [{"moveId": "RETURN", "uses": 24750}, {"moveId": "STRUGGLE", "uses": 250}]}, "moveset": ["POISON_STING", "RETURN", "STRUGGLE"], "score": 9.7}, {"speciesId": "volcarona", "speciesName": "Volcarona", "rating": 17, "matchups": [{"opponent": "darkrai", "rating": 833, "opRating": 166}], "counters": [{"opponent": "stunfisk_galarian", "rating": 5}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 8}, {"opponent": "ninetales", "rating": 11}, {"opponent": "magmar", "rating": 17}, {"opponent": "typhlosion", "rating": 21}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 12807}, {"moveId": "FIRE_SPIN", "uses": 12193}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 10344}, {"moveId": "BUG_BUZZ", "uses": 7290}, {"moveId": "HURRICANE", "uses": 4963}, {"moveId": "SOLAR_BEAM", "uses": 2414}]}, "moveset": ["BUG_BITE", "OVERHEAT", "BUG_BUZZ"], "score": 1.3}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 4, "matchups": [], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 0}, {"opponent": "magmar", "rating": 0}, {"opponent": "stunfisk_galarian", "rating": 0}, {"opponent": "typhlosion", "rating": 4}, {"opponent": "rapidash", "rating": 4}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 13211}, {"moveId": "FEINT_ATTACK", "uses": 11789}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 8511}, {"moveId": "SHADOW_BALL", "uses": 6264}, {"moveId": "FOCUS_BLAST", "uses": 5376}, {"moveId": "SLUDGE_BOMB", "uses": 4861}]}, "moveset": ["SNARL", "DARK_PULSE", "SHADOW_BALL"], "score": 0.1}]