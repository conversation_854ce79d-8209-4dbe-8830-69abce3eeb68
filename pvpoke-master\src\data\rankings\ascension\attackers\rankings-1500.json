[{"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 475, "matchups": [{"opponent": "gardevoir", "rating": 777, "opRating": 222}, {"opponent": "<PERSON><PERSON>", "rating": 726, "opRating": 273}, {"opponent": "charjabug", "rating": 547, "opRating": 452}, {"opponent": "cresselia", "rating": 519, "opRating": 480}, {"opponent": "castform_sunny", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 151}, {"opponent": "armarouge", "rating": 158}, {"opponent": "slowbro_galarian", "rating": 208}, {"opponent": "<PERSON>ras", "rating": 307}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 5401}, {"moveId": "FIRE_SPIN", "uses": 4940}, {"moveId": "FEINT_ATTACK", "uses": 3556}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 3781}, {"moveId": "SCORCHING_SANDS", "uses": 2993}, {"moveId": "PSYSHOCK", "uses": 2429}, {"moveId": "OVERHEAT", "uses": 1523}, {"moveId": "RETURN", "uses": 1075}, {"moveId": "FLAMETHROWER", "uses": 826}, {"moveId": "SOLAR_BEAM", "uses": 568}, {"moveId": "FIRE_BLAST", "uses": 451}, {"moveId": "HEAT_WAVE", "uses": 254}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SCORCHING_SANDS"], "score": 100}, {"speciesId": "p<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 483, "matchups": [{"opponent": "fletchinder", "rating": 679, "opRating": 320}, {"opponent": "talonflame", "rating": 661, "opRating": 338}, {"opponent": "be<PERSON><PERSON><PERSON>", "rating": 595, "opRating": 404}, {"opponent": "lugia", "rating": 577, "opRating": 422}, {"opponent": "gardevoir", "rating": 570, "opRating": 429}], "counters": [{"opponent": "charjabug", "rating": 250}, {"opponent": "cresselia", "rating": 293}, {"opponent": "hypno", "rating": 340}, {"opponent": "ninetales", "rating": 361}, {"opponent": "castform_sunny", "rating": 380}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 8181}, {"moveId": "SPARK", "uses": 5719}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 8785}, {"moveId": "THUNDERBOLT", "uses": 2742}, {"moveId": "THUNDER", "uses": 2364}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 99.7}, {"speciesId": "castform_sunny", "speciesName": "Castform (Sunny)", "rating": 457, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 763, "opRating": 236}, {"opponent": "gardevoir", "rating": 753, "opRating": 246}, {"opponent": "cresselia", "rating": 575, "opRating": 424}, {"opponent": "charjabug", "rating": 565, "opRating": 434}, {"opponent": "hat<PERSON><PERSON>", "rating": 561, "opRating": 438}], "counters": [{"opponent": "armarouge", "rating": 175}, {"opponent": "<PERSON>ras", "rating": 203}, {"opponent": "slowbro_galarian", "rating": 208}, {"opponent": "ninetales", "rating": 238}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 309}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 8303}, {"moveId": "TACKLE", "uses": 5597}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 10913}, {"moveId": "SOLAR_BEAM", "uses": 1691}, {"moveId": "FIRE_BLAST", "uses": 1282}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 93.8}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 408, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 720, "opRating": 279}, {"opponent": "ninetales", "rating": 678, "opRating": 321}, {"opponent": "gardevoir", "rating": 634, "opRating": 365}, {"opponent": "crocalor", "rating": 555, "opRating": 444}, {"opponent": "talonflame", "rating": 508, "opRating": 491}], "counters": [{"opponent": "grumpig", "rating": 90}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 151}, {"opponent": "castform_sunny", "rating": 160}, {"opponent": "cresselia", "rating": 216}, {"opponent": "hypno", "rating": 255}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 4820}, {"moveId": "WATER_GUN", "uses": 3850}, {"moveId": "ICE_SHARD", "uses": 3228}, {"moveId": "FROST_BREATH", "uses": 2006}], "chargedMoves": [{"moveId": "SPARKLING_ARIA", "uses": 5219}, {"moveId": "SURF", "uses": 2289}, {"moveId": "ICE_BEAM", "uses": 2204}, {"moveId": "SKULL_BASH", "uses": 1433}, {"moveId": "DRAGON_PULSE", "uses": 1061}, {"moveId": "HYDRO_PUMP", "uses": 930}, {"moveId": "BLIZZARD", "uses": 799}]}, "moveset": ["PSYWAVE", "SPARKLING_ARIA", "ICE_BEAM"], "score": 87.1}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 417, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 635, "opRating": 364}, {"opponent": "<PERSON>ras", "rating": 623, "opRating": 376}, {"opponent": "medicham", "rating": 623, "opRating": 376}, {"opponent": "deoxys_defense", "rating": 549, "opRating": 450}, {"opponent": "uxie", "rating": 530, "opRating": 469}], "counters": [{"opponent": "incineroar", "rating": 49}, {"opponent": "talonflame", "rating": 100}, {"opponent": "castform_sunny", "rating": 208}, {"opponent": "ninetales", "rating": 337}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 383}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 7176}, {"moveId": "CONFUSION", "uses": 6724}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 4042}, {"moveId": "FUTURE_SIGHT", "uses": 3941}, {"moveId": "MOONBLAST", "uses": 3732}, {"moveId": "AURORA_BEAM", "uses": 2189}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 84.5}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 380, "matchups": [{"opponent": "wobbuffet", "rating": 666, "opRating": 333}, {"opponent": "castform_sunny", "rating": 637, "opRating": 362}, {"opponent": "ninetales", "rating": 629, "opRating": 370}, {"opponent": "cresselia", "rating": 592, "opRating": 407}, {"opponent": "hypno", "rating": 518, "opRating": 481}], "counters": [{"opponent": "mew", "rating": 124}, {"opponent": "incineroar", "rating": 129}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 140}, {"opponent": "uxie", "rating": 181}, {"opponent": "<PERSON>ras", "rating": 192}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 6360}, {"moveId": "FIRE_SPIN", "uses": 3855}, {"moveId": "STEEL_WING", "uses": 2003}, {"moveId": "PECK", "uses": 1697}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 5032}, {"moveId": "FLY", "uses": 4290}, {"moveId": "FLAME_CHARGE", "uses": 2983}, {"moveId": "HURRICANE", "uses": 954}, {"moveId": "FIRE_BLAST", "uses": 690}]}, "moveset": ["INCINERATE", "FLY", "BRAVE_BIRD"], "score": 84.1}, {"speciesId": "hypno", "speciesName": "Hypno", "rating": 409, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 663, "opRating": 336}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 659, "opRating": 340}, {"opponent": "medicham", "rating": 598, "opRating": 401}, {"opponent": "entei", "rating": 540, "opRating": 459}, {"opponent": "wobbuffet", "rating": 530, "opRating": 469}], "counters": [{"opponent": "incineroar", "rating": 76}, {"opponent": "grumpig", "rating": 162}, {"opponent": "castform_sunny", "rating": 383}, {"opponent": "ninetales", "rating": 400}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 440}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12262}, {"moveId": "ZEN_HEADBUTT", "uses": 1638}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 3116}, {"moveId": "SHADOW_BALL", "uses": 2459}, {"moveId": "THUNDER_PUNCH", "uses": 1955}, {"moveId": "ICE_PUNCH", "uses": 1804}, {"moveId": "FIRE_PUNCH", "uses": 1779}, {"moveId": "RETURN", "uses": 957}, {"moveId": "FOCUS_BLAST", "uses": 722}, {"moveId": "FUTURE_SIGHT", "uses": 549}, {"moveId": "PSYCHIC", "uses": 518}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "THUNDER_PUNCH"], "score": 83.8}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 397, "matchups": [{"opponent": "arcanine", "rating": 665, "opRating": 334}, {"opponent": "pignite", "rating": 653, "opRating": 346}, {"opponent": "medicham", "rating": 645, "opRating": 354}, {"opponent": "magmar", "rating": 596, "opRating": 403}, {"opponent": "deoxys_defense", "rating": 524, "opRating": 475}], "counters": [{"opponent": "incineroar", "rating": 61}, {"opponent": "chimecho", "rating": 111}, {"opponent": "hypno", "rating": 234}, {"opponent": "castform_sunny", "rating": 369}, {"opponent": "ninetales", "rating": 388}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 7765}, {"moveId": "EXTRASENSORY", "uses": 6135}], "chargedMoves": [{"moveId": "SWIFT", "uses": 7959}, {"moveId": "FUTURE_SIGHT", "uses": 3043}, {"moveId": "THUNDER", "uses": 2892}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 82.3}, {"speciesId": "grumpig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 381, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 696, "opRating": 303}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 666, "opRating": 333}, {"opponent": "slowpoke_galarian", "rating": 666, "opRating": 333}, {"opponent": "medicham", "rating": 636, "opRating": 363}, {"opponent": "rapidash", "rating": 545, "opRating": 454}], "counters": [{"opponent": "incineroar", "rating": 57}, {"opponent": "talonflame", "rating": 170}, {"opponent": "castform_sunny", "rating": 250}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 352}, {"opponent": "ninetales", "rating": 380}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 7268}, {"moveId": "EXTRASENSORY", "uses": 3671}, {"moveId": "CHARGE_BEAM", "uses": 2965}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 5212}, {"moveId": "DYNAMIC_PUNCH", "uses": 3801}, {"moveId": "PSYCHIC", "uses": 2237}, {"moveId": "RETURN", "uses": 2016}, {"moveId": "MIRROR_COAT", "uses": 682}]}, "moveset": ["PSYWAVE", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 77.9}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 358, "matchups": [{"opponent": "hypno", "rating": 744, "opRating": 255}, {"opponent": "<PERSON><PERSON>", "rating": 702, "opRating": 297}, {"opponent": "uxie", "rating": 625, "opRating": 374}, {"opponent": "cresselia", "rating": 595, "opRating": 404}, {"opponent": "castform_sunny", "rating": 557, "opRating": 442}], "counters": [{"opponent": "talonflame", "rating": 96}, {"opponent": "<PERSON>ras", "rating": 114}, {"opponent": "ninetales", "rating": 115}, {"opponent": "charjabug", "rating": 115}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 359}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 5714}, {"moveId": "DOUBLE_KICK", "uses": 4134}, {"moveId": "FIRE_FANG", "uses": 4043}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 3467}, {"moveId": "DARKEST_LARIAT", "uses": 3399}, {"moveId": "DARK_PULSE", "uses": 3085}, {"moveId": "BLAZE_KICK", "uses": 2568}, {"moveId": "FLAME_CHARGE", "uses": 974}, {"moveId": "FIRE_BLAST", "uses": 429}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 77.4}, {"speciesId": "wobbuffet", "speciesName": "<PERSON>ob<PERSON><PERSON><PERSON>", "rating": 376, "matchups": [{"opponent": "entei", "rating": 555, "opRating": 444}, {"opponent": "magmar", "rating": 540, "opRating": 459}, {"opponent": "uxie", "rating": 537, "opRating": 462}, {"opponent": "medicham", "rating": 527, "opRating": 472}, {"opponent": "raboot", "rating": 501, "opRating": 498}], "counters": [{"opponent": "talonflame", "rating": 137}, {"opponent": "charjabug", "rating": 138}, {"opponent": "grumpig", "rating": 155}, {"opponent": "ninetales", "rating": 341}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 408}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 7463}, {"moveId": "CHARM", "uses": 5972}, {"moveId": "SPLASH", "uses": 467}], "chargedMoves": [{"moveId": "RETURN", "uses": 8621}, {"moveId": "MIRROR_COAT", "uses": 5279}]}, "moveset": ["COUNTER", "RETURN", "MIRROR_COAT"], "score": 77.2}, {"speciesId": "charjabug", "speciesName": "Charjabug", "rating": 374, "matchups": [{"opponent": "wobbuffet", "rating": 587, "opRating": 412}, {"opponent": "deoxys_defense", "rating": 547, "opRating": 452}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 543, "opRating": 456}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 523, "opRating": 476}, {"opponent": "be<PERSON><PERSON><PERSON>", "rating": 507, "opRating": 492}], "counters": [{"opponent": "incineroar", "rating": 114}, {"opponent": "crocalor", "rating": 114}, {"opponent": "castform_sunny", "rating": 126}, {"opponent": "ninetales", "rating": 130}, {"opponent": "armarouge", "rating": 132}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 5339}, {"moveId": "BUG_BITE", "uses": 5034}, {"moveId": "SPARK", "uses": 3529}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 5588}, {"moveId": "DISCHARGE", "uses": 4175}, {"moveId": "CRUNCH", "uses": 4121}]}, "moveset": ["VOLT_SWITCH", "X_SCISSOR", "DISCHARGE"], "score": 76.3}, {"speciesId": "mew", "speciesName": "Mew", "rating": 365, "matchups": [{"opponent": "gardevoir", "rating": 620, "opRating": 379}, {"opponent": "medicham", "rating": 608, "opRating": 391}, {"opponent": "wobbuffet", "rating": 585, "opRating": 414}, {"opponent": "lugia", "rating": 573, "opRating": 426}, {"opponent": "uxie", "rating": 569, "opRating": 430}], "counters": [{"opponent": "incineroar", "rating": 83}, {"opponent": "talonflame", "rating": 114}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 267}, {"opponent": "castform_sunny", "rating": 328}, {"opponent": "ninetales", "rating": 357}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 2200}, {"moveId": "VOLT_SWITCH", "uses": 1729}, {"moveId": "SNARL", "uses": 1559}, {"moveId": "POISON_JAB", "uses": 1550}, {"moveId": "INFESTATION", "uses": 1255}, {"moveId": "DRAGON_TAIL", "uses": 1242}, {"moveId": "WATERFALL", "uses": 1108}, {"moveId": "CHARGE_BEAM", "uses": 783}, {"moveId": "STEEL_WING", "uses": 751}, {"moveId": "STRUGGLE_BUG", "uses": 591}, {"moveId": "FROST_BREATH", "uses": 549}, {"moveId": "CUT", "uses": 282}, {"moveId": "ROCK_SMASH", "uses": 245}, {"moveId": "POUND", "uses": 35}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 1380}, {"moveId": "PSYSHOCK", "uses": 1354}, {"moveId": "SURF", "uses": 1172}, {"moveId": "STONE_EDGE", "uses": 1091}, {"moveId": "ROCK_SLIDE", "uses": 1036}, {"moveId": "DRAGON_CLAW", "uses": 963}, {"moveId": "DARK_PULSE", "uses": 941}, {"moveId": "BULLDOZE", "uses": 634}, {"moveId": "FLAME_CHARGE", "uses": 606}, {"moveId": "GRASS_KNOT", "uses": 546}, {"moveId": "ICE_BEAM", "uses": 487}, {"moveId": "ANCIENT_POWER", "uses": 480}, {"moveId": "DAZZLING_GLEAM", "uses": 438}, {"moveId": "HYPER_BEAM", "uses": 336}, {"moveId": "FOCUS_BLAST", "uses": 276}, {"moveId": "THUNDERBOLT", "uses": 262}, {"moveId": "LOW_SWEEP", "uses": 247}, {"moveId": "OVERHEAT", "uses": 246}, {"moveId": "THUNDER", "uses": 242}, {"moveId": "PSYCHIC", "uses": 239}, {"moveId": "GYRO_BALL", "uses": 216}, {"moveId": "ENERGY_BALL", "uses": 201}, {"moveId": "FLASH_CANNON", "uses": 201}, {"moveId": "BLIZZARD", "uses": 139}, {"moveId": "SOLAR_BEAM", "uses": 76}]}, "moveset": ["SHADOW_CLAW", "WILD_CHARGE", "PSYSHOCK"], "score": 76.1}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 366, "matchups": [{"opponent": "hat<PERSON><PERSON>", "rating": 794, "opRating": 205}, {"opponent": "gardevoir", "rating": 760, "opRating": 239}, {"opponent": "cresselia", "rating": 626, "opRating": 373}, {"opponent": "deoxys_defense", "rating": 595, "opRating": 404}, {"opponent": "wobbuffet", "rating": 589, "opRating": 410}], "counters": [{"opponent": "grumpig", "rating": 136}, {"opponent": "hypno", "rating": 156}, {"opponent": "ninetales", "rating": 202}, {"opponent": "castform_sunny", "rating": 208}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 295}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 8368}, {"moveId": "CONFUSION", "uses": 5532}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 4766}, {"moveId": "SURF", "uses": 3076}, {"moveId": "SLUDGE_BOMB", "uses": 2493}, {"moveId": "SCALD", "uses": 1505}, {"moveId": "PSYCHIC", "uses": 1273}, {"moveId": "FOCUS_BLAST", "uses": 802}]}, "moveset": ["POISON_JAB", "SCALD", "BRUTAL_SWING"], "score": 75.4}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 370, "matchups": [{"opponent": "gardevoir", "rating": 700, "opRating": 299}, {"opponent": "medicham", "rating": 619, "opRating": 380}, {"opponent": "wobbuffet", "rating": 611, "opRating": 388}, {"opponent": "deoxys_defense", "rating": 576, "opRating": 423}, {"opponent": "hat<PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "<PERSON>ras", "rating": 103}, {"opponent": "ninetales", "rating": 123}, {"opponent": "grumpig", "rating": 125}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 211}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 8207}, {"moveId": "EMBER", "uses": 5693}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 7038}, {"moveId": "FLAME_CHARGE", "uses": 4417}, {"moveId": "FLAMETHROWER", "uses": 1836}, {"moveId": "HEAT_WAVE", "uses": 589}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 75.2}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 364, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 679, "opRating": 320}, {"opponent": "gardevoir", "rating": 621, "opRating": 378}, {"opponent": "celebi", "rating": 604, "opRating": 395}, {"opponent": "centiskorch", "rating": 591, "opRating": 408}, {"opponent": "me<PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "talonflame", "rating": 122}, {"opponent": "uxie", "rating": 181}, {"opponent": "<PERSON>ras", "rating": 192}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "ninetales", "rating": 337}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 10030}, {"moveId": "BITE", "uses": 3870}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 6572}, {"moveId": "FLAMETHROWER", "uses": 3781}, {"moveId": "DISARMING_VOICE", "uses": 3586}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 75}, {"speciesId": "chimecho", "speciesName": "Chi<PERSON><PERSON>", "rating": 358, "matchups": [{"opponent": "slowpoke_galarian", "rating": 673, "opRating": 326}, {"opponent": "wobbuffet", "rating": 665, "opRating": 334}, {"opponent": "uxie", "rating": 638, "opRating": 361}, {"opponent": "deoxys_defense", "rating": 638, "opRating": 361}, {"opponent": "medicham", "rating": 615, "opRating": 384}], "counters": [{"opponent": "talonflame", "rating": 137}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}, {"opponent": "<PERSON>ras", "rating": 203}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 320}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 8952}, {"moveId": "EXTRASENSORY", "uses": 4948}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 6642}, {"moveId": "SHADOW_BALL", "uses": 5345}, {"moveId": "ENERGY_BALL", "uses": 1926}]}, "moveset": ["ASTONISH", "PSYSHOCK", "SHADOW_BALL"], "score": 73}, {"speciesId": "eelektrik", "speciesName": "Eelektrik", "rating": 356, "matchups": [{"opponent": "oricorio_baile", "rating": 727, "opRating": 272}, {"opponent": "gardevoir", "rating": 613, "opRating": 386}, {"opponent": "electrode_hisuian", "rating": 566, "opRating": 433}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 562, "opRating": 437}, {"opponent": "be<PERSON><PERSON><PERSON>", "rating": 507, "opRating": 492}], "counters": [{"opponent": "slowbro_galarian", "rating": 106}, {"opponent": "uxie", "rating": 137}, {"opponent": "ninetales", "rating": 146}, {"opponent": "grumpig", "rating": 170}, {"opponent": "castform_sunny", "rating": 291}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 8165}, {"moveId": "SPARK", "uses": 5735}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 6491}, {"moveId": "DISCHARGE", "uses": 5402}, {"moveId": "THUNDERBOLT", "uses": 1989}]}, "moveset": ["ACID", "DISCHARGE", "CRUNCH"], "score": 73}, {"speciesId": "lugia", "speciesName": "Lugia", "rating": 357, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 647, "opRating": 352}, {"opponent": "wobbuffet", "rating": 639, "opRating": 360}, {"opponent": "vikavolt", "rating": 554, "opRating": 445}, {"opponent": "uxie", "rating": 550, "opRating": 449}, {"opponent": "entei", "rating": 538, "opRating": 461}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 151}, {"opponent": "incineroar", "rating": 152}, {"opponent": "<PERSON>ras", "rating": 181}, {"opponent": "charjabug", "rating": 198}, {"opponent": "hypno", "rating": 241}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 7815}, {"moveId": "EXTRASENSORY", "uses": 6085}], "chargedMoves": [{"moveId": "FLY", "uses": 5899}, {"moveId": "FUTURE_SIGHT", "uses": 2385}, {"moveId": "HYDRO_PUMP", "uses": 2198}, {"moveId": "AEROBLAST", "uses": 1941}, {"moveId": "SKY_ATTACK", "uses": 1521}]}, "moveset": ["DRAGON_TAIL", "FLY", "AEROBLAST"], "score": 72.8}, {"speciesId": "fletchinder", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 340, "matchups": [{"opponent": "entei", "rating": 629, "opRating": 370}, {"opponent": "medicham", "rating": 569, "opRating": 430}, {"opponent": "gardevoir", "rating": 538, "opRating": 461}, {"opponent": "delphox", "rating": 534, "opRating": 465}, {"opponent": "cresselia", "rating": 524, "opRating": 475}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 126}, {"opponent": "mew", "rating": 139}, {"opponent": "<PERSON>ras", "rating": 170}, {"opponent": "hypno", "rating": 207}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 7215}, {"moveId": "STEEL_WING", "uses": 3487}, {"moveId": "PECK", "uses": 3196}], "chargedMoves": [{"moveId": "FLY", "uses": 5488}, {"moveId": "AERIAL_ACE", "uses": 4187}, {"moveId": "FLAME_CHARGE", "uses": 3737}, {"moveId": "HEAT_WAVE", "uses": 526}]}, "moveset": ["EMBER", "FLY", "AERIAL_ACE"], "score": 72.6}, {"speciesId": "<PERSON><PERSON>", "speciesName": "Boltund", "rating": 360, "matchups": [{"opponent": "braviary_hisuian", "rating": 833, "opRating": 166}, {"opponent": "xatu", "rating": 682, "opRating": 317}, {"opponent": "oricorio_pau", "rating": 658, "opRating": 341}, {"opponent": "sigilyph", "rating": 642, "opRating": 357}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 579, "opRating": 420}], "counters": [{"opponent": "<PERSON>ras", "rating": 189}, {"opponent": "ninetales", "rating": 202}, {"opponent": "castform_sunny", "rating": 226}, {"opponent": "hypno", "rating": 285}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 309}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 8142}, {"moveId": "BITE", "uses": 5758}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 8765}, {"moveId": "THUNDER", "uses": 5135}]}, "moveset": ["BITE", "CRUNCH", "THUNDER"], "score": 72.4}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 336, "matchups": [{"opponent": "gardevoir", "rating": 700, "opRating": 299}, {"opponent": "charjabug", "rating": 632, "opRating": 367}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 602, "opRating": 397}, {"opponent": "cresselia", "rating": 581, "opRating": 418}, {"opponent": "darum<PERSON>", "rating": 568, "opRating": 431}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "<PERSON>ras", "rating": 108}, {"opponent": "ninetales", "rating": 123}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 274}, {"opponent": "castform_sunny", "rating": 339}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 5443}, {"moveId": "SHADOW_CLAW", "uses": 4828}, {"moveId": "EMBER", "uses": 3639}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 6461}, {"moveId": "THUNDER_PUNCH", "uses": 4234}, {"moveId": "OVERHEAT", "uses": 1349}, {"moveId": "SOLAR_BEAM", "uses": 1060}, {"moveId": "FIRE_BLAST", "uses": 787}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 71.7}, {"speciesId": "medicham", "speciesName": "Medicham", "rating": 337, "matchups": [{"opponent": "exeggcute", "rating": 617, "opRating": 382}, {"opponent": "flaaffy", "rating": 546, "opRating": 453}, {"opponent": "houndour", "rating": 535, "opRating": 464}, {"opponent": "tauros_blaze", "rating": 531, "opRating": 468}, {"opponent": "pignite", "rating": 517, "opRating": 482}], "counters": [{"opponent": "talonflame", "rating": 70}, {"opponent": "cresselia", "rating": 120}, {"opponent": "hypno", "rating": 193}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 264}, {"opponent": "ninetales", "rating": 321}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 7638}, {"moveId": "COUNTER", "uses": 6262}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 5715}, {"moveId": "ICE_PUNCH", "uses": 4533}, {"moveId": "PSYCHIC", "uses": 2389}, {"moveId": "POWER_UP_PUNCH", "uses": 1279}]}, "moveset": ["PSYCHO_CUT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 70.4}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 334, "matchups": [{"opponent": "monferno", "rating": 589, "opRating": 410}, {"opponent": "castform_sunny", "rating": 566, "opRating": 433}, {"opponent": "fuecoco", "rating": 566, "opRating": 433}, {"opponent": "hat<PERSON><PERSON>", "rating": 523, "opRating": 476}, {"opponent": "crocalor", "rating": 507, "opRating": 492}], "counters": [{"opponent": "ninetales", "rating": 182}, {"opponent": "talonflame", "rating": 196}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 200}, {"opponent": "<PERSON>ras", "rating": 203}, {"opponent": "hypno", "rating": 275}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 7297}, {"moveId": "FIRE_SPIN", "uses": 6603}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 5967}, {"moveId": "OVERHEAT", "uses": 5885}, {"moveId": "SOLAR_BEAM", "uses": 2049}]}, "moveset": ["EMBER", "OVERHEAT", "EARTHQUAKE"], "score": 69.7}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 330, "matchups": [{"opponent": "oricorio_baile", "rating": 709, "opRating": 290}, {"opponent": "gardevoir", "rating": 593, "opRating": 406}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 581, "opRating": 418}, {"opponent": "rapidash_galarian", "rating": 542, "opRating": 457}, {"opponent": "victini", "rating": 523, "opRating": 476}], "counters": [{"opponent": "ninetales", "rating": 111}, {"opponent": "crocalor", "rating": 125}, {"opponent": "grumpig", "rating": 193}, {"opponent": "cresselia", "rating": 268}, {"opponent": "castform_sunny", "rating": 304}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 9500}, {"moveId": "CHARGE_BEAM", "uses": 4400}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 4022}, {"moveId": "POWER_GEM", "uses": 2813}, {"moveId": "THUNDER_PUNCH", "uses": 2555}, {"moveId": "TRAILBLAZE", "uses": 1616}, {"moveId": "DRAGON_PULSE", "uses": 851}, {"moveId": "FOCUS_BLAST", "uses": 756}, {"moveId": "THUNDER", "uses": 719}, {"moveId": "ZAP_CANNON", "uses": 587}]}, "moveset": ["VOLT_SWITCH", "BRUTAL_SWING", "TRAILBLAZE"], "score": 69.3}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 334, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 656, "opRating": 343}, {"opponent": "hypno", "rating": 644, "opRating": 355}, {"opponent": "chimecho", "rating": 559, "opRating": 440}, {"opponent": "uxie", "rating": 525, "opRating": 474}, {"opponent": "<PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "<PERSON>ras", "rating": 134}, {"opponent": "ninetales", "rating": 142}, {"opponent": "grumpig", "rating": 159}, {"opponent": "castform_sunny", "rating": 191}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7741}, {"moveId": "FIRE_FANG", "uses": 6159}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 6982}, {"moveId": "CRUNCH", "uses": 3252}, {"moveId": "FLAMETHROWER", "uses": 2888}, {"moveId": "FIRE_BLAST", "uses": 777}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 68.8}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 335, "matchups": [{"opponent": "mr_rime", "rating": 686, "opRating": 313}, {"opponent": "uxie", "rating": 618, "opRating": 381}, {"opponent": "deoxys_defense", "rating": 609, "opRating": 390}, {"opponent": "medicham", "rating": 527, "opRating": 472}, {"opponent": "wobbuffet", "rating": 513, "opRating": 486}], "counters": [{"opponent": "<PERSON>ras", "rating": 111}, {"opponent": "ninetales", "rating": 123}, {"opponent": "castform_sunny", "rating": 167}, {"opponent": "grumpig", "rating": 170}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 228}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 9003}, {"moveId": "SCRATCH", "uses": 3474}, {"moveId": "ZEN_HEADBUTT", "uses": 1403}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 5037}, {"moveId": "MYSTICAL_FIRE", "uses": 3618}, {"moveId": "PSYCHIC", "uses": 2057}, {"moveId": "FLAME_CHARGE", "uses": 1380}, {"moveId": "FLAMETHROWER", "uses": 1153}, {"moveId": "FIRE_BLAST", "uses": 644}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 68.6}, {"speciesId": "hat<PERSON><PERSON>", "speciesName": "Hat<PERSON><PERSON>", "rating": 344, "matchups": [{"opponent": "espeon", "rating": 713, "opRating": 286}, {"opponent": "medicham", "rating": 703, "opRating": 296}, {"opponent": "<PERSON><PERSON>", "rating": 562, "opRating": 437}, {"opponent": "tauros_blaze", "rating": 562, "opRating": 437}, {"opponent": "flaaffy", "rating": 552, "opRating": 447}], "counters": [{"opponent": "slowbro_galarian", "rating": 136}, {"opponent": "ninetales", "rating": 158}, {"opponent": "castform_sunny", "rating": 184}, {"opponent": "<PERSON>ras", "rating": 209}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 271}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 5446}, {"moveId": "CONFUSION", "uses": 4839}, {"moveId": "CHARM", "uses": 3620}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 6534}, {"moveId": "DAZZLING_GLEAM", "uses": 3431}, {"moveId": "POWER_WHIP", "uses": 2830}, {"moveId": "PSYCHIC", "uses": 1110}]}, "moveset": ["CHARM", "PSYSHOCK", "POWER_WHIP"], "score": 68.4}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 322, "matchups": [{"opponent": "celebi", "rating": 679, "opRating": 320}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 624, "opRating": 376}, {"opponent": "castform_sunny", "rating": 552, "opRating": 448}, {"opponent": "gardevoir", "rating": 548, "opRating": 452}, {"opponent": "centiskorch", "rating": 528, "opRating": 472}], "counters": [{"opponent": "ninetales", "rating": 130}, {"opponent": "talonflame", "rating": 133}, {"opponent": "<PERSON>ras", "rating": 136}, {"opponent": "hypno", "rating": 187}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 295}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 4853}, {"moveId": "FIRE_FANG", "uses": 4004}, {"moveId": "THUNDER_FANG", "uses": 3385}, {"moveId": "BITE", "uses": 1666}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 3272}, {"moveId": "CRUNCH", "uses": 2694}, {"moveId": "SCORCHING_SANDS", "uses": 2613}, {"moveId": "PSYCHIC_FANGS", "uses": 1887}, {"moveId": "FLAMETHROWER", "uses": 1620}, {"moveId": "BULLDOZE", "uses": 1384}, {"moveId": "FIRE_BLAST", "uses": 405}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 68.2}, {"speciesId": "gardevoir", "speciesName": "Gardevoir", "rating": 342, "matchups": [{"opponent": "espeon", "rating": 759, "opRating": 240}, {"opponent": "tauros_blaze", "rating": 596, "opRating": 403}, {"opponent": "<PERSON><PERSON>", "rating": 557, "opRating": 442}, {"opponent": "medicham", "rating": 548, "opRating": 451}, {"opponent": "exeggcute", "rating": 548, "opRating": 451}], "counters": [{"opponent": "talonflame", "rating": 166}, {"opponent": "ninetales", "rating": 190}, {"opponent": "castform_sunny", "rating": 215}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 271}, {"opponent": "<PERSON>ras", "rating": 273}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 4618}, {"moveId": "MAGICAL_LEAF", "uses": 3484}, {"moveId": "CHARM", "uses": 2948}, {"moveId": "CHARGE_BEAM", "uses": 2851}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 4248}, {"moveId": "SYNCHRONOISE", "uses": 3094}, {"moveId": "TRIPLE_AXEL", "uses": 2987}, {"moveId": "DAZZLING_GLEAM", "uses": 2549}, {"moveId": "PSYCHIC", "uses": 970}]}, "moveset": ["CHARM", "TRIPLE_AXEL", "SHADOW_BALL"], "score": 68.2}, {"speciesId": "me<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON> (Male)", "rating": 330, "matchups": [{"opponent": "slowpoke_galarian", "rating": 632, "opRating": 367}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 606, "opRating": 393}, {"opponent": "uxie", "rating": 587, "opRating": 412}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 571, "opRating": 428}, {"opponent": "deoxys_defense", "rating": 541, "opRating": 458}], "counters": [{"opponent": "incineroar", "rating": 122}, {"opponent": "talonflame", "rating": 177}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 200}, {"opponent": "ninetales", "rating": 202}, {"opponent": "castform_sunny", "rating": 250}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 8364}, {"moveId": "CONFUSION", "uses": 5536}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 5240}, {"moveId": "PSYCHIC", "uses": 4685}, {"moveId": "ENERGY_BALL", "uses": 4003}]}, "moveset": ["SUCKER_PUNCH", "THUNDERBOLT", "PSYCHIC"], "score": 67.9}, {"speciesId": "toxtricity", "speciesName": "Toxtricity", "rating": 326, "matchups": [{"opponent": "hat<PERSON><PERSON>", "rating": 720, "opRating": 279}, {"opponent": "gardevoir", "rating": 673, "opRating": 326}, {"opponent": "<PERSON><PERSON>", "rating": 627, "opRating": 372}, {"opponent": "ampha<PERSON>", "rating": 627, "opRating": 372}, {"opponent": "eelektrik", "rating": 580, "opRating": 419}], "counters": [{"opponent": "<PERSON>ras", "rating": 103}, {"opponent": "uxie", "rating": 141}, {"opponent": "ninetales", "rating": 150}, {"opponent": "castform_sunny", "rating": 263}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 278}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 5906}, {"moveId": "POISON_JAB", "uses": 4835}, {"moveId": "SPARK", "uses": 3151}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 7996}, {"moveId": "DISCHARGE", "uses": 3907}, {"moveId": "ACID_SPRAY", "uses": 1057}, {"moveId": "POWER_UP_PUNCH", "uses": 941}]}, "moveset": ["ACID", "WILD_CHARGE", "POWER_UP_PUNCH"], "score": 67.5}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 343, "matchups": [{"opponent": "hat<PERSON><PERSON>", "rating": 660, "opRating": 339}, {"opponent": "gardevoir", "rating": 660, "opRating": 339}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 626, "opRating": 373}, {"opponent": "lugia", "rating": 600, "opRating": 400}, {"opponent": "ampha<PERSON>", "rating": 582, "opRating": 417}], "counters": [{"opponent": "ninetales", "rating": 87}, {"opponent": "castform_sunny", "rating": 99}, {"opponent": "armarouge", "rating": 106}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "hypno", "rating": 221}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 9196}, {"moveId": "TACKLE", "uses": 4704}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 7083}, {"moveId": "SWIFT", "uses": 4712}, {"moveId": "ENERGY_BALL", "uses": 2138}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 67.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 329, "matchups": [{"opponent": "cresselia", "rating": 647, "opRating": 352}, {"opponent": "wobbuffet", "rating": 639, "opRating": 360}, {"opponent": "deoxys_defense", "rating": 639, "opRating": 360}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 573, "opRating": 426}, {"opponent": "uxie", "rating": 553, "opRating": 446}], "counters": [{"opponent": "castform_sunny", "rating": 71}, {"opponent": "ninetales", "rating": 83}, {"opponent": "crocalor", "rating": 104}, {"opponent": "talonflame", "rating": 114}, {"opponent": "<PERSON>ras", "rating": 128}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 7487}, {"moveId": "VOLT_SWITCH", "uses": 6413}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 3377}, {"moveId": "LUNGE", "uses": 3280}, {"moveId": "DISCHARGE", "uses": 2919}, {"moveId": "BUG_BUZZ", "uses": 2115}, {"moveId": "RETURN", "uses": 1208}, {"moveId": "ENERGY_BALL", "uses": 988}]}, "moveset": ["FURY_CUTTER", "DISCHARGE", "LUNGE"], "score": 67.1}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 324, "matchups": [{"opponent": "gardevoir", "rating": 755, "opRating": 244}, {"opponent": "mr_rime", "rating": 678, "opRating": 321}, {"opponent": "charjabug", "rating": 608, "opRating": 391}, {"opponent": "<PERSON><PERSON>", "rating": 558, "opRating": 441}, {"opponent": "cresselia", "rating": 550, "opRating": 449}], "counters": [{"opponent": "slowbro_galarian", "rating": 109}, {"opponent": "<PERSON>ras", "rating": 114}, {"opponent": "hypno", "rating": 115}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 130}, {"opponent": "ninetales", "rating": 289}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 7988}, {"moveId": "CONFUSION", "uses": 5912}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 9497}, {"moveId": "PSYCHIC", "uses": 2029}, {"moveId": "FOCUS_BLAST", "uses": 1274}, {"moveId": "OVERHEAT", "uses": 1099}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 67.1}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 312, "matchups": [{"opponent": "toxtricity", "rating": 788, "opRating": 211}, {"opponent": "flaaffy", "rating": 702, "opRating": 297}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "<PERSON><PERSON>", "rating": 621, "opRating": 378}, {"opponent": "deoxys_defense", "rating": 522, "opRating": 477}], "counters": [{"opponent": "lugia", "rating": 50}, {"opponent": "<PERSON>ras", "rating": 114}, {"opponent": "castform_sunny", "rating": 154}, {"opponent": "ninetales", "rating": 166}, {"opponent": "hypno", "rating": 166}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 3998}, {"moveId": "MUD_SLAP", "uses": 3957}, {"moveId": "BUG_BITE", "uses": 3465}, {"moveId": "SPARK", "uses": 2484}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 4603}, {"moveId": "CRUNCH", "uses": 3297}, {"moveId": "DISCHARGE", "uses": 3101}, {"moveId": "FLY", "uses": 2886}]}, "moveset": ["MUD_SLAP", "X_SCISSOR", "FLY"], "score": 66.2}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 307, "matchups": [{"opponent": "celebi", "rating": 786, "opRating": 213}, {"opponent": "<PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "quilava", "rating": 598, "opRating": 401}, {"opponent": "darum<PERSON>", "rating": 568, "opRating": 431}, {"opponent": "cresselia", "rating": 512, "opRating": 487}], "counters": [{"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 144}, {"opponent": "mew", "rating": 162}, {"opponent": "hypno", "rating": 187}, {"opponent": "ninetales", "rating": 353}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 3444}, {"moveId": "FIRE_SPIN", "uses": 3090}, {"moveId": "DRAGON_BREATH", "uses": 2742}, {"moveId": "WING_ATTACK", "uses": 2525}, {"moveId": "AIR_SLASH", "uses": 2062}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 5920}, {"moveId": "DRAGON_CLAW", "uses": 4629}, {"moveId": "FLAMETHROWER", "uses": 1363}, {"moveId": "OVERHEAT", "uses": 1287}, {"moveId": "FIRE_BLAST", "uses": 757}]}, "moveset": ["EMBER", "BLAST_BURN", "DRAGON_CLAW"], "score": 65.3}, {"speciesId": "deoxys_defense", "speciesName": "<PERSON><PERSON><PERSON> (Defense)", "rating": 316, "matchups": [{"opponent": "mr_mime_galarian", "rating": 663, "opRating": 336}, {"opponent": "combusken", "rating": 581, "opRating": 418}, {"opponent": "tauros_blaze", "rating": 540, "opRating": 459}, {"opponent": "entei", "rating": 510, "opRating": 489}, {"opponent": "electrode", "rating": 505, "opRating": 494}], "counters": [{"opponent": "slowbro_galarian", "rating": 92}, {"opponent": "charjabug", "rating": 123}, {"opponent": "grumpig", "rating": 151}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 397}, {"opponent": "ninetales", "rating": 404}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 11255}, {"moveId": "ZEN_HEADBUTT", "uses": 2645}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 5618}, {"moveId": "PSYCHO_BOOST", "uses": 5424}, {"moveId": "THUNDERBOLT", "uses": 2861}]}, "moveset": ["COUNTER", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 64.9}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 312, "matchups": [{"opponent": "vikavolt", "rating": 660, "opRating": 339}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 600, "opRating": 400}, {"opponent": "celebi", "rating": 571, "opRating": 428}, {"opponent": "mr_rime", "rating": 560, "opRating": 439}, {"opponent": "gardevoir", "rating": 525, "opRating": 475}], "counters": [{"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "talonflame", "rating": 148}, {"opponent": "ninetales", "rating": 178}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 257}, {"opponent": "castform_sunny", "rating": 277}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 7330}, {"moveId": "FIRE_FANG", "uses": 6570}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 4922}, {"moveId": "FLAME_CHARGE", "uses": 3174}, {"moveId": "OVERHEAT", "uses": 2461}, {"moveId": "IRON_HEAD", "uses": 1345}, {"moveId": "FLAMETHROWER", "uses": 1296}, {"moveId": "FIRE_BLAST", "uses": 717}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 64.9}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 315, "matchups": [{"opponent": "xatu", "rating": 694, "opRating": 305}, {"opponent": "oricorio_baile", "rating": 665, "opRating": 334}, {"opponent": "oricorio_pau", "rating": 665, "opRating": 334}, {"opponent": "ho_oh", "rating": 636, "opRating": 363}, {"opponent": "oricorio_pom_pom", "rating": 607, "opRating": 392}], "counters": [{"opponent": "armarouge", "rating": 192}, {"opponent": "ninetales", "rating": 226}, {"opponent": "cresselia", "rating": 237}, {"opponent": "castform_sunny", "rating": 291}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 292}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 5902}, {"moveId": "THUNDER_FANG", "uses": 4905}, {"moveId": "CHARGE_BEAM", "uses": 3092}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 6429}, {"moveId": "PSYCHIC_FANGS", "uses": 2890}, {"moveId": "OVERHEAT", "uses": 1611}, {"moveId": "RETURN", "uses": 1429}, {"moveId": "THUNDER", "uses": 1012}, {"moveId": "FLAME_BURST", "uses": 531}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "WILD_CHARGE"], "score": 64.9}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 306, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 580, "opRating": 419}, {"opponent": "cresselia", "rating": 571, "opRating": 428}, {"opponent": "crocalor", "rating": 544, "opRating": 455}, {"opponent": "castform_sunny", "rating": 508, "opRating": 491}, {"opponent": "wobbuffet", "rating": 504, "opRating": 495}], "counters": [{"opponent": "<PERSON>ras", "rating": 106}, {"opponent": "ninetales", "rating": 123}, {"opponent": "grumpig", "rating": 181}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 211}, {"opponent": "hypno", "rating": 221}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 6151}, {"moveId": "EMBER", "uses": 3926}, {"moveId": "FIRE_SPIN", "uses": 3479}, {"moveId": "LOW_KICK", "uses": 340}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 4244}, {"moveId": "WILD_CHARGE", "uses": 4227}, {"moveId": "FLAME_CHARGE", "uses": 2436}, {"moveId": "SCORCHING_SANDS", "uses": 1600}, {"moveId": "FIRE_BLAST", "uses": 1104}, {"moveId": "HEAT_WAVE", "uses": 328}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "WILD_CHARGE"], "score": 64.9}, {"speciesId": "be<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 324, "matchups": [{"opponent": "slowpoke_galarian", "rating": 649, "opRating": 350}, {"opponent": "wobbuffet", "rating": 602, "opRating": 397}, {"opponent": "medicham", "rating": 525, "opRating": 474}, {"opponent": "deoxys_defense", "rating": 521, "opRating": 478}, {"opponent": "gardevoir", "rating": 521, "opRating": 478}], "counters": [{"opponent": "incineroar", "rating": 106}, {"opponent": "charjabug", "rating": 126}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 176}, {"opponent": "ninetales", "rating": 194}, {"opponent": "castform_sunny", "rating": 219}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 8325}, {"moveId": "CONFUSION", "uses": 5575}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 5841}, {"moveId": "DARK_PULSE", "uses": 5250}, {"moveId": "PSYCHIC", "uses": 2791}]}, "moveset": ["ASTONISH", "ROCK_SLIDE", "DARK_PULSE"], "score": 64.6}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Elgyem", "rating": 323, "matchups": [{"opponent": "espeon", "rating": 685, "opRating": 314}, {"opponent": "wobbuffet", "rating": 549, "opRating": 450}, {"opponent": "gardevoir", "rating": 549, "opRating": 450}, {"opponent": "deoxys_defense", "rating": 537, "opRating": 462}, {"opponent": "espurr", "rating": 507, "opRating": 492}], "counters": [{"opponent": "incineroar", "rating": 91}, {"opponent": "charjabug", "rating": 126}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 176}, {"opponent": "ninetales", "rating": 194}, {"opponent": "castform_sunny", "rating": 219}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 8235}, {"moveId": "CONFUSION", "uses": 5665}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 8226}, {"moveId": "PSYCHIC", "uses": 4406}, {"moveId": "PSYBEAM", "uses": 1264}]}, "moveset": ["ASTONISH", "DARK_PULSE", "PSYCHIC"], "score": 64.6}, {"speciesId": "darum<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 307, "matchups": [{"opponent": "celebi", "rating": 671, "opRating": 328}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 605, "opRating": 394}, {"opponent": "cresselia", "rating": 579, "opRating": 420}, {"opponent": "gardevoir", "rating": 538, "opRating": 461}, {"opponent": "mr_rime", "rating": 535, "opRating": 464}], "counters": [{"opponent": "<PERSON>ras", "rating": 134}, {"opponent": "hypno", "rating": 187}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "ninetales", "rating": 265}, {"opponent": "castform_sunny", "rating": 417}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 8013}, {"moveId": "TACKLE", "uses": 5887}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 7528}, {"moveId": "RETURN", "uses": 3529}, {"moveId": "FLAME_CHARGE", "uses": 2847}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 64.4}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 299, "matchups": [{"opponent": "torkoal", "rating": 622, "opRating": 377}, {"opponent": "castform_sunny", "rating": 592, "opRating": 407}, {"opponent": "cresselia", "rating": 578, "opRating": 421}, {"opponent": "entei", "rating": 574, "opRating": 425}, {"opponent": "crocalor", "rating": 504, "opRating": 495}], "counters": [{"opponent": "mew", "rating": 131}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 140}, {"opponent": "<PERSON>ras", "rating": 145}, {"opponent": "ninetales", "rating": 218}, {"opponent": "grumpig", "rating": 227}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 2674}, {"moveId": "EXTRASENSORY", "uses": 890}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 857}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 818}, {"moveId": "STEEL_WING", "uses": 764}, {"moveId": "HIDDEN_POWER_WATER", "uses": 750}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 747}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 710}, {"moveId": "HIDDEN_POWER_DARK", "uses": 708}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 627}, {"moveId": "HIDDEN_POWER_BUG", "uses": 590}, {"moveId": "HIDDEN_POWER_POISON", "uses": 563}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 531}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 528}, {"moveId": "HIDDEN_POWER_ICE", "uses": 499}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 468}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 391}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 390}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 387}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 5985}, {"moveId": "SACRED_FIRE", "uses": 3590}, {"moveId": "EARTHQUAKE", "uses": 2708}, {"moveId": "SOLAR_BEAM", "uses": 875}, {"moveId": "FIRE_BLAST", "uses": 711}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 64.4}, {"speciesId": "charmeleon", "speciesName": "Charmeleon", "rating": 302, "matchups": [{"opponent": "cresselia", "rating": 589, "opRating": 410}, {"opponent": "gardevoir", "rating": 566, "opRating": 433}, {"opponent": "centiskorch", "rating": 554, "opRating": 445}, {"opponent": "celebi", "rating": 554, "opRating": 445}, {"opponent": "castform_sunny", "rating": 546, "opRating": 453}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "<PERSON>ras", "rating": 156}, {"opponent": "ninetales", "rating": 178}, {"opponent": "hypno", "rating": 190}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 348}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 5805}, {"moveId": "FIRE_FANG", "uses": 5179}, {"moveId": "SCRATCH", "uses": 2910}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 7050}, {"moveId": "RETURN", "uses": 3324}, {"moveId": "FLAMETHROWER", "uses": 2201}, {"moveId": "FLAME_BURST", "uses": 1324}]}, "moveset": ["EMBER", "FIRE_PUNCH", "RETURN"], "score": 64.2}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 308, "matchups": [{"opponent": "deoxys_defense", "rating": 615, "opRating": 384}, {"opponent": "medicham", "rating": 608, "opRating": 391}, {"opponent": "wobbuffet", "rating": 586, "opRating": 413}, {"opponent": "gardevoir", "rating": 572, "opRating": 427}, {"opponent": "cresselia", "rating": 521, "opRating": 478}], "counters": [{"opponent": "hypno", "rating": 125}, {"opponent": "<PERSON>ras", "rating": 170}, {"opponent": "castform_sunny", "rating": 174}, {"opponent": "ninetales", "rating": 202}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 302}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 5498}, {"moveId": "HEX", "uses": 5144}, {"moveId": "CONFUSION", "uses": 3273}], "chargedMoves": [{"moveId": "SURF", "uses": 4048}, {"moveId": "SHADOW_BALL", "uses": 3853}, {"moveId": "SLUDGE_WAVE", "uses": 2260}, {"moveId": "SCALD", "uses": 2014}, {"moveId": "FUTURE_SIGHT", "uses": 1729}]}, "moveset": ["ACID", "SURF", "SHADOW_BALL"], "score": 63.5}, {"speciesId": "braixen", "speciesName": "Braixen", "rating": 299, "matchups": [{"opponent": "celebi", "rating": 658, "opRating": 341}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 609, "opRating": 390}, {"opponent": "monferno", "rating": 601, "opRating": 398}, {"opponent": "gardevoir", "rating": 544, "opRating": 455}, {"opponent": "centiskorch", "rating": 532, "opRating": 467}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "ninetales", "rating": 146}, {"opponent": "hypno", "rating": 156}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 334}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 9672}, {"moveId": "SCRATCH", "uses": 4228}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 6257}, {"moveId": "FLAME_CHARGE", "uses": 5385}, {"moveId": "FLAMETHROWER", "uses": 2252}]}, "moveset": ["EMBER", "PSYSHOCK", "FLAME_CHARGE"], "score": 62.9}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 289, "matchups": [{"opponent": "braixen", "rating": 620, "opRating": 379}, {"opponent": "crocalor", "rating": 591, "opRating": 408}, {"opponent": "castform_sunny", "rating": 548, "opRating": 451}, {"opponent": "ninetales", "rating": 533, "opRating": 466}, {"opponent": "darum<PERSON>", "rating": 528, "opRating": 471}], "counters": [{"opponent": "grumpig", "rating": 109}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 116}, {"opponent": "hypno", "rating": 139}, {"opponent": "charjabug", "rating": 154}, {"opponent": "<PERSON>ras", "rating": 203}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 1946}, {"moveId": "QUICK_ATTACK", "uses": 1356}, {"moveId": "WATER_GUN", "uses": 1256}, {"moveId": "HIDDEN_POWER_WATER", "uses": 833}, {"moveId": "TACKLE", "uses": 807}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 746}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 697}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 677}, {"moveId": "HIDDEN_POWER_DARK", "uses": 640}, {"moveId": "HIDDEN_POWER_BUG", "uses": 541}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 531}, {"moveId": "HIDDEN_POWER_POISON", "uses": 508}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 474}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 471}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 470}, {"moveId": "HIDDEN_POWER_ICE", "uses": 458}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 443}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 358}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 355}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 349}], "chargedMoves": [{"moveId": "SURF", "uses": 4781}, {"moveId": "POWER_GEM", "uses": 3382}, {"moveId": "ICE_BEAM", "uses": 1703}, {"moveId": "PSYCHIC", "uses": 1369}, {"moveId": "THUNDER", "uses": 1352}, {"moveId": "HYDRO_PUMP", "uses": 943}, {"moveId": "PSYBEAM", "uses": 407}]}, "moveset": ["PSYWAVE", "SURF", "POWER_GEM"], "score": 62.9}, {"speciesId": "centiskorch", "speciesName": "Centiskorch", "rating": 302, "matchups": [{"opponent": "deoxys_defense", "rating": 632, "opRating": 367}, {"opponent": "wobbuffet", "rating": 591, "opRating": 408}, {"opponent": "gardevoir", "rating": 566, "opRating": 433}, {"opponent": "cresselia", "rating": 551, "opRating": 448}, {"opponent": "me<PERSON><PERSON>", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 107}, {"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "ninetales", "rating": 162}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 369}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 6961}, {"moveId": "EMBER", "uses": 6939}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 4970}, {"moveId": "LUNGE", "uses": 4823}, {"moveId": "BUG_BUZZ", "uses": 3119}, {"moveId": "HEAT_WAVE", "uses": 977}]}, "moveset": ["EMBER", "LUNGE", "CRUNCH"], "score": 62.6}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 294, "matchups": [{"opponent": "oricorio_baile", "rating": 754, "opRating": 245}, {"opponent": "xatu", "rating": 728, "opRating": 271}, {"opponent": "fletchinder", "rating": 631, "opRating": 368}, {"opponent": "swoobat", "rating": 563, "opRating": 436}, {"opponent": "moltres", "rating": 521, "opRating": 478}], "counters": [{"opponent": "ninetales", "rating": 146}, {"opponent": "grumpig", "rating": 170}, {"opponent": "castform_sunny", "rating": 174}, {"opponent": "cresselia", "rating": 253}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 450}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 6159}, {"moveId": "SPARK", "uses": 4112}, {"moveId": "TACKLE", "uses": 3634}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 5357}, {"moveId": "DISCHARGE", "uses": 4214}, {"moveId": "RETURN", "uses": 1993}, {"moveId": "THUNDERBOLT", "uses": 1544}, {"moveId": "HYPER_BEAM", "uses": 783}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "HYPER_BEAM"], "score": 61.1}, {"speciesId": "tauros_blaze", "speciesName": "<PERSON><PERSON> (Blaze)", "rating": 267, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 771, "opRating": 228}, {"opponent": "incineroar", "rating": 649, "opRating": 350}, {"opponent": "darum<PERSON>", "rating": 552, "opRating": 447}, {"opponent": "houndoom", "rating": 535, "opRating": 464}, {"opponent": "ampha<PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "armarouge", "rating": 64}, {"opponent": "<PERSON>ras", "rating": 136}, {"opponent": "hypno", "rating": 214}, {"opponent": "ninetales", "rating": 250}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 306}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 7770}, {"moveId": "TACKLE", "uses": 5291}, {"moveId": "ZEN_HEADBUTT", "uses": 828}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 4518}, {"moveId": "EARTHQUAKE", "uses": 4009}, {"moveId": "TRAILBLAZE", "uses": 3512}, {"moveId": "IRON_HEAD", "uses": 1842}]}, "moveset": ["DOUBLE_KICK", "FLAME_CHARGE", "TRAILBLAZE"], "score": 61.1}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 274, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 614, "opRating": 385}, {"opponent": "victini", "rating": 596, "opRating": 403}, {"opponent": "crocalor", "rating": 592, "opRating": 407}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 587, "opRating": 412}, {"opponent": "eelektrik", "rating": 565, "opRating": 434}], "counters": [{"opponent": "talonflame", "rating": 81}, {"opponent": "grumpig", "rating": 83}, {"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 362}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 7071}, {"moveId": "EMBER", "uses": 6829}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 5076}, {"moveId": "FIRE_PUNCH", "uses": 4655}, {"moveId": "RETURN", "uses": 1886}, {"moveId": "FLAMETHROWER", "uses": 1448}, {"moveId": "FIRE_BLAST", "uses": 793}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 60.9}, {"speciesId": "pignite", "speciesName": "Pignite", "rating": 270, "matchups": [{"opponent": "castform_sunny", "rating": 601, "opRating": 398}, {"opponent": "celebi", "rating": 544, "opRating": 455}, {"opponent": "quilava", "rating": 515, "opRating": 484}, {"opponent": "centiskorch", "rating": 512, "opRating": 487}, {"opponent": "growlithe", "rating": 512, "opRating": 487}], "counters": [{"opponent": "armarouge", "rating": 85}, {"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "talonflame", "rating": 118}, {"opponent": "ninetales", "rating": 210}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 362}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 8473}, {"moveId": "TACKLE", "uses": 5427}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 6896}, {"moveId": "FLAME_CHARGE", "uses": 3621}, {"moveId": "RETURN", "uses": 1891}, {"moveId": "FLAMETHROWER", "uses": 1483}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 59.8}, {"speciesId": "quilava", "speciesName": "Quilava", "rating": 285, "matchups": [{"opponent": "unown", "rating": 648, "opRating": 351}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 628, "opRating": 371}, {"opponent": "smoochum", "rating": 582, "opRating": 417}, {"opponent": "celebi", "rating": 539, "opRating": 460}, {"opponent": "rotom_mow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 118}, {"opponent": "incineroar", "rating": 122}, {"opponent": "<PERSON>ras", "rating": 156}, {"opponent": "ninetales", "rating": 178}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 221}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 8478}, {"moveId": "TACKLE", "uses": 5422}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 4727}, {"moveId": "DIG", "uses": 4612}, {"moveId": "RETURN", "uses": 2589}, {"moveId": "FLAMETHROWER", "uses": 1957}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 59.8}, {"speciesId": "raboot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 284, "matchups": [{"opponent": "unown", "rating": 653, "opRating": 346}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "mr_mime_galarian", "rating": 550, "opRating": 450}, {"opponent": "celebi", "rating": 546, "opRating": 453}, {"opponent": "rotom_mow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "<PERSON>ras", "rating": 139}, {"opponent": "hypno", "rating": 173}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 228}, {"opponent": "ninetales", "rating": 269}, {"opponent": "castform_sunny", "rating": 352}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 8182}, {"moveId": "TACKLE", "uses": 5718}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 9820}, {"moveId": "FLAMETHROWER", "uses": 4080}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 59.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 287, "matchups": [{"opponent": "tauros_blaze", "rating": 705, "opRating": 294}, {"opponent": "combusken", "rating": 559, "opRating": 440}, {"opponent": "salazzle", "rating": 555, "opRating": 444}, {"opponent": "toxtricity", "rating": 522, "opRating": 477}, {"opponent": "pignite", "rating": 522, "opRating": 477}], "counters": [{"opponent": "incineroar", "rating": 91}, {"opponent": "cresselia", "rating": 169}, {"opponent": "castform_sunny", "rating": 208}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 225}, {"opponent": "ninetales", "rating": 265}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12005}, {"moveId": "POUND", "uses": 1895}], "chargedMoves": [{"moveId": "RETURN", "uses": 4496}, {"moveId": "FUTURE_SIGHT", "uses": 4211}, {"moveId": "PSYCHIC", "uses": 4019}, {"moveId": "PSYBEAM", "uses": 1182}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "PSYCHIC"], "score": 58.9}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 266, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 868, "opRating": 131}, {"opponent": "gardevoir", "rating": 785, "opRating": 214}, {"opponent": "<PERSON><PERSON>", "rating": 614, "opRating": 385}, {"opponent": "eelektrik", "rating": 570, "opRating": 429}, {"opponent": "wobbuffet", "rating": 504, "opRating": 495}], "counters": [{"opponent": "talonflame", "rating": 100}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "ninetales", "rating": 134}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 176}, {"opponent": "hypno", "rating": 190}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 7782}, {"moveId": "POISON_JAB", "uses": 6118}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 6305}, {"moveId": "SLUDGE_WAVE", "uses": 3431}, {"moveId": "DRAGON_PULSE", "uses": 2110}, {"moveId": "FIRE_BLAST", "uses": 2047}]}, "moveset": ["INCINERATE", "POISON_FANG", "DRAGON_PULSE"], "score": 58.7}, {"speciesId": "ponyta", "speciesName": "Ponyta", "rating": 275, "matchups": [{"opponent": "spoink", "rating": 728, "opRating": 271}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 590, "opRating": 409}, {"opponent": "darum<PERSON>", "rating": 564, "opRating": 435}, {"opponent": "gardevoir", "rating": 521, "opRating": 478}, {"opponent": "centiskorch", "rating": 504, "opRating": 495}], "counters": [{"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "grumpig", "rating": 181}, {"opponent": "ninetales", "rating": 253}, {"opponent": "castform_sunny", "rating": 304}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 338}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 8442}, {"moveId": "TACKLE", "uses": 5458}], "chargedMoves": [{"moveId": "STOMP", "uses": 5343}, {"moveId": "FLAME_CHARGE", "uses": 5233}, {"moveId": "FIRE_BLAST", "uses": 2347}, {"moveId": "FLAME_WHEEL", "uses": 996}]}, "moveset": ["EMBER", "FLAME_CHARGE", "STOMP"], "score": 58}, {"speciesId": "em<PERSON>ga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 284, "matchups": [{"opponent": "oricorio_baile", "rating": 728, "opRating": 272}, {"opponent": "vikavolt", "rating": 636, "opRating": 364}, {"opponent": "fletchinder", "rating": 572, "opRating": 428}, {"opponent": "gardevoir", "rating": 520, "opRating": 480}, {"opponent": "talonflame", "rating": 504, "opRating": 496}], "counters": [{"opponent": "charjabug", "rating": 83}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "ninetales", "rating": 123}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 7659}, {"moveId": "QUICK_ATTACK", "uses": 6241}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 4445}, {"moveId": "DISCHARGE", "uses": 4025}, {"moveId": "AERIAL_ACE", "uses": 4021}, {"moveId": "THUNDERBOLT", "uses": 1517}]}, "moveset": ["THUNDER_SHOCK", "ACROBATICS", "DISCHARGE"], "score": 57.8}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 269, "matchups": [{"opponent": "celebi", "rating": 781, "opRating": 218}, {"opponent": "tauros_blaze", "rating": 664, "opRating": 335}, {"opponent": "victini", "rating": 597, "opRating": 402}, {"opponent": "centiskorch", "rating": 539, "opRating": 460}, {"opponent": "entei", "rating": 531, "opRating": 468}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 73}, {"opponent": "<PERSON>ras", "rating": 103}, {"opponent": "electrode_hisuian", "rating": 104}, {"opponent": "charjabug", "rating": 115}, {"opponent": "hypno", "rating": 146}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 11713}, {"moveId": "POUND", "uses": 2187}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 8733}, {"moveId": "AERIAL_ACE", "uses": 3308}, {"moveId": "HURRICANE", "uses": 1873}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 57.6}, {"speciesId": "slowpoke_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 280, "matchups": [{"opponent": "salazzle", "rating": 553, "opRating": 446}, {"opponent": "magmar", "rating": 534, "opRating": 465}, {"opponent": "magby", "rating": 524, "opRating": 475}, {"opponent": "pignite", "rating": 510, "opRating": 489}, {"opponent": "combusken", "rating": 510, "opRating": 489}], "counters": [{"opponent": "incineroar", "rating": 76}, {"opponent": "hypno", "rating": 166}, {"opponent": "castform_sunny", "rating": 191}, {"opponent": "ninetales", "rating": 242}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 355}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 10595}, {"moveId": "IRON_TAIL", "uses": 3305}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 6609}, {"moveId": "SURF", "uses": 6178}, {"moveId": "PSYCHIC", "uses": 1115}]}, "moveset": ["CONFUSION", "PSYSHOCK", "SURF"], "score": 57.6}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 277, "matchups": [{"opponent": "oricorio_baile", "rating": 690, "opRating": 309}, {"opponent": "xatu", "rating": 690, "opRating": 309}, {"opponent": "oricorio_pom_pom", "rating": 666, "opRating": 333}, {"opponent": "lugia", "rating": 555, "opRating": 444}, {"opponent": "fletchinder", "rating": 527, "opRating": 472}], "counters": [{"opponent": "hypno", "rating": 125}, {"opponent": "ninetales", "rating": 162}, {"opponent": "<PERSON>ras", "rating": 203}, {"opponent": "castform_sunny", "rating": 315}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 5385}, {"moveId": "ACID", "uses": 5135}, {"moveId": "SPARK", "uses": 3375}], "chargedMoves": [{"moveId": "LIQUIDATION", "uses": 3944}, {"moveId": "CRUNCH", "uses": 3824}, {"moveId": "DRAGON_CLAW", "uses": 3267}, {"moveId": "THUNDERBOLT", "uses": 2356}, {"moveId": "ACID_SPRAY", "uses": 516}]}, "moveset": ["VOLT_SWITCH", "DRAGON_CLAW", "THUNDERBOLT"], "score": 57.3}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 275, "matchups": [{"opponent": "mr_mime_galarian", "rating": 574, "opRating": 425}, {"opponent": "gardevoir", "rating": 537, "opRating": 462}, {"opponent": "centiskorch", "rating": 537, "opRating": 462}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "larve<PERSON>", "rating": 518, "opRating": 481}], "counters": [{"opponent": "ninetales", "rating": 123}, {"opponent": "<PERSON>ras", "rating": 125}, {"opponent": "castform_sunny", "rating": 167}, {"opponent": "hypno", "rating": 187}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 200}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 7579}, {"moveId": "LICK", "uses": 6321}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 6685}, {"moveId": "FLAMETHROWER", "uses": 5582}, {"moveId": "POWER_UP_PUNCH", "uses": 1644}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 57.3}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 268, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 719, "opRating": 280}, {"opponent": "celebi", "rating": 654, "opRating": 345}, {"opponent": "houndour", "rating": 604, "opRating": 395}, {"opponent": "larve<PERSON>", "rating": 568, "opRating": 431}, {"opponent": "houndoom", "rating": 510, "opRating": 489}], "counters": [{"opponent": "armarouge", "rating": 106}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "ninetales", "rating": 130}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "castform_sunny", "rating": 297}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 10582}, {"moveId": "ROCK_SMASH", "uses": 3318}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 5579}, {"moveId": "RETURN", "uses": 3763}, {"moveId": "LOW_SWEEP", "uses": 3299}, {"moveId": "FLAME_WHEEL", "uses": 1256}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 57.1}, {"speciesId": "meowstic_female", "speciesName": "<PERSON><PERSON><PERSON> (Female)", "rating": 276, "matchups": [{"opponent": "tauros_blaze", "rating": 659, "opRating": 340}, {"opponent": "drowzee", "rating": 545, "opRating": 454}, {"opponent": "combusken", "rating": 530, "opRating": 469}, {"opponent": "salazzle", "rating": 518, "opRating": 481}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 511, "opRating": 488}], "counters": [{"opponent": "grumpig", "rating": 106}, {"opponent": "cresselia", "rating": 169}, {"opponent": "ninetales", "rating": 222}, {"opponent": "castform_sunny", "rating": 226}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 250}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 6148}, {"moveId": "MAGICAL_LEAF", "uses": 4648}, {"moveId": "CHARM", "uses": 3115}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 7422}, {"moveId": "PSYCHIC", "uses": 3734}, {"moveId": "ENERGY_BALL", "uses": 2737}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "ENERGY_BALL"], "score": 56.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 273, "matchups": [{"opponent": "unown", "rating": 815, "opRating": 184}, {"opponent": "tauros_blaze", "rating": 680, "opRating": 319}, {"opponent": "pignite", "rating": 529, "opRating": 470}, {"opponent": "salazzle", "rating": 525, "opRating": 474}, {"opponent": "combusken", "rating": 516, "opRating": 483}], "counters": [{"opponent": "incineroar", "rating": 91}, {"opponent": "hypno", "rating": 170}, {"opponent": "castform_sunny", "rating": 208}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "ninetales", "rating": 222}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 8994}, {"moveId": "CHARM", "uses": 4906}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 6114}, {"moveId": "RETURN", "uses": 2614}, {"moveId": "FUTURE_SIGHT", "uses": 2606}, {"moveId": "PSYCHIC", "uses": 2551}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "FUTURE_SIGHT"], "score": 56.5}, {"speciesId": "growlithe", "speciesName": "Grow<PERSON>he", "rating": 268, "matchups": [{"opponent": "celebi", "rating": 818, "opRating": 181}, {"opponent": "unown", "rating": 740, "opRating": 259}, {"opponent": "spoink", "rating": 700, "opRating": 300}, {"opponent": "<PERSON><PERSON>", "rating": 659, "opRating": 340}, {"opponent": "gardevoir", "rating": 507, "opRating": 492}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "castform_sunny", "rating": 154}, {"opponent": "hypno", "rating": 156}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 274}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 8754}, {"moveId": "BITE", "uses": 5146}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 6936}, {"moveId": "FLAMETHROWER", "uses": 4318}, {"moveId": "RETURN", "uses": 1698}, {"moveId": "FLAME_WHEEL", "uses": 962}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 56.2}, {"speciesId": "mr_rime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 271, "matchups": [{"opponent": "unown", "rating": 795, "opRating": 204}, {"opponent": "exeggcute", "rating": 595, "opRating": 404}], "counters": [{"opponent": "incineroar", "rating": 68}, {"opponent": "grumpig", "rating": 90}, {"opponent": "ninetales", "rating": 142}, {"opponent": "castform_sunny", "rating": 143}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 274}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 6307}, {"moveId": "CONFUSION", "uses": 6238}, {"moveId": "ZEN_HEADBUTT", "uses": 1375}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 5276}, {"moveId": "PSYCHIC", "uses": 2674}, {"moveId": "ICY_WIND", "uses": 2576}, {"moveId": "TRIPLE_AXEL", "uses": 2576}, {"moveId": "PSYBEAM", "uses": 764}]}, "moveset": ["CONFUSION", "ICY_WIND", "PSYCHIC"], "score": 56.2}, {"speciesId": "combusken", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 258, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 622, "opRating": 377}, {"opponent": "centiskorch", "rating": 610, "opRating": 389}, {"opponent": "raboot", "rating": 580, "opRating": 419}, {"opponent": "darum<PERSON>", "rating": 526, "opRating": 473}, {"opponent": "<PERSON><PERSON>", "rating": 519, "opRating": 480}], "counters": [{"opponent": "armarouge", "rating": 106}, {"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "ninetales", "rating": 146}, {"opponent": "grumpig", "rating": 159}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 334}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 10330}, {"moveId": "PECK", "uses": 3570}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 5607}, {"moveId": "FLAME_CHARGE", "uses": 4242}, {"moveId": "RETURN", "uses": 2287}, {"moveId": "FLAMETHROWER", "uses": 1758}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 56}, {"speciesId": "fuecoco", "speciesName": "Fuecoco", "rating": 271, "matchups": [{"opponent": "larve<PERSON>", "rating": 628, "opRating": 371}, {"opponent": "gardevoir", "rating": 592, "opRating": 407}, {"opponent": "centiskorch", "rating": 578, "opRating": 421}, {"opponent": "mr_mime_galarian", "rating": 569, "opRating": 430}, {"opponent": "celebi", "rating": 559, "opRating": 440}], "counters": [{"opponent": "<PERSON>ras", "rating": 83}, {"opponent": "talonflame", "rating": 111}, {"opponent": "ninetales", "rating": 130}, {"opponent": "hypno", "rating": 166}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 197}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 9987}, {"moveId": "BITE", "uses": 3913}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 6573}, {"moveId": "FLAMETHROWER", "uses": 3769}, {"moveId": "DISARMING_VOICE", "uses": 3570}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 55.8}, {"speciesId": "rapidash_galarian", "speciesName": "Rapidash (Galarian)", "rating": 281, "matchups": [{"opponent": "slowpoke_galarian", "rating": 607, "opRating": 392}, {"opponent": "hat<PERSON><PERSON>", "rating": 598, "opRating": 401}, {"opponent": "gardevoir", "rating": 598, "opRating": 401}, {"opponent": "deoxys_defense", "rating": 593, "opRating": 406}, {"opponent": "medicham", "rating": 544, "opRating": 455}], "counters": [{"opponent": "<PERSON>ras", "rating": 125}, {"opponent": "cresselia", "rating": 188}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 197}, {"opponent": "ninetales", "rating": 206}, {"opponent": "castform_sunny", "rating": 219}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 7345}, {"moveId": "PSYCHO_CUT", "uses": 5966}, {"moveId": "LOW_KICK", "uses": 589}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 3847}, {"moveId": "BODY_SLAM", "uses": 2709}, {"moveId": "MEGAHORN", "uses": 2630}, {"moveId": "HIGH_HORSEPOWER", "uses": 2173}, {"moveId": "PSYCHIC", "uses": 1321}, {"moveId": "PLAY_ROUGH", "uses": 1217}]}, "moveset": ["FAIRY_WIND", "BODY_SLAM", "WILD_CHARGE"], "score": 55.8}, {"speciesId": "hattrem", "speciesName": "Hattrem", "rating": 270, "matchups": [{"opponent": "unown", "rating": 804, "opRating": 195}, {"opponent": "tauros_blaze", "rating": 632, "opRating": 367}, {"opponent": "deoxys_speed", "rating": 515, "opRating": 484}], "counters": [{"opponent": "grumpig", "rating": 90}, {"opponent": "cresselia", "rating": 114}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "castform_sunny", "rating": 226}, {"opponent": "ninetales", "rating": 242}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 8911}, {"moveId": "CHARM", "uses": 4989}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 8790}, {"moveId": "DAZZLING_GLEAM", "uses": 3636}, {"moveId": "PSYCHIC", "uses": 1472}]}, "moveset": ["CONFUSION", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 55.6}, {"speciesId": "houndour", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 274, "matchups": [{"opponent": "mr_rime", "rating": 641, "opRating": 358}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 633, "opRating": 366}, {"opponent": "hypno", "rating": 620, "opRating": 379}, {"opponent": "espurr", "rating": 612, "opRating": 387}, {"opponent": "meowstic_female", "rating": 583, "opRating": 416}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "grumpig", "rating": 136}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 144}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 7712}, {"moveId": "FEINT_ATTACK", "uses": 6188}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 6535}, {"moveId": "FLAMETHROWER", "uses": 2864}, {"moveId": "DARK_PULSE", "uses": 2544}, {"moveId": "RETURN", "uses": 1963}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 55.1}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 257, "matchups": [{"opponent": "fletchinder", "rating": 607, "opRating": 392}, {"opponent": "darum<PERSON>", "rating": 575, "opRating": 424}, {"opponent": "charjabug", "rating": 559, "opRating": 440}, {"opponent": "lugia", "rating": 559, "opRating": 440}, {"opponent": "gardevoir", "rating": 537, "opRating": 462}], "counters": [{"opponent": "ninetales", "rating": 87}, {"opponent": "<PERSON>ras", "rating": 100}, {"opponent": "hypno", "rating": 125}, {"opponent": "uxie", "rating": 137}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 426}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 6972}, {"moveId": "THUNDER_SHOCK", "uses": 6928}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 5939}, {"moveId": "OVERHEAT", "uses": 5357}, {"moveId": "THUNDER", "uses": 2575}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 54.9}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 262, "matchups": [{"opponent": "unown", "rating": 615, "opRating": 384}, {"opponent": "exeggutor", "rating": 551, "opRating": 448}, {"opponent": "smoochum", "rating": 529, "opRating": 470}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 512, "opRating": 487}], "counters": [{"opponent": "<PERSON>ras", "rating": 122}, {"opponent": "grumpig", "rating": 189}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "ninetales", "rating": 250}, {"opponent": "castform_sunny", "rating": 328}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 8310}, {"moveId": "TACKLE", "uses": 5590}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 7521}, {"moveId": "FOCUS_BLAST", "uses": 3227}, {"moveId": "FLAMETHROWER", "uses": 3126}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 54.7}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 267, "matchups": [{"opponent": "oricorio_baile", "rating": 639, "opRating": 360}, {"opponent": "slowpoke_galarian", "rating": 635, "opRating": 364}, {"opponent": "wobbuffet", "rating": 576, "opRating": 423}, {"opponent": "lugia", "rating": 545, "opRating": 454}, {"opponent": "medicham", "rating": 518, "opRating": 481}], "counters": [{"opponent": "grumpig", "rating": 117}, {"opponent": "ninetales", "rating": 123}, {"opponent": "hypno", "rating": 125}, {"opponent": "castform_sunny", "rating": 191}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 5363}, {"moveId": "VOLT_SWITCH", "uses": 5261}, {"moveId": "SPARK", "uses": 3254}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 4601}, {"moveId": "SURF", "uses": 2890}, {"moveId": "THUNDER_PUNCH", "uses": 2670}, {"moveId": "TRAILBLAZE", "uses": 1774}, {"moveId": "PSYCHIC", "uses": 1251}, {"moveId": "GRASS_KNOT", "uses": 722}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SURF"], "score": 54.5}, {"speciesId": "torracat", "speciesName": "Torracat", "rating": 262, "matchups": [{"opponent": "spoink", "rating": 700, "opRating": 300}, {"opponent": "unown", "rating": 666, "opRating": 333}, {"opponent": "smoochum", "rating": 548, "opRating": 451}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 529, "opRating": 470}, {"opponent": "centiskorch", "rating": 514, "opRating": 485}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "castform_sunny", "rating": 154}, {"opponent": "hypno", "rating": 187}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 338}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 9066}, {"moveId": "BITE", "uses": 4834}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 7020}, {"moveId": "FLAME_CHARGE", "uses": 4866}, {"moveId": "FLAMETHROWER", "uses": 2010}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 54.5}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 266, "matchups": [{"opponent": "jynx", "rating": 728, "opRating": 271}, {"opponent": "spoink", "rating": 641, "opRating": 358}, {"opponent": "unown", "rating": 623, "opRating": 376}, {"opponent": "flaaffy", "rating": 532, "opRating": 467}, {"opponent": "smoochum", "rating": 521, "opRating": 478}], "counters": [{"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "ninetales", "rating": 134}, {"opponent": "grumpig", "rating": 136}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 161}, {"opponent": "castform_sunny", "rating": 181}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 7383}, {"moveId": "FIRE_FANG", "uses": 3757}, {"moveId": "TACKLE", "uses": 2745}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 6166}, {"moveId": "OVERHEAT", "uses": 4029}, {"moveId": "PSYCHIC", "uses": 1959}, {"moveId": "FOCUS_BLAST", "uses": 1739}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 53.8}, {"speciesId": "espurr", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 261, "matchups": [{"opponent": "unown", "rating": 772, "opRating": 227}, {"opponent": "tauros_blaze", "rating": 665, "opRating": 334}, {"opponent": "combusken", "rating": 510, "opRating": 489}, {"opponent": "flaaffy", "rating": 506, "opRating": 493}], "counters": [{"opponent": "incineroar", "rating": 91}, {"opponent": "hypno", "rating": 146}, {"opponent": "castform_sunny", "rating": 208}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}, {"opponent": "ninetales", "rating": 222}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 8848}, {"moveId": "SCRATCH", "uses": 5052}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 9126}, {"moveId": "ENERGY_BALL", "uses": 3224}, {"moveId": "PSYCHIC", "uses": 1524}]}, "moveset": ["CONFUSION", "PSYSHOCK", "ENERGY_BALL"], "score": 53.8}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 259, "matchups": [{"opponent": "unown", "rating": 784, "opRating": 215}, {"opponent": "tauros_blaze", "rating": 650, "opRating": 349}], "counters": [{"opponent": "hypno", "rating": 98}, {"opponent": "cresselia", "rating": 200}, {"opponent": "castform_sunny", "rating": 226}, {"opponent": "ninetales", "rating": 242}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 313}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 7783}, {"moveId": "EXTRASENSORY", "uses": 6117}], "chargedMoves": [{"moveId": "SWIFT", "uses": 8368}, {"moveId": "FUTURE_SIGHT", "uses": 3192}, {"moveId": "BLIZZARD", "uses": 2334}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 53.8}, {"speciesId": "mr_mime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 258, "matchups": [{"opponent": "tauros_blaze", "rating": 633, "opRating": 366}, {"opponent": "slowpoke_galarian", "rating": 627, "opRating": 372}, {"opponent": "wobbuffet", "rating": 605, "opRating": 394}, {"opponent": "exeggcute", "rating": 561, "opRating": 438}, {"opponent": "medicham", "rating": 522, "opRating": 477}], "counters": [{"opponent": "hypno", "rating": 139}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 151}, {"opponent": "cresselia", "rating": 169}, {"opponent": "castform_sunny", "rating": 219}, {"opponent": "ninetales", "rating": 242}], "moves": {"fastMoves": [{"moveId": "PSYWAVE", "uses": 6136}, {"moveId": "CONFUSION", "uses": 3887}, {"moveId": "MAGICAL_LEAF", "uses": 3117}, {"moveId": "ZEN_HEADBUTT", "uses": 761}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 8614}, {"moveId": "PSYCHIC", "uses": 4077}, {"moveId": "PSYBEAM", "uses": 1193}]}, "moveset": ["PSYWAVE", "SHADOW_BALL", "PSYCHIC"], "score": 53.8}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 260, "matchups": [{"opponent": "spoink", "rating": 682, "opRating": 317}, {"opponent": "unown", "rating": 642, "opRating": 357}, {"opponent": "mr_mime_galarian", "rating": 571, "opRating": 428}, {"opponent": "smoochum", "rating": 551, "opRating": 448}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 515, "opRating": 484}], "counters": [{"opponent": "<PERSON>ras", "rating": 122}, {"opponent": "ninetales", "rating": 123}, {"opponent": "hypno", "rating": 187}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 200}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 8836}, {"moveId": "BITE", "uses": 5064}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 7942}, {"moveId": "FLAMETHROWER", "uses": 4705}, {"moveId": "FIRE_BLAST", "uses": 1286}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 53.8}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 269, "matchups": [{"opponent": "oricorio_baile", "rating": 708, "opRating": 291}, {"opponent": "xatu", "rating": 704, "opRating": 295}, {"opponent": "rotom_fan", "rating": 656, "opRating": 343}, {"opponent": "swoobat", "rating": 586, "opRating": 413}, {"opponent": "fletchinder", "rating": 578, "opRating": 421}], "counters": [{"opponent": "charjabug", "rating": 83}, {"opponent": "ninetales", "rating": 130}, {"opponent": "uxie", "rating": 137}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 380}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12037}, {"moveId": "LOW_KICK", "uses": 1863}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 6891}, {"moveId": "RETURN", "uses": 2994}, {"moveId": "THUNDERBOLT", "uses": 2142}, {"moveId": "THUNDER", "uses": 1866}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 53.6}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 264, "matchups": [{"opponent": "uxie", "rating": 631, "opRating": 368}, {"opponent": "wobbuffet", "rating": 593, "opRating": 406}, {"opponent": "deoxys_defense", "rating": 593, "opRating": 406}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 585, "opRating": 414}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 581, "opRating": 418}], "counters": [{"opponent": "talonflame", "rating": 74}, {"opponent": "charjabug", "rating": 79}, {"opponent": "castform_sunny", "rating": 89}, {"opponent": "ninetales", "rating": 99}, {"opponent": "<PERSON>ras", "rating": 195}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 5626}, {"moveId": "MAGICAL_LEAF", "uses": 5117}, {"moveId": "CHARGE_BEAM", "uses": 3167}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 3444}, {"moveId": "PSYCHIC", "uses": 2992}, {"moveId": "LEAF_STORM", "uses": 2692}, {"moveId": "DAZZLING_GLEAM", "uses": 2662}, {"moveId": "HYPER_BEAM", "uses": 2082}]}, "moveset": ["MAGICAL_LEAF", "SEED_BOMB", "LEAF_STORM"], "score": 53.4}, {"speciesId": "larve<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 254, "matchups": [{"opponent": "celebi", "rating": 786, "opRating": 213}, {"opponent": "unown", "rating": 690, "opRating": 309}, {"opponent": "smoochum", "rating": 564, "opRating": 435}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 541, "opRating": 458}, {"opponent": "cresselia", "rating": 507, "opRating": 492}], "counters": [{"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "talonflame", "rating": 118}, {"opponent": "ninetales", "rating": 170}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 186}, {"opponent": "castform_sunny", "rating": 263}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 6992}, {"moveId": "EMBER", "uses": 6908}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 6476}, {"moveId": "BUG_BUZZ", "uses": 6207}, {"moveId": "FLAME_WHEEL", "uses": 1204}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BUG_BUZZ"], "score": 53.2}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 258, "matchups": [{"opponent": "celebi", "rating": 762, "opRating": 237}, {"opponent": "spoink", "rating": 661, "opRating": 338}, {"opponent": "vikavolt", "rating": 538, "opRating": 461}, {"opponent": "quilava", "rating": 512, "opRating": 487}, {"opponent": "larve<PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "<PERSON>ras", "rating": 122}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 144}, {"opponent": "hypno", "rating": 187}, {"opponent": "ninetales", "rating": 218}, {"opponent": "castform_sunny", "rating": 243}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 7600}, {"moveId": "WING_ATTACK", "uses": 6300}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 5352}, {"moveId": "SKY_ATTACK", "uses": 3551}, {"moveId": "OVERHEAT", "uses": 3410}, {"moveId": "FIRE_BLAST", "uses": 995}, {"moveId": "HEAT_WAVE", "uses": 585}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 52.9}, {"speciesId": "minun", "speciesName": "<PERSON><PERSON>", "rating": 260, "matchups": [{"opponent": "braviary_hisuian", "rating": 673, "opRating": 326}, {"opponent": "elekid", "rating": 638, "opRating": 361}, {"opponent": "oricorio_baile", "rating": 611, "opRating": 388}, {"opponent": "zapdos", "rating": 588, "opRating": 411}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 580, "opRating": 419}], "counters": [{"opponent": "ninetales", "rating": 134}, {"opponent": "<PERSON>ras", "rating": 136}, {"opponent": "grumpig", "rating": 140}, {"opponent": "castform_sunny", "rating": 167}, {"opponent": "talonflame", "rating": 196}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 7522}, {"moveId": "SPARK", "uses": 6378}], "chargedMoves": [{"moveId": "SWIFT", "uses": 5605}, {"moveId": "DISCHARGE", "uses": 4268}, {"moveId": "GRASS_KNOT", "uses": 2439}, {"moveId": "THUNDERBOLT", "uses": 1576}]}, "moveset": ["QUICK_ATTACK", "THUNDERBOLT", "GRASS_KNOT"], "score": 52.7}, {"speciesId": "swoobat", "speciesName": "Swoobat", "rating": 255, "matchups": [{"opponent": "spoink", "rating": 818, "opRating": 181}, {"opponent": "tauros_blaze", "rating": 652, "opRating": 347}, {"opponent": "espurr", "rating": 630, "opRating": 369}, {"opponent": "exeggcute", "rating": 568, "opRating": 431}, {"opponent": "celebi", "rating": 528, "opRating": 471}], "counters": [{"opponent": "hypno", "rating": 98}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 207}, {"opponent": "ninetales", "rating": 253}, {"opponent": "castform_sunny", "rating": 253}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 7601}, {"moveId": "AIR_SLASH", "uses": 6299}], "chargedMoves": [{"moveId": "FLY", "uses": 4401}, {"moveId": "PSYCHIC_FANGS", "uses": 3868}, {"moveId": "AERIAL_ACE", "uses": 3343}, {"moveId": "FUTURE_SIGHT", "uses": 1539}, {"moveId": "PSYCHIC", "uses": 733}]}, "moveset": ["CONFUSION", "FLY", "PSYCHIC_FANGS"], "score": 52.3}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 251, "matchups": [{"opponent": "spoink", "rating": 650, "opRating": 350}, {"opponent": "unown", "rating": 595, "opRating": 405}, {"opponent": "flaaffy", "rating": 560, "opRating": 440}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 540, "opRating": 460}, {"opponent": "exeggutor", "rating": 515, "opRating": 485}], "counters": [{"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "ninetales", "rating": 130}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 179}, {"opponent": "hypno", "rating": 187}, {"opponent": "castform_sunny", "rating": 208}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 7268}, {"moveId": "FIRE_SPIN", "uses": 6632}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 3732}, {"moveId": "FLAMETHROWER", "uses": 3171}, {"moveId": "OVERHEAT", "uses": 2940}, {"moveId": "LAST_RESORT", "uses": 2720}, {"moveId": "FIRE_BLAST", "uses": 825}, {"moveId": "HEAT_WAVE", "uses": 505}]}, "moveset": ["EMBER", "SUPER_POWER", "FLAMETHROWER"], "score": 52}, {"speciesId": "magby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 241, "matchups": [{"opponent": "unown", "rating": 750, "opRating": 250}, {"opponent": "spoink", "rating": 666, "opRating": 333}, {"opponent": "mr_mime_galarian", "rating": 533, "opRating": 466}, {"opponent": "smoochum", "rating": 512, "opRating": 487}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 504, "opRating": 495}], "counters": [{"opponent": "grumpig", "rating": 83}, {"opponent": "ninetales", "rating": 226}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 246}, {"opponent": "<PERSON>ras", "rating": 262}, {"opponent": "castform_sunny", "rating": 263}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 7171}, {"moveId": "KARATE_CHOP", "uses": 6729}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 7102}, {"moveId": "BRICK_BREAK", "uses": 3219}, {"moveId": "FLAMETHROWER", "uses": 2226}, {"moveId": "FLAME_BURST", "uses": 1346}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "BRICK_BREAK"], "score": 52}, {"speciesId": "mr_mime_galarian", "speciesName": "Mr. <PERSON> (Galarian)", "rating": 247, "matchups": [{"opponent": "unown", "rating": 759, "opRating": 240}, {"opponent": "salazzle", "rating": 581, "opRating": 418}, {"opponent": "exeggcute", "rating": 519, "opRating": 480}], "counters": [{"opponent": "grumpig", "rating": 90}, {"opponent": "hypno", "rating": 112}, {"opponent": "castform_sunny", "rating": 143}, {"opponent": "ninetales", "rating": 154}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 250}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 11624}, {"moveId": "ZEN_HEADBUTT", "uses": 2276}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 6569}, {"moveId": "PSYCHIC", "uses": 3182}, {"moveId": "TRIPLE_AXEL", "uses": 3173}, {"moveId": "PSYBEAM", "uses": 908}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "TRIPLE_AXEL"], "score": 51.8}, {"speciesId": "flaaffy", "speciesName": "Flaaffy", "rating": 250, "matchups": [{"opponent": "braviary_hisuian", "rating": 675, "opRating": 324}, {"opponent": "sigilyph", "rating": 672, "opRating": 327}, {"opponent": "oricorio_baile", "rating": 649, "opRating": 350}, {"opponent": "fletchinder", "rating": 562, "opRating": 437}, {"opponent": "swoobat", "rating": 536, "opRating": 463}], "counters": [{"opponent": "ninetales", "rating": 83}, {"opponent": "castform_sunny", "rating": 99}, {"opponent": "cresselia", "rating": 182}, {"opponent": "uxie", "rating": 229}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 383}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 7401}, {"moveId": "CHARGE_BEAM", "uses": 6499}], "chargedMoves": [{"moveId": "POWER_GEM", "uses": 4507}, {"moveId": "DISCHARGE", "uses": 3587}, {"moveId": "TRAILBLAZE", "uses": 2807}, {"moveId": "RETURN", "uses": 1695}, {"moveId": "THUNDERBOLT", "uses": 1343}]}, "moveset": ["CHARGE_BEAM", "TRAILBLAZE", "DISCHARGE"], "score": 51.6}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 260, "matchups": [{"opponent": "oricorio_baile", "rating": 672, "opRating": 327}, {"opponent": "xatu", "rating": 672, "opRating": 327}, {"opponent": "oricorio_pau", "rating": 672, "opRating": 327}, {"opponent": "oricorio_pom_pom", "rating": 647, "opRating": 352}, {"opponent": "lugia", "rating": 525, "opRating": 474}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "hypno", "rating": 183}, {"opponent": "castform_sunny", "rating": 184}, {"opponent": "cresselia", "rating": 203}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 327}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 1942}, {"moveId": "SPARK", "uses": 1514}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 947}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 913}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 840}, {"moveId": "HIDDEN_POWER_WATER", "uses": 812}, {"moveId": "HIDDEN_POWER_DARK", "uses": 787}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 763}, {"moveId": "HIDDEN_POWER_BUG", "uses": 666}, {"moveId": "HIDDEN_POWER_POISON", "uses": 635}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 592}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 589}, {"moveId": "HIDDEN_POWER_ICE", "uses": 550}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 543}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 535}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 466}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 438}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 424}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 6122}, {"moveId": "CRUNCH", "uses": 3796}, {"moveId": "PSYCHIC_FANGS", "uses": 2912}, {"moveId": "HYPER_BEAM", "uses": 1094}]}, "moveset": ["SPARK", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 51.6}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 242, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 619, "opRating": 380}, {"opponent": "centiskorch", "rating": 578, "opRating": 421}, {"opponent": "<PERSON><PERSON>", "rating": 557, "opRating": 442}, {"opponent": "larve<PERSON>", "rating": 540, "opRating": 459}, {"opponent": "torracat", "rating": 517, "opRating": 482}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 117}, {"opponent": "grumpig", "rating": 136}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 179}, {"opponent": "castform_sunny", "rating": 181}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 12211}, {"moveId": "LOW_KICK", "uses": 1689}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 5710}, {"moveId": "ROCK_SLIDE", "uses": 4432}, {"moveId": "FOCUS_BLAST", "uses": 1791}, {"moveId": "FLAME_CHARGE", "uses": 1583}, {"moveId": "HEAT_WAVE", "uses": 440}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 50.9}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 239, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 579, "opRating": 420}, {"opponent": "centiskorch", "rating": 575, "opRating": 424}, {"opponent": "flaaffy", "rating": 554, "opRating": 445}, {"opponent": "larve<PERSON>", "rating": 529, "opRating": 470}, {"opponent": "<PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "armarouge", "rating": 102}, {"opponent": "ninetales", "rating": 123}, {"opponent": "<PERSON>ras", "rating": 125}, {"opponent": "castform_sunny", "rating": 171}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 172}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 8137}, {"moveId": "COUNTER", "uses": 5763}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 3584}, {"moveId": "STONE_EDGE", "uses": 3036}, {"moveId": "BRAVE_BIRD", "uses": 2806}, {"moveId": "BLAZE_KICK", "uses": 2610}, {"moveId": "FOCUS_BLAST", "uses": 1120}, {"moveId": "OVERHEAT", "uses": 765}]}, "moveset": ["FIRE_SPIN", "BLAZE_KICK", "BLAST_BURN"], "score": 50.7}, {"speciesId": "exeggcute", "speciesName": "Exeggcute", "rating": 247, "matchups": [{"opponent": "unown", "rating": 806, "opRating": 193}, {"opponent": "pawmo", "rating": 665, "opRating": 334}, {"opponent": "flaaffy", "rating": 654, "opRating": 345}, {"opponent": "elekid", "rating": 514, "opRating": 485}], "counters": [{"opponent": "incineroar", "rating": 57}, {"opponent": "cresselia", "rating": 111}, {"opponent": "charjabug", "rating": 119}, {"opponent": "castform_sunny", "rating": 154}, {"opponent": "ninetales", "rating": 162}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 7017}, {"moveId": "BULLET_SEED", "uses": 6883}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 5498}, {"moveId": "SEED_BOMB", "uses": 3558}, {"moveId": "PSYCHIC", "uses": 2517}, {"moveId": "RETURN", "uses": 2336}]}, "moveset": ["CONFUSION", "SEED_BOMB", "ANCIENT_POWER"], "score": 50.1}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 237, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 579, "opRating": 420}], "counters": [{"opponent": "armarouge", "rating": 89}, {"opponent": "<PERSON>ras", "rating": 122}, {"opponent": "ninetales", "rating": 123}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 200}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 11370}, {"moveId": "ROCK_SMASH", "uses": 2530}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 5894}, {"moveId": "CLOSE_COMBAT", "uses": 5738}, {"moveId": "FLAMETHROWER", "uses": 1360}, {"moveId": "SOLAR_BEAM", "uses": 884}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 50.1}, {"speciesId": "elekid", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 245, "matchups": [{"opponent": "oricorio_baile", "rating": 695, "opRating": 304}, {"opponent": "xatu", "rating": 695, "opRating": 304}, {"opponent": "hat<PERSON><PERSON>", "rating": 550, "opRating": 450}, {"opponent": "fletchinder", "rating": 529, "opRating": 470}, {"opponent": "swoobat", "rating": 529, "opRating": 470}], "counters": [{"opponent": "grumpig", "rating": 83}, {"opponent": "ninetales", "rating": 87}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 274}, {"opponent": "castform_sunny", "rating": 294}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11960}, {"moveId": "LOW_KICK", "uses": 1940}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 6411}, {"moveId": "BRICK_BREAK", "uses": 2791}, {"moveId": "DISCHARGE", "uses": 2703}, {"moveId": "THUNDERBOLT", "uses": 1995}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "BRICK_BREAK"], "score": 49.8}, {"speciesId": "r<PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 242, "matchups": [{"opponent": "oricorio_baile", "rating": 649, "opRating": 350}, {"opponent": "oricorio_pom_pom", "rating": 649, "opRating": 350}, {"opponent": "oricorio_pau", "rating": 649, "opRating": 350}, {"opponent": "lugia", "rating": 561, "opRating": 438}, {"opponent": "elekid", "rating": 548, "opRating": 451}], "counters": [{"opponent": "ninetales", "rating": 123}, {"opponent": "hypno", "rating": 125}, {"opponent": "uxie", "rating": 165}, {"opponent": "castform_sunny", "rating": 191}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 292}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 4750}, {"moveId": "VOLT_SWITCH", "uses": 4698}, {"moveId": "SPARK", "uses": 2897}, {"moveId": "CHARM", "uses": 1565}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 4245}, {"moveId": "SURF", "uses": 2663}, {"moveId": "THUNDER_PUNCH", "uses": 2472}, {"moveId": "TRAILBLAZE", "uses": 1636}, {"moveId": "SKULL_BASH", "uses": 1189}, {"moveId": "BRICK_BREAK", "uses": 1023}, {"moveId": "THUNDER", "uses": 675}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SURF"], "score": 49.8}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 243, "matchups": [{"opponent": "oricorio_baile", "rating": 698, "opRating": 301}, {"opponent": "xatu", "rating": 698, "opRating": 301}, {"opponent": "tauros_blaze", "rating": 599, "opRating": 400}, {"opponent": "hat<PERSON><PERSON>", "rating": 586, "opRating": 413}, {"opponent": "lugia", "rating": 564, "opRating": 435}], "counters": [{"opponent": "grumpig", "rating": 109}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "ninetales", "rating": 162}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 165}, {"opponent": "castform_sunny", "rating": 321}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 9405}, {"moveId": "CHARGE_BEAM", "uses": 4495}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 5224}, {"moveId": "ANCIENT_POWER", "uses": 3892}, {"moveId": "THUNDERBOLT", "uses": 2667}, {"moveId": "THUNDER", "uses": 1182}, {"moveId": "ZAP_CANNON", "uses": 931}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 49.8}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 237, "matchups": [{"opponent": "lugia", "rating": 610, "opRating": 389}, {"opponent": "em<PERSON>ga", "rating": 605, "opRating": 394}, {"opponent": "<PERSON>ras", "rating": 567, "opRating": 432}, {"opponent": "wobbuffet", "rating": 542, "opRating": 457}, {"opponent": "fletchinder", "rating": 504, "opRating": 495}], "counters": [{"opponent": "hypno", "rating": 95}, {"opponent": "cresselia", "rating": 114}, {"opponent": "ninetales", "rating": 126}, {"opponent": "uxie", "rating": 137}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 7026}, {"moveId": "VOLT_SWITCH", "uses": 6874}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 7288}, {"moveId": "SHADOW_BALL", "uses": 4136}, {"moveId": "THUNDERBOLT", "uses": 1324}, {"moveId": "THUNDER", "uses": 1147}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SHADOW_BALL"], "score": 49}, {"speciesId": "drowzee", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 239, "matchups": [{"opponent": "pawmot", "rating": 778, "opRating": 221}, {"opponent": "unown", "rating": 690, "opRating": 309}, {"opponent": "pignite", "rating": 517, "opRating": 482}], "counters": [{"opponent": "incineroar", "rating": 61}, {"opponent": "talonflame", "rating": 148}, {"opponent": "castform_sunny", "rating": 157}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 176}, {"opponent": "ninetales", "rating": 218}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12167}, {"moveId": "POUND", "uses": 1733}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 8399}, {"moveId": "RETURN", "uses": 3264}, {"moveId": "PSYCHIC", "uses": 1411}, {"moveId": "PSYBEAM", "uses": 811}]}, "moveset": ["CONFUSION", "PSYSHOCK", "RETURN"], "score": 48.7}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 239, "matchups": [{"opponent": "oricorio_baile", "rating": 674, "opRating": 325}, {"opponent": "xatu", "rating": 674, "opRating": 325}, {"opponent": "oricorio_pom_pom", "rating": 645, "opRating": 354}, {"opponent": "swoobat", "rating": 538, "opRating": 461}, {"opponent": "fletchinder", "rating": 533, "opRating": 466}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 153}, {"opponent": "cresselia", "rating": 219}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 278}, {"opponent": "castform_sunny", "rating": 294}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 7156}, {"moveId": "THUNDER_SHOCK", "uses": 6744}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 5320}, {"moveId": "LAST_RESORT", "uses": 3434}, {"moveId": "THUNDERBOLT", "uses": 1973}, {"moveId": "THUNDER", "uses": 1727}, {"moveId": "ZAP_CANNON", "uses": 1438}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "LAST_RESORT"], "score": 48.3}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 222, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 574, "opRating": 425}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 560, "opRating": 439}, {"opponent": "darum<PERSON>", "rating": 537, "opRating": 462}, {"opponent": "ponyta", "rating": 532, "opRating": 467}, {"opponent": "raboot", "rating": 518, "opRating": 481}], "counters": [{"opponent": "talonflame", "rating": 81}, {"opponent": "grumpig", "rating": 83}, {"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 246}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 7450}, {"moveId": "FIRE_SPIN", "uses": 6450}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 4223}, {"moveId": "FIRE_PUNCH", "uses": 4150}, {"moveId": "THUNDERBOLT", "uses": 2180}, {"moveId": "BRICK_BREAK", "uses": 1465}, {"moveId": "PSYCHIC", "uses": 1188}, {"moveId": "FIRE_BLAST", "uses": 717}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 48.3}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 242, "matchups": [{"opponent": "zapdos", "rating": 675, "opRating": 324}, {"opponent": "oricorio_baile", "rating": 671, "opRating": 328}, {"opponent": "oricorio_pom_pom", "rating": 671, "opRating": 328}, {"opponent": "em<PERSON>ga", "rating": 560, "opRating": 439}, {"opponent": "lugia", "rating": 523, "opRating": 476}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "castform_sunny", "rating": 126}, {"opponent": "<PERSON>ras", "rating": 139}, {"opponent": "cresselia", "rating": 212}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 221}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12743}, {"moveId": "LOW_KICK", "uses": 1157}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 5680}, {"moveId": "THUNDER_PUNCH", "uses": 3287}, {"moveId": "ICE_PUNCH", "uses": 2434}, {"moveId": "FLAMETHROWER", "uses": 1602}, {"moveId": "THUNDER", "uses": 924}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 48.1}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 230, "matchups": [{"opponent": "unown", "rating": 705, "opRating": 294}], "counters": [{"opponent": "hypno", "rating": 125}, {"opponent": "<PERSON>ras", "rating": 145}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 172}, {"opponent": "castform_sunny", "rating": 222}, {"opponent": "ninetales", "rating": 226}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 7787}, {"moveId": "EXTRASENSORY", "uses": 6113}], "chargedMoves": [{"moveId": "SWIFT", "uses": 8718}, {"moveId": "FUTURE_SIGHT", "uses": 3304}, {"moveId": "FIRE_BLAST", "uses": 1886}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 47.6}, {"speciesId": "rotom_fan", "speciesName": "<PERSON><PERSON><PERSON> (Fan)", "rating": 230, "matchups": [{"opponent": "celebi", "rating": 612, "opRating": 387}, {"opponent": "braviary_hisuian", "rating": 596, "opRating": 403}, {"opponent": "unown", "rating": 586, "opRating": 413}, {"opponent": "oricorio_baile", "rating": 537, "opRating": 462}, {"opponent": "oricorio_pau", "rating": 537, "opRating": 462}], "counters": [{"opponent": "<PERSON>ras", "rating": 111}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 116}, {"opponent": "cresselia", "rating": 169}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 8913}, {"moveId": "AIR_SLASH", "uses": 4987}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 6254}, {"moveId": "OMINOUS_WIND", "uses": 4910}, {"moveId": "THUNDER", "uses": 2715}]}, "moveset": ["AIR_SLASH", "OMINOUS_WIND", "THUNDER"], "score": 47.2}, {"speciesId": "espeon", "speciesName": "Espeon", "rating": 227, "matchups": [{"opponent": "unown", "rating": 690, "opRating": 309}, {"opponent": "tauros_blaze", "rating": 520, "opRating": 479}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 130}, {"opponent": "<PERSON>ras", "rating": 145}, {"opponent": "hypno", "rating": 156}, {"opponent": "ninetales", "rating": 170}, {"opponent": "castform_sunny", "rating": 236}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12260}, {"moveId": "ZEN_HEADBUTT", "uses": 1640}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 4387}, {"moveId": "PSYCHIC_FANGS", "uses": 4331}, {"moveId": "LAST_RESORT", "uses": 2146}, {"moveId": "FUTURE_SIGHT", "uses": 1736}, {"moveId": "PSYCHIC", "uses": 834}, {"moveId": "PSYBEAM", "uses": 469}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "SHADOW_BALL"], "score": 47}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 229, "matchups": [{"opponent": "unown", "rating": 683, "opRating": 316}, {"opponent": "celebi", "rating": 664, "opRating": 335}, {"opponent": "tauros_blaze", "rating": 570, "opRating": 429}, {"opponent": "oricorio_baile", "rating": 515, "opRating": 484}, {"opponent": "oricorio_pau", "rating": 515, "opRating": 484}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 102}, {"opponent": "<PERSON>ras", "rating": 103}, {"opponent": "hypno", "rating": 146}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 11707}, {"moveId": "POUND", "uses": 2193}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 8732}, {"moveId": "AERIAL_ACE", "uses": 3309}, {"moveId": "HURRICANE", "uses": 1873}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 47}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 227, "matchups": [{"opponent": "slowpoke_galarian", "rating": 579, "opRating": 420}, {"opponent": "exeggcute", "rating": 552, "opRating": 447}, {"opponent": "drowzee", "rating": 539, "opRating": 460}, {"opponent": "celebi", "rating": 526, "opRating": 473}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 509, "opRating": 490}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 73}, {"opponent": "<PERSON>ras", "rating": 111}, {"opponent": "grumpig", "rating": 132}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 11603}, {"moveId": "ZEN_HEADBUTT", "uses": 2297}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 4800}, {"moveId": "FLY", "uses": 4090}, {"moveId": "OMINOUS_WIND", "uses": 1803}, {"moveId": "PSYCHIC", "uses": 1658}, {"moveId": "DAZZLING_GLEAM", "uses": 1574}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "FLY"], "score": 46.7}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 232, "matchups": [{"opponent": "flaaffy", "rating": 596, "opRating": 403}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 537, "opRating": 462}, {"opponent": "r<PERSON><PERSON>", "rating": 537, "opRating": 462}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 532, "opRating": 467}, {"opponent": "lugia", "rating": 505, "opRating": 494}], "counters": [{"opponent": "ninetales", "rating": 75}, {"opponent": "castform_sunny", "rating": 85}, {"opponent": "armarouge", "rating": 85}, {"opponent": "<PERSON>ras", "rating": 120}, {"opponent": "uxie", "rating": 137}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 7511}, {"moveId": "THUNDER_SHOCK", "uses": 6389}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 6254}, {"moveId": "OMINOUS_WIND", "uses": 4912}, {"moveId": "THUNDER", "uses": 2714}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 46.5}, {"speciesId": "jynx", "speciesName": "Jynx", "rating": 227, "matchups": [{"opponent": "unown", "rating": 727, "opRating": 272}], "counters": [{"opponent": "castform_sunny", "rating": 102}, {"opponent": "grumpig", "rating": 102}, {"opponent": "ninetales", "rating": 111}, {"opponent": "cresselia", "rating": 132}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 214}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 8215}, {"moveId": "FROST_BREATH", "uses": 5208}, {"moveId": "POUND", "uses": 475}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 4575}, {"moveId": "PSYSHOCK", "uses": 4352}, {"moveId": "ICE_PUNCH", "uses": 3283}, {"moveId": "FOCUS_BLAST", "uses": 1056}, {"moveId": "DRAINING_KISS", "uses": 649}]}, "moveset": ["CONFUSION", "AVALANCHE", "PSYSHOCK"], "score": 46.3}, {"speciesId": "xatu", "speciesName": "Xatu", "rating": 228, "matchups": [{"opponent": "unown", "rating": 747, "opRating": 252}, {"opponent": "exeggutor", "rating": 684, "opRating": 315}, {"opponent": "tauros_blaze", "rating": 630, "opRating": 369}, {"opponent": "gallade", "rating": 605, "opRating": 394}, {"opponent": "exeggcute", "rating": 546, "opRating": 453}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 73}, {"opponent": "charjabug", "rating": 111}, {"opponent": "<PERSON>ras", "rating": 125}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 7074}, {"moveId": "AIR_SLASH", "uses": 6826}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 5512}, {"moveId": "OMINOUS_WIND", "uses": 3080}, {"moveId": "FUTURE_SIGHT", "uses": 2836}, {"moveId": "RETURN", "uses": 2485}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "FUTURE_SIGHT"], "score": 46.1}, {"speciesId": "luxio", "speciesName": "Luxio", "rating": 223, "matchups": [{"opponent": "oricorio_baile", "rating": 633, "opRating": 366}, {"opponent": "oricorio_pom_pom", "rating": 633, "opRating": 366}, {"opponent": "oricorio_pau", "rating": 633, "opRating": 366}, {"opponent": "elekid", "rating": 532, "opRating": 467}, {"opponent": "lugia", "rating": 517, "opRating": 482}], "counters": [{"opponent": "ninetales", "rating": 115}, {"opponent": "hypno", "rating": 125}, {"opponent": "cresselia", "rating": 151}, {"opponent": "castform_sunny", "rating": 174}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 341}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 8854}, {"moveId": "BITE", "uses": 5046}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 6824}, {"moveId": "CRUNCH", "uses": 4190}, {"moveId": "RETURN", "uses": 1644}, {"moveId": "THUNDERBOLT", "uses": 1241}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 45.4}, {"speciesId": "sigilyph", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 222, "matchups": [{"opponent": "unown", "rating": 809, "opRating": 190}, {"opponent": "celebi", "rating": 648, "opRating": 351}, {"opponent": "tauros_blaze", "rating": 639, "opRating": 360}, {"opponent": "gallade", "rating": 610, "opRating": 389}, {"opponent": "exeggcute", "rating": 550, "opRating": 449}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 73}, {"opponent": "<PERSON>ras", "rating": 92}, {"opponent": "hypno", "rating": 146}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 10956}, {"moveId": "ZEN_HEADBUTT", "uses": 2944}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 5485}, {"moveId": "ANCIENT_POWER", "uses": 4529}, {"moveId": "SIGNAL_BEAM", "uses": 2597}, {"moveId": "PSYBEAM", "uses": 1280}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "ANCIENT_POWER"], "score": 45}, {"speciesId": "oricorio_pau", "speciesName": "Oricorio (Pa'u)", "rating": 221, "matchups": [{"opponent": "unown", "rating": 789, "opRating": 210}, {"opponent": "celebi", "rating": 664, "opRating": 335}, {"opponent": "tauros_blaze", "rating": 625, "opRating": 375}, {"opponent": "gallade", "rating": 609, "opRating": 390}, {"opponent": "exeggcute", "rating": 566, "opRating": 433}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 73}, {"opponent": "<PERSON>ras", "rating": 103}, {"opponent": "hypno", "rating": 146}, {"opponent": "castform_sunny", "rating": 195}, {"opponent": "ninetales", "rating": 198}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 11698}, {"moveId": "POUND", "uses": 2202}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 8733}, {"moveId": "AERIAL_ACE", "uses": 3308}, {"moveId": "HURRICANE", "uses": 1873}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 44.8}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 211, "matchups": [{"opponent": "oricorio_baile", "rating": 665, "opRating": 334}, {"opponent": "oricorio_pau", "rating": 665, "opRating": 334}, {"opponent": "xatu", "rating": 633, "opRating": 366}, {"opponent": "oricorio_pom_pom", "rating": 600, "opRating": 399}, {"opponent": "hat<PERSON><PERSON>", "rating": 504, "opRating": 495}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 73}, {"opponent": "grumpig", "rating": 109}, {"opponent": "castform_sunny", "rating": 126}, {"opponent": "<PERSON>ras", "rating": 139}, {"opponent": "ninetales", "rating": 146}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 7089}, {"moveId": "THUNDER_SHOCK", "uses": 6811}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 5240}, {"moveId": "THUNDER_PUNCH", "uses": 5088}, {"moveId": "BRICK_BREAK", "uses": 2174}, {"moveId": "THUNDER", "uses": 1396}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "CRUNCH"], "score": 43.9}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 209, "matchups": [{"opponent": "spoink", "rating": 677, "opRating": 322}, {"opponent": "unown", "rating": 596, "opRating": 403}], "counters": [{"opponent": "ninetales", "rating": 126}, {"opponent": "cresselia", "rating": 163}, {"opponent": "castform_sunny", "rating": 174}, {"opponent": "<PERSON>ras", "rating": 178}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 285}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 12144}, {"moveId": "LOW_KICK", "uses": 1756}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 6694}, {"moveId": "DISCHARGE", "uses": 3274}, {"moveId": "FLAME_CHARGE", "uses": 2334}, {"moveId": "RETURN", "uses": 1588}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 43}, {"speciesId": "plusle", "speciesName": "<PERSON><PERSON>", "rating": 207, "matchups": [{"opponent": "rotom_fan", "rating": 645, "opRating": 354}, {"opponent": "elekid", "rating": 566, "opRating": 433}, {"opponent": "oricorio_baile", "rating": 527, "opRating": 472}, {"opponent": "oricorio_pom_pom", "rating": 527, "opRating": 472}, {"opponent": "oricorio_pau", "rating": 527, "opRating": 472}], "counters": [{"opponent": "hypno", "rating": 95}, {"opponent": "cresselia", "rating": 114}, {"opponent": "ninetales", "rating": 115}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 362}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 7537}, {"moveId": "SPARK", "uses": 6363}], "chargedMoves": [{"moveId": "SWIFT", "uses": 5613}, {"moveId": "DISCHARGE", "uses": 4265}, {"moveId": "GRASS_KNOT", "uses": 2429}, {"moveId": "THUNDERBOLT", "uses": 1576}]}, "moveset": ["QUICK_ATTACK", "THUNDERBOLT", "GRASS_KNOT"], "score": 41.7}, {"speciesId": "ponyta_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 199, "matchups": [{"opponent": "unown", "rating": 793, "opRating": 206}, {"opponent": "tauros_blaze", "rating": 586, "opRating": 413}], "counters": [{"opponent": "grumpig", "rating": 56}, {"opponent": "cresselia", "rating": 163}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 176}, {"opponent": "castform_sunny", "rating": 232}, {"opponent": "ninetales", "rating": 253}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 11772}, {"moveId": "LOW_KICK", "uses": 2128}], "chargedMoves": [{"moveId": "SWIFT", "uses": 9416}, {"moveId": "PLAY_ROUGH", "uses": 2444}, {"moveId": "PSYBEAM", "uses": 2051}]}, "moveset": ["PSYCHO_CUT", "SWIFT", "PLAY_ROUGH"], "score": 40.6}, {"speciesId": "musharna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 202, "matchups": [{"opponent": "unown", "rating": 776, "opRating": 223}, {"opponent": "combusken", "rating": 531, "opRating": 468}], "counters": [{"opponent": "charjabug", "rating": 59}, {"opponent": "ninetales", "rating": 99}, {"opponent": "castform_sunny", "rating": 113}, {"opponent": "<PERSON>ras", "rating": 136}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 207}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 10146}, {"moveId": "ZEN_HEADBUTT", "uses": 3754}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 8748}, {"moveId": "DAZZLING_GLEAM", "uses": 3627}, {"moveId": "FUTURE_SIGHT", "uses": 1561}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 40.3}, {"speciesId": "pawmo", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 195, "matchups": [{"opponent": "spoink", "rating": 577, "opRating": 422}, {"opponent": "sigilyph", "rating": 545, "opRating": 454}, {"opponent": "rotom_fan", "rating": 538, "opRating": 461}, {"opponent": "oricorio_baile", "rating": 503, "opRating": 496}, {"opponent": "oricorio_pau", "rating": 503, "opRating": 496}], "counters": [{"opponent": "cresselia", "rating": 58}, {"opponent": "uxie", "rating": 100}, {"opponent": "<PERSON>ras", "rating": 103}, {"opponent": "ninetales", "rating": 115}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 186}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 6707}, {"moveId": "SPARK", "uses": 4119}, {"moveId": "CHARGE_BEAM", "uses": 3078}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 6928}, {"moveId": "THUNDER_PUNCH", "uses": 4027}, {"moveId": "DISCHARGE", "uses": 1675}, {"moveId": "THUNDERBOLT", "uses": 1251}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 40.3}, {"speciesId": "duosion", "speciesName": "Duosion", "rating": 195, "matchups": [{"opponent": "unown", "rating": 686, "opRating": 313}], "counters": [{"opponent": "grumpig", "rating": 94}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 126}, {"opponent": "cresselia", "rating": 126}, {"opponent": "castform_sunny", "rating": 157}, {"opponent": "ninetales", "rating": 162}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ROCK", "uses": 1191}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1166}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1101}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1041}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1034}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 937}, {"moveId": "HIDDEN_POWER_BUG", "uses": 862}, {"moveId": "HIDDEN_POWER_POISON", "uses": 830}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 784}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 777}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 766}, {"moveId": "HIDDEN_POWER_ICE", "uses": 731}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 707}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 582}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 575}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 573}, {"moveId": "ZEN_HEADBUTT", "uses": 272}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 5491}, {"moveId": "NIGHT_SHADE", "uses": 4772}, {"moveId": "THUNDER", "uses": 1883}, {"moveId": "RETURN", "uses": 1750}]}, "moveset": ["HIDDEN_POWER_PSYCHIC", "PSYSHOCK", "NIGHT_SHADE"], "score": 39.7}, {"speciesId": "solosis", "speciesName": "<PERSON><PERSON>", "rating": 194, "matchups": [{"opponent": "unown", "rating": 673, "opRating": 326}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 126}, {"opponent": "cresselia", "rating": 126}, {"opponent": "<PERSON>ras", "rating": 139}, {"opponent": "castform_sunny", "rating": 157}, {"opponent": "ninetales", "rating": 162}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ROCK", "uses": 1191}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1168}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1096}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1043}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1031}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 939}, {"moveId": "HIDDEN_POWER_BUG", "uses": 865}, {"moveId": "HIDDEN_POWER_POISON", "uses": 831}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 786}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 779}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 773}, {"moveId": "HIDDEN_POWER_ICE", "uses": 728}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 709}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 583}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 579}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 574}, {"moveId": "ZEN_HEADBUTT", "uses": 265}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 5490}, {"moveId": "NIGHT_SHADE", "uses": 4778}, {"moveId": "THUNDER", "uses": 1882}, {"moveId": "RETURN", "uses": 1750}]}, "moveset": ["HIDDEN_POWER_PSYCHIC", "PSYSHOCK", "NIGHT_SHADE"], "score": 39.2}, {"speciesId": "spoink", "speciesName": "S<PERSON>ink", "rating": 194, "matchups": [{"opponent": "unown", "rating": 570, "opRating": 429}], "counters": [{"opponent": "hypno", "rating": 108}, {"opponent": "<PERSON>ras", "rating": 156}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 158}, {"opponent": "ninetales", "rating": 166}, {"opponent": "castform_sunny", "rating": 167}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 10829}, {"moveId": "SPLASH", "uses": 3071}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 7409}, {"moveId": "RETURN", "uses": 3194}, {"moveId": "MIRROR_COAT", "uses": 2236}, {"moveId": "PSYBEAM", "uses": 1071}]}, "moveset": ["ZEN_HEADBUTT", "SHADOW_BALL", "RETURN"], "score": 39.2}, {"speciesId": "pawmot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 188, "matchups": [{"opponent": "spoink", "rating": 593, "opRating": 406}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 558, "opRating": 441}, {"opponent": "unown", "rating": 531, "opRating": 468}, {"opponent": "rotom_fan", "rating": 508, "opRating": 491}], "counters": [{"opponent": "cresselia", "rating": 64}, {"opponent": "hypno", "rating": 108}, {"opponent": "ninetales", "rating": 123}, {"opponent": "castform_sunny", "rating": 140}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 352}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 6781}, {"moveId": "SPARK", "uses": 3721}, {"moveId": "CHARGE_BEAM", "uses": 2786}, {"moveId": "LOW_KICK", "uses": 621}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 4477}, {"moveId": "CLOSE_COMBAT", "uses": 3467}, {"moveId": "THUNDER_PUNCH", "uses": 2599}, {"moveId": "BRICK_BREAK", "uses": 1406}, {"moveId": "DISCHARGE", "uses": 1091}, {"moveId": "THUNDERBOLT", "uses": 791}]}, "moveset": ["THUNDER_SHOCK", "BRICK_BREAK", "WILD_CHARGE"], "score": 38.6}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 187, "matchups": [{"opponent": "oricorio_baile", "rating": 672, "opRating": 327}, {"opponent": "oricorio_pom_pom", "rating": 672, "opRating": 327}, {"opponent": "oricorio_pau", "rating": 672, "opRating": 327}, {"opponent": "rotom_fan", "rating": 666, "opRating": 333}, {"opponent": "lugia", "rating": 559, "opRating": 440}], "counters": [{"opponent": "ninetales", "rating": 83}, {"opponent": "armarouge", "rating": 85}, {"opponent": "grumpig", "rating": 90}, {"opponent": "castform_sunny", "rating": 95}, {"opponent": "cresselia", "rating": 114}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 7285}, {"moveId": "THUNDER_SHOCK", "uses": 6615}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 6485}, {"moveId": "BLIZZARD", "uses": 4612}, {"moveId": "THUNDER", "uses": 2814}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 37.7}, {"speciesId": "smoochum", "speciesName": "Smoochum", "rating": 192, "matchups": [{"opponent": "unown", "rating": 795, "opRating": 204}, {"opponent": "deoxys_speed", "rating": 587, "opRating": 412}, {"opponent": "munna", "rating": 558, "opRating": 441}, {"opponent": "exeggcute", "rating": 512, "opRating": 487}], "counters": [{"opponent": "castform_sunny", "rating": 54}, {"opponent": "ninetales", "rating": 63}, {"opponent": "armarouge", "rating": 89}, {"opponent": "hypno", "rating": 125}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 144}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 9057}, {"moveId": "FROST_BREATH", "uses": 4238}, {"moveId": "POUND", "uses": 610}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 6690}, {"moveId": "ICE_PUNCH", "uses": 5499}, {"moveId": "ICE_BEAM", "uses": 1741}]}, "moveset": ["POWDER_SNOW", "PSYSHOCK", "ICE_PUNCH"], "score": 36.8}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 168, "matchups": [{"opponent": "<PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "flaaffy", "rating": 610, "opRating": 389}, {"opponent": "magmar", "rating": 605, "opRating": 394}, {"opponent": "tauros_blaze", "rating": 576, "opRating": 423}, {"opponent": "elekid", "rating": 576, "opRating": 423}], "counters": [{"opponent": "cresselia", "rating": 40}, {"opponent": "grumpig", "rating": 56}, {"opponent": "talonflame", "rating": 81}, {"opponent": "ninetales", "rating": 214}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 239}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 5875}, {"moveId": "CONFUSION", "uses": 4744}, {"moveId": "CHARM", "uses": 2414}, {"moveId": "LOW_KICK", "uses": 872}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 5168}, {"moveId": "CLOSE_COMBAT", "uses": 5033}, {"moveId": "SYNCHRONOISE", "uses": 2811}, {"moveId": "PSYCHIC", "uses": 859}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 36.4}, {"speciesId": "unown", "speciesName": "Unown", "rating": 176, "matchups": [], "counters": [{"opponent": "hypno", "rating": 95}, {"opponent": "cresselia", "rating": 98}, {"opponent": "ninetales", "rating": 138}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 147}, {"opponent": "castform_sunny", "rating": 191}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ROCK", "uses": 1169}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1106}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1073}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1032}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1022}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 914}, {"moveId": "HIDDEN_POWER_BUG", "uses": 883}, {"moveId": "HIDDEN_POWER_POISON", "uses": 855}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 812}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 811}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 802}, {"moveId": "HIDDEN_POWER_ICE", "uses": 770}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 755}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 642}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 636}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 633}], "chargedMoves": [{"moveId": "STRUGGLE", "uses": 13900}]}, "moveset": ["HIDDEN_POWER_PSYCHIC", "STRUGGLE"], "score": 36.2}, {"speciesId": "reuniclus", "speciesName": "Reuniclus", "rating": 166, "matchups": [{"opponent": "unown", "rating": 734, "opRating": 265}], "counters": [{"opponent": "grumpig", "rating": 75}, {"opponent": "cresselia", "rating": 86}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 144}, {"opponent": "ninetales", "rating": 170}, {"opponent": "castform_sunny", "rating": 171}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ROCK", "uses": 1196}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1144}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1109}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1043}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1035}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 921}, {"moveId": "HIDDEN_POWER_BUG", "uses": 870}, {"moveId": "HIDDEN_POWER_POISON", "uses": 823}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 772}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 771}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 765}, {"moveId": "HIDDEN_POWER_ICE", "uses": 736}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 712}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 578}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 574}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 574}, {"moveId": "ZEN_HEADBUTT", "uses": 272}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 5863}, {"moveId": "SHADOW_BALL", "uses": 4901}, {"moveId": "THUNDER", "uses": 2103}, {"moveId": "FUTURE_SIGHT", "uses": 1033}]}, "moveset": ["HIDDEN_POWER_FIGHTING", "PSYSHOCK", "SHADOW_BALL"], "score": 33.9}, {"speciesId": "articuno_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 175, "matchups": [{"opponent": "tauros_blaze", "rating": 644, "opRating": 355}, {"opponent": "espurr", "rating": 605, "opRating": 394}, {"opponent": "vikavolt", "rating": 583, "opRating": 416}, {"opponent": "celebi", "rating": 517, "opRating": 482}, {"opponent": "wobbuffet", "rating": 508, "opRating": 491}], "counters": [{"opponent": "p<PERSON><PERSON><PERSON>", "rating": 52}, {"opponent": "<PERSON>ras", "rating": 61}, {"opponent": "hypno", "rating": 64}, {"opponent": "castform_sunny", "rating": 106}, {"opponent": "ninetales", "rating": 123}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 8003}, {"moveId": "CONFUSION", "uses": 5897}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 4764}, {"moveId": "FLY", "uses": 4061}, {"moveId": "ANCIENT_POWER", "uses": 3425}, {"moveId": "FUTURE_SIGHT", "uses": 1663}]}, "moveset": ["PSYCHO_CUT", "FLY", "BRAVE_BIRD"], "score": 33.1}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 159, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 625, "opRating": 374}, {"opponent": "elekid", "rating": 557, "opRating": 442}, {"opponent": "luxray", "rating": 538, "opRating": 461}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 526, "opRating": 473}, {"opponent": "flaaffy", "rating": 503, "opRating": 496}], "counters": [{"opponent": "castform_sunny", "rating": 30}, {"opponent": "ninetales", "rating": 35}, {"opponent": "hypno", "rating": 85}, {"opponent": "<PERSON>ras", "rating": 92}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 338}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 4776}, {"moveId": "BULLET_SEED", "uses": 4313}, {"moveId": "EXTRASENSORY", "uses": 3665}, {"moveId": "ZEN_HEADBUTT", "uses": 1160}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 6568}, {"moveId": "PSYCHIC", "uses": 5392}, {"moveId": "SOLAR_BEAM", "uses": 1928}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 31.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 148, "matchups": [{"opponent": "spoink", "rating": 780, "opRating": 219}, {"opponent": "unown", "rating": 736, "opRating": 263}, {"opponent": "drowzee", "rating": 642, "opRating": 357}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 527, "opRating": 472}, {"opponent": "munna", "rating": 505, "opRating": 494}], "counters": [{"opponent": "hypno", "rating": 64}, {"opponent": "ninetales", "rating": 75}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 77}, {"opponent": "cresselia", "rating": 77}, {"opponent": "castform_sunny", "rating": 99}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 5765}, {"moveId": "CONFUSION", "uses": 4663}, {"moveId": "COUNTER", "uses": 3463}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 4184}, {"moveId": "FIRE_PUNCH", "uses": 2921}, {"moveId": "FUTURE_SIGHT", "uses": 1940}, {"moveId": "PSYCHIC", "uses": 1865}, {"moveId": "DAZZLING_GLEAM", "uses": 1725}, {"moveId": "FOCUS_BLAST", "uses": 1263}]}, "moveset": ["PSYCHO_CUT", "FIRE_PUNCH", "SHADOW_BALL"], "score": 27.1}, {"speciesId": "munna", "speciesName": "<PERSON><PERSON>", "rating": 131, "matchups": [{"opponent": "unown", "rating": 626, "opRating": 373}], "counters": [{"opponent": "castform_sunny", "rating": 75}, {"opponent": "hypno", "rating": 85}, {"opponent": "ninetales", "rating": 87}, {"opponent": "<PERSON>ras", "rating": 114}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 190}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 10071}, {"moveId": "ZEN_HEADBUTT", "uses": 3829}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 9233}, {"moveId": "DAZZLING_GLEAM", "uses": 3758}, {"moveId": "PSYBEAM", "uses": 900}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 27.1}, {"speciesId": "deoxys_speed", "speciesName": "<PERSON><PERSON><PERSON> (Speed)", "rating": 126, "matchups": [{"opponent": "unown", "rating": 651, "opRating": 348}, {"opponent": "tauros_blaze", "rating": 534, "opRating": 465}], "counters": [{"opponent": "ninetales", "rating": 83}, {"opponent": "castform_sunny", "rating": 85}, {"opponent": "hypno", "rating": 85}, {"opponent": "cresselia", "rating": 175}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 225}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 10857}, {"moveId": "ZEN_HEADBUTT", "uses": 3043}], "chargedMoves": [{"moveId": "SWIFT", "uses": 6286}, {"moveId": "PSYCHO_BOOST", "uses": 4927}, {"moveId": "THUNDERBOLT", "uses": 2691}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "SWIFT"], "score": 26}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Regieleki", "rating": 122, "matchups": [{"opponent": "spoink", "rating": 625, "opRating": 375}, {"opponent": "sigilyph", "rating": 620, "opRating": 379}, {"opponent": "oricorio_baile", "rating": 583, "opRating": 416}, {"opponent": "oricorio_pom_pom", "rating": 583, "opRating": 416}, {"opponent": "oricorio_pau", "rating": 583, "opRating": 416}], "counters": [{"opponent": "<PERSON>ras", "rating": 41}, {"opponent": "castform_sunny", "rating": 58}, {"opponent": "ninetales", "rating": 67}, {"opponent": "hypno", "rating": 71}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 91}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 5081}, {"moveId": "THUNDER_SHOCK", "uses": 4875}, {"moveId": "LOCK_ON", "uses": 3941}], "chargedMoves": [{"moveId": "THUNDER_CAGE", "uses": 9019}, {"moveId": "HYPER_BEAM", "uses": 2019}, {"moveId": "THUNDER", "uses": 1607}, {"moveId": "ZAP_CANNON", "uses": 1325}]}, "moveset": ["LOCK_ON", "THUNDER", "HYPER_BEAM"], "score": 22.5}, {"speciesId": "kadabra", "speciesName": "Kadabra", "rating": 114, "matchups": [{"opponent": "spoink", "rating": 706, "opRating": 293}, {"opponent": "unown", "rating": 657, "opRating": 342}], "counters": [{"opponent": "cresselia", "rating": 64}, {"opponent": "<PERSON>ras", "rating": 67}, {"opponent": "p<PERSON><PERSON><PERSON>", "rating": 77}, {"opponent": "ninetales", "rating": 83}, {"opponent": "castform_sunny", "rating": 95}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 7530}, {"moveId": "CONFUSION", "uses": 6370}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 6415}, {"moveId": "DAZZLING_GLEAM", "uses": 2803}, {"moveId": "RETURN", "uses": 2759}, {"moveId": "PSYBEAM", "uses": 1925}]}, "moveset": ["PSYCHO_CUT", "SHADOW_BALL", "DAZZLING_GLEAM"], "score": 22}]