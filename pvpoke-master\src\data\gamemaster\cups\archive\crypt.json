{"name": "crypt", "title": "Devon Crypt Cup", "restrictedPicks": 1, "restrictedPokemon": ["lokix", "sableye", "sableye_shadow", "weezing_galarian", "marowak_alolan", "marowak_alolan_shadow", "run<PERSON><PERSON>", "<PERSON>ras", "lap<PERSON>_shadow", "jellicent", "jellicent_shadow", "vespiquen"], "include": [{"filterType": "type", "name": "Type", "values": ["bug", "fairy", "ghost", "ground"]}, {"filterType": "id", "name": "Species", "values": ["ninjask", "yanma", "cacturne", "cacturne_shadow", "amoon<PERSON>s", "amoon<PERSON><PERSON>_shadow", "magmar", "magmar_shadow", "magmortar", "magmortar_shadow", "<PERSON>ras", "lap<PERSON>_shadow", "vespiquen", "marowak_alolan_shadow", "marowak_alolan", "jellicent_shadow", "jellicent"]}], "exclude": [{"filterType": "type", "name": "Type", "values": ["flying", "normal", "steel", "water", "fire"]}, {"filterType": "id", "name": "Species", "values": ["leavanny", "clodsire", "<PERSON><PERSON><PERSON><PERSON>", "carbink", "whimsicott", "whiscash", "gastrodon", "quagsire", "swampert", "ninetales_alolan", "ninetales_alolan_shadow", "tapu_fini", "primarina"]}, {"filterType": "tag", "name": "Tag", "values": ["mega"]}], "levelCap": 50, "includeLowStatProduct": true}