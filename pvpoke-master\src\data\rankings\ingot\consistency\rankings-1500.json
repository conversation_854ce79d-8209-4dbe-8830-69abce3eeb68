[{"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 694, "matchups": [{"opponent": "magmortar", "rating": 717, "opRating": 282}, {"opponent": "nidoking", "rating": 713, "opRating": 286}, {"opponent": "nidoqueen", "rating": 655, "opRating": 344}, {"opponent": "magby", "rating": 585, "opRating": 414}, {"opponent": "steelix", "rating": 519, "opRating": 480}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "raticate_alolan", "rating": 125}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 14454}, {"moveId": "CONFUSION", "uses": 10546}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 16750}, {"moveId": "PSYCHIC", "uses": 3349}, {"moveId": "OVERHEAT", "uses": 1956}, {"moveId": "FOCUS_BLAST", "uses": 2984}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 100, "stats": {"product": 1855, "atk": 120.4, "def": 119.3, "hp": 129}}, {"speciesId": "magby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 726, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 883, "opRating": 116}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 754, "opRating": 245}, {"opponent": "tyrunt", "rating": 645, "opRating": 354}, {"opponent": "umbreon", "rating": 629, "opRating": 370}, {"opponent": "arctibax", "rating": 600, "opRating": 400}], "counters": [{"opponent": "swalot", "rating": 267}, {"opponent": "toxapex", "rating": 288}, {"opponent": "magmar", "rating": 375}, {"opponent": "nidoking", "rating": 393}, {"opponent": "stunfisk_galarian", "rating": 440}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 14545}, {"moveId": "EMBER", "uses": 10455}], "chargedMoves": [{"moveId": "FLAME_BURST", "uses": 2333}, {"moveId": "FLAMETHROWER", "uses": 3837}, {"moveId": "FIRE_PUNCH", "uses": 12200}, {"moveId": "BRICK_BREAK", "uses": 6630}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "BRICK_BREAK"], "score": 100, "stats": {"product": 1603, "atk": 139.4, "def": 95.7, "hp": 120}}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 726, "matchups": [{"opponent": "talonflame", "rating": 736, "opRating": 264}, {"opponent": "rapidash", "rating": 716, "opRating": 284}, {"opponent": "araquanid", "rating": 548, "opRating": 452}, {"opponent": "steelix", "rating": 524, "opRating": 476}, {"opponent": "dedenne", "rating": 524, "opRating": 476}], "counters": [{"opponent": "magby", "rating": 179}, {"opponent": "magmar", "rating": 236}, {"opponent": "magmortar", "rating": 245}, {"opponent": "toxapex", "rating": 266}, {"opponent": "nidoking", "rating": 334}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 1196}, {"moveId": "INCINERATE", "uses": 12620}, {"moveId": "FIRE_FANG", "uses": 6113}, {"moveId": "EMBER", "uses": 5071}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 1976}, {"moveId": "OVERHEAT", "uses": 7427}, {"moveId": "FLAME_CHARGE", "uses": 9625}, {"moveId": "DARK_PULSE", "uses": 5977}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "DARK_PULSE"], "score": 100, "stats": {"product": 1653, "atk": 135, "def": 97.9, "hp": 125}}, {"speciesId": "darum<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 695, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 766, "opRating": 233}, {"opponent": "magby", "rating": 574, "opRating": 425}, {"opponent": "steelix", "rating": 571, "opRating": 428}, {"opponent": "dedenne", "rating": 519, "opRating": 480}, {"opponent": "munchlax", "rating": 512, "opRating": 487}], "counters": [{"opponent": "tyrunt", "rating": 189}, {"opponent": "magmar", "rating": 227}, {"opponent": "magmortar", "rating": 259}, {"opponent": "toxapex", "rating": 271}, {"opponent": "stunfisk_galarian", "rating": 393}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 8824}, {"moveId": "FIRE_FANG", "uses": 16176}], "chargedMoves": [{"moveId": "RETURN", "uses": 4900}, {"moveId": "FLAME_CHARGE", "uses": 5510}, {"moveId": "FIRE_PUNCH", "uses": 14608}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 100, "stats": {"product": 1698, "atk": 131.9, "def": 83.5, "hp": 154}}, {"speciesId": "avalugg_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 685, "matchups": [{"opponent": "nidoqueen", "rating": 847, "opRating": 152}, {"opponent": "arctibax", "rating": 685, "opRating": 314}, {"opponent": "tyrunt", "rating": 566, "opRating": 433}, {"opponent": "umbreon", "rating": 524, "opRating": 475}, {"opponent": "steelix", "rating": 516, "opRating": 483}], "counters": [{"opponent": "heatran", "rating": 113}, {"opponent": "magby", "rating": 116}, {"opponent": "magmar", "rating": 138}, {"opponent": "magmortar", "rating": 157}, {"opponent": "stunfisk_galarian", "rating": 408}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 7397}, {"moveId": "POWDER_SNOW", "uses": 12964}, {"moveId": "BITE", "uses": 4641}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 8782}, {"moveId": "ICY_WIND", "uses": 7302}, {"moveId": "CRUNCH", "uses": 5640}, {"moveId": "BLIZZARD", "uses": 3256}]}, "moveset": ["POWDER_SNOW", "ROCK_SLIDE", "ICY_WIND"], "score": 100, "stats": {"product": 1907, "atk": 117, "def": 134.6, "hp": 121}}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 661, "matchups": [{"opponent": "umbreon", "rating": 757, "opRating": 242}, {"opponent": "avalugg_his<PERSON>an", "rating": 666, "opRating": 333}, {"opponent": "steelix", "rating": 612, "opRating": 387}, {"opponent": "stunfisk_galarian", "rating": 583, "opRating": 416}, {"opponent": "tyrunt", "rating": 532, "opRating": 467}], "counters": [{"opponent": "weezing_galarian", "rating": 100}, {"opponent": "scolipede", "rating": 119}, {"opponent": "beedrill", "rating": 143}, {"opponent": "nidoking", "rating": 192}, {"opponent": "magmar", "rating": 272}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 10991}, {"moveId": "COUNTER", "uses": 14009}], "chargedMoves": [{"moveId": "OBSTRUCT", "uses": 250}, {"moveId": "NIGHT_SLASH", "uses": 10860}, {"moveId": "HYPER_BEAM", "uses": 2994}, {"moveId": "GUNK_SHOT", "uses": 1741}, {"moveId": "CROSS_CHOP", "uses": 9196}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "CROSS_CHOP"], "score": 100, "stats": {"product": 1970, "atk": 112.6, "def": 126.7, "hp": 138}}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 674, "matchups": [{"opponent": "toxapex", "rating": 763, "opRating": 236}, {"opponent": "avalugg_his<PERSON>an", "rating": 740, "opRating": 259}, {"opponent": "ninetales", "rating": 717, "opRating": 282}, {"opponent": "araquanid", "rating": 606, "opRating": 393}, {"opponent": "magmortar", "rating": 592, "opRating": 407}], "counters": [{"opponent": "<PERSON><PERSON>", "rating": 122}, {"opponent": "magmar", "rating": 183}, {"opponent": "ivysaur", "rating": 223}, {"opponent": "swalot", "rating": 252}, {"opponent": "stunfisk_galarian", "rating": 378}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 9659}, {"moveId": "THUNDER_SHOCK", "uses": 9874}, {"moveId": "SPARK", "uses": 5469}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10213}, {"moveId": "TRAILBLAZE", "uses": 3635}, {"moveId": "THUNDER_PUNCH", "uses": 5930}, {"moveId": "PSYCHIC", "uses": 3798}, {"moveId": "GRASS_KNOT", "uses": 1443}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "TRAILBLAZE"], "score": 100, "stats": {"product": 1644, "atk": 136.2, "def": 111.7, "hp": 108}}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622, "matchups": [{"opponent": "stunfisk_galarian", "rating": 739, "opRating": 260}, {"opponent": "avalugg_his<PERSON>an", "rating": 717, "opRating": 282}, {"opponent": "toxapex", "rating": 688, "opRating": 311}, {"opponent": "tyrunt", "rating": 606, "opRating": 393}, {"opponent": "steelix", "rating": 504, "opRating": 495}], "counters": [{"opponent": "litleo", "rating": 87}, {"opponent": "electrode_hisuian", "rating": 176}, {"opponent": "melmetal", "rating": 177}, {"opponent": "magmar", "rating": 205}, {"opponent": "nidoking", "rating": 251}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 15736}, {"moveId": "METAL_CLAW", "uses": 9264}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 3507}, {"moveId": "PLAY_ROUGH", "uses": 1953}, {"moveId": "IRON_HEAD", "uses": 3492}, {"moveId": "FOUL_PLAY", "uses": 5438}, {"moveId": "CLOSE_COMBAT", "uses": 10617}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "TRAILBLAZE"], "score": 100, "stats": {"product": 1728, "atk": 128.8, "def": 114.6, "hp": 117}}, {"speciesId": "toxtricity", "speciesName": "Toxtricity", "rating": 620, "matchups": [{"opponent": "toxapex", "rating": 777, "opRating": 222}, {"opponent": "araquanid", "rating": 768, "opRating": 231}, {"opponent": "dedenne", "rating": 659, "opRating": 340}, {"opponent": "weezing_galarian", "rating": 655, "opRating": 344}, {"opponent": "moltres_galarian", "rating": 558, "opRating": 441}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "nidoking", "rating": 133}, {"opponent": "sandslash_alolan", "rating": 137}, {"opponent": "nidoqueen", "rating": 154}, {"opponent": "steelix", "rating": 197}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 9532}, {"moveId": "POISON_JAB", "uses": 11657}, {"moveId": "ACID", "uses": 3837}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 16124}, {"moveId": "POWER_UP_PUNCH", "uses": 3639}, {"moveId": "DISCHARGE", "uses": 3432}, {"moveId": "ACID_SPRAY", "uses": 1835}]}, "moveset": ["POISON_JAB", "WILD_CHARGE", "DISCHARGE"], "score": 100, "stats": {"product": 1580, "atk": 140.7, "def": 94.2, "hp": 119}}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 612, "matchups": [{"opponent": "dedenne", "rating": 825, "opRating": 175}, {"opponent": "araquanid", "rating": 678, "opRating": 321}, {"opponent": "magby", "rating": 653, "opRating": 346}, {"opponent": "magmortar", "rating": 650, "opRating": 350}, {"opponent": "toxapex", "rating": 575, "opRating": 425}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "nidoking", "rating": 98}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}, {"opponent": "stunfisk_galarian", "rating": 251}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13359}, {"moveId": "POISON_JAB", "uses": 11641}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 5227}, {"moveId": "RETURN", "uses": 2924}, {"moveId": "ICE_BEAM", "uses": 4327}, {"moveId": "HORN_ATTACK", "uses": 4627}, {"moveId": "DIG", "uses": 7849}]}, "moveset": ["POISON_STING", "SLUDGE_BOMB", "DIG"], "score": 100, "stats": {"product": 1803, "atk": 124.2, "def": 103.6, "hp": 140}}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 578, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 825, "opRating": 174}, {"opponent": "r<PERSON><PERSON>", "rating": 812, "opRating": 187}, {"opponent": "dedenne", "rating": 732, "opRating": 267}, {"opponent": "nidoking", "rating": 610, "opRating": 389}, {"opponent": "tyrunt", "rating": 511, "opRating": 488}], "counters": [{"opponent": "litleo", "rating": 115}, {"opponent": "pyroar", "rating": 132}, {"opponent": "muk_alolan", "rating": 157}, {"opponent": "ninetales", "rating": 198}, {"opponent": "avalugg_his<PERSON>an", "rating": 223}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 9589}, {"moveId": "ASTONISH", "uses": 15411}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6856}, {"moveId": "RETURN", "uses": 3994}, {"moveId": "GRASS_KNOT", "uses": 6347}, {"moveId": "FOUL_PLAY", "uses": 7802}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 100, "stats": {"product": 2038, "atk": 110.3, "def": 102, "hp": 181}}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 586, "matchups": [{"opponent": "tyrunt", "rating": 700, "opRating": 300}, {"opponent": "avalugg_his<PERSON>an", "rating": 678, "opRating": 321}, {"opponent": "arctibax", "rating": 663, "opRating": 336}, {"opponent": "toxapex", "rating": 631, "opRating": 368}, {"opponent": "araquanid", "rating": 578, "opRating": 421}], "counters": [{"opponent": "roserade", "rating": 147}, {"opponent": "stunfisk_galarian", "rating": 177}, {"opponent": "magby", "rating": 191}, {"opponent": "magmar", "rating": 205}, {"opponent": "magmortar", "rating": 212}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 6920}, {"moveId": "THUNDER_SHOCK", "uses": 6695}, {"moveId": "SPARK", "uses": 4474}, {"moveId": "METAL_SOUND", "uses": 3696}, {"moveId": "CHARGE_BEAM", "uses": 3210}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 3414}, {"moveId": "MAGNET_BOMB", "uses": 9420}, {"moveId": "FLASH_CANNON", "uses": 1964}, {"moveId": "DISCHARGE", "uses": 10201}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "MAGNET_BOMB"], "score": 100, "stats": {"product": 1551, "atk": 144.7, "def": 112.7, "hp": 95}}, {"speciesId": "quilava", "speciesName": "Quilava", "rating": 615, "matchups": [{"opponent": "piloswine", "rating": 661, "opRating": 338}, {"opponent": "amaura", "rating": 562, "opRating": 437}, {"opponent": "cradily", "rating": 551, "opRating": 448}, {"opponent": "steelix", "rating": 515, "opRating": 484}, {"opponent": "swalot", "rating": 507, "opRating": 492}], "counters": [{"opponent": "toxapex", "rating": 131}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 227}, {"opponent": "magmortar", "rating": 259}, {"opponent": "nidoking", "rating": 358}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9496}, {"moveId": "EMBER", "uses": 15504}], "chargedMoves": [{"moveId": "RETURN", "uses": 3509}, {"moveId": "FLAME_CHARGE", "uses": 9406}, {"moveId": "FLAMETHROWER", "uses": 3913}, {"moveId": "DIG", "uses": 8143}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 100, "stats": {"product": 1761, "atk": 127.1, "def": 109, "hp": 127}}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 622, "matchups": [{"opponent": "talonflame", "rating": 672, "opRating": 327}, {"opponent": "magby", "rating": 581, "opRating": 418}, {"opponent": "magmortar", "rating": 577, "opRating": 422}, {"opponent": "toxapex", "rating": 573, "opRating": 426}, {"opponent": "dedenne", "rating": 543, "opRating": 456}], "counters": [{"opponent": "u<PERSON><PERSON><PERSON>", "rating": 106}, {"opponent": "nidoking", "rating": 122}, {"opponent": "tyrunt", "rating": 166}, {"opponent": "stunfisk_galarian", "rating": 204}, {"opponent": "magmar", "rating": 250}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 11000}, {"moveId": "TACKLE", "uses": 6693}, {"moveId": "SPARK", "uses": 7324}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3402}, {"moveId": "RETURN", "uses": 4177}, {"moveId": "HYPER_BEAM", "uses": 1626}, {"moveId": "FOUL_PLAY", "uses": 7769}, {"moveId": "DISCHARGE", "uses": 7977}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "FOUL_PLAY"], "score": 100, "stats": {"product": 1817, "atk": 123, "def": 127.2, "hp": 116}}, {"speciesId": "ivysaur", "speciesName": "Ivysaur", "rating": 502, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 780, "opRating": 219}, {"opponent": "umbreon", "rating": 530, "opRating": 469}, {"opponent": "magmortar", "rating": 523, "opRating": 476}, {"opponent": "magmar", "rating": 507, "opRating": 492}, {"opponent": "nidoking", "rating": 503, "opRating": 496}], "counters": [{"opponent": "golbat", "rating": 89}, {"opponent": "charizard", "rating": 98}, {"opponent": "heatran", "rating": 113}, {"opponent": "arctibax", "rating": 122}, {"opponent": "ninetales", "rating": 134}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 17271}, {"moveId": "RAZOR_LEAF", "uses": 7729}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 1775}, {"moveId": "SLUDGE_BOMB", "uses": 9096}, {"moveId": "RETURN", "uses": 5686}, {"moveId": "POWER_WHIP", "uses": 8450}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 100, "stats": {"product": 1877, "atk": 118.6, "def": 121.7, "hp": 130}}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 496, "matchups": [{"opponent": "toxapex", "rating": 654, "opRating": 345}, {"opponent": "r<PERSON><PERSON>", "rating": 654, "opRating": 345}, {"opponent": "araquanid", "rating": 573, "opRating": 426}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 556, "opRating": 443}, {"opponent": "amaura", "rating": 520, "opRating": 479}], "counters": [{"opponent": "tyrunt", "rating": 189}, {"opponent": "weezing_galarian", "rating": 208}, {"opponent": "magmar", "rating": 245}, {"opponent": "stunfisk_galarian", "rating": 316}, {"opponent": "nidoking", "rating": 362}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 20188}, {"moveId": "LOW_KICK", "uses": 4812}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12426}, {"moveId": "RETURN", "uses": 3039}, {"moveId": "FLAME_CHARGE", "uses": 6905}, {"moveId": "DISCHARGE", "uses": 2643}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 100, "stats": {"product": 1617, "atk": 137.7, "def": 95.4, "hp": 123}}, {"speciesId": "elekid", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 566, "matchups": [{"opponent": "talonflame", "rating": 745, "opRating": 254}, {"opponent": "toxapex", "rating": 620, "opRating": 379}, {"opponent": "moltres_galarian", "rating": 595, "opRating": 404}, {"opponent": "araquanid", "rating": 562, "opRating": 437}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dedenne", "rating": 221}, {"opponent": "nidoqueen", "rating": 223}, {"opponent": "nidoking", "rating": 279}, {"opponent": "stunfisk_galarian", "rating": 352}, {"opponent": "magmar", "rating": 410}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20167}, {"moveId": "LOW_KICK", "uses": 4833}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 10242}, {"moveId": "THUNDERBOLT", "uses": 3223}, {"moveId": "DISCHARGE", "uses": 3781}, {"moveId": "BRICK_BREAK", "uses": 7803}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "BRICK_BREAK"], "score": 100, "stats": {"product": 1474, "atk": 126, "def": 97.4, "hp": 120}}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 519, "matchups": [{"opponent": "moltres_galarian", "rating": 735, "opRating": 264}, {"opponent": "nidoqueen", "rating": 613, "opRating": 386}, {"opponent": "qwilfish_his<PERSON>an", "rating": 613, "opRating": 386}, {"opponent": "tyrunt", "rating": 566, "opRating": 433}, {"opponent": "arctibax", "rating": 542, "opRating": 457}], "counters": [{"opponent": "escavalier", "rating": 93}, {"opponent": "magby", "rating": 129}, {"opponent": "nidoking", "rating": 145}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 10334}, {"moveId": "ICE_SHARD", "uses": 8221}, {"moveId": "FEINT_ATTACK", "uses": 6444}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 3564}, {"moveId": "FOUL_PLAY", "uses": 7245}, {"moveId": "FOCUS_BLAST", "uses": 4102}, {"moveId": "AVALANCHE", "uses": 10115}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 100, "stats": {"product": 1568, "atk": 143, "def": 103.4, "hp": 106}}, {"speciesId": "magnemite", "speciesName": "Magnemite", "rating": 556, "matchups": [{"opponent": "toxapex", "rating": 859, "opRating": 140}, {"opponent": "piloswine", "rating": 792, "opRating": 207}, {"opponent": "avalugg_his<PERSON>an", "rating": 623, "opRating": 376}, {"opponent": "araquanid", "rating": 595, "opRating": 404}, {"opponent": "arctibax", "rating": 589, "opRating": 410}], "counters": [{"opponent": "stunfisk_galarian", "rating": 76}, {"opponent": "nidoking", "rating": 122}, {"opponent": "r<PERSON><PERSON>", "rating": 146}, {"opponent": "tyrunt", "rating": 213}, {"opponent": "magmar", "rating": 236}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 7914}, {"moveId": "THUNDER_SHOCK", "uses": 7661}, {"moveId": "SPARK", "uses": 5118}, {"moveId": "METAL_SOUND", "uses": 4321}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3659}, {"moveId": "RETURN", "uses": 4675}, {"moveId": "MAGNET_BOMB", "uses": 8079}, {"moveId": "DISCHARGE", "uses": 8577}]}, "moveset": ["VOLT_SWITCH", "MAGNET_BOMB", "DISCHARGE"], "score": 100, "stats": {"product": 1502, "atk": 148.7, "def": 113.4, "hp": 89}}, {"speciesId": "s<PERSON><PERSON><PERSON>", "speciesName": "Skorupi", "rating": 509, "matchups": [{"opponent": "nidoking", "rating": 831, "opRating": 168}, {"opponent": "dedenne", "rating": 619, "opRating": 380}, {"opponent": "magmar", "rating": 561, "opRating": 438}, {"opponent": "magby", "rating": 557, "opRating": 442}, {"opponent": "magmortar", "rating": 553, "opRating": 446}], "counters": [{"opponent": "metang", "rating": 139}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 198}, {"opponent": "toxapex", "rating": 216}, {"opponent": "tyrunt", "rating": 217}, {"opponent": "stunfisk_galarian", "rating": 292}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14282}, {"moveId": "INFESTATION", "uses": 10718}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 2266}, {"moveId": "RETURN", "uses": 2718}, {"moveId": "CROSS_POISON", "uses": 8369}, {"moveId": "AQUA_TAIL", "uses": 11647}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 100, "stats": {"product": 1430, "atk": 90.7, "def": 139.4, "hp": 113}}, {"speciesId": "pikachu_libre", "speciesName": "<PERSON><PERSON><PERSON> (Libre)", "rating": 538, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 733, "opRating": 266}, {"opponent": "obstagoon", "rating": 695, "opRating": 304}, {"opponent": "heatran", "rating": 623, "opRating": 376}, {"opponent": "litleo", "rating": 595, "opRating": 404}, {"opponent": "amaura", "rating": 528, "opRating": 471}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "shiftry", "rating": 98}, {"opponent": "magmar", "rating": 165}, {"opponent": "nidoking", "rating": 224}, {"opponent": "stunfisk_galarian", "rating": 301}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20025}, {"moveId": "CHARM", "uses": 4975}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 8441}, {"moveId": "PLAY_ROUGH", "uses": 2192}, {"moveId": "FLYING_PRESS", "uses": 14407}]}, "moveset": ["THUNDER_SHOCK", "FLYING_PRESS", "THUNDER_PUNCH"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "weepinbell", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 413, "matchups": [{"opponent": "dedenne", "rating": 866, "opRating": 133}, {"opponent": "nidoking", "rating": 701, "opRating": 298}, {"opponent": "avalugg_his<PERSON>an", "rating": 607, "opRating": 392}, {"opponent": "tyrunt", "rating": 535, "opRating": 464}, {"opponent": "steelix", "rating": 503, "opRating": 496}], "counters": [{"opponent": "arcanine", "rating": 86}, {"opponent": "ninetales", "rating": 87}, {"opponent": "magby", "rating": 104}, {"opponent": "magmar", "rating": 111}, {"opponent": "magmortar", "rating": 115}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 6274}, {"moveId": "BULLET_SEED", "uses": 11711}, {"moveId": "ACID", "uses": 7015}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7836}, {"moveId": "SEED_BOMB", "uses": 5511}, {"moveId": "RETURN", "uses": 4827}, {"moveId": "POWER_WHIP", "uses": 6872}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "SLUDGE_BOMB"], "score": 100, "stats": {"product": 1614, "atk": 137.8, "def": 84.2, "hp": 139}}, {"speciesId": "pikachu", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "moltres_galarian", "rating": 614, "opRating": 385}, {"opponent": "talonflame", "rating": 595, "opRating": 404}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "armarouge", "rating": 557, "opRating": 442}, {"opponent": "typhlosion", "rating": 557, "opRating": 442}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "charjabug", "rating": 83}, {"opponent": "r<PERSON><PERSON>", "rating": 92}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 263}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11465}, {"moveId": "QUICK_ATTACK", "uses": 9021}, {"moveId": "PRESENT", "uses": 4531}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 11272}, {"moveId": "THUNDERBOLT", "uses": 2003}, {"moveId": "THUNDER", "uses": 1748}, {"moveId": "SURF", "uses": 7442}, {"moveId": "DISCHARGE", "uses": 2389}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SURF"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "pika<PERSON>_shaymin", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>)", "rating": 500, "matchups": [{"opponent": "moltres_galarian", "rating": 614, "opRating": 385}, {"opponent": "talonflame", "rating": 595, "opRating": 404}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "armarouge", "rating": 557, "opRating": 442}, {"opponent": "typhlosion", "rating": 557, "opRating": 442}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "charjabug", "rating": 83}, {"opponent": "r<PERSON><PERSON>", "rating": 92}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 263}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11466}, {"moveId": "QUICK_ATTACK", "uses": 9023}, {"moveId": "PRESENT", "uses": 4531}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10060}, {"moveId": "THUNDERBOLT", "uses": 1846}, {"moveId": "THUNDER", "uses": 1640}, {"moveId": "SURF", "uses": 6438}, {"moveId": "GRASS_KNOT", "uses": 2951}, {"moveId": "DISCHARGE", "uses": 2126}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SURF"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "kartana", "speciesName": "Kartana", "rating": 451, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 820, "opRating": 179}, {"opponent": "dedenne", "rating": 762, "opRating": 237}, {"opponent": "avalugg_his<PERSON>an", "rating": 737, "opRating": 262}, {"opponent": "tyrunt", "rating": 589, "opRating": 410}, {"opponent": "nidoqueen", "rating": 583, "opRating": 416}], "counters": [{"opponent": "charizard", "rating": 85}, {"opponent": "moltres", "rating": 85}, {"opponent": "heatran", "rating": 90}, {"opponent": "ninetales", "rating": 95}, {"opponent": "talonflame", "rating": 95}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 9647}, {"moveId": "AIR_SLASH", "uses": 15353}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 4738}, {"moveId": "NIGHT_SLASH", "uses": 7018}, {"moveId": "LEAF_BLADE", "uses": 8783}, {"moveId": "AERIAL_ACE", "uses": 4467}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "NIGHT_SLASH"], "score": 100, "stats": {"product": 1316, "atk": 170.7, "def": 98.8, "hp": 78}}, {"speciesId": "flaaffy", "speciesName": "Flaaffy", "rating": 478, "matchups": [{"opponent": "tentacruel", "rating": 569, "opRating": 430}, {"opponent": "magby", "rating": 562, "opRating": 437}, {"opponent": "magmortar", "rating": 559, "opRating": 440}, {"opponent": "arm<PERSON>", "rating": 526, "opRating": 473}, {"opponent": "moltres_galarian", "rating": 503, "opRating": 496}], "counters": [{"opponent": "magmar", "rating": 160}, {"opponent": "nidoqueen", "rating": 226}, {"opponent": "arctibax", "rating": 228}, {"opponent": "nidoking", "rating": 271}, {"opponent": "stunfisk_galarian", "rating": 325}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 13258}, {"moveId": "CHARGE_BEAM", "uses": 11742}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 4870}, {"moveId": "THUNDERBOLT", "uses": 2664}, {"moveId": "RETURN", "uses": 3182}, {"moveId": "POWER_GEM", "uses": 8134}, {"moveId": "DISCHARGE", "uses": 6187}]}, "moveset": ["CHARGE_BEAM", "TRAILBLAZE", "DISCHARGE"], "score": 100, "stats": {"product": 1836, "atk": 121.8, "def": 99.7, "hp": 151}}, {"speciesId": "pikachu_5th_anniversary", "speciesName": "<PERSON><PERSON><PERSON> (5th Anniversary)", "rating": 500, "matchups": [{"opponent": "moltres_galarian", "rating": 614, "opRating": 385}, {"opponent": "talonflame", "rating": 595, "opRating": 404}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "armarouge", "rating": 557, "opRating": 442}, {"opponent": "typhlosion", "rating": 557, "opRating": 442}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "charjabug", "rating": 83}, {"opponent": "r<PERSON><PERSON>", "rating": 92}, {"opponent": "stunfisk_galarian", "rating": 150}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11506}, {"moveId": "QUICK_ATTACK", "uses": 8973}, {"moveId": "PRESENT", "uses": 4543}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12867}, {"moveId": "THUNDERBOLT", "uses": 2357}, {"moveId": "FLY", "uses": 7035}, {"moveId": "DISCHARGE", "uses": 2745}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "FLY"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "pikachu_flying", "speciesName": "<PERSON><PERSON><PERSON> (Flying)", "rating": 500, "matchups": [{"opponent": "moltres_galarian", "rating": 614, "opRating": 385}, {"opponent": "talonflame", "rating": 595, "opRating": 404}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "armarouge", "rating": 557, "opRating": 442}, {"opponent": "typhlosion", "rating": 557, "opRating": 442}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "charjabug", "rating": 83}, {"opponent": "r<PERSON><PERSON>", "rating": 92}, {"opponent": "stunfisk_galarian", "rating": 150}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13975}, {"moveId": "QUICK_ATTACK", "uses": 11025}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12867}, {"moveId": "THUNDERBOLT", "uses": 2357}, {"moveId": "FLY", "uses": 7035}, {"moveId": "DISCHARGE", "uses": 2745}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "FLY"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "vulpix", "speciesName": "Vulpix", "rating": 479, "matchups": [{"opponent": "piloswine", "rating": 831, "opRating": 168}, {"opponent": "heatran", "rating": 677, "opRating": 322}, {"opponent": "wormadam_sandy", "rating": 645, "opRating": 354}, {"opponent": "a<PERSON><PERSON>", "rating": 622, "opRating": 377}, {"opponent": "amoon<PERSON>s", "rating": 586, "opRating": 413}], "counters": [{"opponent": "tyrunt", "rating": 96}, {"opponent": "magmar", "rating": 138}, {"opponent": "munchlax", "rating": 167}, {"opponent": "nidoking", "rating": 240}, {"opponent": "stunfisk_galarian", "rating": 251}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 12490}, {"moveId": "EMBER", "uses": 12510}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 11732}, {"moveId": "RETURN", "uses": 1530}, {"moveId": "FLAME_CHARGE", "uses": 3072}, {"moveId": "FLAMETHROWER", "uses": 2562}, {"moveId": "BODY_SLAM", "uses": 6087}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "BODY_SLAM"], "score": 100, "stats": {"product": 1069, "atk": 93.2, "def": 104.1, "hp": 110}}, {"speciesId": "pikachu_kari<PERSON>shi", "speciesName": "<PERSON><PERSON><PERSON> (Kariyushi)", "rating": 484, "matchups": [{"opponent": "moltres_galarian", "rating": 614, "opRating": 385}, {"opponent": "talonflame", "rating": 595, "opRating": 404}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "armarouge", "rating": 557, "opRating": 442}, {"opponent": "typhlosion", "rating": 557, "opRating": 442}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "r<PERSON><PERSON>", "rating": 92}, {"opponent": "stunfisk_galarian", "rating": 118}, {"opponent": "magmar", "rating": 165}, {"opponent": "nidoking", "rating": 208}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13866}, {"moveId": "QUICK_ATTACK", "uses": 11134}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17945}, {"moveId": "THUNDERBOLT", "uses": 3250}, {"moveId": "DISCHARGE", "uses": 3784}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "DISCHARGE"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "bulbasaur", "speciesName": "Bulbasaur", "rating": 395, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 733, "opRating": 266}, {"opponent": "dedenne", "rating": 675, "opRating": 325}, {"opponent": "arm<PERSON>", "rating": 570, "opRating": 429}, {"opponent": "lileep", "rating": 520, "opRating": 479}], "counters": [{"opponent": "magby", "rating": 116}, {"opponent": "magmar", "rating": 125}, {"opponent": "magmortar", "rating": 129}, {"opponent": "ninetales", "rating": 134}, {"opponent": "nidoking", "rating": 240}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 13834}, {"moveId": "TACKLE", "uses": 11166}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7824}, {"moveId": "SEED_BOMB", "uses": 5505}, {"moveId": "RETURN", "uses": 4828}, {"moveId": "POWER_WHIP", "uses": 6877}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 100, "stats": {"product": 1420, "atk": 111.7, "def": 105.8, "hp": 120}}, {"speciesId": "koffing", "speciesName": "<PERSON><PERSON>", "rating": 420, "matchups": [{"opponent": "lileep", "rating": 561, "opRating": 438}, {"opponent": "cradily", "rating": 517, "opRating": 482}, {"opponent": "archen", "rating": 508, "opRating": 491}, {"opponent": "a<PERSON><PERSON>", "rating": 504, "opRating": 495}, {"opponent": "purrloin", "rating": 504, "opRating": 495}], "counters": [{"opponent": "heatran", "rating": 77}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magmar", "rating": 98}, {"opponent": "nidoking", "rating": 98}, {"opponent": "stunfisk_galarian", "rating": 218}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9561}, {"moveId": "INFESTATION", "uses": 9653}, {"moveId": "ACID", "uses": 5772}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6679}, {"moveId": "SLUDGE", "uses": 6610}, {"moveId": "RETURN", "uses": 4217}, {"moveId": "DARK_PULSE", "uses": 7461}]}, "moveset": ["INFESTATION", "DARK_PULSE", "SLUDGE_BOMB"], "score": 100, "stats": {"product": 1668, "atk": 112.6, "def": 131, "hp": 113}}, {"speciesId": "pikachu_horizons", "speciesName": "<PERSON><PERSON><PERSON> (Horizons)", "rating": 361, "matchups": [{"opponent": "moltres_galarian", "rating": 528, "opRating": 471}, {"opponent": "tentacruel", "rating": 528, "opRating": 471}, {"opponent": "flareon", "rating": 528, "opRating": 471}, {"opponent": "bron<PERSON>", "rating": 504, "opRating": 495}, {"opponent": "ekans", "rating": 504, "opRating": 495}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "nidoking", "rating": 82}, {"opponent": "arctibax", "rating": 99}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13867}, {"moveId": "QUICK_ATTACK", "uses": 11133}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15039}, {"moveId": "VOLT_TACKLE", "uses": 4000}, {"moveId": "THUNDERBOLT", "uses": 2749}, {"moveId": "DISCHARGE", "uses": 3243}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "VOLT_TACKLE"], "score": 100, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "zorua", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 368, "matchups": [{"opponent": "wormadam_sandy", "rating": 809, "opRating": 190}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 730, "opRating": 269}, {"opponent": "amoon<PERSON>s", "rating": 685, "opRating": 314}, {"opponent": "slowking_galarian", "rating": 526, "opRating": 473}, {"opponent": "shinx", "rating": 504, "opRating": 495}], "counters": [{"opponent": "weezing_galarian", "rating": 147}, {"opponent": "araquanid", "rating": 169}, {"opponent": "steelix", "rating": 197}, {"opponent": "stunfisk_galarian", "rating": 215}, {"opponent": "magmar", "rating": 285}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 8942}, {"moveId": "FEINT_ATTACK", "uses": 16058}], "chargedMoves": [{"moveId": "NIGHT_SHADE", "uses": 7233}, {"moveId": "FOUL_PLAY", "uses": 8130}, {"moveId": "DARK_PULSE", "uses": 3459}, {"moveId": "AERIAL_ACE", "uses": 6123}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "NIGHT_SHADE"], "score": 100, "stats": {"product": 1247, "atk": 141.1, "def": 78.1, "hp": 113}}, {"speciesId": "bellsprout", "speciesName": "Bellsprout", "rating": 274, "matchups": [{"opponent": "rotom_heat", "rating": 791, "opRating": 208}, {"opponent": "dedenne", "rating": 543, "opRating": 456}, {"opponent": "gloom", "rating": 523, "opRating": 476}, {"opponent": "kakuna", "rating": 515, "opRating": 484}, {"opponent": "shinx", "rating": 511, "opRating": 488}], "counters": [{"opponent": "talonflame", "rating": 53}, {"opponent": "arcanine", "rating": 98}, {"opponent": "umbreon", "rating": 119}, {"opponent": "magmar", "rating": 125}, {"opponent": "stunfisk_galarian", "rating": 144}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 16521}, {"moveId": "ACID", "uses": 8479}], "chargedMoves": [{"moveId": "WRAP", "uses": 5723}, {"moveId": "SLUDGE_BOMB", "uses": 7484}, {"moveId": "RETURN", "uses": 4519}, {"moveId": "POWER_WHIP", "uses": 7264}]}, "moveset": ["VINE_WHIP", "SLUDGE_BOMB", "POWER_WHIP"], "score": 100, "stats": {"product": 1050, "atk": 129.4, "def": 63.8, "hp": 127}}, {"speciesId": "spinarak", "speciesName": "Spinarak", "rating": 317, "matchups": [{"opponent": "nuzleaf", "rating": 570, "opRating": 429}, {"opponent": "weepinbell", "rating": 566, "opRating": 433}, {"opponent": "cacturne", "rating": 535, "opRating": 464}, {"opponent": "bulbasaur", "rating": 522, "opRating": 477}, {"opponent": "bellsprout", "rating": 517, "opRating": 482}], "counters": [{"opponent": "stunfisk_galarian", "rating": 112}, {"opponent": "steelix", "rating": 120}, {"opponent": "umbreon", "rating": 154}, {"opponent": "nidoking", "rating": 224}, {"opponent": "magmar", "rating": 276}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14671}, {"moveId": "BUG_BITE", "uses": 10329}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 4689}, {"moveId": "NIGHT_SLASH", "uses": 10254}, {"moveId": "CROSS_POISON", "uses": 10063}]}, "moveset": ["POISON_STING", "NIGHT_SLASH", "CROSS_POISON"], "score": 100, "stats": {"product": 843, "atk": 100.8, "def": 73.9, "hp": 113}}, {"speciesId": "budew", "speciesName": "<PERSON><PERSON>", "rating": 301, "matchups": [{"opponent": "magnezone", "rating": 535, "opRating": 464}, {"opponent": "magneton", "rating": 526, "opRating": 473}, {"opponent": "xurkitree", "rating": 522, "opRating": 477}, {"opponent": "pikachu_libre", "rating": 517, "opRating": 482}, {"opponent": "eelektrik", "rating": 504, "opRating": 495}], "counters": [{"opponent": "talonflame", "rating": 68}, {"opponent": "zapdos", "rating": 104}, {"opponent": "umbreon", "rating": 119}, {"opponent": "stunfisk_galarian", "rating": 189}, {"opponent": "magmar", "rating": 214}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 1389}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1709}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1082}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1856}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1379}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1461}, {"moveId": "HIDDEN_POWER_ICE", "uses": 1369}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2257}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1186}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1358}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1337}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1792}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1642}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1291}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1300}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1372}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1239}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 18500}, {"moveId": "ENERGY_BALL", "uses": 6500}]}, "moveset": ["HIDDEN_POWER_GROUND", "GRASS_KNOT", "ENERGY_BALL"], "score": 100, "stats": {"product": 1049, "atk": 89, "def": 104.1, "hp": 113}}, {"speciesId": "tynamo", "speciesName": "Tynamo", "rating": 247, "matchups": [{"opponent": "darkrai", "rating": 1000, "opRating": 0}, {"opponent": "volcarona", "rating": 990, "opRating": 9}, {"opponent": "pawmi", "rating": 795, "opRating": 204}, {"opponent": "weedle", "rating": 795, "opRating": 204}, {"opponent": "pichu", "rating": 771, "opRating": 228}], "counters": [{"opponent": "stunfisk_galarian", "rating": 76}, {"opponent": "cradily", "rating": 80}, {"opponent": "steelix", "rating": 104}, {"opponent": "magmar", "rating": 178}, {"opponent": "nidoking", "rating": 181}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 12797}, {"moveId": "SPARK", "uses": 12203}], "chargedMoves": [{"moveId": "STRUGGLE", "uses": 25000}]}, "moveset": ["TACKLE", "STRUGGLE"], "score": 100, "stats": {"product": 827, "atk": 100.8, "def": 78.1, "hp": 105}}, {"speciesId": "pawmi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 196, "matchups": [{"opponent": "beldum", "rating": 645, "opRating": 354}, {"opponent": "crobat", "rating": 545, "opRating": 454}, {"opponent": "oricorio_baile", "rating": 545, "opRating": 454}, {"opponent": "klink", "rating": 541, "opRating": 458}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "steelix", "rating": 52}, {"opponent": "stunfisk_galarian", "rating": 62}, {"opponent": "tyrunt", "rating": 81}, {"opponent": "nidoking", "rating": 90}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 14358}, {"moveId": "CHARGE_BEAM", "uses": 10642}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17940}, {"moveId": "THUNDERBOLT", "uses": 3251}, {"moveId": "DISCHARGE", "uses": 3792}]}, "moveset": ["SPARK", "WILD_CHARGE", "DISCHARGE"], "score": 100, "stats": {"product": 559, "atk": 92.4, "def": 50.4, "hp": 120}}, {"speciesId": "kakuna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 126, "matchups": [{"opponent": "volcarona", "rating": 983, "opRating": 16}, {"opponent": "tynamo", "rating": 575, "opRating": 425}, {"opponent": "zigzagoon_galarian", "rating": 558, "opRating": 441}, {"opponent": "weedle", "rating": 554, "opRating": 445}, {"opponent": "poochyena", "rating": 504, "opRating": 495}], "counters": [{"opponent": "stunfisk_galarian", "rating": 38}, {"opponent": "nidoking", "rating": 51}, {"opponent": "charmeleon", "rating": 55}, {"opponent": "arcanine", "rating": 65}, {"opponent": "magmar", "rating": 84}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13138}, {"moveId": "BUG_BITE", "uses": 11862}], "chargedMoves": [{"moveId": "STRUGGLE", "uses": 250}, {"moveId": "RETURN", "uses": 24750}]}, "moveset": ["POISON_STING", "RETURN", "STRUGGLE"], "score": 100, "stats": {"product": 465, "atk": 51.2, "def": 75.6, "hp": 120}}, {"speciesId": "weedle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 112, "matchups": [{"opponent": "darkrai", "rating": 991, "opRating": 8}, {"opponent": "volcarona", "rating": 982, "opRating": 17}, {"opponent": "toxel", "rating": 517, "opRating": 482}], "counters": [{"opponent": "stunfisk_galarian", "rating": 38}, {"opponent": "nidoking", "rating": 51}, {"opponent": "tyrunt", "rating": 54}, {"opponent": "moltres_galarian", "rating": 65}, {"opponent": "magmar", "rating": 84}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13413}, {"moveId": "BUG_BITE", "uses": 11587}], "chargedMoves": [{"moveId": "STRUGGLE", "uses": 250}, {"moveId": "RETURN", "uses": 24750}]}, "moveset": ["POISON_STING", "RETURN", "STRUGGLE"], "score": 100, "stats": {"product": 405, "atk": 65.5, "def": 54.6, "hp": 113}}, {"speciesId": "lokix", "speciesName": "<PERSON><PERSON>", "rating": 633, "matchups": [{"opponent": "umbreon", "rating": 636, "opRating": 363}, {"opponent": "arctibax", "rating": 599, "opRating": 400}, {"opponent": "stunfisk_galarian", "rating": 586, "opRating": 413}, {"opponent": "tyrunt", "rating": 566, "opRating": 433}, {"opponent": "steelix", "rating": 516, "opRating": 483}], "counters": [{"opponent": "pawniard", "rating": 216}, {"opponent": "over<PERSON><PERSON>l", "rating": 252}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 301}, {"opponent": "weezing_galarian", "rating": 321}, {"opponent": "magmar", "rating": 325}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 11812}, {"moveId": "COUNTER", "uses": 7375}, {"moveId": "BUG_BITE", "uses": 5837}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 9323}, {"moveId": "TRAILBLAZE", "uses": 4595}, {"moveId": "DARK_PULSE", "uses": 8563}, {"moveId": "BUG_BUZZ", "uses": 2541}]}, "moveset": ["SUCKER_PUNCH", "X_SCISSOR", "TRAILBLAZE"], "score": 99.5, "stats": {"product": 1673, "atk": 132.8, "def": 104, "hp": 121}}, {"speciesId": "blitzle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 321, "matchups": [{"opponent": "shinx", "rating": 516, "opRating": 483}, {"opponent": "spinarak", "rating": 516, "opRating": 483}, {"opponent": "budew", "rating": 508, "opRating": 491}, {"opponent": "chim<PERSON>r", "rating": 508, "opRating": 491}, {"opponent": "cloyster", "rating": 508, "opRating": 491}], "counters": [{"opponent": "amaura", "rating": 85}, {"opponent": "ninetales", "rating": 134}, {"opponent": "magmar", "rating": 165}, {"opponent": "avalugg_his<PERSON>an", "rating": 219}, {"opponent": "stunfisk_galarian", "rating": 263}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11346}, {"moveId": "QUICK_ATTACK", "uses": 13654}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3452}, {"moveId": "RETURN", "uses": 4317}, {"moveId": "FLAME_CHARGE", "uses": 9246}, {"moveId": "DISCHARGE", "uses": 8033}]}, "moveset": ["QUICK_ATTACK", "FLAME_CHARGE", "DISCHARGE"], "score": 99.4, "stats": {"product": 890, "atk": 111.7, "def": 66.3, "hp": 120}}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 737, "matchups": [{"opponent": "swalot", "rating": 761, "opRating": 238}, {"opponent": "talonflame", "rating": 747, "opRating": 252}, {"opponent": "nidoking", "rating": 734, "opRating": 265}, {"opponent": "araquanid", "rating": 639, "opRating": 360}, {"opponent": "steelix", "rating": 594, "opRating": 405}], "counters": [{"opponent": "magmar", "rating": 236}, {"opponent": "victini", "rating": 236}, {"opponent": "magmortar", "rating": 245}, {"opponent": "munchlax", "rating": 288}, {"opponent": "stunfisk_galarian", "rating": 437}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 820}, {"moveId": "INCINERATE", "uses": 12866}, {"moveId": "FIRE_SPIN", "uses": 6666}, {"moveId": "EMBER", "uses": 4647}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 5832}, {"moveId": "SCORCHING_SANDS", "uses": 2954}, {"moveId": "HEAT_WAVE", "uses": 722}, {"moveId": "FLAME_CHARGE", "uses": 5334}, {"moveId": "FIRE_BLAST", "uses": 2408}, {"moveId": "DRILL_RUN", "uses": 7800}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "WILD_CHARGE"], "score": 98.7, "stats": {"product": 1658, "atk": 134.7, "def": 110.9, "hp": 111}}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 655, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 766, "opRating": 233}, {"opponent": "r<PERSON><PERSON>", "rating": 699, "opRating": 300}, {"opponent": "toxapex", "rating": 635, "opRating": 364}, {"opponent": "umbreon", "rating": 593, "opRating": 406}, {"opponent": "magmortar", "rating": 521, "opRating": 478}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "magmar", "rating": 183}, {"opponent": "nidoking", "rating": 251}, {"opponent": "stunfisk_galarian", "rating": 278}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12393}, {"moveId": "THUNDER_SHOCK", "uses": 12607}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 14187}, {"moveId": "THUNDERBOLT", "uses": 2561}, {"moveId": "THUNDER", "uses": 2263}, {"moveId": "SHADOW_BALL", "uses": 5978}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SHADOW_BALL"], "score": 98.5, "stats": {"product": 1700, "atk": 130.9, "def": 110, "hp": 118}}, {"speciesId": "zubat", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 310, "matchups": [{"opponent": "weepinbell", "rating": 539, "opRating": 460}, {"opponent": "bulbasaur", "rating": 535, "opRating": 464}, {"opponent": "kartana", "rating": 530, "opRating": 469}, {"opponent": "gloom", "rating": 513, "opRating": 486}, {"opponent": "bellsprout", "rating": 504, "opRating": 495}], "counters": [{"opponent": "<PERSON>on", "rating": 62}, {"opponent": "steelix", "rating": 68}, {"opponent": "stunfisk_galarian", "rating": 103}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 119}, {"opponent": "magmar", "rating": 258}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 15933}, {"moveId": "BITE", "uses": 9067}], "chargedMoves": [{"moveId": "SWIFT", "uses": 9258}, {"moveId": "SLUDGE_BOMB", "uses": 5064}, {"moveId": "RETURN", "uses": 1614}, {"moveId": "POISON_FANG", "uses": 6216}, {"moveId": "AIR_CUTTER", "uses": 2887}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "POISON_FANG"], "score": 98.4, "stats": {"product": 688, "atk": 82.3, "def": 73.9, "hp": 113}}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 725, "matchups": [{"opponent": "nidoking", "rating": 846, "opRating": 153}, {"opponent": "swalot", "rating": 767, "opRating": 232}, {"opponent": "magby", "rating": 675, "opRating": 324}, {"opponent": "steelix", "rating": 605, "opRating": 394}, {"opponent": "magmortar", "rating": 508, "opRating": 491}], "counters": [{"opponent": "magmar", "rating": 236}, {"opponent": "linoone_galarian", "rating": 253}, {"opponent": "rapidash", "rating": 274}, {"opponent": "umbreon", "rating": 280}, {"opponent": "stunfisk_galarian", "rating": 437}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16999}, {"moveId": "EMBER", "uses": 8001}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 9585}, {"moveId": "HEAT_WAVE", "uses": 1339}, {"moveId": "FLAME_CHARGE", "uses": 9915}, {"moveId": "FLAMETHROWER", "uses": 4139}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 98.2, "stats": {"product": 1685, "atk": 133.5, "def": 110.6, "hp": 114}}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 556, "matchups": [{"opponent": "nidoqueen", "rating": 790, "opRating": 209}, {"opponent": "toxapex", "rating": 736, "opRating": 263}, {"opponent": "avalugg_his<PERSON>an", "rating": 732, "opRating": 267}, {"opponent": "nidoking", "rating": 651, "opRating": 348}, {"opponent": "tyrunt", "rating": 647, "opRating": 352}], "counters": [{"opponent": "bisharp", "rating": 101}, {"opponent": "lokix", "rating": 128}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "stunfisk_galarian", "rating": 272}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 15992}, {"moveId": "CHARGE_BEAM", "uses": 9008}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 7121}, {"moveId": "DOOM_DESIRE", "uses": 14752}, {"moveId": "DAZZLING_GLEAM", "uses": 3106}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 98.2, "stats": {"product": 1855, "atk": 120.4, "def": 119.3, "hp": 129}}, {"speciesId": "nidoran_female", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 390, "matchups": [{"opponent": "flareon", "rating": 529, "opRating": 470}, {"opponent": "magby", "rating": 525, "opRating": 474}, {"opponent": "ivysaur", "rating": 507, "opRating": 492}, {"opponent": "elekid", "rating": 503, "opRating": 496}, {"opponent": "vileplume", "rating": 503, "opRating": 496}], "counters": [{"opponent": "stunfisk_galarian", "rating": 79}, {"opponent": "steelix", "rating": 80}, {"opponent": "<PERSON>on", "rating": 100}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "nidoking", "rating": 157}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 16024}, {"moveId": "BITE", "uses": 8976}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6014}, {"moveId": "RETURN", "uses": 2362}, {"moveId": "POISON_FANG", "uses": 7354}, {"moveId": "BODY_SLAM", "uses": 9311}]}, "moveset": ["POISON_STING", "BODY_SLAM", "POISON_FANG"], "score": 98, "stats": {"product": 1001, "atk": 84.8, "def": 87.3, "hp": 135}}, {"speciesId": "braixen", "speciesName": "Braixen", "rating": 615, "matchups": [{"opponent": "piloswine", "rating": 646, "opRating": 353}, {"opponent": "amaura", "rating": 544, "opRating": 455}, {"opponent": "steelix", "rating": 532, "opRating": 467}, {"opponent": "weezing_galarian", "rating": 512, "opRating": 487}, {"opponent": "dedenne", "rating": 508, "opRating": 491}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "ninetales", "rating": 226}, {"opponent": "magmar", "rating": 227}, {"opponent": "nidoking", "rating": 334}, {"opponent": "stunfisk_galarian", "rating": 349}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 7015}, {"moveId": "EMBER", "uses": 17985}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 8388}, {"moveId": "FLAME_CHARGE", "uses": 11716}, {"moveId": "FLAMETHROWER", "uses": 4881}]}, "moveset": ["EMBER", "FLAME_CHARGE", "PSYSHOCK"], "score": 97.9, "stats": {"product": 1711, "atk": 130.3, "def": 106.6, "hp": 123}}, {"speciesId": "fennekin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 488, "matchups": [{"opponent": "sandslash_alolan", "rating": 862, "opRating": 137}, {"opponent": "a<PERSON><PERSON>", "rating": 579, "opRating": 420}, {"opponent": "piloswine", "rating": 522, "opRating": 477}, {"opponent": "metang", "rating": 513, "opRating": 486}, {"opponent": "arbok", "rating": 508, "opRating": 491}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "ninetales", "rating": 170}, {"opponent": "magmar", "rating": 187}, {"opponent": "nidoking", "rating": 287}, {"opponent": "stunfisk_galarian", "rating": 304}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 7312}, {"moveId": "EMBER", "uses": 17688}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 8383}, {"moveId": "FLAME_CHARGE", "uses": 11713}, {"moveId": "FLAMETHROWER", "uses": 4888}]}, "moveset": ["EMBER", "FLAME_CHARGE", "PSYSHOCK"], "score": 97.9, "stats": {"product": 1223, "atk": 110, "def": 98.3, "hp": 113}}, {"speciesId": "torchic", "speciesName": "Torchic", "rating": 501, "matchups": [{"opponent": "sandslash_alolan", "rating": 829, "opRating": 170}, {"opponent": "wormadam_sandy", "rating": 587, "opRating": 412}, {"opponent": "beedrill", "rating": 545, "opRating": 454}, {"opponent": "piloswine", "rating": 537, "opRating": 462}, {"opponent": "amoon<PERSON>s", "rating": 537, "opRating": 462}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "qwilfish_his<PERSON>an", "rating": 225}, {"opponent": "toxapex", "rating": 228}, {"opponent": "nidoking", "rating": 314}, {"opponent": "stunfisk_galarian", "rating": 340}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 7147}, {"moveId": "EMBER", "uses": 17853}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 5957}, {"moveId": "RETURN", "uses": 3921}, {"moveId": "FLAME_CHARGE", "uses": 10636}, {"moveId": "FLAMETHROWER", "uses": 4430}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_TOMB"], "score": 97.6, "stats": {"product": 1253, "atk": 121.8, "def": 85.7, "hp": 120}}, {"speciesId": "slugma", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 423, "matchups": [{"opponent": "sandslash_alolan", "rating": 818, "opRating": 181}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 517, "opRating": 482}, {"opponent": "bron<PERSON>", "rating": 513, "opRating": 486}, {"opponent": "larve<PERSON>", "rating": 504, "opRating": 495}, {"opponent": "quilava", "rating": 504, "opRating": 495}], "counters": [{"opponent": "ninetales", "rating": 146}, {"opponent": "magmar", "rating": 165}, {"opponent": "toxapex", "rating": 220}, {"opponent": "umbreon", "rating": 270}, {"opponent": "stunfisk_galarian", "rating": 304}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 10792}, {"moveId": "EMBER", "uses": 14208}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 10273}, {"moveId": "FLAME_CHARGE", "uses": 11781}, {"moveId": "FLAME_BURST", "uses": 2978}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 97.5, "stats": {"product": 913, "atk": 111.7, "def": 72.2, "hp": 113}}, {"speciesId": "zigzagoon_galarian", "speciesName": "Zigzagoon (Galarian)", "rating": 231, "matchups": [{"opponent": "poochyena", "rating": 640, "opRating": 359}, {"opponent": "budew", "rating": 613, "opRating": 386}, {"opponent": "tynamo", "rating": 577, "opRating": 422}, {"opponent": "bron<PERSON>", "rating": 536, "opRating": 463}, {"opponent": "trubbish", "rating": 531, "opRating": 468}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "stunfisk_galarian", "rating": 88}, {"opponent": "steelix", "rating": 104}, {"opponent": "nidoking", "rating": 106}, {"opponent": "magmar", "rating": 129}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 4825}, {"moveId": "TACKLE", "uses": 20175}], "chargedMoves": [{"moveId": "SWIFT", "uses": 11357}, {"moveId": "RETURN", "uses": 1950}, {"moveId": "DIG", "uses": 6959}, {"moveId": "BODY_SLAM", "uses": 4746}]}, "moveset": ["TACKLE", "SWIFT", "DIG"], "score": 97.5, "stats": {"product": 539, "atk": 61.3, "def": 79.8, "hp": 110}}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 656, "matchups": [{"opponent": "magmortar", "rating": 581, "opRating": 418}, {"opponent": "magby", "rating": 581, "opRating": 418}, {"opponent": "araquanid", "rating": 542, "opRating": 457}, {"opponent": "toxapex", "rating": 527, "opRating": 472}, {"opponent": "avalugg_his<PERSON>an", "rating": 527, "opRating": 472}], "counters": [{"opponent": "victreebel", "rating": 160}, {"opponent": "stunfisk_galarian", "rating": 210}, {"opponent": "nidoqueen", "rating": 269}, {"opponent": "magmar", "rating": 290}, {"opponent": "nidoking", "rating": 307}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 17459}, {"moveId": "CHARGE_BEAM", "uses": 7541}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 1089}, {"moveId": "TRAILBLAZE", "uses": 2712}, {"moveId": "THUNDER_PUNCH", "uses": 4814}, {"moveId": "THUNDER", "uses": 1327}, {"moveId": "POWER_GEM", "uses": 4905}, {"moveId": "FOCUS_BLAST", "uses": 2780}, {"moveId": "DRAGON_PULSE", "uses": 1640}, {"moveId": "BRUTAL_SWING", "uses": 5764}]}, "moveset": ["VOLT_SWITCH", "BRUTAL_SWING", "TRAILBLAZE"], "score": 97.4, "stats": {"product": 1755, "atk": 127.4, "def": 106.7, "hp": 129}}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 575, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 825, "opRating": 174}, {"opponent": "tyrunt", "rating": 693, "opRating": 306}, {"opponent": "toxapex", "rating": 617, "opRating": 382}, {"opponent": "umbreon", "rating": 575, "opRating": 424}, {"opponent": "munchlax", "rating": 575, "opRating": 424}], "counters": [{"opponent": "darmanitan_standard", "rating": 65}, {"opponent": "castform_sunny", "rating": 99}, {"opponent": "magby", "rating": 154}, {"opponent": "magmortar", "rating": 171}, {"opponent": "stunfisk_galarian", "rating": 304}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 13435}, {"moveId": "BULLET_PUNCH", "uses": 11565}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 8317}, {"moveId": "TRAILBLAZE", "uses": 3923}, {"moveId": "NIGHT_SLASH", "uses": 9078}, {"moveId": "IRON_HEAD", "uses": 3667}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "TRAILBLAZE"], "score": 96.2, "stats": {"product": 1613, "atk": 137.8, "def": 110.3, "hp": 106}}, {"speciesId": "zoroark", "speciesName": "Zoroark", "rating": 493, "matchups": [{"opponent": "dedenne", "rating": 703, "opRating": 296}, {"opponent": "amoon<PERSON>s", "rating": 689, "opRating": 310}, {"opponent": "piloswine", "rating": 645, "opRating": 354}, {"opponent": "lokix", "rating": 645, "opRating": 354}, {"opponent": "castform_sunny", "rating": 529, "opRating": 470}], "counters": [{"opponent": "litleo", "rating": 129}, {"opponent": "araquanid", "rating": 154}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "magmar", "rating": 245}, {"opponent": "nidoking", "rating": 263}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 11572}, {"moveId": "SHADOW_CLAW", "uses": 13428}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 4217}, {"moveId": "NIGHT_SHADE", "uses": 6927}, {"moveId": "FOUL_PLAY", "uses": 7863}, {"moveId": "FLAMETHROWER", "uses": 6000}]}, "moveset": ["SHADOW_CLAW", "FOUL_PLAY", "SLUDGE_BOMB"], "score": 96.2, "stats": {"product": 1414, "atk": 157.9, "def": 86.9, "hp": 103}}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 641, "matchups": [{"opponent": "toxapex", "rating": 595, "opRating": 404}, {"opponent": "magby", "rating": 585, "opRating": 414}, {"opponent": "ninetales", "rating": 550, "opRating": 449}, {"opponent": "magmortar", "rating": 528, "opRating": 471}, {"opponent": "umbreon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magmar", "rating": 316}, {"opponent": "stunfisk_galarian", "rating": 349}, {"opponent": "nidoking", "rating": 358}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 16243}, {"moveId": "BITE", "uses": 8757}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 3909}, {"moveId": "SLUDGE_BOMB", "uses": 4874}, {"moveId": "RETURN", "uses": 2737}, {"moveId": "FLAMETHROWER", "uses": 5394}, {"moveId": "CRUNCH", "uses": 8106}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 95.9, "stats": {"product": 1820, "atk": 121.7, "def": 95.1, "hp": 157}}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 538, "matchups": [{"opponent": "piloswine", "rating": 877, "opRating": 122}, {"opponent": "avalugg_his<PERSON>an", "rating": 617, "opRating": 382}, {"opponent": "qwilfish_his<PERSON>an", "rating": 590, "opRating": 409}, {"opponent": "nidoqueen", "rating": 574, "opRating": 425}, {"opponent": "weezing_galarian", "rating": 569, "opRating": 430}], "counters": [{"opponent": "melmetal", "rating": 109}, {"opponent": "klang", "rating": 116}, {"opponent": "magmar", "rating": 160}, {"opponent": "steelix", "rating": 165}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 16037}, {"moveId": "FROST_BREATH", "uses": 8963}], "chargedMoves": [{"moveId": "RAZOR_SHELL", "uses": 5268}, {"moveId": "LIQUIDATION", "uses": 6899}, {"moveId": "ICY_WIND", "uses": 2483}, {"moveId": "HYDRO_PUMP", "uses": 1303}, {"moveId": "BLIZZARD", "uses": 1058}, {"moveId": "AVALANCHE", "uses": 7048}, {"moveId": "AURORA_BEAM", "uses": 849}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "LIQUIDATION"], "score": 95.9, "stats": {"product": 1868, "atk": 119.6, "def": 166, "hp": 94}}, {"speciesId": "stunky", "speciesName": "Stunky", "rating": 552, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 860, "opRating": 139}, {"opponent": "armarouge", "rating": 751, "opRating": 248}, {"opponent": "slowbro_galarian", "rating": 704, "opRating": 295}, {"opponent": "flareon", "rating": 615, "opRating": 384}, {"opponent": "toxapex", "rating": 530, "opRating": 469}], "counters": [{"opponent": "weezing_galarian", "rating": 200}, {"opponent": "stunfisk_galarian", "rating": 260}, {"opponent": "electrode_hisuian", "rating": 265}, {"opponent": "umbreon", "rating": 335}, {"opponent": "magmar", "rating": 392}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 10146}, {"moveId": "BITE", "uses": 14854}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 3908}, {"moveId": "SLUDGE_BOMB", "uses": 4870}, {"moveId": "RETURN", "uses": 2745}, {"moveId": "FLAMETHROWER", "uses": 5396}, {"moveId": "CRUNCH", "uses": 8122}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 95.9, "stats": {"product": 1482, "atk": 114.2, "def": 88.2, "hp": 147}}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 422, "matchups": [{"opponent": "amaura", "rating": 740, "opRating": 259}, {"opponent": "tyrunt", "rating": 720, "opRating": 279}, {"opponent": "avalugg_his<PERSON>an", "rating": 715, "opRating": 284}, {"opponent": "arctibax", "rating": 627, "opRating": 372}, {"opponent": "swalot", "rating": 568, "opRating": 431}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "litleo", "rating": 139}, {"opponent": "umbreon", "rating": 158}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 168}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 4456}, {"moveId": "BULLET_PUNCH", "uses": 20544}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 5621}, {"moveId": "METEOR_MASH", "uses": 9400}, {"moveId": "FLASH_CANNON", "uses": 1474}, {"moveId": "EARTHQUAKE", "uses": 8487}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "PSYCHIC"], "score": 95.9, "stats": {"product": 1674, "atk": 132.7, "def": 123.6, "hp": 102}}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 693, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 770, "opRating": 229}, {"opponent": "magmortar", "rating": 729, "opRating": 270}, {"opponent": "dedenne", "rating": 666, "opRating": 333}, {"opponent": "magmar", "rating": 613, "opRating": 386}, {"opponent": "tyrunt", "rating": 556, "opRating": 443}], "counters": [{"opponent": "typhlosion", "rating": 226}, {"opponent": "armarouge", "rating": 232}, {"opponent": "nidoking", "rating": 240}, {"opponent": "stunfisk_galarian", "rating": 310}, {"opponent": "talonflame", "rating": 316}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 4432}, {"moveId": "MUD_SHOT", "uses": 12324}, {"moveId": "INFESTATION", "uses": 8248}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 10656}, {"moveId": "ICE_BEAM", "uses": 9167}, {"moveId": "GUNK_SHOT", "uses": 2763}, {"moveId": "ACID_SPRAY", "uses": 2435}]}, "moveset": ["MUD_SHOT", "SLUDGE_BOMB", "ICE_BEAM"], "score": 95.8, "stats": {"product": 2158, "atk": 104.1, "def": 123.3, "hp": 168}}, {"speciesId": "beedrill", "speciesName": "Beedrill", "rating": 607, "matchups": [{"opponent": "nidoking", "rating": 810, "opRating": 189}, {"opponent": "dedenne", "rating": 810, "opRating": 189}, {"opponent": "toxapex", "rating": 604, "opRating": 395}, {"opponent": "magmar", "rating": 534, "opRating": 465}, {"opponent": "magmortar", "rating": 523, "opRating": 476}], "counters": [{"opponent": "<PERSON>on", "rating": 104}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magnezone", "rating": 151}, {"opponent": "avalugg_his<PERSON>an", "rating": 177}, {"opponent": "stunfisk_galarian", "rating": 340}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 9894}, {"moveId": "INFESTATION", "uses": 8802}, {"moveId": "BUG_BITE", "uses": 6280}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 5599}, {"moveId": "SLUDGE_BOMB", "uses": 3210}, {"moveId": "RETURN", "uses": 1843}, {"moveId": "FELL_STINGER", "uses": 1459}, {"moveId": "DRILL_RUN", "uses": 9201}, {"moveId": "AERIAL_ACE", "uses": 3650}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "DRILL_RUN"], "score": 95.8, "stats": {"product": 1749, "atk": 127.6, "def": 106.2, "hp": 129}}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 660, "matchups": [{"opponent": "piloswine", "rating": 840, "opRating": 159}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 769, "opRating": 230}, {"opponent": "amaura", "rating": 578, "opRating": 421}, {"opponent": "cradily", "rating": 560, "opRating": 439}, {"opponent": "steelix", "rating": 539, "opRating": 460}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "toxapex", "rating": 194}, {"opponent": "magmar", "rating": 227}, {"opponent": "ninetales", "rating": 242}, {"opponent": "magmortar", "rating": 259}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 13539}, {"moveId": "FIRE_FANG", "uses": 11461}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 8347}, {"moveId": "OVERHEAT", "uses": 4699}, {"moveId": "IRON_HEAD", "uses": 2019}, {"moveId": "FLAME_CHARGE", "uses": 6051}, {"moveId": "FLAMETHROWER", "uses": 2495}, {"moveId": "FIRE_BLAST", "uses": 1393}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 95.7, "stats": {"product": 1746, "atk": 128.7, "def": 96.1, "hp": 141}}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "dedenne", "rating": 897, "opRating": 102}, {"opponent": "r<PERSON><PERSON>", "rating": 782, "opRating": 217}, {"opponent": "umbreon", "rating": 680, "opRating": 319}, {"opponent": "nidoqueen", "rating": 676, "opRating": 323}, {"opponent": "steelix", "rating": 602, "opRating": 397}], "counters": [{"opponent": "golbat", "rating": 89}, {"opponent": "beedrill", "rating": 96}, {"opponent": "charizard", "rating": 98}, {"opponent": "ninetales", "rating": 134}, {"opponent": "nidoking", "rating": 240}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 17921}, {"moveId": "RAZOR_LEAF", "uses": 7079}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 1525}, {"moveId": "SLUDGE_BOMB", "uses": 8580}, {"moveId": "PETAL_BLIZZARD", "uses": 1808}, {"moveId": "FRENZY_PLANT", "uses": 13126}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 95.5, "stats": {"product": 1821, "atk": 122.4, "def": 121.8, "hp": 122}}, {"speciesId": "tepig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 571, "matchups": [{"opponent": "sandslash_alolan", "rating": 862, "opRating": 137}, {"opponent": "piloswine", "rating": 835, "opRating": 164}, {"opponent": "weezing_galarian", "rating": 647, "opRating": 352}, {"opponent": "steelix", "rating": 523, "opRating": 476}, {"opponent": "cradily", "rating": 503, "opRating": 496}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}, {"opponent": "toxapex", "rating": 190}, {"opponent": "stunfisk_galarian", "rating": 286}, {"opponent": "nidoking", "rating": 311}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9890}, {"moveId": "EMBER", "uses": 15110}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 10657}, {"moveId": "FLAMETHROWER", "uses": 4459}, {"moveId": "BODY_SLAM", "uses": 9913}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BODY_SLAM"], "score": 95.5, "stats": {"product": 1368, "atk": 109.2, "def": 84, "hp": 149}}, {"speciesId": "tyrunt", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 737, "matchups": [{"opponent": "talonflame", "rating": 887, "opRating": 112}, {"opponent": "ninetales", "rating": 798, "opRating": 201}, {"opponent": "nidoqueen", "rating": 798, "opRating": 201}, {"opponent": "toxapex", "rating": 627, "opRating": 372}, {"opponent": "magmortar", "rating": 507, "opRating": 492}], "counters": [{"opponent": "ma<PERSON>le", "rating": 106}, {"opponent": "weezing_galarian", "rating": 126}, {"opponent": "dedenne", "rating": 157}, {"opponent": "klefki", "rating": 161}, {"opponent": "stunfisk_galarian", "rating": 263}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9519}, {"moveId": "DRAGON_TAIL", "uses": 15481}], "chargedMoves": [{"moveId": "STOMP", "uses": 5102}, {"moveId": "DRAGON_CLAW", "uses": 10383}, {"moveId": "ANCIENT_POWER", "uses": 9498}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 95.4, "stats": {"product": 1749, "atk": 127.5, "def": 106.3, "hp": 129}}, {"speciesId": "<PERSON>ay", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 536, "matchups": [{"opponent": "nidoking", "rating": 729, "opRating": 270}, {"opponent": "beedrill", "rating": 629, "opRating": 370}, {"opponent": "rapidash", "rating": 557, "opRating": 442}, {"opponent": "pyroar", "rating": 557, "opRating": 442}, {"opponent": "swalot", "rating": 541, "opRating": 458}], "counters": [{"opponent": "pawniard", "rating": 80}, {"opponent": "obstagoon", "rating": 159}, {"opponent": "umbreon", "rating": 200}, {"opponent": "moltres_galarian", "rating": 245}, {"opponent": "stunfisk_galarian", "rating": 248}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 6770}, {"moveId": "PSYWAVE", "uses": 14226}, {"moveId": "PECK", "uses": 4026}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 4300}, {"moveId": "NIGHT_SLASH", "uses": 20700}]}, "moveset": ["PSYWAVE", "NIGHT_SLASH", "PSYBEAM"], "score": 95.4, "stats": {"product": 1150, "atk": 94.9, "def": 92.4, "hp": 131}}, {"speciesId": "tadbulb", "speciesName": "Tadbulb", "rating": 405, "matchups": [{"opponent": "talonflame", "rating": 632, "opRating": 367}, {"opponent": "charizard", "rating": 604, "opRating": 395}, {"opponent": "bron<PERSON>", "rating": 513, "opRating": 486}, {"opponent": "fennekin", "rating": 510, "opRating": 489}, {"opponent": "torchic", "rating": 503, "opRating": 496}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "stunfisk_galarian", "rating": 82}, {"opponent": "r<PERSON><PERSON>", "rating": 92}, {"opponent": "magmar", "rating": 125}, {"opponent": "nidoking", "rating": 177}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 11313}, {"moveId": "THUNDER_SHOCK", "uses": 13687}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 3811}, {"moveId": "PARABOLIC_CHARGE", "uses": 10536}, {"moveId": "DISCHARGE", "uses": 10630}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "PARABOLIC_CHARGE"], "score": 95.4, "stats": {"product": 1057, "atk": 99.9, "def": 73.9, "hp": 143}}, {"speciesId": "meowth_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 366, "matchups": [{"opponent": "wormadam_sandy", "rating": 561, "opRating": 438}, {"opponent": "metagross", "rating": 535, "opRating": 464}, {"opponent": "delphox", "rating": 530, "opRating": 469}, {"opponent": "spinarak", "rating": 526, "opRating": 473}, {"opponent": "slowking_galarian", "rating": 508, "opRating": 491}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 154}, {"opponent": "steelix", "rating": 201}, {"opponent": "stunfisk_galarian", "rating": 215}, {"opponent": "magmar", "rating": 258}, {"opponent": "nidoking", "rating": 263}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 10742}, {"moveId": "BITE", "uses": 14258}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 4807}, {"moveId": "NIGHT_SLASH", "uses": 12745}, {"moveId": "FOUL_PLAY", "uses": 4003}, {"moveId": "DARK_PULSE", "uses": 3429}]}, "moveset": ["BITE", "NIGHT_SLASH", "TRAILBLAZE"], "score": 95.4, "stats": {"product": 846, "atk": 95.7, "def": 78.1, "hp": 113}}, {"speciesId": "luxio", "speciesName": "Luxio", "rating": 558, "matchups": [{"opponent": "toxapex", "rating": 711, "opRating": 288}, {"opponent": "araquanid", "rating": 700, "opRating": 299}, {"opponent": "moltres_galarian", "rating": 616, "opRating": 383}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 594, "opRating": 405}, {"opponent": "amaura", "rating": 580, "opRating": 419}], "counters": [{"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 98}, {"opponent": "tyrunt", "rating": 189}, {"opponent": "stunfisk_galarian", "rating": 221}, {"opponent": "magmar", "rating": 227}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 17051}, {"moveId": "BITE", "uses": 7949}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12899}, {"moveId": "THUNDERBOLT", "uses": 2364}, {"moveId": "RETURN", "uses": 3112}, {"moveId": "CRUNCH", "uses": 6601}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 95.2, "stats": {"product": 1673, "atk": 134.1, "def": 91, "hp": 137}}, {"speciesId": "eelektrik", "speciesName": "Eelektrik", "rating": 576, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 657, "opRating": 342}, {"opponent": "moltres_galarian", "rating": 654, "opRating": 345}, {"opponent": "arm<PERSON>", "rating": 624, "opRating": 375}, {"opponent": "magby", "rating": 548, "opRating": 451}, {"opponent": "magmortar", "rating": 545, "opRating": 454}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "nidoking", "rating": 98}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "stunfisk_galarian", "rating": 210}, {"opponent": "magmar", "rating": 227}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 17543}, {"moveId": "ACID", "uses": 7457}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 4133}, {"moveId": "DISCHARGE", "uses": 9716}, {"moveId": "CRUNCH", "uses": 11103}]}, "moveset": ["SPARK", "CRUNCH", "DISCHARGE"], "score": 95.2, "stats": {"product": 1818, "atk": 123.2, "def": 110.9, "hp": 133}}, {"speciesId": "murkrow", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 446, "matchups": [{"opponent": "wormadam_sandy", "rating": 839, "opRating": 160}, {"opponent": "amoon<PERSON>s", "rating": 761, "opRating": 238}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 660, "opRating": 339}, {"opponent": "armarouge", "rating": 582, "opRating": 417}, {"opponent": "voltorb_his<PERSON>an", "rating": 507, "opRating": 492}], "counters": [{"opponent": "over<PERSON><PERSON>l", "rating": 169}, {"opponent": "obstagoon", "rating": 177}, {"opponent": "dedenne", "rating": 184}, {"opponent": "steelix", "rating": 197}, {"opponent": "magmar", "rating": 285}], "moves": {"fastMoves": [{"moveId": "PECK", "uses": 9664}, {"moveId": "FEINT_ATTACK", "uses": 15336}], "chargedMoves": [{"moveId": "RETURN", "uses": 3086}, {"moveId": "FOUL_PLAY", "uses": 8096}, {"moveId": "DRILL_PECK", "uses": 10411}, {"moveId": "DARK_PULSE", "uses": 3427}]}, "moveset": ["FEINT_ATTACK", "DRILL_PECK", "FOUL_PLAY"], "score": 95.2, "stats": {"product": 1555, "atk": 143.4, "def": 80.8, "hp": 134}}, {"speciesId": "pichu", "speciesName": "<PERSON><PERSON>", "rating": 176, "matchups": [{"opponent": "archeops", "rating": 619, "opRating": 380}, {"opponent": "zigzagoon_galarian", "rating": 619, "opRating": 380}, {"opponent": "rattata_alolan", "rating": 541, "opRating": 458}, {"opponent": "honch<PERSON><PERSON>", "rating": 535, "opRating": 464}, {"opponent": "beldum", "rating": 529, "opRating": 470}], "counters": [{"opponent": "stunfisk_galarian", "rating": 29}, {"opponent": "nidoking", "rating": 43}, {"opponent": "munchlax", "rating": 57}, {"opponent": "magmar", "rating": 125}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13610}, {"moveId": "QUICK_ATTACK", "uses": 11390}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 13096}, {"moveId": "THUNDERBOLT", "uses": 4065}, {"moveId": "DISARMING_VOICE", "uses": 7820}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "DISARMING_VOICE"], "score": 95.2, "stats": {"product": 371, "atk": 77.3, "def": 57.1, "hp": 84}}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "nidoqueen", "rating": 823, "opRating": 176}, {"opponent": "toxapex", "rating": 747, "opRating": 252}, {"opponent": "nidoking", "rating": 697, "opRating": 302}, {"opponent": "avalugg_his<PERSON>an", "rating": 659, "opRating": 340}, {"opponent": "tyrunt", "rating": 638, "opRating": 361}], "counters": [{"opponent": "bisharp", "rating": 101}, {"opponent": "moltres_galarian", "rating": 102}, {"opponent": "umbreon", "rating": 106}, {"opponent": "pawniard", "rating": 122}, {"opponent": "stunfisk_galarian", "rating": 147}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 7712}, {"moveId": "FEINT_ATTACK", "uses": 6819}, {"moveId": "CONFUSION", "uses": 10470}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 7348}, {"moveId": "PSYCHIC", "uses": 2005}, {"moveId": "PAYBACK", "uses": 4339}, {"moveId": "HEAVY_SLAM", "uses": 4022}, {"moveId": "FLASH_CANNON", "uses": 1324}, {"moveId": "BULLDOZE", "uses": 6015}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 94.9, "stats": {"product": 2010, "atk": 111.3, "def": 151.7, "hp": 119}}, {"speciesId": "bron<PERSON>", "speciesName": "Bronzor", "rating": 347, "matchups": [{"opponent": "beedrill", "rating": 591, "opRating": 408}, {"opponent": "seviper", "rating": 569, "opRating": 430}, {"opponent": "electrike", "rating": 565, "opRating": 434}, {"opponent": "toxtricity", "rating": 536, "opRating": 463}, {"opponent": "venomoth", "rating": 518, "opRating": 481}], "counters": [{"opponent": "moltres_galarian", "rating": 53}, {"opponent": "umbreon", "rating": 54}, {"opponent": "stunfisk_galarian", "rating": 65}, {"opponent": "ninetales", "rating": 103}, {"opponent": "magmar", "rating": 191}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 11481}, {"moveId": "CONFUSION", "uses": 13519}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 10897}, {"moveId": "PAYBACK", "uses": 6603}, {"moveId": "HEAVY_SLAM", "uses": 5705}, {"moveId": "GYRO_BALL", "uses": 1765}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 94.9, "stats": {"product": 948, "atk": 48.7, "def": 142, "hp": 137}}, {"speciesId": "purrloin", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 413, "matchups": [{"opponent": "saland<PERSON>", "rating": 530, "opRating": 469}, {"opponent": "wormadam_sandy", "rating": 517, "opRating": 482}, {"opponent": "metang", "rating": 513, "opRating": 486}, {"opponent": "scorbunny", "rating": 508, "opRating": 491}, {"opponent": "delphox", "rating": 504, "opRating": 495}], "counters": [{"opponent": "raticate_alolan", "rating": 125}, {"opponent": "araquanid", "rating": 165}, {"opponent": "nidoking", "rating": 240}, {"opponent": "magmar", "rating": 245}, {"opponent": "stunfisk_galarian", "rating": 251}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 18584}, {"moveId": "SCRATCH", "uses": 6416}], "chargedMoves": [{"moveId": "RETURN", "uses": 3621}, {"moveId": "PLAY_ROUGH", "uses": 3225}, {"moveId": "NIGHT_SLASH", "uses": 14323}, {"moveId": "DARK_PULSE", "uses": 3865}]}, "moveset": ["SUCKER_PUNCH", "NIGHT_SLASH", "DARK_PULSE"], "score": 94.8, "stats": {"product": 800, "atk": 94.9, "def": 73.9, "hp": 114}}, {"speciesId": "pikachu_pop_star", "speciesName": "<PERSON><PERSON><PERSON> (Pop Star)", "rating": 424, "matchups": [{"opponent": "charizard", "rating": 623, "opRating": 376}, {"opponent": "moltres_galarian", "rating": 538, "opRating": 461}, {"opponent": "bron<PERSON>", "rating": 514, "opRating": 485}, {"opponent": "oricorio_pom_pom", "rating": 514, "opRating": 485}, {"opponent": "voltorb", "rating": 504, "opRating": 495}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 90}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 17509}, {"moveId": "CHARM", "uses": 7491}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 16700}, {"moveId": "PLAY_ROUGH", "uses": 5063}, {"moveId": "DRAINING_KISS", "uses": 3232}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "PLAY_ROUGH"], "score": 94.7, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "linoone_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 637, "matchups": [{"opponent": "rapidash", "rating": 746, "opRating": 253}, {"opponent": "armarouge", "rating": 746, "opRating": 253}, {"opponent": "typhlosion", "rating": 684, "opRating": 315}, {"opponent": "swalot", "rating": 638, "opRating": 361}, {"opponent": "stunfisk_galarian", "rating": 562, "opRating": 437}], "counters": [{"opponent": "pawniard", "rating": 105}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "magby", "rating": 129}, {"opponent": "magmar", "rating": 138}, {"opponent": "magmortar", "rating": 143}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 14763}, {"moveId": "LICK", "uses": 10237}], "chargedMoves": [{"moveId": "RETURN", "uses": 2864}, {"moveId": "GUNK_SHOT", "uses": 2231}, {"moveId": "DIG", "uses": 8347}, {"moveId": "BODY_SLAM", "uses": 11564}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 94.6, "stats": {"product": 1954, "atk": 114.5, "def": 112.2, "hp": 152}}, {"speciesId": "ponyta", "speciesName": "Ponyta", "rating": 636, "matchups": [{"opponent": "piloswine", "rating": 619, "opRating": 380}, {"opponent": "amaura", "rating": 535, "opRating": 464}, {"opponent": "steelix", "rating": 526, "opRating": 473}, {"opponent": "cradily", "rating": 513, "opRating": 486}, {"opponent": "magby", "rating": 504, "opRating": 495}], "counters": [{"opponent": "magmar", "rating": 227}, {"opponent": "ninetales", "rating": 242}, {"opponent": "tyrunt", "rating": 251}, {"opponent": "magmortar", "rating": 259}, {"opponent": "stunfisk_galarian", "rating": 357}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9539}, {"moveId": "EMBER", "uses": 15461}], "chargedMoves": [{"moveId": "STOMP", "uses": 7542}, {"moveId": "FLAME_WHEEL", "uses": 2033}, {"moveId": "FLAME_CHARGE", "uses": 10664}, {"moveId": "FIRE_BLAST", "uses": 4815}]}, "moveset": ["EMBER", "FLAME_CHARGE", "STOMP"], "score": 94.5, "stats": {"product": 1666, "atk": 134.8, "def": 109.3, "hp": 113}}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 556, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 664, "opRating": 336}, {"opponent": "steelix", "rating": 616, "opRating": 384}, {"opponent": "tyrunt", "rating": 608, "opRating": 392}, {"opponent": "nidoqueen", "rating": 564, "opRating": 436}, {"opponent": "stunfisk_galarian", "rating": 528, "opRating": 472}], "counters": [{"opponent": "ninetales", "rating": 59}, {"opponent": "magby", "rating": 79}, {"opponent": "magmar", "rating": 84}, {"opponent": "magmortar", "rating": 87}, {"opponent": "nidoking", "rating": 161}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 12386}, {"moveId": "BULLET_SEED", "uses": 12614}], "chargedMoves": [{"moveId": "THUNDER", "uses": 4602}, {"moveId": "RETURN", "uses": 4021}, {"moveId": "POWER_WHIP", "uses": 6388}, {"moveId": "MIRROR_SHOT", "uses": 5802}, {"moveId": "FLASH_CANNON", "uses": 3008}, {"moveId": "ACID_SPRAY", "uses": 1174}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "THUNDER"], "score": 94.5, "stats": {"product": 2081, "atk": 107.3, "def": 155.1, "hp": 125}}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 496, "matchups": [{"opponent": "arm<PERSON>", "rating": 692, "opRating": 307}, {"opponent": "slowbro_galarian", "rating": 623, "opRating": 376}, {"opponent": "lileep", "rating": 602, "opRating": 397}, {"opponent": "cradily", "rating": 559, "opRating": 440}, {"opponent": "rai<PERSON>u", "rating": 525, "opRating": 474}], "counters": [{"opponent": "heatran", "rating": 77}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "nidoking", "rating": 98}, {"opponent": "stunfisk_galarian", "rating": 124}, {"opponent": "tyrunt", "rating": 158}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9588}, {"moveId": "INFESTATION", "uses": 9833}, {"moveId": "ACID", "uses": 5576}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 4427}, {"moveId": "SLUDGE_BOMB", "uses": 6090}, {"moveId": "SHADOW_BALL", "uses": 5643}, {"moveId": "RETURN", "uses": 3417}, {"moveId": "DARK_PULSE", "uses": 5410}]}, "moveset": ["INFESTATION", "SLUDGE_BOMB", "SHADOW_BALL"], "score": 94.5, "stats": {"product": 1901, "atk": 117, "def": 138.8, "hp": 117}}, {"speciesId": "lie<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 511, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 777, "opRating": 222}, {"opponent": "rapidash", "rating": 615, "opRating": 384}, {"opponent": "armarouge", "rating": 615, "opRating": 384}, {"opponent": "heatran", "rating": 583, "opRating": 416}, {"opponent": "tyrunt", "rating": 519, "opRating": 480}], "counters": [{"opponent": "weezing_galarian", "rating": 95}, {"opponent": "araquanid", "rating": 120}, {"opponent": "ninetales", "rating": 142}, {"opponent": "magmar", "rating": 165}, {"opponent": "nidoking", "rating": 169}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 17484}, {"moveId": "CHARM", "uses": 7516}], "chargedMoves": [{"moveId": "RETURN", "uses": 4311}, {"moveId": "PLAY_ROUGH", "uses": 3655}, {"moveId": "PAYBACK", "uses": 4447}, {"moveId": "GUNK_SHOT", "uses": 2797}, {"moveId": "DARK_PULSE", "uses": 9814}]}, "moveset": ["SNARL", "DARK_PULSE", "PLAY_ROUGH"], "score": 94.3, "stats": {"product": 1585, "atk": 140.9, "def": 89.2, "hp": 126}}, {"speciesId": "litleo", "speciesName": "Litleo", "rating": 768, "matchups": [{"opponent": "munchlax", "rating": 681, "opRating": 318}, {"opponent": "steelix", "rating": 664, "opRating": 335}, {"opponent": "stunfisk_galarian", "rating": 559, "opRating": 440}, {"opponent": "ninetales", "rating": 555, "opRating": 444}, {"opponent": "nidoqueen", "rating": 524, "opRating": 475}], "counters": [{"opponent": "tyrunt", "rating": 112}, {"opponent": "magmar", "rating": 214}, {"opponent": "magby", "rating": 220}, {"opponent": "magmortar", "rating": 226}, {"opponent": "nidoking", "rating": 303}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 4157}, {"moveId": "INCINERATE", "uses": 9944}, {"moveId": "FIRE_FANG", "uses": 5905}, {"moveId": "EMBER", "uses": 4964}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11272}, {"moveId": "FLAMETHROWER", "uses": 4721}, {"moveId": "CRUNCH", "uses": 8996}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "CRUNCH"], "score": 94.1, "stats": {"product": 1837, "atk": 121.1, "def": 106, "hp": 143}}, {"speciesId": "torracat", "speciesName": "Torracat", "rating": 613, "matchups": [{"opponent": "sandslash_alolan", "rating": 876, "opRating": 123}, {"opponent": "piloswine", "rating": 817, "opRating": 182}, {"opponent": "lileep", "rating": 567, "opRating": 432}, {"opponent": "heatran", "rating": 552, "opRating": 447}, {"opponent": "cradily", "rating": 522, "opRating": 477}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "tentacruel", "rating": 200}, {"opponent": "ninetales", "rating": 226}, {"opponent": "magmar", "rating": 227}, {"opponent": "stunfisk_galarian", "rating": 375}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17799}, {"moveId": "BITE", "uses": 7201}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11270}, {"moveId": "FLAMETHROWER", "uses": 4724}, {"moveId": "CRUNCH", "uses": 8994}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 94.1, "stats": {"product": 1638, "atk": 136.5, "def": 89.5, "hp": 134}}, {"speciesId": "pansear", "speciesName": "Pansear", "rating": 505, "matchups": [{"opponent": "sandslash_alolan", "rating": 838, "opRating": 161}, {"opponent": "wormadam_sandy", "rating": 637, "opRating": 362}, {"opponent": "a<PERSON><PERSON>", "rating": 551, "opRating": 448}, {"opponent": "heatran", "rating": 531, "opRating": 468}, {"opponent": "nidoran_male", "rating": 503, "opRating": 496}], "counters": [{"opponent": "tyrunt", "rating": 127}, {"opponent": "magmar", "rating": 160}, {"opponent": "tentacruel", "rating": 238}, {"opponent": "stunfisk_galarian", "rating": 289}, {"opponent": "ninetales", "rating": 289}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 6859}, {"moveId": "FIRE_SPIN", "uses": 18141}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 12265}, {"moveId": "FLAME_BURST", "uses": 3141}, {"moveId": "CRUNCH", "uses": 9620}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "CRUNCH"], "score": 94.1, "stats": {"product": 1163, "atk": 99.9, "def": 91.5, "hp": 127}}, {"speciesId": "litten", "speciesName": "Litten", "rating": 482, "matchups": [{"opponent": "sandslash_alolan", "rating": 829, "opRating": 170}, {"opponent": "wormadam_sandy", "rating": 558, "opRating": 441}, {"opponent": "beedrill", "rating": 545, "opRating": 454}, {"opponent": "piloswine", "rating": 512, "opRating": 487}, {"opponent": "a<PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "tyrunt", "rating": 120}, {"opponent": "typhlosion", "rating": 158}, {"opponent": "magmar", "rating": 183}, {"opponent": "toxapex", "rating": 228}, {"opponent": "stunfisk_galarian", "rating": 340}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 7307}, {"moveId": "EMBER", "uses": 17693}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 11275}, {"moveId": "FLAMETHROWER", "uses": 4708}, {"moveId": "CRUNCH", "uses": 9004}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 94.1, "stats": {"product": 1139, "atk": 120.1, "def": 78.9, "hp": 120}}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 680, "matchups": [{"opponent": "nidoqueen", "rating": 907, "opRating": 92}, {"opponent": "tyrunt", "rating": 798, "opRating": 201}, {"opponent": "steelix", "rating": 604, "opRating": 395}, {"opponent": "nidoking", "rating": 588, "opRating": 411}, {"opponent": "umbreon", "rating": 520, "opRating": 479}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magby", "rating": 116}, {"opponent": "magmar", "rating": 125}, {"opponent": "magmortar", "rating": 129}, {"opponent": "fennekin", "rating": 137}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 9735}, {"moveId": "POWDER_SNOW", "uses": 9542}, {"moveId": "METAL_CLAW", "uses": 5696}], "chargedMoves": [{"moveId": "RETURN", "uses": 1851}, {"moveId": "ICE_PUNCH", "uses": 5802}, {"moveId": "GYRO_BALL", "uses": 1534}, {"moveId": "DRILL_RUN", "uses": 9145}, {"moveId": "BULLDOZE", "uses": 1845}, {"moveId": "BLIZZARD", "uses": 1251}, {"moveId": "AERIAL_ACE", "uses": 3518}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "DRILL_RUN"], "score": 94, "stats": {"product": 1917, "atk": 116.5, "def": 132.6, "hp": 124}}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 689, "matchups": [{"opponent": "stunfisk_galarian", "rating": 768, "opRating": 231}, {"opponent": "talonflame", "rating": 768, "opRating": 231}, {"opponent": "ninetales", "rating": 704, "opRating": 295}, {"opponent": "arctibax", "rating": 632, "opRating": 367}, {"opponent": "steelix", "rating": 541, "opRating": 458}], "counters": [{"opponent": "over<PERSON><PERSON>l", "rating": 90}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "tyrunt", "rating": 158}, {"opponent": "magmar", "rating": 160}, {"opponent": "nidoking", "rating": 161}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 8249}, {"moveId": "FIRE_FANG", "uses": 8124}, {"moveId": "DOUBLE_KICK", "uses": 8622}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 2222}, {"moveId": "FIRE_BLAST", "uses": 995}, {"moveId": "DARK_PULSE", "uses": 3796}, {"moveId": "DARKEST_LARIAT", "uses": 4195}, {"moveId": "BLAZE_KICK", "uses": 5867}, {"moveId": "BLAST_BURN", "uses": 7989}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 93.9, "stats": {"product": 1775, "atk": 125.8, "def": 106.8, "hp": 132}}, {"speciesId": "amaura", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 669, "matchups": [{"opponent": "nidoqueen", "rating": 853, "opRating": 146}, {"opponent": "tyrunt", "rating": 759, "opRating": 240}, {"opponent": "nidoking", "rating": 685, "opRating": 314}, {"opponent": "umbreon", "rating": 545, "opRating": 454}, {"opponent": "toxapex", "rating": 536, "opRating": 463}], "counters": [{"opponent": "magby", "rating": 116}, {"opponent": "magmortar", "rating": 129}, {"opponent": "melmetal", "rating": 198}, {"opponent": "magmar", "rating": 254}, {"opponent": "stunfisk_galarian", "rating": 272}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 16969}, {"moveId": "FROST_BREATH", "uses": 8031}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 11262}, {"moveId": "THUNDERBOLT", "uses": 3534}, {"moveId": "AURORA_BEAM", "uses": 1326}, {"moveId": "ANCIENT_POWER", "uses": 8825}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 93.9, "stats": {"product": 1953, "atk": 114.2, "def": 104.1, "hp": 164}}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 497, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 845, "opRating": 155}, {"opponent": "arctibax", "rating": 755, "opRating": 245}, {"opponent": "tyrunt", "rating": 715, "opRating": 285}, {"opponent": "qwilfish_his<PERSON>an", "rating": 690, "opRating": 310}, {"opponent": "toxapex", "rating": 595, "opRating": 405}], "counters": [{"opponent": "darum<PERSON>", "rating": 77}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "arcanine", "rating": 98}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 218}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 12806}, {"moveId": "BUG_BITE", "uses": 12194}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10873}, {"moveId": "STONE_EDGE", "uses": 9568}, {"moveId": "IRON_HEAD", "uses": 4596}]}, "moveset": ["METAL_CLAW", "X_SCISSOR", "STONE_EDGE"], "score": 93.7, "stats": {"product": 1647, "atk": 135.8, "def": 121.2, "hp": 100}}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 761, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 842, "opRating": 157}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 773, "opRating": 226}, {"opponent": "talonflame", "rating": 773, "opRating": 226}, {"opponent": "umbreon", "rating": 587, "opRating": 412}, {"opponent": "arctibax", "rating": 569, "opRating": 430}], "counters": [{"opponent": "zapdos", "rating": 121}, {"opponent": "over<PERSON><PERSON>l", "rating": 210}, {"opponent": "magmar", "rating": 227}, {"opponent": "swalot", "rating": 270}, {"opponent": "nidoking", "rating": 397}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 13819}, {"moveId": "FIRE_SPIN", "uses": 11181}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 2718}, {"moveId": "SCORCHING_SANDS", "uses": 7216}, {"moveId": "PSYCHIC", "uses": 2181}, {"moveId": "FIRE_PUNCH", "uses": 7809}, {"moveId": "FIRE_BLAST", "uses": 1338}, {"moveId": "BRICK_BREAK", "uses": 3779}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 93.6, "stats": {"product": 1579, "atk": 141.3, "def": 103.3, "hp": 108}}, {"speciesId": "cynda<PERSON><PERSON>", "speciesName": "Cyndaquil", "rating": 507, "matchups": [{"opponent": "sandslash_alolan", "rating": 815, "opRating": 184}, {"opponent": "wormadam_sandy", "rating": 585, "opRating": 414}, {"opponent": "a<PERSON><PERSON>", "rating": 549, "opRating": 450}, {"opponent": "piloswine", "rating": 513, "opRating": 486}, {"opponent": "metang", "rating": 509, "opRating": 490}], "counters": [{"opponent": "tyrunt", "rating": 131}, {"opponent": "magby", "rating": 154}, {"opponent": "magmar", "rating": 183}, {"opponent": "nidoking", "rating": 263}, {"opponent": "stunfisk_galarian", "rating": 286}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9882}, {"moveId": "EMBER", "uses": 15118}], "chargedMoves": [{"moveId": "SWIFT", "uses": 9981}, {"moveId": "RETURN", "uses": 1729}, {"moveId": "FLAME_CHARGE", "uses": 9402}, {"moveId": "FLAMETHROWER", "uses": 3942}]}, "moveset": ["EMBER", "SWIFT", "FLAME_CHARGE"], "score": 93.4, "stats": {"product": 1109, "atk": 110, "def": 90.7, "hp": 111}}, {"speciesId": "meowth_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 407, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 653, "opRating": 346}, {"opponent": "tyrunt", "rating": 574, "opRating": 425}, {"opponent": "arm<PERSON>", "rating": 547, "opRating": 452}, {"opponent": "lileep", "rating": 527, "opRating": 472}, {"opponent": "amaura", "rating": 511, "opRating": 488}], "counters": [{"opponent": "heatran", "rating": 77}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magmar", "rating": 125}, {"opponent": "stunfisk_galarian", "rating": 127}, {"opponent": "magmortar", "rating": 129}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 9277}, {"moveId": "METAL_CLAW", "uses": 15723}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 4681}, {"moveId": "NIGHT_SLASH", "uses": 9671}, {"moveId": "GYRO_BALL", "uses": 2932}, {"moveId": "DIG", "uses": 7742}]}, "moveset": ["METAL_CLAW", "NIGHT_SLASH", "DIG"], "score": 93.3, "stats": {"product": 1247, "atk": 109.2, "def": 89.9, "hp": 127}}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 653, "matchups": [{"opponent": "magby", "rating": 681, "opRating": 318}, {"opponent": "magmortar", "rating": 678, "opRating": 321}, {"opponent": "dedenne", "rating": 623, "opRating": 376}, {"opponent": "araquanid", "rating": 592, "opRating": 407}, {"opponent": "magmar", "rating": 530, "opRating": 469}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "nidoking", "rating": 145}, {"opponent": "nidoqueen", "rating": 201}, {"opponent": "steelix", "rating": 209}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 8883}, {"moveId": "LICK", "uses": 6385}, {"moveId": "INFESTATION", "uses": 6118}, {"moveId": "ACID", "uses": 3639}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 8764}, {"moveId": "SLUDGE_WAVE", "uses": 5121}, {"moveId": "GUNK_SHOT", "uses": 2028}, {"moveId": "DARK_PULSE", "uses": 7355}, {"moveId": "ACID_SPRAY", "uses": 1754}]}, "moveset": ["POISON_JAB", "THUNDER_PUNCH", "DARK_PULSE"], "score": 93.2, "stats": {"product": 1906, "atk": 117.3, "def": 111.2, "hp": 146}}, {"speciesId": "pawniard", "speciesName": "Pawniard", "rating": 602, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 843, "opRating": 156}, {"opponent": "umbreon", "rating": 745, "opRating": 254}, {"opponent": "avalugg_his<PERSON>an", "rating": 644, "opRating": 355}, {"opponent": "moltres_galarian", "rating": 584, "opRating": 415}, {"opponent": "tyrunt", "rating": 567, "opRating": 432}], "counters": [{"opponent": "magmar", "rating": 84}, {"opponent": "litleo", "rating": 136}, {"opponent": "magby", "rating": 154}, {"opponent": "magmortar", "rating": 171}, {"opponent": "stunfisk_galarian", "rating": 325}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 8122}, {"moveId": "FURY_CUTTER", "uses": 16878}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 6956}, {"moveId": "NIGHT_SLASH", "uses": 13905}, {"moveId": "IRON_HEAD", "uses": 4132}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 93, "stats": {"product": 1678, "atk": 131.9, "def": 107.7, "hp": 118}}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 552, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 869, "opRating": 130}, {"opponent": "cradily", "rating": 683, "opRating": 316}, {"opponent": "stunfisk_galarian", "rating": 640, "opRating": 359}, {"opponent": "steelix", "rating": 626, "opRating": 373}, {"opponent": "r<PERSON><PERSON>", "rating": 609, "opRating": 390}], "counters": [{"opponent": "muk_alolan", "rating": 75}, {"opponent": "nidoqueen", "rating": 129}, {"opponent": "magmar", "rating": 138}, {"opponent": "magby", "rating": 154}, {"opponent": "magmortar", "rating": 171}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 8586}, {"moveId": "RAZOR_LEAF", "uses": 3055}, {"moveId": "FEINT_ATTACK", "uses": 6447}, {"moveId": "BULLET_SEED", "uses": 6899}], "chargedMoves": [{"moveId": "RETURN", "uses": 2663}, {"moveId": "LEAF_TORNADO", "uses": 1933}, {"moveId": "LEAF_BLADE", "uses": 9982}, {"moveId": "HURRICANE", "uses": 3155}, {"moveId": "FOUL_PLAY", "uses": 7289}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 93, "stats": {"product": 1671, "atk": 134.1, "def": 87.6, "hp": 142}}, {"speciesId": "castform_sunny", "speciesName": "Castform (Sunny)", "rating": 707, "matchups": [{"opponent": "piloswine", "rating": 873, "opRating": 126}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 705, "opRating": 294}, {"opponent": "steelix", "rating": 654, "opRating": 345}, {"opponent": "swalot", "rating": 561, "opRating": 438}, {"opponent": "avalugg_his<PERSON>an", "rating": 547, "opRating": 452}], "counters": [{"opponent": "toxapex", "rating": 148}, {"opponent": "tyrunt", "rating": 155}, {"opponent": "tentacruel", "rating": 162}, {"opponent": "nidoking", "rating": 287}, {"opponent": "magmar", "rating": 366}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 9956}, {"moveId": "EMBER", "uses": 15044}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 20235}, {"moveId": "SOLAR_BEAM", "uses": 2383}, {"moveId": "FIRE_BLAST", "uses": 2362}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 92.9, "stats": {"product": 1980, "atk": 113.3, "def": 119.6, "hp": 146}}, {"speciesId": "raboot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 650, "matchups": [{"opponent": "piloswine", "rating": 857, "opRating": 142}, {"opponent": "weezing_galarian", "rating": 626, "opRating": 373}, {"opponent": "amaura", "rating": 569, "opRating": 430}, {"opponent": "steelix", "rating": 519, "opRating": 480}, {"opponent": "dedenne", "rating": 507, "opRating": 492}], "counters": [{"opponent": "magmar", "rating": 223}, {"opponent": "talonflame", "rating": 225}, {"opponent": "magmortar", "rating": 240}, {"opponent": "tyrunt", "rating": 263}, {"opponent": "ninetales", "rating": 353}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 8266}, {"moveId": "FIRE_SPIN", "uses": 16734}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 17650}, {"moveId": "FLAMETHROWER", "uses": 7350}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 92.9, "stats": {"product": 1727, "atk": 130, "def": 102.1, "hp": 130}}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 633, "matchups": [{"opponent": "sandslash_alolan", "rating": 889, "opRating": 110}, {"opponent": "piloswine", "rating": 836, "opRating": 163}, {"opponent": "charjabug", "rating": 592, "opRating": 407}, {"opponent": "heatran", "rating": 535, "opRating": 464}, {"opponent": "amaura", "rating": 504, "opRating": 495}], "counters": [{"opponent": "talonflame", "rating": 217}, {"opponent": "magmar", "rating": 232}, {"opponent": "tyrunt", "rating": 267}, {"opponent": "ninetales", "rating": 361}, {"opponent": "stunfisk_galarian", "rating": 393}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 7841}, {"moveId": "FIRE_SPIN", "uses": 17159}], "chargedMoves": [{"moveId": "FOCUS_BLAST", "uses": 6700}, {"moveId": "FLAME_CHARGE", "uses": 12900}, {"moveId": "FLAMETHROWER", "uses": 5369}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 92.9, "stats": {"product": 1607, "atk": 139, "def": 102.2, "hp": 113}}, {"speciesId": "scorbunny", "speciesName": "Scorbunny", "rating": 546, "matchups": [{"opponent": "sandslash_alolan", "rating": 870, "opRating": 129}, {"opponent": "piloswine", "rating": 807, "opRating": 192}, {"opponent": "beedrill", "rating": 570, "opRating": 429}, {"opponent": "amoon<PERSON>s", "rating": 562, "opRating": 437}, {"opponent": "lokix", "rating": 527, "opRating": 472}], "counters": [{"opponent": "tyrunt", "rating": 209}, {"opponent": "magmar", "rating": 223}, {"opponent": "nidoking", "rating": 322}, {"opponent": "ninetales", "rating": 329}, {"opponent": "stunfisk_galarian", "rating": 360}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 8426}, {"moveId": "FIRE_SPIN", "uses": 16574}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 17647}, {"moveId": "FLAMETHROWER", "uses": 7353}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 92.9, "stats": {"product": 1239, "atk": 123.5, "def": 78.9, "hp": 127}}, {"speciesId": "roselia", "speciesName": "Roselia", "rating": 435, "matchups": [{"opponent": "dedenne", "rating": 827, "opRating": 172}, {"opponent": "cradily", "rating": 663, "opRating": 336}, {"opponent": "electrode_hisuian", "rating": 658, "opRating": 341}, {"opponent": "obstagoon", "rating": 542, "opRating": 457}, {"opponent": "charjabug", "rating": 509, "opRating": 490}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 128}, {"opponent": "nidoqueen", "rating": 133}, {"opponent": "nidoking", "rating": 145}, {"opponent": "magmar", "rating": 357}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 4827}, {"moveId": "POISON_JAB", "uses": 12044}, {"moveId": "MAGICAL_LEAF", "uses": 8149}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 13255}, {"moveId": "PETAL_BLIZZARD", "uses": 6872}, {"moveId": "DAZZLING_GLEAM", "uses": 4880}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 92.9, "stats": {"product": 1602, "atk": 139.6, "def": 107.2, "hp": 107}}, {"speciesId": "charcadet", "speciesName": "Charcadet", "rating": 412, "matchups": [{"opponent": "amoon<PERSON>s", "rating": 570, "opRating": 429}, {"opponent": "wormadam_sandy", "rating": 570, "opRating": 429}, {"opponent": "ma<PERSON>le", "rating": 526, "opRating": 473}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 517, "opRating": 482}, {"opponent": "varoom", "rating": 517, "opRating": 482}], "counters": [{"opponent": "umbreon", "rating": 132}, {"opponent": "tyrunt", "rating": 135}, {"opponent": "ninetales", "rating": 198}, {"opponent": "nidoking", "rating": 224}, {"opponent": "magmar", "rating": 290}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 15971}, {"moveId": "EMBER", "uses": 9029}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 2236}, {"moveId": "FLAME_CHARGE", "uses": 16109}, {"moveId": "FLAMETHROWER", "uses": 6730}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "FLAMETHROWER"], "score": 92.6, "stats": {"product": 760, "atk": 89.9, "def": 74.7, "hp": 113}}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 4, "matchups": [], "counters": [{"opponent": "stunfisk_galarian", "rating": 0}, {"opponent": "magmar", "rating": 0}, {"opponent": "steelix", "rating": 0}, {"opponent": "tyrunt", "rating": 3}, {"opponent": "nidoking", "rating": 7}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 13211}, {"moveId": "FEINT_ATTACK", "uses": 11789}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 4861}, {"moveId": "SHADOW_BALL", "uses": 6264}, {"moveId": "FOCUS_BLAST", "uses": 5376}, {"moveId": "DARK_PULSE", "uses": 8511}]}, "moveset": ["SNARL", "DARK_PULSE", "SHADOW_BALL"], "score": 92.5, "stats": {"product": 8, "atk": 26.7, "def": 18.6, "hp": 16}}, {"speciesId": "dustox", "speciesName": "Dustox", "rating": 516, "matchups": [{"opponent": "nidoking", "rating": 869, "opRating": 130}, {"opponent": "dedenne", "rating": 676, "opRating": 323}, {"opponent": "toxapex", "rating": 623, "opRating": 376}, {"opponent": "nidoqueen", "rating": 619, "opRating": 380}, {"opponent": "araquanid", "rating": 619, "opRating": 380}], "counters": [{"opponent": "<PERSON>ay", "rating": 83}, {"opponent": "incineroar", "rating": 94}, {"opponent": "stunfisk_galarian", "rating": 186}, {"opponent": "moltres_galarian", "rating": 221}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 10102}, {"moveId": "CONFUSION", "uses": 14898}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8795}, {"moveId": "SILVER_WIND", "uses": 8521}, {"moveId": "BUG_BUZZ", "uses": 7681}]}, "moveset": ["CONFUSION", "SLUDGE_BOMB", "SILVER_WIND"], "score": 92.4, "stats": {"product": 2005, "atk": 94.9, "def": 148.7, "hp": 142}}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 619, "matchups": [{"opponent": "dedenne", "rating": 919, "opRating": 80}, {"opponent": "nidoqueen", "rating": 790, "opRating": 209}, {"opponent": "stunfisk_galarian", "rating": 687, "opRating": 312}, {"opponent": "steelix", "rating": 677, "opRating": 322}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 670, "opRating": 329}], "counters": [{"opponent": "heatran", "rating": 113}, {"opponent": "typhlosion", "rating": 119}, {"opponent": "magmar", "rating": 125}, {"opponent": "magmortar", "rating": 129}, {"opponent": "litleo", "rating": 129}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 15130}, {"moveId": "ICE_SHARD", "uses": 9870}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 4740}, {"moveId": "RETURN", "uses": 1656}, {"moveId": "HIGH_HORSEPOWER", "uses": 5991}, {"moveId": "BULLDOZE", "uses": 4901}, {"moveId": "AVALANCHE", "uses": 7684}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "HIGH_HORSEPOWER"], "score": 92.3, "stats": {"product": 1842, "atk": 121, "def": 98.1, "hp": 155}}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 708, "matchups": [{"opponent": "tyrunt", "rating": 619, "opRating": 380}, {"opponent": "nidoking", "rating": 580, "opRating": 419}, {"opponent": "steelix", "rating": 551, "opRating": 448}, {"opponent": "nidoqueen", "rating": 519, "opRating": 480}, {"opponent": "stunfisk_galarian", "rating": 512, "opRating": 487}], "counters": [{"opponent": "araquanid", "rating": 172}, {"opponent": "obstagoon", "rating": 242}, {"opponent": "pawniard", "rating": 254}, {"opponent": "weezing_galarian", "rating": 278}, {"opponent": "magmar", "rating": 406}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 13764}, {"moveId": "FEINT_ATTACK", "uses": 11236}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 4152}, {"moveId": "LAST_RESORT", "uses": 5569}, {"moveId": "FOUL_PLAY", "uses": 10687}, {"moveId": "DARK_PULSE", "uses": 4582}]}, "moveset": ["SNARL", "FOUL_PLAY", "LAST_RESORT"], "score": 92.2, "stats": {"product": 2476, "atk": 90.2, "def": 177, "hp": 155}}, {"speciesId": "charjabug", "speciesName": "Charjabug", "rating": 683, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 738, "opRating": 261}, {"opponent": "r<PERSON><PERSON>", "rating": 694, "opRating": 305}, {"opponent": "umbreon", "rating": 563, "opRating": 436}, {"opponent": "magmar", "rating": 555, "opRating": 444}, {"opponent": "magmortar", "rating": 547, "opRating": 452}], "counters": [{"opponent": "camerupt", "rating": 106}, {"opponent": "typhlosion", "rating": 192}, {"opponent": "nidoqueen", "rating": 205}, {"opponent": "stunfisk_galarian", "rating": 269}, {"opponent": "ninetales", "rating": 293}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 10981}, {"moveId": "SPARK", "uses": 7168}, {"moveId": "BUG_BITE", "uses": 6866}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10115}, {"moveId": "DISCHARGE", "uses": 7320}, {"moveId": "CRUNCH", "uses": 7583}]}, "moveset": ["VOLT_SWITCH", "X_SCISSOR", "DISCHARGE"], "score": 92.2, "stats": {"product": 1936, "atk": 116, "def": 132.4, "hp": 126}}, {"speciesId": "persian_alolan", "speciesName": "Persian (Alolan)", "rating": 487, "matchups": [{"opponent": "<PERSON>on", "rating": 656, "opRating": 343}, {"opponent": "sandslash_alolan", "rating": 541, "opRating": 458}, {"opponent": "amoon<PERSON>s", "rating": 503, "opRating": 496}, {"opponent": "minun", "rating": 503, "opRating": 496}, {"opponent": "nuzleaf", "rating": 503, "opRating": 496}], "counters": [{"opponent": "obstagoon", "rating": 134}, {"opponent": "weezing_galarian", "rating": 147}, {"opponent": "dedenne", "rating": 176}, {"opponent": "magmar", "rating": 272}, {"opponent": "nidoking", "rating": 287}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 8979}, {"moveId": "FEINT_ATTACK", "uses": 16021}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 5028}, {"moveId": "PLAY_ROUGH", "uses": 3096}, {"moveId": "PAYBACK", "uses": 3625}, {"moveId": "FOUL_PLAY", "uses": 9298}, {"moveId": "DARK_PULSE", "uses": 3968}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "PLAY_ROUGH"], "score": 92.1, "stats": {"product": 1824, "atk": 122.9, "def": 113.2, "hp": 131}}, {"speciesId": "gulpin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 270, "matchups": [{"opponent": "obstagoon", "rating": 621, "opRating": 378}, {"opponent": "weavile", "rating": 528, "opRating": 471}, {"opponent": "kakuna", "rating": 525, "opRating": 474}, {"opponent": "zubat", "rating": 509, "opRating": 490}, {"opponent": "pikachu_horizons", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 95}, {"opponent": "nidoking", "rating": 98}, {"opponent": "nidoqueen", "rating": 100}, {"opponent": "stunfisk_galarian", "rating": 162}, {"opponent": "magmar", "rating": 205}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 18219}, {"moveId": "POUND", "uses": 6781}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 10363}, {"moveId": "ICE_BEAM", "uses": 9194}, {"moveId": "GUNK_SHOT", "uses": 5456}]}, "moveset": ["ROCK_SMASH", "SLUDGE", "ICE_BEAM"], "score": 92.1, "stats": {"product": 1201, "atk": 79.8, "def": 95.7, "hp": 157}}, {"speciesId": "minun", "speciesName": "<PERSON><PERSON>", "rating": 591, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 740, "opRating": 259}, {"opponent": "magby", "rating": 593, "opRating": 406}, {"opponent": "magmortar", "rating": 589, "opRating": 410}, {"opponent": "dedenne", "rating": 589, "opRating": 410}, {"opponent": "umbreon", "rating": 542, "opRating": 457}], "counters": [{"opponent": "sandslash_alolan", "rating": 116}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}, {"opponent": "nidoking", "rating": 192}, {"opponent": "stunfisk_galarian", "rating": 239}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11501}, {"moveId": "QUICK_ATTACK", "uses": 13499}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3102}, {"moveId": "SWIFT", "uses": 10401}, {"moveId": "GRASS_KNOT", "uses": 4301}, {"moveId": "DISCHARGE", "uses": 7216}]}, "moveset": ["QUICK_ATTACK", "THUNDERBOLT", "GRASS_KNOT"], "score": 91.9, "stats": {"product": 1910, "atk": 116.7, "def": 126.8, "hp": 129}}, {"speciesId": "plusle", "speciesName": "<PERSON><PERSON>", "rating": 526, "matchups": [{"opponent": "em<PERSON>ga", "rating": 688, "opRating": 312}, {"opponent": "r<PERSON><PERSON>", "rating": 679, "opRating": 320}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 596, "opRating": 404}, {"opponent": "arm<PERSON>", "rating": 576, "opRating": 424}, {"opponent": "magmortar", "rating": 508, "opRating": 492}], "counters": [{"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}, {"opponent": "nidoking", "rating": 240}, {"opponent": "stunfisk_galarian", "rating": 295}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11390}, {"moveId": "QUICK_ATTACK", "uses": 13610}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3091}, {"moveId": "SWIFT", "uses": 10408}, {"moveId": "GRASS_KNOT", "uses": 4290}, {"moveId": "DISCHARGE", "uses": 7225}]}, "moveset": ["QUICK_ATTACK", "THUNDERBOLT", "GRASS_KNOT"], "score": 91.9, "stats": {"product": 1733, "atk": 129.2, "def": 107.3, "hp": 125}}, {"speciesId": "nuzleaf", "speciesName": "Nuz<PERSON>", "rating": 490, "matchups": [{"opponent": "manectric", "rating": 678, "opRating": 321}, {"opponent": "piloswine", "rating": 614, "opRating": 385}, {"opponent": "r<PERSON><PERSON>", "rating": 595, "opRating": 404}, {"opponent": "steelix", "rating": 579, "opRating": 420}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 528, "opRating": 471}], "counters": [{"opponent": "muk_alolan", "rating": 140}, {"opponent": "weezing_galarian", "rating": 147}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "magmar", "rating": 245}, {"opponent": "nidoking", "rating": 287}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 8002}, {"moveId": "FEINT_ATTACK", "uses": 16998}], "chargedMoves": [{"moveId": "RETURN", "uses": 3195}, {"moveId": "LEAF_BLADE", "uses": 11103}, {"moveId": "GRASS_KNOT", "uses": 2196}, {"moveId": "FOUL_PLAY", "uses": 8492}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 91.9, "stats": {"product": 1536, "atk": 125.2, "def": 78.1, "hp": 157}}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 634, "matchups": [{"opponent": "tyrunt", "rating": 738, "opRating": 261}, {"opponent": "avalugg_his<PERSON>an", "rating": 738, "opRating": 261}, {"opponent": "toxapex", "rating": 634, "opRating": 365}, {"opponent": "steelix", "rating": 603, "opRating": 396}, {"opponent": "umbreon", "rating": 588, "opRating": 411}], "counters": [{"opponent": "crocalor", "rating": 92}, {"opponent": "nidoking", "rating": 102}, {"opponent": "ninetales", "rating": 115}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 119}, {"opponent": "magmar", "rating": 138}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 4356}, {"moveId": "METAL_CLAW", "uses": 8353}, {"moveId": "LOCK_ON", "uses": 12290}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": null}, {"moveId": "RETURN", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "FLASH_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 91.7, "stats": {"product": 2353, "atk": 95.4, "def": 189.6, "hp": 130}}, {"speciesId": "metang", "speciesName": "Metang", "rating": 565, "matchups": [{"opponent": "arctibax", "rating": 786, "opRating": 213}, {"opponent": "tyrunt", "rating": 724, "opRating": 275}, {"opponent": "toxapex", "rating": 724, "opRating": 275}, {"opponent": "avalugg_his<PERSON>an", "rating": 720, "opRating": 279}, {"opponent": "swalot", "rating": 658, "opRating": 341}], "counters": [{"opponent": "ninetales", "rating": 91}, {"opponent": "heatran", "rating": 113}, {"opponent": "stunfisk_galarian", "rating": 207}, {"opponent": "nidoking", "rating": 240}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 256}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 6361}, {"moveId": "METAL_CLAW", "uses": 18639}], "chargedMoves": [{"moveId": "RETURN", "uses": 5400}, {"moveId": "PSYSHOCK", "uses": 11910}, {"moveId": "PSYCHIC", "uses": 3246}, {"moveId": "GYRO_BALL", "uses": 4471}]}, "moveset": ["METAL_CLAW", "PSYSHOCK", "GYRO_BALL"], "score": 91.6, "stats": {"product": 2043, "atk": 108.9, "def": 145.4, "hp": 129}}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 508, "matchups": [{"opponent": "magby", "rating": 621, "opRating": 378}, {"opponent": "obstagoon", "rating": 590, "opRating": 409}, {"opponent": "lileep", "rating": 530, "opRating": 469}, {"opponent": "over<PERSON><PERSON>l", "rating": 515, "opRating": 484}, {"opponent": "cradily", "rating": 507, "opRating": 492}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 173}, {"opponent": "stunfisk_galarian", "rating": 204}, {"opponent": "steelix", "rating": 205}, {"opponent": "nidoking", "rating": 240}, {"opponent": "magmar", "rating": 263}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 5697}, {"moveId": "INFESTATION", "uses": 19303}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 5125}, {"moveId": "GUNK_SHOT", "uses": 4696}, {"moveId": "BODY_SLAM", "uses": 13124}, {"moveId": "ACID_SPRAY", "uses": 2034}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 91.4, "stats": {"product": 1842, "atk": 121.7, "def": 114.6, "hp": 132}}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Regieleki", "rating": 424, "matchups": [{"opponent": "manectric", "rating": 585, "opRating": 414}, {"opponent": "em<PERSON>ga", "rating": 576, "opRating": 423}, {"opponent": "moltres_galarian", "rating": 534, "opRating": 465}, {"opponent": "beedrill", "rating": 534, "opRating": 465}, {"opponent": "heatran", "rating": 517, "opRating": 482}], "counters": [{"opponent": "stunfisk_galarian", "rating": 76}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 82}, {"opponent": "magmar", "rating": 89}, {"opponent": "nidoking", "rating": 90}, {"opponent": "ninetales", "rating": 91}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 9044}, {"moveId": "THUNDER_SHOCK", "uses": 8578}, {"moveId": "LOCK_ON", "uses": 7358}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 5187}, {"moveId": "THUNDER", "uses": 12329}, {"moveId": "HYPER_BEAM", "uses": 7513}]}, "moveset": ["LOCK_ON", "THUNDER", "HYPER_BEAM"], "score": 91.4, "stats": {"product": 1469, "atk": 152.3, "def": 82.4, "hp": 117}}, {"speciesId": "ma<PERSON>le", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 551, "matchups": [{"opponent": "tyrunt", "rating": 893, "opRating": 106}, {"opponent": "arctibax", "rating": 888, "opRating": 111}, {"opponent": "avalugg_his<PERSON>an", "rating": 722, "opRating": 277}, {"opponent": "umbreon", "rating": 662, "opRating": 337}, {"opponent": "araquanid", "rating": 568, "opRating": 431}], "counters": [{"opponent": "ninetales", "rating": 91}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magmar", "rating": 125}, {"opponent": "nidoking", "rating": 145}, {"opponent": "stunfisk_galarian", "rating": 215}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 4661}, {"moveId": "FIRE_FANG", "uses": 5731}, {"moveId": "FAIRY_WIND", "uses": 6093}, {"moveId": "BITE", "uses": 2901}, {"moveId": "ASTONISH", "uses": 5613}], "chargedMoves": [{"moveId": "VICE_GRIP", "uses": 4582}, {"moveId": "RETURN", "uses": 5042}, {"moveId": "POWER_UP_PUNCH", "uses": 4326}, {"moveId": "PLAY_ROUGH", "uses": 5099}, {"moveId": "IRON_HEAD", "uses": 5943}]}, "moveset": ["FAIRY_WIND", "IRON_HEAD", "PLAY_ROUGH"], "score": 91.3, "stats": {"product": 1785, "atk": 125.8, "def": 121.1, "hp": 117}}, {"speciesId": "poochyena", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 204, "matchups": [{"opponent": "rotom_heat", "rating": 595, "opRating": 404}, {"opponent": "toxel", "rating": 590, "opRating": 409}, {"opponent": "charcadet", "rating": 576, "opRating": 423}, {"opponent": "bellsprout", "rating": 538, "opRating": 461}, {"opponent": "tynamo", "rating": 504, "opRating": 495}], "counters": [{"opponent": "steelix", "rating": 60}, {"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "magmar", "rating": 93}, {"opponent": "nidoking", "rating": 98}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 10274}, {"moveId": "SNARL", "uses": 14726}], "chargedMoves": [{"moveId": "RETURN", "uses": 3191}, {"moveId": "POISON_FANG", "uses": 4977}, {"moveId": "DIG", "uses": 7448}, {"moveId": "CRUNCH", "uses": 9403}]}, "moveset": ["SNARL", "CRUNCH", "DIG"], "score": 91.3, "stats": {"product": 625, "atk": 93.2, "def": 63.8, "hp": 105}}, {"speciesId": "whirlipede", "speciesName": "Whirlipede", "rating": 511, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 743, "opRating": 256}, {"opponent": "electrode_hisuian", "rating": 725, "opRating": 274}, {"opponent": "araquanid", "rating": 623, "opRating": 376}, {"opponent": "dedenne", "rating": 619, "opRating": 380}, {"opponent": "magby", "rating": 575, "opRating": 424}], "counters": [{"opponent": "magnezone", "rating": 63}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 86}, {"opponent": "ninetales", "rating": 134}, {"opponent": "stunfisk_galarian", "rating": 136}, {"opponent": "steelix", "rating": 157}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14160}, {"moveId": "BUG_BITE", "uses": 10840}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8790}, {"moveId": "SIGNAL_BEAM", "uses": 7407}, {"moveId": "RETURN", "uses": 5523}, {"moveId": "GYRO_BALL", "uses": 3273}]}, "moveset": ["POISON_STING", "SLUDGE_BOMB", "SIGNAL_BEAM"], "score": 91.2, "stats": {"product": 1725, "atk": 96.6, "def": 157.9, "hp": 113}}, {"speciesId": "venipede", "speciesName": "Venipede", "rating": 223, "matchups": [{"opponent": "poochyena", "rating": 556, "opRating": 443}, {"opponent": "weepinbell", "rating": 535, "opRating": 464}, {"opponent": "zorua", "rating": 535, "opRating": 464}, {"opponent": "meowscarada", "rating": 530, "opRating": 469}, {"opponent": "weedle", "rating": 525, "opRating": 474}], "counters": [{"opponent": "stunfisk_galarian", "rating": 38}, {"opponent": "nidoqueen", "rating": 46}, {"opponent": "nidoking", "rating": 51}, {"opponent": "steelix", "rating": 52}, {"opponent": "magmar", "rating": 125}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14127}, {"moveId": "BUG_BITE", "uses": 10873}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8796}, {"moveId": "SIGNAL_BEAM", "uses": 7425}, {"moveId": "RETURN", "uses": 5524}, {"moveId": "GYRO_BALL", "uses": 3270}]}, "moveset": ["POISON_STING", "SLUDGE_BOMB", "SIGNAL_BEAM"], "score": 91.2, "stats": {"product": 773, "atk": 82.3, "def": 95.7, "hp": 98}}, {"speciesId": "fletchinder", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 623, "matchups": [{"opponent": "nidoking", "rating": 781, "opRating": 218}, {"opponent": "araquanid", "rating": 647, "opRating": 352}, {"opponent": "magmar", "rating": 573, "opRating": 426}, {"opponent": "ninetales", "rating": 528, "opRating": 471}, {"opponent": "stunfisk_galarian", "rating": 521, "opRating": 478}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "cradily", "rating": 195}, {"opponent": "talonflame", "rating": 202}, {"opponent": "typhlosion", "rating": 226}, {"opponent": "avalugg_his<PERSON>an", "rating": 227}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 6731}, {"moveId": "PECK", "uses": 5588}, {"moveId": "EMBER", "uses": 12673}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 1205}, {"moveId": "FLY", "uses": 8633}, {"moveId": "FLAME_CHARGE", "uses": 8652}, {"moveId": "AERIAL_ACE", "uses": 6568}]}, "moveset": ["EMBER", "FLY", "AERIAL_ACE"], "score": 91.1, "stats": {"product": 1801, "atk": 122.9, "def": 103.1, "hp": 142}}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 515, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 878, "opRating": 121}, {"opponent": "nidoking", "rating": 851, "opRating": 148}, {"opponent": "dedenne", "rating": 679, "opRating": 320}, {"opponent": "nidoqueen", "rating": 628, "opRating": 371}, {"opponent": "tyrunt", "rating": 613, "opRating": 386}], "counters": [{"opponent": "larve<PERSON>", "rating": 119}, {"opponent": "sandslash_alolan", "rating": 145}, {"opponent": "arcanine", "rating": 172}, {"opponent": "magmar", "rating": 191}, {"opponent": "magmortar", "rating": 199}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 6062}, {"moveId": "MAGICAL_LEAF", "uses": 11796}, {"moveId": "ACID", "uses": 7140}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 983}, {"moveId": "SLUDGE_BOMB", "uses": 5528}, {"moveId": "RETURN", "uses": 3364}, {"moveId": "LEAF_TORNADO", "uses": 2221}, {"moveId": "LEAF_BLADE", "uses": 11606}, {"moveId": "ACID_SPRAY", "uses": 1272}]}, "moveset": ["MAGICAL_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 91.1, "stats": {"product": 1648, "atk": 135.8, "def": 94.8, "hp": 128}}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 621, "matchups": [{"opponent": "talonflame", "rating": 665, "opRating": 334}, {"opponent": "r<PERSON><PERSON>", "rating": 655, "opRating": 344}, {"opponent": "moltres_galarian", "rating": 655, "opRating": 344}, {"opponent": "rapidash", "rating": 626, "opRating": 373}, {"opponent": "magmar", "rating": 543, "opRating": 456}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "steelix", "rating": 189}, {"opponent": "stunfisk_galarian", "rating": 198}, {"opponent": "nidoking", "rating": 204}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12804}, {"moveId": "THUNDER_SHOCK", "uses": 12196}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 2666}, {"moveId": "THUNDERBOLT", "uses": 3761}, {"moveId": "THUNDER", "uses": 3272}, {"moveId": "LAST_RESORT", "uses": 6534}, {"moveId": "DISCHARGE", "uses": 8775}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "LAST_RESORT"], "score": 91.1, "stats": {"product": 1612, "atk": 138.5, "def": 112.9, "hp": 103}}, {"speciesId": "absol", "speciesName": "Absol", "rating": 445, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 692, "opRating": 307}, {"opponent": "piloswine", "rating": 665, "opRating": 334}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 555, "opRating": 444}, {"opponent": "rapidash", "rating": 518, "opRating": 481}, {"opponent": "armarouge", "rating": 518, "opRating": 481}], "counters": [{"opponent": "obstagoon", "rating": 76}, {"opponent": "weezing_galarian", "rating": 82}, {"opponent": "araquanid", "rating": 109}, {"opponent": "magmar", "rating": 165}, {"opponent": "nidoking", "rating": 169}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 13121}, {"moveId": "PSYCHO_CUT", "uses": 11879}], "chargedMoves": [{"moveId": "THUNDER", "uses": 4584}, {"moveId": "PAYBACK", "uses": 4194}, {"moveId": "MEGAHORN", "uses": 7061}, {"moveId": "DARK_PULSE", "uses": 9177}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 90.8, "stats": {"product": 1423, "atk": 157.8, "def": 82.7, "hp": 109}}, {"speciesId": "grimer_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 628, "matchups": [{"opponent": "ninetales", "rating": 692, "opRating": 307}, {"opponent": "munchlax", "rating": 558, "opRating": 441}, {"opponent": "umbreon", "rating": 511, "opRating": 488}, {"opponent": "dedenne", "rating": 508, "opRating": 491}, {"opponent": "magmortar", "rating": 502, "opRating": 497}], "counters": [{"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "nidoking", "rating": 145}, {"opponent": "tyrunt", "rating": 189}, {"opponent": "stunfisk_galarian", "rating": 236}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 12420}, {"moveId": "BITE", "uses": 7705}, {"moveId": "ACID", "uses": 4876}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6849}, {"moveId": "RETURN", "uses": 3959}, {"moveId": "GUNK_SHOT", "uses": 1708}, {"moveId": "CRUNCH", "uses": 12382}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 90.7, "stats": {"product": 1819, "atk": 123.6, "def": 86, "hp": 171}}, {"speciesId": "klefki", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "matchups": [{"opponent": "tyrunt", "rating": 838, "opRating": 161}, {"opponent": "arctibax", "rating": 834, "opRating": 165}, {"opponent": "araquanid", "rating": 593, "opRating": 406}, {"opponent": "toxapex", "rating": 588, "opRating": 411}, {"opponent": "umbreon", "rating": 576, "opRating": 423}], "counters": [{"opponent": "obstagoon", "rating": 90}, {"opponent": "litleo", "rating": 115}, {"opponent": "magmar", "rating": 285}, {"opponent": "nidoking", "rating": 287}, {"opponent": "stunfisk_galarian", "rating": 343}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 10430}, {"moveId": "ASTONISH", "uses": 14570}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 5803}, {"moveId": "FOUL_PLAY", "uses": 11044}, {"moveId": "FLASH_CANNON", "uses": 4475}, {"moveId": "DRAINING_KISS", "uses": 3690}]}, "moveset": ["ASTONISH", "FOUL_PLAY", "PLAY_ROUGH"], "score": 90.6, "stats": {"product": 1885, "atk": 118.9, "def": 134.2, "hp": 118}}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 512, "matchups": [{"opponent": "arcanine", "rating": 688, "opRating": 311}, {"opponent": "avalugg_his<PERSON>an", "rating": 618, "opRating": 381}, {"opponent": "magby", "rating": 596, "opRating": 403}, {"opponent": "moltres_galarian", "rating": 575, "opRating": 424}, {"opponent": "steelix", "rating": 543, "opRating": 456}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 98}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13591}, {"moveId": "ASTONISH", "uses": 11409}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 10425}, {"moveId": "THUNDER", "uses": 4542}, {"moveId": "HYDRO_PUMP", "uses": 10071}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 90.6, "stats": {"product": 1721, "atk": 129.1, "def": 143.2, "hp": 93}}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 530, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 674, "opRating": 325}, {"opponent": "piloswine", "rating": 621, "opRating": 378}, {"opponent": "talonflame", "rating": 509, "opRating": 490}, {"opponent": "moltres_galarian", "rating": 509, "opRating": 490}, {"opponent": "over<PERSON><PERSON>l", "rating": 509, "opRating": 490}], "counters": [{"opponent": "nidoqueen", "rating": 82}, {"opponent": "electrode_hisuian", "rating": 84}, {"opponent": "nidoking", "rating": 90}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 205}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15120}, {"moveId": "SPARK", "uses": 9880}], "chargedMoves": [{"moveId": "THUNDER", "uses": 4038}, {"moveId": "POWER_WHIP", "uses": 6482}, {"moveId": "DISCHARGE", "uses": 10927}, {"moveId": "DAZZLING_GLEAM", "uses": 3505}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "POWER_WHIP"], "score": 90.5, "stats": {"product": 1334, "atk": 165.9, "def": 78, "hp": 103}}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 653, "matchups": [{"opponent": "dedenne", "rating": 900, "opRating": 100}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 713, "opRating": 286}, {"opponent": "arctibax", "rating": 665, "opRating": 334}, {"opponent": "steelix", "rating": 586, "opRating": 413}, {"opponent": "toxapex", "rating": 527, "opRating": 472}], "counters": [{"opponent": "amaura", "rating": 121}, {"opponent": "stunfisk_galarian", "rating": 174}, {"opponent": "tyrunt", "rating": 189}, {"opponent": "magmar", "rating": 285}, {"opponent": "nidoking", "rating": 287}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 18416}, {"moveId": "ROCK_SMASH", "uses": 6584}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 2045}, {"moveId": "THUNDER_PUNCH", "uses": 2810}, {"moveId": "SWIFT", "uses": 5515}, {"moveId": "ICE_PUNCH", "uses": 2966}, {"moveId": "HIGH_HORSEPOWER", "uses": 4914}, {"moveId": "FIRE_PUNCH", "uses": 4295}, {"moveId": "AERIAL_ACE", "uses": 2457}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 90.4, "stats": {"product": 1777, "atk": 126.1, "def": 97.1, "hp": 145}}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 642, "matchups": [{"opponent": "sandslash_alolan", "rating": 907, "opRating": 92}, {"opponent": "piloswine", "rating": 827, "opRating": 172}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 815, "opRating": 184}, {"opponent": "heatran", "rating": 537, "opRating": 462}, {"opponent": "amaura", "rating": 521, "opRating": 478}], "counters": [{"opponent": "tyrunt", "rating": 189}, {"opponent": "magby", "rating": 229}, {"opponent": "magmar", "rating": 272}, {"opponent": "qwilfish_his<PERSON>an", "rating": 300}, {"opponent": "stunfisk_galarian", "rating": 393}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 11512}, {"moveId": "FIRE_FANG", "uses": 13488}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 4275}, {"moveId": "FLAMETHROWER", "uses": 8447}, {"moveId": "FIRE_BLAST", "uses": 2335}, {"moveId": "CRUNCH", "uses": 9972}]}, "moveset": ["FIRE_FANG", "CRUNCH", "FLAMETHROWER"], "score": 90.3, "stats": {"product": 1590, "atk": 140.7, "def": 94.8, "hp": 119}}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 495, "matchups": [{"opponent": "nidoking", "rating": 777, "opRating": 222}, {"opponent": "magby", "rating": 683, "opRating": 316}, {"opponent": "magmar", "rating": 671, "opRating": 328}, {"opponent": "araquanid", "rating": 617, "opRating": 382}, {"opponent": "swalot", "rating": 601, "opRating": 398}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "<PERSON>on", "rating": 104}, {"opponent": "stunfisk_galarian", "rating": 121}, {"opponent": "steelix", "rating": 133}, {"opponent": "tyrunt", "rating": 189}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 3531}, {"moveId": "AIR_SLASH", "uses": 21469}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 4914}, {"moveId": "AIR_CUTTER", "uses": 2851}, {"moveId": "AERIAL_ACE", "uses": 17266}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 90.3, "stats": {"product": 1699, "atk": 130.8, "def": 101.4, "hp": 128}}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 489, "matchups": [{"opponent": "nidoking", "rating": 777, "opRating": 222}, {"opponent": "araquanid", "rating": 648, "opRating": 351}, {"opponent": "lileep", "rating": 574, "opRating": 425}, {"opponent": "obstagoon", "rating": 542, "opRating": 457}, {"opponent": "cradily", "rating": 535, "opRating": 464}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "<PERSON>on", "rating": 104}, {"opponent": "amaura", "rating": 112}, {"opponent": "tyrunt", "rating": 189}, {"opponent": "stunfisk_galarian", "rating": 248}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 3521}, {"moveId": "AIR_SLASH", "uses": 21479}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 4911}, {"moveId": "AIR_CUTTER", "uses": 2851}, {"moveId": "AERIAL_ACE", "uses": 17267}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 90.3, "stats": {"product": 1699, "atk": 130.8, "def": 101.4, "hp": 128}}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 806, "matchups": [{"opponent": "ninetales", "rating": 816, "opRating": 183}, {"opponent": "nidoking", "rating": 776, "opRating": 223}, {"opponent": "magmortar", "rating": 772, "opRating": 227}, {"opponent": "umbreon", "rating": 593, "opRating": 406}, {"opponent": "tyrunt", "rating": 589, "opRating": 410}], "counters": [{"opponent": "golbat", "rating": 285}, {"opponent": "slowbro_galarian", "rating": 286}, {"opponent": "dedenne", "rating": 308}, {"opponent": "talonflame", "rating": 404}, {"opponent": "stunfisk_galarian", "rating": 461}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 15254}, {"moveId": "EMBER", "uses": 9746}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 8878}, {"moveId": "RETURN", "uses": 2528}, {"moveId": "FLAMETHROWER", "uses": 2848}, {"moveId": "FIRE_PUNCH", "uses": 9122}, {"moveId": "FIRE_BLAST", "uses": 1551}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 90.2, "stats": {"product": 1645, "atk": 136.6, "def": 107.4, "hp": 112}}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 676, "matchups": [{"opponent": "magmortar", "rating": 878, "opRating": 121}, {"opponent": "toxapex", "rating": 734, "opRating": 265}, {"opponent": "araquanid", "rating": 678, "opRating": 321}, {"opponent": "magby", "rating": 591, "opRating": 408}, {"opponent": "magmar", "rating": 582, "opRating": 417}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "amaura", "rating": 140}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "avalugg_his<PERSON>an", "rating": 177}, {"opponent": "stunfisk_galarian", "rating": 298}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 16979}, {"moveId": "CHARGE_BEAM", "uses": 8021}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 1733}, {"moveId": "THUNDERBOLT", "uses": 4929}, {"moveId": "THUNDER", "uses": 2176}, {"moveId": "DRILL_PECK", "uses": 9709}, {"moveId": "ANCIENT_POWER", "uses": 6442}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDER"], "score": 90.2, "stats": {"product": 1640, "atk": 136.2, "def": 104.6, "hp": 115}}, {"speciesId": "oddish", "speciesName": "<PERSON><PERSON>", "rating": 428, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 762, "opRating": 237}, {"opponent": "r<PERSON><PERSON>", "rating": 733, "opRating": 266}, {"opponent": "avalugg_his<PERSON>an", "rating": 675, "opRating": 325}, {"opponent": "piloswine", "rating": 662, "opRating": 337}, {"opponent": "dedenne", "rating": 629, "opRating": 370}], "counters": [{"opponent": "heatran", "rating": 150}, {"opponent": "larve<PERSON>", "rating": 184}, {"opponent": "ninetales", "rating": 218}, {"opponent": "magmar", "rating": 267}, {"opponent": "stunfisk_galarian", "rating": 292}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 11935}, {"moveId": "ACID", "uses": 13065}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8135}, {"moveId": "SEED_BOMB", "uses": 6214}, {"moveId": "RETURN", "uses": 5051}, {"moveId": "MOONBLAST", "uses": 5557}]}, "moveset": ["RAZOR_LEAF", "SEED_BOMB", "SLUDGE_BOMB"], "score": 90.1, "stats": {"product": 1571, "atk": 122.6, "def": 106.7, "hp": 120}}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 743, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 834, "opRating": 165}, {"opponent": "toxapex", "rating": 770, "opRating": 229}, {"opponent": "tyrunt", "rating": 653, "opRating": 346}, {"opponent": "nidoqueen", "rating": 616, "opRating": 383}, {"opponent": "magmortar", "rating": 584, "opRating": 415}], "counters": [{"opponent": "stunfisk_galarian", "rating": 192}, {"opponent": "nidoking", "rating": 271}, {"opponent": "ninetales", "rating": 293}, {"opponent": "piloswine", "rating": 322}, {"opponent": "heatran", "rating": 359}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 9867}, {"moveId": "IRON_TAIL", "uses": 3923}, {"moveId": "DRAGON_TAIL", "uses": 11219}], "chargedMoves": [{"moveId": "RETURN", "uses": 1905}, {"moveId": "PSYCHIC_FANGS", "uses": 4878}, {"moveId": "HEAVY_SLAM", "uses": 2558}, {"moveId": "EARTHQUAKE", "uses": 5783}, {"moveId": "CRUNCH", "uses": 4056}, {"moveId": "BREAKING_SWIPE", "uses": 5828}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 90, "stats": {"product": 2261, "atk": 98.4, "def": 185.2, "hp": 124}}, {"speciesId": "arbok", "speciesName": "<PERSON>rbok", "rating": 482, "matchups": [{"opponent": "cradily", "rating": 673, "opRating": 326}, {"opponent": "tyrunt", "rating": 632, "opRating": 367}, {"opponent": "magmortar", "rating": 590, "opRating": 409}, {"opponent": "obstagoon", "rating": 545, "opRating": 454}, {"opponent": "lileep", "rating": 528, "opRating": 471}], "counters": [{"opponent": "klefki", "rating": 97}, {"opponent": "stunfisk_galarian", "rating": 162}, {"opponent": "steelix", "rating": 165}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 177}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 12061}, {"moveId": "BITE", "uses": 6987}, {"moveId": "ACID", "uses": 5942}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 6008}, {"moveId": "RETURN", "uses": 5448}, {"moveId": "GUNK_SHOT", "uses": 2344}, {"moveId": "DARK_PULSE", "uses": 9162}, {"moveId": "ACID_SPRAY", "uses": 2040}]}, "moveset": ["DRAGON_TAIL", "DARK_PULSE", "SLUDGE_WAVE"], "score": 90, "stats": {"product": 1799, "atk": 123.7, "def": 120.1, "hp": 121}}, {"speciesId": "houndour", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 563, "matchups": [{"opponent": "sandslash_alolan", "rating": 862, "opRating": 137}, {"opponent": "piloswine", "rating": 812, "opRating": 187}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 754, "opRating": 245}, {"opponent": "lokix", "rating": 733, "opRating": 266}, {"opponent": "castform_sunny", "rating": 508, "opRating": 491}], "counters": [{"opponent": "tyrunt", "rating": 131}, {"opponent": "magmar", "rating": 205}, {"opponent": "qwilfish_his<PERSON>an", "rating": 262}, {"opponent": "nidoking", "rating": 318}, {"opponent": "stunfisk_galarian", "rating": 357}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 10422}, {"moveId": "EMBER", "uses": 14578}], "chargedMoves": [{"moveId": "RETURN", "uses": 3445}, {"moveId": "FLAMETHROWER", "uses": 8504}, {"moveId": "DARK_PULSE", "uses": 3568}, {"moveId": "CRUNCH", "uses": 9547}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 89.9, "stats": {"product": 1387, "atk": 140.3, "def": 82.3, "hp": 120}}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 663, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 815, "opRating": 185}, {"opponent": "stunfisk_galarian", "rating": 695, "opRating": 305}, {"opponent": "rapidash", "rating": 679, "opRating": 320}, {"opponent": "typhlosion", "rating": 679, "opRating": 320}, {"opponent": "steelix", "rating": 670, "opRating": 330}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "talonflame", "rating": 217}, {"opponent": "ninetales", "rating": 218}, {"opponent": "magby", "rating": 237}, {"opponent": "magmar", "rating": 254}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 14624}, {"moveId": "EMBER", "uses": 10376}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 8015}, {"moveId": "OVERHEAT", "uses": 5329}, {"moveId": "LAST_RESORT", "uses": 3494}, {"moveId": "HEAT_WAVE", "uses": 924}, {"moveId": "FLAMETHROWER", "uses": 5733}, {"moveId": "FIRE_BLAST", "uses": 1519}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 89.8, "stats": {"product": 1558, "atk": 141.6, "def": 109.9, "hp": 100}}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 572, "matchups": [{"opponent": "talonflame", "rating": 712, "opRating": 287}, {"opponent": "nidoking", "rating": 570, "opRating": 429}, {"opponent": "araquanid", "rating": 561, "opRating": 438}, {"opponent": "munchlax", "rating": 533, "opRating": 466}, {"opponent": "ninetales", "rating": 514, "opRating": 485}], "counters": [{"opponent": "<PERSON>on", "rating": 87}, {"opponent": "steelix", "rating": 112}, {"opponent": "amaura", "rating": 112}, {"opponent": "stunfisk_galarian", "rating": 210}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 239}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 14967}, {"moveId": "STEEL_WING", "uses": 10033}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 7736}, {"moveId": "CRUNCH", "uses": 7145}, {"moveId": "ANCIENT_POWER", "uses": 10128}]}, "moveset": ["WING_ATTACK", "ANCIENT_POWER", "DRAGON_CLAW"], "score": 89.8, "stats": {"product": 1386, "atk": 160.6, "def": 81.3, "hp": 106}}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 530, "matchups": [{"opponent": "obstagoon", "rating": 880, "opRating": 119}, {"opponent": "dedenne", "rating": 766, "opRating": 233}, {"opponent": "weezing_galarian", "rating": 714, "opRating": 285}, {"opponent": "moltres_galarian", "rating": 652, "opRating": 347}, {"opponent": "arctibax", "rating": 538, "opRating": 461}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "steelix", "rating": 145}, {"opponent": "stunfisk_galarian", "rating": 177}, {"opponent": "litleo", "rating": 244}, {"opponent": "nidoking", "rating": 307}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 14605}, {"moveId": "BUG_BITE", "uses": 10395}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 9560}, {"moveId": "SLUDGE_BOMB", "uses": 5847}, {"moveId": "RETURN", "uses": 3666}, {"moveId": "MEGAHORN", "uses": 3816}, {"moveId": "GYRO_BALL", "uses": 2086}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "SLUDGE_BOMB"], "score": 89.8, "stats": {"product": 1686, "atk": 132.6, "def": 121, "hp": 105}}, {"speciesId": "rattata_alolan", "speciesName": "Rattata (Alolan)", "rating": 249, "matchups": [{"opponent": "poochyena", "rating": 602, "opRating": 397}, {"opponent": "tynamo", "rating": 602, "opRating": 397}, {"opponent": "<PERSON>ay", "rating": 561, "opRating": 438}, {"opponent": "toxel", "rating": 540, "opRating": 459}, {"opponent": "zubat", "rating": 525, "opRating": 474}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "nidoking", "rating": 94}, {"opponent": "steelix", "rating": 100}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "magmar", "rating": 111}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 10951}, {"moveId": "QUICK_ATTACK", "uses": 14049}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 5377}, {"moveId": "RETURN", "uses": 2465}, {"moveId": "HYPER_FANG", "uses": 7193}, {"moveId": "CRUNCH", "uses": 9968}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_FANG"], "score": 89.7, "stats": {"product": 694, "atk": 99.1, "def": 71.4, "hp": 98}}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 714, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 886, "opRating": 113}, {"opponent": "nidoqueen", "rating": 831, "opRating": 168}, {"opponent": "dedenne", "rating": 777, "opRating": 222}, {"opponent": "steelix", "rating": 640, "opRating": 359}, {"opponent": "stunfisk_galarian", "rating": 613, "opRating": 386}], "counters": [{"opponent": "victini", "rating": 166}, {"opponent": "magmar", "rating": 191}, {"opponent": "magmortar", "rating": 199}, {"opponent": "magby", "rating": 204}, {"opponent": "nidoking", "rating": 307}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17627}, {"moveId": "BUG_BITE", "uses": 7373}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 5257}, {"moveId": "MAGMA_STORM", "uses": 8375}, {"moveId": "IRON_HEAD", "uses": 2341}, {"moveId": "FLAMETHROWER", "uses": 2276}, {"moveId": "FIRE_BLAST", "uses": 1199}, {"moveId": "EARTH_POWER", "uses": 5581}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 89.6, "stats": {"product": 1689, "atk": 131.9, "def": 116.4, "hp": 110}}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 663, "matchups": [{"opponent": "talonflame", "rating": 773, "opRating": 226}, {"opponent": "r<PERSON><PERSON>", "rating": 695, "opRating": 304}, {"opponent": "toxapex", "rating": 669, "opRating": 330}, {"opponent": "ninetales", "rating": 656, "opRating": 343}, {"opponent": "araquanid", "rating": 556, "opRating": 443}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "dedenne", "rating": 127}, {"opponent": "nidoking", "rating": 188}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 19999}, {"moveId": "LOW_KICK", "uses": 5001}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 12327}, {"moveId": "THUNDERBOLT", "uses": 3838}, {"moveId": "THUNDER", "uses": 3345}, {"moveId": "RETURN", "uses": 5487}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 89.6, "stats": {"product": 1694, "atk": 130.8, "def": 112.5, "hp": 115}}, {"speciesId": "cacturne", "speciesName": "Cacturne", "rating": 578, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 880, "opRating": 119}, {"opponent": "steelix", "rating": 636, "opRating": 363}, {"opponent": "dedenne", "rating": 636, "opRating": 363}, {"opponent": "stunfisk_galarian", "rating": 632, "opRating": 367}, {"opponent": "electrode_hisuian", "rating": 628, "opRating": 371}], "counters": [{"opponent": "obstagoon", "rating": 195}, {"opponent": "muk_alolan", "rating": 195}, {"opponent": "skuntank", "rating": 210}, {"opponent": "typhlosion", "rating": 307}, {"opponent": "magmar", "rating": 375}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 10162}, {"moveId": "SAND_ATTACK", "uses": 8723}, {"moveId": "POISON_JAB", "uses": 6119}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 4892}, {"moveId": "RETURN", "uses": 2786}, {"moveId": "PAYBACK", "uses": 2803}, {"moveId": "GRASS_KNOT", "uses": 1984}, {"moveId": "DYNAMIC_PUNCH", "uses": 6300}, {"moveId": "DARK_PULSE", "uses": 6222}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DARK_PULSE"], "score": 89.6, "stats": {"product": 1506, "atk": 148.9, "def": 83.5, "hp": 121}}, {"speciesId": "beldum", "speciesName": "Beldum", "rating": 183, "matchups": [{"opponent": "trubbish", "rating": 659, "opRating": 340}, {"opponent": "zigzagoon_galarian", "rating": 659, "opRating": 340}, {"opponent": "ekans", "rating": 637, "opRating": 362}, {"opponent": "venonat", "rating": 597, "opRating": 402}, {"opponent": "gulpin", "rating": 553, "opRating": 446}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 49}, {"opponent": "stunfisk_galarian", "rating": 56}, {"opponent": "magmar", "rating": 80}, {"opponent": "magmortar", "rating": 83}, {"opponent": "nidoking", "rating": 98}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 25000}], "chargedMoves": [{"moveId": "RETURN", "uses": 11772}, {"moveId": "IRON_HEAD", "uses": 13228}]}, "moveset": ["TAKE_DOWN", "IRON_HEAD", "RETURN"], "score": 89.5, "stats": {"product": 1302, "atk": 93.2, "def": 123.5, "hp": 113}}, {"speciesId": "celesteela", "speciesName": "<PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "nidoqueen", "rating": 645, "opRating": 354}, {"opponent": "araquanid", "rating": 637, "opRating": 362}, {"opponent": "nidoking", "rating": 583, "opRating": 416}, {"opponent": "tyrunt", "rating": 561, "opRating": 438}, {"opponent": "toxapex", "rating": 538, "opRating": 461}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "steelix", "rating": 125}, {"opponent": "dedenne", "rating": 142}, {"opponent": "magnezone", "rating": 205}, {"opponent": "magmar", "rating": 254}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 13077}, {"moveId": "AIR_SLASH", "uses": 11923}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 2497}, {"moveId": "HEAVY_SLAM", "uses": 4922}, {"moveId": "BULLDOZE", "uses": 7986}, {"moveId": "BODY_SLAM", "uses": 9591}]}, "moveset": ["AIR_SLASH", "BODY_SLAM", "IRON_HEAD"], "score": 89.3, "stats": {"product": 1865, "atk": 119.5, "def": 119, "hp": 131}}, {"speciesId": "gloom", "speciesName": "Gloom", "rating": 454, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 792, "opRating": 207}, {"opponent": "r<PERSON><PERSON>", "rating": 761, "opRating": 238}, {"opponent": "piloswine", "rating": 726, "opRating": 273}, {"opponent": "avalugg_his<PERSON>an", "rating": 673, "opRating": 326}, {"opponent": "dedenne", "rating": 626, "opRating": 373}], "counters": [{"opponent": "celesteela", "rating": 148}, {"opponent": "heatran", "rating": 177}, {"opponent": "scolipede", "rating": 185}, {"opponent": "charizard", "rating": 188}, {"opponent": "ninetales", "rating": 218}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 11924}, {"moveId": "ACID", "uses": 13076}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 8785}, {"moveId": "RETURN", "uses": 5535}, {"moveId": "PETAL_BLIZZARD", "uses": 4554}, {"moveId": "MOONBLAST", "uses": 6122}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 89.2, "stats": {"product": 1831, "atk": 122.6, "def": 114.8, "hp": 130}}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 453, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 785, "opRating": 214}, {"opponent": "r<PERSON><PERSON>", "rating": 772, "opRating": 227}, {"opponent": "piloswine", "rating": 706, "opRating": 293}, {"opponent": "dedenne", "rating": 648, "opRating": 351}, {"opponent": "avalugg_his<PERSON>an", "rating": 648, "opRating": 351}], "counters": [{"opponent": "celesteela", "rating": 148}, {"opponent": "charizard", "rating": 188}, {"opponent": "ninetales", "rating": 218}, {"opponent": "magmar", "rating": 267}, {"opponent": "stunfisk_galarian", "rating": 355}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 12011}, {"moveId": "ACID", "uses": 12989}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 2047}, {"moveId": "SLUDGE_BOMB", "uses": 10587}, {"moveId": "PETAL_BLIZZARD", "uses": 5176}, {"moveId": "MOONBLAST", "uses": 7138}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 89.2, "stats": {"product": 1732, "atk": 129, "def": 110.9, "hp": 121}}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 707, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 704, "opRating": 295}, {"opponent": "magmar", "rating": 621, "opRating": 378}, {"opponent": "magmortar", "rating": 616, "opRating": 383}, {"opponent": "toxapex", "rating": 616, "opRating": 383}, {"opponent": "nidoqueen", "rating": 575, "opRating": 424}], "counters": [{"opponent": "nidoking", "rating": 287}, {"opponent": "steelix", "rating": 330}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 330}, {"opponent": "ninetales", "rating": 333}, {"opponent": "stunfisk_galarian", "rating": 360}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 13907}, {"moveId": "LICK", "uses": 11093}], "chargedMoves": [{"moveId": "GUNK_SHOT", "uses": 2711}, {"moveId": "BULLDOZE", "uses": 8220}, {"moveId": "BODY_SLAM", "uses": 14090}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 89.1, "stats": {"product": 2158, "atk": 104, "def": 95.1, "hp": 218}}, {"speciesId": "sneasel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 505, "matchups": [{"opponent": "moltres_galarian", "rating": 722, "opRating": 277}, {"opponent": "nidoqueen", "rating": 622, "opRating": 377}, {"opponent": "piloswine", "rating": 600, "opRating": 400}, {"opponent": "qwilfish_his<PERSON>an", "rating": 586, "opRating": 413}, {"opponent": "tyrunt", "rating": 559, "opRating": 440}], "counters": [{"opponent": "ninetales", "rating": 142}, {"opponent": "obstagoon", "rating": 144}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "magmar", "rating": 165}, {"opponent": "magmortar", "rating": 171}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 13738}, {"moveId": "FEINT_ATTACK", "uses": 11262}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 2813}, {"moveId": "RETURN", "uses": 2192}, {"moveId": "ICE_PUNCH", "uses": 5767}, {"moveId": "FOUL_PLAY", "uses": 6178}, {"moveId": "AVALANCHE", "uses": 8037}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICE_PUNCH"], "score": 88.9, "stats": {"product": 1650, "atk": 135.4, "def": 110.7, "hp": 110}}, {"speciesId": "pikachu_rock_star", "speciesName": "<PERSON><PERSON><PERSON> (Rock Star)", "rating": 439, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 757, "opRating": 242}, {"opponent": "charizard", "rating": 623, "opRating": 376}, {"opponent": "amaura", "rating": 528, "opRating": 471}, {"opponent": "bron<PERSON>", "rating": 514, "opRating": 485}, {"opponent": "voltorb", "rating": 504, "opRating": 495}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "charjabug", "rating": 83}, {"opponent": "nidoking", "rating": 157}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 174}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 17922}, {"moveId": "CHARM", "uses": 7078}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 12917}, {"moveId": "PLAY_ROUGH", "uses": 3929}, {"moveId": "METEOR_MASH", "uses": 8147}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "METEOR_MASH"], "score": 88.9, "stats": {"product": 1045, "atk": 106.7, "def": 93.2, "hp": 105}}, {"speciesId": "<PERSON><PERSON>", "speciesName": "Joltik", "rating": 506, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 877, "opRating": 122}, {"opponent": "electivire", "rating": 606, "opRating": 393}, {"opponent": "zapdos", "rating": 578, "opRating": 421}, {"opponent": "charjabug", "rating": 531, "opRating": 468}, {"opponent": "slowbro_galarian", "rating": 515, "opRating": 484}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "umbreon", "rating": 212}, {"opponent": "stunfisk_galarian", "rating": 245}, {"opponent": "magmar", "rating": 245}, {"opponent": "nidoking", "rating": 314}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 16653}, {"moveId": "CHARGE_BEAM", "uses": 8347}], "chargedMoves": [{"moveId": "RETURN", "uses": 3573}, {"moveId": "DISCHARGE", "uses": 7849}, {"moveId": "CROSS_POISON", "uses": 7607}, {"moveId": "BUG_BUZZ", "uses": 5983}]}, "moveset": ["SUCKER_PUNCH", "DISCHARGE", "CROSS_POISON"], "score": 88.8, "stats": {"product": 1267, "atk": 105, "def": 94.9, "hp": 127}}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 715, "matchups": [{"opponent": "dedenne", "rating": 830, "opRating": 169}, {"opponent": "magmortar", "rating": 789, "opRating": 210}, {"opponent": "talonflame", "rating": 698, "opRating": 301}, {"opponent": "magby", "rating": 553, "opRating": 446}, {"opponent": "ninetales", "rating": 512, "opRating": 487}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magnezone", "rating": 151}, {"opponent": "genesect", "rating": 155}, {"opponent": "stunfisk_galarian", "rating": 254}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13363}, {"moveId": "POISON_JAB", "uses": 11637}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 3959}, {"moveId": "SHADOW_BALL", "uses": 3623}, {"moveId": "ICE_BEAM", "uses": 3282}, {"moveId": "DARK_PULSE", "uses": 4949}, {"moveId": "AQUA_TAIL", "uses": 9204}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 88.7, "stats": {"product": 1697, "atk": 131.5, "def": 106.5, "hp": 121}}, {"speciesId": "bombirdier", "speciesName": "Bombirdier", "rating": 506, "matchups": [{"opponent": "nidoking", "rating": 754, "opRating": 245}, {"opponent": "swalot", "rating": 686, "opRating": 313}, {"opponent": "araquanid", "rating": 661, "opRating": 338}, {"opponent": "magmar", "rating": 555, "opRating": 444}, {"opponent": "magby", "rating": 550, "opRating": 449}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "amaura", "rating": 85}, {"opponent": "steelix", "rating": 100}, {"opponent": "avalugg_his<PERSON>an", "rating": 128}, {"opponent": "tyrunt", "rating": 143}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 13474}, {"moveId": "ROCK_THROW", "uses": 11526}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 7402}, {"moveId": "FLY", "uses": 9992}, {"moveId": "AERIAL_ACE", "uses": 7586}]}, "moveset": ["WING_ATTACK", "FLY", "PAYBACK"], "score": 88.7, "stats": {"product": 1742, "atk": 127.9, "def": 115.3, "hp": 118}}, {"speciesId": "electrike", "speciesName": "Electrike", "rating": 421, "matchups": [{"opponent": "charizard", "rating": 530, "opRating": 469}, {"opponent": "ekans", "rating": 517, "opRating": 482}, {"opponent": "helioptile", "rating": 517, "opRating": 482}, {"opponent": "houndour", "rating": 513, "opRating": 486}, {"opponent": "budew", "rating": 508, "opRating": 491}], "counters": [{"opponent": "sandslash_alolan", "rating": 84}, {"opponent": "amaura", "rating": 85}, {"opponent": "stunfisk_galarian", "rating": 130}, {"opponent": "magmar", "rating": 165}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 214}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11475}, {"moveId": "QUICK_ATTACK", "uses": 13525}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3404}, {"moveId": "SWIFT", "uses": 11681}, {"moveId": "RETURN", "uses": 2038}, {"moveId": "DISCHARGE", "uses": 7923}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "DISCHARGE"], "score": 88.7, "stats": {"product": 1024, "atk": 115.9, "def": 78.1, "hp": 113}}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 638, "matchups": [{"opponent": "nidoking", "rating": 743, "opRating": 256}, {"opponent": "magmar", "rating": 662, "opRating": 337}, {"opponent": "magby", "rating": 658, "opRating": 341}, {"opponent": "swalot", "rating": 594, "opRating": 405}, {"opponent": "araquanid", "rating": 576, "opRating": 423}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "typhlosion", "rating": 213}, {"opponent": "talonflame", "rating": 217}, {"opponent": "cradily", "rating": 223}, {"opponent": "stunfisk_galarian", "rating": 357}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 9768}, {"moveId": "FIRE_SPIN", "uses": 15232}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 5359}, {"moveId": "OVERHEAT", "uses": 7886}, {"moveId": "HEAT_WAVE", "uses": 1352}, {"moveId": "FIRE_BLAST", "uses": 2318}, {"moveId": "ANCIENT_POWER", "uses": 8079}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 88.6, "stats": {"product": 1636, "atk": 136.2, "def": 102.5, "hp": 117}}, {"speciesId": "archen", "speciesName": "<PERSON><PERSON>", "rating": 606, "matchups": [{"opponent": "ninetales", "rating": 734, "opRating": 265}, {"opponent": "talonflame", "rating": 717, "opRating": 282}, {"opponent": "typhlosion", "rating": 700, "opRating": 300}, {"opponent": "rapidash", "rating": 682, "opRating": 317}, {"opponent": "magmar", "rating": 530, "opRating": 469}], "counters": [{"opponent": "<PERSON>on", "rating": 87}, {"opponent": "amaura", "rating": 112}, {"opponent": "sandslash_alolan", "rating": 125}, {"opponent": "steelix", "rating": 149}, {"opponent": "stunfisk_galarian", "rating": 177}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 11480}, {"moveId": "QUICK_ATTACK", "uses": 13520}], "chargedMoves": [{"moveId": "RETURN", "uses": 2831}, {"moveId": "DRAGON_CLAW", "uses": 6819}, {"moveId": "CRUNCH", "uses": 6378}, {"moveId": "ANCIENT_POWER", "uses": 9004}]}, "moveset": ["QUICK_ATTACK", "ANCIENT_POWER", "DRAGON_CLAW"], "score": 88.5, "stats": {"product": 1399, "atk": 160, "def": 75.9, "hp": 115}}, {"speciesId": "wormadam_trash", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Trash)", "rating": 529, "matchups": [{"opponent": "swalot", "rating": 772, "opRating": 227}, {"opponent": "nidoking", "rating": 764, "opRating": 235}, {"opponent": "nidoqueen", "rating": 671, "opRating": 328}, {"opponent": "toxapex", "rating": 615, "opRating": 384}, {"opponent": "tyrunt", "rating": 570, "opRating": 429}], "counters": [{"opponent": "houndoom", "rating": 84}, {"opponent": "armarouge", "rating": 153}, {"opponent": "litleo", "rating": 192}, {"opponent": "magmar", "rating": 245}, {"opponent": "stunfisk_galarian", "rating": 301}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 6988}, {"moveId": "CONFUSION", "uses": 9144}, {"moveId": "BUG_BITE", "uses": 8864}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 5016}, {"moveId": "IRON_HEAD", "uses": 8566}, {"moveId": "BUG_BUZZ", "uses": 11405}]}, "moveset": ["CONFUSION", "BUG_BUZZ", "IRON_HEAD"], "score": 88.5, "stats": {"product": 2127, "atk": 105.1, "def": 150.9, "hp": 134}}, {"speciesId": "meltan", "speciesName": "Meltan", "rating": 376, "matchups": [{"opponent": "tentacruel", "rating": 747, "opRating": 252}, {"opponent": "avalugg_his<PERSON>an", "rating": 685, "opRating": 314}, {"opponent": "cradily", "rating": 669, "opRating": 330}, {"opponent": "toxapex", "rating": 570, "opRating": 429}, {"opponent": "amaura", "rating": 545, "opRating": 454}], "counters": [{"opponent": "stunfisk_galarian", "rating": 44}, {"opponent": "nidoqueen", "rating": 53}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "nidoking", "rating": 90}, {"opponent": "magmar", "rating": 125}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 25000}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 14936}, {"moveId": "FLASH_CANNON", "uses": 10064}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "FLASH_CANNON"], "score": 88.5, "stats": {"product": 1295, "atk": 111.7, "def": 95.7, "hp": 121}}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 641, "matchups": [{"opponent": "nidoking", "rating": 641, "opRating": 358}, {"opponent": "nidoqueen", "rating": 610, "opRating": 389}, {"opponent": "munchlax", "rating": 592, "opRating": 407}, {"opponent": "arctibax", "rating": 588, "opRating": 411}, {"opponent": "magmortar", "rating": 508, "opRating": 491}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 161}, {"opponent": "archen", "rating": 178}, {"opponent": "magmar", "rating": 183}, {"opponent": "magnezone", "rating": 200}, {"opponent": "stunfisk_galarian", "rating": 393}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 7922}, {"moveId": "FURY_CUTTER", "uses": 17078}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 2018}, {"moveId": "ROCK_BLAST", "uses": 8703}, {"moveId": "LIQUIDATION", "uses": 7969}, {"moveId": "CROSS_POISON", "uses": 6306}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "LIQUIDATION"], "score": 88.3, "stats": {"product": 1671, "atk": 133.3, "def": 110.8, "hp": 113}}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 469, "matchups": [{"opponent": "moltres_galarian", "rating": 575, "opRating": 424}, {"opponent": "amaura", "rating": 564, "opRating": 435}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 559, "opRating": 440}, {"opponent": "dedenne", "rating": 516, "opRating": 483}, {"opponent": "cradily", "rating": 505, "opRating": 494}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 90}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13570}, {"moveId": "ASTONISH", "uses": 11430}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 11036}, {"moveId": "THUNDER", "uses": 4787}, {"moveId": "BLIZZARD", "uses": 9184}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 88.3, "stats": {"product": 1721, "atk": 129.1, "def": 143.2, "hp": 93}}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 710, "matchups": [{"opponent": "araquanid", "rating": 797, "opRating": 202}, {"opponent": "ninetales", "rating": 709, "opRating": 290}, {"opponent": "nidoking", "rating": 683, "opRating": 316}, {"opponent": "magmar", "rating": 595, "opRating": 404}, {"opponent": "stunfisk_galarian", "rating": 538, "opRating": 461}], "counters": [{"opponent": "tyrunt", "rating": 112}, {"opponent": "cradily", "rating": 199}, {"opponent": "magmortar", "rating": 226}, {"opponent": "armarouge", "rating": 245}, {"opponent": "rapidash", "rating": 252}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 3163}, {"moveId": "PECK", "uses": 2422}, {"moveId": "INCINERATE", "uses": 12173}, {"moveId": "FIRE_SPIN", "uses": 7261}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 1519}, {"moveId": "FLY", "uses": 6814}, {"moveId": "FLAME_CHARGE", "uses": 7113}, {"moveId": "FIRE_BLAST", "uses": 1647}, {"moveId": "BRAVE_BIRD", "uses": 7992}]}, "moveset": ["INCINERATE", "FLY", "BRAVE_BIRD"], "score": 88.1, "stats": {"product": 1834, "atk": 122, "def": 114.6, "hp": 131}}, {"speciesId": "seviper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 614, "matchups": [{"opponent": "dedenne", "rating": 784, "opRating": 215}, {"opponent": "araquanid", "rating": 626, "opRating": 373}, {"opponent": "talonflame", "rating": 573, "opRating": 426}, {"opponent": "magby", "rating": 546, "opRating": 453}, {"opponent": "arctibax", "rating": 534, "opRating": 465}], "counters": [{"opponent": "steelix", "rating": 52}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "sandslash_alolan", "rating": 137}, {"opponent": "nidoking", "rating": 145}, {"opponent": "stunfisk_galarian", "rating": 227}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 18962}, {"moveId": "IRON_TAIL", "uses": 6038}], "chargedMoves": [{"moveId": "WRAP", "uses": 5672}, {"moveId": "POISON_FANG", "uses": 9295}, {"moveId": "CRUNCH", "uses": 10049}]}, "moveset": ["POISON_JAB", "POISON_FANG", "CRUNCH"], "score": 88.1, "stats": {"product": 1616, "atk": 138.8, "def": 89.5, "hp": 130}}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 548, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 883, "opRating": 116}, {"opponent": "tyrunt", "rating": 766, "opRating": 233}, {"opponent": "arctibax", "rating": 658, "opRating": 341}, {"opponent": "umbreon", "rating": 588, "opRating": 411}, {"opponent": "steelix", "rating": 556, "opRating": 443}], "counters": [{"opponent": "talonflame", "rating": 106}, {"opponent": "armarouge", "rating": 122}, {"opponent": "ninetales", "rating": 166}, {"opponent": "nidoking", "rating": 240}, {"opponent": "magmar", "rating": 285}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 13740}, {"moveId": "BUG_BITE", "uses": 11260}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 6579}, {"moveId": "DRILL_RUN", "uses": 12416}, {"moveId": "AERIAL_ACE", "uses": 5228}, {"moveId": "ACID_SPRAY", "uses": 782}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 87.9, "stats": {"product": 1673, "atk": 133.9, "def": 116.7, "hp": 107}}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 605, "matchups": [{"opponent": "nidoqueen", "rating": 815, "opRating": 185}, {"opponent": "tyrunt", "rating": 715, "opRating": 285}, {"opponent": "toxapex", "rating": 590, "opRating": 410}, {"opponent": "umbreon", "rating": 570, "opRating": 430}, {"opponent": "nidoking", "rating": 545, "opRating": 455}], "counters": [{"opponent": "darmanitan_standard", "rating": 72}, {"opponent": "weezing_galarian", "rating": 117}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 161}, {"opponent": "magmar", "rating": 174}, {"opponent": "stunfisk_galarian", "rating": 328}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 10481}, {"moveId": "FURY_CUTTER", "uses": 14519}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 9659}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 7706}, {"moveId": "MAGNET_BOMB", "uses": 6274}, {"moveId": "HYPER_BEAM", "uses": 1389}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 87.8, "stats": {"product": 1575, "atk": 141, "def": 111.6, "hp": 100}}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 553, "matchups": [{"opponent": "magmar", "rating": 708, "opRating": 291}, {"opponent": "magby", "rating": 705, "opRating": 294}, {"opponent": "magmortar", "rating": 701, "opRating": 298}, {"opponent": "steelix", "rating": 550, "opRating": 449}, {"opponent": "arcanine", "rating": 503, "opRating": 496}], "counters": [{"opponent": "litleo", "rating": 59}, {"opponent": "pyroar", "rating": 68}, {"opponent": "qwilfish_his<PERSON>an", "rating": 91}, {"opponent": "tyrunt", "rating": 158}, {"opponent": "nidoking", "rating": 161}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 9463}, {"moveId": "CONFUSION", "uses": 10831}, {"moveId": "ACID", "uses": 4694}], "chargedMoves": [{"moveId": "SURF", "uses": 8051}, {"moveId": "SLUDGE_WAVE", "uses": 3601}, {"moveId": "SHADOW_BALL", "uses": 4803}, {"moveId": "SCALD", "uses": 3924}, {"moveId": "FUTURE_SIGHT", "uses": 4570}]}, "moveset": ["HEX", "SURF", "SHADOW_BALL"], "score": 87.8, "stats": {"product": 1894, "atk": 117.9, "def": 115.5, "hp": 139}}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 664, "matchups": [{"opponent": "magmortar", "rating": 720, "opRating": 279}, {"opponent": "magmar", "rating": 713, "opRating": 286}, {"opponent": "magby", "rating": 710, "opRating": 289}, {"opponent": "ninetales", "rating": 696, "opRating": 303}, {"opponent": "dedenne", "rating": 613, "opRating": 386}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "qwilfish_his<PERSON>an", "rating": 170}, {"opponent": "nidoking", "rating": 295}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 13335}, {"moveId": "CONFUSION", "uses": 11665}], "chargedMoves": [{"moveId": "SURF", "uses": 5668}, {"moveId": "SLUDGE_BOMB", "uses": 3718}, {"moveId": "SCALD", "uses": 2775}, {"moveId": "PSYCHIC", "uses": 3298}, {"moveId": "FOCUS_BLAST", "uses": 3230}, {"moveId": "BRUTAL_SWING", "uses": 6326}]}, "moveset": ["POISON_JAB", "SCALD", "BRUTAL_SWING"], "score": 87.7, "stats": {"product": 1872, "atk": 119.1, "def": 108.3, "hp": 145}}, {"speciesId": "voltorb", "speciesName": "Voltorb", "rating": 389, "matchups": [{"opponent": "purrloin", "rating": 522, "opRating": 477}, {"opponent": "moltres", "rating": 517, "opRating": 482}, {"opponent": "varoom", "rating": 517, "opRating": 482}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 508, "opRating": 491}, {"opponent": "cynda<PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 98}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 11606}, {"moveId": "SPARK", "uses": 13394}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3810}, {"moveId": "SIGNAL_BEAM", "uses": 4969}, {"moveId": "RETURN", "uses": 4679}, {"moveId": "GYRO_BALL", "uses": 2564}, {"moveId": "DISCHARGE", "uses": 8949}]}, "moveset": ["SPARK", "DISCHARGE", "SIGNAL_BEAM"], "score": 87.7, "stats": {"product": 1247, "atk": 104.1, "def": 105.8, "hp": 113}}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 665, "matchups": [{"opponent": "tyrunt", "rating": 704, "opRating": 295}, {"opponent": "arctibax", "rating": 704, "opRating": 295}, {"opponent": "talonflame", "rating": 704, "opRating": 295}, {"opponent": "avalugg_his<PERSON>an", "rating": 700, "opRating": 300}, {"opponent": "munchlax", "rating": 604, "opRating": 395}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magby", "rating": 116}, {"opponent": "magmortar", "rating": 129}, {"opponent": "magmar", "rating": 138}, {"opponent": "stunfisk_galarian", "rating": 174}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 16842}, {"moveId": "IRON_TAIL", "uses": 8158}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 2714}, {"moveId": "ROCK_SLIDE", "uses": 9447}, {"moveId": "RETURN", "uses": 1840}, {"moveId": "HEAVY_SLAM", "uses": 3922}, {"moveId": "BODY_SLAM", "uses": 7055}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 87.4, "stats": {"product": 1962, "atk": 114.5, "def": 142.7, "hp": 120}}, {"speciesId": "larve<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 585, "matchups": [{"opponent": "piloswine", "rating": 650, "opRating": 350}, {"opponent": "araquanid", "rating": 584, "opRating": 415}, {"opponent": "obstagoon", "rating": 576, "opRating": 423}, {"opponent": "swalot", "rating": 557, "opRating": 442}, {"opponent": "amaura", "rating": 550, "opRating": 450}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "victini", "rating": 158}, {"opponent": "ninetales", "rating": 202}, {"opponent": "toxapex", "rating": 271}, {"opponent": "stunfisk_galarian", "rating": 349}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 14687}, {"moveId": "BUG_BITE", "uses": 10313}], "chargedMoves": [{"moveId": "FLAME_WHEEL", "uses": 2582}, {"moveId": "FLAME_CHARGE", "uses": 14030}, {"moveId": "BUG_BUZZ", "uses": 8361}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BUG_BUZZ"], "score": 87.4, "stats": {"product": 1697, "atk": 131.6, "def": 99.1, "hp": 130}}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 519, "matchups": [{"opponent": "nidoking", "rating": 688, "opRating": 311}, {"opponent": "magmar", "rating": 680, "opRating": 319}, {"opponent": "magby", "rating": 676, "opRating": 323}, {"opponent": "araquanid", "rating": 650, "opRating": 350}, {"opponent": "nidoqueen", "rating": 615, "opRating": 384}], "counters": [{"opponent": "steelix", "rating": 100}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "avalugg_his<PERSON>an", "rating": 136}, {"opponent": "amaura", "rating": 189}, {"opponent": "stunfisk_galarian", "rating": 192}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 9646}, {"moveId": "AIR_SLASH", "uses": 15354}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 2654}, {"moveId": "SHADOW_BALL", "uses": 6302}, {"moveId": "POISON_FANG", "uses": 3237}, {"moveId": "CROSS_POISON", "uses": 9770}, {"moveId": "AIR_CUTTER", "uses": 3046}]}, "moveset": ["AIR_SLASH", "POISON_FANG", "SHADOW_BALL"], "score": 87.4, "stats": {"product": 1832, "atk": 121.8, "def": 115.6, "hp": 130}}, {"speciesId": "toxapex", "speciesName": "Toxapex", "rating": 672, "matchups": [{"opponent": "ninetales", "rating": 737, "opRating": 262}, {"opponent": "magmar", "rating": 656, "opRating": 343}, {"opponent": "magmortar", "rating": 622, "opRating": 377}, {"opponent": "umbreon", "rating": 559, "opRating": 440}, {"opponent": "nidoqueen", "rating": 504, "opRating": 495}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "magnezone", "rating": 102}, {"opponent": "klinklang", "rating": 132}, {"opponent": "magnemite", "rating": 140}, {"opponent": "stunfisk_galarian", "rating": 218}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 16165}, {"moveId": "BITE", "uses": 8835}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 8392}, {"moveId": "GUNK_SHOT", "uses": 3306}, {"moveId": "BRINE", "uses": 13296}]}, "moveset": ["POISON_JAB", "BRINE", "SLUDGE_WAVE"], "score": 87.1, "stats": {"product": 2417, "atk": 92.2, "def": 222, "hp": 118}}, {"speciesId": "charmeleon", "speciesName": "Charmeleon", "rating": 659, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "steelix", "rating": 610, "opRating": 389}, {"opponent": "magby", "rating": 574, "opRating": 425}, {"opponent": "araquanid", "rating": 570, "opRating": 429}, {"opponent": "dedenne", "rating": 543, "opRating": 456}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "toxapex", "rating": 194}, {"opponent": "magmar", "rating": 227}, {"opponent": "talonflame", "rating": 232}, {"opponent": "magmortar", "rating": 259}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 4272}, {"moveId": "FIRE_FANG", "uses": 11211}, {"moveId": "EMBER", "uses": 9510}], "chargedMoves": [{"moveId": "RETURN", "uses": 4591}, {"moveId": "FLAME_BURST", "uses": 2556}, {"moveId": "FLAMETHROWER", "uses": 4246}, {"moveId": "FIRE_PUNCH", "uses": 13564}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "RETURN"], "score": 86.8, "stats": {"product": 1761, "atk": 127.1, "def": 109, "hp": 127}}, {"speciesId": "ferroseed", "speciesName": "Ferroseed", "rating": 404, "matchups": [{"opponent": "tyrunt", "rating": 630, "opRating": 369}, {"opponent": "weezing_galarian", "rating": 617, "opRating": 382}, {"opponent": "avalugg_his<PERSON>an", "rating": 596, "opRating": 403}, {"opponent": "cradily", "rating": 575, "opRating": 424}, {"opponent": "qwilfish_his<PERSON>an", "rating": 508, "opRating": 491}], "counters": [{"opponent": "magmar", "rating": 84}, {"opponent": "magmortar", "rating": 87}, {"opponent": "scorbunny", "rating": 106}, {"opponent": "nidoking", "rating": 177}, {"opponent": "stunfisk_galarian", "rating": 180}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 12724}, {"moveId": "METAL_CLAW", "uses": 12276}], "chargedMoves": [{"moveId": "RETURN", "uses": 9039}, {"moveId": "IRON_HEAD", "uses": 9772}, {"moveId": "GYRO_BALL", "uses": 3089}, {"moveId": "FLASH_CANNON", "uses": 3107}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "RETURN"], "score": 86.8, "stats": {"product": 1386, "atk": 81.5, "def": 142.8, "hp": 119}}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "talonflame", "rating": 800, "opRating": 199}, {"opponent": "dedenne", "rating": 657, "opRating": 342}, {"opponent": "steelix", "rating": 597, "opRating": 402}, {"opponent": "magmortar", "rating": 576, "opRating": 423}, {"opponent": "stunfisk_galarian", "rating": 566, "opRating": 433}], "counters": [{"opponent": "genesect", "rating": 75}, {"opponent": "nidoking", "rating": 129}, {"opponent": "weavile", "rating": 169}, {"opponent": "registeel", "rating": 184}, {"opponent": "metang", "rating": 209}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 10154}, {"moveId": "BULLET_SEED", "uses": 11372}, {"moveId": "ACID", "uses": 3463}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 7355}, {"moveId": "ROCK_SLIDE", "uses": 6962}, {"moveId": "RETURN", "uses": 2135}, {"moveId": "GRASS_KNOT", "uses": 3831}, {"moveId": "BULLDOZE", "uses": 4752}]}, "moveset": ["BULLET_SEED", "ROCK_SLIDE", "GRASS_KNOT"], "score": 85.8, "stats": {"product": 2112, "atk": 106.2, "def": 138.9, "hp": 143}}, {"speciesId": "venonat", "speciesName": "Venonat", "rating": 413, "matchups": [{"opponent": "nidoking", "rating": 806, "opRating": 193}, {"opponent": "swalot", "rating": 654, "opRating": 345}, {"opponent": "toxapex", "rating": 556, "opRating": 443}, {"opponent": "magmar", "rating": 531, "opRating": 468}, {"opponent": "magmortar", "rating": 517, "opRating": 482}], "counters": [{"opponent": "stunfisk_galarian", "rating": 106}, {"opponent": "umbreon", "rating": 145}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "steelix", "rating": 169}, {"opponent": "moltres_galarian", "rating": 184}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12718}, {"moveId": "BUG_BITE", "uses": 12282}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 7026}, {"moveId": "RETURN", "uses": 4916}, {"moveId": "PSYBEAM", "uses": 3454}, {"moveId": "POISON_FANG", "uses": 9589}]}, "moveset": ["CONFUSION", "POISON_FANG", "SIGNAL_BEAM"], "score": 85.8, "stats": {"product": 1326, "atk": 96.6, "def": 96.6, "hp": 142}}, {"speciesId": "charmander", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 449, "matchups": [{"opponent": "sandslash_alolan", "rating": 815, "opRating": 184}, {"opponent": "pikachu_pop_star", "rating": 509, "opRating": 490}, {"opponent": "pikachu_rock_star", "rating": 509, "opRating": 490}, {"opponent": "roselia", "rating": 509, "opRating": 490}, {"opponent": "victreebel", "rating": 509, "opRating": 490}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "toxapex", "rating": 220}, {"opponent": "qwilfish_his<PERSON>an", "rating": 225}, {"opponent": "nidoking", "rating": 279}, {"opponent": "stunfisk_galarian", "rating": 304}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 7437}, {"moveId": "EMBER", "uses": 17563}], "chargedMoves": [{"moveId": "RETURN", "uses": 5257}, {"moveId": "FLAME_CHARGE", "uses": 11834}, {"moveId": "FLAME_BURST", "uses": 3029}, {"moveId": "FLAMETHROWER", "uses": 4986}]}, "moveset": ["EMBER", "FLAME_CHARGE", "RETURN"], "score": 85.8, "stats": {"product": 1109, "atk": 110, "def": 90.7, "hp": 111}}, {"speciesId": "chim<PERSON>r", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 446, "matchups": [{"opponent": "sandslash_alolan", "rating": 827, "opRating": 172}, {"opponent": "a<PERSON><PERSON>", "rating": 512, "opRating": 487}, {"opponent": "pikachu_pop_star", "rating": 512, "opRating": 487}, {"opponent": "pikachu_rock_star", "rating": 512, "opRating": 487}, {"opponent": "cacturne", "rating": 508, "opRating": 491}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "toxapex", "rating": 220}, {"opponent": "qwilfish_his<PERSON>an", "rating": 225}, {"opponent": "nidoking", "rating": 279}, {"opponent": "stunfisk_galarian", "rating": 304}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 7510}, {"moveId": "EMBER", "uses": 17490}], "chargedMoves": [{"moveId": "RETURN", "uses": 5348}, {"moveId": "FLAME_WHEEL", "uses": 2269}, {"moveId": "FLAME_CHARGE", "uses": 12228}, {"moveId": "FLAMETHROWER", "uses": 5082}]}, "moveset": ["EMBER", "FLAME_CHARGE", "RETURN"], "score": 85.8, "stats": {"product": 1086, "atk": 107.5, "def": 84.8, "hp": 119}}, {"speciesId": "lileep", "speciesName": "<PERSON><PERSON>", "rating": 594, "matchups": [{"opponent": "dedenne", "rating": 675, "opRating": 324}, {"opponent": "steelix", "rating": 605, "opRating": 394}, {"opponent": "munchlax", "rating": 592, "opRating": 407}, {"opponent": "amaura", "rating": 569, "opRating": 430}, {"opponent": "r<PERSON><PERSON>", "rating": 556, "opRating": 443}], "counters": [{"opponent": "nidoking", "rating": 129}, {"opponent": "weavile", "rating": 136}, {"opponent": "metang", "rating": 201}, {"opponent": "ferrothorn", "rating": 208}, {"opponent": "revavroom", "rating": 214}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 10003}, {"moveId": "BULLET_SEED", "uses": 10880}, {"moveId": "ACID", "uses": 4127}], "chargedMoves": [{"moveId": "RETURN", "uses": 3760}, {"moveId": "MIRROR_COAT", "uses": 2561}, {"moveId": "GRASS_KNOT", "uses": 6649}, {"moveId": "ANCIENT_POWER", "uses": 12006}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "ANCIENT_POWER"], "score": 85.7, "stats": {"product": 2111, "atk": 100.8, "def": 138.6, "hp": 151}}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 641, "matchups": [{"opponent": "piloswine", "rating": 821, "opRating": 178}, {"opponent": "amaura", "rating": 571, "opRating": 428}, {"opponent": "magby", "rating": 559, "opRating": 440}, {"opponent": "steelix", "rating": 519, "opRating": 480}, {"opponent": "arcanine", "rating": 515, "opRating": 484}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "litleo", "rating": 199}, {"opponent": "talonflame", "rating": 217}, {"opponent": "magmar", "rating": 223}, {"opponent": "stunfisk_galarian", "rating": 357}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 18785}, {"moveId": "BITE", "uses": 6215}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 11449}, {"moveId": "FIRE_BLAST", "uses": 3091}, {"moveId": "CRUNCH", "uses": 10476}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 85.7, "stats": {"product": 1628, "atk": 136.6, "def": 94.5, "hp": 126}}, {"speciesId": "qwilfish_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 730, "matchups": [{"opponent": "nidoqueen", "rating": 654, "opRating": 345}, {"opponent": "magmortar", "rating": 558, "opRating": 441}, {"opponent": "toxapex", "rating": 558, "opRating": 441}, {"opponent": "steelix", "rating": 520, "opRating": 479}, {"opponent": "ninetales", "rating": 508, "opRating": 491}], "counters": [{"opponent": "electrode_hisuian", "rating": 203}, {"opponent": "registeel", "rating": 242}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 252}, {"opponent": "stunfisk_galarian", "rating": 381}, {"opponent": "nidoking", "rating": 381}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 13454}, {"moveId": "POISON_JAB", "uses": 11546}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 3973}, {"moveId": "SHADOW_BALL", "uses": 3623}, {"moveId": "ICE_BEAM", "uses": 3279}, {"moveId": "DARK_PULSE", "uses": 4949}, {"moveId": "AQUA_TAIL", "uses": 9202}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 85.6, "stats": {"product": 1730, "atk": 129.5, "def": 111.3, "hp": 120}}, {"speciesId": "growlithe", "speciesName": "Grow<PERSON>he", "rating": 617, "matchups": [{"opponent": "piloswine", "rating": 818, "opRating": 181}, {"opponent": "cradily", "rating": 533, "opRating": 466}, {"opponent": "steelix", "rating": 518, "opRating": 481}, {"opponent": "amaura", "rating": 514, "opRating": 485}, {"opponent": "weezing_galarian", "rating": 503, "opRating": 496}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "toxapex", "rating": 203}, {"opponent": "magmar", "rating": 227}, {"opponent": "nidoking", "rating": 307}, {"opponent": "stunfisk_galarian", "rating": 322}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 17443}, {"moveId": "BITE", "uses": 7557}], "chargedMoves": [{"moveId": "RETURN", "uses": 2580}, {"moveId": "FLAME_WHEEL", "uses": 2229}, {"moveId": "FLAMETHROWER", "uses": 10023}, {"moveId": "BODY_SLAM", "uses": 10188}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 85.5, "stats": {"product": 1555, "atk": 126.8, "def": 90.7, "hp": 135}}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 669, "matchups": [{"opponent": "tyrunt", "rating": 873, "opRating": 126}, {"opponent": "arctibax", "rating": 795, "opRating": 204}, {"opponent": "umbreon", "rating": 721, "opRating": 278}, {"opponent": "magmar", "rating": 686, "opRating": 313}, {"opponent": "magmortar", "rating": 682, "opRating": 317}], "counters": [{"opponent": "sandslash_alolan", "rating": 213}, {"opponent": "tentacruel", "rating": 219}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 272}, {"opponent": "stunfisk_galarian", "rating": 275}, {"opponent": "nidoking", "rating": 330}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 8654}, {"moveId": "FAIRY_WIND", "uses": 16346}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 4637}, {"moveId": "RETURN", "uses": 2693}, {"moveId": "PLAY_ROUGH", "uses": 3130}, {"moveId": "OVERHEAT", "uses": 4980}, {"moveId": "HYPER_BEAM", "uses": 1017}, {"moveId": "BRUTAL_SWING", "uses": 8462}]}, "moveset": ["FAIRY_WIND", "PLAY_ROUGH", "BRUTAL_SWING"], "score": 85.4, "stats": {"product": 1890, "atk": 118.3, "def": 138.8, "hp": 115}}, {"speciesId": "varoom", "speciesName": "Varoom", "rating": 426, "matchups": [{"opponent": "lileep", "rating": 662, "opRating": 337}, {"opponent": "cradily", "rating": 629, "opRating": 370}, {"opponent": "a<PERSON><PERSON>", "rating": 562, "opRating": 437}, {"opponent": "venusaur", "rating": 545, "opRating": 454}, {"opponent": "araquanid", "rating": 529, "opRating": 470}], "counters": [{"opponent": "steelix", "rating": 68}, {"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "nidoking", "rating": 133}, {"opponent": "magmar", "rating": 245}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 14521}, {"moveId": "LICK", "uses": 10479}], "chargedMoves": [{"moveId": "GYRO_BALL", "uses": 9985}, {"moveId": "GUNK_SHOT", "uses": 10439}, {"moveId": "ACID_SPRAY", "uses": 4559}]}, "moveset": ["POISON_JAB", "GYRO_BALL", "GUNK_SHOT"], "score": 85.4, "stats": {"product": 1427, "atk": 115.9, "def": 102.5, "hp": 120}}, {"speciesId": "shinx", "speciesName": "Shinx", "rating": 347, "matchups": [{"opponent": "slugma", "rating": 570, "opRating": 429}, {"opponent": "cloyster", "rating": 562, "opRating": 437}, {"opponent": "spinarak", "rating": 558, "opRating": 441}, {"opponent": "meowth_alolan", "rating": 537, "opRating": 462}, {"opponent": "chim<PERSON>r", "rating": 508, "opRating": 491}], "counters": [{"opponent": "steelix", "rating": 104}, {"opponent": "stunfisk_galarian", "rating": 127}, {"opponent": "magmar", "rating": 205}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 210}, {"opponent": "nidoking", "rating": 236}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 11655}, {"moveId": "SPARK", "uses": 13345}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3393}, {"moveId": "SWIFT", "uses": 11695}, {"moveId": "RETURN", "uses": 2038}, {"moveId": "DISCHARGE", "uses": 7914}]}, "moveset": ["SPARK", "SWIFT", "DISCHARGE"], "score": 85.4, "stats": {"product": 884, "atk": 110.9, "def": 66.3, "hp": 120}}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 736, "matchups": [{"opponent": "steelix", "rating": 671, "opRating": 328}, {"opponent": "stunfisk_galarian", "rating": 568, "opRating": 431}, {"opponent": "ninetales", "rating": 565, "opRating": 434}, {"opponent": "nidoqueen", "rating": 534, "opRating": 465}, {"opponent": "magmortar", "rating": 523, "opRating": 476}], "counters": [{"opponent": "tyrunt", "rating": 112}, {"opponent": "magmar", "rating": 200}, {"opponent": "armarouge", "rating": 245}, {"opponent": "toxapex", "rating": 262}, {"opponent": "nidoking", "rating": 287}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 20114}, {"moveId": "BITE", "uses": 4886}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 9970}, {"moveId": "DISARMING_VOICE", "uses": 6110}, {"moveId": "CRUNCH", "uses": 8914}]}, "moveset": ["INCINERATE", "CRUNCH", "FLAMETHROWER"], "score": 85.3, "stats": {"product": 1859, "atk": 119.8, "def": 106.2, "hp": 146}}, {"speciesId": "arctibax", "speciesName": "Arctibax", "rating": 709, "matchups": [{"opponent": "nidoqueen", "rating": 678, "opRating": 321}, {"opponent": "umbreon", "rating": 668, "opRating": 331}, {"opponent": "r<PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "swalot", "rating": 645, "opRating": 354}, {"opponent": "munchlax", "rating": 589, "opRating": 410}], "counters": [{"opponent": "ma<PERSON>le", "rating": 111}, {"opponent": "klefki", "rating": 165}, {"opponent": "dedenne", "rating": 206}, {"opponent": "stunfisk_galarian", "rating": 245}, {"opponent": "magmar", "rating": 379}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 11921}, {"moveId": "DRAGON_BREATH", "uses": 13079}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 2532}, {"moveId": "ICY_WIND", "uses": 3486}, {"moveId": "DRAGON_CLAW", "uses": 9012}, {"moveId": "AVALANCHE", "uses": 9989}]}, "moveset": ["DRAGON_BREATH", "AVALANCHE", "DRAGON_CLAW"], "score": 85.3, "stats": {"product": 1815, "atk": 123, "def": 97.6, "hp": 151}}, {"speciesId": "fuecoco", "speciesName": "Fuecoco", "rating": 662, "matchups": [{"opponent": "steelix", "rating": 651, "opRating": 348}, {"opponent": "araquanid", "rating": 625, "opRating": 375}, {"opponent": "nidoqueen", "rating": 523, "opRating": 476}, {"opponent": "ninetales", "rating": 519, "opRating": 480}, {"opponent": "stunfisk_galarian", "rating": 516, "opRating": 483}], "counters": [{"opponent": "tyrunt", "rating": 112}, {"opponent": "magmar", "rating": 183}, {"opponent": "armarouge", "rating": 223}, {"opponent": "toxapex", "rating": 224}, {"opponent": "nidoking", "rating": 255}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 20027}, {"moveId": "BITE", "uses": 4973}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 9964}, {"moveId": "DISARMING_VOICE", "uses": 6113}, {"moveId": "CRUNCH", "uses": 8913}]}, "moveset": ["INCINERATE", "CRUNCH", "FLAMETHROWER"], "score": 85.3, "stats": {"product": 1513, "atk": 106.7, "def": 93.2, "hp": 152}}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 682, "matchups": [{"opponent": "avalugg_his<PERSON>an", "rating": 790, "opRating": 209}, {"opponent": "arctibax", "rating": 719, "opRating": 280}, {"opponent": "tyrunt", "rating": 666, "opRating": 333}, {"opponent": "munchlax", "rating": 634, "opRating": 365}, {"opponent": "toxapex", "rating": 549, "opRating": 450}], "counters": [{"opponent": "stunfisk_galarian", "rating": 44}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 144}, {"opponent": "electrode_hisuian", "rating": 159}, {"opponent": "magmar", "rating": 236}, {"opponent": "nidoking", "rating": 279}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 25000}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 2789}, {"moveId": "SUPER_POWER", "uses": 7784}, {"moveId": "ROCK_SLIDE", "uses": 5802}, {"moveId": "HYPER_BEAM", "uses": 1849}, {"moveId": "FLASH_CANNON", "uses": 879}, {"moveId": "DOUBLE_IRON_BASH", "uses": 5904}]}, "moveset": ["THUNDER_SHOCK", "DOUBLE_IRON_BASH", "SUPER_POWER"], "score": 85.1, "stats": {"product": 1847, "atk": 121.4, "def": 107.8, "hp": 141}}, {"speciesId": "a<PERSON><PERSON>", "speciesName": "Ariados", "rating": 609, "matchups": [{"opponent": "umbreon", "rating": 734, "opRating": 265}, {"opponent": "araquanid", "rating": 659, "opRating": 340}, {"opponent": "nidoking", "rating": 602, "opRating": 397}, {"opponent": "toxapex", "rating": 560, "opRating": 439}, {"opponent": "dedenne", "rating": 560, "opRating": 439}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 206}, {"opponent": "forretress", "rating": 230}, {"opponent": "magnezone", "rating": 235}, {"opponent": "stunfisk_galarian", "rating": 310}, {"opponent": "steelix", "rating": 310}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14244}, {"moveId": "INFESTATION", "uses": 10756}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 3442}, {"moveId": "SHADOW_SNEAK", "uses": 2776}, {"moveId": "MEGAHORN", "uses": 5440}, {"moveId": "LUNGE", "uses": 5662}, {"moveId": "CROSS_POISON", "uses": 7654}]}, "moveset": ["POISON_STING", "LUNGE", "TRAILBLAZE"], "score": 85, "stats": {"product": 1794, "atk": 124.6, "def": 102, "hp": 141}}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609, "matchups": [{"opponent": "r<PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "toxapex", "rating": 669, "opRating": 330}, {"opponent": "araquanid", "rating": 625, "opRating": 375}, {"opponent": "dedenne", "rating": 552, "opRating": 447}, {"opponent": "magmar", "rating": 508, "opRating": 491}], "counters": [{"opponent": "litleo", "rating": 185}, {"opponent": "tyrunt", "rating": 189}, {"opponent": "ninetales", "rating": 202}, {"opponent": "nidoking", "rating": 275}, {"opponent": "stunfisk_galarian", "rating": 284}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 13755}, {"moveId": "FURY_CUTTER", "uses": 11245}], "chargedMoves": [{"moveId": "RETURN", "uses": 2450}, {"moveId": "LUNGE", "uses": 5826}, {"moveId": "ENERGY_BALL", "uses": 2019}, {"moveId": "DISCHARGE", "uses": 5710}, {"moveId": "CROSS_POISON", "uses": 5233}, {"moveId": "BUG_BUZZ", "uses": 3757}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LUNGE"], "score": 85, "stats": {"product": 1616, "atk": 138.9, "def": 93.7, "hp": 124}}, {"speciesId": "mareep", "speciesName": "<PERSON><PERSON>", "rating": 478, "matchups": [{"opponent": "charizard", "rating": 662, "opRating": 337}, {"opponent": "talonflame", "rating": 625, "opRating": 374}, {"opponent": "araquanid", "rating": 537, "opRating": 462}, {"opponent": "zapdos", "rating": 503, "opRating": 496}, {"opponent": "tadbulb", "rating": 503, "opRating": 496}], "counters": [{"opponent": "stunfisk_galarian", "rating": 121}, {"opponent": "steelix", "rating": 165}, {"opponent": "nidoking", "rating": 232}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 247}, {"opponent": "magmar", "rating": 294}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15530}, {"moveId": "TACKLE", "uses": 9470}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 4967}, {"moveId": "THUNDERBOLT", "uses": 2930}, {"moveId": "RETURN", "uses": 2027}, {"moveId": "DISCHARGE", "uses": 6896}, {"moveId": "BODY_SLAM", "uses": 8152}]}, "moveset": ["THUNDER_SHOCK", "BODY_SLAM", "DISCHARGE"], "score": 85, "stats": {"product": 1156, "atk": 108.3, "def": 78.9, "hp": 135}}, {"speciesId": "toxel", "speciesName": "Toxel", "rating": 274, "matchups": [{"opponent": "volcarona", "rating": 991, "opRating": 8}, {"opponent": "pawmi", "rating": 765, "opRating": 234}, {"opponent": "pichu", "rating": 725, "opRating": 274}, {"opponent": "tynamo", "rating": 601, "opRating": 398}, {"opponent": "kakuna", "rating": 522, "opRating": 477}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "steelix", "rating": 92}, {"opponent": "nidoqueen", "rating": 111}, {"opponent": "nidoking", "rating": 133}, {"opponent": "magmar", "rating": 263}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 25000}], "chargedMoves": [{"moveId": "POWER_UP_PUNCH", "uses": 25000}]}, "moveset": ["ACID", "POWER_UP_PUNCH"], "score": 85, "stats": {"product": 715, "atk": 94.1, "def": 67.2, "hp": 113}}, {"speciesId": "dedenne", "speciesName": "Dedenne", "rating": 667, "matchups": [{"opponent": "tyrunt", "rating": 842, "opRating": 157}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 751, "opRating": 248}, {"opponent": "r<PERSON><PERSON>", "rating": 729, "opRating": 270}, {"opponent": "magmar", "rating": 691, "opRating": 308}, {"opponent": "magmortar", "rating": 687, "opRating": 312}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 98}, {"opponent": "u<PERSON><PERSON><PERSON>", "rating": 100}, {"opponent": "stunfisk_galarian", "rating": 213}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15341}, {"moveId": "TACKLE", "uses": 9659}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 6130}, {"moveId": "PARABOLIC_CHARGE", "uses": 9456}, {"moveId": "DISCHARGE", "uses": 9381}]}, "moveset": ["THUNDER_SHOCK", "PARABOLIC_CHARGE", "PLAY_ROUGH"], "score": 84.9, "stats": {"product": 1796, "atk": 124.1, "def": 108.7, "hp": 133}}, {"speciesId": "golbat", "speciesName": "Golbat", "rating": 578, "matchups": [{"opponent": "nidoking", "rating": 850, "opRating": 150}, {"opponent": "araquanid", "rating": 746, "opRating": 253}, {"opponent": "magmar", "rating": 714, "opRating": 285}, {"opponent": "magby", "rating": 710, "opRating": 289}, {"opponent": "nidoqueen", "rating": 592, "opRating": 407}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "steelix", "rating": 100}, {"opponent": "amaura", "rating": 103}, {"opponent": "avalugg_his<PERSON>an", "rating": 128}, {"opponent": "tyrunt", "rating": 143}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 15933}, {"moveId": "BITE", "uses": 9067}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 6481}, {"moveId": "RETURN", "uses": 3801}, {"moveId": "POISON_FANG", "uses": 7984}, {"moveId": "OMINOUS_WIND", "uses": 3346}, {"moveId": "AIR_CUTTER", "uses": 3384}]}, "moveset": ["WING_ATTACK", "POISON_FANG", "SHADOW_BALL"], "score": 84.9, "stats": {"product": 1889, "atk": 118.7, "def": 113.6, "hp": 140}}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 744, "matchups": [{"opponent": "dedenne", "rating": 910, "opRating": 89}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 895, "opRating": 104}, {"opponent": "r<PERSON><PERSON>", "rating": 758, "opRating": 241}, {"opponent": "magmortar", "rating": 672, "opRating": 327}, {"opponent": "magmar", "rating": 525, "opRating": 474}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "sandslash_alolan", "rating": 92}, {"opponent": "avalugg_his<PERSON>an", "rating": 152}, {"opponent": "nidoking", "rating": 157}, {"opponent": "tyrunt", "rating": 201}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 20120}, {"moveId": "BITE", "uses": 4880}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 5870}, {"moveId": "SLUDGE_WAVE", "uses": 2502}, {"moveId": "POISON_FANG", "uses": 4615}, {"moveId": "EARTH_POWER", "uses": 8796}, {"moveId": "EARTHQUAKE", "uses": 3229}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "POISON_FANG"], "score": 84.7, "stats": {"product": 1908, "atk": 117.7, "def": 116.5, "hp": 139}}, {"speciesId": "em<PERSON>ga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 682, "matchups": [{"opponent": "nidoking", "rating": 804, "opRating": 195}, {"opponent": "araquanid", "rating": 703, "opRating": 296}, {"opponent": "magmar", "rating": 626, "opRating": 373}, {"opponent": "magmortar", "rating": 617, "opRating": 382}, {"opponent": "toxapex", "rating": 532, "opRating": 467}], "counters": [{"opponent": "piloswine", "rating": 80}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "electrode_hisuian", "rating": 168}, {"opponent": "stunfisk_galarian", "rating": 263}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 13775}, {"moveId": "QUICK_ATTACK", "uses": 11225}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 3028}, {"moveId": "DISCHARGE", "uses": 7211}, {"moveId": "AERIAL_ACE", "uses": 8275}, {"moveId": "ACROBATICS", "uses": 6497}]}, "moveset": ["THUNDER_SHOCK", "ACROBATICS", "DISCHARGE"], "score": 84.7, "stats": {"product": 1753, "atk": 127.8, "def": 111.4, "hp": 123}}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 572, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 800, "opRating": 199}, {"opponent": "avalugg_his<PERSON>an", "rating": 626, "opRating": 373}, {"opponent": "toxapex", "rating": 611, "opRating": 388}, {"opponent": "umbreon", "rating": 597, "opRating": 402}, {"opponent": "tyrunt", "rating": 548, "opRating": 451}], "counters": [{"opponent": "magby", "rating": 129}, {"opponent": "r<PERSON><PERSON>", "rating": 137}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}, {"opponent": "magmar", "rating": 165}, {"opponent": "nidoking", "rating": 169}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 14616}, {"moveId": "METAL_CLAW", "uses": 10384}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 7192}, {"moveId": "IRON_HEAD", "uses": 4455}, {"moveId": "FOCUS_BLAST", "uses": 4751}, {"moveId": "DARK_PULSE", "uses": 8618}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 84.4, "stats": {"product": 1599, "atk": 139.2, "def": 111.4, "hp": 103}}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 486, "matchups": [{"opponent": "nidoking", "rating": 668, "opRating": 331}, {"opponent": "araquanid", "rating": 624, "opRating": 375}, {"opponent": "talonflame", "rating": 610, "opRating": 389}, {"opponent": "swalot", "rating": 575, "opRating": 424}, {"opponent": "rapidash", "rating": 568, "opRating": 431}], "counters": [{"opponent": "umbreon", "rating": 80}, {"opponent": "weezing_galarian", "rating": 95}, {"opponent": "steelix", "rating": 116}, {"opponent": "ninetales", "rating": 142}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 18676}, {"moveId": "PECK", "uses": 6324}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 2543}, {"moveId": "PSYCHIC", "uses": 3222}, {"moveId": "DARK_PULSE", "uses": 7645}, {"moveId": "BRAVE_BIRD", "uses": 11664}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 84.1, "stats": {"product": 1492, "atk": 150.6, "def": 68.3, "hp": 145}}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 674, "matchups": [{"opponent": "steelix", "rating": 628, "opRating": 371}, {"opponent": "dedenne", "rating": 573, "opRating": 426}, {"opponent": "magmortar", "rating": 550, "opRating": 449}, {"opponent": "stunfisk_galarian", "rating": 518, "opRating": 481}, {"opponent": "nidoqueen", "rating": 504, "opRating": 495}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "crocalor", "rating": 195}, {"opponent": "umbreon", "rating": 206}, {"opponent": "ninetales", "rating": 218}, {"opponent": "magmar", "rating": 223}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 2473}, {"moveId": "SCRATCH", "uses": 4417}, {"moveId": "FIRE_SPIN", "uses": 18095}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 3413}, {"moveId": "MYSTICAL_FIRE", "uses": 6599}, {"moveId": "FLAME_CHARGE", "uses": 2535}, {"moveId": "FLAMETHROWER", "uses": 2144}, {"moveId": "FIRE_BLAST", "uses": 1170}, {"moveId": "BLAST_BURN", "uses": 9198}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 83.6, "stats": {"product": 1671, "atk": 132.6, "def": 115.6, "hp": 109}}, {"speciesId": "klink", "speciesName": "Klink", "rating": 368, "matchups": [{"opponent": "toxapex", "rating": 641, "opRating": 358}, {"opponent": "a<PERSON><PERSON>", "rating": 553, "opRating": 446}, {"opponent": "blitzle", "rating": 522, "opRating": 477}, {"opponent": "weezing", "rating": 513, "opRating": 486}, {"opponent": "charcadet", "rating": 508, "opRating": 491}], "counters": [{"opponent": "nidoking", "rating": 62}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 86}, {"opponent": "stunfisk_galarian", "rating": 94}, {"opponent": "magmar", "rating": 129}, {"opponent": "electrode_hisuian", "rating": 137}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 11646}, {"moveId": "METAL_SOUND", "uses": 7713}, {"moveId": "CHARGE_BEAM", "uses": 5667}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 4199}, {"moveId": "VICE_GRIP", "uses": 8804}, {"moveId": "DISCHARGE", "uses": 12003}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "VICE_GRIP"], "score": 83.4, "stats": {"product": 1226, "atk": 94.9, "def": 114.2, "hp": 113}}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 658, "matchups": [{"opponent": "toxapex", "rating": 712, "opRating": 287}, {"opponent": "araquanid", "rating": 700, "opRating": 299}, {"opponent": "r<PERSON><PERSON>", "rating": 653, "opRating": 346}, {"opponent": "magmortar", "rating": 551, "opRating": 448}, {"opponent": "magby", "rating": 515, "opRating": 484}], "counters": [{"opponent": "stunfisk_galarian", "rating": 139}, {"opponent": "dedenne", "rating": 210}, {"opponent": "steelix", "rating": 221}, {"opponent": "nidoking", "rating": 267}, {"opponent": "magmar", "rating": 272}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 13074}, {"moveId": "SPARK", "uses": 8385}, {"moveId": "ACID", "uses": 3543}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 4713}, {"moveId": "LIQUIDATION", "uses": 7406}, {"moveId": "DRAGON_CLAW", "uses": 6453}, {"moveId": "CRUNCH", "uses": 5673}, {"moveId": "ACID_SPRAY", "uses": 794}]}, "moveset": ["VOLT_SWITCH", "DRAGON_CLAW", "THUNDERBOLT"], "score": 83, "stats": {"product": 1672, "atk": 134.4, "def": 97.9, "hp": 127}}, {"speciesId": "ekans", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 429, "matchups": [{"opponent": "dedenne", "rating": 757, "opRating": 242}, {"opponent": "flareon", "rating": 561, "opRating": 438}, {"opponent": "pyroar", "rating": 557, "opRating": 442}, {"opponent": "koffing", "rating": 523, "opRating": 476}, {"opponent": "weepinbell", "rating": 514, "opRating": 485}], "counters": [{"opponent": "stunfisk_galarian", "rating": 82}, {"opponent": "steelix", "rating": 92}, {"opponent": "<PERSON>on", "rating": 108}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 132}, {"opponent": "nidoking", "rating": 220}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 17221}, {"moveId": "ACID", "uses": 7779}], "chargedMoves": [{"moveId": "WRAP", "uses": 5249}, {"moveId": "SLUDGE_BOMB", "uses": 6262}, {"moveId": "RETURN", "uses": 4096}, {"moveId": "POISON_FANG", "uses": 7681}, {"moveId": "GUNK_SHOT", "uses": 1590}]}, "moveset": ["POISON_STING", "POISON_FANG", "SLUDGE_BOMB"], "score": 82.9, "stats": {"product": 1038, "atk": 105, "def": 94.1, "hp": 105}}, {"speciesId": "raticate_alolan", "speciesName": "Raticate (Alolan)", "rating": 671, "matchups": [{"opponent": "swalot", "rating": 635, "opRating": 364}, {"opponent": "umbreon", "rating": 611, "opRating": 388}, {"opponent": "nidoqueen", "rating": 591, "opRating": 408}, {"opponent": "toxapex", "rating": 520, "opRating": 479}, {"opponent": "steelix", "rating": 516, "opRating": 483}], "counters": [{"opponent": "melmetal", "rating": 99}, {"opponent": "obstagoon", "rating": 177}, {"opponent": "magby", "rating": 191}, {"opponent": "magmar", "rating": 218}, {"opponent": "nidoking", "rating": 240}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 16026}, {"moveId": "BITE", "uses": 8974}], "chargedMoves": [{"moveId": "RETURN", "uses": 2824}, {"moveId": "HYPER_FANG", "uses": 8190}, {"moveId": "HYPER_BEAM", "uses": 2167}, {"moveId": "CRUNCH", "uses": 11868}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "RETURN"], "score": 82.8, "stats": {"product": 2078, "atk": 107.4, "def": 130.6, "hp": 148}}, {"speciesId": "<PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "toxapex", "rating": 716, "opRating": 283}, {"opponent": "charizard", "rating": 708, "opRating": 291}, {"opponent": "talonflame", "rating": 641, "opRating": 358}, {"opponent": "moltres_galarian", "rating": 589, "opRating": 410}, {"opponent": "swalot", "rating": 548, "opRating": 451}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "piloswine", "rating": 119}, {"opponent": "nidoqueen", "rating": 133}, {"opponent": "nidoking", "rating": 145}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 152}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 6248}, {"moveId": "ICE_FANG", "uses": 6453}, {"moveId": "FIRE_FANG", "uses": 7843}, {"moveId": "BITE", "uses": 4437}], "chargedMoves": [{"moveId": "RETURN", "uses": 3866}, {"moveId": "POISON_FANG", "uses": 5726}, {"moveId": "PLAY_ROUGH", "uses": 3225}, {"moveId": "CRUNCH", "uses": 12189}]}, "moveset": ["THUNDER_FANG", "CRUNCH", "POISON_FANG"], "score": 82.7, "stats": {"product": 1760, "atk": 125.8, "def": 104.3, "hp": 134}}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 683, "matchups": [{"opponent": "talonflame", "rating": 757, "opRating": 242}, {"opponent": "moltres_galarian", "rating": 643, "opRating": 356}, {"opponent": "araquanid", "rating": 599, "opRating": 400}, {"opponent": "magby", "rating": 591, "opRating": 408}, {"opponent": "steelix", "rating": 518, "opRating": 481}], "counters": [{"opponent": "magmar", "rating": 223}, {"opponent": "magmortar", "rating": 231}, {"opponent": "armarouge", "rating": 250}, {"opponent": "tyrunt", "rating": 279}, {"opponent": "stunfisk_galarian", "rating": 357}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 8385}, {"moveId": "FIRE_SPIN", "uses": 16615}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 9231}, {"moveId": "POWER_UP_PUNCH", "uses": 4061}, {"moveId": "FLAMETHROWER", "uses": 11723}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 82.6, "stats": {"product": 1660, "atk": 133.2, "def": 91.6, "hp": 136}}, {"speciesId": "foongus", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 484, "matchups": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 806, "opRating": 193}, {"opponent": "r<PERSON><PERSON>", "rating": 796, "opRating": 203}, {"opponent": "arm<PERSON>", "rating": 545, "opRating": 454}, {"opponent": "cradily", "rating": 519, "opRating": 480}, {"opponent": "electivire", "rating": 503, "opRating": 496}], "counters": [{"opponent": "u<PERSON><PERSON><PERSON>", "rating": 86}, {"opponent": "amaura", "rating": 149}, {"opponent": "ninetales", "rating": 170}, {"opponent": "stunfisk_galarian", "rating": 207}, {"opponent": "magmar", "rating": 223}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 9690}, {"moveId": "ASTONISH", "uses": 15310}], "chargedMoves": [{"moveId": "RETURN", "uses": 2967}, {"moveId": "GRASS_KNOT", "uses": 7465}, {"moveId": "ENERGY_BALL", "uses": 2716}, {"moveId": "BODY_SLAM", "uses": 11897}]}, "moveset": ["ASTONISH", "BODY_SLAM", "GRASS_KNOT"], "score": 81.6, "stats": {"product": 1299, "atk": 94.1, "def": 89, "hp": 155}}, {"speciesId": "vullaby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 524, "matchups": [{"opponent": "araquanid", "rating": 726, "opRating": 273}, {"opponent": "nidoking", "rating": 697, "opRating": 302}, {"opponent": "obstagoon", "rating": 608, "opRating": 391}, {"opponent": "nidoqueen", "rating": 522, "opRating": 477}, {"opponent": "munchlax", "rating": 512, "opRating": 487}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "avalugg_his<PERSON>an", "rating": 136}, {"opponent": "dedenne", "rating": 157}, {"opponent": "tyrunt", "rating": 178}, {"opponent": "magmar", "rating": 316}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 13035}, {"moveId": "AIR_SLASH", "uses": 11965}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 8832}, {"moveId": "DARK_PULSE", "uses": 3760}, {"moveId": "BRAVE_BIRD", "uses": 12406}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "FOUL_PLAY"], "score": 81.1, "stats": {"product": 2049, "atk": 100.8, "def": 129.4, "hp": 157}}, {"speciesId": "nidoran_male", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 385, "matchups": [{"opponent": "chim<PERSON>r", "rating": 574, "opRating": 425}, {"opponent": "shiftry", "rating": 574, "opRating": 425}, {"opponent": "pikachu_libre", "rating": 537, "opRating": 462}, {"opponent": "pikachu_pop_star", "rating": 537, "opRating": 462}, {"opponent": "fennekin", "rating": 533, "opRating": 466}], "counters": [{"opponent": "bronzong", "rating": 33}, {"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "steelix", "rating": 96}, {"opponent": "nidoking", "rating": 224}, {"opponent": "magmar", "rating": 250}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 17400}, {"moveId": "PECK", "uses": 7600}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7568}, {"moveId": "RETURN", "uses": 2855}, {"moveId": "HORN_ATTACK", "uses": 3564}, {"moveId": "BODY_SLAM", "uses": 11054}]}, "moveset": ["POISON_STING", "BODY_SLAM", "SLUDGE_BOMB"], "score": 80.9, "stats": {"product": 933, "atk": 100.8, "def": 76.4, "hp": 121}}, {"speciesId": "meowscarada", "speciesName": "Meowscarada", "rating": 507, "matchups": [{"opponent": "dedenne", "rating": 652, "opRating": 347}, {"opponent": "stunfisk_galarian", "rating": 610, "opRating": 389}, {"opponent": "tyrunt", "rating": 580, "opRating": 419}, {"opponent": "steelix", "rating": 546, "opRating": 453}, {"opponent": "nidoking", "rating": 525, "opRating": 474}], "counters": [{"opponent": "venomoth", "rating": 118}, {"opponent": "araquanid", "rating": 120}, {"opponent": "revavroom", "rating": 122}, {"opponent": "weezing_galarian", "rating": 147}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 16250}, {"moveId": "CHARM", "uses": 8750}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 2887}, {"moveId": "NIGHT_SLASH", "uses": 14749}, {"moveId": "GRASS_KNOT", "uses": 5429}, {"moveId": "ENERGY_BALL", "uses": 1941}]}, "moveset": ["LEAFAGE", "NIGHT_SLASH", "GRASS_KNOT"], "score": 80.8, "stats": {"product": 1587, "atk": 141.5, "def": 94.9, "hp": 118}}, {"speciesId": "saland<PERSON>", "speciesName": "Salandit", "rating": 569, "matchups": [{"opponent": "sandslash_alolan", "rating": 868, "opRating": 132}, {"opponent": "beedrill", "rating": 708, "opRating": 292}, {"opponent": "magby", "rating": 580, "opRating": 420}, {"opponent": "piloswine", "rating": 536, "opRating": 464}, {"opponent": "araquanid", "rating": 520, "opRating": 480}], "counters": [{"opponent": "typhlosion", "rating": 188}, {"opponent": "magmar", "rating": 241}, {"opponent": "tyrunt", "rating": 248}, {"opponent": "toxapex", "rating": 271}, {"opponent": "nidoking", "rating": 279}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 12500}, {"moveId": "EMBER", "uses": 12500}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 9538}, {"moveId": "FLAMETHROWER", "uses": 11289}, {"moveId": "DRAGON_PULSE", "uses": 4155}]}, "moveset": ["EMBER", "FLAMETHROWER", "POISON_FANG"], "score": 80.7, "stats": {"product": 1266, "atk": 126.8, "def": 79.8, "hp": 125}}, {"speciesId": "voltorb_his<PERSON>an", "speciesName": "Voltorb (<PERSON><PERSON>an)", "rating": 391, "matchups": [{"opponent": "luxray", "rating": 694, "opRating": 305}, {"opponent": "eelektross", "rating": 654, "opRating": 345}, {"opponent": "manectric", "rating": 579, "opRating": 420}, {"opponent": "electivire", "rating": 513, "opRating": 486}, {"opponent": "magnezone", "rating": 504, "opRating": 495}], "counters": [{"opponent": "avalugg_his<PERSON>an", "rating": 107}, {"opponent": "toxapex", "rating": 122}, {"opponent": "stunfisk_galarian", "rating": 142}, {"opponent": "magmar", "rating": 174}, {"opponent": "nidoking", "rating": 192}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 13511}, {"moveId": "CHARGE_BEAM", "uses": 11489}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 7649}, {"moveId": "SWIFT", "uses": 12256}, {"moveId": "ENERGY_BALL", "uses": 5115}]}, "moveset": ["TACKLE", "SWIFT", "THUNDERBOLT"], "score": 80.4, "stats": {"product": 1247, "atk": 104.1, "def": 105.8, "hp": 113}}, {"speciesId": "trubbish", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 182, "matchups": [{"opponent": "oddish", "rating": 543, "opRating": 456}, {"opponent": "pichu", "rating": 543, "opRating": 456}, {"opponent": "vileplume", "rating": 543, "opRating": 456}, {"opponent": "gloom", "rating": 523, "opRating": 476}, {"opponent": "pikachu_horizons", "rating": 507, "opRating": 492}], "counters": [{"opponent": "stunfisk_galarian", "rating": 56}, {"opponent": "tyrunt", "rating": 65}, {"opponent": "magmar", "rating": 98}, {"opponent": "nidoking", "rating": 98}, {"opponent": "rapidash", "rating": 121}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 14346}, {"moveId": "POUND", "uses": 10654}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 12434}, {"moveId": "GUNK_SHOT", "uses": 12566}]}, "moveset": ["TAKE_DOWN", "GUNK_SHOT", "SEED_BOMB"], "score": 80.4, "stats": {"product": 1364, "atk": 93.2, "def": 115.1, "hp": 127}}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 777, "matchups": [{"opponent": "steelix", "rating": 706, "opRating": 293}, {"opponent": "stunfisk_galarian", "rating": 583, "opRating": 416}, {"opponent": "umbreon", "rating": 559, "opRating": 440}, {"opponent": "nidoqueen", "rating": 559, "opRating": 440}, {"opponent": "magmortar", "rating": 543, "opRating": 456}], "counters": [{"opponent": "magmar", "rating": 183}, {"opponent": "tyrunt", "rating": 201}, {"opponent": "tentacruel", "rating": 261}, {"opponent": "toxapex", "rating": 262}, {"opponent": "r<PERSON><PERSON>", "rating": 269}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 11322}, {"moveId": "FEINT_ATTACK", "uses": 5164}, {"moveId": "EMBER", "uses": 8517}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 7766}, {"moveId": "SOLAR_BEAM", "uses": 774}, {"moveId": "SCORCHING_SANDS", "uses": 5636}, {"moveId": "RETURN", "uses": 1604}, {"moveId": "PSYSHOCK", "uses": 2928}, {"moveId": "OVERHEAT", "uses": 3111}, {"moveId": "HEAT_WAVE", "uses": 556}, {"moveId": "FLAMETHROWER", "uses": 1694}, {"moveId": "FIRE_BLAST", "uses": 911}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 80, "stats": {"product": 1936, "atk": 115, "def": 133.5, "hp": 126}}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 808, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 926, "opRating": 73}, {"opponent": "nidoqueen", "rating": 914, "opRating": 85}, {"opponent": "steelix", "rating": 807, "opRating": 192}, {"opponent": "tyrunt", "rating": 736, "opRating": 263}, {"opponent": "magmar", "rating": 538, "opRating": 461}], "counters": [{"opponent": "incineroar", "rating": 231}, {"opponent": "nidoking", "rating": 240}, {"opponent": "<PERSON>rserker", "rating": 260}, {"opponent": "flareon", "rating": 305}, {"opponent": "piloswine", "rating": 312}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 15913}, {"moveId": "METAL_CLAW", "uses": 9087}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 7425}, {"moveId": "MUDDY_WATER", "uses": 5743}, {"moveId": "FLASH_CANNON", "uses": 2761}, {"moveId": "EARTHQUAKE", "uses": 9099}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 79.2, "stats": {"product": 2200, "atk": 101.7, "def": 127.9, "hp": 169}}, {"speciesId": "camerupt", "speciesName": "Camerupt", "rating": 663, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 922, "opRating": 77}, {"opponent": "piloswine", "rating": 790, "opRating": 209}, {"opponent": "r<PERSON><PERSON>", "rating": 709, "opRating": 290}, {"opponent": "dedenne", "rating": 659, "opRating": 340}, {"opponent": "steelix", "rating": 606, "opRating": 393}], "counters": [{"opponent": "tyrunt", "rating": 158}, {"opponent": "tentacruel", "rating": 193}, {"opponent": "ninetales", "rating": 202}, {"opponent": "magmar", "rating": 236}, {"opponent": "stunfisk_galarian", "rating": 402}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 1833}, {"moveId": "INCINERATE", "uses": 16833}, {"moveId": "EMBER", "uses": 6323}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 1441}, {"moveId": "RETURN", "uses": 2884}, {"moveId": "OVERHEAT", "uses": 7124}, {"moveId": "EARTH_POWER", "uses": 10002}, {"moveId": "EARTHQUAKE", "uses": 3656}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 78.5, "stats": {"product": 1666, "atk": 134.9, "def": 101.1, "hp": 122}}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 748, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 799, "opRating": 200}, {"opponent": "nidoqueen", "rating": 635, "opRating": 364}, {"opponent": "arctibax", "rating": 602, "opRating": 397}, {"opponent": "steelix", "rating": 569, "opRating": 430}, {"opponent": "nidoking", "rating": 545, "opRating": 454}], "counters": [{"opponent": "magmar", "rating": 245}, {"opponent": "umbreon", "rating": 287}, {"opponent": "toxapex", "rating": 288}, {"opponent": "rapidash", "rating": 297}, {"opponent": "tyrunt", "rating": 317}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 5643}, {"moveId": "SNARL", "uses": 8069}, {"moveId": "FIRE_FANG", "uses": 9455}, {"moveId": "BITE", "uses": 1831}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 5081}, {"moveId": "SCORCHING_SANDS", "uses": 5175}, {"moveId": "PSYCHIC_FANGS", "uses": 3845}, {"moveId": "FLAMETHROWER", "uses": 4108}, {"moveId": "FIRE_BLAST", "uses": 1079}, {"moveId": "CRUNCH", "uses": 2943}, {"moveId": "BULLDOZE", "uses": 2767}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 78.3, "stats": {"product": 1684, "atk": 132.7, "def": 103.9, "hp": 122}}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 503, "matchups": [{"opponent": "tyrunt", "rating": 729, "opRating": 270}, {"opponent": "avalugg_his<PERSON>an", "rating": 725, "opRating": 274}, {"opponent": "nidoqueen", "rating": 717, "opRating": 282}, {"opponent": "munchlax", "rating": 717, "opRating": 282}, {"opponent": "nidoking", "rating": 549, "opRating": 450}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 123}, {"opponent": "steelix", "rating": 125}, {"opponent": "electrode_hisuian", "rating": 132}, {"opponent": "ninetales", "rating": 146}, {"opponent": "magmar", "rating": 272}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 12659}, {"moveId": "AIR_SLASH", "uses": 12341}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 3072}, {"moveId": "RETURN", "uses": 4242}, {"moveId": "FLASH_CANNON", "uses": 3363}, {"moveId": "BRAVE_BIRD", "uses": 14326}]}, "moveset": ["STEEL_WING", "BRAVE_BIRD", "SKY_ATTACK"], "score": 77.5, "stats": {"product": 2114, "atk": 106.2, "def": 163.1, "hp": 122}}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 756, "matchups": [{"opponent": "swalot", "rating": 773, "opRating": 226}, {"opponent": "nidoking", "rating": 747, "opRating": 252}, {"opponent": "steelix", "rating": 615, "opRating": 384}, {"opponent": "araquanid", "rating": 615, "opRating": 384}, {"opponent": "dedenne", "rating": 521, "opRating": 478}], "counters": [{"opponent": "magmar", "rating": 218}, {"opponent": "magmortar", "rating": 245}, {"opponent": "toxapex", "rating": 317}, {"opponent": "tyrunt", "rating": 321}, {"opponent": "stunfisk_galarian", "rating": 357}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 7065}, {"moveId": "INCINERATE", "uses": 12795}, {"moveId": "EMBER", "uses": 5158}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 5943}, {"moveId": "SOLAR_BEAM", "uses": 1531}, {"moveId": "OVERHEAT", "uses": 2770}, {"moveId": "FIRE_BLAST", "uses": 1611}, {"moveId": "BLAST_BURN", "uses": 13143}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 76.3, "stats": {"product": 1675, "atk": 132.1, "def": 108.3, "hp": 117}}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 653, "matchups": [{"opponent": "nidoking", "rating": 713, "opRating": 286}, {"opponent": "ninetales", "rating": 713, "opRating": 286}, {"opponent": "magmar", "rating": 606, "opRating": 393}, {"opponent": "nidoqueen", "rating": 532, "opRating": 467}, {"opponent": "tyrunt", "rating": 516, "opRating": 483}], "counters": [{"opponent": "weezing_galarian", "rating": 195}, {"opponent": "dedenne", "rating": 210}, {"opponent": "ma<PERSON>le", "rating": 226}, {"opponent": "electrode_hisuian", "rating": 243}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 9418}, {"moveId": "SUCKER_PUNCH", "uses": 15582}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 6786}, {"moveId": "BRAVE_BIRD", "uses": 11298}, {"moveId": "ANCIENT_POWER", "uses": 6954}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 76.3, "stats": {"product": 1931, "atk": 115.1, "def": 137.4, "hp": 122}}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 702, "matchups": [{"opponent": "toxapex", "rating": 722, "opRating": 277}, {"opponent": "araquanid", "rating": 709, "opRating": 290}, {"opponent": "ninetales", "rating": 663, "opRating": 336}, {"opponent": "talonflame", "rating": 631, "opRating": 368}, {"opponent": "r<PERSON><PERSON>", "rating": 600, "opRating": 400}], "counters": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 143}, {"opponent": "avalugg_his<PERSON>an", "rating": 173}, {"opponent": "stunfisk_galarian", "rating": 198}, {"opponent": "magmar", "rating": 205}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 252}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 21711}, {"moveId": "LOW_KICK", "uses": 3289}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 9137}, {"moveId": "THUNDER_PUNCH", "uses": 5310}, {"moveId": "THUNDER", "uses": 1482}, {"moveId": "ICE_PUNCH", "uses": 4821}, {"moveId": "FLAMETHROWER", "uses": 4289}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 75.9, "stats": {"product": 1553, "atk": 143.9, "def": 98, "hp": 110}}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 615, "matchups": [{"opponent": "nidoking", "rating": 753, "opRating": 246}, {"opponent": "toxapex", "rating": 738, "opRating": 261}, {"opponent": "tyrunt", "rating": 703, "opRating": 296}, {"opponent": "avalugg_his<PERSON>an", "rating": 684, "opRating": 315}, {"opponent": "nidoqueen", "rating": 630, "opRating": 369}], "counters": [{"opponent": "r<PERSON><PERSON>_alolan", "rating": 120}, {"opponent": "litleo", "rating": 129}, {"opponent": "magmar", "rating": 147}, {"opponent": "magmortar", "rating": 171}, {"opponent": "darmanitan_standard", "rating": 189}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 11905}, {"moveId": "STRUGGLE_BUG", "uses": 5090}, {"moveId": "BUG_BITE", "uses": 8004}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 3570}, {"moveId": "ROCK_TOMB", "uses": 4333}, {"moveId": "RETURN", "uses": 2900}, {"moveId": "MIRROR_SHOT", "uses": 4449}, {"moveId": "HEAVY_SLAM", "uses": 3607}, {"moveId": "EARTHQUAKE", "uses": 6141}]}, "moveset": ["VOLT_SWITCH", "EARTHQUAKE", "MIRROR_SHOT"], "score": 75.7, "stats": {"product": 2029, "atk": 110.2, "def": 141.6, "hp": 130}}, {"speciesId": "klang", "speciesName": "Klang", "rating": 578, "matchups": [{"opponent": "moltres_galarian", "rating": 732, "opRating": 268}, {"opponent": "toxapex", "rating": 716, "opRating": 284}, {"opponent": "araquanid", "rating": 628, "opRating": 372}, {"opponent": "talonflame", "rating": 628, "opRating": 372}, {"opponent": "munchlax", "rating": 540, "opRating": 460}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "magmar", "rating": 125}, {"opponent": "magmortar", "rating": 129}, {"opponent": "nidoking", "rating": 212}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11456}, {"moveId": "METAL_SOUND", "uses": 7697}, {"moveId": "CHARGE_BEAM", "uses": 5853}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 4058}, {"moveId": "VICE_GRIP", "uses": 9579}, {"moveId": "THUNDERBOLT", "uses": 11330}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "VICE_GRIP"], "score": 74.6, "stats": {"product": 1954, "atk": 114, "def": 137, "hp": 125}}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 692, "matchups": [{"opponent": "araquanid", "rating": 711, "opRating": 288}, {"opponent": "talonflame", "rating": 686, "opRating": 313}, {"opponent": "swalot", "rating": 664, "opRating": 335}, {"opponent": "umbreon", "rating": 598, "opRating": 401}, {"opponent": "ninetales", "rating": 572, "opRating": 427}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "magby", "rating": 204}, {"opponent": "victini", "rating": 205}, {"opponent": "magmar", "rating": 254}, {"opponent": "nidoking", "rating": 366}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 3739}, {"moveId": "INCINERATE", "uses": 14833}, {"moveId": "FIRE_FANG", "uses": 6429}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 8324}, {"moveId": "PSYCHIC", "uses": 3691}, {"moveId": "OVERHEAT", "uses": 8178}, {"moveId": "FOCUS_BLAST", "uses": 4793}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 74.4, "stats": {"product": 1480, "atk": 151.3, "def": 71.4, "hp": 137}}, {"speciesId": "venomoth", "speciesName": "Venomoth", "rating": 519, "matchups": [{"opponent": "obstagoon", "rating": 694, "opRating": 305}, {"opponent": "umbreon", "rating": 690, "opRating": 309}, {"opponent": "araquanid", "rating": 648, "opRating": 351}, {"opponent": "qwilfish_his<PERSON>an", "rating": 538, "opRating": 461}, {"opponent": "dedenne", "rating": 519, "opRating": 480}], "counters": [{"opponent": "darum<PERSON>", "rating": 100}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 103}, {"opponent": "stunfisk_galarian", "rating": 109}, {"opponent": "steelix", "rating": 125}, {"opponent": "celesteela", "rating": 194}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 8620}, {"moveId": "CONFUSION", "uses": 8983}, {"moveId": "BUG_BITE", "uses": 7407}], "chargedMoves": [{"moveId": "SILVER_WIND", "uses": 6543}, {"moveId": "RETURN", "uses": 3717}, {"moveId": "PSYCHIC", "uses": 4396}, {"moveId": "POISON_FANG", "uses": 7349}, {"moveId": "BUG_BUZZ", "uses": 2930}]}, "moveset": ["INFESTATION", "POISON_FANG", "BUG_BUZZ"], "score": 73.9, "stats": {"product": 1753, "atk": 128.1, "def": 104.3, "hp": 131}}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 606, "matchups": [{"opponent": "ninetales", "rating": 688, "opRating": 311}, {"opponent": "araquanid", "rating": 662, "opRating": 337}, {"opponent": "nidoking", "rating": 640, "opRating": 359}, {"opponent": "munchlax", "rating": 592, "opRating": 407}, {"opponent": "magmar", "rating": 530, "opRating": 469}], "counters": [{"opponent": "tyrunt", "rating": 127}, {"opponent": "toxapex", "rating": 156}, {"opponent": "tentacruel", "rating": 174}, {"opponent": "magmortar", "rating": 226}, {"opponent": "stunfisk_galarian", "rating": 372}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 1298}, {"moveId": "INCINERATE", "uses": 6719}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1159}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 625}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1272}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 876}, {"moveId": "HIDDEN_POWER_POISON", "uses": 704}, {"moveId": "HIDDEN_POWER_ICE", "uses": 844}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1576}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 555}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 836}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1108}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1447}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1057}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 813}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 814}, {"moveId": "HIDDEN_POWER_DARK", "uses": 856}, {"moveId": "HIDDEN_POWER_BUG", "uses": 774}, {"moveId": "EXTRASENSORY", "uses": 1618}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 1308}, {"moveId": "SACRED_FIRE", "uses": 7784}, {"moveId": "FIRE_BLAST", "uses": 1549}, {"moveId": "EARTHQUAKE", "uses": 5308}, {"moveId": "BRAVE_BIRD", "uses": 9023}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 73.7, "stats": {"product": 1807, "atk": 123.6, "def": 128.1, "hp": 114}}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 678, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 811, "opRating": 188}, {"opponent": "steelix", "rating": 704, "opRating": 295}, {"opponent": "r<PERSON><PERSON>", "rating": 682, "opRating": 317}, {"opponent": "dedenne", "rating": 682, "opRating": 317}, {"opponent": "magmortar", "rating": 537, "opRating": 462}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "nidoqueen", "rating": 89}, {"opponent": "nidoking", "rating": 90}, {"opponent": "tyrunt", "rating": 143}, {"opponent": "magmar", "rating": 183}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 14848}, {"moveId": "ASTONISH", "uses": 10152}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 8836}, {"moveId": "THUNDER", "uses": 3852}, {"moveId": "OVERHEAT", "uses": 12271}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 73.6, "stats": {"product": 1721, "atk": 129.1, "def": 143.2, "hp": 93}}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 519, "matchups": [{"opponent": "em<PERSON>ga", "rating": 779, "opRating": 220}, {"opponent": "moltres_galarian", "rating": 607, "opRating": 392}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 586, "opRating": 413}, {"opponent": "manectric", "rating": 564, "opRating": 435}, {"opponent": "r<PERSON><PERSON>", "rating": 543, "opRating": 456}], "counters": [{"opponent": "amaura", "rating": 112}, {"opponent": "avalugg_his<PERSON>an", "rating": 152}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 168}, {"opponent": "nidoking", "rating": 244}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12931}, {"moveId": "ASTONISH", "uses": 12069}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 12406}, {"moveId": "THUNDER", "uses": 5385}, {"moveId": "OMINOUS_WIND", "uses": 7196}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 73.3, "stats": {"product": 1721, "atk": 129.1, "def": 143.2, "hp": 93}}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 541, "matchups": [{"opponent": "nidoking", "rating": 845, "opRating": 154}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 710, "opRating": 289}, {"opponent": "nidoqueen", "rating": 706, "opRating": 293}, {"opponent": "steelix", "rating": 615, "opRating": 384}, {"opponent": "stunfisk_galarian", "rating": 575, "opRating": 424}], "counters": [{"opponent": "pawniard", "rating": 93}, {"opponent": "bombirdier", "rating": 105}, {"opponent": "bisharp", "rating": 106}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 112}, {"opponent": "moltres_galarian", "rating": 118}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 12662}, {"moveId": "BUG_BITE", "uses": 12338}], "chargedMoves": [{"moveId": "PSYBEAM", "uses": 3551}, {"moveId": "BULLDOZE", "uses": 13101}, {"moveId": "BUG_BUZZ", "uses": 8363}]}, "moveset": ["CONFUSION", "BUG_BUZZ", "BULLDOZE"], "score": 73.1, "stats": {"product": 2033, "atk": 109.8, "def": 146.7, "hp": 126}}, {"speciesId": "sandshrew_alolan", "speciesName": "Sandshrew (Alolan)", "rating": 586, "matchups": [{"opponent": "arctibax", "rating": 677, "opRating": 322}, {"opponent": "dedenne", "rating": 566, "opRating": 433}, {"opponent": "tyrunt", "rating": 551, "opRating": 448}, {"opponent": "steelix", "rating": 539, "opRating": 460}, {"opponent": "toxapex", "rating": 531, "opRating": 468}], "counters": [{"opponent": "ninetales", "rating": 111}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magby", "rating": 116}, {"opponent": "magmar", "rating": 125}, {"opponent": "magmortar", "rating": 129}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 15277}, {"moveId": "METAL_CLAW", "uses": 9723}], "chargedMoves": [{"moveId": "RETURN", "uses": 4175}, {"moveId": "NIGHT_SLASH", "uses": 11983}, {"moveId": "GYRO_BALL", "uses": 3338}, {"moveId": "BLIZZARD", "uses": 5538}]}, "moveset": ["POWDER_SNOW", "NIGHT_SLASH", "BLIZZARD"], "score": 71.5, "stats": {"product": 1808, "atk": 117.6, "def": 121, "hp": 127}}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 737, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 672, "opRating": 327}, {"opponent": "umbreon", "rating": 654, "opRating": 345}, {"opponent": "araquanid", "rating": 637, "opRating": 362}, {"opponent": "r<PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "toxapex", "rating": 561, "opRating": 438}], "counters": [{"opponent": "amaura", "rating": 112}, {"opponent": "sandslash_alolan", "rating": 125}, {"opponent": "avalugg_his<PERSON>an", "rating": 152}, {"opponent": "stunfisk_galarian", "rating": 215}, {"opponent": "nidoking", "rating": 267}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 16508}, {"moveId": "TACKLE", "uses": 8492}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13007}, {"moveId": "SWIFT", "uses": 8380}, {"moveId": "ENERGY_BALL", "uses": 3651}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 71.4, "stats": {"product": 1813, "atk": 123.2, "def": 130.1, "hp": 113}}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 632, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 811, "opRating": 188}, {"opponent": "araquanid", "rating": 618, "opRating": 381}, {"opponent": "munchlax", "rating": 614, "opRating": 385}, {"opponent": "steelix", "rating": 602, "opRating": 397}, {"opponent": "ninetales", "rating": 594, "opRating": 405}], "counters": [{"opponent": "tyrunt", "rating": 143}, {"opponent": "tentacruel", "rating": 174}, {"opponent": "nidoking", "rating": 287}, {"opponent": "magmar", "rating": 299}, {"opponent": "stunfisk_galarian", "rating": 322}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 14180}, {"moveId": "EMBER", "uses": 10820}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 2659}, {"moveId": "OVERHEAT", "uses": 11883}, {"moveId": "EARTHQUAKE", "uses": 10467}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 71.2, "stats": {"product": 2068, "atk": 107.5, "def": 151.3, "hp": 127}}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 656, "matchups": [{"opponent": "dedenne", "rating": 623, "opRating": 376}, {"opponent": "arcanine", "rating": 606, "opRating": 393}, {"opponent": "magby", "rating": 589, "opRating": 410}, {"opponent": "umbreon", "rating": 578, "opRating": 421}, {"opponent": "magmortar", "rating": 534, "opRating": 465}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "sandslash_alolan", "rating": 108}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "nidoking", "rating": 145}, {"opponent": "nidoqueen", "rating": 154}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 9624}, {"moveId": "POISON_JAB", "uses": 10481}, {"moveId": "BITE", "uses": 4907}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 6632}, {"moveId": "GUNK_SHOT", "uses": 2567}, {"moveId": "DARK_PULSE", "uses": 13591}, {"moveId": "ACID_SPRAY", "uses": 2214}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "ACID_SPRAY"], "score": 70.5, "stats": {"product": 1906, "atk": 117.3, "def": 111.2, "hp": 146}}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 691, "matchups": [{"opponent": "nidoking", "rating": 794, "opRating": 205}, {"opponent": "magmar", "rating": 670, "opRating": 329}, {"opponent": "magby", "rating": 666, "opRating": 333}, {"opponent": "arctibax", "rating": 619, "opRating": 380}, {"opponent": "araquanid", "rating": 572, "opRating": 427}], "counters": [{"opponent": "typhlosion", "rating": 213}, {"opponent": "zapdos", "rating": 291}, {"opponent": "toxapex", "rating": 309}, {"opponent": "ninetales", "rating": 325}, {"opponent": "stunfisk_galarian", "rating": 420}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 4440}, {"moveId": "FIRE_SPIN", "uses": 7298}, {"moveId": "EMBER", "uses": 5281}, {"moveId": "DRAGON_BREATH", "uses": 4623}, {"moveId": "AIR_SLASH", "uses": 3346}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 2523}, {"moveId": "FLAMETHROWER", "uses": 2694}, {"moveId": "FIRE_BLAST", "uses": 1473}, {"moveId": "DRAGON_CLAW", "uses": 6737}, {"moveId": "BLAST_BURN", "uses": 11660}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "DRAGON_CLAW"], "score": 69.8, "stats": {"product": 1675, "atk": 132.1, "def": 108.3, "hp": 117}}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 659, "matchups": [{"opponent": "toxapex", "rating": 695, "opRating": 304}, {"opponent": "araquanid", "rating": 683, "opRating": 316}, {"opponent": "r<PERSON><PERSON>", "rating": 670, "opRating": 329}, {"opponent": "umbreon", "rating": 620, "opRating": 379}, {"opponent": "ninetales", "rating": 595, "opRating": 404}], "counters": [{"opponent": "ferrothorn", "rating": 196}, {"opponent": "stunfisk_galarian", "rating": 248}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 260}, {"opponent": "steelix", "rating": 310}, {"opponent": "magmar", "rating": 424}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 8668}, {"moveId": "SNARL", "uses": 10770}, {"moveId": "CHARGE_BEAM", "uses": 5560}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 9912}, {"moveId": "THUNDER", "uses": 1542}, {"moveId": "RETURN", "uses": 2217}, {"moveId": "PSYCHIC_FANGS", "uses": 5865}, {"moveId": "OVERHEAT", "uses": 4084}, {"moveId": "FLAME_BURST", "uses": 1353}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "WILD_CHARGE"], "score": 68.4, "stats": {"product": 1561, "atk": 142.4, "def": 91.3, "hp": 120}}, {"speciesId": "helioptile", "speciesName": "Helioptile", "rating": 416, "matchups": [{"opponent": "talonflame", "rating": 575, "opRating": 424}, {"opponent": "oricorio_pom_pom", "rating": 525, "opRating": 474}, {"opponent": "budew", "rating": 512, "opRating": 487}, {"opponent": "fennekin", "rating": 508, "opRating": 491}, {"opponent": "vullaby", "rating": 504, "opRating": 495}], "counters": [{"opponent": "r<PERSON><PERSON>", "rating": 84}, {"opponent": "r<PERSON><PERSON>_alolan", "rating": 97}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "magmar", "rating": 165}, {"opponent": "stunfisk_galarian", "rating": 186}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 12722}, {"moveId": "QUICK_ATTACK", "uses": 12278}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 4276}, {"moveId": "PARABOLIC_CHARGE", "uses": 10285}, {"moveId": "BULLDOZE", "uses": 10453}]}, "moveset": ["THUNDER_SHOCK", "BULLDOZE", "PARABOLIC_CHARGE"], "score": 68.3, "stats": {"product": 1016, "atk": 109.2, "def": 78.1, "hp": 119}}, {"speciesId": "volcarona", "speciesName": "Volcarona", "rating": 17, "matchups": [{"opponent": "darkrai", "rating": 833, "opRating": 166}], "counters": [{"opponent": "stunfisk_galarian", "rating": 5}, {"opponent": "umbreon", "rating": 9}, {"opponent": "nidoking", "rating": 11}, {"opponent": "tyrunt", "rating": 11}, {"opponent": "magmar", "rating": 17}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 12193}, {"moveId": "BUG_BITE", "uses": 12807}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 2414}, {"moveId": "OVERHEAT", "uses": 10344}, {"moveId": "HURRICANE", "uses": 4963}, {"moveId": "BUG_BUZZ", "uses": 7290}]}, "moveset": ["BUG_BITE", "OVERHEAT", "BUG_BUZZ"], "score": 68.2, "stats": {"product": 8, "atk": 24.8, "def": 17.7, "hp": 18}}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 648, "matchups": [{"opponent": "magby", "rating": 746, "opRating": 253}, {"opponent": "ninetales", "rating": 738, "opRating": 261}, {"opponent": "avalugg_his<PERSON>an", "rating": 685, "opRating": 314}, {"opponent": "magmar", "rating": 594, "opRating": 405}, {"opponent": "arctibax", "rating": 530, "opRating": 469}], "counters": [{"opponent": "steelix", "rating": 72}, {"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "magnezone", "rating": 102}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 115}, {"opponent": "nidoking", "rating": 157}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 19743}, {"moveId": "ACID", "uses": 5257}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 3517}, {"moveId": "SCALD", "uses": 11751}, {"moveId": "RETURN", "uses": 3045}, {"moveId": "HYDRO_PUMP", "uses": 2341}, {"moveId": "BLIZZARD", "uses": 3189}, {"moveId": "ACID_SPRAY", "uses": 1185}]}, "moveset": ["POISON_JAB", "ACID_SPRAY", "SCALD"], "score": 67, "stats": {"product": 2033, "atk": 110, "def": 139.8, "hp": 132}}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 564, "matchups": [{"opponent": "toxapex", "rating": 867, "opRating": 132}, {"opponent": "avalugg_his<PERSON>an", "rating": 696, "opRating": 303}, {"opponent": "arctibax", "rating": 651, "opRating": 348}, {"opponent": "tyrunt", "rating": 647, "opRating": 352}, {"opponent": "araquanid", "rating": 642, "opRating": 357}], "counters": [{"opponent": "stunfisk_galarian", "rating": 44}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 78}, {"opponent": "armarouge", "rating": 131}, {"opponent": "rapidash", "rating": 135}, {"opponent": "magmar", "rating": 165}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 11685}, {"moveId": "METAL_SOUND", "uses": 7505}, {"moveId": "CHARGE_BEAM", "uses": 5775}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 5946}, {"moveId": "MIRROR_SHOT", "uses": 9021}, {"moveId": "HYPER_BEAM", "uses": 5350}, {"moveId": "FLASH_CANNON", "uses": 4694}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 65.7, "stats": {"product": 1786, "atk": 124.8, "def": 140.1, "hp": 102}}, {"speciesId": "r<PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 737, "matchups": [{"opponent": "toxapex", "rating": 774, "opRating": 225}, {"opponent": "avalugg_his<PERSON>an", "rating": 752, "opRating": 247}, {"opponent": "ninetales", "rating": 730, "opRating": 269}, {"opponent": "umbreon", "rating": 637, "opRating": 362}, {"opponent": "magmortar", "rating": 530, "opRating": 469}], "counters": [{"opponent": "victreebel", "rating": 121}, {"opponent": "magmar", "rating": 183}, {"opponent": "nidoqueen", "rating": 241}, {"opponent": "nidoking", "rating": 291}, {"opponent": "stunfisk_galarian", "rating": 369}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 8763}, {"moveId": "THUNDER_SHOCK", "uses": 8913}, {"moveId": "SPARK", "uses": 4962}, {"moveId": "CHARM", "uses": 2376}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8826}, {"moveId": "TRAILBLAZE", "uses": 3105}, {"moveId": "THUNDER_PUNCH", "uses": 5127}, {"moveId": "THUNDER", "uses": 1396}, {"moveId": "SKULL_BASH", "uses": 2544}, {"moveId": "BRICK_BREAK", "uses": 4047}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "BRICK_BREAK"], "score": 65.1, "stats": {"product": 1670, "atk": 133.5, "def": 110.6, "hp": 113}}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 527, "matchups": [{"opponent": "nidoking", "rating": 836, "opRating": 163}, {"opponent": "dedenne", "rating": 821, "opRating": 178}, {"opponent": "nidoqueen", "rating": 626, "opRating": 373}, {"opponent": "stunfisk_galarian", "rating": 589, "opRating": 410}, {"opponent": "steelix", "rating": 531, "opRating": 468}], "counters": [{"opponent": "darum<PERSON>", "rating": 81}, {"opponent": "arcanine", "rating": 86}, {"opponent": "magby", "rating": 104}, {"opponent": "magmar", "rating": 111}, {"opponent": "magmortar", "rating": 115}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 2671}, {"moveId": "POISON_JAB", "uses": 9166}, {"moveId": "MAGICAL_LEAF", "uses": 6035}, {"moveId": "BULLET_SEED", "uses": 7124}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 10811}, {"moveId": "SOLAR_BEAM", "uses": 870}, {"moveId": "SLUDGE_BOMB", "uses": 5026}, {"moveId": "LEAF_STORM", "uses": 2493}, {"moveId": "GRASS_KNOT", "uses": 4187}, {"moveId": "DAZZLING_GLEAM", "uses": 1609}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 65, "stats": {"product": 1562, "atk": 143, "def": 114.9, "hp": 95}}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 740, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 909, "opRating": 90}, {"opponent": "nidoqueen", "rating": 842, "opRating": 157}, {"opponent": "stunfisk_galarian", "rating": 759, "opRating": 240}, {"opponent": "steelix", "rating": 728, "opRating": 271}, {"opponent": "tyrunt", "rating": 637, "opRating": 362}], "counters": [{"opponent": "victreebel", "rating": 148}, {"opponent": "golbat", "rating": 150}, {"opponent": "armarouge", "rating": 153}, {"opponent": "wormadam_sandy", "rating": 154}, {"opponent": "magmar", "rating": 223}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 7841}, {"moveId": "IRON_TAIL", "uses": 1453}, {"moveId": "FURY_CUTTER", "uses": 6526}, {"moveId": "DOUBLE_KICK", "uses": 9181}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 3274}, {"moveId": "SAND_TOMB", "uses": 3978}, {"moveId": "MEGAHORN", "uses": 4850}, {"moveId": "EARTH_POWER", "uses": 9502}, {"moveId": "EARTHQUAKE", "uses": 3450}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 63.9, "stats": {"product": 1719, "atk": 130, "def": 104, "hp": 127}}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 659, "matchups": [{"opponent": "toxapex", "rating": 690, "opRating": 309}, {"opponent": "araquanid", "rating": 677, "opRating": 322}, {"opponent": "talonflame", "rating": 656, "opRating": 343}, {"opponent": "rapidash", "rating": 622, "opRating": 377}, {"opponent": "magmortar", "rating": 521, "opRating": 478}], "counters": [{"opponent": "meowscarada", "rating": 186}, {"opponent": "stunfisk_galarian", "rating": 218}, {"opponent": "weezing_galarian", "rating": 221}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 272}, {"opponent": "steelix", "rating": 330}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 2880}, {"moveId": "SNARL", "uses": 3135}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1402}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 815}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1573}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1117}, {"moveId": "HIDDEN_POWER_POISON", "uses": 873}, {"moveId": "HIDDEN_POWER_ICE", "uses": 1077}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 1978}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 685}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1068}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1056}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1488}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1382}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1355}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1008}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1081}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1016}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 11311}, {"moveId": "PSYCHIC_FANGS", "uses": 6442}, {"moveId": "HYPER_BEAM", "uses": 1965}, {"moveId": "CRUNCH", "uses": 5261}]}, "moveset": ["SPARK", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 63.6, "stats": {"product": 1612, "atk": 138, "def": 98.9, "hp": 118}}, {"speciesId": "revavroom", "speciesName": "Revavroom", "rating": 597, "matchups": [{"opponent": "dedenne", "rating": 785, "opRating": 214}, {"opponent": "araquanid", "rating": 710, "opRating": 289}, {"opponent": "avalugg_his<PERSON>an", "rating": 666, "opRating": 333}, {"opponent": "toxapex", "rating": 631, "opRating": 368}, {"opponent": "umbreon", "rating": 614, "opRating": 385}], "counters": [{"opponent": "stunfisk_galarian", "rating": 85}, {"opponent": "steelix", "rating": 104}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 123}, {"opponent": "nidoking", "rating": 149}, {"opponent": "magmar", "rating": 303}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 16602}, {"moveId": "LICK", "uses": 8398}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 10755}, {"moveId": "GYRO_BALL", "uses": 5109}, {"moveId": "GUNK_SHOT", "uses": 6404}, {"moveId": "ACID_SPRAY", "uses": 2715}]}, "moveset": ["POISON_JAB", "OVERHEAT", "ACID_SPRAY"], "score": 61.5, "stats": {"product": 1648, "atk": 135.6, "def": 106.5, "hp": 114}}, {"speciesId": "poipole", "speciesName": "Po<PERSON><PERSON>", "rating": 613, "matchups": [{"opponent": "ninetales", "rating": 711, "opRating": 288}, {"opponent": "magby", "rating": 653, "opRating": 346}, {"opponent": "magmortar", "rating": 649, "opRating": 350}, {"opponent": "dedenne", "rating": 602, "opRating": 397}, {"opponent": "araquanid", "rating": 551, "opRating": 448}], "counters": [{"opponent": "stunfisk_galarian", "rating": 130}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 169}, {"opponent": "steelix", "rating": 193}, {"opponent": "nidoqueen", "rating": 223}, {"opponent": "nidoking", "rating": 248}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 17640}, {"moveId": "PECK", "uses": 7360}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 4785}, {"moveId": "SLUDGE_BOMB", "uses": 14556}, {"moveId": "FELL_STINGER", "uses": 5657}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "FELL_STINGER"], "score": 60.4, "stats": {"product": 1863, "atk": 119.7, "def": 113.5, "hp": 137}}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 699, "matchups": [{"opponent": "toxapex", "rating": 897, "opRating": 102}, {"opponent": "araquanid", "rating": 789, "opRating": 210}, {"opponent": "avalugg_his<PERSON>an", "rating": 647, "opRating": 352}, {"opponent": "ninetales", "rating": 583, "opRating": 416}, {"opponent": "tyrunt", "rating": 529, "opRating": 470}], "counters": [{"opponent": "stunfisk_galarian", "rating": 118}, {"opponent": "litleo", "rating": 171}, {"opponent": "magmar", "rating": 218}, {"opponent": "nidoking", "rating": 259}, {"opponent": "electrode_hisuian", "rating": 265}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 9525}, {"moveId": "SPARK", "uses": 5840}, {"moveId": "METAL_SOUND", "uses": 5300}, {"moveId": "CHARGE_BEAM", "uses": 4357}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 2056}, {"moveId": "WILD_CHARGE", "uses": 15064}, {"moveId": "MIRROR_SHOT", "uses": 5227}, {"moveId": "FLASH_CANNON", "uses": 2751}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 60.2, "stats": {"product": 1653, "atk": 134.9, "def": 120, "hp": 102}}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 636, "matchups": [{"opponent": "umbreon", "rating": 827, "opRating": 172}, {"opponent": "nidoking", "rating": 763, "opRating": 236}, {"opponent": "tyrunt", "rating": 609, "opRating": 390}, {"opponent": "stunfisk_galarian", "rating": 560, "opRating": 439}, {"opponent": "magmar", "rating": 552, "opRating": 447}], "counters": [{"opponent": "talonflame", "rating": 202}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 235}, {"opponent": "golbat", "rating": 253}, {"opponent": "revavroom", "rating": 289}, {"opponent": "steelix", "rating": 294}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 13157}, {"moveId": "BUG_BITE", "uses": 11843}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 8984}, {"moveId": "MIRROR_COAT", "uses": 3212}, {"moveId": "BUG_BUZZ", "uses": 7719}, {"moveId": "BUBBLE_BEAM", "uses": 5065}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 47.3, "stats": {"product": 2292, "atk": 97.8, "def": 176.1, "hp": 133}}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 798, "matchups": [{"opponent": "toxapex", "rating": 921, "opRating": 78}, {"opponent": "araquanid", "rating": 764, "opRating": 235}, {"opponent": "avalugg_his<PERSON>an", "rating": 727, "opRating": 272}, {"opponent": "ninetales", "rating": 623, "opRating": 376}, {"opponent": "tyrunt", "rating": 574, "opRating": 425}], "counters": [{"opponent": "stunfisk_galarian", "rating": 73}, {"opponent": "nidoking", "rating": 90}, {"opponent": "nidoqueen", "rating": 104}, {"opponent": "steelix", "rating": 165}, {"opponent": "magmar", "rating": 218}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 15912}, {"moveId": "SPARK", "uses": 9088}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 18484}, {"moveId": "GYRO_BALL", "uses": 3480}, {"moveId": "FELL_STINGER", "uses": 3009}]}, "moveset": ["THUNDER_SHOCK", "FELL_STINGER", "WILD_CHARGE"], "score": 44.7, "stats": {"product": 1688, "atk": 132.1, "def": 105.5, "hp": 121}}]