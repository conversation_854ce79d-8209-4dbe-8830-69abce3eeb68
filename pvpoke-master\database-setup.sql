-- PostgreSQL version of the training database schema for Render

CREATE TABLE training_pokemon (
  training_pokemon_id BIGSERIAL PRIMARY KEY,
  pokemon_id VARCHAR(64) NOT NULL,
  format VARCHAR(64) NOT NULL,
  team_position SMALLINT NOT NULL,
  player_type SMALLINT NOT NULL,
  team_score SMALLINT NOT NULL,
  individual_score REAL NOT NULL,
  shields SMALLINT NOT NULL,
  post_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE training_team (
  training_team_id BIGSERIAL PRIMARY KEY,
  team_str VARCHAR(255) NOT NULL,
  format VARCHAR(64) NOT NULL,
  player_type SMALLINT NOT NULL,
  team_score SMALLINT NOT NULL,
  post_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_training_pokemon_format ON training_pokemon(format);
CREATE INDEX idx_training_pokemon_datetime ON training_pokemon(post_datetime);
CREATE INDEX idx_training_team_format ON training_team(format);
CREATE INDEX idx_training_team_datetime ON training_team(post_datetime);
