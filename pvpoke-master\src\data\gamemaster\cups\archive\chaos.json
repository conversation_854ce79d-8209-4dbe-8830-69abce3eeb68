{"name": "chaos", "title": "Devon Chaos Cup", "include": [{"filterType": "id", "name": "Species", "values": ["giratina_origin", "malamar", "tentacruel", "tentacruel_shadow", "seaking", "gyarados", "gyarado<PERSON>_shadow", "zapdos", "zap<PERSON>_shadow", "milotic", "king<PERSON>", "king<PERSON>_shadow", "<PERSON>ras", "lap<PERSON>_shadow", "flygon", "flygon_shadow", "charizard", "charizard_shadow", "articuno", "articuno_shadow", "moltres", "moltres_shadow", "lugia", "lugia_shadow", "aurorus", "barbara<PERSON>", "tyrunt", "cradily", "cradily_shadow", "run<PERSON><PERSON>", "golisopod", "relicanth", "arm<PERSON>", "<PERSON><PERSON>_<PERSON>", "cloyster", "cloyster_shadow", "omastar", "omastar_shadow", "kabutops", "kabut<PERSON>_shadow", "regirock", "regirock_shadow", "dusclops", "dusclops_shadow", "mew", "arctibax", "regice", "regice_shadow", "abomasnow", "abomasnow_shadow", "gliscor", "gliscor_shadow", "blastoise", "blastoise_shadow", "sceptile", "sceptile_shadow", "golduck", "<PERSON><PERSON>_shadow", "suicune", "suicune_shadow", "scyther", "scyther_shadow", "nidoqueen", "nidoqueen_shadow", "nidoking", "nidoking_shadow", "tangrowth", "tangrowth_shadow", "ninjask", "piloswine", "piloswine_shadow", "octillery", "dragapult", "samu<PERSON><PERSON>_<PERSON><PERSON>an", "arcanine_<PERSON><PERSON>an", "lileep", "lileep_shadow", "uxie", "mesprit", "<PERSON><PERSON><PERSON>", "grou<PERSON>_shadow", "kyogre_shadow", "heatran", "heatran_shadow", "archeops", "archeops_shadow", "cresselia", "cress<PERSON>a_shadow", "<PERSON><PERSON><PERSON>", "jumpluff_shadow", "nidorina", "nidor<PERSON>_shadow"]}], "exclude": [{"filterType": "tag", "name": "Tag", "values": ["mega"]}, {"filterType": "tag", "name": "Tag", "values": []}], "levelCap": 50, "includeLowStatProduct": true}