/*
 * Matrix State Manager for PvPoke
 * Handles persistent state management and shareable links for matrix battle mode
 * Requirements: 1.1, 1.3, 1.4, 1.5
 */

function MatrixStateManager() {
    var self = this;
    var STORAGE_KEY = 'pvpoke_matrix_state';
    var STATE_VERSION = '1.0';
    var MAX_STATE_AGE_DAYS = 7;
    var MAX_POKEMON_PER_TEAM = 100;
    
    // Initialize the manager
    this.init = function() {
        // Clean up old states on initialization
        self.cleanupOldStates();
    };
    
    /**
     * Save matrix state to localStorage
     * @param {Array} teamA - Array of Pokemon objects for Team A
     * @param {Array} teamB - Array of Pokemon objects for Team B  
     * @param {Object} settings - Battle settings object
     * @returns {boolean} - Success status
     */
    this.saveMatrixState = function(teamA, teamB, settings) {
        try {
            // Validate input parameters
            if (!Array.isArray(teamA) || !Array.isArray(teamB)) {
                console.warn('MatrixStateManager: Invalid team data provided');
                return false;
            }
            
            // Validate team sizes
            if (teamA.length > MAX_POKEMON_PER_TEAM || teamB.length > MAX_POKEMON_PER_TEAM) {
                console.warn('MatrixStateManager: Team size exceeds maximum allowed');
                return false;
            }
            
            // Create state object
            var matrixState = {
                version: STATE_VERSION,
                timestamp: Date.now(),
                league: settings && settings.league ? settings.league : 1500,
                settings: {
                    matrixMode: settings && settings.matrixMode ? settings.matrixMode : 'battle',
                    shields: settings && settings.shields ? settings.shields : [1, 1],
                    breakpointMode: settings && settings.breakpointMode ? settings.breakpointMode : 'fast'
                },
                teams: {
                    A: self.serializeTeam(teamA),
                    B: self.serializeTeam(teamB)
                }
            };
            
            // Validate the created state
            if (!self.validateMatrixState(matrixState)) {
                console.warn('MatrixStateManager: Generated state failed validation');
                return false;
            }
            
            // Save to localStorage
            var stateJson = JSON.stringify(matrixState);
            localStorage.setItem(STORAGE_KEY, stateJson);
            
            console.log('MatrixStateManager: State saved successfully');
            return true;
            
        } catch (error) {
            console.error('MatrixStateManager: Failed to save state:', error);
            return false;
        }
    };
    
    /**
     * Load matrix state from localStorage
     * @returns {Object|null} - Matrix state object or null if not found/invalid
     */
    this.loadMatrixState = function() {
        try {
            var stateJson = localStorage.getItem(STORAGE_KEY);
            
            if (!stateJson) {
                return null;
            }
            
            var matrixState = JSON.parse(stateJson);
            
            // Validate loaded state
            if (!self.validateMatrixState(matrixState)) {
                console.warn('MatrixStateManager: Loaded state failed validation, clearing');
                self.clearMatrixState();
                return null;
            }
            
            // Check if state is too old
            if (self.isStateExpired(matrixState)) {
                console.log('MatrixStateManager: State expired, clearing');
                self.clearMatrixState();
                return null;
            }
            
            console.log('MatrixStateManager: State loaded successfully');
            return matrixState;
            
        } catch (error) {
            console.error('MatrixStateManager: Failed to load state:', error);
            // Clear corrupted state
            self.clearMatrixState();
            return null;
        }
    };
    
    /**
     * Clear matrix state from localStorage with verification
     */
    this.clearMatrixState = function() {
        try {
            var hadState = localStorage.getItem(STORAGE_KEY) !== null;
            localStorage.removeItem(STORAGE_KEY);

            // Verify clearing was successful
            var stillHasState = localStorage.getItem(STORAGE_KEY) !== null;

            if (hadState && !stillHasState) {
                console.log('MatrixStateManager: State cleared successfully');
            } else if (!hadState) {
                console.log('MatrixStateManager: No state to clear');
            } else {
                console.warn('MatrixStateManager: State clearing may have failed');
            }
        } catch (error) {
            console.error('MatrixStateManager: Failed to clear state:', error);
        }
    };
    
    /**
     * Check if matrix state exists in localStorage with comprehensive debugging
     * @returns {boolean} - True if state exists and is valid
     */
    this.hasMatrixState = function() {
        try {
            console.log('🔍 DEBUG: MatrixStateManager.hasMatrixState() called');

            // Direct localStorage check to avoid caching issues
            var stateJson = localStorage.getItem(STORAGE_KEY);
            console.log('🔍 DEBUG: Raw localStorage value:', stateJson);

            if (!stateJson) {
                console.log('✅ DEBUG: No localStorage data found - returning false');
                return false;
            }

            // Quick validation without full parsing
            var state = JSON.parse(stateJson);
            var hasValidStructure = state && state.version && state.teams;

            // Check if teams actually have Pokemon (not just empty arrays)
            var hasActualPokemon = false;
            if (hasValidStructure && state.teams) {
                var teamA = state.teams.A || [];
                var teamB = state.teams.B || [];
                hasActualPokemon = teamA.length > 0 || teamB.length > 0;
            }

            console.log('🔍 DEBUG: Parsed state validation:');
            console.log('  - state exists:', !!state);
            console.log('  - has version:', !!state?.version);
            console.log('  - has teams structure:', !!state?.teams);
            console.log('  - team A length:', state?.teams?.A?.length || 0);
            console.log('  - team B length:', state?.teams?.B?.length || 0);
            console.log('  - has actual Pokemon:', hasActualPokemon);
            console.log('  - final result:', hasValidStructure && hasActualPokemon);

            return hasValidStructure && hasActualPokemon;
        } catch (error) {
            console.warn('❌ DEBUG: MatrixStateManager error checking state existence:', error);
            return false;
        }
    };
    
    /**
     * Serialize team data for storage
     * @param {Array} team - Array of Pokemon objects
     * @returns {Array} - Serialized team data
     */
    this.serializeTeam = function(team) {
        if (!Array.isArray(team)) {
            return [];
        }
        
        return team.map(function(pokemon) {
            if (!pokemon) {
                return null;
            }
            
            return {
                speciesId: pokemon.speciesId || pokemon.species,
                level: pokemon.level || 20,
                ivs: {
                    attack: pokemon.ivs ? pokemon.ivs.attack : 15,
                    defense: pokemon.ivs ? pokemon.ivs.defense : 15,
                    hp: pokemon.ivs ? pokemon.ivs.hp : 15
                },
                moves: {
                    fastMove: pokemon.fastMove ? pokemon.fastMove.name : '',
                    chargedMoves: pokemon.chargedMoves ? pokemon.chargedMoves.map(function(move) {
                        return move.name || move;
                    }) : []
                },
                nickname: pokemon.nickname || '',
                shadow: pokemon.shadow || false,
                purified: pokemon.purified || false
            };
        }).filter(function(pokemon) {
            return pokemon !== null;
        });
    };    
 
   /**
     * Validate matrix state object
     * @param {Object} state - Matrix state to validate
     * @returns {boolean} - True if state is valid
     */
    this.validateMatrixState = function(state) {
        try {
            // Check required fields
            if (!state || typeof state !== 'object') {
                return false;
            }
            
            // Check version compatibility
            if (!state.version || parseFloat(state.version) > parseFloat(STATE_VERSION)) {
                console.warn('MatrixStateManager: Unsupported state version:', state.version);
                return false;
            }
            
            // Check timestamp
            if (!state.timestamp || typeof state.timestamp !== 'number') {
                return false;
            }
            
            // Check teams structure
            if (!state.teams || typeof state.teams !== 'object') {
                return false;
            }
            
            if (!Array.isArray(state.teams.A) || !Array.isArray(state.teams.B)) {
                return false;
            }
            
            // Validate team sizes
            if (state.teams.A.length > MAX_POKEMON_PER_TEAM || 
                state.teams.B.length > MAX_POKEMON_PER_TEAM) {
                return false;
            }
            
            // Validate Pokemon in teams
            var isTeamAValid = state.teams.A.every(function(pokemon) {
                return self.validatePokemonData(pokemon);
            });
            
            var isTeamBValid = state.teams.B.every(function(pokemon) {
                return self.validatePokemonData(pokemon);
            });
            
            if (!isTeamAValid || !isTeamBValid) {
                return false;
            }
            
            // Check settings structure
            if (state.settings && typeof state.settings !== 'object') {
                return false;
            }
            
            return true;
            
        } catch (error) {
            console.error('MatrixStateManager: State validation error:', error);
            return false;
        }
    };
    
    /**
     * Validate individual Pokemon data
     * @param {Object} pokemon - Pokemon object to validate
     * @returns {boolean} - True if Pokemon data is valid
     */
    this.validatePokemonData = function(pokemon) {
        if (!pokemon || typeof pokemon !== 'object') {
            return false;
        }
        
        // Check required fields
        if (!pokemon.speciesId || typeof pokemon.speciesId !== 'string') {
            return false;
        }
        
        // Check level
        if (pokemon.level && (typeof pokemon.level !== 'number' || 
            pokemon.level < 1 || pokemon.level > 50)) {
            return false;
        }
        
        // Check IVs
        if (pokemon.ivs) {
            if (typeof pokemon.ivs !== 'object') {
                return false;
            }
            
            var ivFields = ['attack', 'defense', 'hp'];
            for (var i = 0; i < ivFields.length; i++) {
                var field = ivFields[i];
                if (pokemon.ivs[field] !== undefined) {
                    if (typeof pokemon.ivs[field] !== 'number' || 
                        pokemon.ivs[field] < 0 || pokemon.ivs[field] > 15) {
                        return false;
                    }
                }
            }
        }
        
        // Check moves structure
        if (pokemon.moves) {
            if (typeof pokemon.moves !== 'object') {
                return false;
            }
            
            if (pokemon.moves.chargedMoves && !Array.isArray(pokemon.moves.chargedMoves)) {
                return false;
            }
        }
        
        return true;
    };
    
    /**
     * Check if state is expired based on age
     * @param {Object} state - Matrix state to check
     * @returns {boolean} - True if state is expired
     */
    this.isStateExpired = function(state) {
        if (!state || !state.timestamp) {
            return true;
        }
        
        var ageInMs = Date.now() - state.timestamp;
        var maxAgeInMs = MAX_STATE_AGE_DAYS * 24 * 60 * 60 * 1000;
        
        return ageInMs > maxAgeInMs;
    };
    
    /**
     * Clean up old states from localStorage
     */
    this.cleanupOldStates = function() {
        try {
            var state = self.loadMatrixState();
            if (state && self.isStateExpired(state)) {
                self.clearMatrixState();
                console.log('MatrixStateManager: Cleaned up expired state');
            }
        } catch (error) {
            console.error('MatrixStateManager: Error during cleanup:', error);
        }
    };
    
    /**
     * Get empty matrix state template
     * @returns {Object} - Empty matrix state object
     */
    this.getEmptyMatrixState = function() {
        return {
            version: STATE_VERSION,
            timestamp: Date.now(),
            league: 1500,
            settings: {
                matrixMode: 'battle',
                shields: [1, 1],
                breakpointMode: 'fast'
            },
            teams: {
                A: [],
                B: []
            }
        };
    };
    
    /**
     * Handle state recovery failures gracefully
     * @param {Error} error - The error that occurred
     * @returns {Object} - Empty matrix state as fallback
     */
    this.handleStateRecoveryFailure = function(error) {
        console.warn('MatrixStateManager: State recovery failed:', error);
        
        // Clear corrupted state
        self.clearMatrixState();
        
        // Return empty state for graceful fallback
        return self.getEmptyMatrixState();
    };
    
    /**
     * Get storage information for debugging
     * @returns {Object} - Storage information
     */
    this.getStorageInfo = function() {
        try {
            var state = localStorage.getItem(STORAGE_KEY);
            return {
                hasState: !!state,
                stateSize: state ? state.length : 0,
                isValid: state ? self.validateMatrixState(JSON.parse(state)) : false,
                storageKey: STORAGE_KEY
            };
        } catch (error) {
            return {
                hasState: false,
                stateSize: 0,
                isValid: false,
                error: error.message
            };
        }
    };
    
    // ===== COMPRESSION AND URL ENCODING METHODS =====
    
    /**
     * Generate Pokemon ID mapping for compact representation
     * @returns {Object} - Object with pokemonToId and idToPokemon mappings
     */
    this.generatePokemonIdMapping = function() {
        var pokemonToId = {};
        var idToPokemon = {};
        var currentId = 1;
        
        // Get GameMaster instance if available
        if (typeof GameMaster !== 'undefined' && GameMaster.getInstance && GameMaster.getInstance().data) {
            var gm = GameMaster.getInstance();
            if (gm.data && gm.data.pokemon) {
                gm.data.pokemon.forEach(function(pokemon) {
                    if (pokemon.speciesId && !pokemonToId[pokemon.speciesId]) {
                        pokemonToId[pokemon.speciesId] = currentId;
                        idToPokemon[currentId] = pokemon.speciesId;
                        currentId++;
                    }
                });
            }
        }
        
        return {
            pokemonToId: pokemonToId,
            idToPokemon: idToPokemon
        };
    };
    
    /**
     * Generate move ID mapping for compact representation
     * @returns {Object} - Object with moveToId and idToMove mappings
     */
    this.generateMoveIdMapping = function() {
        var moveToId = {};
        var idToMove = {};
        var currentId = 1;
        
        // Get GameMaster instance if available
        if (typeof GameMaster !== 'undefined' && GameMaster.getInstance && GameMaster.getInstance().data) {
            var gm = GameMaster.getInstance();
            if (gm.data && gm.data.moves) {
                gm.data.moves.forEach(function(move) {
                    if (move.moveId && !moveToId[move.moveId]) {
                        moveToId[move.moveId] = currentId;
                        idToMove[currentId] = move.moveId;
                        currentId++;
                    }
                });
            }
        }
        
        return {
            moveToId: moveToId,
            idToMove: idToMove
        };
    };
    
    /**
     * Compress matrix state data for URL sharing
     * @param {Object} matrixState - Full matrix state object
     * @returns {Object} - Compressed state object
     */
    this.compressStateData = function(matrixState) {
        if (!matrixState) {
            return null;
        }
        
        try {
            var pokemonMapping = self.generatePokemonIdMapping();
            var moveMapping = self.generateMoveIdMapping();
            
            var compressed = {
                v: matrixState.version || STATE_VERSION,
                t: matrixState.timestamp,
                l: matrixState.league,
                s: self.compressSettings(matrixState.settings),
                teams: {
                    A: self.compressTeam(matrixState.teams.A, pokemonMapping, moveMapping),
                    B: self.compressTeam(matrixState.teams.B, pokemonMapping, moveMapping)
                }
            };
            
            return compressed;
        } catch (error) {
            console.error('MatrixStateManager: Failed to compress state data:', error);
            return null;
        }
    };
    
    /**
     * Decompress matrix state data from URL
     * @param {Object} compressedState - Compressed state object
     * @returns {Object} - Full matrix state object
     */
    this.decompressStateData = function(compressedState) {
        if (!compressedState) {
            return null;
        }
        
        try {
            var pokemonMapping = self.generatePokemonIdMapping();
            var moveMapping = self.generateMoveIdMapping();
            
            var decompressed = {
                version: compressedState.v || STATE_VERSION,
                timestamp: compressedState.t || Date.now(),
                league: compressedState.l || 1500,
                settings: self.decompressSettings(compressedState.s),
                teams: {
                    A: self.decompressTeam(compressedState.teams.A, pokemonMapping, moveMapping),
                    B: self.decompressTeam(compressedState.teams.B, pokemonMapping, moveMapping)
                }
            };
            
            return decompressed;
        } catch (error) {
            console.error('MatrixStateManager: Failed to decompress state data:', error);
            return null;
        }
    };
    
    /**
     * Compress settings object
     * @param {Object} settings - Settings object
     * @returns {Object} - Compressed settings
     */
    this.compressSettings = function(settings) {
        if (!settings) {
            return {};
        }
        
        var compressed = {};
        
        // Only include non-default values
        if (settings.matrixMode && settings.matrixMode !== 'battle') {
            compressed.m = settings.matrixMode;
        }
        if (settings.shields && (settings.shields[0] !== 1 || settings.shields[1] !== 1)) {
            compressed.sh = settings.shields;
        }
        if (settings.breakpointMode && settings.breakpointMode !== 'fast') {
            compressed.b = settings.breakpointMode;
        }
        
        return compressed;
    };
    
    /**
     * Decompress settings object
     * @param {Object} compressedSettings - Compressed settings
     * @returns {Object} - Full settings object
     */
    this.decompressSettings = function(compressedSettings) {
        if (!compressedSettings) {
            return {
                matrixMode: 'battle',
                shields: [1, 1],
                breakpointMode: 'fast'
            };
        }
        
        return {
            matrixMode: compressedSettings.m || 'battle',
            shields: compressedSettings.sh || [1, 1],
            breakpointMode: compressedSettings.b || 'fast'
        };
    };
    
    /**
     * Compress team array
     * @param {Array} team - Array of Pokemon objects
     * @param {Object} pokemonMapping - Pokemon ID mapping
     * @param {Object} moveMapping - Move ID mapping
     * @returns {Array} - Compressed team array
     */
    this.compressTeam = function(team, pokemonMapping, moveMapping) {
        if (!Array.isArray(team)) {
            return [];
        }
        
        return team.map(function(pokemon) {
            return self.compressPokemon(pokemon, pokemonMapping, moveMapping);
        }).filter(function(pokemon) {
            return pokemon !== null;
        });
    };
    
    /**
     * Decompress team array
     * @param {Array} compressedTeam - Compressed team array
     * @param {Object} pokemonMapping - Pokemon ID mapping
     * @param {Object} moveMapping - Move ID mapping
     * @returns {Array} - Full team array
     */
    this.decompressTeam = function(compressedTeam, pokemonMapping, moveMapping) {
        if (!Array.isArray(compressedTeam)) {
            return [];
        }
        
        return compressedTeam.map(function(compressedPokemon) {
            return self.decompressPokemon(compressedPokemon, pokemonMapping, moveMapping);
        }).filter(function(pokemon) {
            return pokemon !== null;
        });
    };
    
    /**
     * Compress individual Pokemon object
     * @param {Object} pokemon - Pokemon object
     * @param {Object} pokemonMapping - Pokemon ID mapping
     * @param {Object} moveMapping - Move ID mapping
     * @returns {Object} - Compressed Pokemon object
     */
    this.compressPokemon = function(pokemon, pokemonMapping, moveMapping) {
        if (!pokemon || !pokemon.speciesId) {
            return null;
        }
        
        try {
            var compressed = {};
            
            // Pokemon ID (required)
            var pokemonId = pokemonMapping.pokemonToId[pokemon.speciesId];
            if (!pokemonId) {
                console.warn('MatrixStateManager: Unknown Pokemon species:', pokemon.speciesId);
                return null;
            }
            compressed.id = pokemonId;
            
            // Level (only if not default 20)
            if (pokemon.level && pokemon.level !== 20) {
                compressed.l = pokemon.level;
            }
            
            // IVs (only if not default 15/15/15)
            if (pokemon.ivs && (pokemon.ivs.attack !== 15 || pokemon.ivs.defense !== 15 || pokemon.ivs.hp !== 15)) {
                compressed.i = [pokemon.ivs.attack || 15, pokemon.ivs.defense || 15, pokemon.ivs.hp || 15];
            }
            
            // Moves
            if (pokemon.moves) {
                var moves = [];
                
                // Fast move
                if (pokemon.moves.fastMove) {
                    var fastMoveId = moveMapping.moveToId[pokemon.moves.fastMove];
                    if (fastMoveId) {
                        moves.push(fastMoveId);
                    }
                }
                
                // Charged moves
                if (pokemon.moves.chargedMoves && Array.isArray(pokemon.moves.chargedMoves)) {
                    var chargedMoveIds = pokemon.moves.chargedMoves.map(function(moveName) {
                        return moveMapping.moveToId[moveName];
                    }).filter(function(id) {
                        return id !== undefined;
                    });
                    
                    if (chargedMoveIds.length > 0) {
                        moves.push(chargedMoveIds);
                    }
                }
                
                if (moves.length > 0) {
                    compressed.m = moves;
                }
            }
            
            // Nickname (only if present)
            if (pokemon.nickname) {
                compressed.n = pokemon.nickname;
            }
            
            // Shadow/Purified status (only if true)
            if (pokemon.shadow) {
                compressed.sh = 1;
            }
            if (pokemon.purified) {
                compressed.p = 1;
            }
            
            return compressed;
        } catch (error) {
            console.error('MatrixStateManager: Failed to compress Pokemon:', error);
            return null;
        }
    };
    
    /**
     * Decompress individual Pokemon object
     * @param {Object} compressedPokemon - Compressed Pokemon object
     * @param {Object} pokemonMapping - Pokemon ID mapping
     * @param {Object} moveMapping - Move ID mapping
     * @returns {Object} - Full Pokemon object
     */
    this.decompressPokemon = function(compressedPokemon, pokemonMapping, moveMapping) {
        if (!compressedPokemon || !compressedPokemon.id) {
            return null;
        }
        
        try {
            var pokemon = {};
            
            // Pokemon species
            var speciesId = pokemonMapping.idToPokemon[compressedPokemon.id];
            if (!speciesId) {
                console.warn('MatrixStateManager: Unknown Pokemon ID:', compressedPokemon.id);
                return null;
            }
            pokemon.speciesId = speciesId;
            
            // Level
            pokemon.level = compressedPokemon.l || 20;
            
            // IVs
            if (compressedPokemon.i && Array.isArray(compressedPokemon.i)) {
                pokemon.ivs = {
                    attack: compressedPokemon.i[0] || 15,
                    defense: compressedPokemon.i[1] || 15,
                    hp: compressedPokemon.i[2] || 15
                };
            } else {
                pokemon.ivs = {
                    attack: 15,
                    defense: 15,
                    hp: 15
                };
            }
            
            // Moves
            if (compressedPokemon.m && Array.isArray(compressedPokemon.m)) {
                pokemon.moves = {
                    fastMove: '',
                    chargedMoves: []
                };
                
                // Fast move (first element)
                if (compressedPokemon.m[0] && typeof compressedPokemon.m[0] === 'number') {
                    var fastMoveName = moveMapping.idToMove[compressedPokemon.m[0]];
                    if (fastMoveName) {
                        pokemon.moves.fastMove = fastMoveName;
                    }
                }
                
                // Charged moves (second element, array)
                if (compressedPokemon.m[1] && Array.isArray(compressedPokemon.m[1])) {
                    pokemon.moves.chargedMoves = compressedPokemon.m[1].map(function(moveId) {
                        return moveMapping.idToMove[moveId];
                    }).filter(function(moveName) {
                        return moveName !== undefined;
                    });
                }
            } else {
                pokemon.moves = {
                    fastMove: '',
                    chargedMoves: []
                };
            }
            
            // Nickname
            pokemon.nickname = compressedPokemon.n || '';
            
            // Shadow/Purified status
            pokemon.shadow = !!compressedPokemon.sh;
            pokemon.purified = !!compressedPokemon.p;
            
            return pokemon;
        } catch (error) {
            console.error('MatrixStateManager: Failed to decompress Pokemon:', error);
            return null;
        }
    };
    
    /**
     * Generate shareable URL from matrix state
     * @param {Array} teamA - Team A Pokemon array
     * @param {Array} teamB - Team B Pokemon array
     * @param {Object} settings - Battle settings
     * @returns {string|null} - Shareable URL or null if failed
     */
    this.generateShareableURL = function(teamA, teamB, settings) {
        try {
            // Create matrix state object
            var matrixState = {
                version: STATE_VERSION,
                timestamp: Date.now(),
                league: settings && settings.league ? settings.league : 1500,
                settings: settings || {},
                teams: {
                    A: teamA || [],
                    B: teamB || []
                }
            };
            
            // Compress the state
            var compressedState = self.compressStateData(matrixState);
            if (!compressedState) {
                console.error('MatrixStateManager: Failed to compress state for URL generation');
                return null;
            }
            
            // Convert to JSON and encode
            var jsonString = JSON.stringify(compressedState);
            var base64Data = self.encodeBase64(jsonString);
            
            // Generate URL
            var baseUrl = window.location.origin + window.location.pathname;
            var shareableUrl = baseUrl + '?mode=matrix&data=' + encodeURIComponent(base64Data);
            
            // Check URL length (browser limit ~2000 characters)
            if (shareableUrl.length > 2000) {
                console.warn('MatrixStateManager: Generated URL exceeds recommended length:', shareableUrl.length);
            }
            
            return shareableUrl;
        } catch (error) {
            console.error('MatrixStateManager: Failed to generate shareable URL:', error);
            return null;
        }
    };
    
    /**
     * Parse shared matrix URL and extract state
     * @param {URLSearchParams} urlParams - URL parameters object
     * @returns {Object|null} - Matrix state object or null if failed
     */
    this.parseSharedURL = function(urlParams) {
        try {
            // Check if this is a matrix mode URL
            if (urlParams.get('mode') !== 'matrix') {
                return null;
            }
            
            // Get the data parameter
            var encodedData = urlParams.get('data');
            if (!encodedData) {
                console.warn('MatrixStateManager: No data parameter found in shared URL');
                return null;
            }
            
            // Decode the data
            var decodedData = decodeURIComponent(encodedData);
            var jsonString = self.decodeBase64(decodedData);
            
            if (!jsonString) {
                console.error('MatrixStateManager: Failed to decode Base64 data');
                return null;
            }
            
            // Parse JSON
            var compressedState = JSON.parse(jsonString);
            
            // Decompress the state
            var matrixState = self.decompressStateData(compressedState);
            
            if (!matrixState) {
                console.error('MatrixStateManager: Failed to decompress state data');
                return null;
            }
            
            // Validate the decompressed state
            if (!self.validateMatrixState(matrixState)) {
                console.error('MatrixStateManager: Decompressed state failed validation');
                return null;
            }
            
            return matrixState;
        } catch (error) {
            console.error('MatrixStateManager: Failed to parse shared URL:', error);
            return null;
        }
    };
    
    /**
     * Encode string to Base64 (URL-safe)
     * @param {string} str - String to encode
     * @returns {string} - Base64 encoded string
     */
    this.encodeBase64 = function(str) {
        try {
            // Use browser's btoa function
            var base64 = btoa(unescape(encodeURIComponent(str)));
            
            // Make URL-safe by replacing characters
            return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        } catch (error) {
            console.error('MatrixStateManager: Failed to encode Base64:', error);
            return '';
        }
    };
    
    /**
     * Decode Base64 string (URL-safe)
     * @param {string} base64 - Base64 string to decode
     * @returns {string} - Decoded string
     */
    this.decodeBase64 = function(base64) {
        try {
            // Restore URL-safe characters
            var restored = base64.replace(/-/g, '+').replace(/_/g, '/');
            
            // Add padding if needed
            while (restored.length % 4) {
                restored += '=';
            }
            
            // Use browser's atob function
            return decodeURIComponent(escape(atob(restored)));
        } catch (error) {
            console.error('MatrixStateManager: Failed to decode Base64:', error);
            return '';
        }
    };
    
    // Initialize the manager when created
    self.init();
}