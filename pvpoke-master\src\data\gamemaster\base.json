{"timestamp": "", "settings": {"partySize": 3, "maxBuffStages": 4, "buffDivisor": 4}, "rankingScenarios": [{"slug": "leads", "shields": [1, 1], "energy": [0, 0]}, {"slug": "closers", "shields": [0, 0], "energy": [0, 0]}, {"slug": "switches", "shields": [1, 1], "energy": [4, 0]}, {"slug": "chargers", "shields": [1, 1], "energy": [6, 0]}, {"slug": "attackers", "shields": [0, 1], "energy": [0, 0]}], "cups": [], "formats": [], "pokemonTags": ["legendary", "mythical", "alolan", "gal<PERSON>", "starter", "regional", "shadow", "shadoweligible", "untradeable", "mega", "xs", "<PERSON><PERSON><PERSON>", "ultrabeast"], "pokemonTraits": {"pros": ["bulky", "extremely bulky", "spammy", "agile", "flexible", "dynamic", "fast move pressure", "shield pressure", "defensive", "momentum"], "cons": ["less bulky", "glass cannon", "slow", "clumsy", "inflexible", "low fast move pressure", "low shield pressure", "vulnerable", "volatile", "chaotic", "technical", "inconsistent"]}, "pokemonRegions": [{"string": "gen1", "name": "kanto", "dexStart": 1, "dexEnd": 151}, {"string": "gen2", "name": "johto", "dexStart": 152, "dexEnd": 251}, {"string": "gen3", "name": "hoenn", "dexStart": 252, "dexEnd": 386}, {"string": "gen4", "name": "sinnoh", "dexStart": 387, "dexEnd": 493}, {"string": "gen5", "name": "<PERSON>ova", "dexStart": 494, "dexEnd": 649}, {"string": "gen6", "name": "kalos", "dexStart": 650, "dexEnd": 721}, {"string": "gen7", "name": "alola", "dexStart": 722, "dexEnd": 807}, {"string": "gen8", "name": "galar", "dexStart": 808, "dexEnd": 898}, {"string": "hisui", "name": "hisui", "dexStart": 899, "dexEnd": 905}, {"string": "gen9", "name": "paldea", "dexStart": 906, "dexEnd": 1008}], "shadowPokemon": ["bulbasaur", "ivysaur", "venusaur", "venusaur_mega", "charmander", "charmeleon", "charizard", "charizard_mega_x", "charizard_mega_y", "squirtle", "wartortle", "blastoise", "weedle", "kakuna", "beedrill", "beedrill_mega", "rattata", "raticate", "zubat", "golbat", "oddish", "gloom", "vileplume", "venonat", "venomoth", "meowth", "persian", "psyduck", "golduck", "growlithe", "arcanine", "poliwag", "poliwhirl", "poliwrath", "abra", "kadabra", "<PERSON><PERSON><PERSON>", "alakazam_mega", "grimer", "muk", "drowzee", "hypno", "cubone", "marowak", "hitmonchan", "scyther", "electabuzz", "magmar", "magi<PERSON><PERSON>", "gyarados", "gyarados_mega", "<PERSON>ras", "snorlax", "dratini", "dragonair", "dragonite", "crobat", "mareep", "flaaffy", "ampha<PERSON>", "ampharos_mega", "bellossom", "politoed", "s<PERSON><PERSON>", "scizor_mega", "houndour", "houndoom", "houndoom_mega", "<PERSON>r<PERSON><PERSON>", "pupitar", "tyranitar", "tyranitar_mega", "mudkip", "marshtomp", "swampert", "swampert_mega", "seedot", "nuzleaf", "shiftry", "ralts", "kirlia", "gardevoir", "gardevoir_mega", "trapinch", "vibrava", "flygon", "shuppet", "banette", "banette_mega", "duskull", "dusclops", "turtwig", "grotle", "torterra", "electivire", "magmortar", "gallade", "gallade_mega", "dusknoir", "magnemite", "magneton", "magnezone", "bellsprout", "weepinbell", "victreebel", "sandshrew", "sandslash", "porygon", "porygon2", "porygon_z", "wobbuffet", "meowth", "persian", "<PERSON><PERSON><PERSON>", "articuno", "sneasel", "weavile", "sableye", "sableye_mega", "zapdos", "moltres", "delibird", "stantler", "absol", "bagon", "shelgon", "salamence", "salamence_mega", "snover", "abomasnow", "abomasnow_mega", "rai<PERSON>u", "vulpix", "ninetales", "exeggcute", "exeggutor", "omanyte", "omastar", "misdreavus", "mismagius", "<PERSON>van<PERSON>", "sharpedo", "sharpedo_mega", "pinsir", "pinsir_mega", "ma<PERSON>le", "mawile_mega", "beldum", "metang", "metagross", "metagross_mega", "entei", "ekans", "arbok", "koffing", "weezing", "nidoran_female", "nidorina", "nidoran_male", "<PERSON><PERSON><PERSON>", "nidoqueen", "nidoking", "machop", "machoke", "machamp", "gligar", "gliscor", "shuckle", "stunky", "skuntank", "suicune", "pineco", "forretress", "mewtwo", "mewtwo_mega_x", "mewtwo_mega_y", "<PERSON><PERSON>", "<PERSON><PERSON>o", "shellder", "cloyster", "slowpoke", "slowbro", "slowking", "aerodactyl", "<PERSON><PERSON><PERSON><PERSON>", "te<PERSON><PERSON><PERSON>", "ursaring", "hoppip", "skip<PERSON>", "<PERSON><PERSON><PERSON>", "wooper", "quagsire", "swin<PERSON>", "piloswine", "ma<PERSON><PERSON>", "nosepass", "probopass", "aron", "<PERSON>on", "aggron", "spheal", "sealeo", "walrein", "lileep", "cradily", "anorith", "arm<PERSON>", "aipom", "ambipom", "s<PERSON><PERSON><PERSON>", "drapion", "poochyena", "<PERSON><PERSON>", "murkrow", "honch<PERSON><PERSON>", "electrike", "manectric", "snubbull", "gran<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "starly", "staravia", "staraptor", "tangela", "tangrowth", "<PERSON>a", "seadra", "king<PERSON>", "ho_oh", "bidoof", "bibarel", "cacnea", "cacturne", "lugia", "voltorb", "electrode", "chikorita", "bayleef", "meganium", "cynda<PERSON><PERSON>", "quilava", "typhlosion", "totodile", "cro<PERSON><PERSON>", "feraligatr", "whismur", "loudred", "exploud", "hippopotas", "hippo<PERSON><PERSON>", "rattata_alolan", "raticate_alolan", "sandshrew_alolan", "sandslash_alolan", "exeggutor_alolan", "sudowoodo", "girafarig", "numel", "camerupt", "latias", "exeggutor_alolan", "marowak_alolan", "geodude", "graveler", "golem", "shinx", "luxio", "luxray", "purrloin", "lie<PERSON>", "latios", "foongus", "amoon<PERSON>s", "patrat", "watchog", "<PERSON><PERSON>", "swanna", "u<PERSON><PERSON><PERSON>", "diglett_alolan", "dug<PERSON><PERSON>_alolan", "onix", "steelix", "natu", "xatu", "wailmer", "wailord", "go<PERSON>", "golurk", "vulpix_alolan", "ninetales_alolan", "spoink", "grumpig", "blitzle", "zebstrika", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "registeel", "salamence_mega", "aggron_mega", "manectric_mega", "gardevoir_mega", "latios_mega", "latias_mega", "grimer_alolan", "muk_alolan", "phanpy", "<PERSON><PERSON><PERSON>", "treecko", "g<PERSON><PERSON>", "sceptile", "sceptile_mega", "torchic", "combusken", "blaziken", "blaziken_mega", "drifloon", "drifb<PERSON>", "regice", "regirock", "throh", "sawk", "geodude_alolan", "graveler_alolan", "golem_alolan", "ledyba", "<PERSON><PERSON>", "hitmontop", "glam<PERSON>w", "purugly", "gible", "gabite", "garcho<PERSON>", "regigigas", "gastly", "haunter", "gengar", "rhyhorn", "rhydon", "rhyperior", "barboach", "whiscash", "cranidos", "rampardos", "shieldon", "bastiodon", "drilbur", "excadrill", "litwick", "lampent", "chandelure", "snea<PERSON>_his<PERSON>an", "sneasler", "kyogre", "corphish", "crawdaunt", "snorunt", "glalie", "froslass", "chim<PERSON>r", "monferno", "infernape", "pip<PERSON>p", "prin<PERSON><PERSON><PERSON>", "empoleon", "croagunk", "toxicroak", "dwebble", "crustle", "ferroseed", "ferrothorn", "tentacool", "tentacruel", "grou<PERSON>", "pidgey", "pidgeotto", "pidgeot", "darum<PERSON>", "darmanitan_standard", "gothita", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "solosis", "duosion", "reuniclus", "pidove", "tranquill", "unfezant", "cresselia", "archen", "archeops", "t<PERSON><PERSON><PERSON>", "carracosta", "timburr", "gur<PERSON><PERSON>", "con<PERSON><PERSON><PERSON>", "weezing_galarian", "heatran", "mankey", "primeape", "annihilape", "caterpie", "metapod", "butterfree", "rog<PERSON><PERSON><PERSON>", "boldore", "gigalith", "venipede", "whirlipede", "scolipede", "karrablast", "escavalier", "shelmet", "accelgor", "zigzagoon_galarian", "linoone_galarian", "obstagoon", "palkia", "taillow", "swellow", "snivy", "<PERSON><PERSON>e", "serperior", "tepig", "pignite", "emboar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "samu<PERSON>t", "trubbish", "garbodor", "bunnelby", "diggersby", "slakoth", "vigoroth", "slaking", "dialga", "<PERSON>ay", "malamar", "tyrunt", "tyrantrum", "amaura", "aurorus"], "pokemon": [], "moves": []}