<!DOCTYPE html>
<html>
<head>
    <title>Matrix State UI Test</title>
    <style>
        /* Matrix State UI Indicators */
        .battle-results.matrix .matrix-state-indicator {
          background: #f9fdff;
          border: 1px solid #c4def5;
          border-radius: 8px;
          padding: 10px 15px;
          margin: 10px 0;
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 14px;
        }

        .battle-results.matrix .matrix-state-indicator .saved-state-icon {
          font-size: 16px;
        }

        .battle-results.matrix .matrix-state-indicator .saved-state-text {
          flex: 1;
          color: #003462;
          font-weight: bold;
        }

        .battle-results.matrix .matrix-state-indicator .clear-state-btn {
          background: #ff4444;
          color: #fff;
          border: none;
          border-radius: 4px;
          padding: 5px 10px;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .battle-results.matrix .matrix-state-indicator .clear-state-btn:hover {
          background: #cc0000;
        }

        .battle-results.matrix .matrix-share-container {
          background: #f9fdff;
          border: 1px solid #c4def5;
          border-radius: 8px;
          padding: 15px;
          margin: 10px 0;
        }

        .battle-results.matrix .matrix-share-container p {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #003462;
          font-weight: bold;
        }

        .battle-results.matrix .matrix-share-container .share-link {
          display: flex;
          gap: 5px;
        }

        .battle-results.matrix .matrix-share-container .share-link input {
          flex: 1;
          padding: 8px 10px;
          border: 1px solid #ccc;
          border-radius: 4px;
          font-size: 12px;
          background: #fff;
        }

        .battle-results.matrix .matrix-share-container .share-link .copy {
          background: #1073d3;
          color: #fff;
          border: none;
          border-radius: 4px;
          padding: 8px 15px;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          white-space: nowrap;
        }

        .battle-results.matrix .matrix-share-container .share-link .copy:hover {
          background: #0a5aa3;
        }
    </style>
</head>
<body>
    <h1>Matrix State UI Test</h1>
    
    <div class="battle-results matrix">
        <div class="matrix-state-indicator">
            <span class="saved-state-icon">💾</span>
            <span class="saved-state-text">Saved matrix loaded</span>
            <div class="clear-state-btn">Clear</div>
        </div>

        <div class="matrix-share-container">
            <p>Share this matrix setup:</p>
            <div class="share-link">
                <input type="text" readonly value="https://pvpoke.com/battle/?mode=matrix&data=eyJ2IjoiMS4wIiwibCI6MTUwMCwidCI6eyJBIjpbeyJpZCI6NiwiaSI6WzAsMTUsMTRdLCJtIjpbMjQsWzExLDNdXX1dLCJCIjpbeyJpZCI6MTUwfV19fQ">
                <div class="copy">Copy</div>
            </div>
        </div>
    </div>

    <script>
        // Test the copy functionality
        document.querySelector('.copy').addEventListener('click', function() {
            var input = document.querySelector('.share-link input');
            input.select();
            document.execCommand('copy');
            
            var btn = this;
            var originalText = btn.textContent;
            btn.textContent = 'Copied!';
            setTimeout(function() {
                btn.textContent = originalText;
            }, 2000);
        });

        // Test the clear functionality
        document.querySelector('.clear-state-btn').addEventListener('click', function() {
            if(confirm('Clear saved matrix state? This will remove your saved Pokemon selections.')) {
                document.querySelector('.matrix-state-indicator').style.display = 'none';
                alert('Matrix state cleared (test mode)');
            }
        });
    </script>
</body>
</html>