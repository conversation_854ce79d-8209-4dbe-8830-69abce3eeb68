{"name": "tundra", "title": "<PERSON><PERSON>", "include": [{"filterType": "type", "name": "Type", "values": ["bug", "dark", "ice", "normal", "poison"]}, {"filterType": "id", "name": "Species", "values": ["goodra", "politoed", "politoed_shadow", "gyarados", "gyarado<PERSON>_shadow", "<PERSON><PERSON>", "<PERSON><PERSON>_shadow", "arm<PERSON>", "<PERSON><PERSON>_<PERSON>", "ninjask", "vespiquen", "scyther", "scyther_shadow", "relicanth", "tangrowth", "tangrowth_shadow", "sneasler"]}], "exclude": [{"filterType": "tag", "name": "Tag", "values": ["mega"]}, {"filterType": "id", "name": "Species", "values": ["obstagoon", "ursaring", "arctibax", "araquanid", "tyranitar", "dwebble", "toxicroak", "scrafty", "vigoroth", "lokix", "miltank", "greedent", "dubwool", "munchlax", "oinkologne", "oinkologne_female", "castform", "aurorus", "amaura", "crustle", "buzzwole", "heracross", "dunsparce", "toxapex", "cetoddle"]}, {"filterType": "type", "name": "Type", "values": ["electric", "fairy", "fire", "flying", "ground", "rock", "steel"]}], "overrides": [], "league": 1500, "useDefaultMovesets": 1, "levelCap": 50}