[{"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 713, "matchups": [{"opponent": "zacian_hero", "rating": 739}, {"opponent": "metagross", "rating": 734}, {"opponent": "mewtwo", "rating": 658}, {"opponent": "dialga", "rating": 619}, {"opponent": "gyarados", "rating": 575}], "counters": [{"opponent": "zekrom", "rating": 315}, {"opponent": "lugia", "rating": 359}, {"opponent": "giratina_origin", "rating": 426}, {"opponent": "garcho<PERSON>", "rating": 478}, {"opponent": "swampert", "rating": 495}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 18442}, {"moveId": "EXTRASENSORY", "uses": 5400}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3986}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3869}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3744}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3580}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3294}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3289}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3251}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3075}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3033}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2979}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2852}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2835}, {"moveId": "STEEL_WING", "uses": 2685}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2551}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2481}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2372}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2315}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 32098}, {"moveId": "SACRED_FIRE", "uses": 20406}, {"moveId": "EARTHQUAKE", "uses": 12863}, {"moveId": "SOLAR_BEAM", "uses": 6980}, {"moveId": "FIRE_BLAST", "uses": 4143}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 100}, {"speciesId": "x<PERSON><PERSON>", "speciesName": "Xerneas", "rating": 710, "matchups": [{"opponent": "dragonite", "rating": 858}, {"opponent": "garcho<PERSON>", "rating": 687}, {"opponent": "gyarados", "rating": 652}, {"opponent": "zacian_hero", "rating": 623}, {"opponent": "dialga", "rating": 566}], "counters": [{"opponent": "metagross", "rating": 299}, {"opponent": "ho_oh", "rating": 299}, {"opponent": "giratina_origin", "rating": 418}, {"opponent": "grou<PERSON>", "rating": 459}, {"opponent": "mewtwo", "rating": 466}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 68295}, {"moveId": "ZEN_HEADBUTT", "uses": 8205}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27209}, {"moveId": "MOONBLAST", "uses": 19191}, {"moveId": "MEGAHORN", "uses": 14039}, {"moveId": "THUNDER", "uses": 10857}, {"moveId": "GIGA_IMPACT", "uses": 5375}]}, "moveset": ["TACKLE", "CLOSE_COMBAT", "MOONBLAST"], "score": 100}, {"speciesId": "dragonite_shadow", "speciesName": "Dragonite (Shadow)", "rating": 722, "matchups": [{"opponent": "garcho<PERSON>", "rating": 962}, {"opponent": "excadrill", "rating": 646}, {"opponent": "dialga", "rating": 640}, {"opponent": "giratina_origin", "rating": 569}, {"opponent": "mewtwo", "rating": 534}], "counters": [{"opponent": "zacian_hero", "rating": 225}, {"opponent": "lugia", "rating": 326}, {"opponent": "metagross", "rating": 369}, {"opponent": "ho_oh", "rating": 432}, {"opponent": "gyarados", "rating": 492}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33035}, {"moveId": "DRAGON_BREATH", "uses": 32994}, {"moveId": "STEEL_WING", "uses": 10564}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 24342}, {"moveId": "SUPER_POWER", "uses": 20041}, {"moveId": "HURRICANE", "uses": 11512}, {"moveId": "OUTRAGE", "uses": 6824}, {"moveId": "HYPER_BEAM", "uses": 5145}, {"moveId": "DRAGON_PULSE", "uses": 4493}, {"moveId": "DRACO_METEOR", "uses": 4071}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 99.5}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 716, "matchups": [{"opponent": "metagross", "rating": 902}, {"opponent": "mewtwo", "rating": 733}, {"opponent": "zacian_hero", "rating": 649}, {"opponent": "lugia", "rating": 627}, {"opponent": "excadrill", "rating": 585}], "counters": [{"opponent": "dialga", "rating": 375}, {"opponent": "garcho<PERSON>", "rating": 396}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 410}, {"opponent": "dragonite", "rating": 425}, {"opponent": "gyarados", "rating": 443}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 43334}, {"moveId": "DRAGON_TAIL", "uses": 33166}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 36703}, {"moveId": "DRAGON_PULSE", "uses": 21108}, {"moveId": "OMINOUS_WIND", "uses": 18680}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "OMINOUS_WIND"], "score": 97.6}, {"speciesId": "reshiram", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 735, "matchups": [{"opponent": "metagross", "rating": 741}, {"opponent": "zacian_hero", "rating": 657}, {"opponent": "giratina_origin", "rating": 652}, {"opponent": "mewtwo", "rating": 646}, {"opponent": "dialga", "rating": 519}], "counters": [{"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "grou<PERSON>", "rating": 323}, {"opponent": "lugia", "rating": 347}, {"opponent": "swampert", "rating": 398}, {"opponent": "dragonite", "rating": 462}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48187}, {"moveId": "FIRE_FANG", "uses": 28313}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24066}, {"moveId": "OVERHEAT", "uses": 19654}, {"moveId": "STONE_EDGE", "uses": 19315}, {"moveId": "DRACO_METEOR", "uses": 13360}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "OVERHEAT"], "score": 97.2}, {"speciesId": "zekrom", "speciesName": "Zekrom", "rating": 746, "matchups": [{"opponent": "gyarados", "rating": 720}, {"opponent": "zacian_hero", "rating": 657}, {"opponent": "giratina_origin", "rating": 652}, {"opponent": "mewtwo", "rating": 646}, {"opponent": "dragonite", "rating": 505}], "counters": [{"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "excadrill", "rating": 409}, {"opponent": "dialga", "rating": 432}, {"opponent": "metagross", "rating": 447}, {"opponent": "lugia", "rating": 459}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47051}, {"moveId": "CHARGE_BEAM", "uses": 29449}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34904}, {"moveId": "CRUNCH", "uses": 18870}, {"moveId": "OUTRAGE", "uses": 16900}, {"moveId": "FLASH_CANNON", "uses": 5844}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "WILD_CHARGE"], "score": 96.9}, {"speciesId": "grou<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 694, "matchups": [{"opponent": "metagross", "rating": 869}, {"opponent": "dialga", "rating": 815}, {"opponent": "excadrill", "rating": 752}, {"opponent": "zekrom", "rating": 676}, {"opponent": "zacian_hero", "rating": 657}], "counters": [{"opponent": "gyarados", "rating": 226}, {"opponent": "giratina_origin", "rating": 440}, {"opponent": "swampert", "rating": 440}, {"opponent": "mewtwo", "rating": 458}, {"opponent": "garcho<PERSON>", "rating": 488}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43212}, {"moveId": "DRAGON_TAIL", "uses": 33288}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 29652}, {"moveId": "FIRE_PUNCH", "uses": 28460}, {"moveId": "SOLAR_BEAM", "uses": 12757}, {"moveId": "FIRE_BLAST", "uses": 5744}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "FIRE_PUNCH"], "score": 96.3}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 719, "matchups": [{"opponent": "metagross", "rating": 770}, {"opponent": "mewtwo", "rating": 697}, {"opponent": "zacian_hero", "rating": 622}, {"opponent": "excadrill", "rating": 606}, {"opponent": "lugia", "rating": 557}], "counters": [{"opponent": "giratina_origin", "rating": 354}, {"opponent": "garcho<PERSON>", "rating": 396}, {"opponent": "dragonite", "rating": 441}, {"opponent": "dialga", "rating": 461}, {"opponent": "gyarados", "rating": 461}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16049}, {"moveId": "EXTRASENSORY", "uses": 5282}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4065}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4057}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3958}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3703}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3491}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3440}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3424}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3239}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3165}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3097}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3031}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2951}, {"moveId": "STEEL_WING", "uses": 2867}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2801}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2701}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2627}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2489}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 29069}, {"moveId": "SACRED_FIRE", "uses": 18811}, {"moveId": "EARTHQUAKE", "uses": 11936}, {"moveId": "RETURN", "uses": 6434}, {"moveId": "SOLAR_BEAM", "uses": 6365}, {"moveId": "FIRE_BLAST", "uses": 3749}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 96.1}, {"speciesId": "lugia", "speciesName": "Lugia", "rating": 700, "matchups": [{"opponent": "grou<PERSON>", "rating": 816}, {"opponent": "garcho<PERSON>", "rating": 738}, {"opponent": "gyarados", "rating": 671}, {"opponent": "dragonite", "rating": 652}, {"opponent": "zacian_hero", "rating": 569}], "counters": [{"opponent": "mewtwo", "rating": 372}, {"opponent": "giratina_origin", "rating": 372}, {"opponent": "metagross", "rating": 395}, {"opponent": "excadrill", "rating": 465}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42190}, {"moveId": "EXTRASENSORY", "uses": 34310}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26341}, {"moveId": "AEROBLAST", "uses": 19555}, {"moveId": "FUTURE_SIGHT", "uses": 13644}, {"moveId": "HYDRO_PUMP", "uses": 8801}, {"moveId": "RETURN", "uses": 8237}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 96.1}, {"speciesId": "yveltal", "speciesName": "Y<PERSON><PERSON>", "rating": 683, "matchups": [{"opponent": "mewtwo", "rating": 808}, {"opponent": "giratina_origin", "rating": 755}, {"opponent": "lugia", "rating": 696}, {"opponent": "dialga", "rating": 611}, {"opponent": "metagross", "rating": 563}], "counters": [{"opponent": "zekrom", "rating": 269}, {"opponent": "dragonite", "rating": 313}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "ho_oh", "rating": 416}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27743}, {"moveId": "GUST", "uses": 25682}, {"moveId": "SUCKER_PUNCH", "uses": 23029}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27443}, {"moveId": "HURRICANE", "uses": 15682}, {"moveId": "FOCUS_BLAST", "uses": 13286}, {"moveId": "PSYCHIC", "uses": 12873}, {"moveId": "HYPER_BEAM", "uses": 7254}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 96.1}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 656, "matchups": [{"opponent": "garcho<PERSON>", "rating": 930}, {"opponent": "dialga", "rating": 723}, {"opponent": "dragonite", "rating": 616}, {"opponent": "lugia", "rating": 527}, {"opponent": "giratina_origin", "rating": 506}], "counters": [{"opponent": "metagross", "rating": 247}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "swampert", "rating": 447}, {"opponent": "mewtwo", "rating": 458}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52494}, {"moveId": "MUD_SLAP", "uses": 24006}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38960}, {"moveId": "STONE_EDGE", "uses": 13287}, {"moveId": "BULLDOZE", "uses": 12156}, {"moveId": "ANCIENT_POWER", "uses": 12034}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 95.2}, {"speciesId": "mewtwo_shadow", "speciesName": "<PERSON><PERSON>t<PERSON> (Shadow)", "rating": 749, "matchups": [{"opponent": "metagross", "rating": 854}, {"opponent": "excadrill", "rating": 627}, {"opponent": "lugia", "rating": 546}, {"opponent": "zacian_hero", "rating": 541}, {"opponent": "garcho<PERSON>", "rating": 518}], "counters": [{"opponent": "giratina_origin", "rating": 322}, {"opponent": "dialga", "rating": 361}, {"opponent": "swampert", "rating": 460}, {"opponent": "dragonite", "rating": 465}, {"opponent": "gyarados", "rating": 471}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 47774}, {"moveId": "CONFUSION", "uses": 28726}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19751}, {"moveId": "SHADOW_BALL", "uses": 11890}, {"moveId": "ICE_BEAM", "uses": 11522}, {"moveId": "FLAMETHROWER", "uses": 8762}, {"moveId": "THUNDERBOLT", "uses": 8481}, {"moveId": "FOCUS_BLAST", "uses": 7598}, {"moveId": "PSYCHIC", "uses": 4611}, {"moveId": "HYPER_BEAM", "uses": 3740}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 95}, {"speciesId": "land<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 705, "matchups": [{"opponent": "excadrill", "rating": 788}, {"opponent": "zacian_hero", "rating": 557}, {"opponent": "dragonite", "rating": 535, "opRating": 464}, {"opponent": "gyarados", "rating": 527, "opRating": 472}, {"opponent": "dialga", "rating": 516}], "counters": [{"opponent": "mewtwo", "rating": 367}, {"opponent": "garcho<PERSON>", "rating": 455}, {"opponent": "giratina_origin", "rating": 494}, {"opponent": "metagross", "rating": 497}, {"opponent": "lugia", "rating": 497}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51698}, {"moveId": "EXTRASENSORY", "uses": 24802}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24335}, {"moveId": "STONE_EDGE", "uses": 22033}, {"moveId": "EARTHQUAKE", "uses": 18662}, {"moveId": "BULLDOZE", "uses": 11395}]}, "moveset": ["MUD_SHOT", "SUPER_POWER", "STONE_EDGE"], "score": 94.4}, {"speciesId": "dialga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 707, "matchups": [{"opponent": "gyarados", "rating": 682}, {"opponent": "mewtwo", "rating": 663}, {"opponent": "giratina_origin", "rating": 625}, {"opponent": "dragonite", "rating": 519}, {"opponent": "lugia", "rating": 508}], "counters": [{"opponent": "swampert", "rating": 223}, {"opponent": "metagross", "rating": 293}, {"opponent": "excadrill", "rating": 355}, {"opponent": "zacian_hero", "rating": 439}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 477}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52053}, {"moveId": "METAL_CLAW", "uses": 24447}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 28912}, {"moveId": "DRACO_METEOR", "uses": 25257}, {"moveId": "THUNDER", "uses": 22432}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 94.1}, {"speciesId": "mewtwo", "speciesName": "Mewtwo", "rating": 714, "matchups": [{"opponent": "excadrill", "rating": 671}, {"opponent": "gyarados", "rating": 651}, {"opponent": "lugia", "rating": 627}, {"opponent": "zacian_hero", "rating": 619}, {"opponent": "garcho<PERSON>", "rating": 580}], "counters": [{"opponent": "giratina_origin", "rating": 266}, {"opponent": "ho_oh", "rating": 302}, {"opponent": "dialga", "rating": 336}, {"opponent": "zekrom", "rating": 353}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 46718}, {"moveId": "CONFUSION", "uses": 29782}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19056}, {"moveId": "SHADOW_BALL", "uses": 11491}, {"moveId": "ICE_BEAM", "uses": 11101}, {"moveId": "FLAMETHROWER", "uses": 8440}, {"moveId": "THUNDERBOLT", "uses": 8121}, {"moveId": "FOCUS_BLAST", "uses": 7342}, {"moveId": "RETURN", "uses": 4639}, {"moveId": "PSYCHIC", "uses": 4399}, {"moveId": "HYPER_BEAM", "uses": 1742}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 94.1}, {"speciesId": "zacian_hero", "speciesName": "<PERSON><PERSON><PERSON> (Hero)", "rating": 701, "matchups": [{"opponent": "dragonite", "rating": 794}, {"opponent": "excadrill", "rating": 760}, {"opponent": "garcho<PERSON>", "rating": 667}, {"opponent": "gyarados", "rating": 606}, {"opponent": "dialga", "rating": 560}], "counters": [{"opponent": "metagross", "rating": 270}, {"opponent": "giratina_origin", "rating": 350}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "swampert", "rating": 380}, {"opponent": "lugia", "rating": 430}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27378}, {"moveId": "QUICK_ATTACK", "uses": 26439}, {"moveId": "FIRE_FANG", "uses": 12505}, {"moveId": "METAL_CLAW", "uses": 10190}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27797}, {"moveId": "WILD_CHARGE", "uses": 27130}, {"moveId": "PLAY_ROUGH", "uses": 12798}, {"moveId": "IRON_HEAD", "uses": 8946}]}, "moveset": ["QUICK_ATTACK", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 94.1}, {"speciesId": "lugia_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 700, "matchups": [{"opponent": "grou<PERSON>", "rating": 788}, {"opponent": "garcho<PERSON>", "rating": 692}, {"opponent": "dragonite", "rating": 673}, {"opponent": "gyarados", "rating": 645}, {"opponent": "zacian_hero", "rating": 523}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "zekrom", "rating": 355}, {"opponent": "metagross", "rating": 424}, {"opponent": "giratina_origin", "rating": 450}, {"opponent": "mewtwo", "rating": 453}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42570}, {"moveId": "EXTRASENSORY", "uses": 33930}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 29434}, {"moveId": "AEROBLAST", "uses": 21932}, {"moveId": "FUTURE_SIGHT", "uses": 15162}, {"moveId": "HYDRO_PUMP", "uses": 9791}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 93.9}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 705, "matchups": [{"opponent": "zekrom", "rating": 753}, {"opponent": "excadrill", "rating": 751}, {"opponent": "metagross", "rating": 610}, {"opponent": "giratina_origin", "rating": 603}, {"opponent": "dragonite", "rating": 572}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 179}, {"opponent": "lugia", "rating": 261}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "gyarados", "rating": 365}, {"opponent": "mewtwo", "rating": 419}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40134}, {"moveId": "DRAGON_TAIL", "uses": 36366}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 24680}, {"moveId": "EARTH_POWER", "uses": 22590}, {"moveId": "FIRE_BLAST", "uses": 9840}, {"moveId": "EARTHQUAKE", "uses": 9668}, {"moveId": "SAND_TOMB", "uses": 9484}]}, "moveset": ["MUD_SHOT", "OUTRAGE", "EARTH_POWER"], "score": 93.1}, {"speciesId": "meloetta_aria", "speciesName": "<PERSON><PERSON><PERSON> (Aria)", "rating": 690, "matchups": [{"opponent": "dragonite", "rating": 706}, {"opponent": "zacian_hero", "rating": 684}, {"opponent": "giratina_origin", "rating": 651}, {"opponent": "mewtwo", "rating": 641}, {"opponent": "gyarados", "rating": 621}], "counters": [{"opponent": "metagross", "rating": 273}, {"opponent": "lugia", "rating": 292}, {"opponent": "dialga", "rating": 317}, {"opponent": "ho_oh", "rating": 447}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 41455}, {"moveId": "CONFUSION", "uses": 35045}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30116}, {"moveId": "THUNDERBOLT", "uses": 19607}, {"moveId": "HYPER_BEAM", "uses": 13752}, {"moveId": "DAZZLING_GLEAM", "uses": 13214}]}, "moveset": ["QUICK_ATTACK", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 92.9}, {"speciesId": "florges", "speciesName": "Florges", "rating": 662, "matchups": [{"opponent": "dragonite", "rating": 824}, {"opponent": "gyarados", "rating": 702}, {"opponent": "garcho<PERSON>", "rating": 687}, {"opponent": "giratina_origin", "rating": 604}, {"opponent": "dialga", "rating": 535}], "counters": [{"opponent": "metagross", "rating": 188}, {"opponent": "grou<PERSON>", "rating": 317}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "excadrill", "rating": 406}, {"opponent": "zacian_hero", "rating": 482}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 28032}, {"moveId": "VINE_WHIP", "uses": 22299}, {"moveId": "TACKLE", "uses": 15666}, {"moveId": "RAZOR_LEAF", "uses": 10508}], "chargedMoves": [{"moveId": "DISARMING_VOICE", "uses": 34859}, {"moveId": "PSYCHIC", "uses": 15468}, {"moveId": "MOONBLAST", "uses": 14208}, {"moveId": "PETAL_BLIZZARD", "uses": 11983}]}, "moveset": ["FAIRY_WIND", "DISARMING_VOICE", "MOONBLAST"], "score": 92.1}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 684, "matchups": [{"opponent": "zacian_hero", "rating": 729}, {"opponent": "dialga", "rating": 706}, {"opponent": "lugia", "rating": 604}, {"opponent": "excadrill", "rating": 546}, {"opponent": "mewtwo", "rating": 537}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "swampert", "rating": 286}, {"opponent": "gyarados", "rating": 314}, {"opponent": "garcho<PERSON>", "rating": 389}, {"opponent": "dragonite", "rating": 489}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 67170}, {"moveId": "ZEN_HEADBUTT", "uses": 9330}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 29570}, {"moveId": "PSYCHIC", "uses": 17033}, {"moveId": "EARTHQUAKE", "uses": 16341}, {"moveId": "RETURN", "uses": 8923}, {"moveId": "FLASH_CANNON", "uses": 4761}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 91.8}, {"speciesId": "genesect_chill", "speciesName": "Genesect (Chill)", "rating": 676, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 772, "opRating": 227}, {"opponent": "mewtwo", "rating": 708}, {"opponent": "dragonite", "rating": 639}, {"opponent": "garcho<PERSON>", "rating": 623}, {"opponent": "gyarados", "rating": 506}], "counters": [{"opponent": "dialga", "rating": 372}, {"opponent": "giratina_origin", "rating": 420}, {"opponent": "lugia", "rating": 438}, {"opponent": "zacian_hero", "rating": 482}, {"opponent": "metagross", "rating": 497}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51997}, {"moveId": "METAL_CLAW", "uses": 24503}], "chargedMoves": [{"moveId": "TECHNO_BLAST_CHILL", "uses": 26658}, {"moveId": "X_SCISSOR", "uses": 22696}, {"moveId": "MAGNET_BOMB", "uses": 19744}, {"moveId": "ICE_BEAM", "uses": 7546}]}, "moveset": ["FURY_CUTTER", "TECHNO_BLAST_CHILL", "X_SCISSOR"], "score": 91}, {"speciesId": "dragonite", "speciesName": "Dragonite", "rating": 696, "matchups": [{"opponent": "grou<PERSON>", "rating": 776}, {"opponent": "excadrill", "rating": 699}, {"opponent": "giratina_origin", "rating": 574}, {"opponent": "gyarados", "rating": 523}, {"opponent": "metagross", "rating": 510}], "counters": [{"opponent": "zacian_hero", "rating": 205}, {"opponent": "lugia", "rating": 347}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "garcho<PERSON>", "rating": 427}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33156}, {"moveId": "DRAGON_TAIL", "uses": 32150}, {"moveId": "STEEL_WING", "uses": 11171}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 23274}, {"moveId": "SUPER_POWER", "uses": 19256}, {"moveId": "HURRICANE", "uses": 10863}, {"moveId": "OUTRAGE", "uses": 6407}, {"moveId": "RETURN", "uses": 6269}, {"moveId": "DRAGON_PULSE", "uses": 4339}, {"moveId": "DRACO_METEOR", "uses": 3769}, {"moveId": "HYPER_BEAM", "uses": 2452}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 90.7}, {"speciesId": "zarude", "speciesName": "Zarude", "rating": 649, "matchups": [{"opponent": "mewtwo", "rating": 812}, {"opponent": "giratina_origin", "rating": 752}, {"opponent": "gyarados", "rating": 682}, {"opponent": "metagross", "rating": 588}, {"opponent": "garcho<PERSON>", "rating": 562}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 238}, {"opponent": "dialga", "rating": 252}, {"opponent": "dragonite", "rating": 271}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "lugia", "rating": 438}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 49490}, {"moveId": "BITE", "uses": 27010}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37160}, {"moveId": "POWER_WHIP", "uses": 29005}, {"moveId": "ENERGY_BALL", "uses": 10568}]}, "moveset": ["VINE_WHIP", "DARK_PULSE", "POWER_WHIP"], "score": 89.9}, {"speciesId": "raikou_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 711, "matchups": [{"opponent": "lugia", "rating": 720}, {"opponent": "gyarados", "rating": 706}, {"opponent": "metagross", "rating": 688}, {"opponent": "zacian_hero", "rating": 569}, {"opponent": "mewtwo", "rating": 564}], "counters": [{"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "dialga", "rating": 350}, {"opponent": "swampert", "rating": 363}, {"opponent": "giratina_origin", "rating": 424}, {"opponent": "dragonite", "rating": 449}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41933}, {"moveId": "THUNDER_SHOCK", "uses": 34567}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 40215}, {"moveId": "SHADOW_BALL", "uses": 22542}, {"moveId": "THUNDERBOLT", "uses": 7196}, {"moveId": "THUNDER", "uses": 6356}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 89.4}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 654, "matchups": [{"opponent": "excadrill", "rating": 785}, {"opponent": "zacian_hero", "rating": 562}, {"opponent": "gyarados", "rating": 554}, {"opponent": "metagross", "rating": 535}, {"opponent": "dialga", "rating": 508}], "counters": [{"opponent": "swampert", "rating": 343}, {"opponent": "lugia", "rating": 350}, {"opponent": "mewtwo", "rating": 361}, {"opponent": "ho_oh", "rating": 398}, {"opponent": "giratina_origin", "rating": 486}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51837}, {"moveId": "ROCK_THROW", "uses": 24663}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 25405}, {"moveId": "EARTH_POWER", "uses": 24578}, {"moveId": "OUTRAGE", "uses": 15209}, {"moveId": "FOCUS_BLAST", "uses": 11274}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTH_POWER"], "score": 89.3}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 677, "matchups": [{"opponent": "gyarados", "rating": 807}, {"opponent": "dialga", "rating": 771}, {"opponent": "lugia", "rating": 690}, {"opponent": "excadrill", "rating": 617}, {"opponent": "dragonite", "rating": 608}], "counters": [{"opponent": "garcho<PERSON>", "rating": 166}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "mewtwo", "rating": 406}, {"opponent": "metagross", "rating": 427}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 76500}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 26442}, {"moveId": "ROCK_SLIDE", "uses": 21510}, {"moveId": "THUNDERBOLT", "uses": 12335}, {"moveId": "FLASH_CANNON", "uses": 9876}, {"moveId": "HYPER_BEAM", "uses": 6466}]}, "moveset": ["THUNDER_SHOCK", "SUPER_POWER", "ROCK_SLIDE"], "score": 89.1}, {"speciesId": "latios_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 696, "matchups": [{"opponent": "garcho<PERSON>", "rating": 918}, {"opponent": "dragonite", "rating": 720}, {"opponent": "zekrom", "rating": 697, "opRating": 302}, {"opponent": "excadrill", "rating": 622}, {"opponent": "swampert", "rating": 590, "opRating": 409}], "counters": [{"opponent": "lugia", "rating": 376}, {"opponent": "dialga", "rating": 391}, {"opponent": "mewtwo", "rating": 395}, {"opponent": "gyarados", "rating": 402}, {"opponent": "giratina_origin", "rating": 460}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 67563}, {"moveId": "ZEN_HEADBUTT", "uses": 8937}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 33212}, {"moveId": "LUSTER_PURGE", "uses": 21561}, {"moveId": "PSYCHIC", "uses": 14249}, {"moveId": "SOLAR_BEAM", "uses": 7472}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 89}, {"speciesId": "buzzwole", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 688, "matchups": [{"opponent": "dialga", "rating": 832}, {"opponent": "excadrill", "rating": 811}, {"opponent": "garcho<PERSON>", "rating": 644}, {"opponent": "swampert", "rating": 603}, {"opponent": "metagross", "rating": 520}], "counters": [{"opponent": "lugia", "rating": 238}, {"opponent": "giratina_origin", "rating": 250}, {"opponent": "dragonite", "rating": 252}, {"opponent": "gyarados", "rating": 301}, {"opponent": "zacian_hero", "rating": 358}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45062}, {"moveId": "POISON_JAB", "uses": 31438}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34335}, {"moveId": "LUNGE", "uses": 25728}, {"moveId": "POWER_UP_PUNCH", "uses": 8222}, {"moveId": "FELL_STINGER", "uses": 8217}]}, "moveset": ["COUNTER", "SUPER_POWER", "LUNGE"], "score": 88.5}, {"speciesId": "z<PERSON><PERSON><PERSON>_hero", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hero)", "rating": 676, "matchups": [{"opponent": "excadrill", "rating": 760}, {"opponent": "garcho<PERSON>", "rating": 627}, {"opponent": "grou<PERSON>", "rating": 592, "opRating": 407}, {"opponent": "swampert", "rating": 589, "opRating": 410}, {"opponent": "metagross", "rating": 563}], "counters": [{"opponent": "mewtwo", "rating": 343}, {"opponent": "lugia", "rating": 373}, {"opponent": "gyarados", "rating": 456}, {"opponent": "dialga", "rating": 480}, {"opponent": "giratina_origin", "rating": 498}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 26744}, {"moveId": "QUICK_ATTACK", "uses": 25400}, {"moveId": "ICE_FANG", "uses": 14912}, {"moveId": "METAL_CLAW", "uses": 9479}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35187}, {"moveId": "CRUNCH", "uses": 19521}, {"moveId": "MOONBLAST", "uses": 13089}, {"moveId": "IRON_HEAD", "uses": 8854}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 88.3}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 643, "matchups": [{"opponent": "garcho<PERSON>", "rating": 930}, {"opponent": "excadrill", "rating": 690}, {"opponent": "dragonite", "rating": 674}, {"opponent": "lugia", "rating": 618}, {"opponent": "giratina_origin", "rating": 532}], "counters": [{"opponent": "metagross", "rating": 200}, {"opponent": "zacian_hero", "rating": 300}, {"opponent": "dialga", "rating": 331}, {"opponent": "mewtwo", "rating": 401}, {"opponent": "gyarados", "rating": 422}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 51388}, {"moveId": "MUD_SLAP", "uses": 25112}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36180}, {"moveId": "STONE_EDGE", "uses": 12258}, {"moveId": "BULLDOZE", "uses": 11119}, {"moveId": "ANCIENT_POWER", "uses": 11082}, {"moveId": "RETURN", "uses": 5756}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 88.2}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "dialga", "rating": 776}, {"opponent": "metagross", "rating": 713}, {"opponent": "excadrill", "rating": 708}, {"opponent": "zacian_hero", "rating": 619}, {"opponent": "grou<PERSON>", "rating": 559}], "counters": [{"opponent": "lugia", "rating": 235}, {"opponent": "gyarados", "rating": 288}, {"opponent": "giratina_origin", "rating": 366}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "mewtwo", "rating": 440}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 46524}, {"moveId": "WATER_GUN", "uses": 29976}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 29378}, {"moveId": "EARTHQUAKE", "uses": 13474}, {"moveId": "MUDDY_WATER", "uses": 11257}, {"moveId": "SURF", "uses": 9728}, {"moveId": "SLUDGE_WAVE", "uses": 6689}, {"moveId": "RETURN", "uses": 5876}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 88.2}, {"speciesId": "regirock", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 680, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 767, "opRating": 232}, {"opponent": "excadrill", "rating": 674}, {"opponent": "gyarados", "rating": 656}, {"opponent": "lugia", "rating": 651}, {"opponent": "dragonite", "rating": 578}], "counters": [{"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "giratina_origin", "rating": 354}, {"opponent": "zacian_hero", "rating": 421}, {"opponent": "dialga", "rating": 426}, {"opponent": "mewtwo", "rating": 463}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 40923}, {"moveId": "ROCK_THROW", "uses": 25601}, {"moveId": "ROCK_SMASH", "uses": 9968}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 30548}, {"moveId": "EARTHQUAKE", "uses": 18980}, {"moveId": "FOCUS_BLAST", "uses": 14039}, {"moveId": "ZAP_CANNON", "uses": 12844}]}, "moveset": ["LOCK_ON", "STONE_EDGE", "FOCUS_BLAST"], "score": 87.1}, {"speciesId": "zap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 681, "matchups": [{"opponent": "gyarados", "rating": 701}, {"opponent": "lugia", "rating": 639}, {"opponent": "metagross", "rating": 637}, {"opponent": "mewtwo", "rating": 521}, {"opponent": "zacian_hero", "rating": 516}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "giratina_origin", "rating": 382}, {"opponent": "excadrill", "rating": 393}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "swampert", "rating": 495}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50192}, {"moveId": "CHARGE_BEAM", "uses": 26308}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 33027}, {"moveId": "THUNDERBOLT", "uses": 15596}, {"moveId": "ANCIENT_POWER", "uses": 14479}, {"moveId": "THUNDER", "uses": 6915}, {"moveId": "ZAP_CANNON", "uses": 6440}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 86.7}, {"speciesId": "genesect_douse", "speciesName": "Genesect (Douse)", "rating": 680, "matchups": [{"opponent": "mewtwo", "rating": 708}, {"opponent": "excadrill", "rating": 683}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 604, "opRating": 395}, {"opponent": "garcho<PERSON>", "rating": 560}, {"opponent": "metagross", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 277}, {"opponent": "lugia", "rating": 347}, {"opponent": "dragonite", "rating": 372}, {"opponent": "zacian_hero", "rating": 482}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50406}, {"moveId": "METAL_CLAW", "uses": 26094}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24947}, {"moveId": "TECHNO_BLAST_DOUSE", "uses": 22969}, {"moveId": "MAGNET_BOMB", "uses": 22127}, {"moveId": "GUNK_SHOT", "uses": 6555}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_DOUSE"], "score": 86.4}, {"speciesId": "palkia", "speciesName": "Pa<PERSON><PERSON>", "rating": 688, "matchups": [{"opponent": "excadrill", "rating": 695}, {"opponent": "metagross", "rating": 654}, {"opponent": "swampert", "rating": 628}, {"opponent": "giratina_origin", "rating": 584}, {"opponent": "gyarados", "rating": 502}], "counters": [{"opponent": "dialga", "rating": 315}, {"opponent": "dragonite", "rating": 335}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "lugia", "rating": 361}, {"opponent": "mewtwo", "rating": 445}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 38268}, {"moveId": "DRAGON_TAIL", "uses": 38232}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 41144}, {"moveId": "DRACO_METEOR", "uses": 18147}, {"moveId": "FIRE_BLAST", "uses": 10850}, {"moveId": "HYDRO_PUMP", "uses": 6417}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "DRACO_METEOR"], "score": 86.4}, {"speciesId": "gyarado<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "metagross", "rating": 626}, {"opponent": "excadrill", "rating": 623}, {"opponent": "garcho<PERSON>", "rating": 582}, {"opponent": "giratina_origin", "rating": 530}, {"opponent": "mewtwo", "rating": 528}], "counters": [{"opponent": "zacian_hero", "rating": 274}, {"opponent": "zekrom", "rating": 334}, {"opponent": "lugia", "rating": 354}, {"opponent": "dialga", "rating": 404}, {"opponent": "ho_oh", "rating": 424}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22513}, {"moveId": "DRAGON_TAIL", "uses": 22350}, {"moveId": "WATERFALL", "uses": 21111}, {"moveId": "BITE", "uses": 10479}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 28828}, {"moveId": "CRUNCH", "uses": 20156}, {"moveId": "OUTRAGE", "uses": 12238}, {"moveId": "TWISTER", "uses": 6540}, {"moveId": "HYDRO_PUMP", "uses": 4668}, {"moveId": "DRAGON_PULSE", "uses": 3986}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 86.3}, {"speciesId": "genesect_burn", "speciesName": "Genesect (Burn)", "rating": 658, "matchups": [{"opponent": "metagross", "rating": 886}, {"opponent": "mewtwo", "rating": 708}, {"opponent": "excadrill", "rating": 683}, {"opponent": "sylveon", "rating": 680, "opRating": 319}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 604, "opRating": 395}], "counters": [{"opponent": "lugia", "rating": 347}, {"opponent": "dialga", "rating": 372}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "zacian_hero", "rating": 482}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50521}, {"moveId": "METAL_CLAW", "uses": 25979}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24637}, {"moveId": "MAGNET_BOMB", "uses": 22849}, {"moveId": "TECHNO_BLAST_BURN", "uses": 22707}, {"moveId": "FLAMETHROWER", "uses": 6417}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_BURN"], "score": 85.8}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 622, "matchups": [{"opponent": "dialga", "rating": 746}, {"opponent": "excadrill", "rating": 684}, {"opponent": "metagross", "rating": 671}, {"opponent": "mewtwo", "rating": 539}, {"opponent": "zekrom", "rating": 537, "opRating": 462}], "counters": [{"opponent": "lugia", "rating": 278}, {"opponent": "gyarados", "rating": 329}, {"opponent": "giratina_origin", "rating": 334}, {"opponent": "garcho<PERSON>", "rating": 387}, {"opponent": "zacian_hero", "rating": 395}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 48486}, {"moveId": "WATER_GUN", "uses": 28014}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 31745}, {"moveId": "EARTHQUAKE", "uses": 14546}, {"moveId": "MUDDY_WATER", "uses": 12188}, {"moveId": "SURF", "uses": 10539}, {"moveId": "SLUDGE_WAVE", "uses": 7446}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 85.8}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 661, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 843}, {"opponent": "zacian_hero", "rating": 708}, {"opponent": "excadrill", "rating": 614}, {"opponent": "mewtwo", "rating": 559}, {"opponent": "metagross", "rating": 524}], "counters": [{"opponent": "gyarados", "rating": 327}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "lugia", "rating": 340}, {"opponent": "dragonite", "rating": 457}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 43056}, {"moveId": "CONFUSION", "uses": 33444}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 48086}, {"moveId": "PSYCHIC", "uses": 14173}, {"moveId": "FOCUS_BLAST", "uses": 8650}, {"moveId": "OVERHEAT", "uses": 5513}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 85.3}, {"speciesId": "zap<PERSON>_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 697, "matchups": [{"opponent": "swampert", "rating": 795}, {"opponent": "excadrill", "rating": 787}, {"opponent": "gyarados", "rating": 631}, {"opponent": "garcho<PERSON>", "rating": 623}, {"opponent": "grou<PERSON>", "rating": 567, "opRating": 432}], "counters": [{"opponent": "giratina_origin", "rating": 310}, {"opponent": "metagross", "rating": 322}, {"opponent": "lugia", "rating": 340}, {"opponent": "dialga", "rating": 437}, {"opponent": "dragonite", "rating": 486}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 76500}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34240}, {"moveId": "BRAVE_BIRD", "uses": 28902}, {"moveId": "ANCIENT_POWER", "uses": 13368}]}, "moveset": ["COUNTER", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 85.3}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 687, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 838}, {"opponent": "gyarados", "rating": 733}, {"opponent": "lugia", "rating": 658}, {"opponent": "mewtwo", "rating": 607}, {"opponent": "zacian_hero", "rating": 596}], "counters": [{"opponent": "dialga", "rating": 296}, {"opponent": "excadrill", "rating": 351}, {"opponent": "giratina_origin", "rating": 358}, {"opponent": "metagross", "rating": 436}, {"opponent": "dragonite", "rating": 441}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41456}, {"moveId": "THUNDER_SHOCK", "uses": 35044}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 36342}, {"moveId": "SHADOW_BALL", "uses": 19388}, {"moveId": "RETURN", "uses": 8531}, {"moveId": "THUNDERBOLT", "uses": 6652}, {"moveId": "THUNDER", "uses": 5808}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 85.1}, {"speciesId": "genesect_shock", "speciesName": "Genesect (Shock)", "rating": 678, "matchups": [{"opponent": "yveltal", "rating": 905, "opRating": 94}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 772, "opRating": 227}, {"opponent": "gyarados", "rating": 765}, {"opponent": "mewtwo", "rating": 708}, {"opponent": "metagross", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 277}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "lugia", "rating": 438}, {"opponent": "dragonite", "rating": 468}, {"opponent": "zacian_hero", "rating": 482}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50716}, {"moveId": "METAL_CLAW", "uses": 25784}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25919}, {"moveId": "MAGNET_BOMB", "uses": 23200}, {"moveId": "TECHNO_BLAST_SHOCK", "uses": 22204}, {"moveId": "ZAP_CANNON", "uses": 5208}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_SHOCK"], "score": 85}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 589, "matchups": [{"opponent": "garcho<PERSON>", "rating": 820}, {"opponent": "dragonite", "rating": 811}, {"opponent": "gyarados", "rating": 744}, {"opponent": "giratina_origin", "rating": 589}, {"opponent": "dialga", "rating": 522}], "counters": [{"opponent": "excadrill", "rating": 211}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "zekrom", "rating": 298}, {"opponent": "ho_oh", "rating": 429}, {"opponent": "lugia", "rating": 445}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 7517}, {"moveId": "AIR_SLASH", "uses": 6326}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4952}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4870}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4416}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4251}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4202}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4187}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4169}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3894}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3817}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3777}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3736}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3386}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3365}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3325}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3201}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3130}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 21446}, {"moveId": "AERIAL_ACE", "uses": 19675}, {"moveId": "FLAMETHROWER", "uses": 18665}, {"moveId": "DAZZLING_GLEAM", "uses": 16762}]}, "moveset": ["CHARM", "ANCIENT_POWER", "FLAMETHROWER"], "score": 85}, {"speciesId": "giratina_altered", "speciesName": "Giratina (Altered)", "rating": 679, "matchups": [{"opponent": "ho_oh", "rating": 705, "opRating": 294}, {"opponent": "gyarados", "rating": 567}, {"opponent": "snorlax", "rating": 567, "opRating": 432}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 561, "opRating": 438}, {"opponent": "swampert", "rating": 557, "opRating": 442}], "counters": [{"opponent": "dialga", "rating": 326}, {"opponent": "metagross", "rating": 406}, {"opponent": "garcho<PERSON>", "rating": 427}, {"opponent": "mewtwo", "rating": 437}, {"opponent": "giratina_origin", "rating": 442}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 42662}, {"moveId": "DRAGON_BREATH", "uses": 33838}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 38971}, {"moveId": "ANCIENT_POWER", "uses": 18962}, {"moveId": "SHADOW_SNEAK", "uses": 18522}]}, "moveset": ["SHADOW_CLAW", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 84.8}, {"speciesId": "sylveon", "speciesName": "Sylveon", "rating": 612, "matchups": [{"opponent": "dragonite", "rating": 824}, {"opponent": "garcho<PERSON>", "rating": 677}, {"opponent": "gyarados", "rating": 618}, {"opponent": "dialga", "rating": 546}, {"opponent": "zacian_hero", "rating": 505}], "counters": [{"opponent": "mewtwo", "rating": 255}, {"opponent": "excadrill", "rating": 353}, {"opponent": "swampert", "rating": 430}, {"opponent": "lugia", "rating": 438}, {"opponent": "giratina_origin", "rating": 494}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39694}, {"moveId": "CHARM", "uses": 36806}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 27116}, {"moveId": "PSYSHOCK", "uses": 18291}, {"moveId": "LAST_RESORT", "uses": 13207}, {"moveId": "DRAINING_KISS", "uses": 10998}, {"moveId": "DAZZLING_GLEAM", "uses": 6884}]}, "moveset": ["CHARM", "MOONBLAST", "PSYSHOCK"], "score": 84.3}, {"speciesId": "kyogre", "speciesName": "Kyogre", "rating": 659, "matchups": [{"opponent": "grou<PERSON>", "rating": 869}, {"opponent": "excadrill", "rating": 760}, {"opponent": "swampert", "rating": 660}, {"opponent": "metagross", "rating": 597}, {"opponent": "gyarados", "rating": 562}], "counters": [{"opponent": "giratina_origin", "rating": 250}, {"opponent": "dialga", "rating": 260}, {"opponent": "zacian_hero", "rating": 421}, {"opponent": "garcho<PERSON>", "rating": 438}, {"opponent": "mewtwo", "rating": 447}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 76500}], "chargedMoves": [{"moveId": "SURF", "uses": 37731}, {"moveId": "BLIZZARD", "uses": 17462}, {"moveId": "THUNDER", "uses": 15183}, {"moveId": "HYDRO_PUMP", "uses": 6017}]}, "moveset": ["WATERFALL", "SURF", "BLIZZARD"], "score": 84}, {"speciesId": "tapu_bulu", "speciesName": "Tapu Bulu", "rating": 604, "matchups": [{"opponent": "swampert", "rating": 847}, {"opponent": "gyarados", "rating": 761}, {"opponent": "garcho<PERSON>", "rating": 754}, {"opponent": "dragonite", "rating": 700}, {"opponent": "zacian_hero", "rating": 582}], "counters": [{"opponent": "metagross", "rating": 183}, {"opponent": "dialga", "rating": 312}, {"opponent": "mewtwo", "rating": 408}, {"opponent": "giratina_origin", "rating": 430}, {"opponent": "grou<PERSON>", "rating": 448}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 59614}, {"moveId": "ROCK_SMASH", "uses": 16886}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28660}, {"moveId": "MEGAHORN", "uses": 23260}, {"moveId": "DAZZLING_GLEAM", "uses": 18636}, {"moveId": "SOLAR_BEAM", "uses": 5973}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "DAZZLING_GLEAM"], "score": 83.9}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 633, "matchups": [{"opponent": "mewtwo", "rating": 831}, {"opponent": "metagross", "rating": 828}, {"opponent": "giratina_origin", "rating": 671}, {"opponent": "lugia", "rating": 585}, {"opponent": "gyarados", "rating": 544}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "dragonite", "rating": 297}, {"opponent": "dialga", "rating": 307}, {"opponent": "garcho<PERSON>", "rating": 326}, {"opponent": "swampert", "rating": 487}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45863}, {"moveId": "FEINT_ATTACK", "uses": 30637}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 26754}, {"moveId": "SHADOW_BALL", "uses": 20461}, {"moveId": "SLUDGE_BOMB", "uses": 15036}, {"moveId": "FOCUS_BLAST", "uses": 14144}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 83.7}, {"speciesId": "metagross_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 671, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 854}, {"opponent": "zacian_hero", "rating": 686}, {"opponent": "dialga", "rating": 683}, {"opponent": "dragonite", "rating": 630}, {"opponent": "lugia", "rating": 575}], "counters": [{"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "swampert", "rating": 328}, {"opponent": "gyarados", "rating": 373}, {"opponent": "excadrill", "rating": 388}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 68305}, {"moveId": "ZEN_HEADBUTT", "uses": 8195}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 33441}, {"moveId": "PSYCHIC", "uses": 19550}, {"moveId": "EARTHQUAKE", "uses": 18247}, {"moveId": "FLASH_CANNON", "uses": 5202}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 83.7}, {"speciesId": "tapu_fini", "speciesName": "<PERSON><PERSON>", "rating": 620, "matchups": [{"opponent": "excadrill", "rating": 722}, {"opponent": "gyarados", "rating": 665}, {"opponent": "dragonite", "rating": 662}, {"opponent": "garcho<PERSON>", "rating": 643}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 302}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "dialga", "rating": 396}, {"opponent": "lugia", "rating": 404}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 8489}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5687}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5155}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4732}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4572}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4571}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4489}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4265}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4218}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4203}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4138}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4130}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3679}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3665}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3646}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3576}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3410}], "chargedMoves": [{"moveId": "SURF", "uses": 31415}, {"moveId": "MOONBLAST", "uses": 22738}, {"moveId": "ICE_BEAM", "uses": 17305}, {"moveId": "HYDRO_PUMP", "uses": 4984}]}, "moveset": ["WATER_GUN", "SURF", "MOONBLAST"], "score": 83.4}, {"speciesId": "kommo_o", "speciesName": "Kommo-o", "rating": 662, "matchups": [{"opponent": "excadrill", "rating": 731}, {"opponent": "dialga", "rating": 707}, {"opponent": "swampert", "rating": 640}, {"opponent": "yveltal", "rating": 606, "opRating": 393}, {"opponent": "giratina_origin", "rating": 591}], "counters": [{"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "gyarados", "rating": 365}, {"opponent": "grou<PERSON>", "rating": 426}, {"opponent": "metagross", "rating": 427}, {"opponent": "dragonite", "rating": 438}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42084}, {"moveId": "POISON_JAB", "uses": 34416}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35274}, {"moveId": "DRAGON_CLAW", "uses": 29154}, {"moveId": "FLAMETHROWER", "uses": 12121}]}, "moveset": ["DRAGON_TAIL", "CLOSE_COMBAT", "DRAGON_CLAW"], "score": 83.2}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 568, "matchups": [{"opponent": "metagross", "rating": 835}, {"opponent": "dialga", "rating": 752}, {"opponent": "mewtwo", "rating": 752}, {"opponent": "giratina_origin", "rating": 682}, {"opponent": "excadrill", "rating": 675}], "counters": [{"opponent": "grou<PERSON>", "rating": 320}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "swampert", "rating": 390}, {"opponent": "lugia", "rating": 397}, {"opponent": "gyarados", "rating": 448}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46603}, {"moveId": "MUD_SLAP", "uses": 29897}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36899}, {"moveId": "EARTHQUAKE", "uses": 23083}, {"moveId": "OUTRAGE", "uses": 16506}]}, "moveset": ["SNARL", "CRUNCH", "EARTHQUAKE"], "score": 83.1}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 607, "matchups": [{"opponent": "gyarados", "rating": 811}, {"opponent": "dialga", "rating": 723}, {"opponent": "lugia", "rating": 659}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 630, "opRating": 369}, {"opponent": "mewtwo", "rating": 604}], "counters": [{"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "excadrill", "rating": 448}, {"opponent": "metagross", "rating": 470}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42380}, {"moveId": "METAL_CLAW", "uses": 21900}, {"moveId": "ROCK_SMASH", "uses": 12220}], "chargedMoves": [{"moveId": "FLASH_CANNON", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "ZAP_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 83.1}, {"speciesId": "hydreigon", "speciesName": "Hydreigon", "rating": 661, "matchups": [{"opponent": "mewtwo", "rating": 788}, {"opponent": "giratina_origin", "rating": 740}, {"opponent": "gyarados", "rating": 537}, {"opponent": "ho_oh", "rating": 531, "opRating": 468}, {"opponent": "swampert", "rating": 518, "opRating": 481}], "counters": [{"opponent": "garcho<PERSON>", "rating": 230}, {"opponent": "dialga", "rating": 350}, {"opponent": "lugia", "rating": 354}, {"opponent": "excadrill", "rating": 418}, {"opponent": "metagross", "rating": 473}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 50467}, {"moveId": "BITE", "uses": 26033}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 40292}, {"moveId": "DRAGON_PULSE", "uses": 15078}, {"moveId": "DARK_PULSE", "uses": 12389}, {"moveId": "FLASH_CANNON", "uses": 8710}]}, "moveset": ["DRAGON_BREATH", "BRUTAL_SWING", "FLASH_CANNON"], "score": 82.9}, {"speciesId": "avalugg", "speciesName": "Avalugg", "rating": 609, "matchups": [{"opponent": "garcho<PERSON>", "rating": 698}, {"opponent": "giratina_origin", "rating": 641}, {"opponent": "dragonite", "rating": 564}, {"opponent": "gyarados", "rating": 551}, {"opponent": "lugia", "rating": 520}], "counters": [{"opponent": "dialga", "rating": 358}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "zacian_hero", "rating": 387}, {"opponent": "swampert", "rating": 455}, {"opponent": "excadrill", "rating": 472}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 52481}, {"moveId": "BITE", "uses": 24019}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 31698}, {"moveId": "BODY_SLAM", "uses": 17223}, {"moveId": "CRUNCH", "uses": 14425}, {"moveId": "EARTHQUAKE", "uses": 10125}, {"moveId": "MIRROR_COAT", "uses": 3078}]}, "moveset": ["ICE_FANG", "AVALANCHE", "BODY_SLAM"], "score": 82.8}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 660, "matchups": [{"opponent": "gyarados", "rating": 725}, {"opponent": "metagross", "rating": 680}, {"opponent": "lugia", "rating": 677}, {"opponent": "swampert", "rating": 548, "opRating": 451}, {"opponent": "dragonite", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "giratina_origin", "rating": 318}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "zacian_hero", "rating": 430}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 49272}, {"moveId": "CHARGE_BEAM", "uses": 27228}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 29496}, {"moveId": "THUNDERBOLT", "uses": 14285}, {"moveId": "ANCIENT_POWER", "uses": 13140}, {"moveId": "RETURN", "uses": 7340}, {"moveId": "THUNDER", "uses": 6270}, {"moveId": "ZAP_CANNON", "uses": 6005}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 82.6}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 641, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 788}, {"opponent": "dialga", "rating": 644}, {"opponent": "gyarados", "rating": 597}, {"opponent": "zekrom", "rating": 590, "opRating": 409}, {"opponent": "lugia", "rating": 534}], "counters": [{"opponent": "zacian_hero", "rating": 239}, {"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "giratina_origin", "rating": 414}, {"opponent": "metagross", "rating": 453}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38731}, {"moveId": "MUD_SLAP", "uses": 20205}, {"moveId": "METAL_CLAW", "uses": 17560}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32017}, {"moveId": "ROCK_SLIDE", "uses": 22231}, {"moveId": "IRON_HEAD", "uses": 13985}, {"moveId": "EARTHQUAKE", "uses": 8238}]}, "moveset": ["MUD_SHOT", "DRILL_RUN", "ROCK_SLIDE"], "score": 82.4}, {"speciesId": "nihilego", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 681, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 894}, {"opponent": "zacian_hero", "rating": 761}, {"opponent": "lugia", "rating": 752}, {"opponent": "gyarados", "rating": 710}, {"opponent": "dragonite", "rating": 679}], "counters": [{"opponent": "metagross", "rating": 177}, {"opponent": "dialga", "rating": 198}, {"opponent": "zekrom", "rating": 396}, {"opponent": "mewtwo", "rating": 414}, {"opponent": "giratina_origin", "rating": 436}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54838}, {"moveId": "ACID", "uses": 18712}, {"moveId": "POUND", "uses": 3060}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 41282}, {"moveId": "SLUDGE_BOMB", "uses": 22024}, {"moveId": "POWER_GEM", "uses": 7511}, {"moveId": "GUNK_SHOT", "uses": 5709}]}, "moveset": ["POISON_JAB", "ROCK_SLIDE", "SLUDGE_BOMB"], "score": 82.4}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 624, "matchups": [{"opponent": "excadrill", "rating": 740}, {"opponent": "giratina_origin", "rating": 636}, {"opponent": "gyarados", "rating": 636}, {"opponent": "swampert", "rating": 602}, {"opponent": "metagross", "rating": 557}], "counters": [{"opponent": "zacian_hero", "rating": 271}, {"opponent": "mewtwo", "rating": 372}, {"opponent": "lugia", "rating": 378}, {"opponent": "dialga", "rating": 440}, {"opponent": "garcho<PERSON>", "rating": 467}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66329}, {"moveId": "ZEN_HEADBUTT", "uses": 8628}, {"moveId": "YAWN", "uses": 1578}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26076}, {"moveId": "SUPER_POWER", "uses": 16863}, {"moveId": "OUTRAGE", "uses": 9584}, {"moveId": "EARTHQUAKE", "uses": 9353}, {"moveId": "HEAVY_SLAM", "uses": 7183}, {"moveId": "SKULL_BASH", "uses": 4463}, {"moveId": "HYPER_BEAM", "uses": 2959}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 82.4}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 618, "matchups": [{"opponent": "dialga", "rating": 789}, {"opponent": "mewtwo", "rating": 713}, {"opponent": "mewtwo_shadow", "rating": 668, "opRating": 331}, {"opponent": "excadrill", "rating": 656}, {"opponent": "snorlax", "rating": 503, "opRating": 496}], "counters": [{"opponent": "gyarados", "rating": 288}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "dragonite", "rating": 297}, {"opponent": "garcho<PERSON>", "rating": 415}, {"opponent": "metagross", "rating": 465}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46213}, {"moveId": "BUG_BITE", "uses": 30287}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 30575}, {"moveId": "MEGAHORN", "uses": 27416}, {"moveId": "AERIAL_ACE", "uses": 14675}, {"moveId": "ACID_SPRAY", "uses": 3959}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 82.1}, {"speciesId": "gyarados", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 613, "matchups": [{"opponent": "grou<PERSON>", "rating": 773}, {"opponent": "swampert", "rating": 711}, {"opponent": "metagross", "rating": 685}, {"opponent": "garcho<PERSON>", "rating": 634}, {"opponent": "giratina_origin", "rating": 556}], "counters": [{"opponent": "dialga", "rating": 317}, {"opponent": "lugia", "rating": 328}, {"opponent": "mewtwo", "rating": 348}, {"opponent": "zacian_hero", "rating": 393}, {"opponent": "dragonite", "rating": 476}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22367}, {"moveId": "DRAGON_TAIL", "uses": 21803}, {"moveId": "WATERFALL", "uses": 20983}, {"moveId": "BITE", "uses": 11360}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 26283}, {"moveId": "CRUNCH", "uses": 18450}, {"moveId": "OUTRAGE", "uses": 11090}, {"moveId": "RETURN", "uses": 6834}, {"moveId": "TWISTER", "uses": 5977}, {"moveId": "HYDRO_PUMP", "uses": 4156}, {"moveId": "DRAGON_PULSE", "uses": 3733}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 82.1}, {"speciesId": "mew", "speciesName": "Mew", "rating": 639, "matchups": [{"opponent": "gyarados", "rating": 800}, {"opponent": "lugia", "rating": 606}, {"opponent": "metagross", "rating": 584}, {"opponent": "mewtwo", "rating": 554}, {"opponent": "excadrill", "rating": 552}], "counters": [{"opponent": "dialga", "rating": 255}, {"opponent": "dragonite", "rating": 343}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "grou<PERSON>", "rating": 461}, {"opponent": "zacian_hero", "rating": 491}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 12082}, {"moveId": "VOLT_SWITCH", "uses": 10758}, {"moveId": "SNARL", "uses": 9101}, {"moveId": "POISON_JAB", "uses": 7743}, {"moveId": "DRAGON_TAIL", "uses": 7199}, {"moveId": "INFESTATION", "uses": 7084}, {"moveId": "CHARGE_BEAM", "uses": 4685}, {"moveId": "WATERFALL", "uses": 4619}, {"moveId": "FROST_BREATH", "uses": 4380}, {"moveId": "STEEL_WING", "uses": 2898}, {"moveId": "STRUGGLE_BUG", "uses": 2592}, {"moveId": "ROCK_SMASH", "uses": 2007}, {"moveId": "CUT", "uses": 1032}, {"moveId": "POUND", "uses": 99}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8459}, {"moveId": "DRAGON_CLAW", "uses": 5894}, {"moveId": "SURF", "uses": 5601}, {"moveId": "ROCK_SLIDE", "uses": 5282}, {"moveId": "PSYSHOCK", "uses": 4897}, {"moveId": "ICE_BEAM", "uses": 4722}, {"moveId": "FLAME_CHARGE", "uses": 4342}, {"moveId": "DARK_PULSE", "uses": 4287}, {"moveId": "GRASS_KNOT", "uses": 4178}, {"moveId": "FOCUS_BLAST", "uses": 2849}, {"moveId": "LOW_SWEEP", "uses": 2582}, {"moveId": "BULLDOZE", "uses": 2292}, {"moveId": "STONE_EDGE", "uses": 2144}, {"moveId": "DAZZLING_GLEAM", "uses": 2021}, {"moveId": "PSYCHIC", "uses": 1888}, {"moveId": "ANCIENT_POWER", "uses": 1824}, {"moveId": "OVERHEAT", "uses": 1717}, {"moveId": "BLIZZARD", "uses": 1668}, {"moveId": "GYRO_BALL", "uses": 1529}, {"moveId": "ENERGY_BALL", "uses": 1481}, {"moveId": "THUNDERBOLT", "uses": 1468}, {"moveId": "FLASH_CANNON", "uses": 1467}, {"moveId": "HYPER_BEAM", "uses": 1416}, {"moveId": "THUNDER", "uses": 1360}, {"moveId": "SOLAR_BEAM", "uses": 815}]}, "moveset": ["SHADOW_CLAW", "SURF", "WILD_CHARGE"], "score": 82.1}, {"speciesId": "tapu_lele", "speciesName": "<PERSON><PERSON>", "rating": 641, "matchups": [{"opponent": "dragonite", "rating": 812}, {"opponent": "swampert", "rating": 713}, {"opponent": "gyarados", "rating": 707}, {"opponent": "garcho<PERSON>", "rating": 636}, {"opponent": "zacian_hero", "rating": 589}], "counters": [{"opponent": "mewtwo", "rating": 85}, {"opponent": "metagross", "rating": 142}, {"opponent": "lugia", "rating": 273}, {"opponent": "excadrill", "rating": 413}, {"opponent": "dialga", "rating": 421}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56947}, {"moveId": "ASTONISH", "uses": 19553}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28463}, {"moveId": "PSYSHOCK", "uses": 24971}, {"moveId": "FOCUS_BLAST", "uses": 14677}, {"moveId": "FUTURE_SIGHT", "uses": 8379}]}, "moveset": ["CONFUSION", "MOONBLAST", "PSYSHOCK"], "score": 82.1}, {"speciesId": "cobalion", "speciesName": "Cobalion", "rating": 642, "matchups": [{"opponent": "dialga", "rating": 739}, {"opponent": "zekrom", "rating": 670}, {"opponent": "gyarados", "rating": 622}, {"opponent": "excadrill", "rating": 585}, {"opponent": "palkia", "rating": 553, "opRating": 446}], "counters": [{"opponent": "mewtwo", "rating": 270}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "metagross", "rating": 354}, {"opponent": "dragonite", "rating": 367}, {"opponent": "lugia", "rating": 380}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 50391}, {"moveId": "METAL_CLAW", "uses": 21890}, {"moveId": "ZEN_HEADBUTT", "uses": 4182}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 26286}, {"moveId": "CLOSE_COMBAT", "uses": 24091}, {"moveId": "STONE_EDGE", "uses": 14770}, {"moveId": "IRON_HEAD", "uses": 11227}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "STONE_EDGE"], "score": 81.8}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 640, "matchups": [{"opponent": "mewtwo", "rating": 708}, {"opponent": "sylveon", "rating": 680, "opRating": 319}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 604, "opRating": 395}, {"opponent": "garcho<PERSON>", "rating": 560}, {"opponent": "gyarados", "rating": 506}], "counters": [{"opponent": "dialga", "rating": 277}, {"opponent": "lugia", "rating": 347}, {"opponent": "dragonite", "rating": 468}, {"opponent": "zacian_hero", "rating": 482}, {"opponent": "metagross", "rating": 497}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 49271}, {"moveId": "METAL_CLAW", "uses": 27229}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26830}, {"moveId": "MAGNET_BOMB", "uses": 25108}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 20821}, {"moveId": "HYPER_BEAM", "uses": 3605}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 81.5}, {"speciesId": "con<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 629, "matchups": [{"opponent": "dialga", "rating": 754}, {"opponent": "excadrill", "rating": 697}, {"opponent": "gyarados", "rating": 670}, {"opponent": "swampert", "rating": 593}, {"opponent": "yveltal", "rating": 581, "opRating": 418}], "counters": [{"opponent": "lugia", "rating": 259}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "metagross", "rating": 441}, {"opponent": "garcho<PERSON>", "rating": 450}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45997}, {"moveId": "POISON_JAB", "uses": 30503}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 36175}, {"moveId": "STONE_EDGE", "uses": 30453}, {"moveId": "FOCUS_BLAST", "uses": 9869}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "STONE_EDGE"], "score": 81.3}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 574, "matchups": [{"opponent": "garcho<PERSON>", "rating": 923}, {"opponent": "mewtwo", "rating": 710}, {"opponent": "giratina_origin", "rating": 659}, {"opponent": "dragonite", "rating": 621}, {"opponent": "lugia", "rating": 566}], "counters": [{"opponent": "metagross", "rating": 238}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "dialga", "rating": 288}, {"opponent": "grou<PERSON>", "rating": 394}, {"opponent": "swampert", "rating": 420}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30690}, {"moveId": "ICE_SHARD", "uses": 27740}, {"moveId": "FEINT_ATTACK", "uses": 18116}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38246}, {"moveId": "FOUL_PLAY", "uses": 21882}, {"moveId": "FOCUS_BLAST", "uses": 9839}, {"moveId": "RETURN", "uses": 6568}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 81.2}, {"speciesId": "virizion", "speciesName": "Virizion", "rating": 605, "matchups": [{"opponent": "excadrill", "rating": 835}, {"opponent": "swampert", "rating": 760}, {"opponent": "gyarados", "rating": 638}, {"opponent": "dialga", "rating": 625}, {"opponent": "metagross", "rating": 545}], "counters": [{"opponent": "giratina_origin", "rating": 193}, {"opponent": "mewtwo", "rating": 229}, {"opponent": "dragonite", "rating": 239}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 406}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 40911}, {"moveId": "QUICK_ATTACK", "uses": 32679}, {"moveId": "ZEN_HEADBUTT", "uses": 2858}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26280}, {"moveId": "SACRED_SWORD", "uses": 20027}, {"moveId": "CLOSE_COMBAT", "uses": 18415}, {"moveId": "STONE_EDGE", "uses": 11872}]}, "moveset": ["DOUBLE_KICK", "LEAF_BLADE", "SACRED_SWORD"], "score": 80.7}, {"speciesId": "goodra", "speciesName": "<PERSON><PERSON>", "rating": 651, "matchups": [{"opponent": "swampert", "rating": 690}, {"opponent": "giratina_origin", "rating": 655}, {"opponent": "gyarados", "rating": 583}, {"opponent": "excadrill", "rating": 548}, {"opponent": "garcho<PERSON>", "rating": 513}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "lugia", "rating": 266}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "metagross", "rating": 308}, {"opponent": "dragonite", "rating": 319}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48363}, {"moveId": "WATER_GUN", "uses": 28137}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22372}, {"moveId": "MUDDY_WATER", "uses": 21447}, {"moveId": "DRACO_METEOR", "uses": 19603}, {"moveId": "SLUDGE_WAVE", "uses": 13012}]}, "moveset": ["DRAGON_BREATH", "MUDDY_WATER", "DRACO_METEOR"], "score": 79.7}, {"speciesId": "thundurus_therian", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Therian)", "rating": 613, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 735, "opRating": 264}, {"opponent": "swampert", "rating": 711, "opRating": 288}, {"opponent": "metagross", "rating": 658}, {"opponent": "gyarados", "rating": 658}, {"opponent": "excadrill", "rating": 567, "opRating": 432}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "mewtwo", "rating": 445}, {"opponent": "lugia", "rating": 471}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 57508}, {"moveId": "BITE", "uses": 18992}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27779}, {"moveId": "FOCUS_BLAST", "uses": 21844}, {"moveId": "SLUDGE_WAVE", "uses": 14845}, {"moveId": "THUNDER", "uses": 12017}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "FOCUS_BLAST"], "score": 79.7}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 626, "matchups": [{"opponent": "metagross", "rating": 829}, {"opponent": "sylveon", "rating": 792, "opRating": 207}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 744}, {"opponent": "dialga", "rating": 654}, {"opponent": "lugia", "rating": 507}], "counters": [{"opponent": "giratina_origin", "rating": 250}, {"opponent": "gyarados", "rating": 275}, {"opponent": "dragonite", "rating": 327}, {"opponent": "zacian_hero", "rating": 453}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46697}, {"moveId": "BUG_BITE", "uses": 29803}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 24927}, {"moveId": "STONE_EDGE", "uses": 24482}, {"moveId": "IRON_HEAD", "uses": 20254}, {"moveId": "FIRE_BLAST", "uses": 6750}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "IRON_HEAD"], "score": 79.6}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 622, "matchups": [{"opponent": "giratina_origin", "rating": 667}, {"opponent": "swampert", "rating": 653, "opRating": 346}, {"opponent": "metagross", "rating": 614}, {"opponent": "excadrill", "rating": 598}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 339}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "lugia", "rating": 404}, {"opponent": "gyarados", "rating": 427}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 65311}, {"moveId": "ZEN_HEADBUTT", "uses": 9210}, {"moveId": "YAWN", "uses": 2062}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24688}, {"moveId": "SUPER_POWER", "uses": 16243}, {"moveId": "OUTRAGE", "uses": 9120}, {"moveId": "EARTHQUAKE", "uses": 8981}, {"moveId": "HEAVY_SLAM", "uses": 6833}, {"moveId": "SKULL_BASH", "uses": 4249}, {"moveId": "RETURN", "uses": 3605}, {"moveId": "HYPER_BEAM", "uses": 2778}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 79.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Ray<PERSON><PERSON>", "rating": 644, "matchups": [{"opponent": "grou<PERSON>", "rating": 769}, {"opponent": "swampert", "rating": 701}, {"opponent": "giratina_origin", "rating": 549}, {"opponent": "ho_oh", "rating": 518, "opRating": 481}, {"opponent": "gyarados", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 279}, {"opponent": "garcho<PERSON>", "rating": 281}, {"opponent": "metagross", "rating": 354}, {"opponent": "mewtwo", "rating": 447}, {"opponent": "excadrill", "rating": 488}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 46887}, {"moveId": "AIR_SLASH", "uses": 29613}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23884}, {"moveId": "ANCIENT_POWER", "uses": 19213}, {"moveId": "AERIAL_ACE", "uses": 17516}, {"moveId": "HURRICANE", "uses": 15940}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "ANCIENT_POWER"], "score": 79.1}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 653, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 895, "opRating": 104}, {"opponent": "dialga", "rating": 738}, {"opponent": "yveltal", "rating": 633, "opRating": 366}, {"opponent": "snorlax", "rating": 627, "opRating": 372}, {"opponent": "gyarados", "rating": 555}], "counters": [{"opponent": "giratina_origin", "rating": 211}, {"opponent": "zacian_hero", "rating": 300}, {"opponent": "dragonite", "rating": 311}, {"opponent": "garcho<PERSON>", "rating": 326}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 38469}, {"moveId": "POISON_JAB", "uses": 29966}, {"moveId": "ROCK_SMASH", "uses": 8027}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 44045}, {"moveId": "X_SCISSOR", "uses": 18525}, {"moveId": "AERIAL_ACE", "uses": 13919}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "X_SCISSOR"], "score": 79.1}, {"speciesId": "latios", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 667, "matchups": [{"opponent": "kyogre", "rating": 744, "opRating": 255}, {"opponent": "zekrom", "rating": 694, "opRating": 305}, {"opponent": "swampert", "rating": 645, "opRating": 354}, {"opponent": "grou<PERSON>", "rating": 569, "opRating": 430}, {"opponent": "ho_oh", "rating": 563, "opRating": 436}], "counters": [{"opponent": "mewtwo", "rating": 234}, {"opponent": "dialga", "rating": 317}, {"opponent": "giratina_origin", "rating": 370}, {"opponent": "garcho<PERSON>", "rating": 429}, {"opponent": "dragonite", "rating": 481}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 66704}, {"moveId": "ZEN_HEADBUTT", "uses": 9796}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30041}, {"moveId": "LUSTER_PURGE", "uses": 19615}, {"moveId": "PSYCHIC", "uses": 13001}, {"moveId": "RETURN", "uses": 7312}, {"moveId": "SOLAR_BEAM", "uses": 6719}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 78.9}, {"speciesId": "heracross", "speciesName": "Heracross", "rating": 622, "matchups": [{"opponent": "dialga", "rating": 781}, {"opponent": "excadrill", "rating": 750}, {"opponent": "snorlax", "rating": 697, "opRating": 302}, {"opponent": "yveltal", "rating": 662, "opRating": 337}, {"opponent": "swampert", "rating": 529}], "counters": [{"opponent": "giratina_origin", "rating": 258}, {"opponent": "dragonite", "rating": 321}, {"opponent": "gyarados", "rating": 322}, {"opponent": "metagross", "rating": 415}, {"opponent": "garcho<PERSON>", "rating": 464}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 58197}, {"moveId": "STRUGGLE_BUG", "uses": 18303}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32816}, {"moveId": "MEGAHORN", "uses": 18327}, {"moveId": "ROCK_BLAST", "uses": 15063}, {"moveId": "EARTHQUAKE", "uses": 10228}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ROCK_BLAST"], "score": 78.8}, {"speciesId": "latias", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 645, "matchups": [{"opponent": "kyogre", "rating": 837, "opRating": 162}, {"opponent": "swampert", "rating": 709}, {"opponent": "grou<PERSON>", "rating": 604}, {"opponent": "mewtwo", "rating": 569}, {"opponent": "gyarados", "rating": 502}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 267}, {"opponent": "metagross", "rating": 337}, {"opponent": "lugia", "rating": 347}, {"opponent": "zacian_hero", "rating": 424}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 46114}, {"moveId": "CHARM", "uses": 23698}, {"moveId": "ZEN_HEADBUTT", "uses": 6697}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 21903}, {"moveId": "OUTRAGE", "uses": 19624}, {"moveId": "PSYCHIC", "uses": 14566}, {"moveId": "THUNDER", "uses": 12166}, {"moveId": "RETURN", "uses": 8026}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 78.6}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 616, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 825}, {"opponent": "metagross", "rating": 686}, {"opponent": "zacian_hero", "rating": 625}, {"opponent": "dialga", "rating": 614}, {"opponent": "mewtwo", "rating": 578}], "counters": [{"opponent": "giratina_origin", "rating": 215}, {"opponent": "dragonite", "rating": 255}, {"opponent": "lugia", "rating": 259}, {"opponent": "excadrill", "rating": 279}, {"opponent": "gyarados", "rating": 378}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 44265}, {"moveId": "FIRE_FANG", "uses": 32235}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25503}, {"moveId": "OVERHEAT", "uses": 19633}, {"moveId": "IRON_HEAD", "uses": 14896}, {"moveId": "FLAMETHROWER", "uses": 10561}, {"moveId": "FIRE_BLAST", "uses": 5892}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 78.5}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 584, "matchups": [{"opponent": "metagross", "rating": 672}, {"opponent": "zacian_hero", "rating": 658}, {"opponent": "grou<PERSON>", "rating": 658}, {"opponent": "dialga", "rating": 545}, {"opponent": "mewtwo", "rating": 513}], "counters": [{"opponent": "lugia", "rating": 271}, {"opponent": "giratina_origin", "rating": 282}, {"opponent": "dragonite", "rating": 345}, {"opponent": "gyarados", "rating": 347}, {"opponent": "garcho<PERSON>", "rating": 359}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38464}, {"moveId": "WING_ATTACK", "uses": 38036}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30406}, {"moveId": "OVERHEAT", "uses": 18814}, {"moveId": "ANCIENT_POWER", "uses": 18500}, {"moveId": "FIRE_BLAST", "uses": 5470}, {"moveId": "HEAT_WAVE", "uses": 3208}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 78.5}, {"speciesId": "terrakion", "speciesName": "Terrakion", "rating": 658, "matchups": [{"opponent": "snorlax", "rating": 664, "opRating": 335}, {"opponent": "excadrill", "rating": 585}, {"opponent": "ho_oh", "rating": 571, "opRating": 428}, {"opponent": "gyarados", "rating": 563}, {"opponent": "zacian_hero", "rating": 542}], "counters": [{"opponent": "garcho<PERSON>", "rating": 251}, {"opponent": "giratina_origin", "rating": 382}, {"opponent": "dragonite", "rating": 385}, {"opponent": "dialga", "rating": 426}, {"opponent": "lugia", "rating": 438}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47167}, {"moveId": "SMACK_DOWN", "uses": 26719}, {"moveId": "ZEN_HEADBUTT", "uses": 2595}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 24148}, {"moveId": "ROCK_SLIDE", "uses": 22738}, {"moveId": "CLOSE_COMBAT", "uses": 21377}, {"moveId": "EARTHQUAKE", "uses": 8218}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "ROCK_SLIDE"], "score": 78.5}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 572, "matchups": [{"opponent": "garcho<PERSON>", "rating": 904}, {"opponent": "zekrom", "rating": 713}, {"opponent": "mewtwo", "rating": 665}, {"opponent": "giratina_origin", "rating": 582}, {"opponent": "dragonite", "rating": 547}], "counters": [{"opponent": "metagross", "rating": 279}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "dialga", "rating": 345}, {"opponent": "lugia", "rating": 354}, {"opponent": "gyarados", "rating": 389}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 31510}, {"moveId": "ICE_SHARD", "uses": 27694}, {"moveId": "FEINT_ATTACK", "uses": 17275}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41686}, {"moveId": "FOUL_PLAY", "uses": 23975}, {"moveId": "FOCUS_BLAST", "uses": 10749}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 78.3}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 603, "matchups": [{"opponent": "swampert", "rating": 731}, {"opponent": "zacian_hero", "rating": 612}, {"opponent": "dragonite", "rating": 584}, {"opponent": "zekrom", "rating": 569, "opRating": 430}, {"opponent": "garcho<PERSON>", "rating": 525}], "counters": [{"opponent": "giratina_origin", "rating": 241}, {"opponent": "lugia", "rating": 311}, {"opponent": "mewtwo", "rating": 322}, {"opponent": "gyarados", "rating": 363}, {"opponent": "dialga", "rating": 380}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 40782}, {"moveId": "CONFUSION", "uses": 35718}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21700}, {"moveId": "MOONBLAST", "uses": 21483}, {"moveId": "FUTURE_SIGHT", "uses": 18961}, {"moveId": "AURORA_BEAM", "uses": 14299}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 77.8}, {"speciesId": "tapu_koko", "speciesName": "<PERSON><PERSON>", "rating": 600, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 745}, {"opponent": "gyarados", "rating": 742}, {"opponent": "dragonite", "rating": 716}, {"opponent": "lugia", "rating": 605}, {"opponent": "dialga", "rating": 531}], "counters": [{"opponent": "metagross", "rating": 127}, {"opponent": "swampert", "rating": 243}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "giratina_origin", "rating": 440}, {"opponent": "mewtwo", "rating": 471}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43973}, {"moveId": "QUICK_ATTACK", "uses": 32527}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31124}, {"moveId": "THUNDERBOLT", "uses": 20730}, {"moveId": "DAZZLING_GLEAM", "uses": 15521}, {"moveId": "THUNDER", "uses": 9182}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "DAZZLING_GLEAM"], "score": 77.8}, {"speciesId": "mewtwo_armored", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Armored)", "rating": 640, "matchups": [{"opponent": "zacian_hero", "rating": 622}, {"opponent": "excadrill", "rating": 588}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 559, "opRating": 440}, {"opponent": "dragonite", "rating": 552}, {"opponent": "swampert", "rating": 546, "opRating": 453}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "giratina_origin", "rating": 292}, {"opponent": "dialga", "rating": 317}, {"opponent": "garcho<PERSON>", "rating": 354}, {"opponent": "gyarados", "rating": 412}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66253}, {"moveId": "IRON_TAIL", "uses": 10247}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 25580}, {"moveId": "ROCK_SLIDE", "uses": 18843}, {"moveId": "DYNAMIC_PUNCH", "uses": 15519}, {"moveId": "EARTHQUAKE", "uses": 11434}, {"moveId": "FUTURE_SIGHT", "uses": 5241}]}, "moveset": ["CONFUSION", "PSYSTRIKE", "DYNAMIC_PUNCH"], "score": 77.5}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 596, "matchups": [{"opponent": "metagross", "rating": 709}, {"opponent": "zacian_hero", "rating": 647}, {"opponent": "mewtwo", "rating": 599}, {"opponent": "grou<PERSON>", "rating": 575}, {"opponent": "ho_oh", "rating": 510, "opRating": 489}], "counters": [{"opponent": "giratina_origin", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "gyarados", "rating": 324}, {"opponent": "dragonite", "rating": 324}, {"opponent": "dialga", "rating": 489}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38831}, {"moveId": "WING_ATTACK", "uses": 37669}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26934}, {"moveId": "OVERHEAT", "uses": 17020}, {"moveId": "ANCIENT_POWER", "uses": 16537}, {"moveId": "RETURN", "uses": 8190}, {"moveId": "FIRE_BLAST", "uses": 4885}, {"moveId": "HEAT_WAVE", "uses": 2996}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 77.5}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 632, "matchups": [{"opponent": "giratina_origin", "rating": 813}, {"opponent": "excadrill", "rating": 709}, {"opponent": "giratina_altered", "rating": 688, "opRating": 311}, {"opponent": "swampert", "rating": 621}, {"opponent": "metagross", "rating": 528}], "counters": [{"opponent": "zacian_hero", "rating": 196}, {"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "gyarados", "rating": 376}, {"opponent": "lugia", "rating": 404}, {"opponent": "dialga", "rating": 445}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45300}, {"moveId": "TACKLE", "uses": 23820}, {"moveId": "LOW_KICK", "uses": 7374}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34086}, {"moveId": "STOMP", "uses": 22023}, {"moveId": "PAYBACK", "uses": 18055}, {"moveId": "DRAIN_PUNCH", "uses": 2361}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "PAYBACK"], "score": 77.3}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 600, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 625, "opRating": 374}, {"opponent": "zekrom", "rating": 618, "opRating": 381}, {"opponent": "lugia", "rating": 547}, {"opponent": "gyarados", "rating": 535, "opRating": 464}], "counters": [{"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "dialga", "rating": 342}, {"opponent": "mewtwo", "rating": 361}, {"opponent": "giratina_origin", "rating": 414}, {"opponent": "zacian_hero", "rating": 462}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 40275}, {"moveId": "MUD_SLAP", "uses": 36225}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 24525}, {"moveId": "SUPER_POWER", "uses": 15092}, {"moveId": "SURF", "uses": 12714}, {"moveId": "EARTHQUAKE", "uses": 11751}, {"moveId": "STONE_EDGE", "uses": 6954}, {"moveId": "SKULL_BASH", "uses": 5458}]}, "moveset": ["MUD_SLAP", "ROCK_WRECKER", "SURF"], "score": 77.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 637, "matchups": [{"opponent": "sylveon", "rating": 776, "opRating": 223}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 731, "opRating": 268}, {"opponent": "giratina_altered", "rating": 522, "opRating": 477}, {"opponent": "palkia", "rating": 517, "opRating": 482}, {"opponent": "zacian_hero", "rating": 514}], "counters": [{"opponent": "mewtwo", "rating": 315}, {"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "dialga", "rating": 396}, {"opponent": "lugia", "rating": 450}, {"opponent": "gyarados", "rating": 456}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 46791}, {"moveId": "CHARGE_BEAM", "uses": 29709}], "chargedMoves": [{"moveId": "DOOM_DESIRE", "uses": 45675}, {"moveId": "PSYCHIC", "uses": 20199}, {"moveId": "DAZZLING_GLEAM", "uses": 10553}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 77}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 609, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "gyarados", "rating": 685}, {"opponent": "zacian_hero", "rating": 641}, {"opponent": "lugia", "rating": 638}, {"opponent": "metagross", "rating": 632}], "counters": [{"opponent": "dialga", "rating": 203}, {"opponent": "giratina_origin", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "mewtwo", "rating": 375}, {"opponent": "excadrill", "rating": 460}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58288}, {"moveId": "ASTONISH", "uses": 18212}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25659}, {"moveId": "THUNDER_PUNCH", "uses": 23890}, {"moveId": "BRICK_BREAK", "uses": 19295}, {"moveId": "THUNDER", "uses": 7715}]}, "moveset": ["THUNDER_SHOCK", "CRUNCH", "THUNDER"], "score": 77}, {"speciesId": "suicune_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 599, "matchups": [{"opponent": "excadrill", "rating": 708}, {"opponent": "dragonite", "rating": 644}, {"opponent": "metagross", "rating": 587}, {"opponent": "grou<PERSON>", "rating": 579, "opRating": 420}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 552, "opRating": 447}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "giratina_origin", "rating": 342}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "garcho<PERSON>", "rating": 481}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7372}, {"moveId": "ICE_FANG", "uses": 6255}, {"moveId": "EXTRASENSORY", "uses": 4736}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4636}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4604}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4100}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3993}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3915}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3878}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3754}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3680}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3564}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3519}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3434}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3178}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3164}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3162}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2998}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2966}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31542}, {"moveId": "HYDRO_PUMP", "uses": 18284}, {"moveId": "BUBBLE_BEAM", "uses": 13532}, {"moveId": "WATER_PULSE", "uses": 13013}, {"moveId": "FRUSTRATION", "uses": 5}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 76.9}, {"speciesId": "walrein_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 557, "matchups": [{"opponent": "dragonite", "rating": 846}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 690, "opRating": 309}, {"opponent": "excadrill", "rating": 616}, {"opponent": "garcho<PERSON>", "rating": 572}, {"opponent": "giratina_origin", "rating": 539}], "counters": [{"opponent": "mewtwo", "rating": 278}, {"opponent": "lugia", "rating": 416}, {"opponent": "metagross", "rating": 450}, {"opponent": "gyarados", "rating": 456}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 38035}, {"moveId": "WATERFALL", "uses": 21211}, {"moveId": "FROST_BREATH", "uses": 17244}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 40043}, {"moveId": "EARTHQUAKE", "uses": 18400}, {"moveId": "WATER_PULSE", "uses": 9251}, {"moveId": "BLIZZARD", "uses": 8830}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 76.7}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 658, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 859}, {"opponent": "sylveon", "rating": 777, "opRating": 222}, {"opponent": "lugia", "rating": 722}, {"opponent": "gyarados", "rating": 703}, {"opponent": "ho_oh", "rating": 576, "opRating": 423}], "counters": [{"opponent": "zacian_hero", "rating": 210}, {"opponent": "dialga", "rating": 323}, {"opponent": "metagross", "rating": 389}, {"opponent": "dragonite", "rating": 390}, {"opponent": "mewtwo", "rating": 445}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47921}, {"moveId": "CHARGE_BEAM", "uses": 28579}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 41026}, {"moveId": "MIRROR_SHOT", "uses": 19023}, {"moveId": "FLASH_CANNON", "uses": 10173}, {"moveId": "ZAP_CANNON", "uses": 6381}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 76.4}, {"speciesId": "articuno_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 642, "matchups": [{"opponent": "gyarados", "rating": 647}, {"opponent": "swampert", "rating": 615}, {"opponent": "zacian_hero", "rating": 596}, {"opponent": "dragonite", "rating": 551}, {"opponent": "ho_oh", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "lugia", "rating": 304}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "garcho<PERSON>", "rating": 464}, {"opponent": "grou<PERSON>", "rating": 489}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 43826}, {"moveId": "CONFUSION", "uses": 32674}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 41524}, {"moveId": "ANCIENT_POWER", "uses": 18982}, {"moveId": "FUTURE_SIGHT", "uses": 16004}]}, "moveset": ["PSYCHO_CUT", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 76.1}, {"speciesId": "kyurem", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 618, "matchups": [{"opponent": "kyogre", "rating": 704, "opRating": 295}, {"opponent": "swampert", "rating": 658}, {"opponent": "giratina_origin", "rating": 573}, {"opponent": "lugia", "rating": 555}, {"opponent": "gyarados", "rating": 513}], "counters": [{"opponent": "metagross", "rating": 218}, {"opponent": "dialga", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "mewtwo", "rating": 408}, {"opponent": "dragonite", "rating": 465}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 56816}, {"moveId": "STEEL_WING", "uses": 19684}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 40956}, {"moveId": "BLIZZARD", "uses": 22114}, {"moveId": "DRACO_METEOR", "uses": 13667}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "BLIZZARD"], "score": 76.1}, {"speciesId": "regice", "speciesName": "Regice", "rating": 613, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 630, "opRating": 369}, {"opponent": "gyarados", "rating": 610}, {"opponent": "zekrom", "rating": 561, "opRating": 438}, {"opponent": "excadrill", "rating": 546}, {"opponent": "lugia", "rating": 540}], "counters": [{"opponent": "mewtwo", "rating": 231}, {"opponent": "metagross", "rating": 348}, {"opponent": "giratina_origin", "rating": 352}, {"opponent": "dialga", "rating": 355}, {"opponent": "garcho<PERSON>", "rating": 406}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42430}, {"moveId": "FROST_BREATH", "uses": 24541}, {"moveId": "ROCK_SMASH", "uses": 9541}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 25650}, {"moveId": "EARTHQUAKE", "uses": 19606}, {"moveId": "THUNDER", "uses": 16945}, {"moveId": "FOCUS_BLAST", "uses": 14221}]}, "moveset": ["LOCK_ON", "THUNDER", "EARTHQUAKE"], "score": 75.9}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 582, "matchups": [{"opponent": "giratina_origin", "rating": 732}, {"opponent": "gyarados", "rating": 689}, {"opponent": "garcho<PERSON>", "rating": 633}, {"opponent": "metagross", "rating": 532}, {"opponent": "excadrill", "rating": 515}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "mewtwo", "rating": 164}, {"opponent": "dragonite", "rating": 409}, {"opponent": "zacian_hero", "rating": 413}, {"opponent": "lugia", "rating": 445}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5916}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5585}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 5208}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5084}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 5065}, {"moveId": "HIDDEN_POWER_DARK", "uses": 5004}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4900}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4832}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4509}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4491}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4452}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4145}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4089}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4057}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3896}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3801}, {"moveId": "ZEN_HEADBUTT", "uses": 1265}], "chargedMoves": [{"moveId": "THUNDER", "uses": 27031}, {"moveId": "FOCUS_BLAST", "uses": 26084}, {"moveId": "GIGA_IMPACT", "uses": 23492}]}, "moveset": ["HIDDEN_POWER_ICE", "THUNDER", "FOCUS_BLAST"], "score": 75.9}, {"speciesId": "primarina", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 530, "matchups": [{"opponent": "yveltal", "rating": 761, "opRating": 238}, {"opponent": "dragonite", "rating": 738}, {"opponent": "gyarados", "rating": 642}, {"opponent": "dialga", "rating": 604}, {"opponent": "garcho<PERSON>", "rating": 578}], "counters": [{"opponent": "mewtwo", "rating": 291}, {"opponent": "swampert", "rating": 338}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "lugia", "rating": 395}, {"opponent": "giratina_origin", "rating": 448}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 38545}, {"moveId": "WATERFALL", "uses": 37955}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 37947}, {"moveId": "HYDRO_PUMP", "uses": 19677}, {"moveId": "PSYCHIC", "uses": 18769}]}, "moveset": ["CHARM", "MOONBLAST", "HYDRO_PUMP"], "score": 75.7}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 616, "matchups": [{"opponent": "mewtwo", "rating": 639}, {"opponent": "giratina_origin", "rating": 629}, {"opponent": "gyarados", "rating": 575}, {"opponent": "grou<PERSON>", "rating": 569}, {"opponent": "swampert", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "lugia", "rating": 366}, {"opponent": "metagross", "rating": 372}, {"opponent": "dragonite", "rating": 404}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39112}, {"moveId": "WING_ATTACK", "uses": 37388}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36310}, {"moveId": "PAYBACK", "uses": 23937}, {"moveId": "ANCIENT_POWER", "uses": 16407}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 75.3}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 655, "matchups": [{"opponent": "yveltal", "rating": 935, "opRating": 64}, {"opponent": "kyogre", "rating": 835, "opRating": 164}, {"opponent": "gyarados", "rating": 832}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 807, "opRating": 192}, {"opponent": "lugia", "rating": 585}], "counters": [{"opponent": "dialga", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "metagross", "rating": 401}, {"opponent": "dragonite", "rating": 438}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 68165}, {"moveId": "LOW_KICK", "uses": 8335}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28343}, {"moveId": "ICE_PUNCH", "uses": 19257}, {"moveId": "THUNDER_PUNCH", "uses": 13793}, {"moveId": "FLAMETHROWER", "uses": 10491}, {"moveId": "THUNDER", "uses": 4506}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 74.8}, {"speciesId": "walrein", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 739, "opRating": 260}, {"opponent": "excadrill", "rating": 667}, {"opponent": "garcho<PERSON>", "rating": 618}, {"opponent": "grou<PERSON>", "rating": 562, "opRating": 437}, {"opponent": "dragonite", "rating": 518}], "counters": [{"opponent": "mewtwo", "rating": 226}, {"opponent": "zacian_hero", "rating": 343}, {"opponent": "dialga", "rating": 415}, {"opponent": "metagross", "rating": 447}, {"opponent": "giratina_origin", "rating": 462}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 36682}, {"moveId": "WATERFALL", "uses": 21569}, {"moveId": "FROST_BREATH", "uses": 18245}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 35772}, {"moveId": "EARTHQUAKE", "uses": 16218}, {"moveId": "RETURN", "uses": 8627}, {"moveId": "WATER_PULSE", "uses": 8083}, {"moveId": "BLIZZARD", "uses": 7842}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 74.6}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 620, "matchups": [{"opponent": "gyarados", "rating": 843}, {"opponent": "sylveon", "rating": 642, "opRating": 357}, {"opponent": "lugia", "rating": 625}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622, "opRating": 377}, {"opponent": "yveltal", "rating": 602, "opRating": 397}], "counters": [{"opponent": "metagross", "rating": 313}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "dialga", "rating": 372}, {"opponent": "giratina_origin", "rating": 438}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40771}, {"moveId": "SPARK", "uses": 35729}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 28547}, {"moveId": "POWER_WHIP", "uses": 23105}, {"moveId": "DAZZLING_GLEAM", "uses": 14289}, {"moveId": "THUNDER", "uses": 10561}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "DAZZLING_GLEAM"], "score": 74.6}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 571, "matchups": [{"opponent": "excadrill", "rating": 649}, {"opponent": "giratina_origin", "rating": 636}, {"opponent": "grou<PERSON>", "rating": 619}, {"opponent": "gyarados", "rating": 592}, {"opponent": "swampert", "rating": 537, "opRating": 462}], "counters": [{"opponent": "metagross", "rating": 281}, {"opponent": "mewtwo", "rating": 296}, {"opponent": "dialga", "rating": 377}, {"opponent": "zacian_hero", "rating": 430}, {"opponent": "garcho<PERSON>", "rating": 474}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 51894}, {"moveId": "STEEL_WING", "uses": 24606}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 28723}, {"moveId": "CLOSE_COMBAT", "uses": 26572}, {"moveId": "ROCK_SLIDE", "uses": 17803}, {"moveId": "HEAT_WAVE", "uses": 3403}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 74.5}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 559, "matchups": [{"opponent": "excadrill", "rating": 674}, {"opponent": "snorlax", "rating": 580, "opRating": 419}, {"opponent": "zekrom", "rating": 553, "opRating": 446}, {"opponent": "metagross", "rating": 543}, {"opponent": "dialga", "rating": 516}], "counters": [{"opponent": "giratina_origin", "rating": 290}, {"opponent": "gyarados", "rating": 355}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "garcho<PERSON>", "rating": 427}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 27050}, {"moveId": "MUD_SLAP", "uses": 18422}, {"moveId": "CHARM", "uses": 15830}, {"moveId": "TACKLE", "uses": 15198}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30801}, {"moveId": "EARTHQUAKE", "uses": 22411}, {"moveId": "PLAY_ROUGH", "uses": 11854}, {"moveId": "HEAVY_SLAM", "uses": 11564}]}, "moveset": ["COUNTER", "BODY_SLAM", "EARTHQUAKE"], "score": 74.5}, {"speciesId": "machamp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 622, "matchups": [{"opponent": "darkrai", "rating": 951, "opRating": 48}, {"opponent": "ho_oh_shadow", "rating": 798, "opRating": 201}, {"opponent": "dialga", "rating": 680}, {"opponent": "snorlax", "rating": 650, "opRating": 349}, {"opponent": "excadrill", "rating": 618}], "counters": [{"opponent": "giratina_origin", "rating": 201}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "metagross", "rating": 302}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 30036}, {"moveId": "KARATE_CHOP", "uses": 27857}, {"moveId": "BULLET_PUNCH", "uses": 18590}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 21203}, {"moveId": "CROSS_CHOP", "uses": 14622}, {"moveId": "ROCK_SLIDE", "uses": 12554}, {"moveId": "PAYBACK", "uses": 9131}, {"moveId": "HEAVY_SLAM", "uses": 5822}, {"moveId": "DYNAMIC_PUNCH", "uses": 5707}, {"moveId": "STONE_EDGE", "uses": 4921}, {"moveId": "SUBMISSION", "uses": 2508}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 74.3}, {"speciesId": "articuno", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 551, "matchups": [{"opponent": "garcho<PERSON>", "rating": 696}, {"opponent": "dragonite", "rating": 672}, {"opponent": "grou<PERSON>", "rating": 626}, {"opponent": "giratina_origin", "rating": 561}, {"opponent": "gyarados", "rating": 551}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "zacian_hero", "rating": 476}, {"opponent": "lugia", "rating": 476}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 45640}, {"moveId": "FROST_BREATH", "uses": 30860}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 28497}, {"moveId": "ANCIENT_POWER", "uses": 13981}, {"moveId": "HURRICANE", "uses": 11579}, {"moveId": "ICE_BEAM", "uses": 9186}, {"moveId": "RETURN", "uses": 6863}, {"moveId": "BLIZZARD", "uses": 6412}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 74.2}, {"speciesId": "<PERSON><PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 594, "matchups": [{"opponent": "registeel", "rating": 971, "opRating": 28}, {"opponent": "dialga", "rating": 780}, {"opponent": "snorlax", "rating": 710, "opRating": 289}, {"opponent": "swampert", "rating": 671}, {"opponent": "excadrill", "rating": 648}], "counters": [{"opponent": "zacian_hero", "rating": 254}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "gyarados", "rating": 355}, {"opponent": "grou<PERSON>", "rating": 369}, {"opponent": "metagross", "rating": 395}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 47007}, {"moveId": "BULLET_PUNCH", "uses": 29493}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32326}, {"moveId": "SUPER_POWER", "uses": 23845}, {"moveId": "HEAVY_SLAM", "uses": 11700}, {"moveId": "DYNAMIC_PUNCH", "uses": 8646}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "SUPER_POWER"], "score": 74.2}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 633, "matchups": [{"opponent": "swampert", "rating": 662}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 662, "opRating": 337}, {"opponent": "snorlax", "rating": 654, "opRating": 345}, {"opponent": "giratina_origin", "rating": 539}, {"opponent": "excadrill", "rating": 511}], "counters": [{"opponent": "metagross", "rating": 293}, {"opponent": "lugia", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "dialga", "rating": 461}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31921}, {"moveId": "GUST", "uses": 22818}, {"moveId": "WING_ATTACK", "uses": 21740}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 39297}, {"moveId": "CLOSE_COMBAT", "uses": 32707}, {"moveId": "HEAT_WAVE", "uses": 4415}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 74.2}, {"speciesId": "suicune", "speciesName": "Suicune", "rating": 591, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 776, "opRating": 223}, {"opponent": "excadrill", "rating": 763}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 733, "opRating": 266}, {"opponent": "dragonite_shadow", "rating": 644, "opRating": 355}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 606, "opRating": 393}], "counters": [{"opponent": "dialga", "rating": 195}, {"opponent": "mewtwo", "rating": 372}, {"opponent": "garcho<PERSON>", "rating": 415}, {"opponent": "giratina_origin", "rating": 470}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7205}, {"moveId": "ICE_FANG", "uses": 6366}, {"moveId": "EXTRASENSORY", "uses": 4679}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4597}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4510}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4115}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4001}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3959}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3861}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3691}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3666}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3563}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3488}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3434}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3160}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3135}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3125}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3000}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2896}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26475}, {"moveId": "HYDRO_PUMP", "uses": 15218}, {"moveId": "RETURN", "uses": 12643}, {"moveId": "BUBBLE_BEAM", "uses": 11314}, {"moveId": "WATER_PULSE", "uses": 10905}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 74.2}, {"speciesId": "tyranitar", "speciesName": "Tyranitar", "rating": 611, "matchups": [{"opponent": "mewtwo", "rating": 810}, {"opponent": "gyarados", "rating": 666}, {"opponent": "lugia", "rating": 626}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 616}, {"opponent": "giratina_origin", "rating": 587}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "dragonite", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "swampert", "rating": 325}, {"opponent": "excadrill", "rating": 348}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 41839}, {"moveId": "BITE", "uses": 27338}, {"moveId": "IRON_TAIL", "uses": 7330}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 33526}, {"moveId": "STONE_EDGE", "uses": 25487}, {"moveId": "RETURN", "uses": 9051}, {"moveId": "FIRE_BLAST", "uses": 8418}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 74.2}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 628, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 856}, {"opponent": "gyarados", "rating": 773}, {"opponent": "lugia", "rating": 767}, {"opponent": "sylveon", "rating": 659, "opRating": 340}, {"opponent": "kyogre", "rating": 541, "opRating": 458}], "counters": [{"opponent": "giratina_origin", "rating": 205}, {"opponent": "dialga", "rating": 285}, {"opponent": "dragonite", "rating": 321}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "metagross", "rating": 343}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47520}, {"moveId": "CHARGE_BEAM", "uses": 28980}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37198}, {"moveId": "MIRROR_SHOT", "uses": 16867}, {"moveId": "FLASH_CANNON", "uses": 8816}, {"moveId": "RETURN", "uses": 7888}, {"moveId": "ZAP_CANNON", "uses": 5722}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 73.8}, {"speciesId": "sir<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 601, "matchups": [{"opponent": "swampert", "rating": 948}, {"opponent": "mamos<PERSON>_shadow", "rating": 879, "opRating": 120}, {"opponent": "dialga", "rating": 741}, {"opponent": "snorlax", "rating": 658, "opRating": 341}, {"opponent": "excadrill", "rating": 610}], "counters": [{"opponent": "zacian_hero", "rating": 239}, {"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "grou<PERSON>", "rating": 347}, {"opponent": "metagross", "rating": 389}, {"opponent": "gyarados", "rating": 479}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44908}, {"moveId": "FURY_CUTTER", "uses": 31592}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24565}, {"moveId": "LEAF_BLADE", "uses": 19514}, {"moveId": "NIGHT_SLASH", "uses": 17421}, {"moveId": "BRAVE_BIRD", "uses": 14975}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 73.8}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 594, "matchups": [{"opponent": "melmetal", "rating": 775, "opRating": 224}, {"opponent": "swampert", "rating": 691}, {"opponent": "dialga", "rating": 668}, {"opponent": "snorlax", "rating": 626, "opRating": 373}, {"opponent": "excadrill", "rating": 594}], "counters": [{"opponent": "mewtwo", "rating": 276}, {"opponent": "garcho<PERSON>", "rating": 291}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "dragonite", "rating": 377}, {"opponent": "gyarados", "rating": 422}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44835}, {"moveId": "CHARM", "uses": 21914}, {"moveId": "LOW_KICK", "uses": 9757}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31144}, {"moveId": "LEAF_BLADE", "uses": 25789}, {"moveId": "SYNCHRONOISE", "uses": 13430}, {"moveId": "PSYCHIC", "uses": 6188}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 73.7}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 607, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "snorlax", "rating": 691, "opRating": 308}, {"opponent": "kommo_o", "rating": 651, "opRating": 348}, {"opponent": "excadrill", "rating": 584, "opRating": 415}, {"opponent": "grou<PERSON>", "rating": 553, "opRating": 446}], "counters": [{"opponent": "garcho<PERSON>", "rating": 323}, {"opponent": "giratina_origin", "rating": 364}, {"opponent": "dialga", "rating": 377}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "mewtwo", "rating": 427}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30944}, {"moveId": "GUST", "uses": 23535}, {"moveId": "WING_ATTACK", "uses": 21951}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33305}, {"moveId": "CLOSE_COMBAT", "uses": 28805}, {"moveId": "RETURN", "uses": 10488}, {"moveId": "HEAT_WAVE", "uses": 3916}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 73.7}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 582, "matchups": [{"opponent": "heatran", "rating": 859, "opRating": 140}, {"opponent": "melmetal", "rating": 832, "opRating": 167}, {"opponent": "dialga", "rating": 774}, {"opponent": "excadrill", "rating": 697}, {"opponent": "snorlax", "rating": 693, "opRating": 306}], "counters": [{"opponent": "zacian_hero", "rating": 208}, {"opponent": "gyarados", "rating": 296}, {"opponent": "metagross", "rating": 383}, {"opponent": "garcho<PERSON>", "rating": 413}, {"opponent": "swampert", "rating": 447}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46985}, {"moveId": "BULLET_PUNCH", "uses": 29515}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29265}, {"moveId": "SUPER_POWER", "uses": 21760}, {"moveId": "HEAVY_SLAM", "uses": 10187}, {"moveId": "DYNAMIC_PUNCH", "uses": 7861}, {"moveId": "RETURN", "uses": 7387}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "HEAVY_SLAM"], "score": 73.5}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 574, "matchups": [{"opponent": "dragonite", "rating": 640}, {"opponent": "garcho<PERSON>", "rating": 636}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "zekrom", "rating": 589, "opRating": 410}, {"opponent": "excadrill", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "zacian_hero", "rating": 355}, {"opponent": "giratina_origin", "rating": 402}, {"opponent": "metagross", "rating": 421}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23404}, {"moveId": "FIRE_FANG", "uses": 19447}, {"moveId": "THUNDER_FANG", "uses": 19406}, {"moveId": "BITE", "uses": 14201}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 22783}, {"moveId": "BODY_SLAM", "uses": 19810}, {"moveId": "EARTH_POWER", "uses": 17380}, {"moveId": "EARTHQUAKE", "uses": 7524}, {"moveId": "STONE_EDGE", "uses": 6067}, {"moveId": "RETURN", "uses": 2890}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 73.4}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 558, "matchups": [{"opponent": "grou<PERSON>", "rating": 887}, {"opponent": "giratina_origin", "rating": 764}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 733, "opRating": 266}, {"opponent": "yveltal", "rating": 617, "opRating": 382}, {"opponent": "garcho<PERSON>", "rating": 516}], "counters": [{"opponent": "mewtwo", "rating": 281}, {"opponent": "dialga", "rating": 353}, {"opponent": "gyarados", "rating": 456}, {"opponent": "excadrill", "rating": 460}, {"opponent": "lugia", "rating": 464}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 11927}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5056}, {"moveId": "CHARGE_BEAM", "uses": 4521}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4326}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4128}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4047}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4014}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3927}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3828}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3751}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3705}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3692}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3670}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3385}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3346}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3343}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3173}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3067}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 20593}, {"moveId": "BLIZZARD", "uses": 16049}, {"moveId": "ZAP_CANNON", "uses": 13207}, {"moveId": "RETURN", "uses": 12990}, {"moveId": "SOLAR_BEAM", "uses": 8606}, {"moveId": "HYPER_BEAM", "uses": 5017}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 73.4}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "genesect_burn", "rating": 741, "opRating": 258}, {"opponent": "buzzwole", "rating": 690, "opRating": 309}, {"opponent": "metagross", "rating": 654}, {"opponent": "garcho<PERSON>", "rating": 517}, {"opponent": "grou<PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "zacian_hero", "rating": 312}, {"opponent": "giratina_origin", "rating": 362}, {"opponent": "dialga", "rating": 426}, {"opponent": "dragonite", "rating": 449}, {"opponent": "mewtwo", "rating": 489}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16904}, {"moveId": "DRAGON_BREATH", "uses": 16470}, {"moveId": "EMBER", "uses": 15608}, {"moveId": "WING_ATTACK", "uses": 15433}, {"moveId": "AIR_SLASH", "uses": 12121}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 29953}, {"moveId": "DRAGON_CLAW", "uses": 22479}, {"moveId": "RETURN", "uses": 6965}, {"moveId": "FLAMETHROWER", "uses": 6800}, {"moveId": "OVERHEAT", "uses": 6383}, {"moveId": "FIRE_BLAST", "uses": 3586}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 72.9}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 616, "matchups": [{"opponent": "metagross", "rating": 737}, {"opponent": "sylveon", "rating": 654, "opRating": 345}, {"opponent": "excadrill", "rating": 580}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 571}, {"opponent": "snorlax", "rating": 544, "opRating": 455}], "counters": [{"opponent": "giratina_origin", "rating": 193}, {"opponent": "garcho<PERSON>", "rating": 265}, {"opponent": "dialga", "rating": 317}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "mewtwo", "rating": 492}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 42868}, {"moveId": "FIRE_FANG", "uses": 33632}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 22139}, {"moveId": "OVERHEAT", "uses": 17083}, {"moveId": "IRON_HEAD", "uses": 12476}, {"moveId": "RETURN", "uses": 10702}, {"moveId": "FLAMETHROWER", "uses": 9149}, {"moveId": "FIRE_BLAST", "uses": 5005}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 72.9}, {"speciesId": "gengar", "speciesName": "Gengar", "rating": 577, "matchups": [{"opponent": "machamp_shadow", "rating": 873, "opRating": 126}, {"opponent": "metagross", "rating": 802}, {"opponent": "buzzwole", "rating": 774, "opRating": 225}, {"opponent": "metagross_shadow", "rating": 753, "opRating": 246}, {"opponent": "zacian_hero", "rating": 619}], "counters": [{"opponent": "giratina_origin", "rating": 268}, {"opponent": "garcho<PERSON>", "rating": 359}, {"opponent": "dialga", "rating": 391}, {"opponent": "gyarados", "rating": 404}, {"opponent": "lugia", "rating": 419}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 29005}, {"moveId": "HEX", "uses": 18818}, {"moveId": "LICK", "uses": 14925}, {"moveId": "SUCKER_PUNCH", "uses": 13818}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16632}, {"moveId": "SHADOW_PUNCH", "uses": 16495}, {"moveId": "SLUDGE_BOMB", "uses": 11753}, {"moveId": "DARK_PULSE", "uses": 10839}, {"moveId": "FOCUS_BLAST", "uses": 8834}, {"moveId": "PSYCHIC", "uses": 8082}, {"moveId": "SLUDGE_WAVE", "uses": 3963}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SHADOW_PUNCH"], "score": 72.4}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 566, "matchups": [{"opponent": "thundurus_therian", "rating": 950, "opRating": 49}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 661, "opRating": 338}, {"opponent": "dragonite", "rating": 591, "opRating": 408}, {"opponent": "garcho<PERSON>", "rating": 582}, {"opponent": "zekrom", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "giratina_origin", "rating": 466}, {"opponent": "metagross", "rating": 488}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23941}, {"moveId": "FIRE_FANG", "uses": 19784}, {"moveId": "THUNDER_FANG", "uses": 19330}, {"moveId": "BITE", "uses": 13375}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 23511}, {"moveId": "BODY_SLAM", "uses": 20722}, {"moveId": "EARTH_POWER", "uses": 18017}, {"moveId": "EARTHQUAKE", "uses": 7804}, {"moveId": "STONE_EDGE", "uses": 6336}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 72.4}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 582, "matchups": [{"opponent": "tyranitar", "rating": 827, "opRating": 172}, {"opponent": "tyranitar_shadow", "rating": 795, "opRating": 204}, {"opponent": "excadrill", "rating": 661}, {"opponent": "giratina_origin", "rating": 599}, {"opponent": "mewtwo", "rating": 516}], "counters": [{"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "metagross", "rating": 340}, {"opponent": "lugia", "rating": 342}, {"opponent": "dialga", "rating": 423}, {"opponent": "gyarados", "rating": 469}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34188}, {"moveId": "COUNTER", "uses": 30649}, {"moveId": "METAL_CLAW", "uses": 11709}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 38930}, {"moveId": "RETURN", "uses": 16505}, {"moveId": "PLAY_ROUGH", "uses": 14693}, {"moveId": "HYPER_BEAM", "uses": 6382}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "RETURN"], "score": 72.4}, {"speciesId": "tyranitar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "mewtwo", "rating": 781}, {"opponent": "lugia", "rating": 746}, {"opponent": "giratina_origin", "rating": 721}, {"opponent": "gyarados", "rating": 609}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 547}], "counters": [{"opponent": "garcho<PERSON>", "rating": 105}, {"opponent": "zacian_hero", "rating": 138}, {"opponent": "dialga", "rating": 146}, {"opponent": "dragonite", "rating": 305}, {"opponent": "excadrill", "rating": 409}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43608}, {"moveId": "BITE", "uses": 25867}, {"moveId": "IRON_TAIL", "uses": 6979}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37813}, {"moveId": "STONE_EDGE", "uses": 29066}, {"moveId": "FIRE_BLAST", "uses": 9624}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 72.2}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 590, "matchups": [{"opponent": "swampert", "rating": 838}, {"opponent": "kyogre", "rating": 835, "opRating": 164}, {"opponent": "grou<PERSON>", "rating": 686, "opRating": 313}, {"opponent": "gyarados", "rating": 577}, {"opponent": "excadrill", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "giratina_origin", "rating": 354}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "mewtwo", "rating": 403}, {"opponent": "garcho<PERSON>", "rating": 446}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44675}, {"moveId": "INFESTATION", "uses": 31825}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 24867}, {"moveId": "ROCK_SLIDE", "uses": 24491}, {"moveId": "SLUDGE_BOMB", "uses": 13072}, {"moveId": "ANCIENT_POWER", "uses": 8866}, {"moveId": "SOLAR_BEAM", "uses": 5344}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 72.1}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 609, "matchups": [{"opponent": "gyarados", "rating": 865}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 740}, {"opponent": "sylveon", "rating": 658, "opRating": 341}, {"opponent": "lugia", "rating": 655}, {"opponent": "zacian_hero", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "garcho<PERSON>", "rating": 288}, {"opponent": "metagross", "rating": 325}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "dragonite", "rating": 367}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 67022}, {"moveId": "LOW_KICK", "uses": 9478}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26487}, {"moveId": "ICE_PUNCH", "uses": 17905}, {"moveId": "THUNDER_PUNCH", "uses": 13001}, {"moveId": "FLAMETHROWER", "uses": 9811}, {"moveId": "RETURN", "uses": 5123}, {"moveId": "THUNDER", "uses": 4207}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 71.6}, {"speciesId": "milotic", "speciesName": "Milo<PERSON>", "rating": 566, "matchups": [{"opponent": "ho_oh", "rating": 610, "opRating": 389}, {"opponent": "swampert", "rating": 569, "opRating": 430}, {"opponent": "grou<PERSON>", "rating": 533, "opRating": 466}, {"opponent": "giratina_origin", "rating": 528}, {"opponent": "excadrill", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "garcho<PERSON>", "rating": 415}, {"opponent": "metagross", "rating": 415}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 40690}, {"moveId": "WATERFALL", "uses": 35810}], "chargedMoves": [{"moveId": "SURF", "uses": 46234}, {"moveId": "BLIZZARD", "uses": 20002}, {"moveId": "HYPER_BEAM", "uses": 10142}]}, "moveset": ["DRAGON_TAIL", "SURF", "BLIZZARD"], "score": 71.6}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 567, "matchups": [{"opponent": "giratina_origin", "rating": 720}, {"opponent": "dialga", "rating": 680}, {"opponent": "snorlax", "rating": 674, "opRating": 325}, {"opponent": "excadrill", "rating": 588}, {"opponent": "giratina_altered", "rating": 572, "opRating": 427}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "lugia", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "metagross", "rating": 398}, {"opponent": "swampert", "rating": 398}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34824}, {"moveId": "COUNTER", "uses": 30625}, {"moveId": "METAL_CLAW", "uses": 11044}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 43702}, {"moveId": "PLAY_ROUGH", "uses": 17086}, {"moveId": "HYPER_BEAM", "uses": 15434}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 71.4}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 592, "matchups": [{"opponent": "tyranitar", "rating": 870, "opRating": 129}, {"opponent": "terrakion", "rating": 746, "opRating": 253}, {"opponent": "swampert", "rating": 733}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 698, "opRating": 301}, {"opponent": "excadrill", "rating": 646}], "counters": [{"opponent": "mewtwo", "rating": 231}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "dragonite", "rating": 353}, {"opponent": "garcho<PERSON>", "rating": 410}, {"opponent": "dialga", "rating": 478}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43029}, {"moveId": "CHARM", "uses": 22897}, {"moveId": "LOW_KICK", "uses": 10522}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29157}, {"moveId": "LEAF_BLADE", "uses": 23880}, {"moveId": "SYNCHRONOISE", "uses": 12177}, {"moveId": "RETURN", "uses": 5617}, {"moveId": "PSYCHIC", "uses": 5512}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 71.3}, {"speciesId": "articuno_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 541, "matchups": [{"opponent": "dragonite", "rating": 658}, {"opponent": "garcho<PERSON>", "rating": 655}, {"opponent": "swampert", "rating": 639}, {"opponent": "grou<PERSON>", "rating": 561, "opRating": 438}, {"opponent": "yveltal", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 271}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "gyarados", "rating": 386}, {"opponent": "giratina_origin", "rating": 436}, {"opponent": "lugia", "rating": 490}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46491}, {"moveId": "FROST_BREATH", "uses": 30009}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31073}, {"moveId": "ANCIENT_POWER", "uses": 15598}, {"moveId": "HURRICANE", "uses": 12990}, {"moveId": "ICE_BEAM", "uses": 10013}, {"moveId": "BLIZZARD", "uses": 7016}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 71}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 569, "matchups": [{"opponent": "swampert", "rating": 858}, {"opponent": "excadrill", "rating": 808}, {"opponent": "kyogre", "rating": 778, "opRating": 221}, {"opponent": "gyarados", "rating": 616}, {"opponent": "grou<PERSON>", "rating": 592}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "metagross", "rating": 247}, {"opponent": "mewtwo", "rating": 434}, {"opponent": "zacian_hero", "rating": 468}, {"opponent": "garcho<PERSON>", "rating": 469}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 29670}, {"moveId": "CONFUSION", "uses": 29604}, {"moveId": "CHARGE_BEAM", "uses": 17246}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 22034}, {"moveId": "PSYCHIC", "uses": 20086}, {"moveId": "LEAF_STORM", "uses": 15072}, {"moveId": "DAZZLING_GLEAM", "uses": 11100}, {"moveId": "HYPER_BEAM", "uses": 8137}]}, "moveset": ["CONFUSION", "SEED_BOMB", "LEAF_STORM"], "score": 70.8}, {"speciesId": "hoopa_unbound", "speciesName": "<PERSON><PERSON><PERSON> (Unbound)", "rating": 567, "matchups": [{"opponent": "swampert", "rating": 697}, {"opponent": "mewtwo", "rating": 652}, {"opponent": "excadrill", "rating": 624}, {"opponent": "gyarados", "rating": 592}, {"opponent": "giratina_origin", "rating": 538}], "counters": [{"opponent": "dialga", "rating": 130}, {"opponent": "zacian_hero", "rating": 196}, {"opponent": "dragonite", "rating": 212}, {"opponent": "lugia", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 464}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56583}, {"moveId": "ASTONISH", "uses": 19917}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 30852}, {"moveId": "SHADOW_BALL", "uses": 23159}, {"moveId": "PSYCHIC", "uses": 22435}]}, "moveset": ["CONFUSION", "DARK_PULSE", "SHADOW_BALL"], "score": 70.2}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 567, "matchups": [{"opponent": "tapu_lele", "rating": 721, "opRating": 278}, {"opponent": "mewtwo", "rating": 705}, {"opponent": "mewtwo_shadow", "rating": 688, "opRating": 311}, {"opponent": "sylveon", "rating": 567, "opRating": 432}, {"opponent": "zacian_hero", "rating": 564}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "lugia", "rating": 361}, {"opponent": "metagross", "rating": 375}, {"opponent": "gyarados", "rating": 414}, {"opponent": "giratina_origin", "rating": 422}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 39103}, {"moveId": "POISON_STING", "uses": 37397}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17979}, {"moveId": "DARK_PULSE", "uses": 17809}, {"moveId": "ICE_BEAM", "uses": 13688}, {"moveId": "SHADOW_BALL", "uses": 13485}, {"moveId": "SLUDGE_BOMB", "uses": 13432}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 70.2}, {"speciesId": "salamence", "speciesName": "Salamence", "rating": 573, "matchups": [{"opponent": "grou<PERSON>", "rating": 773}, {"opponent": "swampert", "rating": 706}, {"opponent": "giratina_origin", "rating": 554}, {"opponent": "ho_oh", "rating": 523, "opRating": 476}, {"opponent": "gyarados", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "metagross", "rating": 351}, {"opponent": "lugia", "rating": 376}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43109}, {"moveId": "FIRE_FANG", "uses": 19956}, {"moveId": "BITE", "uses": 13345}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 29307}, {"moveId": "RETURN", "uses": 13026}, {"moveId": "FIRE_BLAST", "uses": 12801}, {"moveId": "HYDRO_PUMP", "uses": 12567}, {"moveId": "DRACO_METEOR", "uses": 8800}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "RETURN"], "score": 70.2}, {"speciesId": "machamp", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 597, "matchups": [{"opponent": "tyranitar", "rating": 927, "opRating": 72}, {"opponent": "melmetal", "rating": 803, "opRating": 196}, {"opponent": "snorlax", "rating": 680, "opRating": 319}, {"opponent": "excadrill", "rating": 674}, {"opponent": "yveltal", "rating": 513, "opRating": 486}], "counters": [{"opponent": "metagross", "rating": 258}, {"opponent": "dragonite", "rating": 377}, {"opponent": "gyarados", "rating": 404}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dialga", "rating": 448}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 30038}, {"moveId": "KARATE_CHOP", "uses": 27473}, {"moveId": "BULLET_PUNCH", "uses": 19092}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20231}, {"moveId": "CROSS_CHOP", "uses": 13908}, {"moveId": "ROCK_SLIDE", "uses": 11831}, {"moveId": "PAYBACK", "uses": 8599}, {"moveId": "HEAVY_SLAM", "uses": 5455}, {"moveId": "DYNAMIC_PUNCH", "uses": 5339}, {"moveId": "STONE_EDGE", "uses": 4672}, {"moveId": "RETURN", "uses": 4027}, {"moveId": "SUBMISSION", "uses": 2456}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 70}, {"speciesId": "pangoro", "speciesName": "Pangoro", "rating": 559, "matchups": [{"opponent": "mewtwo", "rating": 708}, {"opponent": "excadrill", "rating": 677}, {"opponent": "mewtwo_shadow", "rating": 639, "opRating": 360}, {"opponent": "giratina_altered", "rating": 603, "opRating": 396}, {"opponent": "giratina_origin", "rating": 559}], "counters": [{"opponent": "metagross", "rating": 281}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "lugia", "rating": 295}, {"opponent": "gyarados", "rating": 345}, {"opponent": "dialga", "rating": 432}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42612}, {"moveId": "BULLET_PUNCH", "uses": 27975}, {"moveId": "LOW_KICK", "uses": 5947}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29029}, {"moveId": "NIGHT_SLASH", "uses": 26578}, {"moveId": "ROCK_SLIDE", "uses": 14192}, {"moveId": "IRON_HEAD", "uses": 6620}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 70}, {"speciesId": "vaporeon", "speciesName": "Vaporeon", "rating": 576, "matchups": [{"opponent": "excadrill", "rating": 722}, {"opponent": "ho_oh", "rating": 587, "opRating": 412}, {"opponent": "metagross", "rating": 579}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 522, "opRating": 477}, {"opponent": "grou<PERSON>", "rating": 516}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "gyarados", "rating": 322}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 76500}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38185}, {"moveId": "LAST_RESORT", "uses": 14540}, {"moveId": "SCALD", "uses": 13516}, {"moveId": "HYDRO_PUMP", "uses": 6023}, {"moveId": "WATER_PULSE", "uses": 4336}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "LAST_RESORT"], "score": 70}, {"speciesId": "gigalith", "speciesName": "Gigalith", "rating": 578, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 764, "opRating": 235}, {"opponent": "ho_oh", "rating": 713, "opRating": 286}, {"opponent": "ho_oh_shadow", "rating": 654, "opRating": 345}, {"opponent": "gyarados", "rating": 592}, {"opponent": "snorlax", "rating": 539, "opRating": 460}], "counters": [{"opponent": "dialga", "rating": 353}, {"opponent": "giratina_origin", "rating": 354}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "mewtwo", "rating": 406}, {"opponent": "lugia", "rating": 480}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43909}, {"moveId": "MUD_SLAP", "uses": 32591}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 31638}, {"moveId": "SUPER_POWER", "uses": 26362}, {"moveId": "HEAVY_SLAM", "uses": 10662}, {"moveId": "SOLAR_BEAM", "uses": 7781}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "SUPER_POWER"], "score": 69.9}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 565, "matchups": [{"opponent": "swampert", "rating": 860}, {"opponent": "kyogre", "rating": 706, "opRating": 293}, {"opponent": "excadrill", "rating": 611}, {"opponent": "gyarados", "rating": 606}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 562, "opRating": 437}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "metagross", "rating": 351}, {"opponent": "garcho<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44438}, {"moveId": "INFESTATION", "uses": 32062}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22313}, {"moveId": "ROCK_SLIDE", "uses": 21939}, {"moveId": "SLUDGE_BOMB", "uses": 11547}, {"moveId": "RETURN", "uses": 8109}, {"moveId": "ANCIENT_POWER", "uses": 7841}, {"moveId": "SOLAR_BEAM", "uses": 4751}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 69.9}, {"speciesId": "haxorus", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 595, "matchups": [{"opponent": "darkrai", "rating": 873, "opRating": 126}, {"opponent": "kyogre", "rating": 756, "opRating": 243}, {"opponent": "snorlax", "rating": 626, "opRating": 373}, {"opponent": "excadrill", "rating": 617}, {"opponent": "swampert", "rating": 572, "opRating": 427}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "garcho<PERSON>", "rating": 323}, {"opponent": "metagross", "rating": 328}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "dialga", "rating": 402}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 38348}, {"moveId": "DRAGON_TAIL", "uses": 38152}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 25656}, {"moveId": "NIGHT_SLASH", "uses": 22519}, {"moveId": "SURF", "uses": 17711}, {"moveId": "EARTHQUAKE", "uses": 10637}]}, "moveset": ["COUNTER", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 69.7}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 573, "matchups": [{"opponent": "swampert", "rating": 858}, {"opponent": "excadrill", "rating": 817}, {"opponent": "kyogre", "rating": 711, "opRating": 288}, {"opponent": "gyarados", "rating": 614}, {"opponent": "grou<PERSON>", "rating": 532}], "counters": [{"opponent": "giratina_origin", "rating": 221}, {"opponent": "metagross", "rating": 238}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "dialga", "rating": 331}, {"opponent": "garcho<PERSON>", "rating": 474}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 43502}, {"moveId": "SMACK_DOWN", "uses": 24646}, {"moveId": "LOW_KICK", "uses": 8355}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 40686}, {"moveId": "ENERGY_BALL", "uses": 19863}, {"moveId": "GYRO_BALL", "uses": 10188}, {"moveId": "SOLAR_BEAM", "uses": 5798}]}, "moveset": ["VINE_WHIP", "SUPER_POWER", "ENERGY_BALL"], "score": 69.5}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 553, "matchups": [{"opponent": "giratina_origin", "rating": 769}, {"opponent": "grou<PERSON>", "rating": 691}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 691, "opRating": 308}, {"opponent": "sylveon", "rating": 606, "opRating": 393}, {"opponent": "excadrill", "rating": 556}], "counters": [{"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "metagross", "rating": 276}, {"opponent": "lugia", "rating": 407}, {"opponent": "dialga", "rating": 413}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 12974}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5000}, {"moveId": "CHARGE_BEAM", "uses": 4679}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4293}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4018}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3959}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3949}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3861}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3804}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3653}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3612}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3605}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3563}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3346}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3287}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3272}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3089}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2984}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 23072}, {"moveId": "BLIZZARD", "uses": 17612}, {"moveId": "ZAP_CANNON", "uses": 14702}, {"moveId": "HYPER_BEAM", "uses": 11280}, {"moveId": "SOLAR_BEAM", "uses": 9648}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 69.5}, {"speciesId": "lap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 525, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 714, "opRating": 285}, {"opponent": "articuno_galarian", "rating": 712, "opRating": 287}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 673, "opRating": 326}, {"opponent": "mamos<PERSON>_shadow", "rating": 663, "opRating": 336}, {"opponent": "garcho<PERSON>", "rating": 600}], "counters": [{"opponent": "dialga", "rating": 317}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "metagross", "rating": 360}, {"opponent": "giratina_origin", "rating": 474}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 31239}, {"moveId": "WATER_GUN", "uses": 25092}, {"moveId": "FROST_BREATH", "uses": 20225}], "chargedMoves": [{"moveId": "SURF", "uses": 27767}, {"moveId": "ICE_BEAM", "uses": 20568}, {"moveId": "SKULL_BASH", "uses": 8567}, {"moveId": "DRAGON_PULSE", "uses": 7894}, {"moveId": "BLIZZARD", "uses": 7118}, {"moveId": "HYDRO_PUMP", "uses": 4376}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 69.1}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 569, "matchups": [{"opponent": "metagross", "rating": 880}, {"opponent": "metagross_shadow", "rating": 857, "opRating": 142}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 821, "opRating": 178}, {"opponent": "sylveon", "rating": 604, "opRating": 395}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 388}, {"opponent": "gyarados", "rating": 391}, {"opponent": "lugia", "rating": 407}, {"opponent": "zacian_hero", "rating": 424}, {"opponent": "mewtwo", "rating": 463}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 30331}, {"moveId": "SHADOW_CLAW", "uses": 29063}, {"moveId": "EMBER", "uses": 17051}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 41687}, {"moveId": "RETURN", "uses": 10672}, {"moveId": "SOLAR_BEAM", "uses": 10117}, {"moveId": "OVERHEAT", "uses": 8871}, {"moveId": "FIRE_BLAST", "uses": 5149}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 69.1}, {"speciesId": "feraligatr_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 549, "matchups": [{"opponent": "heatran", "rating": 901, "opRating": 98}, {"opponent": "ho_oh_shadow", "rating": 882, "opRating": 117}, {"opponent": "metagross_shadow", "rating": 831, "opRating": 168}, {"opponent": "grou<PERSON>", "rating": 814}, {"opponent": "excadrill", "rating": 640}], "counters": [{"opponent": "dialga", "rating": 255}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "metagross", "rating": 389}, {"opponent": "mewtwo", "rating": 463}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22522}, {"moveId": "WATERFALL", "uses": 21846}, {"moveId": "ICE_FANG", "uses": 20433}, {"moveId": "BITE", "uses": 11825}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37393}, {"moveId": "CRUNCH", "uses": 19324}, {"moveId": "ICE_BEAM", "uses": 15638}, {"moveId": "HYDRO_PUMP", "uses": 4020}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 68.7}, {"speciesId": "golisopod", "speciesName": "Golisopod", "rating": 534, "matchups": [{"opponent": "swampert", "rating": 689, "opRating": 310}, {"opponent": "buzzwole", "rating": 667, "opRating": 332}, {"opponent": "mewtwo", "rating": 609}, {"opponent": "mewtwo_shadow", "rating": 551, "opRating": 448}, {"opponent": "kyogre", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "giratina_origin", "rating": 358}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25961}, {"moveId": "FURY_CUTTER", "uses": 21347}, {"moveId": "WATERFALL", "uses": 18885}, {"moveId": "METAL_CLAW", "uses": 10298}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 38620}, {"moveId": "AERIAL_ACE", "uses": 19143}, {"moveId": "AQUA_JET", "uses": 18769}]}, "moveset": ["SHADOW_CLAW", "X_SCISSOR", "AERIAL_ACE"], "score": 68.6}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 574, "matchups": [{"opponent": "sylveon", "rating": 747, "opRating": 252}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 713}, {"opponent": "zacian_hero", "rating": 637}, {"opponent": "giratina_origin", "rating": 562}, {"opponent": "mewtwo", "rating": 543}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "lugia", "rating": 273}, {"opponent": "metagross", "rating": 299}, {"opponent": "dragonite", "rating": 353}, {"opponent": "gyarados", "rating": 365}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29543}, {"moveId": "SNARL", "uses": 28303}, {"moveId": "BITE", "uses": 18642}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 43638}, {"moveId": "SLUDGE_WAVE", "uses": 19044}, {"moveId": "GUNK_SHOT", "uses": 7380}, {"moveId": "ACID_SPRAY", "uses": 6443}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "SLUDGE_WAVE"], "score": 68.6}, {"speciesId": "charizard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 532, "matchups": [{"opponent": "tapu_bulu", "rating": 815, "opRating": 184}, {"opponent": "buzzwole", "rating": 633, "opRating": 366}, {"opponent": "metagross", "rating": 604}, {"opponent": "grou<PERSON>", "rating": 592, "opRating": 407}, {"opponent": "yveltal", "rating": 517, "opRating": 482}], "counters": [{"opponent": "giratina_origin", "rating": 302}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "dragonite", "rating": 457}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17210}, {"moveId": "DRAGON_BREATH", "uses": 16258}, {"moveId": "WING_ATTACK", "uses": 15762}, {"moveId": "EMBER", "uses": 15366}, {"moveId": "AIR_SLASH", "uses": 11891}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 32758}, {"moveId": "DRAGON_CLAW", "uses": 25114}, {"moveId": "FLAMETHROWER", "uses": 7495}, {"moveId": "OVERHEAT", "uses": 7114}, {"moveId": "FIRE_BLAST", "uses": 4099}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 68.3}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 525, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 748, "opRating": 251}, {"opponent": "garcho<PERSON>", "rating": 669}, {"opponent": "dragonite", "rating": 548}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 510, "opRating": 489}, {"opponent": "grou<PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "mewtwo", "rating": 296}, {"opponent": "metagross", "rating": 337}, {"opponent": "giratina_origin", "rating": 398}, {"opponent": "gyarados", "rating": 417}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 30410}, {"moveId": "WATER_GUN", "uses": 26038}, {"moveId": "FROST_BREATH", "uses": 20027}], "chargedMoves": [{"moveId": "SURF", "uses": 25526}, {"moveId": "ICE_BEAM", "uses": 18996}, {"moveId": "SKULL_BASH", "uses": 7696}, {"moveId": "DRAGON_PULSE", "uses": 7154}, {"moveId": "BLIZZARD", "uses": 6645}, {"moveId": "RETURN", "uses": 6501}, {"moveId": "HYDRO_PUMP", "uses": 4131}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 68.3}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 521, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 765, "opRating": 234}, {"opponent": "latias", "rating": 744, "opRating": 255}, {"opponent": "kommo_o", "rating": 708, "opRating": 291}, {"opponent": "excadrill", "rating": 577}, {"opponent": "garcho<PERSON>", "rating": 567}], "counters": [{"opponent": "dialga", "rating": 323}, {"opponent": "lugia", "rating": 380}, {"opponent": "gyarados", "rating": 414}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "giratina_origin", "rating": 490}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47936}, {"moveId": "FROST_BREATH", "uses": 28564}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38911}, {"moveId": "ICY_WIND", "uses": 13522}, {"moveId": "LAST_RESORT", "uses": 9567}, {"moveId": "ICE_BEAM", "uses": 8742}, {"moveId": "WATER_PULSE", "uses": 5685}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 68.1}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 532, "matchups": [{"opponent": "excadrill", "rating": 774}, {"opponent": "magnezone", "rating": 734, "opRating": 265}, {"opponent": "magnezone_shadow", "rating": 716, "opRating": 283}, {"opponent": "nihilego", "rating": 689, "opRating": 310}, {"opponent": "raikou_shadow", "rating": 618, "opRating": 381}], "counters": [{"opponent": "mewtwo", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 345}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "dialga", "rating": 451}, {"opponent": "metagross", "rating": 473}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38727}, {"moveId": "WING_ATTACK", "uses": 37773}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 27758}, {"moveId": "EARTHQUAKE", "uses": 17186}, {"moveId": "AERIAL_ACE", "uses": 15279}, {"moveId": "SAND_TOMB", "uses": 8343}, {"moveId": "RETURN", "uses": 7862}]}, "moveset": ["WING_ATTACK", "NIGHT_SLASH", "EARTHQUAKE"], "score": 68.1}, {"speciesId": "golem_alolan", "speciesName": "Golem (Alolan)", "rating": 582, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 781}, {"opponent": "ho_oh", "rating": 781, "opRating": 218}, {"opponent": "zap<PERSON>_galarian", "rating": 773, "opRating": 226}, {"opponent": "lugia", "rating": 691}, {"opponent": "gyarados", "rating": 633}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "metagross", "rating": 316}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "zacian_hero", "rating": 462}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 34394}, {"moveId": "ROLLOUT", "uses": 24197}, {"moveId": "ROCK_THROW", "uses": 17961}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37076}, {"moveId": "STONE_EDGE", "uses": 20640}, {"moveId": "ROCK_BLAST", "uses": 18769}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "STONE_EDGE"], "score": 68.1}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 552, "matchups": [{"opponent": "gyarados", "rating": 725}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 680}, {"opponent": "dialga", "rating": 586}, {"opponent": "yveltal", "rating": 508, "opRating": 491}, {"opponent": "kyogre", "rating": 505, "opRating": 494}], "counters": [{"opponent": "giratina_origin", "rating": 203}, {"opponent": "metagross", "rating": 220}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "lugia", "rating": 385}, {"opponent": "zacian_hero", "rating": 393}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 52026}, {"moveId": "CHARGE_BEAM", "uses": 24474}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 21737}, {"moveId": "FOCUS_BLAST", "uses": 13037}, {"moveId": "DRAGON_PULSE", "uses": 11068}, {"moveId": "RETURN", "uses": 8789}, {"moveId": "POWER_GEM", "uses": 8016}, {"moveId": "THUNDER", "uses": 7002}, {"moveId": "ZAP_CANNON", "uses": 6805}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 67.8}, {"speciesId": "latias_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "kyogre", "rating": 738, "opRating": 261}, {"opponent": "swampert", "rating": 633}, {"opponent": "ho_oh", "rating": 549, "opRating": 450}, {"opponent": "grou<PERSON>", "rating": 526, "opRating": 473}, {"opponent": "yveltal", "rating": 505, "opRating": 494}], "counters": [{"opponent": "mewtwo", "rating": 195}, {"opponent": "dialga", "rating": 244}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "lugia", "rating": 376}, {"opponent": "metagross", "rating": 415}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47347}, {"moveId": "CHARM", "uses": 22882}, {"moveId": "ZEN_HEADBUTT", "uses": 6256}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 24405}, {"moveId": "OUTRAGE", "uses": 22058}, {"moveId": "PSYCHIC", "uses": 16225}, {"moveId": "THUNDER", "uses": 13740}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 67.8}, {"speciesId": "samu<PERSON>t", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 541, "matchups": [{"opponent": "golem", "rating": 948, "opRating": 51}, {"opponent": "weavile_shadow", "rating": 876, "opRating": 123}, {"opponent": "landorus_incarnate", "rating": 693, "opRating": 306}, {"opponent": "mamos<PERSON>_shadow", "rating": 688, "opRating": 311}, {"opponent": "excadrill", "rating": 644}], "counters": [{"opponent": "dialga", "rating": 274}, {"opponent": "zacian_hero", "rating": 401}, {"opponent": "mewtwo", "rating": 427}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 43478}, {"moveId": "WATERFALL", "uses": 33022}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 33886}, {"moveId": "RAZOR_SHELL", "uses": 14297}, {"moveId": "MEGAHORN", "uses": 13757}, {"moveId": "BLIZZARD", "uses": 10949}, {"moveId": "HYDRO_PUMP", "uses": 3551}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "RAZOR_SHELL"], "score": 67.8}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 579, "matchups": [{"opponent": "metagross", "rating": 859}, {"opponent": "metagross_shadow", "rating": 859, "opRating": 140}, {"opponent": "buzzwole", "rating": 746, "opRating": 253}, {"opponent": "zacian_hero", "rating": 697}, {"opponent": "sylveon", "rating": 566, "opRating": 433}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "dragonite", "rating": 359}, {"opponent": "gyarados", "rating": 371}, {"opponent": "dialga", "rating": 391}, {"opponent": "lugia", "rating": 392}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34109}, {"moveId": "HEX", "uses": 23814}, {"moveId": "FIRE_SPIN", "uses": 18640}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26474}, {"moveId": "FLAME_CHARGE", "uses": 20829}, {"moveId": "OVERHEAT", "uses": 16088}, {"moveId": "ENERGY_BALL", "uses": 13060}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 67.6}, {"speciesId": "feraligatr", "speciesName": "Feraligatr", "rating": 541, "matchups": [{"opponent": "entei_shadow", "rating": 901, "opRating": 98}, {"opponent": "excadrill", "rating": 688}, {"opponent": "metagross", "rating": 657}, {"opponent": "ho_oh", "rating": 530, "opRating": 469}, {"opponent": "sylveon", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "giratina_origin", "rating": 266}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "zacian_hero", "rating": 378}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22905}, {"moveId": "WATERFALL", "uses": 21492}, {"moveId": "ICE_FANG", "uses": 20407}, {"moveId": "BITE", "uses": 11735}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 34410}, {"moveId": "CRUNCH", "uses": 17796}, {"moveId": "ICE_BEAM", "uses": 14294}, {"moveId": "RETURN", "uses": 6370}, {"moveId": "HYDRO_PUMP", "uses": 3698}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 67.6}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 542, "matchups": [{"opponent": "mewtwo_shadow", "rating": 811, "opRating": 188}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 809, "opRating": 190}, {"opponent": "victini", "rating": 786, "opRating": 213}, {"opponent": "giratina_origin", "rating": 716}, {"opponent": "mewtwo", "rating": 644}], "counters": [{"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "gyarados", "rating": 304}, {"opponent": "dialga", "rating": 377}, {"opponent": "metagross", "rating": 380}, {"opponent": "lugia", "rating": 433}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28412}, {"moveId": "DOUBLE_KICK", "uses": 27411}, {"moveId": "FIRE_FANG", "uses": 20696}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37378}, {"moveId": "FLAME_CHARGE", "uses": 31965}, {"moveId": "FIRE_BLAST", "uses": 7096}]}, "moveset": ["SNARL", "DARK_PULSE", "FIRE_BLAST"], "score": 67.6}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 542, "matchups": [{"opponent": "tapu_lele", "rating": 823, "opRating": 176}, {"opponent": "metagross_shadow", "rating": 803, "opRating": 196}, {"opponent": "latias", "rating": 719, "opRating": 280}, {"opponent": "swampert", "rating": 657, "opRating": 342}, {"opponent": "kyogre", "rating": 623, "opRating": 376}], "counters": [{"opponent": "mewtwo", "rating": 265}, {"opponent": "dialga", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 438}, {"opponent": "metagross", "rating": 465}, {"opponent": "zacian_hero", "rating": 491}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 50282}, {"moveId": "SUCKER_PUNCH", "uses": 26218}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 29963}, {"moveId": "SEED_BOMB", "uses": 24033}, {"moveId": "FOUL_PLAY", "uses": 22520}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SEED_BOMB"], "score": 67.6}, {"speciesId": "beartic", "speciesName": "Bear<PERSON>", "rating": 522, "matchups": [{"opponent": "staraptor_shadow", "rating": 909, "opRating": 90}, {"opponent": "dragonite", "rating": 829}, {"opponent": "dragonite_shadow", "rating": 801, "opRating": 198}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 675, "opRating": 324}, {"opponent": "garcho<PERSON>", "rating": 561}], "counters": [{"opponent": "dialga", "rating": 239}, {"opponent": "mewtwo", "rating": 273}, {"opponent": "lugia", "rating": 407}, {"opponent": "gyarados", "rating": 427}, {"opponent": "excadrill", "rating": 434}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52539}, {"moveId": "CHARM", "uses": 23961}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 36204}, {"moveId": "SURF", "uses": 27983}, {"moveId": "PLAY_ROUGH", "uses": 12294}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "SURF"], "score": 67.5}, {"speciesId": "sci<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 535, "matchups": [{"opponent": "sylveon", "rating": 824, "opRating": 175}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 808, "opRating": 191}, {"opponent": "tapu_bulu", "rating": 808, "opRating": 191}, {"opponent": "florges", "rating": 694, "opRating": 305}, {"opponent": "zacian_hero", "rating": 554}], "counters": [{"opponent": "dialga", "rating": 347}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "lugia", "rating": 388}, {"opponent": "gyarados", "rating": 407}, {"opponent": "mewtwo", "rating": 416}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38460}, {"moveId": "BULLET_PUNCH", "uses": 38040}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 34168}, {"moveId": "X_SCISSOR", "uses": 25201}, {"moveId": "IRON_HEAD", "uses": 17049}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "IRON_HEAD"], "score": 67}, {"speciesId": "blastoise", "speciesName": "Blastoise", "rating": 516, "matchups": [{"opponent": "moltres_shadow", "rating": 897, "opRating": 102}, {"opponent": "mamos<PERSON>_shadow", "rating": 732, "opRating": 267}, {"opponent": "excadrill", "rating": 697}, {"opponent": "garcho<PERSON>", "rating": 570}, {"opponent": "ho_oh", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 252}, {"opponent": "giratina_origin", "rating": 280}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "metagross", "rating": 453}, {"opponent": "dragonite", "rating": 481}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 50618}, {"moveId": "BITE", "uses": 25882}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37021}, {"moveId": "ICE_BEAM", "uses": 15579}, {"moveId": "SKULL_BASH", "uses": 8005}, {"moveId": "RETURN", "uses": 6678}, {"moveId": "FLASH_CANNON", "uses": 5265}, {"moveId": "HYDRO_PUMP", "uses": 4034}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 66.8}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 568, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 859, "opRating": 140}, {"opponent": "mamos<PERSON>_shadow", "rating": 820, "opRating": 179}, {"opponent": "sylveon", "rating": 710, "opRating": 289}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 662, "opRating": 337}, {"opponent": "excadrill", "rating": 502}], "counters": [{"opponent": "dialga", "rating": 236}, {"opponent": "metagross", "rating": 325}, {"opponent": "mewtwo", "rating": 416}, {"opponent": "lugia", "rating": 426}, {"opponent": "zacian_hero", "rating": 450}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 43803}, {"moveId": "METAL_CLAW", "uses": 32697}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 35920}, {"moveId": "DRILL_PECK", "uses": 19061}, {"moveId": "BLIZZARD", "uses": 10613}, {"moveId": "FLASH_CANNON", "uses": 7108}, {"moveId": "HYDRO_PUMP", "uses": 3853}]}, "moveset": ["WATERFALL", "HYDRO_CANNON", "FLASH_CANNON"], "score": 66.7}, {"speciesId": "al<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 530, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 844, "opRating": 155}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 822, "opRating": 177}, {"opponent": "mamos<PERSON>_shadow", "rating": 800, "opRating": 200}, {"opponent": "metagross", "rating": 733}, {"opponent": "dialga", "rating": 622}], "counters": [{"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "grou<PERSON>", "rating": 423}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29457}, {"moveId": "PSYCHO_CUT", "uses": 26486}, {"moveId": "CONFUSION", "uses": 20519}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18315}, {"moveId": "FIRE_PUNCH", "uses": 16662}, {"moveId": "PSYCHIC", "uses": 15553}, {"moveId": "FOCUS_BLAST", "uses": 10929}, {"moveId": "DAZZLING_GLEAM", "uses": 8262}, {"moveId": "FUTURE_SIGHT", "uses": 6697}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 66.2}, {"speciesId": "aurorus", "speciesName": "Au<PERSON><PERSON>", "rating": 524, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 746}, {"opponent": "gyarados", "rating": 623}, {"opponent": "lugia", "rating": 538}, {"opponent": "garcho<PERSON>", "rating": 512}, {"opponent": "ho_oh", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 322}, {"opponent": "excadrill", "rating": 404}, {"opponent": "giratina_origin", "rating": 442}, {"opponent": "dragonite", "rating": 462}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37812}, {"moveId": "ROCK_THROW", "uses": 20919}, {"moveId": "FROST_BREATH", "uses": 17766}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 38367}, {"moveId": "ANCIENT_POWER", "uses": 16821}, {"moveId": "THUNDERBOLT", "uses": 10715}, {"moveId": "BLIZZARD", "uses": 5853}, {"moveId": "HYPER_BEAM", "uses": 4818}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 66.2}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 569, "matchups": [{"opponent": "genesect_douse", "rating": 860, "opRating": 139}, {"opponent": "genesect_shock", "rating": 860, "opRating": 139}, {"opponent": "genesect_chill", "rating": 860, "opRating": 139}, {"opponent": "dialga", "rating": 703}, {"opponent": "metagross", "rating": 610}], "counters": [{"opponent": "giratina_origin", "rating": 123}, {"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "excadrill", "rating": 209}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 367}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46302}, {"moveId": "FIRE_SPIN", "uses": 30198}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 21226}, {"moveId": "BRAVE_BIRD", "uses": 16005}, {"moveId": "BLAZE_KICK", "uses": 13112}, {"moveId": "STONE_EDGE", "uses": 11852}, {"moveId": "FOCUS_BLAST", "uses": 9831}, {"moveId": "OVERHEAT", "uses": 4522}]}, "moveset": ["COUNTER", "BLAZE_KICK", "BLAST_BURN"], "score": 66}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 543, "matchups": [{"opponent": "excadrill", "rating": 746}, {"opponent": "magnezone_shadow", "rating": 698, "opRating": 301}, {"opponent": "rai<PERSON>u", "rating": 643, "opRating": 356}, {"opponent": "grou<PERSON>", "rating": 597, "opRating": 402}, {"opponent": "zekrom", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "giratina_origin", "rating": 292}, {"opponent": "zacian_hero", "rating": 335}, {"opponent": "garcho<PERSON>", "rating": 464}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38333}, {"moveId": "WING_ATTACK", "uses": 38167}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30910}, {"moveId": "EARTHQUAKE", "uses": 18817}, {"moveId": "AERIAL_ACE", "uses": 17402}, {"moveId": "SAND_TOMB", "uses": 9247}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "EARTHQUAKE"], "score": 66}, {"speciesId": "tyrantrum", "speciesName": "Tyrantrum", "rating": 556, "matchups": [{"opponent": "entei", "rating": 856, "opRating": 143}, {"opponent": "entei_shadow", "rating": 844, "opRating": 155}, {"opponent": "ho_oh", "rating": 704, "opRating": 295}, {"opponent": "ho_oh_shadow", "rating": 649, "opRating": 350}, {"opponent": "giratina_origin", "rating": 531}], "counters": [{"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "dialga", "rating": 282}, {"opponent": "metagross", "rating": 313}, {"opponent": "lugia", "rating": 373}, {"opponent": "mewtwo", "rating": 471}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 35509}, {"moveId": "ROCK_THROW", "uses": 24031}, {"moveId": "CHARM", "uses": 16934}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23605}, {"moveId": "CRUNCH", "uses": 20689}, {"moveId": "OUTRAGE", "uses": 17975}, {"moveId": "EARTHQUAKE", "uses": 14367}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "CRUNCH"], "score": 66}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 484, "matchups": [{"opponent": "garcho<PERSON>", "rating": 925}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 800, "opRating": 199}, {"opponent": "zap<PERSON>_shadow", "rating": 699, "opRating": 300}, {"opponent": "dragonite", "rating": 644}, {"opponent": "giratina_altered", "rating": 549, "opRating": 450}], "counters": [{"opponent": "dialga", "rating": 252}, {"opponent": "mewtwo", "rating": 286}, {"opponent": "giratina_origin", "rating": 356}, {"opponent": "gyarados", "rating": 365}, {"opponent": "lugia", "rating": 438}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44477}, {"moveId": "ICE_SHARD", "uses": 32023}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41412}, {"moveId": "STONE_EDGE", "uses": 15319}, {"moveId": "BULLDOZE", "uses": 12860}, {"moveId": "RETURN", "uses": 6835}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 65.9}, {"speciesId": "torn<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 522, "matchups": [{"opponent": "zacian_hero", "rating": 664}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 647, "opRating": 352}, {"opponent": "grou<PERSON>", "rating": 623}, {"opponent": "gyarados", "rating": 573}, {"opponent": "swampert", "rating": 567}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "lugia", "rating": 302}, {"opponent": "mewtwo", "rating": 343}, {"opponent": "giratina_origin", "rating": 406}, {"opponent": "garcho<PERSON>", "rating": 471}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 58539}, {"moveId": "ASTONISH", "uses": 17961}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 26501}, {"moveId": "PSYCHIC", "uses": 21144}, {"moveId": "FOCUS_BLAST", "uses": 20278}, {"moveId": "HEAT_WAVE", "uses": 8544}]}, "moveset": ["GUST", "HURRICANE", "PSYCHIC"], "score": 65.9}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 520, "matchups": [{"opponent": "hoopa_unbound", "rating": 917, "opRating": 82}, {"opponent": "latios", "rating": 703, "opRating": 296}, {"opponent": "mewtwo", "rating": 687}, {"opponent": "mewtwo_shadow", "rating": 643, "opRating": 356}, {"opponent": "latios_shadow", "rating": 621, "opRating": 378}], "counters": [{"opponent": "dialga", "rating": 315}, {"opponent": "lugia", "rating": 319}, {"opponent": "garcho<PERSON>", "rating": 323}, {"opponent": "giratina_origin", "rating": 342}, {"opponent": "metagross", "rating": 424}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38919}, {"moveId": "BULLET_PUNCH", "uses": 37581}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30557}, {"moveId": "X_SCISSOR", "uses": 22603}, {"moveId": "IRON_HEAD", "uses": 15054}, {"moveId": "RETURN", "uses": 8426}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 65.6}, {"speciesId": "pinsir_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 564, "matchups": [{"opponent": "weavile_shadow", "rating": 919, "opRating": 80}, {"opponent": "staraptor_shadow", "rating": 838, "opRating": 161}, {"opponent": "hydreigon", "rating": 738, "opRating": 261}, {"opponent": "snorlax", "rating": 590, "opRating": 409}, {"opponent": "excadrill", "rating": 506}], "counters": [{"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "mewtwo", "rating": 330}, {"opponent": "metagross", "rating": 331}, {"opponent": "swampert", "rating": 400}, {"opponent": "dialga", "rating": 472}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40457}, {"moveId": "BUG_BITE", "uses": 26520}, {"moveId": "ROCK_SMASH", "uses": 9515}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25018}, {"moveId": "X_SCISSOR", "uses": 22153}, {"moveId": "SUPER_POWER", "uses": 18625}, {"moveId": "VICE_GRIP", "uses": 7704}, {"moveId": "SUBMISSION", "uses": 3015}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 65.4}, {"speciesId": "blastoise_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 509, "matchups": [{"opponent": "moltres", "rating": 897, "opRating": 102}, {"opponent": "entei_shadow", "rating": 882, "opRating": 117}, {"opponent": "moltres_shadow", "rating": 882, "opRating": 117}, {"opponent": "excadrill", "rating": 647}, {"opponent": "garcho<PERSON>", "rating": 514}], "counters": [{"opponent": "dialga", "rating": 277}, {"opponent": "giratina_origin", "rating": 282}, {"opponent": "mewtwo", "rating": 348}, {"opponent": "metagross", "rating": 360}, {"opponent": "zacian_hero", "rating": 381}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51531}, {"moveId": "BITE", "uses": 24969}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 40300}, {"moveId": "ICE_BEAM", "uses": 16984}, {"moveId": "SKULL_BASH", "uses": 8983}, {"moveId": "FLASH_CANNON", "uses": 5881}, {"moveId": "HYDRO_PUMP", "uses": 4225}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 65.1}, {"speciesId": "golem", "speciesName": "Golem", "rating": 540, "matchups": [{"opponent": "ho_oh", "rating": 700, "opRating": 299}, {"opponent": "zekrom", "rating": 558, "opRating": 441}, {"opponent": "excadrill", "rating": 529}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 526, "opRating": 473}, {"opponent": "sylveon", "rating": 523, "opRating": 476}], "counters": [{"opponent": "giratina_origin", "rating": 252}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "lugia", "rating": 452}, {"opponent": "dialga", "rating": 494}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 33409}, {"moveId": "ROCK_THROW", "uses": 22603}, {"moveId": "MUD_SLAP", "uses": 20474}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 20145}, {"moveId": "STONE_EDGE", "uses": 18855}, {"moveId": "ROCK_BLAST", "uses": 17053}, {"moveId": "ANCIENT_POWER", "uses": 13215}, {"moveId": "RETURN", "uses": 7253}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 65.1}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 570, "matchups": [{"opponent": "metagross", "rating": 865}, {"opponent": "metagross_shadow", "rating": 846, "opRating": 153}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 788, "opRating": 211}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747}, {"opponent": "sylveon", "rating": 545, "opRating": 454}], "counters": [{"opponent": "mewtwo", "rating": 88}, {"opponent": "grou<PERSON>", "rating": 432}, {"opponent": "dialga", "rating": 461}, {"opponent": "zacian_hero", "rating": 465}, {"opponent": "lugia", "rating": 497}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 41407}, {"moveId": "FIRE_FANG", "uses": 18102}, {"moveId": "TACKLE", "uses": 16956}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27006}, {"moveId": "OVERHEAT", "uses": 21179}, {"moveId": "PSYCHIC", "uses": 14498}, {"moveId": "FOCUS_BLAST", "uses": 13788}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 64.9}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 482, "matchups": [{"opponent": "thundurus_therian", "rating": 932, "opRating": 67}, {"opponent": "garcho<PERSON>", "rating": 910}, {"opponent": "ho_oh", "rating": 621, "opRating": 378}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 582, "opRating": 417}, {"opponent": "dragonite", "rating": 577}], "counters": [{"opponent": "zacian_hero", "rating": 260}, {"opponent": "dialga", "rating": 309}, {"opponent": "lugia", "rating": 338}, {"opponent": "giratina_origin", "rating": 356}, {"opponent": "excadrill", "rating": 430}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44988}, {"moveId": "ICE_SHARD", "uses": 31512}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 45133}, {"moveId": "STONE_EDGE", "uses": 17145}, {"moveId": "BULLDOZE", "uses": 14143}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 64.8}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 571, "matchups": [{"opponent": "kyogre", "rating": 779, "opRating": 220}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 744}, {"opponent": "gyarados", "rating": 625}, {"opponent": "yveltal", "rating": 566, "opRating": 433}, {"opponent": "lugia", "rating": 540}], "counters": [{"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "dialga", "rating": 228}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "metagross", "rating": 383}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11781}, {"moveId": "SNARL", "uses": 9593}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4409}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4181}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4097}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3763}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3645}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3622}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3535}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3530}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3285}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3277}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3244}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3019}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2991}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2912}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2874}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2684}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34302}, {"moveId": "CRUNCH", "uses": 19567}, {"moveId": "PSYCHIC_FANGS", "uses": 17338}, {"moveId": "HYPER_BEAM", "uses": 5296}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 64.6}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 580, "matchups": [{"opponent": "sylveon", "rating": 841, "opRating": 158}, {"opponent": "swampert", "rating": 771}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 753}, {"opponent": "zacian_hero", "rating": 647}, {"opponent": "gyarados", "rating": 570}], "counters": [{"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "dialga", "rating": 176}, {"opponent": "metagross", "rating": 238}, {"opponent": "dragonite", "rating": 242}, {"opponent": "excadrill", "rating": 309}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 32746}, {"moveId": "BULLET_SEED", "uses": 28811}, {"moveId": "RAZOR_LEAF", "uses": 14955}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 25122}, {"moveId": "GRASS_KNOT", "uses": 16440}, {"moveId": "SLUDGE_BOMB", "uses": 14383}, {"moveId": "LEAF_STORM", "uses": 9600}, {"moveId": "DAZZLING_GLEAM", "uses": 7497}, {"moveId": "SOLAR_BEAM", "uses": 3418}]}, "moveset": ["POISON_JAB", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 64.6}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 547, "matchups": [{"opponent": "swampert", "rating": 822}, {"opponent": "kyogre", "rating": 659, "opRating": 340}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 604, "opRating": 395}, {"opponent": "gyarados", "rating": 543}, {"opponent": "zacian_hero", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 127}, {"opponent": "giratina_origin", "rating": 278}, {"opponent": "excadrill", "rating": 409}, {"opponent": "grou<PERSON>", "rating": 445}, {"opponent": "garcho<PERSON>", "rating": 488}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53165}, {"moveId": "RAZOR_LEAF", "uses": 23335}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38002}, {"moveId": "SLUDGE_BOMB", "uses": 19368}, {"moveId": "RETURN", "uses": 9515}, {"moveId": "PETAL_BLIZZARD", "uses": 5211}, {"moveId": "SOLAR_BEAM", "uses": 4238}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 64.6}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 517, "matchups": [{"opponent": "swampert_shadow", "rating": 966, "opRating": 33}, {"opponent": "swampert", "rating": 818}, {"opponent": "tapu_fini", "rating": 778, "opRating": 221}, {"opponent": "kyogre", "rating": 708, "opRating": 291}, {"opponent": "excadrill", "rating": 550, "opRating": 449}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "mewtwo", "rating": 432}, {"opponent": "gyarados", "rating": 458}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30492}, {"moveId": "BULLET_SEED", "uses": 29542}, {"moveId": "RAZOR_LEAF", "uses": 16446}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 51118}, {"moveId": "LAST_RESORT", "uses": 13854}, {"moveId": "ENERGY_BALL", "uses": 7221}, {"moveId": "SOLAR_BEAM", "uses": 4256}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 64.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 528, "matchups": [{"opponent": "escavalier", "rating": 888, "opRating": 111}, {"opponent": "mamos<PERSON>_shadow", "rating": 844, "opRating": 155}, {"opponent": "tapu_lele", "rating": 822, "opRating": 177}, {"opponent": "metagross", "rating": 792}, {"opponent": "excadrill", "rating": 581}], "counters": [{"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "mewtwo", "rating": 213}, {"opponent": "lugia", "rating": 266}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "dialga", "rating": 464}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29119}, {"moveId": "PSYCHO_CUT", "uses": 26168}, {"moveId": "CONFUSION", "uses": 21200}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16628}, {"moveId": "FIRE_PUNCH", "uses": 15303}, {"moveId": "PSYCHIC", "uses": 13968}, {"moveId": "FOCUS_BLAST", "uses": 9999}, {"moveId": "DAZZLING_GLEAM", "uses": 7449}, {"moveId": "RETURN", "uses": 7104}, {"moveId": "FUTURE_SIGHT", "uses": 6031}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 64.1}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 494, "matchups": [{"opponent": "swampert", "rating": 851}, {"opponent": "kyogre", "rating": 691, "opRating": 308}, {"opponent": "excadrill", "rating": 581}, {"opponent": "snorlax", "rating": 534, "opRating": 465}, {"opponent": "gyarados", "rating": 531}], "counters": [{"opponent": "zacian_hero", "rating": 274}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "dialga", "rating": 331}, {"opponent": "metagross", "rating": 348}, {"opponent": "garcho<PERSON>", "rating": 436}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53396}, {"moveId": "RAZOR_LEAF", "uses": 23104}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38990}, {"moveId": "EARTHQUAKE", "uses": 17009}, {"moveId": "RETURN", "uses": 10777}, {"moveId": "PETAL_BLIZZARD", "uses": 5385}, {"moveId": "SOLAR_BEAM", "uses": 4432}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 64}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 497, "matchups": [{"opponent": "dragonite_shadow", "rating": 829, "opRating": 170}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 807, "opRating": 192}, {"opponent": "sylveon", "rating": 554, "opRating": 445}, {"opponent": "giratina_altered", "rating": 554, "opRating": 445}, {"opponent": "lugia", "rating": 545}], "counters": [{"opponent": "dialga", "rating": 372}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "mewtwo", "rating": 403}, {"opponent": "gyarados", "rating": 425}, {"opponent": "giratina_origin", "rating": 488}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32555}, {"moveId": "SHADOW_CLAW", "uses": 30515}, {"moveId": "METAL_CLAW", "uses": 13506}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 30543}, {"moveId": "BLIZZARD", "uses": 15859}, {"moveId": "BULLDOZE", "uses": 11471}, {"moveId": "GYRO_BALL", "uses": 9803}, {"moveId": "RETURN", "uses": 8964}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 64}, {"speciesId": "aggron", "speciesName": "Aggron", "rating": 528, "matchups": [{"opponent": "gyarados", "rating": 681}, {"opponent": "lugia", "rating": 665}, {"opponent": "sylveon", "rating": 659, "opRating": 340}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 566}, {"opponent": "ho_oh", "rating": 557, "opRating": 442}], "counters": [{"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "dialga", "rating": 364}, {"opponent": "giratina_origin", "rating": 434}, {"opponent": "mewtwo", "rating": 455}, {"opponent": "zekrom", "rating": 483}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33880}, {"moveId": "SMACK_DOWN", "uses": 32583}, {"moveId": "IRON_TAIL", "uses": 10028}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 27243}, {"moveId": "HEAVY_SLAM", "uses": 18648}, {"moveId": "THUNDER", "uses": 12763}, {"moveId": "RETURN", "uses": 10409}, {"moveId": "ROCK_TOMB", "uses": 7334}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 63.8}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 527, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "genesect_chill", "rating": 685, "opRating": 314}, {"opponent": "genesect_burn", "rating": 685, "opRating": 314}, {"opponent": "gyarados", "rating": 626}, {"opponent": "victini", "rating": 602, "opRating": 397}], "counters": [{"opponent": "metagross", "rating": 316}, {"opponent": "dialga", "rating": 342}, {"opponent": "mewtwo", "rating": 361}, {"opponent": "lugia", "rating": 392}, {"opponent": "giratina_origin", "rating": 448}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28839}, {"moveId": "FIRE_FANG", "uses": 20635}, {"moveId": "THUNDER_FANG", "uses": 15070}, {"moveId": "BITE", "uses": 11932}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20991}, {"moveId": "CRUNCH", "uses": 14870}, {"moveId": "PSYCHIC_FANGS", "uses": 13458}, {"moveId": "FLAMETHROWER", "uses": 12385}, {"moveId": "BULLDOZE", "uses": 6328}, {"moveId": "RETURN", "uses": 5109}, {"moveId": "FIRE_BLAST", "uses": 3340}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 63.8}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 522, "matchups": [{"opponent": "celebi", "rating": 900, "opRating": 99}, {"opponent": "chandelure", "rating": 855, "opRating": 144}, {"opponent": "mewtwo_shadow", "rating": 743, "opRating": 256}, {"opponent": "mewtwo", "rating": 654}, {"opponent": "giratina_origin", "rating": 572}], "counters": [{"opponent": "garcho<PERSON>", "rating": 230}, {"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 273}, {"opponent": "gyarados", "rating": 301}, {"opponent": "metagross", "rating": 412}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 56956}, {"moveId": "PECK", "uses": 19544}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 24821}, {"moveId": "DARK_PULSE", "uses": 19091}, {"moveId": "SKY_ATTACK", "uses": 18706}, {"moveId": "PSYCHIC", "uses": 8077}, {"moveId": "RETURN", "uses": 5796}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 63.8}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 537, "matchups": [{"opponent": "tapu_lele", "rating": 860, "opRating": 139}, {"opponent": "metagross_shadow", "rating": 761, "opRating": 238}, {"opponent": "mewtwo", "rating": 743}, {"opponent": "mewtwo_shadow", "rating": 686, "opRating": 313}, {"opponent": "swampert", "rating": 601}], "counters": [{"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "lugia", "rating": 285}, {"opponent": "dialga", "rating": 315}, {"opponent": "giratina_origin", "rating": 446}, {"opponent": "metagross", "rating": 488}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 58985}, {"moveId": "PECK", "uses": 17515}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 26976}, {"moveId": "DARK_PULSE", "uses": 20530}, {"moveId": "SKY_ATTACK", "uses": 20111}, {"moveId": "PSYCHIC", "uses": 8753}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 63.8}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 554, "matchups": [{"opponent": "genesect_douse", "rating": 926, "opRating": 73}, {"opponent": "genesect_shock", "rating": 926, "opRating": 73}, {"opponent": "genesect_chill", "rating": 926, "opRating": 73}, {"opponent": "gyarados", "rating": 582}, {"opponent": "sylveon", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 229}, {"opponent": "metagross", "rating": 322}, {"opponent": "lugia", "rating": 326}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "dialga", "rating": 461}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39843}, {"moveId": "FIRE_SPIN", "uses": 36657}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23052}, {"moveId": "BRICK_BREAK", "uses": 15633}, {"moveId": "THUNDERBOLT", "uses": 13332}, {"moveId": "PSYCHIC", "uses": 11747}, {"moveId": "RETURN", "uses": 8208}, {"moveId": "FIRE_BLAST", "uses": 4692}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 63.8}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 568, "matchups": [{"opponent": "swampert", "rating": 779}, {"opponent": "kyogre", "rating": 697, "opRating": 302}, {"opponent": "snorlax", "rating": 625, "opRating": 375}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 558, "opRating": 441}, {"opponent": "gyarados", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "zacian_hero", "rating": 439}, {"opponent": "excadrill", "rating": 486}, {"opponent": "grou<PERSON>", "rating": 491}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54265}, {"moveId": "RAZOR_LEAF", "uses": 22235}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 42972}, {"moveId": "SLUDGE_BOMB", "uses": 22733}, {"moveId": "PETAL_BLIZZARD", "uses": 5923}, {"moveId": "SOLAR_BEAM", "uses": 4967}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 63.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 517, "matchups": [{"opponent": "tyranitar", "rating": 874, "opRating": 125}, {"opponent": "hydreigon", "rating": 744, "opRating": 255}, {"opponent": "dialga", "rating": 685}, {"opponent": "excadrill", "rating": 582}, {"opponent": "snorlax", "rating": 523, "opRating": 476}], "counters": [{"opponent": "lugia", "rating": 207}, {"opponent": "zacian_hero", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "metagross", "rating": 325}, {"opponent": "gyarados", "rating": 396}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 57091}, {"moveId": "ROCK_SMASH", "uses": 11584}, {"moveId": "LOW_KICK", "uses": 7827}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28856}, {"moveId": "STONE_EDGE", "uses": 14999}, {"moveId": "BRICK_BREAK", "uses": 12764}, {"moveId": "STOMP", "uses": 10193}, {"moveId": "RETURN", "uses": 6018}, {"moveId": "LOW_SWEEP", "uses": 3744}]}, "moveset": ["DOUBLE_KICK", "CLOSE_COMBAT", "STONE_EDGE"], "score": 63.6}, {"speciesId": "salamence_shadow", "speciesName": "Salamence (Shadow)", "rating": 528, "matchups": [{"opponent": "grou<PERSON>", "rating": 737}, {"opponent": "kyogre", "rating": 675, "opRating": 324}, {"opponent": "swampert", "rating": 654}, {"opponent": "yveltal", "rating": 610, "opRating": 389}, {"opponent": "excadrill", "rating": 590}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "gyarados", "rating": 278}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "giratina_origin", "rating": 346}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 45034}, {"moveId": "FIRE_FANG", "uses": 19505}, {"moveId": "BITE", "uses": 11951}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 35118}, {"moveId": "FIRE_BLAST", "uses": 15427}, {"moveId": "HYDRO_PUMP", "uses": 15292}, {"moveId": "DRACO_METEOR", "uses": 10542}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "FIRE_BLAST"], "score": 63.6}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 524, "matchups": [{"opponent": "victini", "rating": 715, "opRating": 284}, {"opponent": "nihilego", "rating": 653, "opRating": 346}, {"opponent": "rai<PERSON>u", "rating": 639, "opRating": 360}, {"opponent": "giratina_origin", "rating": 605}, {"opponent": "excadrill", "rating": 545}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "zacian_hero", "rating": 430}, {"opponent": "metagross", "rating": 479}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 76500}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 42564}, {"moveId": "EARTHQUAKE", "uses": 17055}, {"moveId": "PLAY_ROUGH", "uses": 12075}, {"moveId": "HYPER_BEAM", "uses": 4739}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 63.5}, {"speciesId": "ampha<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 534, "matchups": [{"opponent": "melmetal", "rating": 744, "opRating": 255}, {"opponent": "snorlax", "rating": 669, "opRating": 330}, {"opponent": "gyarados", "rating": 663}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 623}, {"opponent": "dialga", "rating": 502}], "counters": [{"opponent": "dragonite", "rating": 242}, {"opponent": "metagross", "rating": 267}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "lugia", "rating": 461}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 53261}, {"moveId": "CHARGE_BEAM", "uses": 23239}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 24299}, {"moveId": "FOCUS_BLAST", "uses": 14803}, {"moveId": "DRAGON_PULSE", "uses": 12869}, {"moveId": "POWER_GEM", "uses": 9081}, {"moveId": "THUNDER", "uses": 7859}, {"moveId": "ZAP_CANNON", "uses": 7585}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 63.3}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "mewtwo", "rating": 738}, {"opponent": "mewtwo_shadow", "rating": 681, "opRating": 318}, {"opponent": "latios_shadow", "rating": 677, "opRating": 322}, {"opponent": "giratina_altered", "rating": 664, "opRating": 335}, {"opponent": "giratina_origin", "rating": 630}], "counters": [{"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "dialga", "rating": 282}, {"opponent": "gyarados", "rating": 360}, {"opponent": "metagross", "rating": 418}, {"opponent": "lugia", "rating": 469}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 51347}, {"moveId": "METAL_CLAW", "uses": 25153}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27646}, {"moveId": "X_SCISSOR", "uses": 18491}, {"moveId": "IRON_HEAD", "uses": 17268}, {"moveId": "FOCUS_BLAST", "uses": 13108}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 63.2}, {"speciesId": "gardevoir", "speciesName": "Gardevoir", "rating": 493, "matchups": [{"opponent": "dragonite", "rating": 782}, {"opponent": "gyarados", "rating": 646}, {"opponent": "yveltal", "rating": 613, "opRating": 386}, {"opponent": "garcho<PERSON>", "rating": 529}, {"opponent": "zekrom", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 208}, {"opponent": "dialga", "rating": 228}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "lugia", "rating": 288}, {"opponent": "zacian_hero", "rating": 338}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30320}, {"moveId": "CHARM", "uses": 27735}, {"moveId": "CHARGE_BEAM", "uses": 18496}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 23027}, {"moveId": "SYNCHRONOISE", "uses": 20245}, {"moveId": "DAZZLING_GLEAM", "uses": 14321}, {"moveId": "RETURN", "uses": 9641}, {"moveId": "PSYCHIC", "uses": 9173}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 63.2}, {"speciesId": "aggron_shadow", "speciesName": "A<PERSON><PERSON> (Shadow)", "rating": 516, "matchups": [{"opponent": "lugia", "rating": 656}, {"opponent": "gyarados", "rating": 643}, {"opponent": "giratina_origin", "rating": 528}, {"opponent": "sylveon", "rating": 528, "opRating": 471}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 515}], "counters": [{"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "metagross", "rating": 209}, {"opponent": "dragonite", "rating": 226}, {"opponent": "dialga", "rating": 342}, {"opponent": "mewtwo", "rating": 494}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 34176}, {"moveId": "SMACK_DOWN", "uses": 32938}, {"moveId": "IRON_TAIL", "uses": 9517}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 31433}, {"moveId": "HEAVY_SLAM", "uses": 21916}, {"moveId": "THUNDER", "uses": 14630}, {"moveId": "ROCK_TOMB", "uses": 8481}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 63}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 540, "matchups": [{"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "zap<PERSON>_galarian", "rating": 825, "opRating": 174}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 738}, {"opponent": "gyarados", "rating": 680}, {"opponent": "lugia", "rating": 549}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "giratina_origin", "rating": 233}, {"opponent": "metagross", "rating": 337}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "zacian_hero", "rating": 338}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 10797}, {"moveId": "SNARL", "uses": 9026}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4607}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4351}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4089}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3862}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3748}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3651}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3650}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3597}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3466}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3344}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3307}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3065}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2997}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2995}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2957}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2763}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32793}, {"moveId": "CRUNCH", "uses": 18671}, {"moveId": "PSYCHIC_FANGS", "uses": 16249}, {"moveId": "RETURN", "uses": 6305}, {"moveId": "HYPER_BEAM", "uses": 2493}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 63}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 494, "matchups": [{"opponent": "gengar", "rating": 810, "opRating": 189}, {"opponent": "giratina_origin", "rating": 744}, {"opponent": "mewtwo", "rating": 655}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 636, "opRating": 363}, {"opponent": "mewtwo_shadow", "rating": 578, "opRating": 421}], "counters": [{"opponent": "dialga", "rating": 282}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "excadrill", "rating": 402}, {"opponent": "gyarados", "rating": 404}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44435}, {"moveId": "LICK", "uses": 32065}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 37995}, {"moveId": "CROSS_CHOP", "uses": 22278}, {"moveId": "HYPER_BEAM", "uses": 8618}, {"moveId": "GUNK_SHOT", "uses": 6637}, {"moveId": "OBSTRUCT", "uses": 915}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "OBSTRUCT"], "score": 63}, {"speciesId": "pinsir", "speciesName": "Pinsir", "rating": 541, "matchups": [{"opponent": "ursaring", "rating": 899, "opRating": 100}, {"opponent": "ursaring_shadow", "rating": 879, "opRating": 120}, {"opponent": "hoopa_unbound", "rating": 859, "opRating": 140}, {"opponent": "snorlax", "rating": 590, "opRating": 409}, {"opponent": "excadrill", "rating": 560}], "counters": [{"opponent": "garcho<PERSON>", "rating": 281}, {"opponent": "metagross", "rating": 290}, {"opponent": "gyarados", "rating": 319}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "dialga", "rating": 410}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40210}, {"moveId": "BUG_BITE", "uses": 26454}, {"moveId": "ROCK_SMASH", "uses": 9823}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 22932}, {"moveId": "X_SCISSOR", "uses": 20036}, {"moveId": "SUPER_POWER", "uses": 17059}, {"moveId": "RETURN", "uses": 7263}, {"moveId": "VICE_GRIP", "uses": 6538}, {"moveId": "SUBMISSION", "uses": 2758}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 62.8}, {"speciesId": "gardevoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 479, "matchups": [{"opponent": "hydreigon", "rating": 922, "opRating": 77}, {"opponent": "kommo_o", "rating": 853, "opRating": 146}, {"opponent": "dragonite", "rating": 788}, {"opponent": "palkia", "rating": 649, "opRating": 350}, {"opponent": "yveltal", "rating": 555, "opRating": 444}], "counters": [{"opponent": "mewtwo", "rating": 260}, {"opponent": "dialga", "rating": 277}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31164}, {"moveId": "CHARM", "uses": 26855}, {"moveId": "CHARGE_BEAM", "uses": 18518}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26132}, {"moveId": "SYNCHRONOISE", "uses": 23217}, {"moveId": "DAZZLING_GLEAM", "uses": 16484}, {"moveId": "PSYCHIC", "uses": 10569}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 62.4}, {"speciesId": "poliwrath", "speciesName": "Poliwrath", "rating": 498, "matchups": [{"opponent": "tyranitar_shadow", "rating": 892, "opRating": 107}, {"opponent": "mamos<PERSON>_shadow", "rating": 704, "opRating": 295}, {"opponent": "genesect_douse", "rating": 672, "opRating": 327}, {"opponent": "genesect_chill", "rating": 672, "opRating": 327}, {"opponent": "genesect_burn", "rating": 672, "opRating": 327}], "counters": [{"opponent": "dialga", "rating": 434}, {"opponent": "metagross", "rating": 453}, {"opponent": "garcho<PERSON>", "rating": 464}, {"opponent": "excadrill", "rating": 474}, {"opponent": "dragonite", "rating": 478}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 35230}, {"moveId": "BUBBLE", "uses": 30070}, {"moveId": "ROCK_SMASH", "uses": 11239}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 19778}, {"moveId": "DYNAMIC_PUNCH", "uses": 18488}, {"moveId": "SCALD", "uses": 17530}, {"moveId": "RETURN", "uses": 6449}, {"moveId": "POWER_UP_PUNCH", "uses": 6216}, {"moveId": "SUBMISSION", "uses": 4074}, {"moveId": "HYDRO_PUMP", "uses": 3965}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 62.2}, {"speciesId": "poliwrath_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 497, "matchups": [{"opponent": "tyranitar", "rating": 892, "opRating": 107}, {"opponent": "tyranitar_shadow", "rating": 870, "opRating": 129}, {"opponent": "mamos<PERSON>_shadow", "rating": 693, "opRating": 306}, {"opponent": "excadrill", "rating": 672}, {"opponent": "garcho<PERSON>", "rating": 518}], "counters": [{"opponent": "gyarados", "rating": 255}, {"opponent": "dragonite", "rating": 292}, {"opponent": "giratina_origin", "rating": 326}, {"opponent": "metagross", "rating": 392}, {"opponent": "dialga", "rating": 429}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36055}, {"moveId": "BUBBLE", "uses": 29933}, {"moveId": "ROCK_SMASH", "uses": 10545}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 21670}, {"moveId": "DYNAMIC_PUNCH", "uses": 20082}, {"moveId": "SCALD", "uses": 19207}, {"moveId": "POWER_UP_PUNCH", "uses": 6776}, {"moveId": "SUBMISSION", "uses": 4551}, {"moveId": "HYDRO_PUMP", "uses": 4412}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 62.2}, {"speciesId": "sandslash_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 495, "matchups": [{"opponent": "staraptor_shadow", "rating": 923, "opRating": 76}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 899, "opRating": 100}, {"opponent": "dragonite_shadow", "rating": 865, "opRating": 134}, {"opponent": "dragonite", "rating": 829}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 765, "opRating": 234}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 455}, {"opponent": "mewtwo", "rating": 463}, {"opponent": "lugia", "rating": 473}, {"opponent": "gyarados", "rating": 479}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 33207}, {"moveId": "SHADOW_CLAW", "uses": 30423}, {"moveId": "METAL_CLAW", "uses": 12879}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 34256}, {"moveId": "BLIZZARD", "uses": 17748}, {"moveId": "BULLDOZE", "uses": 13223}, {"moveId": "GYRO_BALL", "uses": 11201}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 62.2}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 497, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 789, "opRating": 210}, {"opponent": "swampert", "rating": 713}, {"opponent": "latios_shadow", "rating": 587, "opRating": 412}, {"opponent": "excadrill", "rating": 584}, {"opponent": "metagross", "rating": 519}], "counters": [{"opponent": "mewtwo", "rating": 195}, {"opponent": "lugia", "rating": 276}, {"opponent": "dialga", "rating": 279}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "zacian_hero", "rating": 393}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49568}, {"moveId": "RAZOR_LEAF", "uses": 26932}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26370}, {"moveId": "SEED_BOMB", "uses": 21687}, {"moveId": "FOUL_PLAY", "uses": 19766}, {"moveId": "FIRE_BLAST", "uses": 8623}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 62.1}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 491, "matchups": [{"opponent": "gengar", "rating": 820, "opRating": 179}, {"opponent": "trevenant", "rating": 797, "opRating": 202}, {"opponent": "gyarados", "rating": 691}, {"opponent": "gyarado<PERSON>_shadow", "rating": 662, "opRating": 337}, {"opponent": "giratina_origin", "rating": 660}], "counters": [{"opponent": "dialga", "rating": 230}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "lugia", "rating": 361}, {"opponent": "metagross", "rating": 369}, {"opponent": "mewtwo", "rating": 432}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 35764}, {"moveId": "ICE_FANG", "uses": 32016}, {"moveId": "TAKE_DOWN", "uses": 8767}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35402}, {"moveId": "CRUNCH", "uses": 27748}, {"moveId": "PLAY_ROUGH", "uses": 13461}]}, "moveset": ["LICK", "WILD_CHARGE", "CRUNCH"], "score": 62.1}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 534, "matchups": [{"opponent": "moltres_shadow", "rating": 892, "opRating": 107}, {"opponent": "braviary", "rating": 870, "opRating": 129}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 720, "opRating": 279}, {"opponent": "genesect_chill", "rating": 647, "opRating": 352}, {"opponent": "gyarados", "rating": 556}], "counters": [{"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "dialga", "rating": 241}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "metagross", "rating": 377}, {"opponent": "mewtwo", "rating": 385}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30590}, {"moveId": "FIRE_FANG", "uses": 20493}, {"moveId": "THUNDER_FANG", "uses": 14531}, {"moveId": "BITE", "uses": 10814}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22454}, {"moveId": "CRUNCH", "uses": 16069}, {"moveId": "PSYCHIC_FANGS", "uses": 14513}, {"moveId": "FLAMETHROWER", "uses": 13271}, {"moveId": "BULLDOZE", "uses": 6701}, {"moveId": "FIRE_BLAST", "uses": 3575}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 61.9}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 502, "matchups": [{"opponent": "swampert", "rating": 828}, {"opponent": "kyogre", "rating": 767, "opRating": 232}, {"opponent": "excadrill", "rating": 718}, {"opponent": "gyarados", "rating": 523}, {"opponent": "garcho<PERSON>", "rating": 514}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "zacian_hero", "rating": 343}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "grou<PERSON>", "rating": 453}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54332}, {"moveId": "RAZOR_LEAF", "uses": 22168}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 45455}, {"moveId": "EARTHQUAKE", "uses": 19560}, {"moveId": "PETAL_BLIZZARD", "uses": 6228}, {"moveId": "SOLAR_BEAM", "uses": 5122}, {"moveId": "FRUSTRATION", "uses": 28}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 61.9}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 480, "matchups": [{"opponent": "heatran", "rating": 903, "opRating": 96}, {"opponent": "nihilego", "rating": 718, "opRating": 281}, {"opponent": "rai<PERSON>u", "rating": 673, "opRating": 326}, {"opponent": "excadrill", "rating": 656}, {"opponent": "metagross", "rating": 649}], "counters": [{"opponent": "giratina_origin", "rating": 249}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "dialga", "rating": 426}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 42165}, {"moveId": "BUBBLE", "uses": 34335}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 30098}, {"moveId": "MUDDY_WATER", "uses": 26508}, {"moveId": "SLUDGE_BOMB", "uses": 19937}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "MUDDY_WATER"], "score": 61.9}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 473, "matchups": [{"opponent": "swampert", "rating": 780}, {"opponent": "excadrill", "rating": 680}, {"opponent": "sylveon", "rating": 639, "opRating": 360}, {"opponent": "zacian_hero", "rating": 621}, {"opponent": "grou<PERSON>", "rating": 505, "opRating": 494}], "counters": [{"opponent": "mewtwo", "rating": 257}, {"opponent": "giratina_origin", "rating": 260}, {"opponent": "metagross", "rating": 316}, {"opponent": "garcho<PERSON>", "rating": 467}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41214}, {"moveId": "BITE", "uses": 35286}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 28476}, {"moveId": "STONE_EDGE", "uses": 16002}, {"moveId": "EARTHQUAKE", "uses": 15183}, {"moveId": "SAND_TOMB", "uses": 7395}, {"moveId": "RETURN", "uses": 6248}, {"moveId": "SOLAR_BEAM", "uses": 3141}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 61.9}, {"speciesId": "king<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 545, "matchups": [{"opponent": "entei_shadow", "rating": 820, "opRating": 179}, {"opponent": "entei", "rating": 698, "opRating": 301}, {"opponent": "thundurus_incarnate", "rating": 689, "opRating": 310}, {"opponent": "giratina_origin", "rating": 542}, {"opponent": "palkia", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "lugia", "rating": 304}, {"opponent": "metagross", "rating": 316}, {"opponent": "mewtwo", "rating": 458}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 32905}, {"moveId": "WATER_GUN", "uses": 22347}, {"moveId": "WATERFALL", "uses": 21308}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23151}, {"moveId": "OCTAZOOKA", "uses": 15353}, {"moveId": "BLIZZARD", "uses": 14864}, {"moveId": "HYDRO_PUMP", "uses": 12946}, {"moveId": "RETURN", "uses": 10018}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 61.6}, {"speciesId": "hitmonchan", "speciesName": "Hitmonchan", "rating": 486, "matchups": [{"opponent": "tyranitar", "rating": 842, "opRating": 157}, {"opponent": "tyranitar_shadow", "rating": 822, "opRating": 177}, {"opponent": "dialga", "rating": 704}, {"opponent": "melmetal", "rating": 645, "opRating": 354}, {"opponent": "excadrill", "rating": 614}], "counters": [{"opponent": "gyarados", "rating": 224}, {"opponent": "dragonite", "rating": 313}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "metagross", "rating": 316}, {"opponent": "swampert", "rating": 370}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 41656}, {"moveId": "BULLET_PUNCH", "uses": 26867}, {"moveId": "ROCK_SMASH", "uses": 7955}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23364}, {"moveId": "ICE_PUNCH", "uses": 15089}, {"moveId": "THUNDER_PUNCH", "uses": 11015}, {"moveId": "BRICK_BREAK", "uses": 10390}, {"moveId": "FIRE_PUNCH", "uses": 9989}, {"moveId": "RETURN", "uses": 4625}, {"moveId": "POWER_UP_PUNCH", "uses": 2079}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 61.4}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 539, "matchups": [{"opponent": "escavalier", "rating": 859, "opRating": 140}, {"opponent": "magnezone", "rating": 838, "opRating": 161}, {"opponent": "magnezone_shadow", "rating": 825, "opRating": 174}, {"opponent": "metagross", "rating": 617}, {"opponent": "snorlax", "rating": 590, "opRating": 409}], "counters": [{"opponent": "mewtwo", "rating": 156}, {"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "lugia", "rating": 250}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "dialga", "rating": 426}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39115}, {"moveId": "EMBER", "uses": 37385}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24765}, {"moveId": "FLAMETHROWER", "uses": 17066}, {"moveId": "OVERHEAT", "uses": 15882}, {"moveId": "LAST_RESORT", "uses": 11591}, {"moveId": "FIRE_BLAST", "uses": 4502}, {"moveId": "HEAT_WAVE", "uses": 2719}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 61.1}, {"speciesId": "politoed_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 462, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 720, "opRating": 279}, {"opponent": "landorus_incarnate", "rating": 688, "opRating": 311}, {"opponent": "mamos<PERSON>_shadow", "rating": 661, "opRating": 338}, {"opponent": "excadrill", "rating": 631}, {"opponent": "garcho<PERSON>", "rating": 518}], "counters": [{"opponent": "zacian_hero", "rating": 289}, {"opponent": "metagross", "rating": 325}, {"opponent": "mewtwo", "rating": 330}, {"opponent": "dialga", "rating": 358}, {"opponent": "giratina_origin", "rating": 386}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41709}, {"moveId": "BUBBLE", "uses": 34791}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 32480}, {"moveId": "BLIZZARD", "uses": 13382}, {"moveId": "SURF", "uses": 13207}, {"moveId": "EARTHQUAKE", "uses": 13080}, {"moveId": "HYDRO_PUMP", "uses": 4196}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "BLIZZARD"], "score": 60.9}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 516, "matchups": [{"opponent": "magnezone_shadow", "rating": 793, "opRating": 206}, {"opponent": "nihilego", "rating": 788, "opRating": 211}, {"opponent": "ho_oh", "rating": 694, "opRating": 305}, {"opponent": "zekrom", "rating": 576, "opRating": 423}, {"opponent": "excadrill", "rating": 550}], "counters": [{"opponent": "dialga", "rating": 279}, {"opponent": "metagross", "rating": 325}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "giratina_origin", "rating": 380}, {"opponent": "zacian_hero", "rating": 421}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 58345}, {"moveId": "ROCK_SMASH", "uses": 18155}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23008}, {"moveId": "SURF", "uses": 19571}, {"moveId": "EARTHQUAKE", "uses": 18351}, {"moveId": "MEGAHORN", "uses": 15514}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "SURF"], "score": 60.9}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 482, "matchups": [{"opponent": "nihilego", "rating": 676, "opRating": 323}, {"opponent": "electivire_shadow", "rating": 676, "opRating": 323}, {"opponent": "rai<PERSON>u", "rating": 640, "opRating": 359}, {"opponent": "giratina_altered", "rating": 536, "opRating": 463}, {"opponent": "zekrom", "rating": 518, "opRating": 481}], "counters": [{"opponent": "mewtwo", "rating": 322}, {"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "giratina_origin", "rating": 356}, {"opponent": "dialga", "rating": 426}, {"opponent": "lugia", "rating": 442}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 40939}, {"moveId": "THUNDER_FANG", "uses": 24419}, {"moveId": "IRON_TAIL", "uses": 11123}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22588}, {"moveId": "PSYCHIC_FANGS", "uses": 20137}, {"moveId": "EARTHQUAKE", "uses": 19167}, {"moveId": "HEAVY_SLAM", "uses": 14666}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "PSYCHIC_FANGS"], "score": 60.9}, {"speciesId": "hitmon<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 490, "matchups": [{"opponent": "tyranitar", "rating": 822, "opRating": 177}, {"opponent": "heatran", "rating": 783, "opRating": 216}, {"opponent": "dialga", "rating": 645}, {"opponent": "snorlax", "rating": 570, "opRating": 429}, {"opponent": "excadrill", "rating": 527}], "counters": [{"opponent": "giratina_origin", "rating": 189}, {"opponent": "dragonite", "rating": 367}, {"opponent": "garcho<PERSON>", "rating": 370}, {"opponent": "metagross", "rating": 377}, {"opponent": "swampert", "rating": 412}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42729}, {"moveId": "BULLET_PUNCH", "uses": 26315}, {"moveId": "ROCK_SMASH", "uses": 7468}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24713}, {"moveId": "ICE_PUNCH", "uses": 16081}, {"moveId": "THUNDER_PUNCH", "uses": 11848}, {"moveId": "BRICK_BREAK", "uses": 10972}, {"moveId": "FIRE_PUNCH", "uses": 10646}, {"moveId": "POWER_UP_PUNCH", "uses": 2161}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 60.8}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 446, "matchups": [{"opponent": "swampert", "rating": 959}, {"opponent": "mewtwo", "rating": 720}, {"opponent": "excadrill", "rating": 588}, {"opponent": "kyogre", "rating": 543, "opRating": 456}, {"opponent": "giratina_origin", "rating": 513}], "counters": [{"opponent": "zacian_hero", "rating": 199}, {"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "dialga", "rating": 239}, {"opponent": "metagross", "rating": 372}, {"opponent": "gyarados", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25663}, {"moveId": "BULLET_SEED", "uses": 23257}, {"moveId": "FEINT_ATTACK", "uses": 17610}, {"moveId": "RAZOR_LEAF", "uses": 10050}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36525}, {"moveId": "FOUL_PLAY", "uses": 24110}, {"moveId": "HURRICANE", "uses": 8838}, {"moveId": "LEAF_TORNADO", "uses": 6932}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 60.8}, {"speciesId": "tornadus_incarnate", "speciesName": "Tornadus (Incarnate)", "rating": 495, "matchups": [{"opponent": "rhyperior", "rating": 747, "opRating": 252}, {"opponent": "swampert", "rating": 717}, {"opponent": "buzzwole", "rating": 650, "opRating": 350}, {"opponent": "sneasler", "rating": 638, "opRating": 361}, {"opponent": "grou<PERSON>", "rating": 588, "opRating": 411}], "counters": [{"opponent": "giratina_origin", "rating": 350}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "gyarados", "rating": 417}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47953}, {"moveId": "BITE", "uses": 28547}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25179}, {"moveId": "GRASS_KNOT", "uses": 21794}, {"moveId": "HURRICANE", "uses": 20329}, {"moveId": "HYPER_BEAM", "uses": 9202}]}, "moveset": ["AIR_SLASH", "DARK_PULSE", "GRASS_KNOT"], "score": 60.8}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 536, "matchups": [{"opponent": "moltres", "rating": 948, "opRating": 51}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 773, "opRating": 226}, {"opponent": "ho_oh", "rating": 695, "opRating": 304}, {"opponent": "sylveon", "rating": 523, "opRating": 476}, {"opponent": "gyarados", "rating": 515}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "metagross", "rating": 299}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "giratina_origin", "rating": 398}, {"opponent": "lugia", "rating": 428}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41556}, {"moveId": "FIRE_FANG", "uses": 25103}, {"moveId": "ROCK_SMASH", "uses": 9770}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22274}, {"moveId": "ROCK_SLIDE", "uses": 21162}, {"moveId": "CRUNCH", "uses": 17820}, {"moveId": "FLAMETHROWER", "uses": 15260}]}, "moveset": ["SNARL", "WILD_CHARGE", "ROCK_SLIDE"], "score": 60.6}, {"speciesId": "cryogonal", "speciesName": "Cryogonal", "rating": 477, "matchups": [{"opponent": "zapdos", "rating": 741, "opRating": 258}, {"opponent": "zap<PERSON>_shadow", "rating": 691, "opRating": 308}, {"opponent": "landorus_incarnate", "rating": 651, "opRating": 348}, {"opponent": "garcho<PERSON>", "rating": 625}, {"opponent": "latios_shadow", "rating": 572, "opRating": 427}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "gyarados", "rating": 350}, {"opponent": "giratina_origin", "rating": 418}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 44983}, {"moveId": "FROST_BREATH", "uses": 31517}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39077}, {"moveId": "AURORA_BEAM", "uses": 19593}, {"moveId": "SOLAR_BEAM", "uses": 10056}, {"moveId": "WATER_PULSE", "uses": 7783}]}, "moveset": ["ICE_SHARD", "NIGHT_SLASH", "AURORA_BEAM"], "score": 60.6}, {"speciesId": "clefable", "speciesName": "Clefable", "rating": 463, "matchups": [{"opponent": "dragonite", "rating": 719}, {"opponent": "yveltal", "rating": 680, "opRating": 319}, {"opponent": "gyarados", "rating": 561}, {"opponent": "zekrom", "rating": 530, "opRating": 469}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 512, "opRating": 487}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "dialga", "rating": 239}, {"opponent": "giratina_origin", "rating": 358}, {"opponent": "garcho<PERSON>", "rating": 401}, {"opponent": "zacian_hero", "rating": 491}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 41471}, {"moveId": "CHARGE_BEAM", "uses": 26392}, {"moveId": "ZEN_HEADBUTT", "uses": 5594}, {"moveId": "POUND", "uses": 3071}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28511}, {"moveId": "METEOR_MASH", "uses": 25674}, {"moveId": "PSYCHIC", "uses": 14995}, {"moveId": "DAZZLING_GLEAM", "uses": 7275}]}, "moveset": ["CHARM", "MOONBLAST", "METEOR_MASH"], "score": 60.5}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 509, "matchups": [{"opponent": "magnezone_shadow", "rating": 901, "opRating": 98}, {"opponent": "nihilego", "rating": 880, "opRating": 119}, {"opponent": "sylveon", "rating": 593, "opRating": 406}, {"opponent": "zekrom", "rating": 553, "opRating": 446}, {"opponent": "excadrill", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 260}, {"opponent": "lugia", "rating": 326}, {"opponent": "gyarados", "rating": 350}, {"opponent": "metagross", "rating": 363}, {"opponent": "dialga", "rating": 394}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 50486}, {"moveId": "METAL_CLAW", "uses": 26014}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26259}, {"moveId": "EARTHQUAKE", "uses": 23729}, {"moveId": "MUDDY_WATER", "uses": 15307}, {"moveId": "FLASH_CANNON", "uses": 11207}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 60.5}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 522, "matchups": [{"opponent": "melmetal", "rating": 732, "opRating": 267}, {"opponent": "zarude", "rating": 720, "opRating": 279}, {"opponent": "genesect_chill", "rating": 711, "opRating": 288}, {"opponent": "genesect_burn", "rating": 711, "opRating": 288}, {"opponent": "metagross", "rating": 655}], "counters": [{"opponent": "giratina_origin", "rating": 231}, {"opponent": "dragonite", "rating": 351}, {"opponent": "gyarados", "rating": 381}, {"opponent": "dialga", "rating": 448}, {"opponent": "zacian_hero", "rating": 456}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 62042}, {"moveId": "LOW_KICK", "uses": 14458}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 31123}, {"moveId": "ROCK_SLIDE", "uses": 20750}, {"moveId": "FOCUS_BLAST", "uses": 13645}, {"moveId": "FLAME_CHARGE", "uses": 8563}, {"moveId": "HEAT_WAVE", "uses": 2346}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 60.3}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 514, "matchups": [{"opponent": "weavile", "rating": 961, "opRating": 38}, {"opponent": "weavile_shadow", "rating": 952, "opRating": 47}, {"opponent": "tyranitar", "rating": 888, "opRating": 111}, {"opponent": "tyranitar_shadow", "rating": 872, "opRating": 127}, {"opponent": "dialga", "rating": 745}], "counters": [{"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "lugia", "rating": 266}, {"opponent": "gyarados", "rating": 365}, {"opponent": "swampert", "rating": 393}, {"opponent": "metagross", "rating": 497}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44490}, {"moveId": "BULLET_PUNCH", "uses": 32010}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34742}, {"moveId": "SHADOW_BALL", "uses": 18795}, {"moveId": "FLASH_CANNON", "uses": 8942}, {"moveId": "AURA_SPHERE", "uses": 7794}, {"moveId": "POWER_UP_PUNCH", "uses": 6144}]}, "moveset": ["COUNTER", "POWER_UP_PUNCH", "SHADOW_BALL"], "score": 60.3}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 518, "matchups": [{"opponent": "heracross", "rating": 789, "opRating": 210}, {"opponent": "zap<PERSON>_galarian", "rating": 753, "opRating": 246}, {"opponent": "kommo_o", "rating": 679, "opRating": 320}, {"opponent": "terrakion", "rating": 652, "opRating": 347}, {"opponent": "gyarados", "rating": 618}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "excadrill", "rating": 348}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42737}, {"moveId": "EXTRASENSORY", "uses": 33763}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 35878}, {"moveId": "THUNDER", "uses": 28077}, {"moveId": "SWIFT", "uses": 12529}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "THUNDER"], "score": 60.3}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 465, "matchups": [{"opponent": "rhyperior", "rating": 870, "opRating": 129}, {"opponent": "swampert", "rating": 830}, {"opponent": "dragonite_shadow", "rating": 736, "opRating": 263}, {"opponent": "kyogre", "rating": 615, "opRating": 384}, {"opponent": "garcho<PERSON>", "rating": 559}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "giratina_origin", "rating": 382}, {"opponent": "gyarados", "rating": 417}, {"opponent": "dragonite", "rating": 452}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 54362}, {"moveId": "RAZOR_LEAF", "uses": 22138}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39321}, {"moveId": "ENERGY_BALL", "uses": 14454}, {"moveId": "OUTRAGE", "uses": 10301}, {"moveId": "RETURN", "uses": 6600}, {"moveId": "BLIZZARD", "uses": 5986}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 60}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 527, "matchups": [{"opponent": "metagross", "rating": 879}, {"opponent": "metagross_shadow", "rating": 855, "opRating": 144}, {"opponent": "genesect_chill", "rating": 716, "opRating": 283}, {"opponent": "genesect_burn", "rating": 716, "opRating": 283}, {"opponent": "melmetal", "rating": 683, "opRating": 316}], "counters": [{"opponent": "giratina_origin", "rating": 185}, {"opponent": "lugia", "rating": 221}, {"opponent": "gyarados", "rating": 257}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "dialga", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 58851}, {"moveId": "ROCK_SMASH", "uses": 17649}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 33657}, {"moveId": "BLAST_BURN", "uses": 29726}, {"moveId": "FLAMETHROWER", "uses": 6849}, {"moveId": "SOLAR_BEAM", "uses": 6250}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 60}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 463, "matchups": [{"opponent": "gengar", "rating": 837, "opRating": 162}, {"opponent": "giratina_origin", "rating": 697}, {"opponent": "mew", "rating": 641, "opRating": 358}, {"opponent": "giratina_altered", "rating": 630, "opRating": 369}, {"opponent": "mewtwo_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "gyarados", "rating": 350}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66132}, {"moveId": "ZEN_HEADBUTT", "uses": 10368}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35298}, {"moveId": "SHADOW_BALL", "uses": 17280}, {"moveId": "EARTHQUAKE", "uses": 13376}, {"moveId": "SOLAR_BEAM", "uses": 6583}, {"moveId": "HYPER_BEAM", "uses": 3917}]}, "moveset": ["LICK", "BODY_SLAM", "SHADOW_BALL"], "score": 60}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 495, "matchups": [{"opponent": "tapu_koko", "rating": 965, "opRating": 34}, {"opponent": "raikou_shadow", "rating": 843, "opRating": 156}, {"opponent": "magnezone_shadow", "rating": 805, "opRating": 194}, {"opponent": "metagross_shadow", "rating": 776, "opRating": 223}, {"opponent": "excadrill", "rating": 590}], "counters": [{"opponent": "mewtwo", "rating": 200}, {"opponent": "garcho<PERSON>", "rating": 230}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "metagross", "rating": 459}, {"opponent": "dialga", "rating": 467}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40514}, {"moveId": "DRAGON_TAIL", "uses": 35986}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30281}, {"moveId": "EARTH_POWER", "uses": 20164}, {"moveId": "STONE_EDGE", "uses": 17387}, {"moveId": "EARTHQUAKE", "uses": 8760}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 59.8}, {"speciesId": "hoopa", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 509, "matchups": [{"opponent": "swampert", "rating": 694}, {"opponent": "sylveon", "rating": 643, "opRating": 356}, {"opponent": "zacian_hero", "rating": 617}, {"opponent": "excadrill", "rating": 617}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 617}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "gyarados", "rating": 185}, {"opponent": "lugia", "rating": 352}, {"opponent": "garcho<PERSON>", "rating": 443}, {"opponent": "dragonite", "rating": 497}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 52595}, {"moveId": "ASTONISH", "uses": 23905}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 43479}, {"moveId": "PSYCHIC", "uses": 27344}, {"moveId": "PSYBEAM", "uses": 5672}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 59.8}, {"speciesId": "ninetales_alolan", "speciesName": "Ninetales (Alolan)", "rating": 453, "matchups": [{"opponent": "dragonite_shadow", "rating": 835, "opRating": 164}, {"opponent": "latios_shadow", "rating": 804, "opRating": 195}, {"opponent": "dragonite", "rating": 652}, {"opponent": "garcho<PERSON>", "rating": 596}, {"opponent": "zekrom", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "giratina_origin", "rating": 382}, {"opponent": "gyarados", "rating": 479}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37512}, {"moveId": "CHARM", "uses": 21091}, {"moveId": "FEINT_ATTACK", "uses": 17932}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39773}, {"moveId": "PSYSHOCK", "uses": 12580}, {"moveId": "DAZZLING_GLEAM", "uses": 9504}, {"moveId": "ICE_BEAM", "uses": 8582}, {"moveId": "BLIZZARD", "uses": 5945}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "PSYSHOCK"], "score": 59.8}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 505, "matchups": [{"opponent": "weavile", "rating": 907, "opRating": 92}, {"opponent": "hoopa_unbound", "rating": 837, "opRating": 162}, {"opponent": "mew", "rating": 613, "opRating": 386}, {"opponent": "zarude", "rating": 592, "opRating": 407}, {"opponent": "giratina_origin", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 203}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "metagross", "rating": 354}, {"opponent": "mewtwo", "rating": 395}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 65727}, {"moveId": "ZEN_HEADBUTT", "uses": 10773}], "chargedMoves": [{"moveId": "STOMP", "uses": 26194}, {"moveId": "MEGAHORN", "uses": 21413}, {"moveId": "EARTHQUAKE", "uses": 19748}, {"moveId": "SKULL_BASH", "uses": 9110}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 59.7}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 540, "matchups": [{"opponent": "registeel", "rating": 955, "opRating": 44}, {"opponent": "metagross", "rating": 857}, {"opponent": "metagross_shadow", "rating": 833, "opRating": 166}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 785, "opRating": 214}, {"opponent": "sylveon", "rating": 547, "opRating": 452}], "counters": [{"opponent": "lugia", "rating": 285}, {"opponent": "grou<PERSON>", "rating": 388}, {"opponent": "dialga", "rating": 418}, {"opponent": "zacian_hero", "rating": 445}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 466}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31358}, {"moveId": "SHADOW_CLAW", "uses": 29083}, {"moveId": "EMBER", "uses": 16018}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 48147}, {"moveId": "SOLAR_BEAM", "uses": 12012}, {"moveId": "OVERHEAT", "uses": 10321}, {"moveId": "FIRE_BLAST", "uses": 6014}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 59.7}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 507, "matchups": [{"opponent": "arcanine_shadow", "rating": 901, "opRating": 98}, {"opponent": "typhlosion_shadow", "rating": 848, "opRating": 151}, {"opponent": "charizard_shadow", "rating": 831, "opRating": 168}, {"opponent": "electivire_shadow", "rating": 564, "opRating": 435}, {"opponent": "sylveon", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "giratina_origin", "rating": 338}, {"opponent": "gyarados", "rating": 340}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "garcho<PERSON>", "rating": 427}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 8519}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5132}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4492}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4374}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4291}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4288}, {"moveId": "CHARGE_BEAM", "uses": 4250}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4095}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4031}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3958}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3910}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3875}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3869}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3611}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3575}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3519}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3413}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3305}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 26306}, {"moveId": "RETURN", "uses": 16645}, {"moveId": "ZAP_CANNON", "uses": 16190}, {"moveId": "SOLAR_BEAM", "uses": 10798}, {"moveId": "HYPER_BEAM", "uses": 6445}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "RETURN"], "score": 59.5}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 502, "matchups": [{"opponent": "swampert", "rating": 878}, {"opponent": "swampert_shadow", "rating": 845, "opRating": 154}, {"opponent": "kyogre", "rating": 768, "opRating": 231}, {"opponent": "rai<PERSON>u", "rating": 664, "opRating": 335}, {"opponent": "grou<PERSON>", "rating": 600, "opRating": 399}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "giratina_origin", "rating": 318}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "excadrill", "rating": 495}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43074}, {"moveId": "BULLET_SEED", "uses": 33426}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 23519}, {"moveId": "DRAGON_PULSE", "uses": 16640}, {"moveId": "DRACO_METEOR", "uses": 14865}, {"moveId": "SOLAR_BEAM", "uses": 10703}, {"moveId": "RETURN", "uses": 10616}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 59.3}, {"speciesId": "mr_rime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 465, "matchups": [{"opponent": "thundurus_therian", "rating": 825, "opRating": 174}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 813, "opRating": 186}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 677, "opRating": 322}, {"opponent": "dragonite", "rating": 662}, {"opponent": "garcho<PERSON>", "rating": 561}], "counters": [{"opponent": "mewtwo", "rating": 223}, {"opponent": "dialga", "rating": 239}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "lugia", "rating": 380}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38575}, {"moveId": "CONFUSION", "uses": 31235}, {"moveId": "ZEN_HEADBUTT", "uses": 6662}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 30903}, {"moveId": "ICE_PUNCH", "uses": 26747}, {"moveId": "PSYCHIC", "uses": 15578}, {"moveId": "PSYBEAM", "uses": 3093}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ICE_PUNCH"], "score": 59.3}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 506, "matchups": [{"opponent": "magnezone", "rating": 862, "opRating": 137}, {"opponent": "magnezone_shadow", "rating": 841, "opRating": 158}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 811}, {"opponent": "sylveon", "rating": 784, "opRating": 215}, {"opponent": "zacian_hero", "rating": 669}], "counters": [{"opponent": "giratina_origin", "rating": 239}, {"opponent": "metagross", "rating": 316}, {"opponent": "dialga", "rating": 317}, {"opponent": "dragonite", "rating": 375}, {"opponent": "gyarados", "rating": 386}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 53180}, {"moveId": "BITE", "uses": 23320}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 20513}, {"moveId": "EARTH_POWER", "uses": 19265}, {"moveId": "STONE_EDGE", "uses": 14094}, {"moveId": "EARTHQUAKE", "uses": 8286}, {"moveId": "SLUDGE_WAVE", "uses": 8238}, {"moveId": "RETURN", "uses": 6265}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 59.3}, {"speciesId": "darmanitan_galarian_standard", "speciesName": "Dar<PERSON><PERSON> (Galarian)", "rating": 474, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 711}, {"opponent": "giratina_altered", "rating": 572, "opRating": 427}, {"opponent": "yveltal", "rating": 560, "opRating": 439}, {"opponent": "excadrill", "rating": 509}, {"opponent": "lugia", "rating": 502}], "counters": [{"opponent": "mewtwo", "rating": 164}, {"opponent": "dialga", "rating": 317}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "dragonite", "rating": 390}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 43427}, {"moveId": "TACKLE", "uses": 33073}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30936}, {"moveId": "ICE_PUNCH", "uses": 18577}, {"moveId": "SUPER_POWER", "uses": 18555}, {"moveId": "OVERHEAT", "uses": 8407}]}, "moveset": ["ICE_FANG", "AVALANCHE", "ICE_PUNCH"], "score": 59.2}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 487, "matchups": [{"opponent": "tapu_koko", "rating": 965, "opRating": 34}, {"opponent": "heatran", "rating": 898, "opRating": 101}, {"opponent": "xurkitree", "rating": 889, "opRating": 110}, {"opponent": "magnezone_shadow", "rating": 831, "opRating": 168}, {"opponent": "excadrill", "rating": 636}], "counters": [{"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "giratina_origin", "rating": 324}, {"opponent": "metagross", "rating": 377}, {"opponent": "dialga", "rating": 385}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39986}, {"moveId": "DRAGON_TAIL", "uses": 36514}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27577}, {"moveId": "EARTH_POWER", "uses": 18520}, {"moveId": "STONE_EDGE", "uses": 15645}, {"moveId": "EARTHQUAKE", "uses": 7870}, {"moveId": "RETURN", "uses": 6739}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 59}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 476, "matchups": [{"opponent": "swampert", "rating": 787}, {"opponent": "dragonite_shadow", "rating": 763, "opRating": 236}, {"opponent": "dragonite", "rating": 736}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 661, "opRating": 338}, {"opponent": "kyogre", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 350}, {"opponent": "garcho<PERSON>", "rating": 457}, {"opponent": "excadrill", "rating": 467}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 55045}, {"moveId": "RAZOR_LEAF", "uses": 21455}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 42682}, {"moveId": "ENERGY_BALL", "uses": 16068}, {"moveId": "OUTRAGE", "uses": 11237}, {"moveId": "BLIZZARD", "uses": 6489}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 58.9}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 472, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 801, "opRating": 198}, {"opponent": "gallade_shadow", "rating": 716, "opRating": 283}, {"opponent": "swampert", "rating": 695}, {"opponent": "excadrill", "rating": 560}, {"opponent": "latios_shadow", "rating": 557, "opRating": 442}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "gyarados", "rating": 407}, {"opponent": "metagross", "rating": 430}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49479}, {"moveId": "RAZOR_LEAF", "uses": 27021}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26376}, {"moveId": "SEED_BOMB", "uses": 21777}, {"moveId": "FOUL_PLAY", "uses": 19813}, {"moveId": "FIRE_BLAST", "uses": 8615}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 58.9}, {"speciesId": "magneton_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 492, "matchups": [{"opponent": "staraptor", "rating": 921, "opRating": 78}, {"opponent": "staraptor_shadow", "rating": 901, "opRating": 98}, {"opponent": "gyarados", "rating": 870}, {"opponent": "sylveon", "rating": 669, "opRating": 330}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 641, "opRating": 358}], "counters": [{"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "dialga", "rating": 293}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "lugia", "rating": 466}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30907}, {"moveId": "SPARK", "uses": 28185}, {"moveId": "CHARGE_BEAM", "uses": 17442}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 32000}, {"moveId": "DISCHARGE", "uses": 27007}, {"moveId": "ZAP_CANNON", "uses": 10693}, {"moveId": "FLASH_CANNON", "uses": 6786}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 58.9}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 445, "matchups": [{"opponent": "mewtwo_shadow", "rating": 786, "opRating": 213}, {"opponent": "mewtwo", "rating": 649}, {"opponent": "giratina_origin", "rating": 600}, {"opponent": "latios_shadow", "rating": 556, "opRating": 443}, {"opponent": "giratina_altered", "rating": 518, "opRating": 481}], "counters": [{"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "dialga", "rating": 247}, {"opponent": "lugia", "rating": 350}, {"opponent": "gyarados", "rating": 386}, {"opponent": "metagross", "rating": 412}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42113}, {"moveId": "FEINT_ATTACK", "uses": 34387}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 33517}, {"moveId": "PSYCHIC", "uses": 15068}, {"moveId": "DARK_PULSE", "uses": 14254}, {"moveId": "LAST_RESORT", "uses": 13651}]}, "moveset": ["SNARL", "FOUL_PLAY", "PSYCHIC"], "score": 58.9}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 565, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 923, "opRating": 76}, {"opponent": "magnezone_shadow", "rating": 893, "opRating": 106}, {"opponent": "genesect_douse", "rating": 823, "opRating": 176}, {"opponent": "genesect_shock", "rating": 823, "opRating": 176}, {"opponent": "genesect_chill", "rating": 823, "opRating": 176}], "counters": [{"opponent": "garcho<PERSON>", "rating": 166}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "lugia", "rating": 273}, {"opponent": "dialga", "rating": 336}, {"opponent": "metagross", "rating": 389}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39964}, {"moveId": "FIRE_SPIN", "uses": 36536}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 25610}, {"moveId": "BRICK_BREAK", "uses": 17302}, {"moveId": "THUNDERBOLT", "uses": 15009}, {"moveId": "PSYCHIC", "uses": 13390}, {"moveId": "FIRE_BLAST", "uses": 5124}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 58.7}, {"speciesId": "mandibuzz", "speciesName": "Mandibuzz", "rating": 437, "matchups": [{"opponent": "mewtwo_shadow", "rating": 786, "opRating": 213}, {"opponent": "celebi", "rating": 776, "opRating": 223}, {"opponent": "mewtwo", "rating": 665}, {"opponent": "mewtwo_armored", "rating": 648, "opRating": 351}, {"opponent": "giratina_origin", "rating": 581}], "counters": [{"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "dialga", "rating": 247}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "gyarados", "rating": 394}, {"opponent": "metagross", "rating": 418}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44601}, {"moveId": "AIR_SLASH", "uses": 31899}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 28272}, {"moveId": "SHADOW_BALL", "uses": 18645}, {"moveId": "AERIAL_ACE", "uses": 17584}, {"moveId": "DARK_PULSE", "uses": 12092}]}, "moveset": ["SNARL", "FOUL_PLAY", "SHADOW_BALL"], "score": 58.5}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 507, "matchups": [{"opponent": "swampert", "rating": 845}, {"opponent": "swampert_shadow", "rating": 824, "opRating": 175}, {"opponent": "kyogre", "rating": 729, "opRating": 270}, {"opponent": "snorlax", "rating": 636, "opRating": 363}, {"opponent": "grou<PERSON>", "rating": 597}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "giratina_origin", "rating": 239}, {"opponent": "dialga", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "excadrill", "rating": 416}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42480}, {"moveId": "BULLET_SEED", "uses": 34020}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26947}, {"moveId": "DRAGON_PULSE", "uses": 19516}, {"moveId": "DRACO_METEOR", "uses": 17435}, {"moveId": "SOLAR_BEAM", "uses": 12501}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 58.2}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 494, "matchups": [{"opponent": "buzzwole", "rating": 598, "opRating": 401}, {"opponent": "gyarados", "rating": 555}, {"opponent": "swampert", "rating": 552}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 511, "opRating": 488}, {"opponent": "ho_oh", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "metagross", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 438}, {"opponent": "zacian_hero", "rating": 488}, {"opponent": "dragonite", "rating": 489}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42798}, {"moveId": "EXTRASENSORY", "uses": 33702}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 33684}, {"moveId": "BLIZZARD", "uses": 30355}, {"moveId": "SWIFT", "uses": 12371}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "BLIZZARD"], "score": 58.2}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 452, "matchups": [{"opponent": "swampert", "rating": 750}, {"opponent": "gyarados", "rating": 600}, {"opponent": "excadrill", "rating": 597}, {"opponent": "garcho<PERSON>", "rating": 554}, {"opponent": "zacian_hero", "rating": 525}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "metagross", "rating": 174}, {"opponent": "giratina_origin", "rating": 292}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "grou<PERSON>", "rating": 380}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41899}, {"moveId": "BITE", "uses": 34601}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30946}, {"moveId": "STONE_EDGE", "uses": 17849}, {"moveId": "EARTHQUAKE", "uses": 16339}, {"moveId": "SAND_TOMB", "uses": 7884}, {"moveId": "SOLAR_BEAM", "uses": 3524}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 58.2}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 431, "matchups": [{"opponent": "electivire_shadow", "rating": 697, "opRating": 302}, {"opponent": "magnezone_shadow", "rating": 695, "opRating": 304}, {"opponent": "nihilego", "rating": 690, "opRating": 309}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 644, "opRating": 355}, {"opponent": "dragonite", "rating": 518}], "counters": [{"opponent": "mewtwo", "rating": 252}, {"opponent": "dialga", "rating": 358}, {"opponent": "metagross", "rating": 377}, {"opponent": "excadrill", "rating": 476}, {"opponent": "garcho<PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 42162}, {"moveId": "WATER_GUN", "uses": 34338}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 41460}, {"moveId": "BLIZZARD", "uses": 22866}, {"moveId": "WATER_PULSE", "uses": 12187}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "BLIZZARD"], "score": 58.2}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 508, "matchups": [{"opponent": "tapu_bulu", "rating": 943, "opRating": 56}, {"opponent": "magnezone_shadow", "rating": 801, "opRating": 198}, {"opponent": "sylveon", "rating": 793, "opRating": 206}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 774}, {"opponent": "zacian_hero", "rating": 618}], "counters": [{"opponent": "giratina_origin", "rating": 235}, {"opponent": "gyarados", "rating": 247}, {"opponent": "lugia", "rating": 266}, {"opponent": "dragonite", "rating": 279}, {"opponent": "dialga", "rating": 383}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 55814}, {"moveId": "BITE", "uses": 20686}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22466}, {"moveId": "EARTH_POWER", "uses": 20740}, {"moveId": "STONE_EDGE", "uses": 15240}, {"moveId": "SLUDGE_WAVE", "uses": 8961}, {"moveId": "EARTHQUAKE", "uses": 8917}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 58.1}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 463, "matchups": [{"opponent": "metagross", "rating": 829}, {"opponent": "metagross_shadow", "rating": 804, "opRating": 195}, {"opponent": "mewtwo_shadow", "rating": 713, "opRating": 286}, {"opponent": "mewtwo", "rating": 707}, {"opponent": "giratina_origin", "rating": 527}], "counters": [{"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "lugia", "rating": 271}, {"opponent": "grou<PERSON>", "rating": 293}, {"opponent": "dialga", "rating": 312}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46854}, {"moveId": "FIRE_FANG", "uses": 29646}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34738}, {"moveId": "FLAMETHROWER", "uses": 20952}, {"moveId": "FOUL_PLAY", "uses": 15010}, {"moveId": "FIRE_BLAST", "uses": 5839}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 57.9}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 499, "matchups": [{"opponent": "gengar", "rating": 764, "opRating": 235}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 733, "opRating": 266}, {"opponent": "suicune_shadow", "rating": 691, "opRating": 308}, {"opponent": "tapu_fini", "rating": 685, "opRating": 314}, {"opponent": "gyarados", "rating": 575}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "giratina_origin", "rating": 406}, {"opponent": "mewtwo", "rating": 424}, {"opponent": "lugia", "rating": 450}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9414}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4985}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4452}, {"moveId": "CHARGE_BEAM", "uses": 4415}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4271}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4208}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4206}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4030}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3969}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3895}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3844}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3816}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3790}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3538}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3535}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3477}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3336}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3215}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 30733}, {"moveId": "ZAP_CANNON", "uses": 18352}, {"moveId": "HYPER_BEAM", "uses": 14987}, {"moveId": "SOLAR_BEAM", "uses": 12289}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 57.6}, {"speciesId": "cloyster_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 420, "matchups": [{"opponent": "thundurus_therian", "rating": 669, "opRating": 330}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 614, "opRating": 385}, {"opponent": "ma<PERSON><PERSON>", "rating": 602, "opRating": 397}, {"opponent": "latios", "rating": 570, "opRating": 429}, {"opponent": "garcho<PERSON>", "rating": 503}], "counters": [{"opponent": "dialga", "rating": 312}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "lugia", "rating": 371}, {"opponent": "gyarados", "rating": 404}, {"opponent": "giratina_origin", "rating": 408}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 48325}, {"moveId": "FROST_BREATH", "uses": 28175}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 40015}, {"moveId": "ICY_WIND", "uses": 13922}, {"moveId": "HYDRO_PUMP", "uses": 11231}, {"moveId": "BLIZZARD", "uses": 6172}, {"moveId": "AURORA_BEAM", "uses": 5031}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 57.3}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 452, "matchups": [{"opponent": "gengar", "rating": 747, "opRating": 252}, {"opponent": "terrakion", "rating": 645, "opRating": 354}, {"opponent": "sneasler", "rating": 612, "opRating": 387}, {"opponent": "electivire_shadow", "rating": 569, "opRating": 430}, {"opponent": "mewtwo", "rating": 526}], "counters": [{"opponent": "dialga", "rating": 195}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "zacian_hero", "rating": 335}, {"opponent": "gyarados", "rating": 350}, {"opponent": "giratina_origin", "rating": 478}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 61781}, {"moveId": "ZEN_HEADBUTT", "uses": 12843}, {"moveId": "YAWN", "uses": 1940}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 36411}, {"moveId": "PSYCHIC", "uses": 27932}, {"moveId": "FUTURE_SIGHT", "uses": 12180}]}, "moveset": ["CONFUSION", "FOUL_PLAY", "PSYCHIC"], "score": 57.3}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 463, "matchups": [{"opponent": "muk_alolan", "rating": 728, "opRating": 271}, {"opponent": "celebi", "rating": 695, "opRating": 304}, {"opponent": "venusaur_shadow", "rating": 689, "opRating": 310}, {"opponent": "over<PERSON><PERSON>l", "rating": 682, "opRating": 317}, {"opponent": "magnezone", "rating": 591, "opRating": 408}], "counters": [{"opponent": "giratina_origin", "rating": 239}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dialga", "rating": 391}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "metagross", "rating": 444}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47762}, {"moveId": "STRUGGLE_BUG", "uses": 28738}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 18199}, {"moveId": "EARTHQUAKE", "uses": 15506}, {"moveId": "HEAVY_SLAM", "uses": 14780}, {"moveId": "ROCK_TOMB", "uses": 11352}, {"moveId": "RETURN", "uses": 8912}, {"moveId": "SAND_TOMB", "uses": 7655}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 57.1}, {"speciesId": "gran<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 465, "matchups": [{"opponent": "tyranitar", "rating": 774, "opRating": 225}, {"opponent": "kyurem", "rating": 733, "opRating": 266}, {"opponent": "snorlax", "rating": 548, "opRating": 451}, {"opponent": "excadrill", "rating": 545}, {"opponent": "palkia", "rating": 543, "opRating": 456}], "counters": [{"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "metagross", "rating": 325}, {"opponent": "lugia", "rating": 378}, {"opponent": "dialga", "rating": 396}, {"opponent": "gyarados", "rating": 422}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37414}, {"moveId": "CHARM", "uses": 25863}, {"moveId": "BITE", "uses": 13248}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34084}, {"moveId": "CRUNCH", "uses": 25731}, {"moveId": "PLAY_ROUGH", "uses": 16578}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 57.1}, {"speciesId": "muk_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 522, "matchups": [{"opponent": "tapu_bulu", "rating": 927, "opRating": 72}, {"opponent": "gyarado<PERSON>_shadow", "rating": 798, "opRating": 201}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 750}, {"opponent": "gyarados", "rating": 557}, {"opponent": "sylveon", "rating": 543, "opRating": 456}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "lugia", "rating": 304}, {"opponent": "giratina_origin", "rating": 312}, {"opponent": "dragonite", "rating": 367}, {"opponent": "zacian_hero", "rating": 427}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 31988}, {"moveId": "LICK", "uses": 23174}, {"moveId": "INFESTATION", "uses": 21348}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27103}, {"moveId": "THUNDER_PUNCH", "uses": 22898}, {"moveId": "SLUDGE_WAVE", "uses": 15305}, {"moveId": "GUNK_SHOT", "uses": 6016}, {"moveId": "ACID_SPRAY", "uses": 5134}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 57.1}, {"speciesId": "primeape", "speciesName": "Primeape", "rating": 468, "matchups": [{"opponent": "tyranitar", "rating": 815, "opRating": 184}, {"opponent": "heatran", "rating": 781, "opRating": 218}, {"opponent": "dialga", "rating": 697}, {"opponent": "snorlax", "rating": 567, "opRating": 432}, {"opponent": "excadrill", "rating": 526}], "counters": [{"opponent": "giratina_origin", "rating": 151}, {"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "gyarados", "rating": 195}, {"opponent": "metagross", "rating": 331}, {"opponent": "swampert", "rating": 368}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37352}, {"moveId": "KARATE_CHOP", "uses": 34488}, {"moveId": "LOW_KICK", "uses": 4656}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23352}, {"moveId": "NIGHT_SLASH", "uses": 18720}, {"moveId": "CROSS_CHOP", "uses": 16158}, {"moveId": "ICE_PUNCH", "uses": 15213}, {"moveId": "LOW_SWEEP", "uses": 3025}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 57.1}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 470, "matchups": [{"opponent": "chandelure", "rating": 848, "opRating": 151}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 784, "opRating": 215}, {"opponent": "magnezone", "rating": 705, "opRating": 294}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 697, "opRating": 302}, {"opponent": "magnezone_shadow", "rating": 669, "opRating": 330}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "lugia", "rating": 319}, {"opponent": "metagross", "rating": 424}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 60572}, {"moveId": "ASTONISH", "uses": 15928}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 41486}, {"moveId": "ROCK_TOMB", "uses": 17594}, {"moveId": "SAND_TOMB", "uses": 17427}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "ROCK_TOMB"], "score": 57.1}, {"speciesId": "scrafty", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 461, "matchups": [{"opponent": "tyranitar_shadow", "rating": 812, "opRating": 187}, {"opponent": "weavile", "rating": 708, "opRating": 291}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 651, "opRating": 348}, {"opponent": "tyranitar", "rating": 651, "opRating": 348}, {"opponent": "mewtwo", "rating": 523}], "counters": [{"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "metagross", "rating": 316}, {"opponent": "dialga", "rating": 336}, {"opponent": "giratina_origin", "rating": 408}, {"opponent": "excadrill", "rating": 409}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43139}, {"moveId": "SNARL", "uses": 33361}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 50786}, {"moveId": "POWER_UP_PUNCH", "uses": 19029}, {"moveId": "ACID_SPRAY", "uses": 6723}]}, "moveset": ["COUNTER", "FOUL_PLAY", "POWER_UP_PUNCH"], "score": 57}, {"speciesId": "d<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 478, "matchups": [{"opponent": "venusaur", "rating": 676, "opRating": 323}, {"opponent": "mew", "rating": 670, "opRating": 329}, {"opponent": "tangrowth_shadow", "rating": 643, "opRating": 356}, {"opponent": "luxray_shadow", "rating": 643, "opRating": 356}, {"opponent": "moltres", "rating": 577, "opRating": 422}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "giratina_origin", "rating": 368}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 52915}, {"moveId": "BITE", "uses": 23585}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 35913}, {"moveId": "NIGHT_SLASH", "uses": 32849}, {"moveId": "HYPER_BEAM", "uses": 7679}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 56.8}, {"speciesId": "falinks", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 495, "matchups": [{"opponent": "tyranitar", "rating": 879, "opRating": 120}, {"opponent": "tyranitar_shadow", "rating": 852, "opRating": 147}, {"opponent": "melmetal", "rating": 667, "opRating": 332}, {"opponent": "excadrill", "rating": 624}, {"opponent": "snorlax", "rating": 583, "opRating": 416}], "counters": [{"opponent": "zacian_hero", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "gyarados", "rating": 275}, {"opponent": "metagross", "rating": 287}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 62605}, {"moveId": "ROCK_SMASH", "uses": 13895}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34650}, {"moveId": "MEGAHORN", "uses": 20917}, {"moveId": "BRICK_BREAK", "uses": 20867}]}, "moveset": ["COUNTER", "SUPER_POWER", "MEGAHORN"], "score": 56.8}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 517, "matchups": [{"opponent": "tapu_bulu", "rating": 793, "opRating": 206}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 665}, {"opponent": "sylveon", "rating": 579, "opRating": 420}, {"opponent": "gyarados", "rating": 514}, {"opponent": "zacian_hero", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "lugia", "rating": 250}, {"opponent": "metagross", "rating": 258}, {"opponent": "giratina_origin", "rating": 272}, {"opponent": "dragonite", "rating": 327}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26593}, {"moveId": "LICK", "uses": 20138}, {"moveId": "INFESTATION", "uses": 19435}, {"moveId": "ACID", "uses": 10404}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 23315}, {"moveId": "THUNDER_PUNCH", "uses": 20192}, {"moveId": "SLUDGE_WAVE", "uses": 13014}, {"moveId": "RETURN", "uses": 10272}, {"moveId": "GUNK_SHOT", "uses": 5130}, {"moveId": "ACID_SPRAY", "uses": 4441}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 56.8}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 504, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 816}, {"opponent": "dragonite", "rating": 601}, {"opponent": "sylveon", "rating": 584, "opRating": 415}, {"opponent": "zacian_hero", "rating": 517}, {"opponent": "ho_oh", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "lugia", "rating": 309}, {"opponent": "excadrill", "rating": 339}, {"opponent": "giratina_origin", "rating": 364}, {"opponent": "gyarados", "rating": 404}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56264}, {"moveId": "ACID", "uses": 20236}], "chargedMoves": [{"moveId": "SCALD", "uses": 30995}, {"moveId": "BLIZZARD", "uses": 17589}, {"moveId": "SLUDGE_WAVE", "uses": 15567}, {"moveId": "HYDRO_PUMP", "uses": 6946}, {"moveId": "ACID_SPRAY", "uses": 5304}]}, "moveset": ["POISON_JAB", "SCALD", "BLIZZARD"], "score": 56.8}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 441, "matchups": [{"opponent": "salamence", "rating": 783, "opRating": 216}, {"opponent": "thundurus_therian", "rating": 716, "opRating": 283}, {"opponent": "ma<PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "garcho<PERSON>", "rating": 570}, {"opponent": "latios_shadow", "rating": 570, "opRating": 429}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "lugia", "rating": 300}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "gyarados", "rating": 368}, {"opponent": "giratina_origin", "rating": 414}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47660}, {"moveId": "FROST_BREATH", "uses": 28840}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36476}, {"moveId": "ICY_WIND", "uses": 12697}, {"moveId": "HYDRO_PUMP", "uses": 10099}, {"moveId": "RETURN", "uses": 7038}, {"moveId": "BLIZZARD", "uses": 5643}, {"moveId": "AURORA_BEAM", "uses": 4557}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 56.6}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 433, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 797, "opRating": 202}, {"opponent": "escavalier", "rating": 715, "opRating": 284}, {"opponent": "buzzwole", "rating": 635, "opRating": 364}, {"opponent": "garcho<PERSON>", "rating": 569}, {"opponent": "grou<PERSON>", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 205}, {"opponent": "dialga", "rating": 266}, {"opponent": "dragonite", "rating": 351}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "metagross", "rating": 415}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55653}, {"moveId": "ASTONISH", "uses": 20847}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31416}, {"moveId": "SHADOW_BALL", "uses": 29829}, {"moveId": "OMINOUS_WIND", "uses": 15295}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 56.6}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 491, "matchups": [{"opponent": "hoopa_unbound", "rating": 712, "opRating": 287}, {"opponent": "gengar", "rating": 706, "opRating": 293}, {"opponent": "electivire_shadow", "rating": 649, "opRating": 350}, {"opponent": "zarude", "rating": 613, "opRating": 386}, {"opponent": "gyarados", "rating": 577}], "counters": [{"opponent": "dialga", "rating": 222}, {"opponent": "giratina_origin", "rating": 247}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "metagross", "rating": 340}, {"opponent": "lugia", "rating": 433}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 31660}, {"moveId": "BUG_BITE", "uses": 23815}, {"moveId": "MUD_SLAP", "uses": 20968}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26874}, {"moveId": "CRUNCH", "uses": 25606}, {"moveId": "DISCHARGE", "uses": 24102}]}, "moveset": ["SPARK", "X_SCISSOR", "CRUNCH"], "score": 56.6}, {"speciesId": "jellicent", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 449, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 773, "opRating": 226}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 713, "opRating": 286}, {"opponent": "heracross", "rating": 641, "opRating": 358}, {"opponent": "genesect", "rating": 624, "opRating": 375}, {"opponent": "victini", "rating": 587, "opRating": 412}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "dragonite", "rating": 412}, {"opponent": "metagross", "rating": 436}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 41494}, {"moveId": "BUBBLE", "uses": 35006}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37814}, {"moveId": "ICE_BEAM", "uses": 25805}, {"moveId": "BUBBLE_BEAM", "uses": 12924}]}, "moveset": ["HEX", "SHADOW_BALL", "ICE_BEAM"], "score": 56.5}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 437, "matchups": [{"opponent": "magnezone", "rating": 844, "opRating": 155}, {"opponent": "magnezone_shadow", "rating": 823, "opRating": 176}, {"opponent": "nihilego", "rating": 737, "opRating": 262}, {"opponent": "excadrill", "rating": 643}, {"opponent": "raikou_shadow", "rating": 603, "opRating": 396}], "counters": [{"opponent": "giratina_origin", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "dialga", "rating": 442}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53359}, {"moveId": "METAL_CLAW", "uses": 23141}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28699}, {"moveId": "EARTHQUAKE", "uses": 17839}, {"moveId": "BULLDOZE", "uses": 10951}, {"moveId": "ROCK_TOMB", "uses": 10635}, {"moveId": "RETURN", "uses": 8497}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 56.5}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 424, "matchups": [{"opponent": "nihilego", "rating": 709, "opRating": 290}, {"opponent": "magnezone_shadow", "rating": 709, "opRating": 290}, {"opponent": "electivire_shadow", "rating": 705, "opRating": 294}, {"opponent": "rai<PERSON>u", "rating": 640, "opRating": 359}, {"opponent": "metagross", "rating": 523}], "counters": [{"opponent": "giratina_origin", "rating": 254}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 316}, {"opponent": "dialga", "rating": 478}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8187}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 6177}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5317}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5130}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4522}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4461}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4428}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4231}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4176}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4036}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4030}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4005}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3624}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3543}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3523}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3510}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3390}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33718}, {"moveId": "EARTH_POWER", "uses": 23703}, {"moveId": "EARTHQUAKE", "uses": 10305}, {"moveId": "WATER_PULSE", "uses": 8846}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 56.3}, {"speciesId": "barbara<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 489, "matchups": [{"opponent": "chandelure", "rating": 943, "opRating": 56}, {"opponent": "moltres", "rating": 937, "opRating": 62}, {"opponent": "moltres_shadow", "rating": 937, "opRating": 62}, {"opponent": "ho_oh_shadow", "rating": 934, "opRating": 65}, {"opponent": "ho_oh", "rating": 693, "opRating": 306}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "metagross", "rating": 273}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "gyarados", "rating": 453}, {"opponent": "lugia", "rating": 461}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31247}, {"moveId": "WATER_GUN", "uses": 26616}, {"moveId": "MUD_SLAP", "uses": 18639}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26409}, {"moveId": "CROSS_CHOP", "uses": 24124}, {"moveId": "GRASS_KNOT", "uses": 16313}, {"moveId": "SKULL_BASH", "uses": 9645}]}, "moveset": ["FURY_CUTTER", "STONE_EDGE", "CROSS_CHOP"], "score": 56.2}, {"speciesId": "cofagrigus", "speciesName": "<PERSON><PERSON>g<PERSON><PERSON>", "rating": 473, "matchups": [{"opponent": "chandelure", "rating": 848, "opRating": 151}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 784, "opRating": 215}, {"opponent": "escavalier", "rating": 723, "opRating": 276}, {"opponent": "latios_shadow", "rating": 557, "opRating": 442}, {"opponent": "x<PERSON><PERSON>", "rating": 553, "opRating": 446}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "lugia", "rating": 319}, {"opponent": "metagross", "rating": 334}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 56155}, {"moveId": "ASTONISH", "uses": 14610}, {"moveId": "ZEN_HEADBUTT", "uses": 5754}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 35641}, {"moveId": "DARK_PULSE", "uses": 23273}, {"moveId": "PSYCHIC", "uses": 17499}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "DARK_PULSE"], "score": 56.2}, {"speciesId": "absol_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 437, "matchups": [{"opponent": "metagross_shadow", "rating": 718, "opRating": 281}, {"opponent": "meloetta_aria", "rating": 697, "opRating": 302}, {"opponent": "mewtwo", "rating": 691}, {"opponent": "metagross", "rating": 677}, {"opponent": "mewtwo_shadow", "rating": 607, "opRating": 392}], "counters": [{"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "lugia", "rating": 297}, {"opponent": "dialga", "rating": 317}, {"opponent": "gyarados", "rating": 324}, {"opponent": "giratina_origin", "rating": 388}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42260}, {"moveId": "PSYCHO_CUT", "uses": 34240}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 28416}, {"moveId": "MEGAHORN", "uses": 19974}, {"moveId": "THUNDER", "uses": 15051}, {"moveId": "PAYBACK", "uses": 12916}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 56}, {"speciesId": "gren<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 437, "matchups": [{"opponent": "chandelure", "rating": 918, "opRating": 81}, {"opponent": "darmanitan_standard", "rating": 918, "opRating": 81}, {"opponent": "mewtwo", "rating": 690}, {"opponent": "mewtwo_shadow", "rating": 609, "opRating": 390}, {"opponent": "excadrill", "rating": 571}], "counters": [{"opponent": "dialga", "rating": 168}, {"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "metagross", "rating": 284}, {"opponent": "gyarados", "rating": 345}, {"opponent": "giratina_origin", "rating": 374}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 40018}, {"moveId": "FEINT_ATTACK", "uses": 36482}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 36069}, {"moveId": "SURF", "uses": 26388}, {"moveId": "AERIAL_ACE", "uses": 9828}, {"moveId": "HYDRO_PUMP", "uses": 4262}]}, "moveset": ["BUBBLE", "NIGHT_SLASH", "SURF"], "score": 56}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 478, "matchups": [{"opponent": "chandelure", "rating": 871, "opRating": 128}, {"opponent": "delphox", "rating": 871, "opRating": 128}, {"opponent": "typhlosion_shadow", "rating": 829, "opRating": 170}, {"opponent": "roserade", "rating": 737, "opRating": 262}, {"opponent": "gengar", "rating": 664, "opRating": 335}], "counters": [{"opponent": "dialga", "rating": 255}, {"opponent": "garcho<PERSON>", "rating": 265}, {"opponent": "giratina_origin", "rating": 304}, {"opponent": "metagross", "rating": 348}, {"opponent": "lugia", "rating": 452}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 51901}, {"moveId": "STEEL_WING", "uses": 24599}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 26455}, {"moveId": "CRUNCH", "uses": 25149}, {"moveId": "ANCIENT_POWER", "uses": 24872}]}, "moveset": ["WING_ATTACK", "DRAGON_CLAW", "CRUNCH"], "score": 55.8}, {"speciesId": "deoxys_defense", "speciesName": "<PERSON><PERSON><PERSON> (Defense)", "rating": 483, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 744, "opRating": 255}, {"opponent": "machamp", "rating": 728, "opRating": 271}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 692, "opRating": 307}, {"opponent": "machamp_shadow", "rating": 673, "opRating": 326}, {"opponent": "terrakion", "rating": 594, "opRating": 405}], "counters": [{"opponent": "mewtwo", "rating": 197}, {"opponent": "dialga", "rating": 239}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 67207}, {"moveId": "ZEN_HEADBUTT", "uses": 9293}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 32971}, {"moveId": "ROCK_SLIDE", "uses": 27360}, {"moveId": "THUNDERBOLT", "uses": 16234}]}, "moveset": ["COUNTER", "PSYCHO_BOOST", "ROCK_SLIDE"], "score": 55.8}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 454, "matchups": [{"opponent": "metagross_shadow", "rating": 829, "opRating": 170}, {"opponent": "mewtwo_shadow", "rating": 707, "opRating": 292}, {"opponent": "mewtwo", "rating": 685}, {"opponent": "genesect_chill", "rating": 682, "opRating": 317}, {"opponent": "giratina_origin", "rating": 606}], "counters": [{"opponent": "garcho<PERSON>", "rating": 194}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dialga", "rating": 271}, {"opponent": "metagross", "rating": 372}, {"opponent": "lugia", "rating": 392}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45865}, {"moveId": "FIRE_FANG", "uses": 30635}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30711}, {"moveId": "FLAMETHROWER", "uses": 18522}, {"moveId": "FOUL_PLAY", "uses": 13342}, {"moveId": "RETURN", "uses": 8945}, {"moveId": "FIRE_BLAST", "uses": 5027}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 55.8}, {"speciesId": "malamar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 455, "matchups": [{"opponent": "weavile_shadow", "rating": 941, "opRating": 58}, {"opponent": "mewtwo", "rating": 655}, {"opponent": "excadrill", "rating": 636}, {"opponent": "bewear", "rating": 583, "opRating": 416}, {"opponent": "mewtwo_shadow", "rating": 563, "opRating": 436}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "lugia", "rating": 278}, {"opponent": "garcho<PERSON>", "rating": 316}, {"opponent": "gyarados", "rating": 365}, {"opponent": "giratina_origin", "rating": 394}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 57905}, {"moveId": "PECK", "uses": 18595}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 31988}, {"moveId": "SUPER_POWER", "uses": 28273}, {"moveId": "PSYBEAM", "uses": 8560}, {"moveId": "HYPER_BEAM", "uses": 7696}]}, "moveset": ["PSYCHO_CUT", "FOUL_PLAY", "SUPER_POWER"], "score": 55.8}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 444, "matchups": [{"opponent": "rhyperior", "rating": 914, "opRating": 85}, {"opponent": "swampert", "rating": 847}, {"opponent": "kyogre", "rating": 701, "opRating": 298}, {"opponent": "excadrill", "rating": 560}, {"opponent": "gyarados", "rating": 503}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "giratina_origin", "rating": 243}, {"opponent": "mewtwo", "rating": 286}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 427}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 65216}, {"moveId": "IRON_TAIL", "uses": 11284}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 34935}, {"moveId": "LEAF_TORNADO", "uses": 17957}, {"moveId": "AERIAL_ACE", "uses": 14226}, {"moveId": "GRASS_KNOT", "uses": 9218}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "LEAF_TORNADO"], "score": 55.8}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 514, "matchups": [{"opponent": "metagross_shadow", "rating": 862, "opRating": 137}, {"opponent": "mamos<PERSON>_shadow", "rating": 707, "opRating": 292}, {"opponent": "buzzwole", "rating": 670, "opRating": 329}, {"opponent": "genesect_chill", "rating": 667, "opRating": 332}, {"opponent": "sylveon", "rating": 548, "opRating": 451}], "counters": [{"opponent": "dialga", "rating": 312}, {"opponent": "gyarados", "rating": 314}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "dragonite", "rating": 327}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46521}, {"moveId": "SCRATCH", "uses": 19230}, {"moveId": "ZEN_HEADBUTT", "uses": 10806}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 30402}, {"moveId": "PSYCHIC", "uses": 26619}, {"moveId": "FLAMETHROWER", "uses": 12805}, {"moveId": "FIRE_BLAST", "uses": 6827}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "PSYCHIC"], "score": 55.7}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 483, "matchups": [{"opponent": "weavile", "rating": 892, "opRating": 107}, {"opponent": "celebi", "rating": 870, "opRating": 129}, {"opponent": "hoopa_unbound", "rating": 859, "opRating": 140}, {"opponent": "electivire", "rating": 856, "opRating": 143}, {"opponent": "latios_shadow", "rating": 579, "opRating": 420}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "gyarados", "rating": 440}, {"opponent": "mewtwo", "rating": 460}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 43341}, {"moveId": "METAL_CLAW", "uses": 33159}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 32667}, {"moveId": "STONE_EDGE", "uses": 23857}, {"moveId": "IRON_HEAD", "uses": 19960}]}, "moveset": ["BUG_BITE", "X_SCISSOR", "STONE_EDGE"], "score": 55.7}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 433, "matchups": [{"opponent": "rhyperior", "rating": 860, "opRating": 139}, {"opponent": "swampert", "rating": 733}, {"opponent": "kyogre", "rating": 701, "opRating": 298}, {"opponent": "excadrill", "rating": 684}, {"opponent": "grou<PERSON>", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 168}, {"opponent": "mewtwo", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 431}, {"opponent": "zacian_hero", "rating": 433}, {"opponent": "gyarados", "rating": 481}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6292}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5717}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 5659}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5066}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4967}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4897}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4710}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4594}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4520}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4471}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4405}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4342}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3983}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3941}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3839}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3646}, {"moveId": "ZEN_HEADBUTT", "uses": 1356}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41196}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12215}, {"moveId": "SOLAR_BEAM", "uses": 8437}]}, "moveset": ["HIDDEN_POWER_GRASS", "GRASS_KNOT", "ENERGY_BALL"], "score": 55.7}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 476, "matchups": [{"opponent": "hoopa", "rating": 923, "opRating": 76}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 840, "opRating": 159}, {"opponent": "tangrowth_shadow", "rating": 643, "opRating": 356}, {"opponent": "mew", "rating": 601, "opRating": 398}, {"opponent": "mewtwo_shadow", "rating": 592, "opRating": 407}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "lugia", "rating": 361}, {"opponent": "gyarados", "rating": 422}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "giratina_origin", "rating": 458}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24546}, {"moveId": "INFESTATION", "uses": 18970}, {"moveId": "ICE_FANG", "uses": 17867}, {"moveId": "BITE", "uses": 15036}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31067}, {"moveId": "AQUA_TAIL", "uses": 22770}, {"moveId": "SLUDGE_BOMB", "uses": 17245}, {"moveId": "FELL_STINGER", "uses": 5419}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 55.5}, {"speciesId": "relicanth", "speciesName": "Relicanth", "rating": 469, "matchups": [{"opponent": "entei", "rating": 853, "opRating": 146}, {"opponent": "entei_shadow", "rating": 843, "opRating": 156}, {"opponent": "ho_oh", "rating": 741, "opRating": 258}, {"opponent": "ho_oh_shadow", "rating": 708, "opRating": 291}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 544, "opRating": 455}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "lugia", "rating": 390}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66095}, {"moveId": "ZEN_HEADBUTT", "uses": 10405}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 40566}, {"moveId": "ANCIENT_POWER", "uses": 29646}, {"moveId": "HYDRO_PUMP", "uses": 6322}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "ANCIENT_POWER"], "score": 55.4}, {"speciesId": "aerodactyl", "speciesName": "Aerodactyl", "rating": 457, "matchups": [{"opponent": "moltres_shadow", "rating": 869, "opRating": 130}, {"opponent": "entei", "rating": 741, "opRating": 258}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 686, "opRating": 313}, {"opponent": "ho_oh", "rating": 633, "opRating": 366}, {"opponent": "gyarados", "rating": 526}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "mewtwo", "rating": 372}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "lugia", "rating": 409}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36317}, {"moveId": "BITE", "uses": 20862}, {"moveId": "STEEL_WING", "uses": 19365}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27636}, {"moveId": "EARTH_POWER", "uses": 18024}, {"moveId": "IRON_HEAD", "uses": 10019}, {"moveId": "ANCIENT_POWER", "uses": 9912}, {"moveId": "RETURN", "uses": 7913}, {"moveId": "HYPER_BEAM", "uses": 3064}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 55.2}, {"speciesId": "lycanroc_midnight", "speciesName": "Lycanroc (Midnight)", "rating": 488, "matchups": [{"opponent": "tyranitar_shadow", "rating": 747, "opRating": 252}, {"opponent": "entei", "rating": 741, "opRating": 258}, {"opponent": "entei_shadow", "rating": 727, "opRating": 272}, {"opponent": "ho_oh", "rating": 626, "opRating": 373}, {"opponent": "ho_oh_shadow", "rating": 556, "opRating": 443}], "counters": [{"opponent": "giratina_origin", "rating": 227}, {"opponent": "metagross", "rating": 305}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "dialga", "rating": 323}, {"opponent": "gyarados", "rating": 476}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44018}, {"moveId": "ROCK_THROW", "uses": 32482}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26958}, {"moveId": "CRUNCH", "uses": 26489}, {"moveId": "PSYCHIC_FANGS", "uses": 23122}]}, "moveset": ["COUNTER", "STONE_EDGE", "CRUNCH"], "score": 55.2}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 466, "matchups": [{"opponent": "staraptor_shadow", "rating": 921, "opRating": 78}, {"opponent": "gyarados", "rating": 870}, {"opponent": "sylveon", "rating": 724, "opRating": 275}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 696}, {"opponent": "lugia", "rating": 531}], "counters": [{"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "metagross", "rating": 223}, {"opponent": "dialga", "rating": 250}, {"opponent": "zacian_hero", "rating": 283}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30652}, {"moveId": "SPARK", "uses": 28238}, {"moveId": "CHARGE_BEAM", "uses": 17599}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 27318}, {"moveId": "DISCHARGE", "uses": 23743}, {"moveId": "RETURN", "uses": 10739}, {"moveId": "ZAP_CANNON", "uses": 9063}, {"moveId": "FLASH_CANNON", "uses": 5810}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 55}, {"speciesId": "omastar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 477, "matchups": [{"opponent": "ho_oh_shadow", "rating": 949, "opRating": 50}, {"opponent": "staraptor_shadow", "rating": 920, "opRating": 79}, {"opponent": "ho_oh", "rating": 646, "opRating": 353}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 621, "opRating": 378}, {"opponent": "gyarados", "rating": 528}], "counters": [{"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "dragonite", "rating": 332}, {"opponent": "lugia", "rating": 397}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30081}, {"moveId": "ROCK_THROW", "uses": 23641}, {"moveId": "WATER_GUN", "uses": 22824}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30178}, {"moveId": "ROCK_BLAST", "uses": 21552}, {"moveId": "HYDRO_PUMP", "uses": 14073}, {"moveId": "ANCIENT_POWER", "uses": 10852}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 55}, {"speciesId": "espeon", "speciesName": "Espeon", "rating": 501, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 879, "opRating": 120}, {"opponent": "blaziken", "rating": 879, "opRating": 120}, {"opponent": "venusaur_shadow", "rating": 879, "opRating": 120}, {"opponent": "machamp_shadow", "rating": 751, "opRating": 248}, {"opponent": "tapu_lele", "rating": 671, "opRating": 328}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 218}, {"opponent": "lugia", "rating": 311}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "excadrill", "rating": 465}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 65681}, {"moveId": "ZEN_HEADBUTT", "uses": 10819}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 24862}, {"moveId": "SHADOW_BALL", "uses": 19335}, {"moveId": "PSYCHIC", "uses": 13611}, {"moveId": "LAST_RESORT", "uses": 10027}, {"moveId": "FUTURE_SIGHT", "uses": 5992}, {"moveId": "PSYBEAM", "uses": 2736}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "SHADOW_BALL"], "score": 54.9}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 460, "matchups": [{"opponent": "golem", "rating": 963, "opRating": 36}, {"opponent": "luxray_shadow", "rating": 865, "opRating": 134}, {"opponent": "swampert", "rating": 814}, {"opponent": "swampert_shadow", "rating": 786, "opRating": 213}, {"opponent": "kyogre", "rating": 600, "opRating": 399}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "metagross", "rating": 232}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "garcho<PERSON>", "rating": 448}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 44899}, {"moveId": "RAZOR_LEAF", "uses": 31601}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42042}, {"moveId": "X_SCISSOR", "uses": 21430}, {"moveId": "SILVER_WIND", "uses": 7734}, {"moveId": "LEAF_STORM", "uses": 5156}]}, "moveset": ["BUG_BITE", "LEAF_BLADE", "X_SCISSOR"], "score": 54.9}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 474, "matchups": [{"opponent": "moltres_shadow", "rating": 881, "opRating": 118}, {"opponent": "gyarados", "rating": 728}, {"opponent": "ho_oh", "rating": 570, "opRating": 429}, {"opponent": "kyogre", "rating": 562, "opRating": 437}, {"opponent": "ho_oh_shadow", "rating": 555, "opRating": 444}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "metagross", "rating": 345}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "lugia", "rating": 454}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 57353}, {"moveId": "ASTONISH", "uses": 19147}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34708}, {"moveId": "HYDRO_PUMP", "uses": 26675}, {"moveId": "THUNDER", "uses": 15111}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 54.9}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 419, "matchups": [{"opponent": "swampert_shadow", "rating": 959, "opRating": 40}, {"opponent": "mewtwo_shadow", "rating": 720, "opRating": 279}, {"opponent": "mewtwo", "rating": 655}, {"opponent": "swampert", "rating": 583, "opRating": 416}, {"opponent": "kyogre", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 195}, {"opponent": "metagross", "rating": 313}, {"opponent": "gyarados", "rating": 407}, {"opponent": "excadrill", "rating": 465}, {"opponent": "giratina_origin", "rating": 482}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25173}, {"moveId": "BULLET_SEED", "uses": 22734}, {"moveId": "FEINT_ATTACK", "uses": 17824}, {"moveId": "RAZOR_LEAF", "uses": 10892}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33791}, {"moveId": "FOUL_PLAY", "uses": 22087}, {"moveId": "HURRICANE", "uses": 8000}, {"moveId": "LEAF_TORNADO", "uses": 6497}, {"moveId": "RETURN", "uses": 6172}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 54.9}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 471, "matchups": [{"opponent": "tapu_fini", "rating": 824, "opRating": 175}, {"opponent": "sylveon", "rating": 663, "opRating": 336}, {"opponent": "tapu_lele", "rating": 636, "opRating": 363}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 634, "opRating": 365}, {"opponent": "mewtwo_shadow", "rating": 570, "opRating": 429}], "counters": [{"opponent": "dialga", "rating": 222}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "metagross", "rating": 311}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "mewtwo", "rating": 458}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 48249}, {"moveId": "BITE", "uses": 28251}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38956}, {"moveId": "SLUDGE_BOMB", "uses": 21348}, {"moveId": "FLAMETHROWER", "uses": 16105}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 54.9}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 472, "matchups": [{"opponent": "gengar", "rating": 843, "opRating": 156}, {"opponent": "roserade", "rating": 653, "opRating": 346}, {"opponent": "electivire", "rating": 599, "opRating": 400}, {"opponent": "bewear", "rating": 564, "opRating": 435}, {"opponent": "giratina_origin", "rating": 559}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 171}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 39176}, {"moveId": "POUND", "uses": 37324}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 29325}, {"moveId": "HYPER_BEAM", "uses": 24671}, {"moveId": "DAZZLING_GLEAM", "uses": 22497}]}, "moveset": ["ZEN_HEADBUTT", "PSYCHIC", "HYPER_BEAM"], "score": 54.7}, {"speciesId": "clawitzer", "speciesName": "Clawitzer", "rating": 455, "matchups": [{"opponent": "entei", "rating": 743, "opRating": 256}, {"opponent": "heatran", "rating": 712, "opRating": 287}, {"opponent": "ma<PERSON><PERSON>", "rating": 702, "opRating": 297}, {"opponent": "landorus_incarnate", "rating": 667, "opRating": 332}, {"opponent": "excadrill", "rating": 610}], "counters": [{"opponent": "dialga", "rating": 282}, {"opponent": "lugia", "rating": 290}, {"opponent": "metagross", "rating": 363}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "zacian_hero", "rating": 390}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41846}, {"moveId": "SMACK_DOWN", "uses": 34654}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 29817}, {"moveId": "ICE_BEAM", "uses": 21610}, {"moveId": "DARK_PULSE", "uses": 20835}, {"moveId": "WATER_PULSE", "uses": 4381}]}, "moveset": ["WATER_GUN", "CRABHAMMER", "ICE_BEAM"], "score": 54.7}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 441, "matchups": [{"opponent": "gallade_shadow", "rating": 701, "opRating": 298}, {"opponent": "gallade", "rating": 697, "opRating": 302}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 691, "opRating": 308}, {"opponent": "swampert", "rating": 671}, {"opponent": "excadrill", "rating": 530}], "counters": [{"opponent": "mewtwo", "rating": 192}, {"opponent": "dialga", "rating": 244}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "zacian_hero", "rating": 384}, {"opponent": "metagross", "rating": 389}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49390}, {"moveId": "RAZOR_LEAF", "uses": 27110}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26353}, {"moveId": "SEED_BOMB", "uses": 21720}, {"moveId": "FOUL_PLAY", "uses": 19745}, {"moveId": "FIRE_BLAST", "uses": 8560}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 54.7}, {"speciesId": "slowbro", "speciesName": "Slowbro", "rating": 443, "matchups": [{"opponent": "terrakion", "rating": 693, "opRating": 306}, {"opponent": "kommo_o", "rating": 675, "opRating": 324}, {"opponent": "landorus_incarnate", "rating": 649, "opRating": 350}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 644, "opRating": 355}, {"opponent": "dragonite", "rating": 597}], "counters": [{"opponent": "dialga", "rating": 230}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "gyarados", "rating": 360}, {"opponent": "swampert", "rating": 435}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38674}, {"moveId": "WATER_GUN", "uses": 37826}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26551}, {"moveId": "PSYCHIC", "uses": 23933}, {"moveId": "WATER_PULSE", "uses": 13237}, {"moveId": "RETURN", "uses": 12783}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 54.7}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 439, "matchups": [{"opponent": "swampert", "rating": 732}, {"opponent": "excadrill", "rating": 690}, {"opponent": "dialga", "rating": 640}, {"opponent": "kyogre", "rating": 566, "opRating": 433}, {"opponent": "snorlax", "rating": 535, "opRating": 464}], "counters": [{"opponent": "mewtwo", "rating": 78}, {"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "metagross", "rating": 162}, {"opponent": "gyarados", "rating": 360}, {"opponent": "grou<PERSON>", "rating": 494}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44251}, {"moveId": "BULLET_SEED", "uses": 32249}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25554}, {"moveId": "GRASS_KNOT", "uses": 19204}, {"moveId": "SEED_BOMB", "uses": 17634}, {"moveId": "SLUDGE_BOMB", "uses": 14205}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 54.6}, {"speciesId": "crawdaunt", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 433, "matchups": [{"opponent": "darmanitan_standard", "rating": 911, "opRating": 88}, {"opponent": "chandelure", "rating": 904, "opRating": 95}, {"opponent": "mewtwo_shadow", "rating": 680, "opRating": 319}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 676, "opRating": 323}, {"opponent": "mewtwo", "rating": 659}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "metagross", "rating": 313}, {"opponent": "giratina_origin", "rating": 422}, {"opponent": "excadrill", "rating": 493}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44464}, {"moveId": "WATERFALL", "uses": 32036}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39301}, {"moveId": "CRABHAMMER", "uses": 23021}, {"moveId": "VICE_GRIP", "uses": 7354}, {"moveId": "BUBBLE_BEAM", "uses": 6913}]}, "moveset": ["SNARL", "NIGHT_SLASH", "CRABHAMMER"], "score": 54.6}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 466, "matchups": [{"opponent": "golem", "rating": 944, "opRating": 55}, {"opponent": "swampert", "rating": 831}, {"opponent": "swampert_shadow", "rating": 809, "opRating": 190}, {"opponent": "kyogre", "rating": 662, "opRating": 337}, {"opponent": "sylveon", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 119}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "gyarados", "rating": 427}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 46262}, {"moveId": "METAL_CLAW", "uses": 30238}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 25467}, {"moveId": "MIRROR_SHOT", "uses": 21649}, {"moveId": "THUNDER", "uses": 14620}, {"moveId": "FLASH_CANNON", "uses": 11294}, {"moveId": "ACID_SPRAY", "uses": 3530}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "MIRROR_SHOT"], "score": 54.6}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 505, "matchups": [{"opponent": "heracross", "rating": 809, "opRating": 190}, {"opponent": "buzzwole", "rating": 729, "opRating": 270}, {"opponent": "nihilego", "rating": 652, "opRating": 347}, {"opponent": "sylveon", "rating": 567, "opRating": 432}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 554, "opRating": 445}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "excadrill", "rating": 355}, {"opponent": "gyarados", "rating": 448}, {"opponent": "dragonite", "rating": 449}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31676}, {"moveId": "HEX", "uses": 30513}, {"moveId": "ACID", "uses": 14338}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32812}, {"moveId": "FUTURE_SIGHT", "uses": 24290}, {"moveId": "SLUDGE_WAVE", "uses": 19391}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 54.6}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 417, "matchups": [{"opponent": "swampert", "rating": 875}, {"opponent": "swampert_shadow", "rating": 837, "opRating": 162}, {"opponent": "rhyperior", "rating": 816, "opRating": 183}, {"opponent": "landorus_incarnate", "rating": 732, "opRating": 267}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 636, "opRating": 363}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "dragonite", "rating": 417}, {"opponent": "garcho<PERSON>", "rating": 424}, {"opponent": "excadrill", "rating": 437}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 48237}, {"moveId": "RAZOR_LEAF", "uses": 28263}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 21126}, {"moveId": "ENERGY_BALL", "uses": 16153}, {"moveId": "LEAF_STORM", "uses": 14059}, {"moveId": "HYDRO_PUMP", "uses": 13103}, {"moveId": "BLIZZARD", "uses": 7320}, {"moveId": "SOLAR_BEAM", "uses": 4631}]}, "moveset": ["BUBBLE", "ICE_BEAM", "ENERGY_BALL"], "score": 54.4}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 442, "matchups": [{"opponent": "gengar", "rating": 686, "opRating": 313}, {"opponent": "trevenant", "rating": 664, "opRating": 335}, {"opponent": "golisopod", "rating": 577, "opRating": 422}, {"opponent": "feraligatr_shadow", "rating": 556, "opRating": 443}, {"opponent": "over<PERSON><PERSON>l", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "gyarados", "rating": 420}, {"opponent": "giratina_origin", "rating": 496}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 28734}, {"moveId": "TACKLE", "uses": 28556}, {"moveId": "BITE", "uses": 19202}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 47786}, {"moveId": "CRUNCH", "uses": 28714}]}, "moveset": ["TACKLE", "BODY_SLAM", "CRUNCH"], "score": 54.2}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 483, "matchups": [{"opponent": "moltres_shadow", "rating": 838, "opRating": 161}, {"opponent": "gyarados", "rating": 671}, {"opponent": "gyarado<PERSON>_shadow", "rating": 637, "opRating": 362}, {"opponent": "zapdos", "rating": 630, "opRating": 369}, {"opponent": "lugia_shadow", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "metagross", "rating": 267}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "lugia", "rating": 466}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43267}, {"moveId": "THUNDER_SHOCK", "uses": 33233}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 26606}, {"moveId": "LAST_RESORT", "uses": 19083}, {"moveId": "THUNDERBOLT", "uses": 11471}, {"moveId": "THUNDER", "uses": 9961}, {"moveId": "ZAP_CANNON", "uses": 9570}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LAST_RESORT"], "score": 54.2}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 464, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 811, "opRating": 188}, {"opponent": "virizion", "rating": 805, "opRating": 194}, {"opponent": "buzzwole", "rating": 616, "opRating": 383}, {"opponent": "mewtwo", "rating": 527}, {"opponent": "swampert", "rating": 505}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "dragonite", "rating": 327}, {"opponent": "gyarados", "rating": 353}, {"opponent": "garcho<PERSON>", "rating": 370}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 40820}, {"moveId": "BUG_BITE", "uses": 35680}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26371}, {"moveId": "ANCIENT_POWER", "uses": 25650}, {"moveId": "AERIAL_ACE", "uses": 24425}]}, "moveset": ["WING_ATTACK", "BUG_BUZZ", "ANCIENT_POWER"], "score": 54.2}, {"speciesId": "crustle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 485, "matchups": [{"opponent": "moltres_shadow", "rating": 859, "opRating": 140}, {"opponent": "charizard", "rating": 770, "opRating": 229}, {"opponent": "hoopa_unbound", "rating": 757, "opRating": 242}, {"opponent": "moltres_galarian", "rating": 646, "opRating": 353}, {"opponent": "zarude", "rating": 589, "opRating": 410}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "lugia", "rating": 340}, {"opponent": "zacian_hero", "rating": 352}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40814}, {"moveId": "SMACK_DOWN", "uses": 35686}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28857}, {"moveId": "X_SCISSOR", "uses": 26882}, {"moveId": "ROCK_BLAST", "uses": 20681}]}, "moveset": ["FURY_CUTTER", "ROCK_SLIDE", "X_SCISSOR"], "score": 54.1}, {"speciesId": "golduck", "speciesName": "Gold<PERSON>", "rating": 425, "matchups": [{"opponent": "infernape", "rating": 860, "opRating": 139}, {"opponent": "hippow<PERSON>_shadow", "rating": 854, "opRating": 145}, {"opponent": "heatran", "rating": 686, "opRating": 313}, {"opponent": "landorus_incarnate", "rating": 671, "opRating": 328}, {"opponent": "mamos<PERSON>_shadow", "rating": 627, "opRating": 372}], "counters": [{"opponent": "dialga", "rating": 239}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "excadrill", "rating": 474}, {"opponent": "garcho<PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41432}, {"moveId": "CONFUSION", "uses": 35068}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 21096}, {"moveId": "ICE_BEAM", "uses": 16469}, {"moveId": "SYNCHRONOISE", "uses": 10714}, {"moveId": "HYDRO_PUMP", "uses": 9235}, {"moveId": "RETURN", "uses": 7193}, {"moveId": "BUBBLE_BEAM", "uses": 6862}, {"moveId": "PSYCHIC", "uses": 4868}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 54.1}, {"speciesId": "hitmontop", "speciesName": "Hitmontop", "rating": 476, "matchups": [{"opponent": "tyranitar", "rating": 842, "opRating": 157}, {"opponent": "tyranitar_shadow", "rating": 822, "opRating": 177}, {"opponent": "regigigas", "rating": 779, "opRating": 220}, {"opponent": "darkrai", "rating": 728, "opRating": 271}, {"opponent": "excadrill", "rating": 629}], "counters": [{"opponent": "zacian_hero", "rating": 167}, {"opponent": "metagross", "rating": 279}, {"opponent": "swampert", "rating": 350}, {"opponent": "gyarados", "rating": 376}, {"opponent": "dialga", "rating": 478}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 63392}, {"moveId": "ROCK_SMASH", "uses": 13108}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 46269}, {"moveId": "STONE_EDGE", "uses": 22109}, {"moveId": "GYRO_BALL", "uses": 8125}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "STONE_EDGE"], "score": 54.1}, {"speciesId": "king<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 509, "matchups": [{"opponent": "kyogre", "rating": 853, "opRating": 146}, {"opponent": "entei", "rating": 820, "opRating": 179}, {"opponent": "entei_shadow", "rating": 798, "opRating": 201}, {"opponent": "landorus_incarnate", "rating": 640, "opRating": 359}, {"opponent": "swampert", "rating": 542, "opRating": 457}], "counters": [{"opponent": "mewtwo", "rating": 177}, {"opponent": "dialga", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "metagross", "rating": 293}, {"opponent": "excadrill", "rating": 448}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33853}, {"moveId": "WATERFALL", "uses": 21596}, {"moveId": "WATER_GUN", "uses": 21157}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 26764}, {"moveId": "OCTAZOOKA", "uses": 17671}, {"moveId": "BLIZZARD", "uses": 17064}, {"moveId": "HYDRO_PUMP", "uses": 14821}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 54.1}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 464, "matchups": [{"opponent": "tapu_bulu", "rating": 746, "opRating": 253}, {"opponent": "tapu_lele", "rating": 697, "opRating": 302}, {"opponent": "tangrowth_shadow", "rating": 697, "opRating": 302}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 590, "opRating": 409}, {"opponent": "sylveon", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "gyarados", "rating": 345}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "giratina_origin", "rating": 450}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 46959}, {"moveId": "BITE", "uses": 29541}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34610}, {"moveId": "SLUDGE_BOMB", "uses": 18504}, {"moveId": "FLAMETHROWER", "uses": 14465}, {"moveId": "RETURN", "uses": 8932}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 54.1}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 477, "matchups": [{"opponent": "trevenant", "rating": 748, "opRating": 251}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 738, "opRating": 261}, {"opponent": "gengar", "rating": 684, "opRating": 315}, {"opponent": "chandelure", "rating": 633, "opRating": 366}, {"opponent": "articuno_galarian", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "lugia", "rating": 314}, {"opponent": "gyarados", "rating": 365}, {"opponent": "mewtwo", "rating": 382}, {"opponent": "giratina_origin", "rating": 390}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24597}, {"moveId": "INFESTATION", "uses": 18947}, {"moveId": "ICE_FANG", "uses": 17764}, {"moveId": "BITE", "uses": 15160}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28374}, {"moveId": "AQUA_TAIL", "uses": 20685}, {"moveId": "SLUDGE_BOMB", "uses": 15425}, {"moveId": "RETURN", "uses": 7092}, {"moveId": "FELL_STINGER", "uses": 4995}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 53.9}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 445, "matchups": [{"opponent": "swampert", "rating": 968}, {"opponent": "swampert_shadow", "rating": 952, "opRating": 47}, {"opponent": "tapu_fini", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 684}, {"opponent": "kyogre", "rating": 617, "opRating": 382}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 366}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "gyarados", "rating": 425}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 40406}, {"moveId": "FURY_CUTTER", "uses": 36094}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30196}, {"moveId": "DRAGON_CLAW", "uses": 17103}, {"moveId": "FRENZY_PLANT", "uses": 11180}, {"moveId": "EARTHQUAKE", "uses": 10013}, {"moveId": "AERIAL_ACE", "uses": 8018}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DRAGON_CLAW"], "score": 53.9}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 466, "matchups": [{"opponent": "articuno_galarian", "rating": 841, "opRating": 158}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 753}, {"opponent": "gyarados", "rating": 693}, {"opponent": "lugia", "rating": 591}, {"opponent": "sylveon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "zacian_hero", "rating": 144}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "dialga", "rating": 222}, {"opponent": "metagross", "rating": 302}, {"opponent": "mewtwo", "rating": 369}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 48838}, {"moveId": "CHARGE_BEAM", "uses": 27662}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 29912}, {"moveId": "ZAP_CANNON", "uses": 19264}, {"moveId": "FLASH_CANNON", "uses": 15569}, {"moveId": "HYPER_BEAM", "uses": 11764}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 53.8}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 414, "matchups": [{"opponent": "heatran", "rating": 747, "opRating": 252}, {"opponent": "nihilego", "rating": 731, "opRating": 268}, {"opponent": "electivire_shadow", "rating": 685, "opRating": 314}, {"opponent": "magnezone_shadow", "rating": 675, "opRating": 324}, {"opponent": "excadrill", "rating": 631}], "counters": [{"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "metagross", "rating": 377}, {"opponent": "dialga", "rating": 391}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43338}, {"moveId": "WATER_GUN", "uses": 33162}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 26777}, {"moveId": "STONE_EDGE", "uses": 21734}, {"moveId": "SLUDGE_BOMB", "uses": 14705}, {"moveId": "RETURN", "uses": 9963}, {"moveId": "ACID_SPRAY", "uses": 3321}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 53.8}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 470, "matchups": [{"opponent": "tyranitar", "rating": 843, "opRating": 156}, {"opponent": "cobalion", "rating": 781, "opRating": 218}, {"opponent": "melmetal", "rating": 744, "opRating": 255}, {"opponent": "dialga", "rating": 661}, {"opponent": "snorlax", "rating": 610, "opRating": 389}], "counters": [{"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "dragonite", "rating": 284}, {"opponent": "gyarados", "rating": 324}, {"opponent": "swampert", "rating": 368}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 376}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42199}, {"moveId": "POISON_JAB", "uses": 34301}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 28885}, {"moveId": "SLUDGE_BOMB", "uses": 24634}, {"moveId": "MUD_BOMB", "uses": 23001}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "SLUDGE_BOMB"], "score": 53.8}, {"speciesId": "omastar", "speciesName": "Omastar", "rating": 462, "matchups": [{"opponent": "moltres_shadow", "rating": 952, "opRating": 47}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 687}, {"opponent": "ho_oh", "rating": 684, "opRating": 315}, {"opponent": "ho_oh_shadow", "rating": 646, "opRating": 353}, {"opponent": "gyarados", "rating": 585}], "counters": [{"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "dialga", "rating": 184}, {"opponent": "mewtwo", "rating": 250}, {"opponent": "giratina_origin", "rating": 324}, {"opponent": "lugia", "rating": 335}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29190}, {"moveId": "ROCK_THROW", "uses": 23688}, {"moveId": "WATER_GUN", "uses": 23599}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26848}, {"moveId": "ROCK_BLAST", "uses": 19273}, {"moveId": "HYDRO_PUMP", "uses": 12267}, {"moveId": "ANCIENT_POWER", "uses": 9728}, {"moveId": "RETURN", "uses": 8428}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 53.6}, {"speciesId": "kingler", "speciesName": "<PERSON><PERSON>", "rating": 471, "matchups": [{"opponent": "typhlosion", "rating": 933, "opRating": 66}, {"opponent": "chandelure", "rating": 918, "opRating": 81}, {"opponent": "darmanitan_standard", "rating": 918, "opRating": 81}, {"opponent": "rhyperior", "rating": 822, "opRating": 177}, {"opponent": "excadrill", "rating": 607}], "counters": [{"opponent": "mewtwo", "rating": 205}, {"opponent": "dialga", "rating": 214}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "zacian_hero", "rating": 236}, {"opponent": "metagross", "rating": 308}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32068}, {"moveId": "BUBBLE", "uses": 28459}, {"moveId": "METAL_CLAW", "uses": 15932}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 35529}, {"moveId": "X_SCISSOR", "uses": 24804}, {"moveId": "VICE_GRIP", "uses": 11032}, {"moveId": "WATER_PULSE", "uses": 5145}]}, "moveset": ["MUD_SHOT", "CRABHAMMER", "X_SCISSOR"], "score": 53.5}, {"speciesId": "politoed", "speciesName": "Politoed", "rating": 439, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 720, "opRating": 279}, {"opponent": "heatran", "rating": 704, "opRating": 295}, {"opponent": "excadrill", "rating": 680}, {"opponent": "ma<PERSON><PERSON>", "rating": 674, "opRating": 325}, {"opponent": "ho_oh", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 265}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "metagross", "rating": 409}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41210}, {"moveId": "BUBBLE", "uses": 35290}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 29640}, {"moveId": "SURF", "uses": 12074}, {"moveId": "BLIZZARD", "uses": 11971}, {"moveId": "EARTHQUAKE", "uses": 11742}, {"moveId": "RETURN", "uses": 7229}, {"moveId": "HYDRO_PUMP", "uses": 3920}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "SURF"], "score": 53.5}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 454, "matchups": [{"opponent": "swampert_shadow", "rating": 952, "opRating": 47}, {"opponent": "weavile", "rating": 942, "opRating": 57}, {"opponent": "weavile_shadow", "rating": 933, "opRating": 66}, {"opponent": "kyogre", "rating": 649, "opRating": 350}, {"opponent": "swampert", "rating": 636}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "gyarados", "rating": 353}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50836}, {"moveId": "RAZOR_LEAF", "uses": 25664}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37314}, {"moveId": "SUPER_POWER", "uses": 20627}, {"moveId": "X_SCISSOR", "uses": 13980}, {"moveId": "LEAF_STORM", "uses": 4637}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 53.3}, {"speciesId": "slurpuff", "speciesName": "Slurpuff", "rating": 416, "matchups": [{"opponent": "kommo_o", "rating": 870, "opRating": 129}, {"opponent": "hydreigon", "rating": 778, "opRating": 221}, {"opponent": "dragonite", "rating": 701}, {"opponent": "yveltal", "rating": 635, "opRating": 364}, {"opponent": "dragonite_shadow", "rating": 580, "opRating": 419}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "mewtwo", "rating": 250}, {"opponent": "giratina_origin", "rating": 282}, {"opponent": "garcho<PERSON>", "rating": 354}, {"opponent": "gyarados", "rating": 445}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 32187}, {"moveId": "CHARM", "uses": 25994}, {"moveId": "TACKLE", "uses": 18291}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22384}, {"moveId": "PLAY_ROUGH", "uses": 21988}, {"moveId": "ENERGY_BALL", "uses": 18115}, {"moveId": "DRAINING_KISS", "uses": 13908}]}, "moveset": ["FAIRY_WIND", "FLAMETHROWER", "PLAY_ROUGH"], "score": 53.3}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 438, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 857, "opRating": 142}, {"opponent": "tapu_bulu", "rating": 827, "opRating": 172}, {"opponent": "genesect_burn", "rating": 726, "opRating": 273}, {"opponent": "buzzwole", "rating": 690, "opRating": 309}, {"opponent": "metagross", "rating": 571}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "giratina_origin", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "mewtwo", "rating": 351}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 33804}, {"moveId": "FIRE_SPIN", "uses": 21399}, {"moveId": "PECK", "uses": 11123}, {"moveId": "STEEL_WING", "uses": 10111}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37660}, {"moveId": "FLAME_CHARGE", "uses": 21961}, {"moveId": "FIRE_BLAST", "uses": 9990}, {"moveId": "HURRICANE", "uses": 6914}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "FLAME_CHARGE"], "score": 53.3}, {"speciesId": "gran<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 462, "matchups": [{"opponent": "aggron", "rating": 919, "opRating": 80}, {"opponent": "tyranitar_shadow", "rating": 774, "opRating": 225}, {"opponent": "kyurem", "rating": 712, "opRating": 287}, {"opponent": "darkrai", "rating": 669, "opRating": 330}, {"opponent": "snorlax", "rating": 639, "opRating": 360}], "counters": [{"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "lugia", "rating": 309}, {"opponent": "dialga", "rating": 339}, {"opponent": "gyarados", "rating": 350}, {"opponent": "excadrill", "rating": 479}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 35624}, {"moveId": "CHARM", "uses": 26880}, {"moveId": "BITE", "uses": 13927}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30806}, {"moveId": "CRUNCH", "uses": 22899}, {"moveId": "PLAY_ROUGH", "uses": 14508}, {"moveId": "RETURN", "uses": 8452}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 53}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 434, "matchups": [{"opponent": "magnezone", "rating": 845, "opRating": 154}, {"opponent": "golem_alolan", "rating": 831, "opRating": 168}, {"opponent": "magnezone_shadow", "rating": 827, "opRating": 172}, {"opponent": "nihilego", "rating": 726, "opRating": 273}, {"opponent": "gyarados", "rating": 605}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "lugia", "rating": 328}, {"opponent": "dialga", "rating": 347}, {"opponent": "excadrill", "rating": 458}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38701}, {"moveId": "THUNDER_SHOCK", "uses": 37799}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 33855}, {"moveId": "DISCHARGE", "uses": 26056}, {"moveId": "MUDDY_WATER", "uses": 16577}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "DISCHARGE"], "score": 53}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 430, "matchups": [{"opponent": "salamence_shadow", "rating": 768, "opRating": 231}, {"opponent": "gengar", "rating": 750, "opRating": 250}, {"opponent": "trevenant", "rating": 701, "opRating": 298}, {"opponent": "giratina_origin", "rating": 659}, {"opponent": "landorus_incarnate", "rating": 644, "opRating": 355}], "counters": [{"opponent": "dialga", "rating": 160}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "gyarados", "rating": 293}, {"opponent": "garcho<PERSON>", "rating": 420}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 36151}, {"moveId": "TACKLE", "uses": 35231}, {"moveId": "ZEN_HEADBUTT", "uses": 5145}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33455}, {"moveId": "ICE_BEAM", "uses": 16631}, {"moveId": "THUNDERBOLT", "uses": 12095}, {"moveId": "STOMP", "uses": 8276}, {"moveId": "GYRO_BALL", "uses": 6029}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 52.7}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 457, "matchups": [{"opponent": "aggron_shadow", "rating": 898, "opRating": 101}, {"opponent": "aggron", "rating": 888, "opRating": 111}, {"opponent": "tyranitar_shadow", "rating": 859, "opRating": 140}, {"opponent": "cresselia", "rating": 668, "opRating": 331}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 659, "opRating": 340}], "counters": [{"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "dialga", "rating": 236}, {"opponent": "metagross", "rating": 340}, {"opponent": "lugia", "rating": 371}, {"opponent": "mewtwo", "rating": 385}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 53757}, {"moveId": "METAL_CLAW", "uses": 22743}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31204}, {"moveId": "FOUL_PLAY", "uses": 20454}, {"moveId": "IRON_HEAD", "uses": 14576}, {"moveId": "PLAY_ROUGH", "uses": 10225}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "FOUL_PLAY"], "score": 52.7}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 439, "matchups": [{"opponent": "magnezone", "rating": 839, "opRating": 160}, {"opponent": "cobalion", "rating": 815, "opRating": 184}, {"opponent": "magnezone_shadow", "rating": 793, "opRating": 206}, {"opponent": "terrakion", "rating": 771, "opRating": 228}, {"opponent": "nihilego", "rating": 733, "opRating": 266}], "counters": [{"opponent": "dialga", "rating": 293}, {"opponent": "metagross", "rating": 345}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "excadrill", "rating": 495}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 49386}, {"moveId": "ASTONISH", "uses": 27114}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 32166}, {"moveId": "EARTH_POWER", "uses": 23036}, {"moveId": "DYNAMIC_PUNCH", "uses": 21369}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "EARTH_POWER"], "score": 52.5}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 419, "matchups": [{"opponent": "magnezone", "rating": 823, "opRating": 176}, {"opponent": "magnezone_shadow", "rating": 774, "opRating": 225}, {"opponent": "dialga", "rating": 689}, {"opponent": "nihilego", "rating": 685, "opRating": 314}, {"opponent": "excadrill", "rating": 588}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "mewtwo", "rating": 195}, {"opponent": "lugia", "rating": 207}, {"opponent": "metagross", "rating": 229}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53725}, {"moveId": "METAL_CLAW", "uses": 22775}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 32053}, {"moveId": "EARTHQUAKE", "uses": 19911}, {"moveId": "ROCK_TOMB", "uses": 12243}, {"moveId": "BULLDOZE", "uses": 12221}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 52.5}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 437, "matchups": [{"opponent": "tyranitar_shadow", "rating": 726, "opRating": 273}, {"opponent": "gengar", "rating": 723, "opRating": 276}, {"opponent": "tyranitar", "rating": 720, "opRating": 279}, {"opponent": "excadrill", "rating": 527}, {"opponent": "giratina_altered", "rating": 515, "opRating": 484}], "counters": [{"opponent": "lugia", "rating": 197}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "metagross", "rating": 290}, {"opponent": "dialga", "rating": 407}, {"opponent": "giratina_origin", "rating": 454}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45353}, {"moveId": "FURY_CUTTER", "uses": 31147}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35974}, {"moveId": "NIGHT_SLASH", "uses": 35894}, {"moveId": "DIG", "uses": 4533}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 52.5}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 452, "matchups": [{"opponent": "luxray", "rating": 832, "opRating": 167}, {"opponent": "charizard_shadow", "rating": 804, "opRating": 195}, {"opponent": "ursaring", "rating": 780, "opRating": 219}, {"opponent": "victini", "rating": 637, "opRating": 362}, {"opponent": "zarude", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "lugia", "rating": 342}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "mewtwo", "rating": 434}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51232}, {"moveId": "STRUGGLE_BUG", "uses": 25268}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 29046}, {"moveId": "CROSS_POISON", "uses": 26912}, {"moveId": "RETURN", "uses": 11291}, {"moveId": "WATER_PULSE", "uses": 9274}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 52.3}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 467, "matchups": [{"opponent": "golem", "rating": 953, "opRating": 46}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 835, "opRating": 164}, {"opponent": "swampert", "rating": 796}, {"opponent": "swampert_shadow", "rating": 762, "opRating": 237}, {"opponent": "kyogre", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "excadrill", "rating": 358}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 27026}, {"moveId": "CONFUSION", "uses": 24959}, {"moveId": "EXTRASENSORY", "uses": 19425}, {"moveId": "ZEN_HEADBUTT", "uses": 5233}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 31775}, {"moveId": "PSYCHIC", "uses": 30005}, {"moveId": "SOLAR_BEAM", "uses": 14536}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 52.3}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 448, "matchups": [{"opponent": "metagross", "rating": 774}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 760, "opRating": 239}, {"opponent": "metagross_shadow", "rating": 718, "opRating": 281}, {"opponent": "tapu_lele", "rating": 704, "opRating": 295}, {"opponent": "latias", "rating": 665, "opRating": 334}], "counters": [{"opponent": "dialga", "rating": 336}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "swampert", "rating": 365}, {"opponent": "lugia", "rating": 366}, {"opponent": "excadrill", "rating": 488}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43848}, {"moveId": "SUCKER_PUNCH", "uses": 32652}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37777}, {"moveId": "DARK_PULSE", "uses": 24662}, {"moveId": "DAZZLING_GLEAM", "uses": 13884}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 52.3}, {"speciesId": "slowbro_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 417, "matchups": [{"opponent": "landorus_incarnate", "rating": 693, "opRating": 306}, {"opponent": "mamos<PERSON>_shadow", "rating": 677, "opRating": 322}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 569, "opRating": 430}, {"opponent": "garcho<PERSON>", "rating": 530}, {"opponent": "dragonite", "rating": 518}], "counters": [{"opponent": "lugia", "rating": 245}, {"opponent": "dialga", "rating": 271}, {"opponent": "excadrill", "rating": 369}, {"opponent": "zacian_hero", "rating": 413}, {"opponent": "swampert", "rating": 452}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40115}, {"moveId": "WATER_GUN", "uses": 36385}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31496}, {"moveId": "PSYCHIC", "uses": 28903}, {"moveId": "WATER_PULSE", "uses": 15912}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 52.3}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 488, "matchups": [{"opponent": "magnezone", "rating": 841, "opRating": 158}, {"opponent": "tapu_koko", "rating": 806, "opRating": 193}, {"opponent": "magnezone_shadow", "rating": 797, "opRating": 202}, {"opponent": "nihilego", "rating": 684, "opRating": 315}, {"opponent": "raikou_shadow", "rating": 580, "opRating": 419}], "counters": [{"opponent": "giratina_origin", "rating": 225}, {"opponent": "lugia", "rating": 240}, {"opponent": "gyarados", "rating": 280}, {"opponent": "dialga", "rating": 361}, {"opponent": "zacian_hero", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26089}, {"moveId": "DOUBLE_KICK", "uses": 25223}, {"moveId": "FURY_CUTTER", "uses": 21339}, {"moveId": "IRON_TAIL", "uses": 3888}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 20309}, {"moveId": "MEGAHORN", "uses": 16722}, {"moveId": "SLUDGE_WAVE", "uses": 13215}, {"moveId": "EARTHQUAKE", "uses": 8790}, {"moveId": "RETURN", "uses": 8775}, {"moveId": "SAND_TOMB", "uses": 8635}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 52.2}, {"speciesId": "aromatisse", "speciesName": "Aromatisse", "rating": 417, "matchups": [{"opponent": "yveltal", "rating": 668, "opRating": 331}, {"opponent": "dragonite_shadow", "rating": 641, "opRating": 358}, {"opponent": "gyarados", "rating": 559}, {"opponent": "giratina_altered", "rating": 554, "opRating": 445}, {"opponent": "dragonite", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "garcho<PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46602}, {"moveId": "CHARGE_BEAM", "uses": 29898}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 30012}, {"moveId": "THUNDERBOLT", "uses": 18545}, {"moveId": "PSYCHIC", "uses": 15742}, {"moveId": "DRAINING_KISS", "uses": 12176}]}, "moveset": ["CHARM", "MOONBLAST", "THUNDERBOLT"], "score": 52}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 453, "matchups": [{"opponent": "venusaur_shadow", "rating": 822, "opRating": 177}, {"opponent": "tapu_bulu", "rating": 761, "opRating": 238}, {"opponent": "tangrowth_shadow", "rating": 751, "opRating": 248}, {"opponent": "zap<PERSON>_galarian", "rating": 617, "opRating": 382}, {"opponent": "buzzwole", "rating": 577, "opRating": 422}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "lugia", "rating": 316}, {"opponent": "mewtwo", "rating": 343}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 43650}, {"moveId": "STEEL_WING", "uses": 32850}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33564}, {"moveId": "SKY_ATTACK", "uses": 25162}, {"moveId": "FLASH_CANNON", "uses": 9650}, {"moveId": "RETURN", "uses": 8189}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 52}, {"speciesId": "skar<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 458, "matchups": [{"opponent": "venusaur_shadow", "rating": 822, "opRating": 177}, {"opponent": "gallade_shadow", "rating": 788, "opRating": 211}, {"opponent": "celebi", "rating": 785, "opRating": 214}, {"opponent": "virizion", "rating": 741, "opRating": 258}, {"opponent": "zarude", "rating": 664, "opRating": 335}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "garcho<PERSON>", "rating": 373}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 43952}, {"moveId": "STEEL_WING", "uses": 32548}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37530}, {"moveId": "SKY_ATTACK", "uses": 28088}, {"moveId": "FLASH_CANNON", "uses": 10835}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 52}, {"speciesId": "<PERSON><PERSON>_<PERSON>", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 464, "matchups": [{"opponent": "weavile_shadow", "rating": 926, "opRating": 73}, {"opponent": "honchk<PERSON>_shadow", "rating": 926, "opRating": 73}, {"opponent": "staraptor_shadow", "rating": 893, "opRating": 106}, {"opponent": "moltres_shadow", "rating": 862, "opRating": 137}, {"opponent": "victini", "rating": 585, "opRating": 414}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "mewtwo", "rating": 226}, {"opponent": "lugia", "rating": 371}, {"opponent": "zacian_hero", "rating": 462}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50821}, {"moveId": "STRUGGLE_BUG", "uses": 25679}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 33887}, {"moveId": "CROSS_POISON", "uses": 31368}, {"moveId": "WATER_PULSE", "uses": 11122}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 51.9}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 453, "matchups": [{"opponent": "golem", "rating": 953, "opRating": 46}, {"opponent": "swampert", "rating": 829}, {"opponent": "swampert_shadow", "rating": 796, "opRating": 203}, {"opponent": "kyogre", "rating": 628, "opRating": 371}, {"opponent": "sylveon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 160}, {"opponent": "mewtwo", "rating": 195}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "gyarados", "rating": 314}, {"opponent": "garcho<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 26294}, {"moveId": "CONFUSION", "uses": 25124}, {"moveId": "EXTRASENSORY", "uses": 19687}, {"moveId": "ZEN_HEADBUTT", "uses": 5420}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26829}, {"moveId": "PSYCHIC", "uses": 24532}, {"moveId": "RETURN", "uses": 12894}, {"moveId": "SOLAR_BEAM", "uses": 12221}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 51.9}, {"speciesId": "kabutops", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 447, "matchups": [{"opponent": "ho_oh", "rating": 936}, {"opponent": "moltres", "rating": 929, "opRating": 70}, {"opponent": "ho_oh_shadow", "rating": 926, "opRating": 73}, {"opponent": "chandelure", "rating": 926, "opRating": 73}, {"opponent": "entei_shadow", "rating": 911, "opRating": 88}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "lugia", "rating": 376}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 25448}, {"moveId": "FURY_CUTTER", "uses": 23054}, {"moveId": "WATERFALL", "uses": 20321}, {"moveId": "ROCK_SMASH", "uses": 7619}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 32979}, {"moveId": "ANCIENT_POWER", "uses": 29719}, {"moveId": "WATER_PULSE", "uses": 13791}]}, "moveset": ["MUD_SHOT", "STONE_EDGE", "ANCIENT_POWER"], "score": 51.9}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 480, "matchups": [{"opponent": "magnezone_shadow", "rating": 768, "opRating": 231}, {"opponent": "sylveon", "rating": 757}, {"opponent": "nihilego", "rating": 722, "opRating": 277}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 595}, {"opponent": "zacian_hero", "rating": 575}], "counters": [{"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "gyarados", "rating": 144}, {"opponent": "lugia", "rating": 257}, {"opponent": "dialga", "rating": 421}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 25861}, {"moveId": "POISON_JAB", "uses": 25773}, {"moveId": "FURY_CUTTER", "uses": 21275}, {"moveId": "IRON_TAIL", "uses": 3537}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 22464}, {"moveId": "MEGAHORN", "uses": 19165}, {"moveId": "SLUDGE_WAVE", "uses": 15602}, {"moveId": "EARTHQUAKE", "uses": 9685}, {"moveId": "SAND_TOMB", "uses": 9429}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 51.7}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 437, "matchups": [{"opponent": "golem", "rating": 950, "opRating": 50}, {"opponent": "swampert", "rating": 755}, {"opponent": "swampert_shadow", "rating": 736, "opRating": 263}, {"opponent": "tapu_fini", "rating": 652, "opRating": 347}, {"opponent": "kyogre", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 127}, {"opponent": "mewtwo", "rating": 247}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "gyarados", "rating": 350}, {"opponent": "zacian_hero", "rating": 410}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36163}, {"moveId": "INFESTATION", "uses": 28991}, {"moveId": "ACID", "uses": 11331}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 29598}, {"moveId": "GRASS_KNOT", "uses": 25429}, {"moveId": "BULLDOZE", "uses": 12143}, {"moveId": "RETURN", "uses": 9385}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 51.2}, {"speciesId": "altaria", "speciesName": "Altaria", "rating": 440, "matchups": [{"opponent": "chesnaught", "rating": 823, "opRating": 176}, {"opponent": "luxray_shadow", "rating": 817, "opRating": 182}, {"opponent": "heracross", "rating": 695, "opRating": 304}, {"opponent": "swampert", "rating": 573}, {"opponent": "grou<PERSON>", "rating": 564}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "dialga", "rating": 209}, {"opponent": "mewtwo", "rating": 286}, {"opponent": "gyarados", "rating": 288}, {"opponent": "giratina_origin", "rating": 384}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52595}, {"moveId": "PECK", "uses": 23905}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 34979}, {"moveId": "MOONBLAST", "uses": 19789}, {"moveId": "DRAGON_PULSE", "uses": 16748}, {"moveId": "DAZZLING_GLEAM", "uses": 5109}]}, "moveset": ["DRAGON_BREATH", "SKY_ATTACK", "MOONBLAST"], "score": 51.1}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 403, "matchups": [{"opponent": "gengar", "rating": 822, "opRating": 177}, {"opponent": "chandelure", "rating": 668, "opRating": 331}, {"opponent": "magnezone_shadow", "rating": 552, "opRating": 447}, {"opponent": "electivire_shadow", "rating": 548, "opRating": 451}, {"opponent": "giratina_origin", "rating": 538}], "counters": [{"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "dialga", "rating": 271}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "metagross", "rating": 375}, {"opponent": "excadrill", "rating": 404}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 57602}, {"moveId": "LOW_KICK", "uses": 18898}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19629}, {"moveId": "STOMP", "uses": 17803}, {"moveId": "BRICK_BREAK", "uses": 13317}, {"moveId": "OUTRAGE", "uses": 11605}, {"moveId": "EARTHQUAKE", "uses": 11426}, {"moveId": "POWER_UP_PUNCH", "uses": 2739}]}, "moveset": ["MUD_SLAP", "CRUNCH", "STOMP"], "score": 51.1}, {"speciesId": "dragalge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 474, "matchups": [{"opponent": "venusaur_shadow", "rating": 781, "opRating": 218}, {"opponent": "thundurus_incarnate", "rating": 724, "opRating": 275}, {"opponent": "entei_shadow", "rating": 674, "opRating": 325}, {"opponent": "heracross", "rating": 630, "opRating": 369}, {"opponent": "raikou_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "lugia", "rating": 235}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "giratina_origin", "rating": 484}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 37621}, {"moveId": "WATER_GUN", "uses": 24267}, {"moveId": "ACID", "uses": 14530}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 31028}, {"moveId": "OUTRAGE", "uses": 27306}, {"moveId": "GUNK_SHOT", "uses": 13271}, {"moveId": "HYDRO_PUMP", "uses": 4952}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "OUTRAGE"], "score": 50.7}, {"speciesId": "lanturn", "speciesName": "Lanturn", "rating": 439, "matchups": [{"opponent": "moltres", "rating": 746, "opRating": 253}, {"opponent": "gyarado<PERSON>_shadow", "rating": 719, "opRating": 280}, {"opponent": "gyarados", "rating": 681}, {"opponent": "kyogre", "rating": 664, "opRating": 335}, {"opponent": "ho_oh_shadow", "rating": 571, "opRating": 428}], "counters": [{"opponent": "metagross", "rating": 177}, {"opponent": "dialga", "rating": 179}, {"opponent": "mewtwo", "rating": 187}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "lugia", "rating": 380}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 29459}, {"moveId": "WATER_GUN", "uses": 28521}, {"moveId": "CHARGE_BEAM", "uses": 18485}], "chargedMoves": [{"moveId": "SURF", "uses": 39479}, {"moveId": "THUNDERBOLT", "uses": 21480}, {"moveId": "THUNDER", "uses": 9271}, {"moveId": "HYDRO_PUMP", "uses": 6176}]}, "moveset": ["SPARK", "SURF", "THUNDERBOLT"], "score": 50.7}, {"speciesId": "rapidash_galarian", "speciesName": "Rapidash (Galarian)", "rating": 432, "matchups": [{"opponent": "celebi", "rating": 899, "opRating": 100}, {"opponent": "hydreigon", "rating": 879, "opRating": 120}, {"opponent": "latios_shadow", "rating": 838, "opRating": 161}, {"opponent": "kommo_o", "rating": 701, "opRating": 298}, {"opponent": "dragonite", "rating": 604}], "counters": [{"opponent": "mewtwo", "rating": 161}, {"opponent": "dialga", "rating": 184}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "lugia", "rating": 295}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 36337}, {"moveId": "PSYCHO_CUT", "uses": 32394}, {"moveId": "LOW_KICK", "uses": 7747}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27406}, {"moveId": "MEGAHORN", "uses": 17179}, {"moveId": "PSYCHIC", "uses": 16816}, {"moveId": "PLAY_ROUGH", "uses": 15030}]}, "moveset": ["FAIRY_WIND", "BODY_SLAM", "MEGAHORN"], "score": 50.7}, {"speciesId": "slowking", "speciesName": "Slowking", "rating": 433, "matchups": [{"opponent": "heracross", "rating": 708, "opRating": 291}, {"opponent": "terrakion", "rating": 693, "opRating": 306}, {"opponent": "kommo_o", "rating": 675, "opRating": 324}, {"opponent": "sneasler", "rating": 621, "opRating": 378}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "gyarados", "rating": 360}, {"opponent": "dragonite", "rating": 361}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38698}, {"moveId": "WATER_GUN", "uses": 37802}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 26848}, {"moveId": "BLIZZARD", "uses": 21444}, {"moveId": "RETURN", "uses": 14135}, {"moveId": "FIRE_BLAST", "uses": 14102}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 50.7}, {"speciesId": "glalie", "speciesName": "G<PERSON><PERSON>", "rating": 392, "matchups": [{"opponent": "thundurus_therian", "rating": 802, "opRating": 197}, {"opponent": "gliscor_shadow", "rating": 781, "opRating": 218}, {"opponent": "salamence", "rating": 752, "opRating": 247}, {"opponent": "landorus_incarnate", "rating": 563, "opRating": 436}, {"opponent": "garcho<PERSON>", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 230}, {"opponent": "lugia", "rating": 280}, {"opponent": "gyarados", "rating": 304}, {"opponent": "giratina_origin", "rating": 316}, {"opponent": "mewtwo", "rating": 354}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46857}, {"moveId": "FROST_BREATH", "uses": 29643}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 49161}, {"moveId": "SHADOW_BALL", "uses": 20388}, {"moveId": "GYRO_BALL", "uses": 6982}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "SHADOW_BALL"], "score": 50.6}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 406, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 733, "opRating": 266}, {"opponent": "gallade_shadow", "rating": 674, "opRating": 325}, {"opponent": "swampert", "rating": 644}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "swampert_shadow", "rating": 592, "opRating": 407}], "counters": [{"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 230}, {"opponent": "garcho<PERSON>", "rating": 349}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "metagross", "rating": 366}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49268}, {"moveId": "RAZOR_LEAF", "uses": 27232}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26410}, {"moveId": "SEED_BOMB", "uses": 21650}, {"moveId": "FOUL_PLAY", "uses": 19802}, {"moveId": "FIRE_BLAST", "uses": 8568}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 50.6}, {"speciesId": "absol", "speciesName": "Absol", "rating": 403, "matchups": [{"opponent": "celebi", "rating": 879, "opRating": 120}, {"opponent": "chandelure", "rating": 832, "opRating": 167}, {"opponent": "mewtwo_shadow", "rating": 691, "opRating": 308}, {"opponent": "metagross_shadow", "rating": 677, "opRating": 322}, {"opponent": "mewtwo", "rating": 610}], "counters": [{"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "dialga", "rating": 266}, {"opponent": "gyarados", "rating": 288}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "metagross", "rating": 415}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41557}, {"moveId": "PSYCHO_CUT", "uses": 34943}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25209}, {"moveId": "MEGAHORN", "uses": 17501}, {"moveId": "THUNDER", "uses": 13137}, {"moveId": "PAYBACK", "uses": 11476}, {"moveId": "RETURN", "uses": 9299}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 50.4}, {"speciesId": "carracosta", "speciesName": "Carracosta", "rating": 444, "matchups": [{"opponent": "darmanitan_standard", "rating": 944, "opRating": 55}, {"opponent": "chandelure", "rating": 935, "opRating": 64}, {"opponent": "entei_shadow", "rating": 834, "opRating": 165}, {"opponent": "entei", "rating": 819, "opRating": 180}, {"opponent": "ho_oh", "rating": 674, "opRating": 325}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "metagross", "rating": 232}, {"opponent": "zacian_hero", "rating": 248}, {"opponent": "mewtwo", "rating": 304}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39285}, {"moveId": "ROCK_THROW", "uses": 37215}], "chargedMoves": [{"moveId": "SURF", "uses": 29826}, {"moveId": "BODY_SLAM", "uses": 25691}, {"moveId": "ANCIENT_POWER", "uses": 21017}]}, "moveset": ["WATER_GUN", "SURF", "BODY_SLAM"], "score": 50.4}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 442, "matchups": [{"opponent": "metagross_shadow", "rating": 774, "opRating": 225}, {"opponent": "tapu_lele", "rating": 760, "opRating": 239}, {"opponent": "pinsir_shadow", "rating": 757, "opRating": 242}, {"opponent": "genesect", "rating": 605, "opRating": 394}, {"opponent": "cobalion", "rating": 602, "opRating": 397}], "counters": [{"opponent": "dialga", "rating": 282}, {"opponent": "lugia", "rating": 321}, {"opponent": "excadrill", "rating": 416}, {"opponent": "zacian_hero", "rating": 421}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43287}, {"moveId": "SUCKER_PUNCH", "uses": 33213}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32472}, {"moveId": "DARK_PULSE", "uses": 21018}, {"moveId": "DAZZLING_GLEAM", "uses": 11572}, {"moveId": "RETURN", "uses": 11309}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 50.3}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 440, "matchups": [{"opponent": "tapu_bulu", "rating": 780, "opRating": 219}, {"opponent": "electivire_shadow", "rating": 767, "opRating": 232}, {"opponent": "genesect_chill", "rating": 694, "opRating": 305}, {"opponent": "genesect_burn", "rating": 694, "opRating": 305}, {"opponent": "sylveon", "rating": 522, "opRating": 477}], "counters": [{"opponent": "giratina_origin", "rating": 185}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "dialga", "rating": 364}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38745}, {"moveId": "EMBER", "uses": 37755}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 34869}, {"moveId": "EARTHQUAKE", "uses": 26338}, {"moveId": "SOLAR_BEAM", "uses": 15306}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 50.3}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 427, "matchups": [{"opponent": "staraptor_shadow", "rating": 876, "opRating": 123}, {"opponent": "gyarados", "rating": 845}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 605}, {"opponent": "kyogre", "rating": 528, "opRating": 471}, {"opponent": "swampert", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 157}, {"opponent": "zacian_hero", "rating": 208}, {"opponent": "metagross", "rating": 220}, {"opponent": "lugia", "rating": 226}, {"opponent": "mewtwo", "rating": 244}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50049}, {"moveId": "TACKLE", "uses": 26451}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 49835}, {"moveId": "ENERGY_BALL", "uses": 20575}, {"moveId": "SWIFT", "uses": 6139}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ENERGY_BALL"], "score": 50}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 437, "matchups": [{"opponent": "golem", "rating": 933, "opRating": 66}, {"opponent": "rhydon", "rating": 866, "opRating": 133}, {"opponent": "swampert", "rating": 736}, {"opponent": "swampert_shadow", "rating": 666, "opRating": 333}, {"opponent": "rhyperior", "rating": 644, "opRating": 355}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "gyarados", "rating": 404}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36434}, {"moveId": "INFESTATION", "uses": 29474}, {"moveId": "ACID", "uses": 10655}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 33795}, {"moveId": "GRASS_KNOT", "uses": 28916}, {"moveId": "BULLDOZE", "uses": 13789}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 49.8}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 422, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 750, "opRating": 250}, {"opponent": "genesect", "rating": 704, "opRating": 295}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 629, "opRating": 370}, {"opponent": "gallade", "rating": 620, "opRating": 379}, {"opponent": "heracross", "rating": 554, "opRating": 445}], "counters": [{"opponent": "dialga", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "lugia", "rating": 273}, {"opponent": "zacian_hero", "rating": 355}, {"opponent": "metagross", "rating": 415}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55504}, {"moveId": "ASTONISH", "uses": 20996}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 25272}, {"moveId": "DARK_PULSE", "uses": 16507}, {"moveId": "OMINOUS_WIND", "uses": 12970}, {"moveId": "PSYCHIC", "uses": 12567}, {"moveId": "RETURN", "uses": 9112}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 49.5}, {"speciesId": "forretress_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 447, "matchups": [{"opponent": "weavile_shadow", "rating": 871, "opRating": 128}, {"opponent": "hoopa_unbound", "rating": 786, "opRating": 213}, {"opponent": "tapu_bulu", "rating": 689, "opRating": 310}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 621, "opRating": 378}, {"opponent": "nihilego", "rating": 606, "opRating": 393}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "metagross", "rating": 218}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "mewtwo", "rating": 351}, {"opponent": "garcho<PERSON>", "rating": 377}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47018}, {"moveId": "STRUGGLE_BUG", "uses": 29482}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 20493}, {"moveId": "EARTHQUAKE", "uses": 17497}, {"moveId": "HEAVY_SLAM", "uses": 16817}, {"moveId": "ROCK_TOMB", "uses": 13160}, {"moveId": "SAND_TOMB", "uses": 8566}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 49.5}, {"speciesId": "reuniclus", "speciesName": "Reuniclus", "rating": 415, "matchups": [{"opponent": "victini", "rating": 644, "opRating": 355}, {"opponent": "landorus_incarnate", "rating": 630, "opRating": 369}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 600, "opRating": 400}, {"opponent": "dragonite", "rating": 551}, {"opponent": "garcho<PERSON>", "rating": 539}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "lugia", "rating": 309}, {"opponent": "zacian_hero", "rating": 329}, {"opponent": "grou<PERSON>", "rating": 339}, {"opponent": "metagross", "rating": 369}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5774}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5224}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 5166}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5107}, {"moveId": "HIDDEN_POWER_DARK", "uses": 5031}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4965}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4737}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4663}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4639}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4496}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4471}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4382}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4060}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4059}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3967}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3750}, {"moveId": "ZEN_HEADBUTT", "uses": 1784}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32331}, {"moveId": "FUTURE_SIGHT", "uses": 24836}, {"moveId": "THUNDER", "uses": 19287}]}, "moveset": ["HIDDEN_POWER_ICE", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 49.5}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 464, "matchups": [{"opponent": "tapu_bulu", "rating": 809, "opRating": 190}, {"opponent": "chesnaught", "rating": 809, "opRating": 190}, {"opponent": "virizion", "rating": 746, "opRating": 253}, {"opponent": "latias_shadow", "rating": 700, "opRating": 299}, {"opponent": "zarude", "rating": 626, "opRating": 373}], "counters": [{"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "dialga", "rating": 206}, {"opponent": "dragonite", "rating": 273}, {"opponent": "gyarados", "rating": 306}, {"opponent": "zacian_hero", "rating": 494}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 43393}, {"moveId": "BUG_BITE", "uses": 33107}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25283}, {"moveId": "MEGAHORN", "uses": 24145}, {"moveId": "SLUDGE_BOMB", "uses": 19172}, {"moveId": "GYRO_BALL", "uses": 7855}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "MEGAHORN"], "score": 49.5}, {"speciesId": "slowking_shadow", "speciesName": "Slowking (Shadow)", "rating": 412, "matchups": [{"opponent": "mamos<PERSON>_shadow", "rating": 677, "opRating": 322}, {"opponent": "terrakion", "rating": 634, "opRating": 365}, {"opponent": "kommo_o", "rating": 621, "opRating": 378}, {"opponent": "sneasler", "rating": 597, "opRating": 402}, {"opponent": "sylveon", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 230}, {"opponent": "metagross", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 380}, {"opponent": "dragonite", "rating": 388}, {"opponent": "zacian_hero", "rating": 488}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40140}, {"moveId": "WATER_GUN", "uses": 36360}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 33182}, {"moveId": "BLIZZARD", "uses": 26070}, {"moveId": "FIRE_BLAST", "uses": 17152}, {"moveId": "FRUSTRATION", "uses": 8}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 49.5}, {"speciesId": "deoxys", "speciesName": "Deoxys", "rating": 421, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 811, "opRating": 188}, {"opponent": "zap<PERSON>_galarian", "rating": 787, "opRating": 212}, {"opponent": "staraptor_shadow", "rating": 787, "opRating": 212}, {"opponent": "ho_oh_shadow", "rating": 669, "opRating": 330}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "metagross", "rating": 325}, {"opponent": "gyarados", "rating": 407}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 60145}, {"moveId": "ZEN_HEADBUTT", "uses": 16355}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 42190}, {"moveId": "THUNDERBOLT", "uses": 22780}, {"moveId": "HYPER_BEAM", "uses": 11526}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 49.3}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 422, "matchups": [{"opponent": "hoopa", "rating": 823, "opRating": 176}, {"opponent": "luxray_shadow", "rating": 620, "opRating": 379}, {"opponent": "gyarados", "rating": 575}, {"opponent": "braviary", "rating": 570, "opRating": 429}, {"opponent": "staraptor", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "giratina_origin", "rating": 227}, {"opponent": "metagross", "rating": 281}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "lugia", "rating": 361}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 57640}, {"moveId": "ACID", "uses": 18860}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27209}, {"moveId": "CRUNCH", "uses": 25142}, {"moveId": "THUNDERBOLT", "uses": 20559}, {"moveId": "ACID_SPRAY", "uses": 3583}]}, "moveset": ["SPARK", "DRAGON_CLAW", "CRUNCH"], "score": 49.3}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 402, "matchups": [{"opponent": "moltres_shadow", "rating": 858, "opRating": 141}, {"opponent": "tyranitar_shadow", "rating": 806, "opRating": 193}, {"opponent": "electivire_shadow", "rating": 703, "opRating": 296}, {"opponent": "nihilego", "rating": 682, "opRating": 317}, {"opponent": "excadrill", "rating": 579}], "counters": [{"opponent": "zacian_hero", "rating": 190}, {"opponent": "dragonite", "rating": 279}, {"opponent": "gyarados", "rating": 296}, {"opponent": "dialga", "rating": 448}, {"opponent": "metagross", "rating": 465}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 44718}, {"moveId": "WATER_GUN", "uses": 31782}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 30421}, {"moveId": "STONE_EDGE", "uses": 24938}, {"moveId": "SLUDGE_BOMB", "uses": 17150}, {"moveId": "ACID_SPRAY", "uses": 3859}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 49.3}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 416, "matchups": [{"opponent": "charizard", "rating": 811, "opRating": 188}, {"opponent": "gyarados", "rating": 728}, {"opponent": "moltres_shadow", "rating": 685, "opRating": 314}, {"opponent": "gyarado<PERSON>_shadow", "rating": 653, "opRating": 346}, {"opponent": "kyogre", "rating": 562, "opRating": 437}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "metagross", "rating": 264}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "lugia", "rating": 454}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 55499}, {"moveId": "ASTONISH", "uses": 21001}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34976}, {"moveId": "OMINOUS_WIND", "uses": 26213}, {"moveId": "THUNDER", "uses": 15165}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 49.3}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 437, "matchups": [{"opponent": "chesnaught", "rating": 856, "opRating": 143}, {"opponent": "virizion", "rating": 811, "opRating": 188}, {"opponent": "buzzwole", "rating": 797, "opRating": 202}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 766, "opRating": 233}, {"opponent": "sneasler", "rating": 660, "opRating": 339}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 323}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "gyarados", "rating": 340}, {"opponent": "giratina_origin", "rating": 358}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45368}, {"moveId": "BITE", "uses": 31132}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 24417}, {"moveId": "SHADOW_BALL", "uses": 19585}, {"moveId": "POISON_FANG", "uses": 11065}, {"moveId": "AIR_CUTTER", "uses": 7446}, {"moveId": "RETURN", "uses": 7400}, {"moveId": "SLUDGE_BOMB", "uses": 6514}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 49.2}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 421, "matchups": [{"opponent": "metagross_shadow", "rating": 766, "opRating": 233}, {"opponent": "tapu_lele", "rating": 733, "opRating": 266}, {"opponent": "pinsir_shadow", "rating": 725, "opRating": 275}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 683, "opRating": 316}, {"opponent": "latias_shadow", "rating": 666, "opRating": 333}], "counters": [{"opponent": "dialga", "rating": 288}, {"opponent": "lugia", "rating": 328}, {"opponent": "excadrill", "rating": 425}, {"opponent": "zacian_hero", "rating": 430}, {"opponent": "metagross", "rating": 468}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55692}, {"moveId": "ASTONISH", "uses": 20808}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 28441}, {"moveId": "DARK_PULSE", "uses": 18922}, {"moveId": "OMINOUS_WIND", "uses": 14583}, {"moveId": "PSYCHIC", "uses": 14531}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 49}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 425, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 767, "opRating": 232}, {"opponent": "tapu_bulu", "rating": 732, "opRating": 267}, {"opponent": "virizion", "rating": 718, "opRating": 281}, {"opponent": "giratina_origin", "rating": 566}, {"opponent": "grou<PERSON>", "rating": 546}], "counters": [{"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "dialga", "rating": 173}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "gyarados", "rating": 322}, {"opponent": "lugia", "rating": 335}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47360}, {"moveId": "STEEL_WING", "uses": 29140}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 49780}, {"moveId": "HYPER_BEAM", "uses": 17445}, {"moveId": "HEAT_WAVE", "uses": 9298}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 49}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 426, "matchups": [{"opponent": "kommo_o", "rating": 872, "opRating": 127}, {"opponent": "buzzwole", "rating": 721, "opRating": 278}, {"opponent": "dragonite_shadow", "rating": 677, "opRating": 322}, {"opponent": "dragonite", "rating": 647}, {"opponent": "yveltal", "rating": 550, "opRating": 449}], "counters": [{"opponent": "dialga", "rating": 203}, {"opponent": "lugia", "rating": 209}, {"opponent": "gyarados", "rating": 280}, {"opponent": "giratina_origin", "rating": 290}, {"opponent": "zacian_hero", "rating": 364}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 48694}, {"moveId": "TACKLE", "uses": 27806}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 23793}, {"moveId": "PLAY_ROUGH", "uses": 23135}, {"moveId": "OVERHEAT", "uses": 19881}, {"moveId": "HYPER_BEAM", "uses": 9658}]}, "moveset": ["FAIRY_WIND", "SLUDGE", "PLAY_ROUGH"], "score": 49}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 460, "matchups": [{"opponent": "braviary", "rating": 902, "opRating": 97}, {"opponent": "moltres_shadow", "rating": 817, "opRating": 182}, {"opponent": "moltres", "rating": 801, "opRating": 198}, {"opponent": "articuno_galarian", "rating": 667, "opRating": 332}, {"opponent": "gyarados", "rating": 615}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "dragonite", "rating": 284}, {"opponent": "metagross", "rating": 290}, {"opponent": "lugia", "rating": 321}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 62548}, {"moveId": "LOW_KICK", "uses": 13952}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 45079}, {"moveId": "FLAME_CHARGE", "uses": 21811}, {"moveId": "DISCHARGE", "uses": 9669}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 49}, {"speciesId": "scyther", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 400, "matchups": [{"opponent": "al<PERSON><PERSON>_shadow", "rating": 923, "opRating": 76}, {"opponent": "hoopa_unbound", "rating": 789, "opRating": 210}, {"opponent": "latios", "rating": 681, "opRating": 318}, {"opponent": "escavalier", "rating": 646, "opRating": 353}, {"opponent": "zarude", "rating": 636, "opRating": 363}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "metagross", "rating": 238}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "gyarados", "rating": 314}, {"opponent": "mewtwo", "rating": 489}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35681}, {"moveId": "AIR_SLASH", "uses": 25178}, {"moveId": "STEEL_WING", "uses": 15670}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25834}, {"moveId": "X_SCISSOR", "uses": 18283}, {"moveId": "AERIAL_ACE", "uses": 13375}, {"moveId": "BUG_BUZZ", "uses": 11887}, {"moveId": "RETURN", "uses": 7046}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 48.7}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 429, "matchups": [{"opponent": "braviary", "rating": 901, "opRating": 98}, {"opponent": "moltres", "rating": 830, "opRating": 169}, {"opponent": "articuno_galarian", "rating": 707, "opRating": 292}, {"opponent": "gyarados", "rating": 623}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 556, "opRating": 443}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "metagross", "rating": 308}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "lugia", "rating": 340}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 29143}, {"moveId": "THUNDER_SHOCK", "uses": 25706}, {"moveId": "SPARK", "uses": 21545}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32412}, {"moveId": "THUNDER_PUNCH", "uses": 15861}, {"moveId": "PSYCHIC", "uses": 14307}, {"moveId": "GRASS_KNOT", "uses": 13871}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 48.5}, {"speciesId": "crobat_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 435, "matchups": [{"opponent": "virizion", "rating": 853, "opRating": 146}, {"opponent": "tapu_bulu", "rating": 837, "opRating": 162}, {"opponent": "buzzwole", "rating": 806, "opRating": 193}, {"opponent": "escavalier", "rating": 716, "opRating": 283}, {"opponent": "zacian_hero", "rating": 589}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "dragonite", "rating": 265}, {"opponent": "lugia", "rating": 307}, {"opponent": "gyarados", "rating": 373}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45760}, {"moveId": "BITE", "uses": 30740}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 26949}, {"moveId": "SHADOW_BALL", "uses": 21750}, {"moveId": "POISON_FANG", "uses": 12062}, {"moveId": "AIR_CUTTER", "uses": 8267}, {"moveId": "SLUDGE_BOMB", "uses": 7313}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 48.2}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 453, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 773}, {"opponent": "heracross", "rating": 773, "opRating": 226}, {"opponent": "sylveon", "rating": 768, "opRating": 231}, {"opponent": "sneasler", "rating": 698, "opRating": 301}, {"opponent": "zacian_hero", "rating": 603}], "counters": [{"opponent": "mewtwo", "rating": 109}, {"opponent": "dialga", "rating": 149}, {"opponent": "lugia", "rating": 214}, {"opponent": "gyarados", "rating": 347}, {"opponent": "dragonite", "rating": 348}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 41227}, {"moveId": "CONFUSION", "uses": 35273}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 28878}, {"moveId": "PSYCHIC", "uses": 27228}, {"moveId": "FOCUS_BLAST", "uses": 20351}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PSYCHIC"], "score": 48}, {"speciesId": "wailord", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 402, "matchups": [{"opponent": "darmanitan_standard", "rating": 901, "opRating": 98}, {"opponent": "typhlosion_shadow", "rating": 896, "opRating": 103}, {"opponent": "rhyperior", "rating": 782, "opRating": 217}, {"opponent": "ma<PERSON><PERSON>", "rating": 675, "opRating": 324}, {"opponent": "mamos<PERSON>_shadow", "rating": 671, "opRating": 328}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "lugia", "rating": 328}, {"opponent": "gyarados", "rating": 363}, {"opponent": "excadrill", "rating": 495}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66758}, {"moveId": "ZEN_HEADBUTT", "uses": 9743}], "chargedMoves": [{"moveId": "SURF", "uses": 46195}, {"moveId": "BLIZZARD", "uses": 20062}, {"moveId": "HYPER_BEAM", "uses": 10203}]}, "moveset": ["WATER_GUN", "SURF", "BLIZZARD"], "score": 48}, {"speciesId": "aerodactyl_shadow", "speciesName": "Aerodactyl (Shadow)", "rating": 435, "matchups": [{"opponent": "moltres_shadow", "rating": 883, "opRating": 116}, {"opponent": "moltres", "rating": 869, "opRating": 130}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 633}, {"opponent": "genesect_burn", "rating": 633, "opRating": 366}, {"opponent": "ho_oh", "rating": 561, "opRating": 438}], "counters": [{"opponent": "mewtwo", "rating": 117}, {"opponent": "dialga", "rating": 122}, {"opponent": "gyarados", "rating": 270}, {"opponent": "zacian_hero", "rating": 447}, {"opponent": "lugia", "rating": 473}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36566}, {"moveId": "BITE", "uses": 20369}, {"moveId": "STEEL_WING", "uses": 19602}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29250}, {"moveId": "EARTH_POWER", "uses": 19283}, {"moveId": "IRON_HEAD", "uses": 10733}, {"moveId": "ANCIENT_POWER", "uses": 10480}, {"moveId": "HYPER_BEAM", "uses": 6621}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 47.9}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 396, "matchups": [{"opponent": "swampert_shadow", "rating": 810, "opRating": 189}, {"opponent": "krookodile", "rating": 704, "opRating": 295}, {"opponent": "luxray_shadow", "rating": 698, "opRating": 301}, {"opponent": "swampert", "rating": 676}, {"opponent": "excadrill", "rating": 512}], "counters": [{"opponent": "giratina_origin", "rating": 256}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 323}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "gyarados", "rating": 340}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38581}, {"moveId": "RAZOR_LEAF", "uses": 22684}, {"moveId": "ACID", "uses": 15234}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 50305}, {"moveId": "DAZZLING_GLEAM", "uses": 10879}, {"moveId": "RETURN", "uses": 10304}, {"moveId": "PETAL_BLIZZARD", "uses": 5285}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 47.9}, {"speciesId": "mantine", "speciesName": "<PERSON><PERSON>", "rating": 378, "matchups": [{"opponent": "pinsir_shadow", "rating": 671, "opRating": 328}, {"opponent": "heracross", "rating": 667, "opRating": 332}, {"opponent": "grou<PERSON>", "rating": 543}, {"opponent": "buzzwole", "rating": 533, "opRating": 466}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "giratina_origin", "rating": 254}, {"opponent": "garcho<PERSON>", "rating": 399}, {"opponent": "dragonite", "rating": 401}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 26854}, {"moveId": "BUBBLE", "uses": 26189}, {"moveId": "BULLET_SEED", "uses": 23469}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26867}, {"moveId": "AERIAL_ACE", "uses": 24174}, {"moveId": "BUBBLE_BEAM", "uses": 12974}, {"moveId": "WATER_PULSE", "uses": 12441}]}, "moveset": ["WING_ATTACK", "ICE_BEAM", "AERIAL_ACE"], "score": 47.7}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 436, "matchups": [{"opponent": "gengar", "rating": 755, "opRating": 244}, {"opponent": "tapu_bulu", "rating": 691, "opRating": 308}, {"opponent": "genesect_chill", "rating": 683, "opRating": 316}, {"opponent": "genesect_burn", "rating": 683, "opRating": 316}, {"opponent": "giratina_origin", "rating": 591}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "mewtwo", "rating": 208}, {"opponent": "gyarados", "rating": 288}, {"opponent": "metagross", "rating": 290}, {"opponent": "lugia", "rating": 300}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 60278}, {"moveId": "TAKE_DOWN", "uses": 16222}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25068}, {"moveId": "DARK_PULSE", "uses": 21862}, {"moveId": "OVERHEAT", "uses": 19296}, {"moveId": "SOLAR_BEAM", "uses": 10301}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "DARK_PULSE"], "score": 47.7}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 410, "matchups": [{"opponent": "rhyperior", "rating": 833, "opRating": 166}, {"opponent": "swampert", "rating": 818}, {"opponent": "swampert_shadow", "rating": 785, "opRating": 214}, {"opponent": "zacian_hero", "rating": 630}, {"opponent": "kyogre", "rating": 619, "opRating": 380}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 164}, {"opponent": "gyarados", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "excadrill", "rating": 393}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42735}, {"moveId": "ASTONISH", "uses": 33765}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34150}, {"moveId": "ENERGY_BALL", "uses": 21335}, {"moveId": "SHADOW_SNEAK", "uses": 21076}]}, "moveset": ["RAZOR_LEAF", "BRAVE_BIRD", "ENERGY_BALL"], "score": 47.6}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 420, "matchups": [{"opponent": "suicune_shadow", "rating": 690, "opRating": 309}, {"opponent": "gengar", "rating": 687, "opRating": 312}, {"opponent": "golisopod", "rating": 678, "opRating": 321}, {"opponent": "gyarados", "rating": 578}, {"opponent": "gyarado<PERSON>_shadow", "rating": 571, "opRating": 428}], "counters": [{"opponent": "dialga", "rating": 157}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "mewtwo", "rating": 265}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "excadrill", "rating": 320}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 39826}, {"moveId": "TACKLE", "uses": 30084}, {"moveId": "TAKE_DOWN", "uses": 6554}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34414}, {"moveId": "WILD_CHARGE", "uses": 27247}, {"moveId": "PAYBACK", "uses": 14965}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "WILD_CHARGE"], "score": 47.6}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 413, "matchups": [{"opponent": "moltres_shadow", "rating": 859, "opRating": 140}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 686}, {"opponent": "tapu_lele", "rating": 672, "opRating": 327}, {"opponent": "lugia", "rating": 630}, {"opponent": "sylveon", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "mewtwo", "rating": 234}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39358}, {"moveId": "ROCK_THROW", "uses": 37142}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34331}, {"moveId": "MAGNET_BOMB", "uses": 27634}, {"moveId": "THUNDERBOLT", "uses": 14426}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 47.6}, {"speciesId": "be<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 444, "matchups": [{"opponent": "braviary", "rating": 786, "opRating": 213}, {"opponent": "heracross", "rating": 695, "opRating": 304}, {"opponent": "magmortar_shadow", "rating": 682, "opRating": 317}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 628, "opRating": 371}, {"opponent": "sneasler", "rating": 564, "opRating": 435}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "excadrill", "rating": 351}, {"opponent": "dragonite", "rating": 417}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 55713}, {"moveId": "ASTONISH", "uses": 20787}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29088}, {"moveId": "DARK_PULSE", "uses": 24807}, {"moveId": "PSYCHIC", "uses": 22754}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "DARK_PULSE"], "score": 47.4}, {"speciesId": "scyther_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 417, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 923, "opRating": 76}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 920, "opRating": 79}, {"opponent": "weavile_shadow", "rating": 898, "opRating": 101}, {"opponent": "hoopa_unbound", "rating": 828, "opRating": 171}, {"opponent": "latias", "rating": 681, "opRating": 318}], "counters": [{"opponent": "giratina_origin", "rating": 199}, {"opponent": "dialga", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "metagross", "rating": 267}, {"opponent": "mewtwo", "rating": 312}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35965}, {"moveId": "AIR_SLASH", "uses": 25089}, {"moveId": "STEEL_WING", "uses": 15375}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28327}, {"moveId": "X_SCISSOR", "uses": 20070}, {"moveId": "AERIAL_ACE", "uses": 14976}, {"moveId": "BUG_BUZZ", "uses": 13074}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 47.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 426, "matchups": [{"opponent": "terrakion", "rating": 668, "opRating": 331}, {"opponent": "heracross", "rating": 662, "opRating": 337}, {"opponent": "sneasler", "rating": 633, "opRating": 366}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 627, "opRating": 372}, {"opponent": "kommo_o", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "dragonite", "rating": 377}, {"opponent": "gyarados", "rating": 378}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43571}, {"moveId": "CHARM", "uses": 32929}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 37378}, {"moveId": "PSYCHIC", "uses": 27323}, {"moveId": "FUTURE_SIGHT", "uses": 11795}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 47.2}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 417, "matchups": [{"opponent": "genesect_douse", "rating": 835, "opRating": 164}, {"opponent": "genesect_shock", "rating": 835, "opRating": 164}, {"opponent": "genesect_chill", "rating": 835, "opRating": 164}, {"opponent": "genesect_burn", "rating": 835, "opRating": 164}, {"opponent": "genesect", "rating": 835, "opRating": 164}], "counters": [{"opponent": "lugia", "rating": 185}, {"opponent": "mewtwo", "rating": 234}, {"opponent": "dialga", "rating": 304}, {"opponent": "metagross", "rating": 308}, {"opponent": "zacian_hero", "rating": 329}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 27804}, {"moveId": "EMBER", "uses": 27771}, {"moveId": "FEINT_ATTACK", "uses": 20940}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28160}, {"moveId": "PSYSHOCK", "uses": 12261}, {"moveId": "OVERHEAT", "uses": 11273}, {"moveId": "SOLAR_BEAM", "uses": 6623}, {"moveId": "RETURN", "uses": 6570}, {"moveId": "FLAMETHROWER", "uses": 6143}, {"moveId": "FIRE_BLAST", "uses": 3307}, {"moveId": "HEAT_WAVE", "uses": 2001}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 47.2}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 384, "matchups": [{"opponent": "gengar", "rating": 800, "opRating": 199}, {"opponent": "trevenant", "rating": 741, "opRating": 258}, {"opponent": "chesnaught", "rating": 701, "opRating": 298}, {"opponent": "giratina_origin", "rating": 522}, {"opponent": "buzzwole", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "garcho<PERSON>", "rating": 230}, {"opponent": "metagross", "rating": 258}, {"opponent": "mewtwo", "rating": 325}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 45320}, {"moveId": "EXTRASENSORY", "uses": 31180}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33867}, {"moveId": "SHADOW_BALL", "uses": 23993}, {"moveId": "PSYCHIC", "uses": 14217}, {"moveId": "NIGHT_SHADE", "uses": 4426}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "SHADOW_BALL"], "score": 47.2}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 445, "matchups": [{"opponent": "venusaur_shadow", "rating": 759, "opRating": 240}, {"opponent": "roserade", "rating": 717, "opRating": 282}, {"opponent": "machamp_shadow", "rating": 621, "opRating": 378}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 608, "opRating": 391}, {"opponent": "nihilego", "rating": 569, "opRating": 430}], "counters": [{"opponent": "dialga", "rating": 247}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "gyarados", "rating": 360}, {"opponent": "dragonite", "rating": 364}, {"opponent": "lugia", "rating": 426}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42988}, {"moveId": "FEINT_ATTACK", "uses": 33512}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 20975}, {"moveId": "PAYBACK", "uses": 17233}, {"moveId": "HEAVY_SLAM", "uses": 15109}, {"moveId": "BULLDOZE", "uses": 10091}, {"moveId": "PSYCHIC", "uses": 8140}, {"moveId": "FLASH_CANNON", "uses": 4835}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 47.1}, {"speciesId": "<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 412, "matchups": [{"opponent": "golem", "rating": 906, "opRating": 93}, {"opponent": "hippo<PERSON><PERSON>", "rating": 854, "opRating": 145}, {"opponent": "heatran", "rating": 633, "opRating": 366}, {"opponent": "ma<PERSON><PERSON>", "rating": 627, "opRating": 372}, {"opponent": "excadrill", "rating": 534}], "counters": [{"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "zacian_hero", "rating": 199}, {"opponent": "dialga", "rating": 247}, {"opponent": "metagross", "rating": 284}, {"opponent": "lugia", "rating": 285}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40086}, {"moveId": "CONFUSION", "uses": 36414}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 22988}, {"moveId": "ICE_BEAM", "uses": 18263}, {"moveId": "SYNCHRONOISE", "uses": 11950}, {"moveId": "HYDRO_PUMP", "uses": 10214}, {"moveId": "BUBBLE_BEAM", "uses": 7544}, {"moveId": "PSYCHIC", "uses": 5430}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 46.8}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 412, "matchups": [{"opponent": "lugia_shadow", "rating": 630, "opRating": 369}, {"opponent": "nihilego", "rating": 619, "opRating": 380}, {"opponent": "sylveon", "rating": 602, "opRating": 397}, {"opponent": "lugia", "rating": 580}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 531}], "counters": [{"opponent": "mewtwo", "rating": 179}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "giratina_origin", "rating": 223}, {"opponent": "dialga", "rating": 228}, {"opponent": "gyarados", "rating": 335}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39621}, {"moveId": "ROCK_THROW", "uses": 36879}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30466}, {"moveId": "MAGNET_BOMB", "uses": 24090}, {"moveId": "THUNDERBOLT", "uses": 12941}, {"moveId": "RETURN", "uses": 9006}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 46.8}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 417, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 718, "opRating": 281}, {"opponent": "landorus_incarnate", "rating": 676, "opRating": 323}, {"opponent": "kyogre", "rating": 552, "opRating": 447}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 549, "opRating": 450}, {"opponent": "dragonite", "rating": 524}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "lugia", "rating": 247}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "gyarados", "rating": 275}, {"opponent": "excadrill", "rating": 306}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 7995}, {"moveId": "WATER_GUN", "uses": 6701}, {"moveId": "TACKLE", "uses": 4931}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4574}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4261}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3941}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3858}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3824}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3742}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3722}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3507}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3502}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3484}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3411}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3343}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3060}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3048}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3012}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2851}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 20419}, {"moveId": "PSYCHIC", "uses": 17237}, {"moveId": "THUNDER", "uses": 13364}, {"moveId": "HYDRO_PUMP", "uses": 12939}, {"moveId": "POWER_GEM", "uses": 8961}, {"moveId": "PSYBEAM", "uses": 3534}]}, "moveset": ["QUICK_ATTACK", "ICE_BEAM", "PSYCHIC"], "score": 46.8}, {"speciesId": "manectric_shadow", "speciesName": "Man<PERSON><PERSON> (Shadow)", "rating": 427, "matchups": [{"opponent": "braviary", "rating": 872, "opRating": 127}, {"opponent": "chandelure", "rating": 815, "opRating": 184}, {"opponent": "moltres", "rating": 796, "opRating": 203}, {"opponent": "moltres_shadow", "rating": 757, "opRating": 242}, {"opponent": "gyarados", "rating": 503}], "counters": [{"opponent": "mewtwo", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "dialga", "rating": 217}, {"opponent": "lugia", "rating": 323}, {"opponent": "metagross", "rating": 345}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37707}, {"moveId": "CHARGE_BEAM", "uses": 20569}, {"moveId": "THUNDER_FANG", "uses": 18191}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35712}, {"moveId": "PSYCHIC_FANGS", "uses": 19295}, {"moveId": "OVERHEAT", "uses": 11942}, {"moveId": "THUNDER", "uses": 5725}, {"moveId": "FLAME_BURST", "uses": 3950}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 46.6}, {"speciesId": "dewgong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 354, "matchups": [{"opponent": "gliscor_shadow", "rating": 809, "opRating": 190}, {"opponent": "salamence", "rating": 715, "opRating": 284}, {"opponent": "hippow<PERSON>_shadow", "rating": 693, "opRating": 306}, {"opponent": "landorus_incarnate", "rating": 599, "opRating": 400}, {"opponent": "garcho<PERSON>", "rating": 537}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "lugia", "rating": 311}, {"opponent": "gyarados", "rating": 355}, {"opponent": "giratina_origin", "rating": 428}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38883}, {"moveId": "FROST_BREATH", "uses": 29551}, {"moveId": "IRON_TAIL", "uses": 8062}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 36268}, {"moveId": "BLIZZARD", "uses": 16154}, {"moveId": "AQUA_JET", "uses": 12693}, {"moveId": "AURORA_BEAM", "uses": 6482}, {"moveId": "WATER_PULSE", "uses": 4729}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "BLIZZARD"], "score": 46.4}, {"speciesId": "musharna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 395, "matchups": [{"opponent": "sneasler", "rating": 679, "opRating": 320}, {"opponent": "gallade_shadow", "rating": 673, "opRating": 326}, {"opponent": "machamp_shadow", "rating": 664, "opRating": 335}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 606, "opRating": 393}, {"opponent": "staraptor_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "gyarados", "rating": 347}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 51839}, {"moveId": "ZEN_HEADBUTT", "uses": 24661}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 42382}, {"moveId": "DAZZLING_GLEAM", "uses": 19894}, {"moveId": "FUTURE_SIGHT", "uses": 14238}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 46.4}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 396, "matchups": [{"opponent": "trevenant", "rating": 823, "opRating": 176}, {"opponent": "gengar", "rating": 781, "opRating": 218}, {"opponent": "zarude", "rating": 627, "opRating": 372}, {"opponent": "bewear", "rating": 625, "opRating": 375}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 229}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "gyarados", "rating": 422}, {"opponent": "giratina_origin", "rating": 498}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 25024}, {"moveId": "WING_ATTACK", "uses": 22145}, {"moveId": "AIR_SLASH", "uses": 17772}, {"moveId": "STEEL_WING", "uses": 11406}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 42084}, {"moveId": "AERIAL_ACE", "uses": 16972}, {"moveId": "HURRICANE", "uses": 7650}, {"moveId": "FEATHER_DANCE", "uses": 5345}, {"moveId": "AIR_CUTTER", "uses": 4590}]}, "moveset": ["GUST", "BRAVE_BIRD", "AERIAL_ACE"], "score": 46.4}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 414, "matchups": [{"opponent": "magnezone", "rating": 770, "opRating": 229}, {"opponent": "magnezone_shadow", "rating": 729, "opRating": 270}, {"opponent": "nihilego", "rating": 723, "opRating": 276}, {"opponent": "ho_oh", "rating": 607, "opRating": 392}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 508, "opRating": 491}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "metagross", "rating": 232}, {"opponent": "dialga", "rating": 277}, {"opponent": "lugia", "rating": 307}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38282}, {"moveId": "ROCK_THROW", "uses": 38218}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 22444}, {"moveId": "STONE_EDGE", "uses": 20775}, {"moveId": "ROCK_BLAST", "uses": 18768}, {"moveId": "ANCIENT_POWER", "uses": 14434}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "EARTHQUAKE", "STONE_EDGE"], "score": 46.1}, {"speciesId": "noivern", "speciesName": "Noivern", "rating": 434, "matchups": [{"opponent": "grou<PERSON>", "rating": 713}, {"opponent": "swampert", "rating": 679}, {"opponent": "buzzwole", "rating": 643, "opRating": 356}, {"opponent": "giratina_origin", "rating": 519}, {"opponent": "ho_oh", "rating": 505}], "counters": [{"opponent": "dialga", "rating": 81}, {"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "gyarados", "rating": 144}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "lugia", "rating": 285}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 46360}, {"moveId": "BITE", "uses": 30140}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 24763}, {"moveId": "DRACO_METEOR", "uses": 22065}, {"moveId": "PSYCHIC", "uses": 20102}, {"moveId": "HEAT_WAVE", "uses": 9564}]}, "moveset": ["AIR_SLASH", "HURRICANE", "DRACO_METEOR"], "score": 46.1}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 400, "matchups": [{"opponent": "feraligatr_shadow", "rating": 871, "opRating": 128}, {"opponent": "rhyperior", "rating": 847, "opRating": 152}, {"opponent": "swampert", "rating": 765}, {"opponent": "swampert_shadow", "rating": 728, "opRating": 271}, {"opponent": "kyogre", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "gyarados", "rating": 317}, {"opponent": "excadrill", "rating": 390}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51536}, {"moveId": "BITE", "uses": 24964}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38002}, {"moveId": "GRASS_KNOT", "uses": 31694}, {"moveId": "SOLAR_BEAM", "uses": 6741}]}, "moveset": ["VINE_WHIP", "CRUNCH", "GRASS_KNOT"], "score": 46.1}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 406, "matchups": [{"opponent": "virizion", "rating": 834, "opRating": 165}, {"opponent": "heracross", "rating": 706, "opRating": 293}, {"opponent": "buzzwole", "rating": 702, "opRating": 297}, {"opponent": "kommo_o", "rating": 634, "opRating": 365}, {"opponent": "swampert", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 81}, {"opponent": "garcho<PERSON>", "rating": 431}, {"opponent": "dragonite", "rating": 438}, {"opponent": "zacian_hero", "rating": 450}, {"opponent": "grou<PERSON>", "rating": 451}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 63993}, {"moveId": "ZEN_HEADBUTT", "uses": 12507}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 38298}, {"moveId": "PSYCHIC", "uses": 17146}, {"moveId": "OMINOUS_WIND", "uses": 11679}, {"moveId": "DAZZLING_GLEAM", "uses": 9351}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "PSYCHIC"], "score": 46}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 417, "matchups": [{"opponent": "gengar", "rating": 768, "opRating": 231}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 706}, {"opponent": "articuno_galarian", "rating": 706, "opRating": 293}, {"opponent": "swampert", "rating": 613}, {"opponent": "gyarados", "rating": 562}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "metagross", "rating": 313}, {"opponent": "giratina_origin", "rating": 326}, {"opponent": "lugia", "rating": 350}, {"opponent": "ho_oh", "rating": 411}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 32333}, {"moveId": "QUICK_ATTACK", "uses": 26804}, {"moveId": "MUD_SLAP", "uses": 17351}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27681}, {"moveId": "GRASS_KNOT", "uses": 24980}, {"moveId": "BULLDOZE", "uses": 16539}, {"moveId": "PARABOLIC_CHARGE", "uses": 7268}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "GRASS_KNOT"], "score": 46}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 425, "matchups": [{"opponent": "metagross_shadow", "rating": 835, "opRating": 164}, {"opponent": "genesect_douse", "rating": 791, "opRating": 208}, {"opponent": "genesect_shock", "rating": 791, "opRating": 208}, {"opponent": "genesect_chill", "rating": 791, "opRating": 208}, {"opponent": "genesect_burn", "rating": 791, "opRating": 208}], "counters": [{"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "lugia", "rating": 171}, {"opponent": "zacian_hero", "rating": 176}, {"opponent": "metagross", "rating": 322}, {"opponent": "dialga", "rating": 377}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38562}, {"moveId": "EMBER", "uses": 37938}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 48587}, {"moveId": "FLAMETHROWER", "uses": 18123}, {"moveId": "FIRE_BLAST", "uses": 9888}, {"moveId": "FRUSTRATION", "uses": 7}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "FLAMETHROWER"], "score": 46}, {"speciesId": "deoxys_speed", "speciesName": "<PERSON><PERSON><PERSON> (Speed)", "rating": 409, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 921, "opRating": 78}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 657, "opRating": 342}, {"opponent": "heracross", "rating": 645, "opRating": 354}, {"opponent": "sneasler", "rating": 622, "opRating": 377}, {"opponent": "gyarados", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 164}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "metagross", "rating": 229}, {"opponent": "zacian_hero", "rating": 260}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 58891}, {"moveId": "ZEN_HEADBUTT", "uses": 17609}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 43891}, {"moveId": "THUNDERBOLT", "uses": 23705}, {"moveId": "SWIFT", "uses": 8915}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 45.8}, {"speciesId": "lycanroc_midday", "speciesName": "Lycanroc (Midday)", "rating": 422, "matchups": [{"opponent": "chandelure", "rating": 871, "opRating": 128}, {"opponent": "darmanitan_standard", "rating": 871, "opRating": 128}, {"opponent": "victini", "rating": 701, "opRating": 298}, {"opponent": "articuno_galarian", "rating": 689, "opRating": 310}, {"opponent": "ho_oh", "rating": 573, "opRating": 426}], "counters": [{"opponent": "giratina_origin", "rating": 225}, {"opponent": "dialga", "rating": 334}, {"opponent": "lugia", "rating": 350}, {"opponent": "metagross", "rating": 369}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 474}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40110}, {"moveId": "ROCK_THROW", "uses": 36390}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27490}, {"moveId": "STONE_EDGE", "uses": 26232}, {"moveId": "CRUNCH", "uses": 22739}]}, "moveset": ["SUCKER_PUNCH", "DRILL_RUN", "STONE_EDGE"], "score": 45.7}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 404, "matchups": [{"opponent": "golem", "rating": 969, "opRating": 30}, {"opponent": "swampert_shadow", "rating": 954, "opRating": 45}, {"opponent": "swampert", "rating": 810}, {"opponent": "tapu_fini", "rating": 765, "opRating": 234}, {"opponent": "kyogre", "rating": 621, "opRating": 378}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "garcho<PERSON>", "rating": 194}, {"opponent": "mewtwo", "rating": 213}, {"opponent": "gyarados", "rating": 399}, {"opponent": "excadrill", "rating": 474}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 39813}, {"moveId": "RAZOR_LEAF", "uses": 22011}, {"moveId": "ACID", "uses": 14675}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 57454}, {"moveId": "DAZZLING_GLEAM", "uses": 13129}, {"moveId": "PETAL_BLIZZARD", "uses": 5953}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 45.3}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 429, "matchups": [{"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 617}, {"opponent": "ho_oh", "rating": 553, "opRating": 446}, {"opponent": "yveltal", "rating": 543, "opRating": 456}, {"opponent": "lugia", "rating": 535}], "counters": [{"opponent": "garcho<PERSON>", "rating": 105}, {"opponent": "mewtwo", "rating": 117}, {"opponent": "dialga", "rating": 146}, {"opponent": "gyarados", "rating": 247}, {"opponent": "dragonite", "rating": 292}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 67970}, {"moveId": "ZEN_HEADBUTT", "uses": 8530}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 38189}, {"moveId": "OUTRAGE", "uses": 19215}, {"moveId": "FLAMETHROWER", "uses": 19164}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "OUTRAGE"], "score": 45.3}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 388, "matchups": [{"opponent": "gourgeist_super", "rating": 843, "opRating": 156}, {"opponent": "hitmon<PERSON>_shadow", "rating": 825, "opRating": 174}, {"opponent": "gengar", "rating": 718, "opRating": 281}, {"opponent": "trevenant", "rating": 703, "opRating": 296}, {"opponent": "chesnaught", "rating": 625, "opRating": 375}], "counters": [{"opponent": "dialga", "rating": 119}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "zacian_hero", "rating": 208}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "giratina_origin", "rating": 436}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 37595}, {"moveId": "PECK", "uses": 24408}, {"moveId": "ROCK_SMASH", "uses": 14485}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 44797}, {"moveId": "ROCK_BLAST", "uses": 22144}, {"moveId": "FLASH_CANNON", "uses": 9598}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 45.3}, {"speciesId": "hunt<PERSON>", "speciesName": "Huntail", "rating": 385, "matchups": [{"opponent": "golem", "rating": 933, "opRating": 66}, {"opponent": "chandelure", "rating": 877, "opRating": 122}, {"opponent": "darmanitan_standard", "rating": 877, "opRating": 122}, {"opponent": "typhlosion", "rating": 866, "opRating": 133}, {"opponent": "excadrill", "rating": 529}], "counters": [{"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "dialga", "rating": 209}, {"opponent": "lugia", "rating": 228}, {"opponent": "metagross", "rating": 360}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 49025}, {"moveId": "BITE", "uses": 27475}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 33341}, {"moveId": "CRUNCH", "uses": 24013}, {"moveId": "ICE_BEAM", "uses": 19106}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "CRUNCH"], "score": 45.2}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 416, "matchups": [{"opponent": "genesect_douse", "rating": 816, "opRating": 183}, {"opponent": "genesect_shock", "rating": 816, "opRating": 183}, {"opponent": "genesect_chill", "rating": 816, "opRating": 183}, {"opponent": "genesect_burn", "rating": 816, "opRating": 183}, {"opponent": "genesect", "rating": 816, "opRating": 183}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "lugia", "rating": 226}, {"opponent": "dialga", "rating": 228}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "metagross", "rating": 375}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 28287}, {"moveId": "EMBER", "uses": 27270}, {"moveId": "FEINT_ATTACK", "uses": 20932}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 30695}, {"moveId": "PSYSHOCK", "uses": 13523}, {"moveId": "OVERHEAT", "uses": 12346}, {"moveId": "SOLAR_BEAM", "uses": 7438}, {"moveId": "FLAMETHROWER", "uses": 6637}, {"moveId": "FIRE_BLAST", "uses": 3553}, {"moveId": "HEAT_WAVE", "uses": 2151}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 45.2}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 414, "matchups": [{"opponent": "tapu_fini", "rating": 767, "opRating": 232}, {"opponent": "swampert_shadow", "rating": 694, "opRating": 305}, {"opponent": "xurkitree", "rating": 694, "opRating": 305}, {"opponent": "kyogre", "rating": 613, "opRating": 386}, {"opponent": "excadrill", "rating": 526}], "counters": [{"opponent": "dialga", "rating": 73}, {"opponent": "garcho<PERSON>", "rating": 147}, {"opponent": "gyarados", "rating": 365}, {"opponent": "zacian_hero", "rating": 453}, {"opponent": "swampert", "rating": 477}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43969}, {"moveId": "ACID", "uses": 32531}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 39421}, {"moveId": "SLUDGE_BOMB", "uses": 15219}, {"moveId": "LEAF_TORNADO", "uses": 7597}, {"moveId": "RETURN", "uses": 7456}, {"moveId": "ACID_SPRAY", "uses": 3461}, {"moveId": "SOLAR_BEAM", "uses": 3313}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 45.2}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 403, "matchups": [{"opponent": "tornadus_incarnate", "rating": 885, "opRating": 114}, {"opponent": "blastoise_shadow", "rating": 834, "opRating": 165}, {"opponent": "moltres_shadow", "rating": 796, "opRating": 203}, {"opponent": "feraligatr_shadow", "rating": 792, "opRating": 207}, {"opponent": "gyarados", "rating": 585}], "counters": [{"opponent": "mewtwo", "rating": 104}, {"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "dialga", "rating": 190}, {"opponent": "metagross", "rating": 302}, {"opponent": "lugia", "rating": 490}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36710}, {"moveId": "CHARGE_BEAM", "uses": 20711}, {"moveId": "THUNDER_FANG", "uses": 19059}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 33072}, {"moveId": "PSYCHIC_FANGS", "uses": 17241}, {"moveId": "OVERHEAT", "uses": 10795}, {"moveId": "RETURN", "uses": 6616}, {"moveId": "THUNDER", "uses": 5165}, {"moveId": "FLAME_BURST", "uses": 3645}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 44.4}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 371, "matchups": [{"opponent": "hoopa", "rating": 917, "opRating": 82}, {"opponent": "espeon", "rating": 826, "opRating": 173}, {"opponent": "gallade", "rating": 669, "opRating": 330}, {"opponent": "gallade_shadow", "rating": 661, "opRating": 338}, {"opponent": "mewtwo_armored", "rating": 606, "opRating": 393}], "counters": [{"opponent": "metagross", "rating": 241}, {"opponent": "dialga", "rating": 260}, {"opponent": "lugia", "rating": 283}, {"opponent": "excadrill", "rating": 348}, {"opponent": "mewtwo", "rating": 492}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40452}, {"moveId": "FEINT_ATTACK", "uses": 36048}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 42016}, {"moveId": "SHADOW_SNEAK", "uses": 23728}, {"moveId": "OMINOUS_WIND", "uses": 10765}]}, "moveset": ["SUCKER_PUNCH", "SHADOW_BALL", "SHADOW_SNEAK"], "score": 44.4}, {"speciesId": "sawk", "speciesName": "Sawk", "rating": 431, "matchups": [{"opponent": "tapu_bulu", "rating": 871, "opRating": 128}, {"opponent": "regigigas", "rating": 765, "opRating": 234}, {"opponent": "regice", "rating": 701, "opRating": 298}, {"opponent": "snorlax", "rating": 621, "opRating": 378}, {"opponent": "regirock", "rating": 588, "opRating": 411}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "garcho<PERSON>", "rating": 166}, {"opponent": "dragonite", "rating": 234}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "gyarados", "rating": 432}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57673}, {"moveId": "LOW_KICK", "uses": 18827}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41155}, {"moveId": "FOCUS_BLAST", "uses": 18618}, {"moveId": "LOW_SWEEP", "uses": 16739}]}, "moveset": ["POISON_JAB", "BODY_SLAM", "FOCUS_BLAST"], "score": 44.2}, {"speciesId": "pelipper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 383, "matchups": [{"opponent": "buzzwole", "rating": 591, "opRating": 408}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 584, "opRating": 415}, {"opponent": "heracross", "rating": 566, "opRating": 433}, {"opponent": "sneasler", "rating": 538, "opRating": 461}, {"opponent": "grou<PERSON>", "rating": 535}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "mewtwo", "rating": 182}, {"opponent": "metagross", "rating": 279}, {"opponent": "gyarados", "rating": 314}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 38955}, {"moveId": "WATER_GUN", "uses": 37545}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 40549}, {"moveId": "HURRICANE", "uses": 16154}, {"moveId": "BLIZZARD", "uses": 14580}, {"moveId": "HYDRO_PUMP", "uses": 5376}]}, "moveset": ["WING_ATTACK", "WEATHER_BALL_WATER", "HURRICANE"], "score": 44.1}, {"speciesId": "simipour", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 384, "matchups": [{"opponent": "typhlosion", "rating": 890, "opRating": 109}, {"opponent": "chandelure", "rating": 862, "opRating": 137}, {"opponent": "moltres_shadow", "rating": 847, "opRating": 152}, {"opponent": "entei_shadow", "rating": 835, "opRating": 164}, {"opponent": "excadrill", "rating": 545}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "lugia", "rating": 219}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "metagross", "rating": 311}, {"opponent": "grou<PERSON>", "rating": 364}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 48273}, {"moveId": "BITE", "uses": 28227}], "chargedMoves": [{"moveId": "SURF", "uses": 39496}, {"moveId": "CRUNCH", "uses": 30665}, {"moveId": "HYDRO_PUMP", "uses": 6333}]}, "moveset": ["WATER_GUN", "SURF", "CRUNCH"], "score": 44.1}, {"speciesId": "seaking", "speciesName": "Seaking", "rating": 369, "matchups": [{"opponent": "exeggutor_alolan_shadow", "rating": 723, "opRating": 276}, {"opponent": "entei", "rating": 656, "opRating": 343}, {"opponent": "heatran", "rating": 625, "opRating": 375}, {"opponent": "empoleon", "rating": 604, "opRating": 395}, {"opponent": "tapu_fini", "rating": 514, "opRating": 485}], "counters": [{"opponent": "mewtwo", "rating": 223}, {"opponent": "dialga", "rating": 239}, {"opponent": "metagross", "rating": 244}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "dragonite", "rating": 345}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 33578}, {"moveId": "WATERFALL", "uses": 29356}, {"moveId": "PECK", "uses": 13570}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 23991}, {"moveId": "ICY_WIND", "uses": 23335}, {"moveId": "MEGAHORN", "uses": 14843}, {"moveId": "ICE_BEAM", "uses": 7525}, {"moveId": "WATER_PULSE", "uses": 6846}]}, "moveset": ["POISON_JAB", "DRILL_RUN", "ICY_WIND"], "score": 43.9}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 411, "matchups": [{"opponent": "gengar", "rating": 768, "opRating": 231}, {"opponent": "slowking_galarian", "rating": 719, "opRating": 280}, {"opponent": "golem", "rating": 615, "opRating": 384}, {"opponent": "golem_alolan", "rating": 557, "opRating": 442}, {"opponent": "chandelure", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "gyarados", "rating": 273}, {"opponent": "giratina_origin", "rating": 276}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 63712}, {"moveId": "ZEN_HEADBUTT", "uses": 12788}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 32821}, {"moveId": "EARTHQUAKE", "uses": 25021}, {"moveId": "IRON_HEAD", "uses": 18713}]}, "moveset": ["TACKLE", "HORN_ATTACK", "EARTHQUAKE"], "score": 43.9}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 361, "matchups": [{"opponent": "hoopa", "rating": 674, "opRating": 325}, {"opponent": "golem", "rating": 667, "opRating": 332}, {"opponent": "swampert", "rating": 601}, {"opponent": "tapu_fini", "rating": 588, "opRating": 411}, {"opponent": "swampert_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "giratina_origin", "rating": 278}, {"opponent": "metagross", "rating": 279}, {"opponent": "gyarados", "rating": 298}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52376}, {"moveId": "ASTONISH", "uses": 24124}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 24231}, {"moveId": "GRASS_KNOT", "uses": 23828}, {"moveId": "SLUDGE_BOMB", "uses": 19288}, {"moveId": "RETURN", "uses": 9139}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 43.7}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 384, "matchups": [{"opponent": "feraligatr_shadow", "rating": 751, "opRating": 248}, {"opponent": "swampert", "rating": 741}, {"opponent": "golem_alolan", "rating": 713, "opRating": 286}, {"opponent": "swampert_shadow", "rating": 701, "opRating": 298}, {"opponent": "terrakion", "rating": 537, "opRating": 462}], "counters": [{"opponent": "giratina_origin", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "gyarados", "rating": 373}, {"opponent": "excadrill", "rating": 395}, {"opponent": "grou<PERSON>", "rating": 421}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6055}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5711}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5257}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5033}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4958}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4782}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4751}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4595}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4561}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4473}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4444}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4418}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4110}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4104}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3915}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3779}, {"moveId": "ZEN_HEADBUTT", "uses": 1433}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41150}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12212}, {"moveId": "SOLAR_BEAM", "uses": 8441}]}, "moveset": ["ZEN_HEADBUTT", "GRASS_KNOT", "SEED_FLARE"], "score": 43.7}, {"speciesId": "sudowoodo", "speciesName": "Sudowoodo", "rating": 396, "matchups": [{"opponent": "moltres_shadow", "rating": 872, "opRating": 127}, {"opponent": "entei", "rating": 675, "opRating": 324}, {"opponent": "moltres", "rating": 659, "opRating": 340}, {"opponent": "ho_oh", "rating": 570, "opRating": 429}, {"opponent": "nihilego", "rating": 566, "opRating": 433}], "counters": [{"opponent": "lugia", "rating": 188}, {"opponent": "dialga", "rating": 233}, {"opponent": "dragonite", "rating": 268}, {"opponent": "gyarados", "rating": 288}, {"opponent": "mewtwo", "rating": 294}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44394}, {"moveId": "ROCK_THROW", "uses": 32106}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30321}, {"moveId": "EARTHQUAKE", "uses": 18327}, {"moveId": "STONE_EDGE", "uses": 12099}, {"moveId": "RETURN", "uses": 9237}, {"moveId": "ROCK_TOMB", "uses": 6528}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 43.7}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 364, "matchups": [{"opponent": "chesnaught", "rating": 821, "opRating": 178}, {"opponent": "swampert", "rating": 723}, {"opponent": "virizion", "rating": 723, "opRating": 276}, {"opponent": "swampert_shadow", "rating": 673, "opRating": 326}, {"opponent": "heracross", "rating": 665, "opRating": 334}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "gyarados", "rating": 355}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 41714}, {"moveId": "RAZOR_LEAF", "uses": 34786}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 45812}, {"moveId": "AERIAL_ACE", "uses": 16484}, {"moveId": "STOMP", "uses": 14191}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 43.7}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 400, "matchups": [{"opponent": "golem", "rating": 898, "opRating": 101}, {"opponent": "swampert", "rating": 694}, {"opponent": "tapu_fini", "rating": 691, "opRating": 308}, {"opponent": "swampert_shadow", "rating": 668, "opRating": 331}, {"opponent": "kyogre", "rating": 543, "opRating": 456}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "excadrill", "rating": 279}, {"opponent": "zacian_hero", "rating": 453}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43665}, {"moveId": "ACID", "uses": 32835}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 43209}, {"moveId": "SLUDGE_BOMB", "uses": 17451}, {"moveId": "LEAF_TORNADO", "uses": 8223}, {"moveId": "ACID_SPRAY", "uses": 3896}, {"moveId": "SOLAR_BEAM", "uses": 3617}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 43.7}, {"speciesId": "grumpig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 378, "matchups": [{"opponent": "machamp", "rating": 694, "opRating": 305}, {"opponent": "heracross", "rating": 680, "opRating": 319}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 671, "opRating": 328}, {"opponent": "machamp_shadow", "rating": 633, "opRating": 366}, {"opponent": "sneasler", "rating": 578, "opRating": 421}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "metagross", "rating": 267}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "gyarados", "rating": 275}, {"opponent": "zacian_hero", "rating": 289}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 44593}, {"moveId": "CHARGE_BEAM", "uses": 31907}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37600}, {"moveId": "PSYCHIC", "uses": 31990}, {"moveId": "MIRROR_COAT", "uses": 6790}]}, "moveset": ["EXTRASENSORY", "SHADOW_BALL", "PSYCHIC"], "score": 43.6}, {"speciesId": "vanilluxe", "speciesName": "Vanilluxe", "rating": 368, "matchups": [{"opponent": "salamence", "rating": 667, "opRating": 332}, {"opponent": "trevenant", "rating": 661, "opRating": 338}, {"opponent": "hippo<PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "landorus_incarnate", "rating": 528, "opRating": 471}, {"opponent": "zarude", "rating": 522, "opRating": 477}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "lugia", "rating": 300}, {"opponent": "giratina_origin", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "FROST_BREATH", "uses": 54187}, {"moveId": "ASTONISH", "uses": 22313}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 38175}, {"moveId": "SIGNAL_BEAM", "uses": 23025}, {"moveId": "FLASH_CANNON", "uses": 15322}]}, "moveset": ["FROST_BREATH", "BLIZZARD", "SIGNAL_BEAM"], "score": 43.6}, {"speciesId": "alomomola", "speciesName": "Alomomola", "rating": 389, "matchups": [{"opponent": "heatran", "rating": 771, "opRating": 228}, {"opponent": "entei", "rating": 771, "opRating": 228}, {"opponent": "entei_shadow", "rating": 722, "opRating": 277}, {"opponent": "hippo<PERSON><PERSON>", "rating": 697, "opRating": 302}, {"opponent": "ma<PERSON><PERSON>", "rating": 559, "opRating": 440}], "counters": [{"opponent": "mewtwo", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 147}, {"opponent": "gyarados", "rating": 309}, {"opponent": "excadrill", "rating": 334}, {"opponent": "metagross", "rating": 377}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 7669}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5602}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5198}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4811}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4655}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4595}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4572}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4299}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4191}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4175}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4173}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4129}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3708}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3691}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3679}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3643}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3446}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 27047}, {"moveId": "HYDRO_PUMP", "uses": 24801}, {"moveId": "PSYCHIC", "uses": 24624}]}, "moveset": ["WATERFALL", "BLIZZARD", "HYDRO_PUMP"], "score": 42.8}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 369, "matchups": [{"opponent": "rhydon", "rating": 861, "opRating": 138}, {"opponent": "rhyperior", "rating": 846, "opRating": 153}, {"opponent": "swampert", "rating": 766}, {"opponent": "swampert_shadow", "rating": 730, "opRating": 269}, {"opponent": "kyogre", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "metagross", "rating": 238}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "excadrill", "rating": 369}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 50632}, {"moveId": "BITE", "uses": 25868}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36265}, {"moveId": "POWER_WHIP", "uses": 29604}, {"moveId": "ENERGY_BALL", "uses": 10665}]}, "moveset": ["VINE_WHIP", "CRUNCH", "POWER_WHIP"], "score": 42.8}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 411, "matchups": [{"opponent": "roserade", "rating": 701, "opRating": 298}, {"opponent": "genesect_chill", "rating": 651, "opRating": 348}, {"opponent": "genesect_burn", "rating": 651, "opRating": 348}, {"opponent": "heatran", "rating": 637, "opRating": 362}, {"opponent": "mamos<PERSON>_shadow", "rating": 620, "opRating": 379}], "counters": [{"opponent": "mewtwo", "rating": 72}, {"opponent": "lugia", "rating": 159}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "dialga", "rating": 320}, {"opponent": "metagross", "rating": 433}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31181}, {"moveId": "FIRE_SPIN", "uses": 20336}, {"moveId": "EMBER", "uses": 20100}, {"moveId": "LOW_KICK", "uses": 4930}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32925}, {"moveId": "FLAME_CHARGE", "uses": 27481}, {"moveId": "FIRE_BLAST", "uses": 12373}, {"moveId": "HEAT_WAVE", "uses": 3688}]}, "moveset": ["INCINERATE", "DRILL_RUN", "FLAME_CHARGE"], "score": 42.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 412, "matchups": [{"opponent": "weavile_shadow", "rating": 939, "opRating": 60}, {"opponent": "weavile", "rating": 932, "opRating": 67}, {"opponent": "tapu_koko", "rating": 758, "opRating": 241}, {"opponent": "incineroar", "rating": 721, "opRating": 278}, {"opponent": "salamence", "rating": 647, "opRating": 352}], "counters": [{"opponent": "mewtwo", "rating": 91}, {"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "zacian_hero", "rating": 202}, {"opponent": "dialga", "rating": 230}, {"opponent": "excadrill", "rating": 262}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 58948}, {"moveId": "LOW_KICK", "uses": 17552}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 28737}, {"moveId": "STONE_EDGE", "uses": 26260}, {"moveId": "GRASS_KNOT", "uses": 21420}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 42.5}, {"speciesId": "electabuzz_shadow", "speciesName": "Electabuzz (Shadow)", "rating": 386, "matchups": [{"opponent": "honchk<PERSON>_shadow", "rating": 909, "opRating": 90}, {"opponent": "braviary", "rating": 865, "opRating": 134}, {"opponent": "gyarado<PERSON>_shadow", "rating": 778, "opRating": 221}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 577}, {"opponent": "gyarados", "rating": 533}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "dialga", "rating": 179}, {"opponent": "dragonite", "rating": 186}, {"opponent": "metagross", "rating": 293}, {"opponent": "lugia", "rating": 419}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58059}, {"moveId": "LOW_KICK", "uses": 18441}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 44951}, {"moveId": "THUNDERBOLT", "uses": 16799}, {"moveId": "THUNDER", "uses": 14524}, {"moveId": "FRUSTRATION", "uses": 109}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 42.3}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 357, "matchups": [{"opponent": "moltres_galarian", "rating": 644, "opRating": 355}, {"opponent": "charizard_shadow", "rating": 612, "opRating": 387}, {"opponent": "gyarados", "rating": 573}, {"opponent": "gyarado<PERSON>_shadow", "rating": 566, "opRating": 433}, {"opponent": "braviary", "rating": 528, "opRating": 471}], "counters": [{"opponent": "dialga", "rating": 157}, {"opponent": "zacian_hero", "rating": 202}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "metagross", "rating": 258}, {"opponent": "lugia", "rating": 352}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33161}, {"moveId": "SPARK", "uses": 24412}, {"moveId": "TACKLE", "uses": 18946}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 26939}, {"moveId": "DISCHARGE", "uses": 23891}, {"moveId": "RETURN", "uses": 11021}, {"moveId": "THUNDERBOLT", "uses": 10161}, {"moveId": "HYPER_BEAM", "uses": 4339}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 42.1}, {"speciesId": "electrode_shadow", "speciesName": "Electrode (Shadow)", "rating": 370, "matchups": [{"opponent": "talonflame", "rating": 894, "opRating": 105}, {"opponent": "tornadus_incarnate", "rating": 859, "opRating": 140}, {"opponent": "hoopa", "rating": 767, "opRating": 232}, {"opponent": "charizard", "rating": 612, "opRating": 387}, {"opponent": "gyarados", "rating": 566}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "giratina_origin", "rating": 205}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "lugia", "rating": 266}, {"opponent": "metagross", "rating": 302}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33466}, {"moveId": "SPARK", "uses": 24451}, {"moveId": "TACKLE", "uses": 18580}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 29663}, {"moveId": "DISCHARGE", "uses": 25931}, {"moveId": "THUNDERBOLT", "uses": 11084}, {"moveId": "HYPER_BEAM", "uses": 9730}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 42.1}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 386, "matchups": [{"opponent": "swampert_shadow", "rating": 737, "opRating": 262}, {"opponent": "rhyperior", "rating": 722, "opRating": 277}, {"opponent": "kyogre", "rating": 603, "opRating": 396}, {"opponent": "swampert", "rating": 576}, {"opponent": "x<PERSON><PERSON>", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 73}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "gyarados", "rating": 301}, {"opponent": "excadrill", "rating": 306}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43366}, {"moveId": "ACID", "uses": 33134}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 21931}, {"moveId": "SLUDGE_BOMB", "uses": 21802}, {"moveId": "PETAL_BLIZZARD", "uses": 15470}, {"moveId": "RETURN", "uses": 10993}, {"moveId": "SOLAR_BEAM", "uses": 6383}]}, "moveset": ["RAZOR_LEAF", "MOONBLAST", "SLUDGE_BOMB"], "score": 42.1}, {"speciesId": "comfey", "speciesName": "Comfey", "rating": 341, "matchups": [{"opponent": "rhydon", "rating": 825, "opRating": 174}, {"opponent": "swampert", "rating": 670}, {"opponent": "swampert_shadow", "rating": 635, "opRating": 364}, {"opponent": "kommo_o", "rating": 565, "opRating": 434}, {"opponent": "haxorus", "rating": 538, "opRating": 461}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "dragonite", "rating": 457}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 8310}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5378}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4939}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4723}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4585}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4511}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4452}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4387}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4237}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4181}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4178}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4075}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3792}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3731}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3703}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3689}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3489}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 38046}, {"moveId": "DRAINING_KISS", "uses": 28553}, {"moveId": "PETAL_BLIZZARD", "uses": 9814}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "DRAINING_KISS"], "score": 42}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 373, "matchups": [{"opponent": "gengar", "rating": 764, "opRating": 235}, {"opponent": "tangrowth_shadow", "rating": 633, "opRating": 366}, {"opponent": "mew", "rating": 595, "opRating": 404}, {"opponent": "gyarados", "rating": 558}, {"opponent": "zarude", "rating": 558, "opRating": 441}], "counters": [{"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "dialga", "rating": 192}, {"opponent": "excadrill", "rating": 295}, {"opponent": "giratina_origin", "rating": 470}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 59960}, {"moveId": "TAKE_DOWN", "uses": 16540}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35229}, {"moveId": "MEGAHORN", "uses": 19472}, {"moveId": "SOLAR_BEAM", "uses": 11483}, {"moveId": "HYPER_BEAM", "uses": 10238}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "MEGAHORN"], "score": 42}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 378, "matchups": [{"opponent": "golem", "rating": 914, "opRating": 85}, {"opponent": "swampert", "rating": 737}, {"opponent": "rhyperior", "rating": 725, "opRating": 274}, {"opponent": "swampert_shadow", "rating": 698, "opRating": 301}, {"opponent": "kyogre", "rating": 548, "opRating": 451}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "excadrill", "rating": 383}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42782}, {"moveId": "ACID", "uses": 33718}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25849}, {"moveId": "MOONBLAST", "uses": 25581}, {"moveId": "PETAL_BLIZZARD", "uses": 17834}, {"moveId": "SOLAR_BEAM", "uses": 7063}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "MOONBLAST"], "score": 42}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 341, "matchups": [{"opponent": "exeggutor_alolan", "rating": 714, "opRating": 285}, {"opponent": "weavile_shadow", "rating": 691, "opRating": 308}, {"opponent": "weavile", "rating": 665, "opRating": 334}, {"opponent": "darkrai", "rating": 581, "opRating": 418}, {"opponent": "zarude", "rating": 568, "opRating": 431}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "garcho<PERSON>", "rating": 251}, {"opponent": "metagross", "rating": 313}, {"opponent": "mewtwo", "rating": 398}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 39333}, {"moveId": "INFESTATION", "uses": 37167}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 39592}, {"moveId": "BUBBLE_BEAM", "uses": 22403}, {"moveId": "MIRROR_COAT", "uses": 14574}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 41.7}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 385, "matchups": [{"opponent": "blastoise_shadow", "rating": 906, "opRating": 93}, {"opponent": "rhyperior", "rating": 865, "opRating": 134}, {"opponent": "swampert", "rating": 798}, {"opponent": "swampert_shadow", "rating": 738, "opRating": 261}, {"opponent": "kyogre", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "gyarados", "rating": 293}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "excadrill", "rating": 365}, {"opponent": "grou<PERSON>", "rating": 396}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44208}, {"moveId": "INFESTATION", "uses": 32292}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 26986}, {"moveId": "SLUDGE_BOMB", "uses": 17775}, {"moveId": "POWER_WHIP", "uses": 13485}, {"moveId": "RETURN", "uses": 12450}, {"moveId": "SOLAR_BEAM", "uses": 5685}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 41.7}, {"speciesId": "jynx", "speciesName": "Jynx", "rating": 377, "matchups": [{"opponent": "salamence", "rating": 697, "opRating": 302}, {"opponent": "virizion", "rating": 661, "opRating": 338}, {"opponent": "ma<PERSON><PERSON>", "rating": 617, "opRating": 382}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 553}, {"opponent": "zapdos", "rating": 523, "opRating": 476}], "counters": [{"opponent": "garcho<PERSON>", "rating": 105}, {"opponent": "lugia", "rating": 269}, {"opponent": "dialga", "rating": 298}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "swampert", "rating": 378}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 41903}, {"moveId": "FROST_BREATH", "uses": 32894}, {"moveId": "POUND", "uses": 1618}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30938}, {"moveId": "ICE_PUNCH", "uses": 18637}, {"moveId": "PSYSHOCK", "uses": 13821}, {"moveId": "FOCUS_BLAST", "uses": 8970}, {"moveId": "DRAINING_KISS", "uses": 4110}]}, "moveset": ["CONFUSION", "AVALANCHE", "ICE_PUNCH"], "score": 41.2}, {"speciesId": "sudowoodo_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 392, "matchups": [{"opponent": "darmanitan_standard", "rating": 875, "opRating": 124}, {"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "typhlosion_shadow", "rating": 866, "opRating": 133}, {"opponent": "moltres_shadow", "rating": 856, "opRating": 143}, {"opponent": "articuno_shadow", "rating": 824, "opRating": 175}], "counters": [{"opponent": "garcho<PERSON>", "rating": 98}, {"opponent": "metagross", "rating": 197}, {"opponent": "lugia", "rating": 211}, {"opponent": "dialga", "rating": 293}, {"opponent": "dragonite", "rating": 329}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44941}, {"moveId": "ROCK_THROW", "uses": 31559}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34392}, {"moveId": "EARTHQUAKE", "uses": 20991}, {"moveId": "STONE_EDGE", "uses": 13665}, {"moveId": "ROCK_TOMB", "uses": 7453}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 40.9}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 383, "matchups": [{"opponent": "tapu_fini", "rating": 865, "opRating": 134}, {"opponent": "rhyperior", "rating": 848, "opRating": 151}, {"opponent": "swampert", "rating": 738}, {"opponent": "swampert_shadow", "rating": 708, "opRating": 291}, {"opponent": "kyogre", "rating": 543, "opRating": 456}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "metagross", "rating": 186}, {"opponent": "gyarados", "rating": 304}, {"opponent": "excadrill", "rating": 432}, {"opponent": "grou<PERSON>", "rating": 470}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44628}, {"moveId": "INFESTATION", "uses": 31872}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31620}, {"moveId": "SLUDGE_BOMB", "uses": 22200}, {"moveId": "POWER_WHIP", "uses": 15838}, {"moveId": "SOLAR_BEAM", "uses": 6649}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 40.9}, {"speciesId": "hypno_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 358, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 685, "opRating": 314}, {"opponent": "heracross", "rating": 632, "opRating": 367}, {"opponent": "machamp", "rating": 632, "opRating": 367}, {"opponent": "machamp_shadow", "rating": 623, "opRating": 376}, {"opponent": "sneasler", "rating": 595, "opRating": 404}], "counters": [{"opponent": "dialga", "rating": 160}, {"opponent": "gyarados", "rating": 257}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "dragonite", "rating": 377}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 67012}, {"moveId": "ZEN_HEADBUTT", "uses": 9488}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14863}, {"moveId": "PSYSHOCK", "uses": 11935}, {"moveId": "SHADOW_BALL", "uses": 11544}, {"moveId": "FIRE_PUNCH", "uses": 11231}, {"moveId": "THUNDER_PUNCH", "uses": 11034}, {"moveId": "FOCUS_BLAST", "uses": 7315}, {"moveId": "PSYCHIC", "uses": 4605}, {"moveId": "FUTURE_SIGHT", "uses": 3951}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 40.6}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 382, "matchups": [{"opponent": "tapu_bulu", "rating": 740, "opRating": 259}, {"opponent": "florges", "rating": 626, "opRating": 373}, {"opponent": "genesect_chill", "rating": 577, "opRating": 422}, {"opponent": "genesect_burn", "rating": 577, "opRating": 422}, {"opponent": "buzzwole", "rating": 538, "opRating": 461}], "counters": [{"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "dialga", "rating": 144}, {"opponent": "metagross", "rating": 273}, {"opponent": "zacian_hero", "rating": 465}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 469}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 42391}, {"moveId": "POISON_JAB", "uses": 34109}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 32849}, {"moveId": "FIRE_BLAST", "uses": 16130}, {"moveId": "DRAGON_PULSE", "uses": 14382}, {"moveId": "SLUDGE_WAVE", "uses": 13164}]}, "moveset": ["INCINERATE", "POISON_FANG", "FIRE_BLAST"], "score": 40.4}, {"speciesId": "solrock", "speciesName": "Solrock", "rating": 382, "matchups": [{"opponent": "moltres", "rating": 750, "opRating": 250}, {"opponent": "staraptor", "rating": 653, "opRating": 346}, {"opponent": "entei_shadow", "rating": 631, "opRating": 368}, {"opponent": "ho_oh", "rating": 580, "opRating": 419}, {"opponent": "ho_oh_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "lugia", "rating": 204}, {"opponent": "zacian_hero", "rating": 343}, {"opponent": "dragonite", "rating": 353}, {"opponent": "gyarados", "rating": 355}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40047}, {"moveId": "ROCK_THROW", "uses": 36453}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 42231}, {"moveId": "PSYCHIC", "uses": 22923}, {"moveId": "SOLAR_BEAM", "uses": 11362}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 40.4}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 361, "matchups": [{"opponent": "rhydon", "rating": 671, "opRating": 328}, {"opponent": "feraligatr_shadow", "rating": 653, "opRating": 346}, {"opponent": "luxray_shadow", "rating": 608, "opRating": 391}, {"opponent": "swampert", "rating": 540}, {"opponent": "ursaring_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "giratina_origin", "rating": 272}, {"opponent": "excadrill", "rating": 290}, {"opponent": "gyarados", "rating": 298}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52194}, {"moveId": "ASTONISH", "uses": 24306}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 27408}, {"moveId": "GRASS_KNOT", "uses": 26756}, {"moveId": "SLUDGE_BOMB", "uses": 22290}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 40.2}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 350, "matchups": [{"opponent": "machamp_shadow", "rating": 735, "opRating": 264}, {"opponent": "magnezone", "rating": 732, "opRating": 267}, {"opponent": "magnezone_shadow", "rating": 697, "opRating": 302}, {"opponent": "nihilego", "rating": 665, "opRating": 334}, {"opponent": "melmetal", "rating": 591, "opRating": 408}], "counters": [{"opponent": "dialga", "rating": 81}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "excadrill", "rating": 386}, {"opponent": "garcho<PERSON>", "rating": 403}, {"opponent": "dragonite", "rating": 449}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 28261}, {"moveId": "MUD_SLAP", "uses": 25169}, {"moveId": "EXTRASENSORY", "uses": 23077}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 16785}, {"moveId": "ICE_BEAM", "uses": 14074}, {"moveId": "SHADOW_BALL", "uses": 13681}, {"moveId": "PSYCHIC", "uses": 11893}, {"moveId": "ROCK_TOMB", "uses": 7902}, {"moveId": "EARTHQUAKE", "uses": 7231}, {"moveId": "GYRO_BALL", "uses": 4906}]}, "moveset": ["CONFUSION", "EARTH_POWER", "ICE_BEAM"], "score": 40.2}, {"speciesId": "hypno", "speciesName": "Hypno", "rating": 358, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 696, "opRating": 303}, {"opponent": "heracross", "rating": 665, "opRating": 334}, {"opponent": "machamp", "rating": 665, "opRating": 334}, {"opponent": "sneasler", "rating": 648, "opRating": 351}, {"opponent": "machamp_shadow", "rating": 632, "opRating": 367}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "gyarados", "rating": 242}, {"opponent": "zacian_hero", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "dragonite", "rating": 343}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66893}, {"moveId": "ZEN_HEADBUTT", "uses": 9607}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14024}, {"moveId": "PSYSHOCK", "uses": 11247}, {"moveId": "SHADOW_BALL", "uses": 10880}, {"moveId": "FIRE_PUNCH", "uses": 10628}, {"moveId": "THUNDER_PUNCH", "uses": 10371}, {"moveId": "FOCUS_BLAST", "uses": 6921}, {"moveId": "RETURN", "uses": 4404}, {"moveId": "PSYCHIC", "uses": 4388}, {"moveId": "FUTURE_SIGHT", "uses": 3793}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 40.2}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 326, "matchups": [{"opponent": "deoxys", "rating": 892, "opRating": 107}, {"opponent": "gengar", "rating": 785, "opRating": 214}, {"opponent": "magneton", "rating": 704, "opRating": 295}, {"opponent": "magneton_shadow", "rating": 644, "opRating": 355}, {"opponent": "run<PERSON><PERSON>", "rating": 617, "opRating": 382}], "counters": [{"opponent": "dialga", "rating": 103}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 281}, {"opponent": "excadrill", "rating": 351}, {"opponent": "giratina_origin", "rating": 416}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 39043}, {"moveId": "TACKLE", "uses": 37457}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 53107}, {"moveId": "BULLDOZE", "uses": 14677}, {"moveId": "GUNK_SHOT", "uses": 8755}]}, "moveset": ["LICK", "BODY_SLAM", "BULLDOZE"], "score": 40.2}, {"speciesId": "pheromosa", "speciesName": "Pheromosa", "rating": 383, "matchups": [{"opponent": "rhyperior", "rating": 778, "opRating": 221}, {"opponent": "regigigas", "rating": 715, "opRating": 284}, {"opponent": "cobalion", "rating": 664, "opRating": 335}, {"opponent": "metagross_shadow", "rating": 594, "opRating": 405}, {"opponent": "snorlax", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "lugia", "rating": 221}, {"opponent": "metagross", "rating": 497}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 60257}, {"moveId": "LOW_KICK", "uses": 16243}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 37069}, {"moveId": "LUNGE", "uses": 20643}, {"moveId": "BUG_BUZZ", "uses": 13409}, {"moveId": "FOCUS_BLAST", "uses": 5420}]}, "moveset": ["BUG_BITE", "CLOSE_COMBAT", "LUNGE"], "score": 40.2}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 384, "matchups": [{"opponent": "tapu_bulu", "rating": 702, "opRating": 297}, {"opponent": "genesect_chill", "rating": 643, "opRating": 356}, {"opponent": "genesect_burn", "rating": 643, "opRating": 356}, {"opponent": "chesnaught", "rating": 629, "opRating": 370}, {"opponent": "metagross_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "lugia", "rating": 188}, {"opponent": "zacian_hero", "rating": 193}, {"opponent": "gyarados", "rating": 309}, {"opponent": "metagross", "rating": 488}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 41601}, {"moveId": "LICK", "uses": 34899}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 33510}, {"moveId": "THUNDER_PUNCH", "uses": 29703}, {"moveId": "POWER_UP_PUNCH", "uses": 13272}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "THUNDER_PUNCH"], "score": 40.1}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 365, "matchups": [{"opponent": "trevenant", "rating": 781, "opRating": 218}, {"opponent": "golisopod", "rating": 714, "opRating": 285}, {"opponent": "gengar", "rating": 693, "opRating": 306}, {"opponent": "swampert_shadow", "rating": 556, "opRating": 443}, {"opponent": "giratina_origin", "rating": 503}], "counters": [{"opponent": "mewtwo", "rating": 104}, {"opponent": "lugia", "rating": 195}, {"opponent": "dialga", "rating": 198}, {"opponent": "metagross", "rating": 212}, {"opponent": "gyarados", "rating": 242}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 47995}, {"moveId": "STEEL_WING", "uses": 28505}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34431}, {"moveId": "DRILL_PECK", "uses": 31297}, {"moveId": "AERIAL_ACE", "uses": 6889}, {"moveId": "AIR_CUTTER", "uses": 3825}]}, "moveset": ["FEINT_ATTACK", "BRAVE_BIRD", "DRILL_PECK"], "score": 39.8}, {"speciesId": "lunatone", "speciesName": "Lunatone", "rating": 386, "matchups": [{"opponent": "moltres", "rating": 750, "opRating": 250}, {"opponent": "staraptor", "rating": 653, "opRating": 346}, {"opponent": "entei_shadow", "rating": 631, "opRating": 368}, {"opponent": "ho_oh", "rating": 580}, {"opponent": "ho_oh_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "lugia", "rating": 204}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "gyarados", "rating": 355}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40032}, {"moveId": "ROCK_THROW", "uses": 36468}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 36347}, {"moveId": "MOONBLAST", "uses": 20446}, {"moveId": "PSYCHIC", "uses": 19662}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "MOONBLAST"], "score": 39.8}, {"speciesId": "lair<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 350, "matchups": [{"opponent": "charizard", "rating": 732, "opRating": 267}, {"opponent": "weavile", "rating": 669, "opRating": 330}, {"opponent": "moltres_shadow", "rating": 623, "opRating": 376}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 535, "opRating": 464}, {"opponent": "articuno_galarian", "rating": 521, "opRating": 478}], "counters": [{"opponent": "garcho<PERSON>", "rating": 147}, {"opponent": "dialga", "rating": 157}, {"opponent": "mewtwo", "rating": 278}, {"opponent": "gyarados", "rating": 319}, {"opponent": "lugia", "rating": 407}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54597}, {"moveId": "IRON_TAIL", "uses": 21903}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28338}, {"moveId": "BODY_SLAM", "uses": 27334}, {"moveId": "HEAVY_SLAM", "uses": 14737}, {"moveId": "ROCK_TOMB", "uses": 6062}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 39.6}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 390, "matchups": [{"opponent": "sandslash_alolan_shadow", "rating": 932, "opRating": 67}, {"opponent": "weavile_shadow", "rating": 919, "opRating": 80}, {"opponent": "weavile", "rating": 674, "opRating": 325}, {"opponent": "genesect_chill", "rating": 597, "opRating": 402}, {"opponent": "genesect_burn", "rating": 597, "opRating": 402}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "lugia", "rating": 161}, {"opponent": "dialga", "rating": 244}, {"opponent": "metagross", "rating": 261}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38844}, {"moveId": "EMBER", "uses": 37656}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38237}, {"moveId": "RETURN", "uses": 16384}, {"moveId": "FLAMETHROWER", "uses": 14261}, {"moveId": "FIRE_BLAST", "uses": 7702}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "RETURN"], "score": 39.6}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 331, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 775, "opRating": 224}, {"opponent": "cofagrigus", "rating": 772, "opRating": 227}, {"opponent": "gengar", "rating": 758, "opRating": 241}, {"opponent": "trevenant", "rating": 625, "opRating": 374}, {"opponent": "chandelure", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "mewtwo", "rating": 286}, {"opponent": "lugia", "rating": 297}, {"opponent": "giratina_origin", "rating": 490}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 47027}, {"moveId": "ASTONISH", "uses": 29473}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28023}, {"moveId": "DISARMING_VOICE", "uses": 24146}, {"moveId": "RETURN", "uses": 14680}, {"moveId": "FIRE_BLAST", "uses": 9752}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 39.3}, {"speciesId": "octillery", "speciesName": "Octillery", "rating": 311, "matchups": [{"opponent": "exeggutor_alolan_shadow", "rating": 756, "opRating": 243}, {"opponent": "flygon_shadow", "rating": 722, "opRating": 277}, {"opponent": "salamence_shadow", "rating": 719, "opRating": 280}, {"opponent": "piloswine_shadow", "rating": 673, "opRating": 326}, {"opponent": "flareon", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "giratina_origin", "rating": 201}, {"opponent": "lugia", "rating": 245}, {"opponent": "garcho<PERSON>", "rating": 349}, {"opponent": "dragonite", "rating": 390}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 26999}, {"moveId": "WATER_GUN", "uses": 24962}, {"moveId": "MUD_SHOT", "uses": 24511}], "chargedMoves": [{"moveId": "OCTAZOOKA", "uses": 26799}, {"moveId": "AURORA_BEAM", "uses": 21830}, {"moveId": "GUNK_SHOT", "uses": 13785}, {"moveId": "WATER_PULSE", "uses": 8013}, {"moveId": "ACID_SPRAY", "uses": 5953}]}, "moveset": ["LOCK_ON", "OCTAZOOKA", "AURORA_BEAM"], "score": 39.3}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 379, "matchups": [{"opponent": "gyarado<PERSON>_shadow", "rating": 765, "opRating": 234}, {"opponent": "primarina", "rating": 637, "opRating": 362}, {"opponent": "samu<PERSON>t", "rating": 597, "opRating": 402}, {"opponent": "braviary", "rating": 546, "opRating": 453}, {"opponent": "gyarados", "rating": 520}], "counters": [{"opponent": "zacian_hero", "rating": 147}, {"opponent": "dialga", "rating": 179}, {"opponent": "metagross", "rating": 188}, {"opponent": "lugia", "rating": 307}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 353}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 60100}, {"moveId": "LOW_KICK", "uses": 16400}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 34818}, {"moveId": "RETURN", "uses": 17535}, {"moveId": "THUNDERBOLT", "uses": 12958}, {"moveId": "THUNDER", "uses": 11151}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 39.1}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 312, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "gengar", "rating": 774, "opRating": 225}, {"opponent": "hoopa", "rating": 768, "opRating": 231}, {"opponent": "golem", "rating": 594, "opRating": 405}, {"opponent": "giratina_origin", "rating": 509}], "counters": [{"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "dialga", "rating": 179}, {"opponent": "lugia", "rating": 202}, {"opponent": "excadrill", "rating": 313}, {"opponent": "mewtwo", "rating": 315}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39369}, {"moveId": "BITE", "uses": 26173}, {"moveId": "TAKE_DOWN", "uses": 10947}], "chargedMoves": [{"moveId": "SURF", "uses": 30806}, {"moveId": "DARK_PULSE", "uses": 24880}, {"moveId": "GRASS_KNOT", "uses": 20838}]}, "moveset": ["SUCKER_PUNCH", "SURF", "DARK_PULSE"], "score": 39}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 370, "matchups": [{"opponent": "virizion", "rating": 753, "opRating": 246}, {"opponent": "chesnaught", "rating": 731, "opRating": 268}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 664, "opRating": 335}, {"opponent": "escavalier", "rating": 652, "opRating": 347}, {"opponent": "buzzwole", "rating": 649, "opRating": 350}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "zacian_hero", "rating": 193}, {"opponent": "lugia", "rating": 228}, {"opponent": "gyarados", "rating": 242}, {"opponent": "metagross", "rating": 252}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66283}, {"moveId": "POUND", "uses": 10217}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35278}, {"moveId": "HURRICANE", "uses": 31967}, {"moveId": "AIR_CUTTER", "uses": 9261}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 39}, {"speciesId": "throh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 364, "matchups": [{"opponent": "tyranitar", "rating": 642, "opRating": 357}, {"opponent": "tyranitar_shadow", "rating": 625, "opRating": 374}, {"opponent": "melmetal", "rating": 610, "opRating": 389}, {"opponent": "heatran", "rating": 582, "opRating": 417}, {"opponent": "snorlax", "rating": 547, "opRating": 452}], "counters": [{"opponent": "zacian_hero", "rating": 156}, {"opponent": "garcho<PERSON>", "rating": 176}, {"opponent": "metagross", "rating": 177}, {"opponent": "dialga", "rating": 206}, {"opponent": "gyarados", "rating": 268}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 49132}, {"moveId": "ZEN_HEADBUTT", "uses": 27368}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41224}, {"moveId": "FOCUS_BLAST", "uses": 18503}, {"moveId": "LOW_SWEEP", "uses": 16778}]}, "moveset": ["LOW_KICK", "BODY_SLAM", "FOCUS_BLAST"], "score": 38.8}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 326, "matchups": [{"opponent": "meganium_shadow", "rating": 818, "opRating": 181}, {"opponent": "hoopa_unbound", "rating": 681, "opRating": 318}, {"opponent": "zarude", "rating": 665, "opRating": 334}, {"opponent": "pinsir_shadow", "rating": 636, "opRating": 363}, {"opponent": "virizion", "rating": 576, "opRating": 423}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "gyarados", "rating": 237}, {"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "swampert", "rating": 370}, {"opponent": "mewtwo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 22346}, {"moveId": "BUG_BITE", "uses": 19365}, {"moveId": "POISON_STING", "uses": 17677}, {"moveId": "AIR_SLASH", "uses": 17149}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 29858}, {"moveId": "BUG_BUZZ", "uses": 19502}, {"moveId": "POWER_GEM", "uses": 14035}, {"moveId": "SIGNAL_BEAM", "uses": 8405}, {"moveId": "FELL_STINGER", "uses": 4900}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 38.6}, {"speciesId": "sigilyph", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 343, "matchups": [{"opponent": "celebi", "rating": 737, "opRating": 262}, {"opponent": "chesnaught", "rating": 721, "opRating": 278}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 693, "opRating": 306}, {"opponent": "virizion", "rating": 681, "opRating": 318}, {"opponent": "heracross", "rating": 650, "opRating": 350}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "lugia", "rating": 197}, {"opponent": "zacian_hero", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 291}, {"opponent": "dragonite", "rating": 300}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 58115}, {"moveId": "ZEN_HEADBUTT", "uses": 18385}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 28869}, {"moveId": "SIGNAL_BEAM", "uses": 19474}, {"moveId": "AIR_CUTTER", "uses": 15113}, {"moveId": "PSYBEAM", "uses": 13060}]}, "moveset": ["AIR_SLASH", "ANCIENT_POWER", "SIGNAL_BEAM"], "score": 38.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 364, "matchups": [{"opponent": "heracross", "rating": 667, "opRating": 332}, {"opponent": "cobalion", "rating": 628, "opRating": 371}, {"opponent": "sylveon", "rating": 585, "opRating": 414}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 554, "opRating": 445}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 536}], "counters": [{"opponent": "mewtwo", "rating": 93}, {"opponent": "dialga", "rating": 108}, {"opponent": "garcho<PERSON>", "rating": 126}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "lugia", "rating": 223}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43174}, {"moveId": "EXTRASENSORY", "uses": 33326}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 39686}, {"moveId": "FIRE_BLAST", "uses": 22575}, {"moveId": "SWIFT", "uses": 14270}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "FIRE_BLAST"], "score": 38}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 340, "matchups": [{"opponent": "charizard_shadow", "rating": 732, "opRating": 267}, {"opponent": "weavile_shadow", "rating": 669, "opRating": 330}, {"opponent": "avalugg", "rating": 633, "opRating": 366}, {"opponent": "articuno_galarian", "rating": 623, "opRating": 376}, {"opponent": "moltres_galarian", "rating": 528, "opRating": 471}], "counters": [{"opponent": "garcho<PERSON>", "rating": 133}, {"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "gyarados", "rating": 301}, {"opponent": "lugia", "rating": 388}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 53890}, {"moveId": "IRON_TAIL", "uses": 22610}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27013}, {"moveId": "BODY_SLAM", "uses": 25836}, {"moveId": "HEAVY_SLAM", "uses": 14063}, {"moveId": "ROCK_TOMB", "uses": 5906}, {"moveId": "RETURN", "uses": 3729}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 38}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 352, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 718, "opRating": 281}, {"opponent": "lura<PERSON>s", "rating": 681, "opRating": 318}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 664, "opRating": 335}, {"opponent": "hoopa", "rating": 651, "opRating": 348}, {"opponent": "hitmonchan", "rating": 651, "opRating": 348}], "counters": [{"opponent": "zacian_hero", "rating": 187}, {"opponent": "dialga", "rating": 190}, {"opponent": "gyarados", "rating": 221}, {"opponent": "giratina_origin", "rating": 231}, {"opponent": "metagross", "rating": 293}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 33158}, {"moveId": "TACKLE", "uses": 26443}, {"moveId": "ACID", "uses": 16884}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 19306}, {"moveId": "DARK_PULSE", "uses": 17900}, {"moveId": "SLUDGE_BOMB", "uses": 17433}, {"moveId": "THUNDERBOLT", "uses": 13280}, {"moveId": "RETURN", "uses": 8580}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 38}, {"speciesId": "weezing_shadow", "speciesName": "Weez<PERSON> (Shadow)", "rating": 347, "matchups": [{"opponent": "gardevoir_shadow", "rating": 765, "opRating": 234}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 664, "opRating": 335}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 600, "opRating": 399}, {"opponent": "hoopa", "rating": 570, "opRating": 429}, {"opponent": "cresselia", "rating": 563, "opRating": 436}], "counters": [{"opponent": "dialga", "rating": 220}, {"opponent": "lugia", "rating": 230}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "gyarados", "rating": 244}, {"opponent": "metagross", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 42089}, {"moveId": "TACKLE", "uses": 34411}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 21610}, {"moveId": "DARK_PULSE", "uses": 20135}, {"moveId": "SLUDGE_BOMB", "uses": 19846}, {"moveId": "THUNDERBOLT", "uses": 14933}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 37.7}, {"speciesId": "chimecho", "speciesName": "Chi<PERSON><PERSON>", "rating": 349, "matchups": [{"opponent": "hoopa", "rating": 853, "opRating": 146}, {"opponent": "blaziken", "rating": 689, "opRating": 310}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 637, "opRating": 362}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 634, "opRating": 365}, {"opponent": "sneasler", "rating": 564, "opRating": 435}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "garcho<PERSON>", "rating": 187}, {"opponent": "zacian_hero", "rating": 236}, {"opponent": "dragonite", "rating": 265}, {"opponent": "excadrill", "rating": 297}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 52933}, {"moveId": "ASTONISH", "uses": 23567}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30435}, {"moveId": "SHADOW_BALL", "uses": 28761}, {"moveId": "ENERGY_BALL", "uses": 17325}]}, "moveset": ["EXTRASENSORY", "PSYSHOCK", "SHADOW_BALL"], "score": 37.5}, {"speciesId": "oricorio_sensu", "speciesName": "Oricorio (Sensu)", "rating": 341, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 850, "opRating": 149}, {"opponent": "virizion", "rating": 826, "opRating": 173}, {"opponent": "buzzwole", "rating": 820, "opRating": 179}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 774, "opRating": 225}, {"opponent": "escavalier", "rating": 768, "opRating": 231}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "lugia", "rating": 228}, {"opponent": "zacian_hero", "rating": 367}, {"opponent": "grou<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66415}, {"moveId": "POUND", "uses": 10085}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35279}, {"moveId": "HURRICANE", "uses": 31987}, {"moveId": "AIR_CUTTER", "uses": 9256}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 37.4}, {"speciesId": "gur<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 361, "matchups": [{"opponent": "vikavolt", "rating": 735, "opRating": 264}, {"opponent": "bisharp", "rating": 719, "opRating": 280}, {"opponent": "charizard", "rating": 629, "opRating": 370}, {"opponent": "charizard_shadow", "rating": 598, "opRating": 401}, {"opponent": "pinsir_shadow", "rating": 536, "opRating": 463}], "counters": [{"opponent": "garcho<PERSON>", "rating": 112}, {"opponent": "dialga", "rating": 163}, {"opponent": "zacian_hero", "rating": 167}, {"opponent": "gyarados", "rating": 402}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 404}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56788}, {"moveId": "LOW_KICK", "uses": 19712}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 34089}, {"moveId": "STONE_EDGE", "uses": 32456}, {"moveId": "LOW_SWEEP", "uses": 10110}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 37.2}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 360, "matchups": [{"opponent": "s<PERSON><PERSON>", "rating": 737, "opRating": 262}, {"opponent": "tapu_bulu", "rating": 682, "opRating": 317}, {"opponent": "genesect_chill", "rating": 621, "opRating": 378}, {"opponent": "genesect_burn", "rating": 621, "opRating": 378}, {"opponent": "registeel", "rating": 585, "opRating": 414}], "counters": [{"opponent": "zacian_hero", "rating": 176}, {"opponent": "lugia", "rating": 202}, {"opponent": "dialga", "rating": 217}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 325}, {"opponent": "metagross", "rating": 491}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48581}, {"moveId": "BITE", "uses": 27919}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38688}, {"moveId": "FLAMETHROWER", "uses": 29797}, {"moveId": "FIRE_BLAST", "uses": 8066}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 37.1}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 343, "matchups": [{"opponent": "barbara<PERSON>", "rating": 668, "opRating": 331}, {"opponent": "hitmontop", "rating": 659, "opRating": 340}, {"opponent": "rotom_wash", "rating": 645, "opRating": 354}, {"opponent": "toxicroak", "rating": 633, "opRating": 366}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "mewtwo", "rating": 156}, {"opponent": "zacian_hero", "rating": 228}, {"opponent": "gyarados", "rating": 262}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 63127}, {"moveId": "TAKE_DOWN", "uses": 13373}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 38565}, {"moveId": "SEED_BOMB", "uses": 20685}, {"moveId": "GUNK_SHOT", "uses": 12008}, {"moveId": "ACID_SPRAY", "uses": 5240}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 36.7}, {"speciesId": "mr_mime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 337, "matchups": [{"opponent": "heracross", "rating": 730, "opRating": 269}, {"opponent": "kommo_o", "rating": 721, "opRating": 278}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 699, "opRating": 300}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 646, "opRating": 353}, {"opponent": "buzzwole", "rating": 601, "opRating": 398}], "counters": [{"opponent": "dialga", "rating": 86}, {"opponent": "lugia", "rating": 230}, {"opponent": "excadrill", "rating": 358}, {"opponent": "gyarados", "rating": 389}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 63734}, {"moveId": "ZEN_HEADBUTT", "uses": 12766}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37783}, {"moveId": "PSYCHIC", "uses": 32149}, {"moveId": "PSYBEAM", "uses": 6432}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 36.6}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 336, "matchups": [{"opponent": "pinsir_shadow", "rating": 592, "opRating": 407}, {"opponent": "tapu_bulu", "rating": 587, "opRating": 412}, {"opponent": "tangrowth", "rating": 557, "opRating": 442}, {"opponent": "staraptor", "rating": 532, "opRating": 467}, {"opponent": "tapu_koko", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "dragonite", "rating": 332}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 432}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54789}, {"moveId": "ROCK_SMASH", "uses": 21711}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 33585}, {"moveId": "SLUDGE_BOMB", "uses": 28772}, {"moveId": "GUNK_SHOT", "uses": 7476}, {"moveId": "ACID_SPRAY", "uses": 6620}]}, "moveset": ["INFESTATION", "ICE_BEAM", "SLUDGE_BOMB"], "score": 36.3}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 303, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 765, "opRating": 234}, {"opponent": "cofagrigus", "rating": 741, "opRating": 258}, {"opponent": "gengar", "rating": 731, "opRating": 268}, {"opponent": "hoopa", "rating": 661, "opRating": 338}, {"opponent": "giratina_origin", "rating": 565}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "metagross", "rating": 197}, {"opponent": "gyarados", "rating": 216}, {"opponent": "lugia", "rating": 290}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 45623}, {"moveId": "ASTONISH", "uses": 30877}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34320}, {"moveId": "DISARMING_VOICE", "uses": 30055}, {"moveId": "FIRE_BLAST", "uses": 11977}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 36.1}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 298, "matchups": [{"opponent": "gren<PERSON><PERSON>", "rating": 729, "opRating": 270}, {"opponent": "flygon_shadow", "rating": 684, "opRating": 315}, {"opponent": "scrafty", "rating": 636, "opRating": 363}, {"opponent": "flygon", "rating": 598, "opRating": 401}, {"opponent": "pangoro", "rating": 570, "opRating": 429}], "counters": [{"opponent": "mewtwo", "rating": 135}, {"opponent": "dialga", "rating": 179}, {"opponent": "giratina_origin", "rating": 189}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "gyarados", "rating": 247}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 6509}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 5497}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5355}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4902}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4707}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4637}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4551}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4395}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4382}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4294}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4214}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4205}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4155}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3820}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3817}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3706}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3565}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 35993}, {"moveId": "HYPER_BEAM", "uses": 26072}, {"moveId": "SOLAR_BEAM", "uses": 14555}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 36.1}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 352, "matchups": [{"opponent": "virizion", "rating": 774, "opRating": 225}, {"opponent": "chesnaught", "rating": 765, "opRating": 234}, {"opponent": "escavalier", "rating": 652, "opRating": 347}, {"opponent": "buzzwole", "rating": 649, "opRating": 350}, {"opponent": "zarude", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "zacian_hero", "rating": 156}, {"opponent": "lugia", "rating": 228}, {"opponent": "metagross", "rating": 252}, {"opponent": "grou<PERSON>", "rating": 355}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66462}, {"moveId": "POUND", "uses": 10038}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35278}, {"moveId": "HURRICANE", "uses": 31983}, {"moveId": "AIR_CUTTER", "uses": 9254}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 35.9}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 295, "matchups": [{"opponent": "dusknoir", "rating": 704, "opRating": 295}, {"opponent": "jellicent", "rating": 701, "opRating": 298}, {"opponent": "crawdaunt", "rating": 695, "opRating": 304}, {"opponent": "flygon", "rating": 551, "opRating": 448}, {"opponent": "stoutland", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "dragonite", "rating": 226}, {"opponent": "excadrill", "rating": 318}, {"opponent": "giratina_origin", "rating": 436}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 64757}, {"moveId": "POUND", "uses": 11743}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38733}, {"moveId": "THUNDERBOLT", "uses": 21568}, {"moveId": "HYPER_BEAM", "uses": 16151}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 35.6}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 342, "matchups": [{"opponent": "tapu_fini", "rating": 679, "opRating": 320}, {"opponent": "rhyperior", "rating": 673, "opRating": 326}, {"opponent": "swampert", "rating": 643}, {"opponent": "krookodile", "rating": 591, "opRating": 408}, {"opponent": "kyogre", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 103}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "dragonite", "rating": 191}, {"opponent": "gyarados", "rating": 203}, {"opponent": "excadrill", "rating": 346}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 38410}, {"moveId": "BULLET_SEED", "uses": 38090}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33831}, {"moveId": "PETAL_BLIZZARD", "uses": 30450}, {"moveId": "SOLAR_BEAM", "uses": 12283}]}, "moveset": ["POISON_JAB", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 35.6}, {"speciesId": "gorebyss", "speciesName": "<PERSON><PERSON>", "rating": 336, "matchups": [{"opponent": "typhlosion", "rating": 866, "opRating": 133}, {"opponent": "entei", "rating": 648, "opRating": 351}, {"opponent": "ma<PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "heatran", "rating": 618, "opRating": 381}, {"opponent": "excadrill", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 101}, {"opponent": "garcho<PERSON>", "rating": 105}, {"opponent": "lugia", "rating": 204}, {"opponent": "metagross", "rating": 238}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40693}, {"moveId": "CONFUSION", "uses": 35807}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 32052}, {"moveId": "WATER_PULSE", "uses": 23936}, {"moveId": "DRAINING_KISS", "uses": 20497}]}, "moveset": ["WATER_GUN", "PSYCHIC", "WATER_PULSE"], "score": 35.5}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 342, "matchups": [{"opponent": "krookodile", "rating": 684, "opRating": 315}, {"opponent": "genesect_douse", "rating": 516, "opRating": 483}, {"opponent": "genesect_shock", "rating": 516, "opRating": 483}, {"opponent": "genesect_chill", "rating": 516, "opRating": 483}, {"opponent": "genesect_burn", "rating": 516, "opRating": 483}], "counters": [{"opponent": "mewtwo", "rating": 138}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "dialga", "rating": 171}, {"opponent": "metagross", "rating": 177}, {"opponent": "excadrill", "rating": 320}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 54666}, {"moveId": "LOW_KICK", "uses": 11762}, {"moveId": "POUND", "uses": 10043}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 34755}, {"moveId": "FOCUS_BLAST", "uses": 20948}, {"moveId": "HYPER_BEAM", "uses": 20777}]}, "moveset": ["DOUBLE_KICK", "FIRE_PUNCH", "FOCUS_BLAST"], "score": 35.5}, {"speciesId": "oricorio_pau", "speciesName": "Oricorio (Pa'u)", "rating": 325, "matchups": [{"opponent": "virizion", "rating": 789, "opRating": 210}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 719, "opRating": 280}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 719, "opRating": 280}, {"opponent": "heracross", "rating": 667, "opRating": 332}, {"opponent": "buzzwole", "rating": 652, "opRating": 347}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "zacian_hero", "rating": 193}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "lugia", "rating": 228}, {"opponent": "grou<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66350}, {"moveId": "POUND", "uses": 10150}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35279}, {"moveId": "HURRICANE", "uses": 31967}, {"moveId": "AIR_CUTTER", "uses": 9256}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 35.5}, {"speciesId": "accelgor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 340, "matchups": [{"opponent": "celebi", "rating": 755, "opRating": 244}, {"opponent": "latias_shadow", "rating": 674, "opRating": 325}, {"opponent": "hoopa_unbound", "rating": 639, "opRating": 360}, {"opponent": "krookodile", "rating": 619, "opRating": 380}, {"opponent": "zarude", "rating": 552, "opRating": 447}], "counters": [{"opponent": "zacian_hero", "rating": 176}, {"opponent": "dialga", "rating": 192}, {"opponent": "gyarados", "rating": 208}, {"opponent": "metagross", "rating": 293}, {"opponent": "swampert", "rating": 330}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54912}, {"moveId": "ACID", "uses": 21588}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26682}, {"moveId": "SIGNAL_BEAM", "uses": 22924}, {"moveId": "FOCUS_BLAST", "uses": 20374}, {"moveId": "ACID_SPRAY", "uses": 6598}]}, "moveset": ["INFESTATION", "BUG_BUZZ", "SIGNAL_BEAM"], "score": 34.7}, {"speciesId": "meowstic_female", "speciesName": "<PERSON><PERSON><PERSON> (Female)", "rating": 337, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 659, "opRating": 340}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 631, "opRating": 368}, {"opponent": "machamp", "rating": 628, "opRating": 371}, {"opponent": "terrakion", "rating": 616, "opRating": 383}, {"opponent": "heracross", "rating": 610, "opRating": 389}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "lugia", "rating": 209}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "dragonite", "rating": 305}, {"opponent": "excadrill", "rating": 306}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44352}, {"moveId": "CHARM", "uses": 32148}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 31197}, {"moveId": "PSYCHIC", "uses": 26604}, {"moveId": "ENERGY_BALL", "uses": 18702}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 34.3}, {"speciesId": "me<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON> (Male)", "rating": 335, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 659, "opRating": 340}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 631, "opRating": 368}, {"opponent": "machamp", "rating": 628, "opRating": 371}, {"opponent": "terrakion", "rating": 616, "opRating": 383}, {"opponent": "heracross", "rating": 610, "opRating": 389}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "lugia", "rating": 197}, {"opponent": "excadrill", "rating": 246}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "dragonite", "rating": 305}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40921}, {"moveId": "SUCKER_PUNCH", "uses": 35579}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 30611}, {"moveId": "THUNDERBOLT", "uses": 24484}, {"moveId": "ENERGY_BALL", "uses": 21405}]}, "moveset": ["CONFUSION", "PSYCHIC", "THUNDERBOLT"], "score": 34}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 303, "matchups": [{"opponent": "typhlosion", "rating": 603, "opRating": 396}, {"opponent": "hippow<PERSON>_shadow", "rating": 601, "opRating": 398}, {"opponent": "rhyperior", "rating": 547, "opRating": 452}, {"opponent": "ma<PERSON><PERSON>", "rating": 530, "opRating": 469}, {"opponent": "excadrill", "rating": 505}], "counters": [{"opponent": "mewtwo", "rating": 93}, {"opponent": "dialga", "rating": 114}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "lugia", "rating": 161}, {"opponent": "metagross", "rating": 276}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 39070}, {"moveId": "WATER_GUN", "uses": 37430}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 30961}, {"moveId": "AQUA_JET", "uses": 29002}, {"moveId": "SWIFT", "uses": 16447}]}, "moveset": ["WATERFALL", "HYDRO_PUMP", "AQUA_JET"], "score": 33.5}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 293, "matchups": [{"opponent": "tauros", "rating": 731, "opRating": 268}, {"opponent": "reuniclus", "rating": 698, "opRating": 301}, {"opponent": "quagsire", "rating": 646, "opRating": 353}, {"opponent": "torterra", "rating": 524, "opRating": 475}, {"opponent": "stoutland", "rating": 512, "opRating": 487}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "dialga", "rating": 146}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47960}, {"moveId": "ASTONISH", "uses": 28540}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28292}, {"moveId": "LOW_SWEEP", "uses": 24291}, {"moveId": "HYPER_BEAM", "uses": 23680}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["SCRATCH", "AERIAL_ACE", "LOW_SWEEP"], "score": 33.2}, {"speciesId": "masquerain", "speciesName": "Masquerain", "rating": 305, "matchups": [{"opponent": "meganium", "rating": 815, "opRating": 184}, {"opponent": "hoopa_unbound", "rating": 761, "opRating": 238}, {"opponent": "chesnaught", "rating": 694, "opRating": 305}, {"opponent": "virizion", "rating": 601, "opRating": 398}, {"opponent": "zarude", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "zacian_hero", "rating": 132}, {"opponent": "gyarados", "rating": 162}, {"opponent": "metagross", "rating": 174}, {"opponent": "garcho<PERSON>", "rating": 314}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 38631}, {"moveId": "AIR_SLASH", "uses": 37869}], "chargedMoves": [{"moveId": "LUNGE", "uses": 30553}, {"moveId": "OMINOUS_WIND", "uses": 14006}, {"moveId": "AIR_CUTTER", "uses": 11994}, {"moveId": "SILVER_WIND", "uses": 10946}, {"moveId": "BUBBLE_BEAM", "uses": 9092}]}, "moveset": ["INFESTATION", "LUNGE", "OMINOUS_WIND"], "score": 32.8}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 281, "matchups": [{"opponent": "quagsire_shadow", "rating": 646, "opRating": 353}, {"opponent": "lickilicky", "rating": 573, "opRating": 426}, {"opponent": "mandibuzz", "rating": 560, "opRating": 439}, {"opponent": "ludico<PERSON>", "rating": 551, "opRating": 448}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 114}, {"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "lugia", "rating": 223}, {"opponent": "giratina_origin", "rating": 245}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47540}, {"moveId": "ASTONISH", "uses": 28960}], "chargedMoves": [{"moveId": "RETURN", "uses": 23833}, {"moveId": "AERIAL_ACE", "uses": 22889}, {"moveId": "LOW_SWEEP", "uses": 20476}, {"moveId": "HYPER_BEAM", "uses": 9192}]}, "moveset": ["SCRATCH", "RETURN", "AERIAL_ACE"], "score": 31.3}, {"speciesId": "sliggoo", "speciesName": "Sliggoo", "rating": 307, "matchups": [{"opponent": "infernape", "rating": 844, "opRating": 155}, {"opponent": "darmanitan_standard", "rating": 626, "opRating": 373}, {"opponent": "typhlosion_shadow", "rating": 594, "opRating": 405}, {"opponent": "golem", "rating": 590, "opRating": 409}, {"opponent": "magmortar_shadow", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "garcho<PERSON>", "rating": 112}, {"opponent": "zacian_hero", "rating": 138}, {"opponent": "excadrill", "rating": 320}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40170}, {"moveId": "TACKLE", "uses": 36330}], "chargedMoves": [{"moveId": "DRAGON_PULSE", "uses": 27422}, {"moveId": "MUDDY_WATER", "uses": 27034}, {"moveId": "SLUDGE_WAVE", "uses": 16730}, {"moveId": "WATER_PULSE", "uses": 5280}]}, "moveset": ["WATER_GUN", "DRAGON_PULSE", "MUDDY_WATER"], "score": 31.2}, {"speciesId": "xatu", "speciesName": "Xatu", "rating": 285, "matchups": [{"opponent": "virizion", "rating": 771, "opRating": 228}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 721, "opRating": 278}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 687, "opRating": 312}, {"opponent": "heracross", "rating": 634, "opRating": 365}, {"opponent": "buzzwole", "rating": 620, "opRating": 379}], "counters": [{"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "dialga", "rating": 135}, {"opponent": "lugia", "rating": 150}, {"opponent": "zacian_hero", "rating": 190}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 39786}, {"moveId": "FEINT_ATTACK", "uses": 36714}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 29238}, {"moveId": "FUTURE_SIGHT", "uses": 26807}, {"moveId": "OMINOUS_WIND", "uses": 20518}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "FUTURE_SIGHT"], "score": 29.7}, {"speciesId": "hit<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 257, "matchups": [{"opponent": "sandslash_alolan", "rating": 803, "opRating": 196}, {"opponent": "regigigas", "rating": 696, "opRating": 303}, {"opponent": "incineroar", "rating": 688, "opRating": 311}, {"opponent": "regice", "rating": 610, "opRating": 389}, {"opponent": "cobalion", "rating": 574, "opRating": 425}], "counters": [{"opponent": "mewtwo", "rating": 54}, {"opponent": "garcho<PERSON>", "rating": 117}, {"opponent": "metagross", "rating": 130}, {"opponent": "excadrill", "rating": 213}, {"opponent": "dialga", "rating": 244}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 46909}, {"moveId": "LOW_KICK", "uses": 29591}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30848}, {"moveId": "STONE_EDGE", "uses": 16578}, {"moveId": "BRICK_BREAK", "uses": 13697}, {"moveId": "STOMP", "uses": 11338}, {"moveId": "LOW_SWEEP", "uses": 3979}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_SMASH", "CLOSE_COMBAT", "STONE_EDGE"], "score": 27.3}]