[{"speciesId": "zekrom", "speciesName": "Zekrom", "rating": 774, "matchups": [{"opponent": "ho_oh", "rating": 850, "opRating": 149}, {"opponent": "metagross", "rating": 834}, {"opponent": "mewtwo", "rating": 567}, {"opponent": "giratina_origin", "rating": 567}, {"opponent": "lugia", "rating": 516}], "counters": [{"opponent": "gyarados", "rating": 363}, {"opponent": "dialga", "rating": 369}, {"opponent": "zacian_hero", "rating": 439}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47055}, {"moveId": "CHARGE_BEAM", "uses": 29445}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34902}, {"moveId": "OUTRAGE", "uses": 16899}, {"moveId": "FLASH_CANNON", "uses": 5849}, {"moveId": "CRUNCH", "uses": 18870}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "WILD_CHARGE"], "score": 100}, {"speciesId": "latias", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 632, "matchups": [{"opponent": "mewtwo_armored", "rating": 723, "opRating": 276}, {"opponent": "electivire_shadow", "rating": 712, "opRating": 287}, {"opponent": "haxorus", "rating": 659, "opRating": 340}, {"opponent": "kyogre", "rating": 630, "opRating": 369}, {"opponent": "ho_oh", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 317}, {"opponent": "giratina_origin", "rating": 378}, {"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "dragonite", "rating": 401}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 6661}, {"moveId": "DRAGON_BREATH", "uses": 46257}, {"moveId": "CHARM", "uses": 23594}], "chargedMoves": [{"moveId": "THUNDER", "uses": 12167}, {"moveId": "RETURN", "uses": 8025}, {"moveId": "PSYCHIC", "uses": 14566}, {"moveId": "OUTRAGE", "uses": 19625}, {"moveId": "MIST_BALL", "uses": 21903}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 100}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 620, "matchups": [{"opponent": "zarude", "rating": 926, "opRating": 73}, {"opponent": "metagross", "rating": 726}, {"opponent": "latios_shadow", "rating": 691, "opRating": 308}, {"opponent": "meloetta_aria", "rating": 668, "opRating": 331}, {"opponent": "mewtwo", "rating": 636}], "counters": [{"opponent": "zacian_hero", "rating": 291}, {"opponent": "garcho<PERSON>", "rating": 340}, {"opponent": "gyarados", "rating": 378}, {"opponent": "dialga", "rating": 388}, {"opponent": "lugia", "rating": 485}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38899}, {"moveId": "BULLET_PUNCH", "uses": 37601}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 22596}, {"moveId": "RETURN", "uses": 8426}, {"moveId": "NIGHT_SLASH", "uses": 30570}, {"moveId": "IRON_HEAD", "uses": 15051}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 100}, {"speciesId": "latias_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 653, "matchups": [{"opponent": "haxorus", "rating": 851, "opRating": 148}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 793, "opRating": 206}, {"opponent": "machamp_shadow", "rating": 770, "opRating": 229}, {"opponent": "heracross", "rating": 750, "opRating": 250}, {"opponent": "kyogre", "rating": 572, "opRating": 427}], "counters": [{"opponent": "mewtwo", "rating": 328}, {"opponent": "dialga", "rating": 342}, {"opponent": "giratina_origin", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 6260}, {"moveId": "DRAGON_BREATH", "uses": 47348}, {"moveId": "CHARM", "uses": 22876}], "chargedMoves": [{"moveId": "THUNDER", "uses": 13738}, {"moveId": "PSYCHIC", "uses": 16223}, {"moveId": "OUTRAGE", "uses": 22055}, {"moveId": "MIST_BALL", "uses": 24405}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 100}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 504, "matchups": [{"opponent": "mew", "rating": 907, "opRating": 92}, {"opponent": "gengar", "rating": 758, "opRating": 241}, {"opponent": "giratina_origin", "rating": 713}, {"opponent": "giratina_altered", "rating": 536, "opRating": 463}, {"opponent": "mewtwo", "rating": 511}], "counters": [{"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "metagross", "rating": 264}, {"opponent": "dialga", "rating": 309}, {"opponent": "excadrill", "rating": 381}, {"opponent": "lugia", "rating": 442}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 8612}, {"moveId": "LICK", "uses": 35892}, {"moveId": "ICE_FANG", "uses": 32032}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35401}, {"moveId": "PLAY_ROUGH", "uses": 13436}, {"moveId": "CRUNCH", "uses": 27740}]}, "moveset": ["LICK", "WILD_CHARGE", "CRUNCH"], "score": 100}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 517, "matchups": [{"opponent": "zarude", "rating": 887, "opRating": 112}, {"opponent": "rhyperior", "rating": 875, "opRating": 125}, {"opponent": "swampert", "rating": 774}, {"opponent": "swampert_shadow", "rating": 746, "opRating": 253}, {"opponent": "kyogre", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "metagross", "rating": 357}, {"opponent": "garcho<PERSON>", "rating": 363}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 31679}, {"moveId": "BUG_BITE", "uses": 44821}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 21423}, {"moveId": "SILVER_WIND", "uses": 7735}, {"moveId": "LEAF_STORM", "uses": 5158}, {"moveId": "LEAF_BLADE", "uses": 42051}]}, "moveset": ["BUG_BITE", "LEAF_BLADE", "X_SCISSOR"], "score": 100}, {"speciesId": "scyther_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 513, "matchups": [{"opponent": "zarude", "rating": 901, "opRating": 98}, {"opponent": "tangrowth_shadow", "rating": 891, "opRating": 108}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "mewtwo_shadow", "rating": 805, "opRating": 194}, {"opponent": "metagross", "rating": 595}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "lugia", "rating": 354}, {"opponent": "mewtwo", "rating": 408}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 15400}, {"moveId": "FURY_CUTTER", "uses": 35967}, {"moveId": "AIR_SLASH", "uses": 25058}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 20071}, {"moveId": "NIGHT_SLASH", "uses": 28331}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BUG_BUZZ", "uses": 13072}, {"moveId": "AERIAL_ACE", "uses": 14974}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 100}, {"speciesId": "scyther", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 493, "matchups": [{"opponent": "zarude", "rating": 882, "opRating": 117}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 882, "opRating": 117}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 882, "opRating": 117}, {"opponent": "celebi", "rating": 742, "opRating": 257}, {"opponent": "virizion", "rating": 633, "opRating": 366}], "counters": [{"opponent": "lugia", "rating": 321}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "dialga", "rating": 350}, {"opponent": "metagross", "rating": 456}, {"opponent": "garcho<PERSON>", "rating": 467}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 15643}, {"moveId": "FURY_CUTTER", "uses": 35744}, {"moveId": "AIR_SLASH", "uses": 25142}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18293}, {"moveId": "RETURN", "uses": 7044}, {"moveId": "NIGHT_SLASH", "uses": 25839}, {"moveId": "BUG_BUZZ", "uses": 11887}, {"moveId": "AERIAL_ACE", "uses": 13369}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 100}, {"speciesId": "pheromosa", "speciesName": "Pheromosa", "rating": 385, "matchups": [{"opponent": "krookodile", "rating": 854, "opRating": 145}, {"opponent": "registeel", "rating": 848, "opRating": 151}, {"opponent": "weavile_shadow", "rating": 848, "opRating": 151}, {"opponent": "zarude", "rating": 746, "opRating": 253}, {"opponent": "yveltal", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 245}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "lugia", "rating": 328}, {"opponent": "mewtwo", "rating": 367}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 16270}, {"moveId": "BUG_BITE", "uses": 60230}], "chargedMoves": [{"moveId": "LUNGE", "uses": 20642}, {"moveId": "FOCUS_BLAST", "uses": 5420}, {"moveId": "CLOSE_COMBAT", "uses": 37070}, {"moveId": "BUG_BUZZ", "uses": 13405}]}, "moveset": ["BUG_BITE", "CLOSE_COMBAT", "LUNGE"], "score": 100}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 342, "matchups": [{"opponent": "gengar", "rating": 727, "opRating": 272}, {"opponent": "cofagrigus", "rating": 727, "opRating": 272}, {"opponent": "gourgeist_super", "rating": 625, "opRating": 374}, {"opponent": "giratina_origin", "rating": 601}, {"opponent": "trevenant", "rating": 579, "opRating": 420}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 245}, {"opponent": "dialga", "rating": 288}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "metagross", "rating": 351}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 47287}, {"moveId": "ASTONISH", "uses": 29213}], "chargedMoves": [{"moveId": "RETURN", "uses": 14676}, {"moveId": "FIRE_BLAST", "uses": 9753}, {"moveId": "DISARMING_VOICE", "uses": 24146}, {"moveId": "CRUNCH", "uses": 28024}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 100}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 359, "matchups": [{"opponent": "gengar", "rating": 891, "opRating": 108}, {"opponent": "gourgeist_average", "rating": 768, "opRating": 231}, {"opponent": "trevenant", "rating": 683, "opRating": 316}, {"opponent": "gourgeist_super", "rating": 586, "opRating": 413}, {"opponent": "giratina_origin", "rating": 504}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "dialga", "rating": 214}, {"opponent": "gyarados", "rating": 260}, {"opponent": "mewtwo", "rating": 286}, {"opponent": "metagross", "rating": 363}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 45556}, {"moveId": "ASTONISH", "uses": 30944}], "chargedMoves": [{"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_BLAST", "uses": 11976}, {"moveId": "DISARMING_VOICE", "uses": 30056}, {"moveId": "CRUNCH", "uses": 34323}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 100}, {"speciesId": "feraligatr", "speciesName": "Feraligatr", "rating": 589, "matchups": [{"opponent": "ho_oh", "rating": 856, "opRating": 143}, {"opponent": "ho_oh_shadow", "rating": 842, "opRating": 157}, {"opponent": "excadrill", "rating": 617}, {"opponent": "zacian_hero", "rating": 536}, {"opponent": "metagross", "rating": 511}], "counters": [{"opponent": "lugia", "rating": 307}, {"opponent": "dialga", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 370}, {"opponent": "swampert", "rating": 445}, {"opponent": "mewtwo", "rating": 450}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22937}, {"moveId": "WATERFALL", "uses": 21471}, {"moveId": "ICE_FANG", "uses": 20237}, {"moveId": "BITE", "uses": 11880}], "chargedMoves": [{"moveId": "RETURN", "uses": 6365}, {"moveId": "ICE_BEAM", "uses": 14296}, {"moveId": "HYDRO_PUMP", "uses": 3693}, {"moveId": "HYDRO_CANNON", "uses": 34403}, {"moveId": "CRUNCH", "uses": 17781}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 99.6}, {"speciesId": "feraligatr_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 582, "matchups": [{"opponent": "ho_oh", "rating": 842, "opRating": 157}, {"opponent": "entei", "rating": 803, "opRating": 196}, {"opponent": "ho_oh_shadow", "rating": 800, "opRating": 199}, {"opponent": "metagross", "rating": 772}, {"opponent": "excadrill", "rating": 570}], "counters": [{"opponent": "dialga", "rating": 160}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "gyarados", "rating": 280}, {"opponent": "swampert", "rating": 487}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22462}, {"moveId": "WATERFALL", "uses": 21921}, {"moveId": "ICE_FANG", "uses": 20329}, {"moveId": "BITE", "uses": 11894}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 15639}, {"moveId": "HYDRO_PUMP", "uses": 4020}, {"moveId": "HYDRO_CANNON", "uses": 37394}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CRUNCH", "uses": 19331}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 99.6}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 357, "matchups": [{"opponent": "gengar", "rating": 744, "opRating": 255}, {"opponent": "gourgeist_average", "rating": 674, "opRating": 325}, {"opponent": "aggron_shadow", "rating": 626, "opRating": 373}, {"opponent": "mr_rime", "rating": 541, "opRating": 458}, {"opponent": "gourgeist_super", "rating": 521, "opRating": 478}], "counters": [{"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "dialga", "rating": 241}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "giratina_origin", "rating": 450}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 36993}, {"moveId": "LICK", "uses": 39507}], "chargedMoves": [{"moveId": "GUNK_SHOT", "uses": 8774}, {"moveId": "BULLDOZE", "uses": 14677}, {"moveId": "BODY_SLAM", "uses": 53109}]}, "moveset": ["LICK", "BODY_SLAM", "BULLDOZE"], "score": 99.5}, {"speciesId": "simipour", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 398, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "heatran", "rating": 777, "opRating": 222}, {"opponent": "chandelure", "rating": 768, "opRating": 231}, {"opponent": "entei", "rating": 759, "opRating": 240}, {"opponent": "ho_oh_shadow", "rating": 753, "opRating": 246}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 264}, {"opponent": "metagross", "rating": 459}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 48640}, {"moveId": "BITE", "uses": 27860}], "chargedMoves": [{"moveId": "SURF", "uses": 39485}, {"moveId": "HYDRO_PUMP", "uses": 6311}, {"moveId": "CRUNCH", "uses": 30674}]}, "moveset": ["WATER_GUN", "SURF", "CRUNCH"], "score": 97.6}, {"speciesId": "gorebyss", "speciesName": "<PERSON><PERSON>", "rating": 334, "matchups": [{"opponent": "golem", "rating": 851, "opRating": 148}, {"opponent": "chandelure", "rating": 751, "opRating": 248}, {"opponent": "rhydon", "rating": 629, "opRating": 370}, {"opponent": "cryogonal", "rating": 618, "opRating": 381}, {"opponent": "typhlosion", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "metagross", "rating": 273}, {"opponent": "excadrill", "rating": 348}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40691}, {"moveId": "CONFUSION", "uses": 35809}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 23935}, {"moveId": "PSYCHIC", "uses": 32073}, {"moveId": "DRAINING_KISS", "uses": 20477}]}, "moveset": ["WATER_GUN", "PSYCHIC", "WATER_PULSE"], "score": 97.5}, {"speciesId": "hydreigon", "speciesName": "Hydreigon", "rating": 747, "matchups": [{"opponent": "giratina_origin", "rating": 791}, {"opponent": "mewtwo", "rating": 759}, {"opponent": "metagross", "rating": 738}, {"opponent": "swampert", "rating": 621}, {"opponent": "excadrill", "rating": 619}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "gyarados", "rating": 350}, {"opponent": "lugia", "rating": 378}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 50352}, {"moveId": "BITE", "uses": 26148}], "chargedMoves": [{"moveId": "FLASH_CANNON", "uses": 8710}, {"moveId": "DRAGON_PULSE", "uses": 15077}, {"moveId": "DARK_PULSE", "uses": 12390}, {"moveId": "BRUTAL_SWING", "uses": 40276}]}, "moveset": ["DRAGON_BREATH", "BRUTAL_SWING", "FLASH_CANNON"], "score": 97.2}, {"speciesId": "zarude", "speciesName": "Zarude", "rating": 718, "matchups": [{"opponent": "mewtwo", "rating": 800}, {"opponent": "giratina_origin", "rating": 692}, {"opponent": "garcho<PERSON>", "rating": 658}, {"opponent": "gyarados", "rating": 593}, {"opponent": "zacian_hero", "rating": 569}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 258}, {"opponent": "dialga", "rating": 288}, {"opponent": "zekrom", "rating": 342}, {"opponent": "lugia", "rating": 473}, {"opponent": "metagross", "rating": 476}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 49486}, {"moveId": "BITE", "uses": 27014}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 29003}, {"moveId": "ENERGY_BALL", "uses": 10566}, {"moveId": "DARK_PULSE", "uses": 37164}]}, "moveset": ["VINE_WHIP", "DARK_PULSE", "POWER_WHIP"], "score": 96.4}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 715, "matchups": [{"opponent": "metagross", "rating": 816}, {"opponent": "dialga", "rating": 746}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 695}, {"opponent": "lugia", "rating": 613}, {"opponent": "gyarados", "rating": 525}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "dragonite", "rating": 329}, {"opponent": "mewtwo", "rating": 382}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 20145}, {"moveId": "MUD_SHOT", "uses": 38912}, {"moveId": "METAL_CLAW", "uses": 17439}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 22231}, {"moveId": "IRON_HEAD", "uses": 13982}, {"moveId": "EARTHQUAKE", "uses": 8238}, {"moveId": "DRILL_RUN", "uses": 32018}]}, "moveset": ["MUD_SHOT", "DRILL_RUN", "ROCK_SLIDE"], "score": 96.4}, {"speciesId": "haxorus", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 735, "matchups": [{"opponent": "excadrill", "rating": 876}, {"opponent": "magnezone_shadow", "rating": 753, "opRating": 246}, {"opponent": "kyogre", "rating": 725, "opRating": 274}, {"opponent": "snorlax", "rating": 560, "opRating": 439}, {"opponent": "swampert", "rating": 524}], "counters": [{"opponent": "giratina_origin", "rating": 282}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 438}, {"opponent": "dialga", "rating": 470}, {"opponent": "metagross", "rating": 473}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 38217}, {"moveId": "COUNTER", "uses": 38283}], "chargedMoves": [{"moveId": "SURF", "uses": 17705}, {"moveId": "NIGHT_SLASH", "uses": 22516}, {"moveId": "EARTHQUAKE", "uses": 10636}, {"moveId": "DRAGON_CLAW", "uses": 25655}]}, "moveset": ["COUNTER", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 96.4}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 624, "matchups": [{"opponent": "giratina_origin", "rating": 789}, {"opponent": "mewtwo", "rating": 676}, {"opponent": "excadrill", "rating": 636}, {"opponent": "dialga", "rating": 605}, {"opponent": "metagross", "rating": 563}], "counters": [{"opponent": "lugia", "rating": 216}, {"opponent": "gyarados", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 342}, {"opponent": "grou<PERSON>", "rating": 413}, {"opponent": "swampert", "rating": 460}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 31364}, {"moveId": "COUNTER", "uses": 45136}], "chargedMoves": [{"moveId": "OBSTRUCT", "uses": 912}, {"moveId": "NIGHT_SLASH", "uses": 37991}, {"moveId": "HYPER_BEAM", "uses": 8609}, {"moveId": "GUNK_SHOT", "uses": 6638}, {"moveId": "CROSS_CHOP", "uses": 22277}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "OBSTRUCT"], "score": 96.4}, {"speciesId": "beartic", "speciesName": "Bear<PERSON>", "rating": 608, "matchups": [{"opponent": "garcho<PERSON>", "rating": 894}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 868, "opRating": 131}, {"opponent": "dragonite", "rating": 672}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "excadrill", "rating": 525}], "counters": [{"opponent": "mewtwo", "rating": 302}, {"opponent": "gyarados", "rating": 332}, {"opponent": "giratina_origin", "rating": 362}, {"opponent": "lugia", "rating": 433}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52547}, {"moveId": "CHARM", "uses": 23953}], "chargedMoves": [{"moveId": "SURF", "uses": 27987}, {"moveId": "PLAY_ROUGH", "uses": 12290}, {"moveId": "ICE_PUNCH", "uses": 36206}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "SURF"], "score": 96.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 597, "matchups": [{"opponent": "swampert", "rating": 665}, {"opponent": "snorlax", "rating": 659, "opRating": 340}, {"opponent": "excadrill", "rating": 639}, {"opponent": "dialga", "rating": 601}, {"opponent": "zekrom", "rating": 516}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "gyarados", "rating": 195}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46977}, {"moveId": "BULLET_PUNCH", "uses": 29523}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21777}, {"moveId": "RETURN", "uses": 7399}, {"moveId": "HEAVY_SLAM", "uses": 10205}, {"moveId": "DYNAMIC_PUNCH", "uses": 7862}, {"moveId": "CLOSE_COMBAT", "uses": 29273}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "HEAVY_SLAM"], "score": 96.4}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "ho_oh", "rating": 691, "opRating": 308}, {"opponent": "zap<PERSON>_shadow", "rating": 659, "opRating": 340}, {"opponent": "lugia_shadow", "rating": 648, "opRating": 351}, {"opponent": "zapdos", "rating": 642, "opRating": 357}, {"opponent": "kyogre", "rating": 555, "opRating": 444}], "counters": [{"opponent": "dialga", "rating": 274}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "gyarados", "rating": 373}, {"opponent": "metagross", "rating": 444}, {"opponent": "lugia", "rating": 452}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11895}, {"moveId": "SNARL", "uses": 9632}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3219}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3030}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3506}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2856}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2702}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4383}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4068}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2880}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3746}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3254}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3502}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3611}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4175}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3281}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3643}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2984}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34303}, {"moveId": "PSYCHIC_FANGS", "uses": 17331}, {"moveId": "HYPER_BEAM", "uses": 5296}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CRUNCH", "uses": 19565}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 96.4}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 581, "matchups": [{"opponent": "zap<PERSON>_shadow", "rating": 642, "opRating": 357}, {"opponent": "kyogre", "rating": 625, "opRating": 375}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 578}, {"opponent": "yveltal", "rating": 552, "opRating": 447}, {"opponent": "gyarados", "rating": 549}], "counters": [{"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "dialga", "rating": 252}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "lugia", "rating": 361}, {"opponent": "metagross", "rating": 421}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 10965}, {"moveId": "SNARL", "uses": 9101}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3266}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3052}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3621}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2928}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2749}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4572}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4061}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2960}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3836}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3302}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3616}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3586}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4327}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3452}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3740}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2995}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32794}, {"moveId": "RETURN", "uses": 6285}, {"moveId": "PSYCHIC_FANGS", "uses": 16249}, {"moveId": "HYPER_BEAM", "uses": 2493}, {"moveId": "CRUNCH", "uses": 18669}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 96.4}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 532, "matchups": [{"opponent": "metagross_shadow", "rating": 710, "opRating": 289}, {"opponent": "articuno_galarian", "rating": 617, "opRating": 382}, {"opponent": "kyurem", "rating": 547, "opRating": 452}, {"opponent": "latios_shadow", "rating": 528, "opRating": 471}, {"opponent": "lugia_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "dialga", "rating": 304}, {"opponent": "mewtwo", "rating": 460}, {"opponent": "metagross", "rating": 465}, {"opponent": "lugia", "rating": 469}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 53790}, {"moveId": "METAL_CLAW", "uses": 22710}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 10224}, {"moveId": "IRON_HEAD", "uses": 14576}, {"moveId": "FOUL_PLAY", "uses": 20456}, {"moveId": "CLOSE_COMBAT", "uses": 31202}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "FOUL_PLAY"], "score": 96.4}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 493, "matchups": [{"opponent": "xurkitree", "rating": 809, "opRating": 190}, {"opponent": "machamp_shadow", "rating": 741, "opRating": 258}, {"opponent": "metagross", "rating": 694}, {"opponent": "magnezone_shadow", "rating": 651, "opRating": 348}, {"opponent": "cobalion", "rating": 600, "opRating": 399}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "dialga", "rating": 355}, {"opponent": "lugia", "rating": 392}, {"opponent": "zacian_hero", "rating": 407}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 60471}, {"moveId": "ASTONISH", "uses": 16029}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 41466}, {"moveId": "SAND_TOMB", "uses": 17451}, {"moveId": "ROCK_TOMB", "uses": 17591}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "ROCK_TOMB"], "score": 96.4}, {"speciesId": "magneton_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 506, "matchups": [{"opponent": "gyarados", "rating": 748}, {"opponent": "gyarado<PERSON>_shadow", "rating": 665, "opRating": 334}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 535, "opRating": 464}, {"opponent": "lugia", "rating": 527}, {"opponent": "sylveon", "rating": 523, "opRating": 476}], "counters": [{"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "dialga", "rating": 323}, {"opponent": "dragonite", "rating": 327}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 31039}, {"moveId": "SPARK", "uses": 28137}, {"moveId": "CHARGE_BEAM", "uses": 17361}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 10577}, {"moveId": "MAGNET_BOMB", "uses": 32058}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 6798}, {"moveId": "DISCHARGE", "uses": 27056}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 96.4}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 511, "matchups": [{"opponent": "gyarados", "rating": 748}, {"opponent": "gyarado<PERSON>_shadow", "rating": 748, "opRating": 251}, {"opponent": "sylveon", "rating": 602, "opRating": 397}, {"opponent": "lugia", "rating": 562}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 562}], "counters": [{"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "dialga", "rating": 279}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "metagross", "rating": 424}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30716}, {"moveId": "SPARK", "uses": 28191}, {"moveId": "CHARGE_BEAM", "uses": 17583}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 9079}, {"moveId": "RETURN", "uses": 10734}, {"moveId": "MAGNET_BOMB", "uses": 27312}, {"moveId": "FLASH_CANNON", "uses": 5828}, {"moveId": "DISCHARGE", "uses": 23734}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 96.4}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 483, "matchups": [{"opponent": "pangoro", "rating": 906, "opRating": 93}, {"opponent": "regirock", "rating": 730, "opRating": 269}, {"opponent": "melmetal", "rating": 696, "opRating": 303}, {"opponent": "yveltal", "rating": 642, "opRating": 357}, {"opponent": "snorlax", "rating": 556, "opRating": 443}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "metagross", "rating": 264}, {"opponent": "dialga", "rating": 355}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 34316}, {"moveId": "COUNTER", "uses": 42184}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 24634}, {"moveId": "MUD_BOMB", "uses": 23008}, {"moveId": "DYNAMIC_PUNCH", "uses": 28886}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "SLUDGE_BOMB"], "score": 96.4}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 450, "matchups": [{"opponent": "registeel", "rating": 890, "opRating": 109}, {"opponent": "darkrai", "rating": 838, "opRating": 161}, {"opponent": "magnezone", "rating": 672, "opRating": 327}, {"opponent": "swampert", "rating": 654}, {"opponent": "excadrill", "rating": 644}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "metagross", "rating": 308}, {"opponent": "dialga", "rating": 394}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44221}, {"moveId": "BULLET_SEED", "uses": 32279}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 14204}, {"moveId": "SEED_BOMB", "uses": 17641}, {"moveId": "GRASS_KNOT", "uses": 19205}, {"moveId": "DYNAMIC_PUNCH", "uses": 25552}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 96.4}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 497, "matchups": [{"opponent": "genesect_shock", "rating": 902, "opRating": 97}, {"opponent": "genesect_douse", "rating": 902, "opRating": 97}, {"opponent": "sylveon", "rating": 600, "opRating": 400}, {"opponent": "metagross", "rating": 563}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 563}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "dialga", "rating": 288}, {"opponent": "excadrill", "rating": 397}, {"opponent": "giratina_origin", "rating": 476}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 15938}, {"moveId": "FIRE_FANG", "uses": 60562}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 10301}, {"moveId": "OVERHEAT", "uses": 19304}, {"moveId": "FLAME_CHARGE", "uses": 25095}, {"moveId": "DARK_PULSE", "uses": 21832}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "DARK_PULSE"], "score": 96.4}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 512, "matchups": [{"opponent": "moltres_galarian", "rating": 777, "opRating": 222}, {"opponent": "ho_oh", "rating": 692, "opRating": 307}, {"opponent": "zap<PERSON>_shadow", "rating": 655, "opRating": 344}, {"opponent": "kyogre", "rating": 557, "opRating": 442}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509}], "counters": [{"opponent": "lugia", "rating": 211}, {"opponent": "gyarados", "rating": 280}, {"opponent": "excadrill", "rating": 302}, {"opponent": "metagross", "rating": 316}, {"opponent": "dialga", "rating": 355}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 63376}, {"moveId": "LOW_KICK", "uses": 13124}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 45107}, {"moveId": "FLAME_CHARGE", "uses": 21810}, {"moveId": "DISCHARGE", "uses": 9661}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 96.4}, {"speciesId": "seaking", "speciesName": "Seaking", "rating": 445, "matchups": [{"opponent": "exeggutor_alolan", "rating": 613, "opRating": 386}, {"opponent": "tapu_koko", "rating": 604, "opRating": 395}, {"opponent": "typhlosion", "rating": 575, "opRating": 424}, {"opponent": "darmanitan_standard", "rating": 563, "opRating": 436}, {"opponent": "salamence_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "garcho<PERSON>", "rating": 316}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "gyarados", "rating": 358}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 29341}, {"moveId": "POISON_JAB", "uses": 33521}, {"moveId": "PECK", "uses": 13643}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 6844}, {"moveId": "MEGAHORN", "uses": 14843}, {"moveId": "ICY_WIND", "uses": 23341}, {"moveId": "ICE_BEAM", "uses": 7526}, {"moveId": "DRILL_RUN", "uses": 23993}]}, "moveset": ["POISON_JAB", "DRILL_RUN", "ICY_WIND"], "score": 96.4}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 446, "matchups": [{"opponent": "florges", "rating": 702, "opRating": 297}, {"opponent": "sylveon", "rating": 679}, {"opponent": "nihilego", "rating": 658, "opRating": 341}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 647}, {"opponent": "zacian_hero", "rating": 534}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dragonite", "rating": 194}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 25914}, {"moveId": "IRON_TAIL", "uses": 3926}, {"moveId": "FURY_CUTTER", "uses": 21409}, {"moveId": "DOUBLE_KICK", "uses": 25253}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 13215}, {"moveId": "SAND_TOMB", "uses": 8635}, {"moveId": "RETURN", "uses": 8776}, {"moveId": "MEGAHORN", "uses": 16721}, {"moveId": "EARTH_POWER", "uses": 20307}, {"moveId": "EARTHQUAKE", "uses": 8793}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 96.4}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 452, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 676}, {"opponent": "nihilego", "rating": 595, "opRating": 404}, {"opponent": "sylveon", "rating": 580, "opRating": 419}, {"opponent": "gyarados", "rating": 556}, {"opponent": "lugia", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 208}, {"opponent": "dialga", "rating": 241}, {"opponent": "dragonite", "rating": 242}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "zacian_hero", "rating": 303}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39738}, {"moveId": "ROCK_THROW", "uses": 36762}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 12937}, {"moveId": "ROCK_SLIDE", "uses": 30476}, {"moveId": "RETURN", "uses": 9006}, {"moveId": "MAGNET_BOMB", "uses": 24114}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 96.4}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 450, "matchups": [{"opponent": "moltres", "rating": 771, "opRating": 228}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 658}, {"opponent": "sylveon", "rating": 658, "opRating": 341}, {"opponent": "lugia", "rating": 556}, {"opponent": "gyarados", "rating": 517}], "counters": [{"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "dialga", "rating": 176}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "dragonite", "rating": 311}, {"opponent": "zacian_hero", "rating": 346}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39437}, {"moveId": "ROCK_THROW", "uses": 37063}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 14427}, {"moveId": "ROCK_SLIDE", "uses": 34325}, {"moveId": "MAGNET_BOMB", "uses": 27635}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 96.4}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 418, "matchups": [{"opponent": "tapu_bulu", "rating": 835, "opRating": 164}, {"opponent": "nihilego", "rating": 627, "opRating": 372}, {"opponent": "florges", "rating": 618, "opRating": 381}, {"opponent": "sylveon", "rating": 615, "opRating": 384}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 552}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 250}, {"opponent": "zacian_hero", "rating": 442}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 25987}, {"moveId": "IRON_TAIL", "uses": 3498}, {"moveId": "FURY_CUTTER", "uses": 21128}, {"moveId": "DOUBLE_KICK", "uses": 25830}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 15599}, {"moveId": "SAND_TOMB", "uses": 9430}, {"moveId": "MEGAHORN", "uses": 19147}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTH_POWER", "uses": 22481}, {"moveId": "EARTHQUAKE", "uses": 9685}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 96.4}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 366, "matchups": [{"opponent": "con<PERSON><PERSON><PERSON>", "rating": 700, "opRating": 299}, {"opponent": "gliscor", "rating": 626, "opRating": 373}, {"opponent": "machamp", "rating": 552, "opRating": 447}, {"opponent": "heracross", "rating": 535, "opRating": 464}, {"opponent": "blaziken", "rating": 524, "opRating": 475}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "lugia", "rating": 264}, {"opponent": "dialga", "rating": 277}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "excadrill", "rating": 346}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 6705}, {"moveId": "TACKLE", "uses": 4946}, {"moveId": "QUICK_ATTACK", "uses": 8006}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4261}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3043}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3715}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3864}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2850}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4569}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3943}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3060}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3821}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3342}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3500}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3507}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3482}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3406}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3733}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3010}], "chargedMoves": [{"moveId": "THUNDER", "uses": 13364}, {"moveId": "PSYCHIC", "uses": 17235}, {"moveId": "PSYBEAM", "uses": 3534}, {"moveId": "POWER_GEM", "uses": 8964}, {"moveId": "ICE_BEAM", "uses": 20420}, {"moveId": "HYDRO_PUMP", "uses": 12939}]}, "moveset": ["QUICK_ATTACK", "ICE_BEAM", "PSYCHIC"], "score": 96.4}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 384, "matchups": [{"opponent": "tapu_fini", "rating": 815, "opRating": 184}, {"opponent": "swampert", "rating": 684}, {"opponent": "kyogre", "rating": 664, "opRating": 335}, {"opponent": "excadrill", "rating": 567}, {"opponent": "grou<PERSON>", "rating": 510}], "counters": [{"opponent": "dialga", "rating": 73}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "gyarados", "rating": 157}, {"opponent": "metagross", "rating": 250}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44722}, {"moveId": "INFESTATION", "uses": 31778}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6649}, {"moveId": "SLUDGE_BOMB", "uses": 22198}, {"moveId": "POWER_WHIP", "uses": 15839}, {"moveId": "GRASS_KNOT", "uses": 31624}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 96.4}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 394, "matchups": [{"opponent": "tapu_fini", "rating": 795, "opRating": 204}, {"opponent": "vaporeon", "rating": 795, "opRating": 204}, {"opponent": "swampert", "rating": 755}, {"opponent": "rhyperior", "rating": 755, "opRating": 244}, {"opponent": "swampert_shadow", "rating": 684, "opRating": 315}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dialga", "rating": 152}, {"opponent": "gyarados", "rating": 317}, {"opponent": "excadrill", "rating": 437}, {"opponent": "grou<PERSON>", "rating": 480}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44252}, {"moveId": "INFESTATION", "uses": 32248}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 5685}, {"moveId": "SLUDGE_BOMB", "uses": 17770}, {"moveId": "RETURN", "uses": 12435}, {"moveId": "POWER_WHIP", "uses": 13499}, {"moveId": "GRASS_KNOT", "uses": 26994}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 96.4}, {"speciesId": "altaria", "speciesName": "Altaria", "rating": 492, "matchups": [{"opponent": "chesnaught", "rating": 801, "opRating": 198}, {"opponent": "swampert_shadow", "rating": 594, "opRating": 405}, {"opponent": "grou<PERSON>", "rating": 588}, {"opponent": "sneasler", "rating": 579, "opRating": 420}, {"opponent": "swampert", "rating": 554}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "gyarados", "rating": 219}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 255}], "moves": {"fastMoves": [{"moveId": "PECK", "uses": 23812}, {"moveId": "DRAGON_BREATH", "uses": 52688}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 34980}, {"moveId": "MOONBLAST", "uses": 19789}, {"moveId": "DRAGON_PULSE", "uses": 16745}, {"moveId": "DAZZLING_GLEAM", "uses": 5109}]}, "moveset": ["DRAGON_BREATH", "SKY_ATTACK", "MOONBLAST"], "score": 96.2}, {"speciesId": "gyarados", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 743, "matchups": [{"opponent": "garcho<PERSON>", "rating": 832}, {"opponent": "giratina_origin", "rating": 688}, {"opponent": "metagross", "rating": 621}, {"opponent": "mewtwo", "rating": 577}, {"opponent": "lugia", "rating": 515}], "counters": [{"opponent": "dialga", "rating": 418}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 441}, {"opponent": "zacian_hero", "rating": 453}, {"opponent": "ho_oh", "rating": 460}, {"opponent": "excadrill", "rating": 474}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 20919}, {"moveId": "DRAGON_TAIL", "uses": 21782}, {"moveId": "DRAGON_BREATH", "uses": 22459}, {"moveId": "BITE", "uses": 11360}], "chargedMoves": [{"moveId": "TWISTER", "uses": 5979}, {"moveId": "RETURN", "uses": 6834}, {"moveId": "OUTRAGE", "uses": 11087}, {"moveId": "HYDRO_PUMP", "uses": 4155}, {"moveId": "DRAGON_PULSE", "uses": 3733}, {"moveId": "CRUNCH", "uses": 18449}, {"moveId": "AQUA_TAIL", "uses": 26292}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 95.8}, {"speciesId": "gyarado<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 733, "matchups": [{"opponent": "garcho<PERSON>", "rating": 935}, {"opponent": "grou<PERSON>", "rating": 829}, {"opponent": "metagross", "rating": 817}, {"opponent": "swampert", "rating": 796}, {"opponent": "dialga", "rating": 502}], "counters": [{"opponent": "mewtwo", "rating": 252}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "dragonite", "rating": 385}, {"opponent": "lugia", "rating": 400}, {"opponent": "giratina_origin", "rating": 438}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 20995}, {"moveId": "DRAGON_TAIL", "uses": 22284}, {"moveId": "DRAGON_BREATH", "uses": 22640}, {"moveId": "BITE", "uses": 10562}], "chargedMoves": [{"moveId": "TWISTER", "uses": 6540}, {"moveId": "OUTRAGE", "uses": 12244}, {"moveId": "HYDRO_PUMP", "uses": 4664}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRAGON_PULSE", "uses": 3984}, {"moveId": "CRUNCH", "uses": 20184}, {"moveId": "AQUA_TAIL", "uses": 28830}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 95.8}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 379, "matchups": [{"opponent": "quagsire_shadow", "rating": 826, "opRating": 173}, {"opponent": "honchk<PERSON>_shadow", "rating": 753, "opRating": 246}, {"opponent": "pelipper", "rating": 740, "opRating": 259}, {"opponent": "heliolisk", "rating": 634, "opRating": 365}, {"opponent": "shiftry", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "gyarados", "rating": 324}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 48062}, {"moveId": "ASTONISH", "uses": 28438}], "chargedMoves": [{"moveId": "LOW_SWEEP", "uses": 24296}, {"moveId": "HYPER_BEAM", "uses": 23678}, {"moveId": "FRUSTRATION", "uses": 2}, {"moveId": "AERIAL_ACE", "uses": 28289}]}, "moveset": ["SCRATCH", "AERIAL_ACE", "LOW_SWEEP"], "score": 95.7}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 546, "matchups": [{"opponent": "giratina_origin", "rating": 696}, {"opponent": "mew", "rating": 690, "opRating": 309}, {"opponent": "metagross", "rating": 661}, {"opponent": "giratina_altered", "rating": 545, "opRating": 454}, {"opponent": "dialga", "rating": 516}], "counters": [{"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "gyarados", "rating": 188}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "lugia", "rating": 347}, {"opponent": "swampert", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34817}, {"moveId": "METAL_CLAW", "uses": 10998}, {"moveId": "COUNTER", "uses": 30678}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 17088}, {"moveId": "HYPER_BEAM", "uses": 15432}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 43703}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 95.4}, {"speciesId": "z<PERSON><PERSON><PERSON>_hero", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hero)", "rating": 752, "matchups": [{"opponent": "excadrill", "rating": 713}, {"opponent": "dialga", "rating": 673}, {"opponent": "yveltal", "rating": 647, "opRating": 352}, {"opponent": "swampert", "rating": 606}, {"opponent": "giratina_origin", "rating": 534}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "lugia", "rating": 423}, {"opponent": "metagross", "rating": 430}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 26740}, {"moveId": "QUICK_ATTACK", "uses": 25411}, {"moveId": "METAL_CLAW", "uses": 9490}, {"moveId": "ICE_FANG", "uses": 14898}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 13092}, {"moveId": "IRON_HEAD", "uses": 8855}, {"moveId": "CRUNCH", "uses": 19517}, {"moveId": "CLOSE_COMBAT", "uses": 35181}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 95.2}, {"speciesId": "virizion", "speciesName": "Virizion", "rating": 711, "matchups": [{"opponent": "swampert", "rating": 848}, {"opponent": "excadrill", "rating": 816}, {"opponent": "dialga", "rating": 688}, {"opponent": "garcho<PERSON>", "rating": 635}, {"opponent": "gyarados", "rating": 614}], "counters": [{"opponent": "mewtwo", "rating": 231}, {"opponent": "dragonite", "rating": 263}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "zacian_hero", "rating": 384}, {"opponent": "metagross", "rating": 409}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 2897}, {"moveId": "QUICK_ATTACK", "uses": 32551}, {"moveId": "DOUBLE_KICK", "uses": 41007}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 11893}, {"moveId": "SACRED_SWORD", "uses": 20094}, {"moveId": "LEAF_BLADE", "uses": 26368}, {"moveId": "CLOSE_COMBAT", "uses": 18247}]}, "moveset": ["DOUBLE_KICK", "LEAF_BLADE", "SACRED_SWORD"], "score": 95.2}, {"speciesId": "articuno", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 666, "matchups": [{"opponent": "garcho<PERSON>", "rating": 870}, {"opponent": "dragonite", "rating": 645}, {"opponent": "gyarados", "rating": 551}, {"opponent": "zacian_hero", "rating": 545}, {"opponent": "lugia", "rating": 532}], "counters": [{"opponent": "metagross", "rating": 261}, {"opponent": "dialga", "rating": 328}, {"opponent": "mewtwo", "rating": 403}, {"opponent": "excadrill", "rating": 416}, {"opponent": "giratina_origin", "rating": 490}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 45591}, {"moveId": "FROST_BREATH", "uses": 30909}], "chargedMoves": [{"moveId": "RETURN", "uses": 6863}, {"moveId": "ICY_WIND", "uses": 28473}, {"moveId": "ICE_BEAM", "uses": 9187}, {"moveId": "HURRICANE", "uses": 11580}, {"moveId": "BLIZZARD", "uses": 6414}, {"moveId": "ANCIENT_POWER", "uses": 13980}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 95.2}, {"speciesId": "articuno_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "grou<PERSON>", "rating": 768}, {"opponent": "garcho<PERSON>", "rating": 696}, {"opponent": "giratina_origin", "rating": 580}, {"opponent": "swampert", "rating": 556, "opRating": 443}, {"opponent": "dragonite", "rating": 529}], "counters": [{"opponent": "mewtwo", "rating": 393}, {"opponent": "dialga", "rating": 404}, {"opponent": "lugia", "rating": 419}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "gyarados", "rating": 430}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46559}, {"moveId": "FROST_BREATH", "uses": 29941}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31071}, {"moveId": "ICE_BEAM", "uses": 10011}, {"moveId": "HURRICANE", "uses": 12992}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BLIZZARD", "uses": 7020}, {"moveId": "ANCIENT_POWER", "uses": 15602}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 95.2}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 610, "matchups": [{"opponent": "excadrill", "rating": 607}, {"opponent": "swampert", "rating": 604}, {"opponent": "garcho<PERSON>", "rating": 573}, {"opponent": "mewtwo", "rating": 543}, {"opponent": "giratina_origin", "rating": 516}], "counters": [{"opponent": "zacian_hero", "rating": 323}, {"opponent": "dialga", "rating": 385}, {"opponent": "lugia", "rating": 421}, {"opponent": "gyarados", "rating": 463}, {"opponent": "dragonite", "rating": 470}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47943}, {"moveId": "FROST_BREATH", "uses": 28557}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 5683}, {"moveId": "LAST_RESORT", "uses": 9565}, {"moveId": "ICY_WIND", "uses": 13524}, {"moveId": "ICE_BEAM", "uses": 8742}, {"moveId": "AVALANCHE", "uses": 38911}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 95.2}, {"speciesId": "mr_rime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 556, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 840, "opRating": 159}, {"opponent": "zap<PERSON>_shadow", "rating": 648, "opRating": 351}, {"opponent": "zapdos", "rating": 630, "opRating": 369}, {"opponent": "zekrom", "rating": 627, "opRating": 372}, {"opponent": "garcho<PERSON>", "rating": 578}], "counters": [{"opponent": "mewtwo", "rating": 351}, {"opponent": "dialga", "rating": 361}, {"opponent": "giratina_origin", "rating": 402}, {"opponent": "zacian_hero", "rating": 404}, {"opponent": "dragonite", "rating": 428}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 6648}, {"moveId": "ICE_SHARD", "uses": 38560}, {"moveId": "CONFUSION", "uses": 31268}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 15571}, {"moveId": "PSYBEAM", "uses": 3093}, {"moveId": "ICY_WIND", "uses": 30906}, {"moveId": "ICE_PUNCH", "uses": 26751}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ICE_PUNCH"], "score": 95.2}, {"speciesId": "cloyster_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 508, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 877, "opRating": 122}, {"opponent": "yveltal", "rating": 602, "opRating": 397}, {"opponent": "excadrill", "rating": 547}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 547, "opRating": 452}, {"opponent": "garcho<PERSON>", "rating": 511}], "counters": [{"opponent": "giratina_origin", "rating": 252}, {"opponent": "dialga", "rating": 334}, {"opponent": "gyarados", "rating": 381}, {"opponent": "lugia", "rating": 414}, {"opponent": "dragonite", "rating": 449}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 48338}, {"moveId": "FROST_BREATH", "uses": 28162}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 13926}, {"moveId": "HYDRO_PUMP", "uses": 11232}, {"moveId": "FRUSTRATION", "uses": 2}, {"moveId": "BLIZZARD", "uses": 6187}, {"moveId": "AVALANCHE", "uses": 40008}, {"moveId": "AURORA_BEAM", "uses": 5049}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 95.2}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 590, "matchups": [{"opponent": "moltres", "rating": 846, "opRating": 153}, {"opponent": "moltres_shadow", "rating": 809, "opRating": 190}, {"opponent": "gyarados", "rating": 701}, {"opponent": "gyarado<PERSON>_shadow", "rating": 642, "opRating": 357}, {"opponent": "sylveon", "rating": 526, "opRating": 473}], "counters": [{"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "dialga", "rating": 298}, {"opponent": "giratina_origin", "rating": 338}, {"opponent": "lugia", "rating": 342}, {"opponent": "metagross", "rating": 482}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 14432}, {"moveId": "SNARL", "uses": 30733}, {"moveId": "FIRE_FANG", "uses": 20472}, {"moveId": "BITE", "uses": 10772}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22452}, {"moveId": "PSYCHIC_FANGS", "uses": 14508}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 13272}, {"moveId": "FIRE_BLAST", "uses": 3568}, {"moveId": "CRUNCH", "uses": 16069}, {"moveId": "BULLDOZE", "uses": 6694}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 95.2}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 519, "matchups": [{"opponent": "grou<PERSON>", "rating": 740}, {"opponent": "yveltal", "rating": 661, "opRating": 338}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 618, "opRating": 381}, {"opponent": "zekrom", "rating": 602, "opRating": 397}, {"opponent": "garcho<PERSON>", "rating": 582}], "counters": [{"opponent": "mewtwo", "rating": 335}, {"opponent": "dialga", "rating": 345}, {"opponent": "gyarados", "rating": 371}, {"opponent": "excadrill", "rating": 462}, {"opponent": "giratina_origin", "rating": 464}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47704}, {"moveId": "FROST_BREATH", "uses": 28796}], "chargedMoves": [{"moveId": "RETURN", "uses": 7060}, {"moveId": "ICY_WIND", "uses": 12695}, {"moveId": "HYDRO_PUMP", "uses": 10119}, {"moveId": "BLIZZARD", "uses": 5642}, {"moveId": "AVALANCHE", "uses": 36470}, {"moveId": "AURORA_BEAM", "uses": 4557}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 95.2}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 571, "matchups": [{"opponent": "moltres_shadow", "rating": 846, "opRating": 153}, {"opponent": "chandelure", "rating": 836, "opRating": 163}, {"opponent": "gyarados", "rating": 760}, {"opponent": "gyarado<PERSON>_shadow", "rating": 701, "opRating": 298}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 561, "opRating": 438}], "counters": [{"opponent": "dialga", "rating": 255}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "mewtwo", "rating": 382}, {"opponent": "metagross", "rating": 406}, {"opponent": "lugia", "rating": 478}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 15054}, {"moveId": "SNARL", "uses": 28971}, {"moveId": "FIRE_FANG", "uses": 20610}, {"moveId": "BITE", "uses": 11834}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20993}, {"moveId": "RETURN", "uses": 5108}, {"moveId": "PSYCHIC_FANGS", "uses": 13488}, {"moveId": "FLAMETHROWER", "uses": 12384}, {"moveId": "FIRE_BLAST", "uses": 3339}, {"moveId": "CRUNCH", "uses": 14872}, {"moveId": "BULLDOZE", "uses": 6328}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 95.2}, {"speciesId": "d<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622, "matchups": [{"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "chandelure", "rating": 817, "opRating": 182}, {"opponent": "xurkitree", "rating": 709, "opRating": 290}, {"opponent": "mew", "rating": 643, "opRating": 356}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 610, "opRating": 389}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "metagross", "rating": 319}, {"opponent": "dragonite", "rating": 337}, {"opponent": "giratina_origin", "rating": 446}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 52869}, {"moveId": "BITE", "uses": 23631}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 32831}, {"moveId": "HYPER_BEAM", "uses": 7699}, {"moveId": "DRAGON_CLAW", "uses": 35911}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 95.2}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 586, "matchups": [{"opponent": "moltres", "rating": 907, "opRating": 92}, {"opponent": "ho_oh_shadow", "rating": 894, "opRating": 105}, {"opponent": "gyarados", "rating": 780}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 657}, {"opponent": "ho_oh", "rating": 657, "opRating": 342}], "counters": [{"opponent": "dialga", "rating": 220}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "mewtwo", "rating": 359}, {"opponent": "dragonite", "rating": 377}, {"opponent": "lugia", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41641}, {"moveId": "ROCK_SMASH", "uses": 9751}, {"moveId": "FIRE_FANG", "uses": 25034}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22272}, {"moveId": "ROCK_SLIDE", "uses": 21161}, {"moveId": "FLAMETHROWER", "uses": 15277}, {"moveId": "CRUNCH", "uses": 17820}]}, "moveset": ["SNARL", "WILD_CHARGE", "ROCK_SLIDE"], "score": 95.2}, {"speciesId": "gran<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 515, "matchups": [{"opponent": "obstagoon", "rating": 779, "opRating": 220}, {"opponent": "ma<PERSON><PERSON>", "rating": 755, "opRating": 244}, {"opponent": "dialga", "rating": 717}, {"opponent": "kyurem", "rating": 634, "opRating": 365}, {"opponent": "latios_shadow", "rating": 602, "opRating": 397}], "counters": [{"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "giratina_origin", "rating": 326}, {"opponent": "lugia", "rating": 440}, {"opponent": "dragonite", "rating": 462}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37189}, {"moveId": "CHARM", "uses": 25966}, {"moveId": "BITE", "uses": 13370}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 16577}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CRUNCH", "uses": 25748}, {"moveId": "CLOSE_COMBAT", "uses": 34083}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 95.2}, {"speciesId": "relicanth", "speciesName": "Relicanth", "rating": 548, "matchups": [{"opponent": "ho_oh", "rating": 922, "opRating": 77}, {"opponent": "ho_oh_shadow", "rating": 910, "opRating": 89}, {"opponent": "ma<PERSON><PERSON>", "rating": 885, "opRating": 114}, {"opponent": "entei", "rating": 748, "opRating": 251}, {"opponent": "excadrill", "rating": 514}], "counters": [{"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "dialga", "rating": 241}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "gyarados", "rating": 358}, {"opponent": "zacian_hero", "rating": 361}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 10289}, {"moveId": "WATER_GUN", "uses": 66211}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 6320}, {"moveId": "AQUA_TAIL", "uses": 40571}, {"moveId": "ANCIENT_POWER", "uses": 29641}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "ANCIENT_POWER"], "score": 95.2}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 525, "matchups": [{"opponent": "swampert", "rating": 939}, {"opponent": "swampert_shadow", "rating": 910, "opRating": 89}, {"opponent": "excadrill", "rating": 633}, {"opponent": "grou<PERSON>", "rating": 579}, {"opponent": "kyogre", "rating": 519, "opRating": 480}], "counters": [{"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "dialga", "rating": 247}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "gyarados", "rating": 286}, {"opponent": "metagross", "rating": 287}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35958}, {"moveId": "BULLET_SEED", "uses": 40542}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30194}, {"moveId": "FRENZY_PLANT", "uses": 11194}, {"moveId": "EARTHQUAKE", "uses": 10022}, {"moveId": "DRAGON_CLAW", "uses": 17084}, {"moveId": "AERIAL_ACE", "uses": 8024}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DRAGON_CLAW"], "score": 95.2}, {"speciesId": "gran<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 489, "matchups": [{"opponent": "lucario", "rating": 747, "opRating": 252}, {"opponent": "kyurem", "rating": 647, "opRating": 352}, {"opponent": "goodra", "rating": 518, "opRating": 481}, {"opponent": "excadrill", "rating": 510}, {"opponent": "palkia", "rating": 508, "opRating": 491}], "counters": [{"opponent": "mewtwo", "rating": 330}, {"opponent": "lugia", "rating": 359}, {"opponent": "gyarados", "rating": 384}, {"opponent": "dragonite", "rating": 390}, {"opponent": "dialga", "rating": 407}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 35565}, {"moveId": "CHARM", "uses": 26936}, {"moveId": "BITE", "uses": 13930}], "chargedMoves": [{"moveId": "RETURN", "uses": 8452}, {"moveId": "PLAY_ROUGH", "uses": 14507}, {"moveId": "CRUNCH", "uses": 22900}, {"moveId": "CLOSE_COMBAT", "uses": 30804}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 95.2}, {"speciesId": "tornadus_incarnate", "speciesName": "Tornadus (Incarnate)", "rating": 469, "matchups": [{"opponent": "heracross", "rating": 820, "opRating": 179}, {"opponent": "buzzwole", "rating": 767, "opRating": 232}, {"opponent": "virizion", "rating": 761, "opRating": 238}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 617, "opRating": 382}, {"opponent": "grou<PERSON>", "rating": 561}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "gyarados", "rating": 234}, {"opponent": "garcho<PERSON>", "rating": 284}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 28716}, {"moveId": "AIR_SLASH", "uses": 47784}], "chargedMoves": [{"moveId": "HYPER_BEAM", "uses": 9201}, {"moveId": "HURRICANE", "uses": 20322}, {"moveId": "GRASS_KNOT", "uses": 21792}, {"moveId": "DARK_PULSE", "uses": 25203}]}, "moveset": ["AIR_SLASH", "DARK_PULSE", "GRASS_KNOT"], "score": 95.2}, {"speciesId": "jellicent", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 451, "matchups": [{"opponent": "tapu_lele", "rating": 684, "opRating": 315}, {"opponent": "landorus_incarnate", "rating": 674, "opRating": 325}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 592, "opRating": 407}, {"opponent": "heracross", "rating": 579, "opRating": 420}, {"opponent": "genesect", "rating": 562, "opRating": 437}], "counters": [{"opponent": "dialga", "rating": 288}, {"opponent": "lugia", "rating": 316}, {"opponent": "garcho<PERSON>", "rating": 420}, {"opponent": "metagross", "rating": 473}, {"opponent": "zacian_hero", "rating": 494}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 41465}, {"moveId": "BUBBLE", "uses": 35035}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37806}, {"moveId": "ICE_BEAM", "uses": 25801}, {"moveId": "BUBBLE_BEAM", "uses": 12932}]}, "moveset": ["HEX", "SHADOW_BALL", "ICE_BEAM"], "score": 95.2}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 441, "matchups": [{"opponent": "rhyperior", "rating": 930, "opRating": 69}, {"opponent": "swampert", "rating": 850}, {"opponent": "swampert_shadow", "rating": 830, "opRating": 169}, {"opponent": "garcho<PERSON>", "rating": 624}, {"opponent": "kyogre", "rating": 549, "opRating": 450}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "gyarados", "rating": 286}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "grou<PERSON>", "rating": 426}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 1358}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4348}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3981}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4704}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3839}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3647}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5709}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5059}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 6261}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4972}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 5636}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4522}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4591}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4466}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4396}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4900}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3937}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 8438}, {"moveId": "SEED_FLARE", "uses": 12214}, {"moveId": "GRASS_KNOT", "uses": 41202}, {"moveId": "ENERGY_BALL", "uses": 14535}]}, "moveset": ["HIDDEN_POWER_GRASS", "GRASS_KNOT", "ENERGY_BALL"], "score": 95.2}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 387, "matchups": [{"opponent": "swampert", "rating": 709}, {"opponent": "rhyperior", "rating": 697, "opRating": 302}, {"opponent": "landorus_incarnate", "rating": 659, "opRating": 340}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 648, "opRating": 351}, {"opponent": "swampert_shadow", "rating": 627, "opRating": 372}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "metagross", "rating": 305}, {"opponent": "garcho<PERSON>", "rating": 438}, {"opponent": "grou<PERSON>", "rating": 448}, {"opponent": "excadrill", "rating": 495}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 28323}, {"moveId": "BUBBLE", "uses": 48177}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4631}, {"moveId": "LEAF_STORM", "uses": 14059}, {"moveId": "ICE_BEAM", "uses": 21130}, {"moveId": "HYDRO_PUMP", "uses": 13101}, {"moveId": "ENERGY_BALL", "uses": 16145}, {"moveId": "BLIZZARD", "uses": 7305}]}, "moveset": ["BUBBLE", "ICE_BEAM", "ENERGY_BALL"], "score": 95.2}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 450, "matchups": [{"opponent": "trevenant", "rating": 761, "opRating": 238}, {"opponent": "golisopod", "rating": 715, "opRating": 284}, {"opponent": "heracross", "rating": 677, "opRating": 322}, {"opponent": "gengar", "rating": 668, "opRating": 331}, {"opponent": "giratina_origin", "rating": 511}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "zacian_hero", "rating": 236}, {"opponent": "gyarados", "rating": 244}, {"opponent": "grou<PERSON>", "rating": 470}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 14391}, {"moveId": "PECK", "uses": 24275}, {"moveId": "BULLET_SEED", "uses": 37807}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 22132}, {"moveId": "FLASH_CANNON", "uses": 9598}, {"moveId": "DRILL_PECK", "uses": 44809}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 95.2}, {"speciesId": "masquerain", "speciesName": "Masquerain", "rating": 420, "matchups": [{"opponent": "obstagoon", "rating": 738, "opRating": 261}, {"opponent": "chesnaught", "rating": 707, "opRating": 292}, {"opponent": "celebi", "rating": 678, "opRating": 321}, {"opponent": "zarude", "rating": 636, "opRating": 363}, {"opponent": "virizion", "rating": 627, "opRating": 372}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "gyarados", "rating": 252}, {"opponent": "metagross", "rating": 348}, {"opponent": "mewtwo", "rating": 372}, {"opponent": "garcho<PERSON>", "rating": 375}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 38647}, {"moveId": "AIR_SLASH", "uses": 37853}], "chargedMoves": [{"moveId": "SILVER_WIND", "uses": 10940}, {"moveId": "OMINOUS_WIND", "uses": 14001}, {"moveId": "LUNGE", "uses": 30544}, {"moveId": "BUBBLE_BEAM", "uses": 9101}, {"moveId": "AIR_CUTTER", "uses": 11990}]}, "moveset": ["INFESTATION", "LUNGE", "OMINOUS_WIND"], "score": 95.2}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 424, "matchups": [{"opponent": "chesnaught", "rating": 829, "opRating": 170}, {"opponent": "virizion", "rating": 708, "opRating": 291}, {"opponent": "swampert", "rating": 706}, {"opponent": "swampert_shadow", "rating": 665, "opRating": 334}, {"opponent": "buzzwole", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 268}, {"opponent": "gyarados", "rating": 280}, {"opponent": "zacian_hero", "rating": 367}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 34795}, {"moveId": "AIR_SLASH", "uses": 41705}], "chargedMoves": [{"moveId": "STOMP", "uses": 14210}, {"moveId": "LEAF_BLADE", "uses": 45797}, {"moveId": "AERIAL_ACE", "uses": 16485}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 95.2}, {"speciesId": "alomomola", "speciesName": "Alomomola", "rating": 374, "matchups": [{"opponent": "typhlosion_shadow", "rating": 820, "opRating": 179}, {"opponent": "heatran", "rating": 646, "opRating": 353}, {"opponent": "darmanitan_standard", "rating": 641, "opRating": 358}, {"opponent": "entei_shadow", "rating": 613, "opRating": 386}, {"opponent": "moltres_shadow", "rating": 579, "opRating": 420}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 7648}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5172}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3687}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4593}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3623}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3417}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5636}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4825}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3704}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4634}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4160}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4165}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4290}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4192}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4130}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4546}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3669}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 24625}, {"moveId": "HYDRO_PUMP", "uses": 24800}, {"moveId": "BLIZZARD", "uses": 27055}]}, "moveset": ["WATERFALL", "BLIZZARD", "HYDRO_PUMP"], "score": 95.2}, {"speciesId": "grumpig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 357, "matchups": [{"opponent": "gallade", "rating": 627, "opRating": 372}, {"opponent": "terrakion", "rating": 590, "opRating": 409}, {"opponent": "machamp", "rating": 590, "opRating": 409}, {"opponent": "kommo_o", "rating": 514, "opRating": 485}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 511, "opRating": 488}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "zacian_hero", "rating": 161}, {"opponent": "lugia", "rating": 238}, {"opponent": "dialga", "rating": 279}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 348}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 44504}, {"moveId": "CHARGE_BEAM", "uses": 31996}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37606}, {"moveId": "PSYCHIC", "uses": 31989}, {"moveId": "MIRROR_COAT", "uses": 6790}]}, "moveset": ["EXTRASENSORY", "SHADOW_BALL", "PSYCHIC"], "score": 95.2}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 319, "matchups": [{"opponent": "sneasler", "rating": 599, "opRating": 400}, {"opponent": "tapu_fini", "rating": 549, "opRating": 450}, {"opponent": "vaporeon", "rating": 544, "opRating": 455}, {"opponent": "chesnaught", "rating": 544, "opRating": 455}, {"opponent": "tapu_koko", "rating": 534, "opRating": 465}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 1436}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4469}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4104}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4768}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3919}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3775}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5722}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5264}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 6058}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5032}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4424}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4590}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4779}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4565}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4462}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4955}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4111}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 8427}, {"moveId": "SEED_FLARE", "uses": 12209}, {"moveId": "GRASS_KNOT", "uses": 41157}, {"moveId": "ENERGY_BALL", "uses": 14535}]}, "moveset": ["ZEN_HEADBUTT", "GRASS_KNOT", "SEED_FLARE"], "score": 95.2}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 644, "matchups": [{"opponent": "gyarados", "rating": 732}, {"opponent": "lugia", "rating": 700}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 688}, {"opponent": "metagross", "rating": 531}, {"opponent": "dialga", "rating": 523}], "counters": [{"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "dragonite", "rating": 303}, {"opponent": "mewtwo", "rating": 330}, {"opponent": "zacian_hero", "rating": 407}, {"opponent": "excadrill", "rating": 474}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 12172}, {"moveId": "METAL_CLAW", "uses": 21790}, {"moveId": "LOCK_ON", "uses": 42539}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "FLASH_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 95}, {"speciesId": "blastoise", "speciesName": "Blastoise", "rating": 553, "matchups": [{"opponent": "heatran", "rating": 838, "opRating": 161}, {"opponent": "ho_oh_shadow", "rating": 835, "opRating": 164}, {"opponent": "landorus_incarnate", "rating": 702, "opRating": 297}, {"opponent": "excadrill", "rating": 658}, {"opponent": "garcho<PERSON>", "rating": 541}], "counters": [{"opponent": "dialga", "rating": 315}, {"opponent": "gyarados", "rating": 345}, {"opponent": "metagross", "rating": 363}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "zacian_hero", "rating": 442}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 50874}, {"moveId": "BITE", "uses": 25626}], "chargedMoves": [{"moveId": "SKULL_BASH", "uses": 8002}, {"moveId": "RETURN", "uses": 6686}, {"moveId": "ICE_BEAM", "uses": 15585}, {"moveId": "HYDRO_PUMP", "uses": 4037}, {"moveId": "HYDRO_CANNON", "uses": 37012}, {"moveId": "FLASH_CANNON", "uses": 5258}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 94.9}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 553, "matchups": [{"opponent": "swampert", "rating": 813}, {"opponent": "excadrill", "rating": 744}, {"opponent": "kyogre", "rating": 619, "opRating": 380}, {"opponent": "sylveon", "rating": 581, "opRating": 418}, {"opponent": "garcho<PERSON>", "rating": 561}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "gyarados", "rating": 342}, {"opponent": "mewtwo", "rating": 348}, {"opponent": "zacian_hero", "rating": 433}, {"opponent": "grou<PERSON>", "rating": 448}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53258}, {"moveId": "RAZOR_LEAF", "uses": 23242}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4432}, {"moveId": "RETURN", "uses": 10786}, {"moveId": "PETAL_BLIZZARD", "uses": 5389}, {"moveId": "FRENZY_PLANT", "uses": 38994}, {"moveId": "EARTHQUAKE", "uses": 17013}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 94.9}, {"speciesId": "blastoise_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 533, "matchups": [{"opponent": "ho_oh", "rating": 835, "opRating": 164}, {"opponent": "entei", "rating": 820, "opRating": 179}, {"opponent": "ho_oh_shadow", "rating": 805, "opRating": 194}, {"opponent": "zacian_hero", "rating": 791}, {"opponent": "excadrill", "rating": 573}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "mewtwo", "rating": 213}, {"opponent": "lugia", "rating": 309}, {"opponent": "dialga", "rating": 339}, {"opponent": "metagross", "rating": 468}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51331}, {"moveId": "BITE", "uses": 25169}], "chargedMoves": [{"moveId": "SKULL_BASH", "uses": 8983}, {"moveId": "ICE_BEAM", "uses": 16992}, {"moveId": "HYDRO_PUMP", "uses": 4204}, {"moveId": "HYDRO_CANNON", "uses": 40307}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 5883}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 94.9}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 539, "matchups": [{"opponent": "gengar", "rating": 793, "opRating": 206}, {"opponent": "articuno_galarian", "rating": 725, "opRating": 274}, {"opponent": "giratina_origin", "rating": 653}, {"opponent": "mew", "rating": 620, "opRating": 379}, {"opponent": "mewtwo_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 326}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "lugia", "rating": 338}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "mewtwo", "rating": 486}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 10369}, {"moveId": "LICK", "uses": 66131}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6581}, {"moveId": "SHADOW_BALL", "uses": 17281}, {"moveId": "HYPER_BEAM", "uses": 3914}, {"moveId": "EARTHQUAKE", "uses": 13377}, {"moveId": "BODY_SLAM", "uses": 35308}]}, "moveset": ["LICK", "BODY_SLAM", "SHADOW_BALL"], "score": 94.9}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 542, "matchups": [{"opponent": "tapu_fini", "rating": 851, "opRating": 148}, {"opponent": "swampert", "rating": 790}, {"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "excadrill", "rating": 671}, {"opponent": "grou<PERSON>", "rating": 636}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "metagross", "rating": 255}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "gyarados", "rating": 391}, {"opponent": "zacian_hero", "rating": 462}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54118}, {"moveId": "RAZOR_LEAF", "uses": 22382}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 5138}, {"moveId": "PETAL_BLIZZARD", "uses": 6230}, {"moveId": "FRUSTRATION", "uses": 28}, {"moveId": "FRENZY_PLANT", "uses": 45470}, {"moveId": "EARTHQUAKE", "uses": 19575}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 94.9}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "sylveon", "rating": 718, "opRating": 281}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 691}, {"opponent": "zacian_hero", "rating": 574}, {"opponent": "metagross", "rating": 552}, {"opponent": "dialga", "rating": 514}], "counters": [{"opponent": "lugia", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "mewtwo", "rating": 348}, {"opponent": "gyarados", "rating": 391}, {"opponent": "dragonite", "rating": 393}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 43078}, {"moveId": "CONFUSION", "uses": 33422}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 48089}, {"moveId": "PSYCHIC", "uses": 14172}, {"moveId": "OVERHEAT", "uses": 5512}, {"moveId": "FOCUS_BLAST", "uses": 8650}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 94.8}, {"speciesId": "<PERSON><PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 575, "matchups": [{"opponent": "metagross", "rating": 693}, {"opponent": "swampert", "rating": 635}, {"opponent": "excadrill", "rating": 590}, {"opponent": "dialga", "rating": 533}, {"opponent": "zekrom", "rating": 533}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dragonite", "rating": 162}, {"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "zacian_hero", "rating": 343}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 47257}, {"moveId": "BULLET_PUNCH", "uses": 29243}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 23864}, {"moveId": "HEAVY_SLAM", "uses": 11713}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DYNAMIC_PUNCH", "uses": 8636}, {"moveId": "CLOSE_COMBAT", "uses": 32313}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "SUPER_POWER"], "score": 94.8}, {"speciesId": "clawitzer", "speciesName": "Clawitzer", "rating": 426, "matchups": [{"opponent": "typhlosion", "rating": 775, "opRating": 224}, {"opponent": "typhlosion_shadow", "rating": 768, "opRating": 231}, {"opponent": "hippo<PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "regirock", "rating": 632, "opRating": 367}, {"opponent": "excadrill", "rating": 518}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "metagross", "rating": 299}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41689}, {"moveId": "SMACK_DOWN", "uses": 34811}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 4381}, {"moveId": "ICE_BEAM", "uses": 21617}, {"moveId": "DARK_PULSE", "uses": 20824}, {"moveId": "CRABHAMMER", "uses": 29818}]}, "moveset": ["WATER_GUN", "CRABHAMMER", "ICE_BEAM"], "score": 94.8}, {"speciesId": "comfey", "speciesName": "Comfey", "rating": 356, "matchups": [{"opponent": "suicune", "rating": 701, "opRating": 298}, {"opponent": "pangoro", "rating": 689, "opRating": 310}, {"opponent": "milotic", "rating": 666, "opRating": 333}, {"opponent": "rhyperior", "rating": 647, "opRating": 352}, {"opponent": "swampert", "rating": 503}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "gyarados", "rating": 278}, {"opponent": "excadrill", "rating": 348}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 8304}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4057}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3793}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4510}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3684}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3486}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5378}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4939}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3726}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4722}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4174}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4370}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4434}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4181}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4230}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4591}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3691}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 9817}, {"moveId": "GRASS_KNOT", "uses": 38045}, {"moveId": "DRAINING_KISS", "uses": 28555}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "DRAINING_KISS"], "score": 94.8}, {"speciesId": "politoed", "speciesName": "Politoed", "rating": 499, "matchups": [{"opponent": "heatran", "rating": 827, "opRating": 172}, {"opponent": "entei", "rating": 706, "opRating": 293}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 639, "opRating": 360}, {"opponent": "excadrill", "rating": 634}, {"opponent": "terrakion", "rating": 599, "opRating": 400}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "metagross", "rating": 331}, {"opponent": "zacian_hero", "rating": 367}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41253}, {"moveId": "BUBBLE", "uses": 35247}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 29639}, {"moveId": "SURF", "uses": 12078}, {"moveId": "RETURN", "uses": 7224}, {"moveId": "HYDRO_PUMP", "uses": 3919}, {"moveId": "EARTHQUAKE", "uses": 11740}, {"moveId": "BLIZZARD", "uses": 11972}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "SURF"], "score": 94.7}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 390, "matchups": [{"opponent": "al<PERSON><PERSON>_shadow", "rating": 831, "opRating": 168}, {"opponent": "zarude", "rating": 656, "opRating": 343}, {"opponent": "obstagoon", "rating": 646, "opRating": 353}, {"opponent": "celebi", "rating": 624, "opRating": 375}, {"opponent": "virizion", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "metagross", "rating": 305}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 382}, {"opponent": "swampert", "rating": 412}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 17586}, {"moveId": "FURY_CUTTER", "uses": 22466}, {"moveId": "BUG_BITE", "uses": 19353}, {"moveId": "AIR_SLASH", "uses": 17118}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 29862}, {"moveId": "SIGNAL_BEAM", "uses": 8402}, {"moveId": "POWER_GEM", "uses": 14028}, {"moveId": "FELL_STINGER", "uses": 4907}, {"moveId": "BUG_BUZZ", "uses": 19501}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 94.7}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 534, "matchups": [{"opponent": "swampert", "rating": 767}, {"opponent": "swampert_shadow", "rating": 729, "opRating": 270}, {"opponent": "kyogre", "rating": 710, "opRating": 289}, {"opponent": "excadrill", "rating": 633}, {"opponent": "grou<PERSON>", "rating": 579}], "counters": [{"opponent": "gyarados", "rating": 219}, {"opponent": "dialga", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "metagross", "rating": 340}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 25145}, {"moveId": "FURY_CUTTER", "uses": 51355}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 13998}, {"moveId": "SUPER_POWER", "uses": 20543}, {"moveId": "LEAF_STORM", "uses": 4639}, {"moveId": "LEAF_BLADE", "uses": 37378}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 94.6}, {"speciesId": "hoopa", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 498, "matchups": [{"opponent": "machamp", "rating": 926, "opRating": 73}, {"opponent": "machamp_shadow", "rating": 910, "opRating": 89}, {"opponent": "zap<PERSON>_galarian", "rating": 872, "opRating": 127}, {"opponent": "buzzwole", "rating": 700, "opRating": 299}, {"opponent": "zacian_hero", "rating": 589}], "counters": [{"opponent": "mewtwo", "rating": 174}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "dialga", "rating": 220}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "gyarados", "rating": 327}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 52598}, {"moveId": "ASTONISH", "uses": 23902}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 43481}, {"moveId": "PSYCHIC", "uses": 27344}, {"moveId": "PSYBEAM", "uses": 5671}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 94.6}, {"speciesId": "slowbro", "speciesName": "Slowbro", "rating": 428, "matchups": [{"opponent": "blaziken", "rating": 796, "opRating": 203}, {"opponent": "sneasler", "rating": 788, "opRating": 211}, {"opponent": "machamp", "rating": 615, "opRating": 384}, {"opponent": "zacian_hero", "rating": 605}, {"opponent": "machamp_shadow", "rating": 603, "opRating": 396}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "lugia", "rating": 254}, {"opponent": "garcho<PERSON>", "rating": 255}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 37889}, {"moveId": "CONFUSION", "uses": 38611}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 13233}, {"moveId": "RETURN", "uses": 12784}, {"moveId": "PSYCHIC", "uses": 23937}, {"moveId": "ICE_BEAM", "uses": 26552}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 94.6}, {"speciesId": "slowbro_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 430, "matchups": [{"opponent": "blaziken", "rating": 817, "opRating": 182}, {"opponent": "sneasler", "rating": 798, "opRating": 201}, {"opponent": "roserade", "rating": 765, "opRating": 234}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 726, "opRating": 273}, {"opponent": "machamp", "rating": 603, "opRating": 396}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "gyarados", "rating": 273}, {"opponent": "zacian_hero", "rating": 280}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 36463}, {"moveId": "CONFUSION", "uses": 40037}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 15912}, {"moveId": "PSYCHIC", "uses": 28904}, {"moveId": "ICE_BEAM", "uses": 31498}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 94.6}, {"speciesId": "electrode_shadow", "speciesName": "Electrode (Shadow)", "rating": 415, "matchups": [{"opponent": "suicune_shadow", "rating": 820, "opRating": 179}, {"opponent": "moltres", "rating": 658, "opRating": 341}, {"opponent": "tapu_fini", "rating": 616, "opRating": 383}, {"opponent": "golisopod", "rating": 577, "opRating": 422}, {"opponent": "zap<PERSON>_galarian", "rating": 570, "opRating": 429}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "lugia", "rating": 321}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "metagross", "rating": 383}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33512}, {"moveId": "TACKLE", "uses": 18578}, {"moveId": "SPARK", "uses": 24411}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 11084}, {"moveId": "HYPER_BEAM", "uses": 9729}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOUL_PLAY", "uses": 29665}, {"moveId": "DISCHARGE", "uses": 25932}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 94.6}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 407, "matchups": [{"opponent": "archeops", "rating": 665, "opRating": 334}, {"opponent": "moltres_shadow", "rating": 658, "opRating": 341}, {"opponent": "empoleon", "rating": 573, "opRating": 426}, {"opponent": "gengar", "rating": 563, "opRating": 436}, {"opponent": "walrein_shadow", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "lugia", "rating": 264}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "metagross", "rating": 331}, {"opponent": "gyarados", "rating": 358}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33311}, {"moveId": "TACKLE", "uses": 18810}, {"moveId": "SPARK", "uses": 24380}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 10161}, {"moveId": "RETURN", "uses": 11023}, {"moveId": "HYPER_BEAM", "uses": 4338}, {"moveId": "FOUL_PLAY", "uses": 26921}, {"moveId": "DISCHARGE", "uses": 23891}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 94.6}, {"speciesId": "mr_mime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 333, "matchups": [{"opponent": "sneasler", "rating": 676, "opRating": 323}, {"opponent": "heracross", "rating": 646, "opRating": 353}, {"opponent": "kommo_o", "rating": 606, "opRating": 393}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 592, "opRating": 407}, {"opponent": "machamp", "rating": 548, "opRating": 451}], "counters": [{"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 198}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "lugia", "rating": 292}, {"opponent": "dragonite", "rating": 313}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 12796}, {"moveId": "CONFUSION", "uses": 63704}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37781}, {"moveId": "PSYCHIC", "uses": 32144}, {"moveId": "PSYBEAM", "uses": 6432}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 94.6}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 319, "matchups": [{"opponent": "nihilego", "rating": 591, "opRating": 408}, {"opponent": "machamp", "rating": 580, "opRating": 419}, {"opponent": "machamp_shadow", "rating": 580, "opRating": 419}, {"opponent": "magnezone", "rating": 563, "opRating": 436}, {"opponent": "magnezone_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "dialga", "rating": 133}, {"opponent": "gyarados", "rating": 157}, {"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "zacian_hero", "rating": 210}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 25198}, {"moveId": "EXTRASENSORY", "uses": 23048}, {"moveId": "CONFUSION", "uses": 28246}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 13681}, {"moveId": "ROCK_TOMB", "uses": 7921}, {"moveId": "PSYCHIC", "uses": 11892}, {"moveId": "ICE_BEAM", "uses": 14072}, {"moveId": "GYRO_BALL", "uses": 4906}, {"moveId": "EARTH_POWER", "uses": 16784}, {"moveId": "EARTHQUAKE", "uses": 7232}]}, "moveset": ["CONFUSION", "EARTH_POWER", "ICE_BEAM"], "score": 94.6}, {"speciesId": "meowstic_female", "speciesName": "<PERSON><PERSON><PERSON> (Female)", "rating": 316, "matchups": [{"opponent": "hitmon<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "poliwrath", "rating": 592, "opRating": 407}, {"opponent": "poliwrath_shadow", "rating": 561, "opRating": 438}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "dialga", "rating": 154}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44379}, {"moveId": "CHARM", "uses": 32121}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 31197}, {"moveId": "PSYCHIC", "uses": 26606}, {"moveId": "ENERGY_BALL", "uses": 18704}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 94.6}, {"speciesId": "me<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON> (Male)", "rating": 310, "matchups": [{"opponent": "hitmon<PERSON>_shadow", "rating": 776, "opRating": 223}, {"opponent": "poliwrath", "rating": 592, "opRating": 407}, {"opponent": "poliwrath_shadow", "rating": 561, "opRating": 438}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "dialga", "rating": 154}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 35606}, {"moveId": "CONFUSION", "uses": 40894}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 24484}, {"moveId": "PSYCHIC", "uses": 30614}, {"moveId": "ENERGY_BALL", "uses": 21401}]}, "moveset": ["CONFUSION", "PSYCHIC", "THUNDERBOLT"], "score": 94.6}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 455, "matchups": [{"opponent": "vaporeon", "rating": 820, "opRating": 179}, {"opponent": "swampert", "rating": 801}, {"opponent": "swampert_shadow", "rating": 783, "opRating": 216}, {"opponent": "tapu_fini", "rating": 725, "opRating": 274}, {"opponent": "kyogre", "rating": 557, "opRating": 442}], "counters": [{"opponent": "dialga", "rating": 105}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "garcho<PERSON>", "rating": 326}, {"opponent": "zacian_hero", "rating": 387}, {"opponent": "excadrill", "rating": 444}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 22615}, {"moveId": "BULLET_SEED", "uses": 38695}, {"moveId": "ACID", "uses": 15204}], "chargedMoves": [{"moveId": "RETURN", "uses": 10296}, {"moveId": "PETAL_BLIZZARD", "uses": 5282}, {"moveId": "LEAF_BLADE", "uses": 50306}, {"moveId": "DAZZLING_GLEAM", "uses": 10891}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 94.3}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 459, "matchups": [{"opponent": "swampert", "rating": 783}, {"opponent": "swampert_shadow", "rating": 722, "opRating": 277}, {"opponent": "excadrill", "rating": 628}, {"opponent": "grou<PERSON>", "rating": 570}, {"opponent": "kyogre", "rating": 527, "opRating": 472}], "counters": [{"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "mewtwo", "rating": 242}, {"opponent": "zacian_hero", "rating": 242}, {"opponent": "gyarados", "rating": 273}, {"opponent": "metagross", "rating": 276}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 22072}, {"moveId": "BULLET_SEED", "uses": 39836}, {"moveId": "ACID", "uses": 14592}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 5945}, {"moveId": "LEAF_BLADE", "uses": 57473}, {"moveId": "FRUSTRATION", "uses": 2}, {"moveId": "DAZZLING_GLEAM", "uses": 13109}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 94.3}, {"speciesId": "wailord", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 446, "matchups": [{"opponent": "chandelure", "rating": 825, "opRating": 174}, {"opponent": "heatran", "rating": 800, "opRating": 199}, {"opponent": "landorus_incarnate", "rating": 631, "opRating": 368}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 590, "opRating": 409}, {"opponent": "excadrill", "rating": 575}], "counters": [{"opponent": "mewtwo", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 187}, {"opponent": "metagross", "rating": 316}, {"opponent": "zacian_hero", "rating": 343}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9638}, {"moveId": "WATER_GUN", "uses": 66863}], "chargedMoves": [{"moveId": "SURF", "uses": 46201}, {"moveId": "HYPER_BEAM", "uses": 10205}, {"moveId": "BLIZZARD", "uses": 20064}]}, "moveset": ["WATER_GUN", "SURF", "BLIZZARD"], "score": 94.3}, {"speciesId": "x<PERSON><PERSON>", "speciesName": "Xerneas", "rating": 709, "matchups": [{"opponent": "dialga", "rating": 847}, {"opponent": "dragonite", "rating": 659}, {"opponent": "gyarados", "rating": 591}, {"opponent": "zacian_hero", "rating": 552}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 552}], "counters": [{"opponent": "mewtwo", "rating": 252}, {"opponent": "giratina_origin", "rating": 444}, {"opponent": "garcho<PERSON>", "rating": 471}, {"opponent": "grou<PERSON>", "rating": 480}, {"opponent": "lugia", "rating": 490}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 8093}, {"moveId": "TACKLE", "uses": 68407}], "chargedMoves": [{"moveId": "THUNDER", "uses": 10853}, {"moveId": "MOONBLAST", "uses": 19189}, {"moveId": "MEGAHORN", "uses": 14040}, {"moveId": "GIGA_IMPACT", "uses": 5376}, {"moveId": "CLOSE_COMBAT", "uses": 27212}]}, "moveset": ["TACKLE", "CLOSE_COMBAT", "MOONBLAST"], "score": 94.2}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 652, "matchups": [{"opponent": "giratina_origin", "rating": 805}, {"opponent": "dialga", "rating": 621}, {"opponent": "excadrill", "rating": 593}, {"opponent": "giratina_altered", "rating": 549, "opRating": 450}, {"opponent": "snorlax", "rating": 532, "opRating": 467}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "gyarados", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "lugia", "rating": 383}, {"opponent": "metagross", "rating": 479}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 24555}, {"moveId": "SHADOW_CLAW", "uses": 44640}, {"moveId": "LOW_KICK", "uses": 7372}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34083}, {"moveId": "STOMP", "uses": 22006}, {"moveId": "PAYBACK", "uses": 18008}, {"moveId": "DRAIN_PUNCH", "uses": 2332}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "PAYBACK"], "score": 94.2}, {"speciesId": "aerodactyl", "speciesName": "Aerodactyl", "rating": 497, "matchups": [{"opponent": "moltres", "rating": 875, "opRating": 125}, {"opponent": "moltres_shadow", "rating": 869, "opRating": 130}, {"opponent": "darmanitan_standard", "rating": 857, "opRating": 142}, {"opponent": "ho_oh_shadow", "rating": 845, "opRating": 154}, {"opponent": "ho_oh", "rating": 837, "opRating": 162}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "gyarados", "rating": 373}, {"opponent": "dragonite", "rating": 385}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 19385}, {"moveId": "ROCK_THROW", "uses": 36312}, {"moveId": "BITE", "uses": 20846}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27654}, {"moveId": "RETURN", "uses": 7913}, {"moveId": "IRON_HEAD", "uses": 10019}, {"moveId": "HYPER_BEAM", "uses": 3063}, {"moveId": "EARTH_POWER", "uses": 18025}, {"moveId": "ANCIENT_POWER", "uses": 9899}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 94.2}, {"speciesId": "aerodactyl_shadow", "speciesName": "Aerodactyl (Shadow)", "rating": 499, "matchups": [{"opponent": "moltres_shadow", "rating": 883, "opRating": 116}, {"opponent": "moltres", "rating": 869, "opRating": 130}, {"opponent": "ho_oh", "rating": 845, "opRating": 154}, {"opponent": "ho_oh_shadow", "rating": 813, "opRating": 186}, {"opponent": "entei", "rating": 752, "opRating": 247}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "dragonite", "rating": 449}, {"opponent": "gyarados", "rating": 466}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 19671}, {"moveId": "ROCK_THROW", "uses": 36442}, {"moveId": "BITE", "uses": 20411}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29254}, {"moveId": "IRON_HEAD", "uses": 10732}, {"moveId": "HYPER_BEAM", "uses": 6620}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTH_POWER", "uses": 19282}, {"moveId": "ANCIENT_POWER", "uses": 10481}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 94.2}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 703, "matchups": [{"opponent": "zacian_hero", "rating": 665}, {"opponent": "dialga", "rating": 581}, {"opponent": "lugia", "rating": 572}, {"opponent": "dragonite", "rating": 549}, {"opponent": "mewtwo", "rating": 523}], "counters": [{"opponent": "giratina_origin", "rating": 183}, {"opponent": "excadrill", "rating": 183}, {"opponent": "grou<PERSON>", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "gyarados", "rating": 378}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9295}, {"moveId": "BULLET_PUNCH", "uses": 67205}], "chargedMoves": [{"moveId": "RETURN", "uses": 8922}, {"moveId": "PSYCHIC", "uses": 17032}, {"moveId": "METEOR_MASH", "uses": 29564}, {"moveId": "FLASH_CANNON", "uses": 4750}, {"moveId": "EARTHQUAKE", "uses": 16341}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 94}, {"speciesId": "metagross_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 658, "matchups": [{"opponent": "sylveon", "rating": 735, "opRating": 264}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 706}, {"opponent": "zacian_hero", "rating": 622}, {"opponent": "dialga", "rating": 558}, {"opponent": "dragonite", "rating": 514}], "counters": [{"opponent": "gyarados", "rating": 182}, {"opponent": "giratina_origin", "rating": 193}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "lugia", "rating": 452}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 8175}, {"moveId": "BULLET_PUNCH", "uses": 68325}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 19552}, {"moveId": "METEOR_MASH", "uses": 33441}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 5203}, {"moveId": "EARTHQUAKE", "uses": 18247}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 94}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 500, "matchups": [{"opponent": "swampert", "rating": 911}, {"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "tapu_fini", "rating": 844, "opRating": 155}, {"opponent": "kyogre", "rating": 698, "opRating": 301}, {"opponent": "sylveon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "gyarados", "rating": 368}, {"opponent": "zacian_hero", "rating": 427}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42823}, {"moveId": "ACID", "uses": 33677}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 7064}, {"moveId": "SLUDGE_BOMB", "uses": 25853}, {"moveId": "PETAL_BLIZZARD", "uses": 17836}, {"moveId": "MOONBLAST", "uses": 25576}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "MOONBLAST"], "score": 93.9}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 473, "matchups": [{"opponent": "swampert", "rating": 923}, {"opponent": "swampert_shadow", "rating": 911, "opRating": 88}, {"opponent": "vaporeon", "rating": 807, "opRating": 192}, {"opponent": "tapu_fini", "rating": 710, "opRating": 289}, {"opponent": "kyogre", "rating": 539, "opRating": 460}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "gyarados", "rating": 327}, {"opponent": "zacian_hero", "rating": 445}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43380}, {"moveId": "ACID", "uses": 33120}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6380}, {"moveId": "SLUDGE_BOMB", "uses": 21800}, {"moveId": "RETURN", "uses": 10997}, {"moveId": "PETAL_BLIZZARD", "uses": 15469}, {"moveId": "MOONBLAST", "uses": 21931}]}, "moveset": ["RAZOR_LEAF", "MOONBLAST", "SLUDGE_BOMB"], "score": 93.9}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 385, "matchups": [{"opponent": "feraligatr_shadow", "rating": 732, "opRating": 267}, {"opponent": "charizard", "rating": 697, "opRating": 302}, {"opponent": "gyarados", "rating": 693}, {"opponent": "charizard_shadow", "rating": 693, "opRating": 306}, {"opponent": "gyarado<PERSON>_shadow", "rating": 619, "opRating": 380}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "metagross", "rating": 264}, {"opponent": "dragonite", "rating": 276}, {"opponent": "lugia", "rating": 285}, {"opponent": "zacian_hero", "rating": 335}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50340}, {"moveId": "TACKLE", "uses": 26160}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 49836}, {"moveId": "SWIFT", "uses": 6139}, {"moveId": "ENERGY_BALL", "uses": 20556}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ENERGY_BALL"], "score": 93.9}, {"speciesId": "giratina_altered", "speciesName": "Giratina (Altered)", "rating": 818, "matchups": [{"opponent": "mewtwo", "rating": 701}, {"opponent": "metagross", "rating": 593}, {"opponent": "garcho<PERSON>", "rating": 569}, {"opponent": "gyarados", "rating": 515}, {"opponent": "dragonite", "rating": 505}], "counters": [{"opponent": "dialga", "rating": 410}, {"opponent": "lugia", "rating": 426}, {"opponent": "zacian_hero", "rating": 442}, {"opponent": "grou<PERSON>", "rating": 489}, {"opponent": "giratina_origin", "rating": 492}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 42639}, {"moveId": "DRAGON_BREATH", "uses": 33861}], "chargedMoves": [{"moveId": "SHADOW_SNEAK", "uses": 18523}, {"moveId": "DRAGON_CLAW", "uses": 38975}, {"moveId": "ANCIENT_POWER", "uses": 18960}]}, "moveset": ["SHADOW_CLAW", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 93.6}, {"speciesId": "regirock", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 740, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 677}, {"opponent": "zacian_hero", "rating": 645}, {"opponent": "excadrill", "rating": 601}, {"opponent": "lugia", "rating": 578}, {"opponent": "gyarados", "rating": 558}], "counters": [{"opponent": "mewtwo", "rating": 283}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "dragonite", "rating": 390}, {"opponent": "dialga", "rating": 459}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 25509}, {"moveId": "ROCK_SMASH", "uses": 9923}, {"moveId": "LOCK_ON", "uses": 41062}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 12845}, {"moveId": "STONE_EDGE", "uses": 30547}, {"moveId": "FOCUS_BLAST", "uses": 14039}, {"moveId": "EARTHQUAKE", "uses": 18969}]}, "moveset": ["LOCK_ON", "STONE_EDGE", "FOCUS_BLAST"], "score": 93.6}, {"speciesId": "raikou_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 707, "matchups": [{"opponent": "metagross", "rating": 809}, {"opponent": "gyarados", "rating": 750}, {"opponent": "ho_oh", "rating": 744}, {"opponent": "lugia", "rating": 717}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 610}], "counters": [{"opponent": "zacian_hero", "rating": 210}, {"opponent": "dragonite", "rating": 226}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "zekrom", "rating": 339}, {"opponent": "dialga", "rating": 418}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41933}, {"moveId": "THUNDER_SHOCK", "uses": 34567}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 40211}, {"moveId": "THUNDERBOLT", "uses": 7199}, {"moveId": "THUNDER", "uses": 6356}, {"moveId": "SHADOW_BALL", "uses": 22541}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 93.6}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 727, "matchups": [{"opponent": "metagross", "rating": 846}, {"opponent": "ho_oh", "rating": 776}, {"opponent": "gyarados", "rating": 739}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 674}, {"opponent": "mewtwo", "rating": 583}], "counters": [{"opponent": "zacian_hero", "rating": 176}, {"opponent": "dragonite", "rating": 194}, {"opponent": "dialga", "rating": 355}, {"opponent": "giratina_origin", "rating": 392}, {"opponent": "lugia", "rating": 495}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41534}, {"moveId": "THUNDER_SHOCK", "uses": 34966}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 36350}, {"moveId": "THUNDERBOLT", "uses": 6654}, {"moveId": "THUNDER", "uses": 5808}, {"moveId": "SHADOW_BALL", "uses": 19381}, {"moveId": "RETURN", "uses": 8531}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 93.6}, {"speciesId": "falinks", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "registeel", "rating": 899, "opRating": 100}, {"opponent": "cobalion", "rating": 607, "opRating": 392}, {"opponent": "dialga", "rating": 570}, {"opponent": "excadrill", "rating": 536}, {"opponent": "snorlax", "rating": 510, "opRating": 489}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "gyarados", "rating": 157}, {"opponent": "zacian_hero", "rating": 265}, {"opponent": "garcho<PERSON>", "rating": 326}, {"opponent": "metagross", "rating": 383}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 13630}, {"moveId": "COUNTER", "uses": 62870}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34617}, {"moveId": "MEGAHORN", "uses": 20964}, {"moveId": "BRICK_BREAK", "uses": 20859}]}, "moveset": ["COUNTER", "SUPER_POWER", "MEGAHORN"], "score": 93.6}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 753, "matchups": [{"opponent": "metagross", "rating": 764}, {"opponent": "giratina_origin", "rating": 707}, {"opponent": "excadrill", "rating": 704}, {"opponent": "dialga", "rating": 686}, {"opponent": "garcho<PERSON>", "rating": 574}], "counters": [{"opponent": "dragonite", "rating": 348}, {"opponent": "gyarados", "rating": 422}, {"opponent": "zacian_hero", "rating": 439}, {"opponent": "lugia", "rating": 466}, {"opponent": "mewtwo", "rating": 479}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 8614}, {"moveId": "YAWN", "uses": 1568}, {"moveId": "LICK", "uses": 66352}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16863}, {"moveId": "SKULL_BASH", "uses": 4465}, {"moveId": "OUTRAGE", "uses": 9582}, {"moveId": "HYPER_BEAM", "uses": 2960}, {"moveId": "HEAVY_SLAM", "uses": 7189}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 9350}, {"moveId": "BODY_SLAM", "uses": 26077}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 93.5}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 730, "matchups": [{"opponent": "excadrill", "rating": 735}, {"opponent": "giratina_origin", "rating": 730}, {"opponent": "dialga", "rating": 660}, {"opponent": "swampert", "rating": 647}, {"opponent": "ho_oh", "rating": 520, "opRating": 479}], "counters": [{"opponent": "metagross", "rating": 383}, {"opponent": "garcho<PERSON>", "rating": 401}, {"opponent": "zacian_hero", "rating": 407}, {"opponent": "mewtwo", "rating": 453}, {"opponent": "gyarados", "rating": 466}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9171}, {"moveId": "YAWN", "uses": 2010}, {"moveId": "LICK", "uses": 65388}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16248}, {"moveId": "SKULL_BASH", "uses": 4251}, {"moveId": "RETURN", "uses": 3602}, {"moveId": "OUTRAGE", "uses": 9120}, {"moveId": "HYPER_BEAM", "uses": 2778}, {"moveId": "HEAVY_SLAM", "uses": 6828}, {"moveId": "EARTHQUAKE", "uses": 8985}, {"moveId": "BODY_SLAM", "uses": 24690}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 93.5}, {"speciesId": "deoxys_speed", "speciesName": "<PERSON><PERSON><PERSON> (Speed)", "rating": 354, "matchups": [{"opponent": "poliwrath_shadow", "rating": 748, "opRating": 251}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 740, "opRating": 259}, {"opponent": "poliwrath", "rating": 641, "opRating": 358}, {"opponent": "empoleon", "rating": 602, "opRating": 397}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 535, "opRating": 464}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "gyarados", "rating": 257}, {"opponent": "lugia", "rating": 266}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "metagross", "rating": 290}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 16956}, {"moveId": "CHARGE_BEAM", "uses": 59544}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 23720}, {"moveId": "SWIFT", "uses": 8868}, {"moveId": "PSYCHO_BOOST", "uses": 43890}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 93.5}, {"speciesId": "deoxys", "speciesName": "Deoxys", "rating": 267, "matchups": [{"opponent": "primeape", "rating": 720, "opRating": 279}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 681, "opRating": 318}, {"opponent": "blaziken", "rating": 649, "opRating": 350}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 610, "opRating": 389}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 586, "opRating": 413}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "lugia", "rating": 130}, {"opponent": "gyarados", "rating": 273}, {"opponent": "zacian_hero", "rating": 361}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 15509}, {"moveId": "CHARGE_BEAM", "uses": 60991}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 22796}, {"moveId": "PSYCHO_BOOST", "uses": 42200}, {"moveId": "HYPER_BEAM", "uses": 11515}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 93.5}, {"speciesId": "golisopod", "speciesName": "Golisopod", "rating": 647, "matchups": [{"opponent": "swampert", "rating": 661}, {"opponent": "metagross", "rating": 594}, {"opponent": "buzzwole", "rating": 588, "opRating": 411}, {"opponent": "kyogre", "rating": 582, "opRating": 417}, {"opponent": "mewtwo", "rating": 545}], "counters": [{"opponent": "dialga", "rating": 345}, {"opponent": "giratina_origin", "rating": 392}, {"opponent": "gyarados", "rating": 409}, {"opponent": "zacian_hero", "rating": 413}, {"opponent": "garcho<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 18908}, {"moveId": "SHADOW_CLAW", "uses": 26028}, {"moveId": "METAL_CLAW", "uses": 10306}, {"moveId": "FURY_CUTTER", "uses": 21257}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 38592}, {"moveId": "AQUA_JET", "uses": 18771}, {"moveId": "AERIAL_ACE", "uses": 19145}]}, "moveset": ["SHADOW_CLAW", "X_SCISSOR", "AERIAL_ACE"], "score": 93.3}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 522, "matchups": [{"opponent": "gengar", "rating": 645, "opRating": 354}, {"opponent": "electivire", "rating": 614, "opRating": 385}, {"opponent": "snorlax", "rating": 612, "opRating": 387}, {"opponent": "giratina_origin", "rating": 523}, {"opponent": "articuno_galarian", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 285}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "gyarados", "rating": 378}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 28588}, {"moveId": "BULLET_SEED", "uses": 28591}, {"moveId": "BITE", "uses": 19293}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28702}, {"moveId": "BODY_SLAM", "uses": 47798}]}, "moveset": ["TACKLE", "BODY_SLAM", "CRUNCH"], "score": 93.3}, {"speciesId": "gardevoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 660, "matchups": [{"opponent": "dragonite", "rating": 866}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "zekrom", "rating": 801}, {"opponent": "dialga", "rating": 675}, {"opponent": "gyarados", "rating": 603}], "counters": [{"opponent": "excadrill", "rating": 358}, {"opponent": "lugia", "rating": 402}, {"opponent": "mewtwo", "rating": 416}, {"opponent": "zacian_hero", "rating": 465}, {"opponent": "giratina_origin", "rating": 478}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31058}, {"moveId": "CHARM", "uses": 27009}, {"moveId": "CHARGE_BEAM", "uses": 18471}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 23219}, {"moveId": "SHADOW_BALL", "uses": 26130}, {"moveId": "PSYCHIC", "uses": 10569}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DAZZLING_GLEAM", "uses": 16483}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 93.2}, {"speciesId": "mewtwo_armored", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Armored)", "rating": 721, "matchups": [{"opponent": "excadrill", "rating": 736}, {"opponent": "swampert", "rating": 585, "opRating": 414}, {"opponent": "sylveon", "rating": 549, "opRating": 450}, {"opponent": "gyarados", "rating": 528}, {"opponent": "ho_oh", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "garcho<PERSON>", "rating": 413}, {"opponent": "zacian_hero", "rating": 482}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "IRON_TAIL", "uses": 10254}, {"moveId": "CONFUSION", "uses": 66246}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 18840}, {"moveId": "PSYSTRIKE", "uses": 25599}, {"moveId": "FUTURE_SIGHT", "uses": 5222}, {"moveId": "EARTHQUAKE", "uses": 11433}, {"moveId": "DYNAMIC_PUNCH", "uses": 15495}]}, "moveset": ["CONFUSION", "PSYSTRIKE", "DYNAMIC_PUNCH"], "score": 93.2}, {"speciesId": "gardevoir", "speciesName": "Gardevoir", "rating": 623, "matchups": [{"opponent": "yveltal", "rating": 866, "opRating": 133}, {"opponent": "palkia", "rating": 860, "opRating": 139}, {"opponent": "dragonite", "rating": 847}, {"opponent": "zekrom", "rating": 847, "opRating": 152}, {"opponent": "garcho<PERSON>", "rating": 827}], "counters": [{"opponent": "mewtwo", "rating": 333}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "giratina_origin", "rating": 398}, {"opponent": "dialga", "rating": 421}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30257}, {"moveId": "CHARM", "uses": 27821}, {"moveId": "CHARGE_BEAM", "uses": 18484}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 20240}, {"moveId": "SHADOW_BALL", "uses": 23031}, {"moveId": "RETURN", "uses": 9641}, {"moveId": "PSYCHIC", "uses": 9174}, {"moveId": "DAZZLING_GLEAM", "uses": 14317}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 93.2}, {"speciesId": "salamence_shadow", "speciesName": "Salamence (Shadow)", "rating": 659, "matchups": [{"opponent": "garcho<PERSON>", "rating": 940}, {"opponent": "swampert", "rating": 621}, {"opponent": "grou<PERSON>", "rating": 579}, {"opponent": "dragonite", "rating": 533}, {"opponent": "kyogre", "rating": 502, "opRating": 497}], "counters": [{"opponent": "metagross", "rating": 293}, {"opponent": "dialga", "rating": 304}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "gyarados", "rating": 373}, {"opponent": "giratina_origin", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 19549}, {"moveId": "DRAGON_TAIL", "uses": 44943}, {"moveId": "BITE", "uses": 11996}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 35117}, {"moveId": "HYDRO_PUMP", "uses": 15292}, {"moveId": "FRUSTRATION", "uses": 2}, {"moveId": "FIRE_BLAST", "uses": 15428}, {"moveId": "DRACO_METEOR", "uses": 10543}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "FIRE_BLAST"], "score": 93}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 434, "matchups": [{"opponent": "pangoro", "rating": 901, "opRating": 98}, {"opponent": "darkrai", "rating": 818, "opRating": 181}, {"opponent": "kommo_o", "rating": 710, "opRating": 289}, {"opponent": "hydreigon", "rating": 678, "opRating": 321}, {"opponent": "machamp_shadow", "rating": 646, "opRating": 353}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "mewtwo", "rating": 270}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "dragonite", "rating": 382}, {"opponent": "garcho<PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_WATER", "uses": 4218}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3848}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4549}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3707}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3588}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5349}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4877}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 5472}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4714}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4152}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4387}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4396}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4288}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4185}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4642}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3823}, {"moveId": "CHARM", "uses": 6474}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 14577}, {"moveId": "PETAL_BLIZZARD", "uses": 35993}, {"moveId": "HYPER_BEAM", "uses": 26093}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 92.8}, {"speciesId": "zacian_hero", "speciesName": "<PERSON><PERSON><PERSON> (Hero)", "rating": 750, "matchups": [{"opponent": "dragonite", "rating": 783}, {"opponent": "excadrill", "rating": 702}, {"opponent": "garcho<PERSON>", "rating": 650}, {"opponent": "gyarados", "rating": 546}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 511, "opRating": 488}], "counters": [{"opponent": "metagross", "rating": 334}, {"opponent": "giratina_origin", "rating": 380}, {"opponent": "mewtwo", "rating": 447}, {"opponent": "dialga", "rating": 478}, {"opponent": "lugia", "rating": 483}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27396}, {"moveId": "QUICK_ATTACK", "uses": 26569}, {"moveId": "METAL_CLAW", "uses": 10125}, {"moveId": "FIRE_FANG", "uses": 12419}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 27139}, {"moveId": "PLAY_ROUGH", "uses": 12798}, {"moveId": "IRON_HEAD", "uses": 8946}, {"moveId": "CLOSE_COMBAT", "uses": 27797}]}, "moveset": ["QUICK_ATTACK", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 92.7}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 642, "matchups": [{"opponent": "garcho<PERSON>", "rating": 858}, {"opponent": "giratina_origin", "rating": 746}, {"opponent": "gyarados", "rating": 718}, {"opponent": "lugia", "rating": 672}, {"opponent": "dialga", "rating": 541}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 184}, {"opponent": "grou<PERSON>", "rating": 296}, {"opponent": "metagross", "rating": 319}, {"opponent": "dragonite", "rating": 481}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52675}, {"moveId": "MUD_SLAP", "uses": 23825}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 13264}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BULLDOZE", "uses": 12156}, {"moveId": "AVALANCHE", "uses": 38960}, {"moveId": "ANCIENT_POWER", "uses": 12013}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 92.7}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 674, "matchups": [{"opponent": "garcho<PERSON>", "rating": 858}, {"opponent": "giratina_origin", "rating": 802}, {"opponent": "lugia", "rating": 690}, {"opponent": "excadrill", "rating": 651}, {"opponent": "dialga", "rating": 606}], "counters": [{"opponent": "zacian_hero", "rating": 182}, {"opponent": "metagross", "rating": 264}, {"opponent": "gyarados", "rating": 425}, {"opponent": "swampert", "rating": 452}, {"opponent": "mewtwo", "rating": 481}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 51537}, {"moveId": "MUD_SLAP", "uses": 24963}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 12256}, {"moveId": "RETURN", "uses": 5756}, {"moveId": "BULLDOZE", "uses": 11117}, {"moveId": "AVALANCHE", "uses": 36177}, {"moveId": "ANCIENT_POWER", "uses": 11081}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 92.7}, {"speciesId": "primarina", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 666, "matchups": [{"opponent": "dragonite", "rating": 869}, {"opponent": "garcho<PERSON>", "rating": 845}, {"opponent": "zekrom", "rating": 845}, {"opponent": "gyarados", "rating": 619}, {"opponent": "dialga", "rating": 537}], "counters": [{"opponent": "lugia", "rating": 335}, {"opponent": "zacian_hero", "rating": 416}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "swampert", "rating": 467}, {"opponent": "giratina_origin", "rating": 498}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 37970}, {"moveId": "CHARM", "uses": 38530}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 18770}, {"moveId": "MOONBLAST", "uses": 37947}, {"moveId": "HYDRO_PUMP", "uses": 19675}]}, "moveset": ["CHARM", "MOONBLAST", "HYDRO_PUMP"], "score": 92.7}, {"speciesId": "nihilego", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 769, "matchups": [{"opponent": "zacian_hero", "rating": 724}, {"opponent": "lugia", "rating": 675}, {"opponent": "gyarados", "rating": 658}, {"opponent": "dragonite", "rating": 640}, {"opponent": "giratina_origin", "rating": 567}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "metagross", "rating": 241}, {"opponent": "dialga", "rating": 258}, {"opponent": "zekrom", "rating": 486}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 3065}, {"moveId": "POISON_JAB", "uses": 54998}, {"moveId": "ACID", "uses": 18553}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22023}, {"moveId": "ROCK_SLIDE", "uses": 41279}, {"moveId": "POWER_GEM", "uses": 7508}, {"moveId": "GUNK_SHOT", "uses": 5709}]}, "moveset": ["POISON_JAB", "ROCK_SLIDE", "SLUDGE_BOMB"], "score": 92.6}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 626, "matchups": [{"opponent": "ho_oh", "rating": 744, "opRating": 255}, {"opponent": "nihilego", "rating": 733, "opRating": 266}, {"opponent": "sylveon", "rating": 643, "opRating": 356}, {"opponent": "mewtwo_shadow", "rating": 637, "opRating": 362}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 632, "opRating": 367}], "counters": [{"opponent": "dialga", "rating": 320}, {"opponent": "gyarados", "rating": 350}, {"opponent": "metagross", "rating": 459}, {"opponent": "lugia", "rating": 461}, {"opponent": "mewtwo", "rating": 476}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 43801}, {"moveId": "METAL_CLAW", "uses": 32699}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 3852}, {"moveId": "HYDRO_CANNON", "uses": 35920}, {"moveId": "FLASH_CANNON", "uses": 7104}, {"moveId": "DRILL_PECK", "uses": 19059}, {"moveId": "BLIZZARD", "uses": 10614}]}, "moveset": ["WATERFALL", "HYDRO_CANNON", "FLASH_CANNON"], "score": 92.6}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 610, "matchups": [{"opponent": "kyogre", "rating": 761, "opRating": 238}, {"opponent": "sylveon", "rating": 683, "opRating": 316}, {"opponent": "zacian_hero", "rating": 665}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 619}, {"opponent": "swampert", "rating": 593}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "metagross", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 321}, {"opponent": "gyarados", "rating": 396}, {"opponent": "excadrill", "rating": 481}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53086}, {"moveId": "RAZOR_LEAF", "uses": 23414}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4238}, {"moveId": "SLUDGE_BOMB", "uses": 19373}, {"moveId": "RETURN", "uses": 9514}, {"moveId": "PETAL_BLIZZARD", "uses": 5214}, {"moveId": "FRENZY_PLANT", "uses": 38000}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 92.6}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 604, "matchups": [{"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "sylveon", "rating": 645, "opRating": 354}, {"opponent": "zacian_hero", "rating": 598}, {"opponent": "excadrill", "rating": 578}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 543}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 173}, {"opponent": "giratina_origin", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 368}, {"opponent": "gyarados", "rating": 427}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54180}, {"moveId": "RAZOR_LEAF", "uses": 22320}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4967}, {"moveId": "SLUDGE_BOMB", "uses": 22732}, {"moveId": "PETAL_BLIZZARD", "uses": 5924}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FRENZY_PLANT", "uses": 42973}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 92.6}, {"speciesId": "<PERSON><PERSON>_<PERSON>", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 552, "matchups": [{"opponent": "victini", "rating": 878, "opRating": 121}, {"opponent": "tangrowth_shadow", "rating": 762, "opRating": 237}, {"opponent": "charizard", "rating": 753, "opRating": 246}, {"opponent": "darmanitan_standard", "rating": 740, "opRating": 259}, {"opponent": "moltres_shadow", "rating": 722, "opRating": 277}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 265}, {"opponent": "mewtwo", "rating": 322}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "gyarados", "rating": 355}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 25573}, {"moveId": "FURY_CUTTER", "uses": 50927}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 11124}, {"moveId": "ROCK_BLAST", "uses": 33867}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CROSS_POISON", "uses": 31388}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 92.6}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "weavile", "rating": 887, "opRating": 112}, {"opponent": "weavile_shadow", "rating": 868, "opRating": 131}, {"opponent": "charizard_shadow", "rating": 753, "opRating": 246}, {"opponent": "celebi", "rating": 676, "opRating": 323}, {"opponent": "pinsir_shadow", "rating": 649, "opRating": 350}], "counters": [{"opponent": "lugia", "rating": 307}, {"opponent": "dialga", "rating": 312}, {"opponent": "gyarados", "rating": 317}, {"opponent": "mewtwo", "rating": 375}, {"opponent": "zacian_hero", "rating": 491}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 25171}, {"moveId": "FURY_CUTTER", "uses": 51329}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 9273}, {"moveId": "ROCK_BLAST", "uses": 29038}, {"moveId": "RETURN", "uses": 11290}, {"moveId": "CROSS_POISON", "uses": 26919}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 92.6}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 444, "matchups": [{"opponent": "machamp", "rating": 795, "opRating": 204}, {"opponent": "heracross", "rating": 795, "opRating": 204}, {"opponent": "machamp_shadow", "rating": 777, "opRating": 222}, {"opponent": "sneasler", "rating": 762, "opRating": 237}, {"opponent": "zap<PERSON>_galarian", "rating": 740, "opRating": 259}], "counters": [{"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "gyarados", "rating": 280}, {"opponent": "zacian_hero", "rating": 349}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 33337}, {"moveId": "CONFUSION", "uses": 43163}], "chargedMoves": [{"moveId": "SWIFT", "uses": 14270}, {"moveId": "FUTURE_SIGHT", "uses": 39684}, {"moveId": "FIRE_BLAST", "uses": 22577}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "FIRE_BLAST"], "score": 92.6}, {"speciesId": "ninetales_alolan", "speciesName": "Ninetales (Alolan)", "rating": 538, "matchups": [{"opponent": "dragonite_shadow", "rating": 878, "opRating": 121}, {"opponent": "dragonite", "rating": 854}, {"opponent": "zekrom", "rating": 810}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 577, "opRating": 422}, {"opponent": "garcho<PERSON>", "rating": 543}], "counters": [{"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 383}, {"opponent": "gyarados", "rating": 389}, {"opponent": "excadrill", "rating": 458}, {"opponent": "lugia", "rating": 478}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37385}, {"moveId": "FEINT_ATTACK", "uses": 17996}, {"moveId": "CHARM", "uses": 21173}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39774}, {"moveId": "PSYSHOCK", "uses": 12574}, {"moveId": "ICE_BEAM", "uses": 8583}, {"moveId": "DAZZLING_GLEAM", "uses": 9503}, {"moveId": "BLIZZARD", "uses": 5941}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "PSYSHOCK"], "score": 92.4}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 472, "matchups": [{"opponent": "machamp_shadow", "rating": 805, "opRating": 194}, {"opponent": "sneasler", "rating": 787, "opRating": 212}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 779, "opRating": 220}, {"opponent": "electivire", "rating": 744, "opRating": 255}, {"opponent": "machamp", "rating": 744, "opRating": 255}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "gyarados", "rating": 273}, {"opponent": "dragonite", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "zacian_hero", "rating": 398}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 33697}, {"moveId": "CONFUSION", "uses": 42803}], "chargedMoves": [{"moveId": "SWIFT", "uses": 12375}, {"moveId": "FUTURE_SIGHT", "uses": 33686}, {"moveId": "BLIZZARD", "uses": 30332}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "BLIZZARD"], "score": 92.3}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 332, "matchups": [{"opponent": "gengar", "rating": 680, "opRating": 319}, {"opponent": "feraligatr", "rating": 662, "opRating": 337}, {"opponent": "trevenant", "rating": 656, "opRating": 343}, {"opponent": "mew", "rating": 517, "opRating": 482}, {"opponent": "giratina_origin", "rating": 514}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "metagross", "rating": 264}, {"opponent": "swampert", "rating": 328}, {"opponent": "lugia", "rating": 345}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 16660}, {"moveId": "FEINT_ATTACK", "uses": 59840}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35228}, {"moveId": "SOLAR_BEAM", "uses": 11482}, {"moveId": "MEGAHORN", "uses": 19473}, {"moveId": "HYPER_BEAM", "uses": 10238}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "MEGAHORN"], "score": 92.3}, {"speciesId": "meloetta_aria", "speciesName": "<PERSON><PERSON><PERSON> (Aria)", "rating": 787, "matchups": [{"opponent": "giratina_origin", "rating": 726}, {"opponent": "zacian_hero", "rating": 651}, {"opponent": "garcho<PERSON>", "rating": 646}, {"opponent": "mewtwo", "rating": 634}, {"opponent": "gyarados", "rating": 552}], "counters": [{"opponent": "metagross", "rating": 311}, {"opponent": "dialga", "rating": 369}, {"opponent": "zekrom", "rating": 383}, {"opponent": "lugia", "rating": 400}, {"opponent": "melmetal", "rating": 487}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 41474}, {"moveId": "CONFUSION", "uses": 35026}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 19605}, {"moveId": "PSYSHOCK", "uses": 30114}, {"moveId": "HYPER_BEAM", "uses": 13751}, {"moveId": "DAZZLING_GLEAM", "uses": 13215}]}, "moveset": ["QUICK_ATTACK", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 92.2}, {"speciesId": "carracosta", "speciesName": "Carracosta", "rating": 519, "matchups": [{"opponent": "ho_oh", "rating": 904, "opRating": 95}, {"opponent": "moltres", "rating": 898, "opRating": 101}, {"opponent": "ho_oh_shadow", "rating": 889, "opRating": 110}, {"opponent": "heatran", "rating": 874, "opRating": 125}, {"opponent": "entei", "rating": 803, "opRating": 196}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "metagross", "rating": 305}, {"opponent": "lugia", "rating": 354}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39349}, {"moveId": "ROCK_THROW", "uses": 37151}], "chargedMoves": [{"moveId": "SURF", "uses": 29813}, {"moveId": "BODY_SLAM", "uses": 25690}, {"moveId": "ANCIENT_POWER", "uses": 21026}]}, "moveset": ["WATER_GUN", "SURF", "BODY_SLAM"], "score": 92.2}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 601, "matchups": [{"opponent": "swampert", "rating": 936}, {"opponent": "kyogre", "rating": 728, "opRating": 271}, {"opponent": "excadrill", "rating": 711}, {"opponent": "zacian_hero", "rating": 536}, {"opponent": "garcho<PERSON>", "rating": 526}], "counters": [{"opponent": "lugia", "rating": 209}, {"opponent": "dialga", "rating": 244}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "gyarados", "rating": 342}, {"opponent": "grou<PERSON>", "rating": 383}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 16531}, {"moveId": "QUICK_ATTACK", "uses": 30511}, {"moveId": "BULLET_SEED", "uses": 29453}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4256}, {"moveId": "LEAF_BLADE", "uses": 51122}, {"moveId": "LAST_RESORT", "uses": 13852}, {"moveId": "ENERGY_BALL", "uses": 7222}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 92}, {"speciesId": "hitmontop", "speciesName": "Hitmontop", "rating": 426, "matchups": [{"opponent": "registeel", "rating": 881, "opRating": 118}, {"opponent": "melmetal", "rating": 618, "opRating": 381}, {"opponent": "hydreigon", "rating": 578, "opRating": 421}, {"opponent": "snorlax", "rating": 566, "opRating": 433}, {"opponent": "excadrill", "rating": 547}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "gyarados", "rating": 146}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 317}, {"opponent": "metagross", "rating": 383}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 13101}, {"moveId": "COUNTER", "uses": 63399}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 22107}, {"moveId": "GYRO_BALL", "uses": 8122}, {"moveId": "CLOSE_COMBAT", "uses": 46268}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "STONE_EDGE"], "score": 92}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 409, "matchups": [{"opponent": "trevenant", "rating": 808, "opRating": 191}, {"opponent": "gengar", "rating": 768, "opRating": 231}, {"opponent": "virizion", "rating": 696, "opRating": 303}, {"opponent": "giratina_origin", "rating": 619}, {"opponent": "sneasler", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "mewtwo", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "gyarados", "rating": 301}, {"opponent": "zacian_hero", "rating": 306}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 45398}, {"moveId": "EXTRASENSORY", "uses": 31102}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33872}, {"moveId": "SHADOW_BALL", "uses": 23968}, {"moveId": "PSYCHIC", "uses": 14221}, {"moveId": "NIGHT_SHADE", "uses": 4428}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "SHADOW_BALL"], "score": 92}, {"speciesId": "mewtwo_shadow", "speciesName": "<PERSON><PERSON>t<PERSON> (Shadow)", "rating": 796, "matchups": [{"opponent": "gyarados", "rating": 747}, {"opponent": "metagross", "rating": 747}, {"opponent": "giratina_origin", "rating": 653}, {"opponent": "excadrill", "rating": 614}, {"opponent": "dialga", "rating": 513}], "counters": [{"opponent": "lugia", "rating": 366}, {"opponent": "grou<PERSON>", "rating": 421}, {"opponent": "garcho<PERSON>", "rating": 443}, {"opponent": "zacian_hero", "rating": 471}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 47826}, {"moveId": "CONFUSION", "uses": 28674}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 8480}, {"moveId": "SHADOW_BALL", "uses": 11890}, {"moveId": "PSYSTRIKE", "uses": 19748}, {"moveId": "PSYCHIC", "uses": 4610}, {"moveId": "ICE_BEAM", "uses": 11522}, {"moveId": "HYPER_BEAM", "uses": 3739}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOCUS_BLAST", "uses": 7597}, {"moveId": "FLAMETHROWER", "uses": 8763}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 91.9}, {"speciesId": "mewtwo", "speciesName": "Mewtwo", "rating": 788, "matchups": [{"opponent": "excadrill", "rating": 617}, {"opponent": "swampert", "rating": 604}, {"opponent": "zacian_hero", "rating": 552}, {"opponent": "lugia", "rating": 531}, {"opponent": "garcho<PERSON>", "rating": 526}], "counters": [{"opponent": "giratina_origin", "rating": 316}, {"opponent": "dialga", "rating": 388}, {"opponent": "dragonite", "rating": 417}, {"opponent": "gyarados", "rating": 422}, {"opponent": "metagross", "rating": 476}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 46747}, {"moveId": "CONFUSION", "uses": 29753}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 8121}, {"moveId": "SHADOW_BALL", "uses": 11491}, {"moveId": "RETURN", "uses": 4638}, {"moveId": "PSYSTRIKE", "uses": 19057}, {"moveId": "PSYCHIC", "uses": 4399}, {"moveId": "ICE_BEAM", "uses": 11096}, {"moveId": "HYPER_BEAM", "uses": 1741}, {"moveId": "FOCUS_BLAST", "uses": 7342}, {"moveId": "FLAMETHROWER", "uses": 8442}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 91.9}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 474, "matchups": [{"opponent": "tapu_bulu", "rating": 904, "opRating": 95}, {"opponent": "sylveon", "rating": 739, "opRating": 260}, {"opponent": "florges", "rating": 739, "opRating": 260}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 713}, {"opponent": "zacian_hero", "rating": 585}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 195}, {"opponent": "gyarados", "rating": 219}, {"opponent": "lugia", "rating": 235}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 41239}, {"moveId": "CONFUSION", "uses": 35261}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 28879}, {"moveId": "PSYCHIC", "uses": 27228}, {"moveId": "FOCUS_BLAST", "uses": 20349}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PSYCHIC"], "score": 91.9}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 528, "matchups": [{"opponent": "tapu_bulu", "rating": 895, "opRating": 104}, {"opponent": "x<PERSON><PERSON>", "rating": 643, "opRating": 356}, {"opponent": "mewtwo", "rating": 604}, {"opponent": "zarude", "rating": 580, "opRating": 419}, {"opponent": "mewtwo_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "lugia", "rating": 323}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "metagross", "rating": 375}, {"opponent": "giratina_origin", "rating": 388}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 48176}, {"moveId": "BITE", "uses": 28324}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 21343}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 16109}, {"moveId": "CRUNCH", "uses": 38954}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 91.8}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 528, "matchups": [{"opponent": "tapu_bulu", "rating": 912, "opRating": 87}, {"opponent": "florges", "rating": 663, "opRating": 336}, {"opponent": "mewtwo_shadow", "rating": 604, "opRating": 395}, {"opponent": "sylveon", "rating": 580, "opRating": 419}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 536}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "lugia", "rating": 283}, {"opponent": "giratina_origin", "rating": 334}, {"opponent": "zacian_hero", "rating": 407}, {"opponent": "mewtwo", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 46986}, {"moveId": "BITE", "uses": 29514}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 18494}, {"moveId": "RETURN", "uses": 8917}, {"moveId": "FLAMETHROWER", "uses": 14465}, {"moveId": "CRUNCH", "uses": 34624}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 91.8}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 514, "matchups": [{"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "ho_oh_shadow", "rating": 859, "opRating": 140}, {"opponent": "ho_oh", "rating": 846, "opRating": 153}, {"opponent": "moltres_shadow", "rating": 846, "opRating": 153}, {"opponent": "gyarados", "rating": 630}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "lugia", "rating": 359}, {"opponent": "dragonite", "rating": 470}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 8491}, {"moveId": "SMACK_DOWN", "uses": 68009}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 38189}, {"moveId": "OUTRAGE", "uses": 19214}, {"moveId": "FLAMETHROWER", "uses": 19179}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "OUTRAGE"], "score": 91.8}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 455, "matchups": [{"opponent": "gengar", "rating": 783, "opRating": 216}, {"opponent": "giratina_origin", "rating": 620}, {"opponent": "victini", "rating": 581, "opRating": 418}, {"opponent": "magnezone", "rating": 564, "opRating": 435}, {"opponent": "magnezone_shadow", "rating": 536, "opRating": 463}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "metagross", "rating": 293}, {"opponent": "garcho<PERSON>", "rating": 300}, {"opponent": "dialga", "rating": 410}, {"opponent": "excadrill", "rating": 460}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 57429}, {"moveId": "LOW_KICK", "uses": 19071}], "chargedMoves": [{"moveId": "STOMP", "uses": 17811}, {"moveId": "POWER_UP_PUNCH", "uses": 2744}, {"moveId": "OUTRAGE", "uses": 11604}, {"moveId": "EARTHQUAKE", "uses": 11451}, {"moveId": "CRUNCH", "uses": 19636}, {"moveId": "BRICK_BREAK", "uses": 13318}]}, "moveset": ["MUD_SLAP", "CRUNCH", "STOMP"], "score": 91.8}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 505, "matchups": [{"opponent": "metagross", "rating": 763}, {"opponent": "genesect_shock", "rating": 757, "opRating": 242}, {"opponent": "genesect_douse", "rating": 757, "opRating": 242}, {"opponent": "sylveon", "rating": 562, "opRating": 437}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 518}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "dialga", "rating": 269}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "excadrill", "rating": 295}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 28375}, {"moveId": "FEINT_ATTACK", "uses": 20915}, {"moveId": "EMBER", "uses": 27218}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 30703}, {"moveId": "SOLAR_BEAM", "uses": 7436}, {"moveId": "PSYSHOCK", "uses": 13538}, {"moveId": "OVERHEAT", "uses": 12344}, {"moveId": "HEAT_WAVE", "uses": 2152}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 6634}, {"moveId": "FIRE_BLAST", "uses": 3553}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 91.8}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 488, "matchups": [{"opponent": "genesect_shock", "rating": 776, "opRating": 223}, {"opponent": "genesect_douse", "rating": 776, "opRating": 223}, {"opponent": "genesect_chill", "rating": 776, "opRating": 223}, {"opponent": "genesect_burn", "rating": 776, "opRating": 223}, {"opponent": "genesect", "rating": 776, "opRating": 223}], "counters": [{"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 277}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "metagross", "rating": 441}, {"opponent": "excadrill", "rating": 451}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 27805}, {"moveId": "FEINT_ATTACK", "uses": 20998}, {"moveId": "EMBER", "uses": 27704}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28172}, {"moveId": "SOLAR_BEAM", "uses": 6623}, {"moveId": "RETURN", "uses": 6569}, {"moveId": "PSYSHOCK", "uses": 12269}, {"moveId": "OVERHEAT", "uses": 11271}, {"moveId": "HEAT_WAVE", "uses": 2000}, {"moveId": "FLAMETHROWER", "uses": 6142}, {"moveId": "FIRE_BLAST", "uses": 3306}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 91.8}, {"speciesId": "sudowoodo", "speciesName": "Sudowoodo", "rating": 427, "matchups": [{"opponent": "weavile_shadow", "rating": 863, "opRating": 136}, {"opponent": "weavile", "rating": 843, "opRating": 156}, {"opponent": "moltres", "rating": 796, "opRating": 203}, {"opponent": "moltres_shadow", "rating": 767, "opRating": 232}, {"opponent": "glaceon", "rating": 710, "opRating": 289}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "lugia", "rating": 214}, {"opponent": "metagross", "rating": 247}, {"opponent": "excadrill", "rating": 258}, {"opponent": "dialga", "rating": 369}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32190}, {"moveId": "COUNTER", "uses": 44310}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 12084}, {"moveId": "ROCK_TOMB", "uses": 6527}, {"moveId": "ROCK_SLIDE", "uses": 30330}, {"moveId": "RETURN", "uses": 9236}, {"moveId": "EARTHQUAKE", "uses": 18330}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 91.8}, {"speciesId": "sudowoodo_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 424, "matchups": [{"opponent": "chandelure", "rating": 773, "opRating": 226}, {"opponent": "moltres", "rating": 767, "opRating": 232}, {"opponent": "ho_oh_shadow", "rating": 757, "opRating": 242}, {"opponent": "moltres_shadow", "rating": 738, "opRating": 261}, {"opponent": "entei_shadow", "rating": 719, "opRating": 280}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "lugia", "rating": 223}, {"opponent": "excadrill", "rating": 304}, {"opponent": "dialga", "rating": 320}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 31533}, {"moveId": "COUNTER", "uses": 44967}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 13664}, {"moveId": "ROCK_TOMB", "uses": 7452}, {"moveId": "ROCK_SLIDE", "uses": 34375}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 20984}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 91.8}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 401, "matchups": [{"opponent": "pinsir_shadow", "rating": 700, "opRating": 299}, {"opponent": "barbara<PERSON>", "rating": 639, "opRating": 360}, {"opponent": "gardevoir", "rating": 549, "opRating": 450}, {"opponent": "primarina", "rating": 517, "opRating": 482}, {"opponent": "zarude", "rating": 514, "opRating": 485}], "counters": [{"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "dialga", "rating": 195}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "metagross", "rating": 235}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 13337}, {"moveId": "INFESTATION", "uses": 63163}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20676}, {"moveId": "GUNK_SHOT", "uses": 12004}, {"moveId": "BODY_SLAM", "uses": 38574}, {"moveId": "ACID_SPRAY", "uses": 5240}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 91.8}, {"speciesId": "regice", "speciesName": "Regice", "rating": 655, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "gyarados", "rating": 627}, {"opponent": "swampert", "rating": 566, "opRating": 433}, {"opponent": "kyogre", "rating": 552, "opRating": 447}, {"opponent": "excadrill", "rating": 502}], "counters": [{"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "dialga", "rating": 388}, {"opponent": "lugia", "rating": 407}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 9504}, {"moveId": "LOCK_ON", "uses": 42442}, {"moveId": "FROST_BREATH", "uses": 24566}], "chargedMoves": [{"moveId": "THUNDER", "uses": 16942}, {"moveId": "FOCUS_BLAST", "uses": 14223}, {"moveId": "EARTHQUAKE", "uses": 19605}, {"moveId": "BLIZZARD", "uses": 25653}]}, "moveset": ["LOCK_ON", "THUNDER", "EARTHQUAKE"], "score": 91.7}, {"speciesId": "be<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 464, "matchups": [{"opponent": "sneasler", "rating": 783, "opRating": 216}, {"opponent": "blaziken", "rating": 777, "opRating": 222}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 722, "opRating": 277}, {"opponent": "ho_oh", "rating": 685, "opRating": 314}, {"opponent": "zap<PERSON>_galarian", "rating": 560, "opRating": 439}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "gyarados", "rating": 273}, {"opponent": "excadrill", "rating": 444}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 55693}, {"moveId": "ASTONISH", "uses": 20807}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29086}, {"moveId": "PSYCHIC", "uses": 22751}, {"moveId": "DARK_PULSE", "uses": 24808}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "DARK_PULSE"], "score": 91.7}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 335, "matchups": [{"opponent": "primarina", "rating": 579, "opRating": 420}, {"opponent": "gardevoir", "rating": 579, "opRating": 420}, {"opponent": "chesnaught", "rating": 572, "opRating": 427}, {"opponent": "tapu_bulu", "rating": 534, "opRating": 465}, {"opponent": "gardevoir_shadow", "rating": 509, "opRating": 490}], "counters": [{"opponent": "lugia", "rating": 166}, {"opponent": "dialga", "rating": 173}, {"opponent": "gyarados", "rating": 185}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 452}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 21686}, {"moveId": "INFESTATION", "uses": 54814}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 28773}, {"moveId": "ICE_BEAM", "uses": 33589}, {"moveId": "GUNK_SHOT", "uses": 7472}, {"moveId": "ACID_SPRAY", "uses": 6612}]}, "moveset": ["INFESTATION", "ICE_BEAM", "SLUDGE_BOMB"], "score": 91.7}, {"speciesId": "hoopa_unbound", "speciesName": "<PERSON><PERSON><PERSON> (Unbound)", "rating": 605, "matchups": [{"opponent": "sneasler", "rating": 901, "opRating": 98}, {"opponent": "mewtwo_armored", "rating": 866, "opRating": 133}, {"opponent": "mewtwo", "rating": 624}, {"opponent": "snorlax", "rating": 624, "opRating": 375}, {"opponent": "giratina_altered", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "gyarados", "rating": 381}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "lugia", "rating": 423}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56645}, {"moveId": "ASTONISH", "uses": 19855}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 23160}, {"moveId": "PSYCHIC", "uses": 22436}, {"moveId": "DARK_PULSE", "uses": 30848}]}, "moveset": ["CONFUSION", "DARK_PULSE", "SHADOW_BALL"], "score": 91.6}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 493, "matchups": [{"opponent": "garcho<PERSON>", "rating": 848}, {"opponent": "zapdos", "rating": 721, "opRating": 278}, {"opponent": "zap<PERSON>_shadow", "rating": 676, "opRating": 323}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 549}, {"opponent": "zekrom", "rating": 549, "opRating": 450}], "counters": [{"opponent": "lugia", "rating": 316}, {"opponent": "dragonite", "rating": 321}, {"opponent": "dialga", "rating": 353}, {"opponent": "giratina_origin", "rating": 386}, {"opponent": "excadrill", "rating": 434}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44599}, {"moveId": "ICE_SHARD", "uses": 31901}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15296}, {"moveId": "RETURN", "uses": 6834}, {"moveId": "BULLDOZE", "uses": 12861}, {"moveId": "AVALANCHE", "uses": 41410}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 91.5}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 482, "matchups": [{"opponent": "garcho<PERSON>", "rating": 818}, {"opponent": "zapdos", "rating": 676, "opRating": 323}, {"opponent": "ho_oh", "rating": 524, "opRating": 475}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 519, "opRating": 480}, {"opponent": "zekrom", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 366}, {"opponent": "lugia", "rating": 376}, {"opponent": "dragonite", "rating": 385}, {"opponent": "giratina_origin", "rating": 454}, {"opponent": "excadrill", "rating": 497}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 45000}, {"moveId": "ICE_SHARD", "uses": 31500}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17119}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BULLDOZE", "uses": 14143}, {"moveId": "AVALANCHE", "uses": 45134}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 91.5}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 476, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 736, "opRating": 263}, {"opponent": "snor<PERSON>_shadow", "rating": 712, "opRating": 287}, {"opponent": "zarude", "rating": 712, "opRating": 287}, {"opponent": "ho_oh", "rating": 618, "opRating": 381}, {"opponent": "cobalion", "rating": 594, "opRating": 405}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "zacian_hero", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "swampert", "rating": 407}, {"opponent": "metagross", "rating": 430}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 11580}, {"moveId": "LOW_KICK", "uses": 7842}, {"moveId": "DOUBLE_KICK", "uses": 57078}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15000}, {"moveId": "STOMP", "uses": 10195}, {"moveId": "RETURN", "uses": 6017}, {"moveId": "LOW_SWEEP", "uses": 3744}, {"moveId": "CLOSE_COMBAT", "uses": 28853}, {"moveId": "BRICK_BREAK", "uses": 12764}]}, "moveset": ["DOUBLE_KICK", "CLOSE_COMBAT", "STONE_EDGE"], "score": 91.5}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 536, "matchups": [{"opponent": "florges", "rating": 738, "opRating": 261}, {"opponent": "sylveon", "rating": 718, "opRating": 281}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 683}, {"opponent": "zacian_hero", "rating": 680}, {"opponent": "yveltal", "rating": 607, "opRating": 392}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "lugia", "rating": 276}, {"opponent": "dragonite", "rating": 292}, {"opponent": "gyarados", "rating": 471}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56313}, {"moveId": "ACID", "uses": 20187}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 15559}, {"moveId": "SCALD", "uses": 31021}, {"moveId": "HYDRO_PUMP", "uses": 6925}, {"moveId": "BLIZZARD", "uses": 17581}, {"moveId": "ACID_SPRAY", "uses": 5310}]}, "moveset": ["POISON_JAB", "SCALD", "BLIZZARD"], "score": 91.4}, {"speciesId": "golem_alolan", "speciesName": "Golem (Alolan)", "rating": 590, "matchups": [{"opponent": "ho_oh", "rating": 840, "opRating": 159}, {"opponent": "yveltal", "rating": 639, "opRating": 360}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 604, "opRating": 395}, {"opponent": "lugia", "rating": 552}, {"opponent": "sylveon", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "giratina_origin", "rating": 274}, {"opponent": "metagross", "rating": 398}, {"opponent": "gyarados", "rating": 420}, {"opponent": "mewtwo", "rating": 466}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 34340}, {"moveId": "ROLLOUT", "uses": 24143}, {"moveId": "ROCK_THROW", "uses": 18051}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37056}, {"moveId": "STONE_EDGE", "uses": 20633}, {"moveId": "ROCK_BLAST", "uses": 18797}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "STONE_EDGE"], "score": 91.3}, {"speciesId": "land<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 747, "matchups": [{"opponent": "excadrill", "rating": 948}, {"opponent": "dialga", "rating": 698}, {"opponent": "garcho<PERSON>", "rating": 570}, {"opponent": "zekrom", "rating": 554, "opRating": 445}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 519, "opRating": 480}], "counters": [{"opponent": "giratina_origin", "rating": 316}, {"opponent": "lugia", "rating": 326}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "mewtwo", "rating": 421}, {"opponent": "metagross", "rating": 424}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51412}, {"moveId": "EXTRASENSORY", "uses": 25088}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24335}, {"moveId": "STONE_EDGE", "uses": 22008}, {"moveId": "EARTHQUAKE", "uses": 18650}, {"moveId": "BULLDOZE", "uses": 11384}]}, "moveset": ["MUD_SHOT", "SUPER_POWER", "STONE_EDGE"], "score": 91.2}, {"speciesId": "aurorus", "speciesName": "Au<PERSON><PERSON>", "rating": 614, "matchups": [{"opponent": "garcho<PERSON>", "rating": 844}, {"opponent": "dragonite", "rating": 795}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 636}, {"opponent": "gyarados", "rating": 604}, {"opponent": "giratina_origin", "rating": 519}], "counters": [{"opponent": "zacian_hero", "rating": 234}, {"opponent": "excadrill", "rating": 337}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "dialga", "rating": 399}, {"opponent": "lugia", "rating": 433}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 20892}, {"moveId": "POWDER_SNOW", "uses": 37837}, {"moveId": "FROST_BREATH", "uses": 17785}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 38366}, {"moveId": "THUNDERBOLT", "uses": 10716}, {"moveId": "HYPER_BEAM", "uses": 4817}, {"moveId": "BLIZZARD", "uses": 5852}, {"moveId": "ANCIENT_POWER", "uses": 16824}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 91.2}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 742, "matchups": [{"opponent": "sylveon", "rating": 798, "opRating": 201}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 773}, {"opponent": "zacian_hero", "rating": 699}, {"opponent": "dragonite", "rating": 681}, {"opponent": "gyarados", "rating": 614}], "counters": [{"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "dialga", "rating": 317}, {"opponent": "excadrill", "rating": 379}, {"opponent": "lugia", "rating": 383}, {"opponent": "mewtwo", "rating": 388}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 46836}, {"moveId": "CHARGE_BEAM", "uses": 29664}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 20202}, {"moveId": "DOOM_DESIRE", "uses": 45655}, {"moveId": "DAZZLING_GLEAM", "uses": 10555}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 91.1}, {"speciesId": "clefable", "speciesName": "Clefable", "rating": 541, "matchups": [{"opponent": "dragonite", "rating": 858}, {"opponent": "palkia", "rating": 664, "opRating": 335}, {"opponent": "yveltal", "rating": 657, "opRating": 342}, {"opponent": "garcho<PERSON>", "rating": 528}, {"opponent": "zekrom", "rating": 507, "opRating": 492}], "counters": [{"opponent": "mewtwo", "rating": 307}, {"opponent": "dialga", "rating": 331}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "giratina_origin", "rating": 358}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 5624}, {"moveId": "POUND", "uses": 3108}, {"moveId": "CHARM", "uses": 41475}, {"moveId": "CHARGE_BEAM", "uses": 26296}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 15000}, {"moveId": "MOONBLAST", "uses": 28514}, {"moveId": "METEOR_MASH", "uses": 25673}, {"moveId": "DAZZLING_GLEAM", "uses": 7275}]}, "moveset": ["CHARM", "MOONBLAST", "METEOR_MASH"], "score": 91.1}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 529, "matchups": [{"opponent": "heatran", "rating": 808, "opRating": 191}, {"opponent": "metagross", "rating": 778}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 674, "opRating": 325}, {"opponent": "sylveon", "rating": 593, "opRating": 406}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "lugia", "rating": 311}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "dialga", "rating": 388}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 4765}, {"moveId": "INCINERATE", "uses": 31917}, {"moveId": "FIRE_SPIN", "uses": 20185}, {"moveId": "EMBER", "uses": 19675}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 3688}, {"moveId": "FLAME_CHARGE", "uses": 27468}, {"moveId": "FIRE_BLAST", "uses": 12373}, {"moveId": "DRILL_RUN", "uses": 32920}]}, "moveset": ["INCINERATE", "DRILL_RUN", "FLAME_CHARGE"], "score": 91.1}, {"speciesId": "golduck", "speciesName": "Gold<PERSON>", "rating": 485, "matchups": [{"opponent": "aurorus", "rating": 875, "opRating": 125}, {"opponent": "rhyperior", "rating": 694, "opRating": 305}, {"opponent": "ma<PERSON><PERSON>", "rating": 633, "opRating": 366}, {"opponent": "landorus_incarnate", "rating": 598, "opRating": 401}, {"opponent": "excadrill", "rating": 578}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "metagross", "rating": 281}, {"opponent": "swampert", "rating": 422}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41589}, {"moveId": "CONFUSION", "uses": 34911}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 10708}, {"moveId": "RETURN", "uses": 7189}, {"moveId": "PSYCHIC", "uses": 4866}, {"moveId": "ICE_BEAM", "uses": 16447}, {"moveId": "HYDRO_PUMP", "uses": 9228}, {"moveId": "CROSS_CHOP", "uses": 21099}, {"moveId": "BUBBLE_BEAM", "uses": 6870}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 91}, {"speciesId": "<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 478, "matchups": [{"opponent": "typhlosion", "rating": 822, "opRating": 177}, {"opponent": "darmanitan_standard", "rating": 793, "opRating": 206}, {"opponent": "heatran", "rating": 787, "opRating": 212}, {"opponent": "chandelure", "rating": 779, "opRating": 220}, {"opponent": "moltres_shadow", "rating": 735, "opRating": 264}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "metagross", "rating": 363}, {"opponent": "excadrill", "rating": 393}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39895}, {"moveId": "CONFUSION", "uses": 36605}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 11949}, {"moveId": "PSYCHIC", "uses": 5450}, {"moveId": "ICE_BEAM", "uses": 18263}, {"moveId": "HYDRO_PUMP", "uses": 10216}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CROSS_CHOP", "uses": 22970}, {"moveId": "BUBBLE_BEAM", "uses": 7544}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 91}, {"speciesId": "hunt<PERSON>", "speciesName": "Huntail", "rating": 448, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 788, "opRating": 211}, {"opponent": "moltres", "rating": 788, "opRating": 211}, {"opponent": "entei_shadow", "rating": 774, "opRating": 225}, {"opponent": "moltres_shadow", "rating": 759, "opRating": 240}, {"opponent": "entei", "rating": 744, "opRating": 255}], "counters": [{"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "metagross", "rating": 366}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 49362}, {"moveId": "BITE", "uses": 27138}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 19103}, {"moveId": "CRUNCH", "uses": 24017}, {"moveId": "AQUA_TAIL", "uses": 33339}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "CRUNCH"], "score": 91}, {"speciesId": "sigilyph", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 376, "matchups": [{"opponent": "virizion", "rating": 790, "opRating": 209}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 678, "opRating": 321}, {"opponent": "gallade", "rating": 631, "opRating": 368}, {"opponent": "heracross", "rating": 609, "opRating": 390}, {"opponent": "buzzwole", "rating": 606, "opRating": 393}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "lugia", "rating": 223}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 18338}, {"moveId": "AIR_SLASH", "uses": 58162}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 19480}, {"moveId": "PSYBEAM", "uses": 13061}, {"moveId": "ANCIENT_POWER", "uses": 28862}, {"moveId": "AIR_CUTTER", "uses": 15116}]}, "moveset": ["AIR_SLASH", "ANCIENT_POWER", "SIGNAL_BEAM"], "score": 91}, {"speciesId": "tapu_fini", "speciesName": "<PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "grou<PERSON>", "rating": 853}, {"opponent": "excadrill", "rating": 671}, {"opponent": "dragonite", "rating": 665}, {"opponent": "garcho<PERSON>", "rating": 601}, {"opponent": "gyarados", "rating": 601}], "counters": [{"opponent": "metagross", "rating": 313}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "mewtwo", "rating": 382}, {"opponent": "lugia", "rating": 454}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 8514}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5161}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3674}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4570}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3584}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3405}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5688}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4731}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3661}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4568}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4150}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4215}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4175}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4129}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4263}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4467}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3628}], "chargedMoves": [{"moveId": "SURF", "uses": 31417}, {"moveId": "MOONBLAST", "uses": 22736}, {"moveId": "ICE_BEAM", "uses": 17305}, {"moveId": "HYDRO_PUMP", "uses": 4984}]}, "moveset": ["WATER_GUN", "SURF", "MOONBLAST"], "score": 90.9}, {"speciesId": "slowking", "speciesName": "Slowking", "rating": 431, "matchups": [{"opponent": "blaziken", "rating": 796, "opRating": 203}, {"opponent": "sneasler", "rating": 788, "opRating": 211}, {"opponent": "machamp", "rating": 615, "opRating": 384}, {"opponent": "zacian_hero", "rating": 605}, {"opponent": "machamp_shadow", "rating": 603, "opRating": 396}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dragonite", "rating": 226}, {"opponent": "garcho<PERSON>", "rating": 255}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 37882}, {"moveId": "CONFUSION", "uses": 38618}], "chargedMoves": [{"moveId": "RETURN", "uses": 14136}, {"moveId": "PSYCHIC", "uses": 26853}, {"moveId": "FIRE_BLAST", "uses": 14101}, {"moveId": "BLIZZARD", "uses": 21445}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 90.9}, {"speciesId": "slowking_shadow", "speciesName": "Slowking (Shadow)", "rating": 429, "matchups": [{"opponent": "blaziken", "rating": 817, "opRating": 182}, {"opponent": "sneasler", "rating": 798, "opRating": 201}, {"opponent": "roserade", "rating": 765, "opRating": 234}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 726, "opRating": 273}, {"opponent": "machamp", "rating": 603, "opRating": 396}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 176}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "gyarados", "rating": 273}, {"opponent": "zacian_hero", "rating": 280}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 36441}, {"moveId": "CONFUSION", "uses": 40059}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 33183}, {"moveId": "FRUSTRATION", "uses": 8}, {"moveId": "FIRE_BLAST", "uses": 17151}, {"moveId": "BLIZZARD", "uses": 26051}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 90.9}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "garcho<PERSON>", "rating": 924}, {"opponent": "dragonite", "rating": 808}, {"opponent": "gyarados", "rating": 558}, {"opponent": "giratina_origin", "rating": 544}, {"opponent": "lugia", "rating": 528}], "counters": [{"opponent": "excadrill", "rating": 304}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "dialga", "rating": 421}, {"opponent": "swampert", "rating": 480}, {"opponent": "zacian_hero", "rating": 488}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_WATER", "uses": 3741}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3383}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4201}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3206}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3131}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4942}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4404}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3362}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4251}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4865}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4168}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3891}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3781}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3813}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4202}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3321}, {"moveId": "CHARM", "uses": 7519}, {"moveId": "AIR_SLASH", "uses": 6333}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 18665}, {"moveId": "DAZZLING_GLEAM", "uses": 16758}, {"moveId": "ANCIENT_POWER", "uses": 21444}, {"moveId": "AERIAL_ACE", "uses": 19677}]}, "moveset": ["CHARM", "ANCIENT_POWER", "FLAMETHROWER"], "score": 90.8}, {"speciesId": "con<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 658, "matchups": [{"opponent": "swampert", "rating": 668}, {"opponent": "excadrill", "rating": 656}, {"opponent": "gyarados", "rating": 572}, {"opponent": "dialga", "rating": 550}, {"opponent": "garcho<PERSON>", "rating": 514}], "counters": [{"opponent": "giratina_origin", "rating": 121}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "grou<PERSON>", "rating": 269}, {"opponent": "metagross", "rating": 308}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 30548}, {"moveId": "COUNTER", "uses": 45952}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 30455}, {"moveId": "FOCUS_BLAST", "uses": 9869}, {"moveId": "DYNAMIC_PUNCH", "uses": 36169}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "STONE_EDGE"], "score": 90.8}, {"speciesId": "lunatone", "speciesName": "Lunatone", "rating": 387, "matchups": [{"opponent": "ho_oh", "rating": 803}, {"opponent": "sneasler", "rating": 760, "opRating": 239}, {"opponent": "typhlosion", "rating": 645, "opRating": 354}, {"opponent": "moltres", "rating": 537, "opRating": 462}, {"opponent": "zacian_hero", "rating": 532}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "dialga", "rating": 154}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "lugia", "rating": 242}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36487}, {"moveId": "CONFUSION", "uses": 40013}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 36346}, {"moveId": "PSYCHIC", "uses": 19662}, {"moveId": "MOONBLAST", "uses": 20448}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "MOONBLAST"], "score": 90.8}, {"speciesId": "kyogre", "speciesName": "Kyogre", "rating": 744, "matchups": [{"opponent": "excadrill", "rating": 717}, {"opponent": "metagross", "rating": 684}, {"opponent": "zacian_hero", "rating": 633}, {"opponent": "garcho<PERSON>", "rating": 600}, {"opponent": "mewtwo", "rating": 584}], "counters": [{"opponent": "dragonite", "rating": 215}, {"opponent": "giratina_origin", "rating": 324}, {"opponent": "dialga", "rating": 372}, {"opponent": "lugia", "rating": 409}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 76500}], "chargedMoves": [{"moveId": "THUNDER", "uses": 15179}, {"moveId": "SURF", "uses": 37750}, {"moveId": "HYDRO_PUMP", "uses": 6017}, {"moveId": "BLIZZARD", "uses": 17476}]}, "moveset": ["WATERFALL", "SURF", "BLIZZARD"], "score": 90.7}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 548, "matchups": [{"opponent": "swampert", "rating": 961}, {"opponent": "swampert_shadow", "rating": 951, "opRating": 48}, {"opponent": "excadrill", "rating": 726}, {"opponent": "grou<PERSON>", "rating": 646}, {"opponent": "kyogre", "rating": 626, "opRating": 373}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "mewtwo", "rating": 403}, {"opponent": "garcho<PERSON>", "rating": 448}, {"opponent": "gyarados", "rating": 456}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41814}, {"moveId": "BITE", "uses": 34686}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17848}, {"moveId": "SOLAR_BEAM", "uses": 3524}, {"moveId": "SAND_TOMB", "uses": 7884}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FRENZY_PLANT", "uses": 30944}, {"moveId": "EARTHQUAKE", "uses": 16340}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 90.7}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 526, "matchups": [{"opponent": "swampert_shadow", "rating": 961, "opRating": 38}, {"opponent": "swampert", "rating": 956}, {"opponent": "xurkitree", "rating": 902, "opRating": 97}, {"opponent": "rhyperior", "rating": 873, "opRating": 126}, {"opponent": "excadrill", "rating": 646}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "gyarados", "rating": 396}, {"opponent": "zacian_hero", "rating": 445}, {"opponent": "garcho<PERSON>", "rating": 446}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41228}, {"moveId": "BITE", "uses": 35272}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 16016}, {"moveId": "SOLAR_BEAM", "uses": 3141}, {"moveId": "SAND_TOMB", "uses": 7377}, {"moveId": "RETURN", "uses": 6268}, {"moveId": "FRENZY_PLANT", "uses": 28463}, {"moveId": "EARTHQUAKE", "uses": 15183}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 90.7}, {"speciesId": "hit<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 312, "matchups": [{"opponent": "weavile", "rating": 917, "opRating": 82}, {"opponent": "weavile_shadow", "rating": 917, "opRating": 82}, {"opponent": "bisharp", "rating": 917, "opRating": 82}, {"opponent": "sandslash_alolan", "rating": 803, "opRating": 196}, {"opponent": "mamos<PERSON>_shadow", "rating": 602, "opRating": 397}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "metagross", "rating": 264}, {"opponent": "dialga", "rating": 285}, {"opponent": "excadrill", "rating": 376}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 46835}, {"moveId": "LOW_KICK", "uses": 29665}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 16573}, {"moveId": "STOMP", "uses": 11335}, {"moveId": "LOW_SWEEP", "uses": 3979}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 30849}, {"moveId": "BRICK_BREAK", "uses": 13694}]}, "moveset": ["ROCK_SMASH", "CLOSE_COMBAT", "STONE_EDGE"], "score": 90.6}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 610, "matchups": [{"opponent": "swampert", "rating": 828}, {"opponent": "excadrill", "rating": 804}, {"opponent": "grou<PERSON>", "rating": 717}, {"opponent": "garcho<PERSON>", "rating": 597}, {"opponent": "gyarados", "rating": 589}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "giratina_origin", "rating": 197}, {"opponent": "metagross", "rating": 238}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "dialga", "rating": 372}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 43475}, {"moveId": "SMACK_DOWN", "uses": 24731}, {"moveId": "LOW_KICK", "uses": 8274}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 40634}, {"moveId": "SOLAR_BEAM", "uses": 5798}, {"moveId": "GYRO_BALL", "uses": 10243}, {"moveId": "ENERGY_BALL", "uses": 19892}]}, "moveset": ["VINE_WHIP", "SUPER_POWER", "ENERGY_BALL"], "score": 90.5}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 496, "matchups": [{"opponent": "gengar", "rating": 701, "opRating": 298}, {"opponent": "salamence_shadow", "rating": 672, "opRating": 327}, {"opponent": "trevenant", "rating": 667, "opRating": 332}, {"opponent": "giratina_origin", "rating": 610}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 605, "opRating": 394}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "lugia", "rating": 266}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "gyarados", "rating": 327}, {"opponent": "garcho<PERSON>", "rating": 441}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 5145}, {"moveId": "TACKLE", "uses": 35276}, {"moveId": "ROLLOUT", "uses": 36100}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 12096}, {"moveId": "STOMP", "uses": 8258}, {"moveId": "ICE_BEAM", "uses": 16634}, {"moveId": "GYRO_BALL", "uses": 6027}, {"moveId": "BODY_SLAM", "uses": 33457}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 90.5}, {"speciesId": "octillery", "speciesName": "Octillery", "rating": 314, "matchups": [{"opponent": "flygon", "rating": 692, "opRating": 307}, {"opponent": "gliscor_shadow", "rating": 640, "opRating": 359}, {"opponent": "exeggutor_alolan", "rating": 606, "opRating": 393}, {"opponent": "hippow<PERSON>_shadow", "rating": 521, "opRating": 478}, {"opponent": "salamence_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "gyarados", "rating": 208}, {"opponent": "dialga", "rating": 209}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "giratina_origin", "rating": 227}, {"opponent": "garcho<PERSON>", "rating": 380}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 24934}, {"moveId": "MUD_SHOT", "uses": 24561}, {"moveId": "LOCK_ON", "uses": 26995}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 8015}, {"moveId": "OCTAZOOKA", "uses": 26803}, {"moveId": "GUNK_SHOT", "uses": 13787}, {"moveId": "AURORA_BEAM", "uses": 21827}, {"moveId": "ACID_SPRAY", "uses": 5949}]}, "moveset": ["LOCK_ON", "OCTAZOOKA", "AURORA_BEAM"], "score": 90.5}, {"speciesId": "musharna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 408, "matchups": [{"opponent": "gallade", "rating": 660, "opRating": 340}, {"opponent": "gallade_shadow", "rating": 608, "opRating": 391}, {"opponent": "kommo_o", "rating": 571, "opRating": 428}, {"opponent": "machamp_shadow", "rating": 568, "opRating": 431}, {"opponent": "sneasler", "rating": 528, "opRating": 471}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "lugia", "rating": 230}, {"opponent": "garcho<PERSON>", "rating": 288}, {"opponent": "gyarados", "rating": 373}, {"opponent": "zacian_hero", "rating": 378}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 24640}, {"moveId": "CHARGE_BEAM", "uses": 51860}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 42380}, {"moveId": "FUTURE_SIGHT", "uses": 14238}, {"moveId": "DAZZLING_GLEAM", "uses": 19893}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 90.4}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 684, "matchups": [{"opponent": "gyarados", "rating": 698}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622}, {"opponent": "zekrom", "rating": 536}, {"opponent": "lugia", "rating": 519}, {"opponent": "metagross", "rating": 502}], "counters": [{"opponent": "giratina_origin", "rating": 189}, {"opponent": "zacian_hero", "rating": 335}, {"opponent": "dragonite", "rating": 351}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "dialga", "rating": 423}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40800}, {"moveId": "SPARK", "uses": 35700}], "chargedMoves": [{"moveId": "THUNDER", "uses": 10539}, {"moveId": "POWER_WHIP", "uses": 23103}, {"moveId": "DISCHARGE", "uses": 28547}, {"moveId": "DAZZLING_GLEAM", "uses": 14288}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "DAZZLING_GLEAM"], "score": 90.3}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 613, "matchups": [{"opponent": "mewtwo", "rating": 718}, {"opponent": "sylveon", "rating": 600, "opRating": 399}, {"opponent": "zacian_hero", "rating": 574}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 548}, {"opponent": "gyarados", "rating": 533}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dragonite", "rating": 210}, {"opponent": "dialga", "rating": 250}, {"opponent": "lugia", "rating": 352}, {"opponent": "giratina_origin", "rating": 372}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28421}, {"moveId": "POISON_JAB", "uses": 29643}, {"moveId": "BITE", "uses": 18430}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 19060}, {"moveId": "GUNK_SHOT", "uses": 7379}, {"moveId": "DARK_PULSE", "uses": 43642}, {"moveId": "ACID_SPRAY", "uses": 6428}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "SLUDGE_WAVE"], "score": 90.3}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 471, "matchups": [{"opponent": "articuno_galarian", "rating": 796, "opRating": 203}, {"opponent": "nihilego", "rating": 677, "opRating": 322}, {"opponent": "roserade", "rating": 644, "opRating": 355}, {"opponent": "cresselia", "rating": 641, "opRating": 358}, {"opponent": "sylveon", "rating": 519, "opRating": 480}], "counters": [{"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "dialga", "rating": 173}, {"opponent": "lugia", "rating": 280}, {"opponent": "gyarados", "rating": 353}, {"opponent": "zacian_hero", "rating": 473}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 33503}, {"moveId": "CONFUSION", "uses": 42997}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 20985}, {"moveId": "PSYCHIC", "uses": 8141}, {"moveId": "PAYBACK", "uses": 17236}, {"moveId": "HEAVY_SLAM", "uses": 15113}, {"moveId": "FLASH_CANNON", "uses": 4835}, {"moveId": "BULLDOZE", "uses": 10084}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 90.3}, {"speciesId": "tapu_bulu", "speciesName": "Tapu Bulu", "rating": 650, "matchups": [{"opponent": "swampert", "rating": 799}, {"opponent": "dragonite", "rating": 757}, {"opponent": "garcho<PERSON>", "rating": 732}, {"opponent": "gyarados", "rating": 659}, {"opponent": "zacian_hero", "rating": 503}], "counters": [{"opponent": "metagross", "rating": 229}, {"opponent": "lugia", "rating": 230}, {"opponent": "dialga", "rating": 336}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 370}, {"opponent": "mewtwo", "rating": 406}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 16857}, {"moveId": "BULLET_SEED", "uses": 59643}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 5973}, {"moveId": "MEGAHORN", "uses": 23260}, {"moveId": "GRASS_KNOT", "uses": 28661}, {"moveId": "DAZZLING_GLEAM", "uses": 18634}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "DAZZLING_GLEAM"], "score": 90.2}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 582, "matchups": [{"opponent": "mewtwo", "rating": 920}, {"opponent": "garcho<PERSON>", "rating": 837}, {"opponent": "giratina_origin", "rating": 805}, {"opponent": "dragonite", "rating": 576}, {"opponent": "zekrom", "rating": 576}], "counters": [{"opponent": "zacian_hero", "rating": 349}, {"opponent": "lugia", "rating": 385}, {"opponent": "dialga", "rating": 396}, {"opponent": "gyarados", "rating": 427}, {"opponent": "swampert", "rating": 427}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 31558}, {"moveId": "ICE_SHARD", "uses": 27706}, {"moveId": "FEINT_ATTACK", "uses": 17210}], "chargedMoves": [{"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOUL_PLAY", "uses": 23974}, {"moveId": "FOCUS_BLAST", "uses": 10746}, {"moveId": "AVALANCHE", "uses": 41685}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 90.1}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 591, "matchups": [{"opponent": "garcho<PERSON>", "rating": 856}, {"opponent": "mewtwo", "rating": 675}, {"opponent": "dragonite", "rating": 646}, {"opponent": "giratina_origin", "rating": 579}, {"opponent": "dialga", "rating": 503}], "counters": [{"opponent": "zacian_hero", "rating": 286}, {"opponent": "lugia", "rating": 323}, {"opponent": "gyarados", "rating": 360}, {"opponent": "excadrill", "rating": 409}, {"opponent": "grou<PERSON>", "rating": 437}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30701}, {"moveId": "ICE_SHARD", "uses": 27693}, {"moveId": "FEINT_ATTACK", "uses": 18133}], "chargedMoves": [{"moveId": "RETURN", "uses": 6568}, {"moveId": "FOUL_PLAY", "uses": 21883}, {"moveId": "FOCUS_BLAST", "uses": 9841}, {"moveId": "AVALANCHE", "uses": 38246}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 90.1}, {"speciesId": "milotic", "speciesName": "Milo<PERSON>", "rating": 661, "matchups": [{"opponent": "garcho<PERSON>", "rating": 595}, {"opponent": "gyarados", "rating": 567}, {"opponent": "giratina_origin", "rating": 543}, {"opponent": "ho_oh", "rating": 525, "opRating": 474}, {"opponent": "giratina_altered", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 345}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "dragonite", "rating": 457}, {"opponent": "metagross", "rating": 462}, {"opponent": "excadrill", "rating": 479}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 35781}, {"moveId": "DRAGON_TAIL", "uses": 40719}], "chargedMoves": [{"moveId": "SURF", "uses": 46235}, {"moveId": "HYPER_BEAM", "uses": 10146}, {"moveId": "BLIZZARD", "uses": 20007}]}, "moveset": ["DRAGON_TAIL", "SURF", "BLIZZARD"], "score": 90.1}, {"speciesId": "tapu_koko", "speciesName": "<PERSON><PERSON>", "rating": 637, "matchups": [{"opponent": "lugia", "rating": 719}, {"opponent": "dragonite", "rating": 691}, {"opponent": "gyarados", "rating": 681}, {"opponent": "zekrom", "rating": 560}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 538}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "zacian_hero", "rating": 421}, {"opponent": "dialga", "rating": 448}, {"opponent": "metagross", "rating": 450}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43931}, {"moveId": "QUICK_ATTACK", "uses": 32569}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 20730}, {"moveId": "THUNDER", "uses": 9180}, {"moveId": "DAZZLING_GLEAM", "uses": 15534}, {"moveId": "BRAVE_BIRD", "uses": 31125}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "DAZZLING_GLEAM"], "score": 90}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 558, "matchups": [{"opponent": "zarude", "rating": 910, "opRating": 89}, {"opponent": "tangrowth_shadow", "rating": 910, "opRating": 89}, {"opponent": "hydreigon", "rating": 715, "opRating": 284}, {"opponent": "latios", "rating": 683, "opRating": 316}, {"opponent": "mewtwo", "rating": 503}], "counters": [{"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "dialga", "rating": 301}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "lugia", "rating": 357}, {"opponent": "metagross", "rating": 398}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 33103}, {"moveId": "BUG_BITE", "uses": 43397}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 32680}, {"moveId": "STONE_EDGE", "uses": 23843}, {"moveId": "IRON_HEAD", "uses": 19962}]}, "moveset": ["BUG_BITE", "X_SCISSOR", "STONE_EDGE"], "score": 90}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 629, "matchups": [{"opponent": "gyarados", "rating": 704}, {"opponent": "sylveon", "rating": 570, "opRating": 429}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 560}, {"opponent": "metagross", "rating": 536}, {"opponent": "dialga", "rating": 509}], "counters": [{"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "excadrill", "rating": 234}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "lugia", "rating": 254}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39816}, {"moveId": "FIRE_SPIN", "uses": 36684}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 13332}, {"moveId": "RETURN", "uses": 8209}, {"moveId": "PSYCHIC", "uses": 11750}, {"moveId": "FIRE_PUNCH", "uses": 23058}, {"moveId": "FIRE_BLAST", "uses": 4694}, {"moveId": "BRICK_BREAK", "uses": 15634}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 89.9}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 655, "matchups": [{"opponent": "genesect_shock", "rating": 759, "opRating": 240}, {"opponent": "genesect_douse", "rating": 759, "opRating": 240}, {"opponent": "genesect_chill", "rating": 759, "opRating": 240}, {"opponent": "gyarados", "rating": 631}, {"opponent": "sylveon", "rating": 628, "opRating": 371}], "counters": [{"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "lugia", "rating": 285}, {"opponent": "metagross", "rating": 465}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 40073}, {"moveId": "FIRE_SPIN", "uses": 36427}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 15010}, {"moveId": "PSYCHIC", "uses": 13387}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_PUNCH", "uses": 25614}, {"moveId": "FIRE_BLAST", "uses": 5123}, {"moveId": "BRICK_BREAK", "uses": 17294}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 89.9}, {"speciesId": "glalie", "speciesName": "G<PERSON><PERSON>", "rating": 406, "matchups": [{"opponent": "hippow<PERSON>_shadow", "rating": 793, "opRating": 206}, {"opponent": "thundurus_therian", "rating": 700, "opRating": 299}, {"opponent": "zapdos", "rating": 622, "opRating": 377}, {"opponent": "hydreigon", "rating": 619, "opRating": 380}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "dialga", "rating": 298}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "dragonite", "rating": 321}, {"opponent": "lugia", "rating": 333}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46714}, {"moveId": "FROST_BREATH", "uses": 29786}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 20397}, {"moveId": "GYRO_BALL", "uses": 6982}, {"moveId": "AVALANCHE", "uses": 49165}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "SHADOW_BALL"], "score": 89.8}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 663, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 876, "opRating": 123}, {"opponent": "kommo_o", "rating": 748, "opRating": 251}, {"opponent": "terrakion", "rating": 709, "opRating": 290}, {"opponent": "swampert", "rating": 707}, {"opponent": "sylveon", "rating": 601, "opRating": 398}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "mewtwo", "rating": 356}, {"opponent": "gyarados", "rating": 363}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "garcho<PERSON>", "rating": 417}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 40778}, {"moveId": "CONFUSION", "uses": 35722}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 21478}, {"moveId": "GRASS_KNOT", "uses": 21703}, {"moveId": "FUTURE_SIGHT", "uses": 18978}, {"moveId": "AURORA_BEAM", "uses": 14327}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 89.7}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 374, "matchups": [{"opponent": "pangoro", "rating": 890, "opRating": 109}, {"opponent": "kommo_o", "rating": 634, "opRating": 365}, {"opponent": "hydreigon", "rating": 631, "opRating": 368}, {"opponent": "giratina_origin", "rating": 609}, {"opponent": "gallade_shadow", "rating": 585, "opRating": 414}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "gyarados", "rating": 250}, {"opponent": "dragonite", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 11905}, {"moveId": "CHARM", "uses": 64595}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 21568}, {"moveId": "HYPER_BEAM", "uses": 16162}, {"moveId": "AQUA_TAIL", "uses": 38734}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 89.7}, {"speciesId": "vaporeon", "speciesName": "Vaporeon", "rating": 658, "matchups": [{"opponent": "excadrill", "rating": 704}, {"opponent": "metagross", "rating": 659}, {"opponent": "swampert", "rating": 644}, {"opponent": "zacian_hero", "rating": 624}, {"opponent": "grou<PERSON>", "rating": 557}], "counters": [{"opponent": "dialga", "rating": 252}, {"opponent": "giratina_origin", "rating": 264}, {"opponent": "mewtwo", "rating": 359}, {"opponent": "gyarados", "rating": 376}, {"opponent": "garcho<PERSON>", "rating": 422}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 76500}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 4344}, {"moveId": "SCALD", "uses": 13517}, {"moveId": "LAST_RESORT", "uses": 14536}, {"moveId": "HYDRO_PUMP", "uses": 6023}, {"moveId": "AQUA_TAIL", "uses": 38170}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "LAST_RESORT"], "score": 89.6}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 617, "matchups": [{"opponent": "zarude", "rating": 842, "opRating": 157}, {"opponent": "metagross", "rating": 803}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 669}, {"opponent": "sylveon", "rating": 669, "opRating": 330}, {"opponent": "zacian_hero", "rating": 538}], "counters": [{"opponent": "giratina_origin", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "lugia", "rating": 280}, {"opponent": "mewtwo", "rating": 468}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 29099}, {"moveId": "INCINERATE", "uses": 30270}, {"moveId": "EMBER", "uses": 17061}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 10117}, {"moveId": "RETURN", "uses": 10666}, {"moveId": "OVERHEAT", "uses": 8872}, {"moveId": "FIRE_BLAST", "uses": 5149}, {"moveId": "BLAST_BURN", "uses": 41688}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 89.4}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 596, "matchups": [{"opponent": "genesect_shock", "rating": 889, "opRating": 110}, {"opponent": "metagross", "rating": 764}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 711}, {"opponent": "sylveon", "rating": 669, "opRating": 330}, {"opponent": "dialga", "rating": 568}], "counters": [{"opponent": "mewtwo", "rating": 179}, {"opponent": "giratina_origin", "rating": 262}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "lugia", "rating": 340}, {"opponent": "excadrill", "rating": 355}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 29078}, {"moveId": "INCINERATE", "uses": 31378}, {"moveId": "EMBER", "uses": 15984}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 12026}, {"moveId": "OVERHEAT", "uses": 10319}, {"moveId": "FRUSTRATION", "uses": 1}, {"moveId": "FIRE_BLAST", "uses": 6014}, {"moveId": "BLAST_BURN", "uses": 48148}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 89.4}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "trevenant", "rating": 725, "opRating": 274}, {"opponent": "gengar", "rating": 704, "opRating": 295}, {"opponent": "metagross_shadow", "rating": 661, "opRating": 338}, {"opponent": "excadrill", "rating": 588}, {"opponent": "giratina_origin", "rating": 564}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "lugia", "rating": 228}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34161}, {"moveId": "METAL_CLAW", "uses": 11690}, {"moveId": "COUNTER", "uses": 30657}], "chargedMoves": [{"moveId": "RETURN", "uses": 16503}, {"moveId": "PLAY_ROUGH", "uses": 14695}, {"moveId": "HYPER_BEAM", "uses": 6382}, {"moveId": "CLOSE_COMBAT", "uses": 38933}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "RETURN"], "score": 89.3}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 573, "matchups": [{"opponent": "gyarados", "rating": 709}, {"opponent": "gyarado<PERSON>_shadow", "rating": 682, "opRating": 317}, {"opponent": "magnezone", "rating": 637, "opRating": 362}, {"opponent": "electivire_shadow", "rating": 601, "opRating": 398}, {"opponent": "mew", "rating": 562, "opRating": 437}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "giratina_origin", "rating": 266}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "metagross", "rating": 424}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 31590}, {"moveId": "MUD_SLAP", "uses": 20971}, {"moveId": "BUG_BITE", "uses": 23891}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26873}, {"moveId": "DISCHARGE", "uses": 24102}, {"moveId": "CRUNCH", "uses": 25610}]}, "moveset": ["SPARK", "X_SCISSOR", "CRUNCH"], "score": 89.3}, {"speciesId": "salamence", "speciesName": "Salamence", "rating": 629, "matchups": [{"opponent": "haxorus", "rating": 842, "opRating": 157}, {"opponent": "mew", "rating": 680, "opRating": 319}, {"opponent": "grou<PERSON>", "rating": 623}, {"opponent": "swampert", "rating": 533, "opRating": 466}, {"opponent": "kyogre", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 307}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "mewtwo", "rating": 419}, {"opponent": "garcho<PERSON>", "rating": 434}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 19982}, {"moveId": "DRAGON_TAIL", "uses": 43056}, {"moveId": "BITE", "uses": 13384}], "chargedMoves": [{"moveId": "RETURN", "uses": 13027}, {"moveId": "OUTRAGE", "uses": 29307}, {"moveId": "HYDRO_PUMP", "uses": 12566}, {"moveId": "FIRE_BLAST", "uses": 12802}, {"moveId": "DRACO_METEOR", "uses": 8800}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "RETURN"], "score": 89.2}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 494, "matchups": [{"opponent": "mewtwo_armored", "rating": 827, "opRating": 172}, {"opponent": "swampert", "rating": 717}, {"opponent": "mewtwo", "rating": 685}, {"opponent": "metagross", "rating": 577}, {"opponent": "excadrill", "rating": 532}], "counters": [{"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "lugia", "rating": 276}, {"opponent": "dialga", "rating": 282}, {"opponent": "gyarados", "rating": 309}, {"opponent": "giratina_origin", "rating": 352}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25707}, {"moveId": "RAZOR_LEAF", "uses": 10003}, {"moveId": "FEINT_ATTACK", "uses": 17608}, {"moveId": "BULLET_SEED", "uses": 23285}], "chargedMoves": [{"moveId": "LEAF_TORNADO", "uses": 6932}, {"moveId": "LEAF_BLADE", "uses": 36521}, {"moveId": "HURRICANE", "uses": 8834}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOUL_PLAY", "uses": 24110}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 89.2}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 493, "matchups": [{"opponent": "mewtwo_shadow", "rating": 685, "opRating": 314}, {"opponent": "swampert", "rating": 634}, {"opponent": "mewtwo", "rating": 629}, {"opponent": "excadrill", "rating": 623}, {"opponent": "grou<PERSON>", "rating": 524}], "counters": [{"opponent": "garcho<PERSON>", "rating": 230}, {"opponent": "dialga", "rating": 241}, {"opponent": "gyarados", "rating": 270}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "metagross", "rating": 494}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25124}, {"moveId": "RAZOR_LEAF", "uses": 10864}, {"moveId": "FEINT_ATTACK", "uses": 17833}, {"moveId": "BULLET_SEED", "uses": 22774}], "chargedMoves": [{"moveId": "RETURN", "uses": 6171}, {"moveId": "LEAF_TORNADO", "uses": 6487}, {"moveId": "LEAF_BLADE", "uses": 33771}, {"moveId": "HURRICANE", "uses": 7999}, {"moveId": "FOUL_PLAY", "uses": 22104}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 89.2}, {"speciesId": "muk_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 618, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747}, {"opponent": "sylveon", "rating": 706, "opRating": 293}, {"opponent": "florges", "rating": 699, "opRating": 300}, {"opponent": "zacian_hero", "rating": 639}, {"opponent": "gyarados", "rating": 555}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "metagross", "rating": 232}, {"opponent": "dialga", "rating": 296}, {"opponent": "lugia", "rating": 342}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 31970}, {"moveId": "LICK", "uses": 23192}, {"moveId": "INFESTATION", "uses": 21350}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 22902}, {"moveId": "SLUDGE_WAVE", "uses": 15303}, {"moveId": "GUNK_SHOT", "uses": 6015}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DARK_PULSE", "uses": 27102}, {"moveId": "ACID_SPRAY", "uses": 5133}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 89.1}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 613, "matchups": [{"opponent": "florges", "rating": 752, "opRating": 247}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 733}, {"opponent": "sylveon", "rating": 709, "opRating": 290}, {"opponent": "zacian_hero", "rating": 675}, {"opponent": "gyarados", "rating": 543}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 225}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "dragonite", "rating": 377}, {"opponent": "lugia", "rating": 397}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26696}, {"moveId": "LICK", "uses": 20155}, {"moveId": "INFESTATION", "uses": 19385}, {"moveId": "ACID", "uses": 10316}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 20187}, {"moveId": "SLUDGE_WAVE", "uses": 13034}, {"moveId": "RETURN", "uses": 10272}, {"moveId": "GUNK_SHOT", "uses": 5124}, {"moveId": "DARK_PULSE", "uses": 23325}, {"moveId": "ACID_SPRAY", "uses": 4432}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 89.1}, {"speciesId": "lanturn", "speciesName": "Lanturn", "rating": 486, "matchups": [{"opponent": "moltres_shadow", "rating": 786, "opRating": 213}, {"opponent": "gyarados", "rating": 658}, {"opponent": "ho_oh", "rating": 609, "opRating": 390}, {"opponent": "gyarado<PERSON>_shadow", "rating": 590, "opRating": 409}, {"opponent": "kyogre", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "mewtwo", "rating": 229}, {"opponent": "metagross", "rating": 331}, {"opponent": "lugia", "rating": 340}, {"opponent": "zacian_hero", "rating": 398}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 28627}, {"moveId": "SPARK", "uses": 29302}, {"moveId": "CHARGE_BEAM", "uses": 18534}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 21460}, {"moveId": "THUNDER", "uses": 9270}, {"moveId": "SURF", "uses": 39490}, {"moveId": "HYDRO_PUMP", "uses": 6175}]}, "moveset": ["SPARK", "SURF", "THUNDERBOLT"], "score": 89.1}, {"speciesId": "slurpuff", "speciesName": "Slurpuff", "rating": 422, "matchups": [{"opponent": "kommo_o", "rating": 841, "opRating": 158}, {"opponent": "hydreigon", "rating": 841, "opRating": 158}, {"opponent": "latios_shadow", "rating": 721, "opRating": 278}, {"opponent": "dragonite", "rating": 692}, {"opponent": "yveltal", "rating": 586, "opRating": 413}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "lugia", "rating": 288}, {"opponent": "metagross", "rating": 290}, {"opponent": "gyarados", "rating": 306}, {"opponent": "garcho<PERSON>", "rating": 384}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 18427}, {"moveId": "FAIRY_WIND", "uses": 32142}, {"moveId": "CHARM", "uses": 25897}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 22007}, {"moveId": "FLAMETHROWER", "uses": 22384}, {"moveId": "ENERGY_BALL", "uses": 18114}, {"moveId": "DRAINING_KISS", "uses": 13927}]}, "moveset": ["FAIRY_WIND", "FLAMETHROWER", "PLAY_ROUGH"], "score": 89}, {"speciesId": "walrein", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 643, "matchups": [{"opponent": "garcho<PERSON>", "rating": 904}, {"opponent": "dragonite", "rating": 753}, {"opponent": "excadrill", "rating": 618}, {"opponent": "giratina_origin", "rating": 558}, {"opponent": "grou<PERSON>", "rating": 532}], "counters": [{"opponent": "zacian_hero", "rating": 268}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "lugia", "rating": 411}, {"opponent": "gyarados", "rating": 425}, {"opponent": "dialga", "rating": 483}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 21576}, {"moveId": "POWDER_SNOW", "uses": 36631}, {"moveId": "FROST_BREATH", "uses": 18279}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 8084}, {"moveId": "RETURN", "uses": 8626}, {"moveId": "ICICLE_SPEAR", "uses": 35782}, {"moveId": "EARTHQUAKE", "uses": 16213}, {"moveId": "BLIZZARD", "uses": 7843}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 88.9}, {"speciesId": "walrein_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 636, "matchups": [{"opponent": "garcho<PERSON>", "rating": 881}, {"opponent": "dragonite", "rating": 704}, {"opponent": "dialga", "rating": 593}, {"opponent": "excadrill", "rating": 567}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 555, "opRating": 444}], "counters": [{"opponent": "gyarados", "rating": 327}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "giratina_origin", "rating": 368}, {"opponent": "lugia", "rating": 466}, {"opponent": "metagross", "rating": 479}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 21101}, {"moveId": "POWDER_SNOW", "uses": 38160}, {"moveId": "FROST_BREATH", "uses": 17233}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 9251}, {"moveId": "ICICLE_SPEAR", "uses": 40043}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 18400}, {"moveId": "BLIZZARD", "uses": 8831}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 88.9}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 489, "matchups": [{"opponent": "magnezone_shadow", "rating": 885, "opRating": 114}, {"opponent": "magnezone", "rating": 820, "opRating": 179}, {"opponent": "melmetal", "rating": 714, "opRating": 285}, {"opponent": "nihilego", "rating": 703, "opRating": 296}, {"opponent": "gyarados", "rating": 509}], "counters": [{"opponent": "mewtwo", "rating": 187}, {"opponent": "giratina_origin", "rating": 197}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "metagross", "rating": 395}, {"opponent": "dialga", "rating": 399}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 37878}, {"moveId": "MUD_SHOT", "uses": 38622}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 33843}, {"moveId": "MUDDY_WATER", "uses": 16590}, {"moveId": "DISCHARGE", "uses": 26059}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "DISCHARGE"], "score": 88.9}, {"speciesId": "terrakion", "speciesName": "Terrakion", "rating": 770, "matchups": [{"opponent": "ho_oh", "rating": 901, "opRating": 98}, {"opponent": "excadrill", "rating": 877}, {"opponent": "dialga", "rating": 718}, {"opponent": "metagross", "rating": 651}, {"opponent": "zekrom", "rating": 611, "opRating": 388}], "counters": [{"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "zacian_hero", "rating": 419}, {"opponent": "dragonite", "rating": 452}, {"opponent": "lugia", "rating": 469}, {"opponent": "gyarados", "rating": 474}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 2632}, {"moveId": "SMACK_DOWN", "uses": 26735}, {"moveId": "DOUBLE_KICK", "uses": 47104}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 24112}, {"moveId": "ROCK_SLIDE", "uses": 22723}, {"moveId": "EARTHQUAKE", "uses": 8213}, {"moveId": "CLOSE_COMBAT", "uses": 21423}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "ROCK_SLIDE"], "score": 88.8}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 443, "matchups": [{"opponent": "golisopod", "rating": 751, "opRating": 248}, {"opponent": "snorlax", "rating": 679, "opRating": 320}, {"opponent": "zap<PERSON>_shadow", "rating": 668, "opRating": 331}, {"opponent": "ho_oh", "rating": 644, "opRating": 355}, {"opponent": "gyarado<PERSON>_shadow", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "giratina_origin", "rating": 342}, {"opponent": "lugia", "rating": 404}, {"opponent": "metagross", "rating": 404}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 32332}, {"moveId": "QUICK_ATTACK", "uses": 26791}, {"moveId": "MUD_SLAP", "uses": 17365}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27644}, {"moveId": "PARABOLIC_CHARGE", "uses": 7256}, {"moveId": "GRASS_KNOT", "uses": 24981}, {"moveId": "BULLDOZE", "uses": 16516}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "GRASS_KNOT"], "score": 88.7}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 761, "matchups": [{"opponent": "gyarados", "rating": 750}, {"opponent": "metagross", "rating": 612}, {"opponent": "mewtwo", "rating": 548}, {"opponent": "zacian_hero", "rating": 540}, {"opponent": "garcho<PERSON>", "rating": 524}], "counters": [{"opponent": "dragonite", "rating": 276}, {"opponent": "dialga", "rating": 296}, {"opponent": "giratina_origin", "rating": 332}, {"opponent": "excadrill", "rating": 341}, {"opponent": "grou<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 49379}, {"moveId": "CHARGE_BEAM", "uses": 27121}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 6004}, {"moveId": "THUNDERBOLT", "uses": 14293}, {"moveId": "THUNDER", "uses": 6270}, {"moveId": "RETURN", "uses": 7340}, {"moveId": "DRILL_PECK", "uses": 29501}, {"moveId": "ANCIENT_POWER", "uses": 13147}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 88.6}, {"speciesId": "zap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 775, "matchups": [{"opponent": "gyarados", "rating": 750}, {"opponent": "swampert", "rating": 696}, {"opponent": "lugia", "rating": 642}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 612}, {"opponent": "metagross", "rating": 553}], "counters": [{"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "dialga", "rating": 366}, {"opponent": "excadrill", "rating": 430}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50277}, {"moveId": "CHARGE_BEAM", "uses": 26223}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 6441}, {"moveId": "THUNDERBOLT", "uses": 15596}, {"moveId": "THUNDER", "uses": 6916}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRILL_PECK", "uses": 33027}, {"moveId": "ANCIENT_POWER", "uses": 14476}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 88.6}, {"speciesId": "thundurus_therian", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Therian)", "rating": 617, "matchups": [{"opponent": "metagross", "rating": 805}, {"opponent": "ho_oh", "rating": 747}, {"opponent": "lugia", "rating": 732}, {"opponent": "gyarados", "rating": 714}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 520}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "excadrill", "rating": 290}, {"opponent": "swampert", "rating": 355}, {"opponent": "zacian_hero", "rating": 497}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 57111}, {"moveId": "BITE", "uses": 19389}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27777}, {"moveId": "THUNDER", "uses": 12035}, {"moveId": "SLUDGE_WAVE", "uses": 14847}, {"moveId": "FOCUS_BLAST", "uses": 21844}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "FOCUS_BLAST"], "score": 88.6}, {"speciesId": "golem", "speciesName": "Golem", "rating": 543, "matchups": [{"opponent": "ho_oh", "rating": 866}, {"opponent": "dialga", "rating": 590}, {"opponent": "metagross", "rating": 590}, {"opponent": "zekrom", "rating": 575}, {"opponent": "sylveon", "rating": 505, "opRating": 494}], "counters": [{"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "giratina_origin", "rating": 260}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 396}, {"opponent": "lugia", "rating": 464}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 22623}, {"moveId": "MUD_SLAP", "uses": 20461}, {"moveId": "MUD_SHOT", "uses": 33383}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 18938}, {"moveId": "ROCK_BLAST", "uses": 17115}, {"moveId": "RETURN", "uses": 7266}, {"moveId": "EARTHQUAKE", "uses": 20151}, {"moveId": "ANCIENT_POWER", "uses": 13057}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 88.5}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 488, "matchups": [{"opponent": "celebi", "rating": 827, "opRating": 172}, {"opponent": "mewtwo_shadow", "rating": 752, "opRating": 247}, {"opponent": "mewtwo_armored", "rating": 685, "opRating": 314}, {"opponent": "mewtwo", "rating": 636}, {"opponent": "giratina_origin", "rating": 597}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 265}, {"opponent": "gyarados", "rating": 304}, {"opponent": "excadrill", "rating": 374}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42111}, {"moveId": "FEINT_ATTACK", "uses": 34389}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 15068}, {"moveId": "LAST_RESORT", "uses": 13651}, {"moveId": "FOUL_PLAY", "uses": 33525}, {"moveId": "DARK_PULSE", "uses": 14253}]}, "moveset": ["SNARL", "FOUL_PLAY", "PSYCHIC"], "score": 88.5}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 588, "matchups": [{"opponent": "roserade", "rating": 814, "opRating": 185}, {"opponent": "sneasler", "rating": 704, "opRating": 295}, {"opponent": "buzzwole", "rating": 661, "opRating": 338}, {"opponent": "latios", "rating": 557, "opRating": 442}, {"opponent": "grou<PERSON>", "rating": 509}], "counters": [{"opponent": "giratina_origin", "rating": 304}, {"opponent": "dialga", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "dragonite", "rating": 393}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 51948}, {"moveId": "STEEL_WING", "uses": 24552}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 26455}, {"moveId": "CRUNCH", "uses": 25148}, {"moveId": "ANCIENT_POWER", "uses": 24874}]}, "moveset": ["WING_ATTACK", "DRAGON_CLAW", "CRUNCH"], "score": 88.4}, {"speciesId": "tyranitar", "speciesName": "Tyranitar", "rating": 645, "matchups": [{"opponent": "ho_oh", "rating": 907}, {"opponent": "mewtwo", "rating": 763}, {"opponent": "lugia", "rating": 691}, {"opponent": "giratina_origin", "rating": 686}, {"opponent": "gyarados", "rating": 604}], "counters": [{"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "metagross", "rating": 206}, {"opponent": "dialga", "rating": 211}, {"opponent": "zacian_hero", "rating": 234}, {"opponent": "dragonite", "rating": 406}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 41944}, {"moveId": "IRON_TAIL", "uses": 7290}, {"moveId": "BITE", "uses": 27254}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 25488}, {"moveId": "RETURN", "uses": 9051}, {"moveId": "FIRE_BLAST", "uses": 8420}, {"moveId": "CRUNCH", "uses": 33529}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 88.3}, {"speciesId": "tyranitar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 655, "matchups": [{"opponent": "ho_oh", "rating": 915}, {"opponent": "mewtwo", "rating": 738}, {"opponent": "giratina_origin", "rating": 659}, {"opponent": "lugia", "rating": 619}, {"opponent": "gyarados", "rating": 547}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "metagross", "rating": 235}, {"opponent": "dialga", "rating": 271}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43701}, {"moveId": "IRON_TAIL", "uses": 6930}, {"moveId": "BITE", "uses": 25824}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 29087}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_BLAST", "uses": 9627}, {"moveId": "CRUNCH", "uses": 37792}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 88.3}, {"speciesId": "florges", "speciesName": "Florges", "rating": 742, "matchups": [{"opponent": "dragonite", "rating": 803}, {"opponent": "gyarados", "rating": 779}, {"opponent": "garcho<PERSON>", "rating": 657}, {"opponent": "zacian_hero", "rating": 577}, {"opponent": "giratina_origin", "rating": 556}], "counters": [{"opponent": "metagross", "rating": 232}, {"opponent": "excadrill", "rating": 304}, {"opponent": "kyogre", "rating": 353}, {"opponent": "dialga", "rating": 377}, {"opponent": "mewtwo", "rating": 377}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 22210}, {"moveId": "TACKLE", "uses": 15703}, {"moveId": "RAZOR_LEAF", "uses": 10508}, {"moveId": "FAIRY_WIND", "uses": 28090}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 15469}, {"moveId": "PETAL_BLIZZARD", "uses": 11984}, {"moveId": "MOONBLAST", "uses": 14208}, {"moveId": "DISARMING_VOICE", "uses": 34869}]}, "moveset": ["FAIRY_WIND", "DISARMING_VOICE", "MOONBLAST"], "score": 88.2}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 491, "matchups": [{"opponent": "metagross", "rating": 755}, {"opponent": "genesect_shock", "rating": 721, "opRating": 278}, {"opponent": "genesect_douse", "rating": 721, "opRating": 278}, {"opponent": "genesect_chill", "rating": 721, "opRating": 278}, {"opponent": "genesect_burn", "rating": 721, "opRating": 278}], "counters": [{"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 211}, {"opponent": "lugia", "rating": 211}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "zacian_hero", "rating": 294}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38552}, {"moveId": "EMBER", "uses": 37948}], "chargedMoves": [{"moveId": "FRUSTRATION", "uses": 7}, {"moveId": "FLAMETHROWER", "uses": 18116}, {"moveId": "FIRE_PUNCH", "uses": 48592}, {"moveId": "FIRE_BLAST", "uses": 9887}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "FLAMETHROWER"], "score": 88.2}, {"speciesId": "accelgor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 270, "matchups": [{"opponent": "torterra", "rating": 648, "opRating": 351}, {"opponent": "serperior", "rating": 636, "opRating": 363}, {"opponent": "meganium", "rating": 627, "opRating": 372}, {"opponent": "meganium_shadow", "rating": 590, "opRating": 409}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 561, "opRating": 438}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 233}, {"opponent": "metagross", "rating": 369}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54995}, {"moveId": "ACID", "uses": 21505}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 22924}, {"moveId": "FOCUS_BLAST", "uses": 20353}, {"moveId": "BUG_BUZZ", "uses": 26682}, {"moveId": "ACID_SPRAY", "uses": 6623}]}, "moveset": ["INFESTATION", "BUG_BUZZ", "SIGNAL_BEAM"], "score": 88.2}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 475, "matchups": [{"opponent": "genesect_shock", "rating": 927, "opRating": 72}, {"opponent": "genesect_chill", "rating": 927, "opRating": 72}, {"opponent": "genesect_douse", "rating": 825, "opRating": 174}, {"opponent": "metagross", "rating": 530}, {"opponent": "dialga", "rating": 518}], "counters": [{"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 295}, {"opponent": "excadrill", "rating": 311}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 17461}, {"moveId": "FIRE_SPIN", "uses": 59039}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6250}, {"moveId": "FLAMETHROWER", "uses": 6852}, {"moveId": "CLOSE_COMBAT", "uses": 33667}, {"moveId": "BLAST_BURN", "uses": 29729}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 88}, {"speciesId": "crobat_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 528, "matchups": [{"opponent": "virizion", "rating": 839, "opRating": 160}, {"opponent": "zap<PERSON>_galarian", "rating": 786, "opRating": 213}, {"opponent": "buzzwole", "rating": 783, "opRating": 216}, {"opponent": "sylveon", "rating": 595, "opRating": 404}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 558, "opRating": 441}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "excadrill", "rating": 453}, {"opponent": "zacian_hero", "rating": 462}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 30716}, {"moveId": "AIR_SLASH", "uses": 45784}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 7310}, {"moveId": "SHADOW_BALL", "uses": 21744}, {"moveId": "POISON_FANG", "uses": 12080}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CROSS_POISON", "uses": 26946}, {"moveId": "AIR_CUTTER", "uses": 8267}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 88}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 531, "matchups": [{"opponent": "virizion", "rating": 856, "opRating": 143}, {"opponent": "buzzwole", "rating": 761, "opRating": 238}, {"opponent": "sylveon", "rating": 668, "opRating": 331}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 632}, {"opponent": "zacian_hero", "rating": 570}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 192}, {"opponent": "lugia", "rating": 207}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "gyarados", "rating": 314}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 31234}, {"moveId": "AIR_SLASH", "uses": 45266}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 6517}, {"moveId": "SHADOW_BALL", "uses": 19583}, {"moveId": "RETURN", "uses": 7401}, {"moveId": "POISON_FANG", "uses": 11068}, {"moveId": "CROSS_POISON", "uses": 24410}, {"moveId": "AIR_CUTTER", "uses": 7447}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 88}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 498, "matchups": [{"opponent": "swampert", "rating": 949}, {"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 889, "opRating": 110}, {"opponent": "rhyperior", "rating": 833, "opRating": 166}, {"opponent": "kyogre", "rating": 589, "opRating": 410}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "garcho<PERSON>", "rating": 340}, {"opponent": "zacian_hero", "rating": 447}, {"opponent": "excadrill", "rating": 486}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42742}, {"moveId": "ASTONISH", "uses": 33758}], "chargedMoves": [{"moveId": "SHADOW_SNEAK", "uses": 21080}, {"moveId": "ENERGY_BALL", "uses": 21335}, {"moveId": "BRAVE_BIRD", "uses": 34128}]}, "moveset": ["RAZOR_LEAF", "BRAVE_BIRD", "ENERGY_BALL"], "score": 88}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 449, "matchups": [{"opponent": "swampert", "rating": 711}, {"opponent": "rhyperior", "rating": 691, "opRating": 308}, {"opponent": "swampert_shadow", "rating": 675, "opRating": 325}, {"opponent": "charizard", "rating": 666, "opRating": 333}, {"opponent": "articuno_galarian", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "gyarados", "rating": 373}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 28986}, {"moveId": "BULLET_SEED", "uses": 36228}, {"moveId": "ACID", "uses": 11280}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 29603}, {"moveId": "RETURN", "uses": 9384}, {"moveId": "GRASS_KNOT", "uses": 25419}, {"moveId": "BULLDOZE", "uses": 12147}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 88}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 439, "matchups": [{"opponent": "thundurus_therian", "rating": 813, "opRating": 186}, {"opponent": "rhyperior", "rating": 708, "opRating": 291}, {"opponent": "ho_oh_shadow", "rating": 705, "opRating": 294}, {"opponent": "swampert", "rating": 675}, {"opponent": "thundurus_incarnate", "rating": 666, "opRating": 333}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "lugia", "rating": 228}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "excadrill", "rating": 369}, {"opponent": "gyarados", "rating": 417}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 29463}, {"moveId": "BULLET_SEED", "uses": 36497}, {"moveId": "ACID", "uses": 10583}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 33818}, {"moveId": "GRASS_KNOT", "uses": 28906}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BULLDOZE", "uses": 13790}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 88}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 454, "matchups": [{"opponent": "moltres", "rating": 818, "opRating": 181}, {"opponent": "heatran", "rating": 799, "opRating": 200}, {"opponent": "moltres_shadow", "rating": 783, "opRating": 216}, {"opponent": "gyarados", "rating": 582}, {"opponent": "ho_oh", "rating": 535}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "dialga", "rating": 206}, {"opponent": "lugia", "rating": 311}, {"opponent": "metagross", "rating": 328}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 460}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 57274}, {"moveId": "ASTONISH", "uses": 19226}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34718}, {"moveId": "THUNDER", "uses": 15107}, {"moveId": "HYDRO_PUMP", "uses": 26677}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 87.9}, {"speciesId": "aggron", "speciesName": "Aggron", "rating": 556, "matchups": [{"opponent": "genesect_chill", "rating": 646, "opRating": 353}, {"opponent": "lugia", "rating": 582}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570}, {"opponent": "giratina_altered", "rating": 547, "opRating": 452}, {"opponent": "palkia", "rating": 535, "opRating": 464}], "counters": [{"opponent": "dialga", "rating": 241}, {"opponent": "gyarados", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "giratina_origin", "rating": 360}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 32655}, {"moveId": "IRON_TAIL", "uses": 9971}, {"moveId": "DRAGON_TAIL", "uses": 33841}], "chargedMoves": [{"moveId": "THUNDER", "uses": 12763}, {"moveId": "STONE_EDGE", "uses": 27240}, {"moveId": "ROCK_TOMB", "uses": 7335}, {"moveId": "RETURN", "uses": 10407}, {"moveId": "HEAVY_SLAM", "uses": 18649}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 87.7}, {"speciesId": "aggron_shadow", "speciesName": "A<PERSON><PERSON> (Shadow)", "rating": 541, "matchups": [{"opponent": "nihilego", "rating": 745, "opRating": 254}, {"opponent": "lugia_shadow", "rating": 703, "opRating": 296}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 652, "opRating": 347}, {"opponent": "lugia", "rating": 589}, {"opponent": "giratina_altered", "rating": 585, "opRating": 414}], "counters": [{"opponent": "mewtwo", "rating": 289}, {"opponent": "dialga", "rating": 301}, {"opponent": "gyarados", "rating": 342}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "garcho<PERSON>", "rating": 361}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 33063}, {"moveId": "IRON_TAIL", "uses": 9439}, {"moveId": "DRAGON_TAIL", "uses": 34106}], "chargedMoves": [{"moveId": "THUNDER", "uses": 14627}, {"moveId": "STONE_EDGE", "uses": 31431}, {"moveId": "ROCK_TOMB", "uses": 8480}, {"moveId": "HEAVY_SLAM", "uses": 21916}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 87.7}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 481, "matchups": [{"opponent": "swampert_shadow", "rating": 909, "opRating": 90}, {"opponent": "swampert", "rating": 895}, {"opponent": "terrakion", "rating": 813, "opRating": 186}, {"opponent": "kyogre", "rating": 590, "opRating": 409}, {"opponent": "sylveon", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 289}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43482}, {"moveId": "ACID", "uses": 33018}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 3617}, {"moveId": "SLUDGE_BOMB", "uses": 17470}, {"moveId": "LEAF_TORNADO", "uses": 8224}, {"moveId": "LEAF_BLADE", "uses": 43201}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ACID_SPRAY", "uses": 3901}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 87.7}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 468, "matchups": [{"opponent": "swampert", "rating": 904}, {"opponent": "swampert_shadow", "rating": 895, "opRating": 104}, {"opponent": "tapu_fini", "rating": 700, "opRating": 299}, {"opponent": "primarina", "rating": 659, "opRating": 340}, {"opponent": "sylveon", "rating": 578, "opRating": 421}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "gyarados", "rating": 304}, {"opponent": "excadrill", "rating": 395}, {"opponent": "zacian_hero", "rating": 404}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 44096}, {"moveId": "ACID", "uses": 32404}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 3313}, {"moveId": "SLUDGE_BOMB", "uses": 15211}, {"moveId": "RETURN", "uses": 7449}, {"moveId": "LEAF_TORNADO", "uses": 7598}, {"moveId": "LEAF_BLADE", "uses": 39426}, {"moveId": "ACID_SPRAY", "uses": 3459}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 87.7}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 327, "matchups": [{"opponent": "gengar", "rating": 820, "opRating": 179}, {"opponent": "trevenant", "rating": 704, "opRating": 295}, {"opponent": "gourgeist_super", "rating": 631, "opRating": 368}, {"opponent": "giratina_origin", "rating": 554}, {"opponent": "victini", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "dialga", "rating": 247}, {"opponent": "metagross", "rating": 258}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 10871}, {"moveId": "SUCKER_PUNCH", "uses": 39129}, {"moveId": "BITE", "uses": 26508}], "chargedMoves": [{"moveId": "SURF", "uses": 30824}, {"moveId": "GRASS_KNOT", "uses": 20838}, {"moveId": "DARK_PULSE", "uses": 24878}]}, "moveset": ["SUCKER_PUNCH", "SURF", "DARK_PULSE"], "score": 87.7}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 549, "matchups": [{"opponent": "garcho<PERSON>", "rating": 900}, {"opponent": "swampert", "rating": 801}, {"opponent": "dragonite", "rating": 690}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 561, "opRating": 438}, {"opponent": "zekrom", "rating": 513, "opRating": 486}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "lugia", "rating": 330}, {"opponent": "gyarados", "rating": 363}, {"opponent": "dialga", "rating": 366}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 22240}, {"moveId": "POWDER_SNOW", "uses": 54260}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39297}, {"moveId": "RETURN", "uses": 6599}, {"moveId": "OUTRAGE", "uses": 10294}, {"moveId": "ENERGY_BALL", "uses": 14453}, {"moveId": "BLIZZARD", "uses": 5986}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 87.6}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 560, "matchups": [{"opponent": "garcho<PERSON>", "rating": 924}, {"opponent": "landorus_incarnate", "rating": 924, "opRating": 75}, {"opponent": "thundurus_incarnate", "rating": 924, "opRating": 75}, {"opponent": "swampert", "rating": 744}, {"opponent": "dragonite", "rating": 639}], "counters": [{"opponent": "mewtwo", "rating": 252}, {"opponent": "dialga", "rating": 263}, {"opponent": "giratina_origin", "rating": 350}, {"opponent": "gyarados", "rating": 378}, {"opponent": "lugia", "rating": 402}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 21372}, {"moveId": "POWDER_SNOW", "uses": 55128}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 42677}, {"moveId": "OUTRAGE", "uses": 11234}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ENERGY_BALL", "uses": 16051}, {"moveId": "BLIZZARD", "uses": 6489}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 87.6}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 459, "matchups": [{"opponent": "magnezone", "rating": 950, "opRating": 49}, {"opponent": "magnezone_shadow", "rating": 944, "opRating": 55}, {"opponent": "xurkitree", "rating": 933, "opRating": 66}, {"opponent": "raikou_shadow", "rating": 875, "opRating": 125}, {"opponent": "nihilego", "rating": 869, "opRating": 130}], "counters": [{"opponent": "mewtwo", "rating": 218}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "dialga", "rating": 418}, {"opponent": "excadrill", "rating": 425}, {"opponent": "metagross", "rating": 468}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 38144}, {"moveId": "MUD_SLAP", "uses": 38356}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 20718}, {"moveId": "ROCK_BLAST", "uses": 18705}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 22411}, {"moveId": "ANCIENT_POWER", "uses": 14608}]}, "moveset": ["MUD_SLAP", "EARTHQUAKE", "STONE_EDGE"], "score": 87.5}, {"speciesId": "electabuzz_shadow", "speciesName": "Electabuzz (Shadow)", "rating": 455, "matchups": [{"opponent": "moltres", "rating": 691, "opRating": 308}, {"opponent": "gyarados", "rating": 677}, {"opponent": "ho_oh", "rating": 644}, {"opponent": "gyarado<PERSON>_shadow", "rating": 614, "opRating": 385}, {"opponent": "zap<PERSON>_galarian", "rating": 553, "opRating": 446}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "mewtwo", "rating": 242}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "metagross", "rating": 340}, {"opponent": "lugia", "rating": 450}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58107}, {"moveId": "LOW_KICK", "uses": 18393}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 44949}, {"moveId": "THUNDERBOLT", "uses": 16800}, {"moveId": "THUNDER", "uses": 14524}, {"moveId": "FRUSTRATION", "uses": 109}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 87.5}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 722, "matchups": [{"opponent": "metagross", "rating": 848}, {"opponent": "dialga", "rating": 686}, {"opponent": "excadrill", "rating": 676}, {"opponent": "zacian_hero", "rating": 562}, {"opponent": "garcho<PERSON>", "rating": 517}], "counters": [{"opponent": "dragonite", "rating": 143}, {"opponent": "giratina_origin", "rating": 310}, {"opponent": "gyarados", "rating": 317}, {"opponent": "lugia", "rating": 357}, {"opponent": "mewtwo", "rating": 395}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 29756}, {"moveId": "MUD_SHOT", "uses": 46744}], "chargedMoves": [{"moveId": "SURF", "uses": 9729}, {"moveId": "SLUDGE_WAVE", "uses": 6689}, {"moveId": "RETURN", "uses": 5862}, {"moveId": "MUDDY_WATER", "uses": 11273}, {"moveId": "HYDRO_CANNON", "uses": 29390}, {"moveId": "EARTHQUAKE", "uses": 13459}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 87.4}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 732, "matchups": [{"opponent": "excadrill", "rating": 907}, {"opponent": "metagross", "rating": 818}, {"opponent": "ho_oh", "rating": 818, "opRating": 181}, {"opponent": "dialga", "rating": 639}, {"opponent": "zekrom", "rating": 624}], "counters": [{"opponent": "lugia", "rating": 288}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "giratina_origin", "rating": 344}, {"opponent": "mewtwo", "rating": 348}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 28014}, {"moveId": "MUD_SHOT", "uses": 48486}], "chargedMoves": [{"moveId": "SURF", "uses": 10539}, {"moveId": "SLUDGE_WAVE", "uses": 7445}, {"moveId": "MUDDY_WATER", "uses": 12202}, {"moveId": "HYDRO_CANNON", "uses": 31753}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 14538}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 87.4}, {"speciesId": "kyurem", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 749, "matchups": [{"opponent": "garcho<PERSON>", "rating": 887}, {"opponent": "giratina_origin", "rating": 788}, {"opponent": "swampert", "rating": 635}, {"opponent": "mewtwo", "rating": 594}, {"opponent": "zekrom", "rating": 538, "opRating": 461}], "counters": [{"opponent": "metagross", "rating": 343}, {"opponent": "dialga", "rating": 421}, {"opponent": "dragonite", "rating": 449}, {"opponent": "gyarados", "rating": 481}, {"opponent": "grou<PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 19916}, {"moveId": "DRAGON_BREATH", "uses": 56584}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 40959}, {"moveId": "DRACO_METEOR", "uses": 13660}, {"moveId": "BLIZZARD", "uses": 22094}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "BLIZZARD"], "score": 87.4}, {"speciesId": "absol_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 379, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 828, "opRating": 171}, {"opponent": "celebi", "rating": 795, "opRating": 204}, {"opponent": "mewtwo_armored", "rating": 785, "opRating": 214}, {"opponent": "victini", "rating": 738, "opRating": 261}, {"opponent": "metagross", "rating": 553}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "lugia", "rating": 383}, {"opponent": "giratina_origin", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42308}, {"moveId": "PSYCHO_CUT", "uses": 34192}], "chargedMoves": [{"moveId": "THUNDER", "uses": 15044}, {"moveId": "PAYBACK", "uses": 12921}, {"moveId": "MEGAHORN", "uses": 19975}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DARK_PULSE", "uses": 28417}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 87.4}, {"speciesId": "absol", "speciesName": "Absol", "rating": 384, "matchups": [{"opponent": "celebi", "rating": 815, "opRating": 184}, {"opponent": "victini", "rating": 755, "opRating": 244}, {"opponent": "espeon", "rating": 677, "opRating": 322}, {"opponent": "mewtwo", "rating": 661}, {"opponent": "metagross", "rating": 560}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 281}, {"opponent": "lugia", "rating": 323}, {"opponent": "gyarados", "rating": 337}, {"opponent": "giratina_origin", "rating": 404}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41599}, {"moveId": "PSYCHO_CUT", "uses": 34901}], "chargedMoves": [{"moveId": "THUNDER", "uses": 13135}, {"moveId": "RETURN", "uses": 9299}, {"moveId": "PAYBACK", "uses": 11474}, {"moveId": "MEGAHORN", "uses": 17502}, {"moveId": "DARK_PULSE", "uses": 25230}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 87.4}, {"speciesId": "mandibuzz", "speciesName": "Mandibuzz", "rating": 489, "matchups": [{"opponent": "espeon", "rating": 830, "opRating": 169}, {"opponent": "mewtwo_shadow", "rating": 755, "opRating": 244}, {"opponent": "mewtwo_armored", "rating": 746, "opRating": 253}, {"opponent": "mewtwo", "rating": 683}, {"opponent": "giratina_origin", "rating": 551}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "lugia", "rating": 273}, {"opponent": "gyarados", "rating": 317}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44535}, {"moveId": "AIR_SLASH", "uses": 31965}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18643}, {"moveId": "FOUL_PLAY", "uses": 28257}, {"moveId": "DARK_PULSE", "uses": 12094}, {"moveId": "AERIAL_ACE", "uses": 17595}]}, "moveset": ["SNARL", "FOUL_PLAY", "SHADOW_BALL"], "score": 87.3}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 491, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 693, "opRating": 306}, {"opponent": "gyarado<PERSON>_shadow", "rating": 674, "opRating": 325}, {"opponent": "magnezone", "rating": 646, "opRating": 353}, {"opponent": "gengar", "rating": 626, "opRating": 373}, {"opponent": "victini", "rating": 505, "opRating": 494}], "counters": [{"opponent": "giratina_origin", "rating": 241}, {"opponent": "dialga", "rating": 244}, {"opponent": "metagross", "rating": 345}, {"opponent": "lugia", "rating": 411}, {"opponent": "gyarados", "rating": 474}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 57670}, {"moveId": "ACID", "uses": 18830}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 20580}, {"moveId": "DRAGON_CLAW", "uses": 27195}, {"moveId": "CRUNCH", "uses": 25145}, {"moveId": "ACID_SPRAY", "uses": 3583}]}, "moveset": ["SPARK", "DRAGON_CLAW", "CRUNCH"], "score": 87.3}, {"speciesId": "weezing_shadow", "speciesName": "Weez<PERSON> (Shadow)", "rating": 340, "matchups": [{"opponent": "lura<PERSON>s", "rating": 637, "opRating": 362}, {"opponent": "gardevoir", "rating": 634, "opRating": 365}, {"opponent": "gardevoir_shadow", "rating": 573, "opRating": 426}, {"opponent": "pinsir_shadow", "rating": 567, "opRating": 432}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "lugia", "rating": 207}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "metagross", "rating": 404}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 34419}, {"moveId": "INFESTATION", "uses": 42081}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 14938}, {"moveId": "SLUDGE_BOMB", "uses": 19848}, {"moveId": "SHADOW_BALL", "uses": 21612}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DARK_PULSE", "uses": 20126}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 87.3}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 351, "matchups": [{"opponent": "gardevoir_shadow", "rating": 634, "opRating": 365}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 614, "opRating": 385}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 593, "opRating": 406}, {"opponent": "mr_rime", "rating": 573, "opRating": 426}, {"opponent": "al<PERSON><PERSON>_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "lugia", "rating": 183}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "metagross", "rating": 354}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 26484}, {"moveId": "INFESTATION", "uses": 33140}, {"moveId": "ACID", "uses": 16860}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 13274}, {"moveId": "SLUDGE_BOMB", "uses": 17426}, {"moveId": "SHADOW_BALL", "uses": 19305}, {"moveId": "RETURN", "uses": 8581}, {"moveId": "DARK_PULSE", "uses": 17904}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 87.3}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 535, "matchups": [{"opponent": "giratina_origin", "rating": 724}, {"opponent": "garcho<PERSON>", "rating": 631}, {"opponent": "giratina_altered", "rating": 512, "opRating": 487}, {"opponent": "sylveon", "rating": 505, "opRating": 494}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "dialga", "rating": 241}, {"opponent": "gyarados", "rating": 304}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "dragonite", "rating": 470}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 1264}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4503}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4083}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 5079}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3899}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3798}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5899}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5578}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4140}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5070}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4441}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4823}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 5213}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4910}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4496}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4990}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4056}], "chargedMoves": [{"moveId": "THUNDER", "uses": 27031}, {"moveId": "GIGA_IMPACT", "uses": 23493}, {"moveId": "FOCUS_BLAST", "uses": 26083}]}, "moveset": ["HIDDEN_POWER_ICE", "THUNDER", "FOCUS_BLAST"], "score": 87.2}, {"speciesId": "chimecho", "speciesName": "Chi<PERSON><PERSON>", "rating": 364, "matchups": [{"opponent": "gallade", "rating": 573, "opRating": 426}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 560, "opRating": 439}, {"opponent": "machamp", "rating": 557, "opRating": 442}, {"opponent": "heracross", "rating": 557, "opRating": 442}, {"opponent": "terrakion", "rating": 530, "opRating": 469}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "dialga", "rating": 190}, {"opponent": "excadrill", "rating": 248}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "swampert", "rating": 296}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 52821}, {"moveId": "ASTONISH", "uses": 23679}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 28768}, {"moveId": "PSYSHOCK", "uses": 30434}, {"moveId": "ENERGY_BALL", "uses": 17331}]}, "moveset": ["EXTRASENSORY", "PSYSHOCK", "SHADOW_BALL"], "score": 87.2}, {"speciesId": "dragonite", "speciesName": "Dragonite", "rating": 821, "matchups": [{"opponent": "garcho<PERSON>", "rating": 933}, {"opponent": "swampert", "rating": 856}, {"opponent": "giratina_origin", "rating": 805}, {"opponent": "mewtwo", "rating": 582}, {"opponent": "dialga", "rating": 513}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 191}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "lugia", "rating": 297}, {"opponent": "metagross", "rating": 450}, {"opponent": "gyarados", "rating": 489}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 11060}, {"moveId": "DRAGON_TAIL", "uses": 32382}, {"moveId": "DRAGON_BREATH", "uses": 33066}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 19236}, {"moveId": "RETURN", "uses": 6269}, {"moveId": "OUTRAGE", "uses": 6400}, {"moveId": "HYPER_BEAM", "uses": 2454}, {"moveId": "HURRICANE", "uses": 10864}, {"moveId": "DRAGON_PULSE", "uses": 4340}, {"moveId": "DRAGON_CLAW", "uses": 23273}, {"moveId": "DRACO_METEOR", "uses": 3768}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 87.1}, {"speciesId": "dragonite_shadow", "speciesName": "Dragonite (Shadow)", "rating": 805, "matchups": [{"opponent": "garcho<PERSON>", "rating": 933}, {"opponent": "grou<PERSON>", "rating": 837}, {"opponent": "giratina_origin", "rating": 773}, {"opponent": "gyarados", "rating": 614}, {"opponent": "mewtwo", "rating": 505}], "counters": [{"opponent": "zacian_hero", "rating": 225}, {"opponent": "dialga", "rating": 288}, {"opponent": "lugia", "rating": 385}, {"opponent": "zekrom", "rating": 459}, {"opponent": "metagross", "rating": 485}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 10405}, {"moveId": "DRAGON_TAIL", "uses": 33139}, {"moveId": "DRAGON_BREATH", "uses": 33032}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 20051}, {"moveId": "OUTRAGE", "uses": 6819}, {"moveId": "HYPER_BEAM", "uses": 5139}, {"moveId": "HURRICANE", "uses": 11512}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRAGON_PULSE", "uses": 4488}, {"moveId": "DRAGON_CLAW", "uses": 24293}, {"moveId": "DRACO_METEOR", "uses": 4065}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 87.1}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 741, "matchups": [{"opponent": "metagross", "rating": 784}, {"opponent": "excadrill", "rating": 692}, {"opponent": "giratina_origin", "rating": 558}, {"opponent": "dialga", "rating": 553}, {"opponent": "zekrom", "rating": 553}], "counters": [{"opponent": "lugia", "rating": 214}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "grou<PERSON>", "rating": 407}, {"opponent": "mewtwo", "rating": 473}, {"opponent": "swampert", "rating": 482}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39954}, {"moveId": "DRAGON_TAIL", "uses": 36546}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 9506}, {"moveId": "OUTRAGE", "uses": 24658}, {"moveId": "FIRE_BLAST", "uses": 9839}, {"moveId": "EARTH_POWER", "uses": 22569}, {"moveId": "EARTHQUAKE", "uses": 9671}]}, "moveset": ["MUD_SHOT", "OUTRAGE", "EARTH_POWER"], "score": 87.1}, {"speciesId": "politoed_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 537, "matchups": [{"opponent": "heatran", "rating": 825, "opRating": 174}, {"opponent": "moltres_shadow", "rating": 803, "opRating": 196}, {"opponent": "entei", "rating": 793, "opRating": 206}, {"opponent": "excadrill", "rating": 586}, {"opponent": "zekrom", "rating": 561}], "counters": [{"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "dialga", "rating": 239}, {"opponent": "swampert", "rating": 358}, {"opponent": "metagross", "rating": 372}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41588}, {"moveId": "BUBBLE", "uses": 34912}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 32480}, {"moveId": "SURF", "uses": 13212}, {"moveId": "HYDRO_PUMP", "uses": 4217}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 13079}, {"moveId": "BLIZZARD", "uses": 13363}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "BLIZZARD"], "score": 87.1}, {"speciesId": "reuniclus", "speciesName": "Reuniclus", "rating": 340, "matchups": [{"opponent": "exeggutor_alolan_shadow", "rating": 688, "opRating": 311}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 555, "opRating": 444}, {"opponent": "salamence_shadow", "rating": 537, "opRating": 462}, {"opponent": "virizion", "rating": 532, "opRating": 467}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 527, "opRating": 472}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 182}, {"opponent": "lugia", "rating": 235}, {"opponent": "dragonite", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 441}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 1780}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4382}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4060}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4972}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 5185}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3751}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5780}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5219}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3968}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5108}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4492}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4663}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4645}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4739}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4473}, {"moveId": "HIDDEN_POWER_DARK", "uses": 5030}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4060}], "chargedMoves": [{"moveId": "THUNDER", "uses": 19306}, {"moveId": "SHADOW_BALL", "uses": 32330}, {"moveId": "FUTURE_SIGHT", "uses": 24818}]}, "moveset": ["HIDDEN_POWER_ICE", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 87.1}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 556, "matchups": [{"opponent": "yveltal", "rating": 795, "opRating": 204}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567}, {"opponent": "kyogre", "rating": 526, "opRating": 473}, {"opponent": "gyarados", "rating": 506}, {"opponent": "lugia", "rating": 503}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 175}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "dialga", "rating": 230}, {"opponent": "metagross", "rating": 386}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43385}, {"moveId": "THUNDER_SHOCK", "uses": 33115}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 9570}, {"moveId": "THUNDERBOLT", "uses": 11478}, {"moveId": "THUNDER", "uses": 9977}, {"moveId": "LAST_RESORT", "uses": 19099}, {"moveId": "DISCHARGE", "uses": 26607}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LAST_RESORT"], "score": 86.9}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 482, "matchups": [{"opponent": "magnezone_shadow", "rating": 914, "opRating": 85}, {"opponent": "magnezone", "rating": 887, "opRating": 112}, {"opponent": "electivire_shadow", "rating": 682, "opRating": 317}, {"opponent": "melmetal", "rating": 631, "opRating": 368}, {"opponent": "nihilego", "rating": 624, "opRating": 375}], "counters": [{"opponent": "garcho<PERSON>", "rating": 253}, {"opponent": "mewtwo", "rating": 333}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "metagross", "rating": 409}, {"opponent": "dialga", "rating": 415}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8201}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5133}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3619}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4451}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3506}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3390}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5290}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 6207}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3537}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4527}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3995}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4239}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4199}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4029}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4038}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4428}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3530}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 8845}, {"moveId": "EARTH_POWER", "uses": 23723}, {"moveId": "EARTHQUAKE", "uses": 10305}, {"moveId": "BODY_SLAM", "uses": 33694}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 86.8}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 497, "matchups": [{"opponent": "gengar", "rating": 784, "opRating": 215}, {"opponent": "sneasler", "rating": 733, "opRating": 266}, {"opponent": "trevenant", "rating": 725, "opRating": 274}, {"opponent": "magmortar_shadow", "rating": 631, "opRating": 368}, {"opponent": "machamp", "rating": 569, "opRating": 430}], "counters": [{"opponent": "dialga", "rating": 274}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "mewtwo", "rating": 388}, {"opponent": "giratina_origin", "rating": 396}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 12846}, {"moveId": "YAWN", "uses": 1946}, {"moveId": "CONFUSION", "uses": 61770}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 27935}, {"moveId": "FUTURE_SIGHT", "uses": 12194}, {"moveId": "FOUL_PLAY", "uses": 36396}]}, "moveset": ["CONFUSION", "FOUL_PLAY", "PSYCHIC"], "score": 86.8}, {"speciesId": "machamp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 718, "matchups": [{"opponent": "snorlax", "rating": 811, "opRating": 188}, {"opponent": "ho_oh", "rating": 715, "opRating": 284}, {"opponent": "yveltal", "rating": 696, "opRating": 303}, {"opponent": "dialga", "rating": 620}, {"opponent": "excadrill", "rating": 548}], "counters": [{"opponent": "giratina_origin", "rating": 250}, {"opponent": "gyarados", "rating": 283}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 389}, {"opponent": "metagross", "rating": 468}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 27822}, {"moveId": "COUNTER", "uses": 29995}, {"moveId": "BULLET_PUNCH", "uses": 18665}], "chargedMoves": [{"moveId": "SUBMISSION", "uses": 2507}, {"moveId": "STONE_EDGE", "uses": 4921}, {"moveId": "ROCK_SLIDE", "uses": 12553}, {"moveId": "PAYBACK", "uses": 9131}, {"moveId": "HEAVY_SLAM", "uses": 5823}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DYNAMIC_PUNCH", "uses": 5705}, {"moveId": "CROSS_CHOP", "uses": 14605}, {"moveId": "CLOSE_COMBAT", "uses": 21205}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 86.7}, {"speciesId": "machamp", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 718, "matchups": [{"opponent": "yveltal", "rating": 889, "opRating": 110}, {"opponent": "snor<PERSON>_shadow", "rating": 811, "opRating": 188}, {"opponent": "dialga", "rating": 674}, {"opponent": "excadrill", "rating": 629}, {"opponent": "snorlax", "rating": 604, "opRating": 395}], "counters": [{"opponent": "giratina_origin", "rating": 227}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "metagross", "rating": 404}, {"opponent": "gyarados", "rating": 458}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 27384}, {"moveId": "COUNTER", "uses": 30057}, {"moveId": "BULLET_PUNCH", "uses": 19141}], "chargedMoves": [{"moveId": "SUBMISSION", "uses": 2455}, {"moveId": "STONE_EDGE", "uses": 4671}, {"moveId": "ROCK_SLIDE", "uses": 11842}, {"moveId": "RETURN", "uses": 4027}, {"moveId": "PAYBACK", "uses": 8601}, {"moveId": "HEAVY_SLAM", "uses": 5475}, {"moveId": "DYNAMIC_PUNCH", "uses": 5338}, {"moveId": "CROSS_CHOP", "uses": 13902}, {"moveId": "CLOSE_COMBAT", "uses": 20217}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 86.7}, {"speciesId": "tapu_lele", "speciesName": "<PERSON><PERSON>", "rating": 636, "matchups": [{"opponent": "dragonite", "rating": 732}, {"opponent": "zacian_hero", "rating": 719}, {"opponent": "gyarados", "rating": 649}, {"opponent": "garcho<PERSON>", "rating": 557}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550}], "counters": [{"opponent": "mewtwo", "rating": 174}, {"opponent": "giratina_origin", "rating": 205}, {"opponent": "dialga", "rating": 334}, {"opponent": "lugia", "rating": 359}, {"opponent": "excadrill", "rating": 432}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56963}, {"moveId": "ASTONISH", "uses": 19537}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 24970}, {"moveId": "MOONBLAST", "uses": 28464}, {"moveId": "FUTURE_SIGHT", "uses": 8380}, {"moveId": "FOCUS_BLAST", "uses": 14679}]}, "moveset": ["CONFUSION", "MOONBLAST", "PSYSHOCK"], "score": 86.6}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 463, "matchups": [{"opponent": "ursaring", "rating": 905, "opRating": 94}, {"opponent": "gengar", "rating": 710, "opRating": 289}, {"opponent": "mew", "rating": 667, "opRating": 332}, {"opponent": "steelix", "rating": 634, "opRating": 365}, {"opponent": "golisopod", "rating": 628, "opRating": 371}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "gyarados", "rating": 298}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 12700}, {"moveId": "TACKLE", "uses": 63800}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 18718}, {"moveId": "HORN_ATTACK", "uses": 32813}, {"moveId": "EARTHQUAKE", "uses": 25020}]}, "moveset": ["TACKLE", "HORN_ATTACK", "EARTHQUAKE"], "score": 86.6}, {"speciesId": "darmanitan_galarian_standard", "speciesName": "Dar<PERSON><PERSON> (Galarian)", "rating": 520, "matchups": [{"opponent": "land<PERSON><PERSON>_therian", "rating": 899, "opRating": 100}, {"opponent": "landorus_incarnate", "rating": 896, "opRating": 103}, {"opponent": "garcho<PERSON>", "rating": 853}, {"opponent": "dragonite", "rating": 711}, {"opponent": "dragonite_shadow", "rating": 694, "opRating": 305}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "gyarados", "rating": 280}, {"opponent": "dialga", "rating": 285}, {"opponent": "lugia", "rating": 345}, {"opponent": "giratina_origin", "rating": 364}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 33154}, {"moveId": "ICE_FANG", "uses": 43346}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 18555}, {"moveId": "OVERHEAT", "uses": 8406}, {"moveId": "ICE_PUNCH", "uses": 18576}, {"moveId": "AVALANCHE", "uses": 30934}]}, "moveset": ["ICE_FANG", "AVALANCHE", "ICE_PUNCH"], "score": 86.5}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 530, "matchups": [{"opponent": "genesect_shock", "rating": 721, "opRating": 278}, {"opponent": "genesect_douse", "rating": 721, "opRating": 278}, {"opponent": "sylveon", "rating": 573, "opRating": 426}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 526}, {"opponent": "metagross", "rating": 513}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "dialga", "rating": 247}, {"opponent": "excadrill", "rating": 344}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39265}, {"moveId": "EMBER", "uses": 37235}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24760}, {"moveId": "OVERHEAT", "uses": 15881}, {"moveId": "LAST_RESORT", "uses": 11586}, {"moveId": "HEAT_WAVE", "uses": 2736}, {"moveId": "FLAMETHROWER", "uses": 17059}, {"moveId": "FIRE_BLAST", "uses": 4520}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 86.5}, {"speciesId": "solrock", "speciesName": "Solrock", "rating": 385, "matchups": [{"opponent": "ho_oh", "rating": 803}, {"opponent": "sneasler", "rating": 760, "opRating": 239}, {"opponent": "typhlosion", "rating": 645, "opRating": 354}, {"opponent": "moltres", "rating": 537, "opRating": 462}, {"opponent": "zacian_hero", "rating": 532}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 241}, {"opponent": "lugia", "rating": 242}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36471}, {"moveId": "CONFUSION", "uses": 40029}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 11362}, {"moveId": "ROCK_SLIDE", "uses": 42226}, {"moveId": "PSYCHIC", "uses": 22925}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 86.5}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 563, "matchups": [{"opponent": "giratina_origin", "rating": 851}, {"opponent": "garcho<PERSON>", "rating": 814}, {"opponent": "zarude", "rating": 705, "opRating": 294}, {"opponent": "mew", "rating": 674, "opRating": 325}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "metagross", "rating": 319}, {"opponent": "dragonite", "rating": 343}, {"opponent": "gyarados", "rating": 345}, {"opponent": "lugia", "rating": 430}, {"opponent": "zacian_hero", "rating": 436}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 13001}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3614}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3289}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3958}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3104}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3001}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4971}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4293}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3350}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4032}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3572}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3864}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3791}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3666}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3610}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3946}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3272}, {"moveId": "CHARGE_BEAM", "uses": 4698}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 14700}, {"moveId": "TRI_ATTACK", "uses": 23073}, {"moveId": "SOLAR_BEAM", "uses": 9648}, {"moveId": "HYPER_BEAM", "uses": 11275}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BLIZZARD", "uses": 17612}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 86.4}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 592, "matchups": [{"opponent": "garcho<PERSON>", "rating": 851}, {"opponent": "giratina_origin", "rating": 702}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570}, {"opponent": "zekrom", "rating": 558, "opRating": 441}, {"opponent": "yveltal", "rating": 556, "opRating": 443}], "counters": [{"opponent": "dragonite", "rating": 308}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "gyarados", "rating": 324}, {"opponent": "lugia", "rating": 371}, {"opponent": "excadrill", "rating": 486}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 11857}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3704}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3350}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4041}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3166}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3068}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5037}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4317}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3392}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4128}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3658}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3930}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3815}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3743}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3698}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4022}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3325}, {"moveId": "CHARGE_BEAM", "uses": 4532}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 13208}, {"moveId": "TRI_ATTACK", "uses": 20562}, {"moveId": "SOLAR_BEAM", "uses": 8601}, {"moveId": "RETURN", "uses": 12989}, {"moveId": "HYPER_BEAM", "uses": 5017}, {"moveId": "BLIZZARD", "uses": 16049}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 86.4}, {"speciesId": "hypno_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 417, "matchups": [{"opponent": "sneasler", "rating": 744, "opRating": 255}, {"opponent": "landorus_incarnate", "rating": 609, "opRating": 390}, {"opponent": "machamp", "rating": 578, "opRating": 421}, {"opponent": "zap<PERSON>_galarian", "rating": 550, "opRating": 449}, {"opponent": "terrakion", "rating": 519, "opRating": 480}], "counters": [{"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dialga", "rating": 239}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "excadrill", "rating": 318}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9493}, {"moveId": "CONFUSION", "uses": 67007}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 11032}, {"moveId": "SHADOW_BALL", "uses": 11544}, {"moveId": "PSYSHOCK", "uses": 11935}, {"moveId": "PSYCHIC", "uses": 4605}, {"moveId": "ICE_PUNCH", "uses": 14861}, {"moveId": "FUTURE_SIGHT", "uses": 3951}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOCUS_BLAST", "uses": 7316}, {"moveId": "FIRE_PUNCH", "uses": 11227}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 86.4}, {"speciesId": "hypno", "speciesName": "Hypno", "rating": 407, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "machamp", "rating": 620, "opRating": 379}, {"opponent": "heracross", "rating": 606, "opRating": 393}, {"opponent": "terrakion", "rating": 592, "opRating": 407}, {"opponent": "machamp_shadow", "rating": 578, "opRating": 421}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "dialga", "rating": 220}, {"opponent": "excadrill", "rating": 262}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 354}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9601}, {"moveId": "CONFUSION", "uses": 66899}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 10368}, {"moveId": "SHADOW_BALL", "uses": 10881}, {"moveId": "RETURN", "uses": 4404}, {"moveId": "PSYSHOCK", "uses": 11242}, {"moveId": "PSYCHIC", "uses": 4386}, {"moveId": "ICE_PUNCH", "uses": 14023}, {"moveId": "FUTURE_SIGHT", "uses": 3793}, {"moveId": "FOCUS_BLAST", "uses": 6921}, {"moveId": "FIRE_PUNCH", "uses": 10627}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 86.4}, {"speciesId": "lair<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 375, "matchups": [{"opponent": "porygon_z_shadow", "rating": 890, "opRating": 109}, {"opponent": "articuno_shadow", "rating": 630, "opRating": 369}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609}, {"opponent": "kyurem", "rating": 573, "opRating": 426}, {"opponent": "sylveon", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "dialga", "rating": 233}, {"opponent": "lugia", "rating": 295}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54753}, {"moveId": "IRON_TAIL", "uses": 21747}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 6083}, {"moveId": "ROCK_SLIDE", "uses": 28340}, {"moveId": "HEAVY_SLAM", "uses": 14736}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BODY_SLAM", "uses": 27334}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 86.4}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 369, "matchups": [{"opponent": "charizard", "rating": 718, "opRating": 281}, {"opponent": "gardevoir_shadow", "rating": 616, "opRating": 383}, {"opponent": "sylveon", "rating": 602, "opRating": 397}, {"opponent": "articuno_galarian", "rating": 570, "opRating": 429}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 552}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 278}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54011}, {"moveId": "IRON_TAIL", "uses": 22489}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 5904}, {"moveId": "ROCK_SLIDE", "uses": 27022}, {"moveId": "RETURN", "uses": 3729}, {"moveId": "HEAVY_SLAM", "uses": 14067}, {"moveId": "BODY_SLAM", "uses": 25840}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 86.4}, {"speciesId": "gren<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 513, "matchups": [{"opponent": "landorus_incarnate", "rating": 846, "opRating": 153}, {"opponent": "ma<PERSON><PERSON>", "rating": 846, "opRating": 153}, {"opponent": "mewtwo", "rating": 662}, {"opponent": "excadrill", "rating": 556}, {"opponent": "metagross", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 214}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "garcho<PERSON>", "rating": 239}, {"opponent": "gyarados", "rating": 239}, {"opponent": "giratina_origin", "rating": 406}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 36400}, {"moveId": "BUBBLE", "uses": 40100}], "chargedMoves": [{"moveId": "SURF", "uses": 26384}, {"moveId": "NIGHT_SLASH", "uses": 36096}, {"moveId": "HYDRO_PUMP", "uses": 4262}, {"moveId": "AERIAL_ACE", "uses": 9826}]}, "moveset": ["BUBBLE", "NIGHT_SLASH", "SURF"], "score": 86.2}, {"speciesId": "cofagrigus", "speciesName": "<PERSON><PERSON>g<PERSON><PERSON>", "rating": 524, "matchups": [{"opponent": "metagross", "rating": 694}, {"opponent": "victini", "rating": 661, "opRating": 338}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 661, "opRating": 338}, {"opponent": "machamp_shadow", "rating": 625, "opRating": 374}, {"opponent": "melmetal", "rating": 521, "opRating": 478}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "dialga", "rating": 355}, {"opponent": "lugia", "rating": 392}, {"opponent": "zacian_hero", "rating": 465}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 5805}, {"moveId": "SHADOW_CLAW", "uses": 55999}, {"moveId": "ASTONISH", "uses": 14698}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 35646}, {"moveId": "PSYCHIC", "uses": 17494}, {"moveId": "DARK_PULSE", "uses": 23270}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "DARK_PULSE"], "score": 86.2}, {"speciesId": "cobalion", "speciesName": "Cobalion", "rating": 771, "matchups": [{"opponent": "magnezone_shadow", "rating": 901, "opRating": 98}, {"opponent": "dialga", "rating": 765}, {"opponent": "snorlax", "rating": 638, "opRating": 361}, {"opponent": "gyarados", "rating": 571}, {"opponent": "excadrill", "rating": 542}], "counters": [{"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "giratina_origin", "rating": 250}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "zacian_hero", "rating": 387}, {"opponent": "metagross", "rating": 427}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 4162}, {"moveId": "METAL_CLAW", "uses": 21781}, {"moveId": "DOUBLE_KICK", "uses": 50526}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 14754}, {"moveId": "SACRED_SWORD", "uses": 26270}, {"moveId": "IRON_HEAD", "uses": 11222}, {"moveId": "CLOSE_COMBAT", "uses": 24135}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "STONE_EDGE"], "score": 86.1}, {"speciesId": "tyrantrum", "speciesName": "Tyrantrum", "rating": 604, "matchups": [{"opponent": "entei", "rating": 695, "opRating": 304}, {"opponent": "articuno_galarian", "rating": 678, "opRating": 321}, {"opponent": "zapdos", "rating": 675, "opRating": 324}, {"opponent": "ho_oh", "rating": 666, "opRating": 333}, {"opponent": "mewtwo", "rating": 514}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "garcho<PERSON>", "rating": 403}, {"opponent": "dragonite", "rating": 406}, {"opponent": "metagross", "rating": 418}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 24033}, {"moveId": "DRAGON_TAIL", "uses": 35563}, {"moveId": "CHARM", "uses": 16879}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23605}, {"moveId": "OUTRAGE", "uses": 17972}, {"moveId": "EARTHQUAKE", "uses": 14370}, {"moveId": "CRUNCH", "uses": 20678}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "CRUNCH"], "score": 86.1}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 630, "matchups": [{"opponent": "garcho<PERSON>", "rating": 875}, {"opponent": "dragonite", "rating": 687}, {"opponent": "yveltal", "rating": 622, "opRating": 377}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 617, "opRating": 382}, {"opponent": "zekrom", "rating": 575, "opRating": 424}], "counters": [{"opponent": "dialga", "rating": 350}, {"opponent": "mewtwo", "rating": 401}, {"opponent": "giratina_origin", "rating": 422}, {"opponent": "lugia", "rating": 428}, {"opponent": "gyarados", "rating": 463}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 19395}, {"moveId": "ICE_FANG", "uses": 23851}, {"moveId": "FIRE_FANG", "uses": 19764}, {"moveId": "BITE", "uses": 13428}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 23531}, {"moveId": "STONE_EDGE", "uses": 6336}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTH_POWER", "uses": 18018}, {"moveId": "EARTHQUAKE", "uses": 7804}, {"moveId": "BODY_SLAM", "uses": 20725}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 86}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 640, "matchups": [{"opponent": "yveltal", "rating": 638, "opRating": 361}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 612, "opRating": 387}, {"opponent": "garcho<PERSON>", "rating": 605}, {"opponent": "dragonite", "rating": 509}, {"opponent": "giratina_origin", "rating": 502}], "counters": [{"opponent": "dialga", "rating": 293}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "lugia", "rating": 395}, {"opponent": "gyarados", "rating": 425}, {"opponent": "zacian_hero", "rating": 462}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 19455}, {"moveId": "ICE_FANG", "uses": 23396}, {"moveId": "FIRE_FANG", "uses": 19456}, {"moveId": "BITE", "uses": 14151}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 22786}, {"moveId": "STONE_EDGE", "uses": 6069}, {"moveId": "RETURN", "uses": 2890}, {"moveId": "EARTH_POWER", "uses": 17379}, {"moveId": "EARTHQUAKE", "uses": 7525}, {"moveId": "BODY_SLAM", "uses": 19811}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 86}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 438, "matchups": [{"opponent": "trevenant", "rating": 761, "opRating": 238}, {"opponent": "golisopod", "rating": 715, "opRating": 284}, {"opponent": "heracross", "rating": 648, "opRating": 351}, {"opponent": "virizion", "rating": 584, "opRating": 415}, {"opponent": "giratina_origin", "rating": 546}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 250}, {"opponent": "excadrill", "rating": 332}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 29199}, {"moveId": "AIR_SLASH", "uses": 47301}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 49779}, {"moveId": "HYPER_BEAM", "uses": 17444}, {"moveId": "HEAT_WAVE", "uses": 9297}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 85.9}, {"speciesId": "mantine", "speciesName": "<PERSON><PERSON>", "rating": 407, "matchups": [{"opponent": "heracross", "rating": 614, "opRating": 385}, {"opponent": "buzzwole", "rating": 610, "opRating": 389}, {"opponent": "landorus_incarnate", "rating": 600, "opRating": 399}, {"opponent": "sneasler", "rating": 590, "opRating": 409}, {"opponent": "grou<PERSON>", "rating": 577}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "mewtwo", "rating": 242}, {"opponent": "gyarados", "rating": 301}, {"opponent": "garcho<PERSON>", "rating": 467}, {"opponent": "swampert", "rating": 495}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 26884}, {"moveId": "BULLET_SEED", "uses": 23475}, {"moveId": "BUBBLE", "uses": 26158}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 12439}, {"moveId": "ICE_BEAM", "uses": 26866}, {"moveId": "BUBBLE_BEAM", "uses": 13002}, {"moveId": "AERIAL_ACE", "uses": 24171}]}, "moveset": ["WING_ATTACK", "ICE_BEAM", "AERIAL_ACE"], "score": 85.9}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 399, "matchups": [{"opponent": "metagross", "rating": 722}, {"opponent": "metagross_shadow", "rating": 719, "opRating": 280}, {"opponent": "escavalier", "rating": 667, "opRating": 332}, {"opponent": "genesect_chill", "rating": 570, "opRating": 429}, {"opponent": "genesect_burn", "rating": 570, "opRating": 429}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 154}, {"opponent": "lugia", "rating": 228}, {"opponent": "excadrill", "rating": 295}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 426}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48828}, {"moveId": "BITE", "uses": 27672}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 29805}, {"moveId": "FIRE_BLAST", "uses": 8063}, {"moveId": "CRUNCH", "uses": 38710}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 85.9}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 674, "matchups": [{"opponent": "metagross", "rating": 771}, {"opponent": "dialga", "rating": 643}, {"opponent": "mewtwo", "rating": 630}, {"opponent": "lugia", "rating": 582}, {"opponent": "zacian_hero", "rating": 550}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "dragonite", "rating": 146}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "gyarados", "rating": 340}, {"opponent": "excadrill", "rating": 344}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46676}, {"moveId": "BUG_BITE", "uses": 29824}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 24485}, {"moveId": "IRON_HEAD", "uses": 20256}, {"moveId": "FLAMETHROWER", "uses": 24925}, {"moveId": "FIRE_BLAST", "uses": 6751}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "IRON_HEAD"], "score": 85.7}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 524, "matchups": [{"opponent": "articuno_galarian", "rating": 923, "opRating": 76}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 832, "opRating": 167}, {"opponent": "metagross", "rating": 740}, {"opponent": "mewtwo", "rating": 704}, {"opponent": "mewtwo_shadow", "rating": 664, "opRating": 335}], "counters": [{"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "dialga", "rating": 301}, {"opponent": "lugia", "rating": 316}, {"opponent": "gyarados", "rating": 327}, {"opponent": "giratina_origin", "rating": 394}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46830}, {"moveId": "FIRE_FANG", "uses": 29670}], "chargedMoves": [{"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOUL_PLAY", "uses": 15014}, {"moveId": "FLAMETHROWER", "uses": 20976}, {"moveId": "FIRE_BLAST", "uses": 5843}, {"moveId": "CRUNCH", "uses": 34736}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 85.7}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 511, "matchups": [{"opponent": "metagross", "rating": 740}, {"opponent": "mewtwo_shadow", "rating": 704, "opRating": 295}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 667, "opRating": 332}, {"opponent": "mewtwo", "rating": 652}, {"opponent": "giratina_origin", "rating": 512}], "counters": [{"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "lugia", "rating": 264}, {"opponent": "gyarados", "rating": 273}, {"opponent": "dragonite", "rating": 273}, {"opponent": "dialga", "rating": 328}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45874}, {"moveId": "FIRE_FANG", "uses": 30626}], "chargedMoves": [{"moveId": "RETURN", "uses": 8945}, {"moveId": "FOUL_PLAY", "uses": 13334}, {"moveId": "FLAMETHROWER", "uses": 18524}, {"moveId": "FIRE_BLAST", "uses": 5026}, {"moveId": "CRUNCH", "uses": 30707}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 85.7}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 501, "matchups": [{"opponent": "mew", "rating": 643, "opRating": 356}, {"opponent": "giratina_origin", "rating": 573}, {"opponent": "melmetal", "rating": 573, "opRating": 426}, {"opponent": "nihilego", "rating": 559, "opRating": 440}, {"opponent": "zacian_hero", "rating": 519}], "counters": [{"opponent": "gyarados", "rating": 211}, {"opponent": "mewtwo", "rating": 213}, {"opponent": "lugia", "rating": 235}, {"opponent": "garcho<PERSON>", "rating": 347}, {"opponent": "swampert", "rating": 405}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 76500}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 12081}, {"moveId": "HYPER_BEAM", "uses": 4742}, {"moveId": "EARTHQUAKE", "uses": 17054}, {"moveId": "BODY_SLAM", "uses": 42565}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 85.7}, {"speciesId": "lycanroc_midday", "speciesName": "Lycanroc (Midday)", "rating": 403, "matchups": [{"opponent": "typhlosion", "rating": 814, "opRating": 185}, {"opponent": "heatran", "rating": 801, "opRating": 198}, {"opponent": "chandelure", "rating": 783, "opRating": 216}, {"opponent": "ho_oh", "rating": 777}, {"opponent": "ho_oh_shadow", "rating": 722, "opRating": 277}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "metagross", "rating": 212}, {"opponent": "giratina_origin", "rating": 310}, {"opponent": "dialga", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40204}, {"moveId": "ROCK_THROW", "uses": 36296}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26230}, {"moveId": "DRILL_RUN", "uses": 27470}, {"moveId": "CRUNCH", "uses": 22758}]}, "moveset": ["SUCKER_PUNCH", "DRILL_RUN", "STONE_EDGE"], "score": 85.7}, {"speciesId": "aromatisse", "speciesName": "Aromatisse", "rating": 511, "matchups": [{"opponent": "kommo_o", "rating": 908, "opRating": 91}, {"opponent": "palkia", "rating": 665, "opRating": 334}, {"opponent": "dragonite", "rating": 660}, {"opponent": "yveltal", "rating": 646, "opRating": 353}, {"opponent": "garcho<PERSON>", "rating": 537}], "counters": [{"opponent": "zacian_hero", "rating": 291}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "dialga", "rating": 331}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "gyarados", "rating": 435}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46608}, {"moveId": "CHARGE_BEAM", "uses": 29892}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18546}, {"moveId": "PSYCHIC", "uses": 15747}, {"moveId": "MOONBLAST", "uses": 30010}, {"moveId": "DRAINING_KISS", "uses": 12175}]}, "moveset": ["CHARM", "MOONBLAST", "THUNDERBOLT"], "score": 85.6}, {"speciesId": "crustle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 571, "matchups": [{"opponent": "weavile", "rating": 885, "opRating": 114}, {"opponent": "victini", "rating": 882, "opRating": 117}, {"opponent": "charizard", "rating": 796, "opRating": 203}, {"opponent": "moltres_shadow", "rating": 751, "opRating": 248}, {"opponent": "moltres", "rating": 710, "opRating": 289}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "gyarados", "rating": 309}, {"opponent": "metagross", "rating": 345}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "zacian_hero", "rating": 482}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 35769}, {"moveId": "FURY_CUTTER", "uses": 40731}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26869}, {"moveId": "ROCK_SLIDE", "uses": 28871}, {"moveId": "ROCK_BLAST", "uses": 20689}]}, "moveset": ["FURY_CUTTER", "ROCK_SLIDE", "X_SCISSOR"], "score": 85.6}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 474, "matchups": [{"opponent": "sandslash_alolan", "rating": 803, "opRating": 196}, {"opponent": "bisharp", "rating": 781, "opRating": 218}, {"opponent": "gyarado<PERSON>_shadow", "rating": 721, "opRating": 278}, {"opponent": "gengar", "rating": 621, "opRating": 378}, {"opponent": "bewear", "rating": 600, "opRating": 400}], "counters": [{"opponent": "mewtwo", "rating": 182}, {"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "dialga", "rating": 342}, {"opponent": "excadrill", "rating": 425}, {"opponent": "gyarados", "rating": 438}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 6608}, {"moveId": "TACKLE", "uses": 30057}, {"moveId": "DOUBLE_KICK", "uses": 39778}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 27242}, {"moveId": "PAYBACK", "uses": 14953}, {"moveId": "BODY_SLAM", "uses": 34427}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "WILD_CHARGE"], "score": 85.6}, {"speciesId": "lap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 595, "matchups": [{"opponent": "goodra", "rating": 646, "opRating": 353}, {"opponent": "excadrill", "rating": 591}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 589, "opRating": 410}, {"opponent": "garcho<PERSON>", "rating": 557}, {"opponent": "giratina_origin", "rating": 526}], "counters": [{"opponent": "mewtwo", "rating": 356}, {"opponent": "dialga", "rating": 396}, {"opponent": "gyarados", "rating": 425}, {"opponent": "lugia", "rating": 428}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 25097}, {"moveId": "ICE_SHARD", "uses": 31300}, {"moveId": "FROST_BREATH", "uses": 20173}], "chargedMoves": [{"moveId": "SURF", "uses": 27769}, {"moveId": "SKULL_BASH", "uses": 8560}, {"moveId": "ICE_BEAM", "uses": 20567}, {"moveId": "HYDRO_PUMP", "uses": 4377}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRAGON_PULSE", "uses": 7895}, {"moveId": "BLIZZARD", "uses": 7116}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 85.5}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 597, "matchups": [{"opponent": "gliscor_shadow", "rating": 895, "opRating": 104}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 671, "opRating": 328}, {"opponent": "landorus_incarnate", "rating": 667, "opRating": 332}, {"opponent": "garcho<PERSON>", "rating": 618}, {"opponent": "grou<PERSON>", "rating": 577, "opRating": 422}], "counters": [{"opponent": "dialga", "rating": 334}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "zacian_hero", "rating": 393}, {"opponent": "gyarados", "rating": 399}, {"opponent": "giratina_origin", "rating": 488}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 26007}, {"moveId": "ICE_SHARD", "uses": 30414}, {"moveId": "FROST_BREATH", "uses": 20057}], "chargedMoves": [{"moveId": "SURF", "uses": 25535}, {"moveId": "SKULL_BASH", "uses": 7696}, {"moveId": "RETURN", "uses": 6501}, {"moveId": "ICE_BEAM", "uses": 18998}, {"moveId": "HYDRO_PUMP", "uses": 4131}, {"moveId": "DRAGON_PULSE", "uses": 7154}, {"moveId": "BLIZZARD", "uses": 6645}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 85.5}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 423, "matchups": [{"opponent": "tapu_fini", "rating": 768, "opRating": 231}, {"opponent": "swampert", "rating": 716}, {"opponent": "swampert_shadow", "rating": 679, "opRating": 320}, {"opponent": "kyogre", "rating": 667, "opRating": 332}, {"opponent": "grou<PERSON>", "rating": 530}], "counters": [{"opponent": "giratina_origin", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "metagross", "rating": 319}, {"opponent": "gyarados", "rating": 358}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51830}, {"moveId": "BITE", "uses": 24670}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6736}, {"moveId": "GRASS_KNOT", "uses": 31726}, {"moveId": "CRUNCH", "uses": 37987}]}, "moveset": ["VINE_WHIP", "CRUNCH", "GRASS_KNOT"], "score": 85.3}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 441, "matchups": [{"opponent": "genesect_shock", "rating": 848, "opRating": 151}, {"opponent": "genesect_douse", "rating": 848, "opRating": 151}, {"opponent": "genesect_chill", "rating": 848, "opRating": 151}, {"opponent": "genesect_burn", "rating": 848, "opRating": 151}, {"opponent": "genesect", "rating": 848, "opRating": 151}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "metagross", "rating": 308}, {"opponent": "dialga", "rating": 312}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 337}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38913}, {"moveId": "EMBER", "uses": 37587}], "chargedMoves": [{"moveId": "RETURN", "uses": 16380}, {"moveId": "FLAMETHROWER", "uses": 14256}, {"moveId": "FIRE_PUNCH", "uses": 38245}, {"moveId": "FIRE_BLAST", "uses": 7699}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "RETURN"], "score": 85.3}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 388, "matchups": [{"opponent": "suicune_shadow", "rating": 849, "opRating": 150}, {"opponent": "tapu_fini", "rating": 812, "opRating": 187}, {"opponent": "rhyperior", "rating": 751, "opRating": 248}, {"opponent": "swampert", "rating": 717}, {"opponent": "swampert_shadow", "rating": 680, "opRating": 319}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "metagross", "rating": 302}, {"opponent": "gyarados", "rating": 309}, {"opponent": "grou<PERSON>", "rating": 486}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 50708}, {"moveId": "BITE", "uses": 25792}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 29639}, {"moveId": "ENERGY_BALL", "uses": 10665}, {"moveId": "CRUNCH", "uses": 36253}]}, "moveset": ["VINE_WHIP", "CRUNCH", "POWER_WHIP"], "score": 85.3}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 668, "matchups": [{"opponent": "dialga", "rating": 738}, {"opponent": "mewtwo", "rating": 643}, {"opponent": "swampert", "rating": 643}, {"opponent": "metagross", "rating": 595}, {"opponent": "excadrill", "rating": 576}], "counters": [{"opponent": "lugia", "rating": 252}, {"opponent": "gyarados", "rating": 332}, {"opponent": "zacian_hero", "rating": 436}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "zekrom", "rating": 486}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46072}, {"moveId": "BUG_BITE", "uses": 30428}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 27412}, {"moveId": "DRILL_RUN", "uses": 30576}, {"moveId": "AERIAL_ACE", "uses": 14674}, {"moveId": "ACID_SPRAY", "uses": 3960}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 85.2}, {"speciesId": "scrafty", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 573, "matchups": [{"opponent": "weavile_shadow", "rating": 959, "opRating": 40}, {"opponent": "pangoro", "rating": 942, "opRating": 57}, {"opponent": "darkrai", "rating": 936, "opRating": 63}, {"opponent": "tyranitar_shadow", "rating": 808, "opRating": 191}, {"opponent": "excadrill", "rating": 644}], "counters": [{"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "metagross", "rating": 392}, {"opponent": "giratina_origin", "rating": 428}, {"opponent": "dialga", "rating": 453}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 33084}, {"moveId": "COUNTER", "uses": 43416}], "chargedMoves": [{"moveId": "POWER_UP_PUNCH", "uses": 19139}, {"moveId": "FOUL_PLAY", "uses": 50686}, {"moveId": "ACID_SPRAY", "uses": 6719}]}, "moveset": ["COUNTER", "FOUL_PLAY", "POWER_UP_PUNCH"], "score": 85.2}, {"speciesId": "al<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 584, "matchups": [{"opponent": "registeel", "rating": 896, "opRating": 103}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 685, "opRating": 314}, {"opponent": "zarude", "rating": 685, "opRating": 314}, {"opponent": "cobalion", "rating": 670, "opRating": 329}, {"opponent": "metagross", "rating": 596}], "counters": [{"opponent": "mewtwo", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 363}, {"opponent": "dialga", "rating": 426}, {"opponent": "zacian_hero", "rating": 462}, {"opponent": "excadrill", "rating": 467}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 26440}, {"moveId": "COUNTER", "uses": 29473}, {"moveId": "CONFUSION", "uses": 20546}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18316}, {"moveId": "PSYCHIC", "uses": 15556}, {"moveId": "FUTURE_SIGHT", "uses": 6697}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOCUS_BLAST", "uses": 10929}, {"moveId": "FIRE_PUNCH", "uses": 16663}, {"moveId": "DAZZLING_GLEAM", "uses": 8265}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 85.1}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 586, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 737, "opRating": 262}, {"opponent": "cobalion", "rating": 722, "opRating": 277}, {"opponent": "metagross", "rating": 633}, {"opponent": "melmetal", "rating": 611, "opRating": 388}, {"opponent": "dialga", "rating": 514}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "grou<PERSON>", "rating": 347}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "excadrill", "rating": 476}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 26102}, {"moveId": "COUNTER", "uses": 29161}, {"moveId": "CONFUSION", "uses": 21239}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16627}, {"moveId": "RETURN", "uses": 7104}, {"moveId": "PSYCHIC", "uses": 13970}, {"moveId": "FUTURE_SIGHT", "uses": 6030}, {"moveId": "FOCUS_BLAST", "uses": 10001}, {"moveId": "FIRE_PUNCH", "uses": 15304}, {"moveId": "DAZZLING_GLEAM", "uses": 7449}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 85.1}, {"speciesId": "lycanroc_midnight", "speciesName": "Lycanroc (Midnight)", "rating": 517, "matchups": [{"opponent": "ho_oh", "rating": 794, "opRating": 205}, {"opponent": "ho_oh_shadow", "rating": 761, "opRating": 238}, {"opponent": "victini", "rating": 676, "opRating": 323}, {"opponent": "yveltal", "rating": 539, "opRating": 460}, {"opponent": "zapdos", "rating": 522, "opRating": 477}], "counters": [{"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "lugia", "rating": 302}, {"opponent": "excadrill", "rating": 327}, {"opponent": "dialga", "rating": 369}, {"opponent": "metagross", "rating": 395}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32465}, {"moveId": "COUNTER", "uses": 44035}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26954}, {"moveId": "PSYCHIC_FANGS", "uses": 23122}, {"moveId": "CRUNCH", "uses": 26469}]}, "moveset": ["COUNTER", "STONE_EDGE", "CRUNCH"], "score": 84.9}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 595, "matchups": [{"opponent": "nihilego", "rating": 716, "opRating": 283}, {"opponent": "zap<PERSON>_shadow", "rating": 667, "opRating": 332}, {"opponent": "zekrom", "rating": 631, "opRating": 368}, {"opponent": "giratina_altered", "rating": 564, "opRating": 435}, {"opponent": "dragonite", "rating": 509}], "counters": [{"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "mewtwo", "rating": 380}, {"opponent": "dialga", "rating": 391}, {"opponent": "giratina_origin", "rating": 404}, {"opponent": "gyarados", "rating": 404}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 23942}, {"moveId": "IRON_TAIL", "uses": 10788}, {"moveId": "DRAGON_TAIL", "uses": 41775}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 20105}, {"moveId": "HEAVY_SLAM", "uses": 14661}, {"moveId": "EARTHQUAKE", "uses": 19198}, {"moveId": "CRUNCH", "uses": 22622}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "PSYCHIC_FANGS"], "score": 84.8}, {"speciesId": "yveltal", "speciesName": "Y<PERSON><PERSON>", "rating": 739, "matchups": [{"opponent": "mewtwo", "rating": 842}, {"opponent": "metagross", "rating": 773}, {"opponent": "giratina_origin", "rating": 698}, {"opponent": "dialga", "rating": 643}, {"opponent": "garcho<PERSON>", "rating": 643}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 205}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "dragonite", "rating": 329}, {"opponent": "zekrom", "rating": 339}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 23033}, {"moveId": "SNARL", "uses": 27731}, {"moveId": "GUST", "uses": 25666}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 12872}, {"moveId": "HYPER_BEAM", "uses": 7256}, {"moveId": "HURRICANE", "uses": 15664}, {"moveId": "FOCUS_BLAST", "uses": 13290}, {"moveId": "DARK_PULSE", "uses": 27439}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 84.7}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 660, "matchups": [{"opponent": "metagross", "rating": 767}, {"opponent": "mewtwo", "rating": 726}, {"opponent": "giratina_origin", "rating": 617}, {"opponent": "excadrill", "rating": 598}, {"opponent": "dialga", "rating": 503}], "counters": [{"opponent": "zacian_hero", "rating": 274}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "dragonite", "rating": 364}, {"opponent": "lugia", "rating": 380}, {"opponent": "gyarados", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45977}, {"moveId": "FEINT_ATTACK", "uses": 30523}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 15039}, {"moveId": "SHADOW_BALL", "uses": 20461}, {"moveId": "FOCUS_BLAST", "uses": 14139}, {"moveId": "DARK_PULSE", "uses": 26754}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 84.7}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 695, "matchups": [{"opponent": "mewtwo", "rating": 685}, {"opponent": "mewtwo_shadow", "rating": 643, "opRating": 356}, {"opponent": "sylveon", "rating": 617, "opRating": 382}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}, {"opponent": "zacian_hero", "rating": 547}], "counters": [{"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "dialga", "rating": 328}, {"opponent": "lugia", "rating": 354}, {"opponent": "giratina_origin", "rating": 456}, {"opponent": "gyarados", "rating": 471}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 37359}, {"moveId": "POISON_JAB", "uses": 39141}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 13432}, {"moveId": "SHADOW_BALL", "uses": 13483}, {"moveId": "ICE_BEAM", "uses": 13690}, {"moveId": "DARK_PULSE", "uses": 17808}, {"moveId": "AQUA_TAIL", "uses": 17987}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 84.7}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 579, "matchups": [{"opponent": "nihilego", "rating": 872, "opRating": 127}, {"opponent": "metagross", "rating": 627}, {"opponent": "ho_oh", "rating": 620, "opRating": 379}, {"opponent": "dialga", "rating": 588}, {"opponent": "sylveon", "rating": 564, "opRating": 435}], "counters": [{"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "giratina_origin", "rating": 282}, {"opponent": "lugia", "rating": 321}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "mewtwo", "rating": 421}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 18135}, {"moveId": "MUD_SLAP", "uses": 58365}], "chargedMoves": [{"moveId": "SURF", "uses": 19570}, {"moveId": "STONE_EDGE", "uses": 23008}, {"moveId": "MEGAHORN", "uses": 15514}, {"moveId": "EARTHQUAKE", "uses": 18351}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "SURF"], "score": 84.6}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 548, "matchups": [{"opponent": "cobalion", "rating": 795, "opRating": 204}, {"opponent": "genesect_chill", "rating": 692, "opRating": 307}, {"opponent": "metagross", "rating": 612}, {"opponent": "sylveon", "rating": 585, "opRating": 414}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570}], "counters": [{"opponent": "excadrill", "rating": 290}, {"opponent": "mewtwo", "rating": 364}, {"opponent": "lugia", "rating": 400}, {"opponent": "dialga", "rating": 429}, {"opponent": "zacian_hero", "rating": 447}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 10424}, {"moveId": "SCRATCH", "uses": 17918}, {"moveId": "FIRE_SPIN", "uses": 48232}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 26638}, {"moveId": "FLAME_CHARGE", "uses": 30371}, {"moveId": "FLAMETHROWER", "uses": 12824}, {"moveId": "FIRE_BLAST", "uses": 6826}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "PSYCHIC"], "score": 84.6}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 544, "matchups": [{"opponent": "tapu_fini", "rating": 769, "opRating": 230}, {"opponent": "kyogre", "rating": 567, "opRating": 432}, {"opponent": "yveltal", "rating": 553, "opRating": 446}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 516}, {"opponent": "giratina_origin", "rating": 508}], "counters": [{"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "gyarados", "rating": 231}, {"opponent": "dialga", "rating": 269}, {"opponent": "zacian_hero", "rating": 300}, {"opponent": "lugia", "rating": 380}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9288}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3800}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3554}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4214}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3359}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3243}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4992}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4453}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3538}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4296}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3828}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4037}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3980}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3911}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3850}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4231}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3485}, {"moveId": "CHARGE_BEAM", "uses": 4418}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 18351}, {"moveId": "TRI_ATTACK", "uses": 30723}, {"moveId": "SOLAR_BEAM", "uses": 12290}, {"moveId": "HYPER_BEAM", "uses": 15001}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 84.6}, {"speciesId": "mew", "speciesName": "Mew", "rating": 746, "matchups": [{"opponent": "gyarados", "rating": 825}, {"opponent": "zacian_hero", "rating": 614}, {"opponent": "mewtwo", "rating": 529}, {"opponent": "metagross", "rating": 522}, {"opponent": "lugia", "rating": 514}], "counters": [{"opponent": "dragonite", "rating": 202}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "dialga", "rating": 361}, {"opponent": "garcho<PERSON>", "rating": 429}, {"opponent": "swampert", "rating": 455}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 4589}, {"moveId": "VOLT_SWITCH", "uses": 10842}, {"moveId": "STRUGGLE_BUG", "uses": 2605}, {"moveId": "STEEL_WING", "uses": 2913}, {"moveId": "SNARL", "uses": 9095}, {"moveId": "SHADOW_CLAW", "uses": 12092}, {"moveId": "ROCK_SMASH", "uses": 2007}, {"moveId": "POUND", "uses": 102}, {"moveId": "POISON_JAB", "uses": 7741}, {"moveId": "INFESTATION", "uses": 7062}, {"moveId": "FROST_BREATH", "uses": 4398}, {"moveId": "DRAGON_TAIL", "uses": 7171}, {"moveId": "CUT", "uses": 1050}, {"moveId": "CHARGE_BEAM", "uses": 4681}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8457}, {"moveId": "THUNDERBOLT", "uses": 1468}, {"moveId": "THUNDER", "uses": 1359}, {"moveId": "SURF", "uses": 5600}, {"moveId": "STONE_EDGE", "uses": 2144}, {"moveId": "SOLAR_BEAM", "uses": 815}, {"moveId": "ROCK_SLIDE", "uses": 5279}, {"moveId": "PSYSHOCK", "uses": 4896}, {"moveId": "PSYCHIC", "uses": 1888}, {"moveId": "OVERHEAT", "uses": 1717}, {"moveId": "LOW_SWEEP", "uses": 2582}, {"moveId": "ICE_BEAM", "uses": 4742}, {"moveId": "HYPER_BEAM", "uses": 1416}, {"moveId": "GYRO_BALL", "uses": 1529}, {"moveId": "GRASS_KNOT", "uses": 4184}, {"moveId": "FOCUS_BLAST", "uses": 2850}, {"moveId": "FLASH_CANNON", "uses": 1489}, {"moveId": "FLAME_CHARGE", "uses": 4342}, {"moveId": "ENERGY_BALL", "uses": 1481}, {"moveId": "DRAGON_CLAW", "uses": 5896}, {"moveId": "DAZZLING_GLEAM", "uses": 2021}, {"moveId": "DARK_PULSE", "uses": 4288}, {"moveId": "BULLDOZE", "uses": 2311}, {"moveId": "BLIZZARD", "uses": 1668}, {"moveId": "ANCIENT_POWER", "uses": 1803}]}, "moveset": ["SHADOW_CLAW", "SURF", "WILD_CHARGE"], "score": 84.5}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 399, "matchups": [{"opponent": "obstagoon", "rating": 710, "opRating": 289}, {"opponent": "golisopod", "rating": 685, "opRating": 314}, {"opponent": "empoleon", "rating": 682, "opRating": 317}, {"opponent": "trevenant", "rating": 662, "opRating": 337}, {"opponent": "excadrill", "rating": 550}], "counters": [{"opponent": "lugia", "rating": 295}, {"opponent": "metagross", "rating": 319}, {"opponent": "giratina_origin", "rating": 394}, {"opponent": "dialga", "rating": 421}, {"opponent": "grou<PERSON>", "rating": 437}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 21953}, {"moveId": "QUICK_ATTACK", "uses": 31035}, {"moveId": "GUST", "uses": 23462}], "chargedMoves": [{"moveId": "RETURN", "uses": 10487}, {"moveId": "HEAT_WAVE", "uses": 3916}, {"moveId": "CLOSE_COMBAT", "uses": 28786}, {"moveId": "BRAVE_BIRD", "uses": 33306}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 84.3}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 384, "matchups": [{"opponent": "rhydon", "rating": 839, "opRating": 160}, {"opponent": "suicune_shadow", "rating": 789, "opRating": 210}, {"opponent": "golisopod", "rating": 651, "opRating": 348}, {"opponent": "heatran", "rating": 598, "opRating": 401}, {"opponent": "tapu_fini", "rating": 592, "opRating": 407}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "lugia", "rating": 359}, {"opponent": "giratina_origin", "rating": 390}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 21764}, {"moveId": "QUICK_ATTACK", "uses": 31928}, {"moveId": "GUST", "uses": 22761}], "chargedMoves": [{"moveId": "HEAT_WAVE", "uses": 4415}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 32703}, {"moveId": "BRAVE_BIRD", "uses": 39299}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 84.3}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 480, "matchups": [{"opponent": "electivire_shadow", "rating": 737, "opRating": 262}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 698, "opRating": 301}, {"opponent": "sneasler", "rating": 695, "opRating": 304}, {"opponent": "machamp_shadow", "rating": 609, "opRating": 390}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 560}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "lugia", "rating": 264}, {"opponent": "zacian_hero", "rating": 473}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 33757}, {"moveId": "CONFUSION", "uses": 42743}], "chargedMoves": [{"moveId": "THUNDER", "uses": 28080}, {"moveId": "SWIFT", "uses": 12531}, {"moveId": "FUTURE_SIGHT", "uses": 35877}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "THUNDER"], "score": 84.3}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 413, "matchups": [{"opponent": "heatran", "rating": 811, "opRating": 188}, {"opponent": "nihilego", "rating": 610, "opRating": 389}, {"opponent": "magnezone_shadow", "rating": 610, "opRating": 389}, {"opponent": "melmetal", "rating": 603, "opRating": 396}, {"opponent": "excadrill", "rating": 587}], "counters": [{"opponent": "lugia", "rating": 166}, {"opponent": "swampert", "rating": 196}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "dialga", "rating": 434}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 33348}, {"moveId": "MUD_SHOT", "uses": 43152}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 21724}, {"moveId": "SLUDGE_BOMB", "uses": 14703}, {"moveId": "RETURN", "uses": 9967}, {"moveId": "EARTHQUAKE", "uses": 26774}, {"moveId": "ACID_SPRAY", "uses": 3321}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 84.3}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 399, "matchups": [{"opponent": "heatran", "rating": 788, "opRating": 211}, {"opponent": "moltres", "rating": 788, "opRating": 211}, {"opponent": "entei", "rating": 783, "opRating": 216}, {"opponent": "metagross", "rating": 744}, {"opponent": "melmetal", "rating": 626, "opRating": 373}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "dialga", "rating": 154}, {"opponent": "lugia", "rating": 192}, {"opponent": "excadrill", "rating": 267}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 31906}, {"moveId": "MUD_SHOT", "uses": 44594}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 24937}, {"moveId": "SLUDGE_BOMB", "uses": 17149}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 30420}, {"moveId": "ACID_SPRAY", "uses": 3862}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 84.3}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 657, "matchups": [{"opponent": "swampert", "rating": 833}, {"opponent": "excadrill", "rating": 756}, {"opponent": "gyarados", "rating": 572}, {"opponent": "zacian_hero", "rating": 567}, {"opponent": "garcho<PERSON>", "rating": 559}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "dragonite", "rating": 284}, {"opponent": "giratina_origin", "rating": 318}, {"opponent": "lugia", "rating": 340}, {"opponent": "mewtwo", "rating": 341}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44378}, {"moveId": "INFESTATION", "uses": 32122}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 4752}, {"moveId": "SLUDGE_BOMB", "uses": 11546}, {"moveId": "ROCK_SLIDE", "uses": 21934}, {"moveId": "RETURN", "uses": 8110}, {"moveId": "POWER_WHIP", "uses": 22318}, {"moveId": "ANCIENT_POWER", "uses": 7842}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 84.2}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 644, "matchups": [{"opponent": "swampert", "rating": 810}, {"opponent": "excadrill", "rating": 716}, {"opponent": "grou<PERSON>", "rating": 669}, {"opponent": "zacian_hero", "rating": 554}, {"opponent": "gyarados", "rating": 524}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "giratina_origin", "rating": 227}, {"opponent": "metagross", "rating": 267}, {"opponent": "garcho<PERSON>", "rating": 436}, {"opponent": "mewtwo", "rating": 468}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44532}, {"moveId": "INFESTATION", "uses": 31968}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 5344}, {"moveId": "SLUDGE_BOMB", "uses": 13074}, {"moveId": "ROCK_SLIDE", "uses": 24480}, {"moveId": "POWER_WHIP", "uses": 24867}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ANCIENT_POWER", "uses": 8881}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 84.2}, {"speciesId": "omastar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 558, "matchups": [{"opponent": "ho_oh_shadow", "rating": 894, "opRating": 105}, {"opponent": "ho_oh", "rating": 863, "opRating": 136}, {"opponent": "entei", "rating": 770, "opRating": 229}, {"opponent": "yveltal", "rating": 531, "opRating": 468}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509, "opRating": 490}], "counters": [{"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "dialga", "rating": 263}, {"opponent": "gyarados", "rating": 391}, {"opponent": "lugia", "rating": 409}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22758}, {"moveId": "ROCK_THROW", "uses": 23764}, {"moveId": "MUD_SHOT", "uses": 30007}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30176}, {"moveId": "ROCK_BLAST", "uses": 21566}, {"moveId": "HYDRO_PUMP", "uses": 14073}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "ANCIENT_POWER", "uses": 10871}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 84.2}, {"speciesId": "omastar", "speciesName": "Omastar", "rating": 545, "matchups": [{"opponent": "ho_oh", "rating": 920, "opRating": 79}, {"opponent": "moltres", "rating": 907, "opRating": 92}, {"opponent": "ho_oh_shadow", "rating": 863, "opRating": 136}, {"opponent": "entei", "rating": 799, "opRating": 200}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550}], "counters": [{"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "mewtwo", "rating": 283}, {"opponent": "gyarados", "rating": 304}, {"opponent": "lugia", "rating": 354}, {"opponent": "dialga", "rating": 361}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 23551}, {"moveId": "ROCK_THROW", "uses": 23577}, {"moveId": "MUD_SHOT", "uses": 29339}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26868}, {"moveId": "ROCK_BLAST", "uses": 19273}, {"moveId": "RETURN", "uses": 8427}, {"moveId": "HYDRO_PUMP", "uses": 12266}, {"moveId": "ANCIENT_POWER", "uses": 9728}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 84.2}, {"speciesId": "jynx", "speciesName": "Jynx", "rating": 407, "matchups": [{"opponent": "sneasler", "rating": 775, "opRating": 224}, {"opponent": "hippow<PERSON>_shadow", "rating": 714, "opRating": 285}, {"opponent": "blaziken", "rating": 661, "opRating": 338}, {"opponent": "gengar", "rating": 647, "opRating": 352}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "zacian_hero", "rating": 228}, {"opponent": "gyarados", "rating": 234}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 1600}, {"moveId": "FROST_BREATH", "uses": 32965}, {"moveId": "CONFUSION", "uses": 41874}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 13820}, {"moveId": "ICE_PUNCH", "uses": 18640}, {"moveId": "FOCUS_BLAST", "uses": 8973}, {"moveId": "DRAINING_KISS", "uses": 4112}, {"moveId": "AVALANCHE", "uses": 30934}]}, "moveset": ["CONFUSION", "AVALANCHE", "ICE_PUNCH"], "score": 84.2}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 433, "matchups": [{"opponent": "heracross", "rating": 640, "opRating": 359}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 637, "opRating": 362}, {"opponent": "metagross", "rating": 630}, {"opponent": "raikou_shadow", "rating": 616, "opRating": 383}, {"opponent": "genesect", "rating": 612, "opRating": 387}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "giratina_origin", "rating": 199}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "lugia", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 32648}, {"moveId": "HEX", "uses": 43852}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37783}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DAZZLING_GLEAM", "uses": 13883}, {"moveId": "DARK_PULSE", "uses": 24662}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 84.1}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 453, "matchups": [{"opponent": "pinsir_shadow", "rating": 690, "opRating": 309}, {"opponent": "metagross", "rating": 676}, {"opponent": "victini", "rating": 612, "opRating": 387}, {"opponent": "cobalion", "rating": 556, "opRating": 443}, {"opponent": "machamp_shadow", "rating": 556, "opRating": 443}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 338}, {"opponent": "dialga", "rating": 339}, {"opponent": "zacian_hero", "rating": 410}, {"opponent": "excadrill", "rating": 455}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 33228}, {"moveId": "HEX", "uses": 43272}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32472}, {"moveId": "RETURN", "uses": 11310}, {"moveId": "DAZZLING_GLEAM", "uses": 11576}, {"moveId": "DARK_PULSE", "uses": 21019}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 84.1}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 437, "matchups": [{"opponent": "chandelure", "rating": 725, "opRating": 275}, {"opponent": "metagross", "rating": 683}, {"opponent": "metagross_shadow", "rating": 670, "opRating": 329}, {"opponent": "victini", "rating": 629, "opRating": 370}, {"opponent": "genesect", "rating": 562, "opRating": 437}], "counters": [{"opponent": "mewtwo", "rating": 169}, {"opponent": "lugia", "rating": 311}, {"opponent": "dialga", "rating": 323}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "excadrill", "rating": 411}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55492}, {"moveId": "ASTONISH", "uses": 21008}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 25274}, {"moveId": "RETURN", "uses": 9112}, {"moveId": "PSYCHIC", "uses": 12569}, {"moveId": "OMINOUS_WIND", "uses": 12970}, {"moveId": "DARK_PULSE", "uses": 16500}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 84.1}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 422, "matchups": [{"opponent": "metagross", "rating": 670}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 670, "opRating": 329}, {"opponent": "celebi", "rating": 670, "opRating": 329}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 608, "opRating": 391}, {"opponent": "machamp", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 345}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "excadrill", "rating": 465}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55753}, {"moveId": "ASTONISH", "uses": 20747}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 28439}, {"moveId": "PSYCHIC", "uses": 14532}, {"moveId": "OMINOUS_WIND", "uses": 14563}, {"moveId": "FRUSTRATION", "uses": 1}, {"moveId": "DARK_PULSE", "uses": 18923}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 84.1}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 400, "matchups": [{"opponent": "gyarados", "rating": 687}, {"opponent": "gyarado<PERSON>_shadow", "rating": 677, "opRating": 322}, {"opponent": "ho_oh_shadow", "rating": 644, "opRating": 355}, {"opponent": "electivire_shadow", "rating": 553, "opRating": 446}, {"opponent": "sci<PERSON>_shadow", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 73}, {"opponent": "mewtwo", "rating": 195}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "metagross", "rating": 235}, {"opponent": "lugia", "rating": 316}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 60395}, {"moveId": "LOW_KICK", "uses": 16105}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 34822}, {"moveId": "THUNDERBOLT", "uses": 12959}, {"moveId": "THUNDER", "uses": 11154}, {"moveId": "RETURN", "uses": 17570}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 84.1}, {"speciesId": "reshiram", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 779, "matchups": [{"opponent": "metagross", "rating": 834}, {"opponent": "ho_oh", "rating": 589, "opRating": 410}, {"opponent": "zacian_hero", "rating": 584}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 578, "opRating": 421}, {"opponent": "giratina_origin", "rating": 567}], "counters": [{"opponent": "gyarados", "rating": 363}, {"opponent": "dialga", "rating": 369}, {"opponent": "mewtwo", "rating": 393}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 468}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 27960}, {"moveId": "DRAGON_BREATH", "uses": 48540}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 19293}, {"moveId": "OVERHEAT", "uses": 19653}, {"moveId": "DRACO_METEOR", "uses": 13362}, {"moveId": "CRUNCH", "uses": 24029}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "OVERHEAT"], "score": 84}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 729, "matchups": [{"opponent": "metagross", "rating": 769}, {"opponent": "excadrill", "rating": 755}, {"opponent": "dialga", "rating": 671}, {"opponent": "garcho<PERSON>", "rating": 565}, {"opponent": "zacian_hero", "rating": 505}], "counters": [{"opponent": "giratina_origin", "rating": 312}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "dragonite", "rating": 343}, {"opponent": "gyarados", "rating": 347}, {"opponent": "lugia", "rating": 369}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 24759}, {"moveId": "MUD_SHOT", "uses": 51741}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 25400}, {"moveId": "OUTRAGE", "uses": 15209}, {"moveId": "FOCUS_BLAST", "uses": 11275}, {"moveId": "EARTH_POWER", "uses": 24576}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTH_POWER"], "score": 84}, {"speciesId": "forretress_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 518, "matchups": [{"opponent": "celebi", "rating": 801, "opRating": 198}, {"opponent": "meloetta_aria", "rating": 637, "opRating": 362}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "nihilego", "rating": 530, "opRating": 469}, {"opponent": "latios_shadow", "rating": 515, "opRating": 484}], "counters": [{"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "dialga", "rating": 230}, {"opponent": "metagross", "rating": 343}, {"opponent": "zacian_hero", "rating": 369}, {"opponent": "mewtwo", "rating": 484}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 29465}, {"moveId": "BUG_BITE", "uses": 47035}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 8573}, {"moveId": "ROCK_TOMB", "uses": 13155}, {"moveId": "MIRROR_SHOT", "uses": 20498}, {"moveId": "HEAVY_SLAM", "uses": 16805}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 17499}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 84}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 526, "matchups": [{"opponent": "celebi", "rating": 783, "opRating": 216}, {"opponent": "zarude", "rating": 670, "opRating": 329}, {"opponent": "nihilego", "rating": 625, "opRating": 375}, {"opponent": "sylveon", "rating": 518, "opRating": 481}, {"opponent": "meloetta_aria", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "gyarados", "rating": 270}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "mewtwo", "rating": 390}, {"opponent": "dialga", "rating": 480}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 28690}, {"moveId": "BUG_BITE", "uses": 47810}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 7655}, {"moveId": "ROCK_TOMB", "uses": 11352}, {"moveId": "RETURN", "uses": 8910}, {"moveId": "MIRROR_SHOT", "uses": 18204}, {"moveId": "HEAVY_SLAM", "uses": 14781}, {"moveId": "EARTHQUAKE", "uses": 15504}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 84}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 472, "matchups": [{"opponent": "florges", "rating": 742, "opRating": 257}, {"opponent": "cobalion", "rating": 724, "opRating": 275}, {"opponent": "sylveon", "rating": 672, "opRating": 327}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 636}, {"opponent": "zacian_hero", "rating": 621}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 198}, {"opponent": "gyarados", "rating": 237}, {"opponent": "lugia", "rating": 278}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 30460}, {"moveId": "CONFUSION", "uses": 31707}, {"moveId": "ACID", "uses": 14360}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 19394}, {"moveId": "SHADOW_BALL", "uses": 32813}, {"moveId": "FUTURE_SIGHT", "uses": 24288}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 84}, {"speciesId": "torn<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 535, "matchups": [{"opponent": "buzzwole", "rating": 864, "opRating": 135}, {"opponent": "sneasler", "rating": 802, "opRating": 197}, {"opponent": "grou<PERSON>", "rating": 608}, {"opponent": "garcho<PERSON>", "rating": 547}, {"opponent": "swampert", "rating": 544}], "counters": [{"opponent": "giratina_origin", "rating": 193}, {"opponent": "dialga", "rating": 198}, {"opponent": "gyarados", "rating": 291}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "mewtwo", "rating": 356}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 58458}, {"moveId": "ASTONISH", "uses": 18042}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 21116}, {"moveId": "HURRICANE", "uses": 26521}, {"moveId": "HEAT_WAVE", "uses": 8544}, {"moveId": "FOCUS_BLAST", "uses": 20280}]}, "moveset": ["GUST", "HURRICANE", "PSYCHIC"], "score": 83.9}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 419, "matchups": [{"opponent": "gengar", "rating": 800, "opRating": 199}, {"opponent": "sneasler", "rating": 722, "opRating": 277}, {"opponent": "golisopod", "rating": 606, "opRating": 393}, {"opponent": "roserade", "rating": 596, "opRating": 403}, {"opponent": "giratina_origin", "rating": 530}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "dialga", "rating": 141}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "swampert", "rating": 303}, {"opponent": "gyarados", "rating": 317}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 38987}, {"moveId": "POUND", "uses": 37513}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 29324}, {"moveId": "HYPER_BEAM", "uses": 24670}, {"moveId": "DAZZLING_GLEAM", "uses": 22497}]}, "moveset": ["ZEN_HEADBUTT", "PSYCHIC", "HYPER_BEAM"], "score": 83.9}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 363, "matchups": [{"opponent": "virizion", "rating": 695, "opRating": 304}, {"opponent": "chesnaught", "rating": 631, "opRating": 368}, {"opponent": "pinsir", "rating": 615, "opRating": 384}, {"opponent": "pinsir_shadow", "rating": 588, "opRating": 411}, {"opponent": "sci<PERSON>_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "zacian_hero", "rating": 176}, {"opponent": "lugia", "rating": 200}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 10228}, {"moveId": "AIR_SLASH", "uses": 66272}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 31962}, {"moveId": "AIR_CUTTER", "uses": 9261}, {"moveId": "AERIAL_ACE", "uses": 35281}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 83.9}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 372, "matchups": [{"opponent": "chesnaught", "rating": 746, "opRating": 253}, {"opponent": "virizion", "rating": 716, "opRating": 283}, {"opponent": "pinsir", "rating": 707, "opRating": 292}, {"opponent": "tapu_bulu", "rating": 634, "opRating": 365}, {"opponent": "pinsir_shadow", "rating": 588, "opRating": 411}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 480}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 10102}, {"moveId": "AIR_SLASH", "uses": 66398}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 31982}, {"moveId": "AIR_CUTTER", "uses": 9257}, {"moveId": "AERIAL_ACE", "uses": 35280}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 83.9}, {"speciesId": "oricorio_sensu", "speciesName": "Oricorio (Sensu)", "rating": 361, "matchups": [{"opponent": "virizion", "rating": 804, "opRating": 195}, {"opponent": "pinsir", "rating": 774, "opRating": 225}, {"opponent": "escavalier", "rating": 728, "opRating": 271}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 716, "opRating": 283}, {"opponent": "buzzwole", "rating": 685, "opRating": 314}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "excadrill", "rating": 258}, {"opponent": "grou<PERSON>", "rating": 315}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 10198}, {"moveId": "AIR_SLASH", "uses": 66302}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 31960}, {"moveId": "AIR_CUTTER", "uses": 9257}, {"moveId": "AERIAL_ACE", "uses": 35281}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 83.9}, {"speciesId": "oricorio_pau", "speciesName": "Oricorio (Pa'u)", "rating": 341, "matchups": [{"opponent": "virizion", "rating": 750, "opRating": 250}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 625, "opRating": 375}, {"opponent": "heracross", "rating": 591, "opRating": 408}, {"opponent": "buzzwole", "rating": 588, "opRating": 411}, {"opponent": "gallade", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "excadrill", "rating": 258}, {"opponent": "grou<PERSON>", "rating": 315}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 10221}, {"moveId": "AIR_SLASH", "uses": 66279}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 31960}, {"moveId": "AIR_CUTTER", "uses": 9257}, {"moveId": "AERIAL_ACE", "uses": 35281}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 83.9}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 474, "matchups": [{"opponent": "magnezone_shadow", "rating": 895, "opRating": 104}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 653, "opRating": 346}, {"opponent": "nihilego", "rating": 646, "opRating": 353}, {"opponent": "melmetal", "rating": 634, "opRating": 365}, {"opponent": "excadrill", "rating": 581}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "metagross", "rating": 406}, {"opponent": "dialga", "rating": 410}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 34532}, {"moveId": "MUD_SHOT", "uses": 41968}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 12196}, {"moveId": "MUD_BOMB", "uses": 41467}, {"moveId": "BLIZZARD", "uses": 22842}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "BLIZZARD"], "score": 83.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 448, "matchups": [{"opponent": "sneasler", "rating": 767, "opRating": 232}, {"opponent": "ho_oh_shadow", "rating": 671, "opRating": 328}, {"opponent": "magmortar_shadow", "rating": 665, "opRating": 334}, {"opponent": "machamp_shadow", "rating": 659, "opRating": 340}, {"opponent": "machamp", "rating": 579, "opRating": 420}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "zacian_hero", "rating": 447}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43555}, {"moveId": "CHARM", "uses": 32945}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 37382}, {"moveId": "PSYCHIC", "uses": 27301}, {"moveId": "FUTURE_SIGHT", "uses": 11794}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 83.7}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 378, "matchups": [{"opponent": "chesnaught", "rating": 610, "opRating": 389}, {"opponent": "rhyperior", "rating": 590, "opRating": 409}, {"opponent": "swampert", "rating": 554}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 538, "opRating": 461}, {"opponent": "tapu_fini", "rating": 513, "opRating": 486}], "counters": [{"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 236}, {"opponent": "excadrill", "rating": 302}, {"opponent": "zacian_hero", "rating": 338}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52413}, {"moveId": "ASTONISH", "uses": 24087}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19279}, {"moveId": "RETURN", "uses": 9141}, {"moveId": "GRASS_KNOT", "uses": 23831}, {"moveId": "FOUL_PLAY", "uses": 24236}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 83.5}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 358, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 794, "opRating": 205}, {"opponent": "relicanth", "rating": 633, "opRating": 366}, {"opponent": "feraligatr", "rating": 628, "opRating": 371}, {"opponent": "gardevoir_shadow", "rating": 567, "opRating": 432}, {"opponent": "chesnaught", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "metagross", "rating": 264}, {"opponent": "excadrill", "rating": 362}, {"opponent": "zacian_hero", "rating": 378}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52228}, {"moveId": "ASTONISH", "uses": 24272}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22287}, {"moveId": "GRASS_KNOT", "uses": 26753}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOUL_PLAY", "uses": 27412}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 83.5}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 642, "matchups": [{"opponent": "swampert", "rating": 778}, {"opponent": "metagross", "rating": 727}, {"opponent": "kyogre", "rating": 716, "opRating": 283}, {"opponent": "excadrill", "rating": 685}, {"opponent": "zacian_hero", "rating": 539}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "lugia", "rating": 366}, {"opponent": "dialga", "rating": 410}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 26171}, {"moveId": "SHADOW_CLAW", "uses": 50329}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 29960}, {"moveId": "SEED_BOMB", "uses": 24021}, {"moveId": "FOUL_PLAY", "uses": 22535}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SEED_BOMB"], "score": 83.3}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 593, "matchups": [{"opponent": "rai<PERSON>u", "rating": 938, "opRating": 61}, {"opponent": "raikou_shadow", "rating": 924, "opRating": 75}, {"opponent": "nihilego", "rating": 822, "opRating": 177}, {"opponent": "metagross", "rating": 735}, {"opponent": "excadrill", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 234}, {"opponent": "giratina_origin", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 274}, {"opponent": "zacian_hero", "rating": 329}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40551}, {"moveId": "DRAGON_TAIL", "uses": 35949}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17387}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTH_POWER", "uses": 20161}, {"moveId": "EARTHQUAKE", "uses": 8760}, {"moveId": "DRAGON_CLAW", "uses": 30284}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 83.3}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 577, "matchups": [{"opponent": "raikou_shadow", "rating": 938, "opRating": 61}, {"opponent": "nihilego", "rating": 851, "opRating": 148}, {"opponent": "magnezone_shadow", "rating": 811, "opRating": 188}, {"opponent": "melmetal", "rating": 799, "opRating": 200}, {"opponent": "excadrill", "rating": 610}], "counters": [{"opponent": "mewtwo", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "dialga", "rating": 421}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39950}, {"moveId": "DRAGON_TAIL", "uses": 36550}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15667}, {"moveId": "RETURN", "uses": 6736}, {"moveId": "EARTH_POWER", "uses": 18518}, {"moveId": "EARTHQUAKE", "uses": 7866}, {"moveId": "DRAGON_CLAW", "uses": 27578}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 83.3}, {"speciesId": "skar<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 475, "matchups": [{"opponent": "celebi", "rating": 721, "opRating": 278}, {"opponent": "genesect", "rating": 657, "opRating": 342}, {"opponent": "zarude", "rating": 607, "opRating": 392}, {"opponent": "genesect_douse", "rating": 526, "opRating": 473}, {"opponent": "genesect_chill", "rating": 526, "opRating": 473}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "metagross", "rating": 305}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "garcho<PERSON>", "rating": 347}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 32473}, {"moveId": "AIR_SLASH", "uses": 44027}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 28084}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 10845}, {"moveId": "BRAVE_BIRD", "uses": 37531}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 83.3}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 485, "matchups": [{"opponent": "celebi", "rating": 758, "opRating": 241}, {"opponent": "tapu_bulu", "rating": 724, "opRating": 275}, {"opponent": "tangrowth_shadow", "rating": 714, "opRating": 285}, {"opponent": "zarude", "rating": 651, "opRating": 348}, {"opponent": "genesect", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 302}, {"opponent": "gyarados", "rating": 311}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "garcho<PERSON>", "rating": 373}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 32811}, {"moveId": "AIR_SLASH", "uses": 43689}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 25158}, {"moveId": "RETURN", "uses": 8189}, {"moveId": "FLASH_CANNON", "uses": 9651}, {"moveId": "BRAVE_BIRD", "uses": 33564}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 83.3}, {"speciesId": "lugia", "speciesName": "Lugia", "rating": 784, "matchups": [{"opponent": "garcho<PERSON>", "rating": 785}, {"opponent": "grou<PERSON>", "rating": 721}, {"opponent": "dragonite", "rating": 702}, {"opponent": "swampert", "rating": 642}, {"opponent": "zacian_hero", "rating": 516}], "counters": [{"opponent": "dialga", "rating": 377}, {"opponent": "metagross", "rating": 427}, {"opponent": "mewtwo", "rating": 468}, {"opponent": "gyarados", "rating": 484}, {"opponent": "giratina_origin", "rating": 486}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 34302}, {"moveId": "DRAGON_TAIL", "uses": 42198}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26334}, {"moveId": "RETURN", "uses": 8238}, {"moveId": "HYDRO_PUMP", "uses": 8800}, {"moveId": "FUTURE_SIGHT", "uses": 13649}, {"moveId": "AEROBLAST", "uses": 19554}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 83.2}, {"speciesId": "lugia_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 768, "matchups": [{"opponent": "garcho<PERSON>", "rating": 752}, {"opponent": "mewtwo", "rating": 633}, {"opponent": "zacian_hero", "rating": 630}, {"opponent": "dragonite", "rating": 614}, {"opponent": "gyarados", "rating": 600}], "counters": [{"opponent": "giratina_origin", "rating": 306}, {"opponent": "zekrom", "rating": 410}, {"opponent": "dialga", "rating": 440}, {"opponent": "yveltal", "rating": 472}, {"opponent": "excadrill", "rating": 476}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 33896}, {"moveId": "DRAGON_TAIL", "uses": 42604}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 29433}, {"moveId": "HYDRO_PUMP", "uses": 9791}, {"moveId": "FUTURE_SIGHT", "uses": 15158}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "AEROBLAST", "uses": 21932}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 83.2}, {"speciesId": "king<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "empoleon", "rating": 826, "opRating": 173}, {"opponent": "entei", "rating": 759, "opRating": 240}, {"opponent": "kyogre", "rating": 728, "opRating": 271}, {"opponent": "swampert", "rating": 527}, {"opponent": "ho_oh", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "dragonite", "rating": 321}, {"opponent": "giratina_origin", "rating": 324}, {"opponent": "mewtwo", "rating": 325}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22324}, {"moveId": "WATERFALL", "uses": 21293}, {"moveId": "DRAGON_BREATH", "uses": 32946}], "chargedMoves": [{"moveId": "RETURN", "uses": 10018}, {"moveId": "OUTRAGE", "uses": 23148}, {"moveId": "OCTAZOOKA", "uses": 15356}, {"moveId": "HYDRO_PUMP", "uses": 12946}, {"moveId": "BLIZZARD", "uses": 14865}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 83.2}, {"speciesId": "king<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 508, "matchups": [{"opponent": "typhlosion", "rating": 847, "opRating": 152}, {"opponent": "entei", "rating": 759, "opRating": 240}, {"opponent": "entei_shadow", "rating": 707, "opRating": 292}, {"opponent": "kyogre", "rating": 676, "opRating": 323}, {"opponent": "heatran", "rating": 637, "opRating": 362}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "dragonite", "rating": 337}, {"opponent": "giratina_origin", "rating": 378}, {"opponent": "garcho<PERSON>", "rating": 446}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 21145}, {"moveId": "WATERFALL", "uses": 21728}, {"moveId": "DRAGON_BREATH", "uses": 33730}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 26760}, {"moveId": "OCTAZOOKA", "uses": 17672}, {"moveId": "HYDRO_PUMP", "uses": 14797}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BLIZZARD", "uses": 17066}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 83.2}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 547, "matchups": [{"opponent": "rhyperior", "rating": 798, "opRating": 201}, {"opponent": "zap<PERSON>_galarian", "rating": 750, "opRating": 250}, {"opponent": "swampert", "rating": 747}, {"opponent": "swampert_shadow", "rating": 713, "opRating": 286}, {"opponent": "regirock", "rating": 631, "opRating": 368}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "mewtwo", "rating": 296}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "gyarados", "rating": 409}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 5284}, {"moveId": "EXTRASENSORY", "uses": 19428}, {"moveId": "CONFUSION", "uses": 24954}, {"moveId": "BULLET_SEED", "uses": 26986}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 14536}, {"moveId": "SEED_BOMB", "uses": 31780}, {"moveId": "PSYCHIC", "uses": 29997}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 83.1}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 532, "matchups": [{"opponent": "rhyperior", "rating": 835, "opRating": 164}, {"opponent": "swampert", "rating": 801}, {"opponent": "swampert_shadow", "rating": 747, "opRating": 252}, {"opponent": "machamp", "rating": 713, "opRating": 286}, {"opponent": "kyogre", "rating": 569, "opRating": 430}], "counters": [{"opponent": "dialga", "rating": 195}, {"opponent": "mewtwo", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 316}, {"opponent": "zacian_hero", "rating": 320}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 5515}, {"moveId": "EXTRASENSORY", "uses": 19707}, {"moveId": "CONFUSION", "uses": 25131}, {"moveId": "BULLET_SEED", "uses": 26174}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 12222}, {"moveId": "SEED_BOMB", "uses": 26829}, {"moveId": "RETURN", "uses": 12889}, {"moveId": "PSYCHIC", "uses": 24512}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 83.1}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 594, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 775, "opRating": 224}, {"opponent": "mewtwo", "rating": 744}, {"opponent": "mewtwo_shadow", "rating": 726, "opRating": 273}, {"opponent": "giratina_origin", "rating": 652}, {"opponent": "metagross", "rating": 623}], "counters": [{"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "dragonite", "rating": 289}, {"opponent": "gyarados", "rating": 304}, {"opponent": "lugia", "rating": 340}, {"opponent": "dialga", "rating": 423}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28431}, {"moveId": "FIRE_FANG", "uses": 20648}, {"moveId": "DOUBLE_KICK", "uses": 27411}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 31962}, {"moveId": "FIRE_BLAST", "uses": 7096}, {"moveId": "DARK_PULSE", "uses": 37377}]}, "moveset": ["SNARL", "DARK_PULSE", "FIRE_BLAST"], "score": 83}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 656, "matchups": [{"opponent": "snorlax", "rating": 696, "opRating": 303}, {"opponent": "excadrill", "rating": 629}, {"opponent": "swampert", "rating": 583}, {"opponent": "dialga", "rating": 580}, {"opponent": "zacian_hero", "rating": 540}], "counters": [{"opponent": "gyarados", "rating": 252}, {"opponent": "metagross", "rating": 273}, {"opponent": "lugia", "rating": 297}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "garcho<PERSON>", "rating": 485}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 15137}, {"moveId": "MUD_SLAP", "uses": 18363}, {"moveId": "COUNTER", "uses": 27077}, {"moveId": "CHARM", "uses": 15900}], "chargedMoves": [{"moveId": "PLAY_ROUGH", "uses": 11854}, {"moveId": "HEAVY_SLAM", "uses": 11564}, {"moveId": "EARTHQUAKE", "uses": 22413}, {"moveId": "BODY_SLAM", "uses": 30803}]}, "moveset": ["COUNTER", "BODY_SLAM", "EARTHQUAKE"], "score": 82.9}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 485, "matchups": [{"opponent": "tapu_bulu", "rating": 835, "opRating": 164}, {"opponent": "moltres_galarian", "rating": 822, "opRating": 177}, {"opponent": "zacian_hero", "rating": 691}, {"opponent": "ho_oh", "rating": 644, "opRating": 355}, {"opponent": "yveltal", "rating": 617, "opRating": 382}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "dialga", "rating": 258}, {"opponent": "gyarados", "rating": 288}, {"opponent": "excadrill", "rating": 293}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 58840}, {"moveId": "LOW_KICK", "uses": 17660}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26234}, {"moveId": "GRASS_KNOT", "uses": 21421}, {"moveId": "BRICK_BREAK", "uses": 28762}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 82.9}, {"speciesId": "gur<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 413, "matchups": [{"opponent": "weavile_shadow", "rating": 879, "opRating": 120}, {"opponent": "tapu_koko", "rating": 710, "opRating": 289}, {"opponent": "articuno_shadow", "rating": 693, "opRating": 306}, {"opponent": "moltres", "rating": 668, "opRating": 331}, {"opponent": "ho_oh_shadow", "rating": 660, "opRating": 339}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "gyarados", "rating": 157}, {"opponent": "dialga", "rating": 192}, {"opponent": "zacian_hero", "rating": 274}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56902}, {"moveId": "LOW_KICK", "uses": 19598}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 32501}, {"moveId": "LOW_SWEEP", "uses": 10101}, {"moveId": "BRICK_BREAK", "uses": 34043}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 82.9}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 563, "matchups": [{"opponent": "gengar", "rating": 739, "opRating": 260}, {"opponent": "celebi", "rating": 688, "opRating": 311}, {"opponent": "cresselia", "rating": 621, "opRating": 378}, {"opponent": "mew", "rating": 579, "opRating": 420}, {"opponent": "electivire_shadow", "rating": 548, "opRating": 451}], "counters": [{"opponent": "dialga", "rating": 255}, {"opponent": "metagross", "rating": 305}, {"opponent": "garcho<PERSON>", "rating": 356}, {"opponent": "mewtwo", "rating": 437}, {"opponent": "giratina_origin", "rating": 444}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 10626}, {"moveId": "MUD_SHOT", "uses": 65874}], "chargedMoves": [{"moveId": "STOMP", "uses": 26193}, {"moveId": "SKULL_BASH", "uses": 9112}, {"moveId": "MEGAHORN", "uses": 21398}, {"moveId": "EARTHQUAKE", "uses": 19750}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 82.8}, {"speciesId": "kabutops", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "ho_oh", "rating": 890, "opRating": 109}, {"opponent": "moltres", "rating": 883, "opRating": 116}, {"opponent": "ho_oh_shadow", "rating": 873, "opRating": 126}, {"opponent": "moltres_shadow", "rating": 873, "opRating": 126}, {"opponent": "entei_shadow", "rating": 746, "opRating": 253}], "counters": [{"opponent": "garcho<PERSON>", "rating": 166}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "gyarados", "rating": 260}, {"opponent": "dialga", "rating": 293}, {"opponent": "metagross", "rating": 305}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 20391}, {"moveId": "ROCK_SMASH", "uses": 7613}, {"moveId": "MUD_SHOT", "uses": 25615}, {"moveId": "FURY_CUTTER", "uses": 22836}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 13790}, {"moveId": "STONE_EDGE", "uses": 32979}, {"moveId": "ANCIENT_POWER", "uses": 29722}]}, "moveset": ["MUD_SHOT", "STONE_EDGE", "ANCIENT_POWER"], "score": 82.8}, {"speciesId": "pelipper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 422, "matchups": [{"opponent": "blaziken", "rating": 742, "opRating": 257}, {"opponent": "rhyperior", "rating": 693, "opRating": 306}, {"opponent": "pinsir_shadow", "rating": 580, "opRating": 419}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 573, "opRating": 426}, {"opponent": "grou<PERSON>", "rating": 510}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "excadrill", "rating": 353}, {"opponent": "swampert", "rating": 405}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 39033}, {"moveId": "WATER_GUN", "uses": 37467}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 40552}, {"moveId": "HYDRO_PUMP", "uses": 5375}, {"moveId": "HURRICANE", "uses": 16153}, {"moveId": "BLIZZARD", "uses": 14581}]}, "moveset": ["WING_ATTACK", "WEATHER_BALL_WATER", "HURRICANE"], "score": 82.8}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 671, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 762, "opRating": 237}, {"opponent": "melmetal", "rating": 743, "opRating": 256}, {"opponent": "swampert", "rating": 629}, {"opponent": "kyogre", "rating": 581, "opRating": 418}, {"opponent": "excadrill", "rating": 525}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "garcho<PERSON>", "rating": 427}, {"opponent": "metagross", "rating": 430}, {"opponent": "gyarados", "rating": 474}, {"opponent": "zacian_hero", "rating": 485}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 9777}, {"moveId": "CONFUSION", "uses": 44764}, {"moveId": "CHARM", "uses": 21958}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 13430}, {"moveId": "PSYCHIC", "uses": 6188}, {"moveId": "LEAF_BLADE", "uses": 25796}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 31138}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 82.7}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 675, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 801, "opRating": 198}, {"opponent": "swampert", "rating": 672}, {"opponent": "excadrill", "rating": 577}, {"opponent": "dialga", "rating": 574}, {"opponent": "zekrom", "rating": 542, "opRating": 457}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "garcho<PERSON>", "rating": 359}, {"opponent": "zacian_hero", "rating": 398}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 10530}, {"moveId": "CONFUSION", "uses": 42919}, {"moveId": "CHARM", "uses": 23003}], "chargedMoves": [{"moveId": "SYNCHRONOISE", "uses": 12178}, {"moveId": "RETURN", "uses": 5618}, {"moveId": "PSYCHIC", "uses": 5513}, {"moveId": "LEAF_BLADE", "uses": 23879}, {"moveId": "CLOSE_COMBAT", "uses": 29156}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 82.7}, {"speciesId": "pinsir_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 659, "matchups": [{"opponent": "zarude", "rating": 875, "opRating": 124}, {"opponent": "mewtwo_shadow", "rating": 815, "opRating": 184}, {"opponent": "snor<PERSON>_shadow", "rating": 775, "opRating": 224}, {"opponent": "terrakion", "rating": 691, "opRating": 308}, {"opponent": "dialga", "rating": 533}], "counters": [{"opponent": "gyarados", "rating": 286}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "garcho<PERSON>", "rating": 408}, {"opponent": "metagross", "rating": 409}, {"opponent": "mewtwo", "rating": 427}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 9586}, {"moveId": "FURY_CUTTER", "uses": 40118}, {"moveId": "BUG_BITE", "uses": 26807}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 22156}, {"moveId": "VICE_GRIP", "uses": 7704}, {"moveId": "SUPER_POWER", "uses": 18624}, {"moveId": "SUBMISSION", "uses": 3015}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "CLOSE_COMBAT", "uses": 25016}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 82.7}, {"speciesId": "pinsir", "speciesName": "Pinsir", "rating": 638, "matchups": [{"opponent": "darkrai", "rating": 855, "opRating": 144}, {"opponent": "zarude", "rating": 848, "opRating": 151}, {"opponent": "lucario", "rating": 815, "opRating": 184}, {"opponent": "hydreigon", "rating": 644, "opRating": 355}, {"opponent": "excadrill", "rating": 536}], "counters": [{"opponent": "lugia", "rating": 273}, {"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "metagross", "rating": 369}, {"opponent": "mewtwo", "rating": 390}, {"opponent": "dialga", "rating": 478}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 9850}, {"moveId": "FURY_CUTTER", "uses": 40175}, {"moveId": "BUG_BITE", "uses": 26461}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 20040}, {"moveId": "VICE_GRIP", "uses": 6541}, {"moveId": "SUPER_POWER", "uses": 17060}, {"moveId": "SUBMISSION", "uses": 2758}, {"moveId": "RETURN", "uses": 7260}, {"moveId": "CLOSE_COMBAT", "uses": 22931}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 82.7}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 605, "matchups": [{"opponent": "gyarados", "rating": 771}, {"opponent": "snorlax", "rating": 596, "opRating": 403}, {"opponent": "dialga", "rating": 553}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 540}, {"opponent": "zacian_hero", "rating": 532}], "counters": [{"opponent": "giratina_origin", "rating": 145}, {"opponent": "dragonite", "rating": 284}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "lugia", "rating": 466}, {"opponent": "metagross", "rating": 476}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 52020}, {"moveId": "CHARGE_BEAM", "uses": 24480}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 6805}, {"moveId": "THUNDER_PUNCH", "uses": 21744}, {"moveId": "THUNDER", "uses": 7001}, {"moveId": "RETURN", "uses": 8789}, {"moveId": "POWER_GEM", "uses": 8015}, {"moveId": "FOCUS_BLAST", "uses": 13038}, {"moveId": "DRAGON_PULSE", "uses": 11065}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 82.6}, {"speciesId": "ampha<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 605, "matchups": [{"opponent": "gyarados", "rating": 715}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 567}, {"opponent": "dialga", "rating": 513}, {"opponent": "lugia", "rating": 508}, {"opponent": "metagross", "rating": 505}], "counters": [{"opponent": "giratina_origin", "rating": 169}, {"opponent": "mewtwo", "rating": 171}, {"opponent": "swampert", "rating": 233}, {"opponent": "dragonite", "rating": 340}, {"opponent": "zacian_hero", "rating": 343}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 53410}, {"moveId": "CHARGE_BEAM", "uses": 23090}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 7598}, {"moveId": "THUNDER_PUNCH", "uses": 24291}, {"moveId": "THUNDER", "uses": 7856}, {"moveId": "POWER_GEM", "uses": 9079}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FOCUS_BLAST", "uses": 14823}, {"moveId": "DRAGON_PULSE", "uses": 12871}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 82.6}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 542, "matchups": [{"opponent": "bewear", "rating": 918, "opRating": 81}, {"opponent": "victini", "rating": 851, "opRating": 148}, {"opponent": "darmanitan_standard", "rating": 758, "opRating": 241}, {"opponent": "moltres_shadow", "rating": 693, "opRating": 306}, {"opponent": "golisopod", "rating": 665, "opRating": 334}], "counters": [{"opponent": "dialga", "rating": 258}, {"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "giratina_origin", "rating": 322}, {"opponent": "gyarados", "rating": 358}, {"opponent": "mewtwo", "rating": 369}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 8623}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3839}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3574}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4291}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3406}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3307}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5104}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4473}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3582}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4354}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3854}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4077}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4038}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3933}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3920}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4297}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3524}, {"moveId": "CHARGE_BEAM", "uses": 4260}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 16190}, {"moveId": "TRI_ATTACK", "uses": 26311}, {"moveId": "SOLAR_BEAM", "uses": 10797}, {"moveId": "RETURN", "uses": 16646}, {"moveId": "HYPER_BEAM", "uses": 6445}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "RETURN"], "score": 82.6}, {"speciesId": "rapidash_galarian", "speciesName": "Rapidash (Galarian)", "rating": 524, "matchups": [{"opponent": "hydreigon", "rating": 855, "opRating": 144}, {"opponent": "latios_shadow", "rating": 785, "opRating": 214}, {"opponent": "latios", "rating": 718, "opRating": 281}, {"opponent": "dragonite", "rating": 687}, {"opponent": "palkia", "rating": 516, "opRating": 483}], "counters": [{"opponent": "mewtwo", "rating": 203}, {"opponent": "dialga", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "gyarados", "rating": 474}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 32419}, {"moveId": "LOW_KICK", "uses": 7712}, {"moveId": "FAIRY_WIND", "uses": 36342}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 16831}, {"moveId": "PLAY_ROUGH", "uses": 15033}, {"moveId": "MEGAHORN", "uses": 17179}, {"moveId": "BODY_SLAM", "uses": 27414}]}, "moveset": ["FAIRY_WIND", "BODY_SLAM", "MEGAHORN"], "score": 82.6}, {"speciesId": "dialga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 772, "matchups": [{"opponent": "lugia", "rating": 622}, {"opponent": "mewtwo", "rating": 611}, {"opponent": "gyarados", "rating": 581}, {"opponent": "giratina_origin", "rating": 567}, {"opponent": "zacian_hero", "rating": 521}], "counters": [{"opponent": "excadrill", "rating": 253}, {"opponent": "swampert", "rating": 313}, {"opponent": "metagross", "rating": 418}, {"opponent": "garcho<PERSON>", "rating": 446}, {"opponent": "dragonite", "rating": 486}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 24136}, {"moveId": "DRAGON_BREATH", "uses": 52364}], "chargedMoves": [{"moveId": "THUNDER", "uses": 22479}, {"moveId": "IRON_HEAD", "uses": 28858}, {"moveId": "DRACO_METEOR", "uses": 25274}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 82.5}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 666, "matchups": [{"opponent": "ho_oh", "rating": 747, "opRating": 252}, {"opponent": "gyarados", "rating": 714}, {"opponent": "lugia", "rating": 644}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 597, "opRating": 402}, {"opponent": "metagross", "rating": 555}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "dragonite", "rating": 404}, {"opponent": "excadrill", "rating": 486}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58330}, {"moveId": "ASTONISH", "uses": 18170}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 23882}, {"moveId": "THUNDER", "uses": 7717}, {"moveId": "CRUNCH", "uses": 25637}, {"moveId": "BRICK_BREAK", "uses": 19320}]}, "moveset": ["THUNDER_SHOCK", "CRUNCH", "THUNDER"], "score": 82.5}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 515, "matchups": [{"opponent": "tapu_bulu", "rating": 913, "opRating": 86}, {"opponent": "virizion", "rating": 777, "opRating": 222}, {"opponent": "swampert_shadow", "rating": 686, "opRating": 313}, {"opponent": "haxorus", "rating": 650, "opRating": 350}, {"opponent": "buzzwole", "rating": 605, "opRating": 394}], "counters": [{"opponent": "dialga", "rating": 190}, {"opponent": "zacian_hero", "rating": 277}, {"opponent": "gyarados", "rating": 381}, {"opponent": "garcho<PERSON>", "rating": 455}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 40813}, {"moveId": "BUG_BITE", "uses": 35687}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26369}, {"moveId": "ANCIENT_POWER", "uses": 25645}, {"moveId": "AERIAL_ACE", "uses": 24432}]}, "moveset": ["WING_ATTACK", "BUG_BUZZ", "ANCIENT_POWER"], "score": 82.1}, {"speciesId": "sylveon", "speciesName": "Sylveon", "rating": 686, "matchups": [{"opponent": "zekrom", "rating": 842}, {"opponent": "dragonite", "rating": 822}, {"opponent": "yveltal", "rating": 814, "opRating": 185}, {"opponent": "garcho<PERSON>", "rating": 644}, {"opponent": "gyarados", "rating": 559}], "counters": [{"opponent": "mewtwo", "rating": 367}, {"opponent": "swampert", "rating": 412}, {"opponent": "lugia", "rating": 414}, {"opponent": "zacian_hero", "rating": 447}, {"opponent": "dialga", "rating": 461}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39623}, {"moveId": "CHARM", "uses": 36877}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 18295}, {"moveId": "MOONBLAST", "uses": 27112}, {"moveId": "LAST_RESORT", "uses": 13215}, {"moveId": "DRAINING_KISS", "uses": 11004}, {"moveId": "DAZZLING_GLEAM", "uses": 6884}]}, "moveset": ["CHARM", "MOONBLAST", "PSYSHOCK"], "score": 82}, {"speciesId": "sci<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 656, "matchups": [{"opponent": "x<PERSON><PERSON>", "rating": 824, "opRating": 175}, {"opponent": "sylveon", "rating": 681, "opRating": 318}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 652, "opRating": 347}, {"opponent": "latios_shadow", "rating": 630, "opRating": 369}, {"opponent": "meloetta_aria", "rating": 627, "opRating": 372}], "counters": [{"opponent": "garcho<PERSON>", "rating": 312}, {"opponent": "zacian_hero", "rating": 395}, {"opponent": "dialga", "rating": 415}, {"opponent": "lugia", "rating": 438}, {"opponent": "mewtwo", "rating": 440}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38525}, {"moveId": "BULLET_PUNCH", "uses": 37975}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25196}, {"moveId": "NIGHT_SLASH", "uses": 34175}, {"moveId": "IRON_HEAD", "uses": 17052}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "IRON_HEAD"], "score": 82}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 541, "matchups": [{"opponent": "genesect_shock", "rating": 909, "opRating": 90}, {"opponent": "genesect_douse", "rating": 909, "opRating": 90}, {"opponent": "genesect_chill", "rating": 904, "opRating": 95}, {"opponent": "genesect_burn", "rating": 904, "opRating": 95}, {"opponent": "metagross", "rating": 555}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "lugia", "rating": 326}, {"opponent": "excadrill", "rating": 374}, {"opponent": "dialga", "rating": 380}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 14588}, {"moveId": "EMBER", "uses": 61912}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 20748}, {"moveId": "HEAT_WAVE", "uses": 2362}, {"moveId": "FOCUS_BLAST", "uses": 13637}, {"moveId": "FLAME_CHARGE", "uses": 8562}, {"moveId": "BLAST_BURN", "uses": 31121}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 82}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 390, "matchups": [{"opponent": "heracross", "rating": 723, "opRating": 276}, {"opponent": "virizion", "rating": 691, "opRating": 308}, {"opponent": "bewear", "rating": 676, "opRating": 323}, {"opponent": "buzzwole", "rating": 661, "opRating": 338}, {"opponent": "giratina_origin", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "mewtwo", "rating": 213}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 24680}, {"moveId": "AIR_SLASH", "uses": 51820}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 17803}, {"moveId": "HEAT_WAVE", "uses": 3404}, {"moveId": "CLOSE_COMBAT", "uses": 26573}, {"moveId": "BRAVE_BIRD", "uses": 28723}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 82}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 660, "matchups": [{"opponent": "metagross", "rating": 661}, {"opponent": "dialga", "rating": 650}, {"opponent": "zacian_hero", "rating": 582}, {"opponent": "excadrill", "rating": 556}, {"opponent": "giratina_origin", "rating": 535}], "counters": [{"opponent": "swampert", "rating": 211}, {"opponent": "grou<PERSON>", "rating": 220}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "lugia", "rating": 376}, {"opponent": "mewtwo", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 40284}, {"moveId": "MUD_SLAP", "uses": 36216}], "chargedMoves": [{"moveId": "SURF", "uses": 12715}, {"moveId": "SUPER_POWER", "uses": 15092}, {"moveId": "STONE_EDGE", "uses": 6954}, {"moveId": "SKULL_BASH", "uses": 5457}, {"moveId": "ROCK_WRECKER", "uses": 24525}, {"moveId": "EARTHQUAKE", "uses": 11749}]}, "moveset": ["MUD_SLAP", "ROCK_WRECKER", "SURF"], "score": 81.9}, {"speciesId": "suicune_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 618, "matchups": [{"opponent": "excadrill", "rating": 646}, {"opponent": "metagross", "rating": 639}, {"opponent": "mewtwo", "rating": 582}, {"opponent": "garcho<PERSON>", "rating": 554}, {"opponent": "grou<PERSON>", "rating": 527}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "lugia", "rating": 302}, {"opponent": "gyarados", "rating": 304}, {"opponent": "giratina_origin", "rating": 386}, {"opponent": "zacian_hero", "rating": 494}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7390}, {"moveId": "ICE_FANG", "uses": 6277}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4609}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3162}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3860}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3000}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2961}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4629}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4097}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3179}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3983}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3435}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3740}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3669}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3560}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3497}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3911}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3149}, {"moveId": "EXTRASENSORY", "uses": 4761}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 13016}, {"moveId": "ICE_BEAM", "uses": 31544}, {"moveId": "HYDRO_PUMP", "uses": 18284}, {"moveId": "FRUSTRATION", "uses": 4}, {"moveId": "BUBBLE_BEAM", "uses": 13525}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 81.9}, {"speciesId": "suicune", "speciesName": "Suicune", "rating": 631, "matchups": [{"opponent": "excadrill", "rating": 716}, {"opponent": "ho_oh", "rating": 696, "opRating": 303}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 579, "opRating": 420}, {"opponent": "giratina_origin", "rating": 562}, {"opponent": "grou<PERSON>", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 247}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "gyarados", "rating": 440}, {"opponent": "garcho<PERSON>", "rating": 455}, {"opponent": "metagross", "rating": 459}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7185}, {"moveId": "ICE_FANG", "uses": 6330}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4487}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3132}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3856}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3014}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2905}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4567}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4126}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3151}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4008}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3447}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3684}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3645}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3572}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3485}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3948}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3137}, {"moveId": "EXTRASENSORY", "uses": 4685}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 10904}, {"moveId": "RETURN", "uses": 12643}, {"moveId": "ICE_BEAM", "uses": 26456}, {"moveId": "HYDRO_PUMP", "uses": 15218}, {"moveId": "BUBBLE_BEAM", "uses": 11311}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 81.9}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 647, "matchups": [{"opponent": "zacian_hero", "rating": 803}, {"opponent": "sylveon", "rating": 760, "opRating": 239}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 739}, {"opponent": "zekrom", "rating": 577, "opRating": 422}, {"opponent": "yveltal", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 160}, {"opponent": "lugia", "rating": 295}, {"opponent": "mewtwo", "rating": 307}, {"opponent": "dragonite", "rating": 329}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57086}, {"moveId": "BITE", "uses": 19414}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15260}, {"moveId": "SLUDGE_WAVE", "uses": 8996}, {"moveId": "POISON_FANG", "uses": 22361}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTH_POWER", "uses": 20733}, {"moveId": "EARTHQUAKE", "uses": 8907}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 81.9}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 634, "matchups": [{"opponent": "rai<PERSON>u", "rating": 916, "opRating": 83}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 728}, {"opponent": "sylveon", "rating": 728, "opRating": 271}, {"opponent": "zacian_hero", "rating": 650}, {"opponent": "zekrom", "rating": 634, "opRating": 365}], "counters": [{"opponent": "mewtwo", "rating": 250}, {"opponent": "dialga", "rating": 358}, {"opponent": "lugia", "rating": 359}, {"opponent": "dragonite", "rating": 396}, {"opponent": "gyarados", "rating": 407}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54546}, {"moveId": "BITE", "uses": 21954}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 14085}, {"moveId": "SLUDGE_WAVE", "uses": 8258}, {"moveId": "RETURN", "uses": 6246}, {"moveId": "POISON_FANG", "uses": 20527}, {"moveId": "EARTH_POWER", "uses": 19234}, {"moveId": "EARTHQUAKE", "uses": 8280}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 81.9}, {"speciesId": "dewgong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 461, "matchups": [{"opponent": "landorus_incarnate", "rating": 626, "opRating": 373}, {"opponent": "garcho<PERSON>", "rating": 602}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537}, {"opponent": "grou<PERSON>", "rating": 529}, {"opponent": "giratina_origin", "rating": 505}], "counters": [{"opponent": "dialga", "rating": 239}, {"opponent": "mewtwo", "rating": 255}, {"opponent": "lugia", "rating": 273}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "gyarados", "rating": 296}], "moves": {"fastMoves": [{"moveId": "IRON_TAIL", "uses": 8058}, {"moveId": "ICE_SHARD", "uses": 38881}, {"moveId": "FROST_BREATH", "uses": 29558}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 4730}, {"moveId": "ICY_WIND", "uses": 36268}, {"moveId": "BLIZZARD", "uses": 16153}, {"moveId": "AURORA_BEAM", "uses": 6483}, {"moveId": "AQUA_JET", "uses": 12694}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "BLIZZARD"], "score": 81.8}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 348, "matchups": [{"opponent": "absol_shadow", "rating": 826, "opRating": 173}, {"opponent": "quagsire", "rating": 612, "opRating": 387}, {"opponent": "noctowl", "rating": 582, "opRating": 417}, {"opponent": "lickilicky", "rating": 539, "opRating": 460}, {"opponent": "shiftry_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "zacian_hero", "rating": 225}, {"opponent": "mewtwo", "rating": 229}, {"opponent": "garcho<PERSON>", "rating": 255}, {"opponent": "gyarados", "rating": 260}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47558}, {"moveId": "ASTONISH", "uses": 28942}], "chargedMoves": [{"moveId": "RETURN", "uses": 23831}, {"moveId": "LOW_SWEEP", "uses": 20461}, {"moveId": "HYPER_BEAM", "uses": 9198}, {"moveId": "AERIAL_ACE", "uses": 22873}]}, "moveset": ["SCRATCH", "RETURN", "AERIAL_ACE"], "score": 81.7}, {"speciesId": "sandslash_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 563, "matchups": [{"opponent": "garcho<PERSON>", "rating": 814}, {"opponent": "dragonite", "rating": 740}, {"opponent": "lugia", "rating": 628}, {"opponent": "gyarados", "rating": 521}, {"opponent": "mewtwo", "rating": 509}], "counters": [{"opponent": "metagross", "rating": 200}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "giratina_origin", "rating": 350}, {"opponent": "zekrom", "rating": 478}, {"opponent": "dialga", "rating": 491}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 30433}, {"moveId": "POWDER_SNOW", "uses": 33229}, {"moveId": "METAL_CLAW", "uses": 12847}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 34266}, {"moveId": "GYRO_BALL", "uses": 11206}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "BULLDOZE", "uses": 13219}, {"moveId": "BLIZZARD", "uses": 17733}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 81.6}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 637, "matchups": [{"opponent": "genesect_shock", "rating": 926, "opRating": 73}, {"opponent": "zacian_hero", "rating": 774}, {"opponent": "metagross", "rating": 767}, {"opponent": "sylveon", "rating": 679, "opRating": 320}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 644}], "counters": [{"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "mewtwo", "rating": 179}, {"opponent": "dialga", "rating": 247}, {"opponent": "excadrill", "rating": 376}, {"opponent": "lugia", "rating": 421}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34956}, {"moveId": "HEX", "uses": 23799}, {"moveId": "FIRE_SPIN", "uses": 17781}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26519}, {"moveId": "OVERHEAT", "uses": 16107}, {"moveId": "FLAME_CHARGE", "uses": 20815}, {"moveId": "ENERGY_BALL", "uses": 13060}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 81.6}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 568, "matchups": [{"opponent": "garcho<PERSON>", "rating": 814}, {"opponent": "dragonite", "rating": 804}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 722}, {"opponent": "lugia", "rating": 646}, {"opponent": "sylveon", "rating": 612, "opRating": 387}], "counters": [{"opponent": "zacian_hero", "rating": 242}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "mewtwo", "rating": 437}, {"opponent": "dialga", "rating": 440}, {"opponent": "gyarados", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 30535}, {"moveId": "POWDER_SNOW", "uses": 32489}, {"moveId": "METAL_CLAW", "uses": 13516}], "chargedMoves": [{"moveId": "RETURN", "uses": 8987}, {"moveId": "ICE_PUNCH", "uses": 30532}, {"moveId": "GYRO_BALL", "uses": 9805}, {"moveId": "BULLDOZE", "uses": 11473}, {"moveId": "BLIZZARD", "uses": 15863}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 81.6}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 493, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 818, "opRating": 181}, {"opponent": "victini", "rating": 778, "opRating": 221}, {"opponent": "metagross", "rating": 641}, {"opponent": "mewtwo", "rating": 582}, {"opponent": "swampert", "rating": 547}], "counters": [{"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "lugia", "rating": 359}, {"opponent": "gyarados", "rating": 396}, {"opponent": "giratina_origin", "rating": 468}, {"opponent": "zacian_hero", "rating": 471}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 58896}, {"moveId": "PECK", "uses": 17604}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 20113}, {"moveId": "PSYCHIC", "uses": 8753}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DARK_PULSE", "uses": 20532}, {"moveId": "BRAVE_BIRD", "uses": 26973}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 81.6}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 515, "matchups": [{"opponent": "celebi", "rating": 848, "opRating": 151}, {"opponent": "victini", "rating": 788, "opRating": 211}, {"opponent": "mewtwo", "rating": 636}, {"opponent": "metagross", "rating": 614}, {"opponent": "mewtwo_shadow", "rating": 582, "opRating": 417}], "counters": [{"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "dialga", "rating": 307}, {"opponent": "gyarados", "rating": 335}, {"opponent": "zacian_hero", "rating": 384}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 56966}, {"moveId": "PECK", "uses": 19534}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18709}, {"moveId": "RETURN", "uses": 5795}, {"moveId": "PSYCHIC", "uses": 8076}, {"moveId": "DARK_PULSE", "uses": 19092}, {"moveId": "BRAVE_BIRD", "uses": 24820}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 81.6}, {"speciesId": "genesect_chill", "speciesName": "Genesect (Chill)", "rating": 774, "matchups": [{"opponent": "garcho<PERSON>", "rating": 844}, {"opponent": "dragonite", "rating": 768}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "lugia", "rating": 569}], "counters": [{"opponent": "metagross", "rating": 421}, {"opponent": "giratina_origin", "rating": 434}, {"opponent": "dialga", "rating": 456}, {"opponent": "gyarados", "rating": 456}, {"opponent": "excadrill", "rating": 483}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 24396}, {"moveId": "FURY_CUTTER", "uses": 52104}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 22703}, {"moveId": "TECHNO_BLAST_CHILL", "uses": 26650}, {"moveId": "MAGNET_BOMB", "uses": 19737}, {"moveId": "ICE_BEAM", "uses": 7542}]}, "moveset": ["FURY_CUTTER", "TECHNO_BLAST_CHILL", "X_SCISSOR"], "score": 81.5}, {"speciesId": "genesect_douse", "speciesName": "Genesect (Douse)", "rating": 777, "matchups": [{"opponent": "mewtwo", "rating": 686}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "metagross", "rating": 598}, {"opponent": "lugia", "rating": 569}, {"opponent": "garcho<PERSON>", "rating": 506}], "counters": [{"opponent": "giratina_origin", "rating": 163}, {"opponent": "dragonite", "rating": 321}, {"opponent": "dialga", "rating": 355}, {"opponent": "gyarados", "rating": 427}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 26107}, {"moveId": "FURY_CUTTER", "uses": 50393}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24949}, {"moveId": "TECHNO_BLAST_DOUSE", "uses": 22970}, {"moveId": "MAGNET_BOMB", "uses": 22123}, {"moveId": "GUNK_SHOT", "uses": 6554}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_DOUSE"], "score": 81.5}, {"speciesId": "genesect_burn", "speciesName": "Genesect (Burn)", "rating": 764, "matchups": [{"opponent": "metagross", "rating": 844}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "excadrill", "rating": 642}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "lugia", "rating": 569}], "counters": [{"opponent": "dragonite", "rating": 321}, {"opponent": "swampert", "rating": 390}, {"opponent": "gyarados", "rating": 427}, {"opponent": "dialga", "rating": 456}, {"opponent": "garcho<PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 26009}, {"moveId": "FURY_CUTTER", "uses": 50491}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24637}, {"moveId": "TECHNO_BLAST_BURN", "uses": 22712}, {"moveId": "MAGNET_BOMB", "uses": 22846}, {"moveId": "FLAMETHROWER", "uses": 6415}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_BURN"], "score": 81.5}, {"speciesId": "genesect_shock", "speciesName": "Genesect (Shock)", "rating": 779, "matchups": [{"opponent": "gyarados", "rating": 844}, {"opponent": "mewtwo", "rating": 686}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "metagross", "rating": 598}, {"opponent": "lugia", "rating": 569}], "counters": [{"opponent": "excadrill", "rating": 332}, {"opponent": "dialga", "rating": 355}, {"opponent": "swampert", "rating": 390}, {"opponent": "dragonite", "rating": 417}, {"opponent": "garcho<PERSON>", "rating": 450}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 25773}, {"moveId": "FURY_CUTTER", "uses": 50727}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 5208}, {"moveId": "X_SCISSOR", "uses": 25922}, {"moveId": "TECHNO_BLAST_SHOCK", "uses": 22203}, {"moveId": "MAGNET_BOMB", "uses": 23200}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_SHOCK"], "score": 81.5}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 752, "matchups": [{"opponent": "mewtwo", "rating": 686}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 598}, {"opponent": "lugia", "rating": 569}, {"opponent": "garcho<PERSON>", "rating": 506}], "counters": [{"opponent": "dialga", "rating": 355}, {"opponent": "dragonite", "rating": 417}, {"opponent": "metagross", "rating": 421}, {"opponent": "gyarados", "rating": 456}, {"opponent": "swampert", "rating": 492}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 27209}, {"moveId": "FURY_CUTTER", "uses": 49291}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26833}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 20825}, {"moveId": "MAGNET_BOMB", "uses": 25109}, {"moveId": "HYPER_BEAM", "uses": 3605}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 81.5}, {"speciesId": "crawdaunt", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 484, "matchups": [{"opponent": "victini", "rating": 792, "opRating": 207}, {"opponent": "metagross", "rating": 710}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "mewtwo_shadow", "rating": 625, "opRating": 374}, {"opponent": "mewtwo", "rating": 622}], "counters": [{"opponent": "dialga", "rating": 225}, {"opponent": "gyarados", "rating": 226}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "lugia", "rating": 328}, {"opponent": "giratina_origin", "rating": 440}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 31996}, {"moveId": "SNARL", "uses": 44504}], "chargedMoves": [{"moveId": "VICE_GRIP", "uses": 7339}, {"moveId": "NIGHT_SLASH", "uses": 39307}, {"moveId": "CRABHAMMER", "uses": 23016}, {"moveId": "BUBBLE_BEAM", "uses": 6931}]}, "moveset": ["SNARL", "NIGHT_SLASH", "CRABHAMMER"], "score": 81.4}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 656, "matchups": [{"opponent": "genesect_chill", "rating": 787, "opRating": 212}, {"opponent": "metagross", "rating": 770}, {"opponent": "yveltal", "rating": 671, "opRating": 328}, {"opponent": "dialga", "rating": 561}, {"opponent": "snorlax", "rating": 558, "opRating": 441}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 173}, {"opponent": "lugia", "rating": 235}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "excadrill", "rating": 420}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30195}, {"moveId": "COUNTER", "uses": 46305}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 11839}, {"moveId": "OVERHEAT", "uses": 4524}, {"moveId": "FOCUS_BLAST", "uses": 9829}, {"moveId": "BRAVE_BIRD", "uses": 16005}, {"moveId": "BLAZE_KICK", "uses": 13115}, {"moveId": "BLAST_BURN", "uses": 21227}]}, "moveset": ["COUNTER", "BLAZE_KICK", "BLAST_BURN"], "score": 81.2}, {"speciesId": "cryogonal", "speciesName": "Cryogonal", "rating": 553, "matchups": [{"opponent": "land<PERSON><PERSON>_therian", "rating": 857, "opRating": 142}, {"opponent": "dragonite_shadow", "rating": 648, "opRating": 351}, {"opponent": "zapdos", "rating": 630, "opRating": 369}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 619, "opRating": 380}, {"opponent": "garcho<PERSON>", "rating": 569}], "counters": [{"opponent": "mewtwo", "rating": 354}, {"opponent": "dialga", "rating": 366}, {"opponent": "gyarados", "rating": 414}, {"opponent": "dragonite", "rating": 470}, {"opponent": "giratina_origin", "rating": 476}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 44969}, {"moveId": "FROST_BREATH", "uses": 31531}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 7783}, {"moveId": "SOLAR_BEAM", "uses": 10048}, {"moveId": "NIGHT_SLASH", "uses": 39081}, {"moveId": "AURORA_BEAM", "uses": 19593}]}, "moveset": ["ICE_SHARD", "NIGHT_SLASH", "AURORA_BEAM"], "score": 81.1}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 344, "matchups": [{"opponent": "gourgeist_large", "rating": 781, "opRating": 218}, {"opponent": "gourgeist_super", "rating": 778, "opRating": 221}, {"opponent": "trevenant", "rating": 725, "opRating": 274}, {"opponent": "giratina_origin", "rating": 633}, {"opponent": "gengar", "rating": 605, "opRating": 394}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "dialga", "rating": 165}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "lugia", "rating": 269}, {"opponent": "metagross", "rating": 337}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 28499}, {"moveId": "FEINT_ATTACK", "uses": 48001}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 31298}, {"moveId": "BRAVE_BIRD", "uses": 34426}, {"moveId": "AIR_CUTTER", "uses": 3825}, {"moveId": "AERIAL_ACE", "uses": 6889}]}, "moveset": ["FEINT_ATTACK", "BRAVE_BIRD", "DRILL_PECK"], "score": 81}, {"speciesId": "latios_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 778, "matchups": [{"opponent": "garcho<PERSON>", "rating": 857}, {"opponent": "ho_oh", "rating": 851, "opRating": 148}, {"opponent": "swampert", "rating": 758}, {"opponent": "giratina_origin", "rating": 648}, {"opponent": "gyarados", "rating": 578}], "counters": [{"opponent": "dialga", "rating": 301}, {"opponent": "mewtwo", "rating": 377}, {"opponent": "lugia", "rating": 385}, {"opponent": "dragonite", "rating": 470}, {"opponent": "excadrill", "rating": 497}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9006}, {"moveId": "DRAGON_BREATH", "uses": 67494}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 7469}, {"moveId": "PSYCHIC", "uses": 14247}, {"moveId": "LUSTER_PURGE", "uses": 21562}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRAGON_CLAW", "uses": 33218}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 80.9}, {"speciesId": "latios", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 783, "matchups": [{"opponent": "garcho<PERSON>", "rating": 892}, {"opponent": "giratina_origin", "rating": 718}, {"opponent": "swampert", "rating": 607}, {"opponent": "grou<PERSON>", "rating": 514}, {"opponent": "dragonite", "rating": 508}], "counters": [{"opponent": "dialga", "rating": 263}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "metagross", "rating": 348}, {"opponent": "excadrill", "rating": 402}, {"opponent": "gyarados", "rating": 492}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9832}, {"moveId": "DRAGON_BREATH", "uses": 66668}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6720}, {"moveId": "RETURN", "uses": 7312}, {"moveId": "PSYCHIC", "uses": 13000}, {"moveId": "LUSTER_PURGE", "uses": 19611}, {"moveId": "DRAGON_CLAW", "uses": 30046}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 80.9}, {"speciesId": "dragalge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "chandelure", "rating": 828, "opRating": 171}, {"opponent": "magmortar_shadow", "rating": 748, "opRating": 251}, {"opponent": "terrakion", "rating": 583, "opRating": 416}, {"opponent": "rai<PERSON>u", "rating": 557, "opRating": 442}, {"opponent": "kyogre", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 173}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "dragonite", "rating": 321}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 24367}, {"moveId": "DRAGON_TAIL", "uses": 37629}, {"moveId": "ACID", "uses": 14401}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 27305}, {"moveId": "HYDRO_PUMP", "uses": 4930}, {"moveId": "GUNK_SHOT", "uses": 13271}, {"moveId": "AQUA_TAIL", "uses": 31005}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "OUTRAGE"], "score": 80.9}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 597, "matchups": [{"opponent": "mewtwo", "rating": 791}, {"opponent": "metagross", "rating": 708}, {"opponent": "excadrill", "rating": 610}, {"opponent": "giratina_origin", "rating": 603}, {"opponent": "dialga", "rating": 579}], "counters": [{"opponent": "dragonite", "rating": 276}, {"opponent": "gyarados", "rating": 278}, {"opponent": "swampert", "rating": 313}, {"opponent": "garcho<PERSON>", "rating": 366}, {"opponent": "lugia", "rating": 447}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46589}, {"moveId": "MUD_SLAP", "uses": 29911}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 16506}, {"moveId": "EARTHQUAKE", "uses": 23061}, {"moveId": "CRUNCH", "uses": 36924}]}, "moveset": ["SNARL", "CRUNCH", "EARTHQUAKE"], "score": 80.8}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 571, "matchups": [{"opponent": "metagross", "rating": 744}, {"opponent": "swampert", "rating": 688}, {"opponent": "zacian_hero", "rating": 637}, {"opponent": "garcho<PERSON>", "rating": 573}, {"opponent": "excadrill", "rating": 547}], "counters": [{"opponent": "giratina_origin", "rating": 183}, {"opponent": "grou<PERSON>", "rating": 252}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "gyarados", "rating": 317}, {"opponent": "dialga", "rating": 336}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 27026}, {"moveId": "HEX", "uses": 49474}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26371}, {"moveId": "SEED_BOMB", "uses": 21688}, {"moveId": "FOUL_PLAY", "uses": 19766}, {"moveId": "FIRE_BLAST", "uses": 8624}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 80.8}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 540, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 841, "opRating": 158}, {"opponent": "swampert", "rating": 661}, {"opponent": "swampert_shadow", "rating": 618, "opRating": 381}, {"opponent": "zacian_hero", "rating": 612}, {"opponent": "excadrill", "rating": 521}], "counters": [{"opponent": "mewtwo", "rating": 281}, {"opponent": "gyarados", "rating": 314}, {"opponent": "dialga", "rating": 334}, {"opponent": "metagross", "rating": 468}, {"opponent": "garcho<PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 27110}, {"moveId": "HEX", "uses": 49390}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26375}, {"moveId": "SEED_BOMB", "uses": 21786}, {"moveId": "FOUL_PLAY", "uses": 19814}, {"moveId": "FIRE_BLAST", "uses": 8612}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 80.8}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 503, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 825, "opRating": 174}, {"opponent": "gallade", "rating": 714, "opRating": 285}, {"opponent": "swampert", "rating": 640}, {"opponent": "swampert_shadow", "rating": 587, "opRating": 412}, {"opponent": "zacian_hero", "rating": 583}], "counters": [{"opponent": "mewtwo", "rating": 278}, {"opponent": "dialga", "rating": 301}, {"opponent": "gyarados", "rating": 309}, {"opponent": "metagross", "rating": 462}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 27143}, {"moveId": "HEX", "uses": 49357}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26353}, {"moveId": "SEED_BOMB", "uses": 21717}, {"moveId": "FOUL_PLAY", "uses": 19749}, {"moveId": "FIRE_BLAST", "uses": 8558}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 80.8}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 475, "matchups": [{"opponent": "kommo_o", "rating": 862, "opRating": 137}, {"opponent": "dragonite", "rating": 620}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 604}, {"opponent": "sylveon", "rating": 604, "opRating": 395}, {"opponent": "yveltal", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 171}, {"opponent": "dialga", "rating": 255}, {"opponent": "lugia", "rating": 292}, {"opponent": "zacian_hero", "rating": 378}, {"opponent": "gyarados", "rating": 402}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 27750}, {"moveId": "FAIRY_WIND", "uses": 48750}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 23793}, {"moveId": "PLAY_ROUGH", "uses": 23135}, {"moveId": "OVERHEAT", "uses": 19884}, {"moveId": "HYPER_BEAM", "uses": 9659}]}, "moveset": ["FAIRY_WIND", "SLUDGE", "PLAY_ROUGH"], "score": 80.8}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 460, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 807, "opRating": 192}, {"opponent": "gallade", "rating": 685, "opRating": 314}, {"opponent": "swampert", "rating": 611}, {"opponent": "latios_shadow", "rating": 570, "opRating": 429}, {"opponent": "zacian_hero", "rating": 540}], "counters": [{"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "dialga", "rating": 298}, {"opponent": "gyarados", "rating": 306}, {"opponent": "excadrill", "rating": 400}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 27217}, {"moveId": "HEX", "uses": 49283}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26411}, {"moveId": "SEED_BOMB", "uses": 21650}, {"moveId": "FOUL_PLAY", "uses": 19825}, {"moveId": "FIRE_BLAST", "uses": 8561}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 80.8}, {"speciesId": "kommo_o", "speciesName": "Kommo-o", "rating": 780, "matchups": [{"opponent": "garcho<PERSON>", "rating": 887}, {"opponent": "giratina_origin", "rating": 695}, {"opponent": "excadrill", "rating": 673}, {"opponent": "swampert", "rating": 637}, {"opponent": "dialga", "rating": 503}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "lugia", "rating": 242}, {"opponent": "gyarados", "rating": 399}, {"opponent": "dragonite", "rating": 406}, {"opponent": "metagross", "rating": 430}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 34403}, {"moveId": "DRAGON_TAIL", "uses": 42097}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 12132}, {"moveId": "DRAGON_CLAW", "uses": 29135}, {"moveId": "CLOSE_COMBAT", "uses": 35293}]}, "moveset": ["DRAGON_TAIL", "CLOSE_COMBAT", "DRAGON_CLAW"], "score": 80.7}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 557, "matchups": [{"opponent": "gengar", "rating": 853, "opRating": 146}, {"opponent": "tapu_lele", "rating": 729, "opRating": 270}, {"opponent": "moltres_shadow", "rating": 665, "opRating": 334}, {"opponent": "tangrowth", "rating": 662, "opRating": 337}, {"opponent": "mewtwo_shadow", "rating": 538, "opRating": 461}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "gyarados", "rating": 268}, {"opponent": "mewtwo", "rating": 484}, {"opponent": "giratina_origin", "rating": 484}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24828}, {"moveId": "INFESTATION", "uses": 18839}, {"moveId": "ICE_FANG", "uses": 17713}, {"moveId": "BITE", "uses": 15045}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 17245}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FELL_STINGER", "uses": 5420}, {"moveId": "CRUNCH", "uses": 31069}, {"moveId": "AQUA_TAIL", "uses": 22765}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 80.5}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 550, "matchups": [{"opponent": "espeon", "rating": 697, "opRating": 302}, {"opponent": "trevenant", "rating": 697, "opRating": 302}, {"opponent": "tangrowth_shadow", "rating": 662, "opRating": 337}, {"opponent": "victini", "rating": 573, "opRating": 426}, {"opponent": "mewtwo_armored", "rating": 544, "opRating": 455}], "counters": [{"opponent": "zacian_hero", "rating": 303}, {"opponent": "dialga", "rating": 304}, {"opponent": "lugia", "rating": 326}, {"opponent": "giratina_origin", "rating": 416}, {"opponent": "mewtwo", "rating": 424}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24573}, {"moveId": "INFESTATION", "uses": 18923}, {"moveId": "ICE_FANG", "uses": 17798}, {"moveId": "BITE", "uses": 15162}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 15417}, {"moveId": "RETURN", "uses": 7093}, {"moveId": "FELL_STINGER", "uses": 5021}, {"moveId": "CRUNCH", "uses": 28373}, {"moveId": "AQUA_TAIL", "uses": 20667}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 80.5}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 342, "matchups": [{"opponent": "tapu_koko", "rating": 728, "opRating": 271}, {"opponent": "tapu_bulu", "rating": 631, "opRating": 368}, {"opponent": "tapu_fini", "rating": 567, "opRating": 432}, {"opponent": "rhyperior", "rating": 548, "opRating": 451}, {"opponent": "tangrowth_shadow", "rating": 539, "opRating": 460}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "gyarados", "rating": 157}, {"opponent": "lugia", "rating": 202}, {"opponent": "zacian_hero", "rating": 254}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 38434}, {"moveId": "BULLET_SEED", "uses": 38066}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 12287}, {"moveId": "PETAL_BLIZZARD", "uses": 30458}, {"moveId": "AERIAL_ACE", "uses": 33813}]}, "moveset": ["POISON_JAB", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 80.1}, {"speciesId": "avalugg", "speciesName": "Avalugg", "rating": 687, "matchups": [{"opponent": "garcho<PERSON>", "rating": 896}, {"opponent": "dragonite", "rating": 750}, {"opponent": "lugia", "rating": 639}, {"opponent": "gyarados", "rating": 618}, {"opponent": "giratina_origin", "rating": 613}], "counters": [{"opponent": "metagross", "rating": 229}, {"opponent": "dialga", "rating": 342}, {"opponent": "mewtwo", "rating": 385}, {"opponent": "excadrill", "rating": 434}, {"opponent": "yveltal", "rating": 474}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 52494}, {"moveId": "BITE", "uses": 24006}], "chargedMoves": [{"moveId": "MIRROR_COAT", "uses": 3079}, {"moveId": "EARTHQUAKE", "uses": 10124}, {"moveId": "CRUNCH", "uses": 14425}, {"moveId": "BODY_SLAM", "uses": 17220}, {"moveId": "AVALANCHE", "uses": 31693}]}, "moveset": ["ICE_FANG", "AVALANCHE", "BODY_SLAM"], "score": 79.9}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 669, "matchups": [{"opponent": "grou<PERSON>", "rating": 645}, {"opponent": "zacian_hero", "rating": 631}, {"opponent": "metagross", "rating": 612}, {"opponent": "dialga", "rating": 580}, {"opponent": "mewtwo", "rating": 532}], "counters": [{"opponent": "giratina_origin", "rating": 264}, {"opponent": "lugia", "rating": 302}, {"opponent": "excadrill", "rating": 344}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "swampert", "rating": 363}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 37362}, {"moveId": "FIRE_SPIN", "uses": 39138}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26949}, {"moveId": "RETURN", "uses": 8184}, {"moveId": "OVERHEAT", "uses": 17006}, {"moveId": "HEAT_WAVE", "uses": 2996}, {"moveId": "FIRE_BLAST", "uses": 4883}, {"moveId": "ANCIENT_POWER", "uses": 16529}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 79.8}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 660, "matchups": [{"opponent": "metagross", "rating": 809}, {"opponent": "sylveon", "rating": 701, "opRating": 298}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 674}, {"opponent": "zacian_hero", "rating": 607}, {"opponent": "grou<PERSON>", "rating": 599}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "dialga", "rating": 274}, {"opponent": "lugia", "rating": 290}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "excadrill", "rating": 409}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 37658}, {"moveId": "FIRE_SPIN", "uses": 38842}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30382}, {"moveId": "OVERHEAT", "uses": 18825}, {"moveId": "HEAT_WAVE", "uses": 3208}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_BLAST", "uses": 5463}, {"moveId": "ANCIENT_POWER", "uses": 18462}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 79.8}, {"speciesId": "sir<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 681, "matchups": [{"opponent": "metagross", "rating": 682}, {"opponent": "swampert", "rating": 651}, {"opponent": "dialga", "rating": 582}, {"opponent": "zekrom", "rating": 582}, {"opponent": "excadrill", "rating": 551}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 329}, {"opponent": "gyarados", "rating": 342}, {"opponent": "grou<PERSON>", "rating": 426}, {"opponent": "garcho<PERSON>", "rating": 460}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31947}, {"moveId": "COUNTER", "uses": 44553}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 17402}, {"moveId": "LEAF_BLADE", "uses": 19527}, {"moveId": "CLOSE_COMBAT", "uses": 24576}, {"moveId": "BRAVE_BIRD", "uses": 14973}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 79.8}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 343, "matchups": [{"opponent": "espeon", "rating": 716, "opRating": 283}, {"opponent": "gallade", "rating": 618, "opRating": 381}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 539, "opRating": 460}, {"opponent": "mewtwo_armored", "rating": 523, "opRating": 476}, {"opponent": "mewtwo", "rating": 507}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dialga", "rating": 144}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "excadrill", "rating": 293}, {"opponent": "metagross", "rating": 345}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40484}, {"moveId": "FEINT_ATTACK", "uses": 36016}], "chargedMoves": [{"moveId": "SHADOW_SNEAK", "uses": 23731}, {"moveId": "SHADOW_BALL", "uses": 42018}, {"moveId": "OMINOUS_WIND", "uses": 10762}]}, "moveset": ["SUCKER_PUNCH", "SHADOW_BALL", "SHADOW_SNEAK"], "score": 79.8}, {"speciesId": "vanilluxe", "speciesName": "Vanilluxe", "rating": 409, "matchups": [{"opponent": "gliscor_shadow", "rating": 882, "opRating": 117}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 870, "opRating": 129}, {"opponent": "landorus_incarnate", "rating": 870, "opRating": 129}, {"opponent": "dragonite_shadow", "rating": 636, "opRating": 363}, {"opponent": "dragonite", "rating": 632}], "counters": [{"opponent": "mewtwo", "rating": 239}, {"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 285}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 490}], "moves": {"fastMoves": [{"moveId": "FROST_BREATH", "uses": 54145}, {"moveId": "ASTONISH", "uses": 22355}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 23024}, {"moveId": "FLASH_CANNON", "uses": 15326}, {"moveId": "BLIZZARD", "uses": 38172}]}, "moveset": ["FROST_BREATH", "BLIZZARD", "SIGNAL_BEAM"], "score": 79.7}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 383, "matchups": [{"opponent": "darmanitan_standard", "rating": 817, "opRating": 182}, {"opponent": "chandelure", "rating": 806, "opRating": 193}, {"opponent": "typhlosion", "rating": 800, "opRating": 199}, {"opponent": "mamos<PERSON>_shadow", "rating": 794, "opRating": 205}, {"opponent": "moltres_shadow", "rating": 719, "opRating": 280}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "metagross", "rating": 293}, {"opponent": "excadrill", "rating": 409}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 37426}, {"moveId": "WATERFALL", "uses": 39074}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16443}, {"moveId": "HYDRO_PUMP", "uses": 30964}, {"moveId": "AQUA_JET", "uses": 29001}]}, "moveset": ["WATERFALL", "HYDRO_PUMP", "AQUA_JET"], "score": 79.5}, {"speciesId": "sawk", "speciesName": "Sawk", "rating": 504, "matchups": [{"opponent": "weavile", "rating": 875, "opRating": 125}, {"opponent": "tapu_bulu", "rating": 847, "opRating": 152}, {"opponent": "x<PERSON><PERSON>", "rating": 606, "opRating": 393}, {"opponent": "regirock", "rating": 554, "opRating": 445}, {"opponent": "snorlax", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "lugia", "rating": 288}, {"opponent": "gyarados", "rating": 327}, {"opponent": "zacian_hero", "rating": 416}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57529}, {"moveId": "LOW_KICK", "uses": 18971}], "chargedMoves": [{"moveId": "LOW_SWEEP", "uses": 16704}, {"moveId": "FOCUS_BLAST", "uses": 18634}, {"moveId": "BODY_SLAM", "uses": 41157}]}, "moveset": ["POISON_JAB", "BODY_SLAM", "FOCUS_BLAST"], "score": 79.4}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 673, "matchups": [{"opponent": "genesect_chill", "rating": 754, "opRating": 245}, {"opponent": "metagross", "rating": 748}, {"opponent": "dialga", "rating": 678}, {"opponent": "zekrom", "rating": 652}, {"opponent": "kyogre", "rating": 509, "opRating": 490}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "gyarados", "rating": 208}, {"opponent": "lugia", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 291}, {"opponent": "excadrill", "rating": 439}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45414}, {"moveId": "BULLET_PUNCH", "uses": 31086}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18813}, {"moveId": "POWER_UP_PUNCH", "uses": 6147}, {"moveId": "FLASH_CANNON", "uses": 8953}, {"moveId": "CLOSE_COMBAT", "uses": 34751}, {"moveId": "AURA_SPHERE", "uses": 7807}]}, "moveset": ["COUNTER", "POWER_UP_PUNCH", "SHADOW_BALL"], "score": 79.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Ray<PERSON><PERSON>", "rating": 704, "matchups": [{"opponent": "garcho<PERSON>", "rating": 929}, {"opponent": "grou<PERSON>", "rating": 725}, {"opponent": "swampert", "rating": 662}, {"opponent": "yveltal", "rating": 620, "opRating": 379}, {"opponent": "mewtwo", "rating": 526}], "counters": [{"opponent": "dialga", "rating": 307}, {"opponent": "gyarados", "rating": 373}, {"opponent": "lugia", "rating": 378}, {"opponent": "giratina_origin", "rating": 450}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 46887}, {"moveId": "AIR_SLASH", "uses": 29613}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23883}, {"moveId": "HURRICANE", "uses": 15939}, {"moveId": "ANCIENT_POWER", "uses": 19213}, {"moveId": "AERIAL_ACE", "uses": 17519}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "ANCIENT_POWER"], "score": 79.2}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 564, "matchups": [{"opponent": "raikou_shadow", "rating": 899, "opRating": 100}, {"opponent": "magnezone", "rating": 885, "opRating": 114}, {"opponent": "magnezone_shadow", "rating": 876, "opRating": 123}, {"opponent": "nihilego", "rating": 859, "opRating": 140}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 637, "opRating": 362}], "counters": [{"opponent": "mewtwo", "rating": 289}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "metagross", "rating": 427}, {"opponent": "lugia", "rating": 435}, {"opponent": "dialga", "rating": 445}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 50477}, {"moveId": "METAL_CLAW", "uses": 26023}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26258}, {"moveId": "MUDDY_WATER", "uses": 15306}, {"moveId": "FLASH_CANNON", "uses": 11208}, {"moveId": "EARTHQUAKE", "uses": 23730}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 79}, {"speciesId": "throh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 358, "matchups": [{"opponent": "weavile", "rating": 686, "opRating": 313}, {"opponent": "obstagoon", "rating": 616, "opRating": 383}, {"opponent": "tyranitar", "rating": 612, "opRating": 387}, {"opponent": "regice", "rating": 558, "opRating": 441}, {"opponent": "melmetal", "rating": 554, "opRating": 445}], "counters": [{"opponent": "zacian_hero", "rating": 170}, {"opponent": "gyarados", "rating": 190}, {"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "dialga", "rating": 290}, {"opponent": "excadrill", "rating": 320}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 27195}, {"moveId": "LOW_KICK", "uses": 49305}], "chargedMoves": [{"moveId": "LOW_SWEEP", "uses": 16788}, {"moveId": "FOCUS_BLAST", "uses": 18510}, {"moveId": "BODY_SLAM", "uses": 41226}]}, "moveset": ["LOW_KICK", "BODY_SLAM", "FOCUS_BLAST"], "score": 79}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 686, "matchups": [{"opponent": "sylveon", "rating": 751, "opRating": 248}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 726}, {"opponent": "metagross", "rating": 668}, {"opponent": "dialga", "rating": 616}, {"opponent": "zacian_hero", "rating": 616}], "counters": [{"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "gyarados", "rating": 288}, {"opponent": "lugia", "rating": 309}, {"opponent": "excadrill", "rating": 409}, {"opponent": "mewtwo", "rating": 479}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45437}, {"moveId": "FIRE_FANG", "uses": 31063}], "chargedMoves": [{"moveId": "RETURN", "uses": 10739}, {"moveId": "OVERHEAT", "uses": 17102}, {"moveId": "IRON_HEAD", "uses": 12537}, {"moveId": "FLAME_CHARGE", "uses": 21992}, {"moveId": "FLAMETHROWER", "uses": 9137}, {"moveId": "FIRE_BLAST", "uses": 5005}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 78.9}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 650, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 706}, {"opponent": "metagross", "rating": 603}, {"opponent": "dialga", "rating": 580}, {"opponent": "zacian_hero", "rating": 542}, {"opponent": "mewtwo", "rating": 508}], "counters": [{"opponent": "giratina_origin", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "gyarados", "rating": 278}, {"opponent": "lugia", "rating": 345}, {"opponent": "excadrill", "rating": 351}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46679}, {"moveId": "FIRE_FANG", "uses": 29821}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 19684}, {"moveId": "IRON_HEAD", "uses": 14851}, {"moveId": "FRUSTRATION", "uses": 3}, {"moveId": "FLAME_CHARGE", "uses": 25489}, {"moveId": "FLAMETHROWER", "uses": 10558}, {"moveId": "FIRE_BLAST", "uses": 5915}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 78.9}, {"speciesId": "barbara<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 568, "matchups": [{"opponent": "ho_oh", "rating": 921, "opRating": 78}, {"opponent": "ho_oh_shadow", "rating": 909, "opRating": 90}, {"opponent": "moltres", "rating": 896, "opRating": 103}, {"opponent": "moltres_shadow", "rating": 896, "opRating": 103}, {"opponent": "articuno_shadow", "rating": 768, "opRating": 231}], "counters": [{"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "metagross", "rating": 334}, {"opponent": "dialga", "rating": 350}, {"opponent": "gyarados", "rating": 430}, {"opponent": "mewtwo", "rating": 453}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 26754}, {"moveId": "MUD_SLAP", "uses": 18655}, {"moveId": "FURY_CUTTER", "uses": 31118}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26406}, {"moveId": "SKULL_BASH", "uses": 9642}, {"moveId": "GRASS_KNOT", "uses": 16312}, {"moveId": "CROSS_CHOP", "uses": 24121}]}, "moveset": ["FURY_CUTTER", "STONE_EDGE", "CROSS_CHOP"], "score": 78.8}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 493, "matchups": [{"opponent": "yveltal", "rating": 750, "opRating": 250}, {"opponent": "gyarados", "rating": 700}, {"opponent": "zap<PERSON>_shadow", "rating": 665, "opRating": 334}, {"opponent": "ho_oh", "rating": 616, "opRating": 383}, {"opponent": "kyogre", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 171}, {"opponent": "lugia", "rating": 366}, {"opponent": "dragonite", "rating": 375}, {"opponent": "zacian_hero", "rating": 381}, {"opponent": "metagross", "rating": 389}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 29125}, {"moveId": "THUNDER_SHOCK", "uses": 25719}, {"moveId": "SPARK", "uses": 21547}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32414}, {"moveId": "THUNDER_PUNCH", "uses": 15861}, {"moveId": "PSYCHIC", "uses": 14307}, {"moveId": "GRASS_KNOT", "uses": 13873}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 78.6}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 533, "matchups": [{"opponent": "gengar", "rating": 885, "opRating": 114}, {"opponent": "walrein_shadow", "rating": 779, "opRating": 220}, {"opponent": "ma<PERSON><PERSON>", "rating": 748, "opRating": 251}, {"opponent": "mew", "rating": 652, "opRating": 347}, {"opponent": "giratina_origin", "rating": 621}], "counters": [{"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "lugia", "rating": 350}, {"opponent": "mewtwo", "rating": 354}, {"opponent": "metagross", "rating": 436}, {"opponent": "dialga", "rating": 451}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45513}, {"moveId": "FURY_CUTTER", "uses": 30987}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 35924}, {"moveId": "DIG", "uses": 4513}, {"moveId": "CLOSE_COMBAT", "uses": 35968}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 78.5}, {"speciesId": "noivern", "speciesName": "Noivern", "rating": 376, "matchups": [{"opponent": "virizion", "rating": 691, "opRating": 308}, {"opponent": "pinsir_shadow", "rating": 657, "opRating": 342}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 640, "opRating": 359}, {"opponent": "grou<PERSON>", "rating": 592}, {"opponent": "buzzwole", "rating": 575, "opRating": 424}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "swampert", "rating": 315}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 30113}, {"moveId": "AIR_SLASH", "uses": 46387}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 20075}, {"moveId": "HURRICANE", "uses": 24785}, {"moveId": "HEAT_WAVE", "uses": 9564}, {"moveId": "DRACO_METEOR", "uses": 22047}]}, "moveset": ["AIR_SLASH", "HURRICANE", "DRACO_METEOR"], "score": 78.4}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 528, "matchups": [{"opponent": "tapu_bulu", "rating": 922, "opRating": 77}, {"opponent": "virizion", "rating": 816, "opRating": 183}, {"opponent": "florges", "rating": 654, "opRating": 345}, {"opponent": "sylveon", "rating": 647, "opRating": 352}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 609}], "counters": [{"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "dialga", "rating": 176}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 188}, {"opponent": "lugia", "rating": 202}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 43139}, {"moveId": "BUG_BITE", "uses": 33361}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25306}, {"moveId": "SLUDGE_BOMB", "uses": 19146}, {"moveId": "MEGAHORN", "uses": 24143}, {"moveId": "GYRO_BALL", "uses": 7857}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "MEGAHORN"], "score": 78.2}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 436, "matchups": [{"opponent": "escavalier", "rating": 693, "opRating": 306}, {"opponent": "genesect_chill", "rating": 606, "opRating": 393}, {"opponent": "genesect_burn", "rating": 606, "opRating": 393}, {"opponent": "virizion", "rating": 578, "opRating": 421}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 522}], "counters": [{"opponent": "mewtwo", "rating": 148}, {"opponent": "dialga", "rating": 154}, {"opponent": "excadrill", "rating": 295}, {"opponent": "gyarados", "rating": 363}, {"opponent": "metagross", "rating": 418}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 34961}, {"moveId": "FIRE_SPIN", "uses": 41539}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 29707}, {"moveId": "POWER_UP_PUNCH", "uses": 13273}, {"moveId": "FLAMETHROWER", "uses": 33507}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "THUNDER_PUNCH"], "score": 78.2}, {"speciesId": "espeon", "speciesName": "Espeon", "rating": 645, "matchups": [{"opponent": "buzzwole", "rating": 775, "opRating": 224}, {"opponent": "zap<PERSON>_galarian", "rating": 755, "opRating": 244}, {"opponent": "ho_oh", "rating": 674, "opRating": 325}, {"opponent": "metagross", "rating": 644}, {"opponent": "excadrill", "rating": 510}], "counters": [{"opponent": "dialga", "rating": 269}, {"opponent": "giratina_origin", "rating": 332}, {"opponent": "garcho<PERSON>", "rating": 441}, {"opponent": "gyarados", "rating": 451}, {"opponent": "zacian_hero", "rating": 456}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9626}, {"moveId": "CONFUSION", "uses": 66874}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 19350}, {"moveId": "PSYCHIC_FANGS", "uses": 24862}, {"moveId": "PSYCHIC", "uses": 13608}, {"moveId": "PSYBEAM", "uses": 2740}, {"moveId": "LAST_RESORT", "uses": 9987}, {"moveId": "FUTURE_SIGHT", "uses": 5998}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "SHADOW_BALL"], "score": 78.1}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "nihilego", "rating": 798, "opRating": 201}, {"opponent": "metagross", "rating": 719}, {"opponent": "excadrill", "rating": 719}, {"opponent": "dialga", "rating": 600}, {"opponent": "zekrom", "rating": 600}], "counters": [{"opponent": "lugia", "rating": 271}, {"opponent": "swampert", "rating": 442}, {"opponent": "garcho<PERSON>", "rating": 448}, {"opponent": "grou<PERSON>", "rating": 461}, {"opponent": "mewtwo", "rating": 497}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 38184}, {"moveId": "FURY_CUTTER", "uses": 38316}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 9255}, {"moveId": "NIGHT_SLASH", "uses": 30912}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 18793}, {"moveId": "AERIAL_ACE", "uses": 17400}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "EARTHQUAKE"], "score": 77.7}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 359, "matchups": [{"opponent": "sandslash_alolan_shadow", "rating": 812, "opRating": 187}, {"opponent": "abomasnow_shadow", "rating": 795, "opRating": 204}, {"opponent": "snor<PERSON>_shadow", "rating": 691, "opRating": 308}, {"opponent": "aurorus", "rating": 604, "opRating": 395}, {"opponent": "tyranitar", "rating": 593, "opRating": 406}], "counters": [{"opponent": "mewtwo", "rating": 140}, {"opponent": "giratina_origin", "rating": 183}, {"opponent": "dialga", "rating": 250}, {"opponent": "metagross", "rating": 357}, {"opponent": "excadrill", "rating": 393}], "moves": {"fastMoves": [{"moveId": "POUND", "uses": 9956}, {"moveId": "LOW_KICK", "uses": 11931}, {"moveId": "DOUBLE_KICK", "uses": 54584}], "chargedMoves": [{"moveId": "HYPER_BEAM", "uses": 20785}, {"moveId": "FOCUS_BLAST", "uses": 20944}, {"moveId": "FIRE_PUNCH", "uses": 34744}]}, "moveset": ["DOUBLE_KICK", "FIRE_PUNCH", "FOCUS_BLAST"], "score": 77.7}, {"speciesId": "malamar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 479, "matchups": [{"opponent": "espeon", "rating": 852, "opRating": 147}, {"opponent": "mewtwo_armored", "rating": 694, "opRating": 305}, {"opponent": "mewtwo", "rating": 638}, {"opponent": "excadrill", "rating": 577}, {"opponent": "mewtwo_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "garcho<PERSON>", "rating": 206}, {"opponent": "dialga", "rating": 211}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "gyarados", "rating": 247}, {"opponent": "lugia", "rating": 290}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 57886}, {"moveId": "PECK", "uses": 18614}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 28278}, {"moveId": "PSYBEAM", "uses": 8543}, {"moveId": "HYPER_BEAM", "uses": 7694}, {"moveId": "FOUL_PLAY", "uses": 32008}]}, "moveset": ["PSYCHO_CUT", "FOUL_PLAY", "SUPER_POWER"], "score": 77.6}, {"speciesId": "xatu", "speciesName": "Xatu", "rating": 306, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 637, "opRating": 362}, {"opponent": "virizion", "rating": 580, "opRating": 419}, {"opponent": "buzzwole", "rating": 550, "opRating": 449}, {"opponent": "heracross", "rating": 550, "opRating": 449}, {"opponent": "gallade", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "zacian_hero", "rating": 176}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 36697}, {"moveId": "AIR_SLASH", "uses": 39803}], "chargedMoves": [{"moveId": "OMINOUS_WIND", "uses": 20526}, {"moveId": "FUTURE_SIGHT", "uses": 26803}, {"moveId": "AERIAL_ACE", "uses": 29235}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "FUTURE_SIGHT"], "score": 77.6}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 805, "matchups": [{"opponent": "metagross", "rating": 816}, {"opponent": "excadrill", "rating": 735}, {"opponent": "mewtwo", "rating": 683}, {"opponent": "zacian_hero", "rating": 619}, {"opponent": "lugia", "rating": 513}], "counters": [{"opponent": "dragonite", "rating": 194}, {"opponent": "gyarados", "rating": 311}, {"opponent": "dialga", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 441}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 455}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 43370}, {"moveId": "DRAGON_TAIL", "uses": 33130}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 36704}, {"moveId": "OMINOUS_WIND", "uses": 18677}, {"moveId": "DRAGON_PULSE", "uses": 21113}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "OMINOUS_WIND"], "score": 77.4}, {"speciesId": "poliwrath_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 567, "matchups": [{"opponent": "darkrai", "rating": 903, "opRating": 96}, {"opponent": "mamos<PERSON>_shadow", "rating": 862, "opRating": 137}, {"opponent": "heatran", "rating": 825, "opRating": 174}, {"opponent": "excadrill", "rating": 615}, {"opponent": "snorlax", "rating": 559, "opRating": 440}], "counters": [{"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 268}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "metagross", "rating": 389}, {"opponent": "dialga", "rating": 486}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 10505}, {"moveId": "MUD_SHOT", "uses": 36052}, {"moveId": "BUBBLE", "uses": 29947}], "chargedMoves": [{"moveId": "SUBMISSION", "uses": 4552}, {"moveId": "SCALD", "uses": 19225}, {"moveId": "POWER_UP_PUNCH", "uses": 6756}, {"moveId": "ICE_PUNCH", "uses": 21673}, {"moveId": "HYDRO_PUMP", "uses": 4412}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DYNAMIC_PUNCH", "uses": 20059}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 77.3}, {"speciesId": "poliwrath", "speciesName": "Poliwrath", "rating": 557, "matchups": [{"opponent": "gliscor_shadow", "rating": 852, "opRating": 147}, {"opponent": "tyranitar_shadow", "rating": 809, "opRating": 190}, {"opponent": "regirock", "rating": 645, "opRating": 354}, {"opponent": "excadrill", "rating": 639}, {"opponent": "snorlax", "rating": 583, "opRating": 416}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "gyarados", "rating": 231}, {"opponent": "giratina_origin", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 485}, {"opponent": "dialga", "rating": 486}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 11211}, {"moveId": "MUD_SHOT", "uses": 35264}, {"moveId": "BUBBLE", "uses": 30060}], "chargedMoves": [{"moveId": "SUBMISSION", "uses": 4073}, {"moveId": "SCALD", "uses": 17533}, {"moveId": "RETURN", "uses": 6448}, {"moveId": "POWER_UP_PUNCH", "uses": 6222}, {"moveId": "ICE_PUNCH", "uses": 19770}, {"moveId": "HYDRO_PUMP", "uses": 3961}, {"moveId": "DYNAMIC_PUNCH", "uses": 18485}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 77.3}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 611, "matchups": [{"opponent": "excadrill", "rating": 740}, {"opponent": "cobalion", "rating": 701, "opRating": 298}, {"opponent": "buzzwole", "rating": 628, "opRating": 371}, {"opponent": "zacian_hero", "rating": 621}, {"opponent": "dialga", "rating": 582}], "counters": [{"opponent": "mewtwo", "rating": 296}, {"opponent": "lugia", "rating": 302}, {"opponent": "gyarados", "rating": 327}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "garcho<PERSON>", "rating": 403}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 37701}, {"moveId": "FURY_CUTTER", "uses": 38799}], "chargedMoves": [{"moveId": "SAND_TOMB", "uses": 8345}, {"moveId": "RETURN", "uses": 7865}, {"moveId": "NIGHT_SLASH", "uses": 27747}, {"moveId": "EARTHQUAKE", "uses": 17198}, {"moveId": "AERIAL_ACE", "uses": 15280}]}, "moveset": ["WING_ATTACK", "NIGHT_SLASH", "EARTHQUAKE"], "score": 77.2}, {"speciesId": "deoxys_defense", "speciesName": "<PERSON><PERSON><PERSON> (Defense)", "rating": 535, "matchups": [{"opponent": "lucario", "rating": 803, "opRating": 196}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 692, "opRating": 307}, {"opponent": "machamp", "rating": 653, "opRating": 346}, {"opponent": "sneasler", "rating": 590, "opRating": 409}, {"opponent": "terrakion", "rating": 543, "opRating": 456}], "counters": [{"opponent": "mewtwo", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "gyarados", "rating": 301}, {"opponent": "dialga", "rating": 339}, {"opponent": "zacian_hero", "rating": 367}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 9258}, {"moveId": "COUNTER", "uses": 67242}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16229}, {"moveId": "ROCK_SLIDE", "uses": 27363}, {"moveId": "PSYCHO_BOOST", "uses": 32949}]}, "moveset": ["COUNTER", "PSYCHO_BOOST", "ROCK_SLIDE"], "score": 77.2}, {"speciesId": "buzzwole", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 781, "matchups": [{"opponent": "excadrill", "rating": 811}, {"opponent": "swampert", "rating": 798}, {"opponent": "garcho<PERSON>", "rating": 682}, {"opponent": "dialga", "rating": 657}, {"opponent": "metagross", "rating": 559}], "counters": [{"opponent": "zacian_hero", "rating": 297}, {"opponent": "dragonite", "rating": 321}, {"opponent": "gyarados", "rating": 329}, {"opponent": "giratina_origin", "rating": 380}, {"opponent": "mewtwo", "rating": 406}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 30642}, {"moveId": "COUNTER", "uses": 45858}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34379}, {"moveId": "POWER_UP_PUNCH", "uses": 8177}, {"moveId": "LUNGE", "uses": 25714}, {"moveId": "FELL_STINGER", "uses": 8187}]}, "moveset": ["COUNTER", "SUPER_POWER", "LUNGE"], "score": 77.1}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 514, "matchups": [{"opponent": "lucario", "rating": 901, "opRating": 98}, {"opponent": "metagross", "rating": 722}, {"opponent": "garcho<PERSON>", "rating": 666}, {"opponent": "grou<PERSON>", "rating": 574}, {"opponent": "zacian_hero", "rating": 520}], "counters": [{"opponent": "mewtwo", "rating": 169}, {"opponent": "lugia", "rating": 311}, {"opponent": "dialga", "rating": 336}, {"opponent": "dragonite", "rating": 380}, {"opponent": "excadrill", "rating": 411}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55761}, {"moveId": "ASTONISH", "uses": 20739}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 29846}, {"moveId": "OMINOUS_WIND", "uses": 15275}, {"moveId": "ICY_WIND", "uses": 31416}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 77.1}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 733, "matchups": [{"opponent": "zacian_hero", "rating": 721}, {"opponent": "metagross", "rating": 674}, {"opponent": "mewtwo", "rating": 656}, {"opponent": "gyarados", "rating": 539}, {"opponent": "dialga", "rating": 531}], "counters": [{"opponent": "giratina_origin", "rating": 101}, {"opponent": "swampert", "rating": 151}, {"opponent": "lugia", "rating": 383}, {"opponent": "garcho<PERSON>", "rating": 462}, {"opponent": "dragonite", "rating": 492}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 2862}, {"moveId": "INCINERATE", "uses": 16261}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3019}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2692}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3413}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2610}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2473}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4043}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3690}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2951}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3481}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3944}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4049}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3221}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3151}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3081}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3431}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2791}, {"moveId": "EXTRASENSORY", "uses": 5264}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6366}, {"moveId": "SACRED_FIRE", "uses": 18812}, {"moveId": "RETURN", "uses": 6426}, {"moveId": "FIRE_BLAST", "uses": 3749}, {"moveId": "EARTHQUAKE", "uses": 11937}, {"moveId": "BRAVE_BIRD", "uses": 29075}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 77}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 701, "matchups": [{"opponent": "grou<PERSON>", "rating": 851}, {"opponent": "zacian_hero", "rating": 671}, {"opponent": "metagross", "rating": 648}, {"opponent": "garcho<PERSON>", "rating": 638}, {"opponent": "mewtwo", "rating": 572}], "counters": [{"opponent": "dragonite", "rating": 148}, {"opponent": "gyarados", "rating": 188}, {"opponent": "lugia", "rating": 440}, {"opponent": "giratina_origin", "rating": 476}, {"opponent": "dialga", "rating": 497}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 2675}, {"moveId": "INCINERATE", "uses": 18698}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2822}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2494}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3286}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2369}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2332}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3974}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3572}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2844}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3292}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3721}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3851}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3069}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3023}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2969}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3251}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2552}, {"moveId": "EXTRASENSORY", "uses": 5405}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 6981}, {"moveId": "SACRED_FIRE", "uses": 20406}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_BLAST", "uses": 4143}, {"moveId": "EARTHQUAKE", "uses": 12862}, {"moveId": "BRAVE_BIRD", "uses": 32098}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 77}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 534, "matchups": [{"opponent": "magnezone_shadow", "rating": 932, "opRating": 67}, {"opponent": "metagross", "rating": 714}, {"opponent": "excadrill", "rating": 573}, {"opponent": "zacian_hero", "rating": 546}, {"opponent": "zekrom", "rating": 524}], "counters": [{"opponent": "lugia", "rating": 154}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "swampert", "rating": 221}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 49436}, {"moveId": "ASTONISH", "uses": 27064}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 32161}, {"moveId": "EARTH_POWER", "uses": 23033}, {"moveId": "DYNAMIC_PUNCH", "uses": 21376}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "EARTH_POWER"], "score": 77}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 663, "matchups": [{"opponent": "swampert", "rating": 835}, {"opponent": "excadrill", "rating": 781}, {"opponent": "garcho<PERSON>", "rating": 664}, {"opponent": "zacian_hero", "rating": 639}, {"opponent": "gyarados", "rating": 554}], "counters": [{"opponent": "giratina_origin", "rating": 161}, {"opponent": "metagross", "rating": 171}, {"opponent": "lugia", "rating": 190}, {"opponent": "dialga", "rating": 192}, {"opponent": "dragonite", "rating": 385}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 29922}, {"moveId": "CONFUSION", "uses": 29628}, {"moveId": "CHARGE_BEAM", "uses": 16911}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 22112}, {"moveId": "PSYCHIC", "uses": 20069}, {"moveId": "LEAF_STORM", "uses": 15085}, {"moveId": "HYPER_BEAM", "uses": 8110}, {"moveId": "DAZZLING_GLEAM", "uses": 11038}]}, "moveset": ["CONFUSION", "SEED_BOMB", "LEAF_STORM"], "score": 76.9}, {"speciesId": "hitmon<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 517, "matchups": [{"opponent": "registeel", "rating": 889, "opRating": 110}, {"opponent": "regirock", "rating": 681, "opRating": 318}, {"opponent": "metagross", "rating": 586}, {"opponent": "melmetal", "rating": 543, "opRating": 456}, {"opponent": "cobalion", "rating": 527, "opRating": 472}], "counters": [{"opponent": "zacian_hero", "rating": 294}, {"opponent": "dialga", "rating": 361}, {"opponent": "excadrill", "rating": 420}, {"opponent": "dragonite", "rating": 449}, {"opponent": "garcho<PERSON>", "rating": 485}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 7495}, {"moveId": "COUNTER", "uses": 42706}, {"moveId": "BULLET_PUNCH", "uses": 26317}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 11850}, {"moveId": "POWER_UP_PUNCH", "uses": 2164}, {"moveId": "ICE_PUNCH", "uses": 16079}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FIRE_PUNCH", "uses": 10644}, {"moveId": "CLOSE_COMBAT", "uses": 24710}, {"moveId": "BRICK_BREAK", "uses": 10974}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 76.7}, {"speciesId": "hitmonchan", "speciesName": "Hitmonchan", "rating": 525, "matchups": [{"opponent": "regirock", "rating": 716, "opRating": 283}, {"opponent": "snorlax", "rating": 618, "opRating": 381}, {"opponent": "cobalion", "rating": 594, "opRating": 405}, {"opponent": "melmetal", "rating": 594, "opRating": 405}, {"opponent": "excadrill", "rating": 551}], "counters": [{"opponent": "giratina_origin", "rating": 191}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "dialga", "rating": 355}, {"opponent": "garcho<PERSON>", "rating": 415}, {"opponent": "metagross", "rating": 441}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 7990}, {"moveId": "COUNTER", "uses": 41623}, {"moveId": "BULLET_PUNCH", "uses": 26882}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 11041}, {"moveId": "RETURN", "uses": 4624}, {"moveId": "POWER_UP_PUNCH", "uses": 2079}, {"moveId": "ICE_PUNCH", "uses": 15108}, {"moveId": "FIRE_PUNCH", "uses": 10010}, {"moveId": "CLOSE_COMBAT", "uses": 23362}, {"moveId": "BRICK_BREAK", "uses": 10388}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 76.7}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 512, "matchups": [{"opponent": "genesect_chill", "rating": 857, "opRating": 142}, {"opponent": "genesect_burn", "rating": 857, "opRating": 142}, {"opponent": "metagross", "rating": 773}, {"opponent": "sylveon", "rating": 571, "opRating": 428}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 532}], "counters": [{"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "dialga", "rating": 271}, {"opponent": "lugia", "rating": 276}, {"opponent": "zacian_hero", "rating": 387}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 9327}, {"moveId": "PECK", "uses": 10025}, {"moveId": "INCINERATE", "uses": 35998}, {"moveId": "FIRE_SPIN", "uses": 21191}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 6863}, {"moveId": "FLAME_CHARGE", "uses": 21953}, {"moveId": "FIRE_BLAST", "uses": 9996}, {"moveId": "BRAVE_BIRD", "uses": 37712}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "FLAME_CHARGE"], "score": 76.7}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 421, "matchups": [{"opponent": "trevenant", "rating": 778, "opRating": 221}, {"opponent": "gengar", "rating": 735, "opRating": 264}, {"opponent": "golisopod", "rating": 735, "opRating": 264}, {"opponent": "heracross", "rating": 656, "opRating": 343}, {"opponent": "giratina_origin", "rating": 551}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "gyarados", "rating": 219}, {"opponent": "garcho<PERSON>", "rating": 443}, {"opponent": "grou<PERSON>", "rating": 453}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 22212}, {"moveId": "STEEL_WING", "uses": 11379}, {"moveId": "GUST", "uses": 25057}, {"moveId": "AIR_SLASH", "uses": 17697}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 7650}, {"moveId": "FEATHER_DANCE", "uses": 5343}, {"moveId": "BRAVE_BIRD", "uses": 42064}, {"moveId": "AIR_CUTTER", "uses": 4590}, {"moveId": "AERIAL_ACE", "uses": 16981}]}, "moveset": ["GUST", "BRAVE_BIRD", "AERIAL_ACE"], "score": 76.7}, {"speciesId": "gengar", "speciesName": "Gengar", "rating": 647, "matchups": [{"opponent": "metagross", "rating": 658}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 616}, {"opponent": "lugia", "rating": 609}, {"opponent": "zacian_hero", "rating": 584}, {"opponent": "giratina_origin", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "dragonite", "rating": 316}, {"opponent": "mewtwo", "rating": 442}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 13817}, {"moveId": "SHADOW_CLAW", "uses": 28961}, {"moveId": "LICK", "uses": 14986}, {"moveId": "HEX", "uses": 18807}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 3962}, {"moveId": "SLUDGE_BOMB", "uses": 11753}, {"moveId": "SHADOW_PUNCH", "uses": 16496}, {"moveId": "SHADOW_BALL", "uses": 16632}, {"moveId": "PSYCHIC", "uses": 8082}, {"moveId": "FOCUS_BLAST", "uses": 8834}, {"moveId": "DARK_PULSE", "uses": 10839}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SHADOW_PUNCH"], "score": 76.3}, {"speciesId": "zap<PERSON>_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 756, "matchups": [{"opponent": "excadrill", "rating": 763}, {"opponent": "grou<PERSON>", "rating": 653}, {"opponent": "dialga", "rating": 634}, {"opponent": "garcho<PERSON>", "rating": 607}, {"opponent": "gyarados", "rating": 543}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "lugia", "rating": 233}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "metagross", "rating": 366}, {"opponent": "dragonite", "rating": 385}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 76500}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34242}, {"moveId": "BRAVE_BIRD", "uses": 28892}, {"moveId": "ANCIENT_POWER", "uses": 13369}]}, "moveset": ["COUNTER", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 76.1}, {"speciesId": "charizard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 628, "matchups": [{"opponent": "garcho<PERSON>", "rating": 889}, {"opponent": "grou<PERSON>", "rating": 750}, {"opponent": "metagross", "rating": 729}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 568}, {"opponent": "zacian_hero", "rating": 520}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "lugia", "rating": 285}, {"opponent": "dialga", "rating": 296}, {"opponent": "dragonite", "rating": 385}, {"opponent": "giratina_origin", "rating": 460}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 15748}, {"moveId": "FIRE_SPIN", "uses": 17159}, {"moveId": "EMBER", "uses": 15386}, {"moveId": "DRAGON_BREATH", "uses": 16263}, {"moveId": "AIR_SLASH", "uses": 11914}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 7114}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 7495}, {"moveId": "FIRE_BLAST", "uses": 4099}, {"moveId": "DRAGON_CLAW", "uses": 25117}, {"moveId": "BLAST_BURN", "uses": 32754}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 75.7}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 632, "matchups": [{"opponent": "metagross", "rating": 773}, {"opponent": "grou<PERSON>", "rating": 589}, {"opponent": "zacian_hero", "rating": 568}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550}, {"opponent": "dialga", "rating": 520}], "counters": [{"opponent": "mewtwo", "rating": 273}, {"opponent": "dragonite", "rating": 321}, {"opponent": "garcho<PERSON>", "rating": 330}, {"opponent": "giratina_origin", "rating": 374}, {"opponent": "swampert", "rating": 460}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 15442}, {"moveId": "FIRE_SPIN", "uses": 16899}, {"moveId": "EMBER", "uses": 15597}, {"moveId": "DRAGON_BREATH", "uses": 16479}, {"moveId": "AIR_SLASH", "uses": 12125}], "chargedMoves": [{"moveId": "RETURN", "uses": 6967}, {"moveId": "OVERHEAT", "uses": 6383}, {"moveId": "FLAMETHROWER", "uses": 6802}, {"moveId": "FIRE_BLAST", "uses": 3588}, {"moveId": "DRAGON_CLAW", "uses": 22479}, {"moveId": "BLAST_BURN", "uses": 29949}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 75.7}, {"speciesId": "sliggoo", "speciesName": "Sliggoo", "rating": 318, "matchups": [{"opponent": "golem", "rating": 870, "opRating": 129}, {"opponent": "arcanine_shadow", "rating": 607, "opRating": 392}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 603, "opRating": 396}, {"opponent": "typhlosion", "rating": 584, "opRating": 415}, {"opponent": "magmortar_shadow", "rating": 558, "opRating": 441}], "counters": [{"opponent": "mewtwo", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "metagross", "rating": 226}, {"opponent": "excadrill", "rating": 393}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40181}, {"moveId": "TACKLE", "uses": 36319}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 5281}, {"moveId": "SLUDGE_WAVE", "uses": 16728}, {"moveId": "MUDDY_WATER", "uses": 27051}, {"moveId": "DRAGON_PULSE", "uses": 27419}]}, "moveset": ["WATER_GUN", "DRAGON_PULSE", "MUDDY_WATER"], "score": 75.7}, {"speciesId": "palkia", "speciesName": "Pa<PERSON><PERSON>", "rating": 812, "matchups": [{"opponent": "garcho<PERSON>", "rating": 883}, {"opponent": "giratina_origin", "rating": 698}, {"opponent": "excadrill", "rating": 675}, {"opponent": "mewtwo", "rating": 561}, {"opponent": "metagross", "rating": 526}], "counters": [{"opponent": "zacian_hero", "rating": 280}, {"opponent": "lugia", "rating": 326}, {"opponent": "dialga", "rating": 394}, {"opponent": "gyarados", "rating": 443}, {"opponent": "zekrom", "rating": 480}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 38512}, {"moveId": "DRAGON_BREATH", "uses": 37988}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 6411}, {"moveId": "FIRE_BLAST", "uses": 10859}, {"moveId": "DRACO_METEOR", "uses": 18160}, {"moveId": "AQUA_TAIL", "uses": 41135}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "DRACO_METEOR"], "score": 75.1}, {"speciesId": "gigalith", "speciesName": "Gigalith", "rating": 584, "matchups": [{"opponent": "ho_oh_shadow", "rating": 873, "opRating": 126}, {"opponent": "ho_oh", "rating": 870, "opRating": 129}, {"opponent": "lugia", "rating": 617}, {"opponent": "sylveon", "rating": 558, "opRating": 441}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 525, "opRating": 474}], "counters": [{"opponent": "mewtwo", "rating": 315}, {"opponent": "zacian_hero", "rating": 332}, {"opponent": "dragonite", "rating": 364}, {"opponent": "gyarados", "rating": 420}, {"opponent": "dialga", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43997}, {"moveId": "MUD_SLAP", "uses": 32503}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 26333}, {"moveId": "SOLAR_BEAM", "uses": 7794}, {"moveId": "ROCK_SLIDE", "uses": 31615}, {"moveId": "HEAVY_SLAM", "uses": 10661}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "SUPER_POWER"], "score": 75.1}, {"speciesId": "heracross", "speciesName": "Heracross", "rating": 693, "matchups": [{"opponent": "metagross", "rating": 732}, {"opponent": "swampert", "rating": 718}, {"opponent": "excadrill", "rating": 712}, {"opponent": "dialga", "rating": 604}, {"opponent": "garcho<PERSON>", "rating": 517}], "counters": [{"opponent": "mewtwo", "rating": 132}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "zacian_hero", "rating": 306}, {"opponent": "gyarados", "rating": 363}, {"opponent": "grou<PERSON>", "rating": 486}], "moves": {"fastMoves": [{"moveId": "STRUGGLE_BUG", "uses": 18375}, {"moveId": "COUNTER", "uses": 58125}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 15062}, {"moveId": "MEGAHORN", "uses": 18334}, {"moveId": "EARTHQUAKE", "uses": 10227}, {"moveId": "CLOSE_COMBAT", "uses": 32819}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ROCK_BLAST"], "score": 74.9}, {"speciesId": "pangoro", "speciesName": "Pangoro", "rating": 650, "matchups": [{"opponent": "metagross", "rating": 729}, {"opponent": "mewtwo", "rating": 667}, {"opponent": "excadrill", "rating": 623}, {"opponent": "giratina_origin", "rating": 590}, {"opponent": "dialga", "rating": 564}], "counters": [{"opponent": "dragonite", "rating": 228}, {"opponent": "gyarados", "rating": 239}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "lugia", "rating": 333}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42511}, {"moveId": "LOW_KICK", "uses": 6020}, {"moveId": "BULLET_PUNCH", "uses": 27990}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 14193}, {"moveId": "NIGHT_SLASH", "uses": 26573}, {"moveId": "IRON_HEAD", "uses": 6620}, {"moveId": "CLOSE_COMBAT", "uses": 29035}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 74.7}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 637, "matchups": [{"opponent": "zacian_hero", "rating": 795}, {"opponent": "metagross", "rating": 778}, {"opponent": "sylveon", "rating": 728, "opRating": 271}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 709}, {"opponent": "dialga", "rating": 555}], "counters": [{"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "mewtwo", "rating": 179}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "lugia", "rating": 319}, {"opponent": "excadrill", "rating": 432}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 16447}, {"moveId": "INCINERATE", "uses": 42156}, {"moveId": "FIRE_FANG", "uses": 17867}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27027}, {"moveId": "PSYCHIC", "uses": 14542}, {"moveId": "OVERHEAT", "uses": 21181}, {"moveId": "FOCUS_BLAST", "uses": 13759}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 74.7}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 506, "matchups": [{"opponent": "swampert", "rating": 814}, {"opponent": "kyogre", "rating": 600, "opRating": 399}, {"opponent": "gyarados", "rating": 588}, {"opponent": "sylveon", "rating": 560, "opRating": 439}, {"opponent": "excadrill", "rating": 527}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "grou<PERSON>", "rating": 440}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 65301}, {"moveId": "IRON_TAIL", "uses": 11199}], "chargedMoves": [{"moveId": "LEAF_TORNADO", "uses": 17974}, {"moveId": "GRASS_KNOT", "uses": 9212}, {"moveId": "FRENZY_PLANT", "uses": 34919}, {"moveId": "AERIAL_ACE", "uses": 14225}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "LEAF_TORNADO"], "score": 74.7}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 762, "matchups": [{"opponent": "dialga", "rating": 803}, {"opponent": "gyarados", "rating": 762}, {"opponent": "lugia", "rating": 737}, {"opponent": "excadrill", "rating": 561}, {"opponent": "dragonite", "rating": 542}], "counters": [{"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "giratina_origin", "rating": 312}, {"opponent": "metagross", "rating": 340}, {"opponent": "mewtwo", "rating": 447}, {"opponent": "zacian_hero", "rating": 456}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 76500}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 12332}, {"moveId": "SUPER_POWER", "uses": 26435}, {"moveId": "ROCK_SLIDE", "uses": 21501}, {"moveId": "HYPER_BEAM", "uses": 6476}, {"moveId": "FLASH_CANNON", "uses": 9904}]}, "moveset": ["THUNDER_SHOCK", "SUPER_POWER", "ROCK_SLIDE"], "score": 74.4}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 363, "matchups": [{"opponent": "avalugg", "rating": 646, "opRating": 353}, {"opponent": "heatran", "rating": 633, "opRating": 366}, {"opponent": "zarude", "rating": 589, "opRating": 410}, {"opponent": "genesect_chill", "rating": 515, "opRating": 484}, {"opponent": "genesect_burn", "rating": 515, "opRating": 484}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "zacian_hero", "rating": 144}, {"opponent": "dialga", "rating": 182}, {"opponent": "lugia", "rating": 183}, {"opponent": "metagross", "rating": 258}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38550}, {"moveId": "EMBER", "uses": 37950}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 15314}, {"moveId": "OVERHEAT", "uses": 34870}, {"moveId": "EARTHQUAKE", "uses": 26338}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 74.3}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 605, "matchups": [{"opponent": "kyogre", "rating": 791, "opRating": 208}, {"opponent": "grou<PERSON>", "rating": 701}, {"opponent": "swampert", "rating": 662}, {"opponent": "excadrill", "rating": 636}, {"opponent": "snorlax", "rating": 548, "opRating": 451}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "dragonite", "rating": 430}, {"opponent": "giratina_origin", "rating": 432}, {"opponent": "garcho<PERSON>", "rating": 434}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42431}, {"moveId": "BULLET_SEED", "uses": 34069}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 12501}, {"moveId": "SEED_BOMB", "uses": 26944}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "DRAGON_PULSE", "uses": 19505}, {"moveId": "DRACO_METEOR", "uses": 17438}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 74.1}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 608, "matchups": [{"opponent": "swampert", "rating": 734}, {"opponent": "grou<PERSON>", "rating": 716}, {"opponent": "kyogre", "rating": 711, "opRating": 288}, {"opponent": "land<PERSON><PERSON>_therian", "rating": 652, "opRating": 347}, {"opponent": "snorlax", "rating": 577, "opRating": 422}], "counters": [{"opponent": "dialga", "rating": 211}, {"opponent": "mewtwo", "rating": 273}, {"opponent": "giratina_origin", "rating": 360}, {"opponent": "dragonite", "rating": 404}, {"opponent": "garcho<PERSON>", "rating": 408}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42901}, {"moveId": "BULLET_SEED", "uses": 33599}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 10705}, {"moveId": "SEED_BOMB", "uses": 23474}, {"moveId": "RETURN", "uses": 10621}, {"moveId": "DRAGON_PULSE", "uses": 16690}, {"moveId": "DRACO_METEOR", "uses": 14877}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 74.1}, {"speciesId": "kingler", "speciesName": "<PERSON><PERSON>", "rating": 537, "matchups": [{"opponent": "heatran", "rating": 818, "opRating": 181}, {"opponent": "moltres_shadow", "rating": 818, "opRating": 181}, {"opponent": "entei", "rating": 759, "opRating": 240}, {"opponent": "magnezone_shadow", "rating": 629, "opRating": 370}, {"opponent": "excadrill", "rating": 529}], "counters": [{"opponent": "mewtwo", "rating": 231}, {"opponent": "dialga", "rating": 271}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "metagross", "rating": 369}, {"opponent": "grou<PERSON>", "rating": 396}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32039}, {"moveId": "METAL_CLAW", "uses": 15895}, {"moveId": "BUBBLE", "uses": 28553}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24817}, {"moveId": "WATER_PULSE", "uses": 5144}, {"moveId": "VICE_GRIP", "uses": 11043}, {"moveId": "CRABHAMMER", "uses": 35513}]}, "moveset": ["MUD_SHOT", "CRABHAMMER", "X_SCISSOR"], "score": 73.8}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 683, "matchups": [{"opponent": "sylveon", "rating": 711, "opRating": 288}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 679}, {"opponent": "zacian_hero", "rating": 573}, {"opponent": "excadrill", "rating": 528}, {"opponent": "gyarados", "rating": 521}], "counters": [{"opponent": "garcho<PERSON>", "rating": 208}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "dialga", "rating": 228}, {"opponent": "dragonite", "rating": 300}, {"opponent": "metagross", "rating": 302}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 14594}, {"moveId": "POISON_JAB", "uses": 32789}, {"moveId": "BULLET_SEED", "uses": 29115}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 25144}, {"moveId": "SOLAR_BEAM", "uses": 3419}, {"moveId": "SLUDGE_BOMB", "uses": 14350}, {"moveId": "LEAF_STORM", "uses": 9677}, {"moveId": "GRASS_KNOT", "uses": 16397}, {"moveId": "DAZZLING_GLEAM", "uses": 7480}]}, "moveset": ["POISON_JAB", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 73.5}, {"speciesId": "samu<PERSON>t", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 628, "matchups": [{"opponent": "magmortar_shadow", "rating": 842, "opRating": 157}, {"opponent": "regirock", "rating": 688, "opRating": 311}, {"opponent": "metagross", "rating": 603}, {"opponent": "excadrill", "rating": 600}, {"opponent": "swampert", "rating": 528}], "counters": [{"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "lugia", "rating": 295}, {"opponent": "dialga", "rating": 304}, {"opponent": "gyarados", "rating": 314}, {"opponent": "mewtwo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 33164}, {"moveId": "FURY_CUTTER", "uses": 43336}], "chargedMoves": [{"moveId": "RAZOR_SHELL", "uses": 14294}, {"moveId": "MEGAHORN", "uses": 13760}, {"moveId": "HYDRO_PUMP", "uses": 3551}, {"moveId": "HYDRO_CANNON", "uses": 33883}, {"moveId": "BLIZZARD", "uses": 10948}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "RAZOR_SHELL"], "score": 73.4}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 676, "matchups": [{"opponent": "mewtwo", "rating": 779}, {"opponent": "swampert", "rating": 642}, {"opponent": "gyarados", "rating": 594}, {"opponent": "giratina_origin", "rating": 569}, {"opponent": "grou<PERSON>", "rating": 556}], "counters": [{"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "dialga", "rating": 342}, {"opponent": "metagross", "rating": 363}, {"opponent": "excadrill", "rating": 434}, {"opponent": "lugia", "rating": 440}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 37470}, {"moveId": "SUCKER_PUNCH", "uses": 39030}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 23939}, {"moveId": "BRAVE_BIRD", "uses": 36311}, {"moveId": "ANCIENT_POWER", "uses": 16409}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 73.1}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 568, "matchups": [{"opponent": "metagross", "rating": 744}, {"opponent": "mewtwo", "rating": 728}, {"opponent": "lugia", "rating": 620}, {"opponent": "giratina_altered", "rating": 580, "opRating": 419}, {"opponent": "giratina_origin", "rating": 546}], "counters": [{"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "dragonite", "rating": 303}, {"opponent": "swampert", "rating": 325}, {"opponent": "gyarados", "rating": 329}, {"opponent": "dialga", "rating": 339}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 51443}, {"moveId": "METAL_CLAW", "uses": 25057}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18496}, {"moveId": "IRON_HEAD", "uses": 17264}, {"moveId": "FOCUS_BLAST", "uses": 13127}, {"moveId": "DARK_PULSE", "uses": 27649}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 73}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 699, "matchups": [{"opponent": "zacian_hero", "rating": 777}, {"opponent": "ho_oh", "rating": 740, "opRating": 259}, {"opponent": "gyarados", "rating": 676}, {"opponent": "lugia", "rating": 631}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 551}], "counters": [{"opponent": "mewtwo", "rating": 247}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 359}, {"opponent": "metagross", "rating": 462}, {"opponent": "dragonite", "rating": 494}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 68151}, {"moveId": "LOW_KICK", "uses": 8349}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28344}, {"moveId": "THUNDER_PUNCH", "uses": 13793}, {"moveId": "THUNDER", "uses": 4507}, {"moveId": "ICE_PUNCH", "uses": 19261}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAMETHROWER", "uses": 10505}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 72.7}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 677, "matchups": [{"opponent": "yveltal", "rating": 868, "opRating": 131}, {"opponent": "gyarados", "rating": 740}, {"opponent": "ho_oh", "rating": 685, "opRating": 314}, {"opponent": "lugia", "rating": 646}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 637}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "dragonite", "rating": 401}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 67014}, {"moveId": "LOW_KICK", "uses": 9486}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26488}, {"moveId": "THUNDER_PUNCH", "uses": 12997}, {"moveId": "THUNDER", "uses": 4187}, {"moveId": "RETURN", "uses": 5123}, {"moveId": "ICE_PUNCH", "uses": 17904}, {"moveId": "FLAMETHROWER", "uses": 9810}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 72.7}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 421, "matchups": [{"opponent": "empoleon", "rating": 807, "opRating": 192}, {"opponent": "feraligatr", "rating": 799, "opRating": 200}, {"opponent": "feraligatr_shadow", "rating": 799, "opRating": 200}, {"opponent": "tapu_fini", "rating": 744, "opRating": 255}, {"opponent": "gyarados", "rating": 606}], "counters": [{"opponent": "dialga", "rating": 206}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "lugia", "rating": 290}, {"opponent": "metagross", "rating": 328}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 460}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 55462}, {"moveId": "ASTONISH", "uses": 21038}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34969}, {"moveId": "THUNDER", "uses": 15165}, {"moveId": "OMINOUS_WIND", "uses": 26252}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 72.5}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 389, "matchups": [{"opponent": "heracross", "rating": 895, "opRating": 104}, {"opponent": "virizion", "rating": 695, "opRating": 304}, {"opponent": "gallade", "rating": 644, "opRating": 355}, {"opponent": "buzzwole", "rating": 618, "opRating": 381}, {"opponent": "garcho<PERSON>", "rating": 546}], "counters": [{"opponent": "giratina_origin", "rating": 129}, {"opponent": "dialga", "rating": 165}, {"opponent": "gyarados", "rating": 188}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "zacian_hero", "rating": 205}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 12488}, {"moveId": "AIR_SLASH", "uses": 64012}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 17147}, {"moveId": "OMINOUS_WIND", "uses": 11680}, {"moveId": "DAZZLING_GLEAM", "uses": 9351}, {"moveId": "BRAVE_BIRD", "uses": 38298}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "PSYCHIC"], "score": 72.3}, {"speciesId": "grou<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 801, "matchups": [{"opponent": "metagross", "rating": 801}, {"opponent": "dialga", "rating": 725}, {"opponent": "zacian_hero", "rating": 633}, {"opponent": "mewtwo", "rating": 595}, {"opponent": "garcho<PERSON>", "rating": 592}], "counters": [{"opponent": "gyarados", "rating": 255}, {"opponent": "lugia", "rating": 278}, {"opponent": "giratina_origin", "rating": 490}, {"opponent": "swampert", "rating": 492}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 494}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43196}, {"moveId": "DRAGON_TAIL", "uses": 33304}], "chargedMoves": [{"moveId": "SOLAR_BEAM", "uses": 12779}, {"moveId": "FIRE_PUNCH", "uses": 28457}, {"moveId": "FIRE_BLAST", "uses": 5732}, {"moveId": "EARTHQUAKE", "uses": 29652}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "FIRE_PUNCH"], "score": 72.2}, {"speciesId": "primeape", "speciesName": "Primeape", "rating": 542, "matchups": [{"opponent": "registeel", "rating": 899, "opRating": 100}, {"opponent": "snorlax", "rating": 755, "opRating": 244}, {"opponent": "regirock", "rating": 701, "opRating": 298}, {"opponent": "snor<PERSON>_shadow", "rating": 674, "opRating": 325}, {"opponent": "dialga", "rating": 513}], "counters": [{"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "gyarados", "rating": 250}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "excadrill", "rating": 374}, {"opponent": "metagross", "rating": 386}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 4666}, {"moveId": "KARATE_CHOP", "uses": 34481}, {"moveId": "COUNTER", "uses": 37349}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 18724}, {"moveId": "LOW_SWEEP", "uses": 3025}, {"moveId": "ICE_PUNCH", "uses": 15215}, {"moveId": "CROSS_CHOP", "uses": 16153}, {"moveId": "CLOSE_COMBAT", "uses": 23348}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 72.2}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 528, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 711}, {"opponent": "gyarados", "rating": 661}, {"opponent": "sylveon", "rating": 602}, {"opponent": "yveltal", "rating": 598, "opRating": 401}, {"opponent": "lugia", "rating": 538}], "counters": [{"opponent": "garcho<PERSON>", "rating": 98}, {"opponent": "giratina_origin", "rating": 127}, {"opponent": "zacian_hero", "rating": 179}, {"opponent": "mewtwo", "rating": 213}, {"opponent": "dialga", "rating": 263}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 48961}, {"moveId": "CHARGE_BEAM", "uses": 27539}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 19264}, {"moveId": "MIRROR_SHOT", "uses": 29933}, {"moveId": "HYPER_BEAM", "uses": 11764}, {"moveId": "FLASH_CANNON", "uses": 15564}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 72.1}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 487, "matchups": [{"opponent": "nihilego", "rating": 814, "opRating": 185}, {"opponent": "metagross", "rating": 698}, {"opponent": "dialga", "rating": 600}, {"opponent": "excadrill", "rating": 591}, {"opponent": "zekrom", "rating": 585}], "counters": [{"opponent": "zacian_hero", "rating": 153}, {"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "giratina_origin", "rating": 175}, {"opponent": "lugia", "rating": 188}, {"opponent": "mewtwo", "rating": 234}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53155}, {"moveId": "METAL_CLAW", "uses": 23345}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 10634}, {"moveId": "RETURN", "uses": 8497}, {"moveId": "NIGHT_SLASH", "uses": 28714}, {"moveId": "EARTHQUAKE", "uses": 17840}, {"moveId": "BULLDOZE", "uses": 10935}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 71}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 510, "matchups": [{"opponent": "rai<PERSON>u", "rating": 887, "opRating": 112}, {"opponent": "metagross", "rating": 667}, {"opponent": "dialga", "rating": 557}, {"opponent": "zekrom", "rating": 539}, {"opponent": "excadrill", "rating": 536}], "counters": [{"opponent": "gyarados", "rating": 164}, {"opponent": "garcho<PERSON>", "rating": 173}, {"opponent": "giratina_origin", "rating": 195}, {"opponent": "lugia", "rating": 219}, {"opponent": "mewtwo", "rating": 236}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53831}, {"moveId": "METAL_CLAW", "uses": 22669}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 12244}, {"moveId": "NIGHT_SLASH", "uses": 32038}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "EARTHQUAKE", "uses": 19907}, {"moveId": "BULLDOZE", "uses": 12221}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 71}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 467, "matchups": [{"opponent": "genesect_chill", "rating": 782, "opRating": 217}, {"opponent": "genesect_burn", "rating": 782, "opRating": 217}, {"opponent": "sylveon", "rating": 737, "opRating": 262}, {"opponent": "metagross", "rating": 704}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 704}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "lugia", "rating": 297}, {"opponent": "excadrill", "rating": 374}, {"opponent": "zacian_hero", "rating": 393}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 34029}, {"moveId": "INCINERATE", "uses": 42471}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 13183}, {"moveId": "POISON_FANG", "uses": 32832}, {"moveId": "FIRE_BLAST", "uses": 16160}, {"moveId": "DRAGON_PULSE", "uses": 14355}]}, "moveset": ["INCINERATE", "POISON_FANG", "FIRE_BLAST"], "score": 70.9}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 538, "matchups": [{"opponent": "magnezone_shadow", "rating": 891, "opRating": 108}, {"opponent": "magnezone", "rating": 860, "opRating": 139}, {"opponent": "nihilego", "rating": 853, "opRating": 146}, {"opponent": "melmetal", "rating": 774, "opRating": 225}, {"opponent": "excadrill", "rating": 653}], "counters": [{"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "mewtwo", "rating": 309}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "dialga", "rating": 461}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41928}, {"moveId": "BUBBLE", "uses": 34572}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19914}, {"moveId": "MUDDY_WATER", "uses": 26523}, {"moveId": "EARTH_POWER", "uses": 30087}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "MUDDY_WATER"], "score": 70.6}, {"speciesId": "articuno_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 711, "matchups": [{"opponent": "grou<PERSON>", "rating": 647}, {"opponent": "garcho<PERSON>", "rating": 604}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 594}, {"opponent": "sylveon", "rating": 577, "opRating": 422}, {"opponent": "zacian_hero", "rating": 526}], "counters": [{"opponent": "mewtwo", "rating": 197}, {"opponent": "dialga", "rating": 282}, {"opponent": "lugia", "rating": 402}, {"opponent": "gyarados", "rating": 481}, {"opponent": "swampert", "rating": 495}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 43825}, {"moveId": "CONFUSION", "uses": 32675}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 16002}, {"moveId": "BRAVE_BIRD", "uses": 41522}, {"moveId": "ANCIENT_POWER", "uses": 18982}]}, "moveset": ["PSYCHO_CUT", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 70.1}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 387, "matchups": [{"opponent": "krookodile", "rating": 672, "opRating": 327}, {"opponent": "darkrai", "rating": 584, "opRating": 415}, {"opponent": "latias", "rating": 581, "opRating": 418}, {"opponent": "zarude", "rating": 542, "opRating": 457}, {"opponent": "latias_shadow", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "mewtwo", "rating": 281}, {"opponent": "garcho<PERSON>", "rating": 319}, {"opponent": "metagross", "rating": 398}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 36950}, {"moveId": "BUG_BITE", "uses": 39550}], "chargedMoves": [{"moveId": "MIRROR_COAT", "uses": 14551}, {"moveId": "BUG_BUZZ", "uses": 39573}, {"moveId": "BUBBLE_BEAM", "uses": 22427}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 69.9}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 755, "matchups": [{"opponent": "genesect_shock", "rating": 863, "opRating": 136}, {"opponent": "excadrill", "rating": 822}, {"opponent": "metagross", "rating": 741}, {"opponent": "dialga", "rating": 648}, {"opponent": "snorlax", "rating": 543, "opRating": 456}], "counters": [{"opponent": "giratina_origin", "rating": 318}, {"opponent": "gyarados", "rating": 360}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "mewtwo", "rating": 385}, {"opponent": "garcho<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 38367}, {"moveId": "ROCK_SMASH", "uses": 8039}, {"moveId": "POISON_JAB", "uses": 30057}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18544}, {"moveId": "CLOSE_COMBAT", "uses": 44040}, {"moveId": "AERIAL_ACE", "uses": 13900}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "X_SCISSOR"], "score": 69}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 516, "matchups": [{"opponent": "swampert", "rating": 791}, {"opponent": "swampert_shadow", "rating": 769, "opRating": 230}, {"opponent": "florges", "rating": 595, "opRating": 404}, {"opponent": "kyogre", "rating": 561, "opRating": 438}, {"opponent": "sylveon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "zacian_hero", "rating": 312}, {"opponent": "mewtwo", "rating": 341}, {"opponent": "garcho<PERSON>", "rating": 392}, {"opponent": "gyarados", "rating": 461}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 30190}, {"moveId": "BULLET_SEED", "uses": 46310}], "chargedMoves": [{"moveId": "THUNDER", "uses": 14619}, {"moveId": "POWER_WHIP", "uses": 25479}, {"moveId": "MIRROR_SHOT", "uses": 21642}, {"moveId": "FLASH_CANNON", "uses": 11292}, {"moveId": "ACID_SPRAY", "uses": 3525}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "MIRROR_SHOT"], "score": 67.7}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 749, "matchups": [{"opponent": "zacian_hero", "rating": 882}, {"opponent": "gyarados", "rating": 796}, {"opponent": "lugia", "rating": 786}, {"opponent": "metagross", "rating": 707}, {"opponent": "mewtwo", "rating": 515}], "counters": [{"opponent": "excadrill", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "giratina_origin", "rating": 157}, {"opponent": "dialga", "rating": 375}, {"opponent": "dragonite", "rating": 460}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47907}, {"moveId": "CHARGE_BEAM", "uses": 28593}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 6381}, {"moveId": "WILD_CHARGE", "uses": 41024}, {"moveId": "MIRROR_SHOT", "uses": 19026}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLASH_CANNON", "uses": 10172}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 64.3}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 725, "matchups": [{"opponent": "yveltal", "rating": 885, "opRating": 114}, {"opponent": "gyarados", "rating": 863}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 799}, {"opponent": "ho_oh", "rating": 646, "opRating": 353}, {"opponent": "lugia", "rating": 630}], "counters": [{"opponent": "dialga", "rating": 336}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "dragonite", "rating": 377}, {"opponent": "metagross", "rating": 427}, {"opponent": "mewtwo", "rating": 460}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47623}, {"moveId": "CHARGE_BEAM", "uses": 28877}], "chargedMoves": [{"moveId": "ZAP_CANNON", "uses": 5721}, {"moveId": "WILD_CHARGE", "uses": 37218}, {"moveId": "RETURN", "uses": 7888}, {"moveId": "MIRROR_SHOT", "uses": 16869}, {"moveId": "FLASH_CANNON", "uses": 8818}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 64.3}, {"speciesId": "goodra", "speciesName": "<PERSON><PERSON>", "rating": 767, "matchups": [{"opponent": "swampert", "rating": 639}, {"opponent": "grou<PERSON>", "rating": 580}, {"opponent": "giratina_origin", "rating": 572}, {"opponent": "zekrom", "rating": 529, "opRating": 470}, {"opponent": "excadrill", "rating": 513}], "counters": [{"opponent": "dialga", "rating": 328}, {"opponent": "gyarados", "rating": 335}, {"opponent": "lugia", "rating": 347}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "garcho<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 27694}, {"moveId": "DRAGON_BREATH", "uses": 48806}], "chargedMoves": [{"moveId": "SLUDGE_WAVE", "uses": 13007}, {"moveId": "POWER_WHIP", "uses": 22405}, {"moveId": "MUDDY_WATER", "uses": 21359}, {"moveId": "DRACO_METEOR", "uses": 19656}]}, "moveset": ["DRAGON_BREATH", "MUDDY_WATER", "DRACO_METEOR"], "score": 64.2}, {"speciesId": "manectric_shadow", "speciesName": "Man<PERSON><PERSON> (Shadow)", "rating": 499, "matchups": [{"opponent": "metagross", "rating": 684}, {"opponent": "ho_oh", "rating": 665}, {"opponent": "gyarados", "rating": 595}, {"opponent": "zap<PERSON>_shadow", "rating": 592, "opRating": 407}, {"opponent": "lugia", "rating": 519}], "counters": [{"opponent": "zacian_hero", "rating": 158}, {"opponent": "giratina_origin", "rating": 211}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "excadrill", "rating": 293}, {"opponent": "garcho<PERSON>", "rating": 312}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 16890}, {"moveId": "SNARL", "uses": 39221}, {"moveId": "CHARGE_BEAM", "uses": 20345}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35729}, {"moveId": "THUNDER", "uses": 5690}, {"moveId": "PSYCHIC_FANGS", "uses": 19228}, {"moveId": "OVERHEAT", "uses": 12006}, {"moveId": "FRUSTRATION", "uses": 0}, {"moveId": "FLAME_BURST", "uses": 3953}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 61.2}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 501, "matchups": [{"opponent": "suicune_shadow", "rating": 863, "opRating": 136}, {"opponent": "metagross", "rating": 710}, {"opponent": "ho_oh_shadow", "rating": 665, "opRating": 334}, {"opponent": "gyarados", "rating": 646}, {"opponent": "gyarado<PERSON>_shadow", "rating": 595, "opRating": 404}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "mewtwo", "rating": 130}, {"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "zacian_hero", "rating": 260}, {"opponent": "lugia", "rating": 440}], "moves": {"fastMoves": [{"moveId": "THUNDER_FANG", "uses": 17673}, {"moveId": "SNARL", "uses": 38179}, {"moveId": "CHARGE_BEAM", "uses": 20610}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 33046}, {"moveId": "THUNDER", "uses": 5203}, {"moveId": "RETURN", "uses": 6618}, {"moveId": "PSYCHIC_FANGS", "uses": 17212}, {"moveId": "OVERHEAT", "uses": 10769}, {"moveId": "FLAME_BURST", "uses": 3614}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 61.2}]