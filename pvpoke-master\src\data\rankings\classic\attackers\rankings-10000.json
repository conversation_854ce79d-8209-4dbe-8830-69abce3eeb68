[{"speciesId": "lugia", "speciesName": "Lugia", "rating": 613, "matchups": [{"opponent": "grou<PERSON>", "rating": 647}, {"opponent": "dragonite", "rating": 623}, {"opponent": "garcho<PERSON>", "rating": 614}, {"opponent": "palkia", "rating": 590, "opRating": 409}, {"opponent": "swampert", "rating": 542}], "counters": [{"opponent": "mewtwo", "rating": 213}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "dialga", "rating": 355}, {"opponent": "gyarados", "rating": 391}, {"opponent": "zacian_hero", "rating": 450}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42217}, {"moveId": "EXTRASENSORY", "uses": 34283}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 26339}, {"moveId": "AEROBLAST", "uses": 19555}, {"moveId": "FUTURE_SIGHT", "uses": 13644}, {"moveId": "HYDRO_PUMP", "uses": 8800}, {"moveId": "RETURN", "uses": 8239}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 100}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 517, "matchups": [{"opponent": "garcho<PERSON>", "rating": 814}, {"opponent": "buzzwole", "rating": 769, "opRating": 230}, {"opponent": "dragonite", "rating": 758}, {"opponent": "palkia", "rating": 724, "opRating": 275}, {"opponent": "yveltal", "rating": 671, "opRating": 328}], "counters": [{"opponent": "mewtwo", "rating": 291}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "dialga", "rating": 421}, {"opponent": "giratina_origin", "rating": 432}, {"opponent": "gyarados", "rating": 466}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 7529}, {"moveId": "AIR_SLASH", "uses": 6329}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4953}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4865}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4414}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4257}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4208}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4185}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4173}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3895}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3839}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3778}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3736}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3364}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3363}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3324}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3205}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3110}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 21446}, {"moveId": "AERIAL_ACE", "uses": 19695}, {"moveId": "FLAMETHROWER", "uses": 18665}, {"moveId": "DAZZLING_GLEAM", "uses": 16763}]}, "moveset": ["CHARM", "ANCIENT_POWER", "FLAMETHROWER"], "score": 93.9}, {"speciesId": "melmetal", "speciesName": "Melmetal", "rating": 586, "matchups": [{"opponent": "dialga", "rating": 700}, {"opponent": "gyarados", "rating": 645}, {"opponent": "lugia", "rating": 638}, {"opponent": "sylveon", "rating": 606, "opRating": 393}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 551}], "counters": [{"opponent": "giratina_origin", "rating": 171}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "mewtwo", "rating": 252}, {"opponent": "metagross", "rating": 340}, {"opponent": "dragonite", "rating": 343}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 76500}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 26448}, {"moveId": "ROCK_SLIDE", "uses": 21506}, {"moveId": "THUNDERBOLT", "uses": 12332}, {"moveId": "FLASH_CANNON", "uses": 9875}, {"moveId": "HYPER_BEAM", "uses": 6466}]}, "moveset": ["THUNDER_SHOCK", "SUPER_POWER", "ROCK_SLIDE"], "score": 92.8}, {"speciesId": "primarina", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 496, "matchups": [{"opponent": "palkia", "rating": 776, "opRating": 223}, {"opponent": "yveltal", "rating": 761, "opRating": 238}, {"opponent": "dragonite", "rating": 738}, {"opponent": "gyarados", "rating": 590}, {"opponent": "garcho<PERSON>", "rating": 578}], "counters": [{"opponent": "lugia", "rating": 240}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "zacian_hero", "rating": 375}, {"opponent": "dialga", "rating": 421}, {"opponent": "giratina_origin", "rating": 448}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 38535}, {"moveId": "WATERFALL", "uses": 37965}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 37947}, {"moveId": "HYDRO_PUMP", "uses": 19677}, {"moveId": "PSYCHIC", "uses": 18770}]}, "moveset": ["CHARM", "MOONBLAST", "HYDRO_PUMP"], "score": 90.1}, {"speciesId": "florges", "speciesName": "Florges", "rating": 548, "matchups": [{"opponent": "dragonite", "rating": 755}, {"opponent": "yveltal", "rating": 732, "opRating": 267}, {"opponent": "gyarados", "rating": 669}, {"opponent": "zekrom", "rating": 589, "opRating": 410}, {"opponent": "giratina_altered", "rating": 589, "opRating": 410}], "counters": [{"opponent": "giratina_origin", "rating": 318}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "dialga", "rating": 377}, {"opponent": "lugia", "rating": 383}, {"opponent": "garcho<PERSON>", "rating": 495}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 28100}, {"moveId": "VINE_WHIP", "uses": 22295}, {"moveId": "TACKLE", "uses": 15640}, {"moveId": "RAZOR_LEAF", "uses": 10472}], "chargedMoves": [{"moveId": "DISARMING_VOICE", "uses": 34862}, {"moveId": "PSYCHIC", "uses": 15469}, {"moveId": "MOONBLAST", "uses": 14208}, {"moveId": "PETAL_BLIZZARD", "uses": 11982}]}, "moveset": ["FAIRY_WIND", "DISARMING_VOICE", "MOONBLAST"], "score": 89.7}, {"speciesId": "giratina_altered", "speciesName": "Giratina (Altered)", "rating": 656, "matchups": [{"opponent": "genesect", "rating": 715, "opRating": 284}, {"opponent": "genesect_shock", "rating": 641, "opRating": 358}, {"opponent": "buzzwole", "rating": 591, "opRating": 408}, {"opponent": "ho_oh", "rating": 553, "opRating": 446}, {"opponent": "snorlax", "rating": 537, "opRating": 462}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "giratina_origin", "rating": 318}, {"opponent": "mewtwo", "rating": 335}, {"opponent": "gyarados", "rating": 358}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 42663}, {"moveId": "DRAGON_BREATH", "uses": 33837}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 38975}, {"moveId": "ANCIENT_POWER", "uses": 18959}, {"moveId": "SHADOW_SNEAK", "uses": 18523}]}, "moveset": ["SHADOW_CLAW", "DRAGON_CLAW", "ANCIENT_POWER"], "score": 88.2}, {"speciesId": "dragonite", "speciesName": "Dragonite", "rating": 594, "matchups": [{"opponent": "swampert", "rating": 750}, {"opponent": "grou<PERSON>", "rating": 686}, {"opponent": "snorlax", "rating": 590, "opRating": 409}, {"opponent": "giratina_origin", "rating": 574}, {"opponent": "buzzwole", "rating": 550, "opRating": 449}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "metagross", "rating": 436}, {"opponent": "gyarados", "rating": 476}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33069}, {"moveId": "DRAGON_BREATH", "uses": 32486}, {"moveId": "STEEL_WING", "uses": 11026}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 23275}, {"moveId": "SUPER_POWER", "uses": 19274}, {"moveId": "HURRICANE", "uses": 10880}, {"moveId": "OUTRAGE", "uses": 6397}, {"moveId": "RETURN", "uses": 6269}, {"moveId": "DRAGON_PULSE", "uses": 4341}, {"moveId": "DRACO_METEOR", "uses": 3769}, {"moveId": "HYPER_BEAM", "uses": 2454}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 87.8}, {"speciesId": "lugia_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 564, "matchups": [{"opponent": "grou<PERSON>", "rating": 590}, {"opponent": "swampert", "rating": 590}, {"opponent": "garcho<PERSON>", "rating": 545}, {"opponent": "palkia", "rating": 538, "opRating": 461}, {"opponent": "dragonite", "rating": 519}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "mewtwo", "rating": 260}, {"opponent": "gyarados", "rating": 286}, {"opponent": "giratina_origin", "rating": 306}, {"opponent": "lugia", "rating": 478}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42586}, {"moveId": "EXTRASENSORY", "uses": 33914}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 29435}, {"moveId": "AEROBLAST", "uses": 21932}, {"moveId": "FUTURE_SIGHT", "uses": 15160}, {"moveId": "HYDRO_PUMP", "uses": 9792}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SKY_ATTACK", "AEROBLAST"], "score": 86.6}, {"speciesId": "sylveon", "speciesName": "Sylveon", "rating": 518, "matchups": [{"opponent": "dragonite", "rating": 773}, {"opponent": "yveltal", "rating": 739, "opRating": 260}, {"opponent": "palkia", "rating": 739, "opRating": 260}, {"opponent": "goodra", "rating": 706, "opRating": 293}, {"opponent": "zekrom", "rating": 585, "opRating": 414}], "counters": [{"opponent": "mewtwo", "rating": 255}, {"opponent": "dialga", "rating": 391}, {"opponent": "giratina_origin", "rating": 396}, {"opponent": "gyarados", "rating": 435}, {"opponent": "garcho<PERSON>", "rating": 446}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39766}, {"moveId": "CHARM", "uses": 36734}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 27114}, {"moveId": "PSYSHOCK", "uses": 18291}, {"moveId": "LAST_RESORT", "uses": 13209}, {"moveId": "DRAINING_KISS", "uses": 11000}, {"moveId": "DAZZLING_GLEAM", "uses": 6884}]}, "moveset": ["CHARM", "MOONBLAST", "PSYSHOCK"], "score": 86.4}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 627, "matchups": [{"opponent": "genesect", "rating": 780, "opRating": 219}, {"opponent": "genesect_shock", "rating": 709, "opRating": 290}, {"opponent": "buzzwole", "rating": 619, "opRating": 380}, {"opponent": "metagross", "rating": 607}, {"opponent": "excadrill", "rating": 569}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "lugia", "rating": 288}, {"opponent": "zacian_hero", "rating": 421}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 43394}, {"moveId": "DRAGON_TAIL", "uses": 33106}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 36703}, {"moveId": "DRAGON_PULSE", "uses": 21114}, {"moveId": "OMINOUS_WIND", "uses": 18678}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "OMINOUS_WIND"], "score": 85.5}, {"speciesId": "dialga", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 584, "matchups": [{"opponent": "nihilego", "rating": 627, "opRating": 372}, {"opponent": "latios", "rating": 605, "opRating": 394}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 603, "opRating": 396}, {"opponent": "meloetta_aria", "rating": 521, "opRating": 478}, {"opponent": "lugia", "rating": 508}], "counters": [{"opponent": "mewtwo", "rating": 328}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "dragonite", "rating": 356}, {"opponent": "gyarados", "rating": 402}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52387}, {"moveId": "METAL_CLAW", "uses": 24113}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 28858}, {"moveId": "DRACO_METEOR", "uses": 25275}, {"moveId": "THUNDER", "uses": 22481}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 85.3}, {"speciesId": "zarude", "speciesName": "Zarude", "rating": 504, "matchups": [{"opponent": "swampert", "rating": 759}, {"opponent": "mewtwo", "rating": 668}, {"opponent": "grou<PERSON>", "rating": 600}, {"opponent": "gyarados", "rating": 576}, {"opponent": "giratina_origin", "rating": 572}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "lugia", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 359}, {"opponent": "metagross", "rating": 433}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 49578}, {"moveId": "BITE", "uses": 26922}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37162}, {"moveId": "POWER_WHIP", "uses": 29002}, {"moveId": "ENERGY_BALL", "uses": 10565}]}, "moveset": ["VINE_WHIP", "DARK_PULSE", "POWER_WHIP"], "score": 85.3}, {"speciesId": "buzzwole", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 597, "matchups": [{"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 750, "opRating": 250}, {"opponent": "snorlax", "rating": 657, "opRating": 342}, {"opponent": "excadrill", "rating": 652}, {"opponent": "yveltal", "rating": 634, "opRating": 365}, {"opponent": "swampert", "rating": 631}], "counters": [{"opponent": "giratina_origin", "rating": 250}, {"opponent": "dialga", "rating": 328}, {"opponent": "garcho<PERSON>", "rating": 436}, {"opponent": "grou<PERSON>", "rating": 456}, {"opponent": "metagross", "rating": 488}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45482}, {"moveId": "POISON_JAB", "uses": 31018}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34372}, {"moveId": "LUNGE", "uses": 25690}, {"moveId": "FELL_STINGER", "uses": 8179}, {"moveId": "POWER_UP_PUNCH", "uses": 8164}]}, "moveset": ["COUNTER", "SUPER_POWER", "LUNGE"], "score": 84.3}, {"speciesId": "gyarado<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 481, "matchups": [{"opponent": "grou<PERSON>", "rating": 731}, {"opponent": "entei", "rating": 716, "opRating": 283}, {"opponent": "swampert", "rating": 664}, {"opponent": "metagross", "rating": 613}, {"opponent": "garcho<PERSON>", "rating": 572}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "dialga", "rating": 241}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "zacian_hero", "rating": 352}, {"opponent": "gyarados", "rating": 458}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22508}, {"moveId": "DRAGON_TAIL", "uses": 22385}, {"moveId": "WATERFALL", "uses": 21107}, {"moveId": "BITE", "uses": 10452}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 28829}, {"moveId": "CRUNCH", "uses": 20157}, {"moveId": "OUTRAGE", "uses": 12236}, {"moveId": "TWISTER", "uses": 6539}, {"moveId": "HYDRO_PUMP", "uses": 4661}, {"moveId": "DRAGON_PULSE", "uses": 3985}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 83.6}, {"speciesId": "meloetta_aria", "speciesName": "<PERSON><PERSON><PERSON> (Aria)", "rating": 587, "matchups": [{"opponent": "zap<PERSON>_galarian", "rating": 681, "opRating": 318}, {"opponent": "giratina_origin", "rating": 606}, {"opponent": "goodra", "rating": 587, "opRating": 412}, {"opponent": "mewtwo_armored", "rating": 577, "opRating": 422}, {"opponent": "giratina_altered", "rating": 507, "opRating": 492}], "counters": [{"opponent": "lugia", "rating": 285}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "dragonite", "rating": 343}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "mewtwo", "rating": 489}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 41572}, {"moveId": "CONFUSION", "uses": 34928}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30115}, {"moveId": "THUNDERBOLT", "uses": 19611}, {"moveId": "HYPER_BEAM", "uses": 13753}, {"moveId": "DAZZLING_GLEAM", "uses": 13213}]}, "moveset": ["QUICK_ATTACK", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 83.2}, {"speciesId": "dragonite_shadow", "speciesName": "Dragonite (Shadow)", "rating": 559, "matchups": [{"opponent": "grou<PERSON>", "rating": 744}, {"opponent": "swampert", "rating": 675}, {"opponent": "kyogre", "rating": 651, "opRating": 348}, {"opponent": "buzzwole", "rating": 651, "opRating": 348}, {"opponent": "snorlax", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 233}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "gyarados", "rating": 327}, {"opponent": "mewtwo", "rating": 330}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33429}, {"moveId": "DRAGON_BREATH", "uses": 32970}, {"moveId": "STEEL_WING", "uses": 10153}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 24285}, {"moveId": "SUPER_POWER", "uses": 20027}, {"moveId": "HURRICANE", "uses": 11509}, {"moveId": "OUTRAGE", "uses": 6823}, {"moveId": "HYPER_BEAM", "uses": 5137}, {"moveId": "DRAGON_PULSE", "uses": 4487}, {"moveId": "DRACO_METEOR", "uses": 4067}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "SUPER_POWER"], "score": 80.7}, {"speciesId": "grou<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 592, "matchups": [{"opponent": "melmetal", "rating": 801, "opRating": 198}, {"opponent": "nihilego", "rating": 760, "opRating": 239}, {"opponent": "zekrom", "rating": 578}, {"opponent": "metagross", "rating": 565}, {"opponent": "excadrill", "rating": 529}], "counters": [{"opponent": "giratina_origin", "rating": 177}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "dialga", "rating": 263}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "garcho<PERSON>", "rating": 417}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43373}, {"moveId": "DRAGON_TAIL", "uses": 33127}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 29656}, {"moveId": "FIRE_PUNCH", "uses": 28456}, {"moveId": "SOLAR_BEAM", "uses": 12760}, {"moveId": "FIRE_BLAST", "uses": 5745}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "FIRE_PUNCH"], "score": 80.7}, {"speciesId": "articuno", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 480, "matchups": [{"opponent": "celebi", "rating": 680, "opRating": 319}, {"opponent": "garcho<PERSON>", "rating": 618}, {"opponent": "kommo_o", "rating": 599, "opRating": 400}, {"opponent": "virizion", "rating": 580, "opRating": 419}, {"opponent": "haxorus", "rating": 548, "opRating": 451}], "counters": [{"opponent": "dialga", "rating": 250}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "lugia", "rating": 364}, {"opponent": "giratina_origin", "rating": 370}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 45758}, {"moveId": "FROST_BREATH", "uses": 30742}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 28499}, {"moveId": "ANCIENT_POWER", "uses": 13979}, {"moveId": "HURRICANE", "uses": 11579}, {"moveId": "ICE_BEAM", "uses": 9187}, {"moveId": "RETURN", "uses": 6865}, {"moveId": "BLIZZARD", "uses": 6415}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 80.5}, {"speciesId": "palkia", "speciesName": "Pa<PERSON><PERSON>", "rating": 601, "matchups": [{"opponent": "entei", "rating": 856, "opRating": 143}, {"opponent": "vaporeon", "rating": 687, "opRating": 312}, {"opponent": "heatran", "rating": 625, "opRating": 374}, {"opponent": "swampert", "rating": 549}, {"opponent": "snorlax", "rating": 549, "opRating": 450}], "counters": [{"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "dialga", "rating": 230}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "metagross", "rating": 375}, {"opponent": "giratina_origin", "rating": 456}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 38411}, {"moveId": "DRAGON_BREATH", "uses": 38089}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 41133}, {"moveId": "DRACO_METEOR", "uses": 18160}, {"moveId": "FIRE_BLAST", "uses": 10859}, {"moveId": "HYDRO_PUMP", "uses": 6418}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "DRACO_METEOR"], "score": 80.5}, {"speciesId": "zacian_hero", "speciesName": "<PERSON><PERSON><PERSON> (Hero)", "rating": 552, "matchups": [{"opponent": "goodra", "rating": 676, "opRating": 323}, {"opponent": "yveltal", "rating": 670, "opRating": 329}, {"opponent": "dragonite", "rating": 624}, {"opponent": "palkia", "rating": 589, "opRating": 410}, {"opponent": "excadrill", "rating": 508}], "counters": [{"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "lugia", "rating": 292}, {"opponent": "swampert", "rating": 383}, {"opponent": "dialga", "rating": 461}, {"opponent": "gyarados", "rating": 476}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27401}, {"moveId": "QUICK_ATTACK", "uses": 26582}, {"moveId": "FIRE_FANG", "uses": 12404}, {"moveId": "METAL_CLAW", "uses": 10126}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27796}, {"moveId": "WILD_CHARGE", "uses": 27135}, {"moveId": "PLAY_ROUGH", "uses": 12798}, {"moveId": "IRON_HEAD", "uses": 8944}]}, "moveset": ["QUICK_ATTACK", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 80.5}, {"speciesId": "cobalion", "speciesName": "Cobalion", "rating": 569, "matchups": [{"opponent": "dialga", "rating": 659}, {"opponent": "snorlax", "rating": 651, "opRating": 348}, {"opponent": "melmetal", "rating": 640, "opRating": 359}, {"opponent": "goodra", "rating": 627, "opRating": 372}, {"opponent": "nihilego", "rating": 627, "opRating": 372}], "counters": [{"opponent": "lugia", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "metagross", "rating": 241}, {"opponent": "gyarados", "rating": 435}, {"opponent": "excadrill", "rating": 462}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 50473}, {"moveId": "METAL_CLAW", "uses": 21814}, {"moveId": "ZEN_HEADBUTT", "uses": 4181}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 26282}, {"moveId": "CLOSE_COMBAT", "uses": 24110}, {"moveId": "STONE_EDGE", "uses": 14759}, {"moveId": "IRON_HEAD", "uses": 11226}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "STONE_EDGE"], "score": 80.3}, {"speciesId": "gyarados", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 518, "matchups": [{"opponent": "grou<PERSON>", "rating": 664}, {"opponent": "swampert", "rating": 572}, {"opponent": "buzzwole", "rating": 541, "opRating": 458}, {"opponent": "ho_oh", "rating": 538, "opRating": 461}, {"opponent": "garcho<PERSON>", "rating": 530}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "zacian_hero", "rating": 280}, {"opponent": "giratina_origin", "rating": 374}, {"opponent": "metagross", "rating": 409}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 22380}, {"moveId": "DRAGON_TAIL", "uses": 21804}, {"moveId": "WATERFALL", "uses": 20970}, {"moveId": "BITE", "uses": 11355}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 26284}, {"moveId": "CRUNCH", "uses": 18449}, {"moveId": "OUTRAGE", "uses": 11089}, {"moveId": "RETURN", "uses": 6834}, {"moveId": "TWISTER", "uses": 5976}, {"moveId": "HYDRO_PUMP", "uses": 4156}, {"moveId": "DRAGON_PULSE", "uses": 3733}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "CRUNCH"], "score": 80.3}, {"speciesId": "avalugg", "speciesName": "Avalugg", "rating": 494, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 783, "opRating": 216}, {"opponent": "garcho<PERSON>", "rating": 659}, {"opponent": "goodra", "rating": 644, "opRating": 355}, {"opponent": "gyarados", "rating": 536}, {"opponent": "dragonite", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "giratina_origin", "rating": 308}, {"opponent": "lugia", "rating": 476}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 497}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 52569}, {"moveId": "BITE", "uses": 23931}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 31702}, {"moveId": "BODY_SLAM", "uses": 17222}, {"moveId": "CRUNCH", "uses": 14426}, {"moveId": "EARTHQUAKE", "uses": 10125}, {"moveId": "MIRROR_COAT", "uses": 3079}]}, "moveset": ["ICE_FANG", "AVALANCHE", "BODY_SLAM"], "score": 78.8}, {"speciesId": "goodra", "speciesName": "<PERSON><PERSON>", "rating": 594, "matchups": [{"opponent": "magnezone", "rating": 639, "opRating": 360}, {"opponent": "mew", "rating": 602, "opRating": 397}, {"opponent": "regirock", "rating": 594, "opRating": 405}, {"opponent": "snor<PERSON>_shadow", "rating": 575, "opRating": 424}, {"opponent": "virizion", "rating": 516, "opRating": 483}], "counters": [{"opponent": "mewtwo", "rating": 221}, {"opponent": "dialga", "rating": 228}, {"opponent": "gyarados", "rating": 324}, {"opponent": "giratina_origin", "rating": 370}, {"opponent": "garcho<PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48345}, {"moveId": "WATER_GUN", "uses": 28155}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22417}, {"moveId": "MUDDY_WATER", "uses": 21314}, {"moveId": "DRACO_METEOR", "uses": 19691}, {"moveId": "SLUDGE_WAVE", "uses": 13018}]}, "moveset": ["DRAGON_BREATH", "MUDDY_WATER", "DRACO_METEOR"], "score": 78.8}, {"speciesId": "hydreigon", "speciesName": "Hydreigon", "rating": 532, "matchups": [{"opponent": "giratina_origin", "rating": 640}, {"opponent": "mewtwo", "rating": 592}, {"opponent": "giratina_altered", "rating": 563, "opRating": 436}, {"opponent": "ho_oh", "rating": 531, "opRating": 468}, {"opponent": "swampert", "rating": 510}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "lugia", "rating": 195}, {"opponent": "garcho<PERSON>", "rating": 230}, {"opponent": "dragonite", "rating": 260}, {"opponent": "gyarados", "rating": 324}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 50572}, {"moveId": "BITE", "uses": 25928}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 40290}, {"moveId": "DRAGON_PULSE", "uses": 15076}, {"moveId": "DARK_PULSE", "uses": 12392}, {"moveId": "FLASH_CANNON", "uses": 8710}]}, "moveset": ["DRAGON_BREATH", "BRUTAL_SWING", "FLASH_CANNON"], "score": 78.8}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 553, "matchups": [{"opponent": "mew", "rating": 754, "opRating": 245}, {"opponent": "gardevoir_shadow", "rating": 673, "opRating": 326}, {"opponent": "giratina_origin", "rating": 662}, {"opponent": "excadrill", "rating": 577, "opRating": 422}, {"opponent": "cresselia", "rating": 576, "opRating": 423}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "zacian_hero", "rating": 317}, {"opponent": "lugia", "rating": 335}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 65449}, {"moveId": "ZEN_HEADBUTT", "uses": 9129}, {"moveId": "YAWN", "uses": 1994}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24683}, {"moveId": "SUPER_POWER", "uses": 16251}, {"moveId": "OUTRAGE", "uses": 9113}, {"moveId": "EARTHQUAKE", "uses": 8984}, {"moveId": "HEAVY_SLAM", "uses": 6825}, {"moveId": "SKULL_BASH", "uses": 4252}, {"moveId": "RETURN", "uses": 3603}, {"moveId": "HYPER_BEAM", "uses": 2778}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 78.8}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 527, "matchups": [{"opponent": "gardevoir", "rating": 673, "opRating": 326}, {"opponent": "gardevoir_shadow", "rating": 653, "opRating": 346}, {"opponent": "giratina_origin", "rating": 626}, {"opponent": "metagross", "rating": 525}, {"opponent": "excadrill", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "garcho<PERSON>", "rating": 309}, {"opponent": "zacian_hero", "rating": 349}, {"opponent": "gyarados", "rating": 384}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66583}, {"moveId": "ZEN_HEADBUTT", "uses": 8517}, {"moveId": "YAWN", "uses": 1426}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26072}, {"moveId": "SUPER_POWER", "uses": 16914}, {"moveId": "OUTRAGE", "uses": 9583}, {"moveId": "EARTHQUAKE", "uses": 9355}, {"moveId": "HEAVY_SLAM", "uses": 7190}, {"moveId": "SKULL_BASH", "uses": 4464}, {"moveId": "HYPER_BEAM", "uses": 2962}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 78.6}, {"speciesId": "x<PERSON><PERSON>", "speciesName": "Xerneas", "rating": 492, "matchups": [{"opponent": "goodra", "rating": 641, "opRating": 358}, {"opponent": "palkia", "rating": 584, "opRating": 415}, {"opponent": "yveltal", "rating": 577, "opRating": 422}, {"opponent": "dragonite", "rating": 573}, {"opponent": "dialga", "rating": 559}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "lugia", "rating": 369}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 68414}, {"moveId": "ZEN_HEADBUTT", "uses": 8086}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 27211}, {"moveId": "MOONBLAST", "uses": 19172}, {"moveId": "MEGAHORN", "uses": 14020}, {"moveId": "THUNDER", "uses": 10854}, {"moveId": "GIGA_IMPACT", "uses": 5376}]}, "moveset": ["TACKLE", "CLOSE_COMBAT", "MOONBLAST"], "score": 78.6}, {"speciesId": "kyogre", "speciesName": "Kyogre", "rating": 551, "matchups": [{"opponent": "ho_oh", "rating": 581, "opRating": 418}, {"opponent": "metagross", "rating": 576}, {"opponent": "swampert", "rating": 554}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 540}, {"opponent": "excadrill", "rating": 529}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "gyarados", "rating": 234}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "garcho<PERSON>", "rating": 276}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 76500}], "chargedMoves": [{"moveId": "SURF", "uses": 37730}, {"moveId": "BLIZZARD", "uses": 17463}, {"moveId": "THUNDER", "uses": 15183}, {"moveId": "HYDRO_PUMP", "uses": 6017}]}, "moveset": ["WATERFALL", "SURF", "BLIZZARD"], "score": 78.2}, {"speciesId": "virizion", "speciesName": "Virizion", "rating": 525, "matchups": [{"opponent": "melmetal", "rating": 760, "opRating": 239}, {"opponent": "swampert", "rating": 755}, {"opponent": "excadrill", "rating": 702}, {"opponent": "snorlax", "rating": 622, "opRating": 377}, {"opponent": "grou<PERSON>", "rating": 569}], "counters": [{"opponent": "zacian_hero", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 279}, {"opponent": "dialga", "rating": 380}, {"opponent": "metagross", "rating": 392}, {"opponent": "gyarados", "rating": 451}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 40948}, {"moveId": "QUICK_ATTACK", "uses": 32658}, {"moveId": "ZEN_HEADBUTT", "uses": 2846}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26276}, {"moveId": "SACRED_SWORD", "uses": 20033}, {"moveId": "CLOSE_COMBAT", "uses": 18416}, {"moveId": "STONE_EDGE", "uses": 11871}]}, "moveset": ["DOUBLE_KICK", "LEAF_BLADE", "SACRED_SWORD"], "score": 78.2}, {"speciesId": "yveltal", "speciesName": "Y<PERSON><PERSON>", "rating": 499, "matchups": [{"opponent": "mewtwo", "rating": 698}, {"opponent": "snorlax", "rating": 630, "opRating": 369}, {"opponent": "grou<PERSON>", "rating": 568}, {"opponent": "giratina_origin", "rating": 566}, {"opponent": "metagross", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "lugia", "rating": 311}, {"opponent": "gyarados", "rating": 371}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27766}, {"moveId": "GUST", "uses": 25693}, {"moveId": "SUCKER_PUNCH", "uses": 22994}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27443}, {"moveId": "HURRICANE", "uses": 15682}, {"moveId": "FOCUS_BLAST", "uses": 13287}, {"moveId": "PSYCHIC", "uses": 12872}, {"moveId": "HYPER_BEAM", "uses": 7256}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 78.2}, {"speciesId": "mew", "speciesName": "Mew", "rating": 545, "matchups": [{"opponent": "primarina", "rating": 721, "opRating": 278}, {"opponent": "gyarados", "rating": 624}, {"opponent": "mewtwo_armored", "rating": 611, "opRating": 388}, {"opponent": "cobalion", "rating": 577, "opRating": 422}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 184}, {"opponent": "garcho<PERSON>", "rating": 251}, {"opponent": "zacian_hero", "rating": 335}, {"opponent": "metagross", "rating": 424}, {"opponent": "mewtwo", "rating": 427}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 12115}, {"moveId": "VOLT_SWITCH", "uses": 10800}, {"moveId": "SNARL", "uses": 9102}, {"moveId": "POISON_JAB", "uses": 7737}, {"moveId": "DRAGON_TAIL", "uses": 7188}, {"moveId": "INFESTATION", "uses": 7082}, {"moveId": "CHARGE_BEAM", "uses": 4677}, {"moveId": "WATERFALL", "uses": 4602}, {"moveId": "FROST_BREATH", "uses": 4372}, {"moveId": "STEEL_WING", "uses": 2892}, {"moveId": "STRUGGLE_BUG", "uses": 2582}, {"moveId": "ROCK_SMASH", "uses": 1998}, {"moveId": "CUT", "uses": 1036}, {"moveId": "POUND", "uses": 101}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 8458}, {"moveId": "DRAGON_CLAW", "uses": 5894}, {"moveId": "SURF", "uses": 5601}, {"moveId": "ROCK_SLIDE", "uses": 5279}, {"moveId": "PSYSHOCK", "uses": 4896}, {"moveId": "ICE_BEAM", "uses": 4722}, {"moveId": "FLAME_CHARGE", "uses": 4340}, {"moveId": "DARK_PULSE", "uses": 4288}, {"moveId": "GRASS_KNOT", "uses": 4175}, {"moveId": "FOCUS_BLAST", "uses": 2849}, {"moveId": "LOW_SWEEP", "uses": 2582}, {"moveId": "BULLDOZE", "uses": 2292}, {"moveId": "STONE_EDGE", "uses": 2144}, {"moveId": "DAZZLING_GLEAM", "uses": 2021}, {"moveId": "PSYCHIC", "uses": 1889}, {"moveId": "ANCIENT_POWER", "uses": 1823}, {"moveId": "OVERHEAT", "uses": 1716}, {"moveId": "BLIZZARD", "uses": 1668}, {"moveId": "GYRO_BALL", "uses": 1529}, {"moveId": "ENERGY_BALL", "uses": 1481}, {"moveId": "FLASH_CANNON", "uses": 1468}, {"moveId": "THUNDERBOLT", "uses": 1468}, {"moveId": "HYPER_BEAM", "uses": 1416}, {"moveId": "THUNDER", "uses": 1360}, {"moveId": "SOLAR_BEAM", "uses": 815}]}, "moveset": ["SHADOW_CLAW", "SURF", "WILD_CHARGE"], "score": 78}, {"speciesId": "gardevoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 467, "matchups": [{"opponent": "hydreigon", "rating": 922, "opRating": 77}, {"opponent": "kommo_o", "rating": 853, "opRating": 146}, {"opponent": "dragonite", "rating": 788}, {"opponent": "palkia", "rating": 649, "opRating": 350}, {"opponent": "yveltal", "rating": 555, "opRating": 444}], "counters": [{"opponent": "mewtwo", "rating": 260}, {"opponent": "dialga", "rating": 277}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "gyarados", "rating": 391}, {"opponent": "garcho<PERSON>", "rating": 492}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31164}, {"moveId": "CHARM", "uses": 26855}, {"moveId": "CHARGE_BEAM", "uses": 18518}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26133}, {"moveId": "SYNCHRONOISE", "uses": 23217}, {"moveId": "DAZZLING_GLEAM", "uses": 16484}, {"moveId": "PSYCHIC", "uses": 10569}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 77.8}, {"speciesId": "genesect_chill", "speciesName": "Genesect (Chill)", "rating": 541, "matchups": [{"opponent": "goodra", "rating": 655, "opRating": 344}, {"opponent": "mewtwo_armored", "rating": 651, "opRating": 348}, {"opponent": "meloetta_aria", "rating": 645, "opRating": 354}, {"opponent": "garcho<PERSON>", "rating": 560}, {"opponent": "dragonite", "rating": 544}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "metagross", "rating": 386}, {"opponent": "mewtwo", "rating": 411}, {"opponent": "gyarados", "rating": 420}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 52035}, {"moveId": "METAL_CLAW", "uses": 24465}], "chargedMoves": [{"moveId": "TECHNO_BLAST_CHILL", "uses": 26662}, {"moveId": "X_SCISSOR", "uses": 22699}, {"moveId": "MAGNET_BOMB", "uses": 19738}, {"moveId": "ICE_BEAM", "uses": 7544}]}, "moveset": ["FURY_CUTTER", "TECHNO_BLAST_CHILL", "X_SCISSOR"], "score": 76.7}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "sylveon", "rating": 716}, {"opponent": "grou<PERSON>", "rating": 692}, {"opponent": "zacian_hero", "rating": 604}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 549}, {"opponent": "metagross", "rating": 541}], "counters": [{"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "dialga", "rating": 154}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "excadrill", "rating": 318}, {"opponent": "lugia", "rating": 359}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 16281}, {"moveId": "EXTRASENSORY", "uses": 5295}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4048}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4047}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3943}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3688}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3481}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3431}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3414}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3223}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3153}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3081}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3016}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2945}, {"moveId": "STEEL_WING", "uses": 2858}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2792}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2687}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2606}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2472}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 29067}, {"moveId": "SACRED_FIRE", "uses": 18811}, {"moveId": "EARTHQUAKE", "uses": 11936}, {"moveId": "RETURN", "uses": 6433}, {"moveId": "SOLAR_BEAM", "uses": 6365}, {"moveId": "FIRE_BLAST", "uses": 3749}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 75.7}, {"speciesId": "nihilego", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 569, "matchups": [{"opponent": "ho_oh", "rating": 726, "opRating": 273}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 712}, {"opponent": "sylveon", "rating": 665, "opRating": 334}, {"opponent": "lugia", "rating": 558}, {"opponent": "zacian_hero", "rating": 544}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "dragonite", "rating": 244}, {"opponent": "gyarados", "rating": 270}, {"opponent": "giratina_origin", "rating": 284}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 55092}, {"moveId": "ACID", "uses": 18459}, {"moveId": "POUND", "uses": 3035}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 41283}, {"moveId": "SLUDGE_BOMB", "uses": 22023}, {"moveId": "POWER_GEM", "uses": 7511}, {"moveId": "GUNK_SHOT", "uses": 5709}]}, "moveset": ["POISON_JAB", "ROCK_SLIDE", "SLUDGE_BOMB"], "score": 75.5}, {"speciesId": "zekrom", "speciesName": "Zekrom", "rating": 563, "matchups": [{"opponent": "magnezone", "rating": 755, "opRating": 244}, {"opponent": "mew", "rating": 690, "opRating": 309}, {"opponent": "ho_oh", "rating": 657, "opRating": 342}, {"opponent": "mewtwo_armored", "rating": 622, "opRating": 377}, {"opponent": "snorlax", "rating": 586, "opRating": 413}], "counters": [{"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "dialga", "rating": 274}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "gyarados", "rating": 350}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 46756}, {"moveId": "CHARGE_BEAM", "uses": 29744}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34903}, {"moveId": "CRUNCH", "uses": 18868}, {"moveId": "OUTRAGE", "uses": 16900}, {"moveId": "FLASH_CANNON", "uses": 5850}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "WILD_CHARGE"], "score": 75.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 539, "matchups": [{"opponent": "nihilego", "rating": 689, "opRating": 310}, {"opponent": "sylveon", "rating": 671, "opRating": 328}, {"opponent": "meloetta_aria", "rating": 564, "opRating": 435}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 527}, {"opponent": "zacian_hero", "rating": 507}], "counters": [{"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "dragonite", "rating": 263}, {"opponent": "dialga", "rating": 317}, {"opponent": "lugia", "rating": 354}, {"opponent": "gyarados", "rating": 360}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 46800}, {"moveId": "CHARGE_BEAM", "uses": 29700}], "chargedMoves": [{"moveId": "DOOM_DESIRE", "uses": 45676}, {"moveId": "PSYCHIC", "uses": 20195}, {"moveId": "DAZZLING_GLEAM", "uses": 10553}]}, "moveset": ["CONFUSION", "DOOM_DESIRE", "PSYCHIC"], "score": 75.1}, {"speciesId": "zap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 505, "matchups": [{"opponent": "virizion", "rating": 803, "opRating": 196}, {"opponent": "buzzwole", "rating": 701, "opRating": 298}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 612}, {"opponent": "yveltal", "rating": 599, "opRating": 400}, {"opponent": "gyarados", "rating": 594}], "counters": [{"opponent": "dialga", "rating": 209}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "mewtwo", "rating": 312}, {"opponent": "lugia", "rating": 392}, {"opponent": "metagross", "rating": 436}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50242}, {"moveId": "CHARGE_BEAM", "uses": 26258}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 33026}, {"moveId": "THUNDERBOLT", "uses": 15597}, {"moveId": "ANCIENT_POWER", "uses": 14479}, {"moveId": "THUNDER", "uses": 6915}, {"moveId": "ZAP_CANNON", "uses": 6441}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 75.1}, {"speciesId": "mewtwo", "speciesName": "Mewtwo", "rating": 529, "matchups": [{"opponent": "terrakion", "rating": 677, "opRating": 322}, {"opponent": "kommo_o", "rating": 640, "opRating": 359}, {"opponent": "buzzwole", "rating": 593, "opRating": 406}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 593, "opRating": 406}, {"opponent": "nihilego", "rating": 585, "opRating": 414}], "counters": [{"opponent": "dialga", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 366}, {"opponent": "zacian_hero", "rating": 372}, {"opponent": "dragonite", "rating": 406}, {"opponent": "gyarados", "rating": 412}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 46903}, {"moveId": "CONFUSION", "uses": 29597}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19038}, {"moveId": "SHADOW_BALL", "uses": 11491}, {"moveId": "ICE_BEAM", "uses": 11099}, {"moveId": "FLAMETHROWER", "uses": 8440}, {"moveId": "THUNDERBOLT", "uses": 8119}, {"moveId": "FOCUS_BLAST", "uses": 7342}, {"moveId": "RETURN", "uses": 4637}, {"moveId": "PSYCHIC", "uses": 4399}, {"moveId": "HYPER_BEAM", "uses": 1742}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 74.8}, {"speciesId": "zapdos", "speciesName": "Zapdos", "rating": 527, "matchups": [{"opponent": "milotic", "rating": 782, "opRating": 217}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 677}, {"opponent": "gyarados", "rating": 618}, {"opponent": "buzzwole", "rating": 569, "opRating": 430}, {"opponent": "kyogre", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 160}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "metagross", "rating": 351}, {"opponent": "lugia", "rating": 376}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 49317}, {"moveId": "CHARGE_BEAM", "uses": 27183}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 29500}, {"moveId": "THUNDERBOLT", "uses": 14286}, {"moveId": "ANCIENT_POWER", "uses": 13142}, {"moveId": "RETURN", "uses": 7340}, {"moveId": "THUNDER", "uses": 6270}, {"moveId": "ZAP_CANNON", "uses": 6004}]}, "moveset": ["THUNDER_SHOCK", "DRILL_PECK", "THUNDERBOLT"], "score": 74.6}, {"speciesId": "reshiram", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 567, "matchups": [{"opponent": "genesect_burn", "rating": 690, "opRating": 309}, {"opponent": "heatran", "rating": 657, "opRating": 342}, {"opponent": "mewtwo_armored", "rating": 622, "opRating": 377}, {"opponent": "mew", "rating": 603, "opRating": 396}, {"opponent": "snorlax", "rating": 581, "opRating": 418}], "counters": [{"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "dialga", "rating": 274}, {"opponent": "gyarados", "rating": 324}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 48302}, {"moveId": "FIRE_FANG", "uses": 28198}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24023}, {"moveId": "OVERHEAT", "uses": 19656}, {"moveId": "STONE_EDGE", "uses": 19288}, {"moveId": "DRACO_METEOR", "uses": 13363}]}, "moveset": ["DRAGON_BREATH", "CRUNCH", "OVERHEAT"], "score": 74.4}, {"speciesId": "gardevoir", "speciesName": "Gardevoir", "rating": 456, "matchups": [{"opponent": "dragonite", "rating": 782}, {"opponent": "palkia", "rating": 685, "opRating": 314}, {"opponent": "yveltal", "rating": 613, "opRating": 386}, {"opponent": "garcho<PERSON>", "rating": 529}, {"opponent": "zekrom", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 208}, {"opponent": "dialga", "rating": 228}, {"opponent": "giratina_origin", "rating": 249}, {"opponent": "zacian_hero", "rating": 338}, {"opponent": "gyarados", "rating": 373}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30326}, {"moveId": "CHARM", "uses": 27730}, {"moveId": "CHARGE_BEAM", "uses": 18494}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 23029}, {"moveId": "SYNCHRONOISE", "uses": 20245}, {"moveId": "DAZZLING_GLEAM", "uses": 14321}, {"moveId": "RETURN", "uses": 9641}, {"moveId": "PSYCHIC", "uses": 9173}]}, "moveset": ["CHARM", "SHADOW_BALL", "SYNCHRONOISE"], "score": 74.2}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 507, "matchups": [{"opponent": "nihilego", "rating": 713, "opRating": 286}, {"opponent": "metagross", "rating": 676}, {"opponent": "melmetal", "rating": 621, "opRating": 378}, {"opponent": "ho_oh", "rating": 512, "opRating": 487}, {"opponent": "zekrom", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "garcho<PERSON>", "rating": 244}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "zacian_hero", "rating": 263}, {"opponent": "giratina_origin", "rating": 280}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 46701}, {"moveId": "WATER_GUN", "uses": 29799}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 29378}, {"moveId": "EARTHQUAKE", "uses": 13474}, {"moveId": "MUDDY_WATER", "uses": 11258}, {"moveId": "SURF", "uses": 9729}, {"moveId": "SLUDGE_WAVE", "uses": 6689}, {"moveId": "RETURN", "uses": 5876}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 74.2}, {"speciesId": "genesect_shock", "speciesName": "Genesect (Shock)", "rating": 545, "matchups": [{"opponent": "primarina", "rating": 746, "opRating": 253}, {"opponent": "zarude", "rating": 693, "opRating": 306}, {"opponent": "gyarados", "rating": 677}, {"opponent": "mewtwo_armored", "rating": 651, "opRating": 348}, {"opponent": "meloetta_aria", "rating": 645, "opRating": 354}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "mewtwo", "rating": 411}, {"opponent": "metagross", "rating": 421}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50779}, {"moveId": "METAL_CLAW", "uses": 25721}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25916}, {"moveId": "MAGNET_BOMB", "uses": 23200}, {"moveId": "TECHNO_BLAST_SHOCK", "uses": 22204}, {"moveId": "ZAP_CANNON", "uses": 5208}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_SHOCK"], "score": 74}, {"speciesId": "tapu_fini", "speciesName": "<PERSON><PERSON>", "rating": 485, "matchups": [{"opponent": "buzzwole", "rating": 681, "opRating": 318}, {"opponent": "goodra", "rating": 681, "opRating": 318}, {"opponent": "dragonite", "rating": 617}, {"opponent": "yveltal", "rating": 576, "opRating": 423}, {"opponent": "gyarados", "rating": 544}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "metagross", "rating": 302}, {"opponent": "lugia", "rating": 330}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 8520}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5684}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5148}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4724}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4570}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4564}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4480}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4258}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4217}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4197}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4149}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4129}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3672}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3657}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3641}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3581}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3405}], "chargedMoves": [{"moveId": "SURF", "uses": 31414}, {"moveId": "MOONBLAST", "uses": 22740}, {"moveId": "ICE_BEAM", "uses": 17304}, {"moveId": "HYDRO_PUMP", "uses": 4984}]}, "moveset": ["WATER_GUN", "SURF", "MOONBLAST"], "score": 74}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 494, "matchups": [{"opponent": "genesect_chill", "rating": 848, "opRating": 151}, {"opponent": "sylveon", "rating": 792}, {"opponent": "florges", "rating": 781, "opRating": 218}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 675}, {"opponent": "dialga", "rating": 571}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "metagross", "rating": 229}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "lugia", "rating": 369}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46733}, {"moveId": "BUG_BITE", "uses": 29767}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 24925}, {"moveId": "STONE_EDGE", "uses": 24485}, {"moveId": "IRON_HEAD", "uses": 20256}, {"moveId": "FIRE_BLAST", "uses": 6751}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "IRON_HEAD"], "score": 73.4}, {"speciesId": "tyranitar", "speciesName": "Tyranitar", "rating": 470, "matchups": [{"opponent": "ho_oh_shadow", "rating": 890, "opRating": 109}, {"opponent": "ho_oh", "rating": 728}, {"opponent": "mewtwo", "rating": 621}, {"opponent": "lugia", "rating": 567}, {"opponent": "giratina_origin", "rating": 557}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "garcho<PERSON>", "rating": 152}, {"opponent": "dragonite", "rating": 252}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 407}, {"opponent": "gyarados", "rating": 414}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 41927}, {"moveId": "BITE", "uses": 27266}, {"moveId": "IRON_TAIL", "uses": 7314}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 33528}, {"moveId": "STONE_EDGE", "uses": 25485}, {"moveId": "RETURN", "uses": 9051}, {"moveId": "FIRE_BLAST", "uses": 8418}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 73.4}, {"speciesId": "articuno_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 440, "matchups": [{"opponent": "celebi", "rating": 725, "opRating": 274}, {"opponent": "dragonite_shadow", "rating": 564, "opRating": 435}, {"opponent": "grou<PERSON>", "rating": 561}, {"opponent": "virizion", "rating": 545, "opRating": 454}, {"opponent": "yveltal", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 271}, {"opponent": "mewtwo", "rating": 320}, {"opponent": "lugia", "rating": 354}, {"opponent": "dragonite", "rating": 449}, {"opponent": "garcho<PERSON>", "rating": 467}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46593}, {"moveId": "FROST_BREATH", "uses": 29907}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31072}, {"moveId": "ANCIENT_POWER", "uses": 15600}, {"moveId": "HURRICANE", "uses": 12990}, {"moveId": "ICE_BEAM", "uses": 10014}, {"moveId": "BLIZZARD", "uses": 7018}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ANCIENT_POWER"], "score": 73.2}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 507, "matchups": [{"opponent": "kommo_o", "rating": 677, "opRating": 322}, {"opponent": "goodra", "rating": 610, "opRating": 389}, {"opponent": "zacian_hero", "rating": 603}, {"opponent": "swampert", "rating": 599}, {"opponent": "cobalion", "rating": 508, "opRating": 491}], "counters": [{"opponent": "mewtwo", "rating": 195}, {"opponent": "lugia", "rating": 226}, {"opponent": "dialga", "rating": 239}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "dragonite", "rating": 337}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 40795}, {"moveId": "CONFUSION", "uses": 35705}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21707}, {"moveId": "MOONBLAST", "uses": 21483}, {"moveId": "FUTURE_SIGHT", "uses": 18961}, {"moveId": "AURORA_BEAM", "uses": 14300}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 72.8}, {"speciesId": "kyurem", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 535, "matchups": [{"opponent": "swampert", "rating": 637}, {"opponent": "vaporeon", "rating": 607, "opRating": 392}, {"opponent": "zapdos", "rating": 571, "opRating": 428}, {"opponent": "giratina_origin", "rating": 557}, {"opponent": "zap<PERSON>_shadow", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "lugia", "rating": 264}, {"opponent": "mewtwo", "rating": 276}, {"opponent": "gyarados", "rating": 324}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 56894}, {"moveId": "STEEL_WING", "uses": 19606}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 40955}, {"moveId": "BLIZZARD", "uses": 22117}, {"moveId": "DRACO_METEOR", "uses": 13670}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "BLIZZARD"], "score": 72.5}, {"speciesId": "genesect_douse", "speciesName": "Genesect (Douse)", "rating": 537, "matchups": [{"opponent": "celebi", "rating": 800, "opRating": 199}, {"opponent": "zarude", "rating": 693, "opRating": 306}, {"opponent": "cresselia", "rating": 670, "opRating": 329}, {"opponent": "mewtwo_armored", "rating": 651, "opRating": 348}, {"opponent": "meloetta_aria", "rating": 645, "opRating": 354}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "lugia", "rating": 280}, {"opponent": "mewtwo", "rating": 411}, {"opponent": "metagross", "rating": 421}, {"opponent": "garcho<PERSON>", "rating": 422}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50444}, {"moveId": "METAL_CLAW", "uses": 26056}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24947}, {"moveId": "TECHNO_BLAST_DOUSE", "uses": 22969}, {"moveId": "MAGNET_BOMB", "uses": 22127}, {"moveId": "GUNK_SHOT", "uses": 6555}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_DOUSE"], "score": 72.1}, {"speciesId": "zap<PERSON>_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 501, "matchups": [{"opponent": "yveltal", "rating": 677, "opRating": 322}, {"opponent": "grou<PERSON>", "rating": 650}, {"opponent": "swampert", "rating": 591}, {"opponent": "excadrill", "rating": 591}, {"opponent": "snorlax", "rating": 580, "opRating": 419}], "counters": [{"opponent": "gyarados", "rating": 188}, {"opponent": "lugia", "rating": 219}, {"opponent": "metagross", "rating": 247}, {"opponent": "garcho<PERSON>", "rating": 352}, {"opponent": "dialga", "rating": 361}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 76500}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34242}, {"moveId": "BRAVE_BIRD", "uses": 28899}, {"moveId": "ANCIENT_POWER", "uses": 13369}]}, "moveset": ["COUNTER", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 72.1}, {"speciesId": "genesect", "speciesName": "Genesect", "rating": 524, "matchups": [{"opponent": "zarude", "rating": 693, "opRating": 306}, {"opponent": "cresselia", "rating": 670, "opRating": 329}, {"opponent": "mewtwo_armored", "rating": 651, "opRating": 348}, {"opponent": "meloetta_aria", "rating": 645, "opRating": 354}, {"opponent": "goodra", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "metagross", "rating": 386}, {"opponent": "mewtwo", "rating": 411}, {"opponent": "gyarados", "rating": 420}, {"opponent": "garcho<PERSON>", "rating": 422}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 49311}, {"moveId": "METAL_CLAW", "uses": 27189}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26834}, {"moveId": "MAGNET_BOMB", "uses": 25107}, {"moveId": "TECHNO_BLAST_NORMAL", "uses": 20823}, {"moveId": "HYPER_BEAM", "uses": 3605}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_NORMAL"], "score": 71.7}, {"speciesId": "tyranitar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 472, "matchups": [{"opponent": "moltres", "rating": 912, "opRating": 87}, {"opponent": "ho_oh", "rating": 890}, {"opponent": "mewtwo", "rating": 567}, {"opponent": "giratina_altered", "rating": 509, "opRating": 490}, {"opponent": "giratina_origin", "rating": 502}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "dragonite", "rating": 305}, {"opponent": "lugia", "rating": 395}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 415}, {"opponent": "gyarados", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43659}, {"moveId": "BITE", "uses": 25836}, {"moveId": "IRON_TAIL", "uses": 6965}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37812}, {"moveId": "STONE_EDGE", "uses": 29061}, {"moveId": "FIRE_BLAST", "uses": 9624}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "CRUNCH", "STONE_EDGE"], "score": 71.5}, {"speciesId": "mewtwo_armored", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Armored)", "rating": 537, "matchups": [{"opponent": "zacian_hero", "rating": 622}, {"opponent": "excadrill", "rating": 588}, {"opponent": "kommo_o", "rating": 575, "opRating": 424}, {"opponent": "cobalion", "rating": 531, "opRating": 468}, {"opponent": "buzzwole", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "gyarados", "rating": 237}, {"opponent": "swampert", "rating": 442}, {"opponent": "dragonite", "rating": 462}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66270}, {"moveId": "IRON_TAIL", "uses": 10230}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 25580}, {"moveId": "ROCK_SLIDE", "uses": 18845}, {"moveId": "DYNAMIC_PUNCH", "uses": 15515}, {"moveId": "EARTHQUAKE", "uses": 11434}, {"moveId": "FUTURE_SIGHT", "uses": 5243}]}, "moveset": ["CONFUSION", "PSYSTRIKE", "DYNAMIC_PUNCH"], "score": 71.3}, {"speciesId": "genesect_burn", "speciesName": "Genesect (Burn)", "rating": 537, "matchups": [{"opponent": "zarude", "rating": 693, "opRating": 306}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 680, "opRating": 319}, {"opponent": "mewtwo_armored", "rating": 651, "opRating": 348}, {"opponent": "meloetta_aria", "rating": 645, "opRating": 354}, {"opponent": "metagross", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "lugia", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 293}, {"opponent": "gyarados", "rating": 319}, {"opponent": "mewtwo", "rating": 411}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50545}, {"moveId": "METAL_CLAW", "uses": 25955}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 24635}, {"moveId": "MAGNET_BOMB", "uses": 22846}, {"moveId": "TECHNO_BLAST_BURN", "uses": 22711}, {"moveId": "FLAMETHROWER", "uses": 6417}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TECHNO_BLAST_BURN"], "score": 71.1}, {"speciesId": "registeel", "speciesName": "Registeel", "rating": 460, "matchups": [{"opponent": "sylveon", "rating": 659}, {"opponent": "gyarados", "rating": 627}, {"opponent": "lugia", "rating": 587}, {"opponent": "lugia_shadow", "rating": 587, "opRating": 412}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 558}], "counters": [{"opponent": "giratina_origin", "rating": 165}, {"opponent": "zekrom", "rating": 274}, {"opponent": "metagross", "rating": 287}, {"opponent": "mewtwo", "rating": 304}, {"opponent": "dialga", "rating": 407}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42529}, {"moveId": "METAL_CLAW", "uses": 21798}, {"moveId": "ROCK_SMASH", "uses": 12176}], "chargedMoves": [{"moveId": "FLASH_CANNON", "uses": null}, {"moveId": "FOCUS_BLAST", "uses": null}, {"moveId": "HYPER_BEAM", "uses": null}, {"moveId": "ZAP_CANNON", "uses": null}]}, "moveset": ["LOCK_ON", "FOCUS_BLAST", "ZAP_CANNON"], "score": 70.7}, {"speciesId": "regirock", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 546, "matchups": [{"opponent": "ho_oh", "rating": 604, "opRating": 395}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 595}, {"opponent": "lugia_shadow", "rating": 590, "opRating": 409}, {"opponent": "snorlax", "rating": 514, "opRating": 485}, {"opponent": "lugia", "rating": 511}], "counters": [{"opponent": "giratina_origin", "rating": 207}, {"opponent": "zacian_hero", "rating": 248}, {"opponent": "mewtwo", "rating": 265}, {"opponent": "dragonite", "rating": 372}, {"opponent": "gyarados", "rating": 378}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 41065}, {"moveId": "ROCK_THROW", "uses": 25504}, {"moveId": "ROCK_SMASH", "uses": 9925}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 30551}, {"moveId": "EARTHQUAKE", "uses": 18974}, {"moveId": "FOCUS_BLAST", "uses": 14039}, {"moveId": "ZAP_CANNON", "uses": 12845}]}, "moveset": ["LOCK_ON", "STONE_EDGE", "FOCUS_BLAST"], "score": 70.5}, {"speciesId": "kommo_o", "speciesName": "Kommo-o", "rating": 555, "matchups": [{"opponent": "melmetal", "rating": 649, "opRating": 350}, {"opponent": "mew", "rating": 609, "opRating": 390}, {"opponent": "cobalion", "rating": 603, "opRating": 396}, {"opponent": "snorlax", "rating": 585, "opRating": 414}, {"opponent": "swampert", "rating": 524}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "garcho<PERSON>", "rating": 180}, {"opponent": "gyarados", "rating": 250}, {"opponent": "metagross", "rating": 430}, {"opponent": "giratina_origin", "rating": 468}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42068}, {"moveId": "POISON_JAB", "uses": 34432}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35273}, {"moveId": "DRAGON_CLAW", "uses": 29153}, {"moveId": "FLAMETHROWER", "uses": 12124}]}, "moveset": ["DRAGON_TAIL", "CLOSE_COMBAT", "DRAGON_CLAW"], "score": 70}, {"speciesId": "latios", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 545, "matchups": [{"opponent": "mewtwo_armored", "rating": 610, "opRating": 389}, {"opponent": "virizion", "rating": 604, "opRating": 395}, {"opponent": "zapdos", "rating": 575, "opRating": 424}, {"opponent": "ho_oh", "rating": 537, "opRating": 462}, {"opponent": "cobalion", "rating": 534, "opRating": 465}], "counters": [{"opponent": "dialga", "rating": 187}, {"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "lugia", "rating": 269}, {"opponent": "swampert", "rating": 497}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 66752}, {"moveId": "ZEN_HEADBUTT", "uses": 9748}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30046}, {"moveId": "LUSTER_PURGE", "uses": 19614}, {"moveId": "PSYCHIC", "uses": 13002}, {"moveId": "RETURN", "uses": 7311}, {"moveId": "SOLAR_BEAM", "uses": 6720}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 69.8}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 447, "matchups": [{"opponent": "steelix", "rating": 765, "opRating": 234}, {"opponent": "giratina_origin", "rating": 744}, {"opponent": "mewtwo_shadow", "rating": 589, "opRating": 410}, {"opponent": "mewtwo", "rating": 568}, {"opponent": "suicune", "rating": 552, "opRating": 447}], "counters": [{"opponent": "lugia", "rating": 195}, {"opponent": "garcho<PERSON>", "rating": 197}, {"opponent": "gyarados", "rating": 260}, {"opponent": "metagross", "rating": 281}, {"opponent": "dialga", "rating": 282}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44707}, {"moveId": "LICK", "uses": 31793}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 37976}, {"moveId": "CROSS_CHOP", "uses": 22293}, {"moveId": "HYPER_BEAM", "uses": 8612}, {"moveId": "GUNK_SHOT", "uses": 6638}, {"moveId": "OBSTRUCT", "uses": 910}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "OBSTRUCT"], "score": 69.4}, {"speciesId": "z<PERSON><PERSON><PERSON>_hero", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hero)", "rating": 555, "matchups": [{"opponent": "obstagoon", "rating": 812, "opRating": 187}, {"opponent": "melmetal", "rating": 661, "opRating": 338}, {"opponent": "kyurem", "rating": 627, "opRating": 372}, {"opponent": "snorlax", "rating": 578, "opRating": 421}, {"opponent": "excadrill", "rating": 520}], "counters": [{"opponent": "lugia", "rating": 230}, {"opponent": "gyarados", "rating": 301}, {"opponent": "giratina_origin", "rating": 306}, {"opponent": "garcho<PERSON>", "rating": 333}, {"opponent": "metagross", "rating": 412}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 26778}, {"moveId": "QUICK_ATTACK", "uses": 25493}, {"moveId": "ICE_FANG", "uses": 14836}, {"moveId": "METAL_CLAW", "uses": 9437}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35186}, {"moveId": "CRUNCH", "uses": 19520}, {"moveId": "MOONBLAST", "uses": 13083}, {"moveId": "IRON_HEAD", "uses": 8856}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 69.2}, {"speciesId": "latios_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 511, "matchups": [{"opponent": "kyogre", "rating": 764, "opRating": 235}, {"opponent": "entei", "rating": 720, "opRating": 279}, {"opponent": "mewtwo_armored", "rating": 656, "opRating": 343}, {"opponent": "virizion", "rating": 648, "opRating": 351}, {"opponent": "swampert", "rating": 572}], "counters": [{"opponent": "mewtwo", "rating": 205}, {"opponent": "lugia", "rating": 230}, {"opponent": "dialga", "rating": 233}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 298}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 67616}, {"moveId": "ZEN_HEADBUTT", "uses": 8884}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 33210}, {"moveId": "LUSTER_PURGE", "uses": 21563}, {"moveId": "PSYCHIC", "uses": 14248}, {"moveId": "SOLAR_BEAM", "uses": 7472}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "DRAGON_CLAW", "LUSTER_PURGE"], "score": 68.6}, {"speciesId": "metagross", "speciesName": "Metagross", "rating": 493, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 686, "opRating": 313}, {"opponent": "sylveon", "rating": 654, "opRating": 345}, {"opponent": "nihilego", "rating": 645, "opRating": 354}, {"opponent": "goodra", "rating": 627, "opRating": 372}, {"opponent": "melmetal", "rating": 572, "opRating": 427}], "counters": [{"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "dialga", "rating": 347}, {"opponent": "lugia", "rating": 359}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 67290}, {"moveId": "ZEN_HEADBUTT", "uses": 9210}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 29570}, {"moveId": "PSYCHIC", "uses": 17032}, {"moveId": "EARTHQUAKE", "uses": 16341}, {"moveId": "RETURN", "uses": 8923}, {"moveId": "FLASH_CANNON", "uses": 4764}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 68.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "Ray<PERSON><PERSON>", "rating": 483, "matchups": [{"opponent": "grou<PERSON>", "rating": 636}, {"opponent": "virizion", "rating": 609, "opRating": 390}, {"opponent": "swampert", "rating": 539}, {"opponent": "buzzwole", "rating": 518, "opRating": 481}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 281}, {"opponent": "gyarados", "rating": 291}, {"opponent": "mewtwo", "rating": 294}, {"opponent": "giratina_origin", "rating": 400}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 46913}, {"moveId": "AIR_SLASH", "uses": 29587}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23883}, {"moveId": "ANCIENT_POWER", "uses": 19217}, {"moveId": "AERIAL_ACE", "uses": 17514}, {"moveId": "HURRICANE", "uses": 15938}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "ANCIENT_POWER"], "score": 68.4}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 524, "matchups": [{"opponent": "nihilego", "rating": 737, "opRating": 262}, {"opponent": "melmetal", "rating": 697, "opRating": 302}, {"opponent": "cobalion", "rating": 647, "opRating": 352}, {"opponent": "metagross", "rating": 544}, {"opponent": "excadrill", "rating": 502}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "dialga", "rating": 138}, {"opponent": "grou<PERSON>", "rating": 383}, {"opponent": "swampert", "rating": 452}, {"opponent": "giratina_origin", "rating": 480}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40169}, {"moveId": "DRAGON_TAIL", "uses": 36331}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 24679}, {"moveId": "EARTH_POWER", "uses": 22591}, {"moveId": "FIRE_BLAST", "uses": 9840}, {"moveId": "EARTHQUAKE", "uses": 9671}, {"moveId": "SAND_TOMB", "uses": 9487}]}, "moveset": ["MUD_SHOT", "OUTRAGE", "EARTH_POWER"], "score": 67.5}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 472, "matchups": [{"opponent": "sylveon", "rating": 671}, {"opponent": "grou<PERSON>", "rating": 651}, {"opponent": "snorlax", "rating": 609, "opRating": 390}, {"opponent": "zacian_hero", "rating": 541}, {"opponent": "metagross", "rating": 520}], "counters": [{"opponent": "giratina_origin", "rating": 121}, {"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "dialga", "rating": 176}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 18791}, {"moveId": "EXTRASENSORY", "uses": 5401}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3975}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3843}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3714}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3569}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3281}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3279}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3241}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3064}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3021}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2968}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2842}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2822}, {"moveId": "STEEL_WING", "uses": 2675}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2543}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2492}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2358}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2324}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 32095}, {"moveId": "SACRED_FIRE", "uses": 20406}, {"moveId": "EARTHQUAKE", "uses": 12863}, {"moveId": "SOLAR_BEAM", "uses": 6979}, {"moveId": "FIRE_BLAST", "uses": 4143}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "SACRED_FIRE"], "score": 67.5}, {"speciesId": "moltres_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 463, "matchups": [{"opponent": "celebi", "rating": 771, "opRating": 228}, {"opponent": "mewtwo", "rating": 639}, {"opponent": "mewtwo_armored", "rating": 629, "opRating": 370}, {"opponent": "zarude", "rating": 586, "opRating": 413}, {"opponent": "virizion", "rating": 532, "opRating": 467}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "lugia", "rating": 269}, {"opponent": "giratina_origin", "rating": 448}, {"opponent": "grou<PERSON>", "rating": 456}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39063}, {"moveId": "WING_ATTACK", "uses": 37437}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36311}, {"moveId": "PAYBACK", "uses": 23935}, {"moveId": "ANCIENT_POWER", "uses": 16410}]}, "moveset": ["SUCKER_PUNCH", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 67.3}, {"speciesId": "clefable", "speciesName": "Clefable", "rating": 390, "matchups": [{"opponent": "kommo_o", "rating": 737, "opRating": 262}, {"opponent": "dragonite", "rating": 719}, {"opponent": "buzzwole", "rating": 621, "opRating": 378}, {"opponent": "goodra", "rating": 567, "opRating": 432}, {"opponent": "yveltal", "rating": 507, "opRating": 492}], "counters": [{"opponent": "mewtwo", "rating": 236}, {"opponent": "dialga", "rating": 239}, {"opponent": "giratina_origin", "rating": 358}, {"opponent": "gyarados", "rating": 373}, {"opponent": "garcho<PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 41472}, {"moveId": "CHARGE_BEAM", "uses": 26395}, {"moveId": "ZEN_HEADBUTT", "uses": 5587}, {"moveId": "POUND", "uses": 3073}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28508}, {"moveId": "METEOR_MASH", "uses": 25676}, {"moveId": "PSYCHIC", "uses": 14999}, {"moveId": "DAZZLING_GLEAM", "uses": 7275}]}, "moveset": ["CHARM", "MOONBLAST", "METEOR_MASH"], "score": 67.1}, {"speciesId": "salamence_shadow", "speciesName": "Salamence (Shadow)", "rating": 450, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 819, "opRating": 180}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 662, "opRating": 337}, {"opponent": "swampert_shadow", "rating": 577, "opRating": 422}, {"opponent": "haxorus", "rating": 538, "opRating": 461}, {"opponent": "virizion", "rating": 520, "opRating": 479}], "counters": [{"opponent": "dialga", "rating": 260}, {"opponent": "garcho<PERSON>", "rating": 328}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "giratina_origin", "rating": 346}, {"opponent": "grou<PERSON>", "rating": 491}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 45088}, {"moveId": "FIRE_FANG", "uses": 19487}, {"moveId": "BITE", "uses": 11914}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 35116}, {"moveId": "FIRE_BLAST", "uses": 15427}, {"moveId": "HYDRO_PUMP", "uses": 15292}, {"moveId": "DRACO_METEOR", "uses": 10543}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "FIRE_BLAST"], "score": 66.9}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 472, "matchups": [{"opponent": "cobalion", "rating": 703, "opRating": 296}, {"opponent": "magnezone", "rating": 641, "opRating": 358}, {"opponent": "nihilego", "rating": 622, "opRating": 377}, {"opponent": "melmetal", "rating": 597, "opRating": 402}, {"opponent": "excadrill", "rating": 576, "opRating": 423}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "giratina_origin", "rating": 288}, {"opponent": "zacian_hero", "rating": 341}, {"opponent": "gyarados", "rating": 342}, {"opponent": "garcho<PERSON>", "rating": 345}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51845}, {"moveId": "ROCK_THROW", "uses": 24655}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 25407}, {"moveId": "EARTH_POWER", "uses": 24557}, {"moveId": "OUTRAGE", "uses": 15207}, {"moveId": "FOCUS_BLAST", "uses": 11275}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTH_POWER"], "score": 65.4}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 492, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 799}, {"opponent": "primarina", "rating": 799, "opRating": 200}, {"opponent": "gyarados", "rating": 697}, {"opponent": "sylveon", "rating": 547}, {"opponent": "lugia", "rating": 509}], "counters": [{"opponent": "zacian_hero", "rating": 106}, {"opponent": "metagross", "rating": 107}, {"opponent": "giratina_origin", "rating": 137}, {"opponent": "dialga", "rating": 138}, {"opponent": "mewtwo", "rating": 408}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47560}, {"moveId": "CHARGE_BEAM", "uses": 28940}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37197}, {"moveId": "MIRROR_SHOT", "uses": 16866}, {"moveId": "FLASH_CANNON", "uses": 8817}, {"moveId": "RETURN", "uses": 7887}, {"moveId": "ZAP_CANNON", "uses": 5721}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 65.2}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 464, "matchups": [{"opponent": "nihilego", "rating": 656, "opRating": 343}, {"opponent": "excadrill", "rating": 644}, {"opponent": "regirock", "rating": 639, "opRating": 360}, {"opponent": "melmetal", "rating": 559, "opRating": 440}, {"opponent": "cobalion", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 266}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "mewtwo", "rating": 317}, {"opponent": "metagross", "rating": 363}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 48561}, {"moveId": "WATER_GUN", "uses": 27939}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 31747}, {"moveId": "EARTHQUAKE", "uses": 14545}, {"moveId": "MUDDY_WATER", "uses": 12184}, {"moveId": "SURF", "uses": 10539}, {"moveId": "SLUDGE_WAVE", "uses": 7444}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "EARTHQUAKE"], "score": 65.2}, {"speciesId": "golisopod", "speciesName": "Golisopod", "rating": 445, "matchups": [{"opponent": "walrein", "rating": 670, "opRating": 329}, {"opponent": "gardevoir_shadow", "rating": 609, "opRating": 390}, {"opponent": "empoleon", "rating": 588, "opRating": 411}, {"opponent": "suicune", "rating": 545, "opRating": 454}, {"opponent": "swampert_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 201}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "metagross", "rating": 345}, {"opponent": "mewtwo", "rating": 364}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25994}, {"moveId": "FURY_CUTTER", "uses": 21306}, {"moveId": "WATERFALL", "uses": 18885}, {"moveId": "METAL_CLAW", "uses": 10307}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 38617}, {"moveId": "AERIAL_ACE", "uses": 19145}, {"moveId": "AQUA_JET", "uses": 18770}]}, "moveset": ["SHADOW_CLAW", "X_SCISSOR", "AERIAL_ACE"], "score": 64.4}, {"speciesId": "salamence", "speciesName": "Salamence", "rating": 443, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "chesnaught", "rating": 608, "opRating": 391}, {"opponent": "virizion", "rating": 582, "opRating": 417}, {"opponent": "haxorus", "rating": 582, "opRating": 417}, {"opponent": "grou<PERSON>", "rating": 536}], "counters": [{"opponent": "dialga", "rating": 228}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "mewtwo", "rating": 291}, {"opponent": "giratina_origin", "rating": 384}, {"opponent": "swampert", "rating": 480}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43209}, {"moveId": "FIRE_FANG", "uses": 19916}, {"moveId": "BITE", "uses": 13286}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 29306}, {"moveId": "RETURN", "uses": 13027}, {"moveId": "FIRE_BLAST", "uses": 12802}, {"moveId": "HYDRO_PUMP", "uses": 12566}, {"moveId": "DRACO_METEOR", "uses": 8800}]}, "moveset": ["DRAGON_TAIL", "OUTRAGE", "RETURN"], "score": 64.4}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 447, "matchups": [{"opponent": "nihilego", "rating": 652, "opRating": 347}, {"opponent": "rai<PERSON>u", "rating": 628, "opRating": 371}, {"opponent": "zapdos", "rating": 567, "opRating": 432}, {"opponent": "latios", "rating": 521, "opRating": 478}, {"opponent": "giratina_altered", "rating": 512, "opRating": 487}], "counters": [{"opponent": "giratina_origin", "rating": 241}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "dialga", "rating": 339}, {"opponent": "dragonite", "rating": 353}, {"opponent": "lugia", "rating": 357}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 41206}, {"moveId": "THUNDER_FANG", "uses": 24305}, {"moveId": "IRON_TAIL", "uses": 10972}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22575}, {"moveId": "PSYCHIC_FANGS", "uses": 20152}, {"moveId": "EARTHQUAKE", "uses": 19177}, {"moveId": "HEAVY_SLAM", "uses": 14659}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "PSYCHIC_FANGS"], "score": 64.4}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 407, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 704, "opRating": 295}, {"opponent": "magnezone", "rating": 636, "opRating": 363}, {"opponent": "zapdos", "rating": 563, "opRating": 436}, {"opponent": "garcho<PERSON>", "rating": 553}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 537}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "lugia", "rating": 219}, {"opponent": "giratina_origin", "rating": 286}, {"opponent": "dragonite", "rating": 454}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23957}, {"moveId": "FIRE_FANG", "uses": 19790}, {"moveId": "THUNDER_FANG", "uses": 19347}, {"moveId": "BITE", "uses": 13335}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 23511}, {"moveId": "BODY_SLAM", "uses": 20723}, {"moveId": "EARTH_POWER", "uses": 18017}, {"moveId": "EARTHQUAKE", "uses": 7804}, {"moveId": "STONE_EDGE", "uses": 6337}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 64.2}, {"speciesId": "tapu_bulu", "speciesName": "Tapu Bulu", "rating": 403, "matchups": [{"opponent": "swampert", "rating": 681}, {"opponent": "garcho<PERSON>", "rating": 566}, {"opponent": "dragonite", "rating": 566}, {"opponent": "yveltal", "rating": 535}, {"opponent": "gyarados", "rating": 512}], "counters": [{"opponent": "dialga", "rating": 40}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "zacian_hero", "rating": 346}, {"opponent": "excadrill", "rating": 488}, {"opponent": "kyogre", "rating": 489}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 59708}, {"moveId": "ROCK_SMASH", "uses": 16792}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28644}, {"moveId": "MEGAHORN", "uses": 23259}, {"moveId": "DAZZLING_GLEAM", "uses": 18636}, {"moveId": "SOLAR_BEAM", "uses": 5974}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "DAZZLING_GLEAM"], "score": 63.8}, {"speciesId": "vaporeon", "speciesName": "Vaporeon", "rating": 475, "matchups": [{"opponent": "entei", "rating": 753, "opRating": 246}, {"opponent": "moltres", "rating": 704, "opRating": 295}, {"opponent": "heatran", "rating": 644, "opRating": 355}, {"opponent": "avalugg", "rating": 593, "opRating": 406}, {"opponent": "excadrill", "rating": 510}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 284}, {"opponent": "metagross", "rating": 404}, {"opponent": "grou<PERSON>", "rating": 461}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 76500}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38186}, {"moveId": "LAST_RESORT", "uses": 14544}, {"moveId": "SCALD", "uses": 13516}, {"moveId": "HYDRO_PUMP", "uses": 6023}, {"moveId": "WATER_PULSE", "uses": 4337}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "LAST_RESORT"], "score": 63.8}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 480, "matchups": [{"opponent": "genesect_chill", "rating": 686, "opRating": 313}, {"opponent": "genesect_burn", "rating": 686, "opRating": 313}, {"opponent": "genesect_shock", "rating": 659, "opRating": 340}, {"opponent": "genesect", "rating": 659, "opRating": 340}, {"opponent": "florges", "rating": 621, "opRating": 378}], "counters": [{"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "dialga", "rating": 285}, {"opponent": "zacian_hero", "rating": 309}, {"opponent": "mewtwo", "rating": 338}, {"opponent": "metagross", "rating": 386}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 43175}, {"moveId": "CONFUSION", "uses": 33325}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 48085}, {"moveId": "PSYCHIC", "uses": 14173}, {"moveId": "FOCUS_BLAST", "uses": 8649}, {"moveId": "OVERHEAT", "uses": 5513}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 63.8}, {"speciesId": "latias", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 462, "matchups": [{"opponent": "celebi", "rating": 656, "opRating": 343}, {"opponent": "mewtwo_armored", "rating": 627, "opRating": 372}, {"opponent": "virizion", "rating": 523, "opRating": 476}, {"opponent": "machamp", "rating": 517, "opRating": 482}, {"opponent": "magnezone", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dialga", "rating": 217}, {"opponent": "mewtwo", "rating": 263}, {"opponent": "giratina_origin", "rating": 264}, {"opponent": "garcho<PERSON>", "rating": 267}, {"opponent": "dragonite", "rating": 303}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 46261}, {"moveId": "CHARM", "uses": 23596}, {"moveId": "ZEN_HEADBUTT", "uses": 6654}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 21906}, {"moveId": "OUTRAGE", "uses": 19624}, {"moveId": "PSYCHIC", "uses": 14565}, {"moveId": "THUNDER", "uses": 12168}, {"moveId": "RETURN", "uses": 8022}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 63.3}, {"speciesId": "milotic", "speciesName": "Milo<PERSON>", "rating": 465, "matchups": [{"opponent": "nidoqueen", "rating": 654, "opRating": 345}, {"opponent": "heatran", "rating": 561, "opRating": 438}, {"opponent": "ho_oh_shadow", "rating": 528, "opRating": 471}, {"opponent": "haxorus", "rating": 507, "opRating": 492}, {"opponent": "entei", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 298}, {"opponent": "dragonite", "rating": 300}, {"opponent": "gyarados", "rating": 319}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 40708}, {"moveId": "WATERFALL", "uses": 35792}], "chargedMoves": [{"moveId": "SURF", "uses": 46234}, {"moveId": "BLIZZARD", "uses": 20002}, {"moveId": "HYPER_BEAM", "uses": 10140}]}, "moveset": ["DRAGON_TAIL", "SURF", "BLIZZARD"], "score": 63.1}, {"speciesId": "terrakion", "speciesName": "Terrakion", "rating": 511, "matchups": [{"opponent": "kyurem", "rating": 632, "opRating": 367}, {"opponent": "snorlax", "rating": 622, "opRating": 377}, {"opponent": "genesect_burn", "rating": 617, "opRating": 382}, {"opponent": "ho_oh", "rating": 571, "opRating": 428}, {"opponent": "excadrill", "rating": 531}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "giratina_origin", "rating": 225}, {"opponent": "lugia", "rating": 250}, {"opponent": "zacian_hero", "rating": 398}, {"opponent": "gyarados", "rating": 458}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47365}, {"moveId": "SMACK_DOWN", "uses": 26575}, {"moveId": "ZEN_HEADBUTT", "uses": 2542}], "chargedMoves": [{"moveId": "SACRED_SWORD", "uses": 24210}, {"moveId": "ROCK_SLIDE", "uses": 22841}, {"moveId": "CLOSE_COMBAT", "uses": 21146}, {"moveId": "EARTHQUAKE", "uses": 8261}]}, "moveset": ["DOUBLE_KICK", "SACRED_SWORD", "ROCK_SLIDE"], "score": 62.9}, {"speciesId": "land<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 482, "matchups": [{"opponent": "obstagoon", "rating": 834, "opRating": 165}, {"opponent": "magnezone", "rating": 826, "opRating": 173}, {"opponent": "excadrill", "rating": 766}, {"opponent": "cobalion", "rating": 660, "opRating": 339}, {"opponent": "melmetal", "rating": 548, "opRating": 451}], "counters": [{"opponent": "dialga", "rating": 165}, {"opponent": "lugia", "rating": 266}, {"opponent": "giratina_origin", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 314}, {"opponent": "zacian_hero", "rating": 343}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 51926}, {"moveId": "EXTRASENSORY", "uses": 24574}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24335}, {"moveId": "STONE_EDGE", "uses": 22031}, {"moveId": "EARTHQUAKE", "uses": 18658}, {"moveId": "BULLDOZE", "uses": 11450}]}, "moveset": ["MUD_SHOT", "SUPER_POWER", "STONE_EDGE"], "score": 62.7}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 450, "matchups": [{"opponent": "swampert", "rating": 741}, {"opponent": "virizion", "rating": 661, "opRating": 338}, {"opponent": "primarina", "rating": 651, "opRating": 348}, {"opponent": "excadrill", "rating": 636}, {"opponent": "regirock", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 232}, {"opponent": "zacian_hero", "rating": 358}, {"opponent": "gyarados", "rating": 381}, {"opponent": "grou<PERSON>", "rating": 404}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 30116}, {"moveId": "CONFUSION", "uses": 29483}, {"moveId": "CHARGE_BEAM", "uses": 16873}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 22087}, {"moveId": "PSYCHIC", "uses": 20079}, {"moveId": "LEAF_STORM", "uses": 15054}, {"moveId": "DAZZLING_GLEAM", "uses": 11063}, {"moveId": "HYPER_BEAM", "uses": 8085}]}, "moveset": ["CONFUSION", "SEED_BOMB", "LEAF_STORM"], "score": 62.5}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 440, "matchups": [{"opponent": "magnezone", "rating": 685, "opRating": 314}, {"opponent": "zapdos", "rating": 629, "opRating": 370}, {"opponent": "rai<PERSON>u", "rating": 626, "opRating": 373}, {"opponent": "melmetal", "rating": 582, "opRating": 417}, {"opponent": "nihilego", "rating": 572, "opRating": 427}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "giratina_origin", "rating": 252}, {"opponent": "garcho<PERSON>", "rating": 429}, {"opponent": "dragonite", "rating": 449}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23417}, {"moveId": "FIRE_FANG", "uses": 19455}, {"moveId": "THUNDER_FANG", "uses": 19420}, {"moveId": "BITE", "uses": 14162}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 22783}, {"moveId": "BODY_SLAM", "uses": 19810}, {"moveId": "EARTH_POWER", "uses": 17380}, {"moveId": "EARTHQUAKE", "uses": 7525}, {"moveId": "STONE_EDGE", "uses": 6066}, {"moveId": "RETURN", "uses": 2890}]}, "moveset": ["ICE_FANG", "WEATHER_BALL_ROCK", "EARTH_POWER"], "score": 62.1}, {"speciesId": "tapu_koko", "speciesName": "<PERSON><PERSON>", "rating": 367, "matchups": [{"opponent": "goodra", "rating": 617, "opRating": 382}, {"opponent": "yveltal", "rating": 608}, {"opponent": "lugia", "rating": 566}, {"opponent": "dragonite", "rating": 538}, {"opponent": "gyarados", "rating": 522}], "counters": [{"opponent": "giratina_origin", "rating": 81}, {"opponent": "mewtwo", "rating": 171}, {"opponent": "zacian_hero", "rating": 176}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 255}, {"opponent": "dialga", "rating": 429}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43996}, {"moveId": "QUICK_ATTACK", "uses": 32504}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31125}, {"moveId": "THUNDERBOLT", "uses": 20730}, {"moveId": "DAZZLING_GLEAM", "uses": 15533}, {"moveId": "THUNDER", "uses": 9182}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "DAZZLING_GLEAM"], "score": 62.1}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 370, "matchups": [{"opponent": "salamence_shadow", "rating": 717, "opRating": 282}, {"opponent": "landorus_incarnate", "rating": 696, "opRating": 303}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 696, "opRating": 303}, {"opponent": "giratina_origin", "rating": 643}, {"opponent": "golisopod", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "mewtwo", "rating": 164}, {"opponent": "gyarados", "rating": 257}, {"opponent": "dragonite", "rating": 409}, {"opponent": "garcho<PERSON>", "rating": 467}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5922}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5593}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 5217}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5077}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 5064}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4998}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4903}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4833}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4503}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4491}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4446}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 4142}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4084}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4057}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3891}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3800}, {"moveId": "ZEN_HEADBUTT", "uses": 1261}], "chargedMoves": [{"moveId": "THUNDER", "uses": 27030}, {"moveId": "FOCUS_BLAST", "uses": 26083}, {"moveId": "GIGA_IMPACT", "uses": 23494}]}, "moveset": ["HIDDEN_POWER_ICE", "THUNDER", "FOCUS_BLAST"], "score": 61.9}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 461, "matchups": [{"opponent": "magnezone", "rating": 838, "opRating": 161}, {"opponent": "nihilego", "rating": 804, "opRating": 195}, {"opponent": "entei", "rating": 766, "opRating": 233}, {"opponent": "melmetal", "rating": 654, "opRating": 345}, {"opponent": "zapdos", "rating": 641, "opRating": 358}], "counters": [{"opponent": "garcho<PERSON>", "rating": 183}, {"opponent": "giratina_origin", "rating": 199}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "dialga", "rating": 263}, {"opponent": "lugia", "rating": 354}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 40289}, {"moveId": "MUD_SLAP", "uses": 36211}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 24526}, {"moveId": "SUPER_POWER", "uses": 15091}, {"moveId": "SURF", "uses": 12713}, {"moveId": "EARTHQUAKE", "uses": 11749}, {"moveId": "STONE_EDGE", "uses": 6954}, {"moveId": "SKULL_BASH", "uses": 5458}]}, "moveset": ["MUD_SLAP", "ROCK_WRECKER", "SURF"], "score": 61.9}, {"speciesId": "walrein", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 395, "matchups": [{"opponent": "garcho<PERSON>", "rating": 618}, {"opponent": "heatran", "rating": 611, "opRating": 388}, {"opponent": "hippo<PERSON><PERSON>", "rating": 593, "opRating": 406}, {"opponent": "articuno", "rating": 586, "opRating": 413}, {"opponent": "over<PERSON><PERSON>l", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 222}, {"opponent": "lugia", "rating": 235}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "gyarados", "rating": 275}, {"opponent": "giratina_origin", "rating": 316}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 36741}, {"moveId": "WATERFALL", "uses": 21536}, {"moveId": "FROST_BREATH", "uses": 18218}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 35775}, {"moveId": "EARTHQUAKE", "uses": 16217}, {"moveId": "RETURN", "uses": 8626}, {"moveId": "WATER_PULSE", "uses": 8083}, {"moveId": "BLIZZARD", "uses": 7843}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 61.9}, {"speciesId": "aromatisse", "speciesName": "Aromatisse", "rating": 375, "matchups": [{"opponent": "kommo_o", "rating": 735, "opRating": 264}, {"opponent": "hydreigon", "rating": 727, "opRating": 272}, {"opponent": "dragonite_shadow", "rating": 641, "opRating": 358}, {"opponent": "goodra", "rating": 566, "opRating": 433}, {"opponent": "dragonite", "rating": 517}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "giratina_origin", "rating": 340}, {"opponent": "gyarados", "rating": 373}, {"opponent": "garcho<PERSON>", "rating": 401}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46578}, {"moveId": "CHARGE_BEAM", "uses": 29922}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 30011}, {"moveId": "THUNDERBOLT", "uses": 18545}, {"moveId": "PSYCHIC", "uses": 15742}, {"moveId": "DRAINING_KISS", "uses": 12175}]}, "moveset": ["CHARM", "MOONBLAST", "THUNDERBOLT"], "score": 61.2}, {"speciesId": "latias_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 454, "matchups": [{"opponent": "ampha<PERSON>", "rating": 630, "opRating": 369}, {"opponent": "blaziken", "rating": 613, "opRating": 386}, {"opponent": "haxorus", "rating": 604, "opRating": 395}, {"opponent": "mewtwo_armored", "rating": 534, "opRating": 465}, {"opponent": "salamence_shadow", "rating": 529, "opRating": 470}], "counters": [{"opponent": "mewtwo", "rating": 195}, {"opponent": "giratina_origin", "rating": 239}, {"opponent": "dialga", "rating": 244}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "dragonite", "rating": 279}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 47504}, {"moveId": "CHARM", "uses": 22771}, {"moveId": "ZEN_HEADBUTT", "uses": 6212}], "chargedMoves": [{"moveId": "MIST_BALL", "uses": 24405}, {"moveId": "OUTRAGE", "uses": 22058}, {"moveId": "PSYCHIC", "uses": 16222}, {"moveId": "THUNDER", "uses": 13740}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "MIST_BALL", "OUTRAGE"], "score": 61.2}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 474, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 754}, {"opponent": "lugia_shadow", "rating": 694, "opRating": 305}, {"opponent": "genesect_shock", "rating": 640, "opRating": 359}, {"opponent": "gyarados", "rating": 627}, {"opponent": "lugia", "rating": 601}], "counters": [{"opponent": "dragonite", "rating": 95}, {"opponent": "metagross", "rating": 107}, {"opponent": "dialga", "rating": 146}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "sylveon", "rating": 461}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 47962}, {"moveId": "CHARGE_BEAM", "uses": 28538}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 41024}, {"moveId": "MIRROR_SHOT", "uses": 19025}, {"moveId": "FLASH_CANNON", "uses": 10173}, {"moveId": "ZAP_CANNON", "uses": 6381}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "MIRROR_SHOT"], "score": 61}, {"speciesId": "articuno_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 456, "matchups": [{"opponent": "celebi", "rating": 776, "opRating": 223}, {"opponent": "buzzwole", "rating": 752, "opRating": 247}, {"opponent": "virizion", "rating": 752, "opRating": 247}, {"opponent": "zap<PERSON>_galarian", "rating": 658, "opRating": 341}, {"opponent": "kommo_o", "rating": 623, "opRating": 376}], "counters": [{"opponent": "mewtwo", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 392}, {"opponent": "zacian_hero", "rating": 427}, {"opponent": "gyarados", "rating": 471}, {"opponent": "swampert", "rating": 495}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 43863}, {"moveId": "CONFUSION", "uses": 32637}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 41524}, {"moveId": "ANCIENT_POWER", "uses": 18962}, {"moveId": "FUTURE_SIGHT", "uses": 16003}]}, "moveset": ["PSYCHO_CUT", "ANCIENT_POWER", "BRAVE_BIRD"], "score": 60.2}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 377, "matchups": [{"opponent": "rai<PERSON>u", "rating": 660, "opRating": 339}, {"opponent": "zapdos", "rating": 553, "opRating": 446}, {"opponent": "giratina_origin", "rating": 532}, {"opponent": "garcho<PERSON>", "rating": 516}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 509}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dragonite", "rating": 244}, {"opponent": "lugia", "rating": 390}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 51437}, {"moveId": "MUD_SLAP", "uses": 25063}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36182}, {"moveId": "STONE_EDGE", "uses": 12256}, {"moveId": "BULLDOZE", "uses": 11117}, {"moveId": "ANCIENT_POWER", "uses": 11082}, {"moveId": "RETURN", "uses": 5756}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 60}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 417, "matchups": [{"opponent": "nihilego", "rating": 832, "opRating": 167}, {"opponent": "magnezone", "rating": 797, "opRating": 202}, {"opponent": "genesect_shock", "rating": 574, "opRating": 425}, {"opponent": "zekrom", "rating": 534}, {"opponent": "sylveon", "rating": 530, "opRating": 469}], "counters": [{"opponent": "giratina_origin", "rating": 249}, {"opponent": "gyarados", "rating": 335}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 337}, {"opponent": "lugia", "rating": 350}, {"opponent": "dialga", "rating": 464}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38770}, {"moveId": "MUD_SLAP", "uses": 20171}, {"moveId": "METAL_CLAW", "uses": 17555}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32017}, {"moveId": "ROCK_SLIDE", "uses": 22232}, {"moveId": "IRON_HEAD", "uses": 13983}, {"moveId": "EARTHQUAKE", "uses": 8238}]}, "moveset": ["MUD_SHOT", "DRILL_RUN", "ROCK_SLIDE"], "score": 59.6}, {"speciesId": "heracross", "speciesName": "Heracross", "rating": 455, "matchups": [{"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 607, "opRating": 392}, {"opponent": "cobalion", "rating": 587, "opRating": 412}, {"opponent": "melmetal", "rating": 540, "opRating": 459}, {"opponent": "excadrill", "rating": 517}, {"opponent": "swampert", "rating": 511}], "counters": [{"opponent": "gyarados", "rating": 157}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "metagross", "rating": 186}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "dialga", "rating": 296}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 58275}, {"moveId": "STRUGGLE_BUG", "uses": 18225}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32816}, {"moveId": "MEGAHORN", "uses": 18326}, {"moveId": "ROCK_BLAST", "uses": 15062}, {"moveId": "EARTHQUAKE", "uses": 10228}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ROCK_BLAST"], "score": 59.4}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 444, "matchups": [{"opponent": "genesect_burn", "rating": 763, "opRating": 236}, {"opponent": "virizion", "rating": 725, "opRating": 274}, {"opponent": "metagross", "rating": 615}, {"opponent": "buzzwole", "rating": 615, "opRating": 384}, {"opponent": "sylveon", "rating": 596, "opRating": 403}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "dialga", "rating": 173}, {"opponent": "mewtwo", "rating": 177}, {"opponent": "zacian_hero", "rating": 390}, {"opponent": "grou<PERSON>", "rating": 396}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38996}, {"moveId": "WING_ATTACK", "uses": 37504}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 27027}, {"moveId": "OVERHEAT", "uses": 17022}, {"moveId": "ANCIENT_POWER", "uses": 16463}, {"moveId": "RETURN", "uses": 8192}, {"moveId": "FIRE_BLAST", "uses": 4888}, {"moveId": "HEAT_WAVE", "uses": 2947}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 59.4}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 396, "matchups": [{"opponent": "swampert", "rating": 750}, {"opponent": "rhyperior", "rating": 719, "opRating": 280}, {"opponent": "primarina", "rating": 657, "opRating": 342}, {"opponent": "tapu_fini", "rating": 654, "opRating": 345}, {"opponent": "excadrill", "rating": 567}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 307}, {"opponent": "mewtwo", "rating": 315}, {"opponent": "zacian_hero", "rating": 315}, {"opponent": "gyarados", "rating": 342}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41891}, {"moveId": "BITE", "uses": 34609}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30946}, {"moveId": "STONE_EDGE", "uses": 17849}, {"moveId": "EARTHQUAKE", "uses": 16339}, {"moveId": "SAND_TOMB", "uses": 7864}, {"moveId": "SOLAR_BEAM", "uses": 3524}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 59.2}, {"speciesId": "rai<PERSON>u", "speciesName": "Raikou", "rating": 485, "matchups": [{"opponent": "zap<PERSON>_shadow", "rating": 653, "opRating": 346}, {"opponent": "genesect_shock", "rating": 626, "opRating": 373}, {"opponent": "gyarados", "rating": 615}, {"opponent": "mew", "rating": 551, "opRating": 448}, {"opponent": "kyogre", "rating": 534, "opRating": 465}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 171}, {"opponent": "zacian_hero", "rating": 176}, {"opponent": "dragonite", "rating": 194}, {"opponent": "lugia", "rating": 409}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 41578}, {"moveId": "THUNDER_SHOCK", "uses": 34922}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 36342}, {"moveId": "SHADOW_BALL", "uses": 19388}, {"moveId": "RETURN", "uses": 8530}, {"moveId": "THUNDERBOLT", "uses": 6653}, {"moveId": "THUNDER", "uses": 5808}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 58.7}, {"speciesId": "charizard", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 383, "matchups": [{"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 693, "opRating": 306}, {"opponent": "tapu_bulu", "rating": 684, "opRating": 315}, {"opponent": "virizion", "rating": 571, "opRating": 428}, {"opponent": "metagross", "rating": 565}, {"opponent": "celebi", "rating": 562, "opRating": 437}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "giratina_origin", "rating": 241}, {"opponent": "garcho<PERSON>", "rating": 448}, {"opponent": "grou<PERSON>", "rating": 478}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 16927}, {"moveId": "DRAGON_BREATH", "uses": 16448}, {"moveId": "EMBER", "uses": 15587}, {"moveId": "WING_ATTACK", "uses": 15473}, {"moveId": "AIR_SLASH", "uses": 12103}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 29952}, {"moveId": "DRAGON_CLAW", "uses": 22477}, {"moveId": "RETURN", "uses": 6965}, {"moveId": "FLAMETHROWER", "uses": 6801}, {"moveId": "OVERHEAT", "uses": 6383}, {"moveId": "FIRE_BLAST", "uses": 3587}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 58.5}, {"speciesId": "metagross_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 439, "matchups": [{"opponent": "meloetta_aria", "rating": 654, "opRating": 345}, {"opponent": "florges", "rating": 627, "opRating": 372}, {"opponent": "nihilego", "rating": 598, "opRating": 401}, {"opponent": "goodra", "rating": 581, "opRating": 418}, {"opponent": "sylveon", "rating": 569, "opRating": 430}], "counters": [{"opponent": "mewtwo", "rating": 145}, {"opponent": "dialga", "rating": 152}, {"opponent": "dragonite", "rating": 279}, {"opponent": "zacian_hero", "rating": 364}, {"opponent": "lugia", "rating": 440}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 68365}, {"moveId": "ZEN_HEADBUTT", "uses": 8135}], "chargedMoves": [{"moveId": "METEOR_MASH", "uses": 33438}, {"moveId": "PSYCHIC", "uses": 19554}, {"moveId": "EARTHQUAKE", "uses": 18247}, {"moveId": "FLASH_CANNON", "uses": 5203}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "METEOR_MASH", "EARTHQUAKE"], "score": 58.5}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 462, "matchups": [{"opponent": "genesect_chill", "rating": 748, "opRating": 251}, {"opponent": "genesect_burn", "rating": 748, "opRating": 251}, {"opponent": "genesect_shock", "rating": 654, "opRating": 345}, {"opponent": "sylveon", "rating": 632, "opRating": 367}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 547}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "dialga", "rating": 160}, {"opponent": "giratina_origin", "rating": 193}, {"opponent": "lugia", "rating": 214}, {"opponent": "gyarados", "rating": 231}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 44039}, {"moveId": "FIRE_FANG", "uses": 32461}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 22114}, {"moveId": "OVERHEAT", "uses": 17102}, {"moveId": "IRON_HEAD", "uses": 12468}, {"moveId": "RETURN", "uses": 10715}, {"moveId": "FLAMETHROWER", "uses": 9145}, {"moveId": "FIRE_BLAST", "uses": 5009}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 58.1}, {"speciesId": "s<PERSON><PERSON>", "speciesName": "Scizor", "rating": 416, "matchups": [{"opponent": "celebi", "rating": 792, "opRating": 207}, {"opponent": "zarude", "rating": 668, "opRating": 331}, {"opponent": "cresselia", "rating": 636, "opRating": 363}, {"opponent": "meloetta_aria", "rating": 614, "opRating": 385}, {"opponent": "mewtwo_armored", "rating": 608, "opRating": 391}], "counters": [{"opponent": "giratina_origin", "rating": 215}, {"opponent": "dialga", "rating": 217}, {"opponent": "lugia", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 225}, {"opponent": "mewtwo", "rating": 388}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38913}, {"moveId": "BULLET_PUNCH", "uses": 37587}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30557}, {"moveId": "X_SCISSOR", "uses": 22603}, {"moveId": "IRON_HEAD", "uses": 15054}, {"moveId": "RETURN", "uses": 8426}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 57.7}, {"speciesId": "regice", "speciesName": "Regice", "rating": 454, "matchups": [{"opponent": "articuno", "rating": 662, "opRating": 337}, {"opponent": "gyarado<PERSON>_shadow", "rating": 656, "opRating": 343}, {"opponent": "magnezone", "rating": 616, "opRating": 383}, {"opponent": "avalugg", "rating": 555, "opRating": 444}, {"opponent": "gyarados", "rating": 526}], "counters": [{"opponent": "giratina_origin", "rating": 205}, {"opponent": "dragonite", "rating": 228}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "lugia", "rating": 400}, {"opponent": "excadrill", "rating": 467}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 42485}, {"moveId": "FROST_BREATH", "uses": 24500}, {"moveId": "ROCK_SMASH", "uses": 9528}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 25650}, {"moveId": "EARTHQUAKE", "uses": 19605}, {"moveId": "THUNDER", "uses": 16942}, {"moveId": "FOCUS_BLAST", "uses": 14223}]}, "moveset": ["LOCK_ON", "THUNDER", "EARTHQUAKE"], "score": 57.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 423, "matchups": [{"opponent": "magnezone_shadow", "rating": 709, "opRating": 290}, {"opponent": "snorlax", "rating": 569, "opRating": 430}, {"opponent": "nihilego", "rating": 540, "opRating": 459}, {"opponent": "regirock", "rating": 534, "opRating": 465}, {"opponent": "melmetal", "rating": 513, "opRating": 486}], "counters": [{"opponent": "metagross", "rating": 229}, {"opponent": "gyarados", "rating": 242}, {"opponent": "zacian_hero", "rating": 254}, {"opponent": "dialga", "rating": 271}, {"opponent": "garcho<PERSON>", "rating": 272}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 27139}, {"moveId": "MUD_SLAP", "uses": 18410}, {"moveId": "CHARM", "uses": 15786}, {"moveId": "TACKLE", "uses": 15165}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30793}, {"moveId": "EARTHQUAKE", "uses": 22415}, {"moveId": "PLAY_ROUGH", "uses": 11856}, {"moveId": "HEAVY_SLAM", "uses": 11565}]}, "moveset": ["COUNTER", "BODY_SLAM", "EARTHQUAKE"], "score": 56.9}, {"speciesId": "tapu_lele", "speciesName": "<PERSON><PERSON>", "rating": 406, "matchups": [{"opponent": "kommo_o", "rating": 681, "opRating": 318}, {"opponent": "dragonite", "rating": 649}, {"opponent": "goodra", "rating": 624, "opRating": 375}, {"opponent": "dragonite_shadow", "rating": 617, "opRating": 382}, {"opponent": "buzzwole", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "garcho<PERSON>", "rating": 241}, {"opponent": "grou<PERSON>", "rating": 247}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "gyarados", "rating": 327}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 57030}, {"moveId": "ASTONISH", "uses": 19470}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 28464}, {"moveId": "PSYSHOCK", "uses": 24970}, {"moveId": "FOCUS_BLAST", "uses": 14664}, {"moveId": "FUTURE_SIGHT", "uses": 8383}]}, "moveset": ["CONFUSION", "MOONBLAST", "PSYSHOCK"], "score": 56.6}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 419, "matchups": [{"opponent": "genesect_chill", "rating": 730, "opRating": 269}, {"opponent": "genesect_burn", "rating": 730, "opRating": 269}, {"opponent": "genesect_shock", "rating": 616, "opRating": 383}, {"opponent": "florges", "rating": 609, "opRating": 390}, {"opponent": "sylveon", "rating": 582, "opRating": 417}], "counters": [{"opponent": "mewtwo", "rating": 195}, {"opponent": "dialga", "rating": 198}, {"opponent": "giratina_origin", "rating": 215}, {"opponent": "lugia", "rating": 254}, {"opponent": "gyarados", "rating": 278}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45746}, {"moveId": "FIRE_FANG", "uses": 30754}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25496}, {"moveId": "OVERHEAT", "uses": 19638}, {"moveId": "IRON_HEAD", "uses": 14874}, {"moveId": "FLAMETHROWER", "uses": 10558}, {"moveId": "FIRE_BLAST", "uses": 5914}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "OVERHEAT"], "score": 56.4}, {"speciesId": "mewtwo_shadow", "speciesName": "<PERSON><PERSON>t<PERSON> (Shadow)", "rating": 460, "matchups": [{"opponent": "terrakion", "rating": 617, "opRating": 382}, {"opponent": "kommo_o", "rating": 578, "opRating": 421}, {"opponent": "buzzwole", "rating": 544, "opRating": 455}, {"opponent": "z<PERSON><PERSON><PERSON>_hero", "rating": 523, "opRating": 476}, {"opponent": "nihilego", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "lugia", "rating": 352}, {"opponent": "garcho<PERSON>", "rating": 431}, {"opponent": "zacian_hero", "rating": 442}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 449}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 48121}, {"moveId": "CONFUSION", "uses": 28379}], "chargedMoves": [{"moveId": "PSYSTRIKE", "uses": 19748}, {"moveId": "SHADOW_BALL", "uses": 11890}, {"moveId": "ICE_BEAM", "uses": 11521}, {"moveId": "FLAMETHROWER", "uses": 8762}, {"moveId": "THUNDERBOLT", "uses": 8481}, {"moveId": "FOCUS_BLAST", "uses": 7598}, {"moveId": "PSYCHIC", "uses": 4611}, {"moveId": "HYPER_BEAM", "uses": 3740}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "PSYSTRIKE", "SHADOW_BALL"], "score": 56.4}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 383, "matchups": [{"opponent": "rhyperior", "rating": 873, "opRating": 126}, {"opponent": "swampert", "rating": 780}, {"opponent": "primarina", "rating": 587, "opRating": 412}, {"opponent": "magnezone", "rating": 587, "opRating": 412}, {"opponent": "excadrill", "rating": 515}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "mewtwo", "rating": 257}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "gyarados", "rating": 280}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 41490}, {"moveId": "BITE", "uses": 35010}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 28477}, {"moveId": "STONE_EDGE", "uses": 16002}, {"moveId": "EARTHQUAKE", "uses": 15182}, {"moveId": "SAND_TOMB", "uses": 7376}, {"moveId": "RETURN", "uses": 6267}, {"moveId": "SOLAR_BEAM", "uses": 3141}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 56.2}, {"speciesId": "con<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 419, "matchups": [{"opponent": "tyranitar_shadow", "rating": 841, "opRating": 158}, {"opponent": "tyranitar", "rating": 721, "opRating": 278}, {"opponent": "melmetal", "rating": 564, "opRating": 435}, {"opponent": "yveltal", "rating": 543, "opRating": 456}, {"opponent": "regirock", "rating": 531, "opRating": 468}], "counters": [{"opponent": "gyarados", "rating": 188}, {"opponent": "metagross", "rating": 226}, {"opponent": "garcho<PERSON>", "rating": 246}, {"opponent": "dialga", "rating": 328}, {"opponent": "excadrill", "rating": 420}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46018}, {"moveId": "POISON_JAB", "uses": 30482}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 36174}, {"moveId": "STONE_EDGE", "uses": 30453}, {"moveId": "FOCUS_BLAST", "uses": 9869}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "STONE_EDGE"], "score": 56}, {"speciesId": "walrein_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 362, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 665, "opRating": 334}, {"opponent": "hippow<PERSON>_shadow", "rating": 660, "opRating": 339}, {"opponent": "steelix", "rating": 567, "opRating": 432}, {"opponent": "garcho<PERSON>", "rating": 537}, {"opponent": "moltres_galarian", "rating": 537, "opRating": 462}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "lugia", "rating": 288}, {"opponent": "gyarados", "rating": 314}, {"opponent": "giratina_origin", "rating": 368}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 38191}, {"moveId": "WATERFALL", "uses": 21162}, {"moveId": "FROST_BREATH", "uses": 17153}], "chargedMoves": [{"moveId": "ICICLE_SPEAR", "uses": 40046}, {"moveId": "EARTHQUAKE", "uses": 18396}, {"moveId": "WATER_PULSE", "uses": 9252}, {"moveId": "BLIZZARD", "uses": 8830}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "EARTHQUAKE"], "score": 56}, {"speciesId": "machamp", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 463, "matchups": [{"opponent": "tyranitar_shadow", "rating": 793, "opRating": 206}, {"opponent": "snor<PERSON>_shadow", "rating": 634, "opRating": 365}, {"opponent": "hydreigon", "rating": 583, "opRating": 416}, {"opponent": "melmetal", "rating": 580, "opRating": 419}, {"opponent": "cobalion", "rating": 548, "opRating": 451}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "gyarados", "rating": 157}, {"opponent": "dialga", "rating": 263}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "excadrill", "rating": 420}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 30052}, {"moveId": "KARATE_CHOP", "uses": 27490}, {"moveId": "BULLET_PUNCH", "uses": 19065}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20231}, {"moveId": "CROSS_CHOP", "uses": 13908}, {"moveId": "ROCK_SLIDE", "uses": 11830}, {"moveId": "PAYBACK", "uses": 8598}, {"moveId": "HEAVY_SLAM", "uses": 5457}, {"moveId": "DYNAMIC_PUNCH", "uses": 5336}, {"moveId": "STONE_EDGE", "uses": 4672}, {"moveId": "RETURN", "uses": 4027}, {"moveId": "SUBMISSION", "uses": 2456}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 55.8}, {"speciesId": "<PERSON>ras", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 388, "matchups": [{"opponent": "gliscor", "rating": 673, "opRating": 326}, {"opponent": "hippo<PERSON><PERSON>", "rating": 620, "opRating": 379}, {"opponent": "nidoqueen", "rating": 600, "opRating": 400}, {"opponent": "steelix", "rating": 561, "opRating": 438}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 510}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "giratina_origin", "rating": 199}, {"opponent": "zacian_hero", "rating": 300}, {"opponent": "dragonite", "rating": 300}, {"opponent": "garcho<PERSON>", "rating": 406}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 30466}, {"moveId": "WATER_GUN", "uses": 25971}, {"moveId": "FROST_BREATH", "uses": 20039}], "chargedMoves": [{"moveId": "SURF", "uses": 25526}, {"moveId": "ICE_BEAM", "uses": 18998}, {"moveId": "SKULL_BASH", "uses": 7697}, {"moveId": "DRAGON_PULSE", "uses": 7153}, {"moveId": "BLIZZARD", "uses": 6645}, {"moveId": "RETURN", "uses": 6503}, {"moveId": "HYDRO_PUMP", "uses": 4131}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 55.4}, {"speciesId": "aggron", "speciesName": "Aggron", "rating": 390, "matchups": [{"opponent": "articuno", "rating": 713, "opRating": 286}, {"opponent": "avalugg", "rating": 633, "opRating": 366}, {"opponent": "genesect", "rating": 592, "opRating": 407}, {"opponent": "kyurem", "rating": 554, "opRating": 445}, {"opponent": "lugia_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "dialga", "rating": 241}, {"opponent": "giratina_origin", "rating": 270}, {"opponent": "lugia", "rating": 426}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33912}, {"moveId": "SMACK_DOWN", "uses": 32574}, {"moveId": "IRON_TAIL", "uses": 10006}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 27243}, {"moveId": "HEAVY_SLAM", "uses": 18645}, {"moveId": "THUNDER", "uses": 12763}, {"moveId": "RETURN", "uses": 10407}, {"moveId": "ROCK_TOMB", "uses": 7334}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 54.8}, {"speciesId": "aggron_shadow", "speciesName": "A<PERSON><PERSON> (Shadow)", "rating": 368, "matchups": [{"opponent": "articuno", "rating": 652, "opRating": 347}, {"opponent": "nihilego", "rating": 611, "opRating": 388}, {"opponent": "lugia_shadow", "rating": 585, "opRating": 414}, {"opponent": "lugia", "rating": 525}, {"opponent": "giratina_altered", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 192}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "gyarados", "rating": 229}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "giratina_origin", "rating": 324}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 34195}, {"moveId": "SMACK_DOWN", "uses": 32931}, {"moveId": "IRON_TAIL", "uses": 9506}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 31433}, {"moveId": "HEAVY_SLAM", "uses": 21916}, {"moveId": "THUNDER", "uses": 14630}, {"moveId": "ROCK_TOMB", "uses": 8481}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "HEAVY_SLAM"], "score": 54.8}, {"speciesId": "ninetales_alolan", "speciesName": "Ninetales (Alolan)", "rating": 337, "matchups": [{"opponent": "salamence", "rating": 888, "opRating": 111}, {"opponent": "dragonite_shadow", "rating": 804, "opRating": 195}, {"opponent": "dragonite", "rating": 627}, {"opponent": "goodra", "rating": 621, "opRating": 378}, {"opponent": "kommo_o", "rating": 568, "opRating": 431}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "giratina_origin", "rating": 258}, {"opponent": "gyarados", "rating": 378}, {"opponent": "garcho<PERSON>", "rating": 485}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37581}, {"moveId": "CHARM", "uses": 21029}, {"moveId": "FEINT_ATTACK", "uses": 17929}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39773}, {"moveId": "PSYSHOCK", "uses": 12576}, {"moveId": "DAZZLING_GLEAM", "uses": 9504}, {"moveId": "ICE_BEAM", "uses": 8582}, {"moveId": "BLIZZARD", "uses": 5945}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "PSYSHOCK"], "score": 54.8}, {"speciesId": "sci<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 409, "matchups": [{"opponent": "gardevoir", "rating": 710, "opRating": 289}, {"opponent": "articuno", "rating": 668, "opRating": 331}, {"opponent": "gardevoir_shadow", "rating": 665, "opRating": 334}, {"opponent": "primarina", "rating": 617, "opRating": 382}, {"opponent": "sylveon", "rating": 563, "opRating": 436}], "counters": [{"opponent": "mewtwo", "rating": 210}, {"opponent": "dialga", "rating": 230}, {"opponent": "lugia", "rating": 273}, {"opponent": "gyarados", "rating": 283}, {"opponent": "zacian_hero", "rating": 395}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38502}, {"moveId": "BULLET_PUNCH", "uses": 37998}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 34172}, {"moveId": "X_SCISSOR", "uses": 25197}, {"moveId": "IRON_HEAD", "uses": 17052}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_PUNCH", "NIGHT_SLASH", "IRON_HEAD"], "score": 54.8}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 430, "matchups": [{"opponent": "articuno", "rating": 783, "opRating": 216}, {"opponent": "avalugg", "rating": 676, "opRating": 323}, {"opponent": "nihilego", "rating": 598, "opRating": 401}, {"opponent": "genesect_chill", "rating": 584, "opRating": 415}, {"opponent": "florges", "rating": 550, "opRating": 449}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "mewtwo", "rating": 231}, {"opponent": "lugia", "rating": 323}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 455}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 43811}, {"moveId": "METAL_CLAW", "uses": 32689}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 35919}, {"moveId": "DRILL_PECK", "uses": 19058}, {"moveId": "BLIZZARD", "uses": 10614}, {"moveId": "FLASH_CANNON", "uses": 7105}, {"moveId": "HYDRO_PUMP", "uses": 3853}]}, "moveset": ["WATERFALL", "HYDRO_CANNON", "FLASH_CANNON"], "score": 54.6}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 396, "matchups": [{"opponent": "genesect_burn", "rating": 739, "opRating": 260}, {"opponent": "virizion", "rating": 688, "opRating": 311}, {"opponent": "genesect_chill", "rating": 610, "opRating": 389}, {"opponent": "metagross", "rating": 591}, {"opponent": "sylveon", "rating": 510, "opRating": 489}], "counters": [{"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "dialga", "rating": 192}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "grou<PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38816}, {"moveId": "WING_ATTACK", "uses": 37684}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30393}, {"moveId": "OVERHEAT", "uses": 18828}, {"moveId": "ANCIENT_POWER", "uses": 18463}, {"moveId": "FIRE_BLAST", "uses": 5473}, {"moveId": "HEAT_WAVE", "uses": 3207}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "OVERHEAT"], "score": 54.6}, {"speciesId": "aurorus", "speciesName": "Au<PERSON><PERSON>", "rating": 393, "matchups": [{"opponent": "gliscor", "rating": 863, "opRating": 136}, {"opponent": "salamence", "rating": 782, "opRating": 217}, {"opponent": "salamence_shadow", "rating": 702, "opRating": 297}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 546, "opRating": 453}, {"opponent": "articuno", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "giratina_origin", "rating": 306}, {"opponent": "lugia", "rating": 433}, {"opponent": "garcho<PERSON>", "rating": 464}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 37898}, {"moveId": "ROCK_THROW", "uses": 20869}, {"moveId": "FROST_BREATH", "uses": 17728}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 38370}, {"moveId": "ANCIENT_POWER", "uses": 16820}, {"moveId": "THUNDERBOLT", "uses": 10714}, {"moveId": "BLIZZARD", "uses": 5852}, {"moveId": "HYPER_BEAM", "uses": 4818}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "THUNDERBOLT"], "score": 54.3}, {"speciesId": "raikou_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 425, "matchups": [{"opponent": "zapdos", "rating": 653, "opRating": 346}, {"opponent": "gyarados", "rating": 599}, {"opponent": "zap<PERSON>_shadow", "rating": 596, "opRating": 403}, {"opponent": "mew", "rating": 510, "opRating": 489}, {"opponent": "lugia_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 339}, {"opponent": "lugia", "rating": 497}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 42004}, {"moveId": "THUNDER_SHOCK", "uses": 34496}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 40197}, {"moveId": "SHADOW_BALL", "uses": 22540}, {"moveId": "THUNDERBOLT", "uses": 7196}, {"moveId": "THUNDER", "uses": 6356}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "SHADOW_BALL"], "score": 54.3}, {"speciesId": "haxorus", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 475, "matchups": [{"opponent": "obstagoon", "rating": 768, "opRating": 231}, {"opponent": "magnezone", "rating": 710, "opRating": 289}, {"opponent": "magnezone_shadow", "rating": 638, "opRating": 361}, {"opponent": "excadrill", "rating": 605}, {"opponent": "melmetal", "rating": 509, "opRating": 490}], "counters": [{"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "metagross", "rating": 145}, {"opponent": "dialga", "rating": 263}, {"opponent": "swampert", "rating": 402}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 38348}, {"moveId": "DRAGON_TAIL", "uses": 38152}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 25656}, {"moveId": "NIGHT_SLASH", "uses": 22516}, {"moveId": "SURF", "uses": 17709}, {"moveId": "EARTHQUAKE", "uses": 10638}]}, "moveset": ["COUNTER", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 54.1}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 392, "matchups": [{"opponent": "swampert", "rating": 726}, {"opponent": "vaporeon", "rating": 703, "opRating": 296}, {"opponent": "kyogre", "rating": 684, "opRating": 315}, {"opponent": "primarina", "rating": 681, "opRating": 318}, {"opponent": "excadrill", "rating": 592}], "counters": [{"opponent": "giratina_origin", "rating": 181}, {"opponent": "lugia", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "metagross", "rating": 235}, {"opponent": "gyarados", "rating": 407}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44474}, {"moveId": "INFESTATION", "uses": 32026}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22311}, {"moveId": "ROCK_SLIDE", "uses": 21945}, {"moveId": "SLUDGE_BOMB", "uses": 11548}, {"moveId": "RETURN", "uses": 8111}, {"moveId": "ANCIENT_POWER", "uses": 7841}, {"moveId": "SOLAR_BEAM", "uses": 4752}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 54.1}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 391, "matchups": [{"opponent": "swampert", "rating": 722}, {"opponent": "kyogre", "rating": 690}, {"opponent": "excadrill", "rating": 673}, {"opponent": "snorlax", "rating": 567, "opRating": 432}, {"opponent": "grou<PERSON>", "rating": 521}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "giratina_origin", "rating": 129}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "garcho<PERSON>", "rating": 305}, {"opponent": "gyarados", "rating": 430}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 43349}, {"moveId": "SMACK_DOWN", "uses": 24776}, {"moveId": "LOW_KICK", "uses": 8376}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 40623}, {"moveId": "ENERGY_BALL", "uses": 19863}, {"moveId": "GYRO_BALL", "uses": 10195}, {"moveId": "SOLAR_BEAM", "uses": 5797}]}, "moveset": ["VINE_WHIP", "SUPER_POWER", "ENERGY_BALL"], "score": 53.9}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 400, "matchups": [{"opponent": "giratina_origin", "rating": 705}, {"opponent": "tyranitar", "rating": 699, "opRating": 300}, {"opponent": "s<PERSON><PERSON>", "rating": 640, "opRating": 359}, {"opponent": "tyranitar_shadow", "rating": 547, "opRating": 452}, {"opponent": "mew", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "lugia", "rating": 204}, {"opponent": "metagross", "rating": 226}, {"opponent": "swampert", "rating": 315}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45662}, {"moveId": "TACKLE", "uses": 23604}, {"moveId": "LOW_KICK", "uses": 7247}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34094}, {"moveId": "STOMP", "uses": 22015}, {"moveId": "PAYBACK", "uses": 18052}, {"moveId": "DRAIN_PUNCH", "uses": 2359}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "PAYBACK"], "score": 53.7}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 369, "matchups": [{"opponent": "swampert", "rating": 812}, {"opponent": "swampert_shadow", "rating": 788, "opRating": 211}, {"opponent": "vaporeon", "rating": 610, "opRating": 389}, {"opponent": "tapu_fini", "rating": 546, "opRating": 453}, {"opponent": "excadrill", "rating": 523}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "zacian_hero", "rating": 251}, {"opponent": "garcho<PERSON>", "rating": 258}, {"opponent": "mewtwo", "rating": 265}, {"opponent": "gyarados", "rating": 311}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30508}, {"moveId": "BULLET_SEED", "uses": 29585}, {"moveId": "RAZOR_LEAF", "uses": 16389}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 51119}, {"moveId": "LAST_RESORT", "uses": 13854}, {"moveId": "ENERGY_BALL", "uses": 7221}, {"moveId": "SOLAR_BEAM", "uses": 4256}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 53.5}, {"speciesId": "mandibuzz", "speciesName": "Mandibuzz", "rating": 338, "matchups": [{"opponent": "gourgeist_super", "rating": 711, "opRating": 288}, {"opponent": "celebi", "rating": 688, "opRating": 311}, {"opponent": "mewtwo_armored", "rating": 625, "opRating": 374}, {"opponent": "mewtwo_shadow", "rating": 595, "opRating": 404}, {"opponent": "mewtwo", "rating": 548}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "metagross", "rating": 270}, {"opponent": "gyarados", "rating": 293}, {"opponent": "giratina_origin", "rating": 392}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44595}, {"moveId": "AIR_SLASH", "uses": 31905}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 28270}, {"moveId": "SHADOW_BALL", "uses": 18645}, {"moveId": "AERIAL_ACE", "uses": 17584}, {"moveId": "DARK_PULSE", "uses": 12093}]}, "moveset": ["SNARL", "FOUL_PLAY", "SHADOW_BALL"], "score": 53.5}, {"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 476, "matchups": [{"opponent": "obstagoon", "rating": 845, "opRating": 154}, {"opponent": "cobalion", "rating": 709, "opRating": 290}, {"opponent": "melmetal", "rating": 645, "opRating": 354}, {"opponent": "terrakion", "rating": 604, "opRating": 395}, {"opponent": "regirock", "rating": 590, "opRating": 409}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 117}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "giratina_origin", "rating": 163}, {"opponent": "gyarados", "rating": 360}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 38501}, {"moveId": "POISON_JAB", "uses": 30030}, {"moveId": "ROCK_SMASH", "uses": 7932}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 44041}, {"moveId": "X_SCISSOR", "uses": 18523}, {"moveId": "AERIAL_ACE", "uses": 13920}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "X_SCISSOR"], "score": 53.5}, {"speciesId": "beartic", "speciesName": "Bear<PERSON>", "rating": 361, "matchups": [{"opponent": "torterra", "rating": 688, "opRating": 311}, {"opponent": "nidoqueen", "rating": 572, "opRating": 427}, {"opponent": "steelix", "rating": 536, "opRating": 463}, {"opponent": "garcho<PERSON>", "rating": 530}, {"opponent": "moltres_galarian", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "lugia", "rating": 283}, {"opponent": "gyarados", "rating": 293}, {"opponent": "giratina_origin", "rating": 346}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 426}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52659}, {"moveId": "CHARM", "uses": 23841}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 36206}, {"moveId": "SURF", "uses": 27984}, {"moveId": "PLAY_ROUGH", "uses": 12290}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "SURF"], "score": 53.3}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 416, "matchups": [{"opponent": "primarina", "rating": 763, "opRating": 236}, {"opponent": "magnezone", "rating": 736, "opRating": 263}, {"opponent": "florges", "rating": 569, "opRating": 430}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 529}, {"opponent": "sylveon", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "lugia", "rating": 240}, {"opponent": "dragonite", "rating": 375}, {"opponent": "gyarados", "rating": 386}, {"opponent": "zacian_hero", "rating": 442}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 54081}, {"moveId": "BITE", "uses": 22419}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 20493}, {"moveId": "EARTH_POWER", "uses": 19278}, {"moveId": "STONE_EDGE", "uses": 14096}, {"moveId": "EARTHQUAKE", "uses": 8298}, {"moveId": "SLUDGE_WAVE", "uses": 8249}, {"moveId": "RETURN", "uses": 6265}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 53.3}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 348, "matchups": [{"opponent": "gourgeist_super", "rating": 748, "opRating": 251}, {"opponent": "trevenant", "rating": 672, "opRating": 327}, {"opponent": "giratina_origin", "rating": 567}, {"opponent": "cresselia", "rating": 548, "opRating": 451}, {"opponent": "golisopod", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "gyarados", "rating": 247}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "metagross", "rating": 328}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 66136}, {"moveId": "ZEN_HEADBUTT", "uses": 10364}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35298}, {"moveId": "SHADOW_BALL", "uses": 17280}, {"moveId": "EARTHQUAKE", "uses": 13377}, {"moveId": "SOLAR_BEAM", "uses": 6581}, {"moveId": "HYPER_BEAM", "uses": 3916}]}, "moveset": ["LICK", "BODY_SLAM", "SHADOW_BALL"], "score": 52.9}, {"speciesId": "muk_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 406, "matchups": [{"opponent": "celebi", "rating": 673, "opRating": 326}, {"opponent": "tapu_bulu", "rating": 639, "opRating": 360}, {"opponent": "zarude", "rating": 552, "opRating": 447}, {"opponent": "mewtwo", "rating": 543}, {"opponent": "golisopod", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "dragonite", "rating": 194}, {"opponent": "gyarados", "rating": 203}, {"opponent": "zacian_hero", "rating": 289}, {"opponent": "giratina_origin", "rating": 364}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29716}, {"moveId": "SNARL", "uses": 28296}, {"moveId": "BITE", "uses": 18482}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 43640}, {"moveId": "SLUDGE_WAVE", "uses": 19047}, {"moveId": "GUNK_SHOT", "uses": 7380}, {"moveId": "ACID_SPRAY", "uses": 6436}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "SLUDGE_WAVE"], "score": 52.7}, {"speciesId": "samu<PERSON>t", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 388, "matchups": [{"opponent": "heatran", "rating": 644, "opRating": 355}, {"opponent": "entei_shadow", "rating": 623, "opRating": 376}, {"opponent": "hippo<PERSON><PERSON>", "rating": 577, "opRating": 422}, {"opponent": "entei", "rating": 536, "opRating": 463}, {"opponent": "steelix", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 168}, {"opponent": "zacian_hero", "rating": 239}, {"opponent": "mewtwo", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 286}, {"opponent": "metagross", "rating": 331}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 43498}, {"moveId": "WATERFALL", "uses": 33002}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 33888}, {"moveId": "RAZOR_SHELL", "uses": 14296}, {"moveId": "MEGAHORN", "uses": 13756}, {"moveId": "BLIZZARD", "uses": 10948}, {"moveId": "HYDRO_PUMP", "uses": 3551}]}, "moveset": ["FURY_CUTTER", "HYDRO_CANNON", "RAZOR_SHELL"], "score": 52.7}, {"speciesId": "darmanitan_galarian_standard", "speciesName": "Dar<PERSON><PERSON> (Galarian)", "rating": 333, "matchups": [{"opponent": "gliscor_shadow", "rating": 891, "opRating": 108}, {"opponent": "gliscor", "rating": 742, "opRating": 257}, {"opponent": "salamence_shadow", "rating": 730, "opRating": 269}, {"opponent": "salamence", "rating": 723, "opRating": 276}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 711, "opRating": 288}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "giratina_origin", "rating": 195}, {"opponent": "lugia", "rating": 280}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "dragonite", "rating": 390}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 43439}, {"moveId": "TACKLE", "uses": 33061}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30936}, {"moveId": "ICE_PUNCH", "uses": 18579}, {"moveId": "SUPER_POWER", "uses": 18554}, {"moveId": "OVERHEAT", "uses": 8407}]}, "moveset": ["ICE_FANG", "AVALANCHE", "ICE_PUNCH"], "score": 52.5}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 377, "matchups": [{"opponent": "primarina", "rating": 755, "opRating": 244}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 639}, {"opponent": "zacian_hero", "rating": 618}, {"opponent": "florges", "rating": 551, "opRating": 448}, {"opponent": "nihilego", "rating": 526, "opRating": 473}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "gyarados", "rating": 247}, {"opponent": "lugia", "rating": 266}, {"opponent": "dragonite", "rating": 279}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56720}, {"moveId": "BITE", "uses": 19780}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22456}, {"moveId": "EARTH_POWER", "uses": 20731}, {"moveId": "STONE_EDGE", "uses": 15237}, {"moveId": "SLUDGE_WAVE", "uses": 8963}, {"moveId": "EARTHQUAKE", "uses": 8919}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "POISON_FANG", "EARTH_POWER"], "score": 52.5}, {"speciesId": "umbreon", "speciesName": "Umbreon", "rating": 342, "matchups": [{"opponent": "espeon", "rating": 726, "opRating": 273}, {"opponent": "mewtwo_shadow", "rating": 597, "opRating": 402}, {"opponent": "celebi", "rating": 577, "opRating": 422}, {"opponent": "hoopa_unbound", "rating": 551, "opRating": 448}, {"opponent": "mewtwo", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 154}, {"opponent": "lugia", "rating": 261}, {"opponent": "metagross", "rating": 267}, {"opponent": "gyarados", "rating": 288}, {"opponent": "giratina_origin", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42126}, {"moveId": "FEINT_ATTACK", "uses": 34374}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 33517}, {"moveId": "PSYCHIC", "uses": 15069}, {"moveId": "DARK_PULSE", "uses": 14252}, {"moveId": "LAST_RESORT", "uses": 13652}]}, "moveset": ["SNARL", "FOUL_PLAY", "PSYCHIC"], "score": 52.5}, {"speciesId": "escavalier", "speciesName": "Esca<PERSON>ier", "rating": 441, "matchups": [{"opponent": "steelix", "rating": 694, "opRating": 305}, {"opponent": "celebi", "rating": 678, "opRating": 321}, {"opponent": "avalugg", "rating": 614, "opRating": 385}, {"opponent": "hydreigon", "rating": 522, "opRating": 477}, {"opponent": "cresselia", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "zacian_hero", "rating": 144}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "metagross", "rating": 229}, {"opponent": "dialga", "rating": 271}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46223}, {"moveId": "BUG_BITE", "uses": 30277}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 30573}, {"moveId": "MEGAHORN", "uses": 27416}, {"moveId": "AERIAL_ACE", "uses": 14677}, {"moveId": "ACID_SPRAY", "uses": 3958}]}, "moveset": ["COUNTER", "DRILL_RUN", "MEGAHORN"], "score": 52.3}, {"speciesId": "machamp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 424, "matchups": [{"opponent": "tyranitar", "rating": 793, "opRating": 206}, {"opponent": "tyranitar_shadow", "rating": 790, "opRating": 209}, {"opponent": "snorlax", "rating": 634, "opRating": 365}, {"opponent": "regirock", "rating": 580, "opRating": 419}, {"opponent": "snor<PERSON>_shadow", "rating": 559, "opRating": 440}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "metagross", "rating": 165}, {"opponent": "gyarados", "rating": 268}, {"opponent": "dialga", "rating": 307}, {"opponent": "excadrill", "rating": 490}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 30066}, {"moveId": "KARATE_CHOP", "uses": 27907}, {"moveId": "BULLET_PUNCH", "uses": 18515}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 21206}, {"moveId": "CROSS_CHOP", "uses": 14620}, {"moveId": "ROCK_SLIDE", "uses": 12554}, {"moveId": "PAYBACK", "uses": 9131}, {"moveId": "HEAVY_SLAM", "uses": 5821}, {"moveId": "DYNAMIC_PUNCH", "uses": 5706}, {"moveId": "STONE_EDGE", "uses": 4923}, {"moveId": "SUBMISSION", "uses": 2509}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "ROCK_SLIDE"], "score": 52.3}, {"speciesId": "darkrai", "speciesName": "Darkrai", "rating": 373, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 687, "opRating": 312}, {"opponent": "mew", "rating": 665, "opRating": 334}, {"opponent": "steelix", "rating": 652, "opRating": 347}, {"opponent": "victini", "rating": 608, "opRating": 391}, {"opponent": "mewtwo", "rating": 598}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "lugia", "rating": 338}, {"opponent": "gyarados", "rating": 368}, {"opponent": "giratina_origin", "rating": 478}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45921}, {"moveId": "FEINT_ATTACK", "uses": 30579}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 26752}, {"moveId": "SHADOW_BALL", "uses": 20460}, {"moveId": "SLUDGE_BOMB", "uses": 15034}, {"moveId": "FOCUS_BLAST", "uses": 14145}]}, "moveset": ["SNARL", "DARK_PULSE", "FOCUS_BLAST"], "score": 52}, {"speciesId": "hoopa_unbound", "speciesName": "<PERSON><PERSON><PERSON> (Unbound)", "rating": 394, "matchups": [{"opponent": "mewtwo_armored", "rating": 691, "opRating": 308}, {"opponent": "muk", "rating": 643, "opRating": 356}, {"opponent": "machamp", "rating": 617, "opRating": 382}, {"opponent": "nidoqueen", "rating": 595, "opRating": 404}, {"opponent": "sneasler", "rating": 585, "opRating": 414}], "counters": [{"opponent": "dialga", "rating": 130}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "giratina_origin", "rating": 211}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "gyarados", "rating": 327}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 56592}, {"moveId": "ASTONISH", "uses": 19908}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 30851}, {"moveId": "SHADOW_BALL", "uses": 23160}, {"moveId": "PSYCHIC", "uses": 22434}]}, "moveset": ["CONFUSION", "DARK_PULSE", "SHADOW_BALL"], "score": 52}, {"speciesId": "lap<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 362, "matchups": [{"opponent": "hippow<PERSON>_shadow", "rating": 708, "opRating": 291}, {"opponent": "glaceon", "rating": 693, "opRating": 306}, {"opponent": "nidoqueen", "rating": 651, "opRating": 348}, {"opponent": "hippo<PERSON><PERSON>", "rating": 591, "opRating": 408}, {"opponent": "steelix", "rating": 571, "opRating": 428}], "counters": [{"opponent": "dialga", "rating": 135}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "dragonite", "rating": 255}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "garcho<PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 31279}, {"moveId": "WATER_GUN", "uses": 25065}, {"moveId": "FROST_BREATH", "uses": 20212}], "chargedMoves": [{"moveId": "SURF", "uses": 27767}, {"moveId": "ICE_BEAM", "uses": 20569}, {"moveId": "SKULL_BASH", "uses": 8566}, {"moveId": "DRAGON_PULSE", "uses": 7894}, {"moveId": "BLIZZARD", "uses": 7119}, {"moveId": "HYDRO_PUMP", "uses": 4376}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ICE_SHARD", "SURF", "ICE_BEAM"], "score": 52}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 371, "matchups": [{"opponent": "swampert", "rating": 646}, {"opponent": "vaporeon", "rating": 646, "opRating": 353}, {"opponent": "regice", "rating": 613, "opRating": 386}, {"opponent": "magnezone", "rating": 587, "opRating": 412}, {"opponent": "swampert_shadow", "rating": 556, "opRating": 443}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "gyarados", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 234}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "giratina_origin", "rating": 318}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 43018}, {"moveId": "BULLET_SEED", "uses": 33482}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 23513}, {"moveId": "DRAGON_PULSE", "uses": 16642}, {"moveId": "DRACO_METEOR", "uses": 14876}, {"moveId": "SOLAR_BEAM", "uses": 10702}, {"moveId": "RETURN", "uses": 10616}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 51.6}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 361, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 652, "opRating": 347}, {"opponent": "vaporeon", "rating": 618, "opRating": 381}, {"opponent": "mew", "rating": 572, "opRating": 427}, {"opponent": "swampert", "rating": 556}, {"opponent": "regice", "rating": 556, "opRating": 443}], "counters": [{"opponent": "mewtwo", "rating": 166}, {"opponent": "dialga", "rating": 192}, {"opponent": "giratina_origin", "rating": 239}, {"opponent": "dragonite", "rating": 244}, {"opponent": "garcho<PERSON>", "rating": 269}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 42450}, {"moveId": "BULLET_SEED", "uses": 34050}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26942}, {"moveId": "DRAGON_PULSE", "uses": 19513}, {"moveId": "DRACO_METEOR", "uses": 17440}, {"moveId": "SOLAR_BEAM", "uses": 12504}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 51.6}, {"speciesId": "gigalith", "speciesName": "Gigalith", "rating": 390, "matchups": [{"opponent": "moltres", "rating": 769, "opRating": 230}, {"opponent": "articuno", "rating": 696, "opRating": 303}, {"opponent": "articuno_shadow", "rating": 696, "opRating": 303}, {"opponent": "ho_oh", "rating": 662, "opRating": 337}, {"opponent": "ho_oh_shadow", "rating": 623, "opRating": 376}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "giratina_origin", "rating": 217}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "lugia", "rating": 316}, {"opponent": "gyarados", "rating": 373}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 43904}, {"moveId": "MUD_SLAP", "uses": 32596}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 31648}, {"moveId": "SUPER_POWER", "uses": 26363}, {"moveId": "HEAVY_SLAM", "uses": 10662}, {"moveId": "SOLAR_BEAM", "uses": 7784}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "SUPER_POWER"], "score": 51.4}, {"speciesId": "torn<PERSON><PERSON>_therian", "speciesName": "<PERSON><PERSON><PERSON> (Therian)", "rating": 367, "matchups": [{"opponent": "buzzwole", "rating": 717, "opRating": 282}, {"opponent": "virizion", "rating": 711, "opRating": 288}, {"opponent": "heracross", "rating": 694, "opRating": 305}, {"opponent": "sneasler", "rating": 611, "opRating": 388}, {"opponent": "golisopod", "rating": 552, "opRating": 447}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "giratina_origin", "rating": 193}, {"opponent": "mewtwo", "rating": 223}, {"opponent": "garcho<PERSON>", "rating": 227}, {"opponent": "zacian_hero", "rating": 228}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 58580}, {"moveId": "ASTONISH", "uses": 17920}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 26501}, {"moveId": "PSYCHIC", "uses": 21145}, {"moveId": "FOCUS_BLAST", "uses": 20278}, {"moveId": "HEAT_WAVE", "uses": 8544}]}, "moveset": ["GUST", "HURRICANE", "PSYCHIC"], "score": 51.4}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 359, "matchups": [{"opponent": "rhyperior", "rating": 833, "opRating": 166}, {"opponent": "swampert", "rating": 818}, {"opponent": "swampert_shadow", "rating": 785, "opRating": 214}, {"opponent": "vaporeon", "rating": 648, "opRating": 351}, {"opponent": "primarina", "rating": 619, "opRating": 380}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 164}, {"opponent": "gyarados", "rating": 231}, {"opponent": "garcho<PERSON>", "rating": 295}, {"opponent": "zacian_hero", "rating": 306}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42739}, {"moveId": "ASTONISH", "uses": 33761}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34130}, {"moveId": "ENERGY_BALL", "uses": 21335}, {"moveId": "SHADOW_SNEAK", "uses": 21076}]}, "moveset": ["RAZOR_LEAF", "BRAVE_BIRD", "ENERGY_BALL"], "score": 51.2}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 454, "matchups": [{"opponent": "celebi", "rating": 738, "opRating": 261}, {"opponent": "tapu_fini", "rating": 615, "opRating": 384}, {"opponent": "gardevoir_shadow", "rating": 587, "opRating": 412}, {"opponent": "primarina", "rating": 570, "opRating": 429}, {"opponent": "sylveon", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "giratina_origin", "rating": 179}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "gyarados", "rating": 280}, {"opponent": "zacian_hero", "rating": 424}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 39077}, {"moveId": "POISON_STING", "uses": 37423}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17978}, {"moveId": "DARK_PULSE", "uses": 17807}, {"moveId": "ICE_BEAM", "uses": 13691}, {"moveId": "SHADOW_BALL", "uses": 13485}, {"moveId": "SLUDGE_BOMB", "uses": 13432}]}, "moveset": ["POISON_JAB", "AQUA_TAIL", "SHADOW_BALL"], "score": 51.2}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 389, "matchups": [{"opponent": "tyranitar_shadow", "rating": 847, "opRating": 152}, {"opponent": "hydreigon", "rating": 707, "opRating": 292}, {"opponent": "tyranitar", "rating": 681, "opRating": 318}, {"opponent": "kyurem", "rating": 563, "opRating": 436}, {"opponent": "avalugg", "rating": 563, "opRating": 436}], "counters": [{"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "gyarados", "rating": 157}, {"opponent": "grou<PERSON>", "rating": 179}, {"opponent": "metagross", "rating": 191}, {"opponent": "dialga", "rating": 434}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 45237}, {"moveId": "BULLET_PUNCH", "uses": 31263}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34752}, {"moveId": "SHADOW_BALL", "uses": 18793}, {"moveId": "FLASH_CANNON", "uses": 8944}, {"moveId": "AURA_SPHERE", "uses": 7804}, {"moveId": "POWER_UP_PUNCH", "uses": 6137}]}, "moveset": ["COUNTER", "POWER_UP_PUNCH", "SHADOW_BALL"], "score": 51}, {"speciesId": "suicune", "speciesName": "Suicune", "rating": 418, "matchups": [{"opponent": "entei", "rating": 631, "opRating": 368}, {"opponent": "hippo<PERSON><PERSON>", "rating": 621, "opRating": 378}, {"opponent": "victini", "rating": 609, "opRating": 390}, {"opponent": "heatran", "rating": 567, "opRating": 432}, {"opponent": "excadrill", "rating": 549}], "counters": [{"opponent": "dialga", "rating": 59}, {"opponent": "lugia", "rating": 242}, {"opponent": "metagross", "rating": 250}, {"opponent": "gyarados", "rating": 273}, {"opponent": "giratina_origin", "rating": 290}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7241}, {"moveId": "ICE_FANG", "uses": 6364}, {"moveId": "EXTRASENSORY", "uses": 4693}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4590}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4513}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4110}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3994}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3950}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3854}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3681}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3666}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3555}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3479}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3431}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3151}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3133}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3114}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2996}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2886}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26479}, {"moveId": "HYDRO_PUMP", "uses": 15219}, {"moveId": "RETURN", "uses": 12646}, {"moveId": "BUBBLE_BEAM", "uses": 11313}, {"moveId": "WATER_PULSE", "uses": 10905}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 51}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 368, "matchups": [{"opponent": "primarina", "rating": 767, "opRating": 232}, {"opponent": "gardevoir_shadow", "rating": 720, "opRating": 279}, {"opponent": "tapu_fini", "rating": 697, "opRating": 302}, {"opponent": "kyogre", "rating": 633, "opRating": 366}, {"opponent": "zacian_hero", "rating": 511}], "counters": [{"opponent": "lugia", "rating": 161}, {"opponent": "giratina_origin", "rating": 177}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "swampert", "rating": 360}, {"opponent": "gyarados", "rating": 384}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53195}, {"moveId": "RAZOR_LEAF", "uses": 23305}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 37999}, {"moveId": "SLUDGE_BOMB", "uses": 19370}, {"moveId": "RETURN", "uses": 9515}, {"moveId": "PETAL_BLIZZARD", "uses": 5212}, {"moveId": "SOLAR_BEAM", "uses": 4238}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 50.8}, {"speciesId": "thundurus_incarnate", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Incarnate)", "rating": 386, "matchups": [{"opponent": "vaporeon", "rating": 647, "opRating": 352}, {"opponent": "yveltal", "rating": 585, "opRating": 414}, {"opponent": "primarina", "rating": 576, "opRating": 423}, {"opponent": "gyarados", "rating": 555}, {"opponent": "zap<PERSON>_galarian", "rating": 511, "opRating": 488}], "counters": [{"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 176}, {"opponent": "zacian_hero", "rating": 248}, {"opponent": "metagross", "rating": 372}, {"opponent": "lugia", "rating": 373}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58410}, {"moveId": "ASTONISH", "uses": 18090}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25657}, {"moveId": "THUNDER_PUNCH", "uses": 23889}, {"moveId": "BRICK_BREAK", "uses": 19297}, {"moveId": "THUNDER", "uses": 7717}]}, "moveset": ["THUNDER_SHOCK", "CRUNCH", "THUNDER"], "score": 50.6}, {"speciesId": "tyrantrum", "speciesName": "Tyrantrum", "rating": 387, "matchups": [{"opponent": "moltres", "rating": 620, "opRating": 379}, {"opponent": "entei", "rating": 609, "opRating": 390}, {"opponent": "over<PERSON><PERSON>l", "rating": 609, "opRating": 390}, {"opponent": "zapdos", "rating": 557, "opRating": 442}, {"opponent": "ho_oh", "rating": 528, "opRating": 471}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "gyarados", "rating": 203}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "mewtwo", "rating": 239}, {"opponent": "giratina_origin", "rating": 360}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 35600}, {"moveId": "ROCK_THROW", "uses": 24000}, {"moveId": "CHARM", "uses": 16875}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23605}, {"moveId": "CRUNCH", "uses": 20687}, {"moveId": "OUTRAGE", "uses": 17974}, {"moveId": "EARTHQUAKE", "uses": 14367}]}, "moveset": ["DRAGON_TAIL", "STONE_EDGE", "CRUNCH"], "score": 50.6}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 324, "matchups": [{"opponent": "salamence", "rating": 709, "opRating": 290}, {"opponent": "salamence_shadow", "rating": 604, "opRating": 395}, {"opponent": "swampert", "rating": 594}, {"opponent": "landorus_incarnate", "rating": 591, "opRating": 408}, {"opponent": "garcho<PERSON>", "rating": 534}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "lugia", "rating": 185}, {"opponent": "mewtwo", "rating": 208}, {"opponent": "gyarados", "rating": 244}, {"opponent": "giratina_origin", "rating": 252}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 54398}, {"moveId": "RAZOR_LEAF", "uses": 22102}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 39320}, {"moveId": "ENERGY_BALL", "uses": 14453}, {"moveId": "OUTRAGE", "uses": 10300}, {"moveId": "RETURN", "uses": 6599}, {"moveId": "BLIZZARD", "uses": 5986}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 50.4}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 370, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 637, "opRating": 362}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 624, "opRating": 375}, {"opponent": "hippow<PERSON>_shadow", "rating": 583, "opRating": 416}, {"opponent": "nidoqueen", "rating": 553, "opRating": 446}, {"opponent": "articuno", "rating": 520, "opRating": 479}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "giratina_origin", "rating": 211}, {"opponent": "lugia", "rating": 311}, {"opponent": "garcho<PERSON>", "rating": 415}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 48028}, {"moveId": "FROST_BREATH", "uses": 28472}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38913}, {"moveId": "ICY_WIND", "uses": 13522}, {"moveId": "LAST_RESORT", "uses": 9566}, {"moveId": "ICE_BEAM", "uses": 8742}, {"moveId": "WATER_PULSE", "uses": 5683}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 50.4}, {"speciesId": "feraligatr", "speciesName": "Feraligatr", "rating": 373, "matchups": [{"opponent": "entei", "rating": 724, "opRating": 275}, {"opponent": "heatran", "rating": 685, "opRating": 314}, {"opponent": "steelix", "rating": 654, "opRating": 345}, {"opponent": "articuno", "rating": 606, "opRating": 393}, {"opponent": "ho_oh", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "metagross", "rating": 456}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22936}, {"moveId": "WATERFALL", "uses": 21504}, {"moveId": "ICE_FANG", "uses": 20382}, {"moveId": "BITE", "uses": 11715}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 34409}, {"moveId": "CRUNCH", "uses": 17796}, {"moveId": "ICE_BEAM", "uses": 14296}, {"moveId": "RETURN", "uses": 6371}, {"moveId": "HYDRO_PUMP", "uses": 3695}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 50.2}, {"speciesId": "muk", "speciesName": "Mu<PERSON>", "rating": 407, "matchups": [{"opponent": "primarina", "rating": 762, "opRating": 237}, {"opponent": "gardevoir_shadow", "rating": 750, "opRating": 250}, {"opponent": "florges", "rating": 596, "opRating": 403}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 552}, {"opponent": "sylveon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "dragonite", "rating": 194}, {"opponent": "lugia", "rating": 204}, {"opponent": "gyarados", "rating": 445}, {"opponent": "zacian_hero", "rating": 459}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26614}, {"moveId": "LICK", "uses": 20116}, {"moveId": "INFESTATION", "uses": 19445}, {"moveId": "ACID", "uses": 10395}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 23317}, {"moveId": "THUNDER_PUNCH", "uses": 20192}, {"moveId": "SLUDGE_WAVE", "uses": 13017}, {"moveId": "RETURN", "uses": 10274}, {"moveId": "GUNK_SHOT", "uses": 5130}, {"moveId": "ACID_SPRAY", "uses": 4438}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 49.5}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 342, "matchups": [{"opponent": "virizion", "rating": 676, "opRating": 323}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 658, "opRating": 341}, {"opponent": "avalugg", "rating": 631, "opRating": 368}, {"opponent": "genesect_chill", "rating": 612, "opRating": 387}, {"opponent": "genesect_burn", "rating": 612, "opRating": 387}], "counters": [{"opponent": "lugia", "rating": 214}, {"opponent": "metagross", "rating": 220}, {"opponent": "dialga", "rating": 312}, {"opponent": "zacian_hero", "rating": 323}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 376}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 47130}, {"moveId": "SCRATCH", "uses": 18895}, {"moveId": "ZEN_HEADBUTT", "uses": 10513}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 30396}, {"moveId": "PSYCHIC", "uses": 26626}, {"moveId": "FLAMETHROWER", "uses": 12812}, {"moveId": "FIRE_BLAST", "uses": 6827}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "PSYCHIC"], "score": 49.3}, {"speciesId": "sir<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 407, "matchups": [{"opponent": "obstagoon", "rating": 751, "opRating": 248}, {"opponent": "regice", "rating": 686, "opRating": 313}, {"opponent": "snorlax", "rating": 575, "opRating": 424}, {"opponent": "regirock", "rating": 568, "opRating": 431}, {"opponent": "snor<PERSON>_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "metagross", "rating": 145}, {"opponent": "dialga", "rating": 263}, {"opponent": "gyarados", "rating": 327}, {"opponent": "excadrill", "rating": 444}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44966}, {"moveId": "FURY_CUTTER", "uses": 31534}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24568}, {"moveId": "LEAF_BLADE", "uses": 19512}, {"moveId": "NIGHT_SLASH", "uses": 17421}, {"moveId": "BRAVE_BIRD", "uses": 14976}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 49.3}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 355, "matchups": [{"opponent": "swampert", "rating": 681}, {"opponent": "kyogre", "rating": 644, "opRating": 355}, {"opponent": "primarina", "rating": 621, "opRating": 378}, {"opponent": "excadrill", "rating": 519}, {"opponent": "mew", "rating": 512, "opRating": 487}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 182}, {"opponent": "giratina_origin", "rating": 227}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "gyarados", "rating": 471}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44690}, {"moveId": "INFESTATION", "uses": 31810}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 24863}, {"moveId": "ROCK_SLIDE", "uses": 24493}, {"moveId": "SLUDGE_BOMB", "uses": 13072}, {"moveId": "ANCIENT_POWER", "uses": 8864}, {"moveId": "SOLAR_BEAM", "uses": 5344}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 49.1}, {"speciesId": "cryogonal", "speciesName": "Cryogonal", "rating": 348, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 712, "opRating": 287}, {"opponent": "gliscor", "rating": 601, "opRating": 398}, {"opponent": "gliscor_shadow", "rating": 590, "opRating": 409}, {"opponent": "nidoqueen", "rating": 546, "opRating": 453}, {"opponent": "hippo<PERSON><PERSON>", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "lugia", "rating": 214}, {"opponent": "gyarados", "rating": 275}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "garcho<PERSON>", "rating": 417}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 44973}, {"moveId": "FROST_BREATH", "uses": 31527}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39078}, {"moveId": "AURORA_BEAM", "uses": 19594}, {"moveId": "SOLAR_BEAM", "uses": 10054}, {"moveId": "WATER_PULSE", "uses": 7782}]}, "moveset": ["ICE_SHARD", "NIGHT_SLASH", "AURORA_BEAM"], "score": 48.9}, {"speciesId": "mr_rime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 333, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 656, "opRating": 343}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 639, "opRating": 360}, {"opponent": "hippow<PERSON>_shadow", "rating": 587, "opRating": 412}, {"opponent": "celebi", "rating": 558, "opRating": 441}, {"opponent": "nidoqueen", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "lugia", "rating": 269}, {"opponent": "zacian_hero", "rating": 286}, {"opponent": "dragonite", "rating": 321}, {"opponent": "garcho<PERSON>", "rating": 377}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38635}, {"moveId": "CONFUSION", "uses": 31192}, {"moveId": "ZEN_HEADBUTT", "uses": 6671}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 30904}, {"moveId": "ICE_PUNCH", "uses": 26736}, {"moveId": "PSYCHIC", "uses": 15577}, {"moveId": "PSYBEAM", "uses": 3094}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "ICE_PUNCH"], "score": 48.9}, {"speciesId": "thundurus_therian", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Therian)", "rating": 348, "matchups": [{"opponent": "obstagoon", "rating": 682, "opRating": 317}, {"opponent": "golisopod", "rating": 594, "opRating": 405}, {"opponent": "metagross", "rating": 585}, {"opponent": "escavalier", "rating": 579, "opRating": 420}, {"opponent": "gyarados", "rating": 552}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "dragonite", "rating": 188}, {"opponent": "lugia", "rating": 216}, {"opponent": "zacian_hero", "rating": 228}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 57441}, {"moveId": "BITE", "uses": 19059}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27779}, {"moveId": "FOCUS_BLAST", "uses": 21844}, {"moveId": "SLUDGE_WAVE", "uses": 14847}, {"moveId": "THUNDER", "uses": 12016}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "FOCUS_BLAST"], "score": 48.9}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 348, "matchups": [{"opponent": "swampert", "rating": 737}, {"opponent": "rhyperior", "rating": 725, "opRating": 274}, {"opponent": "primarina", "rating": 722, "opRating": 277}, {"opponent": "kyogre", "rating": 698, "opRating": 301}, {"opponent": "swampert_shadow", "rating": 698, "opRating": 301}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "gyarados", "rating": 255}, {"opponent": "zacian_hero", "rating": 349}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 42780}, {"moveId": "ACID", "uses": 33720}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25849}, {"moveId": "MOONBLAST", "uses": 25581}, {"moveId": "PETAL_BLIZZARD", "uses": 17834}, {"moveId": "SOLAR_BEAM", "uses": 7063}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "SLUDGE_BOMB", "MOONBLAST"], "score": 48.9}, {"speciesId": "sandslash_alolan", "speciesName": "Sandslash (Alolan)", "rating": 369, "matchups": [{"opponent": "celebi", "rating": 631, "opRating": 368}, {"opponent": "articuno_galarian", "rating": 609, "opRating": 390}, {"opponent": "goodra", "rating": 567, "opRating": 432}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 551, "opRating": 448}, {"opponent": "articuno", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 226}, {"opponent": "gyarados", "rating": 262}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "lugia", "rating": 440}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32571}, {"moveId": "SHADOW_CLAW", "uses": 30506}, {"moveId": "METAL_CLAW", "uses": 13500}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 30548}, {"moveId": "BLIZZARD", "uses": 15851}, {"moveId": "BULLDOZE", "uses": 11472}, {"moveId": "GYRO_BALL", "uses": 9803}, {"moveId": "RETURN", "uses": 8967}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 48.7}, {"speciesId": "ampha<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 374, "matchups": [{"opponent": "gyarados", "rating": 629}, {"opponent": "primarina", "rating": 588, "opRating": 411}, {"opponent": "moltres_galarian", "rating": 564, "opRating": 435}, {"opponent": "gyarado<PERSON>_shadow", "rating": 516, "opRating": 483}, {"opponent": "magnezone", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "dragonite", "rating": 257}, {"opponent": "lugia", "rating": 273}, {"opponent": "zacian_hero", "rating": 283}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 52062}, {"moveId": "CHARGE_BEAM", "uses": 24438}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 21740}, {"moveId": "FOCUS_BLAST", "uses": 13038}, {"moveId": "DRAGON_PULSE", "uses": 11068}, {"moveId": "RETURN", "uses": 8789}, {"moveId": "POWER_GEM", "uses": 8015}, {"moveId": "THUNDER", "uses": 7001}, {"moveId": "ZAP_CANNON", "uses": 6805}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 48.5}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 351, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "swampert", "rating": 570}, {"opponent": "vaporeon", "rating": 570, "opRating": 429}, {"opponent": "swampert_shadow", "rating": 508, "opRating": 491}, {"opponent": "virizion", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 269}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "excadrill", "rating": 413}, {"opponent": "metagross", "rating": 462}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49581}, {"moveId": "RAZOR_LEAF", "uses": 26919}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26370}, {"moveId": "SEED_BOMB", "uses": 21687}, {"moveId": "FOUL_PLAY", "uses": 19767}, {"moveId": "FIRE_BLAST", "uses": 8624}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 48.5}, {"speciesId": "relicanth", "speciesName": "Relicanth", "rating": 375, "matchups": [{"opponent": "entei_shadow", "rating": 830, "opRating": 169}, {"opponent": "ho_oh_shadow", "rating": 773, "opRating": 226}, {"opponent": "entei", "rating": 746, "opRating": 253}, {"opponent": "ho_oh", "rating": 594}, {"opponent": "avalugg", "rating": 557, "opRating": 442}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "zacian_hero", "rating": 271}, {"opponent": "lugia", "rating": 295}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 362}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66108}, {"moveId": "ZEN_HEADBUTT", "uses": 10392}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 40567}, {"moveId": "ANCIENT_POWER", "uses": 29647}, {"moveId": "HYDRO_PUMP", "uses": 6322}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "ANCIENT_POWER"], "score": 48.5}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 347, "matchups": [{"opponent": "vaporeon", "rating": 646, "opRating": 353}, {"opponent": "swampert", "rating": 634}, {"opponent": "celebi", "rating": 612, "opRating": 387}, {"opponent": "swampert_shadow", "rating": 567, "opRating": 432}, {"opponent": "tapu_fini", "rating": 528, "opRating": 471}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 335}, {"opponent": "zacian_hero", "rating": 387}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 50289}, {"moveId": "SUCKER_PUNCH", "uses": 26211}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 29961}, {"moveId": "SEED_BOMB", "uses": 24040}, {"moveId": "FOUL_PLAY", "uses": 22518}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SEED_BOMB"], "score": 48.5}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 387, "matchups": [{"opponent": "nidoqueen", "rating": 716, "opRating": 283}, {"opponent": "magnezone", "rating": 609, "opRating": 390}, {"opponent": "excadrill", "rating": 573}, {"opponent": "cobalion", "rating": 530, "opRating": 469}, {"opponent": "buzzwole", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 92}, {"opponent": "garcho<PERSON>", "rating": 133}, {"opponent": "giratina_origin", "rating": 201}, {"opponent": "gyarados", "rating": 211}, {"opponent": "metagross", "rating": 232}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38776}, {"moveId": "WING_ATTACK", "uses": 37724}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 27758}, {"moveId": "EARTHQUAKE", "uses": 17191}, {"moveId": "AERIAL_ACE", "uses": 15280}, {"moveId": "SAND_TOMB", "uses": 8343}, {"moveId": "RETURN", "uses": 7865}]}, "moveset": ["WING_ATTACK", "NIGHT_SLASH", "EARTHQUAKE"], "score": 48.3}, {"speciesId": "scrafty", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 374, "matchups": [{"opponent": "tyranitar", "rating": 651, "opRating": 348}, {"opponent": "steelix", "rating": 651, "opRating": 348}, {"opponent": "aggron", "rating": 644, "opRating": 355}, {"opponent": "obstagoon", "rating": 640, "opRating": 359}, {"opponent": "tyranitar_shadow", "rating": 590, "opRating": 409}], "counters": [{"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "dialga", "rating": 220}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "excadrill", "rating": 325}, {"opponent": "mewtwo", "rating": 364}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 43128}, {"moveId": "SNARL", "uses": 33372}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 50794}, {"moveId": "POWER_UP_PUNCH", "uses": 19024}, {"moveId": "ACID_SPRAY", "uses": 6725}]}, "moveset": ["COUNTER", "FOUL_PLAY", "POWER_UP_PUNCH"], "score": 48.3}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 332, "matchups": [{"opponent": "magnezone", "rating": 726, "opRating": 273}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 693, "opRating": 306}, {"opponent": "rai<PERSON>u", "rating": 670, "opRating": 329}, {"opponent": "nihilego", "rating": 628, "opRating": 371}, {"opponent": "mewtwo", "rating": 603}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "lugia", "rating": 242}, {"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "gyarados", "rating": 278}, {"opponent": "giratina_origin", "rating": 356}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46610}, {"moveId": "MUD_SLAP", "uses": 29890}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36898}, {"moveId": "EARTHQUAKE", "uses": 23083}, {"moveId": "OUTRAGE", "uses": 16506}]}, "moveset": ["SNARL", "CRUNCH", "EARTHQUAKE"], "score": 48.1}, {"speciesId": "blastoise", "speciesName": "Blastoise", "rating": 350, "matchups": [{"opponent": "entei_shadow", "rating": 691, "opRating": 308}, {"opponent": "heatran", "rating": 685, "opRating": 314}, {"opponent": "steelix", "rating": 605, "opRating": 394}, {"opponent": "entei", "rating": 585, "opRating": 414}, {"opponent": "ho_oh", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "gyarados", "rating": 252}, {"opponent": "metagross", "rating": 316}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 50667}, {"moveId": "BITE", "uses": 25833}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37021}, {"moveId": "ICE_BEAM", "uses": 15577}, {"moveId": "SKULL_BASH", "uses": 8005}, {"moveId": "RETURN", "uses": 6679}, {"moveId": "FLASH_CANNON", "uses": 5266}, {"moveId": "HYDRO_PUMP", "uses": 4035}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 47.9}, {"speciesId": "king<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 342, "matchups": [{"opponent": "empoleon", "rating": 652, "opRating": 347}, {"opponent": "relicanth", "rating": 637, "opRating": 362}, {"opponent": "entei", "rating": 582, "opRating": 417}, {"opponent": "entei_shadow", "rating": 539, "opRating": 460}, {"opponent": "vaporeon", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 138}, {"opponent": "lugia", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "giratina_origin", "rating": 312}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 32944}, {"moveId": "WATER_GUN", "uses": 22325}, {"moveId": "WATERFALL", "uses": 21286}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23150}, {"moveId": "OCTAZOOKA", "uses": 15355}, {"moveId": "BLIZZARD", "uses": 14864}, {"moveId": "HYDRO_PUMP", "uses": 12946}, {"moveId": "RETURN", "uses": 10018}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 47.9}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 326, "matchups": [{"opponent": "rai<PERSON>u", "rating": 595, "opRating": 404}, {"opponent": "steelix", "rating": 565, "opRating": 434}, {"opponent": "gardevoir", "rating": 553, "opRating": 446}, {"opponent": "magnezone", "rating": 523, "opRating": 476}, {"opponent": "giratina_altered", "rating": 520, "opRating": 479}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "giratina_origin", "rating": 181}, {"opponent": "garcho<PERSON>", "rating": 302}, {"opponent": "lugia", "rating": 461}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 52636}, {"moveId": "MUD_SLAP", "uses": 23864}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38963}, {"moveId": "STONE_EDGE", "uses": 13286}, {"moveId": "BULLDOZE", "uses": 12156}, {"moveId": "ANCIENT_POWER", "uses": 12033}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "BULLDOZE"], "score": 47.9}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 367, "matchups": [{"opponent": "celebi", "rating": 726, "opRating": 273}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 664, "opRating": 335}, {"opponent": "mewtwo", "rating": 628}, {"opponent": "cresselia", "rating": 628, "opRating": 371}, {"opponent": "mewtwo_armored", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "gyarados", "rating": 105}, {"opponent": "metagross", "rating": 107}, {"opponent": "lugia", "rating": 266}, {"opponent": "giratina_origin", "rating": 420}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28453}, {"moveId": "DOUBLE_KICK", "uses": 27436}, {"moveId": "FIRE_FANG", "uses": 20628}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 37376}, {"moveId": "FLAME_CHARGE", "uses": 31967}, {"moveId": "FIRE_BLAST", "uses": 7096}]}, "moveset": ["SNARL", "DARK_PULSE", "FIRE_BLAST"], "score": 47.6}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 377, "matchups": [{"opponent": "magnezone_shadow", "rating": 788, "opRating": 211}, {"opponent": "nihilego", "rating": 750, "opRating": 250}, {"opponent": "magnezone", "rating": 663, "opRating": 336}, {"opponent": "zapdos", "rating": 586, "opRating": 413}, {"opponent": "heatran", "rating": 548, "opRating": 451}], "counters": [{"opponent": "giratina_origin", "rating": 199}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "dialga", "rating": 206}, {"opponent": "metagross", "rating": 206}, {"opponent": "zacian_hero", "rating": 210}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 58395}, {"moveId": "ROCK_SMASH", "uses": 18105}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 23008}, {"moveId": "SURF", "uses": 19571}, {"moveId": "EARTHQUAKE", "uses": 18351}, {"moveId": "MEGAHORN", "uses": 15514}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "SURF"], "score": 47.6}, {"speciesId": "charizard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 349, "matchups": [{"opponent": "ferrothorn", "rating": 761, "opRating": 238}, {"opponent": "aromatisse", "rating": 702, "opRating": 297}, {"opponent": "tapu_bulu", "rating": 639, "opRating": 360}, {"opponent": "heatran", "rating": 589, "opRating": 410}, {"opponent": "obstagoon", "rating": 541, "opRating": 458}], "counters": [{"opponent": "giratina_origin", "rating": 157}, {"opponent": "lugia", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "dialga", "rating": 198}, {"opponent": "dragonite", "rating": 273}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 17233}, {"moveId": "DRAGON_BREATH", "uses": 16239}, {"moveId": "WING_ATTACK", "uses": 15787}, {"moveId": "EMBER", "uses": 15357}, {"moveId": "AIR_SLASH", "uses": 11871}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 32758}, {"moveId": "DRAGON_CLAW", "uses": 25115}, {"moveId": "FLAMETHROWER", "uses": 7495}, {"moveId": "OVERHEAT", "uses": 7114}, {"moveId": "FIRE_BLAST", "uses": 4099}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "BLAST_BURN", "DRAGON_CLAW"], "score": 47.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 383, "matchups": [{"opponent": "tyranitar_shadow", "rating": 802, "opRating": 197}, {"opponent": "registeel", "rating": 721, "opRating": 278}, {"opponent": "regirock", "rating": 674, "opRating": 325}, {"opponent": "melmetal", "rating": 607, "opRating": 392}, {"opponent": "cobalion", "rating": 528, "opRating": 471}], "counters": [{"opponent": "gyarados", "rating": 157}, {"opponent": "metagross", "rating": 177}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "dialga", "rating": 220}, {"opponent": "excadrill", "rating": 374}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 47044}, {"moveId": "BULLET_PUNCH", "uses": 29456}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29261}, {"moveId": "SUPER_POWER", "uses": 21761}, {"moveId": "HEAVY_SLAM", "uses": 10185}, {"moveId": "DYNAMIC_PUNCH", "uses": 7861}, {"moveId": "RETURN", "uses": 7404}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "HEAVY_SLAM"], "score": 47.2}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 337, "matchups": [{"opponent": "s<PERSON><PERSON>", "rating": 777, "opRating": 222}, {"opponent": "genesect_chill", "rating": 683, "opRating": 316}, {"opponent": "genesect_burn", "rating": 683, "opRating": 316}, {"opponent": "genesect_shock", "rating": 552, "opRating": 447}, {"opponent": "genesect", "rating": 552, "opRating": 447}], "counters": [{"opponent": "lugia", "rating": 157}, {"opponent": "dialga", "rating": 190}, {"opponent": "mewtwo", "rating": 208}, {"opponent": "metagross", "rating": 290}, {"opponent": "giratina_origin", "rating": 406}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 60303}, {"moveId": "TAKE_DOWN", "uses": 16197}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25069}, {"moveId": "DARK_PULSE", "uses": 21859}, {"moveId": "OVERHEAT", "uses": 19299}, {"moveId": "SOLAR_BEAM", "uses": 10299}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "DARK_PULSE"], "score": 47.2}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 386, "matchups": [{"opponent": "primarina", "rating": 683, "opRating": 316}, {"opponent": "virizion", "rating": 640, "opRating": 359}, {"opponent": "florges", "rating": 563, "opRating": 436}, {"opponent": "kyogre", "rating": 552, "opRating": 447}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 517}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "dragonite", "rating": 170}, {"opponent": "gyarados", "rating": 208}, {"opponent": "zacian_hero", "rating": 450}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 32928}, {"moveId": "BULLET_SEED", "uses": 29787}, {"moveId": "RAZOR_LEAF", "uses": 13775}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 25081}, {"moveId": "GRASS_KNOT", "uses": 16320}, {"moveId": "SLUDGE_BOMB", "uses": 14300}, {"moveId": "LEAF_STORM", "uses": 9924}, {"moveId": "DAZZLING_GLEAM", "uses": 7456}, {"moveId": "SOLAR_BEAM", "uses": 3392}]}, "moveset": ["POISON_JAB", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 47.2}, {"speciesId": "king<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 324, "matchups": [{"opponent": "blastoise", "rating": 698, "opRating": 301}, {"opponent": "empoleon", "rating": 603, "opRating": 396}, {"opponent": "relicanth", "rating": 594, "opRating": 405}, {"opponent": "entei", "rating": 539, "opRating": 460}, {"opponent": "samu<PERSON>t", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 177}, {"opponent": "dialga", "rating": 184}, {"opponent": "dragonite", "rating": 207}, {"opponent": "giratina_origin", "rating": 237}, {"opponent": "garcho<PERSON>", "rating": 246}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 33908}, {"moveId": "WATERFALL", "uses": 21562}, {"moveId": "WATER_GUN", "uses": 21134}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 26763}, {"moveId": "OCTAZOOKA", "uses": 17675}, {"moveId": "BLIZZARD", "uses": 17065}, {"moveId": "HYDRO_PUMP", "uses": 14819}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "OUTRAGE", "OCTAZOOKA"], "score": 47}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 402, "matchups": [{"opponent": "buzzwole", "rating": 642, "opRating": 357}, {"opponent": "melmetal", "rating": 636, "opRating": 363}, {"opponent": "cobalion", "rating": 600, "opRating": 399}, {"opponent": "regirock", "rating": 597, "opRating": 402}, {"opponent": "terrakion", "rating": 590, "opRating": 409}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 190}, {"opponent": "dragonite", "rating": 202}, {"opponent": "gyarados", "rating": 399}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43102}, {"moveId": "CHARM", "uses": 22834}, {"moveId": "LOW_KICK", "uses": 10504}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29155}, {"moveId": "LEAF_BLADE", "uses": 23879}, {"moveId": "SYNCHRONOISE", "uses": 12179}, {"moveId": "RETURN", "uses": 5617}, {"moveId": "PSYCHIC", "uses": 5511}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 46.8}, {"speciesId": "poliwrath", "speciesName": "Poliwrath", "rating": 352, "matchups": [{"opponent": "tyranitar", "rating": 682, "opRating": 317}, {"opponent": "obstagoon", "rating": 631, "opRating": 368}, {"opponent": "tyranitar_shadow", "rating": 599, "opRating": 400}, {"opponent": "avalugg", "rating": 505, "opRating": 494}, {"opponent": "heatran", "rating": 505, "opRating": 494}], "counters": [{"opponent": "giratina_origin", "rating": 161}, {"opponent": "dialga", "rating": 171}, {"opponent": "dragonite", "rating": 255}, {"opponent": "garcho<PERSON>", "rating": 262}, {"opponent": "metagross", "rating": 290}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 35267}, {"moveId": "BUBBLE", "uses": 30066}, {"moveId": "ROCK_SMASH", "uses": 11200}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 19779}, {"moveId": "DYNAMIC_PUNCH", "uses": 18485}, {"moveId": "SCALD", "uses": 17530}, {"moveId": "RETURN", "uses": 6449}, {"moveId": "POWER_UP_PUNCH", "uses": 6221}, {"moveId": "SUBMISSION", "uses": 4074}, {"moveId": "HYDRO_PUMP", "uses": 3963}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 46.8}, {"speciesId": "aerodactyl", "speciesName": "Aerodactyl", "rating": 325, "matchups": [{"opponent": "moltres_shadow", "rating": 869, "opRating": 130}, {"opponent": "moltres", "rating": 715, "opRating": 284}, {"opponent": "charizard", "rating": 694, "opRating": 305}, {"opponent": "ho_oh", "rating": 569, "opRating": 430}, {"opponent": "ho_oh_shadow", "rating": 561, "opRating": 438}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "lugia", "rating": 250}, {"opponent": "dragonite", "rating": 287}, {"opponent": "gyarados", "rating": 311}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36331}, {"moveId": "BITE", "uses": 20859}, {"moveId": "STEEL_WING", "uses": 19357}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27636}, {"moveId": "EARTH_POWER", "uses": 18025}, {"moveId": "IRON_HEAD", "uses": 10020}, {"moveId": "ANCIENT_POWER", "uses": 9913}, {"moveId": "RETURN", "uses": 7914}, {"moveId": "HYPER_BEAM", "uses": 3064}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 46.6}, {"speciesId": "d<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 374, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 748, "opRating": 251}, {"opponent": "vileplume", "rating": 748, "opRating": 251}, {"opponent": "vileplume_shadow", "rating": 727, "opRating": 272}, {"opponent": "magmortar", "rating": 577, "opRating": 422}, {"opponent": "relicanth", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 152}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "mewtwo", "rating": 221}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "swampert", "rating": 378}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 52932}, {"moveId": "BITE", "uses": 23568}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 35913}, {"moveId": "NIGHT_SLASH", "uses": 32851}, {"moveId": "HYPER_BEAM", "uses": 7679}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "NIGHT_SLASH"], "score": 46.6}, {"speciesId": "bisharp", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 356, "matchups": [{"opponent": "mewtwo_shadow", "rating": 654, "opRating": 345}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 597, "opRating": 402}, {"opponent": "latios_shadow", "rating": 557, "opRating": 442}, {"opponent": "cresselia", "rating": 520, "opRating": 479}, {"opponent": "mewtwo", "rating": 506}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "gyarados", "rating": 164}, {"opponent": "metagross", "rating": 212}, {"opponent": "lugia", "rating": 290}, {"opponent": "giratina_origin", "rating": 390}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 51390}, {"moveId": "METAL_CLAW", "uses": 25110}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27648}, {"moveId": "X_SCISSOR", "uses": 18492}, {"moveId": "IRON_HEAD", "uses": 17266}, {"moveId": "FOCUS_BLAST", "uses": 13107}]}, "moveset": ["SNARL", "DARK_PULSE", "X_SCISSOR"], "score": 46.4}, {"speciesId": "vanilluxe", "speciesName": "Vanilluxe", "rating": 277, "matchups": [{"opponent": "salamence", "rating": 667, "opRating": 332}, {"opponent": "salamence_shadow", "rating": 658, "opRating": 341}, {"opponent": "torterra", "rating": 658, "opRating": 341}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 651, "opRating": 348}, {"opponent": "landorus_incarnate", "rating": 528, "opRating": 471}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "giratina_origin", "rating": 243}, {"opponent": "dragonite", "rating": 297}, {"opponent": "garcho<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "FROST_BREATH", "uses": 54186}, {"moveId": "ASTONISH", "uses": 22314}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 38174}, {"moveId": "SIGNAL_BEAM", "uses": 23025}, {"moveId": "FLASH_CANNON", "uses": 15323}]}, "moveset": ["FROST_BREATH", "BLIZZARD", "SIGNAL_BEAM"], "score": 46.2}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 388, "matchups": [{"opponent": "moltres_galarian", "rating": 685, "opRating": 314}, {"opponent": "vaporeon", "rating": 658, "opRating": 341}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 637}, {"opponent": "primarina", "rating": 615, "opRating": 384}, {"opponent": "gyarados", "rating": 573}], "counters": [{"opponent": "dialga", "rating": 51}, {"opponent": "mewtwo", "rating": 85}, {"opponent": "zacian_hero", "rating": 329}, {"opponent": "sylveon", "rating": 329}, {"opponent": "lugia", "rating": 369}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 67065}, {"moveId": "LOW_KICK", "uses": 9435}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 26486}, {"moveId": "ICE_PUNCH", "uses": 17904}, {"moveId": "THUNDER_PUNCH", "uses": 13002}, {"moveId": "FLAMETHROWER", "uses": 9811}, {"moveId": "RETURN", "uses": 5123}, {"moveId": "THUNDER", "uses": 4207}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 46}, {"speciesId": "<PERSON><PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 365, "matchups": [{"opponent": "tyranitar", "rating": 802, "opRating": 197}, {"opponent": "tyranitar_shadow", "rating": 796, "opRating": 203}, {"opponent": "snorlax", "rating": 650, "opRating": 349}, {"opponent": "regirock", "rating": 571, "opRating": 428}, {"opponent": "snor<PERSON>_shadow", "rating": 571, "opRating": 428}], "counters": [{"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "metagross", "rating": 145}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dialga", "rating": 263}, {"opponent": "excadrill", "rating": 444}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 47243}, {"moveId": "BULLET_PUNCH", "uses": 29257}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 32333}, {"moveId": "SUPER_POWER", "uses": 23833}, {"moveId": "HEAVY_SLAM", "uses": 11698}, {"moveId": "DYNAMIC_PUNCH", "uses": 8645}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "SUPER_POWER"], "score": 46}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 331, "matchups": [{"opponent": "swampert_shadow", "rating": 737, "opRating": 262}, {"opponent": "rhyperior", "rating": 722, "opRating": 277}, {"opponent": "primarina", "rating": 698, "opRating": 301}, {"opponent": "tapu_fini", "rating": 631, "opRating": 368}, {"opponent": "swampert", "rating": 576}], "counters": [{"opponent": "dialga", "rating": 73}, {"opponent": "mewtwo", "rating": 117}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "gyarados", "rating": 301}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43346}, {"moveId": "ACID", "uses": 33154}], "chargedMoves": [{"moveId": "MOONBLAST", "uses": 21931}, {"moveId": "SLUDGE_BOMB", "uses": 21800}, {"moveId": "PETAL_BLIZZARD", "uses": 15470}, {"moveId": "RETURN", "uses": 10993}, {"moveId": "SOLAR_BEAM", "uses": 6383}]}, "moveset": ["RAZOR_LEAF", "MOONBLAST", "SLUDGE_BOMB"], "score": 46}, {"speciesId": "aerodactyl_shadow", "speciesName": "Aerodactyl (Shadow)", "rating": 325, "matchups": [{"opponent": "moltres_shadow", "rating": 883, "opRating": 116}, {"opponent": "moltres", "rating": 869, "opRating": 130}, {"opponent": "charizard", "rating": 811, "opRating": 188}, {"opponent": "ho_oh", "rating": 561, "opRating": 438}, {"opponent": "golisopod", "rating": 526, "opRating": 473}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "dragonite", "rating": 260}, {"opponent": "gyarados", "rating": 270}, {"opponent": "lugia", "rating": 280}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 36584}, {"moveId": "BITE", "uses": 20357}, {"moveId": "STEEL_WING", "uses": 19592}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29251}, {"moveId": "EARTH_POWER", "uses": 19283}, {"moveId": "IRON_HEAD", "uses": 10734}, {"moveId": "ANCIENT_POWER", "uses": 10480}, {"moveId": "HYPER_BEAM", "uses": 6620}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_SLIDE", "EARTH_POWER"], "score": 45.8}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 339, "matchups": [{"opponent": "genesect", "rating": 781, "opRating": 218}, {"opponent": "buzzwole", "rating": 732, "opRating": 267}, {"opponent": "genesect_chill", "rating": 693, "opRating": 306}, {"opponent": "genesect_burn", "rating": 693, "opRating": 306}, {"opponent": "zacian_hero", "rating": 566}], "counters": [{"opponent": "garcho<PERSON>", "rating": 77}, {"opponent": "dialga", "rating": 125}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "lugia", "rating": 133}, {"opponent": "metagross", "rating": 284}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34449}, {"moveId": "HEX", "uses": 23790}, {"moveId": "FIRE_SPIN", "uses": 18325}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26496}, {"moveId": "FLAME_CHARGE", "uses": 20827}, {"moveId": "OVERHEAT", "uses": 16091}, {"moveId": "ENERGY_BALL", "uses": 13060}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 45.8}, {"speciesId": "muk_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 383, "matchups": [{"opponent": "primarina", "rating": 750, "opRating": 250}, {"opponent": "gardevoir", "rating": 750, "opRating": 250}, {"opponent": "gardevoir_shadow", "rating": 692, "opRating": 307}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 603}, {"opponent": "florges", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "dragonite", "rating": 207}, {"opponent": "gyarados", "rating": 219}, {"opponent": "lugia", "rating": 250}, {"opponent": "zacian_hero", "rating": 326}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 32016}, {"moveId": "LICK", "uses": 23154}, {"moveId": "INFESTATION", "uses": 21338}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 27104}, {"moveId": "THUNDER_PUNCH", "uses": 22897}, {"moveId": "SLUDGE_WAVE", "uses": 15305}, {"moveId": "GUNK_SHOT", "uses": 6017}, {"moveId": "ACID_SPRAY", "uses": 5133}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "DARK_PULSE", "THUNDER_PUNCH"], "score": 45.8}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 328, "matchups": [{"opponent": "moltres", "rating": 872, "opRating": 127}, {"opponent": "moltres_shadow", "rating": 846, "opRating": 153}, {"opponent": "articuno", "rating": 614, "opRating": 385}, {"opponent": "articuno_shadow", "rating": 568, "opRating": 431}, {"opponent": "ho_oh", "rating": 553, "opRating": 446}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "giratina_origin", "rating": 149}, {"opponent": "gyarados", "rating": 247}, {"opponent": "lugia", "rating": 288}, {"opponent": "dragonite", "rating": 292}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 67972}, {"moveId": "ZEN_HEADBUTT", "uses": 8528}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 38190}, {"moveId": "OUTRAGE", "uses": 19216}, {"moveId": "FLAMETHROWER", "uses": 19163}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "OUTRAGE"], "score": 45.6}, {"speciesId": "deoxys_defense", "speciesName": "<PERSON><PERSON><PERSON> (Defense)", "rating": 354, "matchups": [{"opponent": "aurorus", "rating": 598, "opRating": 401}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 559, "opRating": 440}, {"opponent": "articuno", "rating": 515, "opRating": 484}, {"opponent": "machamp", "rating": 515, "opRating": 484}, {"opponent": "obstagoon", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 159}, {"opponent": "dialga", "rating": 182}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "metagross", "rating": 223}, {"opponent": "gyarados", "rating": 262}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 67147}, {"moveId": "ZEN_HEADBUTT", "uses": 9353}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 32985}, {"moveId": "ROCK_SLIDE", "uses": 27348}, {"moveId": "THUNDERBOLT", "uses": 16230}]}, "moveset": ["COUNTER", "PSYCHO_BOOST", "ROCK_SLIDE"], "score": 45.3}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 315, "matchups": [{"opponent": "swampert", "rating": 697}, {"opponent": "primarina", "rating": 651, "opRating": 348}, {"opponent": "vaporeon", "rating": 627, "opRating": 372}, {"opponent": "magnezone", "rating": 619, "opRating": 380}, {"opponent": "excadrill", "rating": 569}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 276}, {"opponent": "gyarados", "rating": 322}, {"opponent": "zacian_hero", "rating": 361}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 53464}, {"moveId": "RAZOR_LEAF", "uses": 23036}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 38989}, {"moveId": "EARTHQUAKE", "uses": 17009}, {"moveId": "RETURN", "uses": 10777}, {"moveId": "PETAL_BLIZZARD", "uses": 5385}, {"moveId": "SOLAR_BEAM", "uses": 4432}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 45.3}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 291, "matchups": [{"opponent": "gourgeist_super", "rating": 702, "opRating": 297}, {"opponent": "giratina_origin", "rating": 609}, {"opponent": "trevenant", "rating": 603, "opRating": 396}, {"opponent": "golisopod", "rating": 556, "opRating": 443}, {"opponent": "suicune_shadow", "rating": 547, "opRating": 452}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 195}, {"opponent": "gyarados", "rating": 195}, {"opponent": "mewtwo", "rating": 221}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 35778}, {"moveId": "ICE_FANG", "uses": 32048}, {"moveId": "TAKE_DOWN", "uses": 8725}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35401}, {"moveId": "CRUNCH", "uses": 27742}, {"moveId": "PLAY_ROUGH", "uses": 13461}]}, "moveset": ["LICK", "WILD_CHARGE", "CRUNCH"], "score": 45.3}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 377, "matchups": [{"opponent": "nidoqueen", "rating": 689, "opRating": 310}, {"opponent": "rhyperior", "rating": 609, "opRating": 390}, {"opponent": "nihilego", "rating": 560, "opRating": 439}, {"opponent": "excadrill", "rating": 518}, {"opponent": "magnezone", "rating": 506, "opRating": 493}], "counters": [{"opponent": "lugia", "rating": 157}, {"opponent": "giratina_origin", "rating": 171}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "grou<PERSON>", "rating": 250}, {"opponent": "mewtwo", "rating": 325}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 38353}, {"moveId": "WING_ATTACK", "uses": 38147}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 30910}, {"moveId": "EARTHQUAKE", "uses": 18816}, {"moveId": "AERIAL_ACE", "uses": 17403}, {"moveId": "SAND_TOMB", "uses": 9247}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "EARTHQUAKE"], "score": 45.1}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 360, "matchups": [{"opponent": "swampert", "rating": 628}, {"opponent": "vaporeon", "rating": 598, "opRating": 401}, {"opponent": "swampert_shadow", "rating": 573, "opRating": 426}, {"opponent": "tapu_fini", "rating": 546, "opRating": 453}, {"opponent": "rhyperior", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "zacian_hero", "rating": 167}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "gyarados", "rating": 273}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 46289}, {"moveId": "METAL_CLAW", "uses": 30211}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 25468}, {"moveId": "MIRROR_SHOT", "uses": 21645}, {"moveId": "THUNDER", "uses": 14620}, {"moveId": "FLASH_CANNON", "uses": 11294}, {"moveId": "ACID_SPRAY", "uses": 3532}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "MIRROR_SHOT"], "score": 44.9}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 320, "matchups": [{"opponent": "swampert", "rating": 694}, {"opponent": "rhyperior", "rating": 686, "opRating": 313}, {"opponent": "swampert_shadow", "rating": 668, "opRating": 331}, {"opponent": "tapu_fini", "rating": 639, "opRating": 360}, {"opponent": "primarina", "rating": 627, "opRating": 372}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 164}, {"opponent": "gyarados", "rating": 226}, {"opponent": "zacian_hero", "rating": 289}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43660}, {"moveId": "ACID", "uses": 32840}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 43211}, {"moveId": "SLUDGE_BOMB", "uses": 17451}, {"moveId": "LEAF_TORNADO", "uses": 8221}, {"moveId": "ACID_SPRAY", "uses": 3896}, {"moveId": "SOLAR_BEAM", "uses": 3617}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 44.9}, {"speciesId": "dewgong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 275, "matchups": [{"opponent": "mandibuzz", "rating": 712, "opRating": 287}, {"opponent": "gliscor", "rating": 615, "opRating": 384}, {"opponent": "hippo<PERSON><PERSON>", "rating": 602, "opRating": 397}, {"opponent": "gliscor_shadow", "rating": 513, "opRating": 486}, {"opponent": "sandslash_alolan", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 182}, {"opponent": "lugia", "rating": 200}, {"opponent": "mewtwo", "rating": 205}, {"opponent": "gyarados", "rating": 250}, {"opponent": "garcho<PERSON>", "rating": 298}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 38878}, {"moveId": "FROST_BREATH", "uses": 29554}, {"moveId": "IRON_TAIL", "uses": 8063}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 36266}, {"moveId": "BLIZZARD", "uses": 16153}, {"moveId": "AQUA_JET", "uses": 12695}, {"moveId": "AURORA_BEAM", "uses": 6482}, {"moveId": "WATER_PULSE", "uses": 4730}]}, "moveset": ["ICE_SHARD", "ICY_WIND", "BLIZZARD"], "score": 44.7}, {"speciesId": "forretress", "speciesName": "Forretress", "rating": 355, "matchups": [{"opponent": "venusaur", "rating": 713, "opRating": 286}, {"opponent": "celebi", "rating": 695, "opRating": 304}, {"opponent": "aggron", "rating": 567, "opRating": 432}, {"opponent": "muk_alolan", "rating": 536, "opRating": 463}, {"opponent": "nidoqueen", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "gyarados", "rating": 211}, {"opponent": "metagross", "rating": 215}, {"opponent": "mewtwo", "rating": 252}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47743}, {"moveId": "STRUGGLE_BUG", "uses": 28757}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 18198}, {"moveId": "EARTHQUAKE", "uses": 15507}, {"moveId": "HEAVY_SLAM", "uses": 14781}, {"moveId": "ROCK_TOMB", "uses": 11354}, {"moveId": "RETURN", "uses": 8911}, {"moveId": "SAND_TOMB", "uses": 7654}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 44.7}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 331, "matchups": [{"opponent": "magnezone_shadow", "rating": 798, "opRating": 201}, {"opponent": "magnezone", "rating": 728, "opRating": 271}, {"opponent": "nihilego", "rating": 701, "opRating": 298}, {"opponent": "cobalion", "rating": 638, "opRating": 361}, {"opponent": "melmetal", "rating": 548, "opRating": 451}], "counters": [{"opponent": "mewtwo", "rating": 143}, {"opponent": "garcho<PERSON>", "rating": 166}, {"opponent": "dialga", "rating": 206}, {"opponent": "metagross", "rating": 206}, {"opponent": "zacian_hero", "rating": 323}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 49392}, {"moveId": "ASTONISH", "uses": 27108}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 32163}, {"moveId": "EARTH_POWER", "uses": 23037}, {"moveId": "DYNAMIC_PUNCH", "uses": 21367}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "EARTH_POWER"], "score": 44.7}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 334, "matchups": [{"opponent": "charizard", "rating": 647, "opRating": 352}, {"opponent": "moltres_shadow", "rating": 569, "opRating": 430}, {"opponent": "cresselia", "rating": 556, "opRating": 443}, {"opponent": "gyarados", "rating": 529}, {"opponent": "moltres_galarian", "rating": 524, "opRating": 475}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "dialga", "rating": 211}, {"opponent": "lugia", "rating": 264}, {"opponent": "giratina_origin", "rating": 274}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 367}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28944}, {"moveId": "FIRE_FANG", "uses": 20604}, {"moveId": "THUNDER_FANG", "uses": 15030}, {"moveId": "BITE", "uses": 11897}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20993}, {"moveId": "CRUNCH", "uses": 14873}, {"moveId": "PSYCHIC_FANGS", "uses": 13456}, {"moveId": "FLAMETHROWER", "uses": 12385}, {"moveId": "BULLDOZE", "uses": 6328}, {"moveId": "RETURN", "uses": 5110}, {"moveId": "FIRE_BLAST", "uses": 3340}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 44.5}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 370, "matchups": [{"opponent": "articuno", "rating": 729, "opRating": 270}, {"opponent": "heatran", "rating": 642, "opRating": 357}, {"opponent": "zarude", "rating": 613, "opRating": 386}, {"opponent": "genesect_chill", "rating": 546, "opRating": 453}, {"opponent": "genesect_burn", "rating": 546, "opRating": 453}], "counters": [{"opponent": "mewtwo", "rating": 78}, {"opponent": "zacian_hero", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "metagross", "rating": 186}, {"opponent": "dialga", "rating": 296}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 46320}, {"moveId": "FIRE_SPIN", "uses": 30180}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 21226}, {"moveId": "BRAVE_BIRD", "uses": 16006}, {"moveId": "BLAZE_KICK", "uses": 13113}, {"moveId": "STONE_EDGE", "uses": 11854}, {"moveId": "FOCUS_BLAST", "uses": 9830}, {"moveId": "OVERHEAT", "uses": 4522}]}, "moveset": ["COUNTER", "BLAZE_KICK", "BLAST_BURN"], "score": 44.5}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 324, "matchups": [{"opponent": "stoutland", "rating": 610, "opRating": 389}, {"opponent": "lickilicky", "rating": 586, "opRating": 413}, {"opponent": "crobat", "rating": 558, "opRating": 441}, {"opponent": "cofagrigus", "rating": 547, "opRating": 452}, {"opponent": "gengar", "rating": 538, "opRating": 461}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "gyarados", "rating": 317}, {"opponent": "giratina_origin", "rating": 370}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 28721}, {"moveId": "TACKLE", "uses": 28574}, {"moveId": "BITE", "uses": 19199}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 47787}, {"moveId": "CRUNCH", "uses": 28713}]}, "moveset": ["TACKLE", "BODY_SLAM", "CRUNCH"], "score": 44.5}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 289, "matchups": [{"opponent": "flygon_shadow", "rating": 684, "opRating": 315}, {"opponent": "scrafty", "rating": 636, "opRating": 363}, {"opponent": "flygon", "rating": 598, "opRating": 401}, {"opponent": "pangoro", "rating": 570, "opRating": 429}, {"opponent": "d<PERSON><PERSON><PERSON>", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 179}, {"opponent": "giratina_origin", "rating": 189}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "dragonite", "rating": 239}, {"opponent": "gyarados", "rating": 247}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 6509}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 5497}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5355}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4902}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4707}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4637}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4551}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4395}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4382}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4294}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4214}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4205}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4155}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3820}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3817}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3706}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3565}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 35993}, {"moveId": "HYPER_BEAM", "uses": 26072}, {"moveId": "SOLAR_BEAM", "uses": 14555}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 44.5}, {"speciesId": "cloyster", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 303, "matchups": [{"opponent": "mandibuzz", "rating": 685, "opRating": 314}, {"opponent": "hippow<PERSON>_shadow", "rating": 673, "opRating": 326}, {"opponent": "gliscor", "rating": 614, "opRating": 385}, {"opponent": "hippo<PERSON><PERSON>", "rating": 598, "opRating": 401}, {"opponent": "d<PERSON><PERSON><PERSON>", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "giratina_origin", "rating": 193}, {"opponent": "lugia", "rating": 245}, {"opponent": "garcho<PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 47766}, {"moveId": "FROST_BREATH", "uses": 28734}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 36473}, {"moveId": "ICY_WIND", "uses": 12699}, {"moveId": "HYDRO_PUMP", "uses": 10099}, {"moveId": "RETURN", "uses": 7039}, {"moveId": "BLIZZARD", "uses": 5643}, {"moveId": "AURORA_BEAM", "uses": 4557}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 44.3}, {"speciesId": "ampha<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 334, "matchups": [{"opponent": "primarina", "rating": 604, "opRating": 395}, {"opponent": "vaporeon", "rating": 591, "opRating": 408}, {"opponent": "golisopod", "rating": 564, "opRating": 435}, {"opponent": "gyarados", "rating": 516}, {"opponent": "obstagoon", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "mewtwo", "rating": 138}, {"opponent": "zacian_hero", "rating": 297}, {"opponent": "lugia", "rating": 326}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 483}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 53289}, {"moveId": "CHARGE_BEAM", "uses": 23211}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 24298}, {"moveId": "FOCUS_BLAST", "uses": 14803}, {"moveId": "DRAGON_PULSE", "uses": 12868}, {"moveId": "POWER_GEM", "uses": 9080}, {"moveId": "THUNDER", "uses": 7857}, {"moveId": "ZAP_CANNON", "uses": 7585}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "FOCUS_BLAST"], "score": 44.1}, {"speciesId": "gengar", "speciesName": "Gengar", "rating": 320, "matchups": [{"opponent": "gardevoir", "rating": 746, "opRating": 253}, {"opponent": "gardevoir_shadow", "rating": 683, "opRating": 316}, {"opponent": "primarina", "rating": 661, "opRating": 338}, {"opponent": "genesect", "rating": 658, "opRating": 341}, {"opponent": "buzzwole", "rating": 633, "opRating": 366}], "counters": [{"opponent": "dialga", "rating": 133}, {"opponent": "giratina_origin", "rating": 133}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "lugia", "rating": 169}, {"opponent": "zacian_hero", "rating": 494}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 29121}, {"moveId": "HEX", "uses": 18837}, {"moveId": "LICK", "uses": 14849}, {"moveId": "SUCKER_PUNCH", "uses": 13765}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16632}, {"moveId": "SHADOW_PUNCH", "uses": 16498}, {"moveId": "SLUDGE_BOMB", "uses": 11773}, {"moveId": "DARK_PULSE", "uses": 10841}, {"moveId": "FOCUS_BLAST", "uses": 8834}, {"moveId": "PSYCHIC", "uses": 8083}, {"moveId": "SLUDGE_WAVE", "uses": 3963}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "SHADOW_PUNCH"], "score": 44.1}, {"speciesId": "pangoro", "speciesName": "Pangoro", "rating": 387, "matchups": [{"opponent": "steelix", "rating": 662, "opRating": 337}, {"opponent": "obstagoon", "rating": 618, "opRating": 381}, {"opponent": "tyranitar", "rating": 610, "opRating": 389}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 590, "opRating": 409}, {"opponent": "tyranitar_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "lugia", "rating": 185}, {"opponent": "gyarados", "rating": 226}, {"opponent": "mewtwo", "rating": 328}, {"opponent": "giratina_origin", "rating": 444}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42692}, {"moveId": "BULLET_PUNCH", "uses": 27945}, {"moveId": "LOW_KICK", "uses": 5897}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 29032}, {"moveId": "NIGHT_SLASH", "uses": 26577}, {"moveId": "ROCK_SLIDE", "uses": 14193}, {"moveId": "IRON_HEAD", "uses": 6621}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 43.9}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 351, "matchups": [{"opponent": "nihilego", "rating": 704, "opRating": 295}, {"opponent": "heatran", "rating": 694, "opRating": 305}, {"opponent": "magnezone", "rating": 682, "opRating": 317}, {"opponent": "magnezone_shadow", "rating": 677, "opRating": 322}, {"opponent": "over<PERSON><PERSON>l", "rating": 629, "opRating": 370}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "dialga", "rating": 154}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "metagross", "rating": 438}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 42158}, {"moveId": "BUBBLE", "uses": 34342}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 30098}, {"moveId": "MUDDY_WATER", "uses": 26506}, {"moveId": "SLUDGE_BOMB", "uses": 19935}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "MUDDY_WATER"], "score": 43.9}, {"speciesId": "feraligatr_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 338, "matchups": [{"opponent": "heatran", "rating": 671, "opRating": 328}, {"opponent": "entei", "rating": 668, "opRating": 331}, {"opponent": "steelix", "rating": 575, "opRating": 424}, {"opponent": "hippo<PERSON><PERSON>", "rating": 550, "opRating": 449}, {"opponent": "articuno", "rating": 544, "opRating": 455}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "zacian_hero", "rating": 193}, {"opponent": "metagross", "rating": 194}, {"opponent": "mewtwo", "rating": 210}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 22594}, {"moveId": "WATERFALL", "uses": 21839}, {"moveId": "ICE_FANG", "uses": 20418}, {"moveId": "BITE", "uses": 11774}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 37395}, {"moveId": "CRUNCH", "uses": 19324}, {"moveId": "ICE_BEAM", "uses": 15638}, {"moveId": "HYDRO_PUMP", "uses": 4020}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "CRUNCH"], "score": 43.7}, {"speciesId": "hoopa", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 327, "matchups": [{"opponent": "heracross", "rating": 805, "opRating": 194}, {"opponent": "machamp", "rating": 735, "opRating": 264}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 649, "opRating": 350}, {"opponent": "sneasler", "rating": 560, "opRating": 439}, {"opponent": "buzzwole", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "gyarados", "rating": 185}, {"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "dragonite", "rating": 228}, {"opponent": "zacian_hero", "rating": 326}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 52599}, {"moveId": "ASTONISH", "uses": 23901}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 43482}, {"moveId": "PSYCHIC", "uses": 27344}, {"moveId": "PSYBEAM", "uses": 5672}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 43.7}, {"speciesId": "politoed", "speciesName": "Politoed", "rating": 317, "matchups": [{"opponent": "entei", "rating": 706, "opRating": 293}, {"opponent": "entei_shadow", "rating": 680, "opRating": 319}, {"opponent": "heatran", "rating": 666, "opRating": 333}, {"opponent": "steelix", "rating": 569, "opRating": 430}, {"opponent": "hippow<PERSON>_shadow", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "zacian_hero", "rating": 182}, {"opponent": "metagross", "rating": 313}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41236}, {"moveId": "BUBBLE", "uses": 35264}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 29637}, {"moveId": "SURF", "uses": 12075}, {"moveId": "BLIZZARD", "uses": 11971}, {"moveId": "EARTHQUAKE", "uses": 11742}, {"moveId": "RETURN", "uses": 7231}, {"moveId": "HYDRO_PUMP", "uses": 3920}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "SURF"], "score": 43.7}, {"speciesId": "cloyster_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 290, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 673, "opRating": 326}, {"opponent": "ma<PERSON><PERSON>", "rating": 653, "opRating": 346}, {"opponent": "aromatisse", "rating": 606, "opRating": 393}, {"opponent": "hippow<PERSON>_shadow", "rating": 594, "opRating": 405}, {"opponent": "gliscor", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 248}, {"opponent": "dragonite", "rating": 279}, {"opponent": "lugia", "rating": 304}, {"opponent": "gyarados", "rating": 355}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 48429}, {"moveId": "FROST_BREATH", "uses": 28071}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 40014}, {"moveId": "ICY_WIND", "uses": 13921}, {"moveId": "HYDRO_PUMP", "uses": 11231}, {"moveId": "BLIZZARD", "uses": 6173}, {"moveId": "AURORA_BEAM", "uses": 5031}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 43.5}, {"speciesId": "golem_alolan", "speciesName": "Golem (Alolan)", "rating": 348, "matchups": [{"opponent": "entei", "rating": 700, "opRating": 299}, {"opponent": "ho_oh", "rating": 622, "opRating": 377}, {"opponent": "zapdos", "rating": 598, "opRating": 401}, {"opponent": "primarina", "rating": 587, "opRating": 412}, {"opponent": "lugia_shadow", "rating": 531, "opRating": 468}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "zacian_hero", "rating": 170}, {"opponent": "gyarados", "rating": 420}, {"opponent": "lugia", "rating": 445}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 34413}, {"moveId": "ROLLOUT", "uses": 24153}, {"moveId": "ROCK_THROW", "uses": 17983}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 37071}, {"moveId": "STONE_EDGE", "uses": 20640}, {"moveId": "ROCK_BLAST", "uses": 18774}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "STONE_EDGE"], "score": 43.5}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 325, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 706, "opRating": 293}, {"opponent": "heracross", "rating": 654, "opRating": 345}, {"opponent": "sneasler", "rating": 625, "opRating": 375}, {"opponent": "machamp_shadow", "rating": 619, "opRating": 380}, {"opponent": "con<PERSON><PERSON><PERSON>", "rating": 566, "opRating": 433}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "dragonite", "rating": 226}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "gyarados", "rating": 273}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42792}, {"moveId": "EXTRASENSORY", "uses": 33708}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 33686}, {"moveId": "BLIZZARD", "uses": 30356}, {"moveId": "SWIFT", "uses": 12372}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "BLIZZARD"], "score": 43.5}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 302, "matchups": [{"opponent": "magnezone", "rating": 770, "opRating": 229}, {"opponent": "magnezone_shadow", "rating": 729, "opRating": 270}, {"opponent": "nihilego", "rating": 712, "opRating": 287}, {"opponent": "raikou_shadow", "rating": 549, "opRating": 450}, {"opponent": "entei_shadow", "rating": 502, "opRating": 497}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "giratina_origin", "rating": 167}, {"opponent": "mewtwo", "rating": 218}, {"opponent": "metagross", "rating": 232}, {"opponent": "dialga", "rating": 277}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38259}, {"moveId": "ROCK_THROW", "uses": 38241}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 22445}, {"moveId": "STONE_EDGE", "uses": 20771}, {"moveId": "ROCK_BLAST", "uses": 18757}, {"moveId": "ANCIENT_POWER", "uses": 14445}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "EARTHQUAKE", "STONE_EDGE"], "score": 43.3}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 315, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 774, "opRating": 225}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 658, "opRating": 341}, {"opponent": "gallade", "rating": 625, "opRating": 375}, {"opponent": "swampert", "rating": 542}, {"opponent": "vaporeon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "garcho<PERSON>", "rating": 267}, {"opponent": "zacian_hero", "rating": 294}, {"opponent": "gyarados", "rating": 301}, {"opponent": "excadrill", "rating": 411}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49481}, {"moveId": "RAZOR_LEAF", "uses": 27019}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26376}, {"moveId": "SEED_BOMB", "uses": 21776}, {"moveId": "FOUL_PLAY", "uses": 19817}, {"moveId": "FIRE_BLAST", "uses": 8614}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 43.3}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 357, "matchups": [{"opponent": "sci<PERSON>_shadow", "rating": 685, "opRating": 314}, {"opponent": "genesect_chill", "rating": 658, "opRating": 341}, {"opponent": "genesect_burn", "rating": 658, "opRating": 341}, {"opponent": "genesect_shock", "rating": 530, "opRating": 469}, {"opponent": "genesect", "rating": 530, "opRating": 469}], "counters": [{"opponent": "garcho<PERSON>", "rating": 72}, {"opponent": "mewtwo", "rating": 80}, {"opponent": "lugia", "rating": 147}, {"opponent": "zacian_hero", "rating": 231}, {"opponent": "dialga", "rating": 328}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39879}, {"moveId": "FIRE_SPIN", "uses": 36621}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23049}, {"moveId": "BRICK_BREAK", "uses": 15634}, {"moveId": "THUNDERBOLT", "uses": 13334}, {"moveId": "PSYCHIC", "uses": 11746}, {"moveId": "RETURN", "uses": 8209}, {"moveId": "FIRE_BLAST", "uses": 4695}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 43.3}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 354, "matchups": [{"opponent": "articuno", "rating": 660, "opRating": 339}, {"opponent": "avalugg", "rating": 657, "opRating": 342}, {"opponent": "florges", "rating": 580, "opRating": 419}, {"opponent": "genesect_chill", "rating": 559, "opRating": 440}, {"opponent": "genesect_burn", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dialga", "rating": 144}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 190}, {"opponent": "lugia", "rating": 259}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 435}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 30392}, {"moveId": "SHADOW_CLAW", "uses": 29033}, {"moveId": "EMBER", "uses": 17012}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 41688}, {"moveId": "RETURN", "uses": 10672}, {"moveId": "SOLAR_BEAM", "uses": 10117}, {"moveId": "OVERHEAT", "uses": 8870}, {"moveId": "FIRE_BLAST", "uses": 5149}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 43.3}, {"speciesId": "espeon", "speciesName": "Espeon", "rating": 348, "matchups": [{"opponent": "heracross", "rating": 614, "opRating": 385}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 587, "opRating": 412}, {"opponent": "machamp", "rating": 563, "opRating": 436}, {"opponent": "sneasler", "rating": 530, "opRating": 469}, {"opponent": "machamp_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "giratina_origin", "rating": 109}, {"opponent": "garcho<PERSON>", "rating": 122}, {"opponent": "dialga", "rating": 173}, {"opponent": "dragonite", "rating": 194}, {"opponent": "zacian_hero", "rating": 228}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66273}, {"moveId": "ZEN_HEADBUTT", "uses": 10227}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 24846}, {"moveId": "SHADOW_BALL", "uses": 19359}, {"moveId": "PSYCHIC", "uses": 13614}, {"moveId": "LAST_RESORT", "uses": 10030}, {"moveId": "FUTURE_SIGHT", "uses": 5995}, {"moveId": "PSYBEAM", "uses": 2742}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "SHADOW_BALL"], "score": 43}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 303, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 722, "opRating": 277}, {"opponent": "escavalier", "rating": 638, "opRating": 361}, {"opponent": "virizion", "rating": 596, "opRating": 403}, {"opponent": "genesect", "rating": 543, "opRating": 456}, {"opponent": "buzzwole", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "giratina_origin", "rating": 185}, {"opponent": "garcho<PERSON>", "rating": 316}, {"opponent": "dragonite", "rating": 351}, {"opponent": "grou<PERSON>", "rating": 432}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55691}, {"moveId": "ASTONISH", "uses": 20809}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 31420}, {"moveId": "SHADOW_BALL", "uses": 29827}, {"moveId": "OMINOUS_WIND", "uses": 15294}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 42.8}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 358, "matchups": [{"opponent": "gourgeist_super", "rating": 605, "opRating": 394}, {"opponent": "umbreon", "rating": 597, "opRating": 402}, {"opponent": "lickilicky", "rating": 572, "opRating": 427}, {"opponent": "exeggutor_alolan_shadow", "rating": 543, "opRating": 456}, {"opponent": "drapion", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 92}, {"opponent": "lugia", "rating": 145}, {"opponent": "zacian_hero", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "giratina_origin", "rating": 438}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 65750}, {"moveId": "ZEN_HEADBUTT", "uses": 10750}], "chargedMoves": [{"moveId": "STOMP", "uses": 26193}, {"moveId": "MEGAHORN", "uses": 21415}, {"moveId": "EARTHQUAKE", "uses": 19749}, {"moveId": "SKULL_BASH", "uses": 9113}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 42.6}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 300, "matchups": [{"opponent": "tentacruel", "rating": 650, "opRating": 349}, {"opponent": "gengar", "rating": 610, "opRating": 389}, {"opponent": "trevenant", "rating": 607, "opRating": 392}, {"opponent": "hoopa", "rating": 586, "opRating": 413}, {"opponent": "nidoqueen", "rating": 529, "opRating": 470}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "mewtwo", "rating": 346}, {"opponent": "giratina_origin", "rating": 348}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 61822}, {"moveId": "ZEN_HEADBUTT", "uses": 12803}, {"moveId": "YAWN", "uses": 1937}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 36409}, {"moveId": "PSYCHIC", "uses": 27933}, {"moveId": "FUTURE_SIGHT", "uses": 12183}]}, "moveset": ["CONFUSION", "FOUL_PLAY", "PSYCHIC"], "score": 42.6}, {"speciesId": "sandslash_alolan_shadow", "speciesName": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>) (Shadow)", "rating": 342, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 731, "opRating": 268}, {"opponent": "articuno", "rating": 554, "opRating": 445}, {"opponent": "celebi", "rating": 551, "opRating": 448}, {"opponent": "cresselia", "rating": 539, "opRating": 460}, {"opponent": "goodra", "rating": 536, "opRating": 463}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "lugia", "rating": 285}, {"opponent": "gyarados", "rating": 286}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 471}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 33303}, {"moveId": "SHADOW_CLAW", "uses": 30385}, {"moveId": "METAL_CLAW", "uses": 12834}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 34254}, {"moveId": "BLIZZARD", "uses": 17749}, {"moveId": "BULLDOZE", "uses": 13224}, {"moveId": "GYRO_BALL", "uses": 11203}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_PUNCH", "BLIZZARD"], "score": 42.6}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 298, "matchups": [{"opponent": "swampert", "rating": 695}, {"opponent": "swampert_shadow", "rating": 652, "opRating": 347}, {"opponent": "primarina", "rating": 634, "opRating": 365}, {"opponent": "vaporeon", "rating": 612, "opRating": 387}, {"opponent": "tapu_fini", "rating": 567, "opRating": 432}], "counters": [{"opponent": "giratina_origin", "rating": 159}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "garcho<PERSON>", "rating": 272}, {"opponent": "gyarados", "rating": 324}, {"opponent": "excadrill", "rating": 400}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 65228}, {"moveId": "IRON_TAIL", "uses": 11272}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 34935}, {"moveId": "LEAF_TORNADO", "uses": 17956}, {"moveId": "AERIAL_ACE", "uses": 14225}, {"moveId": "GRASS_KNOT", "uses": 9218}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "LEAF_TORNADO"], "score": 42.6}, {"speciesId": "suicune_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 355, "matchups": [{"opponent": "heatran", "rating": 708, "opRating": 291}, {"opponent": "hippo<PERSON><PERSON>", "rating": 669, "opRating": 330}, {"opponent": "steelix", "rating": 654, "opRating": 345}, {"opponent": "entei", "rating": 621, "opRating": 378}, {"opponent": "articuno", "rating": 529, "opRating": 470}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "giratina_origin", "rating": 127}, {"opponent": "mewtwo", "rating": 130}, {"opponent": "gyarados", "rating": 273}, {"opponent": "metagross", "rating": 479}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 7390}, {"moveId": "ICE_FANG", "uses": 6255}, {"moveId": "EXTRASENSORY", "uses": 4756}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4633}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4610}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4095}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3984}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3910}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3873}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3749}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3676}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3557}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3513}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3436}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3171}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3157}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3156}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2998}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2957}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31537}, {"moveId": "HYDRO_PUMP", "uses": 18285}, {"moveId": "BUBBLE_BEAM", "uses": 13539}, {"moveId": "WATER_PULSE", "uses": 13012}, {"moveId": "FRUSTRATION", "uses": 5}]}, "moveset": ["SNARL", "ICE_BEAM", "HYDRO_PUMP"], "score": 42.6}, {"speciesId": "forretress_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 323, "matchups": [{"opponent": "ferrothorn", "rating": 774, "opRating": 225}, {"opponent": "venusaur", "rating": 676, "opRating": 323}, {"opponent": "celebi", "rating": 658, "opRating": 341}, {"opponent": "steelix", "rating": 567, "opRating": 432}, {"opponent": "torterra", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "lugia", "rating": 164}, {"opponent": "dragonite", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 251}, {"opponent": "mewtwo", "rating": 276}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 47006}, {"moveId": "STRUGGLE_BUG", "uses": 29494}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 20496}, {"moveId": "EARTHQUAKE", "uses": 17497}, {"moveId": "HEAVY_SLAM", "uses": 16817}, {"moveId": "ROCK_TOMB", "uses": 13157}, {"moveId": "SAND_TOMB", "uses": 8568}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUG_BITE", "MIRROR_SHOT", "EARTHQUAKE"], "score": 42.4}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 307, "matchups": [{"opponent": "magnezone", "rating": 707, "opRating": 292}, {"opponent": "magnezone_shadow", "rating": 700, "opRating": 299}, {"opponent": "heatran", "rating": 626, "opRating": 373}, {"opponent": "muk_shadow", "rating": 605, "opRating": 394}, {"opponent": "steelix", "rating": 578, "opRating": 421}], "counters": [{"opponent": "zacian_hero", "rating": 164}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "dialga", "rating": 230}, {"opponent": "metagross", "rating": 290}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8221}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 6195}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5310}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5131}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4522}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4455}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4430}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4228}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4176}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4032}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4029}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4002}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3620}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3537}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3519}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3507}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3388}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33720}, {"moveId": "EARTH_POWER", "uses": 23705}, {"moveId": "EARTHQUAKE", "uses": 10305}, {"moveId": "WATER_PULSE", "uses": 8846}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 42.4}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 346, "matchups": [{"opponent": "machamp", "rating": 623, "opRating": 376}, {"opponent": "vaporeon", "rating": 603, "opRating": 396}, {"opponent": "melmetal", "rating": 581, "opRating": 418}, {"opponent": "cobalion", "rating": 512, "opRating": 487}, {"opponent": "regirock", "rating": 512, "opRating": 487}], "counters": [{"opponent": "giratina_origin", "rating": 115}, {"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "dragonite", "rating": 194}, {"opponent": "gyarados", "rating": 247}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44919}, {"moveId": "CHARM", "uses": 21857}, {"moveId": "LOW_KICK", "uses": 9731}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31137}, {"moveId": "LEAF_BLADE", "uses": 25796}, {"moveId": "SYNCHRONOISE", "uses": 13432}, {"moveId": "PSYCHIC", "uses": 6188}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 42.2}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 305, "matchups": [{"opponent": "torterra", "rating": 829, "opRating": 170}, {"opponent": "celebi", "rating": 643, "opRating": 356}, {"opponent": "swampert", "rating": 628}, {"opponent": "zarude", "rating": 615, "opRating": 384}, {"opponent": "swampert_shadow", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "metagross", "rating": 142}, {"opponent": "mewtwo", "rating": 236}, {"opponent": "garcho<PERSON>", "rating": 314}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 44928}, {"moveId": "RAZOR_LEAF", "uses": 31572}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42041}, {"moveId": "X_SCISSOR", "uses": 21429}, {"moveId": "SILVER_WIND", "uses": 7734}, {"moveId": "LEAF_STORM", "uses": 5156}]}, "moveset": ["BUG_BITE", "LEAF_BLADE", "X_SCISSOR"], "score": 42.2}, {"speciesId": "altaria", "speciesName": "Altaria", "rating": 306, "matchups": [{"opponent": "chesnaught", "rating": 676, "opRating": 323}, {"opponent": "seismitoad", "rating": 676, "opRating": 323}, {"opponent": "pinsir", "rating": 591, "opRating": 408}, {"opponent": "gliscor_shadow", "rating": 554, "opRating": 445}, {"opponent": "buzzwole", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 141}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "giratina_origin", "rating": 209}, {"opponent": "swampert", "rating": 407}, {"opponent": "grou<PERSON>", "rating": 464}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 52617}, {"moveId": "PECK", "uses": 23883}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 34980}, {"moveId": "MOONBLAST", "uses": 19791}, {"moveId": "DRAGON_PULSE", "uses": 16749}, {"moveId": "DAZZLING_GLEAM", "uses": 5111}]}, "moveset": ["DRAGON_BREATH", "SKY_ATTACK", "MOONBLAST"], "score": 42}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 330, "matchups": [{"opponent": "genesect_chill", "rating": 693, "opRating": 306}, {"opponent": "genesect_burn", "rating": 693, "opRating": 306}, {"opponent": "avalugg", "rating": 637, "opRating": 362}, {"opponent": "genesect_shock", "rating": 565, "opRating": 434}, {"opponent": "genesect", "rating": 565, "opRating": 434}], "counters": [{"opponent": "mewtwo", "rating": 91}, {"opponent": "giratina_origin", "rating": 111}, {"opponent": "dialga", "rating": 192}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "metagross", "rating": 238}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 62090}, {"moveId": "LOW_KICK", "uses": 14410}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 31123}, {"moveId": "ROCK_SLIDE", "uses": 20748}, {"moveId": "FOCUS_BLAST", "uses": 13645}, {"moveId": "FLAME_CHARGE", "uses": 8563}, {"moveId": "HEAT_WAVE", "uses": 2346}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 41.8}, {"speciesId": "tentacruel", "speciesName": "Tentacruel", "rating": 325, "matchups": [{"opponent": "tapu_fini", "rating": 625, "opRating": 375}, {"opponent": "florges", "rating": 581, "opRating": 418}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 563}, {"opponent": "sylveon", "rating": 514}, {"opponent": "ho_oh", "rating": 508}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "giratina_origin", "rating": 105}, {"opponent": "dragonite", "rating": 162}, {"opponent": "gyarados", "rating": 195}, {"opponent": "zacian_hero", "rating": 476}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56328}, {"moveId": "ACID", "uses": 20172}], "chargedMoves": [{"moveId": "SCALD", "uses": 30997}, {"moveId": "BLIZZARD", "uses": 17591}, {"moveId": "SLUDGE_WAVE", "uses": 15566}, {"moveId": "HYDRO_PUMP", "uses": 6948}, {"moveId": "ACID_SPRAY", "uses": 5308}]}, "moveset": ["POISON_JAB", "SCALD", "BLIZZARD"], "score": 41.8}, {"speciesId": "crobat", "speciesName": "<PERSON><PERSON>bat", "rating": 331, "matchups": [{"opponent": "virizion", "rating": 794, "opRating": 205}, {"opponent": "tapu_bulu", "rating": 724, "opRating": 275}, {"opponent": "buzzwole", "rating": 668, "opRating": 331}, {"opponent": "escavalier", "rating": 634, "opRating": 365}, {"opponent": "sneasler", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "giratina_origin", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 339}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45392}, {"moveId": "BITE", "uses": 31108}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 24418}, {"moveId": "SHADOW_BALL", "uses": 19584}, {"moveId": "POISON_FANG", "uses": 11065}, {"moveId": "AIR_CUTTER", "uses": 7446}, {"moveId": "RETURN", "uses": 7400}, {"moveId": "SLUDGE_BOMB", "uses": 6515}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 41.6}, {"speciesId": "dragalge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 345, "matchups": [{"opponent": "venusaur", "rating": 637, "opRating": 362}, {"opponent": "torterra", "rating": 604, "opRating": 395}, {"opponent": "chesnaught", "rating": 553, "opRating": 446}, {"opponent": "roserade", "rating": 523, "opRating": 476}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 125}, {"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "gyarados", "rating": 157}, {"opponent": "zacian_hero", "rating": 205}, {"opponent": "giratina_origin", "rating": 225}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 37652}, {"moveId": "WATER_GUN", "uses": 24271}, {"moveId": "ACID", "uses": 14498}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 31028}, {"moveId": "OUTRAGE", "uses": 27305}, {"moveId": "GUNK_SHOT", "uses": 13272}, {"moveId": "HYDRO_PUMP", "uses": 4951}]}, "moveset": ["DRAGON_TAIL", "AQUA_TAIL", "OUTRAGE"], "score": 41.6}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 319, "matchups": [{"opponent": "magnezone_shadow", "rating": 822, "opRating": 177}, {"opponent": "magnezone", "rating": 766, "opRating": 233}, {"opponent": "nihilego", "rating": 584, "opRating": 415}, {"opponent": "melmetal", "rating": 542, "opRating": 457}, {"opponent": "heatran", "rating": 507, "opRating": 492}], "counters": [{"opponent": "giratina_origin", "rating": 117}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "dialga", "rating": 217}, {"opponent": "lugia", "rating": 235}, {"opponent": "gyarados", "rating": 298}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 38703}, {"moveId": "THUNDER_SHOCK", "uses": 37797}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 33854}, {"moveId": "DISCHARGE", "uses": 26058}, {"moveId": "MUDDY_WATER", "uses": 16576}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "DISCHARGE"], "score": 41.6}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 280, "matchups": [{"opponent": "gengar", "rating": 673, "opRating": 326}, {"opponent": "lickilicky", "rating": 555, "opRating": 444}, {"opponent": "giratina_origin", "rating": 524}, {"opponent": "dragalge", "rating": 514, "opRating": 485}, {"opponent": "aggron", "rating": 512, "opRating": 487}], "counters": [{"opponent": "zacian_hero", "rating": 124}, {"opponent": "mewtwo", "rating": 148}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "dialga", "rating": 165}, {"opponent": "metagross", "rating": 206}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 57597}, {"moveId": "LOW_KICK", "uses": 18903}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19629}, {"moveId": "STOMP", "uses": 17805}, {"moveId": "BRICK_BREAK", "uses": 13314}, {"moveId": "OUTRAGE", "uses": 11607}, {"moveId": "EARTHQUAKE", "uses": 11426}, {"moveId": "POWER_UP_PUNCH", "uses": 2738}]}, "moveset": ["MUD_SLAP", "CRUNCH", "STOMP"], "score": 41.4}, {"speciesId": "pinsir", "speciesName": "Pinsir", "rating": 380, "matchups": [{"opponent": "obstagoon", "rating": 812, "opRating": 187}, {"opponent": "bewear", "rating": 661, "opRating": 338}, {"opponent": "zarude", "rating": 577, "opRating": 422}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 567, "opRating": 432}, {"opponent": "celebi", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "zacian_hero", "rating": 130}, {"opponent": "gyarados", "rating": 188}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "swampert", "rating": 253}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40274}, {"moveId": "BUG_BITE", "uses": 26406}, {"moveId": "ROCK_SMASH", "uses": 9795}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 22933}, {"moveId": "X_SCISSOR", "uses": 20038}, {"moveId": "SUPER_POWER", "uses": 17058}, {"moveId": "RETURN", "uses": 7264}, {"moveId": "VICE_GRIP", "uses": 6536}, {"moveId": "SUBMISSION", "uses": 2758}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 41.4}, {"speciesId": "blastoise_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 306, "matchups": [{"opponent": "entei", "rating": 691, "opRating": 308}, {"opponent": "heatran", "rating": 667, "opRating": 332}, {"opponent": "hippo<PERSON><PERSON>", "rating": 594, "opRating": 405}, {"opponent": "articuno", "rating": 582, "opRating": 417}, {"opponent": "steelix", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "metagross", "rating": 194}, {"opponent": "zacian_hero", "rating": 222}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 51616}, {"moveId": "BITE", "uses": 24884}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 40300}, {"moveId": "ICE_BEAM", "uses": 16981}, {"moveId": "SKULL_BASH", "uses": 8984}, {"moveId": "FLASH_CANNON", "uses": 5881}, {"moveId": "HYDRO_PUMP", "uses": 4226}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "HYDRO_CANNON", "ICE_BEAM"], "score": 41.2}, {"speciesId": "poliwrath_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 332, "matchups": [{"opponent": "obstagoon", "rating": 776, "opRating": 223}, {"opponent": "empoleon", "rating": 706, "opRating": 293}, {"opponent": "heatran", "rating": 680, "opRating": 319}, {"opponent": "tyranitar", "rating": 599, "opRating": 400}, {"opponent": "vaporeon", "rating": 591, "opRating": 408}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "gyarados", "rating": 146}, {"opponent": "metagross", "rating": 194}, {"opponent": "giratina_origin", "rating": 195}, {"opponent": "garcho<PERSON>", "rating": 319}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36068}, {"moveId": "BUBBLE", "uses": 29931}, {"moveId": "ROCK_SMASH", "uses": 10533}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 21668}, {"moveId": "DYNAMIC_PUNCH", "uses": 20082}, {"moveId": "SCALD", "uses": 19209}, {"moveId": "POWER_UP_PUNCH", "uses": 6779}, {"moveId": "SUBMISSION", "uses": 4552}, {"moveId": "HYDRO_PUMP", "uses": 4412}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ICE_PUNCH", "DYNAMIC_PUNCH"], "score": 41.2}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 284, "matchups": [{"opponent": "seismitoad", "rating": 878, "opRating": 121}, {"opponent": "swampert", "rating": 731}, {"opponent": "swampert_shadow", "rating": 689, "opRating": 310}, {"opponent": "relicanth", "rating": 681, "opRating": 318}, {"opponent": "chesnaught", "rating": 594, "opRating": 405}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "mewtwo", "rating": 182}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "garcho<PERSON>", "rating": 213}, {"opponent": "gyarados", "rating": 234}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6293}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5718}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 5675}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5064}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4969}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4896}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4707}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4598}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4520}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4472}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4405}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4341}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3981}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3938}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3846}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3646}, {"moveId": "ZEN_HEADBUTT", "uses": 1351}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41192}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12215}, {"moveId": "SOLAR_BEAM", "uses": 8437}]}, "moveset": ["HIDDEN_POWER_GRASS", "GRASS_KNOT", "ENERGY_BALL"], "score": 41.2}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 306, "matchups": [{"opponent": "mewtwo_shadow", "rating": 659, "opRating": 340}, {"opponent": "steelix", "rating": 608, "opRating": 391}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 579, "opRating": 420}, {"opponent": "nidoqueen", "rating": 554, "opRating": 445}, {"opponent": "moltres_galarian", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "lugia", "rating": 300}, {"opponent": "gyarados", "rating": 360}, {"opponent": "mewtwo", "rating": 429}, {"opponent": "giratina_origin", "rating": 432}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30672}, {"moveId": "ICE_SHARD", "uses": 27836}, {"moveId": "FEINT_ATTACK", "uses": 18068}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 38247}, {"moveId": "FOUL_PLAY", "uses": 21882}, {"moveId": "FOCUS_BLAST", "uses": 9839}, {"moveId": "RETURN", "uses": 6568}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 41.2}, {"speciesId": "durant", "speciesName": "<PERSON><PERSON>", "rating": 345, "matchups": [{"opponent": "celebi", "rating": 737, "opRating": 262}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 683, "opRating": 316}, {"opponent": "zarude", "rating": 629, "opRating": 370}, {"opponent": "cresselia", "rating": 604, "opRating": 395}, {"opponent": "articuno", "rating": 568, "opRating": 431}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "metagross", "rating": 177}, {"opponent": "mewtwo", "rating": 276}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 43332}, {"moveId": "METAL_CLAW", "uses": 33168}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 32668}, {"moveId": "STONE_EDGE", "uses": 23857}, {"moveId": "IRON_HEAD", "uses": 19960}]}, "moveset": ["BUG_BITE", "X_SCISSOR", "STONE_EDGE"], "score": 41}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 365, "matchups": [{"opponent": "magnezone", "rating": 831, "opRating": 168}, {"opponent": "magnezone_shadow", "rating": 813, "opRating": 186}, {"opponent": "nihilego", "rating": 785, "opRating": 214}, {"opponent": "genesect_shock", "rating": 560, "opRating": 439}, {"opponent": "zap<PERSON>_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "mewtwo", "rating": 164}, {"opponent": "dragonite", "rating": 191}, {"opponent": "gyarados", "rating": 193}, {"opponent": "lugia", "rating": 235}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 50494}, {"moveId": "METAL_CLAW", "uses": 26006}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26258}, {"moveId": "EARTHQUAKE", "uses": 23726}, {"moveId": "MUDDY_WATER", "uses": 15309}, {"moveId": "FLASH_CANNON", "uses": 11205}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 40.7}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 303, "matchups": [{"opponent": "rhydon", "rating": 755, "opRating": 244}, {"opponent": "relicanth", "rating": 726, "opRating": 273}, {"opponent": "swampert_shadow", "rating": 694, "opRating": 305}, {"opponent": "primarina", "rating": 659, "opRating": 340}, {"opponent": "tapu_fini", "rating": 613, "opRating": 386}], "counters": [{"opponent": "dialga", "rating": 73}, {"opponent": "garcho<PERSON>", "rating": 147}, {"opponent": "gyarados", "rating": 211}, {"opponent": "zacian_hero", "rating": 245}, {"opponent": "swampert", "rating": 477}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 43955}, {"moveId": "ACID", "uses": 32545}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 39422}, {"moveId": "SLUDGE_BOMB", "uses": 15220}, {"moveId": "LEAF_TORNADO", "uses": 7596}, {"moveId": "RETURN", "uses": 7456}, {"moveId": "ACID_SPRAY", "uses": 3461}, {"moveId": "SOLAR_BEAM", "uses": 3313}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 40.7}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 312, "matchups": [{"opponent": "gengar", "rating": 748, "opRating": 251}, {"opponent": "sneasler", "rating": 672, "opRating": 327}, {"opponent": "muk", "rating": 560, "opRating": 439}, {"opponent": "golisopod", "rating": 554, "opRating": 445}, {"opponent": "nidoqueen", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "zacian_hero", "rating": 130}, {"opponent": "garcho<PERSON>", "rating": 133}, {"opponent": "gyarados", "rating": 188}, {"opponent": "giratina_origin", "rating": 440}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 39113}, {"moveId": "POUND", "uses": 37387}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 29326}, {"moveId": "HYPER_BEAM", "uses": 24668}, {"moveId": "DAZZLING_GLEAM", "uses": 22495}]}, "moveset": ["ZEN_HEADBUTT", "PSYCHIC", "HYPER_BEAM"], "score": 40.5}, {"speciesId": "klinklang", "speciesName": "Klinklang", "rating": 332, "matchups": [{"opponent": "primarina", "rating": 714, "opRating": 285}, {"opponent": "articuno_galarian", "rating": 711, "opRating": 288}, {"opponent": "gyarados", "rating": 630}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 623}, {"opponent": "tapu_fini", "rating": 552, "opRating": 447}], "counters": [{"opponent": "giratina_origin", "rating": 53}, {"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 96}, {"opponent": "lugia", "rating": 397}, {"opponent": "sylveon", "rating": 417}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 48840}, {"moveId": "CHARGE_BEAM", "uses": 27660}], "chargedMoves": [{"moveId": "MIRROR_SHOT", "uses": 29913}, {"moveId": "ZAP_CANNON", "uses": 19263}, {"moveId": "FLASH_CANNON", "uses": 15568}, {"moveId": "HYPER_BEAM", "uses": 11764}]}, "moveset": ["THUNDER_SHOCK", "MIRROR_SHOT", "ZAP_CANNON"], "score": 40.5}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 246, "matchups": [{"opponent": "gengar", "rating": 594, "opRating": 405}, {"opponent": "run<PERSON><PERSON>", "rating": 594, "opRating": 405}, {"opponent": "cofagrigus", "rating": 582, "opRating": 417}, {"opponent": "drifb<PERSON>", "rating": 528, "opRating": 471}, {"opponent": "gourgeist_large", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "garcho<PERSON>", "rating": 161}, {"opponent": "lugia", "rating": 195}, {"opponent": "mewtwo", "rating": 286}, {"opponent": "giratina_origin", "rating": 352}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 47030}, {"moveId": "ASTONISH", "uses": 29470}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28023}, {"moveId": "DISARMING_VOICE", "uses": 24146}, {"moveId": "RETURN", "uses": 14680}, {"moveId": "FIRE_BLAST", "uses": 9752}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 40.3}, {"speciesId": "tornadus_incarnate", "speciesName": "Tornadus (Incarnate)", "rating": 296, "matchups": [{"opponent": "chesnaught", "rating": 738, "opRating": 261}, {"opponent": "ferrothorn", "rating": 650, "opRating": 350}, {"opponent": "virizion", "rating": 647, "opRating": 352}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 647, "opRating": 352}, {"opponent": "heracross", "rating": 608, "opRating": 391}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 174}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "gyarados", "rating": 208}, {"opponent": "garcho<PERSON>", "rating": 213}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47983}, {"moveId": "BITE", "uses": 28517}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25180}, {"moveId": "GRASS_KNOT", "uses": 21794}, {"moveId": "HURRICANE", "uses": 20327}, {"moveId": "HYPER_BEAM", "uses": 9201}]}, "moveset": ["AIR_SLASH", "DARK_PULSE", "GRASS_KNOT"], "score": 40.3}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 251, "matchups": [{"opponent": "gourgeist_super", "rating": 713, "opRating": 286}, {"opponent": "mewtwo", "rating": 659}, {"opponent": "steelix", "rating": 611, "opRating": 388}, {"opponent": "torterra", "rating": 579, "opRating": 420}, {"opponent": "giratina_origin", "rating": 566}], "counters": [{"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "dialga", "rating": 84}, {"opponent": "lugia", "rating": 102}, {"opponent": "gyarados", "rating": 110}, {"opponent": "metagross", "rating": 119}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 31599}, {"moveId": "ICE_SHARD", "uses": 27735}, {"moveId": "FEINT_ATTACK", "uses": 17141}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41686}, {"moveId": "FOUL_PLAY", "uses": 23973}, {"moveId": "FOCUS_BLAST", "uses": 10749}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "AVALANCHE", "FOCUS_BLAST"], "score": 40.3}, {"speciesId": "xurkitree", "speciesName": "Xurk<PERSON><PERSON>", "rating": 358, "matchups": [{"opponent": "articuno_galarian", "rating": 698, "opRating": 301}, {"opponent": "milotic", "rating": 693, "opRating": 306}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 622}, {"opponent": "zapdos", "rating": 590, "opRating": 409}, {"opponent": "primarina", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "gyarados", "rating": 286}, {"opponent": "lugia", "rating": 333}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 40806}, {"moveId": "SPARK", "uses": 35694}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 28544}, {"moveId": "POWER_WHIP", "uses": 23103}, {"moveId": "DAZZLING_GLEAM", "uses": 14288}, {"moveId": "THUNDER", "uses": 10558}]}, "moveset": ["THUNDER_SHOCK", "DISCHARGE", "DAZZLING_GLEAM"], "score": 40.3}, {"speciesId": "arm<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 311, "matchups": [{"opponent": "lilligant", "rating": 722, "opRating": 277}, {"opponent": "mr_rime", "rating": 621, "opRating": 378}, {"opponent": "leavanny", "rating": 612, "opRating": 387}, {"opponent": "aromatisse", "rating": 603, "opRating": 396}, {"opponent": "ninetales_alolan", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "giratina_origin", "rating": 173}, {"opponent": "zacian_hero", "rating": 219}, {"opponent": "lugia", "rating": 235}, {"opponent": "mewtwo", "rating": 333}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 51252}, {"moveId": "STRUGGLE_BUG", "uses": 25248}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 29045}, {"moveId": "CROSS_POISON", "uses": 26910}, {"moveId": "RETURN", "uses": 11290}, {"moveId": "WATER_PULSE", "uses": 9274}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 40.1}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 305, "matchups": [{"opponent": "gourgeist_super", "rating": 621, "opRating": 378}, {"opponent": "gourgeist_large", "rating": 621, "opRating": 378}, {"opponent": "lickilicky", "rating": 569, "opRating": 430}, {"opponent": "trevenant", "rating": 554, "opRating": 445}, {"opponent": "giratina_origin", "rating": 520}], "counters": [{"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "zacian_hero", "rating": 138}, {"opponent": "mewtwo", "rating": 156}, {"opponent": "gyarados", "rating": 190}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 289}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 36167}, {"moveId": "TACKLE", "uses": 35229}, {"moveId": "ZEN_HEADBUTT", "uses": 5132}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 33454}, {"moveId": "ICE_BEAM", "uses": 16629}, {"moveId": "THUNDERBOLT", "uses": 12094}, {"moveId": "STOMP", "uses": 8275}, {"moveId": "GYRO_BALL", "uses": 6030}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 40.1}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 260, "matchups": [{"opponent": "forretress", "rating": 592, "opRating": 407}, {"opponent": "ferrothorn", "rating": 579, "opRating": 420}, {"opponent": "gourgeist_super", "rating": 557, "opRating": 442}, {"opponent": "virizion", "rating": 547, "opRating": 452}, {"opponent": "obstagoon", "rating": 522, "opRating": 477}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "gyarados", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "giratina_origin", "rating": 482}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 51993}, {"moveId": "STEEL_WING", "uses": 24507}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 28722}, {"moveId": "CLOSE_COMBAT", "uses": 26572}, {"moveId": "ROCK_SLIDE", "uses": 17803}, {"moveId": "HEAT_WAVE", "uses": 3405}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 39.9}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 315, "matchups": [{"opponent": "heatran", "rating": 637, "opRating": 362}, {"opponent": "avalugg", "rating": 627, "opRating": 372}, {"opponent": "sylveon", "rating": 545}, {"opponent": "zarude", "rating": 543, "opRating": 456}, {"opponent": "florges", "rating": 538, "opRating": 461}], "counters": [{"opponent": "mewtwo", "rating": 88}, {"opponent": "dialga", "rating": 125}, {"opponent": "zacian_hero", "rating": 153}, {"opponent": "metagross", "rating": 258}, {"opponent": "lugia", "rating": 340}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 42532}, {"moveId": "FIRE_FANG", "uses": 17419}, {"moveId": "TACKLE", "uses": 16529}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26979}, {"moveId": "OVERHEAT", "uses": 21169}, {"moveId": "PSYCHIC", "uses": 14499}, {"moveId": "FOCUS_BLAST", "uses": 13790}]}, "moveset": ["INCINERATE", "ROCK_SLIDE", "OVERHEAT"], "score": 39.9}, {"speciesId": "lanturn", "speciesName": "Lanturn", "rating": 312, "matchups": [{"opponent": "articuno", "rating": 607, "opRating": 392}, {"opponent": "gyarados", "rating": 575}, {"opponent": "articuno_shadow", "rating": 510, "opRating": 489}, {"opponent": "empoleon", "rating": 508, "opRating": 491}, {"opponent": "suicune", "rating": 506, "opRating": 493}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "giratina_origin", "rating": 101}, {"opponent": "dialga", "rating": 119}, {"opponent": "zacian_hero", "rating": 196}, {"opponent": "lugia", "rating": 250}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 29460}, {"moveId": "WATER_GUN", "uses": 28537}, {"moveId": "CHARGE_BEAM", "uses": 18470}], "chargedMoves": [{"moveId": "SURF", "uses": 39476}, {"moveId": "THUNDERBOLT", "uses": 21480}, {"moveId": "THUNDER", "uses": 9270}, {"moveId": "HYDRO_PUMP", "uses": 6176}]}, "moveset": ["SPARK", "SURF", "THUNDERBOLT"], "score": 39.9}, {"speciesId": "slowking_galarian", "speciesName": "Slowking (Galarian)", "rating": 310, "matchups": [{"opponent": "sneasler", "rating": 682, "opRating": 317}, {"opponent": "florges", "rating": 582, "opRating": 417}, {"opponent": "cobalion", "rating": 546, "opRating": 453}, {"opponent": "virizion", "rating": 525, "opRating": 474}, {"opponent": "sylveon", "rating": 520}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "gyarados", "rating": 203}, {"opponent": "dragonite", "rating": 210}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 306}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 31693}, {"moveId": "HEX", "uses": 30515}, {"moveId": "ACID", "uses": 14320}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32811}, {"moveId": "FUTURE_SIGHT", "uses": 24288}, {"moveId": "SLUDGE_WAVE", "uses": 19390}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 39.7}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 313, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 576, "opRating": 423}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 573, "opRating": 426}, {"opponent": "heracross", "rating": 560, "opRating": 439}, {"opponent": "sneasler", "rating": 560, "opRating": 439}, {"opponent": "walrein", "rating": 533, "opRating": 466}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "gyarados", "rating": 201}, {"opponent": "zacian_hero", "rating": 210}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 42735}, {"moveId": "EXTRASENSORY", "uses": 33765}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 35882}, {"moveId": "THUNDER", "uses": 28077}, {"moveId": "SWIFT", "uses": 12530}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "THUNDER"], "score": 39.7}, {"speciesId": "vikavolt", "speciesName": "Vikavolt", "rating": 327, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 685, "opRating": 314}, {"opponent": "drifb<PERSON>", "rating": 610, "opRating": 389}, {"opponent": "klinklang", "rating": 604, "opRating": 395}, {"opponent": "poliwrath_shadow", "rating": 580, "opRating": 419}, {"opponent": "gyarados", "rating": 541}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "zacian_hero", "rating": 176}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 233}, {"opponent": "lugia", "rating": 290}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 31699}, {"moveId": "BUG_BITE", "uses": 23795}, {"moveId": "MUD_SLAP", "uses": 20951}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 26875}, {"moveId": "CRUNCH", "uses": 25606}, {"moveId": "DISCHARGE", "uses": 24101}]}, "moveset": ["SPARK", "X_SCISSOR", "CRUNCH"], "score": 39.7}, {"speciesId": "carracosta", "speciesName": "Carracosta", "rating": 318, "matchups": [{"opponent": "entei", "rating": 803, "opRating": 196}, {"opponent": "entei_shadow", "rating": 803, "opRating": 196}, {"opponent": "moltres", "rating": 760, "opRating": 239}, {"opponent": "ho_oh_shadow", "rating": 696, "opRating": 303}, {"opponent": "heatran", "rating": 665, "opRating": 334}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "zacian_hero", "rating": 130}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 185}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 39284}, {"moveId": "ROCK_THROW", "uses": 37216}], "chargedMoves": [{"moveId": "SURF", "uses": 29827}, {"moveId": "BODY_SLAM", "uses": 25688}, {"moveId": "ANCIENT_POWER", "uses": 21017}]}, "moveset": ["WATER_GUN", "SURF", "BODY_SLAM"], "score": 39.5}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 251, "matchups": [{"opponent": "steelix", "rating": 612, "opRating": 387}, {"opponent": "<PERSON>ras", "rating": 599, "opRating": 400}, {"opponent": "relicanth", "rating": 599, "opRating": 400}, {"opponent": "stunfisk_galarian", "rating": 594, "opRating": 405}, {"opponent": "giratina_origin", "rating": 548}], "counters": [{"opponent": "mewtwo", "rating": 130}, {"opponent": "dialga", "rating": 133}, {"opponent": "lugia", "rating": 145}, {"opponent": "gyarados", "rating": 157}, {"opponent": "metagross", "rating": 165}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34821}, {"moveId": "COUNTER", "uses": 30683}, {"moveId": "METAL_CLAW", "uses": 10988}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 43703}, {"moveId": "PLAY_ROUGH", "uses": 17086}, {"moveId": "HYPER_BEAM", "uses": 15432}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "PLAY_ROUGH"], "score": 39.5}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 248, "matchups": [{"opponent": "gengar", "rating": 731, "opRating": 268}, {"opponent": "hoopa", "rating": 661, "opRating": 338}, {"opponent": "run<PERSON><PERSON>", "rating": 618, "opRating": 381}, {"opponent": "cofagrigus", "rating": 519, "opRating": 480}, {"opponent": "trevenant", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 146}, {"opponent": "lugia", "rating": 166}, {"opponent": "mewtwo", "rating": 169}, {"opponent": "gyarados", "rating": 216}, {"opponent": "giratina_origin", "rating": 420}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 45625}, {"moveId": "ASTONISH", "uses": 30875}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34320}, {"moveId": "DISARMING_VOICE", "uses": 30055}, {"moveId": "FIRE_BLAST", "uses": 11977}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "DISARMING_VOICE"], "score": 39.3}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 296, "matchups": [{"opponent": "pinsir", "rating": 640, "opRating": 359}, {"opponent": "machamp_shadow", "rating": 611, "opRating": 388}, {"opponent": "magnezone_shadow", "rating": 535, "opRating": 464}, {"opponent": "registeel", "rating": 521, "opRating": 478}, {"opponent": "escavalier", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "lugia", "rating": 157}, {"opponent": "zacian_hero", "rating": 326}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 60603}, {"moveId": "ASTONISH", "uses": 15897}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 41489}, {"moveId": "ROCK_TOMB", "uses": 17592}, {"moveId": "SAND_TOMB", "uses": 17424}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "ROCK_TOMB"], "score": 39.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 293, "matchups": [{"opponent": "heracross", "rating": 667, "opRating": 332}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 585, "opRating": 414}, {"opponent": "machamp", "rating": 573, "opRating": 426}, {"opponent": "sneasler", "rating": 527, "opRating": 472}, {"opponent": "machamp_shadow", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 108}, {"opponent": "garcho<PERSON>", "rating": 126}, {"opponent": "dragonite", "rating": 180}, {"opponent": "gyarados", "rating": 185}, {"opponent": "zacian_hero", "rating": 216}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43175}, {"moveId": "EXTRASENSORY", "uses": 33325}], "chargedMoves": [{"moveId": "FUTURE_SIGHT", "uses": 39686}, {"moveId": "FIRE_BLAST", "uses": 22575}, {"moveId": "SWIFT", "uses": 14268}]}, "moveset": ["CONFUSION", "FUTURE_SIGHT", "FIRE_BLAST"], "score": 38.9}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 236, "matchups": [{"opponent": "pheromosa", "rating": 725, "opRating": 274}, {"opponent": "shiftry_shadow", "rating": 594, "opRating": 405}, {"opponent": "munchlax", "rating": 588, "opRating": 411}, {"opponent": "umbreon", "rating": 536, "opRating": 463}, {"opponent": "mandibuzz", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 163}, {"opponent": "gyarados", "rating": 185}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "dragonite", "rating": 226}, {"opponent": "giratina_origin", "rating": 360}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 64757}, {"moveId": "POUND", "uses": 11743}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 38732}, {"moveId": "THUNDERBOLT", "uses": 21569}, {"moveId": "HYPER_BEAM", "uses": 16151}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 38.9}, {"speciesId": "crobat_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 312, "matchups": [{"opponent": "pinsir", "rating": 808, "opRating": 191}, {"opponent": "buzzwole", "rating": 783, "opRating": 216}, {"opponent": "virizion", "rating": 764, "opRating": 235}, {"opponent": "heracross", "rating": 637, "opRating": 362}, {"opponent": "escavalier", "rating": 556, "opRating": 443}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "giratina_origin", "rating": 107}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "gyarados", "rating": 164}, {"opponent": "zacian_hero", "rating": 393}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 45822}, {"moveId": "BITE", "uses": 30678}], "chargedMoves": [{"moveId": "CROSS_POISON", "uses": 26927}, {"moveId": "SHADOW_BALL", "uses": 21750}, {"moveId": "POISON_FANG", "uses": 12083}, {"moveId": "AIR_CUTTER", "uses": 8267}, {"moveId": "SLUDGE_BOMB", "uses": 7312}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "CROSS_POISON", "SHADOW_BALL"], "score": 38.9}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 318, "matchups": [{"opponent": "gardevoir", "rating": 720, "opRating": 279}, {"opponent": "primarina", "rating": 686, "opRating": 313}, {"opponent": "gardevoir_shadow", "rating": 674, "opRating": 325}, {"opponent": "tapu_fini", "rating": 665, "opRating": 334}, {"opponent": "kyogre", "rating": 610}], "counters": [{"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "gyarados", "rating": 414}, {"opponent": "swampert", "rating": 420}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 421}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54287}, {"moveId": "RAZOR_LEAF", "uses": 22213}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 42972}, {"moveId": "SLUDGE_BOMB", "uses": 22732}, {"moveId": "PETAL_BLIZZARD", "uses": 5924}, {"moveId": "SOLAR_BEAM", "uses": 4967}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 38.9}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 349, "matchups": [{"opponent": "moltres", "rating": 768, "opRating": 231}, {"opponent": "entei", "rating": 621, "opRating": 378}, {"opponent": "ho_oh_shadow", "rating": 618, "opRating": 381}, {"opponent": "avalugg", "rating": 585, "opRating": 414}, {"opponent": "genesect_burn", "rating": 574, "opRating": 425}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 130}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "lugia", "rating": 278}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 359}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41592}, {"moveId": "FIRE_FANG", "uses": 25073}, {"moveId": "ROCK_SMASH", "uses": 9764}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22275}, {"moveId": "ROCK_SLIDE", "uses": 21160}, {"moveId": "CRUNCH", "uses": 17821}, {"moveId": "FLAMETHROWER", "uses": 15261}]}, "moveset": ["SNARL", "WILD_CHARGE", "ROCK_SLIDE"], "score": 38.7}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 276, "matchups": [{"opponent": "bisharp", "rating": 781, "opRating": 218}, {"opponent": "tyranitar_shadow", "rating": 746, "opRating": 253}, {"opponent": "obstagoon", "rating": 647, "opRating": 352}, {"opponent": "relicanth", "rating": 591, "opRating": 408}, {"opponent": "registeel", "rating": 552, "opRating": 447}], "counters": [{"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "metagross", "rating": 162}, {"opponent": "dialga", "rating": 263}, {"opponent": "swampert", "rating": 271}, {"opponent": "excadrill", "rating": 420}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44253}, {"moveId": "BULLET_SEED", "uses": 32247}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25555}, {"moveId": "GRASS_KNOT", "uses": 19204}, {"moveId": "SEED_BOMB", "uses": 17635}, {"moveId": "SLUDGE_BOMB", "uses": 14207}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 38.7}, {"speciesId": "al<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 263, "matchups": [{"opponent": "aggron_shadow", "rating": 666, "opRating": 333}, {"opponent": "tyranitar_shadow", "rating": 629, "opRating": 370}, {"opponent": "aurorus", "rating": 588, "opRating": 411}, {"opponent": "obstagoon", "rating": 533, "opRating": 466}, {"opponent": "registeel", "rating": 522, "opRating": 477}], "counters": [{"opponent": "mewtwo", "rating": 96}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "metagross", "rating": 188}, {"opponent": "dialga", "rating": 285}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29481}, {"moveId": "PSYCHO_CUT", "uses": 26488}, {"moveId": "CONFUSION", "uses": 20493}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 18315}, {"moveId": "FIRE_PUNCH", "uses": 16661}, {"moveId": "PSYCHIC", "uses": 15554}, {"moveId": "FOCUS_BLAST", "uses": 10929}, {"moveId": "DAZZLING_GLEAM", "uses": 8263}, {"moveId": "FUTURE_SIGHT", "uses": 6697}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 38.4}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 300, "matchups": [{"opponent": "swampert_shadow", "rating": 640, "opRating": 359}, {"opponent": "swampert", "rating": 617}, {"opponent": "vaporeon", "rating": 592, "opRating": 407}, {"opponent": "kyogre", "rating": 554, "opRating": 445}, {"opponent": "tapu_fini", "rating": 509, "opRating": 490}], "counters": [{"opponent": "mewtwo", "rating": 127}, {"opponent": "zacian_hero", "rating": 196}, {"opponent": "gyarados", "rating": 208}, {"opponent": "garcho<PERSON>", "rating": 215}, {"opponent": "excadrill", "rating": 390}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50872}, {"moveId": "RAZOR_LEAF", "uses": 25628}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37313}, {"moveId": "SUPER_POWER", "uses": 20641}, {"moveId": "X_SCISSOR", "uses": 13978}, {"moveId": "LEAF_STORM", "uses": 4638}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 38.2}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 307, "matchups": [{"opponent": "sci<PERSON>_shadow", "rating": 670, "opRating": 329}, {"opponent": "genesect_chill", "rating": 664, "opRating": 335}, {"opponent": "genesect_burn", "rating": 664, "opRating": 335}, {"opponent": "genesect_shock", "rating": 546, "opRating": 453}, {"opponent": "genesect", "rating": 546, "opRating": 453}], "counters": [{"opponent": "lugia", "rating": 128}, {"opponent": "mewtwo", "rating": 130}, {"opponent": "metagross", "rating": 159}, {"opponent": "dialga", "rating": 211}, {"opponent": "zacian_hero", "rating": 239}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 27809}, {"moveId": "EMBER", "uses": 27778}, {"moveId": "FEINT_ATTACK", "uses": 20924}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28159}, {"moveId": "PSYSHOCK", "uses": 12260}, {"moveId": "OVERHEAT", "uses": 11273}, {"moveId": "SOLAR_BEAM", "uses": 6623}, {"moveId": "RETURN", "uses": 6567}, {"moveId": "FLAMETHROWER", "uses": 6142}, {"moveId": "FIRE_BLAST", "uses": 3307}, {"moveId": "HEAT_WAVE", "uses": 2001}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 38.2}, {"speciesId": "slowking_shadow", "speciesName": "Slowking (Shadow)", "rating": 279, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 605, "opRating": 394}, {"opponent": "sneasler", "rating": 597, "opRating": 402}, {"opponent": "<PERSON>ras", "rating": 554, "opRating": 445}, {"opponent": "machamp_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "gyarados", "rating": 154}, {"opponent": "dragonite", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 228}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40138}, {"moveId": "WATER_GUN", "uses": 36362}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 33183}, {"moveId": "BLIZZARD", "uses": 26070}, {"moveId": "FIRE_BLAST", "uses": 17154}, {"moveId": "FRUSTRATION", "uses": 8}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 38.2}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 269, "matchups": [{"opponent": "escavalier", "rating": 681, "opRating": 318}, {"opponent": "genesect_burn", "rating": 630, "opRating": 369}, {"opponent": "buzzwole", "rating": 565, "opRating": 434}, {"opponent": "virizion", "rating": 544, "opRating": 455}, {"opponent": "metagross", "rating": 541}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "garcho<PERSON>", "rating": 119}, {"opponent": "lugia", "rating": 250}, {"opponent": "grou<PERSON>", "rating": 255}, {"opponent": "zacian_hero", "rating": 274}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34669}, {"moveId": "FIRE_SPIN", "uses": 21186}, {"moveId": "PECK", "uses": 10717}, {"moveId": "STEEL_WING", "uses": 9832}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37691}, {"moveId": "FLAME_CHARGE", "uses": 21947}, {"moveId": "FIRE_BLAST", "uses": 9990}, {"moveId": "HURRICANE", "uses": 6915}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "FLAME_CHARGE"], "score": 38.2}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 299, "matchups": [{"opponent": "torterra", "rating": 784, "opRating": 215}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 758, "opRating": 241}, {"opponent": "salamence", "rating": 604, "opRating": 395}, {"opponent": "salamence_shadow", "rating": 529, "opRating": 470}, {"opponent": "landorus_incarnate", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "dragonite", "rating": 207}, {"opponent": "gyarados", "rating": 260}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 393}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 55039}, {"moveId": "RAZOR_LEAF", "uses": 21461}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 42680}, {"moveId": "ENERGY_BALL", "uses": 16068}, {"moveId": "OUTRAGE", "uses": 11237}, {"moveId": "BLIZZARD", "uses": 6489}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 38}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 348, "matchups": [{"opponent": "gourgeist_super", "rating": 617, "opRating": 382}, {"opponent": "trevenant", "rating": 614, "opRating": 385}, {"opponent": "ferrothorn", "rating": 582, "opRating": 417}, {"opponent": "deoxys_defense", "rating": 579, "opRating": 420}, {"opponent": "espeon", "rating": 557, "opRating": 442}], "counters": [{"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 179}, {"opponent": "lugia", "rating": 190}, {"opponent": "giratina_origin", "rating": 223}, {"opponent": "gyarados", "rating": 231}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24649}, {"moveId": "INFESTATION", "uses": 18957}, {"moveId": "ICE_FANG", "uses": 17736}, {"moveId": "BITE", "uses": 15132}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28376}, {"moveId": "AQUA_TAIL", "uses": 20683}, {"moveId": "SLUDGE_BOMB", "uses": 15427}, {"moveId": "RETURN", "uses": 7092}, {"moveId": "FELL_STINGER", "uses": 4992}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 38}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 274, "matchups": [{"opponent": "swampert", "rating": 651}, {"opponent": "vaporeon", "rating": 645, "opRating": 354}, {"opponent": "kyogre", "rating": 622}, {"opponent": "tapu_fini", "rating": 572, "opRating": 427}, {"opponent": "primarina", "rating": 529, "opRating": 470}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "garcho<PERSON>", "rating": 338}, {"opponent": "gyarados", "rating": 391}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 54392}, {"moveId": "RAZOR_LEAF", "uses": 22108}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 45452}, {"moveId": "EARTHQUAKE", "uses": 19561}, {"moveId": "PETAL_BLIZZARD", "uses": 6228}, {"moveId": "SOLAR_BEAM", "uses": 5122}, {"moveId": "FRUSTRATION", "uses": 28}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 38}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 319, "matchups": [{"opponent": "celebi", "rating": 677, "opRating": 322}, {"opponent": "tapu_bulu", "rating": 624, "opRating": 375}, {"opponent": "nidoqueen", "rating": 597, "opRating": 402}, {"opponent": "golisopod", "rating": 550, "opRating": 449}, {"opponent": "s<PERSON><PERSON>", "rating": 540, "opRating": 459}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "gyarados", "rating": 157}, {"opponent": "lugia", "rating": 197}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 43690}, {"moveId": "STEEL_WING", "uses": 32810}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33564}, {"moveId": "SKY_ATTACK", "uses": 25162}, {"moveId": "FLASH_CANNON", "uses": 9650}, {"moveId": "RETURN", "uses": 8189}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 38}, {"speciesId": "slowbro", "speciesName": "Slowbro", "rating": 286, "matchups": [{"opponent": "ninetales_alolan", "rating": 659, "opRating": 340}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 623, "opRating": 376}, {"opponent": "sneasler", "rating": 621, "opRating": 378}, {"opponent": "blaziken", "rating": 615, "opRating": 384}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dragonite", "rating": 194}, {"opponent": "zacian_hero", "rating": 205}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38702}, {"moveId": "WATER_GUN", "uses": 37798}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26549}, {"moveId": "PSYCHIC", "uses": 23935}, {"moveId": "WATER_PULSE", "uses": 13237}, {"moveId": "RETURN", "uses": 12782}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 38}, {"speciesId": "slowbro_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 279, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 623, "opRating": 376}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 605, "opRating": 394}, {"opponent": "sneasler", "rating": 597, "opRating": 402}, {"opponent": "<PERSON>ras", "rating": 554, "opRating": 445}, {"opponent": "machamp_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "gyarados", "rating": 154}, {"opponent": "dragonite", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "zacian_hero", "rating": 228}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40113}, {"moveId": "WATER_GUN", "uses": 36387}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 31499}, {"moveId": "PSYCHIC", "uses": 28903}, {"moveId": "WATER_PULSE", "uses": 15912}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["CONFUSION", "ICE_BEAM", "PSYCHIC"], "score": 38}, {"speciesId": "slowking", "speciesName": "Slowking", "rating": 287, "matchups": [{"opponent": "ninetales_alolan", "rating": 659, "opRating": 340}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 623, "opRating": 376}, {"opponent": "sneasler", "rating": 621, "opRating": 378}, {"opponent": "blaziken", "rating": 615, "opRating": 384}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dragonite", "rating": 194}, {"opponent": "zacian_hero", "rating": 205}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 38710}, {"moveId": "WATER_GUN", "uses": 37790}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 26849}, {"moveId": "BLIZZARD", "uses": 21444}, {"moveId": "RETURN", "uses": 14136}, {"moveId": "FIRE_BLAST", "uses": 14102}]}, "moveset": ["CONFUSION", "PSYCHIC", "BLIZZARD"], "score": 38}, {"speciesId": "alomomola", "speciesName": "Alomomola", "rating": 274, "matchups": [{"opponent": "ninetales", "rating": 672, "opRating": 327}, {"opponent": "pyroar", "rating": 631, "opRating": 368}, {"opponent": "rhydon", "rating": 548, "opRating": 451}, {"opponent": "relicanth", "rating": 521, "opRating": 478}, {"opponent": "mandibuzz", "rating": 508, "opRating": 491}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 147}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "metagross", "rating": 194}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 7673}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5603}, {"moveId": "HIDDEN_POWER_WATER", "uses": 5197}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4812}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4652}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4596}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4569}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4298}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4194}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4173}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4171}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4129}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3709}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3692}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3679}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3643}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3447}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 27046}, {"moveId": "HYDRO_PUMP", "uses": 24800}, {"moveId": "PSYCHIC", "uses": 24625}]}, "moveset": ["WATERFALL", "BLIZZARD", "HYDRO_PUMP"], "score": 37.8}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 274, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 781, "opRating": 218}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 657, "opRating": 342}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 630, "opRating": 369}, {"opponent": "gallade", "rating": 610, "opRating": 389}, {"opponent": "swampert", "rating": 513}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "gyarados", "rating": 198}, {"opponent": "garcho<PERSON>", "rating": 265}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "excadrill", "rating": 404}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49383}, {"moveId": "RAZOR_LEAF", "uses": 27117}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26354}, {"moveId": "SEED_BOMB", "uses": 21719}, {"moveId": "FOUL_PLAY", "uses": 19748}, {"moveId": "FIRE_BLAST", "uses": 8559}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 37.8}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 294, "matchups": [{"opponent": "moltres_galarian", "rating": 647, "opRating": 352}, {"opponent": "articuno", "rating": 580, "opRating": 419}, {"opponent": "avalugg", "rating": 521, "opRating": 478}, {"opponent": "sylveon", "rating": 517}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 507}], "counters": [{"opponent": "mewtwo", "rating": 65}, {"opponent": "giratina_origin", "rating": 137}, {"opponent": "gyarados", "rating": 157}, {"opponent": "dialga", "rating": 163}, {"opponent": "lugia", "rating": 426}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39617}, {"moveId": "ROCK_THROW", "uses": 36883}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30467}, {"moveId": "MAGNET_BOMB", "uses": 24090}, {"moveId": "THUNDERBOLT", "uses": 12941}, {"moveId": "RETURN", "uses": 9005}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 37.8}, {"speciesId": "scyther", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 279, "matchups": [{"opponent": "celebi", "rating": 678, "opRating": 321}, {"opponent": "chesnaught", "rating": 675, "opRating": 324}, {"opponent": "zarude", "rating": 608, "opRating": 391}, {"opponent": "virizion", "rating": 560, "opRating": 439}, {"opponent": "obstagoon", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "dragonite", "rating": 199}, {"opponent": "garcho<PERSON>", "rating": 218}, {"opponent": "gyarados", "rating": 219}, {"opponent": "mewtwo", "rating": 317}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35688}, {"moveId": "AIR_SLASH", "uses": 25180}, {"moveId": "STEEL_WING", "uses": 15661}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25834}, {"moveId": "X_SCISSOR", "uses": 18283}, {"moveId": "AERIAL_ACE", "uses": 13375}, {"moveId": "BUG_BUZZ", "uses": 11887}, {"moveId": "RETURN", "uses": 7046}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 37.8}, {"speciesId": "cofagrigus", "speciesName": "<PERSON><PERSON>g<PERSON><PERSON>", "rating": 294, "matchups": [{"opponent": "deoxys_defense", "rating": 651, "opRating": 348}, {"opponent": "pinsir", "rating": 640, "opRating": 359}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 611, "opRating": 388}, {"opponent": "durant", "rating": 575, "opRating": 424}, {"opponent": "escavalier", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "lugia", "rating": 157}, {"opponent": "zacian_hero", "rating": 326}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 56177}, {"moveId": "ASTONISH", "uses": 14593}, {"moveId": "ZEN_HEADBUTT", "uses": 5749}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 35642}, {"moveId": "DARK_PULSE", "uses": 23271}, {"moveId": "PSYCHIC", "uses": 17500}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "DARK_PULSE"], "score": 37.6}, {"speciesId": "crustle", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 340, "matchups": [{"opponent": "hoopa_unbound", "rating": 697, "opRating": 302}, {"opponent": "leavanny", "rating": 621, "opRating": 378}, {"opponent": "weavile", "rating": 617, "opRating": 382}, {"opponent": "umbreon", "rating": 541, "opRating": 458}, {"opponent": "mandibuzz", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "lugia", "rating": 202}, {"opponent": "zacian_hero", "rating": 216}, {"opponent": "mewtwo", "rating": 302}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40807}, {"moveId": "SMACK_DOWN", "uses": 35693}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28854}, {"moveId": "X_SCISSOR", "uses": 26881}, {"moveId": "ROCK_BLAST", "uses": 20684}]}, "moveset": ["FURY_CUTTER", "ROCK_SLIDE", "X_SCISSOR"], "score": 37.6}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 340, "matchups": [{"opponent": "moltres_galarian", "rating": 640, "opRating": 359}, {"opponent": "salamence", "rating": 634, "opRating": 365}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 551}, {"opponent": "primarina", "rating": 551, "opRating": 448}, {"opponent": "articuno", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "metagross", "rating": 107}, {"opponent": "gyarados", "rating": 234}, {"opponent": "sylveon", "rating": 407}, {"opponent": "lugia", "rating": 416}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 68205}, {"moveId": "LOW_KICK", "uses": 8295}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 28345}, {"moveId": "ICE_PUNCH", "uses": 19258}, {"moveId": "THUNDER_PUNCH", "uses": 13794}, {"moveId": "FLAMETHROWER", "uses": 10490}, {"moveId": "THUNDER", "uses": 4507}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ICE_PUNCH"], "score": 37.6}, {"speciesId": "rapidash_galarian", "speciesName": "Rapidash (Galarian)", "rating": 273, "matchups": [{"opponent": "exeggutor_alolan", "rating": 657, "opRating": 342}, {"opponent": "hydreigon", "rating": 597, "opRating": 402}, {"opponent": "dragonite", "rating": 577}, {"opponent": "latias", "rating": 526, "opRating": 473}, {"opponent": "kommo_o", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "zacian_hero", "rating": 173}, {"opponent": "lugia", "rating": 180}, {"opponent": "garcho<PERSON>", "rating": 185}, {"opponent": "gyarados", "rating": 208}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 36354}, {"moveId": "PSYCHO_CUT", "uses": 32384}, {"moveId": "LOW_KICK", "uses": 7738}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27406}, {"moveId": "MEGAHORN", "uses": 17180}, {"moveId": "PSYCHIC", "uses": 16815}, {"moveId": "PLAY_ROUGH", "uses": 15031}]}, "moveset": ["FAIRY_WIND", "BODY_SLAM", "MEGAHORN"], "score": 37.6}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 265, "matchups": [{"opponent": "stunfisk_galarian", "rating": 647, "opRating": 352}, {"opponent": "lap<PERSON>_shadow", "rating": 599, "opRating": 400}, {"opponent": "ferrothorn", "rating": 599, "opRating": 400}, {"opponent": "cryogonal", "rating": 567, "opRating": 432}, {"opponent": "walrein", "rating": 548, "opRating": 451}], "counters": [{"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "dialga", "rating": 111}, {"opponent": "lugia", "rating": 157}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "giratina_origin", "rating": 436}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 34197}, {"moveId": "COUNTER", "uses": 30674}, {"moveId": "METAL_CLAW", "uses": 11674}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 38933}, {"moveId": "RETURN", "uses": 16508}, {"moveId": "PLAY_ROUGH", "uses": 14694}, {"moveId": "HYPER_BEAM", "uses": 6382}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "RETURN"], "score": 37.6}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 249, "matchups": [{"opponent": "munchlax", "rating": 557, "opRating": 442}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "mewtwo", "rating": 143}, {"opponent": "dialga", "rating": 146}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "gyarados", "rating": 219}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47965}, {"moveId": "ASTONISH", "uses": 28535}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28291}, {"moveId": "LOW_SWEEP", "uses": 24290}, {"moveId": "HYPER_BEAM", "uses": 23681}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["SCRATCH", "AERIAL_ACE", "LOW_SWEEP"], "score": 37.4}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 353, "matchups": [{"opponent": "magnezone", "rating": 726, "opRating": 273}, {"opponent": "nihilego", "rating": 688, "opRating": 311}, {"opponent": "magnezone_shadow", "rating": 680, "opRating": 319}, {"opponent": "entei", "rating": 674, "opRating": 325}, {"opponent": "heatran", "rating": 625, "opRating": 375}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "gyarados", "rating": 159}, {"opponent": "mewtwo", "rating": 187}, {"opponent": "giratina_origin", "rating": 191}, {"opponent": "excadrill", "rating": 216}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 39979}, {"moveId": "DRAGON_TAIL", "uses": 36521}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27575}, {"moveId": "EARTH_POWER", "uses": 18523}, {"moveId": "STONE_EDGE", "uses": 15645}, {"moveId": "EARTHQUAKE", "uses": 7870}, {"moveId": "RETURN", "uses": 6739}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 37.4}, {"speciesId": "weezing_galarian", "speciesName": "Weez<PERSON> (Galarian)", "rating": 286, "matchups": [{"opponent": "kommo_o", "rating": 748, "opRating": 251}, {"opponent": "dragonite", "rating": 587}, {"opponent": "dragonite_shadow", "rating": 583, "opRating": 416}, {"opponent": "buzzwole", "rating": 546, "opRating": 453}, {"opponent": "virizion", "rating": 546, "opRating": 453}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "giratina_origin", "rating": 81}, {"opponent": "lugia", "rating": 114}, {"opponent": "zacian_hero", "rating": 213}, {"opponent": "gyarados", "rating": 283}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 48782}, {"moveId": "TACKLE", "uses": 27718}], "chargedMoves": [{"moveId": "SLUDGE", "uses": 23796}, {"moveId": "PLAY_ROUGH", "uses": 23135}, {"moveId": "OVERHEAT", "uses": 19883}, {"moveId": "HYPER_BEAM", "uses": 9659}]}, "moveset": ["FAIRY_WIND", "SLUDGE", "PLAY_ROUGH"], "score": 37.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 273, "matchups": [{"opponent": "obstagoon", "rating": 740, "opRating": 259}, {"opponent": "sandslash_alolan", "rating": 662, "opRating": 337}, {"opponent": "cobalion", "rating": 577, "opRating": 422}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}, {"opponent": "steelix", "rating": 518, "opRating": 481}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "garcho<PERSON>", "rating": 133}, {"opponent": "metagross", "rating": 165}, {"opponent": "dialga", "rating": 241}, {"opponent": "excadrill", "rating": 397}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 29149}, {"moveId": "PSYCHO_CUT", "uses": 26154}, {"moveId": "CONFUSION", "uses": 21188}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16628}, {"moveId": "FIRE_PUNCH", "uses": 15305}, {"moveId": "PSYCHIC", "uses": 13969}, {"moveId": "FOCUS_BLAST", "uses": 9999}, {"moveId": "DAZZLING_GLEAM", "uses": 7449}, {"moveId": "RETURN", "uses": 7103}, {"moveId": "FUTURE_SIGHT", "uses": 6031}]}, "moveset": ["COUNTER", "FIRE_PUNCH", "SHADOW_BALL"], "score": 37.2}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 309, "matchups": [{"opponent": "vileplume_shadow", "rating": 631, "opRating": 368}, {"opponent": "venusaur", "rating": 559, "opRating": 440}, {"opponent": "cresselia", "rating": 555, "opRating": 444}, {"opponent": "nihilego", "rating": 539, "opRating": 460}, {"opponent": "clefable", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "zacian_hero", "rating": 173}, {"opponent": "gyarados", "rating": 201}, {"opponent": "dragonite", "rating": 207}, {"opponent": "lugia", "rating": 280}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43010}, {"moveId": "FEINT_ATTACK", "uses": 33490}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 20974}, {"moveId": "PAYBACK", "uses": 17236}, {"moveId": "HEAVY_SLAM", "uses": 15110}, {"moveId": "BULLDOZE", "uses": 10092}, {"moveId": "PSYCHIC", "uses": 8140}, {"moveId": "FLASH_CANNON", "uses": 4835}]}, "moveset": ["CONFUSION", "PSYSHOCK", "PAYBACK"], "score": 37.2}, {"speciesId": "barbara<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 335, "matchups": [{"opponent": "articuno_shadow", "rating": 768, "opRating": 231}, {"opponent": "moltres", "rating": 759, "opRating": 240}, {"opponent": "ho_oh", "rating": 743}, {"opponent": "ho_oh_shadow", "rating": 637, "opRating": 362}, {"opponent": "articuno", "rating": 625, "opRating": 375}], "counters": [{"opponent": "dialga", "rating": 51}, {"opponent": "gyarados", "rating": 121}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "metagross", "rating": 194}, {"opponent": "lugia", "rating": 271}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31249}, {"moveId": "WATER_GUN", "uses": 26612}, {"moveId": "MUD_SLAP", "uses": 18634}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26409}, {"moveId": "CROSS_CHOP", "uses": 24121}, {"moveId": "GRASS_KNOT", "uses": 16312}, {"moveId": "SKULL_BASH", "uses": 9643}]}, "moveset": ["FURY_CUTTER", "STONE_EDGE", "CROSS_CHOP"], "score": 37}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 257, "matchups": [{"opponent": "ferrothorn", "rating": 840, "opRating": 159}, {"opponent": "tapu_bulu", "rating": 769, "opRating": 230}, {"opponent": "gardevoir_shadow", "rating": 750, "opRating": 250}, {"opponent": "florges", "rating": 620, "opRating": 379}, {"opponent": "registeel", "rating": 551, "opRating": 448}], "counters": [{"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "dialga", "rating": 108}, {"opponent": "metagross", "rating": 238}, {"opponent": "zacian_hero", "rating": 326}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 351}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 42654}, {"moveId": "POISON_JAB", "uses": 33846}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 32818}, {"moveId": "FIRE_BLAST", "uses": 16142}, {"moveId": "DRAGON_PULSE", "uses": 14385}, {"moveId": "SLUDGE_WAVE", "uses": 13173}]}, "moveset": ["INCINERATE", "POISON_FANG", "FIRE_BLAST"], "score": 37}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 291, "matchups": [{"opponent": "tyranitar_shadow", "rating": 741, "opRating": 258}, {"opponent": "obstagoon", "rating": 684, "opRating": 315}, {"opponent": "registeel", "rating": 613, "opRating": 386}, {"opponent": "s<PERSON><PERSON>", "rating": 536, "opRating": 463}, {"opponent": "regirock", "rating": 505, "opRating": 494}], "counters": [{"opponent": "zacian_hero", "rating": 92}, {"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "metagross", "rating": 139}, {"opponent": "gyarados", "rating": 144}, {"opponent": "dialga", "rating": 220}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42201}, {"moveId": "POISON_JAB", "uses": 34299}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 28887}, {"moveId": "SLUDGE_BOMB", "uses": 24635}, {"moveId": "MUD_BOMB", "uses": 23001}]}, "moveset": ["COUNTER", "DYNAMIC_PUNCH", "SLUDGE_BOMB"], "score": 37}, {"speciesId": "golduck", "speciesName": "Gold<PERSON>", "rating": 275, "matchups": [{"opponent": "sandslash_alolan", "rating": 659, "opRating": 340}, {"opponent": "sandslash_alolan_shadow", "rating": 601, "opRating": 398}, {"opponent": "pyroar", "rating": 587, "opRating": 412}, {"opponent": "hippo<PERSON><PERSON>", "rating": 575, "opRating": 424}, {"opponent": "relicanth", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "lugia", "rating": 130}, {"opponent": "garcho<PERSON>", "rating": 136}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "mewtwo", "rating": 151}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41401}, {"moveId": "CONFUSION", "uses": 35099}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 21098}, {"moveId": "ICE_BEAM", "uses": 16468}, {"moveId": "SYNCHRONOISE", "uses": 10715}, {"moveId": "HYDRO_PUMP", "uses": 9234}, {"moveId": "RETURN", "uses": 7193}, {"moveId": "BUBBLE_BEAM", "uses": 6864}, {"moveId": "PSYCHIC", "uses": 4867}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 36.8}, {"speciesId": "jolteon", "speciesName": "Jolteon", "rating": 303, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 788, "opRating": 211}, {"opponent": "vaporeon", "rating": 610, "opRating": 389}, {"opponent": "golisopod", "rating": 533, "opRating": 466}, {"opponent": "primarina", "rating": 516, "opRating": 483}, {"opponent": "articuno_galarian", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 127}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "lugia", "rating": 321}, {"opponent": "gyarados", "rating": 389}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 43283}, {"moveId": "THUNDER_SHOCK", "uses": 33217}], "chargedMoves": [{"moveId": "DISCHARGE", "uses": 26605}, {"moveId": "LAST_RESORT", "uses": 19082}, {"moveId": "THUNDERBOLT", "uses": 11472}, {"moveId": "THUNDER", "uses": 9961}, {"moveId": "ZAP_CANNON", "uses": 9570}]}, "moveset": ["VOLT_SWITCH", "DISCHARGE", "LAST_RESORT"], "score": 36.8}, {"speciesId": "omastar", "speciesName": "Omastar", "rating": 309, "matchups": [{"opponent": "entei", "rating": 799, "opRating": 200}, {"opponent": "moltres", "rating": 764, "opRating": 235}, {"opponent": "ho_oh", "rating": 735}, {"opponent": "ho_oh_shadow", "rating": 687, "opRating": 312}, {"opponent": "avalugg", "rating": 659, "opRating": 340}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "lugia", "rating": 185}, {"opponent": "giratina_origin", "rating": 197}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 292}, {"opponent": "gyarados", "rating": 301}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29252}, {"moveId": "ROCK_THROW", "uses": 23658}, {"moveId": "WATER_GUN", "uses": 23565}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 26850}, {"moveId": "ROCK_BLAST", "uses": 19272}, {"moveId": "HYDRO_PUMP", "uses": 12267}, {"moveId": "ANCIENT_POWER", "uses": 9728}, {"moveId": "RETURN", "uses": 8428}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 36.8}, {"speciesId": "pinsir_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 341, "matchups": [{"opponent": "obstagoon", "rating": 805, "opRating": 194}, {"opponent": "celebi", "rating": 634, "opRating": 365}, {"opponent": "steelix", "rating": 573, "opRating": 426}, {"opponent": "zarude", "rating": 550, "opRating": 449}, {"opponent": "snor<PERSON>_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "dialga", "rating": 95}, {"opponent": "zacian_hero", "rating": 95}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "swampert", "rating": 278}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 40508}, {"moveId": "BUG_BITE", "uses": 26492}, {"moveId": "ROCK_SMASH", "uses": 9491}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25018}, {"moveId": "X_SCISSOR", "uses": 22153}, {"moveId": "SUPER_POWER", "uses": 18624}, {"moveId": "VICE_GRIP", "uses": 7702}, {"moveId": "SUBMISSION", "uses": 3015}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "X_SCISSOR"], "score": 36.8}, {"speciesId": "archeops", "speciesName": "Archeops", "rating": 301, "matchups": [{"opponent": "nidoqueen_shadow", "rating": 576, "opRating": 423}, {"opponent": "pinsir", "rating": 551, "opRating": 448}, {"opponent": "golisopod", "rating": 536, "opRating": 463}, {"opponent": "s<PERSON><PERSON>", "rating": 530, "opRating": 469}, {"opponent": "nidoqueen", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "mewtwo", "rating": 91}, {"opponent": "zacian_hero", "rating": 161}, {"opponent": "lugia", "rating": 290}, {"opponent": "grou<PERSON>", "rating": 323}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 51890}, {"moveId": "STEEL_WING", "uses": 24610}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 26453}, {"moveId": "CRUNCH", "uses": 25148}, {"moveId": "ANCIENT_POWER", "uses": 24875}]}, "moveset": ["WING_ATTACK", "DRAGON_CLAW", "CRUNCH"], "score": 36.6}, {"speciesId": "<PERSON>rserker", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 308, "matchups": [{"opponent": "aurorus", "rating": 671, "opRating": 328}, {"opponent": "sandslash_alolan", "rating": 646, "opRating": 353}, {"opponent": "aggron", "rating": 633, "opRating": 366}, {"opponent": "glaceon", "rating": 621, "opRating": 378}, {"opponent": "steelix", "rating": 541, "opRating": 458}], "counters": [{"opponent": "giratina_origin", "rating": 113}, {"opponent": "dialga", "rating": 122}, {"opponent": "gyarados", "rating": 157}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "lugia", "rating": 259}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 53773}, {"moveId": "METAL_CLAW", "uses": 22727}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 31203}, {"moveId": "FOUL_PLAY", "uses": 20455}, {"moveId": "IRON_HEAD", "uses": 14578}, {"moveId": "PLAY_ROUGH", "uses": 10225}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "FOUL_PLAY"], "score": 36.6}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 314, "matchups": [{"opponent": "aromatisse", "rating": 609, "opRating": 390}, {"opponent": "celebi", "rating": 590, "opRating": 409}, {"opponent": "gourgeist_super", "rating": 580, "opRating": 419}, {"opponent": "tapu_bulu", "rating": 536, "opRating": 463}, {"opponent": "tangrowth", "rating": 512, "opRating": 487}], "counters": [{"opponent": "lugia", "rating": 126}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "gyarados", "rating": 188}, {"opponent": "zacian_hero", "rating": 257}, {"opponent": "giratina_origin", "rating": 278}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 46989}, {"moveId": "BITE", "uses": 29511}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34609}, {"moveId": "SLUDGE_BOMB", "uses": 18504}, {"moveId": "FLAMETHROWER", "uses": 14468}, {"moveId": "RETURN", "uses": 8933}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 36.6}, {"speciesId": "clawitzer", "speciesName": "Clawitzer", "rating": 255, "matchups": [{"opponent": "ninetales", "rating": 610, "opRating": 389}, {"opponent": "mr_rime", "rating": 582, "opRating": 417}, {"opponent": "pyroar", "rating": 553, "opRating": 446}, {"opponent": "hippo<PERSON><PERSON>", "rating": 525, "opRating": 474}, {"opponent": "sandslash_alolan", "rating": 525, "opRating": 474}], "counters": [{"opponent": "dialga", "rating": 116}, {"opponent": "garcho<PERSON>", "rating": 119}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "zacian_hero", "rating": 210}, {"opponent": "excadrill", "rating": 327}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 41854}, {"moveId": "SMACK_DOWN", "uses": 34646}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 29815}, {"moveId": "ICE_BEAM", "uses": 21608}, {"moveId": "DARK_PULSE", "uses": 20837}, {"moveId": "WATER_PULSE", "uses": 4381}]}, "moveset": ["WATER_GUN", "CRABHAMMER", "ICE_BEAM"], "score": 36.4}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 293, "matchups": [{"opponent": "sci<PERSON>_shadow", "rating": 694, "opRating": 305}, {"opponent": "aurorus", "rating": 674, "opRating": 325}, {"opponent": "genesect_chill", "rating": 617, "opRating": 382}, {"opponent": "genesect_burn", "rating": 617, "opRating": 382}, {"opponent": "steelix", "rating": 506, "opRating": 493}], "counters": [{"opponent": "lugia", "rating": 116}, {"opponent": "dialga", "rating": 149}, {"opponent": "mewtwo", "rating": 156}, {"opponent": "zacian_hero", "rating": 187}, {"opponent": "metagross", "rating": 229}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39195}, {"moveId": "EMBER", "uses": 37305}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 24770}, {"moveId": "FLAMETHROWER", "uses": 17068}, {"moveId": "OVERHEAT", "uses": 15883}, {"moveId": "LAST_RESORT", "uses": 11588}, {"moveId": "FIRE_BLAST", "uses": 4496}, {"moveId": "HEAT_WAVE", "uses": 2719}]}, "moveset": ["FIRE_SPIN", "SUPER_POWER", "FLAMETHROWER"], "score": 36.4}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 285, "matchups": [{"opponent": "gourgeist_super", "rating": 640, "opRating": 359}, {"opponent": "clefable", "rating": 620, "opRating": 379}, {"opponent": "aromatisse", "rating": 620, "opRating": 379}, {"opponent": "decid<PERSON><PERSON>", "rating": 598, "opRating": 401}, {"opponent": "giratina_origin", "rating": 578}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "garcho<PERSON>", "rating": 86}, {"opponent": "mewtwo", "rating": 96}, {"opponent": "lugia", "rating": 169}, {"opponent": "gyarados", "rating": 309}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 12058}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5039}, {"moveId": "CHARGE_BEAM", "uses": 4549}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4311}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4112}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4030}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4015}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3913}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3813}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3731}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3685}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3676}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3661}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3375}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3334}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3332}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3162}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3051}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 20592}, {"moveId": "BLIZZARD", "uses": 16049}, {"moveId": "ZAP_CANNON", "uses": 13208}, {"moveId": "RETURN", "uses": 12991}, {"moveId": "SOLAR_BEAM", "uses": 8602}, {"moveId": "HYPER_BEAM", "uses": 5017}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 36.4}, {"speciesId": "seaking", "speciesName": "Seaking", "rating": 261, "matchups": [{"opponent": "ninetales", "rating": 598, "opRating": 401}, {"opponent": "pyroar", "rating": 584, "opRating": 415}, {"opponent": "altaria", "rating": 561, "opRating": 438}, {"opponent": "ninetales_alolan", "rating": 558, "opRating": 441}, {"opponent": "blastoise", "rating": 505, "opRating": 494}], "counters": [{"opponent": "giratina_origin", "rating": 171}, {"opponent": "lugia", "rating": 180}, {"opponent": "gyarados", "rating": 250}, {"opponent": "zacian_hero", "rating": 303}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 348}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 33627}, {"moveId": "WATERFALL", "uses": 29305}, {"moveId": "PECK", "uses": 13569}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 23991}, {"moveId": "ICY_WIND", "uses": 23338}, {"moveId": "MEGAHORN", "uses": 14866}, {"moveId": "ICE_BEAM", "uses": 7525}, {"moveId": "WATER_PULSE", "uses": 6847}]}, "moveset": ["POISON_JAB", "DRILL_RUN", "ICY_WIND"], "score": 36.4}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 249, "matchups": [{"opponent": "seismitoad", "rating": 758, "opRating": 241}, {"opponent": "chesnaught", "rating": 703, "opRating": 296}, {"opponent": "virizion", "rating": 623, "opRating": 376}, {"opponent": "swampert", "rating": 585}, {"opponent": "swampert_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "giratina_origin", "rating": 149}, {"opponent": "garcho<PERSON>", "rating": 223}, {"opponent": "zacian_hero", "rating": 228}, {"opponent": "gyarados", "rating": 250}, {"opponent": "grou<PERSON>", "rating": 274}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 41726}, {"moveId": "RAZOR_LEAF", "uses": 34774}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 45813}, {"moveId": "AERIAL_ACE", "uses": 16484}, {"moveId": "STOMP", "uses": 14190}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 36.1}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 296, "matchups": [{"opponent": "genesect_chill", "rating": 636, "opRating": 363}, {"opponent": "genesect_burn", "rating": 636, "opRating": 363}, {"opponent": "registeel", "rating": 627, "opRating": 372}, {"opponent": "avalugg", "rating": 610, "opRating": 389}, {"opponent": "articuno", "rating": 592, "opRating": 407}], "counters": [{"opponent": "mewtwo", "rating": 88}, {"opponent": "lugia", "rating": 107}, {"opponent": "dialga", "rating": 125}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "metagross", "rating": 270}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31412}, {"moveId": "SHADOW_CLAW", "uses": 29075}, {"moveId": "EMBER", "uses": 15970}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 48148}, {"moveId": "SOLAR_BEAM", "uses": 12011}, {"moveId": "OVERHEAT", "uses": 10322}, {"moveId": "FIRE_BLAST", "uses": 6014}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["INCINERATE", "BLAST_BURN", "SOLAR_BEAM"], "score": 36.1}, {"speciesId": "wailord", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 262, "matchups": [{"opponent": "entei_shadow", "rating": 626, "opRating": 373}, {"opponent": "heatran", "rating": 613, "opRating": 386}, {"opponent": "hippo<PERSON><PERSON>", "rating": 547, "opRating": 452}, {"opponent": "relicanth", "rating": 527, "opRating": 472}, {"opponent": "gliscor", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "lugia", "rating": 126}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 166}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 66772}, {"moveId": "ZEN_HEADBUTT", "uses": 9729}], "chargedMoves": [{"moveId": "SURF", "uses": 46194}, {"moveId": "BLIZZARD", "uses": 20060}, {"moveId": "HYPER_BEAM", "uses": 10204}]}, "moveset": ["WATER_GUN", "SURF", "BLIZZARD"], "score": 36.1}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 250, "matchups": [{"opponent": "trevenant", "rating": 681, "opRating": 318}, {"opponent": "gourgeist_super", "rating": 642, "opRating": 357}, {"opponent": "forretress", "rating": 627, "opRating": 372}, {"opponent": "golisopod", "rating": 596, "opRating": 403}, {"opponent": "tapu_bulu", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "mewtwo", "rating": 127}, {"opponent": "garcho<PERSON>", "rating": 157}, {"opponent": "gyarados", "rating": 188}, {"opponent": "giratina_origin", "rating": 422}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 25020}, {"moveId": "WING_ATTACK", "uses": 22164}, {"moveId": "AIR_SLASH", "uses": 17762}, {"moveId": "STEEL_WING", "uses": 11403}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 42084}, {"moveId": "AERIAL_ACE", "uses": 16974}, {"moveId": "HURRICANE", "uses": 7650}, {"moveId": "FEATHER_DANCE", "uses": 5345}, {"moveId": "AIR_CUTTER", "uses": 4590}]}, "moveset": ["GUST", "BRAVE_BIRD", "AERIAL_ACE"], "score": 35.9}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 311, "matchups": [{"opponent": "vileplume_shadow", "rating": 598, "opRating": 401}, {"opponent": "umbreon", "rating": 561, "opRating": 438}, {"opponent": "mandibuzz", "rating": 553, "opRating": 446}, {"opponent": "golisopod", "rating": 550, "opRating": 449}, {"opponent": "lickilicky", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "lugia", "rating": 145}, {"opponent": "gyarados", "rating": 203}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "garcho<PERSON>", "rating": 305}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 8561}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5130}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4491}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4370}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4288}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4288}, {"moveId": "CHARGE_BEAM", "uses": 4263}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4088}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4029}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3956}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3905}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3874}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3867}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3605}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3567}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3512}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3411}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3300}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 26305}, {"moveId": "RETURN", "uses": 16646}, {"moveId": "ZAP_CANNON", "uses": 16191}, {"moveId": "SOLAR_BEAM", "uses": 10798}, {"moveId": "HYPER_BEAM", "uses": 6445}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "RETURN"], "score": 35.9}, {"speciesId": "reuniclus", "speciesName": "Reuniclus", "rating": 217, "matchups": [{"opponent": "hypno", "rating": 651, "opRating": 348}, {"opponent": "tropius", "rating": 616, "opRating": 383}, {"opponent": "altaria", "rating": 609, "opRating": 390}, {"opponent": "slowking", "rating": 516, "opRating": 483}, {"opponent": "blissey", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "lugia", "rating": 135}, {"opponent": "zacian_hero", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 361}, {"opponent": "dragonite", "rating": 364}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5775}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5225}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 5176}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5110}, {"moveId": "HIDDEN_POWER_DARK", "uses": 5031}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4964}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4737}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4664}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4642}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4502}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4474}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4381}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4060}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4058}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3968}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3751}, {"moveId": "ZEN_HEADBUTT", "uses": 1779}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32331}, {"moveId": "FUTURE_SIGHT", "uses": 24837}, {"moveId": "THUNDER", "uses": 19287}]}, "moveset": ["HIDDEN_POWER_ICE", "SHADOW_BALL", "FUTURE_SIGHT"], "score": 35.9}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 285, "matchups": [{"opponent": "swampert", "rating": 789}, {"opponent": "rhydon", "rating": 754, "opRating": 245}, {"opponent": "swampert_shadow", "rating": 649, "opRating": 350}, {"opponent": "vaporeon", "rating": 592, "opRating": 407}, {"opponent": "tapu_fini", "rating": 509, "opRating": 490}], "counters": [{"opponent": "mewtwo", "rating": 67}, {"opponent": "garcho<PERSON>", "rating": 220}, {"opponent": "zacian_hero", "rating": 222}, {"opponent": "gyarados", "rating": 260}, {"opponent": "excadrill", "rating": 311}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 40421}, {"moveId": "FURY_CUTTER", "uses": 36079}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30195}, {"moveId": "DRAGON_CLAW", "uses": 17103}, {"moveId": "FRENZY_PLANT", "uses": 11180}, {"moveId": "EARTHQUAKE", "uses": 10013}, {"moveId": "AERIAL_ACE", "uses": 8018}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DRAGON_CLAW"], "score": 35.9}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 236, "matchups": [{"opponent": "seismitoad", "rating": 723, "opRating": 276}, {"opponent": "decid<PERSON><PERSON>", "rating": 704, "opRating": 295}, {"opponent": "relicanth", "rating": 623, "opRating": 376}, {"opponent": "espeon", "rating": 572, "opRating": 427}, {"opponent": "swampert", "rating": 513}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "gyarados", "rating": 278}, {"opponent": "excadrill", "rating": 327}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "mewtwo", "rating": 466}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25666}, {"moveId": "BULLET_SEED", "uses": 23261}, {"moveId": "FEINT_ATTACK", "uses": 17609}, {"moveId": "RAZOR_LEAF", "uses": 10046}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36525}, {"moveId": "FOUL_PLAY", "uses": 24110}, {"moveId": "HURRICANE", "uses": 8837}, {"moveId": "LEAF_TORNADO", "uses": 6932}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 35.9}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 279, "matchups": [{"opponent": "bisharp", "rating": 556, "opRating": 443}, {"opponent": "greedent", "rating": 540, "opRating": 459}, {"opponent": "gyarado<PERSON>_shadow", "rating": 521, "opRating": 478}, {"opponent": "umbreon", "rating": 521, "opRating": 478}, {"opponent": "lickilicky", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "giratina_origin", "rating": 149}, {"opponent": "mewtwo", "rating": 158}, {"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "gyarados", "rating": 438}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 39814}, {"moveId": "TACKLE", "uses": 30095}, {"moveId": "TAKE_DOWN", "uses": 6555}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34415}, {"moveId": "WILD_CHARGE", "uses": 27246}, {"moveId": "PAYBACK", "uses": 14967}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "WILD_CHARGE"], "score": 35.7}, {"speciesId": "gren<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 267, "matchups": [{"opponent": "ma<PERSON><PERSON>", "rating": 634, "opRating": 365}, {"opponent": "steelix", "rating": 515, "opRating": 484}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 512, "opRating": 487}, {"opponent": "nidoqueen", "rating": 512, "opRating": 487}, {"opponent": "moltres", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "lugia", "rating": 178}, {"opponent": "gyarados", "rating": 226}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "mewtwo", "rating": 346}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 40030}, {"moveId": "FEINT_ATTACK", "uses": 36470}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 36069}, {"moveId": "SURF", "uses": 26388}, {"moveId": "AERIAL_ACE", "uses": 9828}, {"moveId": "HYDRO_PUMP", "uses": 4262}]}, "moveset": ["BUBBLE", "NIGHT_SLASH", "SURF"], "score": 35.7}, {"speciesId": "lycanroc_midnight", "speciesName": "Lycanroc (Midnight)", "rating": 286, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 758, "opRating": 241}, {"opponent": "bisharp", "rating": 654, "opRating": 345}, {"opponent": "umbreon", "rating": 623, "opRating": 376}, {"opponent": "mandibuzz", "rating": 617, "opRating": 382}, {"opponent": "ho_oh", "rating": 525}], "counters": [{"opponent": "mewtwo", "rating": 96}, {"opponent": "garcho<PERSON>", "rating": 98}, {"opponent": "gyarados", "rating": 105}, {"opponent": "lugia", "rating": 190}, {"opponent": "dialga", "rating": 198}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44004}, {"moveId": "ROCK_THROW", "uses": 32496}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 26959}, {"moveId": "CRUNCH", "uses": 26490}, {"moveId": "PSYCHIC_FANGS", "uses": 23126}]}, "moveset": ["COUNTER", "STONE_EDGE", "CRUNCH"], "score": 35.7}, {"speciesId": "masquerain", "speciesName": "Masquerain", "rating": 262, "matchups": [{"opponent": "ferrothorn", "rating": 636, "opRating": 363}, {"opponent": "celebi", "rating": 621, "opRating": 378}, {"opponent": "chesnaught", "rating": 595, "opRating": 404}, {"opponent": "obstagoon", "rating": 592, "opRating": 407}, {"opponent": "virizion", "rating": 544, "opRating": 455}], "counters": [{"opponent": "dialga", "rating": 114}, {"opponent": "zacian_hero", "rating": 132}, {"opponent": "gyarados", "rating": 162}, {"opponent": "metagross", "rating": 174}, {"opponent": "garcho<PERSON>", "rating": 314}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 38643}, {"moveId": "AIR_SLASH", "uses": 37857}], "chargedMoves": [{"moveId": "LUNGE", "uses": 30553}, {"moveId": "OMINOUS_WIND", "uses": 14005}, {"moveId": "AIR_CUTTER", "uses": 11993}, {"moveId": "SILVER_WIND", "uses": 10947}, {"moveId": "BUBBLE_BEAM", "uses": 9093}]}, "moveset": ["INFESTATION", "LUNGE", "OMINOUS_WIND"], "score": 35.7}, {"speciesId": "golem", "speciesName": "Golem", "rating": 304, "matchups": [{"opponent": "entei", "rating": 720, "opRating": 279}, {"opponent": "zap<PERSON>_shadow", "rating": 651, "opRating": 348}, {"opponent": "ho_oh", "rating": 648}, {"opponent": "magnezone", "rating": 622, "opRating": 377}, {"opponent": "nihilego", "rating": 619, "opRating": 380}], "counters": [{"opponent": "mewtwo", "rating": 80}, {"opponent": "dialga", "rating": 89}, {"opponent": "zacian_hero", "rating": 109}, {"opponent": "lugia", "rating": 247}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 33457}, {"moveId": "ROCK_THROW", "uses": 22572}, {"moveId": "MUD_SLAP", "uses": 20453}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 20147}, {"moveId": "STONE_EDGE", "uses": 18877}, {"moveId": "ROCK_BLAST", "uses": 17074}, {"moveId": "ANCIENT_POWER", "uses": 13169}, {"moveId": "RETURN", "uses": 7257}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 35.5}, {"speciesId": "scolipede", "speciesName": "Scolipede", "rating": 300, "matchups": [{"opponent": "clefable", "rating": 746, "opRating": 253}, {"opponent": "virizion", "rating": 735, "opRating": 264}, {"opponent": "gardevoir", "rating": 690, "opRating": 309}, {"opponent": "gardevoir_shadow", "rating": 679, "opRating": 320}, {"opponent": "primarina", "rating": 651, "opRating": 348}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 91}, {"opponent": "gyarados", "rating": 157}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 317}, {"opponent": "zacian_hero", "rating": 439}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 43412}, {"moveId": "BUG_BITE", "uses": 33088}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25285}, {"moveId": "MEGAHORN", "uses": 24142}, {"moveId": "SLUDGE_BOMB", "uses": 19171}, {"moveId": "GYRO_BALL", "uses": 7856}]}, "moveset": ["POISON_JAB", "X_SCISSOR", "MEGAHORN"], "score": 35.5}, {"speciesId": "falinks", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 279, "matchups": [{"opponent": "relicanth", "rating": 630, "opRating": 369}, {"opponent": "obstagoon", "rating": 604, "opRating": 395}, {"opponent": "scrafty", "rating": 587, "opRating": 412}, {"opponent": "tyranitar", "rating": 520, "opRating": 479}, {"opponent": "s<PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "metagross", "rating": 125}, {"opponent": "gyarados", "rating": 157}, {"opponent": "dialga", "rating": 220}, {"opponent": "excadrill", "rating": 351}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 62649}, {"moveId": "ROCK_SMASH", "uses": 13851}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 34639}, {"moveId": "MEGAHORN", "uses": 20916}, {"moveId": "BRICK_BREAK", "uses": 20879}]}, "moveset": ["COUNTER", "SUPER_POWER", "MEGAHORN"], "score": 35.3}, {"speciesId": "hitmon<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 262, "matchups": [{"opponent": "weavile", "rating": 905, "opRating": 94}, {"opponent": "bisharp", "rating": 704, "opRating": 295}, {"opponent": "obstagoon", "rating": 665, "opRating": 334}, {"opponent": "registeel", "rating": 547, "opRating": 452}, {"opponent": "s<PERSON><PERSON>", "rating": 543, "opRating": 456}], "counters": [{"opponent": "garcho<PERSON>", "rating": 115}, {"opponent": "gyarados", "rating": 118}, {"opponent": "metagross", "rating": 145}, {"opponent": "dialga", "rating": 263}, {"opponent": "excadrill", "rating": 337}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 42779}, {"moveId": "BULLET_PUNCH", "uses": 26300}, {"moveId": "ROCK_SMASH", "uses": 7433}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 24712}, {"moveId": "ICE_PUNCH", "uses": 16079}, {"moveId": "THUNDER_PUNCH", "uses": 11848}, {"moveId": "BRICK_BREAK", "uses": 10975}, {"moveId": "FIRE_PUNCH", "uses": 10644}, {"moveId": "POWER_UP_PUNCH", "uses": 2162}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 35.3}, {"speciesId": "magneton", "speciesName": "Magneton", "rating": 269, "matchups": [{"opponent": "gardevoir_shadow", "rating": 641, "opRating": 358}, {"opponent": "aurorus", "rating": 622, "opRating": 377}, {"opponent": "torn<PERSON><PERSON>_therian", "rating": 578, "opRating": 421}, {"opponent": "gyarados", "rating": 503}, {"opponent": "articuno", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 49}, {"opponent": "dialga", "rating": 154}, {"opponent": "sylveon", "rating": 355}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 401}, {"opponent": "lugia", "rating": 402}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30665}, {"moveId": "SPARK", "uses": 28240}, {"moveId": "CHARGE_BEAM", "uses": 17584}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 27316}, {"moveId": "DISCHARGE", "uses": 23743}, {"moveId": "RETURN", "uses": 10738}, {"moveId": "ZAP_CANNON", "uses": 9064}, {"moveId": "FLASH_CANNON", "uses": 5807}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 35.3}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 268, "matchups": [{"opponent": "magnezone_shadow", "rating": 676, "opRating": 323}, {"opponent": "heatran", "rating": 639, "opRating": 360}, {"opponent": "nidoqueen", "rating": 574, "opRating": 425}, {"opponent": "aggron", "rating": 565, "opRating": 434}, {"opponent": "magnezone", "rating": 506, "opRating": 493}], "counters": [{"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "zacian_hero", "rating": 190}, {"opponent": "dialga", "rating": 222}, {"opponent": "metagross", "rating": 244}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 42202}, {"moveId": "WATER_GUN", "uses": 34298}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 41462}, {"moveId": "BLIZZARD", "uses": 22865}, {"moveId": "WATER_PULSE", "uses": 12189}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "BLIZZARD"], "score": 35.3}, {"speciesId": "hitmonchan", "speciesName": "Hitmonchan", "rating": 280, "matchups": [{"opponent": "bisharp", "rating": 759, "opRating": 240}, {"opponent": "obstagoon", "rating": 622, "opRating": 377}, {"opponent": "s<PERSON><PERSON>", "rating": 590, "opRating": 409}, {"opponent": "regirock", "rating": 543, "opRating": 456}, {"opponent": "tyranitar", "rating": 503, "opRating": 496}], "counters": [{"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "metagross", "rating": 125}, {"opponent": "gyarados", "rating": 131}, {"opponent": "dialga", "rating": 220}, {"opponent": "excadrill", "rating": 351}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 41692}, {"moveId": "BULLET_PUNCH", "uses": 26862}, {"moveId": "ROCK_SMASH", "uses": 7920}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23363}, {"moveId": "ICE_PUNCH", "uses": 15087}, {"moveId": "THUNDER_PUNCH", "uses": 11020}, {"moveId": "BRICK_BREAK", "uses": 10390}, {"moveId": "FIRE_PUNCH", "uses": 9988}, {"moveId": "RETURN", "uses": 4625}, {"moveId": "POWER_UP_PUNCH", "uses": 2078}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "ICE_PUNCH"], "score": 35.1}, {"speciesId": "slowbro_galarian", "speciesName": "<PERSON><PERSON> (Galarian)", "rating": 276, "matchups": [{"opponent": "florges", "rating": 623, "opRating": 376}, {"opponent": "heracross", "rating": 608, "opRating": 391}, {"opponent": "virizion", "rating": 605, "opRating": 394}, {"opponent": "sylveon", "rating": 564}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 502}], "counters": [{"opponent": "mewtwo", "rating": 109}, {"opponent": "lugia", "rating": 126}, {"opponent": "gyarados", "rating": 172}, {"opponent": "dragonite", "rating": 178}, {"opponent": "zacian_hero", "rating": 286}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 41268}, {"moveId": "CONFUSION", "uses": 35232}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 28879}, {"moveId": "PSYCHIC", "uses": 27228}, {"moveId": "FOCUS_BLAST", "uses": 20351}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PSYCHIC"], "score": 35.1}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 246, "matchups": [{"opponent": "golurk", "rating": 735, "opRating": 264}, {"opponent": "gourgeist_super", "rating": 729, "opRating": 270}, {"opponent": "trevenant", "rating": 645, "opRating": 354}, {"opponent": "seismitoad", "rating": 601, "opRating": 398}, {"opponent": "golisopod", "rating": 566, "opRating": 433}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "mewtwo", "rating": 117}, {"opponent": "lugia", "rating": 130}, {"opponent": "garcho<PERSON>", "rating": 169}, {"opponent": "giratina_origin", "rating": 374}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 47419}, {"moveId": "STEEL_WING", "uses": 29081}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 49779}, {"moveId": "HYPER_BEAM", "uses": 17445}, {"moveId": "HEAT_WAVE", "uses": 9297}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 35.1}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 259, "matchups": [{"opponent": "seismitoad", "rating": 807, "opRating": 192}, {"opponent": "swampert", "rating": 670}, {"opponent": "swampert_shadow", "rating": 628, "opRating": 371}, {"opponent": "vaporeon", "rating": 625, "opRating": 375}, {"opponent": "tapu_fini", "rating": 554, "opRating": 445}], "counters": [{"opponent": "garcho<PERSON>", "rating": 192}, {"opponent": "zacian_hero", "rating": 196}, {"opponent": "mewtwo", "rating": 197}, {"opponent": "gyarados", "rating": 213}, {"opponent": "excadrill", "rating": 444}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38611}, {"moveId": "RAZOR_LEAF", "uses": 22665}, {"moveId": "ACID", "uses": 15225}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 50309}, {"moveId": "DAZZLING_GLEAM", "uses": 10881}, {"moveId": "RETURN", "uses": 10304}, {"moveId": "PETAL_BLIZZARD", "uses": 5284}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 34.9}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 319, "matchups": [{"opponent": "gengar", "rating": 617, "opRating": 382}, {"opponent": "gourgeist_super", "rating": 605, "opRating": 394}, {"opponent": "umbreon", "rating": 560, "opRating": 439}, {"opponent": "mandibuzz", "rating": 541, "opRating": 458}, {"opponent": "espeon", "rating": 525, "opRating": 474}], "counters": [{"opponent": "mewtwo", "rating": 174}, {"opponent": "zacian_hero", "rating": 208}, {"opponent": "lugia", "rating": 214}, {"opponent": "giratina_origin", "rating": 256}, {"opponent": "gyarados", "rating": 260}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 24586}, {"moveId": "INFESTATION", "uses": 18981}, {"moveId": "ICE_FANG", "uses": 17839}, {"moveId": "BITE", "uses": 15014}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31069}, {"moveId": "AQUA_TAIL", "uses": 22770}, {"moveId": "SLUDGE_BOMB", "uses": 17245}, {"moveId": "FELL_STINGER", "uses": 5416}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 34.9}, {"speciesId": "politoed_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 279, "matchups": [{"opponent": "delphox", "rating": 712, "opRating": 287}, {"opponent": "heatran", "rating": 674, "opRating": 325}, {"opponent": "stunfisk_galarian", "rating": 645, "opRating": 354}, {"opponent": "sandslash_alolan", "rating": 548, "opRating": 451}, {"opponent": "gliscor", "rating": 513, "opRating": 486}], "counters": [{"opponent": "dialga", "rating": 111}, {"opponent": "garcho<PERSON>", "rating": 176}, {"opponent": "zacian_hero", "rating": 179}, {"opponent": "mewtwo", "rating": 203}, {"opponent": "metagross", "rating": 209}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 41657}, {"moveId": "BUBBLE", "uses": 34843}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 32479}, {"moveId": "BLIZZARD", "uses": 13383}, {"moveId": "SURF", "uses": 13208}, {"moveId": "EARTHQUAKE", "uses": 13081}, {"moveId": "HYDRO_PUMP", "uses": 4196}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "WEATHER_BALL_WATER", "BLIZZARD"], "score": 34.9}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 255, "matchups": [{"opponent": "seismitoad", "rating": 758, "opRating": 241}, {"opponent": "relicanth", "rating": 599, "opRating": 400}, {"opponent": "hoopa_unbound", "rating": 521, "opRating": 478}, {"opponent": "swampert_shadow", "rating": 513, "opRating": 486}, {"opponent": "vaporeon", "rating": 510, "opRating": 489}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "gyarados", "rating": 257}, {"opponent": "giratina_origin", "rating": 298}, {"opponent": "mewtwo", "rating": 398}, {"opponent": "swampert", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 25179}, {"moveId": "BULLET_SEED", "uses": 22731}, {"moveId": "FEINT_ATTACK", "uses": 17826}, {"moveId": "RAZOR_LEAF", "uses": 10885}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33791}, {"moveId": "FOUL_PLAY", "uses": 22086}, {"moveId": "HURRICANE", "uses": 8000}, {"moveId": "LEAF_TORNADO", "uses": 6497}, {"moveId": "RETURN", "uses": 6172}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 34.9}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 282, "matchups": [{"opponent": "chesnaught", "rating": 744, "opRating": 255}, {"opponent": "virizion", "rating": 708, "opRating": 291}, {"opponent": "buzzwole", "rating": 594, "opRating": 405}, {"opponent": "celebi", "rating": 541, "opRating": 458}, {"opponent": "obstagoon", "rating": 527, "opRating": 472}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 132}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "swampert", "rating": 477}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 40838}, {"moveId": "BUG_BITE", "uses": 35662}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26372}, {"moveId": "ANCIENT_POWER", "uses": 25649}, {"moveId": "AERIAL_ACE", "uses": 24425}]}, "moveset": ["WING_ATTACK", "BUG_BUZZ", "ANCIENT_POWER"], "score": 34.9}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 239, "matchups": [], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "lugia", "rating": 166}, {"opponent": "giratina_origin", "rating": 169}, {"opponent": "gyarados", "rating": 206}], "moves": {"fastMoves": [{"moveId": "SCRATCH", "uses": 47541}, {"moveId": "ASTONISH", "uses": 28959}], "chargedMoves": [{"moveId": "RETURN", "uses": 23833}, {"moveId": "AERIAL_ACE", "uses": 22887}, {"moveId": "LOW_SWEEP", "uses": 20476}, {"moveId": "HYPER_BEAM", "uses": 9193}]}, "moveset": ["SCRATCH", "RETURN", "AERIAL_ACE"], "score": 34.7}, {"speciesId": "be<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 264, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 612, "opRating": 387}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 588, "opRating": 411}, {"opponent": "poliwrath_shadow", "rating": 588, "opRating": 411}, {"opponent": "sneasler", "rating": 564, "opRating": 435}, {"opponent": "machamp_shadow", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "gyarados", "rating": 154}, {"opponent": "dragonite", "rating": 188}, {"opponent": "zacian_hero", "rating": 210}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 55703}, {"moveId": "ASTONISH", "uses": 20797}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 29090}, {"moveId": "DARK_PULSE", "uses": 24808}, {"moveId": "PSYCHIC", "uses": 22753}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "DARK_PULSE"], "score": 34.7}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 275, "matchups": [{"opponent": "ferrothorn", "rating": 731, "opRating": 268}, {"opponent": "decid<PERSON><PERSON>", "rating": 722, "opRating": 277}, {"opponent": "sci<PERSON>_shadow", "rating": 582, "opRating": 417}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 512, "opRating": 487}, {"opponent": "mewtwo_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "metagross", "rating": 125}, {"opponent": "lugia", "rating": 240}, {"opponent": "giratina_origin", "rating": 336}, {"opponent": "mewtwo", "rating": 445}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 45895}, {"moveId": "FIRE_FANG", "uses": 30605}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 30712}, {"moveId": "FLAMETHROWER", "uses": 18521}, {"moveId": "FOUL_PLAY", "uses": 13342}, {"moveId": "RETURN", "uses": 8943}, {"moveId": "FIRE_BLAST", "uses": 5026}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 34.7}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 324, "matchups": [{"opponent": "primarina", "rating": 682, "opRating": 317}, {"opponent": "genesect_chill", "rating": 615, "opRating": 384}, {"opponent": "genesect_burn", "rating": 615, "opRating": 384}, {"opponent": "registeel", "rating": 603, "opRating": 396}, {"opponent": "heatran", "rating": 600, "opRating": 399}], "counters": [{"opponent": "mewtwo", "rating": 52}, {"opponent": "garcho<PERSON>", "rating": 72}, {"opponent": "zacian_hero", "rating": 72}, {"opponent": "metagross", "rating": 133}, {"opponent": "dialga", "rating": 176}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 39995}, {"moveId": "FIRE_SPIN", "uses": 36505}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 25611}, {"moveId": "BRICK_BREAK", "uses": 17301}, {"moveId": "THUNDERBOLT", "uses": 15008}, {"moveId": "PSYCHIC", "uses": 13389}, {"moveId": "FIRE_BLAST", "uses": 5123}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 34.7}, {"speciesId": "skar<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 291, "matchups": [{"opponent": "venusaur", "rating": 677, "opRating": 322}, {"opponent": "celebi", "rating": 630, "opRating": 369}, {"opponent": "golisopod", "rating": 573, "opRating": 426}, {"opponent": "tapu_bulu", "rating": 536, "opRating": 463}, {"opponent": "nidoqueen", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "mewtwo", "rating": 130}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "garcho<PERSON>", "rating": 150}, {"opponent": "lugia", "rating": 207}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 44003}, {"moveId": "STEEL_WING", "uses": 32497}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 37527}, {"moveId": "SKY_ATTACK", "uses": 28088}, {"moveId": "FLASH_CANNON", "uses": 10834}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "SKY_ATTACK"], "score": 34.7}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 289, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 720, "opRating": 279}, {"opponent": "crobat", "rating": 663, "opRating": 336}, {"opponent": "cryogonal", "rating": 580, "opRating": 419}, {"opponent": "moltres", "rating": 569, "opRating": 430}, {"opponent": "articuno_shadow", "rating": 551, "opRating": 448}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "zacian_hero", "rating": 283}, {"opponent": "lugia", "rating": 319}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 429}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 30737}, {"moveId": "FIRE_FANG", "uses": 20460}, {"moveId": "THUNDER_FANG", "uses": 14461}, {"moveId": "BITE", "uses": 10765}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22455}, {"moveId": "CRUNCH", "uses": 16068}, {"moveId": "PSYCHIC_FANGS", "uses": 14511}, {"moveId": "FLAMETHROWER", "uses": 13272}, {"moveId": "BULLDOZE", "uses": 6701}, {"moveId": "FIRE_BLAST", "uses": 3574}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "CRUNCH"], "score": 34.5}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 263, "matchups": [{"opponent": "probopass", "rating": 621, "opRating": 378}, {"opponent": "gengar", "rating": 582, "opRating": 417}, {"opponent": "stunfisk", "rating": 545, "opRating": 454}, {"opponent": "gastrodon", "rating": 530, "opRating": 469}, {"opponent": "ninetales", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 154}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 176}, {"opponent": "gyarados", "rating": 188}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 63721}, {"moveId": "ZEN_HEADBUTT", "uses": 12779}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 32819}, {"moveId": "EARTHQUAKE", "uses": 25021}, {"moveId": "IRON_HEAD", "uses": 18711}]}, "moveset": ["TACKLE", "HORN_ATTACK", "EARTHQUAKE"], "score": 34.5}, {"speciesId": "omastar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 286, "matchups": [{"opponent": "entei", "rating": 770, "opRating": 229}, {"opponent": "moltres", "rating": 713, "opRating": 286}, {"opponent": "ho_oh", "rating": 687}, {"opponent": "avalugg", "rating": 595, "opRating": 404}, {"opponent": "heatran", "rating": 582, "opRating": 417}], "counters": [{"opponent": "mewtwo", "rating": 57}, {"opponent": "dialga", "rating": 89}, {"opponent": "lugia", "rating": 216}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 334}, {"opponent": "gyarados", "rating": 381}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30111}, {"moveId": "ROCK_THROW", "uses": 23630}, {"moveId": "WATER_GUN", "uses": 22807}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30176}, {"moveId": "ROCK_BLAST", "uses": 21551}, {"moveId": "HYDRO_PUMP", "uses": 14073}, {"moveId": "ANCIENT_POWER", "uses": 10852}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "ROCK_BLAST"], "score": 34.3}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 286, "matchups": [{"opponent": "gourgeist_super", "rating": 602, "opRating": 397}, {"opponent": "umbreon", "rating": 597, "opRating": 402}, {"opponent": "mandibuzz", "rating": 597, "opRating": 402}, {"opponent": "clefable", "rating": 560, "opRating": 439}, {"opponent": "aromatisse", "rating": 560, "opRating": 439}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "giratina_origin", "rating": 131}, {"opponent": "gyarados", "rating": 182}, {"opponent": "mewtwo", "rating": 184}, {"opponent": "zacian_hero", "rating": 184}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 48235}, {"moveId": "BITE", "uses": 28265}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38942}, {"moveId": "SLUDGE_BOMB", "uses": 21362}, {"moveId": "FLAMETHROWER", "uses": 16105}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CRUNCH", "SLUDGE_BOMB"], "score": 34.1}, {"speciesId": "<PERSON><PERSON>_<PERSON>", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 284, "matchups": [{"opponent": "hoopa_unbound", "rating": 603, "opRating": 396}, {"opponent": "abomasnow", "rating": 582, "opRating": 417}, {"opponent": "mr_rime", "rating": 576, "opRating": 423}, {"opponent": "mandibuzz", "rating": 573, "opRating": 426}, {"opponent": "aromatisse", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "lugia", "rating": 242}, {"opponent": "zacian_hero", "rating": 294}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 50856}, {"moveId": "STRUGGLE_BUG", "uses": 25644}], "chargedMoves": [{"moveId": "ROCK_BLAST", "uses": 33883}, {"moveId": "CROSS_POISON", "uses": 31369}, {"moveId": "WATER_PULSE", "uses": 11122}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "ROCK_BLAST", "CROSS_POISON"], "score": 33.8}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 237, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 670, "opRating": 329}, {"opponent": "ferrothorn", "rating": 667, "opRating": 332}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 643, "opRating": 356}, {"opponent": "celebi", "rating": 515, "opRating": 484}, {"opponent": "mewtwo", "rating": 506}], "counters": [{"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "dialga", "rating": 70}, {"opponent": "lugia", "rating": 88}, {"opponent": "metagross", "rating": 142}, {"opponent": "giratina_origin", "rating": 394}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 46894}, {"moveId": "FIRE_FANG", "uses": 29606}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 34736}, {"moveId": "FLAMETHROWER", "uses": 20954}, {"moveId": "FOUL_PLAY", "uses": 15010}, {"moveId": "FIRE_BLAST", "uses": 5840}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CRUNCH", "FLAMETHROWER"], "score": 33.8}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 238, "matchups": [{"opponent": "trevenant", "rating": 726, "opRating": 273}, {"opponent": "gourgeist_super", "rating": 671, "opRating": 328}, {"opponent": "virizion", "rating": 557, "opRating": 442}, {"opponent": "giratina_origin", "rating": 514}, {"opponent": "chesnaught", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 51}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "grou<PERSON>", "rating": 222}, {"opponent": "swampert", "rating": 266}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 45327}, {"moveId": "EXTRASENSORY", "uses": 31173}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33866}, {"moveId": "SHADOW_BALL", "uses": 23994}, {"moveId": "PSYCHIC", "uses": 14217}, {"moveId": "NIGHT_SHADE", "uses": 4426}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "SHADOW_BALL"], "score": 33.8}, {"speciesId": "noivern", "speciesName": "Noivern", "rating": 263, "matchups": [{"opponent": "chesnaught", "rating": 691, "opRating": 308}, {"opponent": "seismitoad", "rating": 676, "opRating": 323}, {"opponent": "ferrothorn", "rating": 651, "opRating": 348}, {"opponent": "virizion", "rating": 603, "opRating": 396}, {"opponent": "pinsir", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 81}, {"opponent": "giratina_origin", "rating": 141}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "grou<PERSON>", "rating": 288}, {"opponent": "swampert", "rating": 293}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 46616}, {"moveId": "BITE", "uses": 29884}], "chargedMoves": [{"moveId": "HURRICANE", "uses": 24787}, {"moveId": "DRACO_METEOR", "uses": 22075}, {"moveId": "PSYCHIC", "uses": 20092}, {"moveId": "HEAT_WAVE", "uses": 9564}]}, "moveset": ["AIR_SLASH", "HURRICANE", "DRACO_METEOR"], "score": 33.8}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 285, "matchups": [{"opponent": "gengar", "rating": 717, "opRating": 282}, {"opponent": "stunfisk_galarian", "rating": 579, "opRating": 420}, {"opponent": "muk", "rating": 555, "opRating": 444}, {"opponent": "golisopod", "rating": 545, "opRating": 454}, {"opponent": "sandslash_alolan", "rating": 527, "opRating": 472}], "counters": [{"opponent": "lugia", "rating": 123}, {"opponent": "garcho<PERSON>", "rating": 178}, {"opponent": "gyarados", "rating": 206}, {"opponent": "zacian_hero", "rating": 268}, {"opponent": "giratina_origin", "rating": 324}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 76500}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 42566}, {"moveId": "EARTHQUAKE", "uses": 17054}, {"moveId": "PLAY_ROUGH", "uses": 12072}, {"moveId": "HYPER_BEAM", "uses": 4740}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 33.8}, {"speciesId": "araquanid", "speciesName": "Araquanid", "rating": 250, "matchups": [{"opponent": "oranguru", "rating": 574, "opRating": 425}, {"opponent": "mr_rime", "rating": 571, "opRating": 428}, {"opponent": "umbreon", "rating": 542, "opRating": 457}, {"opponent": "slowking", "rating": 538, "opRating": 461}, {"opponent": "abomasnow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "zacian_hero", "rating": 150}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "grou<PERSON>", "rating": 293}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 39342}, {"moveId": "INFESTATION", "uses": 37158}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 39587}, {"moveId": "BUBBLE_BEAM", "uses": 22407}, {"moveId": "MIRROR_COAT", "uses": 14574}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BUBBLE_BEAM"], "score": 33.6}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 242, "matchups": [{"opponent": "nidoqueen", "rating": 626, "opRating": 373}, {"opponent": "nidoqueen_shadow", "rating": 552, "opRating": 447}, {"opponent": "thundurus_incarnate", "rating": 527, "opRating": 472}, {"opponent": "gliscor", "rating": 514, "opRating": 485}, {"opponent": "zapdos", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "giratina_origin", "rating": 141}, {"opponent": "dragonite", "rating": 188}, {"opponent": "garcho<PERSON>", "rating": 237}, {"opponent": "lugia", "rating": 278}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 44556}, {"moveId": "ICE_SHARD", "uses": 31944}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 41413}, {"moveId": "STONE_EDGE", "uses": 15319}, {"moveId": "BULLDOZE", "uses": 12861}, {"moveId": "RETURN", "uses": 6833}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 33.6}, {"speciesId": "<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 251, "matchups": [{"opponent": "ninetales", "rating": 694, "opRating": 305}, {"opponent": "heatran", "rating": 610, "opRating": 389}, {"opponent": "sandslash_alolan", "rating": 601, "opRating": 398}, {"opponent": "sandslash_alolan_shadow", "rating": 537, "opRating": 462}, {"opponent": "beartic", "rating": 529, "opRating": 470}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "metagross", "rating": 177}, {"opponent": "excadrill", "rating": 344}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40090}, {"moveId": "CONFUSION", "uses": 36410}], "chargedMoves": [{"moveId": "CROSS_CHOP", "uses": 22987}, {"moveId": "ICE_BEAM", "uses": 18263}, {"moveId": "SYNCHRONOISE", "uses": 11948}, {"moveId": "HYDRO_PUMP", "uses": 10213}, {"moveId": "BUBBLE_BEAM", "uses": 7542}, {"moveId": "PSYCHIC", "uses": 5430}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WATER_GUN", "CROSS_CHOP", "ICE_BEAM"], "score": 33.4}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 294, "matchups": [{"opponent": "moltres_galarian", "rating": 680, "opRating": 319}, {"opponent": "milotic", "rating": 563, "opRating": 436}, {"opponent": "primarina", "rating": 558, "opRating": 441}, {"opponent": "articuno_shadow", "rating": 546, "opRating": 453}, {"opponent": "golisopod", "rating": 529, "opRating": 470}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "mewtwo", "rating": 96}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "gyarados", "rating": 311}, {"opponent": "lugia", "rating": 342}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 10865}, {"moveId": "SNARL", "uses": 9063}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4597}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4336}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4087}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3855}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3744}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3644}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3640}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3590}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3455}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3340}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3298}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3055}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2992}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2990}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2950}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2752}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32792}, {"moveId": "CRUNCH", "uses": 18672}, {"moveId": "PSYCHIC_FANGS", "uses": 16249}, {"moveId": "RETURN", "uses": 6305}, {"moveId": "HYPER_BEAM", "uses": 2493}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 33.4}, {"speciesId": "primeape", "speciesName": "Primeape", "rating": 260, "matchups": [{"opponent": "s<PERSON><PERSON>", "rating": 597, "opRating": 402}, {"opponent": "steelix", "rating": 567, "opRating": 432}, {"opponent": "registeel", "rating": 563, "opRating": 436}, {"opponent": "snorlax", "rating": 546, "opRating": 453}, {"opponent": "obstagoon", "rating": 543, "opRating": 456}], "counters": [{"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "gyarados", "rating": 105}, {"opponent": "metagross", "rating": 125}, {"opponent": "dialga", "rating": 220}, {"opponent": "excadrill", "rating": 300}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37350}, {"moveId": "KARATE_CHOP", "uses": 34507}, {"moveId": "LOW_KICK", "uses": 4636}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 23352}, {"moveId": "NIGHT_SLASH", "uses": 18720}, {"moveId": "CROSS_CHOP", "uses": 16158}, {"moveId": "ICE_PUNCH", "uses": 15213}, {"moveId": "LOW_SWEEP", "uses": 3025}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 33.2}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 244, "matchups": [{"opponent": "heracross", "rating": 697, "opRating": 302}, {"opponent": "chesnaught", "rating": 637, "opRating": 362}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 604, "opRating": 395}, {"opponent": "virizion", "rating": 588, "opRating": 411}, {"opponent": "gallade", "rating": 534, "opRating": 465}], "counters": [{"opponent": "dialga", "rating": 81}, {"opponent": "mewtwo", "rating": 117}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "dragonite", "rating": 172}, {"opponent": "zacian_hero", "rating": 184}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 64041}, {"moveId": "ZEN_HEADBUTT", "uses": 12459}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 38300}, {"moveId": "PSYCHIC", "uses": 17147}, {"moveId": "OMINOUS_WIND", "uses": 11680}, {"moveId": "DAZZLING_GLEAM", "uses": 9350}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "PSYCHIC"], "score": 33}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 234, "matchups": [{"opponent": "golem_shadow", "rating": 679, "opRating": 320}, {"opponent": "ninetales_shadow", "rating": 601, "opRating": 398}, {"opponent": "rapidash", "rating": 601, "opRating": 398}, {"opponent": "ninetales", "rating": 550, "opRating": 449}, {"opponent": "rhydon", "rating": 547, "opRating": 452}], "counters": [{"opponent": "mewtwo", "rating": 93}, {"opponent": "dialga", "rating": 114}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "metagross", "rating": 177}, {"opponent": "excadrill", "rating": 351}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 39061}, {"moveId": "WATER_GUN", "uses": 37439}], "chargedMoves": [{"moveId": "HYDRO_PUMP", "uses": 30961}, {"moveId": "AQUA_JET", "uses": 29002}, {"moveId": "SWIFT", "uses": 16447}]}, "moveset": ["WATERFALL", "HYDRO_PUMP", "AQUA_JET"], "score": 33}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 226, "matchups": [{"opponent": "giratina_origin", "rating": 702}, {"opponent": "gourgeist_super", "rating": 584, "opRating": 415}, {"opponent": "lickilicky", "rating": 581, "opRating": 418}, {"opponent": "clefable", "rating": 544, "opRating": 455}, {"opponent": "aromatisse", "rating": 544, "opRating": 455}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "garcho<PERSON>", "rating": 68}, {"opponent": "gyarados", "rating": 100}, {"opponent": "excadrill", "rating": 267}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 339}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 13068}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4988}, {"moveId": "CHARGE_BEAM", "uses": 4706}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4287}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4010}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3953}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3939}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3854}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3796}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3644}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3603}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3596}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3554}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3338}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3283}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3264}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3081}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2975}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 23072}, {"moveId": "BLIZZARD", "uses": 17612}, {"moveId": "ZAP_CANNON", "uses": 14702}, {"moveId": "HYPER_BEAM", "uses": 11280}, {"moveId": "SOLAR_BEAM", "uses": 9649}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "BLIZZARD"], "score": 33}, {"speciesId": "eelektross", "speciesName": "Eelektross", "rating": 281, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 643, "opRating": 356}, {"opponent": "crobat", "rating": 601, "opRating": 398}, {"opponent": "drifb<PERSON>", "rating": 589, "opRating": 410}, {"opponent": "skar<PERSON><PERSON>_shadow", "rating": 589, "opRating": 410}, {"opponent": "altaria", "rating": 558, "opRating": 441}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 96}, {"opponent": "zacian_hero", "rating": 124}, {"opponent": "lugia", "rating": 240}, {"opponent": "gyarados", "rating": 448}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 57641}, {"moveId": "ACID", "uses": 18859}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 27210}, {"moveId": "CRUNCH", "uses": 25142}, {"moveId": "THUNDERBOLT", "uses": 20559}, {"moveId": "ACID_SPRAY", "uses": 3581}]}, "moveset": ["SPARK", "DRAGON_CLAW", "CRUNCH"], "score": 32.8}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 274, "matchups": [{"opponent": "swampert", "rating": 664}, {"opponent": "heracross", "rating": 621, "opRating": 378}, {"opponent": "swampert_shadow", "rating": 592, "opRating": 407}, {"opponent": "tapu_fini", "rating": 559, "opRating": 440}, {"opponent": "virizion", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 29}, {"opponent": "gyarados", "rating": 92}, {"opponent": "zacian_hero", "rating": 95}, {"opponent": "excadrill", "rating": 288}, {"opponent": "garcho<PERSON>", "rating": 293}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 26291}, {"moveId": "CONFUSION", "uses": 25122}, {"moveId": "EXTRASENSORY", "uses": 19688}, {"moveId": "ZEN_HEADBUTT", "uses": 5426}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26828}, {"moveId": "PSYCHIC", "uses": 24532}, {"moveId": "RETURN", "uses": 12892}, {"moveId": "SOLAR_BEAM", "uses": 12221}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 32.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 254, "matchups": [{"opponent": "poliwrath", "rating": 595, "opRating": 404}, {"opponent": "tentacruel", "rating": 595, "opRating": 404}, {"opponent": "ninetales", "rating": 560, "opRating": 439}, {"opponent": "nidoqueen", "rating": 503, "opRating": 496}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "gyarados", "rating": 188}, {"opponent": "dragonite", "rating": 194}, {"opponent": "zacian_hero", "rating": 205}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 43584}, {"moveId": "CHARM", "uses": 32916}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 37379}, {"moveId": "PSYCHIC", "uses": 27321}, {"moveId": "FUTURE_SIGHT", "uses": 11796}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 32.8}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 211, "matchups": [{"opponent": "gengar", "rating": 619, "opRating": 380}, {"opponent": "gourgeist_small", "rating": 565, "opRating": 434}, {"opponent": "probopass_shadow", "rating": 555, "opRating": 444}, {"opponent": "mismagius", "rating": 555, "opRating": 444}, {"opponent": "gourgeist_average", "rating": 553, "opRating": 446}], "counters": [{"opponent": "dialga", "rating": 103}, {"opponent": "mewtwo", "rating": 166}, {"opponent": "lugia", "rating": 173}, {"opponent": "garcho<PERSON>", "rating": 201}, {"opponent": "giratina_origin", "rating": 338}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 39043}, {"moveId": "TACKLE", "uses": 37457}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 53108}, {"moveId": "BULLDOZE", "uses": 14677}, {"moveId": "GUNK_SHOT", "uses": 8755}]}, "moveset": ["LICK", "BODY_SLAM", "BULLDOZE"], "score": 32.8}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 243, "matchups": [{"opponent": "gardevoir_shadow", "rating": 606, "opRating": 393}, {"opponent": "tapu_koko", "rating": 569, "opRating": 430}, {"opponent": "klinklang", "rating": 563, "opRating": 436}, {"opponent": "obstagoon", "rating": 514, "opRating": 485}, {"opponent": "ampha<PERSON>", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "gyarados", "rating": 144}, {"opponent": "zacian_hero", "rating": 361}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 424}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26023}, {"moveId": "DOUBLE_KICK", "uses": 25814}, {"moveId": "FURY_CUTTER", "uses": 21128}, {"moveId": "IRON_TAIL", "uses": 3480}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 22486}, {"moveId": "MEGAHORN", "uses": 19145}, {"moveId": "SLUDGE_WAVE", "uses": 15600}, {"moveId": "EARTHQUAKE", "uses": 9685}, {"moveId": "SAND_TOMB", "uses": 9429}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 32.8}, {"speciesId": "pheromosa", "speciesName": "Pheromosa", "rating": 228, "matchups": [{"opponent": "exeggutor_shadow", "rating": 898, "opRating": 101}, {"opponent": "malamar", "rating": 822, "opRating": 177}, {"opponent": "exeggutor", "rating": 699, "opRating": 300}, {"opponent": "shiftry", "rating": 689, "opRating": 310}, {"opponent": "meganium_shadow", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 140}, {"opponent": "metagross", "rating": 188}, {"opponent": "mewtwo", "rating": 200}, {"opponent": "lugia", "rating": 221}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 60277}, {"moveId": "LOW_KICK", "uses": 16223}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 37069}, {"moveId": "LUNGE", "uses": 20638}, {"moveId": "BUG_BUZZ", "uses": 13412}, {"moveId": "FOCUS_BLAST", "uses": 5420}]}, "moveset": ["BUG_BITE", "CLOSE_COMBAT", "LUNGE"], "score": 32.8}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 268, "matchups": [{"opponent": "magnezone", "rating": 707, "opRating": 292}, {"opponent": "magnezone_shadow", "rating": 664, "opRating": 335}, {"opponent": "nihilego", "rating": 661, "opRating": 338}, {"opponent": "rai<PERSON>u", "rating": 640, "opRating": 359}, {"opponent": "melmetal", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "giratina_origin", "rating": 163}, {"opponent": "lugia", "rating": 183}, {"opponent": "mewtwo", "rating": 210}, {"opponent": "excadrill", "rating": 232}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53378}, {"moveId": "METAL_CLAW", "uses": 23122}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28701}, {"moveId": "EARTHQUAKE", "uses": 17840}, {"moveId": "BULLDOZE", "uses": 10951}, {"moveId": "ROCK_TOMB", "uses": 10636}, {"moveId": "RETURN", "uses": 8497}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 32.8}, {"speciesId": "malamar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 261, "matchups": [{"opponent": "bronzong", "rating": 738, "opRating": 261}, {"opponent": "slowking_galarian", "rating": 702, "opRating": 297}, {"opponent": "espeon", "rating": 577, "opRating": 422}, {"opponent": "obstagoon", "rating": 536, "opRating": 463}, {"opponent": "mewtwo_armored", "rating": 516, "opRating": 483}], "counters": [{"opponent": "lugia", "rating": 157}, {"opponent": "garcho<PERSON>", "rating": 199}, {"opponent": "giratina_origin", "rating": 229}, {"opponent": "gyarados", "rating": 231}, {"opponent": "mewtwo", "rating": 289}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 58064}, {"moveId": "PECK", "uses": 18436}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 31969}, {"moveId": "SUPER_POWER", "uses": 28261}, {"moveId": "PSYBEAM", "uses": 8558}, {"moveId": "HYPER_BEAM", "uses": 7697}]}, "moveset": ["PSYCHO_CUT", "FOUL_PLAY", "SUPER_POWER"], "score": 32.4}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 251, "matchups": [{"opponent": "klinklang", "rating": 656, "opRating": 343}, {"opponent": "ampha<PERSON>", "rating": 583, "opRating": 416}, {"opponent": "magnezone", "rating": 563, "opRating": 436}, {"opponent": "aromatisse", "rating": 543, "opRating": 456}, {"opponent": "golem_alolan", "rating": 523, "opRating": 476}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "lugia", "rating": 126}, {"opponent": "gyarados", "rating": 157}, {"opponent": "zacian_hero", "rating": 291}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 382}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 26123}, {"moveId": "DOUBLE_KICK", "uses": 25234}, {"moveId": "FURY_CUTTER", "uses": 21305}, {"moveId": "IRON_TAIL", "uses": 3876}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 20310}, {"moveId": "MEGAHORN", "uses": 16722}, {"moveId": "SLUDGE_WAVE", "uses": 13218}, {"moveId": "EARTHQUAKE", "uses": 8790}, {"moveId": "RETURN", "uses": 8777}, {"moveId": "SAND_TOMB", "uses": 8636}]}, "moveset": ["POISON_JAB", "EARTH_POWER", "MEGAHORN"], "score": 32.4}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 283, "matchups": [{"opponent": "ferrothorn", "rating": 795, "opRating": 204}, {"opponent": "to<PERSON><PERSON>_shadow", "rating": 726, "opRating": 273}, {"opponent": "genesect_chill", "rating": 624, "opRating": 375}, {"opponent": "genesect_burn", "rating": 624, "opRating": 375}, {"opponent": "s<PERSON><PERSON>", "rating": 571, "opRating": 428}], "counters": [{"opponent": "mewtwo", "rating": 85}, {"opponent": "dialga", "rating": 116}, {"opponent": "zacian_hero", "rating": 124}, {"opponent": "lugia", "rating": 157}, {"opponent": "metagross", "rating": 194}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 28291}, {"moveId": "EMBER", "uses": 27257}, {"moveId": "FEINT_ATTACK", "uses": 20943}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 30699}, {"moveId": "PSYSHOCK", "uses": 13524}, {"moveId": "OVERHEAT", "uses": 12345}, {"moveId": "SOLAR_BEAM", "uses": 7437}, {"moveId": "FLAMETHROWER", "uses": 6637}, {"moveId": "FIRE_BLAST", "uses": 3553}, {"moveId": "HEAT_WAVE", "uses": 2151}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "WEATHER_BALL_FIRE", "PSYSHOCK"], "score": 32.4}, {"speciesId": "mantine", "speciesName": "<PERSON><PERSON>", "rating": 245, "matchups": [{"opponent": "seismitoad", "rating": 681, "opRating": 318}, {"opponent": "flygon", "rating": 593, "opRating": 406}, {"opponent": "gliscor_shadow", "rating": 570, "opRating": 429}, {"opponent": "buzzwole", "rating": 520, "opRating": 479}, {"opponent": "scrafty", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "swampert", "rating": 318}, {"opponent": "grou<PERSON>", "rating": 353}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 26863}, {"moveId": "BUBBLE", "uses": 26193}, {"moveId": "BULLET_SEED", "uses": 23457}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 26868}, {"moveId": "AERIAL_ACE", "uses": 24176}, {"moveId": "BUBBLE_BEAM", "uses": 12973}, {"moveId": "WATER_PULSE", "uses": 12441}]}, "moveset": ["WING_ATTACK", "ICE_BEAM", "AERIAL_ACE"], "score": 31.7}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 260, "matchups": [{"opponent": "sci<PERSON>_shadow", "rating": 644, "opRating": 355}, {"opponent": "heatran", "rating": 637, "opRating": 362}, {"opponent": "gardevoir_shadow", "rating": 577, "opRating": 422}, {"opponent": "steelix", "rating": 536, "opRating": 463}, {"opponent": "articuno_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 100}, {"opponent": "zacian_hero", "rating": 130}, {"opponent": "lugia", "rating": 159}, {"opponent": "metagross", "rating": 212}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 272}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 31316}, {"moveId": "FIRE_SPIN", "uses": 20283}, {"moveId": "EMBER", "uses": 20051}, {"moveId": "LOW_KICK", "uses": 4891}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 32923}, {"moveId": "FLAME_CHARGE", "uses": 27462}, {"moveId": "FIRE_BLAST", "uses": 12374}, {"moveId": "HEAT_WAVE", "uses": 3688}]}, "moveset": ["INCINERATE", "DRILL_RUN", "FLAME_CHARGE"], "score": 31.7}, {"speciesId": "magneton_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 250, "matchups": [{"opponent": "aromatisse", "rating": 724, "opRating": 275}, {"opponent": "aurorus", "rating": 653, "opRating": 346}, {"opponent": "primarina", "rating": 641, "opRating": 358}, {"opponent": "gardevoir", "rating": 641, "opRating": 358}, {"opponent": "gardevoir_shadow", "rating": 559, "opRating": 440}], "counters": [{"opponent": "dialga", "rating": 176}, {"opponent": "gyarados", "rating": 211}, {"opponent": "lugia", "rating": 297}, {"opponent": "sylveon", "rating": 409}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 446}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30929}, {"moveId": "SPARK", "uses": 28186}, {"moveId": "CHARGE_BEAM", "uses": 17420}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 32000}, {"moveId": "DISCHARGE", "uses": 27008}, {"moveId": "ZAP_CANNON", "uses": 10693}, {"moveId": "FLASH_CANNON", "uses": 6785}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "MAGNET_BOMB", "DISCHARGE"], "score": 31.5}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 263, "matchups": [{"opponent": "articuno", "rating": 661, "opRating": 338}, {"opponent": "moltres_galarian", "rating": 605, "opRating": 394}, {"opponent": "articuno_galarian", "rating": 602, "opRating": 397}, {"opponent": "articuno_shadow", "rating": 588, "opRating": 411}, {"opponent": "golisopod", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "giratina_origin", "rating": 155}, {"opponent": "gyarados", "rating": 188}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 387}, {"opponent": "lugia", "rating": 414}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 39344}, {"moveId": "ROCK_THROW", "uses": 37156}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34331}, {"moveId": "MAGNET_BOMB", "uses": 27634}, {"moveId": "THUNDERBOLT", "uses": 14427}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "MAGNET_BOMB"], "score": 31.5}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 314, "matchups": [{"opponent": "magnezone", "rating": 680, "opRating": 319}, {"opponent": "nihilego", "rating": 639, "opRating": 360}, {"opponent": "magnezone_shadow", "rating": 627, "opRating": 372}, {"opponent": "rai<PERSON>u", "rating": 622, "opRating": 377}, {"opponent": "heatran", "rating": 558, "opRating": 441}], "counters": [{"opponent": "mewtwo", "rating": 65}, {"opponent": "metagross", "rating": 104}, {"opponent": "dialga", "rating": 111}, {"opponent": "swampert", "rating": 218}, {"opponent": "excadrill", "rating": 253}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 40491}, {"moveId": "DRAGON_TAIL", "uses": 36009}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 30278}, {"moveId": "EARTH_POWER", "uses": 20164}, {"moveId": "STONE_EDGE", "uses": 17387}, {"moveId": "EARTHQUAKE", "uses": 8760}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DRAGON_CLAW", "EARTH_POWER"], "score": 31.3}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 233, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 759, "opRating": 240}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 614, "opRating": 385}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 596, "opRating": 403}, {"opponent": "gallade", "rating": 574, "opRating": 425}, {"opponent": "ampha<PERSON>", "rating": 566, "opRating": 433}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "mewtwo", "rating": 85}, {"opponent": "garcho<PERSON>", "rating": 260}, {"opponent": "zacian_hero", "rating": 274}, {"opponent": "swampert", "rating": 383}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 49276}, {"moveId": "RAZOR_LEAF", "uses": 27224}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 26413}, {"moveId": "SEED_BOMB", "uses": 21648}, {"moveId": "FOUL_PLAY", "uses": 19803}, {"moveId": "FIRE_BLAST", "uses": 8567}]}, "moveset": ["HEX", "SHADOW_BALL", "SEED_BOMB"], "score": 31.3}, {"speciesId": "hunt<PERSON>", "speciesName": "Huntail", "rating": 236, "matchups": [{"opponent": "ninetales", "rating": 688, "opRating": 311}, {"opponent": "ninetales_shadow", "rating": 659, "opRating": 340}, {"opponent": "entei", "rating": 588, "opRating": 411}, {"opponent": "pyroar", "rating": 540, "opRating": 459}, {"opponent": "emboar", "rating": 518, "opRating": 481}], "counters": [{"opponent": "mewtwo", "rating": 93}, {"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "dialga", "rating": 95}, {"opponent": "metagross", "rating": 177}, {"opponent": "excadrill", "rating": 295}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 49026}, {"moveId": "BITE", "uses": 27474}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 33343}, {"moveId": "CRUNCH", "uses": 24012}, {"moveId": "ICE_BEAM", "uses": 19104}]}, "moveset": ["WATER_GUN", "AQUA_TAIL", "CRUNCH"], "score": 31.3}, {"speciesId": "crawdaunt", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 223, "matchups": [{"opponent": "oranguru", "rating": 741, "opRating": 258}, {"opponent": "delphox", "rating": 649, "opRating": 350}, {"opponent": "pyroar", "rating": 629, "opRating": 370}, {"opponent": "cryogonal", "rating": 591, "opRating": 408}, {"opponent": "espeon", "rating": 571, "opRating": 428}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "gyarados", "rating": 201}, {"opponent": "giratina_origin", "rating": 276}, {"opponent": "metagross", "rating": 305}, {"opponent": "mewtwo", "rating": 380}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 44499}, {"moveId": "WATERFALL", "uses": 32001}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 39298}, {"moveId": "CRABHAMMER", "uses": 23023}, {"moveId": "VICE_GRIP", "uses": 7354}, {"moveId": "BUBBLE_BEAM", "uses": 6913}]}, "moveset": ["SNARL", "NIGHT_SLASH", "CRABHAMMER"], "score": 31.1}, {"speciesId": "hitmontop", "speciesName": "Hitmontop", "rating": 244, "matchups": [{"opponent": "bisharp", "rating": 755, "opRating": 244}, {"opponent": "stunfisk_galarian", "rating": 625, "opRating": 374}, {"opponent": "obstagoon", "rating": 598, "opRating": 401}, {"opponent": "s<PERSON><PERSON>", "rating": 586, "opRating": 413}, {"opponent": "aggron", "rating": 507, "opRating": 492}], "counters": [{"opponent": "garcho<PERSON>", "rating": 98}, {"opponent": "metagross", "rating": 104}, {"opponent": "gyarados", "rating": 126}, {"opponent": "dialga", "rating": 198}, {"opponent": "excadrill", "rating": 327}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 63431}, {"moveId": "ROCK_SMASH", "uses": 13069}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 46270}, {"moveId": "STONE_EDGE", "uses": 22108}, {"moveId": "GYRO_BALL", "uses": 8124}]}, "moveset": ["COUNTER", "CLOSE_COMBAT", "STONE_EDGE"], "score": 31.1}, {"speciesId": "jellicent", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 266, "matchups": [{"opponent": "poliwrath", "rating": 659, "opRating": 340}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 636, "opRating": 363}, {"opponent": "victini", "rating": 587, "opRating": 412}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 584, "opRating": 415}, {"opponent": "pinsir", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "garcho<PERSON>", "rating": 86}, {"opponent": "mewtwo", "rating": 93}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "metagross", "rating": 177}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 41490}, {"moveId": "BUBBLE", "uses": 35010}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37813}, {"moveId": "ICE_BEAM", "uses": 25808}, {"moveId": "BUBBLE_BEAM", "uses": 12922}]}, "moveset": ["HEX", "SHADOW_BALL", "ICE_BEAM"], "score": 30.9}, {"speciesId": "scyther_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 262, "matchups": [{"opponent": "to<PERSON><PERSON>_shadow", "rating": 789, "opRating": 210}, {"opponent": "obstagoon", "rating": 732, "opRating": 267}, {"opponent": "celebi", "rating": 678, "opRating": 321}, {"opponent": "virizion", "rating": 570, "opRating": 429}, {"opponent": "zarude", "rating": 570, "opRating": 429}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "zacian_hero", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 100}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "swampert", "rating": 266}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 35947}, {"moveId": "AIR_SLASH", "uses": 25110}, {"moveId": "STEEL_WING", "uses": 15371}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 28330}, {"moveId": "X_SCISSOR", "uses": 20069}, {"moveId": "AERIAL_ACE", "uses": 14977}, {"moveId": "BUG_BUZZ", "uses": 13073}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "NIGHT_SLASH", "X_SCISSOR"], "score": 30.9}, {"speciesId": "gorebyss", "speciesName": "<PERSON><PERSON>", "rating": 212, "matchups": [{"opponent": "magmar_shadow", "rating": 574, "opRating": 425}], "counters": [{"opponent": "dialga", "rating": 97}, {"opponent": "mewtwo", "rating": 101}, {"opponent": "garcho<PERSON>", "rating": 105}, {"opponent": "zacian_hero", "rating": 147}, {"opponent": "metagross", "rating": 177}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40683}, {"moveId": "CONFUSION", "uses": 35817}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 32053}, {"moveId": "WATER_PULSE", "uses": 23936}, {"moveId": "DRAINING_KISS", "uses": 20497}]}, "moveset": ["WATER_GUN", "PSYCHIC", "WATER_PULSE"], "score": 30.7}, {"speciesId": "mr_mime", "speciesName": "Mr. <PERSON><PERSON>", "rating": 215, "matchups": [{"opponent": "toxicroak", "rating": 893, "opRating": 106}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 659, "opRating": 340}, {"opponent": "dragalge", "rating": 566, "opRating": 433}, {"opponent": "hitmonchan", "rating": 539, "opRating": 460}, {"opponent": "sneasler", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 86}, {"opponent": "garcho<PERSON>", "rating": 122}, {"opponent": "zacian_hero", "rating": 127}, {"opponent": "gyarados", "rating": 203}, {"opponent": "dragonite", "rating": 287}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 63744}, {"moveId": "ZEN_HEADBUTT", "uses": 12756}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37783}, {"moveId": "PSYCHIC", "uses": 32150}, {"moveId": "PSYBEAM", "uses": 6433}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 30.7}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 229, "matchups": [{"opponent": "probopass", "rating": 708, "opRating": 291}, {"opponent": "gengar", "rating": 698, "opRating": 301}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 546, "opRating": 453}, {"opponent": "gourgeist_super", "rating": 537, "opRating": 462}, {"opponent": "bisharp", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 104}, {"opponent": "lugia", "rating": 109}, {"opponent": "dialga", "rating": 111}, {"opponent": "metagross", "rating": 145}, {"opponent": "giratina_origin", "rating": 332}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 45365}, {"moveId": "FURY_CUTTER", "uses": 31135}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 35975}, {"moveId": "NIGHT_SLASH", "uses": 35890}, {"moveId": "DIG", "uses": 4534}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 30.7}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 259, "matchups": [{"opponent": "genesect_chill", "rating": 692, "opRating": 307}, {"opponent": "genesect_burn", "rating": 692, "opRating": 307}, {"opponent": "heatran", "rating": 608, "opRating": 391}, {"opponent": "genesect_shock", "rating": 545, "opRating": 454}, {"opponent": "genesect", "rating": 545, "opRating": 454}], "counters": [{"opponent": "mewtwo", "rating": 70}, {"opponent": "gyarados", "rating": 95}, {"opponent": "dialga", "rating": 111}, {"opponent": "zacian_hero", "rating": 124}, {"opponent": "metagross", "rating": 194}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 58890}, {"moveId": "ROCK_SMASH", "uses": 17610}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 33654}, {"moveId": "BLAST_BURN", "uses": 29730}, {"moveId": "FLAMETHROWER", "uses": 6849}, {"moveId": "SOLAR_BEAM", "uses": 6250}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 30.5}, {"speciesId": "hypno", "speciesName": "Hypno", "rating": 241, "matchups": [{"opponent": "toxicroak", "rating": 668, "opRating": 331}, {"opponent": "poliwrath_shadow", "rating": 626, "opRating": 373}, {"opponent": "poliwrath", "rating": 606, "opRating": 393}, {"opponent": "weezing_galarian", "rating": 601, "opRating": 398}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 505, "opRating": 494}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "gyarados", "rating": 131}, {"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "dragonite", "rating": 162}, {"opponent": "zacian_hero", "rating": 164}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 66908}, {"moveId": "ZEN_HEADBUTT", "uses": 9592}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14024}, {"moveId": "PSYSHOCK", "uses": 11245}, {"moveId": "SHADOW_BALL", "uses": 10880}, {"moveId": "FIRE_PUNCH", "uses": 10628}, {"moveId": "THUNDER_PUNCH", "uses": 10370}, {"moveId": "FOCUS_BLAST", "uses": 6922}, {"moveId": "RETURN", "uses": 4404}, {"moveId": "PSYCHIC", "uses": 4386}, {"moveId": "FUTURE_SIGHT", "uses": 3793}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 30.1}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 223, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 748, "opRating": 251}, {"opponent": "drifb<PERSON>", "rating": 648, "opRating": 351}, {"opponent": "lanturn", "rating": 606, "opRating": 393}, {"opponent": "golisopod", "rating": 600, "opRating": 400}, {"opponent": "mandibuzz", "rating": 579, "opRating": 420}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "lugia", "rating": 159}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 227}, {"opponent": "giratina_origin", "rating": 243}, {"opponent": "gyarados", "rating": 363}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 32318}, {"moveId": "QUICK_ATTACK", "uses": 26822}, {"moveId": "MUD_SLAP", "uses": 17350}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 27680}, {"moveId": "GRASS_KNOT", "uses": 24980}, {"moveId": "BULLDOZE", "uses": 16538}, {"moveId": "PARABOLIC_CHARGE", "uses": 7267}]}, "moveset": ["VOLT_SWITCH", "THUNDERBOLT", "GRASS_KNOT"], "score": 29.9}, {"speciesId": "simipour", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 212, "matchups": [{"opponent": "<PERSON>on", "rating": 661, "opRating": 338}, {"opponent": "torkoal", "rating": 594, "opRating": 405}, {"opponent": "heatran", "rating": 591, "opRating": 408}, {"opponent": "lair<PERSON>_shadow", "rating": 576, "opRating": 423}, {"opponent": "ninetales", "rating": 557, "opRating": 442}], "counters": [{"opponent": "dialga", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 98}, {"opponent": "zacian_hero", "rating": 132}, {"opponent": "metagross", "rating": 177}, {"opponent": "excadrill", "rating": 281}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 48282}, {"moveId": "BITE", "uses": 28218}], "chargedMoves": [{"moveId": "SURF", "uses": 39495}, {"moveId": "CRUNCH", "uses": 30664}, {"moveId": "HYDRO_PUMP", "uses": 6333}]}, "moveset": ["WATER_GUN", "SURF", "CRUNCH"], "score": 29.7}, {"speciesId": "hit<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 202, "matchups": [{"opponent": "sandslash_alolan", "rating": 803, "opRating": 196}, {"opponent": "bisharp", "rating": 688, "opRating": 311}, {"opponent": "probopass", "rating": 669, "opRating": 330}, {"opponent": "sandslash_alolan_shadow", "rating": 570, "opRating": 429}, {"opponent": "magneton", "rating": 527, "opRating": 472}], "counters": [{"opponent": "gyarados", "rating": 90}, {"opponent": "garcho<PERSON>", "rating": 117}, {"opponent": "metagross", "rating": 130}, {"opponent": "excadrill", "rating": 213}, {"opponent": "dialga", "rating": 244}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 46932}, {"moveId": "LOW_KICK", "uses": 29568}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30848}, {"moveId": "STONE_EDGE", "uses": 16576}, {"moveId": "BRICK_BREAK", "uses": 13697}, {"moveId": "STOMP", "uses": 11339}, {"moveId": "LOW_SWEEP", "uses": 3979}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_SMASH", "CLOSE_COMBAT", "STONE_EDGE"], "score": 29.4}, {"speciesId": "hypno_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 230, "matchups": [{"opponent": "hitmonchan", "rating": 651, "opRating": 348}, {"opponent": "poliwrath", "rating": 626, "opRating": 373}, {"opponent": "poliwrath_shadow", "rating": 615, "opRating": 384}, {"opponent": "altaria", "rating": 606, "opRating": 393}, {"opponent": "lilligant", "rating": 606, "opRating": 393}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "gyarados", "rating": 126}, {"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "dragonite", "rating": 162}, {"opponent": "zacian_hero", "rating": 176}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 67027}, {"moveId": "ZEN_HEADBUTT", "uses": 9473}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 14863}, {"moveId": "PSYSHOCK", "uses": 11935}, {"moveId": "SHADOW_BALL", "uses": 11543}, {"moveId": "FIRE_PUNCH", "uses": 11231}, {"moveId": "THUNDER_PUNCH", "uses": 11034}, {"moveId": "FOCUS_BLAST", "uses": 7315}, {"moveId": "PSYCHIC", "uses": 4605}, {"moveId": "FUTURE_SIGHT", "uses": 3951}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "ICE_PUNCH", "PSYSHOCK"], "score": 29.4}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 236, "matchups": [{"opponent": "relicanth", "rating": 648, "opRating": 351}, {"opponent": "moltres_galarian", "rating": 616, "opRating": 383}, {"opponent": "clefable", "rating": 604, "opRating": 395}, {"opponent": "<PERSON>ras", "rating": 601, "opRating": 398}, {"opponent": "articuno", "rating": 546, "opRating": 453}], "counters": [{"opponent": "mewtwo", "rating": 65}, {"opponent": "dialga", "rating": 67}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 219}, {"opponent": "gyarados", "rating": 280}, {"opponent": "lugia", "rating": 428}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 11849}, {"moveId": "SNARL", "uses": 9620}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4402}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4169}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4089}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3761}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3642}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3613}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3525}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3522}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3281}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3275}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3237}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3007}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2987}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2901}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2875}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2681}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 34303}, {"moveId": "CRUNCH", "uses": 19565}, {"moveId": "PSYCHIC_FANGS", "uses": 17331}, {"moveId": "HYPER_BEAM", "uses": 5296}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 29.4}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 217, "matchups": [{"opponent": "stunfisk", "rating": 651, "opRating": 348}, {"opponent": "lanturn", "rating": 651, "opRating": 348}, {"opponent": "politoed", "rating": 594, "opRating": 405}, {"opponent": "gastrodon", "rating": 574, "opRating": 425}, {"opponent": "seismitoad", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "giratina_origin", "rating": 109}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "zacian_hero", "rating": 135}, {"opponent": "gyarados", "rating": 172}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_GRASS", "uses": 6057}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5712}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 5258}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 5033}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4958}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4781}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4750}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4595}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4558}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4471}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4442}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4420}, {"moveId": "HIDDEN_POWER_BUG", "uses": 4112}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 4102}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3918}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3779}, {"moveId": "ZEN_HEADBUTT", "uses": 1428}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 41150}, {"moveId": "ENERGY_BALL", "uses": 14535}, {"moveId": "SEED_FLARE", "uses": 12212}, {"moveId": "SOLAR_BEAM", "uses": 8441}]}, "moveset": ["ZEN_HEADBUTT", "GRASS_KNOT", "SEED_FLARE"], "score": 29.4}, {"speciesId": "glalie", "speciesName": "G<PERSON><PERSON>", "rating": 203, "matchups": [{"opponent": "altaria", "rating": 604, "opRating": 395}, {"opponent": "stunfisk", "rating": 601, "opRating": 398}, {"opponent": "lilligant", "rating": 593, "opRating": 406}, {"opponent": "run<PERSON><PERSON>", "rating": 566, "opRating": 433}, {"opponent": "crobat", "rating": 517, "opRating": 482}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "giratina_origin", "rating": 121}, {"opponent": "dragonite", "rating": 199}, {"opponent": "garcho<PERSON>", "rating": 227}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 46874}, {"moveId": "FROST_BREATH", "uses": 29626}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 49161}, {"moveId": "SHADOW_BALL", "uses": 20387}, {"moveId": "GYRO_BALL", "uses": 6983}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "SHADOW_BALL"], "score": 29.2}, {"speciesId": "jynx", "speciesName": "Jynx", "rating": 217, "matchups": [{"opponent": "toxicroak", "rating": 758, "opRating": 241}, {"opponent": "hypno_shadow", "rating": 624, "opRating": 375}, {"opponent": "ninetales_alolan", "rating": 557, "opRating": 442}, {"opponent": "stunfisk", "rating": 553, "opRating": 446}, {"opponent": "mantine", "rating": 510, "opRating": 489}], "counters": [{"opponent": "giratina_origin", "rating": 89}, {"opponent": "dialga", "rating": 100}, {"opponent": "garcho<PERSON>", "rating": 105}, {"opponent": "zacian_hero", "rating": 138}, {"opponent": "gyarados", "rating": 154}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 41897}, {"moveId": "FROST_BREATH", "uses": 32893}, {"moveId": "POUND", "uses": 1626}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 30939}, {"moveId": "ICE_PUNCH", "uses": 18637}, {"moveId": "PSYSHOCK", "uses": 13822}, {"moveId": "FOCUS_BLAST", "uses": 8970}, {"moveId": "DRAINING_KISS", "uses": 4108}]}, "moveset": ["CONFUSION", "AVALANCHE", "ICE_PUNCH"], "score": 29.2}, {"speciesId": "pelipper", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 223, "matchups": [{"opponent": "seismitoad", "rating": 669, "opRating": 330}, {"opponent": "forretress_shadow", "rating": 647, "opRating": 352}, {"opponent": "forretress", "rating": 552, "opRating": 447}, {"opponent": "poliwrath", "rating": 549, "opRating": 450}, {"opponent": "golurk", "rating": 542, "opRating": 457}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "garcho<PERSON>", "rating": 77}, {"opponent": "metagross", "rating": 183}, {"opponent": "swampert", "rating": 248}, {"opponent": "grou<PERSON>", "rating": 470}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 38973}, {"moveId": "WATER_GUN", "uses": 37527}], "chargedMoves": [{"moveId": "WEATHER_BALL_WATER", "uses": 40546}, {"moveId": "HURRICANE", "uses": 16157}, {"moveId": "BLIZZARD", "uses": 14581}, {"moveId": "HYDRO_PUMP", "uses": 5376}]}, "moveset": ["WING_ATTACK", "WEATHER_BALL_WATER", "HURRICANE"], "score": 29.2}, {"speciesId": "sigilyph", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 215, "matchups": [{"opponent": "virizion", "rating": 671, "opRating": 328}, {"opponent": "sir<PERSON><PERSON><PERSON>", "rating": 550, "opRating": 450}, {"opponent": "chesnaught", "rating": 537, "opRating": 462}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 537, "opRating": 462}, {"opponent": "gallade", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "grou<PERSON>", "rating": 192}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 58168}, {"moveId": "ZEN_HEADBUTT", "uses": 18332}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 28869}, {"moveId": "SIGNAL_BEAM", "uses": 19473}, {"moveId": "AIR_CUTTER", "uses": 15116}, {"moveId": "PSYBEAM", "uses": 13060}]}, "moveset": ["AIR_SLASH", "ANCIENT_POWER", "SIGNAL_BEAM"], "score": 29.2}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 208, "matchups": [{"opponent": "stunfisk", "rating": 649, "opRating": 350}, {"opponent": "mandibuzz", "rating": 579, "opRating": 420}, {"opponent": "electivire", "rating": 572, "opRating": 427}, {"opponent": "nidoqueen", "rating": 552, "opRating": 447}, {"opponent": "altaria", "rating": 532, "opRating": 467}], "counters": [{"opponent": "giratina_origin", "rating": 97}, {"opponent": "dialga", "rating": 111}, {"opponent": "lugia", "rating": 145}, {"opponent": "garcho<PERSON>", "rating": 171}, {"opponent": "dragonite", "rating": 226}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 45025}, {"moveId": "ICE_SHARD", "uses": 31475}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 45131}, {"moveId": "STONE_EDGE", "uses": 17144}, {"moveId": "BULLDOZE", "uses": 14146}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "AVALANCHE", "STONE_EDGE"], "score": 29}, {"speciesId": "lunatone", "speciesName": "Lunatone", "rating": 226, "matchups": [{"opponent": "ninetales", "rating": 690, "opRating": 309}, {"opponent": "emboar", "rating": 561, "opRating": 438}, {"opponent": "blissey", "rating": 559, "opRating": 440}, {"opponent": "ho_oh", "rating": 548}, {"opponent": "torn<PERSON><PERSON>_therian", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "garcho<PERSON>", "rating": 84}, {"opponent": "gyarados", "rating": 126}, {"opponent": "dragonite", "rating": 127}, {"opponent": "zacian_hero", "rating": 190}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40050}, {"moveId": "ROCK_THROW", "uses": 36450}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 36348}, {"moveId": "MOONBLAST", "uses": 20445}, {"moveId": "PSYCHIC", "uses": 19662}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "MOONBLAST"], "score": 28.8}, {"speciesId": "sawk", "speciesName": "Sawk", "rating": 254, "matchups": [{"opponent": "mandibuzz", "rating": 567, "opRating": 432}, {"opponent": "forretress", "rating": 548, "opRating": 451}, {"opponent": "bisharp", "rating": 524, "opRating": 475}, {"opponent": "obstagoon", "rating": 515, "opRating": 484}, {"opponent": "s<PERSON><PERSON>", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 78}, {"opponent": "dragonite", "rating": 114}, {"opponent": "zacian_hero", "rating": 164}, {"opponent": "gyarados", "rating": 311}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 57679}, {"moveId": "LOW_KICK", "uses": 18821}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41156}, {"moveId": "FOCUS_BLAST", "uses": 18619}, {"moveId": "LOW_SWEEP", "uses": 16738}]}, "moveset": ["POISON_JAB", "BODY_SLAM", "FOCUS_BLAST"], "score": 28.8}, {"speciesId": "solrock", "speciesName": "Solrock", "rating": 227, "matchups": [{"opponent": "ninetales", "rating": 690, "opRating": 309}, {"opponent": "emboar", "rating": 561, "opRating": 438}, {"opponent": "ho_oh", "rating": 548}, {"opponent": "tentacruel", "rating": 548, "opRating": 451}, {"opponent": "torn<PERSON><PERSON>_therian", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 78}, {"opponent": "garcho<PERSON>", "rating": 84}, {"opponent": "gyarados", "rating": 126}, {"opponent": "dragonite", "rating": 127}, {"opponent": "zacian_hero", "rating": 190}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40063}, {"moveId": "ROCK_THROW", "uses": 36437}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 42232}, {"moveId": "PSYCHIC", "uses": 22923}, {"moveId": "SOLAR_BEAM", "uses": 11362}]}, "moveset": ["CONFUSION", "ROCK_SLIDE", "PSYCHIC"], "score": 28.8}, {"speciesId": "gran<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 246, "matchups": [{"opponent": "d<PERSON><PERSON><PERSON>", "rating": 620, "opRating": 379}, {"opponent": "steelix", "rating": 610, "opRating": 389}, {"opponent": "aggron", "rating": 561, "opRating": 438}, {"opponent": "aurorus", "rating": 532, "opRating": 467}, {"opponent": "s<PERSON><PERSON>", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "garcho<PERSON>", "rating": 68}, {"opponent": "lugia", "rating": 190}, {"opponent": "gyarados", "rating": 219}, {"opponent": "dragonite", "rating": 228}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 35614}, {"moveId": "CHARM", "uses": 26879}, {"moveId": "BITE", "uses": 13940}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 30806}, {"moveId": "CRUNCH", "uses": 22901}, {"moveId": "PLAY_ROUGH", "uses": 14508}, {"moveId": "RETURN", "uses": 8452}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 28.6}, {"speciesId": "kabutops", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 250, "matchups": [{"opponent": "entei_shadow", "rating": 746, "opRating": 253}, {"opponent": "ho_oh", "rating": 714}, {"opponent": "articuno", "rating": 714, "opRating": 285}, {"opponent": "moltres", "rating": 714, "opRating": 285}, {"opponent": "entei", "rating": 647, "opRating": 352}], "counters": [{"opponent": "mewtwo", "rating": 65}, {"opponent": "metagross", "rating": 84}, {"opponent": "dialga", "rating": 89}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 247}, {"opponent": "lugia", "rating": 250}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 25475}, {"moveId": "FURY_CUTTER", "uses": 23031}, {"moveId": "WATERFALL", "uses": 20317}, {"moveId": "ROCK_SMASH", "uses": 7616}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 32980}, {"moveId": "ANCIENT_POWER", "uses": 29720}, {"moveId": "WATER_PULSE", "uses": 13790}]}, "moveset": ["MUD_SHOT", "STONE_EDGE", "ANCIENT_POWER"], "score": 28.6}, {"speciesId": "slurpuff", "speciesName": "Slurpuff", "rating": 204, "matchups": [{"opponent": "s<PERSON><PERSON>", "rating": 672, "opRating": 327}, {"opponent": "kommo_o", "rating": 658, "opRating": 341}, {"opponent": "haxorus", "rating": 557, "opRating": 442}, {"opponent": "moltres_galarian", "rating": 522, "opRating": 477}, {"opponent": "goodra", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 80}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "gyarados", "rating": 283}, {"opponent": "dragonite", "rating": 462}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 32316}, {"moveId": "CHARM", "uses": 25852}, {"moveId": "TACKLE", "uses": 18311}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22381}, {"moveId": "PLAY_ROUGH", "uses": 21986}, {"moveId": "ENERGY_BALL", "uses": 18115}, {"moveId": "DRAINING_KISS", "uses": 13908}]}, "moveset": ["FAIRY_WIND", "FLAMETHROWER", "PLAY_ROUGH"], "score": 28.6}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 220, "matchups": [{"opponent": "obstagoon", "rating": 646, "opRating": 353}, {"opponent": "chesnaught", "rating": 576, "opRating": 423}, {"opponent": "celebi", "rating": 566, "opRating": 433}, {"opponent": "pinsir", "rating": 522, "opRating": 477}, {"opponent": "virizion", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "metagross", "rating": 95}, {"opponent": "garcho<PERSON>", "rating": 194}, {"opponent": "mewtwo", "rating": 273}, {"opponent": "swampert", "rating": 296}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 22361}, {"moveId": "BUG_BITE", "uses": 19362}, {"moveId": "POISON_STING", "uses": 17676}, {"moveId": "AIR_SLASH", "uses": 17138}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 29859}, {"moveId": "BUG_BUZZ", "uses": 19501}, {"moveId": "POWER_GEM", "uses": 14036}, {"moveId": "SIGNAL_BEAM", "uses": 8403}, {"moveId": "FELL_STINGER", "uses": 4900}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 28.6}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 180, "matchups": [{"opponent": "gourgeist_large", "rating": 616, "opRating": 383}, {"opponent": "gourgeist_super", "rating": 607, "opRating": 392}, {"opponent": "politoed", "rating": 563, "opRating": 436}, {"opponent": "trevenant", "rating": 546, "opRating": 453}, {"opponent": "seismitoad", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "gyarados", "rating": 128}, {"opponent": "metagross", "rating": 139}, {"opponent": "mewtwo", "rating": 145}, {"opponent": "giratina_origin", "rating": 376}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 59971}, {"moveId": "TAKE_DOWN", "uses": 16529}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35231}, {"moveId": "MEGAHORN", "uses": 19472}, {"moveId": "SOLAR_BEAM", "uses": 11482}, {"moveId": "HYPER_BEAM", "uses": 10237}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "MEGAHORN"], "score": 28.4}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 169, "matchups": [{"opponent": "golurk", "rating": 661, "opRating": 338}, {"opponent": "gourgeist_super", "rating": 644, "opRating": 355}, {"opponent": "trevenant", "rating": 595, "opRating": 404}, {"opponent": "forretress", "rating": 556, "opRating": 443}, {"opponent": "ferrothorn", "rating": 549, "opRating": 450}], "counters": [{"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "lugia", "rating": 107}, {"opponent": "dialga", "rating": 111}, {"opponent": "giratina_origin", "rating": 490}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 48011}, {"moveId": "STEEL_WING", "uses": 28489}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 34430}, {"moveId": "DRILL_PECK", "uses": 31296}, {"moveId": "AERIAL_ACE", "uses": 6889}, {"moveId": "AIR_CUTTER", "uses": 3825}]}, "moveset": ["FEINT_ATTACK", "BRAVE_BIRD", "DRILL_PECK"], "score": 28.2}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 204, "matchups": [{"opponent": "politoed", "rating": 740, "opRating": 259}, {"opponent": "empoleon", "rating": 661, "opRating": 338}, {"opponent": "feraligatr", "rating": 657, "opRating": 342}, {"opponent": "feraligatr_shadow", "rating": 625, "opRating": 374}, {"opponent": "gyarados", "rating": 507}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "mewtwo", "rating": 65}, {"opponent": "zacian_hero", "rating": 106}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 205}, {"opponent": "lugia", "rating": 276}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 55530}, {"moveId": "ASTONISH", "uses": 20970}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34978}, {"moveId": "OMINOUS_WIND", "uses": 26211}, {"moveId": "THUNDER", "uses": 15165}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 28.2}, {"speciesId": "gran<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 210, "matchups": [{"opponent": "obstagoon", "rating": 779, "opRating": 220}, {"opponent": "steelix", "rating": 612, "opRating": 387}, {"opponent": "bisharp", "rating": 588, "opRating": 411}, {"opponent": "d<PERSON><PERSON><PERSON>", "rating": 553, "opRating": 446}, {"opponent": "king<PERSON>", "rating": 545, "opRating": 454}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "mewtwo", "rating": 62}, {"opponent": "lugia", "rating": 233}, {"opponent": "gyarados", "rating": 265}, {"opponent": "dragonite", "rating": 265}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37464}, {"moveId": "CHARM", "uses": 25832}, {"moveId": "BITE", "uses": 13233}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 34084}, {"moveId": "CRUNCH", "uses": 25734}, {"moveId": "PLAY_ROUGH", "uses": 16577}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "CLOSE_COMBAT", "CRUNCH"], "score": 27.8}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 200, "matchups": [{"opponent": "relicanth", "rating": 741, "opRating": 258}, {"opponent": "seismitoad", "rating": 639, "opRating": 360}, {"opponent": "rhydon", "rating": 514, "opRating": 485}, {"opponent": "swampert", "rating": 511}, {"opponent": "empoleon", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 59}, {"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "mewtwo", "rating": 111}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "metagross", "rating": 148}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 48280}, {"moveId": "RAZOR_LEAF", "uses": 28220}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 21126}, {"moveId": "ENERGY_BALL", "uses": 16153}, {"moveId": "LEAF_STORM", "uses": 14059}, {"moveId": "HYDRO_PUMP", "uses": 13102}, {"moveId": "BLIZZARD", "uses": 7320}, {"moveId": "SOLAR_BEAM", "uses": 4631}]}, "moveset": ["BUBBLE", "ICE_BEAM", "ENERGY_BALL"], "score": 27.8}, {"speciesId": "sudowoodo", "speciesName": "Sudowoodo", "rating": 232, "matchups": [{"opponent": "moltres", "rating": 636, "opRating": 363}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 630, "opRating": 369}, {"opponent": "arm<PERSON>", "rating": 595, "opRating": 404}, {"opponent": "pyroar", "rating": 585, "opRating": 414}, {"opponent": "cryogonal", "rating": 557, "opRating": 442}], "counters": [{"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "metagross", "rating": 84}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "dialga", "rating": 154}, {"opponent": "ho_oh", "rating": 388}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44381}, {"moveId": "ROCK_THROW", "uses": 32119}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 30323}, {"moveId": "EARTHQUAKE", "uses": 18329}, {"moveId": "STONE_EDGE", "uses": 12099}, {"moveId": "RETURN", "uses": 9236}, {"moveId": "ROCK_TOMB", "uses": 6528}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 27.8}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 200, "matchups": [{"opponent": "serperior", "rating": 626, "opRating": 373}, {"opponent": "carracosta", "rating": 615, "opRating": 384}, {"opponent": "relicanth", "rating": 610, "opRating": 389}, {"opponent": "politoed", "rating": 538, "opRating": 461}, {"opponent": "vileplume", "rating": 529, "opRating": 470}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "lugia", "rating": 116}, {"opponent": "garcho<PERSON>", "rating": 124}, {"opponent": "gyarados", "rating": 146}, {"opponent": "giratina_origin", "rating": 157}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52378}, {"moveId": "ASTONISH", "uses": 24122}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 24231}, {"moveId": "GRASS_KNOT", "uses": 23828}, {"moveId": "SLUDGE_BOMB", "uses": 19289}, {"moveId": "RETURN", "uses": 9139}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 27.6}, {"speciesId": "oricorio_sensu", "speciesName": "Oricorio (Sensu)", "rating": 215, "matchups": [{"opponent": "pinsir", "rating": 710, "opRating": 289}, {"opponent": "buzzwole", "rating": 685, "opRating": 314}, {"opponent": "virizion", "rating": 670, "opRating": 329}, {"opponent": "escavalier", "rating": 579, "opRating": 420}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 560, "opRating": 439}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "lugia", "rating": 97}, {"opponent": "zacian_hero", "rating": 158}, {"opponent": "grou<PERSON>", "rating": 192}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66412}, {"moveId": "POUND", "uses": 10088}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35279}, {"moveId": "HURRICANE", "uses": 31986}, {"moveId": "AIR_CUTTER", "uses": 9256}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 27.6}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 230, "matchups": [{"opponent": "relicanth", "rating": 731, "opRating": 268}, {"opponent": "swampert_shadow", "rating": 631, "opRating": 368}, {"opponent": "swampert", "rating": 628}, {"opponent": "vaporeon", "rating": 588, "opRating": 411}, {"opponent": "tapu_fini", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 29}, {"opponent": "mewtwo", "rating": 54}, {"opponent": "garcho<PERSON>", "rating": 61}, {"opponent": "gyarados", "rating": 247}, {"opponent": "excadrill", "rating": 281}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 39826}, {"moveId": "RAZOR_LEAF", "uses": 21995}, {"moveId": "ACID", "uses": 14679}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 57456}, {"moveId": "DAZZLING_GLEAM", "uses": 13129}, {"moveId": "PETAL_BLIZZARD", "uses": 5951}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 27.4}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 243, "matchups": [{"opponent": "relicanth", "rating": 729, "opRating": 270}, {"opponent": "chesnaught", "rating": 695, "opRating": 304}, {"opponent": "swampert_shadow", "rating": 618, "opRating": 381}, {"opponent": "swampert", "rating": 592}, {"opponent": "regice", "rating": 574, "opRating": 425}], "counters": [{"opponent": "dialga", "rating": 43}, {"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "mewtwo", "rating": 65}, {"opponent": "grou<PERSON>", "rating": 133}, {"opponent": "kyogre", "rating": 407}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 27019}, {"moveId": "CONFUSION", "uses": 24960}, {"moveId": "EXTRASENSORY", "uses": 19422}, {"moveId": "ZEN_HEADBUTT", "uses": 5244}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 31773}, {"moveId": "PSYCHIC", "uses": 30005}, {"moveId": "SOLAR_BEAM", "uses": 14537}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 27.4}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 214, "matchups": [{"opponent": "gourgeist_super", "rating": 696, "opRating": 303}, {"opponent": "decid<PERSON><PERSON>", "rating": 676, "opRating": 323}, {"opponent": "gliscor_shadow", "rating": 589, "opRating": 410}, {"opponent": "celebi", "rating": 557, "opRating": 442}, {"opponent": "hoopa_unbound", "rating": 522, "opRating": 477}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "lugia", "rating": 85}, {"opponent": "gyarados", "rating": 95}, {"opponent": "mewtwo", "rating": 190}, {"opponent": "giratina_origin", "rating": 384}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 57057}, {"moveId": "PECK", "uses": 19443}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 24818}, {"moveId": "DARK_PULSE", "uses": 19091}, {"moveId": "SKY_ATTACK", "uses": 18709}, {"moveId": "PSYCHIC", "uses": 8077}, {"moveId": "RETURN", "uses": 5796}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 27.4}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 232, "matchups": [{"opponent": "moltres", "rating": 657, "opRating": 342}, {"opponent": "feraligatr", "rating": 641, "opRating": 358}, {"opponent": "empoleon", "rating": 637, "opRating": 362}, {"opponent": "heatran", "rating": 606, "opRating": 393}, {"opponent": "articuno", "rating": 555, "opRating": 444}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "zacian_hero", "rating": 106}, {"opponent": "gyarados", "rating": 237}, {"opponent": "lugia", "rating": 276}, {"opponent": "ho_oh", "rating": 395}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 57373}, {"moveId": "ASTONISH", "uses": 19127}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 34707}, {"moveId": "HYDRO_PUMP", "uses": 26675}, {"moveId": "THUNDER", "uses": 15110}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 27.4}, {"speciesId": "zebstrika", "speciesName": "Zebstrika", "rating": 223, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 756, "opRating": 243}, {"opponent": "relicanth", "rating": 640, "opRating": 359}, {"opponent": "moltres_galarian", "rating": 606, "opRating": 393}, {"opponent": "<PERSON>ras", "rating": 594, "opRating": 405}, {"opponent": "aromatisse", "rating": 560, "opRating": 439}], "counters": [{"opponent": "mewtwo", "rating": 52}, {"opponent": "grou<PERSON>", "rating": 146}, {"opponent": "dialga", "rating": 163}, {"opponent": "lugia", "rating": 188}, {"opponent": "gyarados", "rating": 208}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 62716}, {"moveId": "LOW_KICK", "uses": 13784}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 45073}, {"moveId": "FLAME_CHARGE", "uses": 21811}, {"moveId": "DISCHARGE", "uses": 9669}]}, "moveset": ["SPARK", "WILD_CHARGE", "FLAME_CHARGE"], "score": 27.4}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 201, "matchups": [{"opponent": "toxicroak", "rating": 714, "opRating": 285}, {"opponent": "klinklang", "rating": 598, "opRating": 401}, {"opponent": "probopass", "rating": 580, "opRating": 419}, {"opponent": "omastar", "rating": 559, "opRating": 440}, {"opponent": "golem_alolan", "rating": 549, "opRating": 450}], "counters": [{"opponent": "dialga", "rating": 81}, {"opponent": "gyarados", "rating": 131}, {"opponent": "garcho<PERSON>", "rating": 143}, {"opponent": "zacian_hero", "rating": 153}, {"opponent": "dragonite", "rating": 162}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 28265}, {"moveId": "MUD_SLAP", "uses": 25161}, {"moveId": "EXTRASENSORY", "uses": 23080}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 16785}, {"moveId": "ICE_BEAM", "uses": 14074}, {"moveId": "SHADOW_BALL", "uses": 13681}, {"moveId": "PSYCHIC", "uses": 11893}, {"moveId": "ROCK_TOMB", "uses": 7902}, {"moveId": "EARTHQUAKE", "uses": 7233}, {"moveId": "GYRO_BALL", "uses": 4906}]}, "moveset": ["CONFUSION", "EARTH_POWER", "ICE_BEAM"], "score": 27.1}, {"speciesId": "r<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON> (Alolan)", "rating": 211, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 732, "opRating": 267}, {"opponent": "relicanth", "rating": 602, "opRating": 397}, {"opponent": "carracosta", "rating": 577, "opRating": 422}, {"opponent": "klinklang", "rating": 549, "opRating": 450}, {"opponent": "poliwrath", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 88}, {"opponent": "gyarados", "rating": 203}, {"opponent": "lugia", "rating": 342}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 379}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 29154}, {"moveId": "THUNDER_SHOCK", "uses": 25701}, {"moveId": "SPARK", "uses": 21536}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 32412}, {"moveId": "THUNDER_PUNCH", "uses": 15861}, {"moveId": "PSYCHIC", "uses": 14306}, {"moveId": "GRASS_KNOT", "uses": 13872}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "THUNDER_PUNCH"], "score": 27.1}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 168, "matchups": [{"opponent": "<PERSON>rserker", "rating": 688, "opRating": 311}, {"opponent": "gourgeist_large", "rating": 592, "opRating": 407}, {"opponent": "gourgeist_super", "rating": 589, "opRating": 410}, {"opponent": "seismitoad", "rating": 581, "opRating": 418}, {"opponent": "golurk", "rating": 528, "opRating": 471}], "counters": [{"opponent": "mewtwo", "rating": 78}, {"opponent": "garcho<PERSON>", "rating": 86}, {"opponent": "dialga", "rating": 89}, {"opponent": "gyarados", "rating": 110}, {"opponent": "giratina_origin", "rating": 384}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31944}, {"moveId": "GUST", "uses": 22800}, {"moveId": "WING_ATTACK", "uses": 21730}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 39300}, {"moveId": "CLOSE_COMBAT", "uses": 32707}, {"moveId": "HEAT_WAVE", "uses": 4415}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 27.1}, {"speciesId": "sudowoodo_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 209, "matchups": [{"opponent": "skar<PERSON><PERSON>_shadow", "rating": 684, "opRating": 315}, {"opponent": "greedent", "rating": 646, "opRating": 353}, {"opponent": "umbreon", "rating": 560, "opRating": 439}, {"opponent": "bisharp", "rating": 557, "opRating": 442}, {"opponent": "pyroar", "rating": 503, "opRating": 496}], "counters": [{"opponent": "mewtwo", "rating": 52}, {"opponent": "garcho<PERSON>", "rating": 98}, {"opponent": "metagross", "rating": 104}, {"opponent": "excadrill", "rating": 151}, {"opponent": "dialga", "rating": 198}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 44996}, {"moveId": "ROCK_THROW", "uses": 31504}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 34390}, {"moveId": "EARTHQUAKE", "uses": 20993}, {"moveId": "STONE_EDGE", "uses": 13665}, {"moveId": "ROCK_TOMB", "uses": 7452}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "ROCK_SLIDE", "EARTHQUAKE"], "score": 27.1}, {"speciesId": "throh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 216, "matchups": [{"opponent": "bisharp", "rating": 606, "opRating": 393}, {"opponent": "forretress", "rating": 577, "opRating": 422}, {"opponent": "s<PERSON><PERSON>", "rating": 541, "opRating": 458}, {"opponent": "obstagoon", "rating": 530, "opRating": 469}, {"opponent": "registeel", "rating": 515, "opRating": 484}], "counters": [{"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "metagross", "rating": 125}, {"opponent": "dialga", "rating": 152}, {"opponent": "excadrill", "rating": 165}, {"opponent": "gyarados", "rating": 175}], "moves": {"fastMoves": [{"moveId": "LOW_KICK", "uses": 49150}, {"moveId": "ZEN_HEADBUTT", "uses": 27350}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 41224}, {"moveId": "FOCUS_BLAST", "uses": 18506}, {"moveId": "LOW_SWEEP", "uses": 16774}]}, "moveset": ["LOW_KICK", "BODY_SLAM", "FOCUS_BLAST"], "score": 27.1}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 210, "matchups": [{"opponent": "gourgeist_super", "rating": 720, "opRating": 279}, {"opponent": "golurk", "rating": 720, "opRating": 279}, {"opponent": "trevenant", "rating": 659, "opRating": 340}, {"opponent": "seismitoad", "rating": 607, "opRating": 392}, {"opponent": "golisopod", "rating": 566, "opRating": 433}], "counters": [{"opponent": "lugia", "rating": 126}, {"opponent": "swampert", "rating": 166}, {"opponent": "garcho<PERSON>", "rating": 204}, {"opponent": "grou<PERSON>", "rating": 285}, {"opponent": "giratina_origin", "rating": 318}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 37633}, {"moveId": "PECK", "uses": 24382}, {"moveId": "ROCK_SMASH", "uses": 14473}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 44797}, {"moveId": "ROCK_BLAST", "uses": 22143}, {"moveId": "FLASH_CANNON", "uses": 9598}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 27.1}, {"speciesId": "lair<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 209, "matchups": [{"opponent": "ninetales_alolan", "rating": 665, "opRating": 334}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 647, "opRating": 352}, {"opponent": "crobat", "rating": 545, "opRating": 454}, {"opponent": "articuno_galarian", "rating": 510, "opRating": 489}, {"opponent": "crustle", "rating": 507, "opRating": 492}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "zacian_hero", "rating": 104}, {"opponent": "mewtwo", "rating": 119}, {"opponent": "lugia", "rating": 266}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 474}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 54581}, {"moveId": "IRON_TAIL", "uses": 21919}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 28340}, {"moveId": "BODY_SLAM", "uses": 27334}, {"moveId": "HEAVY_SLAM", "uses": 14737}, {"moveId": "ROCK_TOMB", "uses": 6063}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 26.9}, {"speciesId": "meowstic_female", "speciesName": "<PERSON><PERSON><PERSON> (Female)", "rating": 203, "matchups": [{"opponent": "hitmon<PERSON>_shadow", "rating": 644, "opRating": 355}, {"opponent": "hitmontop", "rating": 644, "opRating": 355}, {"opponent": "toxicroak", "rating": 638, "opRating": 361}, {"opponent": "hitmonchan", "rating": 628, "opRating": 371}, {"opponent": "hypno_shadow", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "garcho<PERSON>", "rating": 103}, {"opponent": "gyarados", "rating": 123}, {"opponent": "dragonite", "rating": 148}, {"opponent": "zacian_hero", "rating": 158}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 44378}, {"moveId": "CHARM", "uses": 32122}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 31198}, {"moveId": "PSYCHIC", "uses": 26605}, {"moveId": "ENERGY_BALL", "uses": 18701}]}, "moveset": ["CONFUSION", "SHADOW_BALL", "PSYCHIC"], "score": 26.9}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 215, "matchups": [{"opponent": "ferrothorn", "rating": 692, "opRating": 307}, {"opponent": "virizion", "rating": 603, "opRating": 396}, {"opponent": "poliwrath", "rating": 603, "opRating": 396}, {"opponent": "chesnaught", "rating": 585, "opRating": 414}, {"opponent": "pinsir", "rating": 512, "opRating": 487}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "giratina_origin", "rating": 79}, {"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "lugia", "rating": 97}, {"opponent": "zacian_hero", "rating": 164}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66458}, {"moveId": "POUND", "uses": 10042}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35278}, {"moveId": "HURRICANE", "uses": 31982}, {"moveId": "AIR_CUTTER", "uses": 9254}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 26.9}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 214, "matchups": [{"opponent": "ferrothorn", "rating": 662, "opRating": 337}, {"opponent": "sandslash_alolan", "rating": 576, "opRating": 423}, {"opponent": "steelix", "rating": 570, "opRating": 429}, {"opponent": "tapu_bulu", "rating": 550, "opRating": 449}, {"opponent": "aromatisse", "rating": 525, "opRating": 474}], "counters": [{"opponent": "lugia", "rating": 73}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "zacian_hero", "rating": 115}, {"opponent": "dialga", "rating": 116}, {"opponent": "metagross", "rating": 142}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38782}, {"moveId": "EMBER", "uses": 37718}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 34868}, {"moveId": "EARTHQUAKE", "uses": 26338}, {"moveId": "SOLAR_BEAM", "uses": 15315}]}, "moveset": ["FIRE_SPIN", "OVERHEAT", "EARTHQUAKE"], "score": 26.9}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 226, "matchups": [{"opponent": "vileplume_shadow", "rating": 671, "opRating": 328}, {"opponent": "ferrothorn", "rating": 626, "opRating": 373}, {"opponent": "sandslash_alolan", "rating": 595, "opRating": 404}, {"opponent": "s<PERSON><PERSON>", "rating": 550, "opRating": 449}, {"opponent": "steelix", "rating": 516, "opRating": 483}], "counters": [{"opponent": "mewtwo", "rating": 62}, {"opponent": "dialga", "rating": 97}, {"opponent": "lugia", "rating": 97}, {"opponent": "zacian_hero", "rating": 104}, {"opponent": "metagross", "rating": 194}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 41617}, {"moveId": "LICK", "uses": 34883}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 33509}, {"moveId": "THUNDER_PUNCH", "uses": 29704}, {"moveId": "POWER_UP_PUNCH", "uses": 13269}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "THUNDER_PUNCH"], "score": 26.7}, {"speciesId": "me<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON> (Male)", "rating": 201, "matchups": [{"opponent": "hitmon<PERSON>_shadow", "rating": 644, "opRating": 355}, {"opponent": "hitmontop", "rating": 644, "opRating": 355}, {"opponent": "toxicroak", "rating": 638, "opRating": 361}, {"opponent": "hitmonchan", "rating": 628, "opRating": 371}, {"opponent": "throh", "rating": 582, "opRating": 417}], "counters": [{"opponent": "dialga", "rating": 76}, {"opponent": "garcho<PERSON>", "rating": 103}, {"opponent": "gyarados", "rating": 123}, {"opponent": "dragonite", "rating": 148}, {"opponent": "zacian_hero", "rating": 158}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 40923}, {"moveId": "SUCKER_PUNCH", "uses": 35577}], "chargedMoves": [{"moveId": "PSYCHIC", "uses": 30613}, {"moveId": "THUNDERBOLT", "uses": 24482}, {"moveId": "ENERGY_BALL", "uses": 21405}]}, "moveset": ["CONFUSION", "PSYCHIC", "THUNDERBOLT"], "score": 26.7}, {"speciesId": "oricorio_pom_pom", "speciesName": "Oricorio (Pom-Pom)", "rating": 213, "matchups": [{"opponent": "leavanny", "rating": 637, "opRating": 362}, {"opponent": "virizion", "rating": 560, "opRating": 439}, {"opponent": "ferrothorn", "rating": 548, "opRating": 451}, {"opponent": "forretress", "rating": 539, "opRating": 460}, {"opponent": "meganium", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "giratina_origin", "rating": 79}, {"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "lugia", "rating": 97}, {"opponent": "gyarados", "rating": 126}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66276}, {"moveId": "POUND", "uses": 10224}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35278}, {"moveId": "HURRICANE", "uses": 31966}, {"moveId": "AIR_CUTTER", "uses": 9261}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 26.7}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 208, "matchups": [{"opponent": "deoxys_defense", "rating": 619, "opRating": 380}, {"opponent": "pinsir_shadow", "rating": 577, "opRating": 422}, {"opponent": "decid<PERSON><PERSON>", "rating": 556, "opRating": 443}, {"opponent": "pinsir", "rating": 514, "opRating": 485}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "giratina_origin", "rating": 89}, {"opponent": "lugia", "rating": 116}, {"opponent": "mewtwo", "rating": 117}, {"opponent": "zacian_hero", "rating": 219}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43311}, {"moveId": "SUCKER_PUNCH", "uses": 33189}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 32473}, {"moveId": "DARK_PULSE", "uses": 21020}, {"moveId": "DAZZLING_GLEAM", "uses": 11573}, {"moveId": "RETURN", "uses": 11308}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 26.3}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 249, "matchups": [{"opponent": "relicanth", "rating": 629, "opRating": 370}, {"opponent": "clefable", "rating": 620, "opRating": 379}, {"opponent": "golisopod", "rating": 603, "opRating": 396}, {"opponent": "milotic", "rating": 539, "opRating": 460}, {"opponent": "primarina", "rating": 519, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "mewtwo", "rating": 96}, {"opponent": "lugia", "rating": 159}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 224}, {"opponent": "giratina_origin", "rating": 252}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9466}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4982}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4447}, {"moveId": "CHARGE_BEAM", "uses": 4428}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4269}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4206}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4202}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4030}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3965}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3895}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3841}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3818}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3788}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3535}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3533}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3473}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3334}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3212}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 30736}, {"moveId": "ZAP_CANNON", "uses": 18350}, {"moveId": "HYPER_BEAM", "uses": 14986}, {"moveId": "SOLAR_BEAM", "uses": 12289}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 26.3}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 234, "matchups": [{"opponent": "relicanth", "rating": 677, "opRating": 322}, {"opponent": "carracosta", "rating": 650, "opRating": 350}, {"opponent": "seismitoad", "rating": 633, "opRating": 366}, {"opponent": "mandibuzz", "rating": 583, "opRating": 416}, {"opponent": "rhydon", "rating": 530, "opRating": 469}], "counters": [{"opponent": "dialga", "rating": 29}, {"opponent": "mewtwo", "rating": 75}, {"opponent": "swampert", "rating": 181}, {"opponent": "garcho<PERSON>", "rating": 211}, {"opponent": "zacian_hero", "rating": 277}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36181}, {"moveId": "INFESTATION", "uses": 28994}, {"moveId": "ACID", "uses": 11311}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 29597}, {"moveId": "GRASS_KNOT", "uses": 25430}, {"moveId": "BULLDOZE", "uses": 12143}, {"moveId": "RETURN", "uses": 9385}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 26.1}, {"speciesId": "kingler", "speciesName": "<PERSON><PERSON>", "rating": 249, "matchups": [{"opponent": "heatran", "rating": 648, "opRating": 351}, {"opponent": "nidoqueen", "rating": 640, "opRating": 359}, {"opponent": "entei", "rating": 611, "opRating": 388}, {"opponent": "incineroar", "rating": 600, "opRating": 400}, {"opponent": "rhydon", "rating": 596, "opRating": 403}], "counters": [{"opponent": "mewtwo", "rating": 49}, {"opponent": "lugia", "rating": 83}, {"opponent": "dialga", "rating": 89}, {"opponent": "metagross", "rating": 107}, {"opponent": "excadrill", "rating": 204}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32094}, {"moveId": "BUBBLE", "uses": 28468}, {"moveId": "METAL_CLAW", "uses": 15900}], "chargedMoves": [{"moveId": "CRABHAMMER", "uses": 35530}, {"moveId": "X_SCISSOR", "uses": 24803}, {"moveId": "VICE_GRIP", "uses": 11029}, {"moveId": "WATER_PULSE", "uses": 5144}]}, "moveset": ["MUD_SHOT", "CRABHAMMER", "X_SCISSOR"], "score": 25.9}, {"speciesId": "<PERSON>on", "speciesName": "<PERSON><PERSON>", "rating": 212, "matchups": [{"opponent": "ninetales_alolan", "rating": 693, "opRating": 306}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 669, "opRating": 330}, {"opponent": "crustle", "rating": 654, "opRating": 345}, {"opponent": "mr_rime", "rating": 556, "opRating": 443}, {"opponent": "aromatisse", "rating": 521, "opRating": 478}], "counters": [{"opponent": "dialga", "rating": 62}, {"opponent": "giratina_origin", "rating": 73}, {"opponent": "mewtwo", "rating": 106}, {"opponent": "lugia", "rating": 271}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 283}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 53879}, {"moveId": "IRON_TAIL", "uses": 22621}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 27007}, {"moveId": "BODY_SLAM", "uses": 25840}, {"moveId": "HEAVY_SLAM", "uses": 14064}, {"moveId": "ROCK_TOMB", "uses": 5907}, {"moveId": "RETURN", "uses": 3729}]}, "moveset": ["METAL_CLAW", "ROCK_SLIDE", "BODY_SLAM"], "score": 25.9}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 215, "matchups": [{"opponent": "hitmonchan", "rating": 633, "opRating": 366}, {"opponent": "deoxys_defense", "rating": 625, "opRating": 375}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 612, "opRating": 387}, {"opponent": "falinks", "rating": 608, "opRating": 391}, {"opponent": "forretress", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "garcho<PERSON>", "rating": 84}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "metagross", "rating": 125}, {"opponent": "zacian_hero", "rating": 182}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55498}, {"moveId": "ASTONISH", "uses": 21002}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 25273}, {"moveId": "DARK_PULSE", "uses": 16503}, {"moveId": "OMINOUS_WIND", "uses": 12970}, {"moveId": "PSYCHIC", "uses": 12568}, {"moveId": "RETURN", "uses": 9112}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 25.7}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 214, "matchups": [{"opponent": "scrafty", "rating": 594, "opRating": 405}, {"opponent": "s<PERSON><PERSON>", "rating": 582, "opRating": 417}, {"opponent": "<PERSON>ras", "rating": 582, "opRating": 417}, {"opponent": "steelix", "rating": 578, "opRating": 421}, {"opponent": "obstagoon", "rating": 511, "opRating": 488}], "counters": [{"opponent": "garcho<PERSON>", "rating": 77}, {"opponent": "gyarados", "rating": 79}, {"opponent": "metagross", "rating": 104}, {"opponent": "dialga", "rating": 152}, {"opponent": "excadrill", "rating": 239}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 57199}, {"moveId": "ROCK_SMASH", "uses": 11519}, {"moveId": "LOW_KICK", "uses": 7783}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28856}, {"moveId": "STONE_EDGE", "uses": 15000}, {"moveId": "BRICK_BREAK", "uses": 12762}, {"moveId": "STOMP", "uses": 10194}, {"moveId": "RETURN", "uses": 6018}, {"moveId": "LOW_SWEEP", "uses": 3744}]}, "moveset": ["DOUBLE_KICK", "CLOSE_COMBAT", "STONE_EDGE"], "score": 25.7}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 173, "matchups": [{"opponent": "<PERSON>rserker", "rating": 721, "opRating": 278}, {"opponent": "gourgeist_large", "rating": 646, "opRating": 353}, {"opponent": "gourgeist_super", "rating": 640, "opRating": 359}, {"opponent": "golurk", "rating": 606, "opRating": 393}, {"opponent": "trevenant", "rating": 558, "opRating": 441}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "zacian_hero", "rating": 83}, {"opponent": "mewtwo", "rating": 93}, {"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "giratina_origin", "rating": 306}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30985}, {"moveId": "GUST", "uses": 23457}, {"moveId": "WING_ATTACK", "uses": 21969}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 33307}, {"moveId": "CLOSE_COMBAT", "uses": 28804}, {"moveId": "RETURN", "uses": 10487}, {"moveId": "HEAT_WAVE", "uses": 3916}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "CLOSE_COMBAT"], "score": 25.7}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 184, "matchups": [{"opponent": "lanturn", "rating": 717, "opRating": 282}, {"opponent": "politoed", "rating": 601, "opRating": 398}, {"opponent": "shaymin_sky", "rating": 529, "opRating": 470}, {"opponent": "serperior", "rating": 522, "opRating": 477}, {"opponent": "stunfisk", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "mewtwo", "rating": 91}, {"opponent": "gyarados", "rating": 115}, {"opponent": "giratina_origin", "rating": 127}, {"opponent": "metagross", "rating": 139}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 52205}, {"moveId": "ASTONISH", "uses": 24295}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 27409}, {"moveId": "GRASS_KNOT", "uses": 26756}, {"moveId": "SLUDGE_BOMB", "uses": 22290}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "FOUL_PLAY", "GRASS_KNOT"], "score": 25.5}, {"speciesId": "oricorio_pau", "speciesName": "Oricorio (Pa'u)", "rating": 188, "matchups": [{"opponent": "virizion", "rating": 615, "opRating": 384}, {"opponent": "gastrodon", "rating": 579, "opRating": 420}, {"opponent": "leavanny", "rating": 557, "opRating": 442}, {"opponent": "meganium", "rating": 521, "opRating": 478}, {"opponent": "ferrothorn", "rating": 515, "opRating": 484}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "garcho<PERSON>", "rating": 96}, {"opponent": "lugia", "rating": 97}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "grou<PERSON>", "rating": 192}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 66338}, {"moveId": "POUND", "uses": 10162}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 35279}, {"moveId": "HURRICANE", "uses": 31967}, {"moveId": "AIR_CUTTER", "uses": 9256}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "HURRICANE"], "score": 25.5}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 205, "matchups": [{"opponent": "abomasnow", "rating": 664, "opRating": 335}, {"opponent": "cryogonal", "rating": 618, "opRating": 381}, {"opponent": "ferrothorn", "rating": 609, "opRating": 390}, {"opponent": "sandslash_alolan", "rating": 557, "opRating": 442}, {"opponent": "s<PERSON><PERSON>", "rating": 518, "opRating": 481}], "counters": [{"opponent": "mewtwo", "rating": 62}, {"opponent": "lugia", "rating": 85}, {"opponent": "dialga", "rating": 97}, {"opponent": "zacian_hero", "rating": 104}, {"opponent": "metagross", "rating": 194}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 48579}, {"moveId": "BITE", "uses": 27921}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 38689}, {"moveId": "FLAMETHROWER", "uses": 29796}, {"moveId": "FIRE_BLAST", "uses": 8066}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 25.5}, {"speciesId": "garbodor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 225, "matchups": [{"opponent": "leavanny", "rating": 558, "opRating": 441}, {"opponent": "lura<PERSON>s", "rating": 555, "opRating": 444}, {"opponent": "lilligant", "rating": 511, "opRating": 488}, {"opponent": "vileplume", "rating": 508, "opRating": 491}, {"opponent": "victree<PERSON>_shadow", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 43}, {"opponent": "lugia", "rating": 109}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "dragonite", "rating": 154}, {"opponent": "gyarados", "rating": 164}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 63150}, {"moveId": "TAKE_DOWN", "uses": 13350}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 38568}, {"moveId": "SEED_BOMB", "uses": 20683}, {"moveId": "GUNK_SHOT", "uses": 12008}, {"moveId": "ACID_SPRAY", "uses": 5240}]}, "moveset": ["INFESTATION", "BODY_SLAM", "SEED_BOMB"], "score": 25.1}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 206, "matchups": [{"opponent": "forretress", "rating": 785, "opRating": 214}, {"opponent": "ferrothorn", "rating": 748, "opRating": 251}, {"opponent": "steelix", "rating": 607, "opRating": 392}, {"opponent": "genesect_chill", "rating": 546, "opRating": 453}, {"opponent": "genesect_burn", "rating": 546, "opRating": 453}], "counters": [{"opponent": "mewtwo", "rating": 39}, {"opponent": "garcho<PERSON>", "rating": 72}, {"opponent": "metagross", "rating": 107}, {"opponent": "dialga", "rating": 154}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 205}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38565}, {"moveId": "EMBER", "uses": 37935}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 48585}, {"moveId": "FLAMETHROWER", "uses": 18123}, {"moveId": "FIRE_BLAST", "uses": 9888}, {"moveId": "FRUSTRATION", "uses": 7}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "FLAMETHROWER"], "score": 25.1}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 209, "matchups": [{"opponent": "exploud_shadow", "rating": 778, "opRating": 221}, {"opponent": "araquanid", "rating": 654, "opRating": 345}, {"opponent": "scyther", "rating": 583, "opRating": 416}, {"opponent": "masquerain", "rating": 577, "opRating": 422}, {"opponent": "bisharp", "rating": 546, "opRating": 453}], "counters": [{"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "dialga", "rating": 67}, {"opponent": "mewtwo", "rating": 91}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "gyarados", "rating": 144}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 58951}, {"moveId": "LOW_KICK", "uses": 17549}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 28736}, {"moveId": "STONE_EDGE", "uses": 26260}, {"moveId": "GRASS_KNOT", "uses": 21421}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 25.1}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 220, "matchups": [{"opponent": "ferrothorn", "rating": 768, "opRating": 231}, {"opponent": "sandslash_alolan", "rating": 637, "opRating": 362}, {"opponent": "genesect_chill", "rating": 583, "opRating": 416}, {"opponent": "genesect_burn", "rating": 583, "opRating": 416}, {"opponent": "s<PERSON><PERSON>", "rating": 536, "opRating": 463}], "counters": [{"opponent": "mewtwo", "rating": 39}, {"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "metagross", "rating": 81}, {"opponent": "dialga", "rating": 133}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 179}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 38863}, {"moveId": "EMBER", "uses": 37637}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38242}, {"moveId": "RETURN", "uses": 16384}, {"moveId": "FLAMETHROWER", "uses": 14259}, {"moveId": "FIRE_BLAST", "uses": 7700}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "RETURN"], "score": 24.6}, {"speciesId": "musharna", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 225, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 651, "opRating": 348}, {"opponent": "poliwrath", "rating": 613, "opRating": 386}, {"opponent": "poliwrath_shadow", "rating": 580, "opRating": 420}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 562, "opRating": 437}, {"opponent": "gallade", "rating": 520, "opRating": 480}], "counters": [{"opponent": "dialga", "rating": 40}, {"opponent": "lugia", "rating": 78}, {"opponent": "mewtwo", "rating": 85}, {"opponent": "zacian_hero", "rating": 208}, {"opponent": "gyarados", "rating": 211}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 51846}, {"moveId": "ZEN_HEADBUTT", "uses": 24654}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 42379}, {"moveId": "DAZZLING_GLEAM", "uses": 19893}, {"moveId": "FUTURE_SIGHT", "uses": 14239}]}, "moveset": ["CHARGE_BEAM", "PSYSHOCK", "DAZZLING_GLEAM"], "score": 24.6}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 191, "matchups": [{"opponent": "stunfisk", "rating": 744, "opRating": 255}, {"opponent": "relicanth", "rating": 664, "opRating": 335}, {"opponent": "feraligatr", "rating": 661, "opRating": 338}, {"opponent": "swampert", "rating": 597}, {"opponent": "vaporeon", "rating": 563, "opRating": 436}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 72}, {"opponent": "gyarados", "rating": 115}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "kyogre", "rating": 464}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44220}, {"moveId": "INFESTATION", "uses": 32280}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 26986}, {"moveId": "SLUDGE_BOMB", "uses": 17776}, {"moveId": "POWER_WHIP", "uses": 13485}, {"moveId": "RETURN", "uses": 12450}, {"moveId": "SOLAR_BEAM", "uses": 5685}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 24.6}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 175, "matchups": [{"opponent": "toxicroak", "rating": 658, "opRating": 341}, {"opponent": "falinks", "rating": 650, "opRating": 350}, {"opponent": "deoxys_defense", "rating": 583, "opRating": 416}, {"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 529, "opRating": 470}, {"opponent": "pinsir_shadow", "rating": 525, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "giratina_origin", "rating": 89}, {"opponent": "mewtwo", "rating": 93}, {"opponent": "lugia", "rating": 116}, {"opponent": "zacian_hero", "rating": 222}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 55715}, {"moveId": "ASTONISH", "uses": 20785}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 28436}, {"moveId": "DARK_PULSE", "uses": 18927}, {"moveId": "OMINOUS_WIND", "uses": 14583}, {"moveId": "PSYCHIC", "uses": 14531}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 24.4}, {"speciesId": "electrode", "speciesName": "Electrode", "rating": 198, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 746, "opRating": 253}, {"opponent": "pidgeot", "rating": 739, "opRating": 260}, {"opponent": "skar<PERSON><PERSON>_shadow", "rating": 721, "opRating": 278}, {"opponent": "drifb<PERSON>", "rating": 573, "opRating": 426}, {"opponent": "crobat", "rating": 566, "opRating": 433}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "mewtwo", "rating": 72}, {"opponent": "zacian_hero", "rating": 95}, {"opponent": "lugia", "rating": 242}, {"opponent": "gyarados", "rating": 298}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33151}, {"moveId": "SPARK", "uses": 24409}, {"moveId": "TACKLE", "uses": 18958}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 26939}, {"moveId": "DISCHARGE", "uses": 23891}, {"moveId": "RETURN", "uses": 11021}, {"moveId": "THUNDERBOLT", "uses": 10160}, {"moveId": "HYPER_BEAM", "uses": 4339}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 24.4}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 226, "matchups": [{"opponent": "magnezone", "rating": 664, "opRating": 335}, {"opponent": "steelix", "rating": 637, "opRating": 362}, {"opponent": "nihilego", "rating": 594, "opRating": 405}, {"opponent": "magnezone_shadow", "rating": 576, "opRating": 423}, {"opponent": "rai<PERSON>u", "rating": 567, "opRating": 432}], "counters": [{"opponent": "zacian_hero", "rating": 63}, {"opponent": "metagross", "rating": 84}, {"opponent": "dialga", "rating": 89}, {"opponent": "lugia", "rating": 119}, {"opponent": "excadrill", "rating": 274}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 53735}, {"moveId": "METAL_CLAW", "uses": 22765}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 32054}, {"moveId": "EARTHQUAKE", "uses": 19912}, {"moveId": "ROCK_TOMB", "uses": 12243}, {"moveId": "BULLDOZE", "uses": 12221}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "NIGHT_SLASH", "EARTHQUAKE"], "score": 24.2}, {"speciesId": "electabuzz", "speciesName": "Electabuzz", "rating": 191, "matchups": [{"opponent": "mantine", "rating": 721, "opRating": 278}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 714, "opRating": 285}, {"opponent": "skar<PERSON><PERSON>_shadow", "rating": 691, "opRating": 308}, {"opponent": "pidgeot", "rating": 691, "opRating": 308}, {"opponent": "jellicent", "rating": 567, "opRating": 432}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "metagross", "rating": 81}, {"opponent": "lugia", "rating": 202}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 294}, {"opponent": "gyarados", "rating": 484}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 60101}, {"moveId": "LOW_KICK", "uses": 16399}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 34819}, {"moveId": "RETURN", "uses": 17535}, {"moveId": "THUNDERBOLT", "uses": 12958}, {"moveId": "THUNDER", "uses": 11149}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "RETURN"], "score": 24}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 168, "matchups": [{"opponent": "gengar", "rating": 679, "opRating": 320}, {"opponent": "run<PERSON><PERSON>", "rating": 609, "opRating": 390}, {"opponent": "cofagrigus", "rating": 573, "opRating": 426}, {"opponent": "trevenant", "rating": 551, "opRating": 448}, {"opponent": "gourgeist_large", "rating": 506, "opRating": 493}], "counters": [{"opponent": "garcho<PERSON>", "rating": 65}, {"opponent": "dialga", "rating": 67}, {"opponent": "lugia", "rating": 95}, {"opponent": "mewtwo", "rating": 119}, {"opponent": "giratina_origin", "rating": 396}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39386}, {"moveId": "BITE", "uses": 26155}, {"moveId": "TAKE_DOWN", "uses": 10948}], "chargedMoves": [{"moveId": "SURF", "uses": 30805}, {"moveId": "DARK_PULSE", "uses": 24880}, {"moveId": "GRASS_KNOT", "uses": 20837}]}, "moveset": ["SUCKER_PUNCH", "SURF", "DARK_PULSE"], "score": 24}, {"speciesId": "grumpig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 187, "matchups": [{"opponent": "throh", "rating": 697, "opRating": 302}, {"opponent": "poliwrath", "rating": 601, "opRating": 398}, {"opponent": "toxicroak", "rating": 593, "opRating": 406}, {"opponent": "hypno_shadow", "rating": 593, "opRating": 406}, {"opponent": "hitmonchan", "rating": 511, "opRating": 488}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "gyarados", "rating": 110}, {"opponent": "garcho<PERSON>", "rating": 129}, {"opponent": "dragonite", "rating": 130}, {"opponent": "zacian_hero", "rating": 138}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 44601}, {"moveId": "CHARGE_BEAM", "uses": 31899}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37599}, {"moveId": "PSYCHIC", "uses": 31991}, {"moveId": "MIRROR_COAT", "uses": 6790}]}, "moveset": ["EXTRASENSORY", "SHADOW_BALL", "PSYCHIC"], "score": 24}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 172, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>_shadow", "rating": 644, "opRating": 355}, {"opponent": "pinsir", "rating": 577, "opRating": 422}, {"opponent": "deoxys_defense", "rating": 559, "opRating": 440}, {"opponent": "pinsir_shadow", "rating": 528, "opRating": 471}, {"opponent": "aromatisse", "rating": 507, "opRating": 492}], "counters": [{"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "dialga", "rating": 84}, {"opponent": "mewtwo", "rating": 85}, {"opponent": "giratina_origin", "rating": 87}, {"opponent": "lugia", "rating": 119}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 43890}, {"moveId": "SUCKER_PUNCH", "uses": 32610}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 37778}, {"moveId": "DARK_PULSE", "uses": 24662}, {"moveId": "DAZZLING_GLEAM", "uses": 13883}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 24}, {"speciesId": "manectric_shadow", "speciesName": "Man<PERSON><PERSON> (Shadow)", "rating": 167, "matchups": [{"opponent": "noivern", "rating": 770, "opRating": 229}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 681, "opRating": 318}, {"opponent": "alomomola", "rating": 668, "opRating": 331}, {"opponent": "forretress_shadow", "rating": 550, "opRating": 449}, {"opponent": "ninetales", "rating": 541, "opRating": 458}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "zacian_hero", "rating": 118}, {"opponent": "mewtwo", "rating": 122}, {"opponent": "garcho<PERSON>", "rating": 131}, {"opponent": "giratina_origin", "rating": 137}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 38257}, {"moveId": "CHARGE_BEAM", "uses": 20501}, {"moveId": "THUNDER_FANG", "uses": 17707}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 35711}, {"moveId": "PSYCHIC_FANGS", "uses": 19297}, {"moveId": "OVERHEAT", "uses": 11949}, {"moveId": "THUNDER", "uses": 5726}, {"moveId": "FLAME_BURST", "uses": 3950}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 23.8}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 178, "matchups": [{"opponent": "uxie", "rating": 637, "opRating": 362}, {"opponent": "slowbro", "rating": 633, "opRating": 366}, {"opponent": "bronzong", "rating": 610, "opRating": 389}, {"opponent": "slowking_galarian", "rating": 594, "opRating": 405}, {"opponent": "deoxys_defense", "rating": 543, "opRating": 456}], "counters": [{"opponent": "garcho<PERSON>", "rating": 65}, {"opponent": "giratina_origin", "rating": 83}, {"opponent": "dialga", "rating": 89}, {"opponent": "lugia", "rating": 116}, {"opponent": "mewtwo", "rating": 190}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40457}, {"moveId": "FEINT_ATTACK", "uses": 36043}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 42016}, {"moveId": "SHADOW_SNEAK", "uses": 23728}, {"moveId": "OMINOUS_WIND", "uses": 10765}]}, "moveset": ["SUCKER_PUNCH", "SHADOW_BALL", "SHADOW_SNEAK"], "score": 23.6}, {"speciesId": "manectric", "speciesName": "Manectric", "rating": 184, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 732, "opRating": 267}, {"opponent": "crobat", "rating": 659, "opRating": 340}, {"opponent": "relicanth", "rating": 614, "opRating": 385}, {"opponent": "klinklang", "rating": 541, "opRating": 458}, {"opponent": "tentacruel", "rating": 522, "opRating": 477}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "lugia", "rating": 97}, {"opponent": "mewtwo", "rating": 104}, {"opponent": "garcho<PERSON>", "rating": 107}, {"opponent": "giratina_origin", "rating": 111}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 37288}, {"moveId": "CHARGE_BEAM", "uses": 20657}, {"moveId": "THUNDER_FANG", "uses": 18541}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 33068}, {"moveId": "PSYCHIC_FANGS", "uses": 17246}, {"moveId": "OVERHEAT", "uses": 10796}, {"moveId": "RETURN", "uses": 6615}, {"moveId": "THUNDER", "uses": 5166}, {"moveId": "FLAME_BURST", "uses": 3643}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 23.4}, {"speciesId": "xatu", "speciesName": "Xatu", "rating": 170, "matchups": [{"opponent": "pheromosa", "rating": 748, "opRating": 251}, {"opponent": "breloom", "rating": 657, "opRating": 342}, {"opponent": "tropius", "rating": 540, "opRating": 459}, {"opponent": "gastrodon", "rating": 536, "opRating": 463}, {"opponent": "leavanny", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "zacian_hero", "rating": 89}, {"opponent": "gyarados", "rating": 103}, {"opponent": "mewtwo", "rating": 104}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 39784}, {"moveId": "FEINT_ATTACK", "uses": 36716}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 29238}, {"moveId": "FUTURE_SIGHT", "uses": 26807}, {"moveId": "OMINOUS_WIND", "uses": 20519}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "FUTURE_SIGHT"], "score": 23.4}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 189, "matchups": [{"opponent": "stunfisk", "rating": 719, "opRating": 280}, {"opponent": "politoed", "rating": 719, "opRating": 280}, {"opponent": "relicanth", "rating": 676, "opRating": 323}, {"opponent": "seismitoad", "rating": 640, "opRating": 359}, {"opponent": "feraligatr", "rating": 594, "opRating": 405}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "garcho<PERSON>", "rating": 72}, {"opponent": "gyarados", "rating": 118}, {"opponent": "swampert", "rating": 360}, {"opponent": "kyogre", "rating": 478}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 51532}, {"moveId": "BITE", "uses": 24968}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 37999}, {"moveId": "GRASS_KNOT", "uses": 31698}, {"moveId": "SOLAR_BEAM", "uses": 6742}]}, "moveset": ["VINE_WHIP", "CRUNCH", "GRASS_KNOT"], "score": 23.2}, {"speciesId": "comfey", "speciesName": "Comfey", "rating": 178, "matchups": [{"opponent": "relicanth", "rating": 647, "opRating": 352}, {"opponent": "gastrodon", "rating": 631, "opRating": 368}, {"opponent": "barbara<PERSON>", "rating": 550, "opRating": 449}, {"opponent": "poliwrath_shadow", "rating": 550, "opRating": 449}, {"opponent": "d<PERSON><PERSON><PERSON>", "rating": 538, "opRating": 461}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "garcho<PERSON>", "rating": 93}, {"opponent": "zacian_hero", "rating": 106}, {"opponent": "swampert", "rating": 181}, {"opponent": "dragonite", "rating": 289}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 8304}, {"moveId": "HIDDEN_POWER_ICE", "uses": 5379}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 4940}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 4722}, {"moveId": "HIDDEN_POWER_DARK", "uses": 4584}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4512}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4450}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 4388}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 4237}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4188}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 4180}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4075}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3793}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3728}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3702}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3688}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3488}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 38047}, {"moveId": "DRAINING_KISS", "uses": 28552}, {"moveId": "PETAL_BLIZZARD", "uses": 9815}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "DRAINING_KISS"], "score": 23}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 178, "matchups": [{"opponent": "golurk", "rating": 654, "opRating": 345}, {"opponent": "gourgeist_super", "rating": 636, "opRating": 363}, {"opponent": "decid<PERSON><PERSON>", "rating": 611, "opRating": 388}, {"opponent": "vileplume_shadow", "rating": 579, "opRating": 420}, {"opponent": "gliscor", "rating": 534, "opRating": 465}], "counters": [{"opponent": "dialga", "rating": 84}, {"opponent": "gyarados", "rating": 90}, {"opponent": "metagross", "rating": 119}, {"opponent": "giratina_origin", "rating": 141}, {"opponent": "mewtwo", "rating": 158}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 59113}, {"moveId": "PECK", "uses": 17387}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 26976}, {"moveId": "DARK_PULSE", "uses": 20530}, {"moveId": "SKY_ATTACK", "uses": 20110}, {"moveId": "PSYCHIC", "uses": 8754}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 23}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 191, "matchups": [{"opponent": "lickilicky", "rating": 630, "opRating": 369}, {"opponent": "forretress", "rating": 563, "opRating": 436}, {"opponent": "relicanth", "rating": 550, "opRating": 449}, {"opponent": "stunfisk_galarian", "rating": 536, "opRating": 463}, {"opponent": "greedent", "rating": 530, "opRating": 469}], "counters": [{"opponent": "mewtwo", "rating": 57}, {"opponent": "garcho<PERSON>", "rating": 70}, {"opponent": "lugia", "rating": 80}, {"opponent": "dialga", "rating": 97}, {"opponent": "giratina_origin", "rating": 133}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 54682}, {"moveId": "LOW_KICK", "uses": 11747}, {"moveId": "POUND", "uses": 10039}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 34754}, {"moveId": "FOCUS_BLAST", "uses": 20948}, {"moveId": "HYPER_BEAM", "uses": 20776}]}, "moveset": ["DOUBLE_KICK", "FIRE_PUNCH", "FOCUS_BLAST"], "score": 22.8}, {"speciesId": "sliggoo", "speciesName": "Sliggoo", "rating": 181, "matchups": [{"opponent": "ninetales", "rating": 594, "opRating": 405}, {"opponent": "golem", "rating": 571, "opRating": 428}, {"opponent": "pyroar", "rating": 529, "opRating": 470}, {"opponent": "salazzle", "rating": 525, "opRating": 474}, {"opponent": "infernape", "rating": 509, "opRating": 490}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "garcho<PERSON>", "rating": 68}, {"opponent": "giratina_origin", "rating": 79}, {"opponent": "zacian_hero", "rating": 95}, {"opponent": "mewtwo", "rating": 104}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 40163}, {"moveId": "TACKLE", "uses": 36337}], "chargedMoves": [{"moveId": "DRAGON_PULSE", "uses": 27423}, {"moveId": "MUDDY_WATER", "uses": 27029}, {"moveId": "SLUDGE_WAVE", "uses": 16730}, {"moveId": "WATER_PULSE", "uses": 5280}]}, "moveset": ["WATER_GUN", "DRAGON_PULSE", "MUDDY_WATER"], "score": 22.8}, {"speciesId": "starmie", "speciesName": "<PERSON><PERSON>", "rating": 177, "matchups": [{"opponent": "ninetales_shadow", "rating": 630, "opRating": 369}, {"opponent": "poliwrath", "rating": 584, "opRating": 415}, {"opponent": "lilligant", "rating": 577, "opRating": 422}, {"opponent": "<PERSON>ras", "rating": 559, "opRating": 440}, {"opponent": "emboar", "rating": 514, "opRating": 485}], "counters": [{"opponent": "garcho<PERSON>", "rating": 65}, {"opponent": "mewtwo", "rating": 72}, {"opponent": "dialga", "rating": 76}, {"opponent": "zacian_hero", "rating": 89}, {"opponent": "lugia", "rating": 95}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 8033}, {"moveId": "WATER_GUN", "uses": 6701}, {"moveId": "TACKLE", "uses": 4926}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4574}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4259}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3939}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3860}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 3822}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3739}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3719}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3506}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3501}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 3483}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3410}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3344}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3059}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3047}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3009}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2850}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 20419}, {"moveId": "PSYCHIC", "uses": 17237}, {"moveId": "THUNDER", "uses": 13365}, {"moveId": "HYDRO_PUMP", "uses": 12939}, {"moveId": "POWER_GEM", "uses": 8961}, {"moveId": "PSYBEAM", "uses": 3533}]}, "moveset": ["QUICK_ATTACK", "ICE_BEAM", "PSYCHIC"], "score": 22.3}, {"speciesId": "gur<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 200, "matchups": [{"opponent": "araquanid", "rating": 676, "opRating": 323}, {"opponent": "scyther", "rating": 657, "opRating": 342}, {"opponent": "masquerain", "rating": 657, "opRating": 342}, {"opponent": "scyther_shadow", "rating": 617, "opRating": 382}, {"opponent": "vikavolt", "rating": 573, "opRating": 426}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 65}, {"opponent": "dragonite", "rating": 95}, {"opponent": "zacian_hero", "rating": 124}, {"opponent": "gyarados", "rating": 144}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 56788}, {"moveId": "LOW_KICK", "uses": 19712}], "chargedMoves": [{"moveId": "BRICK_BREAK", "uses": 34089}, {"moveId": "STONE_EDGE", "uses": 32454}, {"moveId": "LOW_SWEEP", "uses": 10109}]}, "moveset": ["POISON_JAB", "BRICK_BREAK", "STONE_EDGE"], "score": 22.1}, {"speciesId": "lycanroc_midday", "speciesName": "Lycanroc (Midday)", "rating": 174, "matchups": [{"opponent": "skar<PERSON><PERSON>_shadow", "rating": 673, "opRating": 326}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 594, "opRating": 405}, {"opponent": "crobat", "rating": 548, "opRating": 451}, {"opponent": "pyroar", "rating": 518, "opRating": 481}, {"opponent": "klinklang", "rating": 518, "opRating": 481}], "counters": [{"opponent": "dialga", "rating": 89}, {"opponent": "mewtwo", "rating": 91}, {"opponent": "giratina_origin", "rating": 99}, {"opponent": "metagross", "rating": 125}, {"opponent": "lugia", "rating": 126}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 40137}, {"moveId": "ROCK_THROW", "uses": 36363}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27490}, {"moveId": "STONE_EDGE", "uses": 26232}, {"moveId": "CRUNCH", "uses": 22739}]}, "moveset": ["SUCKER_PUNCH", "DRILL_RUN", "STONE_EDGE"], "score": 22.1}, {"speciesId": "electrode_shadow", "speciesName": "Electrode (Shadow)", "rating": 173, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 721, "opRating": 278}, {"opponent": "pidgeot", "rating": 679, "opRating": 320}, {"opponent": "mantine", "rating": 658, "opRating": 341}, {"opponent": "skar<PERSON><PERSON>_shadow", "rating": 647, "opRating": 352}, {"opponent": "jellicent", "rating": 524, "opRating": 475}], "counters": [{"opponent": "dialga", "rating": 67}, {"opponent": "metagross", "rating": 107}, {"opponent": "lugia", "rating": 133}, {"opponent": "gyarados", "rating": 211}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 213}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 33487}, {"moveId": "SPARK", "uses": 24446}, {"moveId": "TACKLE", "uses": 18566}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 29662}, {"moveId": "DISCHARGE", "uses": 25931}, {"moveId": "THUNDERBOLT", "uses": 11085}, {"moveId": "HYPER_BEAM", "uses": 9730}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "FOUL_PLAY", "DISCHARGE"], "score": 21.9}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 172, "matchups": [{"opponent": "stunfisk", "rating": 697, "opRating": 302}, {"opponent": "politoed", "rating": 691, "opRating": 308}, {"opponent": "relicanth", "rating": 634, "opRating": 365}, {"opponent": "feraligatr", "rating": 540, "opRating": 459}, {"opponent": "seismitoad", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 65}, {"opponent": "garcho<PERSON>", "rating": 82}, {"opponent": "grou<PERSON>", "rating": 173}, {"opponent": "swampert", "rating": 390}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44615}, {"moveId": "INFESTATION", "uses": 31885}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31620}, {"moveId": "SLUDGE_BOMB", "uses": 22199}, {"moveId": "POWER_WHIP", "uses": 15839}, {"moveId": "SOLAR_BEAM", "uses": 6649}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 21.9}, {"speciesId": "chimecho", "speciesName": "Chi<PERSON><PERSON>", "rating": 179, "matchups": [{"opponent": "hitmontop", "rating": 625, "opRating": 375}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 603, "opRating": 396}, {"opponent": "poliwrath_shadow", "rating": 600, "opRating": 399}, {"opponent": "toxicroak", "rating": 567, "opRating": 432}, {"opponent": "weezing_galarian", "rating": 539, "opRating": 460}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "giratina_origin", "rating": 59}, {"opponent": "garcho<PERSON>", "rating": 72}, {"opponent": "dragonite", "rating": 114}, {"opponent": "zacian_hero", "rating": 118}], "moves": {"fastMoves": [{"moveId": "EXTRASENSORY", "uses": 52931}, {"moveId": "ASTONISH", "uses": 23569}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 30431}, {"moveId": "SHADOW_BALL", "uses": 28765}, {"moveId": "ENERGY_BALL", "uses": 17326}]}, "moveset": ["EXTRASENSORY", "PSYSHOCK", "SHADOW_BALL"], "score": 21.5}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 168, "matchups": [{"opponent": "stunfisk", "rating": 625, "opRating": 375}, {"opponent": "lanturn", "rating": 603, "opRating": 396}, {"opponent": "politoed", "rating": 551, "opRating": 448}, {"opponent": "lilligant", "rating": 533, "opRating": 466}, {"opponent": "serperior", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 65}, {"opponent": "gyarados", "rating": 105}, {"opponent": "zacian_hero", "rating": 141}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 219}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 38435}, {"moveId": "BULLET_SEED", "uses": 38065}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33831}, {"moveId": "PETAL_BLIZZARD", "uses": 30463}, {"moveId": "SOLAR_BEAM", "uses": 12282}]}, "moveset": ["POISON_JAB", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 21.5}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 175, "matchups": [{"opponent": "politoed", "rating": 723, "opRating": 276}, {"opponent": "stunfisk", "rating": 711, "opRating": 288}, {"opponent": "relicanth", "rating": 677, "opRating": 322}, {"opponent": "seismitoad", "rating": 674, "opRating": 325}, {"opponent": "feraligatr", "rating": 595, "opRating": 404}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "zacian_hero", "rating": 83}, {"opponent": "gyarados", "rating": 95}, {"opponent": "grou<PERSON>", "rating": 149}, {"opponent": "swampert", "rating": 330}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 50638}, {"moveId": "BITE", "uses": 25862}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 36267}, {"moveId": "POWER_WHIP", "uses": 29603}, {"moveId": "ENERGY_BALL", "uses": 10665}]}, "moveset": ["VINE_WHIP", "CRUNCH", "POWER_WHIP"], "score": 21.1}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 195, "matchups": [{"opponent": "heatran", "rating": 664, "opRating": 335}, {"opponent": "drapion", "rating": 582, "opRating": 417}, {"opponent": "aggron", "rating": 551, "opRating": 448}, {"opponent": "nidoqueen", "rating": 530, "opRating": 469}, {"opponent": "magmortar", "rating": 525, "opRating": 474}], "counters": [{"opponent": "garcho<PERSON>", "rating": 44}, {"opponent": "mewtwo", "rating": 54}, {"opponent": "zacian_hero", "rating": 75}, {"opponent": "metagross", "rating": 81}, {"opponent": "dialga", "rating": 92}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 43377}, {"moveId": "WATER_GUN", "uses": 33123}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 26777}, {"moveId": "STONE_EDGE", "uses": 21733}, {"moveId": "SLUDGE_BOMB", "uses": 14706}, {"moveId": "RETURN", "uses": 9966}, {"moveId": "ACID_SPRAY", "uses": 3320}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 20.7}, {"speciesId": "electabuzz_shadow", "speciesName": "Electabuzz (Shadow)", "rating": 176, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 741, "opRating": 258}, {"opponent": "skar<PERSON><PERSON>_shadow", "rating": 691, "opRating": 308}, {"opponent": "alomomola", "rating": 664, "opRating": 335}, {"opponent": "crobat", "rating": 516, "opRating": 483}, {"opponent": "klinklang", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 52}, {"opponent": "gyarados", "rating": 146}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 157}, {"opponent": "lugia", "rating": 292}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 58077}, {"moveId": "LOW_KICK", "uses": 18423}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 44951}, {"moveId": "THUNDERBOLT", "uses": 16799}, {"moveId": "THUNDER", "uses": 14524}, {"moveId": "FRUSTRATION", "uses": 109}]}, "moveset": ["THUNDER_SHOCK", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 20.5}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 165, "matchups": [{"opponent": "klinklang", "rating": 686, "opRating": 313}, {"opponent": "relicanth", "rating": 654, "opRating": 345}, {"opponent": "lanturn", "rating": 602, "opRating": 397}, {"opponent": "politoed", "rating": 598, "opRating": 401}, {"opponent": "feraligatr_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "dialga", "rating": 46}, {"opponent": "mewtwo", "rating": 57}, {"opponent": "gyarados", "rating": 157}, {"opponent": "kyogre", "rating": 385}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 404}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 50090}, {"moveId": "TACKLE", "uses": 26410}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 49836}, {"moveId": "ENERGY_BALL", "uses": 20573}, {"moveId": "SWIFT", "uses": 6137}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "ENERGY_BALL"], "score": 19.2}, {"speciesId": "absol_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 133, "matchups": [{"opponent": "oranguru", "rating": 681, "opRating": 318}, {"opponent": "drifb<PERSON>", "rating": 610, "opRating": 389}, {"opponent": "bronzong", "rating": 604, "opRating": 395}, {"opponent": "slowking_galarian", "rating": 567, "opRating": 432}, {"opponent": "mesprit", "rating": 516, "opRating": 483}], "counters": [{"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "giratina_origin", "rating": 81}, {"opponent": "dialga", "rating": 84}, {"opponent": "metagross", "rating": 133}, {"opponent": "mewtwo", "rating": 158}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 42298}, {"moveId": "PSYCHO_CUT", "uses": 34202}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 28416}, {"moveId": "MEGAHORN", "uses": 19974}, {"moveId": "THUNDER", "uses": 15052}, {"moveId": "PAYBACK", "uses": 12919}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 19}, {"speciesId": "absol", "speciesName": "Absol", "rating": 142, "matchups": [{"opponent": "oranguru", "rating": 728, "opRating": 271}, {"opponent": "bronzong", "rating": 661, "opRating": 338}, {"opponent": "mesprit", "rating": 610, "opRating": 389}, {"opponent": "espeon", "rating": 516, "opRating": 483}, {"opponent": "umbreon", "rating": 516, "opRating": 483}], "counters": [{"opponent": "dialga", "rating": 70}, {"opponent": "gyarados", "rating": 79}, {"opponent": "metagross", "rating": 104}, {"opponent": "giratina_origin", "rating": 127}, {"opponent": "mewtwo", "rating": 166}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 41586}, {"moveId": "PSYCHO_CUT", "uses": 34914}], "chargedMoves": [{"moveId": "DARK_PULSE", "uses": 25209}, {"moveId": "MEGAHORN", "uses": 17503}, {"moveId": "THUNDER", "uses": 13136}, {"moveId": "PAYBACK", "uses": 11476}, {"moveId": "RETURN", "uses": 9299}]}, "moveset": ["SNARL", "DARK_PULSE", "MEGAHORN"], "score": 18.8}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 185, "matchups": [{"opponent": "relicanth", "rating": 658, "opRating": 341}, {"opponent": "lanturn", "rating": 655, "opRating": 344}, {"opponent": "carracosta", "rating": 622, "opRating": 377}, {"opponent": "seismitoad", "rating": 583, "opRating": 416}, {"opponent": "greedent", "rating": 583, "opRating": 416}], "counters": [{"opponent": "dialga", "rating": 29}, {"opponent": "garcho<PERSON>", "rating": 68}, {"opponent": "mewtwo", "rating": 75}, {"opponent": "gyarados", "rating": 85}, {"opponent": "swampert", "rating": 166}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 36445}, {"moveId": "INFESTATION", "uses": 29463}, {"moveId": "ACID", "uses": 10656}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 33796}, {"moveId": "GRASS_KNOT", "uses": 28916}, {"moveId": "BULLDOZE", "uses": 13787}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "STONE_EDGE", "GRASS_KNOT"], "score": 18.6}, {"speciesId": "swalot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 183, "matchups": [{"opponent": "lura<PERSON>s", "rating": 606, "opRating": 393}, {"opponent": "aromatisse", "rating": 527, "opRating": 472}, {"opponent": "vileplume", "rating": 512, "opRating": 487}, {"opponent": "victree<PERSON>_shadow", "rating": 512, "opRating": 487}, {"opponent": "clefable", "rating": 504, "opRating": 495}], "counters": [{"opponent": "dialga", "rating": 35}, {"opponent": "mewtwo", "rating": 39}, {"opponent": "zacian_hero", "rating": 57}, {"opponent": "sylveon", "rating": 237}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 247}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54847}, {"moveId": "ROCK_SMASH", "uses": 21653}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 33582}, {"moveId": "SLUDGE_BOMB", "uses": 28774}, {"moveId": "GUNK_SHOT", "uses": 7477}, {"moveId": "ACID_SPRAY", "uses": 6621}]}, "moveset": ["INFESTATION", "ICE_BEAM", "SLUDGE_BOMB"], "score": 18.6}, {"speciesId": "deoxys_speed", "speciesName": "<PERSON><PERSON><PERSON> (Speed)", "rating": 151, "matchups": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 641, "opRating": 358}, {"opponent": "hitmonchan", "rating": 622, "opRating": 377}, {"opponent": "poliwrath", "rating": 610, "opRating": 389}, {"opponent": "poliwrath_shadow", "rating": 606, "opRating": 393}, {"opponent": "toxicroak", "rating": 570, "opRating": 429}], "counters": [{"opponent": "dialga", "rating": 43}, {"opponent": "mewtwo", "rating": 54}, {"opponent": "lugia", "rating": 78}, {"opponent": "zacian_hero", "rating": 95}, {"opponent": "gyarados", "rating": 144}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 59045}, {"moveId": "ZEN_HEADBUTT", "uses": 17455}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 43907}, {"moveId": "THUNDERBOLT", "uses": 23698}, {"moveId": "SWIFT", "uses": 8914}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 17.3}, {"speciesId": "weezing", "speciesName": "Weezing", "rating": 165, "matchups": [{"opponent": "araquanid", "rating": 627, "opRating": 372}, {"opponent": "ferrothorn", "rating": 536, "opRating": 463}, {"opponent": "vespiquen", "rating": 526, "opRating": 473}, {"opponent": "vileplume", "rating": 510, "opRating": 489}, {"opponent": "rapidash_galarian", "rating": 506, "opRating": 493}], "counters": [{"opponent": "dialga", "rating": 43}, {"opponent": "mewtwo", "rating": 46}, {"opponent": "garcho<PERSON>", "rating": 46}, {"opponent": "lugia", "rating": 59}, {"opponent": "gyarados", "rating": 64}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 33165}, {"moveId": "TACKLE", "uses": 26469}, {"moveId": "ACID", "uses": 16861}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 19306}, {"moveId": "DARK_PULSE", "uses": 17905}, {"moveId": "SLUDGE_BOMB", "uses": 17428}, {"moveId": "THUNDERBOLT", "uses": 13275}, {"moveId": "RETURN", "uses": 8580}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 17.3}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 142, "matchups": [{"opponent": "stunfisk", "rating": 631, "opRating": 368}, {"opponent": "heatran", "rating": 613, "opRating": 386}, {"opponent": "klinklang", "rating": 597, "opRating": 402}, {"opponent": "muk_alolan", "rating": 530, "opRating": 469}, {"opponent": "stunfisk_galarian", "rating": 510, "opRating": 489}], "counters": [{"opponent": "mewtwo", "rating": 39}, {"opponent": "garcho<PERSON>", "rating": 49}, {"opponent": "dialga", "rating": 89}, {"opponent": "metagross", "rating": 107}, {"opponent": "excadrill", "rating": 141}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 44756}, {"moveId": "WATER_GUN", "uses": 31744}], "chargedMoves": [{"moveId": "EARTHQUAKE", "uses": 30420}, {"moveId": "STONE_EDGE", "uses": 24937}, {"moveId": "SLUDGE_BOMB", "uses": 17151}, {"moveId": "ACID_SPRAY", "uses": 3859}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "EARTHQUAKE", "STONE_EDGE"], "score": 16.9}, {"speciesId": "accelgor", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 125, "matchups": [{"opponent": "amoon<PERSON><PERSON>_shadow", "rating": 537, "opRating": 462}, {"opponent": "ludico<PERSON>", "rating": 529, "opRating": 470}, {"opponent": "shaymin_land", "rating": 523, "opRating": 476}, {"opponent": "obstagoon", "rating": 502, "opRating": 497}], "counters": [{"opponent": "dialga", "rating": 57}, {"opponent": "gyarados", "rating": 64}, {"opponent": "mewtwo", "rating": 70}, {"opponent": "garcho<PERSON>", "rating": 70}, {"opponent": "metagross", "rating": 75}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 54916}, {"moveId": "ACID", "uses": 21584}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 26682}, {"moveId": "SIGNAL_BEAM", "uses": 22929}, {"moveId": "FOCUS_BLAST", "uses": 20375}, {"moveId": "ACID_SPRAY", "uses": 6596}]}, "moveset": ["INFESTATION", "BUG_BUZZ", "SIGNAL_BEAM"], "score": 16.1}, {"speciesId": "weezing_shadow", "speciesName": "Weez<PERSON> (Shadow)", "rating": 141, "matchups": [{"opponent": "lilligant", "rating": 697, "opRating": 302}, {"opponent": "hitmontop", "rating": 590, "opRating": 409}, {"opponent": "araquanid", "rating": 526, "opRating": 473}, {"opponent": "lura<PERSON>s", "rating": 506, "opRating": 493}, {"opponent": "breloom", "rating": 503, "opRating": 496}], "counters": [{"opponent": "dialga", "rating": 43}, {"opponent": "garcho<PERSON>", "rating": 58}, {"opponent": "lugia", "rating": 59}, {"opponent": "mewtwo", "rating": 62}, {"opponent": "zacian_hero", "rating": 72}], "moves": {"fastMoves": [{"moveId": "INFESTATION", "uses": 42116}, {"moveId": "TACKLE", "uses": 34384}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 21610}, {"moveId": "DARK_PULSE", "uses": 20129}, {"moveId": "SLUDGE_BOMB", "uses": 19847}, {"moveId": "THUNDERBOLT", "uses": 14938}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INFESTATION", "SHADOW_BALL", "DARK_PULSE"], "score": 15.6}, {"speciesId": "octillery", "speciesName": "Octillery", "rating": 123, "matchups": [{"opponent": "torkoal", "rating": 560, "opRating": 439}], "counters": [{"opponent": "dialga", "rating": 48}, {"opponent": "zacian_hero", "rating": 49}, {"opponent": "metagross", "rating": 61}, {"opponent": "lugia", "rating": 116}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 168}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 27009}, {"moveId": "WATER_GUN", "uses": 24960}, {"moveId": "MUD_SHOT", "uses": 24502}], "chargedMoves": [{"moveId": "OCTAZOOKA", "uses": 26798}, {"moveId": "AURORA_BEAM", "uses": 21830}, {"moveId": "GUNK_SHOT", "uses": 13785}, {"moveId": "WATER_PULSE", "uses": 8012}, {"moveId": "ACID_SPRAY", "uses": 5954}]}, "moveset": ["LOCK_ON", "OCTAZOOKA", "AURORA_BEAM"], "score": 15.2}, {"speciesId": "deoxys", "speciesName": "Deoxys", "rating": 100, "matchups": [{"opponent": "throh", "rating": 610, "opRating": 389}], "counters": [{"opponent": "mewtwo", "rating": 46}, {"opponent": "dialga", "rating": 57}, {"opponent": "lugia", "rating": 73}, {"opponent": "metagross", "rating": 75}, {"opponent": "gyarados", "rating": 157}], "moves": {"fastMoves": [{"moveId": "CHARGE_BEAM", "uses": 60404}, {"moveId": "ZEN_HEADBUTT", "uses": 16096}], "chargedMoves": [{"moveId": "PSYCHO_BOOST", "uses": 42197}, {"moveId": "THUNDERBOLT", "uses": 22781}, {"moveId": "HYPER_BEAM", "uses": 11525}]}, "moveset": ["CHARGE_BEAM", "PSYCHO_BOOST", "THUNDERBOLT"], "score": 14.6}]