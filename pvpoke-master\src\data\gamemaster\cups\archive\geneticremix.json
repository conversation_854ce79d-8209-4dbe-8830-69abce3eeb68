{"name": "geneticremix", "title": "Genetic Remix", "include": [{"filterType": "id", "name": "Species", "values": ["shadow", "slowking", "slowking_galarian", "slowking_shadow", "slowpoke", "slowpoke_", "gal<PERSON>", "slowpoke_shadow", "smoochum", "snea<PERSON>_his<PERSON>an", "snea<PERSON>_<PERSON><PERSON>an_shadow", "sneasler", "snea<PERSON>_shadow", "solosis", "solosis_shadow", "solrock", "spiritomb", "spoink", "spoink_shadow", "starmie", "stunky", "stunky_shadow", "sudowoodo", "sudowoodo_shadow", "suicune", "suicune_shadow", "swalot", "swanna", "swanna_shadow", "swoobat", "talonflame", "tapu_fini", "tentacool", "tentacool_shadow", "tentacruel", "tentacruel_shadow", "t<PERSON><PERSON><PERSON>", "torkoal", "torracat", "totodile", "totodile_shadow", "toxapex", "trevenant", "turtonator", "typhlosion", "typhlosion_hisuian", "typhlosion_shadow", "tyranitar", "tyranitar_shadow", "tyrantrum", "tyrunt", "unown", "uxie", "vaporeon", "venomoth", "venomoth_shadow", "venusaur", "venusaur_shadow", "victini", "victreebel", "victree<PERSON>_shadow", "vileplume", "vileplume_shadow", "wailmer", "wailmer_shadow", "wailord", "wailord_shadow", "walrein", "walrein_shadow", "wartortle", "wartortle_shadow", "weepinbell", "weepin<PERSON>_shadow", "weezing", "weezing_galarian", "weezing_shadow", "whirlipede", "wobbuffet", "wobbuffet_shadow", "wugtrio", "xatu", "xatu_shadow", "abomasnow", "abomasnow_shadow", "altaria", "arctibax", "articuno", "articuno_shadow", "bax<PERSON><PERSON><PERSON>", "beautifly", "bombirdier", "breloom", "butterfree", "buzzwole", "cacturne", "cacturne_shadow", "chesnaught", "crabominable", "dart<PERSON>", "decid<PERSON><PERSON>_<PERSON><PERSON>an", "dragonite", "dragonite_shadow", "exeggutor_alolan", "exeggutor_alolan_shadow", "frigibax", "guzzlord", "hakamo_o", "hawlucha", "heracross", "honch<PERSON><PERSON>", "honchk<PERSON>_shadow", "hydreigon", "<PERSON><PERSON><PERSON>", "jumpluff_shadow", "kommo_o", "leavanny", "<PERSON><PERSON>", "<PERSON><PERSON>_shadow", "lokix", "mandibuzz", "masquerain", "meowscarada", "moltres_galarian", "<PERSON><PERSON>", "murkrow", "murkrow_shadow", "ninetales_alolan", "ninetales_alolan_shadow", "ninjask", "noivern", "nuzleaf", "nuzleaf_shadow", "pangoro", "parasect", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "rowlet", "salamence", "salamence_shadow", "scrafty", "scraggy", "scyther", "scyther_shadow", "shaymin_sky", "shiftry", "shiftry_shadow", "shiinotic", "sneasel", "sneasel_shadow", "snover", "snover_shadow", "swadloon", "<PERSON><PERSON><PERSON><PERSON>", "togetic", "tropius", "vespiquen", "vivillon", "vullaby", "weavile", "weavile_shadow", "whimsicott", "wormadam_plant", "yanma", "yanmega", "zap<PERSON>_galarian", "<PERSON><PERSON><PERSON>", "aerodactyl", "aerodactyl_shadow", "<PERSON><PERSON><PERSON>", "al<PERSON><PERSON>_shadow", "alomomola", "amaura", "amoon<PERSON>s", "amoon<PERSON><PERSON>_shadow", "anorith", "anorith_shadow", "araquanid", "arbok", "arbok_shadow", "arcanine", "arcanine_<PERSON><PERSON>an", "arcanine_shadow", "archen", "archeops", "a<PERSON><PERSON>", "arm<PERSON>", "<PERSON><PERSON>_<PERSON>", "armarouge", "articuno_galarian", "avalugg_his<PERSON>an", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "banette", "banette_shadow", "barbara<PERSON>", "basculin", "beedrill", "beedrill_shadow", "be<PERSON><PERSON><PERSON>", "blastoise", "blastoise_shadow", "blaziken", "blazi<PERSON>_shadow", "boldore", "bonsly", "braixen", "brionne", "bruxish", "bulbasaur", "bulbasaur_shadow", "carracosta", "castform_rainy", "castform_sunny", "celebi", "ceruledge", "chandelure", "chandelure_shadow", "charizard", "charizard_shadow", "charmeleon", "charm<PERSON>on_shadow", "chimecho", "clamperl", "clauncher", "clawitzer", "cloyster", "cloyster_shadow", "cofagrigus", "combusken", "combusken_shadow", "corphish", "corphish_shadow", "corsola", "cradily", "cradily_shadow", "crawdaunt", "crawdaunt_shadow", "crobat", "crobat_shadow", "crocalor", "cro<PERSON><PERSON>", "croconaw_shadow", "crustle", "crustle_shadow", "darmanitan_standard", "darmanitan_standard_shadow", "darum<PERSON>", "decid<PERSON><PERSON>", "delphox", "deoxys_speed", "dewgong", "<PERSON><PERSON><PERSON>", "diancie", "dragalge", "drapion", "drapion_shadow", "drifb<PERSON>", "drifb<PERSON>_shadow", "drifloon", "drifloon_shadow", "drowzee", "drowzee_shadow", "duosion", "duosion_shadow", "dusclops", "dusclops_shadow", "dusknoir", "dusknoir_shadow", "dustox", "dwebble", "dwebble_shadow", "<PERSON><PERSON><PERSON>", "emboar", "entei", "entei_shadow", "espeon", "espurr", "exeggcute", "exeggcute_shadow", "exeggutor", "exeggutor_shadow", "flareon", "fletchinder", "floatzel", "frillish", "<PERSON><PERSON><PERSON>", "froslass", "froslass_shadow", "fuecoco", "gallade", "gallade_shadow", "garbodor", "gardevoir", "gardevoir_shadow", "gengar", "gengar_shadow", "gigalith", "giratina_origin", "gloom", "gloom_shadow", "golbat", "golbat_shadow", "goldeen", "golduck", "<PERSON><PERSON>_shadow", "golisopod", "gorebyss", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_shadow", "gourgeist_average", "gourgeist_large", "gourgeist_small", "gourgeist_super", "grimer", "grimer_alolan", "grimer_alolan_shadow", "grimer_shadow", "growlithe", "growl<PERSON>e_his<PERSON>an", "growlithe_shadow", "grumpig", "grumpig_shadow", "gyarados", "gyarado<PERSON>_shadow", "haunter", "haunter_shadow", "heatmor", "ho_oh", "ho_oh_shadow", "hoopa", "houndoom", "houndoom_shadow", "houndour", "houndour_shadow", "houndstone", "hunt<PERSON>", "hypno", "hypno_shadow", "incineroar", "infernape", "infernape_shadow", "ivysaur", "ivy<PERSON>_shadow", "jellicent", "jynx", "kabuto", "kabutops", "kadabra", "king<PERSON>", "king<PERSON>_shadow", "kingler", "kleavor", "koffing", "koffing_shadow", "krabby", "kyogre_shadow", "lampent", "lampent_shadow", "<PERSON>ras", "lap<PERSON>_shadow", "larve<PERSON>", "latias", "latias_shadow", "latios", "latios_shadow", "lileep", "lileep_shadow", "lombre", "ludico<PERSON>", "lugia", "lugia_shadow", "lumineon", "lunatone", "lycanroc_dusk", "lycanroc_midday", "lycanroc_midnight", "magby", "magcargo", "magmar", "magmar_shadow", "magmortar", "magmortar_shadow", "malamar", "mantine", "mantyke", "marowak_alolan", "marowak_alolan_shadow", "medicham", "meowstic_female", "me<PERSON><PERSON>", "mesprit", "mewtwo_shadow", "milotic", "misdreavus", "misdreavus_shadow", "mismagius", "mismagius_shadow", "moltres", "moltres_shadow", "monferno", "monferno_shadow", "mr_mime", "mr_mime_galarian", "mr_rime", "muk", "muk_alolan", "muk_alolan_shadow", "muk_shadow", "munna", "musharna", "naganadel", "nidorina", "nidor<PERSON>_shadow", "<PERSON><PERSON><PERSON>", "nidor<PERSON>_shadow", "ninetales", "ninetales_shadow", "nosepass", "nosepass_shadow", "octillery", "oddish", "oddish_shadow", "omanyte", "omanyte_shadow", "omastar", "omastar_shadow", "oricorio_baile", "oricorio_pau", "oricorio_sensu", "over<PERSON><PERSON>l", "pelipper", "pignite", "pip<PERSON>p", "piplup_shadow", "poipole", "politoed", "politoed_shadow", "poliwhirl", "poliwhirl_shadow", "ponyta", "ponyta_galarian", "popp<PERSON>", "primarina", "prin<PERSON><PERSON><PERSON>", "prinplup_shadow", "pumpkaboo_average", "pumpkaboo_large", "pumpkaboo_small", "pumpkaboo_super", "quaquaval", "quaxwell", "quilava", "quilava_shadow", "qwilfish", "qwilfish_his<PERSON>an", "rampardos", "rampardos_shadow", "rapidash", "rapidash_galarian", "regirock", "regirock_shadow", "relicanth", "reuniclus", "reuniclus_shadow", "rog<PERSON><PERSON><PERSON>", "roselia", "roserade", "sableye", "sableye_shadow", "salazzle", "samu<PERSON>t", "samu<PERSON><PERSON>_<PERSON><PERSON>an", "scolipede", "seadra", "seadra_shadow", "seaking", "sealeo", "sealeo_shadow", "seel", "seviper", "sharpedo", "sharpedo_shadow", "shellos", "sigilyph", "simipour", "simisear", "skeledirge", "s<PERSON><PERSON><PERSON>", "skorup<PERSON>_shadow", "skrelp", "skuntank", "skuntank_shadow", "slowbro", "slowbro_galarian", "slowbro_"]}], "exclude": [], "overrides": [], "league": 1500, "useDefaultMovesets": 1, "excludeLowPokemon": 1}