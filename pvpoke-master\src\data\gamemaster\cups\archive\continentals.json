{"name": "continentals", "title": "Silph <PERSON>s", "include": [{"filterType": "id", "values": ["charizard", "samu<PERSON>t", "g<PERSON><PERSON>", "blaziken", "swampert", "empoleon", "marshtomp", "ivysaur", "blastoise", "quilladin", "venusaur", "chesnaught", "delphox", "<PERSON><PERSON><PERSON>", "combusken", "feraligatr", "serperior", "bayleef", "monferno", "emboar", "braixen", "wartortle", "pignite", "cro<PERSON><PERSON>", "prin<PERSON><PERSON><PERSON>", "grotle", "quilava", "typhlosion", "<PERSON><PERSON>e", "meganium", "charmeleon", "gren<PERSON><PERSON>", "chespin", "sceptile", "torterra", "turtwig", "pip<PERSON>p", "infernape", "<PERSON><PERSON><PERSON>", "bulbasaur", "totodile", "r<PERSON><PERSON>_alolan", "ninetales", "golbat", "venomoth", "magneton", "noctowl", "xatu", "<PERSON><PERSON><PERSON><PERSON>", "quagsire", "forretress", "<PERSON><PERSON><PERSON><PERSON>", "gardevoir", "vigoroth", "medicham", "flygon", "altaria", "whiscash", "tropius", "bastiodon", "lucario", "toxicroak", "beedrill", "sandslash_alolan", "ninetales_alolan", "wigglytuff", "poliwrath", "graveler_alolan", "muk_alolan", "marowak_alolan", "<PERSON>ras", "snorlax", "dragonair", "sudowoodo", "<PERSON><PERSON><PERSON>", "umbreon", "king<PERSON>", "shiftry", "dusclops", "bibarel", "wormadam_trash", "vespiquen", "skuntank", "bronzong", "gliscor", "probopass", "scrafty", "<PERSON><PERSON><PERSON>", "ferrothorn", "r<PERSON><PERSON>", "nidoqueen", "primeape", "victreebel", "dewgong", "hypno", "lanturn", "politoed", "mantine", "sableye", "cradily", "castform", "castform_rainy", "drifb<PERSON>", "munchlax", "gallade", "froslass", "whimsicott", "escavalier", "jellicent", "stunfisk_galarian", "stunfisk", "mandibuzz", "talonflame", "pidgeot", "machamp", "muk", "lickitung", "dragonite", "steelix", "heracross", "magcargo", "pelipper", "milotic", "walrein", "drapion", "abomasnow", "crustle", "cofagrigus", "<PERSON><PERSON><PERSON>", "litleo", "slurpuff", "malamar", "dragalge", "dedenne", "gourgeist_super", "gourgeist_small", "gourgeist_large", "gourgeist_average", "lura<PERSON>s", "bewear", "oranguru", "greedent", "obstagoon", "articuno", "articuno_galarian", "zapdos", "zap<PERSON>_galarian", "moltres", "moltres_galarian", "mew", "mewtwo", "rai<PERSON>u", "entei", "suicune", "lugia", "ho_oh", "celebi", "regirock", "regice", "latias", "latios", "kyogre", "grou<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "deoxys_defense", "deoxys_speed", "deoxys_attack", "deoxys", "uxie", "mesprit", "<PERSON><PERSON><PERSON>", "dialga", "palkia", "heatran", "regigigas", "giratina_altered", "giratina_origin", "cresselia", "darkrai", "shaymin_land", "shaymin_sky", "cobalion", "terrakion", "virizion", "tornadus_incarnate", "torn<PERSON><PERSON>_therian", "thundurus_incarnate", "thundurus_therian", "land<PERSON><PERSON>_therian", "landorus_incarnate", "kyurem", "victini", "keldeo_ordinary", "genesect", "meloetta_aria", "x<PERSON><PERSON>", "yveltal", "hoopa_unbound", "hoopa_confined", "tapu_fini", "tapu_bulu", "tapu_koko", "tapu_lele", "cosmog", "cosmoem", "solgaleo", "lunala", "nihilego", "buzzwole", "pheromosa", "xurkitree", "celesteela", "kartana", "guzzlord", "meltan", "melmetal", "zacian_hero", "z<PERSON><PERSON><PERSON>_hero", "<PERSON><PERSON><PERSON><PERSON>", "regidrago", "zarude"], "includeShadows": true}], "exclude": [], "slots": [{"pokemon": ["charizard", "samu<PERSON>t", "g<PERSON><PERSON>", "blaziken", "swampert", "empoleon", "marshtomp", "ivysaur", "blastoise", "quilladin", "venusaur", "chesnaught", "delphox", "<PERSON><PERSON><PERSON>", "combusken", "feraligatr", "serperior", "bayleef", "monferno", "emboar", "braixen", "wartortle", "pignite", "cro<PERSON><PERSON>", "prin<PERSON><PERSON><PERSON>", "grotle", "quilava", "typhlosion", "<PERSON><PERSON>e", "meganium", "charmeleon", "gren<PERSON><PERSON>", "chespin", "sceptile", "torterra", "turtwig", "pip<PERSON>p", "infernape", "<PERSON><PERSON><PERSON>", "bulbasaur", "totodile"]}, {"pokemon": ["r<PERSON><PERSON>_alolan", "ninetales", "golbat", "venomoth", "magneton", "noctowl", "xatu", "<PERSON><PERSON><PERSON><PERSON>", "quagsire", "forretress", "<PERSON><PERSON><PERSON><PERSON>", "gardevoir", "vigoroth", "medicham", "flygon", "altaria", "whiscash", "tropius", "bastiodon", "lucario", "toxicroak"]}, {"pokemon": ["beedrill", "sandslash_alolan", "ninetales_alolan", "wigglytuff", "poliwrath", "graveler_alolan", "muk_alolan", "marowak_alolan", "<PERSON>ras", "snorlax", "dragonair", "sudowoodo", "<PERSON><PERSON><PERSON>", "umbreon", "king<PERSON>", "shiftry", "dusclops", "bibarel", "wormadam_trash", "vespiquen", "skuntank", "bronzong", "gliscor", "probopass", "scrafty", "<PERSON><PERSON><PERSON>", "ferrothorn"]}, {"pokemon": ["r<PERSON><PERSON>", "nidoqueen", "primeape", "victreebel", "dewgong", "hypno", "lanturn", "politoed", "mantine", "sableye", "cradily", "castform", "castform_rainy", "drifb<PERSON>", "munchlax", "gallade", "froslass", "whimsicott", "escavalier", "jellicent", "stunfisk_galarian", "stunfisk", "mandibuzz", "talonflame"]}, {"pokemon": ["pidgeot", "machamp", "muk", "lickitung", "dragonite", "steelix", "heracross", "magcargo", "pelipper", "milotic", "walrein", "drapion", "abomasnow", "crustle", "cofagrigus", "<PERSON><PERSON><PERSON>", "litleo", "slurpuff", "malamar", "dragalge", "dedenne", "gourgeist_super", "gourgeist_small", "gourgeist_large", "gourgeist_average", "lura<PERSON>s", "bewear", "oranguru", "greedent", "obstagoon"]}, {"pokemon": ["articuno", "articuno_galarian", "zapdos", "zap<PERSON>_galarian", "moltres", "moltres_galarian", "mew", "mewtwo", "rai<PERSON>u", "entei", "suicune", "lugia", "ho_oh", "celebi", "regirock", "regice", "latias", "latios", "kyogre", "grou<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "deoxys_defense", "deoxys_speed", "deoxys_attack", "deoxys", "uxie", "mesprit", "<PERSON><PERSON><PERSON>", "dialga", "palkia", "heatran", "regigigas", "giratina_altered", "giratina_origin", "cresselia", "darkrai", "shaymin_land", "shaymin_sky", "cobalion", "terrakion", "virizion", "tornadus_incarnate", "torn<PERSON><PERSON>_therian", "thundurus_incarnate", "thundurus_therian", "land<PERSON><PERSON>_therian", "landorus_incarnate", "kyurem", "victini", "keldeo_ordinary", "genesect", "meloetta_aria", "x<PERSON><PERSON>", "yveltal", "hoopa_unbound", "hoopa_confined", "tapu_fini", "tapu_bulu", "tapu_koko", "tapu_lele", "cosmog", "cosmoem", "solgaleo", "lunala", "nihilego", "buzzwole", "pheromosa", "xurkitree", "celesteela", "kartana", "guzzlord", "meltan", "melmetal", "zacian_hero", "z<PERSON><PERSON><PERSON>_hero", "<PERSON><PERSON><PERSON><PERSON>", "regidrago", "zarude"]}]}