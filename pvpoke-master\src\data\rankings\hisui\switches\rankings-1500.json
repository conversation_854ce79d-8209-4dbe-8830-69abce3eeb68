[{"speciesId": "sneasler", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 798, "matchups": [{"opponent": "drifb<PERSON>", "rating": 745}, {"opponent": "spiritomb", "rating": 683}, {"opponent": "dusknoir_shadow", "rating": 660}, {"opponent": "drapion_shadow", "rating": 647}, {"opponent": "gallade_shadow", "rating": 602}], "counters": [{"opponent": "wyr<PERSON><PERSON>", "rating": 58}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 382}, {"opponent": "gliscor", "rating": 383}, {"opponent": "gliscor_shadow", "rating": 474}, {"opponent": "gastrodon", "rating": 491}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 18816}, {"moveId": "POISON_JAB", "uses": 14456}, {"moveId": "ROCK_SMASH", "uses": 4211}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18515}, {"moveId": "AERIAL_ACE", "uses": 10840}, {"moveId": "X_SCISSOR", "uses": 8145}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 100}, {"speciesId": "spiritomb", "speciesName": "Spiritomb", "rating": 742, "matchups": [{"opponent": "dusknoir_shadow", "rating": 716}, {"opponent": "drifb<PERSON>", "rating": 692}, {"opponent": "drapion_shadow", "rating": 677}, {"opponent": "gastrodon", "rating": 677}, {"opponent": "gliscor", "rating": 615}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 178}, {"opponent": "skuntank_shadow", "rating": 250}, {"opponent": "magnezone_shadow", "rating": 397}, {"opponent": "electivire_shadow", "rating": 421}, {"opponent": "bastiodon", "rating": 446}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 24632}, {"moveId": "FEINT_ATTACK", "uses": 12868}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 14838}, {"moveId": "SHADOW_BALL", "uses": 12442}, {"moveId": "SHADOW_SNEAK", "uses": 7048}, {"moveId": "OMINOUS_WIND", "uses": 3177}]}, "moveset": ["SUCKER_PUNCH", "ROCK_TOMB", "SHADOW_BALL"], "score": 98.8}, {"speciesId": "over<PERSON><PERSON>l", "speciesName": "Overqwil", "rating": 796, "matchups": [{"opponent": "drifb<PERSON>", "rating": 913}, {"opponent": "dusknoir_shadow", "rating": 831}, {"opponent": "drapion_shadow", "rating": 651}, {"opponent": "bastiodon", "rating": 622}, {"opponent": "gliscor", "rating": 545}], "counters": [{"opponent": "gastrodon", "rating": 339}, {"opponent": "vespiquen", "rating": 356}, {"opponent": "lickilicky", "rating": 378}, {"opponent": "spiritomb", "rating": 379}, {"opponent": "bibarel", "rating": 422}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 20914}, {"moveId": "POISON_JAB", "uses": 16586}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 11993}, {"moveId": "DARK_PULSE", "uses": 7816}, {"moveId": "ICE_BEAM", "uses": 6420}, {"moveId": "SHADOW_BALL", "uses": 5692}, {"moveId": "SLUDGE_BOMB", "uses": 5646}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 97.8}, {"speciesId": "qwilfish_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 800, "matchups": [{"opponent": "drifb<PERSON>", "rating": 913}, {"opponent": "dusknoir_shadow", "rating": 830}, {"opponent": "drapion_shadow", "rating": 661}, {"opponent": "bastiodon", "rating": 619}, {"opponent": "gliscor", "rating": 549}], "counters": [{"opponent": "gastrodon", "rating": 333}, {"opponent": "vespiquen", "rating": 352}, {"opponent": "spiritomb", "rating": 370}, {"opponent": "lickilicky", "rating": 371}, {"opponent": "empoleon_shadow", "rating": 446}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21053}, {"moveId": "POISON_JAB", "uses": 16447}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 11958}, {"moveId": "DARK_PULSE", "uses": 7848}, {"moveId": "ICE_BEAM", "uses": 6397}, {"moveId": "SHADOW_BALL", "uses": 5683}, {"moveId": "SLUDGE_BOMB", "uses": 5649}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "SHADOW_BALL"], "score": 97.6}, {"speciesId": "drapion_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 814, "matchups": [{"opponent": "dusknoir_shadow", "rating": 826}, {"opponent": "spiritomb", "rating": 720}, {"opponent": "drifb<PERSON>", "rating": 703}, {"opponent": "gliscor", "rating": 631}, {"opponent": "bastiodon", "rating": 610}], "counters": [{"opponent": "gastrodon", "rating": 315}, {"opponent": "electrode_hisuian", "rating": 321}, {"opponent": "magnezone_shadow", "rating": 343}, {"opponent": "vespiquen", "rating": 375}, {"opponent": "sneasler", "rating": 379}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 12250}, {"moveId": "ICE_FANG", "uses": 10946}, {"moveId": "INFESTATION", "uses": 7996}, {"moveId": "BITE", "uses": 6320}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 15275}, {"moveId": "CRUNCH", "uses": 12803}, {"moveId": "SLUDGE_BOMB", "uses": 6915}, {"moveId": "FELL_STINGER", "uses": 2491}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 96.9}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 790, "matchups": [{"opponent": "gastrodon", "rating": 741}, {"opponent": "spiritomb", "rating": 633}, {"opponent": "gallade_shadow", "rating": 590}, {"opponent": "gliscor", "rating": 581}, {"opponent": "drapion_shadow", "rating": 560}], "counters": [{"opponent": "drifb<PERSON>", "rating": 253}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 321}, {"opponent": "froslass", "rating": 365}, {"opponent": "bibarel", "rating": 419}, {"opponent": "dusknoir_shadow", "rating": 472}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 13684}, {"moveId": "FURY_CUTTER", "uses": 13299}, {"moveId": "WING_ATTACK", "uses": 10505}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 12812}, {"moveId": "AERIAL_ACE", "uses": 12762}, {"moveId": "EARTHQUAKE", "uses": 7541}, {"moveId": "SAND_TOMB", "uses": 4323}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 92.4}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 757, "matchups": [{"opponent": "dusknoir_shadow", "rating": 700}, {"opponent": "drapion_shadow", "rating": 654}, {"opponent": "spiritomb", "rating": 651}, {"opponent": "drifb<PERSON>", "rating": 538}, {"opponent": "gallade_shadow", "rating": 507}], "counters": [{"opponent": "abomasnow", "rating": 293}, {"opponent": "gliscor", "rating": 370}, {"opponent": "gastrodon", "rating": 375}, {"opponent": "abomasnow_shadow", "rating": 381}, {"opponent": "gliscor_shadow", "rating": 418}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 10375}, {"moveId": "ICE_FANG", "uses": 8699}, {"moveId": "FIRE_FANG", "uses": 7842}, {"moveId": "THUNDER_FANG", "uses": 6899}, {"moveId": "BITE", "uses": 3716}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 11552}, {"moveId": "SCORCHING_SANDS", "uses": 9694}, {"moveId": "BODY_SLAM", "uses": 6601}, {"moveId": "EARTH_POWER", "uses": 3798}, {"moveId": "STONE_EDGE", "uses": 3138}, {"moveId": "EARTHQUAKE", "uses": 2804}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 91.1}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 774, "matchups": [{"opponent": "bastiodon", "rating": 894, "opRating": 105}, {"opponent": "sneasler", "rating": 806, "opRating": 193}, {"opponent": "dusknoir_shadow", "rating": 700}, {"opponent": "spiritomb", "rating": 630}, {"opponent": "drapion_shadow", "rating": 605}], "counters": [{"opponent": "drifb<PERSON>", "rating": 289}, {"opponent": "gallade_shadow", "rating": 394}, {"opponent": "gliscor", "rating": 418}, {"opponent": "abomasnow_shadow", "rating": 430}, {"opponent": "gastrodon", "rating": 497}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 11174}, {"moveId": "ICE_FANG", "uses": 8642}, {"moveId": "FIRE_FANG", "uses": 7679}, {"moveId": "THUNDER_FANG", "uses": 6740}, {"moveId": "BITE", "uses": 3255}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 11538}, {"moveId": "SCORCHING_SANDS", "uses": 9723}, {"moveId": "BODY_SLAM", "uses": 6565}, {"moveId": "EARTH_POWER", "uses": 3799}, {"moveId": "STONE_EDGE", "uses": 3133}, {"moveId": "EARTHQUAKE", "uses": 2825}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 90.9}, {"speciesId": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 742, "matchups": [{"opponent": "drifb<PERSON>", "rating": 878}, {"opponent": "dusknoir_shadow", "rating": 780}, {"opponent": "gliscor", "rating": 734}, {"opponent": "drapion_shadow", "rating": 643}, {"opponent": "spiritomb", "rating": 553}], "counters": [{"opponent": "gallade_shadow", "rating": 278}, {"opponent": "vespiquen", "rating": 375}, {"opponent": "gastrodon", "rating": 395}, {"opponent": "bastiodon", "rating": 395}, {"opponent": "sneasler", "rating": 446}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 14344}, {"moveId": "SNARL", "uses": 12412}, {"moveId": "WATERFALL", "uses": 10786}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 10554}, {"moveId": "RAZOR_SHELL", "uses": 9879}, {"moveId": "DARK_PULSE", "uses": 8926}, {"moveId": "X_SCISSOR", "uses": 8134}]}, "moveset": ["FURY_CUTTER", "DARK_PULSE", "ICY_WIND"], "score": 89.8}, {"speciesId": "drapion", "speciesName": "Drapion", "rating": 787, "matchups": [{"opponent": "dusknoir_shadow", "rating": 817}, {"opponent": "gliscor", "rating": 652}, {"opponent": "drifb<PERSON>", "rating": 597}, {"opponent": "spiritomb", "rating": 529}, {"opponent": "drapion_shadow", "rating": 504}], "counters": [{"opponent": "lickilicky", "rating": 278}, {"opponent": "gastrodon", "rating": 279}, {"opponent": "magnezone_shadow", "rating": 299}, {"opponent": "empoleon_shadow", "rating": 364}, {"opponent": "bastiodon", "rating": 474}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 12294}, {"moveId": "ICE_FANG", "uses": 10681}, {"moveId": "INFESTATION", "uses": 7989}, {"moveId": "BITE", "uses": 6591}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 13983}, {"moveId": "CRUNCH", "uses": 11815}, {"moveId": "SLUDGE_BOMB", "uses": 6226}, {"moveId": "RETURN", "uses": 3209}, {"moveId": "FELL_STINGER", "uses": 2213}]}, "moveset": ["POISON_STING", "CRUNCH", "AQUA_TAIL"], "score": 89.3}, {"speciesId": "vespiquen", "speciesName": "Vespiquen", "rating": 769, "matchups": [{"opponent": "gastrodon", "rating": 829}, {"opponent": "spiritomb", "rating": 678}, {"opponent": "gliscor", "rating": 670}, {"opponent": "drapion_shadow", "rating": 625}, {"opponent": "dusknoir_shadow", "rating": 530}], "counters": [{"opponent": "bastiodon", "rating": 212}, {"opponent": "empoleon_shadow", "rating": 348}, {"opponent": "magnezone_shadow", "rating": 362}, {"opponent": "drifb<PERSON>", "rating": 382}, {"opponent": "bibarel", "rating": 432}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 12497}, {"moveId": "BUG_BITE", "uses": 9113}, {"moveId": "POISON_STING", "uses": 8734}, {"moveId": "AIR_SLASH", "uses": 7176}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 14412}, {"moveId": "POWER_GEM", "uses": 11936}, {"moveId": "FELL_STINGER", "uses": 3976}, {"moveId": "BUG_BUZZ", "uses": 3927}, {"moveId": "SIGNAL_BEAM", "uses": 3341}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "POWER_GEM"], "score": 89.3}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 777, "matchups": [{"opponent": "gastrodon", "rating": 780}, {"opponent": "gallade_shadow", "rating": 668}, {"opponent": "drapion_shadow", "rating": 659}, {"opponent": "spiritomb", "rating": 659}, {"opponent": "dusknoir_shadow", "rating": 508}], "counters": [{"opponent": "drifb<PERSON>", "rating": 208}, {"opponent": "drifloon", "rating": 233}, {"opponent": "froslass", "rating": 334}, {"opponent": "electivire_shadow", "rating": 425}, {"opponent": "dusknoir", "rating": 488}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 13499}, {"moveId": "FURY_CUTTER", "uses": 13091}, {"moveId": "WING_ATTACK", "uses": 10894}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 12881}, {"moveId": "AERIAL_ACE", "uses": 12740}, {"moveId": "EARTHQUAKE", "uses": 7520}, {"moveId": "SAND_TOMB", "uses": 4384}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 88.6}, {"speciesId": "hippopotas", "speciesName": "Hippopotas", "rating": 711, "matchups": [{"opponent": "drapion_shadow", "rating": 723}, {"opponent": "gallade_shadow", "rating": 598}, {"opponent": "dusknoir_shadow", "rating": 578}, {"opponent": "gliscor", "rating": 536}, {"opponent": "abomasnow_shadow", "rating": 519}], "counters": [{"opponent": "gastrodon", "rating": 345}, {"opponent": "gliscor_shadow", "rating": 366}, {"opponent": "drifb<PERSON>", "rating": 387}, {"opponent": "spiritomb", "rating": 432}, {"opponent": "bibarel", "rating": 436}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 18813}, {"moveId": "TACKLE", "uses": 9959}, {"moveId": "BITE", "uses": 8698}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 13899}, {"moveId": "DIG", "uses": 11334}, {"moveId": "BODY_SLAM", "uses": 9573}, {"moveId": "RETURN", "uses": 2655}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 88}, {"speciesId": "palkia_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 708, "matchups": [{"opponent": "gallade_shadow", "rating": 794}, {"opponent": "drifb<PERSON>", "rating": 750}, {"opponent": "gliscor", "rating": 695}, {"opponent": "gastrodon", "rating": 575}, {"opponent": "spiritomb", "rating": 545}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 100}, {"opponent": "abomasnow_shadow", "rating": 402}, {"opponent": "dusknoir_shadow", "rating": 405}, {"opponent": "drapion_shadow", "rating": 406}, {"opponent": "electrode_hisuian", "rating": 417}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 18841}, {"moveId": "DRAGON_BREATH", "uses": 18659}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 23238}, {"moveId": "DRACO_METEOR", "uses": 6101}, {"moveId": "FIRE_BLAST", "uses": 5023}, {"moveId": "HYDRO_PUMP", "uses": 2983}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "AQUA_TAIL", "DRACO_METEOR"], "score": 87.7}, {"speciesId": "electivire", "speciesName": "Electivire", "rating": 758, "matchups": [{"opponent": "drifb<PERSON>", "rating": 833}, {"opponent": "drapion_shadow", "rating": 810}, {"opponent": "gliscor", "rating": 773}, {"opponent": "spiritomb", "rating": 699}, {"opponent": "dusknoir_shadow", "rating": 671}], "counters": [{"opponent": "abomasnow", "rating": 237}, {"opponent": "gastrodon", "rating": 247}, {"opponent": "lickilicky", "rating": 293}, {"opponent": "abomasnow_shadow", "rating": 297}, {"opponent": "magnezone_shadow", "rating": 308}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 33630}, {"moveId": "LOW_KICK", "uses": 3870}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13183}, {"moveId": "ICE_PUNCH", "uses": 9451}, {"moveId": "THUNDER_PUNCH", "uses": 7631}, {"moveId": "FLAMETHROWER", "uses": 5134}, {"moveId": "THUNDER", "uses": 2138}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 87.3}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 776, "matchups": [{"opponent": "gliscor", "rating": 819}, {"opponent": "gallade_shadow", "rating": 747, "opRating": 252}, {"opponent": "gastrodon", "rating": 737}, {"opponent": "spiritomb", "rating": 706}, {"opponent": "dusknoir_shadow", "rating": 685}], "counters": [{"opponent": "drapion_shadow", "rating": 296}, {"opponent": "qwilfish_his<PERSON>an", "rating": 305}, {"opponent": "drifb<PERSON>", "rating": 346}, {"opponent": "bastiodon", "rating": 370}, {"opponent": "vespiquen", "rating": 371}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 9418}, {"moveId": "BULLET_SEED", "uses": 8095}, {"moveId": "POISON_JAB", "uses": 8037}, {"moveId": "MAGICAL_LEAF", "uses": 7811}, {"moveId": "RAZOR_LEAF", "uses": 4087}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 12577}, {"moveId": "GRASS_KNOT", "uses": 8333}, {"moveId": "LEAF_STORM", "uses": 5132}, {"moveId": "SLUDGE_BOMB", "uses": 4893}, {"moveId": "DAZZLING_GLEAM", "uses": 4799}, {"moveId": "SOLAR_BEAM", "uses": 1755}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 85.8}, {"speciesId": "froslass", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 749, "matchups": [{"opponent": "gliscor", "rating": 857}, {"opponent": "drifb<PERSON>", "rating": 765, "opRating": 234}, {"opponent": "abomasnow_shadow", "rating": 711, "opRating": 288}, {"opponent": "gallade_shadow", "rating": 623, "opRating": 376}, {"opponent": "dusknoir_shadow", "rating": 557, "opRating": 442}], "counters": [{"opponent": "bastiodon", "rating": 262}, {"opponent": "bibarel", "rating": 278}, {"opponent": "spiritomb", "rating": 394}, {"opponent": "gastrodon", "rating": 419}, {"opponent": "drapion_shadow", "rating": 427}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18972}, {"moveId": "HEX", "uses": 18528}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 15438}, {"moveId": "SHADOW_BALL", "uses": 7559}, {"moveId": "CRUNCH", "uses": 6563}, {"moveId": "TRIPLE_AXEL", "uses": 5423}, {"moveId": "RETURN", "uses": 2588}]}, "moveset": ["HEX", "AVALANCHE", "SHADOW_BALL"], "score": 85.5}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 751, "matchups": [{"opponent": "drifb<PERSON>", "rating": 874}, {"opponent": "gastrodon", "rating": 800}, {"opponent": "gliscor", "rating": 786}, {"opponent": "dusknoir_shadow", "rating": 629}, {"opponent": "spiritomb", "rating": 587}], "counters": [{"opponent": "heatran", "rating": 67}, {"opponent": "bronzong", "rating": 220}, {"opponent": "bastiodon", "rating": 273}, {"opponent": "drapion_shadow", "rating": 402}, {"opponent": "qwilfish_his<PERSON>an", "rating": 413}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 19136}, {"moveId": "LEAFAGE", "uses": 12528}, {"moveId": "RAZOR_LEAF", "uses": 5865}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 17045}, {"moveId": "ENERGY_BALL", "uses": 7433}, {"moveId": "ICY_WIND", "uses": 5773}, {"moveId": "OUTRAGE", "uses": 4523}, {"moveId": "BLIZZARD", "uses": 2615}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 85}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 765, "matchups": [{"opponent": "gallade_shadow", "rating": 904, "opRating": 95}, {"opponent": "drifb<PERSON>", "rating": 865, "opRating": 134}, {"opponent": "drapion_shadow", "rating": 786}, {"opponent": "dusknoir_shadow", "rating": 756}, {"opponent": "spiritomb", "rating": 643, "opRating": 356}], "counters": [{"opponent": "gliscor_shadow", "rating": 250}, {"opponent": "abomasnow_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 375}, {"opponent": "gastrodon", "rating": 375}, {"opponent": "bastiodon", "rating": 406}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 26828}, {"moveId": "TACKLE", "uses": 10672}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19359}, {"moveId": "SWIFT", "uses": 9791}, {"moveId": "ENERGY_BALL", "uses": 8329}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 85}, {"speciesId": "bronzong", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 761, "matchups": [{"opponent": "gallade_shadow", "rating": 962, "opRating": 37}, {"opponent": "sneasler", "rating": 795, "opRating": 204}, {"opponent": "abomasnow_shadow", "rating": 766, "opRating": 233}, {"opponent": "bastiodon", "rating": 550, "opRating": 450}, {"opponent": "drapion_shadow", "rating": 545}], "counters": [{"opponent": "gastrodon", "rating": 339}, {"opponent": "gliscor", "rating": 353}, {"opponent": "dusknoir_shadow", "rating": 405}, {"opponent": "spiritomb", "rating": 413}, {"opponent": "drifb<PERSON>", "rating": 476}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 15808}, {"moveId": "CONFUSION", "uses": 12881}, {"moveId": "FEINT_ATTACK", "uses": 8808}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 14310}, {"moveId": "HEAVY_SLAM", "uses": 6737}, {"moveId": "PAYBACK", "uses": 6270}, {"moveId": "BULLDOZE", "uses": 5671}, {"moveId": "PSYCHIC", "uses": 2407}, {"moveId": "FLASH_CANNON", "uses": 2159}]}, "moveset": ["METAL_SOUND", "PSYSHOCK", "PAYBACK"], "score": 83.4}, {"speciesId": "gallade_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 789, "matchups": [{"opponent": "bastiodon", "rating": 879, "opRating": 120}, {"opponent": "drapion_shadow", "rating": 826}, {"opponent": "abomasnow_shadow", "rating": 793, "opRating": 206}, {"opponent": "gastrodon", "rating": 783, "opRating": 216}, {"opponent": "spiritomb", "rating": 572, "opRating": 427}], "counters": [{"opponent": "bronzong", "rating": 45}, {"opponent": "vespiquen", "rating": 196}, {"opponent": "gliscor", "rating": 357}, {"opponent": "dusknoir_shadow", "rating": 394}, {"opponent": "drifb<PERSON>", "rating": 413}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 18369}, {"moveId": "CONFUSION", "uses": 11444}, {"moveId": "CHARM", "uses": 5497}, {"moveId": "LOW_KICK", "uses": 2243}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 14526}, {"moveId": "LEAF_BLADE", "uses": 13507}, {"moveId": "SYNCHRONOISE", "uses": 7153}, {"moveId": "PSYCHIC", "uses": 2215}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 83.1}, {"speciesId": "electivire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 759, "matchups": [{"opponent": "drifb<PERSON>", "rating": 810}, {"opponent": "gliscor", "rating": 787}, {"opponent": "drapion_shadow", "rating": 773}, {"opponent": "spiritomb", "rating": 662}, {"opponent": "dusknoir_shadow", "rating": 601}], "counters": [{"opponent": "rhyperior_shadow", "rating": 59}, {"opponent": "rhyperior", "rating": 82}, {"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "gastrodon", "rating": 279}, {"opponent": "abomasnow", "rating": 286}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34170}, {"moveId": "LOW_KICK", "uses": 3330}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 13187}, {"moveId": "ICE_PUNCH", "uses": 9439}, {"moveId": "THUNDER_PUNCH", "uses": 7588}, {"moveId": "FLAMETHROWER", "uses": 5190}, {"moveId": "THUNDER", "uses": 2138}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_SHOCK", "ICE_PUNCH", "WILD_CHARGE"], "score": 82.2}, {"speciesId": "toxicroak", "speciesName": "Toxicroak", "rating": 792, "matchups": [{"opponent": "bastiodon", "rating": 821, "opRating": 178}, {"opponent": "abomasnow_shadow", "rating": 810, "opRating": 189}, {"opponent": "drifb<PERSON>", "rating": 806}, {"opponent": "drapion_shadow", "rating": 658}, {"opponent": "dusknoir_shadow", "rating": 589}], "counters": [{"opponent": "gallade_shadow", "rating": 197}, {"opponent": "gliscor_shadow", "rating": 349}, {"opponent": "gastrodon", "rating": 360}, {"opponent": "spiritomb", "rating": 370}, {"opponent": "gliscor", "rating": 383}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 11386}, {"moveId": "POISON_JAB", "uses": 9201}, {"moveId": "MUD_SHOT", "uses": 8483}, {"moveId": "COUNTER", "uses": 8467}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 14016}, {"moveId": "MUD_BOMB", "uses": 9066}, {"moveId": "SHADOW_BALL", "uses": 8226}, {"moveId": "SLUDGE_BOMB", "uses": 6238}]}, "moveset": ["POISON_STING", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 81.6}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 703, "matchups": [{"opponent": "drapion_shadow", "rating": 880}, {"opponent": "drifb<PERSON>", "rating": 833}, {"opponent": "gliscor", "rating": 750}, {"opponent": "dusknoir_shadow", "rating": 652}, {"opponent": "spiritomb", "rating": 557}], "counters": [{"opponent": "bronzong", "rating": 129}, {"opponent": "empoleon_shadow", "rating": 254}, {"opponent": "gallade_shadow", "rating": 274}, {"opponent": "bibarel", "rating": 348}, {"opponent": "gastrodon", "rating": 452}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 20304}, {"moveId": "MUD_SLAP", "uses": 17196}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 10994}, {"moveId": "ICICLE_SPEAR", "uses": 9189}, {"moveId": "HIGH_HORSEPOWER", "uses": 5465}, {"moveId": "BULLDOZE", "uses": 4462}, {"moveId": "STONE_EDGE", "uses": 3949}, {"moveId": "ANCIENT_POWER", "uses": 3534}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 81.3}, {"speciesId": "empoleon", "speciesName": "Empoleon", "rating": 740, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 913, "opRating": 86}, {"opponent": "abomasnow_shadow", "rating": 836, "opRating": 163}, {"opponent": "gliscor", "rating": 831}, {"opponent": "drapion_shadow", "rating": 807}, {"opponent": "dusknoir_shadow", "rating": 651, "opRating": 348}], "counters": [{"opponent": "electivire_shadow", "rating": 189}, {"opponent": "toxicroak", "rating": 236}, {"opponent": "gallade_shadow", "rating": 264}, {"opponent": "gastrodon", "rating": 318}, {"opponent": "drifb<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 12868}, {"moveId": "WATERFALL", "uses": 12576}, {"moveId": "STEEL_WING", "uses": 12122}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 18467}, {"moveId": "DRILL_PECK", "uses": 9518}, {"moveId": "BLIZZARD", "uses": 4215}, {"moveId": "FLASH_CANNON", "uses": 3251}, {"moveId": "HYDRO_PUMP", "uses": 1967}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 81}, {"speciesId": "snea<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 778, "matchups": [{"opponent": "drifb<PERSON>", "rating": 714}, {"opponent": "spiritomb", "rating": 647}, {"opponent": "dusknoir_shadow", "rating": 593}, {"opponent": "drapion_shadow", "rating": 562}, {"opponent": "gallade_shadow", "rating": 531}], "counters": [{"opponent": "wyr<PERSON><PERSON>", "rating": 71}, {"opponent": "rhyperior_shadow", "rating": 152}, {"opponent": "gastrodon", "rating": 169}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 213}, {"opponent": "gliscor", "rating": 474}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 18941}, {"moveId": "POISON_JAB", "uses": 14706}, {"moveId": "ROCK_SMASH", "uses": 3882}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18495}, {"moveId": "AERIAL_ACE", "uses": 10881}, {"moveId": "X_SCISSOR", "uses": 8122}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 80.9}, {"speciesId": "kleavor", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 690, "matchups": [{"opponent": "gallade_shadow", "rating": 841, "opRating": 158}, {"opponent": "abomasnow_shadow", "rating": 764, "opRating": 235}, {"opponent": "gastrodon", "rating": 677, "opRating": 322}, {"opponent": "spiritomb", "rating": 649, "opRating": 350}, {"opponent": "qwilfish_his<PERSON>an", "rating": 524, "opRating": 475}], "counters": [{"opponent": "vespiquen", "rating": 276}, {"opponent": "gliscor", "rating": 366}, {"opponent": "bastiodon", "rating": 374}, {"opponent": "drifb<PERSON>", "rating": 385}, {"opponent": "drapion_shadow", "rating": 491}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 18495}, {"moveId": "QUICK_ATTACK", "uses": 11575}, {"moveId": "AIR_SLASH", "uses": 7471}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 10204}, {"moveId": "TRAILBLAZE", "uses": 9336}, {"moveId": "STONE_EDGE", "uses": 9259}, {"moveId": "ROCK_SLIDE", "uses": 8738}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "TRAILBLAZE"], "score": 78.3}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 673, "matchups": [{"opponent": "sneasler", "rating": 906, "opRating": 93}, {"opponent": "dusknoir_shadow", "rating": 884, "opRating": 115}, {"opponent": "gallade_shadow", "rating": 876, "opRating": 123}, {"opponent": "drifb<PERSON>", "rating": 585, "opRating": 414}, {"opponent": "gliscor", "rating": 514}], "counters": [{"opponent": "bastiodon", "rating": 294}, {"opponent": "qwilfish_his<PERSON>an", "rating": 318}, {"opponent": "drapion_shadow", "rating": 326}, {"opponent": "spiritomb", "rating": 456}, {"opponent": "gastrodon", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25717}, {"moveId": "SCRATCH", "uses": 11783}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 13744}, {"moveId": "THUNDER", "uses": 8651}, {"moveId": "RETURN", "uses": 7957}, {"moveId": "PLAY_ROUGH", "uses": 7137}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 78.1}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 713, "matchups": [{"opponent": "drifb<PERSON>", "rating": 851, "opRating": 148}, {"opponent": "gliscor", "rating": 811}, {"opponent": "spiritomb", "rating": 539, "opRating": 460}, {"opponent": "drapion_shadow", "rating": 514}, {"opponent": "gastrodon", "rating": 510}], "counters": [{"opponent": "bronzong", "rating": 129}, {"opponent": "gallade_shadow", "rating": 235}, {"opponent": "froslass", "rating": 265}, {"opponent": "bibarel", "rating": 285}, {"opponent": "dusknoir_shadow", "rating": 494}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 20178}, {"moveId": "MUD_SLAP", "uses": 17322}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 10960}, {"moveId": "ICICLE_SPEAR", "uses": 9186}, {"moveId": "HIGH_HORSEPOWER", "uses": 5464}, {"moveId": "BULLDOZE", "uses": 4473}, {"moveId": "STONE_EDGE", "uses": 3947}, {"moveId": "ANCIENT_POWER", "uses": 3552}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 77.5}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 713, "matchups": [{"opponent": "drifb<PERSON>", "rating": 863}, {"opponent": "gastrodon", "rating": 762}, {"opponent": "gliscor", "rating": 755}, {"opponent": "dusknoir_shadow", "rating": 562}, {"opponent": "drapion_shadow", "rating": 541}], "counters": [{"opponent": "empoleon_shadow", "rating": 188}, {"opponent": "gallade_shadow", "rating": 221}, {"opponent": "spiritomb", "rating": 384}, {"opponent": "bastiodon", "rating": 392}, {"opponent": "sneasler", "rating": 491}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 19034}, {"moveId": "LEAFAGE", "uses": 12559}, {"moveId": "RAZOR_LEAF", "uses": 5973}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 16004}, {"moveId": "ENERGY_BALL", "uses": 6957}, {"moveId": "ICY_WIND", "uses": 5448}, {"moveId": "OUTRAGE", "uses": 4185}, {"moveId": "RETURN", "uses": 2489}, {"moveId": "BLIZZARD", "uses": 2468}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 76.8}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 723, "matchups": [{"opponent": "sneasler", "rating": 803, "opRating": 196}, {"opponent": "qwilfish_his<PERSON>an", "rating": 756, "opRating": 243}, {"opponent": "drapion_shadow", "rating": 678}, {"opponent": "gliscor", "rating": 546}, {"opponent": "dusknoir_shadow", "rating": 518, "opRating": 481}], "counters": [{"opponent": "toxicroak", "rating": 112}, {"opponent": "gallade_shadow", "rating": 326}, {"opponent": "gastrodon", "rating": 330}, {"opponent": "spiritomb", "rating": 375}, {"opponent": "drifb<PERSON>", "rating": 406}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 21838}, {"moveId": "LICK", "uses": 13033}, {"moveId": "ZEN_HEADBUTT", "uses": 2661}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 13456}, {"moveId": "SHADOW_BALL", "uses": 9615}, {"moveId": "EARTHQUAKE", "uses": 7004}, {"moveId": "SOLAR_BEAM", "uses": 4335}, {"moveId": "HYPER_BEAM", "uses": 3071}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "EARTHQUAKE"], "score": 76.8}, {"speciesId": "dusknoir_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 769, "matchups": [{"opponent": "gallade_shadow", "rating": 877}, {"opponent": "drifb<PERSON>", "rating": 727}, {"opponent": "bastiodon", "rating": 716}, {"opponent": "gastrodon", "rating": 683}, {"opponent": "gliscor", "rating": 672}], "counters": [{"opponent": "drapion", "rating": 182}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "qwilfish_his<PERSON>an", "rating": 235}, {"opponent": "electrode_hisuian", "rating": 291}, {"opponent": "spiritomb", "rating": 456}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 20318}, {"moveId": "ASTONISH", "uses": 17182}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 12340}, {"moveId": "DYNAMIC_PUNCH", "uses": 10398}, {"moveId": "DARK_PULSE", "uses": 4505}, {"moveId": "SHADOW_BALL", "uses": 3338}, {"moveId": "PSYCHIC", "uses": 3013}, {"moveId": "POLTERGEIST", "uses": 2171}, {"moveId": "OMINOUS_WIND", "uses": 1761}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "DYNAMIC_PUNCH", "SHADOW_PUNCH"], "score": 75.6}, {"speciesId": "mantyke", "speciesName": "Mantyke", "rating": 636, "matchups": [{"opponent": "gliscor", "rating": 766}, {"opponent": "sneasler", "rating": 695, "opRating": 304}, {"opponent": "gastrodon", "rating": 666}, {"opponent": "gallade_shadow", "rating": 662, "opRating": 337}, {"opponent": "drifb<PERSON>", "rating": 554, "opRating": 445}], "counters": [{"opponent": "magnezone_shadow", "rating": 210}, {"opponent": "electivire_shadow", "rating": 226}, {"opponent": "bastiodon", "rating": 384}, {"opponent": "spiritomb", "rating": 394}, {"opponent": "drapion_shadow", "rating": 419}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 23454}, {"moveId": "TACKLE", "uses": 14046}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 16498}, {"moveId": "WATER_PULSE", "uses": 10740}, {"moveId": "ICE_BEAM", "uses": 10319}]}, "moveset": ["BUBBLE", "AERIAL_ACE", "WATER_PULSE"], "score": 75.1}, {"speciesId": "froslass_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 731, "matchups": [{"opponent": "abomasnow_shadow", "rating": 873, "opRating": 126}, {"opponent": "gliscor", "rating": 857}, {"opponent": "drifb<PERSON>", "rating": 742, "opRating": 257}, {"opponent": "drapion_shadow", "rating": 700}, {"opponent": "gastrodon", "rating": 673, "opRating": 326}], "counters": [{"opponent": "electrode_hisuian", "rating": 186}, {"opponent": "spiritomb", "rating": 197}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "bastiodon", "rating": 302}, {"opponent": "bibarel", "rating": 305}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 18884}, {"moveId": "HEX", "uses": 18616}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 16585}, {"moveId": "SHADOW_BALL", "uses": 8041}, {"moveId": "CRUNCH", "uses": 7037}, {"moveId": "TRIPLE_AXEL", "uses": 5822}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "AVALANCHE", "SHADOW_BALL"], "score": 74.9}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 672, "matchups": [{"opponent": "abomasnow_shadow", "rating": 895, "opRating": 104}, {"opponent": "spiritomb", "rating": 780, "opRating": 219}, {"opponent": "drifb<PERSON>", "rating": 658, "opRating": 341}, {"opponent": "bastiodon", "rating": 651, "opRating": 348}, {"opponent": "gliscor", "rating": 611}], "counters": [{"opponent": "gastrodon", "rating": 270}, {"opponent": "dusknoir_shadow", "rating": 344}, {"opponent": "gallade_shadow", "rating": 350}, {"opponent": "sneasler", "rating": 361}, {"opponent": "drapion_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 28664}, {"moveId": "ROCK_SMASH", "uses": 8836}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 16895}, {"moveId": "LOW_SWEEP", "uses": 10534}, {"moveId": "RETURN", "uses": 6326}, {"moveId": "FLAME_WHEEL", "uses": 3769}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 73.2}, {"speciesId": "hippopotas_shadow", "speciesName": "Hip<PERSON><PERSON><PERSON> (Shadow)", "rating": 708, "matchups": [{"opponent": "bastiodon", "rating": 891, "opRating": 108}, {"opponent": "qwilfish_his<PERSON>an", "rating": 690, "opRating": 309}, {"opponent": "drapion_shadow", "rating": 667}, {"opponent": "spiritomb", "rating": 651, "opRating": 348}, {"opponent": "dusknoir_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "abomasnow_shadow", "rating": 136}, {"opponent": "froslass", "rating": 165}, {"opponent": "drifb<PERSON>", "rating": 356}, {"opponent": "gliscor", "rating": 366}, {"opponent": "gastrodon", "rating": 461}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 19916}, {"moveId": "TACKLE", "uses": 9580}, {"moveId": "BITE", "uses": 7969}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 14837}, {"moveId": "DIG", "uses": 12034}, {"moveId": "BODY_SLAM", "uses": 10595}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 72.6}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 714, "matchups": [{"opponent": "sneasler", "rating": 906, "opRating": 93}, {"opponent": "dusknoir_shadow", "rating": 855, "opRating": 144}, {"opponent": "gliscor", "rating": 815}, {"opponent": "abomasnow_shadow", "rating": 674, "opRating": 325}, {"opponent": "drifb<PERSON>", "rating": 624, "opRating": 375}], "counters": [{"opponent": "gallade_shadow", "rating": 125}, {"opponent": "electrode_hisuian", "rating": 160}, {"opponent": "electivire_shadow", "rating": 226}, {"opponent": "gastrodon", "rating": 389}, {"opponent": "drapion_shadow", "rating": 478}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 19561}, {"moveId": "WATER_GUN", "uses": 14994}, {"moveId": "TAKE_DOWN", "uses": 2912}], "chargedMoves": [{"moveId": "SURF", "uses": 22822}, {"moveId": "HYPER_FANG", "uses": 11487}, {"moveId": "HYPER_BEAM", "uses": 3085}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 72.4}, {"speciesId": "buneary", "speciesName": "Buneary", "rating": 643, "matchups": [{"opponent": "sneasler", "rating": 885, "opRating": 114}, {"opponent": "abomasnow_shadow", "rating": 818, "opRating": 181}, {"opponent": "hippo<PERSON><PERSON>", "rating": 655, "opRating": 344}, {"opponent": "gliscor", "rating": 533}, {"opponent": "lickilicky", "rating": 514, "opRating": 485}], "counters": [{"opponent": "bastiodon", "rating": 291}, {"opponent": "dusknoir_shadow", "rating": 322}, {"opponent": "spiritomb", "rating": 379}, {"opponent": "drifb<PERSON>", "rating": 421}, {"opponent": "drapion_shadow", "rating": 453}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 34262}, {"moveId": "POUND", "uses": 3238}], "chargedMoves": [{"moveId": "SWIFT", "uses": 20371}, {"moveId": "FIRE_PUNCH", "uses": 17129}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "FIRE_PUNCH"], "score": 72.1}, {"speciesId": "kricketune", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 722, "matchups": [{"opponent": "abomasnow_shadow", "rating": 813, "opRating": 186}, {"opponent": "gallade_shadow", "rating": 692, "opRating": 307}, {"opponent": "drapion_shadow", "rating": 647}, {"opponent": "gastrodon", "rating": 624}, {"opponent": "spiritomb", "rating": 614, "opRating": 385}], "counters": [{"opponent": "drifb<PERSON>", "rating": 234}, {"opponent": "bastiodon", "rating": 244}, {"opponent": "gliscor", "rating": 323}, {"opponent": "gliscor_shadow", "rating": 340}, {"opponent": "dusknoir_shadow", "rating": 416}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28235}, {"moveId": "STRUGGLE_BUG", "uses": 9265}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 18115}, {"moveId": "AERIAL_ACE", "uses": 14540}, {"moveId": "BUG_BUZZ", "uses": 4840}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "AERIAL_ACE"], "score": 71.7}, {"speciesId": "avalugg_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 701, "matchups": [{"opponent": "abomasnow_shadow", "rating": 872, "opRating": 127}, {"opponent": "drifb<PERSON>", "rating": 868, "opRating": 131}, {"opponent": "gliscor", "rating": 831}, {"opponent": "gliscor_shadow", "rating": 795, "opRating": 204}, {"opponent": "drapion_shadow", "rating": 504}], "counters": [{"opponent": "empoleon_shadow", "rating": 86}, {"opponent": "gallade_shadow", "rating": 221}, {"opponent": "bastiodon", "rating": 294}, {"opponent": "gastrodon", "rating": 354}, {"opponent": "spiritomb", "rating": 418}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 21383}, {"moveId": "TACKLE", "uses": 8912}, {"moveId": "BITE", "uses": 7163}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 13651}, {"moveId": "ROCK_SLIDE", "uses": 9287}, {"moveId": "CRUNCH", "uses": 8413}, {"moveId": "BLIZZARD", "uses": 6128}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ROCK_SLIDE"], "score": 71.4}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 676, "matchups": [{"opponent": "drapion_shadow", "rating": 676}, {"opponent": "qwilfish_his<PERSON>an", "rating": 639, "opRating": 360}, {"opponent": "gastrodon", "rating": 555}, {"opponent": "gliscor", "rating": 541}, {"opponent": "abomasnow_shadow", "rating": 541, "opRating": 458}], "counters": [{"opponent": "drifb<PERSON>", "rating": 263}, {"opponent": "spiritomb", "rating": 269}, {"opponent": "gallade_shadow", "rating": 278}, {"opponent": "dusknoir_shadow", "rating": 327}, {"opponent": "bastiodon", "rating": 482}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 19119}, {"moveId": "TACKLE", "uses": 18381}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21277}, {"moveId": "BULLDOZE", "uses": 11123}, {"moveId": "GUNK_SHOT", "uses": 5064}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 71}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 696, "matchups": [{"opponent": "lickilicky", "rating": 824, "opRating": 175}, {"opponent": "sneasler", "rating": 817, "opRating": 182}, {"opponent": "bastiodon", "rating": 809, "opRating": 190}, {"opponent": "spiritomb", "rating": 638, "opRating": 361}, {"opponent": "qwilfish_his<PERSON>an", "rating": 514, "opRating": 485}], "counters": [{"opponent": "abomasnow_shadow", "rating": 171}, {"opponent": "gallade_shadow", "rating": 317}, {"opponent": "drapion_shadow", "rating": 364}, {"opponent": "gastrodon", "rating": 372}, {"opponent": "gliscor", "rating": 487}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23950}, {"moveId": "SMACK_DOWN", "uses": 13550}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 9957}, {"moveId": "BREAKING_SWIPE", "uses": 6795}, {"moveId": "SUPER_POWER", "uses": 6299}, {"moveId": "SURF", "uses": 5293}, {"moveId": "EARTHQUAKE", "uses": 4313}, {"moveId": "STONE_EDGE", "uses": 2757}, {"moveId": "SKULL_BASH", "uses": 2106}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 70.5}, {"speciesId": "dusknoir", "speciesName": "Dusknoir", "rating": 767, "matchups": [{"opponent": "gallade_shadow", "rating": 861, "opRating": 138}, {"opponent": "drifb<PERSON>", "rating": 794, "opRating": 205}, {"opponent": "bastiodon", "rating": 705, "opRating": 294}, {"opponent": "sneasler", "rating": 694, "opRating": 305}, {"opponent": "gliscor", "rating": 511}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "qwilfish_his<PERSON>an", "rating": 206}, {"opponent": "spiritomb", "rating": 437}, {"opponent": "dusknoir_shadow", "rating": 438}, {"opponent": "gastrodon", "rating": 467}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 20035}, {"moveId": "ASTONISH", "uses": 17465}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 11587}, {"moveId": "DYNAMIC_PUNCH", "uses": 9646}, {"moveId": "DARK_PULSE", "uses": 4262}, {"moveId": "SHADOW_BALL", "uses": 3138}, {"moveId": "PSYCHIC", "uses": 2727}, {"moveId": "RETURN", "uses": 2464}, {"moveId": "POLTERGEIST", "uses": 2086}, {"moveId": "OMINOUS_WIND", "uses": 1605}]}, "moveset": ["ASTONISH", "DYNAMIC_PUNCH", "SHADOW_PUNCH"], "score": 70.3}, {"speciesId": "drifb<PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 714, "matchups": [{"opponent": "gliscor_shadow", "rating": 906, "opRating": 93}, {"opponent": "gliscor", "rating": 820}, {"opponent": "gallade_shadow", "rating": 767, "opRating": 232}, {"opponent": "gastrodon", "rating": 741, "opRating": 258}, {"opponent": "qwilfish_his<PERSON>an", "rating": 595, "opRating": 404}], "counters": [{"opponent": "abomasnow_shadow", "rating": 150}, {"opponent": "electrode_hisuian", "rating": 160}, {"opponent": "electivire_shadow", "rating": 226}, {"opponent": "bastiodon", "rating": 312}, {"opponent": "drapion_shadow", "rating": 330}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19162}, {"moveId": "ASTONISH", "uses": 18338}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 10904}, {"moveId": "MYSTICAL_FIRE", "uses": 9496}, {"moveId": "SHADOW_BALL", "uses": 9114}, {"moveId": "OMINOUS_WIND", "uses": 4636}, {"moveId": "RETURN", "uses": 3341}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 70.1}, {"speciesId": "toxicroak_shadow", "speciesName": "To<PERSON>croa<PERSON> (Shadow)", "rating": 797, "matchups": [{"opponent": "drapion_shadow", "rating": 872}, {"opponent": "drifb<PERSON>", "rating": 751}, {"opponent": "abomasnow_shadow", "rating": 748}, {"opponent": "spiritomb", "rating": 616}, {"opponent": "dusknoir_shadow", "rating": 507}], "counters": [{"opponent": "rhyperior_shadow", "rating": 82}, {"opponent": "uxie", "rating": 88}, {"opponent": "gastrodon", "rating": 98}, {"opponent": "gallade_shadow", "rating": 235}, {"opponent": "gliscor", "rating": 349}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 11779}, {"moveId": "MUD_SHOT", "uses": 8756}, {"moveId": "POISON_JAB", "uses": 8747}, {"moveId": "COUNTER", "uses": 8180}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 13958}, {"moveId": "MUD_BOMB", "uses": 9057}, {"moveId": "SHADOW_BALL", "uses": 8248}, {"moveId": "SLUDGE_BOMB", "uses": 6264}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "DYNAMIC_PUNCH", "SHADOW_BALL"], "score": 70}, {"speciesId": "magnezone", "speciesName": "Magnezone", "rating": 760, "matchups": [{"opponent": "drifb<PERSON>", "rating": 848}, {"opponent": "gallade_shadow", "rating": 725}, {"opponent": "spiritomb", "rating": 720}, {"opponent": "drapion_shadow", "rating": 715}, {"opponent": "dusknoir_shadow", "rating": 700}], "counters": [{"opponent": "rhyperior", "rating": 41}, {"opponent": "rhyperior_shadow", "rating": 48}, {"opponent": "gastrodon", "rating": 74}, {"opponent": "hippo<PERSON><PERSON>", "rating": 204}, {"opponent": "gliscor", "rating": 383}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12500}, {"moveId": "METAL_SOUND", "uses": 11711}, {"moveId": "SPARK", "uses": 7645}, {"moveId": "CHARGE_BEAM", "uses": 5614}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20356}, {"moveId": "MIRROR_SHOT", "uses": 9493}, {"moveId": "FLASH_CANNON", "uses": 4985}, {"moveId": "ZAP_CANNON", "uses": 2785}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 68.9}, {"speciesId": "empoleon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 743, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 913, "opRating": 86}, {"opponent": "gliscor", "rating": 831}, {"opponent": "drapion_shadow", "rating": 786}, {"opponent": "dusknoir_shadow", "rating": 594, "opRating": 405}, {"opponent": "spiritomb", "rating": 536, "opRating": 463}], "counters": [{"opponent": "gastrodon", "rating": 122}, {"opponent": "magnezone_shadow", "rating": 137}, {"opponent": "electivire_shadow", "rating": 236}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "drifb<PERSON>", "rating": 461}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 13229}, {"moveId": "WATERFALL", "uses": 12218}, {"moveId": "STEEL_WING", "uses": 12053}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 18464}, {"moveId": "DRILL_PECK", "uses": 9505}, {"moveId": "BLIZZARD", "uses": 4218}, {"moveId": "FLASH_CANNON", "uses": 3256}, {"moveId": "HYDRO_PUMP", "uses": 1959}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["METAL_CLAW", "HYDRO_CANNON", "DRILL_PECK"], "score": 68.8}, {"speciesId": "cresselia", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 681, "matchups": [{"opponent": "gallade_shadow", "rating": 790, "opRating": 209}, {"opponent": "gastrodon", "rating": 774, "opRating": 225}, {"opponent": "bibarel", "rating": 734, "opRating": 265}, {"opponent": "sneasler", "rating": 709, "opRating": 290}, {"opponent": "lickilicky", "rating": 626, "opRating": 373}], "counters": [{"opponent": "drifb<PERSON>", "rating": 277}, {"opponent": "gliscor", "rating": 340}, {"opponent": "dusknoir_shadow", "rating": 350}, {"opponent": "spiritomb", "rating": 350}, {"opponent": "drapion_shadow", "rating": 381}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 20263}, {"moveId": "CONFUSION", "uses": 17237}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 11688}, {"moveId": "MOONBLAST", "uses": 9913}, {"moveId": "FUTURE_SIGHT", "uses": 8730}, {"moveId": "AURORA_BEAM", "uses": 7182}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 68.1}, {"speciesId": "dialga_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 650, "matchups": [{"opponent": "toxicroak", "rating": 901, "opRating": 98}, {"opponent": "gallade_shadow", "rating": 883, "opRating": 116}, {"opponent": "sneasler", "rating": 700, "opRating": 299}, {"opponent": "gliscor", "rating": 584}, {"opponent": "vespiquen", "rating": 537, "opRating": 462}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 178}, {"opponent": "gastrodon", "rating": 241}, {"opponent": "drifb<PERSON>", "rating": 356}, {"opponent": "spiritomb", "rating": 418}, {"opponent": "drapion_shadow", "rating": 470}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21839}, {"moveId": "METAL_CLAW", "uses": 15661}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 15298}, {"moveId": "DRACO_METEOR", "uses": 11402}, {"moveId": "THUNDER", "uses": 10816}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_BREATH", "IRON_HEAD", "DRACO_METEOR"], "score": 67.6}, {"speciesId": "skuntank_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 703, "matchups": [{"opponent": "spiritomb", "rating": 750, "opRating": 250}, {"opponent": "drapion_shadow", "rating": 642}, {"opponent": "drifb<PERSON>", "rating": 620, "opRating": 379}, {"opponent": "dusknoir_shadow", "rating": 598, "opRating": 401}, {"opponent": "gliscor", "rating": 588}], "counters": [{"opponent": "gastrodon", "rating": 190}, {"opponent": "magnezone_shadow", "rating": 200}, {"opponent": "gliscor_shadow", "rating": 241}, {"opponent": "sneasler", "rating": 299}, {"opponent": "bastiodon", "rating": 312}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 24901}, {"moveId": "BITE", "uses": 12599}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 12928}, {"moveId": "TRAILBLAZE", "uses": 10419}, {"moveId": "FLAMETHROWER", "uses": 7429}, {"moveId": "SLUDGE_BOMB", "uses": 6636}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 67.3}, {"speciesId": "lucario", "speciesName": "<PERSON><PERSON>", "rating": 695, "matchups": [{"opponent": "bastiodon", "rating": 876, "opRating": 123}, {"opponent": "abomasnow_shadow", "rating": 853, "opRating": 146}, {"opponent": "drapion_shadow", "rating": 676, "opRating": 323}, {"opponent": "gliscor", "rating": 641, "opRating": 358}, {"opponent": "spiritomb", "rating": 623, "opRating": 376}], "counters": [{"opponent": "dusknoir", "rating": 161}, {"opponent": "gastrodon", "rating": 226}, {"opponent": "gallade_shadow", "rating": 293}, {"opponent": "drifb<PERSON>", "rating": 322}, {"opponent": "dusknoir_shadow", "rating": 383}], "moves": {"fastMoves": [{"moveId": "BULLET_PUNCH", "uses": 15467}, {"moveId": "FORCE_PALM", "uses": 13052}, {"moveId": "COUNTER", "uses": 8994}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 12110}, {"moveId": "BLAZE_KICK", "uses": 6709}, {"moveId": "THUNDER_PUNCH", "uses": 5739}, {"moveId": "SHADOW_BALL", "uses": 5315}, {"moveId": "FLASH_CANNON", "uses": 2799}, {"moveId": "AURA_SPHERE", "uses": 2740}, {"moveId": "POWER_UP_PUNCH", "uses": 2137}]}, "moveset": ["FORCE_PALM", "THUNDER_PUNCH", "SHADOW_BALL"], "score": 67.2}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 729, "matchups": [{"opponent": "sneasler", "rating": 906, "opRating": 93}, {"opponent": "dusknoir_shadow", "rating": 884, "opRating": 115}, {"opponent": "gallade_shadow", "rating": 847, "opRating": 152}, {"opponent": "drifb<PERSON>", "rating": 720, "opRating": 279}, {"opponent": "spiritomb", "rating": 526, "opRating": 473}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "lickilicky", "rating": 315}, {"opponent": "gastrodon", "rating": 339}, {"opponent": "bastiodon", "rating": 395}, {"opponent": "gliscor", "rating": 448}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 26269}, {"moveId": "SCRATCH", "uses": 11231}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 17575}, {"moveId": "THUNDER", "uses": 10670}, {"moveId": "PLAY_ROUGH", "uses": 9206}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 67.2}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 630, "matchups": [{"opponent": "qwilfish_his<PERSON>an", "rating": 670, "opRating": 329}, {"opponent": "drapion_shadow", "rating": 654}, {"opponent": "lickilicky", "rating": 563, "opRating": 436}, {"opponent": "spiritomb", "rating": 551, "opRating": 448}, {"opponent": "bastiodon", "rating": 523, "opRating": 476}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 147}, {"opponent": "drifb<PERSON>", "rating": 289}, {"opponent": "abomasnow_shadow", "rating": 314}, {"opponent": "gliscor", "rating": 405}, {"opponent": "dusknoir_shadow", "rating": 405}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 19007}, {"moveId": "MUD_SHOT", "uses": 18493}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 11743}, {"moveId": "EARTH_POWER", "uses": 11139}, {"moveId": "FIRE_BLAST", "uses": 5824}, {"moveId": "SAND_TOMB", "uses": 4685}, {"moveId": "EARTHQUAKE", "uses": 4052}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 66.9}, {"speciesId": "luxray", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 684, "matchups": [{"opponent": "sneasler", "rating": 794, "opRating": 205}, {"opponent": "drifb<PERSON>", "rating": 789}, {"opponent": "spiritomb", "rating": 659}, {"opponent": "dusknoir_shadow", "rating": 584}, {"opponent": "gallade_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "drapion_shadow", "rating": 211}, {"opponent": "gastrodon", "rating": 229}, {"opponent": "abomasnow_shadow", "rating": 311}, {"opponent": "gliscor_shadow", "rating": 366}, {"opponent": "gliscor", "rating": 465}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 5030}, {"moveId": "SPARK", "uses": 4661}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2200}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2138}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2076}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1875}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1861}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1792}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1784}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1730}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1660}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1655}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1645}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1610}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1543}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1508}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1384}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1135}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17438}, {"moveId": "PSYCHIC_FANGS", "uses": 9232}, {"moveId": "CRUNCH", "uses": 8336}, {"moveId": "HYPER_BEAM", "uses": 2555}]}, "moveset": ["SPARK", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 66.5}, {"speciesId": "magnezone_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 763, "matchups": [{"opponent": "drifb<PERSON>", "rating": 823}, {"opponent": "spiritomb", "rating": 681}, {"opponent": "drapion_shadow", "rating": 671}, {"opponent": "dusknoir_shadow", "rating": 651}, {"opponent": "gallade_shadow", "rating": 647}], "counters": [{"opponent": "rhyperior", "rating": 48}, {"opponent": "gastrodon", "rating": 62}, {"opponent": "hippo<PERSON><PERSON>", "rating": 239}, {"opponent": "gliscor", "rating": 452}, {"opponent": "abomasnow_shadow", "rating": 461}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 12971}, {"moveId": "METAL_SOUND", "uses": 11815}, {"moveId": "SPARK", "uses": 7381}, {"moveId": "CHARGE_BEAM", "uses": 5367}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20402}, {"moveId": "MIRROR_SHOT", "uses": 9443}, {"moveId": "FLASH_CANNON", "uses": 5017}, {"moveId": "ZAP_CANNON", "uses": 2791}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VOLT_SWITCH", "WILD_CHARGE", "MIRROR_SHOT"], "score": 66.2}, {"speciesId": "garcho<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 644, "matchups": [{"opponent": "electivire_shadow", "rating": 932, "opRating": 67}, {"opponent": "sneasler", "rating": 718, "opRating": 281}, {"opponent": "bastiodon", "rating": 662, "opRating": 337}, {"opponent": "empoleon_shadow", "rating": 662, "opRating": 337}, {"opponent": "spiritomb", "rating": 567, "opRating": 432}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 147}, {"opponent": "gastrodon", "rating": 348}, {"opponent": "drifb<PERSON>", "rating": 351}, {"opponent": "gliscor", "rating": 375}, {"opponent": "drapion_shadow", "rating": 385}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 18892}, {"moveId": "MUD_SHOT", "uses": 18608}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 11727}, {"moveId": "EARTH_POWER", "uses": 11148}, {"moveId": "FIRE_BLAST", "uses": 5838}, {"moveId": "SAND_TOMB", "uses": 4660}, {"moveId": "EARTHQUAKE", "uses": 4055}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 65.9}, {"speciesId": "wormadam_trash", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Trash)", "rating": 638, "matchups": [{"opponent": "abomasnow_shadow", "rating": 777, "opRating": 222}, {"opponent": "gliscor", "rating": 614, "opRating": 385}, {"opponent": "gallade_shadow", "rating": 600, "opRating": 400}, {"opponent": "drapion_shadow", "rating": 574, "opRating": 425}, {"opponent": "qwilfish_his<PERSON>an", "rating": 570, "opRating": 429}], "counters": [{"opponent": "bastiodon", "rating": 241}, {"opponent": "gliscor_shadow", "rating": 254}, {"opponent": "sneasler", "rating": 272}, {"opponent": "dusknoir_shadow", "rating": 316}, {"opponent": "drifb<PERSON>", "rating": 361}], "moves": {"fastMoves": [{"moveId": "METAL_SOUND", "uses": 14031}, {"moveId": "BUG_BITE", "uses": 13407}, {"moveId": "CONFUSION", "uses": 9973}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 16203}, {"moveId": "BUG_BUZZ", "uses": 15105}, {"moveId": "PSYBEAM", "uses": 6150}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "IRON_HEAD"], "score": 65.9}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 690, "matchups": [{"opponent": "drifb<PERSON>", "rating": 864, "opRating": 135}, {"opponent": "gallade_shadow", "rating": 812, "opRating": 187}, {"opponent": "gliscor", "rating": 786}, {"opponent": "dusknoir_shadow", "rating": 734}, {"opponent": "gastrodon", "rating": 520}], "counters": [{"opponent": "spiritomb", "rating": 177}, {"opponent": "drapion_shadow", "rating": 194}, {"opponent": "electrode_hisuian", "rating": 239}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 247}, {"opponent": "lickilicky", "rating": 275}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 23176}, {"moveId": "SNARL", "uses": 14324}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 12196}, {"moveId": "FOUL_PLAY", "uses": 11310}, {"moveId": "FLAMETHROWER", "uses": 8387}, {"moveId": "SLUDGE_BOMB", "uses": 5685}]}, "moveset": ["SHADOW_CLAW", "SHADOW_BALL", "FOUL_PLAY"], "score": 65.7}, {"speciesId": "weavile", "speciesName": "Weavile", "rating": 647, "matchups": [{"opponent": "drifb<PERSON>", "rating": 882}, {"opponent": "abomasnow_shadow", "rating": 844, "opRating": 155}, {"opponent": "dusknoir_shadow", "rating": 768}, {"opponent": "spiritomb", "rating": 665}, {"opponent": "gliscor", "rating": 655}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 56}, {"opponent": "bastiodon", "rating": 187}, {"opponent": "bibarel", "rating": 335}, {"opponent": "gastrodon", "rating": 398}, {"opponent": "drapion_shadow", "rating": 495}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15075}, {"moveId": "ICE_SHARD", "uses": 13698}, {"moveId": "FEINT_ATTACK", "uses": 8734}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 16403}, {"moveId": "FOUL_PLAY", "uses": 10354}, {"moveId": "TRIPLE_AXEL", "uses": 5744}, {"moveId": "FOCUS_BLAST", "uses": 5034}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 65.5}, {"speciesId": "drifb<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 676, "matchups": [{"opponent": "gallade_shadow", "rating": 880, "opRating": 119}, {"opponent": "gastrodon", "rating": 856, "opRating": 143}, {"opponent": "vespiquen", "rating": 815, "opRating": 184}, {"opponent": "gliscor", "rating": 732}, {"opponent": "gliscor_shadow", "rating": 700, "opRating": 299}], "counters": [{"opponent": "drapion_shadow", "rating": 207}, {"opponent": "qwilfish_his<PERSON>an", "rating": 235}, {"opponent": "bibarel", "rating": 305}, {"opponent": "abomasnow_shadow", "rating": 318}, {"opponent": "spiritomb", "rating": 331}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19383}, {"moveId": "ASTONISH", "uses": 18117}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 12029}, {"moveId": "MYSTICAL_FIRE", "uses": 10496}, {"moveId": "SHADOW_BALL", "uses": 9940}, {"moveId": "OMINOUS_WIND", "uses": 5031}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "ICY_WIND", "SHADOW_BALL"], "score": 65.2}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 694, "matchups": [{"opponent": "bastiodon", "rating": 877, "opRating": 122}, {"opponent": "sneasler", "rating": 836, "opRating": 163}, {"opponent": "qwilfish_his<PERSON>an", "rating": 782, "opRating": 217}, {"opponent": "spiritomb", "rating": 711, "opRating": 288}, {"opponent": "drapion_shadow", "rating": 693}], "counters": [{"opponent": "gliscor", "rating": 232}, {"opponent": "drifb<PERSON>", "rating": 258}, {"opponent": "gallade_shadow", "rating": 269}, {"opponent": "vespiquen", "rating": 276}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 6383}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2824}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2677}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2506}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2080}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2016}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2008}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1904}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1868}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1820}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1816}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1765}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1757}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1725}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1721}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1482}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1336}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 11995}, {"moveId": "BODY_SLAM", "uses": 11043}, {"moveId": "WATER_PULSE", "uses": 10075}, {"moveId": "EARTHQUAKE", "uses": 4397}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 65.2}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 594, "matchups": [{"opponent": "gallade_shadow", "rating": 873, "opRating": 126}, {"opponent": "spiritomb", "rating": 839, "opRating": 160}, {"opponent": "sneasler", "rating": 778, "opRating": 221}, {"opponent": "gliscor", "rating": 691}, {"opponent": "gastrodon", "rating": 691, "opRating": 308}], "counters": [{"opponent": "bastiodon", "rating": 176}, {"opponent": "bronzong", "rating": 179}, {"opponent": "abomasnow_shadow", "rating": 339}, {"opponent": "drifb<PERSON>", "rating": 346}, {"opponent": "drapion_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 3538}, {"moveId": "CHARM", "uses": 2988}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2601}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2396}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2318}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2130}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2115}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2086}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1972}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1865}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1846}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1812}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1790}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1752}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1752}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1699}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1509}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1320}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 8040}, {"moveId": "PSYSHOCK", "uses": 7755}, {"moveId": "DAZZLING_GLEAM", "uses": 6058}, {"moveId": "AURA_SPHERE", "uses": 5901}, {"moveId": "ANCIENT_POWER", "uses": 5049}, {"moveId": "FLAMETHROWER", "uses": 4621}]}, "moveset": ["CHARM", "PSYSHOCK", "AURA_SPHERE"], "score": 65.2}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 676, "matchups": [{"opponent": "gliscor_shadow", "rating": 815, "opRating": 184}, {"opponent": "abomasnow_shadow", "rating": 731, "opRating": 268}, {"opponent": "drifb<PERSON>", "rating": 697, "opRating": 302}, {"opponent": "gliscor", "rating": 563}, {"opponent": "qwilfish_his<PERSON>an", "rating": 550, "opRating": 449}], "counters": [{"opponent": "gallade_shadow", "rating": 100}, {"opponent": "gastrodon", "rating": 321}, {"opponent": "spiritomb", "rating": 384}, {"opponent": "dusknoir_shadow", "rating": 438}, {"opponent": "drapion_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 19311}, {"moveId": "WATER_GUN", "uses": 15029}, {"moveId": "TAKE_DOWN", "uses": 3154}], "chargedMoves": [{"moveId": "SURF", "uses": 21100}, {"moveId": "HYPER_FANG", "uses": 10230}, {"moveId": "RETURN", "uses": 3501}, {"moveId": "HYPER_BEAM", "uses": 2702}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 64.9}, {"speciesId": "giratina_origin", "speciesName": "<PERSON><PERSON><PERSON> (Origin)", "rating": 696, "matchups": [{"opponent": "gallade_shadow", "rating": 732, "opRating": 267}, {"opponent": "gliscor", "rating": 676}, {"opponent": "gastrodon", "rating": 663, "opRating": 336}, {"opponent": "drifb<PERSON>", "rating": 532, "opRating": 467}, {"opponent": "dusknoir_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 169}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 221}, {"opponent": "drapion_shadow", "rating": 288}, {"opponent": "lickilicky", "rating": 303}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 20495}, {"moveId": "DRAGON_TAIL", "uses": 17005}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 16574}, {"moveId": "DRAGON_PULSE", "uses": 10754}, {"moveId": "OMINOUS_WIND", "uses": 8477}, {"moveId": "SHADOW_FORCE", "uses": 1703}]}, "moveset": ["SHADOW_CLAW", "OMINOUS_WIND", "SHADOW_BALL"], "score": 64.9}, {"speciesId": "floatzel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "gliscor", "rating": 770}, {"opponent": "gliscor_shadow", "rating": 695, "opRating": 304}, {"opponent": "bastiodon", "rating": 657, "opRating": 342}, {"opponent": "spiritomb", "rating": 582, "opRating": 417}, {"opponent": "dusknoir_shadow", "rating": 548, "opRating": 451}], "counters": [{"opponent": "vespiquen", "rating": 242}, {"opponent": "drapion_shadow", "rating": 245}, {"opponent": "qwilfish_his<PERSON>an", "rating": 264}, {"opponent": "gallade_shadow", "rating": 331}, {"opponent": "sneasler", "rating": 361}], "moves": {"fastMoves": [{"moveId": "WATERFALL", "uses": 18758}, {"moveId": "WATER_GUN", "uses": 18742}], "chargedMoves": [{"moveId": "AQUA_JET", "uses": 18276}, {"moveId": "SWIFT", "uses": 10103}, {"moveId": "LIQUIDATION", "uses": 6545}, {"moveId": "HYDRO_PUMP", "uses": 2501}]}, "moveset": ["WATER_GUN", "AQUA_JET", "SWIFT"], "score": 64.4}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 687, "matchups": [{"opponent": "gliscor", "rating": 756}, {"opponent": "drifb<PERSON>", "rating": 663}, {"opponent": "spiritomb", "rating": 606}, {"opponent": "dusknoir_shadow", "rating": 561}, {"opponent": "drapion_shadow", "rating": 539}], "counters": [{"opponent": "gastrodon", "rating": 83}, {"opponent": "rhyperior", "rating": 104}, {"opponent": "hippo<PERSON><PERSON>", "rating": 257}, {"opponent": "bibarel", "rating": 385}, {"opponent": "gallade_shadow", "rating": 408}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 23679}, {"moveId": "BUG_BITE", "uses": 13821}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 13159}, {"moveId": "EARTH_POWER", "uses": 7050}, {"moveId": "STONE_EDGE", "uses": 6388}, {"moveId": "IRON_HEAD", "uses": 5505}, {"moveId": "FLAMETHROWER", "uses": 3567}, {"moveId": "FIRE_BLAST", "uses": 1892}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 64.1}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 743, "matchups": [{"opponent": "abomasnow_shadow", "rating": 865, "opRating": 134}, {"opponent": "sneasler", "rating": 745, "opRating": 254}, {"opponent": "gallade_shadow", "rating": 638, "opRating": 361}, {"opponent": "gliscor", "rating": 541}, {"opponent": "spiritomb", "rating": 513}], "counters": [{"opponent": "drapion_shadow", "rating": 173}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "hippo<PERSON><PERSON>", "rating": 330}, {"opponent": "dusknoir_shadow", "rating": 372}, {"opponent": "drifb<PERSON>", "rating": 394}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 19784}, {"moveId": "FIRE_SPIN", "uses": 17716}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 12841}, {"moveId": "SCORCHING_SANDS", "uses": 8114}, {"moveId": "BRICK_BREAK", "uses": 5507}, {"moveId": "THUNDERBOLT", "uses": 5223}, {"moveId": "PSYCHIC", "uses": 3763}, {"moveId": "FIRE_BLAST", "uses": 2184}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 63.9}, {"speciesId": "monferno_shadow", "speciesName": "Mon<PERSON> (Shadow)", "rating": 681, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "spiritomb", "rating": 737, "opRating": 262}, {"opponent": "bastiodon", "rating": 640, "opRating": 359}, {"opponent": "drifb<PERSON>", "rating": 604, "opRating": 395}, {"opponent": "qwilfish_his<PERSON>an", "rating": 503, "opRating": 496}], "counters": [{"opponent": "gastrodon", "rating": 181}, {"opponent": "drapion_shadow", "rating": 275}, {"opponent": "gliscor", "rating": 353}, {"opponent": "gallade_shadow", "rating": 394}, {"opponent": "dusknoir_shadow", "rating": 416}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 28838}, {"moveId": "ROCK_SMASH", "uses": 8662}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 20184}, {"moveId": "LOW_SWEEP", "uses": 12766}, {"moveId": "FLAME_WHEEL", "uses": 4547}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 63.7}, {"speciesId": "staravia", "speciesName": "Staravia", "rating": 689, "matchups": [{"opponent": "sneasler", "rating": 885, "opRating": 114}, {"opponent": "dusknoir_shadow", "rating": 740, "opRating": 259}, {"opponent": "gastrodon", "rating": 714, "opRating": 285}, {"opponent": "gliscor", "rating": 637}, {"opponent": "drapion_shadow", "rating": 514}], "counters": [{"opponent": "bastiodon", "rating": 104}, {"opponent": "electrode_hisuian", "rating": 160}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "toxicroak", "rating": 236}, {"opponent": "gallade_shadow", "rating": 264}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13299}, {"moveId": "SAND_ATTACK", "uses": 13079}, {"moveId": "WING_ATTACK", "uses": 11155}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 12751}, {"moveId": "FLY", "uses": 10830}, {"moveId": "AERIAL_ACE", "uses": 8270}, {"moveId": "RETURN", "uses": 3679}, {"moveId": "HEAT_WAVE", "uses": 2012}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 63}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 683, "matchups": [{"opponent": "abomasnow_shadow", "rating": 941, "opRating": 58}, {"opponent": "gliscor", "rating": 752}, {"opponent": "dusknoir_shadow", "rating": 617}, {"opponent": "drapion_shadow", "rating": 599}, {"opponent": "spiritomb", "rating": 554}], "counters": [{"opponent": "gastrodon", "rating": 86}, {"opponent": "hippo<PERSON><PERSON>", "rating": 214}, {"opponent": "bibarel", "rating": 335}, {"opponent": "gallade_shadow", "rating": 350}, {"opponent": "drifb<PERSON>", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 23006}, {"moveId": "BUG_BITE", "uses": 14494}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 13211}, {"moveId": "EARTH_POWER", "uses": 7052}, {"moveId": "STONE_EDGE", "uses": 6366}, {"moveId": "IRON_HEAD", "uses": 5474}, {"moveId": "FLAMETHROWER", "uses": 3568}, {"moveId": "FIRE_BLAST", "uses": 1888}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 62.1}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 637, "matchups": [{"opponent": "sneasler", "rating": 708, "opRating": 291}, {"opponent": "qwilfish_his<PERSON>an", "rating": 611, "opRating": 388}, {"opponent": "gliscor", "rating": 600, "opRating": 399}, {"opponent": "drapion_shadow", "rating": 597, "opRating": 402}, {"opponent": "gliscor_shadow", "rating": 565, "opRating": 434}], "counters": [{"opponent": "froslass", "rating": 88}, {"opponent": "abomasnow_shadow", "rating": 269}, {"opponent": "spiritomb", "rating": 278}, {"opponent": "dusknoir_shadow", "rating": 327}, {"opponent": "drifb<PERSON>", "rating": 330}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 28367}, {"moveId": "ROCK_SMASH", "uses": 9133}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 6618}, {"moveId": "SWIFT", "uses": 6161}, {"moveId": "FIRE_PUNCH", "uses": 5405}, {"moveId": "TRAILBLAZE", "uses": 5310}, {"moveId": "HIGH_HORSEPOWER", "uses": 5256}, {"moveId": "THUNDER_PUNCH", "uses": 4633}, {"moveId": "AERIAL_ACE", "uses": 4173}]}, "moveset": ["TACKLE", "ICE_PUNCH", "SWIFT"], "score": 62.1}, {"speciesId": "cress<PERSON>a_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 678, "matchups": [{"opponent": "gastrodon", "rating": 824, "opRating": 175}, {"opponent": "gallade_shadow", "rating": 762, "opRating": 237}, {"opponent": "lickilicky", "rating": 682, "opRating": 317}, {"opponent": "bibarel", "rating": 679, "opRating": 320}, {"opponent": "sneasler", "rating": 657, "opRating": 342}], "counters": [{"opponent": "vespiquen", "rating": 94}, {"opponent": "drifb<PERSON>", "rating": 208}, {"opponent": "gliscor", "rating": 323}, {"opponent": "dusknoir_shadow", "rating": 372}, {"opponent": "drapion_shadow", "rating": 423}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 19958}, {"moveId": "CONFUSION", "uses": 17542}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 11642}, {"moveId": "MOONBLAST", "uses": 9909}, {"moveId": "FUTURE_SIGHT", "uses": 8718}, {"moveId": "AURORA_BEAM", "uses": 7175}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["PSYCHO_CUT", "GRASS_KNOT", "MOONBLAST"], "score": 61.5}, {"speciesId": "prin<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 589, "matchups": [{"opponent": "bastiodon", "rating": 735, "opRating": 264}, {"opponent": "gliscor", "rating": 694}, {"opponent": "drifb<PERSON>", "rating": 595, "opRating": 404}, {"opponent": "qwilfish_his<PERSON>an", "rating": 588, "opRating": 411}, {"opponent": "drapion_shadow", "rating": 514}], "counters": [{"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 143}, {"opponent": "gallade_shadow", "rating": 235}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "sneasler", "rating": 290}, {"opponent": "gastrodon", "rating": 375}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 22340}, {"moveId": "METAL_CLAW", "uses": 15160}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 17201}, {"moveId": "HYDRO_PUMP", "uses": 8485}, {"moveId": "BUBBLE_BEAM", "uses": 6295}, {"moveId": "RETURN", "uses": 5475}]}, "moveset": ["BUBBLE", "ICY_WIND", "HYDRO_PUMP"], "score": 61.5}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 751, "matchups": [{"opponent": "abomasnow_shadow", "rating": 865, "opRating": 134}, {"opponent": "gallade_shadow", "rating": 833, "opRating": 166}, {"opponent": "empoleon_shadow", "rating": 833, "opRating": 166}, {"opponent": "vespiquen", "rating": 810, "opRating": 189}, {"opponent": "sneasler", "rating": 768, "opRating": 231}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 169}, {"opponent": "drapion_shadow", "rating": 173}, {"opponent": "gastrodon", "rating": 270}, {"opponent": "spiritomb", "rating": 350}, {"opponent": "gliscor", "rating": 396}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 19917}, {"moveId": "FIRE_SPIN", "uses": 17583}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 12807}, {"moveId": "SCORCHING_SANDS", "uses": 8123}, {"moveId": "BRICK_BREAK", "uses": 5491}, {"moveId": "THUNDERBOLT", "uses": 5235}, {"moveId": "PSYCHIC", "uses": 3754}, {"moveId": "FIRE_BLAST", "uses": 2194}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 60.6}, {"speciesId": "skuntank", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 706, "matchups": [{"opponent": "dusknoir_shadow", "rating": 667, "opRating": 332}, {"opponent": "drifb<PERSON>", "rating": 667, "opRating": 332}, {"opponent": "abomasnow_shadow", "rating": 604, "opRating": 395}, {"opponent": "gliscor_shadow", "rating": 588, "opRating": 411}, {"opponent": "spiritomb", "rating": 503, "opRating": 496}], "counters": [{"opponent": "gastrodon", "rating": 178}, {"opponent": "bastiodon", "rating": 244}, {"opponent": "sneasler", "rating": 276}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "gliscor", "rating": 362}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 23619}, {"moveId": "BITE", "uses": 13881}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 11957}, {"moveId": "TRAILBLAZE", "uses": 9503}, {"moveId": "FLAMETHROWER", "uses": 6706}, {"moveId": "SLUDGE_BOMB", "uses": 5893}, {"moveId": "RETURN", "uses": 3352}]}, "moveset": ["POISON_JAB", "CRUNCH", "TRAILBLAZE"], "score": 60.6}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 642, "matchups": [{"opponent": "bastiodon", "rating": 722, "opRating": 277}, {"opponent": "gastrodon", "rating": 664, "opRating": 335}, {"opponent": "gallade_shadow", "rating": 652, "opRating": 347}, {"opponent": "sneasler", "rating": 566, "opRating": 433}, {"opponent": "qwilfish_his<PERSON>an", "rating": 503, "opRating": 496}], "counters": [{"opponent": "froslass", "rating": 173}, {"opponent": "drifb<PERSON>", "rating": 198}, {"opponent": "vespiquen", "rating": 246}, {"opponent": "gliscor", "rating": 271}, {"opponent": "drapion_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 21605}, {"moveId": "CONFUSION", "uses": 15895}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 15535}, {"moveId": "BULLDOZE", "uses": 14970}, {"moveId": "PSYBEAM", "uses": 6987}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BULLDOZE"], "score": 59.8}, {"speciesId": "luxray_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 656, "matchups": [{"opponent": "drifb<PERSON>", "rating": 794, "opRating": 205}, {"opponent": "spiritomb", "rating": 617, "opRating": 382}, {"opponent": "dusknoir_shadow", "rating": 533, "opRating": 466}, {"opponent": "gliscor", "rating": 521, "opRating": 478}, {"opponent": "drapion_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 82}, {"opponent": "gallade_shadow", "rating": 149}, {"opponent": "gastrodon", "rating": 223}, {"opponent": "bastiodon", "rating": 294}, {"opponent": "abomasnow_shadow", "rating": 314}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 5155}, {"moveId": "SPARK", "uses": 4832}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2211}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2126}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2064}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1841}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1836}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1801}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1768}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1709}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1634}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1615}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1601}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1594}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1525}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1475}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1354}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1121}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17443}, {"moveId": "PSYCHIC_FANGS", "uses": 9215}, {"moveId": "CRUNCH", "uses": 8298}, {"moveId": "HYPER_BEAM", "uses": 2544}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 59.2}, {"speciesId": "rhyperior_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 682, "matchups": [{"opponent": "bastiodon", "rating": 847, "opRating": 152}, {"opponent": "sneasler", "rating": 839, "opRating": 160}, {"opponent": "dusknoir_shadow", "rating": 638, "opRating": 361}, {"opponent": "gliscor", "rating": 593, "opRating": 406}, {"opponent": "spiritomb", "rating": 593, "opRating": 406}], "counters": [{"opponent": "vespiquen", "rating": 94}, {"opponent": "gliscor_shadow", "rating": 176}, {"opponent": "abomasnow_shadow", "rating": 213}, {"opponent": "gastrodon", "rating": 252}, {"opponent": "drifb<PERSON>", "rating": 272}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 24031}, {"moveId": "SMACK_DOWN", "uses": 13469}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 9910}, {"moveId": "BREAKING_SWIPE", "uses": 6771}, {"moveId": "SUPER_POWER", "uses": 6299}, {"moveId": "SURF", "uses": 5291}, {"moveId": "EARTHQUAKE", "uses": 4314}, {"moveId": "STONE_EDGE", "uses": 2762}, {"moveId": "SKULL_BASH", "uses": 2084}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 59.2}, {"speciesId": "p<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 659, "matchups": [{"opponent": "drifb<PERSON>", "rating": 873, "opRating": 126}, {"opponent": "sneasler", "rating": 781, "opRating": 218}, {"opponent": "gallade_shadow", "rating": 732, "opRating": 267}, {"opponent": "vespiquen", "rating": 697, "opRating": 302}, {"opponent": "qwilfish_his<PERSON>an", "rating": 556, "opRating": 443}], "counters": [{"opponent": "hippo<PERSON><PERSON>", "rating": 193}, {"opponent": "gastrodon", "rating": 226}, {"opponent": "gliscor", "rating": 228}, {"opponent": "abomasnow_shadow", "rating": 325}, {"opponent": "drapion_shadow", "rating": 402}], "moves": {"fastMoves": [{"moveId": "VOLT_SWITCH", "uses": 22000}, {"moveId": "SPARK", "uses": 15500}], "chargedMoves": [{"moveId": "THUNDER_PUNCH", "uses": 23741}, {"moveId": "THUNDERBOLT", "uses": 7379}, {"moveId": "THUNDER", "uses": 6400}]}, "moveset": ["VOLT_SWITCH", "THUNDER_PUNCH", "THUNDERBOLT"], "score": 58.9}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 643, "matchups": [{"opponent": "gallade_shadow", "rating": 892, "opRating": 107}, {"opponent": "gastrodon", "rating": 872, "opRating": 127}, {"opponent": "bibarel", "rating": 872, "opRating": 127}, {"opponent": "lickilicky", "rating": 686, "opRating": 313}, {"opponent": "spiritomb", "rating": 612, "opRating": 387}], "counters": [{"opponent": "drifb<PERSON>", "rating": 186}, {"opponent": "vespiquen", "rating": 193}, {"opponent": "drapion_shadow", "rating": 305}, {"opponent": "abomasnow_shadow", "rating": 307}, {"opponent": "gliscor", "rating": 344}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 15945}, {"moveId": "QUICK_ATTACK", "uses": 14091}, {"moveId": "RAZOR_LEAF", "uses": 7472}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 26031}, {"moveId": "LAST_RESORT", "uses": 5618}, {"moveId": "ENERGY_BALL", "uses": 3636}, {"moveId": "SOLAR_BEAM", "uses": 2174}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 58.8}, {"speciesId": "lumineon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 545, "matchups": [{"opponent": "gliscor_shadow", "rating": 799, "opRating": 200}, {"opponent": "qwilfish_his<PERSON>an", "rating": 620, "opRating": 379}, {"opponent": "drapion_shadow", "rating": 598}, {"opponent": "drifb<PERSON>", "rating": 565, "opRating": 434}, {"opponent": "gliscor", "rating": 510}], "counters": [{"opponent": "abomasnow_shadow", "rating": 178}, {"opponent": "vespiquen", "rating": 246}, {"opponent": "dusknoir_shadow", "rating": 283}, {"opponent": "sneasler", "rating": 312}, {"opponent": "gastrodon", "rating": 386}], "moves": {"fastMoves": [{"moveId": "WATER_GUN", "uses": 18961}, {"moveId": "WATERFALL", "uses": 18539}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 15183}, {"moveId": "SILVER_WIND", "uses": 11146}, {"moveId": "BLIZZARD", "uses": 11113}]}, "moveset": ["WATER_GUN", "WATER_PULSE", "SILVER_WIND"], "score": 58.3}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 651, "matchups": [{"opponent": "gliscor_shadow", "rating": 818, "opRating": 181}, {"opponent": "bastiodon", "rating": 814, "opRating": 185}, {"opponent": "lickilicky", "rating": 750, "opRating": 250}, {"opponent": "abomasnow_shadow", "rating": 645, "opRating": 354}, {"opponent": "qwilfish_his<PERSON>an", "rating": 592, "opRating": 407}], "counters": [{"opponent": "gallade_shadow", "rating": 134}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gastrodon", "rating": 306}, {"opponent": "spiritomb", "rating": 322}, {"opponent": "gliscor", "rating": 482}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 30855}, {"moveId": "LOW_KICK", "uses": 4190}, {"moveId": "POUND", "uses": 2458}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 14258}, {"moveId": "FIRE_PUNCH", "uses": 12209}, {"moveId": "FOCUS_BLAST", "uses": 6552}, {"moveId": "HYPER_BEAM", "uses": 4465}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FOCUS_BLAST"], "score": 58}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 681, "matchups": [{"opponent": "sneasler", "rating": 876, "opRating": 124}, {"opponent": "gastrodon", "rating": 671, "opRating": 328}, {"opponent": "qwilfish_his<PERSON>an", "rating": 612, "opRating": 388}, {"opponent": "gliscor", "rating": 552}, {"opponent": "drifb<PERSON>", "rating": 548, "opRating": 452}], "counters": [{"opponent": "magnezone_shadow", "rating": 181}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "drapion_shadow", "rating": 194}, {"opponent": "gallade_shadow", "rating": 264}, {"opponent": "spiritomb", "rating": 360}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 10174}, {"moveId": "QUICK_ATTACK", "uses": 10157}, {"moveId": "GUST", "uses": 8982}, {"moveId": "WING_ATTACK", "uses": 8234}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 13174}, {"moveId": "CLOSE_COMBAT", "uses": 11600}, {"moveId": "FLY", "uses": 11157}, {"moveId": "HEAT_WAVE", "uses": 1593}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 58}, {"speciesId": "prinplup_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 560, "matchups": [{"opponent": "bastiodon", "rating": 731, "opRating": 268}, {"opponent": "lickilicky", "rating": 720, "opRating": 279}, {"opponent": "gliscor", "rating": 621}, {"opponent": "gliscor_shadow", "rating": 551, "opRating": 448}, {"opponent": "drapion_shadow", "rating": 536}], "counters": [{"opponent": "electrode_hisuian", "rating": 134}, {"opponent": "qwilfish_his<PERSON>an", "rating": 235}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "gallade_shadow", "rating": 293}, {"opponent": "gastrodon", "rating": 404}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 22554}, {"moveId": "METAL_CLAW", "uses": 14946}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 20216}, {"moveId": "HYDRO_PUMP", "uses": 9942}, {"moveId": "BUBBLE_BEAM", "uses": 7274}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUBBLE", "ICY_WIND", "HYDRO_PUMP"], "score": 57.3}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 571, "matchups": [{"opponent": "gallade_shadow", "rating": 866, "opRating": 133}, {"opponent": "dusknoir_shadow", "rating": 826, "opRating": 173}, {"opponent": "drifb<PERSON>", "rating": 697, "opRating": 302}, {"opponent": "sneasler", "rating": 665, "opRating": 334}, {"opponent": "abomasnow_shadow", "rating": 532, "opRating": 467}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "gliscor_shadow", "rating": 275}, {"opponent": "bastiodon", "rating": 316}, {"opponent": "gastrodon", "rating": 404}, {"opponent": "gliscor", "rating": 426}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 25071}, {"moveId": "SCRATCH", "uses": 12429}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 16969}, {"moveId": "LOW_SWEEP", "uses": 8627}, {"moveId": "RETURN", "uses": 8555}, {"moveId": "HYPER_BEAM", "uses": 3302}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 57.2}, {"speciesId": "staravia_shadow", "speciesName": "Staravia (Shadow)", "rating": 664, "matchups": [{"opponent": "sneasler", "rating": 885, "opRating": 114}, {"opponent": "dusknoir_shadow", "rating": 814, "opRating": 185}, {"opponent": "gastrodon", "rating": 659, "opRating": 340}, {"opponent": "gliscor", "rating": 607, "opRating": 392}, {"opponent": "spiritomb", "rating": 588, "opRating": 411}], "counters": [{"opponent": "bastiodon", "rating": 104}, {"opponent": "abomasnow_shadow", "rating": 223}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "drifb<PERSON>", "rating": 325}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 13304}, {"moveId": "SAND_ATTACK", "uses": 12994}, {"moveId": "WING_ATTACK", "uses": 11172}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 14102}, {"moveId": "FLY", "uses": 12006}, {"moveId": "AERIAL_ACE", "uses": 9137}, {"moveId": "HEAT_WAVE", "uses": 2228}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 57.2}, {"speciesId": "stunky_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 564, "matchups": [{"opponent": "dusknoir_shadow", "rating": 792, "opRating": 207}, {"opponent": "dusknoir", "rating": 758, "opRating": 241}, {"opponent": "empoleon_shadow", "rating": 670, "opRating": 329}, {"opponent": "drifb<PERSON>", "rating": 636, "opRating": 363}, {"opponent": "bastiodon", "rating": 520, "opRating": 479}], "counters": [{"opponent": "vespiquen", "rating": 250}, {"opponent": "gliscor", "rating": 284}, {"opponent": "gastrodon", "rating": 285}, {"opponent": "abomasnow_shadow", "rating": 332}, {"opponent": "drapion_shadow", "rating": 334}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 24313}, {"moveId": "SCRATCH", "uses": 13187}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 12941}, {"moveId": "TRAILBLAZE", "uses": 10408}, {"moveId": "FLAMETHROWER", "uses": 7474}, {"moveId": "SLUDGE_BOMB", "uses": 6621}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 57}, {"speciesId": "drifloon", "speciesName": "Drifloon", "rating": 659, "matchups": [{"opponent": "gliscor_shadow", "rating": 895, "opRating": 104}, {"opponent": "gliscor", "rating": 793, "opRating": 206}, {"opponent": "gallade_shadow", "rating": 768, "opRating": 231}, {"opponent": "gastrodon", "rating": 715, "opRating": 284}, {"opponent": "drifb<PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "qwilfish_his<PERSON>an", "rating": 119}, {"opponent": "abomasnow_shadow", "rating": 150}, {"opponent": "sneasler", "rating": 223}, {"opponent": "bastiodon", "rating": 287}, {"opponent": "drapion_shadow", "rating": 300}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19002}, {"moveId": "ASTONISH", "uses": 18498}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 14362}, {"moveId": "SHADOW_BALL", "uses": 12192}, {"moveId": "OMINOUS_WIND", "uses": 6279}, {"moveId": "RETURN", "uses": 4760}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 56.9}, {"speciesId": "drifloon_shadow", "speciesName": "Dr<PERSON><PERSON><PERSON> (Shadow)", "rating": 664, "matchups": [{"opponent": "gliscor", "rating": 895, "opRating": 104}, {"opponent": "gliscor_shadow", "rating": 895, "opRating": 104}, {"opponent": "gastrodon", "rating": 806, "opRating": 193}, {"opponent": "gallade_shadow", "rating": 720, "opRating": 279}, {"opponent": "lickilicky", "rating": 575, "opRating": 424}], "counters": [{"opponent": "spiritomb", "rating": 168}, {"opponent": "abomasnow_shadow", "rating": 171}, {"opponent": "bastiodon", "rating": 251}, {"opponent": "drapion_shadow", "rating": 296}, {"opponent": "dusknoir_shadow", "rating": 305}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19140}, {"moveId": "ASTONISH", "uses": 18360}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 16743}, {"moveId": "SHADOW_BALL", "uses": 13701}, {"moveId": "OMINOUS_WIND", "uses": 6997}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "ICY_WIND", "SHADOW_BALL"], "score": 56.7}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 627, "matchups": [{"opponent": "sneasler", "rating": 876, "opRating": 124}, {"opponent": "gliscor", "rating": 836, "opRating": 164}, {"opponent": "gastrodon", "rating": 828, "opRating": 172}, {"opponent": "dusknoir_shadow", "rating": 800, "opRating": 200}, {"opponent": "qwilfish_his<PERSON>an", "rating": 536, "opRating": 464}], "counters": [{"opponent": "bastiodon", "rating": 82}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "abomasnow_shadow", "rating": 255}, {"opponent": "gallade_shadow", "rating": 307}, {"opponent": "drifb<PERSON>", "rating": 354}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 10335}, {"moveId": "SAND_ATTACK", "uses": 10308}, {"moveId": "GUST", "uses": 8693}, {"moveId": "WING_ATTACK", "uses": 8142}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 13162}, {"moveId": "CLOSE_COMBAT", "uses": 11595}, {"moveId": "FLY", "uses": 11199}, {"moveId": "HEAT_WAVE", "uses": 1567}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 55.3}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 598, "matchups": [{"opponent": "gallade_shadow", "rating": 875, "opRating": 125}, {"opponent": "dusknoir_shadow", "rating": 826, "opRating": 173}, {"opponent": "drifb<PERSON>", "rating": 661, "opRating": 338}, {"opponent": "sneasler", "rating": 620, "opRating": 379}, {"opponent": "bastiodon", "rating": 560, "opRating": 439}], "counters": [{"opponent": "drapion_shadow", "rating": 182}, {"opponent": "vespiquen", "rating": 242}, {"opponent": "lickilicky", "rating": 259}, {"opponent": "gliscor", "rating": 275}, {"opponent": "abomasnow_shadow", "rating": 297}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 25190}, {"moveId": "SCRATCH", "uses": 12310}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 19576}, {"moveId": "LOW_SWEEP", "uses": 9878}, {"moveId": "HYPER_BEAM", "uses": 8032}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 55}, {"speciesId": "bonsly", "speciesName": "Bon<PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "vespiquen", "rating": 775, "opRating": 224}, {"opponent": "abomasnow_shadow", "rating": 653, "opRating": 346}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "froslass", "rating": 614, "opRating": 385}, {"opponent": "drifb<PERSON>", "rating": 559, "opRating": 440}], "counters": [{"opponent": "gallade_shadow", "rating": 221}, {"opponent": "sneasler", "rating": 250}, {"opponent": "gastrodon", "rating": 342}, {"opponent": "gliscor", "rating": 366}, {"opponent": "drapion_shadow", "rating": 389}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 19152}, {"moveId": "COUNTER", "uses": 18348}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 17734}, {"moveId": "ROCK_SLIDE", "uses": 11388}, {"moveId": "EARTHQUAKE", "uses": 8450}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 54.7}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 716, "matchups": [{"opponent": "vespiquen", "rating": 907, "opRating": 92}, {"opponent": "abomasnow_shadow", "rating": 847, "opRating": 152}, {"opponent": "drifb<PERSON>", "rating": 717, "opRating": 282}, {"opponent": "gallade_shadow", "rating": 643, "opRating": 356}, {"opponent": "sneasler", "rating": 620, "opRating": 379}], "counters": [{"opponent": "gastrodon", "rating": 145}, {"opponent": "spiritomb", "rating": 149}, {"opponent": "bastiodon", "rating": 215}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "gliscor", "rating": 435}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 19319}, {"moveId": "EMBER", "uses": 18181}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 10402}, {"moveId": "FIRE_PUNCH", "uses": 9993}, {"moveId": "NIGHT_SHADE", "uses": 7785}, {"moveId": "OVERHEAT", "uses": 5782}, {"moveId": "SHADOW_BALL", "uses": 3628}]}, "moveset": ["HEX", "FIRE_PUNCH", "WILD_CHARGE"], "score": 54.5}, {"speciesId": "skorup<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 577, "matchups": [{"opponent": "toxicroak_shadow", "rating": 849, "opRating": 150}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 747, "opRating": 252}, {"opponent": "gallade_shadow", "rating": 703, "opRating": 296}, {"opponent": "abomasnow", "rating": 592, "opRating": 407}, {"opponent": "abomasnow_shadow", "rating": 539, "opRating": 460}], "counters": [{"opponent": "dusknoir_shadow", "rating": 266}, {"opponent": "drifb<PERSON>", "rating": 299}, {"opponent": "gastrodon", "rating": 318}, {"opponent": "spiritomb", "rating": 365}, {"opponent": "drapion_shadow", "rating": 394}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 20981}, {"moveId": "INFESTATION", "uses": 16519}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 19794}, {"moveId": "CROSS_POISON", "uses": 13986}, {"moveId": "SLUDGE_BOMB", "uses": 3727}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 54.1}, {"speciesId": "snea<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 660, "matchups": [{"opponent": "lickilicky", "rating": 734, "opRating": 265}, {"opponent": "drapion_shadow", "rating": 653, "opRating": 346}, {"opponent": "abomasnow_shadow", "rating": 545, "opRating": 454}, {"opponent": "qwilfish_his<PERSON>an", "rating": 522, "opRating": 477}, {"opponent": "bastiodon", "rating": 518, "opRating": 481}], "counters": [{"opponent": "gastrodon", "rating": 151}, {"opponent": "gliscor_shadow", "rating": 211}, {"opponent": "sneasler", "rating": 227}, {"opponent": "drifb<PERSON>", "rating": 311}, {"opponent": "gliscor", "rating": 426}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 28608}, {"moveId": "ROCK_SMASH", "uses": 8892}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 17129}, {"moveId": "AERIAL_ACE", "uses": 9794}, {"moveId": "X_SCISSOR", "uses": 7485}, {"moveId": "RETURN", "uses": 3112}]}, "moveset": ["POISON_JAB", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 53.5}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 625, "matchups": [{"opponent": "bibarel", "rating": 867, "opRating": 132}, {"opponent": "gastrodon", "rating": 841, "opRating": 158}, {"opponent": "empoleon_shadow", "rating": 775, "opRating": 224}, {"opponent": "dusknoir_shadow", "rating": 687, "opRating": 312}, {"opponent": "lickilicky", "rating": 540, "opRating": 459}], "counters": [{"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "gallade_shadow", "rating": 221}, {"opponent": "drapion_shadow", "rating": 330}, {"opponent": "drifb<PERSON>", "rating": 334}, {"opponent": "gliscor", "rating": 431}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 22699}, {"moveId": "INFESTATION", "uses": 14801}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 14258}, {"moveId": "ROCK_SLIDE", "uses": 9494}, {"moveId": "SLUDGE_BOMB", "uses": 5847}, {"moveId": "ANCIENT_POWER", "uses": 4862}, {"moveId": "SOLAR_BEAM", "uses": 2947}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 53.4}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 685, "matchups": [{"opponent": "bibarel", "rating": 899, "opRating": 100}, {"opponent": "sneasler", "rating": 826, "opRating": 173}, {"opponent": "bastiodon", "rating": 754, "opRating": 245}, {"opponent": "abomasnow_shadow", "rating": 669, "opRating": 330}, {"opponent": "gastrodon", "rating": 524, "opRating": 475}], "counters": [{"opponent": "bronzong", "rating": 45}, {"opponent": "qwilfish_his<PERSON>an", "rating": 86}, {"opponent": "gliscor", "rating": 232}, {"opponent": "drifb<PERSON>", "rating": 260}, {"opponent": "dusknoir_shadow", "rating": 294}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 19284}, {"moveId": "MAGICAL_LEAF", "uses": 18216}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 10517}, {"moveId": "AURA_SPHERE", "uses": 8721}, {"moveId": "AERIAL_ACE", "uses": 7952}, {"moveId": "NIGHT_SHADE", "uses": 7194}, {"moveId": "ENERGY_BALL", "uses": 3052}]}, "moveset": ["PSYCHO_CUT", "AERIAL_ACE", "AURA_SPHERE"], "score": 53.1}, {"speciesId": "glaceon", "speciesName": "Glaceon", "rating": 553, "matchups": [{"opponent": "gliscor", "rating": 792, "opRating": 207}, {"opponent": "drifb<PERSON>", "rating": 702, "opRating": 297}, {"opponent": "abomasnow_shadow", "rating": 702, "opRating": 297}, {"opponent": "spiritomb", "rating": 611, "opRating": 388}, {"opponent": "gastrodon", "rating": 580, "opRating": 419}], "counters": [{"opponent": "bastiodon", "rating": 230}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "dusknoir_shadow", "rating": 277}, {"opponent": "gallade_shadow", "rating": 293}, {"opponent": "qwilfish_his<PERSON>an", "rating": 301}], "moves": {"fastMoves": [{"moveId": "ICE_SHARD", "uses": 23435}, {"moveId": "FROST_BREATH", "uses": 14065}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 18078}, {"moveId": "ICY_WIND", "uses": 6280}, {"moveId": "WATER_PULSE", "uses": 5108}, {"moveId": "ICE_BEAM", "uses": 4096}, {"moveId": "LAST_RESORT", "uses": 3998}]}, "moveset": ["ICE_SHARD", "AVALANCHE", "ICY_WIND"], "score": 53.1}, {"speciesId": "s<PERSON><PERSON><PERSON>", "speciesName": "Skorupi", "rating": 548, "matchups": [{"opponent": "electrode_hisuian", "rating": 849, "opRating": 150}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 778, "opRating": 221}, {"opponent": "gallade_shadow", "rating": 681, "opRating": 318}, {"opponent": "abomasnow_shadow", "rating": 623, "opRating": 376}, {"opponent": "toxicroak", "rating": 579, "opRating": 420}], "counters": [{"opponent": "dusknoir_shadow", "rating": 244}, {"opponent": "gastrodon", "rating": 270}, {"opponent": "drifb<PERSON>", "rating": 275}, {"opponent": "spiritomb", "rating": 288}, {"opponent": "drapion_shadow", "rating": 351}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 21531}, {"moveId": "INFESTATION", "uses": 15969}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 17853}, {"moveId": "CROSS_POISON", "uses": 12553}, {"moveId": "RETURN", "uses": 3666}, {"moveId": "SLUDGE_BOMB", "uses": 3354}]}, "moveset": ["POISON_STING", "AQUA_TAIL", "CROSS_POISON"], "score": 52.8}, {"speciesId": "uxie", "speciesName": "Uxie", "rating": 615, "matchups": [{"opponent": "sneasler", "rating": 826, "opRating": 173}, {"opponent": "gallade_shadow", "rating": 750, "opRating": 250}, {"opponent": "bibarel", "rating": 641, "opRating": 358}, {"opponent": "lickilicky", "rating": 572, "opRating": 427}, {"opponent": "gastrodon", "rating": 528, "opRating": 471}], "counters": [{"opponent": "spiritomb", "rating": 125}, {"opponent": "drifb<PERSON>", "rating": 234}, {"opponent": "dusknoir_shadow", "rating": 255}, {"opponent": "drapion_shadow", "rating": 347}, {"opponent": "gliscor", "rating": 349}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 20888}, {"moveId": "EXTRASENSORY", "uses": 16612}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17029}, {"moveId": "FUTURE_SIGHT", "uses": 10586}, {"moveId": "THUNDER", "uses": 9864}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 52.1}, {"speciesId": "stunky", "speciesName": "Stunky", "rating": 524, "matchups": [{"opponent": "dusknoir", "rating": 806, "opRating": 193}, {"opponent": "dusknoir_shadow", "rating": 758, "opRating": 241}, {"opponent": "drifb<PERSON>", "rating": 663, "opRating": 336}, {"opponent": "bronzong", "rating": 629, "opRating": 370}, {"opponent": "froslass", "rating": 527, "opRating": 472}], "counters": [{"opponent": "drapion_shadow", "rating": 224}, {"opponent": "lickilicky", "rating": 237}, {"opponent": "gastrodon", "rating": 267}, {"opponent": "qwilfish_his<PERSON>an", "rating": 293}, {"opponent": "gliscor", "rating": 405}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 24263}, {"moveId": "SCRATCH", "uses": 13237}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 11938}, {"moveId": "TRAILBLAZE", "uses": 9531}, {"moveId": "FLAMETHROWER", "uses": 6725}, {"moveId": "SLUDGE_BOMB", "uses": 5938}, {"moveId": "RETURN", "uses": 3325}]}, "moveset": ["BITE", "CRUNCH", "TRAILBLAZE"], "score": 51.8}, {"speciesId": "shellos", "speciesName": "<PERSON><PERSON>", "rating": 571, "matchups": [{"opponent": "sneasler", "rating": 834, "opRating": 165}, {"opponent": "qwilfish_his<PERSON>an", "rating": 789, "opRating": 210}, {"opponent": "bastiodon", "rating": 743, "opRating": 256}, {"opponent": "drapion_shadow", "rating": 614, "opRating": 385}, {"opponent": "gallade_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "vespiquen", "rating": 166}, {"opponent": "drifb<PERSON>", "rating": 186}, {"opponent": "gliscor", "rating": 219}, {"opponent": "abomasnow_shadow", "rating": 293}, {"opponent": "gastrodon", "rating": 306}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 5188}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2794}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2593}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2191}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2186}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2090}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2082}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2067}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2054}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1953}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1917}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1908}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1870}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1865}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1829}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1565}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1432}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 14550}, {"moveId": "BODY_SLAM", "uses": 12042}, {"moveId": "WATER_PULSE", "uses": 10892}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "BODY_SLAM"], "score": 51.6}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 577, "matchups": [{"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "bastiodon", "rating": 743, "opRating": 256}, {"opponent": "spiritomb", "rating": 701, "opRating": 298}, {"opponent": "vespiquen", "rating": 634, "opRating": 365}, {"opponent": "gliscor", "rating": 546, "opRating": 453}], "counters": [{"opponent": "gastrodon", "rating": 145}, {"opponent": "bibarel", "rating": 218}, {"opponent": "drapion_shadow", "rating": 237}, {"opponent": "drifb<PERSON>", "rating": 279}, {"opponent": "dusknoir_shadow", "rating": 355}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31159}, {"moveId": "ROCK_SMASH", "uses": 6341}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15839}, {"moveId": "CLOSE_COMBAT", "uses": 14844}, {"moveId": "FLAMETHROWER", "uses": 3638}, {"moveId": "SOLAR_BEAM", "uses": 3159}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 50.9}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 585, "matchups": [{"opponent": "abomasnow_shadow", "rating": 890, "opRating": 109}, {"opponent": "empoleon_shadow", "rating": 743, "opRating": 256}, {"opponent": "bastiodon", "rating": 693, "opRating": 306}, {"opponent": "spiritomb", "rating": 642, "opRating": 357}, {"opponent": "lickilicky", "rating": 508, "opRating": 491}], "counters": [{"opponent": "gastrodon", "rating": 136}, {"opponent": "drapion_shadow", "rating": 279}, {"opponent": "gliscor", "rating": 306}, {"opponent": "drifb<PERSON>", "rating": 327}, {"opponent": "dusknoir_shadow", "rating": 338}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31770}, {"moveId": "ROCK_SMASH", "uses": 5730}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15819}, {"moveId": "CLOSE_COMBAT", "uses": 14817}, {"moveId": "FLAMETHROWER", "uses": 3635}, {"moveId": "SOLAR_BEAM", "uses": 3159}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 50.3}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 514, "matchups": [{"opponent": "bibarel", "rating": 921, "opRating": 78}, {"opponent": "gastrodon", "rating": 890, "opRating": 109}, {"opponent": "bastiodon", "rating": 733, "opRating": 266}, {"opponent": "empoleon_shadow", "rating": 706, "opRating": 293}, {"opponent": "gallade_shadow", "rating": 586, "opRating": 413}], "counters": [{"opponent": "vespiquen", "rating": 117}, {"opponent": "abomasnow_shadow", "rating": 220}, {"opponent": "drifb<PERSON>", "rating": 224}, {"opponent": "gliscor", "rating": 306}, {"opponent": "drapion_shadow", "rating": 309}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20731}, {"moveId": "BITE", "uses": 16769}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 16476}, {"moveId": "STONE_EDGE", "uses": 8510}, {"moveId": "EARTHQUAKE", "uses": 6800}, {"moveId": "SAND_TOMB", "uses": 3941}, {"moveId": "SOLAR_BEAM", "uses": 1838}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 50.2}, {"speciesId": "rotom", "speciesName": "Rotom", "rating": 607, "matchups": [{"opponent": "gallade_shadow", "rating": 863, "opRating": 136}, {"opponent": "empoleon_shadow", "rating": 768, "opRating": 231}, {"opponent": "gliscor_shadow", "rating": 622, "opRating": 377}, {"opponent": "gliscor", "rating": 617, "opRating": 382}, {"opponent": "drifb<PERSON>", "rating": 514, "opRating": 485}], "counters": [{"opponent": "lickilicky", "rating": 103}, {"opponent": "drapion_shadow", "rating": 182}, {"opponent": "qwilfish_his<PERSON>an", "rating": 206}, {"opponent": "gastrodon", "rating": 252}, {"opponent": "spiritomb", "rating": 264}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 19656}, {"moveId": "THUNDER_SHOCK", "uses": 17844}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16512}, {"moveId": "OMINOUS_WIND", "uses": 13836}, {"moveId": "THUNDER", "uses": 7153}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 49.6}, {"speciesId": "snea<PERSON>_<PERSON><PERSON>an_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) (<PERSON>)", "rating": 642, "matchups": [{"opponent": "bibarel", "rating": 869, "opRating": 130}, {"opponent": "bastiodon", "rating": 837, "opRating": 162}, {"opponent": "vespiquen", "rating": 815, "opRating": 184}, {"opponent": "abomasnow_shadow", "rating": 779, "opRating": 220}, {"opponent": "drapion_shadow", "rating": 612, "opRating": 387}], "counters": [{"opponent": "gastrodon", "rating": 145}, {"opponent": "qwilfish_his<PERSON>an", "rating": 210}, {"opponent": "gliscor", "rating": 211}, {"opponent": "spiritomb", "rating": 254}, {"opponent": "sneasler", "rating": 272}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29047}, {"moveId": "ROCK_SMASH", "uses": 8453}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 18500}, {"moveId": "AERIAL_ACE", "uses": 10817}, {"moveId": "X_SCISSOR", "uses": 8135}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_JAB", "CLOSE_COMBAT", "AERIAL_ACE"], "score": 49.4}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 592, "matchups": [{"opponent": "abomasnow_shadow", "rating": 903, "opRating": 96}, {"opponent": "magnezone_shadow", "rating": 810, "opRating": 189}, {"opponent": "gliscor", "rating": 670, "opRating": 329}, {"opponent": "gliscor_shadow", "rating": 647, "opRating": 352}, {"opponent": "spiritomb", "rating": 593, "opRating": 406}], "counters": [{"opponent": "gastrodon", "rating": 92}, {"opponent": "qwilfish_his<PERSON>an", "rating": 268}, {"opponent": "drapion_shadow", "rating": 275}, {"opponent": "drifb<PERSON>", "rating": 303}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 17644}, {"moveId": "SNARL", "uses": 15374}, {"moveId": "ROCK_SMASH", "uses": 4480}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 12353}, {"moveId": "FLAMETHROWER", "uses": 8640}, {"moveId": "ROCK_SLIDE", "uses": 8453}, {"moveId": "CRUNCH", "uses": 7995}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "ROCK_SLIDE"], "score": 48.6}, {"speciesId": "growl<PERSON>e_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 629, "matchups": [{"opponent": "abomasnow_shadow", "rating": 897, "opRating": 102}, {"opponent": "gliscor_shadow", "rating": 693, "opRating": 306}, {"opponent": "gliscor", "rating": 690, "opRating": 309}, {"opponent": "drifb<PERSON>", "rating": 654, "opRating": 345}, {"opponent": "spiritomb", "rating": 584, "opRating": 415}], "counters": [{"opponent": "gastrodon", "rating": 92}, {"opponent": "bibarel", "rating": 187}, {"opponent": "qwilfish_his<PERSON>an", "rating": 235}, {"opponent": "drapion_shadow", "rating": 241}, {"opponent": "dusknoir_shadow", "rating": 311}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 27884}, {"moveId": "BITE", "uses": 9616}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 13105}, {"moveId": "FLAMETHROWER", "uses": 12364}, {"moveId": "CRUNCH", "uses": 12007}]}, "moveset": ["EMBER", "ROCK_SLIDE", "FLAMETHROWER"], "score": 48.6}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 531, "matchups": [{"opponent": "gastrodon", "rating": 897, "opRating": 102}, {"opponent": "electivire_shadow", "rating": 882, "opRating": 117}, {"opponent": "bibarel", "rating": 868, "opRating": 131}, {"opponent": "gallade_shadow", "rating": 663, "opRating": 336}, {"opponent": "bastiodon", "rating": 599, "opRating": 400}], "counters": [{"opponent": "vespiquen", "rating": 117}, {"opponent": "abomasnow_shadow", "rating": 223}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "drifb<PERSON>", "rating": 241}, {"opponent": "gliscor", "rating": 306}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20581}, {"moveId": "BITE", "uses": 16919}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16869}, {"moveId": "ENERGY_BALL", "uses": 15899}, {"moveId": "SOLAR_BEAM", "uses": 4692}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 48.3}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 583, "matchups": [{"opponent": "gallade_shadow", "rating": 865, "opRating": 134}, {"opponent": "drifb<PERSON>", "rating": 806, "opRating": 193}, {"opponent": "sneasler", "rating": 779, "opRating": 220}, {"opponent": "qwilfish_his<PERSON>an", "rating": 645, "opRating": 354}, {"opponent": "dusknoir_shadow", "rating": 618, "opRating": 381}], "counters": [{"opponent": "abomasnow_shadow", "rating": 97}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "hippo<PERSON><PERSON>", "rating": 197}, {"opponent": "gliscor", "rating": 262}, {"opponent": "gastrodon", "rating": 282}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 19516}, {"moveId": "ASTONISH", "uses": 17984}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18187}, {"moveId": "OMINOUS_WIND", "uses": 11363}, {"moveId": "THUNDER", "uses": 7923}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 47.4}, {"speciesId": "gallade", "speciesName": "Gallade", "rating": 769, "matchups": [{"opponent": "lickilicky", "rating": 923, "opRating": 76}, {"opponent": "abomasnow_shadow", "rating": 826, "opRating": 173}, {"opponent": "gastrodon", "rating": 802, "opRating": 197}, {"opponent": "bastiodon", "rating": 793, "opRating": 206}, {"opponent": "sneasler", "rating": 725, "opRating": 274}], "counters": [{"opponent": "bronzong", "rating": 45}, {"opponent": "drapion_shadow", "rating": 80}, {"opponent": "qwilfish_his<PERSON>an", "rating": 86}, {"opponent": "vespiquen", "rating": 181}, {"opponent": "gliscor", "rating": 323}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 17907}, {"moveId": "CONFUSION", "uses": 11445}, {"moveId": "CHARM", "uses": 5735}, {"moveId": "LOW_KICK", "uses": 2415}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 14499}, {"moveId": "LEAF_BLADE", "uses": 13531}, {"moveId": "SYNCHRONOISE", "uses": 7163}, {"moveId": "PSYCHIC", "uses": 2233}]}, "moveset": ["PSYCHO_CUT", "CLOSE_COMBAT", "LEAF_BLADE"], "score": 47.1}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 558, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 765, "opRating": 234}, {"opponent": "abomasnow_shadow", "rating": 719, "opRating": 280}, {"opponent": "gastrodon", "rating": 711, "opRating": 288}, {"opponent": "spiritomb", "rating": 646, "opRating": 353}, {"opponent": "lickilicky", "rating": 626, "opRating": 373}], "counters": [{"opponent": "gallade_shadow", "rating": 149}, {"opponent": "gliscor", "rating": 241}, {"opponent": "drapion_shadow", "rating": 245}, {"opponent": "drifb<PERSON>", "rating": 303}, {"opponent": "dusknoir_shadow", "rating": 333}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 25093}, {"moveId": "RAZOR_LEAF", "uses": 12407}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 19143}, {"moveId": "DAZZLING_GLEAM", "uses": 7838}, {"moveId": "SOLAR_BEAM", "uses": 7098}, {"moveId": "HYPER_BEAM", "uses": 3395}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 46.8}, {"speciesId": "weavile_shadow", "speciesName": "<PERSON><PERSON>le (Shadow)", "rating": 630, "matchups": [{"opponent": "drifb<PERSON>", "rating": 853, "opRating": 146}, {"opponent": "abomasnow_shadow", "rating": 844, "opRating": 155}, {"opponent": "dusknoir_shadow", "rating": 712, "opRating": 287}, {"opponent": "spiritomb", "rating": 627, "opRating": 372}, {"opponent": "gliscor", "rating": 570}], "counters": [{"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 56}, {"opponent": "drapion_shadow", "rating": 88}, {"opponent": "bastiodon", "rating": 197}, {"opponent": "electivire_shadow", "rating": 226}, {"opponent": "gastrodon", "rating": 473}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 15239}, {"moveId": "ICE_SHARD", "uses": 13861}, {"moveId": "FEINT_ATTACK", "uses": 8389}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 16401}, {"moveId": "FOUL_PLAY", "uses": 10369}, {"moveId": "TRIPLE_AXEL", "uses": 5714}, {"moveId": "FOCUS_BLAST", "uses": 5016}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "AVALANCHE", "FOUL_PLAY"], "score": 46.5}, {"speciesId": "bastiodon_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 608, "matchups": [{"opponent": "abomasnow_shadow", "rating": 834, "opRating": 165}, {"opponent": "drifb<PERSON>", "rating": 687, "opRating": 312}, {"opponent": "spiritomb", "rating": 557, "opRating": 442}, {"opponent": "bastiodon", "rating": 539, "opRating": 460}, {"opponent": "qwilfish_his<PERSON>an", "rating": 525, "opRating": 474}], "counters": [{"opponent": "gastrodon", "rating": 163}, {"opponent": "gallade_shadow", "rating": 177}, {"opponent": "sneasler", "rating": 191}, {"opponent": "gliscor", "rating": 237}, {"opponent": "dusknoir_shadow", "rating": 272}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 23284}, {"moveId": "IRON_TAIL", "uses": 14216}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 18039}, {"moveId": "FLAMETHROWER", "uses": 11600}, {"moveId": "FLASH_CANNON", "uses": 7824}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "STONE_EDGE", "FLAMETHROWER"], "score": 46.2}, {"speciesId": "pip<PERSON>p", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 480, "matchups": [{"opponent": "gliscor", "rating": 755, "opRating": 244}, {"opponent": "gliscor_shadow", "rating": 709, "opRating": 290}, {"opponent": "magmortar", "rating": 679, "opRating": 320}, {"opponent": "sneasler", "rating": 606, "opRating": 393}, {"opponent": "hippo<PERSON><PERSON>", "rating": 564, "opRating": 435}], "counters": [{"opponent": "gallade_shadow", "rating": 173}, {"opponent": "qwilfish_his<PERSON>an", "rating": 177}, {"opponent": "dusknoir_shadow", "rating": 200}, {"opponent": "gastrodon", "rating": 300}, {"opponent": "drapion_shadow", "rating": 351}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 35224}, {"moveId": "POUND", "uses": 2276}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 14006}, {"moveId": "ICY_WIND", "uses": 13612}, {"moveId": "BUBBLE_BEAM", "uses": 5819}, {"moveId": "RETURN", "uses": 4018}]}, "moveset": ["BUBBLE", "DRILL_PECK", "ICY_WIND"], "score": 46.2}, {"speciesId": "rotom_fan", "speciesName": "<PERSON><PERSON><PERSON> (Fan)", "rating": 504, "matchups": [{"opponent": "dusknoir_shadow", "rating": 618, "opRating": 381}, {"opponent": "gliscor", "rating": 586, "opRating": 413}, {"opponent": "gallade_shadow", "rating": 548, "opRating": 451}, {"opponent": "spiritomb", "rating": 543, "opRating": 456}, {"opponent": "drifb<PERSON>", "rating": 526, "opRating": 473}], "counters": [{"opponent": "bibarel", "rating": 97}, {"opponent": "drapion_shadow", "rating": 182}, {"opponent": "qwilfish_his<PERSON>an", "rating": 202}, {"opponent": "abomasnow_shadow", "rating": 234}, {"opponent": "vespiquen", "rating": 246}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 21038}, {"moveId": "AIR_SLASH", "uses": 16462}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 18192}, {"moveId": "OMINOUS_WIND", "uses": 11380}, {"moveId": "THUNDER", "uses": 7907}]}, "moveset": ["ASTONISH", "THUNDERBOLT", "OMINOUS_WIND"], "score": 45.9}, {"speciesId": "rampardos_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 496, "matchups": [{"opponent": "drifb<PERSON>", "rating": 757, "opRating": 242}, {"opponent": "abomasnow_shadow", "rating": 757, "opRating": 242}, {"opponent": "froslass", "rating": 757, "opRating": 242}, {"opponent": "vespiquen", "rating": 718, "opRating": 281}, {"opponent": "electivire_shadow", "rating": 678, "opRating": 321}], "counters": [{"opponent": "gastrodon", "rating": 163}, {"opponent": "bastiodon", "rating": 165}, {"opponent": "gallade_shadow", "rating": 269}, {"opponent": "drapion_shadow", "rating": 322}, {"opponent": "gliscor", "rating": 366}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 31561}, {"moveId": "ZEN_HEADBUTT", "uses": 5939}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 16321}, {"moveId": "FLAMETHROWER", "uses": 11192}, {"moveId": "OUTRAGE", "uses": 10004}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "FLAMETHROWER"], "score": 45.8}, {"speciesId": "bastiodon", "speciesName": "Bastiodon", "rating": 621, "matchups": [{"opponent": "abomasnow_shadow", "rating": 726, "opRating": 273}, {"opponent": "drifb<PERSON>", "rating": 687, "opRating": 312}, {"opponent": "qwilfish_his<PERSON>an", "rating": 593, "opRating": 406}, {"opponent": "spiritomb", "rating": 582, "opRating": 417}, {"opponent": "drapion_shadow", "rating": 575, "opRating": 424}], "counters": [{"opponent": "gastrodon", "rating": 136}, {"opponent": "gallade_shadow", "rating": 149}, {"opponent": "sneasler", "rating": 160}, {"opponent": "gliscor", "rating": 262}, {"opponent": "dusknoir_shadow", "rating": 283}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 23125}, {"moveId": "IRON_TAIL", "uses": 14375}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15503}, {"moveId": "FLAMETHROWER", "uses": 9956}, {"moveId": "FLASH_CANNON", "uses": 6638}, {"moveId": "RETURN", "uses": 5407}]}, "moveset": ["SMACK_DOWN", "STONE_EDGE", "FLAMETHROWER"], "score": 45.7}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 620, "matchups": [{"opponent": "sneasler", "rating": 858, "opRating": 141}, {"opponent": "gallade_shadow", "rating": 790, "opRating": 209}, {"opponent": "gliscor_shadow", "rating": 666, "opRating": 333}, {"opponent": "dusknoir_shadow", "rating": 615, "opRating": 384}, {"opponent": "drifb<PERSON>", "rating": 602, "opRating": 397}], "counters": [{"opponent": "vespiquen", "rating": 83}, {"opponent": "abomasnow_shadow", "rating": 136}, {"opponent": "bibarel", "rating": 151}, {"opponent": "spiritomb", "rating": 192}, {"opponent": "gliscor", "rating": 379}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 6761}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2388}, {"moveId": "CHARGE_BEAM", "uses": 2196}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2166}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 1973}, {"moveId": "HIDDEN_POWER_WATER", "uses": 1929}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1902}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1843}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1833}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1768}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1749}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1726}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1702}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1648}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1622}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1546}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1387}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1241}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 10620}, {"moveId": "BLIZZARD", "uses": 8722}, {"moveId": "ZAP_CANNON", "uses": 7026}, {"moveId": "SOLAR_BEAM", "uses": 5874}, {"moveId": "HYPER_BEAM", "uses": 5278}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 45.7}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 526, "matchups": [{"opponent": "gastrodon", "rating": 765, "opRating": 234}, {"opponent": "magnezone_shadow", "rating": 656, "opRating": 343}, {"opponent": "gallade_shadow", "rating": 648, "opRating": 351}, {"opponent": "lickilicky", "rating": 535, "opRating": 464}, {"opponent": "drapion_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "gliscor", "rating": 185}, {"opponent": "gliscor_shadow", "rating": 185}, {"opponent": "sneasler", "rating": 191}, {"opponent": "drifb<PERSON>", "rating": 267}, {"opponent": "dusknoir_shadow", "rating": 316}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 21815}, {"moveId": "CONFUSION", "uses": 15685}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 17093}, {"moveId": "BUG_BUZZ", "uses": 14157}, {"moveId": "PSYBEAM", "uses": 6272}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 45.5}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 481, "matchups": [{"opponent": "electivire_shadow", "rating": 928, "opRating": 71}, {"opponent": "bibarel", "rating": 917, "opRating": 82}, {"opponent": "hippo<PERSON><PERSON>", "rating": 913, "opRating": 86}, {"opponent": "gastrodon", "rating": 906, "opRating": 93}, {"opponent": "bastiodon", "rating": 511, "opRating": 488}], "counters": [{"opponent": "vespiquen", "rating": 117}, {"opponent": "drifb<PERSON>", "rating": 179}, {"opponent": "abomasnow_shadow", "rating": 209}, {"opponent": "gliscor", "rating": 262}, {"opponent": "drapion_shadow", "rating": 279}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20305}, {"moveId": "BITE", "uses": 17195}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 16471}, {"moveId": "STONE_EDGE", "uses": 8519}, {"moveId": "EARTHQUAKE", "uses": 6792}, {"moveId": "SAND_TOMB", "uses": 3942}, {"moveId": "SOLAR_BEAM", "uses": 1844}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 45.4}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 502, "matchups": [{"opponent": "gastrodon", "rating": 911, "opRating": 88}, {"opponent": "bibarel", "rating": 872, "opRating": 127}, {"opponent": "hippo<PERSON><PERSON>", "rating": 787, "opRating": 212}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 698, "opRating": 301}, {"opponent": "electivire_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "vespiquen", "rating": 117}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "drifb<PERSON>", "rating": 236}, {"opponent": "drapion_shadow", "rating": 258}, {"opponent": "gliscor", "rating": 262}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 20167}, {"moveId": "BITE", "uses": 17333}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 14870}, {"moveId": "ENERGY_BALL", "uses": 14643}, {"moveId": "SOLAR_BEAM", "uses": 4312}, {"moveId": "RETURN", "uses": 3725}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 45.2}, {"speciesId": "luxio", "speciesName": "Luxio", "rating": 540, "matchups": [{"opponent": "empoleon_shadow", "rating": 798, "opRating": 201}, {"opponent": "drifb<PERSON>", "rating": 607, "opRating": 392}, {"opponent": "spiritomb", "rating": 575, "opRating": 424}, {"opponent": "bastiodon", "rating": 535, "opRating": 464}, {"opponent": "drapion_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gastrodon", "rating": 133}, {"opponent": "qwilfish_his<PERSON>an", "rating": 210}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "gallade_shadow", "rating": 264}, {"opponent": "gliscor", "rating": 366}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 25915}, {"moveId": "BITE", "uses": 11585}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 19633}, {"moveId": "CRUNCH", "uses": 10091}, {"moveId": "RETURN", "uses": 4184}, {"moveId": "THUNDERBOLT", "uses": 3574}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 44.8}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 595, "matchups": [{"opponent": "bibarel", "rating": 841, "opRating": 158}, {"opponent": "gastrodon", "rating": 790, "opRating": 209}, {"opponent": "bastiodon", "rating": 643, "opRating": 356}, {"opponent": "dusknoir_shadow", "rating": 610, "opRating": 389}, {"opponent": "spiritomb", "rating": 566, "opRating": 433}], "counters": [{"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "qwilfish_his<PERSON>an", "rating": 169}, {"opponent": "sneasler", "rating": 183}, {"opponent": "gallade_shadow", "rating": 264}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 23014}, {"moveId": "INFESTATION", "uses": 14486}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 14294}, {"moveId": "ROCK_SLIDE", "uses": 9737}, {"moveId": "SLUDGE_BOMB", "uses": 5864}, {"moveId": "ANCIENT_POWER", "uses": 4625}, {"moveId": "SOLAR_BEAM", "uses": 2944}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 44.5}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 576, "matchups": [{"opponent": "sneasler", "rating": 928, "opRating": 71}, {"opponent": "toxicroak", "rating": 910, "opRating": 89}, {"opponent": "drifb<PERSON>", "rating": 674, "opRating": 325}, {"opponent": "bibarel", "rating": 592, "opRating": 407}, {"opponent": "abomasnow_shadow", "rating": 551, "opRating": 448}], "counters": [{"opponent": "spiritomb", "rating": 134}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "bastiodon", "rating": 269}, {"opponent": "gliscor", "rating": 288}, {"opponent": "dusknoir_shadow", "rating": 372}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19361}, {"moveId": "TACKLE", "uses": 15011}, {"moveId": "ZEN_HEADBUTT", "uses": 3108}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 15373}, {"moveId": "STOMP", "uses": 8373}, {"moveId": "MEGAHORN", "uses": 7601}, {"moveId": "PSYCHIC", "uses": 6114}]}, "moveset": ["CONFUSION", "WILD_CHARGE", "STOMP"], "score": 44.3}, {"speciesId": "chatot", "speciesName": "Chatot", "rating": 457, "matchups": [{"opponent": "giratina_origin", "rating": 863, "opRating": 136}, {"opponent": "drifb<PERSON>_shadow", "rating": 782, "opRating": 217}, {"opponent": "gliscor", "rating": 574, "opRating": 425}, {"opponent": "drifb<PERSON>", "rating": 537, "opRating": 462}, {"opponent": "dusknoir", "rating": 527, "opRating": 472}], "counters": [{"opponent": "empoleon_shadow", "rating": 151}, {"opponent": "bibarel", "rating": 187}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "bastiodon", "rating": 237}, {"opponent": "dusknoir_shadow", "rating": 338}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 20855}, {"moveId": "PECK", "uses": 16645}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 16652}, {"moveId": "NIGHT_SHADE", "uses": 16147}, {"moveId": "HEAT_WAVE", "uses": 4699}]}, "moveset": ["STEEL_WING", "SKY_ATTACK", "NIGHT_SHADE"], "score": 43.3}, {"speciesId": "mesprit", "speciesName": "Me<PERSON>rit", "rating": 559, "matchups": [{"opponent": "toxicroak", "rating": 875, "opRating": 125}, {"opponent": "sneasler", "rating": 823, "opRating": 176}, {"opponent": "gallade_shadow", "rating": 668, "opRating": 331}, {"opponent": "bibarel", "rating": 543, "opRating": 456}, {"opponent": "lickilicky", "rating": 538, "opRating": 461}], "counters": [{"opponent": "spiritomb", "rating": 139}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "qwilfish_his<PERSON>an", "rating": 190}, {"opponent": "drifb<PERSON>", "rating": 289}, {"opponent": "dusknoir_shadow", "rating": 305}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 21005}, {"moveId": "EXTRASENSORY", "uses": 16495}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16440}, {"moveId": "FUTURE_SIGHT", "uses": 10655}, {"moveId": "BLIZZARD", "uses": 10403}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 42.6}, {"speciesId": "yanmega", "speciesName": "Yanmega", "rating": 582, "matchups": [{"opponent": "gastrodon", "rating": 756, "opRating": 244}, {"opponent": "gallade_shadow", "rating": 728, "opRating": 272}, {"opponent": "abomasnow_shadow", "rating": 675, "opRating": 324}, {"opponent": "toxicroak", "rating": 620, "opRating": 380}, {"opponent": "spiritomb", "rating": 612, "opRating": 388}], "counters": [{"opponent": "drifb<PERSON>", "rating": 136}, {"opponent": "bastiodon", "rating": 147}, {"opponent": "gliscor", "rating": 193}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drapion_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 19433}, {"moveId": "WING_ATTACK", "uses": 18067}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 17010}, {"moveId": "ANCIENT_POWER", "uses": 10567}, {"moveId": "BUG_BUZZ", "uses": 9907}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "ANCIENT_POWER"], "score": 42.3}, {"speciesId": "<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 568, "matchups": [{"opponent": "gastrodon", "rating": 731, "opRating": 268}, {"opponent": "gallade_shadow", "rating": 702, "opRating": 297}, {"opponent": "abomasnow_shadow", "rating": 677, "opRating": 322}, {"opponent": "toxicroak", "rating": 590, "opRating": 409}, {"opponent": "spiritomb", "rating": 561, "opRating": 438}], "counters": [{"opponent": "drifb<PERSON>", "rating": 136}, {"opponent": "bastiodon", "rating": 147}, {"opponent": "gliscor", "rating": 193}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drapion_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 20731}, {"moveId": "AIR_SLASH", "uses": 16769}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 20858}, {"moveId": "BUG_BUZZ", "uses": 12207}, {"moveId": "PSYBEAM", "uses": 4456}]}, "moveset": ["BUG_BITE", "AERIAL_ACE", "BUG_BUZZ"], "score": 42}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 564, "matchups": [{"opponent": "bibarel", "rating": 834, "opRating": 165}, {"opponent": "gastrodon", "rating": 807, "opRating": 192}, {"opponent": "empoleon_shadow", "rating": 765, "opRating": 234}, {"opponent": "dusknoir_shadow", "rating": 619, "opRating": 380}, {"opponent": "gliscor_shadow", "rating": 611, "opRating": 388}], "counters": [{"opponent": "drapion_shadow", "rating": 118}, {"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "toxicroak", "rating": 158}, {"opponent": "sneasler", "rating": 183}, {"opponent": "gallade_shadow", "rating": 264}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 26178}, {"moveId": "BITE", "uses": 11322}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 16296}, {"moveId": "CRUNCH", "uses": 15306}, {"moveId": "ENERGY_BALL", "uses": 5771}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 41.9}, {"speciesId": "piplup_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 495, "matchups": [{"opponent": "heatran", "rating": 778, "opRating": 221}, {"opponent": "hippow<PERSON>_shadow", "rating": 744, "opRating": 255}, {"opponent": "gliscor", "rating": 709, "opRating": 290}, {"opponent": "hippopotas_shadow", "rating": 709, "opRating": 290}, {"opponent": "lickilicky", "rating": 519, "opRating": 480}], "counters": [{"opponent": "gastrodon", "rating": 169}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "gallade_shadow", "rating": 221}, {"opponent": "dusknoir_shadow", "rating": 238}, {"opponent": "drifb<PERSON>", "rating": 311}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 35380}, {"moveId": "POUND", "uses": 2120}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 15858}, {"moveId": "ICY_WIND", "uses": 15135}, {"moveId": "BUBBLE_BEAM", "uses": 6483}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BUBBLE", "DRILL_PECK", "ICY_WIND"], "score": 41.3}, {"speciesId": "rampardos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 460, "matchups": [{"opponent": "froslass", "rating": 757, "opRating": 242}, {"opponent": "vespiquen", "rating": 726, "opRating": 273}, {"opponent": "drifb<PERSON>", "rating": 718, "opRating": 281}, {"opponent": "abomasnow_shadow", "rating": 710, "opRating": 289}, {"opponent": "abomasnow", "rating": 690, "opRating": 309}], "counters": [{"opponent": "gastrodon", "rating": 133}, {"opponent": "bastiodon", "rating": 161}, {"opponent": "gallade_shadow", "rating": 245}, {"opponent": "drapion_shadow", "rating": 279}, {"opponent": "gliscor", "rating": 306}], "moves": {"fastMoves": [{"moveId": "SMACK_DOWN", "uses": 31288}, {"moveId": "ZEN_HEADBUTT", "uses": 6212}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 16298}, {"moveId": "FLAMETHROWER", "uses": 11214}, {"moveId": "OUTRAGE", "uses": 10029}]}, "moveset": ["SMACK_DOWN", "ROCK_SLIDE", "FLAMETHROWER"], "score": 41.3}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 524, "matchups": [{"opponent": "toxicroak", "rating": 878, "opRating": 121}, {"opponent": "toxicroak_shadow", "rating": 855, "opRating": 144}, {"opponent": "sneasler", "rating": 761, "opRating": 238}, {"opponent": "electivire_shadow", "rating": 742, "opRating": 257}, {"opponent": "gastrodon", "rating": 602, "opRating": 397}], "counters": [{"opponent": "spiritomb", "rating": 96}, {"opponent": "bastiodon", "rating": 183}, {"opponent": "drapion_shadow", "rating": 190}, {"opponent": "dusknoir_shadow", "rating": 222}, {"opponent": "gliscor", "rating": 306}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 20918}, {"moveId": "EXTRASENSORY", "uses": 16582}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17821}, {"moveId": "FUTURE_SIGHT", "uses": 11562}, {"moveId": "FIRE_BLAST", "uses": 8152}]}, "moveset": ["CONFUSION", "SWIFT", "FUTURE_SIGHT"], "score": 40.9}, {"speciesId": "mismagius_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 576, "matchups": [{"opponent": "drifb<PERSON>", "rating": 625, "opRating": 375}, {"opponent": "gastrodon", "rating": 557, "opRating": 442}, {"opponent": "bastiodon", "rating": 557, "opRating": 442}, {"opponent": "gliscor", "rating": 528, "opRating": 471}, {"opponent": "gliscor_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "drapion_shadow", "rating": 131}, {"opponent": "qwilfish_his<PERSON>an", "rating": 148}, {"opponent": "spiritomb", "rating": 149}, {"opponent": "abomasnow_shadow", "rating": 213}, {"opponent": "dusknoir_shadow", "rating": 227}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 10791}, {"moveId": "PSYWAVE", "uses": 10202}, {"moveId": "SUCKER_PUNCH", "uses": 9738}, {"moveId": "MAGICAL_LEAF", "uses": 6691}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 15935}, {"moveId": "DARK_PULSE", "uses": 11030}, {"moveId": "DAZZLING_GLEAM", "uses": 10407}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 40.4}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 584, "matchups": [{"opponent": "sneasler", "rating": 893, "opRating": 106}, {"opponent": "gallade_shadow", "rating": 824, "opRating": 175}, {"opponent": "dusknoir_shadow", "rating": 696, "opRating": 303}, {"opponent": "drifb<PERSON>", "rating": 679, "opRating": 320}, {"opponent": "qwilfish_his<PERSON>an", "rating": 538, "opRating": 461}], "counters": [{"opponent": "vespiquen", "rating": 83}, {"opponent": "abomasnow_shadow", "rating": 136}, {"opponent": "bibarel", "rating": 151}, {"opponent": "drapion_shadow", "rating": 165}, {"opponent": "gliscor", "rating": 331}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 5971}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2570}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2167}, {"moveId": "CHARGE_BEAM", "uses": 2127}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2036}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2020}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 1939}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 1916}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 1875}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1872}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1853}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1845}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1768}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1707}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1704}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1684}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1478}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1255}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 10614}, {"moveId": "BLIZZARD", "uses": 8714}, {"moveId": "ZAP_CANNON", "uses": 7035}, {"moveId": "SOLAR_BEAM", "uses": 5892}, {"moveId": "HYPER_BEAM", "uses": 5263}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 40.4}, {"speciesId": "probopass", "speciesName": "Probopass", "rating": 604, "matchups": [{"opponent": "drifb<PERSON>", "rating": 651, "opRating": 348}, {"opponent": "abomasnow_shadow", "rating": 638, "opRating": 361}, {"opponent": "qwilfish_his<PERSON>an", "rating": 626, "opRating": 373}, {"opponent": "drapion_shadow", "rating": 600, "opRating": 399}, {"opponent": "spiritomb", "rating": 529, "opRating": 470}], "counters": [{"opponent": "gastrodon", "rating": 74}, {"opponent": "hippo<PERSON><PERSON>", "rating": 80}, {"opponent": "gallade_shadow", "rating": 177}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "gliscor", "rating": 262}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 20179}, {"moveId": "SPARK", "uses": 17321}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 12574}, {"moveId": "ROCK_SLIDE", "uses": 11735}, {"moveId": "THUNDERBOLT", "uses": 6464}, {"moveId": "RETURN", "uses": 4420}, {"moveId": "ZAP_CANNON", "uses": 2321}]}, "moveset": ["SPARK", "ROCK_SLIDE", "ZAP_CANNON"], "score": 39.8}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 570, "matchups": [{"opponent": "bibarel", "rating": 893, "opRating": 106}, {"opponent": "bastiodon", "rating": 782, "opRating": 217}, {"opponent": "lickilicky", "rating": 713, "opRating": 286}, {"opponent": "abomasnow_shadow", "rating": 649, "opRating": 350}, {"opponent": "gastrodon", "rating": 636, "opRating": 363}], "counters": [{"opponent": "drifb<PERSON>", "rating": 78}, {"opponent": "gallade_shadow", "rating": 177}, {"opponent": "gliscor", "rating": 245}, {"opponent": "dusknoir_shadow", "rating": 272}, {"opponent": "drapion_shadow", "rating": 364}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 19967}, {"moveId": "MAGICAL_LEAF", "uses": 17533}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 15007}, {"moveId": "UPPER_HAND", "uses": 13592}, {"moveId": "PETAL_BLIZZARD", "uses": 6273}, {"moveId": "SOLAR_BEAM", "uses": 2558}]}, "moveset": ["BULLET_SEED", "PETAL_BLIZZARD", "UPPER_HAND"], "score": 39.7}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 668, "matchups": [{"opponent": "gliscor", "rating": 752, "opRating": 247}, {"opponent": "drifb<PERSON>", "rating": 693, "opRating": 306}, {"opponent": "abomasnow_shadow", "rating": 688, "opRating": 311}, {"opponent": "spiritomb", "rating": 639, "opRating": 360}, {"opponent": "qwilfish_his<PERSON>an", "rating": 521, "opRating": 478}], "counters": [{"opponent": "gastrodon", "rating": 56}, {"opponent": "hippo<PERSON><PERSON>", "rating": 66}, {"opponent": "drapion_shadow", "rating": 173}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "gallade_shadow", "rating": 221}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 21482}, {"moveId": "ASTONISH", "uses": 16018}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17145}, {"moveId": "THUNDERBOLT", "uses": 14187}, {"moveId": "THUNDER", "uses": 6141}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 39.7}, {"speciesId": "mismagius", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570, "matchups": [{"opponent": "empoleon_shadow", "rating": 730, "opRating": 269}, {"opponent": "drifb<PERSON>", "rating": 692, "opRating": 307}, {"opponent": "gliscor", "rating": 548, "opRating": 451}, {"opponent": "gliscor_shadow", "rating": 528, "opRating": 471}, {"opponent": "gallade_shadow", "rating": 509, "opRating": 490}], "counters": [{"opponent": "drapion_shadow", "rating": 105}, {"opponent": "qwilfish_his<PERSON>an", "rating": 119}, {"opponent": "spiritomb", "rating": 168}, {"opponent": "dusknoir_shadow", "rating": 227}, {"opponent": "sneasler", "rating": 254}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 10543}, {"moveId": "PSYWAVE", "uses": 10305}, {"moveId": "SUCKER_PUNCH", "uses": 9864}, {"moveId": "MAGICAL_LEAF", "uses": 6811}], "chargedMoves": [{"moveId": "SHADOW_BALL", "uses": 15948}, {"moveId": "DARK_PULSE", "uses": 11077}, {"moveId": "DAZZLING_GLEAM", "uses": 10413}]}, "moveset": ["HEX", "SHADOW_BALL", "DARK_PULSE"], "score": 39.1}, {"speciesId": "probopass_shadow", "speciesName": "Probopass (Shadow)", "rating": 580, "matchups": [{"opponent": "vespiquen", "rating": 815, "opRating": 184}, {"opponent": "drifb<PERSON>", "rating": 785, "opRating": 214}, {"opponent": "abomasnow_shadow", "rating": 567, "opRating": 432}, {"opponent": "qwilfish_his<PERSON>an", "rating": 558, "opRating": 441}, {"opponent": "drapion_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "hippo<PERSON><PERSON>", "rating": 80}, {"opponent": "gastrodon", "rating": 83}, {"opponent": "gallade_shadow", "rating": 177}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "gliscor", "rating": 297}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 19964}, {"moveId": "SPARK", "uses": 17536}], "chargedMoves": [{"moveId": "MAGNET_BOMB", "uses": 14288}, {"moveId": "ROCK_SLIDE", "uses": 13323}, {"moveId": "THUNDERBOLT", "uses": 7234}, {"moveId": "ZAP_CANNON", "uses": 2571}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "ROCK_SLIDE", "ZAP_CANNON"], "score": 38.7}, {"speciesId": "braviary_hisuian", "speciesName": "Braviary (Hisuian)", "rating": 440, "matchups": [{"opponent": "hippo<PERSON><PERSON>", "rating": 658, "opRating": 341}, {"opponent": "sneasler", "rating": 609, "opRating": 390}, {"opponent": "gallade_shadow", "rating": 566, "opRating": 433}, {"opponent": "gastrodon", "rating": 556, "opRating": 443}, {"opponent": "gliscor", "rating": 543, "opRating": 456}], "counters": [{"opponent": "bastiodon", "rating": 122}, {"opponent": "drifb<PERSON>", "rating": 193}, {"opponent": "spiritomb", "rating": 206}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "dusknoir_shadow", "rating": 238}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 30944}, {"moveId": "ZEN_HEADBUTT", "uses": 6556}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 13376}, {"moveId": "FLY", "uses": 11389}, {"moveId": "DAZZLING_GLEAM", "uses": 4821}, {"moveId": "PSYCHIC", "uses": 4445}, {"moveId": "OMINOUS_WIND", "uses": 3520}]}, "moveset": ["AIR_SLASH", "BRAVE_BIRD", "FLY"], "score": 38.1}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 465, "matchups": [{"opponent": "gastrodon", "rating": 837, "opRating": 162}, {"opponent": "hippo<PERSON><PERSON>", "rating": 765, "opRating": 234}, {"opponent": "bibarel", "rating": 606, "opRating": 393}, {"opponent": "spiritomb", "rating": 598, "opRating": 401}, {"opponent": "lickilicky", "rating": 587, "opRating": 412}], "counters": [{"opponent": "vespiquen", "rating": 83}, {"opponent": "abomasnow_shadow", "rating": 150}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "gliscor", "rating": 215}, {"opponent": "dusknoir_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 5087}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2552}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2549}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2290}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2183}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2161}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2073}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2072}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1995}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1953}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1936}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1878}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1838}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1794}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1744}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1550}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1362}, {"moveId": "ZEN_HEADBUTT", "uses": 732}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20169}, {"moveId": "ENERGY_BALL", "uses": 7125}, {"moveId": "SEED_FLARE", "uses": 5995}, {"moveId": "SOLAR_BEAM", "uses": 4126}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "SEED_FLARE"], "score": 38.1}, {"speciesId": "gabite", "speciesName": "Gabite", "rating": 552, "matchups": [{"opponent": "magnezone_shadow", "rating": 935, "opRating": 64}, {"opponent": "bastiodon", "rating": 844, "opRating": 155}, {"opponent": "empoleon_shadow", "rating": 746, "opRating": 253}, {"opponent": "drapion_shadow", "rating": 693, "opRating": 306}, {"opponent": "qwilfish_his<PERSON>an", "rating": 571, "opRating": 428}], "counters": [{"opponent": "drifb<PERSON>", "rating": 76}, {"opponent": "abomasnow_shadow", "rating": 97}, {"opponent": "gliscor_shadow", "rating": 107}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "gliscor", "rating": 344}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32729}, {"moveId": "TAKE_DOWN", "uses": 4771}], "chargedMoves": [{"moveId": "DIG", "uses": 13495}, {"moveId": "FLAMETHROWER", "uses": 11087}, {"moveId": "TWISTER", "uses": 7383}, {"moveId": "RETURN", "uses": 5550}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 37.8}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 370, "matchups": [{"opponent": "drifb<PERSON>_shadow", "rating": 750, "opRating": 250}, {"opponent": "gliscor", "rating": 722, "opRating": 277}, {"opponent": "gliscor_shadow", "rating": 704, "opRating": 295}, {"opponent": "drifb<PERSON>", "rating": 604, "opRating": 395}, {"opponent": "roserade", "rating": 527, "opRating": 472}], "counters": [{"opponent": "bastiodon", "rating": 143}, {"opponent": "gastrodon", "rating": 211}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "abomasnow_shadow", "rating": 234}, {"opponent": "spiritomb", "rating": 269}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 2974}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2878}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2510}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2510}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2496}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2427}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2389}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2282}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2224}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2199}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2190}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2157}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2076}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2075}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1871}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1588}, {"moveId": "ZEN_HEADBUTT", "uses": 526}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 18634}, {"moveId": "THUNDER", "uses": 8698}, {"moveId": "FOCUS_BLAST", "uses": 7470}, {"moveId": "GIGA_IMPACT", "uses": 2589}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "THUNDER"], "score": 37.5}, {"speciesId": "snover_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 443, "matchups": [{"opponent": "electivire", "rating": 827, "opRating": 172}, {"opponent": "hippopotas", "rating": 820, "opRating": 179}, {"opponent": "electrode_hisuian", "rating": 693, "opRating": 306}, {"opponent": "gastrodon", "rating": 616, "opRating": 383}, {"opponent": "hippo<PERSON><PERSON>", "rating": 545, "opRating": 454}], "counters": [{"opponent": "drapion_shadow", "rating": 194}, {"opponent": "qwilfish_his<PERSON>an", "rating": 210}, {"opponent": "abomasnow_shadow", "rating": 234}, {"opponent": "dusknoir_shadow", "rating": 250}, {"opponent": "drifb<PERSON>", "rating": 303}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 14804}, {"moveId": "LEAFAGE", "uses": 11502}, {"moveId": "ICE_SHARD", "uses": 11203}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 16329}, {"moveId": "ENERGY_BALL", "uses": 12592}, {"moveId": "STOMP", "uses": 8554}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 36.5}, {"speciesId": "rotom_frost", "speciesName": "<PERSON><PERSON><PERSON> (Frost)", "rating": 549, "matchups": [{"opponent": "drifb<PERSON>", "rating": 693, "opRating": 306}, {"opponent": "gliscor", "rating": 596, "opRating": 403}, {"opponent": "spiritomb", "rating": 575, "opRating": 424}, {"opponent": "gliscor_shadow", "rating": 575, "opRating": 424}, {"opponent": "qwilfish_his<PERSON>an", "rating": 521, "opRating": 478}], "counters": [{"opponent": "gastrodon", "rating": 101}, {"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drapion_shadow", "rating": 190}, {"opponent": "gallade_shadow", "rating": 221}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20922}, {"moveId": "ASTONISH", "uses": 16578}], "chargedMoves": [{"moveId": "BLIZZARD", "uses": 15870}, {"moveId": "THUNDERBOLT", "uses": 15132}, {"moveId": "THUNDER", "uses": 6523}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "BLIZZARD"], "score": 36.3}, {"speciesId": "rotom_wash", "speciesName": "Rotom (Wash)", "rating": 546, "matchups": [{"opponent": "empoleon_shadow", "rating": 876, "opRating": 123}, {"opponent": "drifb<PERSON>", "rating": 693, "opRating": 306}, {"opponent": "gliscor", "rating": 596, "opRating": 403}, {"opponent": "spiritomb", "rating": 575, "opRating": 424}, {"opponent": "qwilfish_his<PERSON>an", "rating": 521, "opRating": 478}], "counters": [{"opponent": "hippo<PERSON><PERSON>", "rating": 80}, {"opponent": "gastrodon", "rating": 101}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drapion_shadow", "rating": 190}, {"opponent": "gallade_shadow", "rating": 197}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 20344}, {"moveId": "ASTONISH", "uses": 17156}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 16326}, {"moveId": "HYDRO_PUMP", "uses": 14063}, {"moveId": "THUNDER", "uses": 7110}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "HYDRO_PUMP"], "score": 36.2}, {"speciesId": "luxio_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 508, "matchups": [{"opponent": "empoleon_shadow", "rating": 798, "opRating": 201}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 665, "opRating": 334}, {"opponent": "lickilicky", "rating": 597, "opRating": 402}, {"opponent": "spiritomb", "rating": 561, "opRating": 438}, {"opponent": "drifb<PERSON>", "rating": 546, "opRating": 453}], "counters": [{"opponent": "gastrodon", "rating": 110}, {"opponent": "gliscor", "rating": 159}, {"opponent": "abomasnow_shadow", "rating": 160}, {"opponent": "drapion_shadow", "rating": 233}, {"opponent": "dusknoir_shadow", "rating": 311}], "moves": {"fastMoves": [{"moveId": "SPARK", "uses": 26007}, {"moveId": "BITE", "uses": 11493}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 21843}, {"moveId": "CRUNCH", "uses": 11636}, {"moveId": "THUNDERBOLT", "uses": 4018}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SPARK", "WILD_CHARGE", "CRUNCH"], "score": 35}, {"speciesId": "honch<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 566, "matchups": [{"opponent": "drifb<PERSON>", "rating": 851, "opRating": 148}, {"opponent": "spiritomb", "rating": 706, "opRating": 293}, {"opponent": "dusknoir_shadow", "rating": 682, "opRating": 317}, {"opponent": "gastrodon", "rating": 641, "opRating": 358}, {"opponent": "qwilfish_his<PERSON>an", "rating": 517, "opRating": 482}], "counters": [{"opponent": "drapion_shadow", "rating": 122}, {"opponent": "abomasnow_shadow", "rating": 150}, {"opponent": "gliscor", "rating": 155}, {"opponent": "gallade_shadow", "rating": 206}, {"opponent": "bastiodon", "rating": 215}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 27802}, {"moveId": "PECK", "uses": 9698}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18243}, {"moveId": "DARK_PULSE", "uses": 11325}, {"moveId": "PSYCHIC", "uses": 3982}, {"moveId": "SKY_ATTACK", "uses": 3947}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 34.6}, {"speciesId": "snover", "speciesName": "Snover", "rating": 473, "matchups": [{"opponent": "electivire_shadow", "rating": 827, "opRating": 172}, {"opponent": "gastrodon", "rating": 669, "opRating": 330}, {"opponent": "hippo<PERSON><PERSON>", "rating": 595, "opRating": 404}, {"opponent": "<PERSON><PERSON><PERSON><PERSON>", "rating": 573, "opRating": 426}, {"opponent": "abomasnow_shadow", "rating": 549, "opRating": 450}], "counters": [{"opponent": "drapion_shadow", "rating": 156}, {"opponent": "qwilfish_his<PERSON>an", "rating": 169}, {"opponent": "dusknoir_shadow", "rating": 188}, {"opponent": "drifb<PERSON>", "rating": 253}, {"opponent": "gliscor", "rating": 349}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 14586}, {"moveId": "LEAFAGE", "uses": 11664}, {"moveId": "ICE_SHARD", "uses": 11251}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 14639}, {"moveId": "ENERGY_BALL", "uses": 11396}, {"moveId": "STOMP", "uses": 7345}, {"moveId": "RETURN", "uses": 4145}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 34.3}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 337, "matchups": [{"opponent": "gliscor_shadow", "rating": 725, "opRating": 274}, {"opponent": "staravia_shadow", "rating": 698, "opRating": 301}, {"opponent": "drifloon", "rating": 662, "opRating": 337}, {"opponent": "drifb<PERSON>", "rating": 648, "opRating": 351}, {"opponent": "giratina_origin", "rating": 608, "opRating": 391}], "counters": [{"opponent": "gastrodon", "rating": 169}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "drapion_shadow", "rating": 207}, {"opponent": "spiritomb", "rating": 254}, {"opponent": "dusknoir_shadow", "rating": 272}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 2982}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2787}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2538}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2498}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2427}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2404}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2392}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2285}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2249}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2226}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2208}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2146}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2113}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2098}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1863}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1596}, {"moveId": "ZEN_HEADBUTT", "uses": 527}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 18654}, {"moveId": "THUNDER", "uses": 8672}, {"moveId": "FOCUS_BLAST", "uses": 7470}, {"moveId": "GIGA_IMPACT", "uses": 2614}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "THUNDER"], "score": 34.2}, {"speciesId": "turtwig_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 468, "matchups": [{"opponent": "hippopotas_shadow", "rating": 818, "opRating": 181}, {"opponent": "hippo<PERSON><PERSON>", "rating": 648, "opRating": 351}, {"opponent": "gastrodon", "rating": 629, "opRating": 370}, {"opponent": "bibarel", "rating": 622, "opRating": 377}, {"opponent": "hippow<PERSON>_shadow", "rating": 614, "opRating": 385}], "counters": [{"opponent": "drifb<PERSON>", "rating": 107}, {"opponent": "dusknoir_shadow", "rating": 127}, {"opponent": "gliscor", "rating": 176}, {"opponent": "abomasnow_shadow", "rating": 202}, {"opponent": "drapion_shadow", "rating": 245}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 19540}, {"moveId": "TACKLE", "uses": 17960}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 15359}, {"moveId": "SEED_BOMB", "uses": 15289}, {"moveId": "ENERGY_BALL", "uses": 6875}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 34}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 388, "matchups": [{"opponent": "gastrodon", "rating": 922, "opRating": 77}, {"opponent": "samu<PERSON><PERSON>_<PERSON><PERSON>an", "rating": 715, "opRating": 284}, {"opponent": "hippo<PERSON><PERSON>", "rating": 597, "opRating": 402}, {"opponent": "lickilicky", "rating": 524, "opRating": 475}, {"opponent": "bibarel", "rating": 516, "opRating": 483}], "counters": [{"opponent": "vespiquen", "rating": 83}, {"opponent": "abomasnow_shadow", "rating": 143}, {"opponent": "drifb<PERSON>", "rating": 145}, {"opponent": "drapion_shadow", "rating": 156}, {"opponent": "gliscor", "rating": 245}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 5409}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 2560}, {"moveId": "HIDDEN_POWER_ICE", "uses": 2453}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2418}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2197}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2115}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2057}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2019}, {"moveId": "HIDDEN_POWER_DARK", "uses": 1903}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 1872}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1868}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1817}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1783}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1730}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1707}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1507}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1319}, {"moveId": "ZEN_HEADBUTT", "uses": 664}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20203}, {"moveId": "ENERGY_BALL", "uses": 7125}, {"moveId": "SEED_FLARE", "uses": 5997}, {"moveId": "SOLAR_BEAM", "uses": 4125}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 33.7}, {"speciesId": "turtwig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 467, "matchups": [{"opponent": "bibarel", "rating": 818, "opRating": 181}, {"opponent": "weavile_shadow", "rating": 814, "opRating": 185}, {"opponent": "gastrodon", "rating": 681, "opRating": 318}, {"opponent": "hippow<PERSON>_shadow", "rating": 648, "opRating": 351}, {"opponent": "electivire", "rating": 555, "opRating": 444}], "counters": [{"opponent": "drifb<PERSON>", "rating": 126}, {"opponent": "dusknoir_shadow", "rating": 127}, {"opponent": "drapion_shadow", "rating": 165}, {"opponent": "qwilfish_his<PERSON>an", "rating": 169}, {"opponent": "gliscor", "rating": 176}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 19207}, {"moveId": "TACKLE", "uses": 18293}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 14159}, {"moveId": "BODY_SLAM", "uses": 13719}, {"moveId": "ENERGY_BALL", "uses": 6351}, {"moveId": "RETURN", "uses": 3253}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 32.3}, {"speciesId": "gabite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 519, "matchups": [{"opponent": "magnezone_shadow", "rating": 939, "opRating": 60}, {"opponent": "bastiodon", "rating": 837, "opRating": 162}, {"opponent": "empoleon_shadow", "rating": 704, "opRating": 295}, {"opponent": "qwilfish_his<PERSON>an", "rating": 511, "opRating": 488}, {"opponent": "bibarel", "rating": 503, "opRating": 496}], "counters": [{"opponent": "vespiquen", "rating": 45}, {"opponent": "drifb<PERSON>", "rating": 76}, {"opponent": "abomasnow_shadow", "rating": 101}, {"opponent": "gliscor", "rating": 107}, {"opponent": "gastrodon", "rating": 166}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32644}, {"moveId": "TAKE_DOWN", "uses": 4856}], "chargedMoves": [{"moveId": "DIG", "uses": 15407}, {"moveId": "FLAMETHROWER", "uses": 13150}, {"moveId": "TWISTER", "uses": 8848}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 28.9}, {"speciesId": "honchk<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 499, "matchups": [{"opponent": "drifb<PERSON>", "rating": 803, "opRating": 196}, {"opponent": "dusknoir_shadow", "rating": 634, "opRating": 365}, {"opponent": "spiritomb", "rating": 624, "opRating": 375}, {"opponent": "bibarel", "rating": 610, "opRating": 389}, {"opponent": "gastrodon", "rating": 579, "opRating": 420}], "counters": [{"opponent": "bastiodon", "rating": 79}, {"opponent": "drapion_shadow", "rating": 88}, {"opponent": "qwilfish_his<PERSON>an", "rating": 119}, {"opponent": "abomasnow_shadow", "rating": 143}, {"opponent": "gliscor", "rating": 155}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 28510}, {"moveId": "PECK", "uses": 8990}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18231}, {"moveId": "DARK_PULSE", "uses": 11321}, {"moveId": "PSYCHIC", "uses": 3975}, {"moveId": "SKY_ATTACK", "uses": 3975}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BRAVE_BIRD", "DARK_PULSE"], "score": 28}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 401, "matchups": [{"opponent": "bibarel", "rating": 673, "opRating": 326}, {"opponent": "gastrodon", "rating": 665, "opRating": 334}, {"opponent": "hippow<PERSON>_shadow", "rating": 623, "opRating": 376}, {"opponent": "hippo<PERSON><PERSON>", "rating": 611, "opRating": 388}, {"opponent": "spiritomb", "rating": 592, "opRating": 407}], "counters": [{"opponent": "drapion_shadow", "rating": 80}, {"opponent": "abomasnow_shadow", "rating": 87}, {"opponent": "qwilfish_his<PERSON>an", "rating": 90}, {"opponent": "gliscor", "rating": 125}, {"opponent": "dusknoir_shadow", "rating": 161}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 22844}, {"moveId": "RAZOR_LEAF", "uses": 14656}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 16921}, {"moveId": "SOLAR_BEAM", "uses": 13037}, {"moveId": "HYPER_BEAM", "uses": 7582}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 23.5}, {"speciesId": "cranidos_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 165, "matchups": [{"opponent": "cranidos", "rating": 706, "opRating": 293}], "counters": [{"opponent": "spiritomb", "rating": 52}, {"opponent": "dusknoir_shadow", "rating": 61}, {"opponent": "drifb<PERSON>", "rating": 64}, {"opponent": "drapion_shadow", "rating": 131}, {"opponent": "gliscor", "rating": 155}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 19265}, {"moveId": "ZEN_HEADBUTT", "uses": 18235}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18108}, {"moveId": "ANCIENT_POWER", "uses": 11176}, {"moveId": "BULLDOZE", "uses": 8163}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TAKE_DOWN", "ANCIENT_POWER", "BULLDOZE"], "score": 17.7}, {"speciesId": "cranidos", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 152, "matchups": [{"opponent": "cranidos_shadow", "rating": 706, "opRating": 293}], "counters": [{"opponent": "drifb<PERSON>", "rating": 52}, {"opponent": "dusknoir_shadow", "rating": 72}, {"opponent": "gastrodon", "rating": 89}, {"opponent": "drapion_shadow", "rating": 110}, {"opponent": "gliscor", "rating": 125}], "moves": {"fastMoves": [{"moveId": "TAKE_DOWN", "uses": 19442}, {"moveId": "ZEN_HEADBUTT", "uses": 18058}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 16183}, {"moveId": "ANCIENT_POWER", "uses": 9968}, {"moveId": "BULLDOZE", "uses": 7219}, {"moveId": "RETURN", "uses": 4158}]}, "moveset": ["TAKE_DOWN", "ANCIENT_POWER", "BULLDOZE"], "score": 16.7}]