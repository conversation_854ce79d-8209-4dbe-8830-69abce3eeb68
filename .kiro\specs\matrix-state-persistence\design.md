# Design Document

## Overview

This design implements persistent state management and shareable links for PvPoke's matrix battle mode. The solution extends the existing architecture by adding browser storage capabilities and URL-based state sharing, bringing matrix mode to parity with single and multi battle modes while enabling collaborative team analysis.

The design leverages PvPoke's existing patterns: PHP-based URL parameter handling (like `$_GET['p1']` and `$_GET['p2']` in single mode), JavaScript-based Pokemon selection through `PokeMultiSelect.js`, and localStorage usage already present in the codebase for custom groups and settings.

## Architecture

### Current Matrix Mode Architecture
- **PHP Layer**: `battle.php` renders matrix UI with mode selection (`single`, `multi`, `matrix`)
- **JavaScript Layer**: `Interface.js` manages battle modes, `PokeMultiSelect.js` handles Pokemon selection
- **State Management**: Currently ephemeral - no persistence across page refreshes
- **Data Flow**: Pokemon selections → JavaScript objects → Battle simulation → Results display

### Enhanced Architecture with Persistence
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PHP Layer     │    │  JavaScript      │    │  Storage Layer  │
│                 │    │  Controllers     │    │                 │
│ • battle.php    │◄──►│ • Interface.js   │◄──►│ • localStorage  │
│ • URL params    │    │ • PokeMultiSelect│    │ • URL encoding  │
│ • Meta tags     │    │ • MatrixState.js │    │ • Compression   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Shareable URLs  │    │ Battle Engine    │    │ State Recovery  │
│ • Compressed    │    │ • Matrix calc    │    │ • Auto-restore  │
│ • Team configs  │    │ • Results gen    │    │ • Validation    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Components and Interfaces

### 1. MatrixStateManager (New Component)
**Purpose**: Central state management for matrix mode persistence and sharing

**Interface**:
```javascript
class MatrixStateManager {
    // State persistence
    saveMatrixState(teamA, teamB, settings)
    loadMatrixState()
    clearMatrixState()
    
    // URL sharing
    generateShareableURL(teamA, teamB, settings)
    parseSharedURL(urlParams)
    
    // Validation and cleanup
    validateMatrixState(state)
    compressStateData(data)
    decompressStateData(data)
}
```

**Integration Points**:
- Called by `Interface.js` when Pokemon selections change
- Integrates with existing `PokeMultiSelect.js` Pokemon management
- Uses existing localStorage patterns from pokebox functionality

### 2. Enhanced PokeMultiSelect (Modified Component)
**Current Functionality**: Pokemon selection, quick-fill lists, custom groups
**New Functionality**: Matrix state awareness, auto-save triggers

**New Methods**:
```javascript
// Add to existing PokeMultiSelect class
setMatrixTeamSide(side) // 'A' or 'B'
getMatrixTeamSide()
triggerMatrixStateSave()
loadFromMatrixState(pokemonData)
```

**Integration**: 
- Extends existing `init()` method to check for saved matrix state
- Modifies `addPokemon()` and `removePokemon()` to trigger auto-save
- Maintains backward compatibility with multi-battle mode

### 3. Enhanced Interface.js (Modified Component)
**Current Functionality**: Battle mode switching, Pokemon coordination
**New Functionality**: Matrix state orchestration, URL generation

**New Methods**:
```javascript
// Add to existing Interface class
initMatrixStateManagement()
onMatrixPokemonChange()
generateMatrixShareLink()
handleMatrixURLParams()
showMatrixStateIndicator()
```

### 4. Enhanced battle.php (Modified Template)
**Current Functionality**: Mode selection UI, Pokemon selectors
**New Functionality**: Matrix state indicators, share link UI

**New Elements**:
```html
<!-- Matrix state indicator -->
<div class="matrix-state-indicator" style="display: none;">
    <span class="saved-state-icon">💾</span>
    <span>Saved matrix loaded</span>
    <button class="clear-state-btn">Clear</button>
</div>

<!-- Share link section for matrix mode -->
<div class="matrix-share-container" style="display: none;">
    <p>Share this matrix setup:</p>
    <div class="share-link">
        <input type="text" readonly>
        <button class="copy-btn">Copy</button>
    </div>
</div>
```

## Data Models

### Matrix State Schema
```javascript
const MatrixState = {
    version: "1.0",                    // Schema version for future compatibility
    timestamp: 1642694400000,          // Unix timestamp for cleanup
    league: 1500,                      // CP limit (500, 1500, 2500, 10000)
    settings: {
        matrixMode: "battle",          // "battle", "breakpoint", "bulkpoint", "attack"
        shields: [1, 1],               // Default shield scenario
        breakpointMode: "fast"         // For breakpoint analysis
    },
    teams: {
        A: [                           // Team A Pokemon array
            {
                speciesId: "charizard",
                level: 20,
                ivs: { attack: 0, defense: 15, hp: 14 },
                moves: {
                    fastMove: "fire_spin",
                    chargedMoves: ["blast_burn", "dragon_claw"]
                },
                nickname: "Starter Zard"  // Optional custom name
            }
            // ... more Pokemon
        ],
        B: [                           // Team B Pokemon array
            // Same structure as Team A
        ]
    }
}
```

### URL Encoding Schema
For shareable links, the matrix state is compressed and encoded:
```
/battle/?mode=matrix&data=<compressed_base64_state>
```

**Compression Strategy**:
1. **Pokemon ID Mapping**: Use numeric IDs instead of full speciesId strings
2. **Move ID Mapping**: Use numeric move IDs instead of move names  
3. **Default Value Omission**: Skip common values (15/15/15 IVs, level 20, etc.)
4. **Base64 Encoding**: Compress JSON and encode for URL safety

**Example Compressed URL**:
```
/battle/?mode=matrix&data=eyJ2IjoiMS4wIiwibCI6MTUwMCwidCI6eyJBIjpbeyJpZCI6NiwiaSI6WzAsMTUsMTRdLCJtIjpbMjQsWzExLDNdXX1dLCJCIjpbeyJpZCI6MTUwfV19fQ
```

## Error Handling

### Browser Storage Limitations
```javascript
// Graceful degradation when localStorage unavailable
try {
    localStorage.setItem('pvpoke_matrix_state', stateData);
} catch (e) {
    console.warn('Matrix state persistence unavailable:', e.message);
    // Continue without persistence - no user-facing error
}
```

### URL Parameter Validation
```javascript
function validateSharedMatrixURL(urlData) {
    try {
        const state = JSON.parse(atob(urlData));
        
        // Version compatibility check
        if (!state.version || parseFloat(state.version) > 1.0) {
            throw new Error('Unsupported matrix state version');
        }
        
        // Required fields validation
        if (!state.teams || !state.teams.A || !state.teams.B) {
            throw new Error('Invalid team data');
        }
        
        // Pokemon validation
        state.teams.A.forEach(validatePokemonData);
        state.teams.B.forEach(validatePokemonData);
        
        return state;
    } catch (error) {
        console.error('Invalid shared matrix URL:', error);
        return null; // Graceful fallback to empty matrix
    }
}
```

### State Recovery Failures
```javascript
function handleStateRecoveryFailure(error) {
    // Log error for debugging
    console.warn('Matrix state recovery failed:', error);
    
    // Clear corrupted state
    localStorage.removeItem('pvpoke_matrix_state');
    
    // Show user-friendly message
    showNotification('Previous matrix setup could not be restored. Starting fresh.', 'info');
    
    // Continue with empty matrix
    return getEmptyMatrixState();
}
```

## Testing Strategy

### Unit Testing Approach
```javascript
describe('MatrixStateManager', () => {
    beforeEach(() => {
        localStorage.clear();
        // Mock Pokemon data and battle instance
    });

    describe('State Persistence', () => {
        test('saves and loads matrix state correctly', () => {
            const testState = createTestMatrixState();
            manager.saveMatrixState(testState.teams.A, testState.teams.B, testState.settings);
            
            const loaded = manager.loadMatrixState();
            expect(loaded.teams.A).toEqual(testState.teams.A);
            expect(loaded.league).toBe(testState.league);
        });

        test('handles corrupted localStorage gracefully', () => {
            localStorage.setItem('pvpoke_matrix_state', 'invalid json');
            
            expect(() => manager.loadMatrixState()).not.toThrow();
            expect(manager.loadMatrixState()).toBe(null);
        });
    });

    describe('URL Sharing', () => {
        test('generates valid shareable URLs', () => {
            const testState = createTestMatrixState();
            const url = manager.generateShareableURL(testState.teams.A, testState.teams.B, testState.settings);
            
            expect(url).toMatch(/\/battle\/\?mode=matrix&data=[A-Za-z0-9+/=]+/);
            expect(url.length).toBeLessThan(2000); // Browser URL limit
        });

        test('parses shared URLs correctly', () => {
            const originalState = createTestMatrixState();
            const url = manager.generateShareableURL(originalState.teams.A, originalState.teams.B, originalState.settings);
            const urlParams = new URLSearchParams(url.split('?')[1]);
            
            const parsedState = manager.parseSharedURL(urlParams);
            expect(parsedState.teams.A[0].speciesId).toBe(originalState.teams.A[0].speciesId);
        });
    });
});
```

### Integration Testing
1. **Cross-browser localStorage compatibility** - Test in Chrome, Firefox, Safari, Edge
2. **Large team handling** - Test with 50+ Pokemon per team (meta analysis scenarios)
3. **URL length limits** - Ensure compressed URLs stay under 2000 characters
4. **Mode switching** - Verify matrix state doesn't interfere with single/multi modes

### Performance Testing
1. **State save/load speed** - Target <100ms for typical team sizes
2. **URL generation time** - Target <50ms for compression and encoding
3. **Memory usage** - Monitor JavaScript heap with large Pokemon datasets
4. **localStorage quota** - Test behavior approaching 5-10MB storage limits

## Implementation Phases

### Phase 1: Core State Persistence (Week 1)
- Implement `MatrixStateManager` class
- Add localStorage save/load functionality
- Integrate with existing `PokeMultiSelect` auto-save triggers
- Add state indicator UI to `battle.php`

### Phase 2: URL Sharing (Week 2)
- Implement URL compression and encoding
- Add share link generation to matrix results
- Implement URL parameter parsing on page load
- Add copy-to-clipboard functionality

### Phase 3: Enhanced UX (Week 3)
- Add state management UI (clear, restore options)
- Implement state validation and error handling
- Add loading indicators for large team imports
- Optimize compression for better URL lengths

### Phase 4: Testing & Polish (Week 4)
- Cross-browser compatibility testing
- Performance optimization for large datasets
- User experience refinement
- Documentation and code cleanup

## Security Considerations

### Data Validation
- **Input sanitization**: All Pokemon IDs, move names, and numeric values validated against GameMaster data
- **State size limits**: Maximum 100 Pokemon per team to prevent abuse
- **URL length limits**: Compressed data capped at 1500 characters to prevent URL manipulation

### Privacy
- **No personal data**: Matrix states contain only Pokemon game data, no user identification
- **Local storage only**: No server-side storage of user configurations
- **Shareable by choice**: URLs only generated when user explicitly requests sharing

## Compatibility Considerations

### Backward Compatibility
- **Existing URLs**: Current single/multi battle URLs continue working unchanged
- **Browser support**: Graceful degradation for browsers without localStorage
- **Mobile devices**: Touch-friendly UI for matrix state management

### Future Extensibility
- **Schema versioning**: Matrix state includes version field for future migrations
- **Plugin architecture**: State manager designed to support future Google Sheets integration
- **API readiness**: Data structures compatible with future REST API implementation

This design maintains PvPoke's existing architecture patterns while adding the requested matrix persistence and sharing capabilities. The implementation leverages proven localStorage patterns already used in the codebase and follows the established PHP/JavaScript separation of concerns.