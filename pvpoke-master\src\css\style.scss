/* VARIABLES */
$global-font-color:#000;

$color-attack:#ed6774;
$color-defense:#6696ed;
$color-hp:#0eb084;
$color-hp-yellow:#eabd49;
$color-hp-red:#aa0505;

$color-move-power:#a60087;
$color-move-energy:#0087a6;
$color-move-duration:#87a600;

$color-faint:#ff0000;
$color-shield:#df46e5;

$color-bug-dark:#9bc231;
$color-bug-light:#aec92c;
$color-dark-dark:#52505e;
$color-dark-light:#6e7681;
$color-dragon-dark:#1065b6;
$color-dragon-light:#067fc4;
$color-electric-dark:#f3d43e;
$color-electric-light:#fedf6b;
$color-fairy-dark:#eb8de1;
$color-fairy-light:#f6a7e8;
$color-fighting-dark:#ce3d64;
$color-fighting-light:#e34448;
$color-fire-dark:#fe9d59;
$color-fire-light:#feb04b;
$color-flying-dark:#91a8de;
$color-flying-light:#a7c1f2;
$color-ghost-dark:#5069ac;
$color-ghost-light:#7571d0;
$color-grass-dark:#5fbb50;
$color-grass-light:#59c079;
$color-ground-dark:#d87b40;
$color-ground-light:#d2976b;
$color-ice-dark:#74d3bd;
$color-ice-light:#94ddd6;
$color-normal-dark:#909aa3;
$color-normal-light:#a3a49e;
$color-poison-light:#a662c7;
$color-poison-dark:#c662d6;
$color-psychic-dark:#f2726f;
$color-psychic-light:#fda194;
$color-rock-dark:#c7b98c;
$color-rock-light:#d7cd90;
$color-steel-dark:#50879c;
$color-steel-light:#5aafb4;
$color-water-dark:#4f91db;
$color-water-light:#6ac7e9;

$color-ranking:#fff6d4;
$color-highlight:#222;

$color-links:#003462;
$color-blue-light:#6296be;
$color-blue-dark:#003462;
$color-blue-medium:#285e85;
$color-sky-blue:#c4def5;

$color-gold:#ffcf01;
$color-gold-dark:#f9a01b;
$color-purple:#7f4da8;
$color-purple-dark:#3b1f52;

$color-off-white:#f9fdff;
$color-off-black:#111;

$color-stat-buff:#f1841c;
$color-stat-debuff:#0158d0;


/* MIXINS */

@mixin transition($property, $duration, $ease, $delay){
    -webkit-transition: $property $duration $ease $delay;
    -moz-transition: $property $duration $ease $delay;
    -o-transition: $property $duration $ease $delay;
    transition: $property $duration $ease $delay;
}

@mixin transform($property){
	-moz-transform: $property;
	-webkit-transform: $property;
	-o-transform: $property;
	-ms-transform: $property;
	transform: $property;
}

@mixin gradient($light, $dark){
	background: $dark; /* Old browsers */
	background: -moz-linear-gradient(top, $light 30%, $dark 99%) !important; /* FF3.6-15 */
	background: -webkit-linear-gradient(top, $light 30%,$dark 99%) !important; /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, $light 30%,$dark 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

/* STRUCTURE */

html{
	height:100%;
}

body, html{
	margin: 0;
	font-family: Arial, sans-serif;
	color:$global-font-color;
}

body{
	background-color:#f3fbfe;
	background: url('../img/themes/sunflower/sunflower-bg.jpg');
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-size:cover;
	background-position: center bottom;
	min-height:100%;
}

.clear{
	clear:both;
}

.flex{
	display:flex;

	&.space-between{
		justify-content: space-between;
	}

	&.cols-3 > div{
		flex-basis:33%;
	}
}

.hide{
	display:none;
}

.mobile{
	display:none;
}

header{
	padding:3px;
	box-sizing:border-box;
	background: $color-off-white;
	position: fixed;
    z-index: 25;
    width: 100%;
    box-shadow: rgba(0,0,0,0.3) 0px 0px 6px;
	top: 0;
	//top:28px;

	.header-wrap{
		max-width:800px;
		margin:0 auto;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	a img{
		display: block;
		height: 30px;
	}

	.menu{
		padding:3px 0;
		a{
			display:block;
			margin-left:20px;
			color:$color-links;
			text-decoration: none;
			background-repeat: no-repeat;
			background-position: 8px center;
			padding: 4px 8px 4px 30px;
			background-size: 16px 16px;
			font-weight:bold;
			border-radius: 20px;

			@include transition(background-color, 0.2s, ease-out, 0s);


			&.twitter{ color: #01bbff !important; }
			&.tera{ color: #d250a3 !important; }
		}

		& > a.selected, .parent-menu > a.selected{
			background-color: $color-sky-blue;

			@include transition(background-color, 0s, ease-out, 0s);
		}

		.parent-menu{
			position:relative;

			.submenu{
				max-height: 0;
				overflow: hidden;

				@include transition(max-height, .125s, ease-out, 0s);

				&.active{
					display:block;
					max-height: 600px;
					pointer-events: auto;
				}
			}

			& > a{
				position:relative;

				span{
					position: absolute;
					display: block;
					content:".";
					font-size: 0;
					width:12px;
					height:4px;
					background:url('../img/themes/sunflower/dropdown-triangle-white.png');
					background-repeat: no-repeat;

					right: 0px !important;
				    left: auto;
				    top: 4px;
				    padding: 13px 12px;
				    background-position: center;
				}
			}

			.icon-rankings + .submenu a{
				padding-left: 24px;
				background-repeat: no-repeat;
				background-position: 0 center;

				&.nav-great{
					background-image: url('../img/themes/sunflower/nav-league-great.png');
				}

				&.nav-ultra{
					background-image: url('../img/themes/sunflower/nav-league-ultra.png');
				}

				&.nav-master{
					background-image: url('../img/themes/sunflower/nav-league-master.png');
				}
			}
		}
	}

	.hamburger{
		.meat{
			width: 30px;
			height: 5px;
			margin: 3px 0;
			border-radius: 8px;
			background:#000;
		}
	}
}

.header-ticker{
	display: flex;
    justify-content: center;
    background: #4b8f56;
    padding: 3px;
	width: 100%;
	position: fixed;
	z-index: 25;

	a{
		text-decoration: none;
	    font-size: 13px;
	    color: #fff;
	    border: 1px solid #fff;
	    padding: 3px 10px;
	    border-radius: 4px;
		margin: 1px 0;
	}

	&.old-version{
		background:#554778;
	}
}

.banner-link{
	display:block;
	padding: 10px;
	border-radius: 8px;
	background:#554778;
	margin-bottom: 10px;
	color: $color-off-white;
	font-size: 14px;

	a{
		color: $color-off-white;
	}
}

footer{
	max-width:800px;
	position: relative;
	z-index: 10;
	text-align: center;
	font-size:12px;
	margin:0 auto;

	p:first-of-type{
		margin-top:0;
	}

	a.domz-link{
		color: #c7007d;
		font-weight: 900;
		background-image: url('../img/themes/sunflower/domz.png');
		background-repeat: no-repeat;
		background-size: contain;
		padding-left: 26px;
		text-decoration: none;
	}
}

#main{
	max-width:800px;
	margin: auto;
	padding:60px 10px 10px 10px;
    border-radius: 12px;
	//padding: 90px 10px 10px 10px;

	& > h1{
		margin:0;
	}
}

#main{
	position:relative;
	z-index: 10;
}

.main-wrap{
	margin-bottom: 100px;
}

.section{
	margin:10px 0;

	height:auto !important;

	&.white{
		background: rgba(255,255,255,0.9);
		padding: 10px;
		border-radius: 8px;
	}

	&.error{
		display:none;
		text-align: center;
	}

	p:first-of-type{
		margin-top:0;
	}

	.toggle-content p:first-of-type{
		margin:1em 0;
	}

	p.description{
		margin:1em 0;
	}

	p{
		font-size: 14px;
	}

	p.small{
		font-size: 12px;
	}

	h2{
		font-size:18px;
	}

	.continentals{
		margin-bottom:15px;

		.check{
			margin-right:10px;
		}
	}
}

.section.about{
	text-align: left;

	p{
		margin:1em 0;
	}
}

.toggle-content{
	display:none;
}

.active + .toggle-content{
	display:block;
}

b{
	font-weight: 700;
}

/* HOME PAGE STYLING */

.home{
	margin-top: 0;

	a.button{
		display:block;
		text-decoration: none;
		color:$color-off-white;
		border-radius:50px;
		margin:20px auto;
		padding:10px 20px;
		box-sizing:border-box;
		max-width:400px;

		@include gradient($color-blue-medium, $color-blue-dark);

		.btn-icon{
			width: 30px;
			height: 30px;
			margin-right: 20px;

			&.btn-icon-battle{
				background-image: url('../img/themes/sunflower/nav-battle-white.png');
			}

			&.btn-icon-rankings{
				background-image: url('../img/themes/sunflower/nav-rankings-white.png');
			}

			&.btn-icon-team{
				background-image: url('../img/themes/sunflower/nav-team-white.png');
			}

			&.btn-icon-train{
				background-image: url('../img/themes/sunflower/nav-train-white.png');
			}

			&.btn-icon-heart{
				background-image: url('../img/themes/sunflower/nav-heart-white.png');
			}

			&.btn-icon-tera{
				background-image: url('../img/themes/sunflower/nav-tera-white.png');
			}
		}

		h2{
			margin:0;
			font-size:24px;
			color:$color-off-white;

			&.no-logo{
				padding-left:0;
			}
		}

		p{
			margin:0;
			font-size:14px;
		}

		&.tera-button{
			background-image: url('../img/tera-button-bg.jpg') !important;
			background-size: cover !important;
			background-repeat: no-repeat;
			color: $color-off-white;

			h2{
				font-size: 24px;
			}
		}

		&.discord{
			background: #5567e3 !important;
			color: #fff;

			h2{
				color: #fff;
			}
		}

		&.github{
			background: #fff !important;
			color: #1f2328;

			h2{
				color: #1f2328;
			}
		}

		&.twitch{
			background: #9147ff !important;
			color: #fff;

			h2{
				color: #fff;
			}
		}

		&.patreon{
			background: #f96854 !important;
			color: #fff;

			h2{
				color: #fff;
			}
		}
	}

	h3{
		margin-bottom: 10px;
	}

	.flex.new-header{
		margin-top: 45px;
		justify-content: space-between;
		align-items: flex-end;

		a{
			font-size:14px;
			font-weight: bold;
			margin-bottom: 10px;
			padding-left: 20px;
			background-image: url('../img/themes/sunflower/rss-blue.png');
			background-repeat: no-repeat;
		}
	}
}

.icon-battle{
	background-image:url('../img/themes/sunflower/nav-battle-blue.png');
}

.icon-train{
	background-image:url('../img/themes/sunflower/nav-train-blue.png');
}

.icon-rankings{
	background-image:url('../img/themes/sunflower/nav-rankings-blue.png');
}

.icon-team{
	background-image:url('../img/themes/sunflower/nav-team-blue.png');
}

.icon-heart{
	background-image:url('../img/themes/sunflower/nav-heart-blue.png');
}

.icon-articles{
	background-image:url('../img/icon_articles.png');
}

.icon-moves{
	background-image:url('../img/icon_moves.png');
}

.icon-settings{
	background-image:url('../img/icon_settings.png');
}

.icon-twitter{
	background-image:url('../img/icon_twitter.png');
	min-height:18px;

	span{ display:none; }
}

.icon-unite{
	background-image:url('../img/favicon-unite.png');
	min-height:18px;

	span{ display:none; }
}

.icon-tera{
	background-image:url('../img/favicon_tera.png');
	min-height:18px;

	span{ display:none; }
}

/* POKEMON SELECT */
.poke-select-container{
	display: flex;
	justify-content: space-between;
}

.poke{
	position: relative;
	max-width:200px;

	&:nth-of-type(2){

		a.swap{
			display:none;
		}
	}

	input, select{
		display:block;
		margin:10px 0;
		max-width:200px;
		background: $color-off-white;
		border: 1px solid $color-off-black;

		&.poke-select{
			font-size:18px;
			font-weight:bold;
			border: 2px solid $color-blue-dark;

			option:checked{
				font-weight:bold;
				background:#ddd;
				color:#000;
			}
		}

		&.charged{
			font-weight:bold;
		}

		&.poke-search{
			background: $color-sky-blue;
		    border: 1px solid $color-blue-medium;
		    border-radius: 30px;
			padding: 3px 13px;

			&::placeholder{
				color:rgba(0, 52, 98, 0.75);
			}
		}
	}

	.form-select-container{
		visibility: hidden;
		position: relative;

		.form-select, a.form-link{
			display: block;
			width: 75%;
			margin: 10px 0 10px 25%;
			font-size: 11px;
			padding: 2px;
			box-sizing: border-box;
		}

		a.form-link{
			display: none;
			background: $color-off-white;
			border-radius: 4px;
			padding: 3px;
			border: 1px solid;
		}

		.form-select-border{
			position: absolute;
		    top: 0;
		    left: 4%;
		    width: 27px;
		    height: 11px;
		    border-left: 1px dashed;
		    border-bottom: 1px dashed;
		    opacity: 0.75;
		    display: block;
		    border-bottom-left-radius: 4px;

			&:after{
				content: "\25B6";
				position: absolute;
			    left: 19px;
			    top: 4px;
			    font-size: 11px;
				line-height: 14px;
			}
		}
	}

	button.auto-select{
		font-size: 11px;
	}

	.poke-search-container{
		width: 100%;
		align-items: center;

		.poke-search{
			margin: 0;
		}

		a.search-info{
			line-height: 16px;
		}
	}

	.poke-stats{
		box-sizing:border-box;
		border-radius: 8px;
		padding: 5px;
		max-width:200px;
		background:rgba(255, 255, 255, 0.9);
		display:none;

		.stat-container {
			font-size:10px;

			color:$color-off-black;

			.stat-label{
				width:80px;
				float:left;

				.label-name{
					display:inline-block;
					width:35px;
				}

				.stat{
					display: inline-block;
					font-size:14px;
					font-weight: bold;

					&.buff{
						color:$color-stat-buff;
					}

					&.debuff{
						color:$color-stat-debuff;
					}
				}
			}

			&.overall .stat-label{
				width:auto;
				margin-top: 5px;
				border-top: 1px solid #666;
				padding-top: 5px;
			}

			.bar-back{
				width:100px;
				height:4px;
				border-radius:8px;
				float:left;

				.bar{
					width:100%;
					height:100%;
					border-radius:8px;
					margin-top:7px;
					background:rgba(0,0,0,0.6);

					@include transition(width, 150ms, cubic-bezier(0.47, 0, 0.745, 0.715), 0s);
				}
			}
		}

		.damage-adjustments{
			display:flex;
			border: 1px solid #888;
		    border-radius: 8px;
			font-size:10px;
			justify-content: center;
			text-align: center;
			margin: 5px 0 10px 0;

			.adjustment{
				padding:5px;
				width:50%;

				.value{
					font-size:14px;
					font-weight:bold;

					&.buff{
						color:$color-stat-buff;
					}

					&.debuff{
						color:$color-stat-debuff;
					}
				}
			}

			.label{
				font-size:10px;
			}
		}

		.types {
			margin-bottom: 3px;
			text-align: center;

			.type{
				display:inline-block;
				border-radius: 4px;
				padding: 2px 8px;
				margin: 0 2px;
				font-size:12px;
			}
		}

		h3{
			margin:0;
			font-weight:normal;

			&.cp{
				text-align:center;

				.identifier{
					display:inline-block;
					width: 12px;
				    height: 12px;
				    border-radius: 20px;
					background:#9e34ef;
				}
			}

			&.section-title{
				font-size: 14px;
				font-weight: bold;
				color: $color-off-black;
				margin-top: 20px;
			}
		}


		a{
			font-size:14px;
			text-decoration:none;
			color:$color-links;
			display:block;
			margin:5px 0;

			&.clear-selection{
				margin-top:10px;
			}
		}

		.mega-cp-container{
			border: 1px solid #888;
		    border-radius: 8px;
		    padding: 5px;
			margin:10px 0;
			text-align: center;
			display: none;

			h3.section-title{
				font-size:12px;
				margin-top: 0;
			}

			.mega-cp{
				font-size:14px;
			}
		}

		.advanced-section{
			margin-top: 15px;

			.fields{
				display:none;

				.ivs input{
					display:block;
					width:27%;
					max-width:66px;
					background:none;
					border:none;
					border-bottom:1px solid #888;
					border-radius:0px;
					height: 22px;
					margin-bottom: 0;
					-moz-appearance:textfield; /* Firefox */

					&::-webkit-outer-spin-button,
					&::-webkit-inner-spin-button {
						/* display: none; <- Crashes Chrome on hover */
						-webkit-appearance: none;
						margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
					}

					&:focus{
						outline: none;
						border-bottom: 3px solid $color-gold;
					}

					&+ span{
						margin-top: 10px;
					}
				}

				input.level,
				label.level{
					width:25%;
				}

				label.iv{
					width: 27%;
					max-width:66px;
				}

				label{
					margin-top:3px;
					font-size: 12px;
					font-weight: bold;
					display: block;
				}

				.ivs-group{
					width:75%;
					display: flex;
					justify-content: end;
					align-items: center;

					&.labels{
						align-items: start;
					}
				}
			}

			.iv-rank{
				border:1px solid $color-off-black;
				cursor:pointer;

				.info-icon{
					color: rgba(255,255,255,.95);
					border: 1px solid rgba(255,255,255,.95);
					width: 14px;
					height: 14px;
					border-radius: 14px;
					display: flex;
					justify-content: center;
					align-items: center;
					float: right;
				}
			}

			.maximize-section{
				border: 1px solid #888;
			    border-radius: 8px;
			    padding: 5px;
				margin-bottom:15px;
				clear:both;

				.check{
					display: inline-block;
				    font-size: 12px;
				    margin-right: 3px;
				    margin-bottom: 5px;
					margin-top:0;

					span{
						width:12px;
						height:12px;
					}

					&.auto-level{
						margin-top:10px;
						text-align: left;
						display: block;
					}
				}

				.check-group{
					text-align: left;
				}

				.level-cap-group{
					display:flex;
					margin-top:10px;

					div:first-of-type{
						font-size: 12px;
						font-weight: bold;
						margin-right: 10px;
					}

					.check{
						margin-right:10px;
					}
				}


				.maximize-stats{
					font-size:12px;
					margin-top:5px;
				}

				.restore-default{
					font-size:12px;
				}
			}

			&.active{

				.fields{
					display:block;
				}
			}
		}

		.move-select.fast{
			margin-top:5px;
		}

	}

	.fifty-warning{
		display:none;
		font-weight: bold;
	    font-size: 12px;
	    padding: 4px;
	    border-radius: 8px;
	    border: 1px solid #000;
	    margin: 6px 0;
	}

	.legacy{
		font-size:12px;
		margin-top:10px;
	}

	.options{
		margin: 20px 0;

		.start-hp,
		.start-energy{
			margin:5px 0;
			max-width:120px;
			max-height: 23px;
		}

		.flex{
			justify-content: space-between;
		}

		.shield-baiting,
		.show-ivs,
		.optimize-timing,
		.switch-delay,
		.priority,
		.negate-fast-moves{
			text-align: left;
			font-size:14px;
		}

		.show-ivs{
			margin-bottom: 15px;
		}

		.add-fast-move{
			border: 1px solid #000;
		    outline: none;
		    border-radius: 8px;
		    cursor: pointer;
		    margin: 0px 0px 10px 40px;
		    float: right;

			&.dark{
				color:#fff;
			}
		}

		.label{
			font-size: 14px;
			margin:5px 5px 5px 0;
		}

		&.multi-battle-options{
			margin-top:0;

			h3:first-of-type{
				margin-top: 0;
			}
		}

		.stat-modifiers input{
			float:left;
			width:50%;
		}
	}

	.custom-ranking-options{
		display: none;
	}

	.form-group{
		border: 1px solid #888;
		border-radius: 30px;
		margin-bottom:15px;
		clear:both;
		display:flex;
		flex-wrap:wrap;
		margin-top:10px;
		overflow: hidden;

		.option{
			text-align: left;
			padding: 5px;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 12px;
			flex-grow: 1;
			margin-top: 0;
			border-left: 1px solid #999;
			border-right: 1px solid #999;
			background:#eee;
			cursor: pointer;

			&.on{
				background: #ddd;
				-moz-box-shadow:    inset 0 0 10px #999;
			    -webkit-box-shadow: inset 0 0 10px #999;
			    box-shadow:         inset 0 0 10px #999;

				div.icon{
					opacity: 1;
				}
			}

			&:first-of-type{
				border-left: none;
			}

			&:last-of-type{
				border-right: none;
			}
		}


		&.shield-picker{
			.option{
				font-size: 14px;
			}

			div.icon{
				width: 20px;
				height: 15px;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: contain;
				margin-right: 5px;
				opacity: 0.25;
			}

			.option:nth-of-type(1) div.icon{
				background-image: url('../img/shield_none.png');
			}

			.option:nth-of-type(2) div.icon{
				background-image: url('../img/shield.png?v=2');
			}

			.option:nth-of-type(3) div.icon{
				background-image: url('../img/shield_two.png?v=2');
				width: 25px;
				height: 20px;
			}
		}
	}

	a.random,
	a.swap{
		font-size: 10px;
		text-decoration: none;
		color: #003748;
		padding: 3px 3px 3px 16px;
		background: rgba(255,255,255,.75);
		display: inline-block;
		background-image:url('../img/random.png');
		background-repeat: no-repeat;
		background-position: 3px center;
		border-radius:8px;
		margin-bottom:10px;
	}

	a.swap{
		padding-left:18px;
		background-image:url('../img/swap.png');
	}

	.move-details{
		font-size:12px;
		min-height:15px;
		text-align: left;

		.move-stat{

			color:$color-off-black;

			.stat{
				width: 15px;
				height: 14px;
				background: #999;
				display: inline-block;
				border-radius: 15px;
				text-align: center;
				padding-top: 1px;
				color:$color-off-white;
				font-weight:bold;
				background:$color-off-black;
			}
		}
	}

	&.multi{
		display:none;
		float:right;

		.poke-stats{
			display:block;
		}

		.poke-count.error{
			color:#e66a19;
		}

		.format-select, .cup-select{
			margin-top:62px;
		}

		.cup-select{
			width:100%;

			option[value="custom"]{
				display:block;
			}
		}

		.charged-count-select{
			display:none !important;
		}

		.custom-options{
			display:none;

			a.custom-group-sort{
				font-weight: bold;
				font-size: 12px;
				text-decoration: underline;
				visibility: hidden;
				padding-right: 20px;
			}

			.check.show-move-counts{
				visibility: hidden;
				margin-top: 5px;
				font-size: 12px;

				span{
					width: 12px;
					height: 12px;
				}
			}

			.add-poke-btn{
				font-size: 14px;
				width: 100%;
				margin-bottom: 0;
			}

			.export-btn,
			.search-string-btn{
				margin-top:15px;
			}

			.rankings-container{
				margin-top: 5px;
			}

			p{
				font-size:12px;
			}
		}

		.search-string-btn{
			margin-top: 5px;
			margin-bottom: 15px;
		}

		.rankings-container{

			max-height:410px;

			.rank{
				padding: 0;
				position:relative;
				border-left:none;
				display:flex;

				&:after{
					border-radius: 8px;
				}

				&.warning{
					border: 3px solid #e66a19;
				}

				.name-container{
					float:none;
					flex: 1;
					padding: 5px;
					font-size: 15px;

					.number{
						padding-right: 5px;
					    font-size: 12px;
					    font-weight: normal;
					    opacity: 0.5;
					}
				}

				span.cliffhanger-points{
					font-weight: normal;
				    border: 1px solid;
				    border-radius: 20px;
				    width: 20px;
				    display: inline-block;
				    text-align: center;
				    margin-right: 5px;
				    padding-top: 2px;
				}

				.name{
					display: block;
				}

				.moves{
					margin-left:0;
				}

				.remove{

					font-weight: bold;
					background:rgba(0,0,0,0.5);
					color:$color-off-white;
					border-radius: 4px;
					padding: 2px 6px;
					font-size: 14px;
					height: 20px;

					&:after{
						content:"x";
					}

					&:hover{
						background:#ff0000;
					}
				}
			}
		}

		.quick-fill-buttons{
			justify-content: space-around;
		}

		.team-warning{
			display:none;
			font-size: 12px;
		    font-weight: bold;
		    padding: 4px;
		    border-radius: 8px;
		    border: 3px solid #e66a19;
		    color: #e66a19;
			margin-bottom:5px;
		}
	}

	.energy-label{
		margin-top: 15px;
	    text-align: center;
	    font-size: 12px;
	    line-height: 14px;

		.num{
			font-size: 16px;
			font-weight: bold;
		}
	}

	.hp.bar-back .bar.damage{
		display:none;

		-webkit-animation: BLINK_ANIMATION 2s infinite;
	     -moz-animation: BLINK_ANIMATION 2s infinite;
	     -o-animation: BLINK_ANIMATION 2s infinite;
	      animation: BLINK_ANIMATION 2s infinite;
	}

}

.hp.bar-back{
	position:relative;
	border:3px solid #666;
	padding:2px;
	width:100%;
	height:30px;
	box-sizing:border-box;
	margin-top:20px;
	background: $color-blue-dark;

	.bar{
		width:100%;
		height:100%;
		background-color:$color-hp;

		&[color="yellow"]{
			background-color:$color-hp-yellow;
		}

		&[color="red"]{
			background-color:$color-hp-red;
		}

		&:nth-child(1){
			@include transition(width, 0.2s, ease-out, 0s);
		}

		&.damage{
			background:#ff5115;
			position: relative;
			max-width: 100%;
			top: -20px;
		}
	}

	.stat{
		position:absolute;
		width:100%;
		left:0;
		top:4px;
		text-align:center;
		color:#fff;
		font-weight:bold;
	}
}


.move-bars{
	display:flex;
	justify-content: space-around;

	.move-bar{
		width: 40px;
		height: 40px;
		border-radius: 40px;
		position: relative;
		overflow: hidden;
		border: 2px solid rgba(0,0,0,0);
		box-sizing: border-box;
		color:$color-off-white;
		margin-top:5px;
		display:none;
		cursor:pointer;

		@include transition(border, 0.2s, ease-out, 0s);

		&.active{
			border:2px solid rgba(0,0,0,1);
		}

		.label{
			position: relative;
			z-index: 10;
			text-align: center;
			padding-top: 10px;
			font-weight: bold;
			pointer-events: none;
		}

		.bar, .bar-back{
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 100%;
			pointer-events: none;
		}

		.bar{
			@include transition(height, 0.2s, ease-out, 0s);

			bottom: -5%;

			&:nth-child(3),
			&:nth-child(4)
			{
				border-top: 2px solid #fff;
				background:rgba(0,0,0,0.35) !important;
			}
		}

		.bar-back{
			opacity:.3;
		}
	}
}

.poke-select-container{
	&.single{

		.poke:nth-of-type(1){
			a.random{
				float:left;
			}
			a.swap{
				float:right;
			}
		}

		.poke:nth-of-type(2){
			display: block;

			a.random{
				float:right;
			}

			a.swap{
				float:left;
			}
		}
		.poke:nth-of-type(3){
			display: none;
		}
	}

	&.multi{
		.poke:nth-of-type(2),
		.hp.bar-back,
		.move-bars{
			display: none;
		}
		.poke:nth-of-type(3){
			display: block;
		}
		a.swap{
			display:none;
		}
	}

	&.matrix{
		.poke.single,
		.hp.bar-back,
		.move-bars{
			display: none;
		}
		.poke.multi
		{
			display: block;
			float:left;

			.format-select, .cup-select{
				display:none;
			}

			.custom-options{
				display:block;
			}

			.multi-battle-options{
				display: none;
			}
		}

		.delete-list-confirm + .poke.multi{
			float:right;
		}
	}

	&.train .swap{
		display: none;
	}
}

.arrow-up{
	display: none;
}

.active{
	.arrow-up{
		display:inline;
	}

	.arrow-down{
		display:none;
	}
}


.iv-rank{
	color: rgba(255,255,255, 0.95);
	font-size:13px;
	font-weight: bold;
	margin: 10px 0;
	background: rgba(0,0,0,0.25);
    padding: 6px;
    border-radius: 8px;

	.count{
		font-weight: normal;
		font-size: 12px;
	}
}

.modal .iv-rank-details{

	h3{
		border-bottom: 1px solid $color-off-black;
		margin-bottom: 6px;
		margin-top: 0;
		padding-bottom: 3px;
	}

	.stats-table{
		width:100%;

		thead{
			font-weight: bold;
		}
	}

	.iv-rank{
		margin: 2px 0;
	}

	.iv-rank-result{
		margin-top: 30px;

		&.primary{
			border: 1px solid #111;
		    padding: 6px;
		    border-radius: 8px;
			margin-top: 0;
		}

		td.league-1500,
		td.league-2500{
			padding-left: 28px;
		    background-repeat: no-repeat;
		    background-size: 20px;
		    background-position: 4px center;

			&.league-1500{ background-image: url('../img/themes/sunflower/nav-league-great.png'); }
			&.league-2500{ background-image: url('../img/themes/sunflower/nav-league-ultra.png'); }
		}
	}
}
/* GENERAL FORM STYLING */

select, input{
	border-radius:30px;
	padding:3px 8px;
	box-sizing:border-box;
	width:100%;
	color:$color-off-black;
	border: 2px solid $color-blue-medium;
}

.league-select,
.cup-select,
.format-select,
.slot-select,
.mode-select,
.category-select{
    font-size: 18px;
    width: auto;
    background: none;
    font-weight: bold;
	max-width:250px;
}

.button{
	position: relative;
	display:block;
	font-size:18px;
	margin:10px auto;
    border-radius: 30px;
    border: 2px solid #000;
    padding: 10px 18px;
    cursor: pointer;
	text-decoration: none;
	color:#000;

	@include gradient($color-gold, $color-gold-dark);

	&.update-btn{
		display:none;
	}

	span.btn-content-wrap{
		display: flex;
		align-items: center;
	}

	span.btn-icon{
		display: inline-block;
		width: 20px;
		height: 20px;
		background-repeat: no-repeat;
		background-size: 100%;
		margin-right: 8px;

		&.btn-icon-battle{
			background-image: url('../img/themes/sunflower/nav-battle-blue.png');
		}

		&.btn-icon-team{
			background-image: url('../img/themes/sunflower/nav-team-blue.png');
		}

		&.btn-icon-train{
			background-image: url('../img/themes/sunflower/nav-train-blue.png');
		}
	}

	span.btn-label{
		flex: 1;
	}
}

.button, .button-highlight, select, .advanced-section .iv-rank{
	position: relative;
	box-shadow: 2px 2px 2px rgba(0, 0, 0, .1);

	@include transition(box-shadow, .25s, ease-out, 0s);

	&:after{
		content: " ";
		position:absolute;
		top: 0;
		left: 0;
		width:100%;
		height: 100%;
		background:rgba(255,255,255,0);
		pointer-events: none;
		border-radius: 50px;

		@include transition(background-color, .25s, ease-out, 0s);
	}

	&:hover{
		box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);

		@include transition(box-shadow, 0s, ease-out, 0s);

		&:after{
			background-color:rgba(255,255,255,0.1);

			@include transition(background-color, 0s, ease-out, 0s);
		}
	}
}

.pokebox{
	a.open-pokebox{
		display:block;
		border:1px solid #000;
		padding: 10px;
	    cursor: pointer;
		text-decoration: none;
		color:#000;
		margin:10px auto;
	    border-radius: 30px;
		background:url('../img/pokebattler_logo.png') $color-off-white;
		background-size: 24px;
	    background-position: 5px center;
	    background-repeat: no-repeat;
		padding: 8px 5px 8px 25px;
		text-align: center;
		font-size: 12px;


		span:first-of-type{
			margin-right: 3px;
		}
	}

}

.share-link-container{

	text-align: center;
	margin-top:20px;

	p{
		margin:0;
	}
}

.share-link,
.rank .details .share-link.detail-section{
	display: inline-block;
	background: $color-blue-light;
	background-image:url('../img/link.png');
	background-repeat: no-repeat;
	background-position: 6px center;
	background-size: 20px;
	padding: 5px 5px 5px 35px;
	border-radius: 8px;
	margin:5px auto;
	box-shadow: 2px 2px 5px rgba(0,0,0,0.5);


	input{
		width: auto;
		display: inline-block;
		border: none;
		background: $color-blue-dark;
		color: $color-off-white;
		padding: 5px;
	}

	.copy{
		display: inline-block;
		font-weight: bold;
		font-size:13px;
		cursor: pointer;
		color: $color-off-white;
		margin: 5px;
		border-radius: 8px;
	}
}

.continue-container{
	margin:15px auto;

	p{
		margin:0;
	}

	.name{
		font-weight:bold;
	}

	.button{
		display:inline-block;
		margin:5px auto;
	}
}


.check{
	margin-top:10px;
	cursor: pointer;

	span{
		display: block;
		width: 17px;
		height: 17px;
		border-radius: 8px;
		background: $color-off-black;
		border: 2px solid $color-off-black;
		box-sizing:border-box;
		margin: 0px 5px 0 0;
		float: left;
	}

	&.on{
		span{
			@include gradient($color-gold, $color-gold-dark);
		}
	}

	&:hover:not(.on){
		span{
			background:#555;
		}
	}
}


/* BATTLE TIMELINE */

.battle-results{
	display:none;
	text-align: center;

	.timeline-container{
		max-width:95%;
		margin: 0 auto;
		background: rgba(255, 255, 255, 0.5);
		height: 80px;
		border-radius:8px;
		border:1px solid #000;
		position:relative;
		overflow:hidden;

		.tracker{
			position:absolute;
			width:1px;
			height:100%;
			border-left:1px solid #000;
			top:0;
		}

		&.sandbox-mode{
			background-color: rgba(255, 240, 200, 0.75);
			border: 1px solid $color-gold;

			.item.charged:hover,
			.item.interaction:hover,
			.item.fast:not(.disabled):hover{
				@include gradient($color-gold, $color-gold-dark);
				border-color:$color-gold-dark;
			}

			.item.disabled{
				opacity: 0.5;

				&:hover{
					cursor:initial;
				}
			}

			.item.interaction{
				display: block !important;
				border:2px solid #000;
				cursor:pointer;

				&.disabled{
					cursor:pointer;
					border:2px solid #666;
				}

				&.both{
					border: 3px solid #000;
				}

				&.wait{
					border-style:dotted;
				}
			}
		}

		&.zoom{
			height:90px;
			overflow-x:scroll;
		}
	}

	.sandbox-btn-container{
		max-width:95%;
		margin: 0 auto;

		.sandbox-btn{
			@include transition(background, 0.25s, 0s, ease-out);


			position: relative;
			background:#888;
			color:$color-off-white;
			border-radius: 12px;
			font-size:17px;
			line-height:16px;
			text-decoration: none;
			cursor: pointer;
			padding: 1px 8px;
			margin-bottom:5px;
			float:right;
			overflow:hidden;

		  -webkit-touch-callout: none; /* iOS Safari */
			-webkit-user-select: none; /* Safari */
			 -khtml-user-select: none; /* Konqueror HTML */
			   -moz-user-select: none; /* Firefox */
				-ms-user-select: none; /* Internet Explorer/Edge */
					user-select: none; /* Non-prefixed version, currently
										  supported by Chrome and Opera */

			span{
				position: relative;
				z-index:5;
			}

			&.active{
				color:$color-off-black;
				background:$color-gold;

				.btn-background{
					left:0;
				}
			}
		}

		.sandbox{
			width:19px;
			height:19px;
			border-radius:12px;
			background:$color-gold;
			float:right;
			margin: 0 10px;
			cursor:pointer;

			@include transform(scale(0, 0));
			@include transition(transform, 0.3s, cubic-bezier(0.64, 0.57, 0.67, 1.53), 0s);

			&.active{
				@include transform(scale(1, 1));
			}
		}

		.clear-btn:after{
			content:"\021BB";
		}
	}

	.playback{
		max-width: 300px;
    	margin: 10px auto;
		padding: 2px 10px;

		.flex{
			display:flex;
			justify-content: space-between;
		}

		.playback-btn{
			width:16px;
			height:19px;
			background-position: center center;
			background-repeat:no-repeat;

			&.play{
				background-image:url('../img/playback_play.png');
			}

			&.play.active{
				background-image:url('../img/playback_pause.png');
			}

			&.replay{
				background-image:url('../img/playback_replay.png');
			}
		}

		.playback-speed,
		.playback-scale{
			padding: 0;
			background: none;
			border-radius: 8px;
			width: 85px;
		}

		.disclaimer{
			display:none;
			font-size: 12px;
			margin-top:10px;
		}
	}

	.bulk-btn{
		font-size: 14px;
		margin-top: 10px;
		margin-bottom: 10px;
		color:$color-off-white;
		background:url('../img/histogram-icon.png') no-repeat 8px center, rgb(62, 94, 172) !important;
		padding-left:30px;
	}

	.summary{
		display:inline-block;
		margin:15px auto;
		font-size:18px;
		line-height: 26px;

		.battle-summary-line{
			display: flex;
			justify-content: center;
			align-items: center;
			flex-wrap: wrap;

			span{
				margin-right:6px;
			}

			span.time{
				margin-left: 6px;
			}

			span.rating{
				margin: 0 0 0 6px;
				padding: 2px 8px;
				border-radius: 30px;

				span{
					padding: 0;
				}
			}
		}

		span.time,
		span.name{
			padding:0;
		}

		.disclaimer{
			margin:15px 0;
			font-size:14px;
			text-align: left;
		}

		p{
			font-size:14px;
		}

		.turn-margin-description{
			font-size: 12px;
			margin-top: 15px;
			line-height: 18px;

			span.turn-margin{
				color:#000;
			}

			span.turn-margin[value="extreme"]{ background:#ff4d4d; }
			span.turn-margin[value="low"]{ background:#fff943; }
			span.turn-margin[value="medium"]{ background:#94ff43; }
			span.turn-margin[value="high"]{ background:#43ff85; }
		}

		.bulk-outcomes{
			display:flex;
			justify-content: center;
			flex-wrap: wrap;

			.outcome{
				padding: 0 2px;
			}

			a.rating{
				span{
					padding: 0;
					margin: 0 6px 0 0;
				}
			}

			.outcome-label{
				font-size: 14px;
			    font-weight: bold;
			    line-height: 16px;
			    min-height: 40px;
			    display: flex;
			    justify-content: center;
			    align-items: center;
			}
		}

		.histogram span{
			padding:0;
		}
	}

	.tip{
		margin:15px;
		text-align:center;
		font-size: 12px;
	}

	.battle-details,
	&.matrix{
		text-align: left;

		table{
			margin: 10px auto 0 auto;
			text-align: center;

			.name{
				font-size:14px;
				font-weight: bold;
			}

			th span{
				font-weight: normal;
				font-size:11px;
				display:block;
			}

			.rating.star{
				display: block;
				background-image:url('../img/starburst_red.png');
				text-decoration: none;

				&.win{
					background-image:url('../img/starburst.png');
				}
			}

			&[mode='attack'] .rating{
				font-size:14px;
				line-height:18px;
				padding: 5px 10px 5px 31px;
			}
		}

		.breakpoints-section,
		.matchup-detail-section,
		.cmp-section,
		.optimal-timing-section{
			max-width:340px;
			margin: 0 auto 35px auto;

			.bold{
				font-weight:bold;
			}

			.ivs .button{
				font-size:14px;
				margin:0;
				padding: 5px;
				border-radius: 8px;
				border: none;
				background:$color-off-black !important;
				color:$color-off-white;
			}

			.golden-combination .button{
				display:inline-block;
				font-size:14px;
				padding: 5px 15px;
				margin:0;
				font-weight:bold;
				border: none;
				background:$color-off-black !important;
				color:$color-off-white;
			}

			.optimal-timing-timeline{
				width: 100%;
				margin: 1em auto 0 auto;
				background: rgba(255, 255, 255, 0.5);
				height: 80px;
				border-radius:8px;
				border:1px solid #000;
				position:relative;
				overflow:hidden;
				display: flex;
				flex-wrap: wrap;
				align-items: center;

				.timeline{
					display: flex;
					border: none;
					width: 100%;
					margin: 0px !important;

					&:first-of-type{
						margin-top: 15px !important;
					}

					&:last-of-type{
						margin-bottom: 15px !important;
					}

					.item{
						display: flex;
						height: 20px;
						border: 1px solid #000;
						box-sizing: border-box;

						&.fade{
							opacity: 0.4;

							.chunk{
								border: 1px dashed rgba(0,0,0,0.25);
							}
						}

						.chunk{
							flex: 1;
							border: 1px dashed rgba(0,0,0,0.125);
							box-sizing:border-box;
						}
					}
				}
			}

			.optimal-1, .optimal-2, .optimal-3{
				font-weight: bold;
			}
		}

		select.breakpoint-move,
		select.bulkpoint-move{
			margin-top: 15px;
		    font-size: 16px;
		    font-weight: bold;
		}

		.ranking-categories{
			float: none;
			margin-top:10px;
		}

		p{
			font-size:14px;
			margin-bottom:0 !important;
		}
	}

	.battle-details .rating{
		border-radius: 30px;
	}

	&.multi{
		text-align: left;

		.poke-search{
			margin-top:15px;
		}

		.button{
			display:inline-block;
		}

		.rankings-container{
			.rank:hover{
				border-left:none;
				cursor:initial;
			}
		}

		.multi-battle-sort{
			display: block;
			margin-top: 15px;
			margin-left: 20px;
		}
	}

	&.matrix{
		.table-container{
			overflow:scroll;
			max-height: 500px;
		}

		.rating-table{
			margin-top: 0;

			th.selected, td.selected{
				background: #546fe8;
			}
		}

		.breakpoint-mode{
			max-width: 150px;
			margin: 10px 0;
		}

		.difference-table{
			width:100%;

			th:nth-of-type(2){
				padding: 3px 6px;
				width: 100px;
			}

			th.number{
				padding-left: 5px;
			}

			td{
				text-align: left;
			}

			tr:first-of-type{
				background: #444;
				color: #eee;
			}

			.differences{
				font-size: 12px;
			}


			a.difference{
				color: #fff6d4;
				font-size: 12px;
				font-weight: bold;
				border-radius: 8px;
				display: inline-block;
				text-decoration: none;
				text-align: center;
				padding: 3px 6px;
				margin: 0 5px 5px 0;

				span{
					font-weight:normal;
					font-size: 11px;
				}

				&.win{
					background: #1073d3;
				}

				&.lose{
					background: #9e2085;
				}
			}
		}
	}
}

a.download-csv{
	display:none;
}

.rating-table{
	text-align: center;

	tr{
		background:none;
	}

	thead th {
	  position: -webkit-sticky; /* for Safari */
	  position: sticky;
	  top: 0;

	  background: #444;
	  color: #eee;
	  font-size:14px;
	  font-weight:normal;
	  z-index: 11;
	}

	tbody th {
	  position: -webkit-sticky; /* for Safari */
	  position: sticky;
	  left: 0;
	  background: #fcfcfc;
  	  text-align:center;
	  padding:2px 5px;
	  z-index: 10;
	}

	tr:nth-of-type(2n) th{
		background: #ddd;
	}

	.shield{
		display:inline-block;
		width:17px;
		height:16px;
		background-size:contain;
		background-image:url('../img/shield.png');

		&.shield-none{
			background-image:url('../img/shield_none.png');
		}

		&.shield-double{
			width:36px;
		}
	}

	a.rating{
		padding: 5px 8px;

		&.margin-6{
			span{
				margin: 0 6px 0 0;
			}
		}
	}
}

.matchup-detail-section .rating-table{
	td.y-axis{
		display: flex;
	    align-items: center;
	    justify-content: space-between;
	    height: 30px;
		width: 30px;


		.shield{
			height:30px;
			background-repeat: no-repeat;
			background-position: center;
		}
	}

	.x-axis{
		display: flex;
	    justify-content: center;
	    align-items: center;

		.shield{
			margin-left: 4px;
		}
	}
}

.detail-section.performance{
	position: relative;

	button.ranking-compare{
		position: absolute;
	    top: 12px;
	    right: 5px;
	    border-radius: 8px;
		border: 1px solid #000;
	    font-size: 12px;
		background: none;
		cursor: pointer;
		z-index: 5;
		max-width: 120px;

		&.dark, &.dragon{
			color:#fff;
		}
	}

	.pokemon-compare-label{
		font-size: 12px;
		font-weight: bold;
	}
}

.timeline{
	border-top:1px dashed #000;
	position:relative;
	width:100%;
	margin: 25px 0 30px 0;
	top:0;
	left:0;

	&:nth-of-type(2){
		margin: 35px 0 25px 0;
	}

	.item-container{
		position:absolute;
		top:0;


		.item{
			display:block;
			cursor:pointer;
			position:relative;
			left:-50%;
			z-index:10;
			text-decoration:none;

			@include transform(scale(0, 0));
			@include transition(transform, 0.3s, cubic-bezier(0.64, 0.57, 0.67, 1.53), 0s);

			&.fast{
				width:7px;
				height:8px;
				top:-5px;
				border:1px solid #000;
				z-index:10;
			}

			&.charged{
				width:20px;
				height:20px;
				border-radius:20px;
				top:-12px;
				border:2px solid #000;
				z-index: 10;
			}

			&.shield{
				width:22px;
				height:20px;
				top:-10px;
				background:url('../img/shield.png');
				z-index: 10;
			}

			&.faint{
				color:$color-faint;
				font-size:20px;
				top: -11px;
				font-weight: bold;
			}

			&.faint::after{
				content:"X";
			}

			&.tap{
				width:6px;
				height:2px;
				top:-2px;
				background:#000;
				cursor:initial;
				border:2px solid #000;
				border-radius:12px;
				background:none !important;

				&.interaction{
					display:none;
				}
			}

			&.buff{
				background-image:url('../img/move_buff.png') !important;
				background-position: center 0% !important;
				background-repeat: no-repeat !important;

				-webkit-animation: BUFF_ANIMATION 1.5s infinite; /* Safari 4+ */
				-moz-animation:    BUFF_ANIMATION 1.5s infinite; /* Fx 5+ */
				-o-animation:      BUFF_ANIMATION 1.5s infinite; /* Opera 12+ */
				animation:         BUFF_ANIMATION 1.5s infinite; /* IE 10+, Fx 29+ */
				animation-timing-function: linear;
			}

			&.debuff{
				background-image:url('../img/move_debuff.png') !important;
				background-position: center 50% !important;
				background-repeat: no-repeat !important;

				-webkit-animation: DEBUFF_ANIMATION 1.5s infinite; /* Safari 4+ */
				-moz-animation:    DEBUFF_ANIMATION 1.5s infinite; /* Fx 5+ */
				-o-animation:      DEBUFF_ANIMATION 1.5s infinite; /* Opera 12+ */
				animation:         DEBUFF_ANIMATION 1.5s infinite; /* IE 10+, Fx 29+ */
				animation-timing-function: linear;
			}

			&.switchAvailable{
				width:12px;
				height:12px;
				border-radius:12px;
				background:#fff;
				border:1px solid #333;
				top:-21px;
				z-index: 10;
			}

			&.switchAvailable:after{
				font-size:11px;
				color:#333;
				content:"\021C5";
				position: relative;
			    top: -4px;
			}

			&.active{
				@include transform(scale(1, 1));
			}

		}
	}
}

.sandbox{
	display:none;
}

.stats-table{
	.label{
		max-width:85px;
		text-align: left;
		font-weight: bold;
		font-size:12px;

		span:after{
			font-size:14px;
			content:"\025B4";
			visibility: hidden;
		}

		&.asc span:after{
			visibility: visible;
			content:"\025B4";
		}

		&.desc span:after{
			visibility: visible;
			content:"\025BE";
		}
	}

	tr:nth-child(2n){
		background: rgba(0,0,0,.15);
	}

	td{
		padding:5px;
	}

	span.type{
		display:block;
		border-radius:8px;
		padding:2px 4px;
		text-align: center;

		&.dark,
		&.dragon,
		&.ghost{
			color:$color-off-white;
		}
	}

	&.sortable-table{

		.label{
			cursor:pointer;

			-webkit-touch-callout: none; /* iOS Safari */
			-webkit-user-select: none; /* Safari */
			-khtml-user-select: none; /* Konqueror HTML */
			-moz-user-select: none; /* Firefox */
			-ms-user-select: none; /* Internet Explorer/Edge */
				user-select: none; /* Non-prefixed version, currently
									  supported by Chrome and Opera */
		}
	}

	.status-effect-description{
		font-size:12px;
	}

	.rating span{
		margin: 0 6px 0 0 !important;
	}
}

/* Buff and debuff background animations */

@-webkit-keyframes BUFF_ANIMATION {
  0%   { background-position: center -50%; }
  100% { background-position: center 150%; }
}
@-moz-keyframes BUFF_ANIMATION {
  0%   { background-position: center -50%; }
  100% { background-position: center 150%; }
}
@-o-keyframes BUFF_ANIMATION {
  0%   { background-position: center -50%; }
  100% { background-position: center 150%; }
}
@keyframes BUFF_ANIMATION {
  0%   { background-position: center -50%; }
  100% { background-position: center 150%; }
}


@-webkit-keyframes DEBUFF_ANIMATION {
  0%   { background-position: center 150%; }
  100% { background-position: center -50%; }
}
@-moz-keyframes DEBUFF_ANIMATION {
  0%   { background-position: center 150%; }
  100% { background-position: center -50%; }
}
@-o-keyframes DEBUFF_ANIMATION {
  0%   { background-position: center 150%; }
  100% { background-position: center -50%; }
}
@keyframes DEBUFF_ANIMATION {
  0%   { background-position: center 150%; }
  100% { background-position: center -50%; }
}

@-webkit-keyframes BLINK_ANIMATION {
	0%   { opacity:1; }
    50%  { opacity:.75; }
    100% { opacity:1; }
}
@-moz-keyframes BLINK_ANIMATION {
	0%   { opacity:1; }
    50%  { opacity:.75; }
    100% { opacity:1; }
}
@-o-keyframes BLINK_ANIMATION {
	0%   { opacity:1; }
    50%  { opacity:.75; }
    100% { opacity:1; }
}
@keyframes BLINK_ANIMATION {
	0%   { opacity:1; }
    50%  { opacity:.75; }
    100% { opacity:1; }
}


.tooltip{
	position:absolute;
	border-radius:8px;
	padding:5px;
	width:130px;
	text-align: left;
	z-index:20;
	pointer-events: none;

	.details{
		font-size:14px;

		span.label{
			font-size:12px;
			font-weight: bold;
			font-style: italic;
		}
	}

	&.faint{
		background:$color-faint;
	}

	&.shield{
		background:$color-shield;
	}

	&.tap{
		background:#eee;
	}

	&.dark{
		color:#fff;
	}

	&.switchAvailable{
		background:#fff;
	}

	h3{
		margin:0;
		font-size:14px;
	}
}

/* RANKING STYLING */

.ranking-header {
	width:50%;
	float:left;
	font-weight: bold;
	padding:5px 0px;
	box-sizing:border-box;

	&.right{
		text-align: right;
	}
}

.rankings-container {
	margin-top:20px;
	max-height:600px;
	overflow-y:scroll;

	&.threats,
	&.alternatives{
		overflow-y:initial;
	}

	& > .rank:hover, > .rank.selected{
		border-left:20px solid $color-highlight;
	}

	&.pokebox-list{
		display:flex;
		flex-wrap: wrap;
		justify-content: space-between;
		max-height:350px;
		margin-top:0;

		.rank{
		    width: 49%;
		    border-left: none;
		    box-sizing: border-box;
			border:3px solid rgba(0,0,0,0);

			&:hover{
				border:3px solid rgba(0,0,0,.4);
			}

			&.selected{
				border:3px solid #000;
			}
		}

		.name-container{
			float:none;
			padding:0;
		}
	}
}

.rank{
	padding:5px 10px;
	border-radius:8px;
	margin-bottom:5px;
	border-left: 20px solid rgba(0,0,0,.10);
	position:relative;

	&.hide{
		display:none !important;
	}

	&.selected .expand-label:after{
		content:"-";
	}

	.expand-label{
		position: absolute;
		color: rgba(255,255,255,.75);
		left: -16px;
		font-size: 24px;
		top: 10px;
		width: 14px;
		text-align: center;

		&:after{
			content:"+";
		}
	}

	.name-container{
		float: left;
		padding:5px 0;
	}

	.number{
		padding-right:10px;
	}
	.name{
		font-weight:bold;
	}

	.xl-info-icon{
		display: inline-block;
	    margin-left: 10px;
	    opacity: .3;
		position: relative;
		top: 3px;
		width: 22px;
		height: 18px;
		background: url('../img/xl-icon-small.png');
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	.rating-container{
		.rating{
			float:right;
			color:$color-ranking;
			background-color: rgba(0,0,0,.6);
			border-radius: 8px;
			text-align: right;
			padding: 2px 6px;
		    font-size: 15px;

			&.score-rating{
				width:55px !important;
			}

			span{
				margin-left: 0;
				margin-right: 6px;
			}
		}

		/*a{
			display:inline-block;
			width:32px;
			height:28px;
			background-image:url('../img/eye.png');
			background-repeat: no-repeat;
			background-position: 0 center;
			margin-right:10px;
			float:right;
			opacity: .5;
		}*/
	}


	.moves{
		display:block;
		font-size:12px;
		max-width: 300px;

		.move{
			width: 16px;
			height: 16px;
			border: 1px solid #000;
			border-radius: 12px;
			padding: 3px;
			line-height: 16px;
			text-align: center;
			margin-right:5px;

			&:first-of-type{
				border:none;
			}
		}

		.count{
			display:none;
			margin-left:5px;
			background:rgba(0, 0, 0, 0.25);
			color:#fff;
			border-radius: 20px;
			min-width: 16px;
			text-align: center;
			font-size:11px;
			padding-top: 1px;

			&.fast{
				border-radius: 0;
			}
		}
	}

	.cliffhanger-points{
		border: 1px solid;
		border-radius: 8px;
		padding: 0 2px;
		margin-right: 5px;
		text-align: center;
		width: 38px;
		position: relative;
		top: -1px;
	}

	.moveset{
		.rating{
			background: rgba(0,0,0,.7);
			padding:5px;
		}
	}

	.details{
		display:none;
		cursor:initial;

		&.active{
			display:flex;
			flex-wrap:wrap;
		}

		.detail-section{
			background:rgba(255,255,255,0.8);
			padding:5px;
			border-radius:8px;
			box-sizing: border-box;
			margin-top:5px;

			&.float{
				width: 49.5%;

				&.margin{
					margin-right:1%;
				}
			}

			&.overall,
			&.stats,
			&.typing{
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-around;

				.rating-container{
					text-align:center;
					padding:5px;

					.ranking-header{
						float:none;
						margin: 0;
						width: 100%;
						padding: 0 5px;
						font-size: 14px;
					}

					.rating{
						padding: 5px;
						display: inline-block;
						float: none;
						text-align: center;
						margin: 0 auto;
						font-weight:bold;
						background-color: rgba(0,0,0,.65);
					}
				}

				.type{
					display:inline-block;
					text-transform: capitalize;
					padding: 2px 12px;
				}
			}

			&.traits-container,
			&.similar-pokemon,
			&.partner-pokemon{
				width:100%;

				.ranking-header{
					float:none;
					width:100%;

					a.trait-info{
						text-decoration: none;
					    color: #00171c;
					    border: 1px solid #00171c;
					    border-radius: 20px;
					    text-align: center;
					    width: 15px;
					    height: 15px;
					    font-weight: bold;
					    margin-left: 5px;
						display: inline-block;
						font-size: 12px;
						position: relative;
						top: -4px;
					}
				}

				.list{
					justify-content: center;
				}
			}

			.stat-details{
				.stat-row{
					display: flex;
					align-items: baseline;
					margin:5px 0;

					&.overall{
						border-bottom: 1px solid;
						padding-bottom: 10px;
						margin-bottom: 20px;

						.value{
							font-size:16px;
					    	background-color: rgba(0, 0, 0, 0.65);
							text-align: center;
							color:#fff6d4;
						}
					}

					&.level, &.rank-1{
						.value{
							text-align: left;
							padding:0;
						}
					}

					.label{
						font-size:12px;
						min-width:50px;
					}

					.value{
						font-size:14px;
						font-weight: bold;
						padding: 2px 5px;
					    text-align: right;
					    font-weight: bold;
						border-radius: 8px;
						min-width:45px;
						margin-right:5px;
					}

					.bar-container{
						display:flex;
						flex-grow: 1;
						overflow: hidden;
						align-items: center;

						.bar{
							width:0px;
							height:10px;
							border-radius:4px;

							&.shadow{
								position:relative;
								background:rgba(158,52,259,0.5);
							}
						}

						.shadow-mult{
							background:#9e34ef;
							color:#fff;
							padding:2px 4px;
							font-size:12px;
							border-radius:4px;
							margin-left:5px;
							display: none;
							position:relative;
						}
					}
				}

				.xl-info-container{
					display: flex;
					align-items: center;
					font-size:14px;

					.icon{
						width:28px;
						height:28px;
						background-image:url('../img/xl-icons.png');
					}

					&.regular{
						.xl-info.regular{ display: block; }
						.icon{ background-position: 0px; }
					}

					&.mixed{
						.xl-info.mixed{ display: block; }
						.icon{ background-position: -28px * 1; }
					}

					&.xl{
						.xl-info.xl{ display: block; }
						.icon{ background-position: -28px * 2; }
					}

					&.unavailable{
						.xl-info.unavailable{ display: block; }
						.icon{ background-position: -28px * 3; }
					}

					&.xs{
						.xl-info.xs{ display: block; }
						.icon{ background-position: -28px * 3; }
					}
				}
			}

			&.performance{
				.ranking-header{
					float:none;
				}

				.hexagon-container{
					padding:20px;
					position:relative;
					max-width: 350px;
    				margin: 0 auto;

					.hexagon{
						margin:10px auto;
						display:block;
						max-width:100%;
					}

					.chart-label{
						text-align: center;
						position:absolute;
						font-size:12px;

						.label{
							font-size:10px;
						}

						.value{
							font-weight:bold;
						}

						.comparison{
							color: rgba(255,255,255,0.9);
							position: absolute;
						    top: -3px;
						    left: 34px;
						    padding: 3px 5px;
						    border-radius: 15px;
							display: none;
							width:40px;
							box-sizing: border-box;
							text-align: center;
						}

						&:nth-of-type(1){
							top: 0%;
							left: 47%;

							.comparison{
								left: -44px;
							}
						}

						&:nth-of-type(2){
							top: 87%;
							left: 46%;
						}

						&:nth-of-type(3){
							top: 25%;
							left: 72%;
						}

						&:nth-of-type(4){
							top: 72%;
							left: 70%;
						}

						&:nth-of-type(5){
							top: 25%;
							left: 18%;

							.comparison{
								left: -38px;
							}
						}

						&:nth-of-type(6){
							top: 72%;
							left: 15%;

							.comparison{
								left: -38px;
							}
						}
					}
				}
			}


			.moveset{

				&.show-stats{
					.name-container{
						border-bottom: 1px solid;

						&.stats-container{
							display:flex;
						}
					}

					&.fast{
						.move-detail-template{
							cursor: pointer;

							&:hover{
								border-left: 10px solid rgba(0, 0, 0, 0.2);

								&.selected{
									border-left: 10px solid #000;
								}
							}
						}
					}
				}

				.name-container{
					float:none;
					padding-bottom:3px;
					margin-bottom:3px;
					justify-content: space-between;
					align-items: baseline;
					font-size:14px;

					&.stats-container{
						border:none;
						margin:0;
						padding:0;
						display:none;
						overflow: hidden;

						.damage,.energy,.dpe,.dpt,.ept,.turns{
							min-width:75px;
						}

						.dpe,.turns{
							min-width:50px;
							text-align: right;
						}

						&.move-effect,
						&.move-count{
							justify-content: flex-start;

							div{
								margin:5px 5px 0 0;
							}
						}
					}
				}

				.stats-container{
					font-size:12px;
					justify-content: space-between;
				}

				.archetype{
					display:flex;
					align-items: center;
					margin-top:2px;
					font-size:12px;

					.name,.icon{
						display: block;
					}

					.icon{
						width:16px;
						height:16px;
						background-image:url('../img/move-archetype-icons.png');
						background-repeat:no-repeat;
						margin-left:5px;

						&.spam{ background-position: (-16px * 1) 0; }
						&.self-debuff{ background-position: (-16px * 2) 0; }
						&.debuff{ background-position: (-16px * 3) 0; }
						&.boost{ background-position: (-16px * 4) 0; }
						&.high-energy{ background-position: (-16px * 5) 0; }
						&.general{ background-position: (-16px * 6) 0; }
						&.low-quality{
							&:before{
								content:'x';
							}

							border-radius:16px;
							background:#000;
							color:#fff;
							text-align: center;
							font-weight: bold;
						}
					}
				}
			}

			.ranking-header.stat-toggle{
				text-align: right;

				a.show-move-stats{
					padding: 2px 8px;
				    border-radius: 8px;
				    text-decoration: none;
				    font-weight: normal;
				    border: 1px solid #000;
				    color: #000;
				    font-size: 12px;

					@include transition(all, 0.3s, ease-out, 0s);

					&.on{
						background:#444;
						color:#fff;
					}
				}
			}

			.rank{
				border-left: 10px solid rgba(0, 0, 0, 0.1);

				&.selected{
					border-left: 10px solid rgba(0, 0, 0, 1);
				}

				&.recommended{
					border-left: 10px solid rgba(0, 0, 0, 1);
					margin:20px 0 10px 0;
					font-size:12px;
				}
			}

			.footnote{
				font-size: 12px;
			}

			.move{
				display:inline-block;
				padding:5px;
				margin-right:5px;
				border-radius:8px;

				&.charged{
					font-weight:bold;
				}
			}

			.type{
				border-radius:12px;
				margin:3px;
			}

			.resistances, .weaknesses{
				display:flex;
				flex-wrap: wrap;

				.type{
					display:flex;

					div:nth-of-type(2){
						text-transform: capitalize;
						padding: 2px 8px 2px 4px;
						font-size:12px;
						line-height: 18px;
					}
				}

				.multiplier{
					font-size: 12px;
				    background: #333;
				    color: #ddd;
				    border-radius: 12px;
				    padding: 2px 4px;
					line-height: 18px;
				}
			}

			.rating-container .rating{
				box-shadow: 2px 2px 2px rgba(0,0,0,0.4)
			}
		}

		.multi-battle-link{
			width:100%;
			margin:10px 0;
			text-align: center;

			p{
				margin: 0;
			}

			a.button{
				font-size:16px;
				display:inline-block;
				margin: 0;
			}
		}

		h2{ margin: 0 0 5px 0; }
	}
}

.rank.typed-ranking{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	padding: 8px 10px;

	.pokemon-info{
		flex: 1;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		min-width: 0;

		.name-container{
			margin-right: 8px;
			align-items: center;
			margin-top: -3px;
			margin-bottom: -3px;
			min-width: 46.5%;
		}

		.type-container{
			display: flex;
			flex-wrap: wrap;
			gap: 4px;
			margin-right: 15px;
			align-self: flex-start;

			.type-info{
				display: inline-flex;
				border-radius: 4px;
				padding: 2px 5px;
				font-size: 12px;
				line-height: 1.2;
				border: 1px solid #222;
				color: $color-off-black;

				&.dark, &.dragon, &.ghost{
					color: $color-off-white;
				}
			}
		}

		.moves{
			width: 100%;
			margin-top: 3px;
		}
	}

	& > .rating-container{
		margin-left: auto;
		align-self: flex-start;
	}
}

.ranking-filters{

	.ranking-filter{
		max-width: 60%;
		padding-right: 20px;
		box-sizing: border-box;

		&:last-of-type{
			max-width: 40%;
			padding-right: 0;
		}

		h4{
			margin: 0 0 5px 0;
	    	font-size: 12px;
		}

		select{
			max-width: 100%;

			optgroup{
				font-weight: normal;
			}
		}

		.sort-button{
			background: none !important;
		    font-size: 14px;
			text-align: left;
		    margin: 0;
		    padding: 5px 8px;
			min-width: 100px;
		}

		.flex{
			justify-content: space-between;
		}

		a.format-rules{
			font-size:12px;
			display:none;
		}
	}
}

/* Detail tabs */

.detail-tab-nav{
	display: none;
}

.detail-tab{
	display:flex;
	flex-wrap:wrap;
	flex-basis:100%;

	&.active{
		display:flex;
	}
}

.detail-tabs-on{

	.detail-tab-nav{
		margin-top:10px;
		flex-basis: 100%;
		display:flex;
		flex-wrap: wrap;

		a{
			color:$color-off-black;
			text-decoration: none;
			display:flex;
			align-items: center;
			padding: 5px 5px;
			font-size:12px;
			font-weight: bold;
			border-radius: 8px;
			margin-right: 5px;

			.icon{
				display:block;
				background-size:200%;
				background-repeat: no-repeat;
				background-position: 0 center;
				margin: 0 4px 0 2px;
			}

			&[tab="matchups"] .icon{
				width:18px;
				height:18px;
				background-image:url('../img/detail-tab-matchups.png');
			}

			&[tab="moves"] .icon{
				width:17px;
				height:17px;
				background-image:url('../img/detail-tab-moves.png');
			}

			&[tab="misc"] .icon{
				width:17px;
				height:5px;
				background-image:url('../img/detail-tab-misc.png');
			}

			&[tab="stats"] .icon{
				width:15px;
				height:17px;
				background-image:url('../img/detail-tab-stats.png');
			}


			&:hover{
				background-color:rgba(0,0,0,0.15);
			}

			&.active{
				background-color:$color-off-black;
				color:$color-off-white;

				.icon{
					background-position: 100% center;
				}
			}
		}
	}

	.detail-tab{
		display:none;

		&.active{
			display:flex;
		}
	}
}


.rankings-container.show-move-counts .count{
	display:inline-block;
}

.ranking-checks{
	font-size:14px;

	.check.xl{
		margin-right:10px;
	}
}

.traits,
.similar-pokemon .list,
.partner-pokemon .list{
	display:flex;
	flex-wrap: wrap;

	& > div,
	& > a{
		margin:3px;
		padding:5px 8px;
		border-radius:12px;
		cursor:pointer;
		border:2px solid rgba(0,0,0,0);
		text-decoration: none;
		color:#000;

		&.dark,&.dragon,&.ghost{
			color:#fff;
		}

		&:hover{
			border:2px solid rgba(0,0,0,.25);
		}

		&.pro{ background:rgb(23 222 75 / 44%);	}
		&.con{ background:rgb(255 88 88 / 67%);	}
	}
}

.modal .traits > div{
	display:flex;
	width:100%;

	.name{
		font-weight:bold;
		font-size:14px;
		flex-basis:35%;
	}

	.desc{
		font-size:12px;
		padding-left:5px;
		flex-basis:65%;
	}
}

.modal .trait-modal .traits > div{
	cursor:default;
	border:none !important;
}

.modal .search-traits-selector{
	.traits > div{
		display:block;
		width:auto;

		&.selected{
			border:2px solid rgba(0,0,0,1);
		}
	}
}

.modal .sort-group .center .button{
	display: block;
	min-width: 200px;
}

.rankings-container > .rank,
.rankings-container > .rank > .rating-container{
	cursor: pointer;
}

.rating.star,
.rankings-container > .rank > .rating-container > .rating.star{
	background-image:url('../img/starburst.png');
	background-size:contain;
	background-repeat:no-repeat;
	color:$color-ranking;
	background-color: rgba(0,0,0,.6);
	border-radius: 8px;
	padding: 5px 5px 5px 36px;
	font-weight: bold;
	width:auto;
	text-decoration: none;

	&.loss{
		background-image:url('../img/starburst_red.png');
	}
}

.rankings-container > .rank > .rating-container > .rating{
	background-image:none;
	width:80px;
	text-align: center;
	font-weight: bold;
	box-sizing: border-box;
}

.ranking-categories{
	float:right;
    display: flex;
    flex-wrap: wrap;

	a{
		text-decoration: none;
		color:$color-blue-dark;
		font-size:14px;
		padding:8px 10px;
		margin: 0 2px;
		border-radius:30px;
		display:inline-block;

		@include transition(background-color, 0.2s, ease-out, 0s);

		&.selected, &:hover{
			background:$color-sky-blue;
			font-weight:bold;

			@include transition(background-color, 0s, ease-out, 0s);
		}
	}

	&.training-mode-select{
		margin-bottom: 15px;
	}
}

.poke-search-container{
	display:flex;

	.poke-search{
		max-width:200px;
		display:block;
	}

	a.search-info,
	a.search-traits{
		text-decoration: none;
		color:$color-blue-dark;
		border:1px solid $color-blue-dark;
		border-radius:20px;
		text-align: center;
		width:15px;
		height:15px;
		font-weight: bold;
		margin-left:5px;
	}

	a.search-traits{
		background:rgba(23, 222, 75, 0.44);
	}
}


.options{
	margin: 40px 0 20px 0;
}

.check.limited{
	.limited-title{
		display: inline;
	}
}

.limited-title{
	color:$color-gold;
    text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
    font-weight: bold;
}

.rankings-container .limited-rank{
	@include gradient($color-gold, $color-gold-dark);

	.name-container{
		color:#000;
	}
}

.asterisk{
	font-size:12px;
	margin-bottom:15px;
}

/* TEAM BUILD STYLING */

.team-build{
	display:flex;
	justify-content: space-between;

	.poke{
		float:none;

		a.random{
			margin-bottom: 0;
		}

		a.swap{
			display:none;
		}

		&:nth-of-type(2){

			.poke-stats{
				text-align: left;

				.stat-label{
					float:left !important;
				}

				.bar-back{
					@include transform(scale(1, 1));
				}

				.hp.bar-back .stat{
					@include transform(scale(1, 1));
				}
			}
		}

		.options .shield-section, .options .toggle, .options .toggle-content, .bar-back, .move-bars{
			display:none;
		}
	}

	.poke.multi{
		display:block;
		.poke-stats{
			display: block;
		}
		.custom-options{
			display: block;
		}
		.options{
			display:none;
		}
	}
}

.team-content{
	a.toggle{
		margin-top:10px;
	}
}

.team-advanced{
	.poke .poke-stats{
		background:none;
	}

	.flex:nth-of-type(2){
		justify-content: space-between;
		flex-wrap: wrap;

		.flex-section{
			max-width:350px;
		}
	}

	.poke.flex{
		float:none;
		max-width:none;
		flex-wrap: wrap;

		.flex-break{
			flex-basis:100%;
		}
	}

	.team-option{
		font-size:12px;
		margin-right:20px;
		margin-bottom:10px;

		h3{
			font-size:14px;
			margin-top:0;
		}
	}
}

.typings{
	display:none;

	a.toggle{
		font-size:1.17em;
		font-weight:bold;
		margin:1em 0;

		&:first-of-type{
			margin-top:0;
		}
	}

	.defense,
	.offense{
		overflow-x:scroll;

		table{
			td{
				padding:5px 10px;

				&.heading{
					font-weight:bold;
				}

				&.name{
					display:block;
					width:120px;
				}
			}
		}
	}

	.overview-section{
		border-radius: 8px;
	    padding: 4px 8px;
	    margin-bottom: 10px;
	    background: rgba(0,0,0,0.05);

		.flex{
			justify-content: space-between;
		}

		h3{
			margin: 10px 0 0px 0;
		    font-size: 18px;
		    line-height: 26px;
		}

		.grade{
			font-size: 24px;
		    font-weight: bold;
		    padding: 6px 8px;
		    border-radius: 8px;
		    line-height: 30px;
		}

		.notes div{
			display: none;
			font-size: 12px;
		    border-top: rgba(0,0,0,0.25) 1px solid;
		    margin-top: 5px;
		    padding-top: 5px;
		}

		.grade[grade="A"]{
			background:#018dba;
			color:#fff6d4;
		}

		.grade[grade="B"]{
			background:#1b79b4;
			color:#a5d6fb;
		}

		.grade[grade="C"]{
			background:#968cb6;
			color:#fff6d4;
		}

		.grade[grade="D"]{
			background:#96359e;
			color:#fd91e0;
		}

		.grade[grade="F"]{
			background:#b9008f;
			color:#ff92e2;
		}
	}
}

.summary{
	li{
		line-height:24px;
		margin-bottom:10px;
	}
}

.summary span,
.article span.type{
	padding: 2px 5px;
	border-radius: 8px;
	font-weight: bold;
}

.article span.type{
	font-weight:normal;
}

.typeRating{
	border:2px solid #000;
	border-radius:8px;
	text-align: center;
    margin: 5px;
    overflow: hidden;

	h4{
		margin:0;
		padding:2px 10px;
	}
}

.threats.rankings-container,
.alternatives.rankings-container,
.section.battle-results .rankings-container{
	.rank{
		cursor:initial;
		border-left:none;

		&:hover{
			border-left:none;
		}
	}
}

.table-container{
	overflow-x:scroll;
}

.histograms{
    display: flex;
    justify-content: space-around;
	flex-wrap: wrap;

	.histogram{
		position:relative;
		width:31%;
		text-align: center;
		padding-left:20px;
		box-sizing:border-box;

		.chart{
			height:200px;
			border:1px solid $color-blue-dark;
			background: rgba(255,255,255,.6);
			overflow: hidden;

			.segment{
				float:left;
				position:relative;
				height:100%;
				box-sizing:border-box;

				&:nth-of-type(10){
					border-right: 1px dashed rgba(0, 0, 0, .5);
				}

				.bar{
					position: absolute;
					bottom:0;
					width:100%;

					@include transition(height, 0.3s, ease-out, 0s);
				}
			}
		}

		h2{
			font-size:18px;
		}

		.move-label{
			font-size:12px;
			height:28px;
		}

		.star.rating{
			font-size: 14px;
    		display: inline-block;
		}

		.x-axis{
			display: flex;
			justify-content: space-between;

			div{
				font-size:12px;
				width:27px;

				&:first-of-type{
					text-align:left;
				}
			}
		}

		.button{
			font-size: 12px;
			border: 1px solid #000;
			padding: 5px;
			display: none;
		}

		.label-x-axis{
			position:absolute;
			left:-25px;
			top:40%;

			@include transform(rotate(270deg));
		}

		.label-y-axis{
			color: $color-off-white;
		    background-color: rgba(0,0,0,.6);
		    border-radius: 8px;
		    padding: 5px 5px 5px 5px;
		    font-weight: bold;
			font-size: 16px;
		}

		.stats{
			text-align: left;
			margin: 10px 0;
			display:flex;
			font-size:12px;
			line-height: 16px;
		}
	}
}

/* ARTICLE */

.article{

	p:first-of-type{
		margin-top:1em;
	}

	p, ul{
		font-size:14px;
		line-height:20px;
	}

	ul li{
		margin-bottom:10px;
	}

	.article-section{

		h3{
			font-size: 18px;
		    border-bottom: 1px solid;
		    padding-bottom: 4px;
		}
	}

	h2{
		font-size: 22px;
		margin-bottom: 10px;
	}

	h3.article-header,
	.tip{
		background: rgb(98 114 139);
		padding: 10px;
		border-radius: 8px;
		color:$color-off-white;
		line-height: 24px;
	}

	table{
		border:none;
		margin:0 auto;

		td{
			padding:5px;
			vertical-align: top;

			&.buff{
				color:$color-stat-buff;
			}
		}

		tr:nth-of-type(2n){
			background:rgba(0,0,0,.1);
		}

		&.alternatives-table{
			min-width: 600px;
		}
	}

	.rating-table{

		td{
			padding: 3px;
		}

		td.name{
			vertical-align: middle;
			text-align: left;
			padding-right:10px;
		}

		td.name-small{
			font-size:14px;
			max-width:80px;
			vertical-align: bottom;
		}

		&.legend{
			margin:0;
			text-align: left;

			td{
				padding:5px;
			}

			a.rating{
				text-align: center;
			}
		}

		a.rating{
			width: 55px;
			box-sizing: border-box;
		}


		&.matrix-table, &.meta-table, &.threats-table, &.alternatives-table{
			th.arrow, thead td.arrow{
				background-image:url('../img/matrix-arrow-header.png');
				background-repeat: no-repeat;
				background-position: right 4px bottom 4px;
			}

			th.name{
				background-image:url('../img/matrix-arrow.png');
				background-repeat: no-repeat;
				background-position: right 4px center;
				padding-right: 20px;
			}

			td.name-small{
				padding-bottom:12px;
			}
		}

		&.matrix-table{

			a.rating{
				width:70px;
				padding: 5px;
				border-radius: 8px !important;

				span{
					margin: 0 6px 0 0;
				}

				&:after{
					border-radius: 8px !important;
				}
			}

			.matrix-record,
			.matrix-average{
				font-size:14px;
				white-space: nowrap;
				padding:8px 10px 0px 10px;
			}

			.rating.average{
				padding: 5px;
				width: 85px;
				box-sizing: border-box;
				pointer-events: none;

				span{
					margin: 0 6px 0 0;
				}
			}

			th:first-of-type{
				min-width: 18px;
			}
		}
	}

	.results-buttons{
		display:flex;
		justify-content: center;

		a.button{
			margin: 10px;
		}
	}

	.threats-table,
	.alternatives-table,
	.meta-table{
		.name{
			min-width:120px;
			font-size:12px;
		}

		th{
			text-align: left;
		}

		.button.add{
			display: inline-block;
		    padding: 0 5px;
		    margin: 0 0 0 5px;
		    border-radius: 30px;
		}

		.region-label{
			font-size:14px;
			font-weight: normal;
			font-style: italic;
			text-transform: capitalize;

			&.kanto{ color:#ce0000; }
			&.johto{ color:#a59662; }
			&.hoenn{ color:#2665f3; }
			&.sinnoh{ color:#821a37; }
			&.unova{ color:#333; }
			&.alola{ color:#fb900b; }
			&.galar{ color:#946000; }
		}
	}
}

.matrix-table th.number{
	font-weight: normal;
	font-size: 12px;
	text-align: left;
	color: rgb(181 181 181);
	padding-right: 0;
}

.matrix-table th:nth-of-type(2){
	left: 18px;
}

a.rating, .rating-container .rating, .summary .rating,
.stats-table .rating, .train-table .rating{
	display:flex;
	padding:5px 12px;
	text-decoration: none;
	font-weight: bold;
	margin: 0 auto;
	justify-content: center;
	align-items: center;

	span{
		display:block;
		margin:0 auto;
		width:18px;
		height:18px;
		background-size:100%;
		background-repeat: no-repeat;
	}

	i{
		display: flex;
		align-items: center;
		flex: 1;
		min-width:13px;

		&:after{
			content: "→";
			font-style: normal;
			font-weight: normal;
			font-size: 10px;
			line-height: 0;
			position: absolute;
			right: 8px;
		}
	}

	&.win{
		background-color: #1280b6;
		color: #fff6d4;

		span{
			border-radius:20px;
			background:#fff6d4;
		}

		i{
			color: #fff6d4;
		}
	}

	&.close-win{
		background-color: #2e6bb0;
		color: #fff6d4;

		span{
			border-radius:20px;
			border:2px solid #a5d6fb;
			box-sizing:border-box;
		}

		i{
			color: #fff6d4;
		}
	}

	&.close-loss{
		background-color: #a91c7e;
		color: #ffd3f4;

		span{
			background-image: url('../img/themes/sunflower/rating-close-loss.png?v=7');
			opacity: 0.9;
		}

		i{
			color: #ffd3f4;
		}
	}

	&.loss{
		background-color: #c71b71;
		color: #ffc9f1;

		span{
			background-image: url('../img/themes/sunflower/rating-loss.png?v=7');
		}

		i{
			color: #ffd3f4;
		}
	}

	&.tie{
		background-color: rgb(150, 140, 182) !important;
		color: #f2f2f2;

		span{
			background-image: url('../img/themes/sunflower/rating-tie.png');
			opacity: 0.75;
		}

		i{
			color: #f2f2f2;
		}
	}
}

a.rating{
	border-radius: 30px !important;
	position: relative;

	@include transition(box-shadow, .25s, ease-out, 0s);

	&:after{
		content: " ";
		position:absolute;
		top: 0;
		left: 0;
		width:100%;
		height: 100%;
		background:rgba(255,255,255,0);
		pointer-events: none;
		border-radius: 30px !important;

		@include transition(background-color, .25s, ease-out, 0s);
	}

	&:hover{
		box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.55) !important;

		@include transition(box-shadow, 0s, ease-out, 0s);

		&:after{
			background:rgba(255,255,255,0.1);

			@include transition(background-color, 0s, ease-out, 0s);
		}

	}
}

.rank a.rating{
	box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.25);
}

.article-item{
	margin-bottom:10px;

	.col-9{
		width:75%;
		padding: 0 10px;
	}

	img{
		display:block;
		border:1px solid #000;
	}

	h4{
		margin-top:0;
	}

	.tags{
		display: flex;

		a{
			display: block;
			text-decoration: none;
			background:$color-links;
			color:$color-off-white;
			border-radius: 8px;
			font-size:12px;
			padding:4px 8px;
			margin-right:5px;
		}
	}
}

.article, .article-item{

	.date{
		margin-bottom:1em;
		font-size:14px;
	}

	h1:first-of-type,
	h4:first-of-type{
		margin:0;
	}

	img{
		display:block;
		max-width:100%;
		border:1px solid #000;
	}

}


/* CONTRIBUTORS */

.contribute{

	h2{
		font-size:20px;
	}

	.supporters{
		justify-content: center;
		flex-wrap:wrap;

		.supporter-container{

			margin-bottom:15px;

			img{
				display:block;
				max-height:85px;
				margin: 0 auto;
			}

			.space{
				width:85px;
				height:85px;
			}

		}

		.supporter{
			background:#fff;
			border-radius:8px;
			padding:10px;
			margin:5px;
		}
	}
}

/* MOVES PAGE */

.move-table-container{
	max-width: 400px;
	margin: 0 auto;

	.poke-search{
		margin-top:15px;
	}
}

.move-explore-container{
	max-width:500px;
	margin:0 auto;

	.move-select-container{
		justify-content: space-between;

		.move-select-item{
			max-width:180px;
		}

		.poke-search{
			margin-bottom:5px;
		}

		.check{
			font-size:14px;
		}

		h3{
			font-size:14px;
		}
	}

	.moveset-stats{
		flex-wrap:wrap;
		justify-content: center;
		text-align: center;

		.stat{
			margin:10px 15px;

			h3{
				margin-top:0;
				font-size:14px;
			}
		}
	}

	.explore-results{

		margin-top:30px;

		h2{
			margin-top:30px;
		}

		h2 + div{
			font-size:14px;
		}

		.rankings-container{
			margin-top:10px;
		}

		a.rank{
			display:block;
			text-decoration: none;
			color:#000;

			&:hover{
				border-left:none;
			}

			.name-container{
				pointer-events: none;
				float: none;
			}
		}
	}
}

.moves{
	p:first-of-type{
		margin-top:1em;
	}
}

/* MODAL WINDOW */

.modal{
	position:fixed;
	top:0;
	left:0;
	width:100%;
	height:100%;
	background:rgba(0,0,0,.5);
	z-index:50;
	padding:10px;
	box-sizing:border-box;
	overflow-y:scroll;

	.modal-container{
		position:relative;
		background: $color-off-white;
		margin: 0 auto;
		max-width: 400px;
		vertical-align: middle;
		margin-top: 50px;
		border-radius: 8px;
		margin-bottom: 75px;

		.modal-header{
			padding:10px;
			background:$color-blue-dark;
			color: $color-off-white;
			font-size:24px;
			font-weight: bold;
			border-top-left-radius: 8px;
			border-top-right-radius: 8px;
		}

		.modal-close{

			position: absolute;
			top:0;
			right:0;
			padding:10px;
			color:$color-off-white;
			cursor:pointer;

			&:after{
				content:"X";
			}
		}

		.modal-content{
			padding:10px;
		}

		.poke.single{
			float:none;
			margin:0 auto;
			max-width:200px;

			.poke-stats{
				background:none;
			}

			.clear-selection,
			.hp.bar-back,
			.move-bars,
			.random,
			.swap,
			.auto-select,
			.options .shield-section,
			.options .toggle,
			.options .toggle-content{
				display:none;
			}

			.pokebox{
				display:none !important;
			}

			&[context="modalrankcompare"]{
				.advanced-section, .move-select-container, .options{
					display: none;
				}
			}
		}

		.poke-search{
			background: none !important;
			margin-bottom:5px;
		}

		.move-select{
			max-width:200px;
		}
	}

	.sandbox-move-select{
		.move-select{
			font-size:18px;
			margin-bottom:10px;
		}

		.charge-select{
			max-width:200px;
			margin-bottom:10px;
		}

		.move-stats{
			flex-wrap:wrap;
			justify-content: space-around;
			text-align:center;

			&> div{
				padding:10px;

			}

			span.stat{
				display:block;
				font-weight:bold;
			}
		}
	}

	.list-export,
	.search-string-window{

		.list-text,
		.team-string-text{
			width:100%;
			height:200px;
		}

		.copy{
			display:inline-block;
			cursor: pointer;
			font-weight:bold;
			background:$color-blue-light;
			color:$color-off-white;
			padding:5px;
			border-radius:8px;
			margin:5px 0;
		}

		.search-string-options .check{
			margin-right: 10px;
		}

		.export-options{
			display: flex;
			justify-content: space-between;

			a.json{
				font-size: 12px;
			}
		}
	}

	.save-list{
		input{
			font-size:18px;
		}
	}


	.button{
		margin:25px auto 0 auto;
		display:inline-block;
	}

	.pokebox-options{
		display:flex;

		a{
			padding: 4px 8px;
			border-radius: 8px;
			text-decoration: none;
			font-size:14px;
			color:#fff;
			background:$color-links;
			margin-right:10px;
			margin-bottom:10px;

			&.pokebox-edit{
				border:1px solid #000;
				color:#000;
				background:url('../img/pokebattler_logo.png') $color-off-white;
				background-repeat: no-repeat;
				background-position: 5px center;
				background-size: 20px;
				padding: 4px 8px 4px 30px;
			}

			&.pvpoke-sponsor{
				border:1px solid #000;
				color:#000;
				background:url('../img/app_192.png') $color-off-white;
				background-repeat: no-repeat;
				background-position: 5px center;
				background-size: 20px;
				padding: 4px 8px 4px 30px;
				margin-left:auto;
			}
		}
	}

	.pokebox-import{

		ol{
			line-height:30px;
		}

		input.pokebox-id{
			max-width:200px;
		}

		.poke-count-container{
			font-size:12px;
			margin-bottom:5px;
		}

		.poke-search-container{
			margin-top: 10px;
			justify-content: space-between;
			align-items: center;

			.check{
				margin-top: 0;
			}
		}
	}

	.pokebox-import[select-mode="multi"]{
		.multi{
			display:block;
		}
	}

	.pokebox-import[select-mode="single"]{
		.multi{
			display:none;
		}
	}

	a.compare-poke,
	a.duplicate-poke,
	a.compare-pokebox{
		display: block;
		font-size: 14px;
		margin-top:25px;
	}

}


/* TEXTS & FONTS */

h1.title{
	font-size:18px;
	margin:0;
}

h2{
	font-size:14px;
	margin-bottom:0;
}

a.toggle{
	color:$color-blue-dark;
	text-decoration: none;
	font-weight:bold;
	display: block;
}

a.pokebattler{
	background-image:url('../img/pokebattler_logo.png');
	background-size: 20px;
	background-repeat: no-repeat;
	padding-left:24px;
}

.center{
	text-align: center;
}

/* TYPE COLORS */

.bug{ @include gradient($color-bug-dark, $color-bug-light); }
.dark{ @include gradient($color-dark-dark, $color-dark-light); }
.dragon{ @include gradient($color-dragon-dark, $color-dragon-light); }
.electric{ @include gradient($color-electric-dark, $color-electric-light); }
.fairy{ @include gradient($color-fairy-dark, $color-fairy-light); }
.fighting{ @include gradient($color-fighting-dark, $color-fighting-light); }
.fire{ @include gradient($color-fire-dark, $color-fire-light); }
.flying{ @include gradient($color-flying-dark, $color-flying-light); }
.ghost{ @include gradient($color-ghost-dark, $color-ghost-light); }
.grass{ @include gradient($color-grass-dark, $color-grass-light); }
.ground{ @include gradient($color-ground-dark, $color-ground-light); }
.ice{ @include gradient($color-ice-dark, $color-ice-light); }
.normal{ @include gradient($color-normal-dark, $color-normal-light); }
.poison{ @include gradient($color-poison-dark, $color-poison-light); }
.psychic{ @include gradient($color-psychic-dark, $color-psychic-light); }
.rock{ @include gradient($color-rock-dark, $color-rock-light); }
.steel{ @include gradient($color-steel-dark, $color-steel-light); }
.water{ @include gradient($color-water-dark, $color-water-light); }

.dark, .dragon, .ghost {
	> .name-container{
		color:$color-off-white;
	}

	color:$color-off-white;

	option{
		color:#000;
	}

	.detail-section{
		color:#000;
	}
}

.buff, .debuff{
	&.bug{ background-color:$color-bug-dark !important; }
	&.dark{ background-color:$color-dark-dark !important; }
	&.dragon{ background-color:$color-dragon-dark !important; }
	&.electric{ background-color:$color-electric-dark !important; }
	&.fairy{ background-color:$color-fairy-dark !important; }
	&.fighting{ background-color:$color-fighting-dark !important; }
	&.fire{ background-color:$color-fire-dark !important; }
	&.flying{ background-color:$color-flying-dark !important; }
	&.ghost{ background-color:$color-ghost-dark !important; }
	&.grass{ background-color:$color-grass-dark !important; }
	&.ground{ background-color:$color-ground-dark !important; }
	&.ice{ background-color:$color-ice-dark !important; }
	&.normal{ background-color:$color-normal-dark !important; }
	&.poison{ background-color:$color-poison-dark !important; }
	&.psychic{ background-color:$color-psychic-dark !important; }
	&.rock{ background-color:$color-rock-dark !important; }
	&.steel{ background-color:$color-steel-dark !important; }
	&.water{ background-color:$color-water-dark !important; }

}

.tap{
	&.bug{ border-color:$color-bug-dark !important; }
	&.dark{ border-color:$color-dark-dark !important; }
	&.dragon{ border-color:$color-dragon-dark !important; }
	&.electric{ border-color:$color-electric-dark !important; }
	&.fairy{ border-color:$color-fairy-dark !important; }
	&.fighting{ border-color:$color-fighting-dark !important; }
	&.fire{ border-color:$color-fire-dark !important; }
	&.flying{ border-color:$color-flying-dark !important; }
	&.ghost{ border-color:$color-ghost-dark !important; }
	&.grass{ border-color:$color-grass-dark !important; }
	&.ground{ border-color:$color-ground-dark !important; }
	&.ice{ border-color:$color-ice-dark !important; }
	&.normal{ border-color:$color-normal-dark !important; }
	&.poison{ border-color:$color-poison-dark !important; }
	&.psychic{ border-color:$color-psychic-dark !important; }
	&.rock{ border-color:$color-rock-dark !important; }
	&.steel{ border-color:$color-steel-dark !important; }
	&.water{ border-color:$color-water-dark !important; }

}

/* RESPONSIVE */


@media only screen and (max-width: 468px) {

	.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(1){
		top: 0%;
		left: 44%;
	}

	.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(2){
		top: 87%;
		left: 43%;
	}

	.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(3){
		top: 25%;
		left: 74%;
	}

	.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(4){
		top: 72%;
		left: 72%;
	}

	.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(5){
		top: 25%;
		left: 10%;
	}

	.rank .details .detail-section.performance .hexagon-container .chart-label:nth-of-type(6){
		top: 72%;
		left: 7%;
	}
}

@media only screen and (max-width: 720px) {

	.mobile{
		display: block;
	}

	.desktop{
		display: none !important;
	}

	.header-ticker{
		z-index: 50;
	}

	header{
		z-index:50;
		padding: 3px 10px;

		.title{
			float:none;
			text-align: center;
		}

		.hamburger{
			margin-top: 4px;
		}

		.menu{
			display:none;
			float:none;
			clear:both;
			margin-top:5px;
			padding:0;
			flex: 0 0 100%;
			background: $color-blue-dark;
			position: fixed;
		    width: 100%;
		    margin: 0 -10px;
		    top: 37px;
			height: calc(100% - 37px);
			padding: 8px 8px;
    		box-sizing: border-box;
			box-shadow: 0px 5px 5px rgba(0,0,0,0.4);
			overflow-y: auto;
			//top: 65px;

			a{
				padding: 9px 8px 9px 38px;
				margin: 0;
			    font-size: 18px;
			    background-size: 18px 18px;
			    background-position: 8px center;
				color: $color-off-white;
			}

			& > a.selected, .parent-menu > a.selected{
				background-color: $color-blue-medium;

				@include transition(background-color, 0s, ease-out, 0s);
			}

			.parent-menu a{
				position:relative;

				span{
					width:20px;
					height:10px;
					background:url('../img/themes/sunflower/dropdown-triangle-large.png');
					background-repeat: no-repeat;
					background-position: center;
					top: 2px;
					@include transition(all, 0.125s, ease-out, 0s);
				}

				&.active span{
					transform: rotate(180deg);
				}
			}

			.icon-battle{
				background-image:url('../img/themes/sunflower/nav-battle-white.png');
			}

			.icon-train{
				background-image:url('../img/themes/sunflower/nav-train-white.png');
			}

			.icon-rankings{
				background-image:url('../img/themes/sunflower/nav-rankings-white.png');
			}

			.icon-team{
				background-image:url('../img/themes/sunflower/nav-team-white.png');
			}

			.icon-heart{
				background-image:url('../img/themes/sunflower/nav-heart-white.png');
			}

			.submenu{
				padding-left: 38px;

				a{
					font-size: 16px;

					&:first-of-type{
						margin-top: 0;
					}
				}

				a{
					padding-left: 24px;
				}
			}

			.more-parent-menu .submenu{
				padding-left: 0;
				display: block;
				max-height: 350px;

				a{
					font-size: 18px;
					padding-left: 38px;
				}
			}
		}
	}

	.header-ticker{
		position: fixed;
		z-index: 50;

		a{
			font-size: 11px;
		}
	}

	body{
		background-position: center top;
	}

	#main{
		& > h1{
		font-size:26px;
		}
	}

	.tooltip.sandbox{
		display:none !important;
	}

	.stats-table.sortable-table{
		font-size:14px;
	}

	.poke{
		width:49%;

		.poke-stats{
			max-width:100%;
		}

		input, select, .bar-back{
			max-width:100%;
		}

		.poke-stats .stat-container .bar-back{
			width:60px;
		}

		a.search-info{
			display: none;
		}
	}

	.modal .poke{
		width:100%;
	}

	.rank{
		.details .detail-section{

			&.float{
				float:none;
				width:100%;

				&:first-of-type{
					margin: 5px 0 5px 0;
				}

				&.margin{
					margin-right:0;
				}
			}

			.name{
				font-size:14px;
			}


			.archetype .name{
				font-size:10px;
			}
		}

		.rating-container .rating{
			width:auto !important;
		}
	}

	.modal .search-traits-selector .traits > div,
	.similar-pokemon a, .partner-pokemon a{
		font-size:12px;
	}




	.histograms{
		display:block;

		.histogram{
			width:80%;
			max-width:600px;
			margin:20px auto;
		}
	}

	.flex{
		.col-3{
			flex-basis:25%;
		}
		.col-9{
			flex-basis:75%;
		}
	}

	.league-select,
	.format-select,
	.cup-select,
	.category-select{
		max-width:48%;
		margin-bottom:5px;
		font-size: 16px;
	}

	.ranking-categories a{
		font-size: 14px;
		padding: 5px;
	}

	.rank .details .detail-section .moveset .name-container.stats-container > div{
		min-width:auto !important;
	}

	.traits > div{
		font-size:12px;
	}
}


/* SETTINGS */

.settings{
	select, input{
		max-width:200px;
	}

	.button.save{
		display:inline-block;
		margin-top:30px;
	}
}

/* CUSTOM RANKINGS */

.custom-rankings{
	.league-select{
		margin-bottom:15px;
	}

	.include,
	.exclude{
		margin-bottom:15px;

		& > h3:first-of-type{
			margin:0;
		}

		& > p:first-of-type{
			margin-top:0;
		}
	}

	.include{
		.filter{
			background:#c9ebf5;
		}

	}

	.exclude{
		.filter{
			background:#f1b9b9;
		}
	}

	.filters{
		margin-bottom:5px;

		.filter{
			padding: 10px;
			border:2px solid #000;
			border-radius: 12px;
			margin-bottom:8px;
			position: relative;

			.toggle-content{
				margin-top:10px;
				padding-top:10px;
				border-top:1px solid rgba(0,0,0,0.25);
			}

			.field-container{
				display:flex;
				flex-wrap:wrap;
			}

			label{
				font-weight:bold;
				font-size:14px;
				padding-top:4px;
				margin-right:10px;
			}

			select{
				max-width:150px;
			}

			.field-section{
				display:none;

				&.type,
				&.tag,
				&.cost,
				&.distance,
				&.move-type,
				&.evolution{
					.check{
						margin: 10px 5px;
					}

					.select-all{
						margin-right:10px;
					}
				}

				&.dex{
					input{
						max-width:100px;
						margin-right:10px;
					}
				}
			}

			.remove{
				cursor:pointer;
				color:#ff0000;
				position: absolute;
				top: 10px;
				right: 10px;
			}

			&[type="type"] .field-section.type{
				display:block;
			}

			&[type="tag"] .field-section.tag{
				display:block;
			}

			&[type="id"] .field-section.id{
				display:block;
			}

			&[type="dex"] .field-section.dex{
				display:block;
			}

			&[type="evolution"] .field-section.evolution{
				display:block;
			}

			&[type="cost"] .field-section.cost{
				display:block;
			}

			&[type="distance"] .field-section.distance{
				display:block;
			}

			&[type="move"] .field-section.move{
				display:block;
			}

			&[type="moveType"] .field-section.move-type{
				display:block;
			}
		}
	}

	.advanced{
		.flex-section{
			display:flex;

			div{
				margin-right:30px;
			}
		}

		.format-select{
			display:none;
		}

		.cup-select{
			display:block;
			font-size:14px;
			max-width:200px;
		}

		select,
		input{
			max-width:100px;
		}

		h3{
			font-size:14px;
			margin-bottom:5px;
		}
	}

	.import-custom-group{
		margin-top: 15px;
	}
}

.custom-rankings-list{
	display:none;

	h3:first-of-type{
		margin-top:0;
	}

	textarea.pokemon-list{
		width:100%;
		height:150px;
		font-family:sans-serif;
	}
}

.custom-rankings-results{
	display:none;

	h3:first-of-type{
		margin-top:0;
	}
}

.custom-rankings-overrides,
.custom-rankings-meta-group,
.custom-rankings{
	.poke.multi{
		display:block;
		float:none;

		.poke-stats{
			padding:0;
			background:none;
		}

		.cup-select,
		.options{
			display:none;
		}

		.custom-options{
			display:block;
		}
	}

	h3{
		margin-top:0;
	}


	a.swap{
		display: none;
	}
}

.custom-rankings-import{
	h3{
		margin-top:0;
	}

	textarea.import{
		width:100%;
		height:100px;
	}

	.copy{
		display:inline-block;
		cursor: pointer;
		font-weight:bold;
		background:$color-blue-light;
		color:$color-off-white;
		padding:5px;
		border-radius:8px;
		margin:5px 0;
	}
}

/* TRAINING ANALYSIS */

.analysis-container{
	.table-container{
		max-height: 500px;
		margin-bottom:20px;
	}

	.format-select{
		max-width: 143px;
	}

	.column-description{
		font-size:12px;
	}

	.poke-search-container{
		margin: 15px 0;
	}

	.sort-category{
		margin: 20px 0 10px 0;
		display: flex;

		div, a{
			margin-right:15px;
		}

		a{
			display:block;
			background:$color-links;
			padding:4px 8px;
			text-decoration: none;
			border-radius: 12px;
			color:#fff;

			&.selected{
				font-weight: bold;
			}
		}
	}

	.low-volume{
		color:#ff5115;
		font-weight: bold;
	}

	a.download-csv{
		margin:0;
		font-size:14px;
	}

	.date-updated{
		font-style: italic;
	    font-size: 12px;
	}

	&.screenshot{
		.poke-search-container, a.download-csv, p.column-description{
			display: none;
		}
	}
}

.train-table{
	width:100%;
	margin-top:5px;

	thead{
		font-size: 12px;
		font-weight: bold;

		td.poke-name{
			padding:0;
			width:auto;
		}

		a{
			text-decoration: none;

			&.selected,
			&:hover{
				text-decoration: underline;
			}
		}
	}

	td.poke-name{
		display:flex;
		padding: 6px 2px;
		width:125px;
		vertical-align: top;

		.sprite-container{
			width:30px;
			height:30px;
			position: relative;
			background:none;
			border:none;
			margin: 0 5px 0 0;
			padding: 0;
			flex-shrink: 0;
		}

		.number{
			padding-right:10px;
		}

		.name{
			font-size:14px;
			font-weight: bold;
			max-width:100px;
		}

		.moves{
			font-size:12px;
		}

		.team-member{
			display: flex;
		}
	}

	td.individual-score,
	td.team-score,
	td.usage{
		padding: 2px 16px 2px 0;
		font-weight: bold;
	    font-size: 14px;
	    line-height: 0;
	}

	td.team-score .score{
		background-image:url('../img/starburst_red.png');
		background-color: rgb(52, 102, 174);
		background-size: 25px;
	    background-repeat: no-repeat;
	    background-position: 4px center;
	    padding-left: 30px;
		line-height: 25px;
		color:#fff6d4;
		display: inline-block;
	    padding: 4px 4px 4px 30px;
	    border-radius: 12px;
		min-width:40px;
		text-align: center;

		&.win{
			background-image:url('../img/starburst.png');
		}
	}

	td.link a{
		display: block;
	    width: 32px;
	    height: 28px;
	    background-image: url(../img/eye.png);
	    background-repeat: no-repeat;
	    background-position: 0 center;
	    margin-right: 10px;
	    opacity: 0.5;
	}

	tr:nth-child(2n) {
	    background: rgba(0, 0, 0, 0.15);
	}

	a.usage-link{
		display: inline-block;
	    width: 20px;
	    height: 18px;
	    background-image: url(../img/usage-link.png);
	    background-repeat: no-repeat;
	    background-position: 0 center;
		margin-left: 10px;
	}

	span.usage-value{
		display: block;
		width: 35px;
	}

	.usage .flex{
		align-items: center;
	}

	.rating{
		width: 80px;
		padding: 5px;
		margin: 0;
		box-sizing: border-box;

		span{
			margin: 0 6px 0 0;
		}
	}
}

.usage-modal{
	.usage-container{
		display: flex;

		.y-axis-container{
			display: flex;
		    flex-direction: column;
		    justify-content: space-between;
			padding-right: 5px;
			box-sizing: border-box;
			width: 7%;

			.label{
				writing-mode: vertical-rl;
				transform: rotate(180deg);
			}

			.value{
				font-size: 11px;
			}
		}
	}

	.x-axis-container{
		margin-left: 7%;
		margin-top: 5px;
		text-align: center;
		display: flex;
	    justify-content: space-between;
	    flex-wrap: wrap;

		.value{
			font-size: 11px;
		}
	}

	.canvas-container{
		position: relative;
		width: 92%;
	}

	.usage-chart{
		display: block;
		max-width: 100%;
		border: 1px dashed $color-blue-dark;
    	border-radius: 8px;

		&[canvas-id="0"]{
			background: rgba(0,52,98,0.025);
		}

		&[canvas-id="1"]{
			position: absolute;
		    top: 0;
		    left: 0;
			opacity: 0.8;
		}

	}

	.usage-compare-select{
		max-width: 175px;
		margin-bottom: 10px;
	}

	.usage-legend{
		display: inline-block;
		border-top: 4px solid #000;
		width: 25px;
		height: 4px;
		margin-right: 3px;

		&.dashed{
			border-style: dashed;
			border-bottom-color: rgba(0,0,0,0);
			border-left-color: rgba(0,0,0,0);
			border-right-color: rgba(0,0,0,0);
		}
	}
}

/* RSS Feed */

.feed-container{
	position: relative;
    margin: 0 auto 10px auto;
	height: 150px;
	overflow:hidden;
	border: 1px solid rgba(0,0,0,0.3);
	border-radius: 12px;
	background: rgba(0,0,0,0.05);

	@include transition(all, 0.25s, ease-out, 0s);

	&:after{
		content: " ";
	    width: 140%;
	    height: 100px;
	    display: block;
	    position: absolute;
	    bottom: 0;
	    box-shadow: inset 0 -14px 10px;
	    pointer-events: none;
	    left: -25%;
	    opacity: 0.15;
		display: none;
	}

	&.expanded{
		height: 400px;

		.feed{
			height: 100%;
			overflow-y: auto;
		}

		.feed-expand:after{
			content: "∧";
		}
	}

	.feed{
		padding: 8px;
		position: relative;
		height: 150px;
    	overflow: hidden;

		@include transition(all, 0.25s, ease-out, 0s);
	}

	.feed-expand{
		cursor: pointer;
		background: #f8fbfe;
		color: $color-links;
		box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.3);
		width: 50px;
		height: 50px;
		border-radius: 50px;
		position: absolute;
		bottom: -24px;
		left: calc(50% - 25px);
		font-size: 18px;
		font-weight: bold;
		border: 2px solid $color-links;
		display: none;

		&:after{
			content: "∨";
		}
	}
}

a.feed-subscribe{
	display: block;
	color: $color-links;
	font-size: 12px;
	text-align: right;
}

.feed{
	.news-item{
		font-size: 14px;
		line-height: 18px;
		padding: 15px 0;
	    border-radius: 0;
	    border-bottom: 1px solid rgba(0,0,0,0.15);

		&:first-of-type{
			padding-top: 0;
		}

		&:last-of-type{
			border-bottom: none;
		}

		h4{
			margin-top: 0;
			font-size: 14px;
		}

		.news-info{
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size:12px;
			color: rgba(0,0,0,0.75);
			margin-top: 10px;

			a{
				display: flex;
				color: $color-off-white;
				padding: 2px 10px;
				text-decoration: none;
				justify-content: space-between;
				border-radius: 12px;
				border: 1px solid #000;

				.link-text{
					text-decoration: underline;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 145px;
				}

				@include gradient($color-blue-medium, $color-blue-dark);
			}
		}
	}
}

.latest-section{
	padding: 9px 8px 9px 8px;
	font-size: 14px;

	h4{
		display: flex;
		justify-content: space-between;
		margin: 0;
	    border-bottom: 1px solid #5896ce;
	    color: #558ec2;

		a{
			display: block;
			width:14px;
			height:14px;
			background-image: url('../img/themes/sunflower/rss-white.png');
			background-repeat: no-repeat;
			background-position: 0 0;
			background-size: 14px 14px;
			border-radius: 0;
			padding: 0 !important;
		}

	}

	.date{
		font-size: 12px;
		color: #558ec2;
		padding-left: 38px;
	}

	a{
		font-size: 14px !important;
		padding-bottom: 5px !important;
		line-height: 20px;
	}
}


@media only screen and (min-width: 481px) {
	.home a.button h2{
		font-size: 32px;
	}
}


@media only screen and (min-width: 721px) {

	header .menu{
		position:relative;

		.menu-content{
			display:flex;
			align-items: center;
		}

		.more{
			font-size:40px;
			font-family:serif;
			line-height:0;
			padding:0;

			&:after{
				content:"...";
			}
		}

		.parent-menu{
			position: relative;

			&.more-parent-menu{
				& > a{
					background: none !important;

					&:after{
						display: none;
					}
				}

				.safe-mouse-space{
					position: absolute;
					width:150%;
					height:100%;
					left: 95%;
					top:0;
				}
			}

			.submenu .icon-rankings{
				font-size:14px;
			}

			& > a span{
				right: auto !important;
				left: 50% !important;
				bottom:-4px !important;
				top: auto !important;
				padding: 0 !important;

				background:url('../img/themes/sunflower/dropdown-triangle.png');
			}
		}

		& .menu-content > a:hover, & .menu-content > .parent-menu:hover > a{
			background-color: $color-sky-blue;

			@include transition(background-color, 0s, ease-out, 0s);
		}

		.submenu{
			padding-top:15px;
			position: absolute;
			padding-left: 20px;
			top: 18px;
			pointer-events: none;
			white-space: nowrap;

			&.active{
				padding-bottom: 100px;
				padding-right: 50px;
			}

			.submenu-wrap{
				padding:2px 5px;
				background:$color-blue-dark;
				border-radius: 0 0 8px 8px;
				box-shadow: 0px 5px 5px rgba(0,0,0,0.2);
			}

			a{
				float:none;
				margin: 0;
				color: $color-off-white;
				font-size: 14px;
				padding: 10px 8px;

				@include transition(color, 0.2s, ease-out, 0s);

				&:hover{
					color: $color-sky-blue;

					@include transition(background-color, 0s, ease-out, 0s);
				}
			}
		}
	}

	.rank .details .detail-section.overall .rating-container .ranking-header{
		font-size:16px;
	}

	.rank .details .detail-section .weaknesses .type div:nth-of-type(2),
	.rank .details .detail-section .resistances .type div:nth-of-type(2){
		font-size:16px;
		line-height: 16px;
	}

	.rankings-container{
		max-height:1000px;

		&.pokebox-list{
			max-height:600px;
		}
	}

	.table-container{
		overflow-x: auto;
	}

	.article{
		.threats-table,
		.alternatives-table,
		.meta-table{
			.name{
				font-size:16px;
			}
		}
	}

	a.download-csv{
		display:inline-block;
	}

	.battle-results.matrix .table-container{
		max-height: 800px;
	}

	.analysis-container .table-container{
		max-height: 600px;
	}

	.train-table{

		td.poke-name{
			padding: 8px 4px;
			width:200px;

			.name{
				font-size:16px;
				max-width:none;
			}
		}

		&.teams td.poke-name{
			display:table-cell;
		}

		td.individual-score,
		td.team-score,
		td.usage{
		    font-size: 20px;
			padding: 4px 20px 4px 0;

			.rating{
				min-width: 90px;
			}
		}

		span.usage-value{
			width: 55px;
		}
	}

	.analysis-container .format-select{
		max-width: 100%;
	}
}

// Colorblind styling

.colorblind{
	a.rating.win, .rating-container .rating.win, .summary .rating.win, .stats-table .rating.win, .train-table .rating.win,
	.battle-results.matrix .difference-table a.difference.win{
		background: #1A85FF;
	}

	a.rating.close-win, .rating-container .rating.close-win, .summary .rating.close-win, .stats-table .rating.close-win, .train-table .rating.close-win{
		background: #3176e9;
	}

	a.rating.close-loss, .rating-container .rating.close-loss, .summary .rating.close-loss, .stats-table .rating.close-loss, .train-table .rating.close-loss{
		background: #c71965;
		color: #ffe4f8;

		span{
			background-image: url('../img/themes/sunflower/rating-close-loss-dark.png?v=7');
    		opacity: 0.7;
		}
	}

	a.rating.loss, .rating-container .rating.loss, .summary .rating.loss, .stats-table .rating.loss, .train-table .rating.loss{
		background: #D41159;
		color: #ffe4f8;

		span{
			background-image: url('../img/themes/sunflower/rating-loss-dark.png?v=7');
    		opacity: 1;
		}
	}

	.battle-results.matrix .difference-table a.difference.lose{
		background: #D41159;
		color: #ffe4f8;
	}
}

// Developer panel styling and display

#dev-panel{
	width: 300px;
	position: fixed;
	right: -320px;
	top: 100px;
	background: #01475f;
	z-index: 1000;
	padding: 10px;

	@include transition(right, 0.2s, ease-out, 0s);

	&.active{
		right: 0px;
	}

	a.wrench{
		width: 50px;
		height: 50px;
		background: url('../articles/article-assets/developer-notes-1-27-0/thumb.jpg');
		background-size: 100%;
		display: block;
		position:absolute;
		left: -50px;
		top: 0;
	}

	a{
		display: block;
		width: 100%;
		box-sizing: border-box;
		font-size: 14px;
	}

	h3{
		color: #eee;
		margin-top: 0;
	}

	h4{
		color: #ccc;
		border-bottom: 1px solid #bbb;
		margin-bottom: 0;
	}
}

@media only screen and (max-width: 480px) {
	#dev-panel{ display: none; }
}


/* Ad styling and display */

#nitro-header-mobile,
#nitro-body-mobile,
#nitro-body-desktop,
#nitro-body-mobile,
#nitro-sidebar-right,
#nitro-sidebar-right-300,
#nitro-sidebar-left,
#nitro-sidebar-left-300{
	display:none;
}

#nitro-header-mobile{
	overflow:hidden;
}

.mobile-ad-container,
.desktop-ad-container{
	overflow: hidden;
	clip-path: inset(0 0 0 0);
	margin: 0 auto 5px auto;
	display:none;
}

.section.battle .mobile-ad-container{
	margin: 10px auto 5px auto;
}

// Bad ad fix

iframe#bh-div {
    display: none !important;
}

@media only screen and (min-width: 420px) {
	.rank .details .detail-tab-nav a{
		font-size: 14px;
	}
}

@media only screen and (min-width: 320px) and (max-width: 767px) {

	.nitro-pin-container{
		position: fixed;
		bottom:0;
		width:100%;
		z-index:25;
	}
	#nitro-header-mobile{
		display: block;
		margin: 0 auto;
	}

	#nitro-body-mobile{
		display: block;
		margin-top: 30px;
	}

	.mobile-ad-container{
		display:block;
		width:320px;
		height:70px;
	}
}

@media only screen and (min-width: 768px) {
	#nitro-header-desktop{
		display: block;
	}

	#nitro-body-desktop{
		display: block;
		margin-top: 30px;
	}

	.desktop-ad-container{
		display:block;
		width:728px;
		height:110px;
		margin:20px auto;

		&.nitro-header{
			margin:0 auto;
		}
	}
}

@media only screen and (min-width: 1190px) and (max-width:1449px) {
	#nitro-sidebar-left{
		display: block;
		position: fixed;
		top:95px;
		left:20px;
	}

	#nitro-sidebar-right{
		display: block;
		position: fixed;
		top:95px;
		right:20px;
	}
}

@media only screen and (min-width: 1450px) {
	#nitro-sidebar-left-300{
		display: block;
		position: fixed;
		top:95px;
		left:20px;
	}

	#nitro-sidebar-right-300{
		display: block;
		position: fixed;
		top:95px;
		right:20px;
	}
}

/* Printing for meta scorecard */

@media print {

	.background{
		display:none;
	}

	.scorecard-print{
		.section{
			display:none !important;
		}

		.section.typings{
			display:block !important;
		}

		.button{
			display:none !important;
		}

		.toggle,
		.toggle-content{
			display:none !important;
		}

		.toggle-content:nth-of-type(1){
			display:block !important;
		}

		.article .rating-table a{
			background:none;
			padding: 2px 5px;
		}

		.article .rating-table a.close-win span{
			border:2px solid #1b79b4;
		}

		.article .rating-table a.win span{
			background:#1b79b4;
		}

		.article .rating-table a.tie span{
			color:#aaa;
		}

		.article .rating-table a span{
			-webkit-print-color-adjust: exact;
			color-adjust: exact;
		}

		.rating-table.legend{
			font-size:12px;
		}

		#main h1{
			display:none;
		}

		#main p{
			display:none;
		}

		header, footer{
			display:none;
		}

		.share-link-container{
			display:none;
		}

		.article table tr:nth-of-type(2n){
			background: rgba(0, 0, 0, 0.07);
			-webkit-print-color-adjust: exact;
		}
	}

	.table-container{
		overflow-x:auto;
	}
}
