[{"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 806, "matchups": [{"opponent": "talonflame", "rating": 812}, {"opponent": "jumpluff_shadow", "rating": 729}, {"opponent": "furret", "rating": 618}, {"opponent": "gligar", "rating": 614}, {"opponent": "magcargo", "rating": 548}], "counters": [{"opponent": "mamos<PERSON>_shadow", "rating": 148}, {"opponent": "bewear", "rating": 183}, {"opponent": "claydol", "rating": 363}, {"opponent": "clodsire", "rating": 387}, {"opponent": "flygon", "rating": 456}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22854}, {"moveId": "BULLET_SEED", "uses": 19768}, {"moveId": "INFESTATION", "uses": 15692}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 17428}, {"moveId": "GRASS_KNOT", "uses": 14317}, {"moveId": "ROCK_SLIDE", "uses": 11252}, {"moveId": "STONE_EDGE", "uses": 5932}, {"moveId": "BULLDOZE", "uses": 5048}, {"moveId": "RETURN", "uses": 4330}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 100}, {"speciesId": "gligar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 836, "matchups": [{"opponent": "clodsire", "rating": 667}, {"opponent": "gligar", "rating": 641}, {"opponent": "cradily", "rating": 625}, {"opponent": "jumpluff_shadow", "rating": 530}, {"opponent": "furret", "rating": 503}], "counters": [{"opponent": "talonflame", "rating": 281}, {"opponent": "fletchinder", "rating": 286}, {"opponent": "piloswine", "rating": 383}, {"opponent": "diggersby", "rating": 428}, {"opponent": "skeledirge", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32320}, {"moveId": "WING_ATTACK", "uses": 25980}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 23280}, {"moveId": "NIGHT_SLASH", "uses": 21325}, {"moveId": "DIG", "uses": 13689}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "DIG"], "score": 96.1}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 798, "matchups": [{"opponent": "talonflame", "rating": 774}, {"opponent": "jumpluff_shadow", "rating": 770}, {"opponent": "cradily", "rating": 645}, {"opponent": "swampert_shadow", "rating": 638}, {"opponent": "furret", "rating": 552}], "counters": [{"opponent": "piloswine_shadow", "rating": 133}, {"opponent": "gligar", "rating": 396}, {"opponent": "claydol", "rating": 409}, {"opponent": "clodsire", "rating": 449}, {"opponent": "magcargo", "rating": 478}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 23301}, {"moveId": "BULLET_SEED", "uses": 19840}, {"moveId": "INFESTATION", "uses": 15160}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18753}, {"moveId": "GRASS_KNOT", "uses": 15479}, {"moveId": "ROCK_SLIDE", "uses": 12123}, {"moveId": "STONE_EDGE", "uses": 6332}, {"moveId": "BULLDOZE", "uses": 5460}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 95.8}, {"speciesId": "furret", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 765, "matchups": [{"opponent": "claydol", "rating": 746}, {"opponent": "clodsire", "rating": 662}, {"opponent": "diggersby", "rating": 631}, {"opponent": "gligar", "rating": 603}, {"opponent": "swampert_shadow", "rating": 565}], "counters": [{"opponent": "magcargo", "rating": 410}, {"opponent": "talonflame", "rating": 433}, {"opponent": "ninetales_shadow", "rating": 464}, {"opponent": "jumpluff_shadow", "rating": 480}, {"opponent": "cradily", "rating": 482}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 30217}, {"moveId": "QUICK_ATTACK", "uses": 28083}], "chargedMoves": [{"moveId": "SWIFT", "uses": 23047}, {"moveId": "TRAILBLAZE", "uses": 13167}, {"moveId": "BRICK_BREAK", "uses": 10863}, {"moveId": "DIG", "uses": 8095}, {"moveId": "HYPER_BEAM", "uses": 3135}]}, "moveset": ["SUCKER_PUNCH", "SWIFT", "TRAILBLAZE"], "score": 93.9}, {"speciesId": "diggersby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 753, "matchups": [{"opponent": "magcargo", "rating": 847}, {"opponent": "clodsire", "rating": 729}, {"opponent": "furret", "rating": 594}, {"opponent": "gligar", "rating": 589}, {"opponent": "swampert_shadow", "rating": 534}], "counters": [{"opponent": "quagsire_shadow", "rating": 375}, {"opponent": "talonflame", "rating": 411}, {"opponent": "cradily", "rating": 427}, {"opponent": "jumpluff_shadow", "rating": 460}, {"opponent": "gliscor", "rating": 461}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31573}, {"moveId": "MUD_SHOT", "uses": 26727}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 17359}, {"moveId": "SCORCHING_SANDS", "uses": 17186}, {"moveId": "RETURN", "uses": 9391}, {"moveId": "DIG", "uses": 5724}, {"moveId": "EARTHQUAKE", "uses": 4899}, {"moveId": "HYPER_BEAM", "uses": 3604}]}, "moveset": ["QUICK_ATTACK", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 92.4}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 816, "matchups": [{"opponent": "furret", "rating": 715}, {"opponent": "cradily", "rating": 668}, {"opponent": "clodsire", "rating": 663}, {"opponent": "gligar", "rating": 551}, {"opponent": "jumpluff_shadow", "rating": 530}], "counters": [{"opponent": "miltank", "rating": 150}, {"opponent": "talonflame", "rating": 262}, {"opponent": "fletchinder", "rating": 265}, {"opponent": "piloswine", "rating": 360}, {"opponent": "swampert_shadow", "rating": 426}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 21605}, {"moveId": "SAND_ATTACK", "uses": 19234}, {"moveId": "WING_ATTACK", "uses": 17453}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 21973}, {"moveId": "NIGHT_SLASH", "uses": 19755}, {"moveId": "EARTHQUAKE", "uses": 10509}, {"moveId": "SAND_TOMB", "uses": 6073}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 92.1}, {"speciesId": "diggersby_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 770, "matchups": [{"opponent": "magcargo", "rating": 824}, {"opponent": "clodsire", "rating": 698}, {"opponent": "jumpluff_shadow", "rating": 637}, {"opponent": "gligar", "rating": 571}, {"opponent": "furret", "rating": 517}], "counters": [{"opponent": "talonflame", "rating": 348}, {"opponent": "claydol", "rating": 417}, {"opponent": "quagsire_shadow", "rating": 459}, {"opponent": "cradily", "rating": 489}, {"opponent": "diggersby", "rating": 497}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31344}, {"moveId": "MUD_SHOT", "uses": 26956}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19405}, {"moveId": "SCORCHING_SANDS", "uses": 18814}, {"moveId": "HYPER_BEAM", "uses": 8404}, {"moveId": "DIG", "uses": 6210}, {"moveId": "EARTHQUAKE", "uses": 5433}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "SCORCHING_SANDS", "FIRE_PUNCH"], "score": 92}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 805, "matchups": [{"opponent": "jumpluff_shadow", "rating": 910, "opRating": 89}, {"opponent": "clodsire", "rating": 829, "opRating": 170}, {"opponent": "cradily", "rating": 773}, {"opponent": "swampert_shadow", "rating": 696, "opRating": 303}, {"opponent": "drampa", "rating": 636, "opRating": 363}], "counters": [{"opponent": "magcargo", "rating": 256}, {"opponent": "diggersby", "rating": 324}, {"opponent": "claydol", "rating": 363}, {"opponent": "talonflame", "rating": 422}, {"opponent": "furret", "rating": 468}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34605}, {"moveId": "EMBER", "uses": 23695}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 31914}, {"moveId": "FLAME_CHARGE", "uses": 16992}, {"moveId": "FLAMETHROWER", "uses": 7102}, {"moveId": "HEAT_WAVE", "uses": 2248}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 91.1}, {"speciesId": "gligar", "speciesName": "Gligar", "rating": 819, "matchups": [{"opponent": "clodsire", "rating": 702}, {"opponent": "cradily", "rating": 675}, {"opponent": "furret", "rating": 614}, {"opponent": "jumpluff_shadow", "rating": 583}, {"opponent": "swampert_shadow", "rating": 522}], "counters": [{"opponent": "talonflame", "rating": 251}, {"opponent": "miltank", "rating": 260}, {"opponent": "piloswine", "rating": 350}, {"opponent": "skeledirge", "rating": 402}, {"opponent": "magcargo", "rating": 461}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32567}, {"moveId": "WING_ATTACK", "uses": 25733}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 20913}, {"moveId": "NIGHT_SLASH", "uses": 19073}, {"moveId": "DIG", "uses": 12359}, {"moveId": "RETURN", "uses": 6033}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "DIG"], "score": 90.7}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 816, "matchups": [{"opponent": "clodsire", "rating": 892, "opRating": 107}, {"opponent": "magcargo", "rating": 737, "opRating": 262}, {"opponent": "furret", "rating": 693}, {"opponent": "cradily", "rating": 607}, {"opponent": "gligar", "rating": 594}], "counters": [{"opponent": "ninetales_shadow", "rating": 170}, {"opponent": "talonflame", "rating": 292}, {"opponent": "diggersby", "rating": 425}, {"opponent": "jumpluff_shadow", "rating": 457}, {"opponent": "swampert_shadow", "rating": 466}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 21486}, {"moveId": "SAND_ATTACK", "uses": 19792}, {"moveId": "WING_ATTACK", "uses": 17025}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 21924}, {"moveId": "NIGHT_SLASH", "uses": 19773}, {"moveId": "EARTHQUAKE", "uses": 10505}, {"moveId": "SAND_TOMB", "uses": 6058}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 90.6}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 715, "matchups": [{"opponent": "talonflame", "rating": 784}, {"opponent": "cradily", "rating": 697}, {"opponent": "clodsire", "rating": 678}, {"opponent": "gligar", "rating": 623}, {"opponent": "jumpluff_shadow", "rating": 564}], "counters": [{"opponent": "drampa", "rating": 369}, {"opponent": "pidgeot", "rating": 394}, {"opponent": "furret", "rating": 443}, {"opponent": "diggersby", "rating": 465}, {"opponent": "magcargo", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 31204}, {"moveId": "ASTONISH", "uses": 27096}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 20809}, {"moveId": "ROCK_TOMB", "uses": 18697}, {"moveId": "SHADOW_BALL", "uses": 12188}, {"moveId": "SAND_TOMB", "uses": 6603}]}, "moveset": ["SHADOW_CLAW", "ROCK_TOMB", "SHADOW_BALL"], "score": 90}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 744, "matchups": [{"opponent": "jumpluff_shadow", "rating": 906, "opRating": 93}, {"opponent": "gligar", "rating": 870}, {"opponent": "flygon", "rating": 796, "opRating": 203}, {"opponent": "diggersby", "rating": 583, "opRating": 416}, {"opponent": "swampert_shadow", "rating": 580, "opRating": 420}], "counters": [{"opponent": "magcargo", "rating": 282}, {"opponent": "furret", "rating": 465}, {"opponent": "talonflame", "rating": 466}, {"opponent": "cradily", "rating": 472}, {"opponent": "clodsire", "rating": 487}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 31917}, {"moveId": "TACKLE", "uses": 21686}, {"moveId": "ZEN_HEADBUTT", "uses": 4694}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21536}, {"moveId": "ICE_BEAM", "uses": 16897}, {"moveId": "STOMP", "uses": 7617}, {"moveId": "THUNDERBOLT", "uses": 7160}, {"moveId": "GYRO_BALL", "uses": 5046}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 89.9}, {"speciesId": "spinda", "speciesName": "Spinda", "rating": 725, "matchups": [{"opponent": "talonflame", "rating": 707}, {"opponent": "gligar", "rating": 672}, {"opponent": "jumpluff_shadow", "rating": 644}, {"opponent": "clodsire", "rating": 637}, {"opponent": "cradily", "rating": 619}], "counters": [{"opponent": "flygon_shadow", "rating": 340}, {"opponent": "furret", "rating": 412}, {"opponent": "magcargo", "rating": 427}, {"opponent": "piloswine", "rating": 438}, {"opponent": "quagsire_shadow", "rating": 493}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 32001}, {"moveId": "PSYCHO_CUT", "uses": 26299}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 27092}, {"moveId": "ROCK_TOMB", "uses": 20601}, {"moveId": "DIG", "uses": 10624}]}, "moveset": ["SUCKER_PUNCH", "ROCK_TOMB", "ICY_WIND"], "score": 89.9}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 793, "matchups": [{"opponent": "swampert_shadow", "rating": 801}, {"opponent": "cradily", "rating": 757}, {"opponent": "furret", "rating": 742}, {"opponent": "magcargo", "rating": 722}, {"opponent": "talonflame", "rating": 599}], "counters": [{"opponent": "diggersby", "rating": 215}, {"opponent": "pidgeot", "rating": 341}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "gligar", "rating": 385}, {"opponent": "clodsire", "rating": 468}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24808}, {"moveId": "COUNTER", "uses": 19272}, {"moveId": "METAL_CLAW", "uses": 14224}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20692}, {"moveId": "SWIFT", "uses": 19304}, {"moveId": "TRAILBLAZE", "uses": 11307}, {"moveId": "PLAY_ROUGH", "uses": 4393}, {"moveId": "HYPER_BEAM", "uses": 2607}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 89.9}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 705, "matchups": [{"opponent": "talonflame", "rating": 757}, {"opponent": "gligar", "rating": 645}, {"opponent": "clodsire", "rating": 608}, {"opponent": "swampert_shadow", "rating": 570}, {"opponent": "cradily", "rating": 514}], "counters": [{"opponent": "blazi<PERSON>_shadow", "rating": 193}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 274}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "abomasnow_shadow", "rating": 405}, {"opponent": "drampa", "rating": 465}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 24585}, {"moveId": "SAND_ATTACK", "uses": 20096}, {"moveId": "BITE", "uses": 9285}, {"moveId": "TAKE_DOWN", "uses": 4332}], "chargedMoves": [{"moveId": "SURF", "uses": 25567}, {"moveId": "DARK_PULSE", "uses": 16936}, {"moveId": "GRASS_KNOT", "uses": 15808}]}, "moveset": ["SUCKER_PUNCH", "SURF", "GRASS_KNOT"], "score": 87.7}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 725, "matchups": [{"opponent": "magcargo", "rating": 796, "opRating": 204}, {"opponent": "talonflame", "rating": 776}, {"opponent": "clodsire", "rating": 740}, {"opponent": "cradily", "rating": 568}, {"opponent": "diggersby", "rating": 504, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 359}, {"opponent": "swampert_shadow", "rating": 389}, {"opponent": "claydol", "rating": 413}, {"opponent": "furret", "rating": 478}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 21217}, {"moveId": "SAND_ATTACK", "uses": 19565}, {"moveId": "MUD_SHOT", "uses": 17588}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 19350}, {"moveId": "SCORCHING_SANDS", "uses": 12950}, {"moveId": "STONE_EDGE", "uses": 10724}, {"moveId": "BOOMBURST", "uses": 6424}, {"moveId": "EARTH_POWER", "uses": 5118}, {"moveId": "EARTHQUAKE", "uses": 3708}]}, "moveset": ["DRAGON_TAIL", "SCORCHING_SANDS", "DRAGON_CLAW"], "score": 87.3}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 839, "matchups": [{"opponent": "gligar", "rating": 770, "opRating": 229}, {"opponent": "jumpluff_shadow", "rating": 755, "opRating": 244}, {"opponent": "diggersby", "rating": 696, "opRating": 303}, {"opponent": "flygon", "rating": 685, "opRating": 314}, {"opponent": "furret", "rating": 614, "opRating": 385}], "counters": [{"opponent": "magcargo", "rating": 188}, {"opponent": "swampert_shadow", "rating": 209}, {"opponent": "typhlosion_shadow", "rating": 243}, {"opponent": "clodsire", "rating": 442}, {"opponent": "cradily", "rating": 472}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27019}, {"moveId": "FIRE_SPIN", "uses": 15390}, {"moveId": "STEEL_WING", "uses": 9153}, {"moveId": "PECK", "uses": 6790}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22100}, {"moveId": "FLY", "uses": 18901}, {"moveId": "FLAME_CHARGE", "uses": 10837}, {"moveId": "HURRICANE", "uses": 4198}, {"moveId": "FIRE_BLAST", "uses": 2505}]}, "moveset": ["INCINERATE", "FLY", "BRAVE_BIRD"], "score": 86.7}, {"speciesId": "drampa", "speciesName": "Drampa", "rating": 757, "matchups": [{"opponent": "talonflame", "rating": 786}, {"opponent": "swampert_shadow", "rating": 778, "opRating": 221}, {"opponent": "skeledirge", "rating": 773, "opRating": 226}, {"opponent": "quagsire_shadow", "rating": 669, "opRating": 330}, {"opponent": "furret", "rating": 526, "opRating": 473}], "counters": [{"opponent": "gliscor", "rating": 288}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "clodsire", "rating": 391}, {"opponent": "gligar", "rating": 454}, {"opponent": "cradily", "rating": 465}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 37294}, {"moveId": "EXTRASENSORY", "uses": 21006}], "chargedMoves": [{"moveId": "SWIFT", "uses": 22813}, {"moveId": "FLY", "uses": 18144}, {"moveId": "OUTRAGE", "uses": 12845}, {"moveId": "DRAGON_PULSE", "uses": 4287}]}, "moveset": ["DRAGON_BREATH", "SWIFT", "FLY"], "score": 86.5}, {"speciesId": "kecleon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 728, "matchups": [{"opponent": "claydol", "rating": 758, "opRating": 241}, {"opponent": "gligar", "rating": 652}, {"opponent": "cradily", "rating": 567}, {"opponent": "jumpluff_shadow", "rating": 555}, {"opponent": "swampert_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "magcargo", "rating": 354}, {"opponent": "furret", "rating": 468}, {"opponent": "diggersby", "rating": 471}, {"opponent": "clodsire", "rating": 495}, {"opponent": "talonflame", "rating": 496}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39695}, {"moveId": "LICK", "uses": 18605}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 13825}, {"moveId": "FOUL_PLAY", "uses": 13253}, {"moveId": "AERIAL_ACE", "uses": 12961}, {"moveId": "FLAMETHROWER", "uses": 8290}, {"moveId": "THUNDER", "uses": 5455}, {"moveId": "SHADOW_SNEAK", "uses": 4537}]}, "moveset": ["SUCKER_PUNCH", "AERIAL_ACE", "ICE_BEAM"], "score": 85.6}, {"speciesId": "skeledirge", "speciesName": "Skeledirge", "rating": 809, "matchups": [{"opponent": "gligar", "rating": 920, "opRating": 79}, {"opponent": "talonflame", "rating": 838}, {"opponent": "jumpluff_shadow", "rating": 758, "opRating": 241}, {"opponent": "diggersby", "rating": 579, "opRating": 420}, {"opponent": "cradily", "rating": 510}], "counters": [{"opponent": "quagsire_shadow", "rating": 124}, {"opponent": "swampert_shadow", "rating": 209}, {"opponent": "magcargo", "rating": 307}, {"opponent": "clodsire", "rating": 451}, {"opponent": "furret", "rating": 475}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32678}, {"moveId": "HEX", "uses": 20351}, {"moveId": "BITE", "uses": 5236}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15447}, {"moveId": "TORCH_SONG", "uses": 14984}, {"moveId": "CRUNCH", "uses": 8811}, {"moveId": "SHADOW_BALL", "uses": 8392}, {"moveId": "DISARMING_VOICE", "uses": 7031}, {"moveId": "FLAMETHROWER", "uses": 3479}]}, "moveset": ["INCINERATE", "TORCH_SONG", "SHADOW_BALL"], "score": 84.7}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 711, "matchups": [{"opponent": "talonflame", "rating": 788}, {"opponent": "clodsire", "rating": 668, "opRating": 332}, {"opponent": "claydol", "rating": 600, "opRating": 400}, {"opponent": "magcargo", "rating": 564, "opRating": 436}, {"opponent": "cradily", "rating": 516}], "counters": [{"opponent": "piloswine", "rating": 262}, {"opponent": "furret", "rating": 331}, {"opponent": "diggersby", "rating": 402}, {"opponent": "jumpluff_shadow", "rating": 428}, {"opponent": "gligar", "rating": 458}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 21094}, {"moveId": "SAND_ATTACK", "uses": 19632}, {"moveId": "MUD_SHOT", "uses": 17576}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 19289}, {"moveId": "SCORCHING_SANDS", "uses": 12964}, {"moveId": "STONE_EDGE", "uses": 10717}, {"moveId": "BOOMBURST", "uses": 6423}, {"moveId": "EARTH_POWER", "uses": 5115}, {"moveId": "EARTHQUAKE", "uses": 3706}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "SCORCHING_SANDS"], "score": 84.3}, {"speciesId": "clodsire", "speciesName": "Clodsire", "rating": 750, "matchups": [{"opponent": "talonflame", "rating": 843}, {"opponent": "magcargo", "rating": 817, "opRating": 182}, {"opponent": "cradily", "rating": 762}, {"opponent": "furret", "rating": 629, "opRating": 370}, {"opponent": "jumpluff_shadow", "rating": 512, "opRating": 487}], "counters": [{"opponent": "claydol", "rating": 264}, {"opponent": "diggersby", "rating": 281}, {"opponent": "piloswine", "rating": 285}, {"opponent": "swampert_shadow", "rating": 294}, {"opponent": "gligar", "rating": 477}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 32873}, {"moveId": "MUD_SHOT", "uses": 25427}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 12373}, {"moveId": "STONE_EDGE", "uses": 11672}, {"moveId": "SLUDGE_BOMB", "uses": 11622}, {"moveId": "WATER_PULSE", "uses": 10077}, {"moveId": "EARTHQUAKE", "uses": 9950}, {"moveId": "ACID_SPRAY", "uses": 2642}]}, "moveset": ["POISON_STING", "EARTHQUAKE", "STONE_EDGE"], "score": 84.1}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 809, "matchups": [{"opponent": "jumpluff_shadow", "rating": 932, "opRating": 67}, {"opponent": "cradily", "rating": 789, "opRating": 210}, {"opponent": "flygon", "rating": 662, "opRating": 337}, {"opponent": "gligar", "rating": 654, "opRating": 345}, {"opponent": "furret", "rating": 583, "opRating": 416}], "counters": [{"opponent": "magcargo", "rating": 170}, {"opponent": "quagsire_shadow", "rating": 251}, {"opponent": "clodsire", "rating": 324}, {"opponent": "swampert_shadow", "rating": 352}, {"opponent": "talonflame", "rating": 496}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 24558}, {"moveId": "FIRE_SPIN", "uses": 21778}, {"moveId": "FEINT_ATTACK", "uses": 12004}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17671}, {"moveId": "PSYSHOCK", "uses": 12553}, {"moveId": "SCORCHING_SANDS", "uses": 9502}, {"moveId": "OVERHEAT", "uses": 7066}, {"moveId": "SOLAR_BEAM", "uses": 4266}, {"moveId": "FLAMETHROWER", "uses": 3772}, {"moveId": "FIRE_BLAST", "uses": 2044}, {"moveId": "HEAT_WAVE", "uses": 1201}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 84.1}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 699, "matchups": [{"opponent": "talonflame", "rating": 796}, {"opponent": "skeledirge", "rating": 787, "opRating": 212}, {"opponent": "swampert_shadow", "rating": 590, "opRating": 409}, {"opponent": "gligar", "rating": 521, "opRating": 478}, {"opponent": "furret", "rating": 512, "opRating": 487}], "counters": [{"opponent": "magcargo", "rating": 346}, {"opponent": "flygon", "rating": 364}, {"opponent": "cradily", "rating": 375}, {"opponent": "clodsire", "rating": 403}, {"opponent": "jumpluff_shadow", "rating": 408}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 35000}, {"moveId": "LICK", "uses": 18022}, {"moveId": "ZEN_HEADBUTT", "uses": 5276}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25260}, {"moveId": "SHADOW_BALL", "uses": 11114}, {"moveId": "EARTHQUAKE", "uses": 8980}, {"moveId": "SOLAR_BEAM", "uses": 6825}, {"moveId": "HYPER_BEAM", "uses": 6163}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "SHADOW_BALL"], "score": 84}, {"speciesId": "jumpluff_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 834, "matchups": [{"opponent": "swampert_shadow", "rating": 967, "opRating": 32}, {"opponent": "claydol", "rating": 931, "opRating": 68}, {"opponent": "diggersby", "rating": 699, "opRating": 300}, {"opponent": "furret", "rating": 660}, {"opponent": "gligar", "rating": 633}], "counters": [{"opponent": "talonflame", "rating": 259}, {"opponent": "pidgeot", "rating": 274}, {"opponent": "magcargo", "rating": 277}, {"opponent": "cradily", "rating": 305}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 22886}, {"moveId": "BULLET_SEED", "uses": 19720}, {"moveId": "INFESTATION", "uses": 15614}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 19442}, {"moveId": "AERIAL_ACE", "uses": 17564}, {"moveId": "ENERGY_BALL", "uses": 11282}, {"moveId": "DAZZLING_GLEAM", "uses": 6586}, {"moveId": "SOLAR_BEAM", "uses": 3204}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FAIRY_WIND", "AERIAL_ACE", "ACROBATICS"], "score": 83.4}, {"speciesId": "farfetchd", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 777, "matchups": [{"opponent": "swampert_shadow", "rating": 942, "opRating": 57}, {"opponent": "quagsire_shadow", "rating": 942, "opRating": 57}, {"opponent": "claydol", "rating": 874, "opRating": 125}, {"opponent": "diggersby", "rating": 664, "opRating": 335}, {"opponent": "furret", "rating": 545, "opRating": 454}], "counters": [{"opponent": "clodsire", "rating": 338}, {"opponent": "magcargo", "rating": 346}, {"opponent": "talonflame", "rating": 366}, {"opponent": "gligar", "rating": 400}, {"opponent": "cradily", "rating": 479}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31432}, {"moveId": "AIR_SLASH", "uses": 19732}, {"moveId": "CUT", "uses": 7124}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 24721}, {"moveId": "LEAF_BLADE", "uses": 24241}, {"moveId": "AERIAL_ACE", "uses": 9324}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "AIR_CUTTER"], "score": 83}, {"speciesId": "girafarig_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 695, "matchups": [{"opponent": "clodsire", "rating": 842}, {"opponent": "swampert_shadow", "rating": 765}, {"opponent": "talonflame", "rating": 642}, {"opponent": "magcargo", "rating": 592}, {"opponent": "furret", "rating": 534}], "counters": [{"opponent": "abomasnow_shadow", "rating": 339}, {"opponent": "jumpluff_shadow", "rating": 411}, {"opponent": "cradily", "rating": 440}, {"opponent": "gligar", "rating": 454}, {"opponent": "claydol", "rating": 483}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 22863}, {"moveId": "CONFUSION", "uses": 21380}, {"moveId": "TACKLE", "uses": 14069}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 26055}, {"moveId": "TRAILBLAZE", "uses": 16587}, {"moveId": "THUNDERBOLT", "uses": 7517}, {"moveId": "PSYCHIC", "uses": 4966}, {"moveId": "MIRROR_COAT", "uses": 3091}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 82.8}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 717, "matchups": [{"opponent": "clodsire", "rating": 890, "opRating": 109}, {"opponent": "swampert_shadow", "rating": 838, "opRating": 161}, {"opponent": "magcargo", "rating": 806, "opRating": 193}, {"opponent": "talonflame", "rating": 785}, {"opponent": "furret", "rating": 521}], "counters": [{"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "quagsire_shadow", "rating": 285}, {"opponent": "cradily", "rating": 357}, {"opponent": "gligar", "rating": 370}, {"opponent": "diggersby", "rating": 428}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 16365}, {"moveId": "ICE_FANG", "uses": 15813}, {"moveId": "FIRE_FANG", "uses": 11533}, {"moveId": "THUNDER_FANG", "uses": 8939}, {"moveId": "BITE", "uses": 5625}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 18179}, {"moveId": "SCORCHING_SANDS", "uses": 13825}, {"moveId": "BODY_SLAM", "uses": 12042}, {"moveId": "EARTH_POWER", "uses": 5375}, {"moveId": "STONE_EDGE", "uses": 4926}, {"moveId": "EARTHQUAKE", "uses": 4028}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 81.9}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 787, "matchups": [{"opponent": "cradily", "rating": 831, "opRating": 168}, {"opponent": "swampert_shadow", "rating": 799, "opRating": 200}, {"opponent": "furret", "rating": 766, "opRating": 233}, {"opponent": "magcargo", "rating": 750, "opRating": 250}, {"opponent": "diggersby", "rating": 594, "opRating": 405}], "counters": [{"opponent": "ninetales_shadow", "rating": 170}, {"opponent": "gligar", "rating": 305}, {"opponent": "clodsire", "rating": 314}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "talonflame", "rating": 403}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31785}, {"moveId": "SHADOW_CLAW", "uses": 26515}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28257}, {"moveId": "NIGHT_SLASH", "uses": 21485}, {"moveId": "DIG", "uses": 8605}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 81.9}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 773, "matchups": [{"opponent": "magcargo", "rating": 824, "opRating": 175}, {"opponent": "skeledirge", "rating": 811, "opRating": 188}, {"opponent": "cradily", "rating": 710}, {"opponent": "swampert_shadow", "rating": 679, "opRating": 320}, {"opponent": "flygon", "rating": 539, "opRating": 460}], "counters": [{"opponent": "claydol", "rating": 280}, {"opponent": "gligar", "rating": 332}, {"opponent": "clodsire", "rating": 360}, {"opponent": "jumpluff_shadow", "rating": 405}, {"opponent": "talonflame", "rating": 455}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 31440}, {"moveId": "EMBER", "uses": 26860}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26320}, {"moveId": "SCORCHING_SANDS", "uses": 19297}, {"moveId": "FLAMETHROWER", "uses": 8227}, {"moveId": "FIRE_BLAST", "uses": 4445}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 81.7}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 741, "matchups": [{"opponent": "jumpluff_shadow", "rating": 892}, {"opponent": "gligar", "rating": 831}, {"opponent": "cradily", "rating": 827}, {"opponent": "magcargo", "rating": 555}, {"opponent": "furret", "rating": 542}], "counters": [{"opponent": "castform_sunny", "rating": 106}, {"opponent": "ninetales", "rating": 123}, {"opponent": "quagsire_shadow", "rating": 198}, {"opponent": "ninetales_shadow", "rating": 325}, {"opponent": "talonflame", "rating": 492}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 35336}, {"moveId": "ICE_SHARD", "uses": 22964}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 20901}, {"moveId": "ICICLE_SPEAR", "uses": 17518}, {"moveId": "STONE_EDGE", "uses": 7422}, {"moveId": "HIGH_HORSEPOWER", "uses": 6771}, {"moveId": "BULLDOZE", "uses": 5583}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 81.4}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 759, "matchups": [{"opponent": "jumpluff_shadow", "rating": 893, "opRating": 106}, {"opponent": "swampert_shadow", "rating": 835, "opRating": 164}, {"opponent": "furret", "rating": 721, "opRating": 278}, {"opponent": "cradily", "rating": 698}, {"opponent": "talonflame", "rating": 549}], "counters": [{"opponent": "typhlosion_shadow", "rating": 273}, {"opponent": "skeledirge", "rating": 312}, {"opponent": "magcargo", "rating": 367}, {"opponent": "gligar", "rating": 389}, {"opponent": "clodsire", "rating": 444}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 30786}, {"moveId": "MAGICAL_LEAF", "uses": 19900}, {"moveId": "RAZOR_LEAF", "uses": 7644}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30701}, {"moveId": "SLUDGE_BOMB", "uses": 15677}, {"moveId": "LEAF_TORNADO", "uses": 5864}, {"moveId": "ACID_SPRAY", "uses": 3465}, {"moveId": "SOLAR_BEAM", "uses": 2546}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 80.7}, {"speciesId": "lileep_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 745, "matchups": [{"opponent": "talonflame", "rating": 771}, {"opponent": "diggersby", "rating": 655, "opRating": 344}, {"opponent": "cradily", "rating": 582}, {"opponent": "jumpluff_shadow", "rating": 569, "opRating": 430}, {"opponent": "furret", "rating": 523}], "counters": [{"opponent": "piloswine", "rating": 100}, {"opponent": "magcargo", "rating": 333}, {"opponent": "claydol", "rating": 396}, {"opponent": "gligar", "rating": 400}, {"opponent": "clodsire", "rating": 430}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22975}, {"moveId": "BULLET_SEED", "uses": 19827}, {"moveId": "INFESTATION", "uses": 15467}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 26684}, {"moveId": "GRASS_KNOT", "uses": 24934}, {"moveId": "MIRROR_COAT", "uses": 6668}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "GRASS_KNOT", "ANCIENT_POWER"], "score": 80.4}, {"speciesId": "lileep", "speciesName": "<PERSON><PERSON>", "rating": 727, "matchups": [{"opponent": "talonflame", "rating": 811}, {"opponent": "diggersby", "rating": 738, "opRating": 261}, {"opponent": "swampert_shadow", "rating": 642, "opRating": 357}, {"opponent": "jumpluff_shadow", "rating": 609, "opRating": 390}, {"opponent": "furret", "rating": 599}], "counters": [{"opponent": "magcargo", "rating": 299}, {"opponent": "gligar", "rating": 305}, {"opponent": "gliscor", "rating": 327}, {"opponent": "clodsire", "rating": 382}, {"opponent": "cradily", "rating": 430}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22824}, {"moveId": "BULLET_SEED", "uses": 19813}, {"moveId": "INFESTATION", "uses": 15692}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 23035}, {"moveId": "GRASS_KNOT", "uses": 21802}, {"moveId": "RETURN", "uses": 7871}, {"moveId": "MIRROR_COAT", "uses": 5576}]}, "moveset": ["ACID", "GRASS_KNOT", "ANCIENT_POWER"], "score": 80.3}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 746, "matchups": [{"opponent": "talonflame", "rating": 772}, {"opponent": "skeledirge", "rating": 716, "opRating": 284}, {"opponent": "jumpluff_shadow", "rating": 652, "opRating": 348}, {"opponent": "swampert_shadow", "rating": 636, "opRating": 364}, {"opponent": "drampa", "rating": 560, "opRating": 440}], "counters": [{"opponent": "quagsire_shadow", "rating": 229}, {"opponent": "magcargo", "rating": 243}, {"opponent": "claydol", "rating": 351}, {"opponent": "diggersby", "rating": 367}, {"opponent": "cradily", "rating": 454}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 21229}, {"moveId": "SNARL", "uses": 19793}, {"moveId": "THUNDER_FANG", "uses": 11595}, {"moveId": "BITE", "uses": 5661}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 11551}, {"moveId": "WILD_CHARGE", "uses": 10937}, {"moveId": "FLAMETHROWER", "uses": 10082}, {"moveId": "CRUNCH", "uses": 9428}, {"moveId": "SCORCHING_SANDS", "uses": 8801}, {"moveId": "BULLDOZE", "uses": 4720}, {"moveId": "FIRE_BLAST", "uses": 2707}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 80}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 758, "matchups": [{"opponent": "clodsire", "rating": 912, "opRating": 87}, {"opponent": "jumpluff_shadow", "rating": 892, "opRating": 107}, {"opponent": "gligar", "rating": 831}, {"opponent": "magcargo", "rating": 633, "opRating": 366}, {"opponent": "diggersby", "rating": 629, "opRating": 370}], "counters": [{"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "furret", "rating": 359}, {"opponent": "talonflame", "rating": 411}, {"opponent": "cradily", "rating": 427}, {"opponent": "swampert_shadow", "rating": 452}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34680}, {"moveId": "ICE_SHARD", "uses": 23620}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19978}, {"moveId": "ICICLE_SPEAR", "uses": 16757}, {"moveId": "STONE_EDGE", "uses": 6970}, {"moveId": "HIGH_HORSEPOWER", "uses": 6460}, {"moveId": "BULLDOZE", "uses": 5289}, {"moveId": "RETURN", "uses": 2774}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 79.9}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 738, "matchups": [{"opponent": "magcargo", "rating": 863}, {"opponent": "clodsire", "rating": 852}, {"opponent": "talonflame", "rating": 841}, {"opponent": "flygon", "rating": 720, "opRating": 279}, {"opponent": "diggersby", "rating": 514}], "counters": [{"opponent": "jumpluff_shadow", "rating": 307}, {"opponent": "cradily", "rating": 333}, {"opponent": "pidgeot", "rating": 362}, {"opponent": "furret", "rating": 450}, {"opponent": "gligar", "rating": 477}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32781}, {"moveId": "WATER_GUN", "uses": 25519}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 23409}, {"moveId": "SLUDGE", "uses": 12230}, {"moveId": "MUDDY_WATER", "uses": 8941}, {"moveId": "SURF", "uses": 6527}, {"moveId": "EARTHQUAKE", "uses": 5192}, {"moveId": "SLUDGE_WAVE", "uses": 2213}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 79.9}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 751, "matchups": [{"opponent": "swampert_shadow", "rating": 839, "opRating": 160}, {"opponent": "magcargo", "rating": 800, "opRating": 199}, {"opponent": "furret", "rating": 581}, {"opponent": "gligar", "rating": 539}, {"opponent": "cradily", "rating": 506}], "counters": [{"opponent": "diggersby_shadow", "rating": 71}, {"opponent": "pidgeot", "rating": 246}, {"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "clodsire", "rating": 396}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 30937}, {"moveId": "TACKLE", "uses": 20487}, {"moveId": "LOW_KICK", "uses": 6891}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 29057}, {"moveId": "STOMP", "uses": 15743}, {"moveId": "PAYBACK", "uses": 11504}, {"moveId": "DRAIN_PUNCH", "uses": 2029}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "STOMP"], "score": 79.7}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 722, "matchups": [{"opponent": "clodsire", "rating": 893}, {"opponent": "magcargo", "rating": 886}, {"opponent": "talonflame", "rating": 875}, {"opponent": "gligar", "rating": 639}, {"opponent": "diggersby", "rating": 602}], "counters": [{"opponent": "cradily", "rating": 281}, {"opponent": "furret", "rating": 368}, {"opponent": "drampa", "rating": 447}, {"opponent": "flygon", "rating": 480}, {"opponent": "swampert_shadow", "rating": 481}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31788}, {"moveId": "WATER_GUN", "uses": 26512}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 23437}, {"moveId": "SLUDGE", "uses": 12191}, {"moveId": "MUDDY_WATER", "uses": 8978}, {"moveId": "SURF", "uses": 6542}, {"moveId": "EARTHQUAKE", "uses": 5182}, {"moveId": "SLUDGE_WAVE", "uses": 2202}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 79.7}, {"speciesId": "marowak_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 772, "matchups": [{"opponent": "skeledirge", "rating": 836, "opRating": 164}, {"opponent": "jumpluff_shadow", "rating": 752, "opRating": 248}, {"opponent": "flygon", "rating": 688, "opRating": 312}, {"opponent": "gligar", "rating": 568, "opRating": 432}, {"opponent": "diggersby", "rating": 540, "opRating": 460}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "swampert_shadow", "rating": 209}, {"opponent": "clodsire", "rating": 370}, {"opponent": "cradily", "rating": 395}, {"opponent": "talonflame", "rating": 481}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 25803}, {"moveId": "FIRE_SPIN", "uses": 24518}, {"moveId": "ROCK_SMASH", "uses": 7989}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 20635}, {"moveId": "SHADOW_BONE", "uses": 17266}, {"moveId": "FIRE_BLAST", "uses": 8120}, {"moveId": "FLAME_WHEEL", "uses": 6785}, {"moveId": "SHADOW_BALL", "uses": 5482}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SHADOW_BONE", "BONE_CLUB"], "score": 79.6}, {"speciesId": "golurk_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 702, "matchups": [{"opponent": "cradily", "rating": 910}, {"opponent": "clodsire", "rating": 902, "opRating": 97}, {"opponent": "swampert_shadow", "rating": 785, "opRating": 214}, {"opponent": "magcargo", "rating": 742, "opRating": 257}, {"opponent": "diggersby", "rating": 738, "opRating": 261}], "counters": [{"opponent": "abomasnow_shadow", "rating": 234}, {"opponent": "jumpluff_shadow", "rating": 245}, {"opponent": "gligar", "rating": 358}, {"opponent": "furret", "rating": 365}, {"opponent": "talonflame", "rating": 496}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31607}, {"moveId": "ASTONISH", "uses": 26693}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 22175}, {"moveId": "SHADOW_PUNCH", "uses": 20830}, {"moveId": "EARTH_POWER", "uses": 11640}, {"moveId": "POLTERGEIST", "uses": 3666}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "DYNAMIC_PUNCH"], "score": 79.5}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 756, "matchups": [{"opponent": "gligar", "rating": 824}, {"opponent": "cradily", "rating": 782}, {"opponent": "clodsire", "rating": 773}, {"opponent": "talonflame", "rating": 696}, {"opponent": "furret", "rating": 696}], "counters": [{"opponent": "quagsire_shadow", "rating": 161}, {"opponent": "claydol", "rating": 227}, {"opponent": "magcargo", "rating": 243}, {"opponent": "flygon_shadow", "rating": 288}, {"opponent": "diggersby", "rating": 491}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27040}, {"moveId": "EMBER", "uses": 15842}, {"moveId": "SHADOW_CLAW", "uses": 15417}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 28456}, {"moveId": "THUNDER_PUNCH", "uses": 12333}, {"moveId": "SOLAR_BEAM", "uses": 7885}, {"moveId": "OVERHEAT", "uses": 5988}, {"moveId": "FIRE_BLAST", "uses": 3516}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 79.2}, {"speciesId": "marowak_alolan", "speciesName": "Marowak (Alolan)", "rating": 749, "matchups": [{"opponent": "skeledirge", "rating": 868, "opRating": 132}, {"opponent": "jumpluff_shadow", "rating": 624, "opRating": 376}, {"opponent": "gligar", "rating": 600, "opRating": 400}, {"opponent": "cradily", "rating": 568}, {"opponent": "magcargo", "rating": 504, "opRating": 496}], "counters": [{"opponent": "swampert_shadow", "rating": 183}, {"opponent": "furret", "rating": 346}, {"opponent": "clodsire", "rating": 372}, {"opponent": "diggersby", "rating": 405}, {"opponent": "talonflame", "rating": 429}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 25597}, {"moveId": "FIRE_SPIN", "uses": 24582}, {"moveId": "ROCK_SMASH", "uses": 8098}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 18676}, {"moveId": "SHADOW_BONE", "uses": 15766}, {"moveId": "FIRE_BLAST", "uses": 7141}, {"moveId": "FLAME_WHEEL", "uses": 5901}, {"moveId": "RETURN", "uses": 5865}, {"moveId": "SHADOW_BALL", "uses": 4997}]}, "moveset": ["FIRE_SPIN", "BONE_CLUB", "SHADOW_BONE"], "score": 78.9}, {"speciesId": "linoone", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 696, "matchups": [{"opponent": "swampert_shadow", "rating": 850, "opRating": 149}, {"opponent": "claydol", "rating": 733, "opRating": 266}, {"opponent": "gligar", "rating": 649, "opRating": 350}, {"opponent": "clodsire", "rating": 629, "opRating": 370}, {"opponent": "diggersby", "rating": 600, "opRating": 399}], "counters": [{"opponent": "drampa", "rating": 313}, {"opponent": "talonflame", "rating": 344}, {"opponent": "magcargo", "rating": 346}, {"opponent": "furret", "rating": 390}, {"opponent": "cradily", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 21956}, {"moveId": "SAND_ATTACK", "uses": 20057}, {"moveId": "TACKLE", "uses": 16261}], "chargedMoves": [{"moveId": "SWIFT", "uses": 28162}, {"moveId": "GRASS_KNOT", "uses": 13764}, {"moveId": "DIG", "uses": 9985}, {"moveId": "THUNDER", "uses": 6361}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "GRASS_KNOT"], "score": 78.8}, {"speciesId": "pidgeot_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 745, "matchups": [{"opponent": "claydol", "rating": 855, "opRating": 144}, {"opponent": "jumpluff_shadow", "rating": 686, "opRating": 313}, {"opponent": "diggersby", "rating": 676, "opRating": 323}, {"opponent": "gligar", "rating": 626, "opRating": 373}, {"opponent": "swampert_shadow", "rating": 588, "opRating": 411}], "counters": [{"opponent": "magcargo", "rating": 243}, {"opponent": "clodsire", "rating": 319}, {"opponent": "cradily", "rating": 343}, {"opponent": "furret", "rating": 375}, {"opponent": "talonflame", "rating": 462}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 18654}, {"moveId": "WING_ATTACK", "uses": 16709}, {"moveId": "AIR_SLASH", "uses": 12519}, {"moveId": "STEEL_WING", "uses": 10388}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 23346}, {"moveId": "AIR_CUTTER", "uses": 20068}, {"moveId": "AERIAL_ACE", "uses": 7589}, {"moveId": "HURRICANE", "uses": 4081}, {"moveId": "FEATHER_DANCE", "uses": 2915}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["GUST", "BRAVE_BIRD", "AIR_CUTTER"], "score": 78.6}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 689, "matchups": [{"opponent": "magcargo", "rating": 792, "opRating": 208}, {"opponent": "talonflame", "rating": 768}, {"opponent": "clodsire", "rating": 548, "opRating": 452}, {"opponent": "jumpluff_shadow", "rating": 528, "opRating": 472}, {"opponent": "furret", "rating": 512, "opRating": 488}], "counters": [{"opponent": "gligar", "rating": 309}, {"opponent": "quagsire_shadow", "rating": 329}, {"opponent": "cradily", "rating": 333}, {"opponent": "diggersby", "rating": 370}, {"opponent": "swampert_shadow", "rating": 415}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 22970}, {"moveId": "MUD_SHOT", "uses": 20199}, {"moveId": "METAL_CLAW", "uses": 15138}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 16880}, {"moveId": "ROCK_TOMB", "uses": 16169}, {"moveId": "SCORCHING_SANDS", "uses": 13879}, {"moveId": "BULLDOZE", "uses": 7440}, {"moveId": "EARTHQUAKE", "uses": 3920}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "NIGHT_SLASH"], "score": 78.4}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 810, "matchups": [{"opponent": "claydol", "rating": 931, "opRating": 68}, {"opponent": "flygon", "rating": 761, "opRating": 238}, {"opponent": "swampert_shadow", "rating": 728, "opRating": 271}, {"opponent": "furret", "rating": 696, "opRating": 303}, {"opponent": "jumpluff_shadow", "rating": 578, "opRating": 421}], "counters": [{"opponent": "magcargo", "rating": 209}, {"opponent": "talonflame", "rating": 229}, {"opponent": "cradily", "rating": 420}, {"opponent": "clodsire", "rating": 425}, {"opponent": "gligar", "rating": 450}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 22911}, {"moveId": "BULLET_SEED", "uses": 19719}, {"moveId": "INFESTATION", "uses": 15643}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 17823}, {"moveId": "AERIAL_ACE", "uses": 16151}, {"moveId": "ENERGY_BALL", "uses": 10502}, {"moveId": "DAZZLING_GLEAM", "uses": 6049}, {"moveId": "RETURN", "uses": 4656}, {"moveId": "SOLAR_BEAM", "uses": 3089}]}, "moveset": ["FAIRY_WIND", "AERIAL_ACE", "ACROBATICS"], "score": 77.9}, {"speciesId": "girafarig", "speciesName": "Girafarig", "rating": 691, "matchups": [{"opponent": "clodsire", "rating": 846, "opRating": 153}, {"opponent": "swampert_shadow", "rating": 803, "opRating": 196}, {"opponent": "flygon", "rating": 696, "opRating": 303}, {"opponent": "talonflame", "rating": 630}, {"opponent": "drampa", "rating": 592, "opRating": 407}], "counters": [{"opponent": "claydol", "rating": 342}, {"opponent": "jumpluff_shadow", "rating": 349}, {"opponent": "cradily", "rating": 361}, {"opponent": "diggersby", "rating": 370}, {"opponent": "gligar", "rating": 381}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 22790}, {"moveId": "CONFUSION", "uses": 21050}, {"moveId": "TACKLE", "uses": 14492}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 22312}, {"moveId": "TRAILBLAZE", "uses": 14462}, {"moveId": "RETURN", "uses": 8303}, {"moveId": "THUNDERBOLT", "uses": 6310}, {"moveId": "PSYCHIC", "uses": 4326}, {"moveId": "MIRROR_COAT", "uses": 2589}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 77.5}, {"speciesId": "marowak_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 707, "matchups": [{"opponent": "clodsire", "rating": 900, "opRating": 100}, {"opponent": "swampert_shadow", "rating": 852, "opRating": 148}, {"opponent": "magcargo", "rating": 828, "opRating": 172}, {"opponent": "talonflame", "rating": 740}, {"opponent": "flygon", "rating": 732, "opRating": 268}], "counters": [{"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "gligar", "rating": 305}, {"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "cradily", "rating": 361}, {"opponent": "furret", "rating": 471}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45181}, {"moveId": "ROCK_SMASH", "uses": 13119}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 30510}, {"moveId": "ROCK_SLIDE", "uses": 16770}, {"moveId": "DIG", "uses": 5880}, {"moveId": "EARTHQUAKE", "uses": 5088}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "ROCK_SLIDE"], "score": 77.4}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 763, "matchups": [{"opponent": "jumpluff_shadow", "rating": 910, "opRating": 89}, {"opponent": "gligar", "rating": 824, "opRating": 175}, {"opponent": "furret", "rating": 739, "opRating": 260}, {"opponent": "swampert_shadow", "rating": 696, "opRating": 303}, {"opponent": "drampa", "rating": 636, "opRating": 363}], "counters": [{"opponent": "quagsire_shadow", "rating": 133}, {"opponent": "magcargo", "rating": 213}, {"opponent": "diggersby", "rating": 410}, {"opponent": "talonflame", "rating": 455}, {"opponent": "cradily", "rating": 468}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 26078}, {"moveId": "EMBER", "uses": 16558}, {"moveId": "SHADOW_CLAW", "uses": 15689}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 28497}, {"moveId": "THUNDER_PUNCH", "uses": 12374}, {"moveId": "SOLAR_BEAM", "uses": 7875}, {"moveId": "OVERHEAT", "uses": 5999}, {"moveId": "FIRE_BLAST", "uses": 3530}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 77.4}, {"speciesId": "vigoroth", "speciesName": "Vigoroth", "rating": 684, "matchups": [{"opponent": "magcargo", "rating": 737, "opRating": 262}, {"opponent": "talonflame", "rating": 703}, {"opponent": "diggersby", "rating": 593, "opRating": 406}, {"opponent": "furret", "rating": 586, "opRating": 413}, {"opponent": "cradily", "rating": 548}], "counters": [{"opponent": "skeledirge", "rating": 194}, {"opponent": "gligar", "rating": 316}, {"opponent": "jumpluff_shadow", "rating": 349}, {"opponent": "claydol", "rating": 355}, {"opponent": "clodsire", "rating": 358}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 36125}, {"moveId": "SCRATCH", "uses": 22175}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 20294}, {"moveId": "ROCK_SLIDE", "uses": 12874}, {"moveId": "BRICK_BREAK", "uses": 12500}, {"moveId": "BULLDOZE", "uses": 7369}, {"moveId": "RETURN", "uses": 5339}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 76.8}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 742, "matchups": [{"opponent": "talonflame", "rating": 800}, {"opponent": "magcargo", "rating": 754, "opRating": 245}, {"opponent": "furret", "rating": 703, "opRating": 296}, {"opponent": "cradily", "rating": 694}, {"opponent": "drampa", "rating": 625, "opRating": 375}], "counters": [{"opponent": "claydol", "rating": 252}, {"opponent": "gligar", "rating": 339}, {"opponent": "diggersby", "rating": 387}, {"opponent": "clodsire", "rating": 411}, {"opponent": "jumpluff_shadow", "rating": 418}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33562}, {"moveId": "FIRE_SPIN", "uses": 24738}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19294}, {"moveId": "SCORCHING_SANDS", "uses": 12472}, {"moveId": "BRICK_BREAK", "uses": 11192}, {"moveId": "PSYCHIC", "uses": 6140}, {"moveId": "THUNDERBOLT", "uses": 6035}, {"moveId": "FIRE_BLAST", "uses": 3237}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 76.7}, {"speciesId": "oinkologne_female", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Female)", "rating": 687, "matchups": [{"opponent": "clodsire", "rating": 658, "opRating": 341}, {"opponent": "swampert_shadow", "rating": 583, "opRating": 416}, {"opponent": "flygon", "rating": 574, "opRating": 425}, {"opponent": "diggersby", "rating": 529, "opRating": 470}, {"opponent": "claydol", "rating": 526, "opRating": 473}], "counters": [{"opponent": "skeledirge", "rating": 230}, {"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "magcargo", "rating": 324}, {"opponent": "talonflame", "rating": 411}, {"opponent": "cradily", "rating": 437}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 46389}, {"moveId": "TAKE_DOWN", "uses": 11911}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28506}, {"moveId": "TRAILBLAZE", "uses": 18389}, {"moveId": "DIG", "uses": 11414}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 76.4}, {"speciesId": "dunsparce", "speciesName": "Dunsparce", "rating": 652, "matchups": [{"opponent": "magcargo", "rating": 866, "opRating": 133}, {"opponent": "talonflame", "rating": 850}, {"opponent": "typhlosion_shadow", "rating": 809, "opRating": 190}, {"opponent": "clodsire", "rating": 633, "opRating": 366}, {"opponent": "jumpluff_shadow", "rating": 630, "opRating": 369}], "counters": [{"opponent": "flygon", "rating": 312}, {"opponent": "diggersby", "rating": 330}, {"opponent": "cradily", "rating": 350}, {"opponent": "swampert_shadow", "rating": 371}, {"opponent": "gligar", "rating": 423}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 27128}, {"moveId": "ASTONISH", "uses": 19946}, {"moveId": "BITE", "uses": 11231}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27808}, {"moveId": "ROCK_SLIDE", "uses": 23486}, {"moveId": "DIG", "uses": 6989}]}, "moveset": ["ROLLOUT", "DRILL_RUN", "ROCK_SLIDE"], "score": 75.9}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 747, "matchups": [{"opponent": "jumpluff_shadow", "rating": 725, "opRating": 274}, {"opponent": "gligar", "rating": 654}, {"opponent": "swampert_shadow", "rating": 637, "opRating": 362}, {"opponent": "claydol", "rating": 591, "opRating": 408}, {"opponent": "furret", "rating": 570, "opRating": 429}], "counters": [{"opponent": "magcargo", "rating": 209}, {"opponent": "piloswine", "rating": 246}, {"opponent": "cradily", "rating": 295}, {"opponent": "talonflame", "rating": 429}, {"opponent": "clodsire", "rating": 451}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 18157}, {"moveId": "WING_ATTACK", "uses": 16588}, {"moveId": "AIR_SLASH", "uses": 12605}, {"moveId": "STEEL_WING", "uses": 10885}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 21010}, {"moveId": "AIR_CUTTER", "uses": 17980}, {"moveId": "AERIAL_ACE", "uses": 6684}, {"moveId": "RETURN", "uses": 6094}, {"moveId": "HURRICANE", "uses": 3696}, {"moveId": "FEATHER_DANCE", "uses": 2548}]}, "moveset": ["GUST", "AIR_CUTTER", "BRAVE_BIRD"], "score": 75.6}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 764, "matchups": [{"opponent": "jumpluff_shadow", "rating": 893, "opRating": 106}, {"opponent": "swampert_shadow", "rating": 862, "opRating": 137}, {"opponent": "quagsire_shadow", "rating": 862, "opRating": 137}, {"opponent": "claydol", "rating": 660, "opRating": 339}, {"opponent": "diggersby", "rating": 568, "opRating": 431}], "counters": [{"opponent": "skeledirge", "rating": 273}, {"opponent": "talonflame", "rating": 277}, {"opponent": "gligar", "rating": 312}, {"opponent": "cradily", "rating": 361}, {"opponent": "magcargo", "rating": 363}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 29896}, {"moveId": "MAGICAL_LEAF", "uses": 19894}, {"moveId": "RAZOR_LEAF", "uses": 8529}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28266}, {"moveId": "SLUDGE_BOMB", "uses": 13708}, {"moveId": "LEAF_TORNADO", "uses": 5391}, {"moveId": "RETURN", "uses": 5317}, {"moveId": "ACID_SPRAY", "uses": 3096}, {"moveId": "SOLAR_BEAM", "uses": 2388}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 75.6}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 723, "matchups": [{"opponent": "drampa", "rating": 738, "opRating": 261}, {"opponent": "skeledirge", "rating": 722, "opRating": 277}, {"opponent": "swampert_shadow", "rating": 585, "opRating": 414}, {"opponent": "furret", "rating": 582, "opRating": 417}, {"opponent": "diggersby", "rating": 527, "opRating": 472}], "counters": [{"opponent": "talonflame", "rating": 266}, {"opponent": "clodsire", "rating": 295}, {"opponent": "jumpluff_shadow", "rating": 408}, {"opponent": "cradily", "rating": 437}, {"opponent": "gligar", "rating": 442}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 30527}, {"moveId": "TACKLE", "uses": 22045}, {"moveId": "TAKE_DOWN", "uses": 5695}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26458}, {"moveId": "WILD_CHARGE", "uses": 18556}, {"moveId": "PAYBACK", "uses": 13291}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "PAYBACK"], "score": 75.5}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 744, "matchups": [{"opponent": "piloswine", "rating": 876, "opRating": 124}, {"opponent": "talonflame", "rating": 756}, {"opponent": "skeledirge", "rating": 756, "opRating": 244}, {"opponent": "jumpluff_shadow", "rating": 712, "opRating": 288}, {"opponent": "furret", "rating": 536, "opRating": 464}], "counters": [{"opponent": "magcargo", "rating": 252}, {"opponent": "swampert_shadow", "rating": 268}, {"opponent": "diggersby", "rating": 307}, {"opponent": "clodsire", "rating": 362}, {"opponent": "cradily", "rating": 368}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 20948}, {"moveId": "SNARL", "uses": 18791}, {"moveId": "THUNDER_FANG", "uses": 11857}, {"moveId": "BITE", "uses": 6606}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 11574}, {"moveId": "WILD_CHARGE", "uses": 10913}, {"moveId": "FLAMETHROWER", "uses": 10092}, {"moveId": "CRUNCH", "uses": 9434}, {"moveId": "SCORCHING_SANDS", "uses": 8808}, {"moveId": "BULLDOZE", "uses": 4714}, {"moveId": "FIRE_BLAST", "uses": 2707}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 75.3}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 703, "matchups": [{"opponent": "swampert_shadow", "rating": 820, "opRating": 179}, {"opponent": "magcargo", "rating": 785, "opRating": 214}, {"opponent": "clodsire", "rating": 730, "opRating": 269}, {"opponent": "diggersby", "rating": 640, "opRating": 359}, {"opponent": "furret", "rating": 527, "opRating": 472}], "counters": [{"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "talonflame", "rating": 400}, {"opponent": "gligar", "rating": 461}, {"opponent": "cradily", "rating": 493}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31340}, {"moveId": "ASTONISH", "uses": 26960}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 22231}, {"moveId": "SHADOW_PUNCH", "uses": 20835}, {"moveId": "EARTH_POWER", "uses": 11629}, {"moveId": "POLTERGEIST", "uses": 3668}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "DYNAMIC_PUNCH"], "score": 75.1}, {"speciesId": "magcargo", "speciesName": "Magcargo", "rating": 789, "matchups": [{"opponent": "talonflame", "rating": 918}, {"opponent": "jumpluff_shadow", "rating": 829}, {"opponent": "gligar", "rating": 824}, {"opponent": "furret", "rating": 641}, {"opponent": "cradily", "rating": 551}], "counters": [{"opponent": "quagsire_shadow", "rating": 114}, {"opponent": "swampert_shadow", "rating": 180}, {"opponent": "clodsire", "rating": 271}, {"opponent": "claydol", "rating": 425}, {"opponent": "diggersby", "rating": 485}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27165}, {"moveId": "EMBER", "uses": 18453}, {"moveId": "ROCK_THROW", "uses": 12695}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 28977}, {"moveId": "OVERHEAT", "uses": 16552}, {"moveId": "STONE_EDGE", "uses": 9935}, {"moveId": "HEAT_WAVE", "uses": 2970}]}, "moveset": ["INCINERATE", "ROCK_TOMB", "OVERHEAT"], "score": 74.9}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 700, "matchups": [{"opponent": "quagsire_shadow", "rating": 683, "opRating": 316}, {"opponent": "clodsire", "rating": 658, "opRating": 341}, {"opponent": "swampert_shadow", "rating": 591, "opRating": 408}, {"opponent": "flygon", "rating": 577, "opRating": 422}, {"opponent": "diggersby", "rating": 552, "opRating": 447}], "counters": [{"opponent": "furret", "rating": 293}, {"opponent": "claydol", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "talonflame", "rating": 374}, {"opponent": "cradily", "rating": 493}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 46986}, {"moveId": "ZEN_HEADBUTT", "uses": 10406}, {"moveId": "YAWN", "uses": 868}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 19453}, {"moveId": "TRAILBLAZE", "uses": 13884}, {"moveId": "FUTURE_SIGHT", "uses": 9240}, {"moveId": "PSYCHIC", "uses": 8951}, {"moveId": "FOUL_PLAY", "uses": 6723}]}, "moveset": ["CONFUSION", "BRUTAL_SWING", "TRAILBLAZE"], "score": 74.6}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Zygarde (50% Forme)", "rating": 648, "matchups": [{"opponent": "cradily", "rating": 642}, {"opponent": "magcargo", "rating": 631, "opRating": 368}, {"opponent": "clodsire", "rating": 620, "opRating": 379}, {"opponent": "diggersby", "rating": 616, "opRating": 383}, {"opponent": "furret", "rating": 526, "opRating": 473}], "counters": [{"opponent": "piloswine", "rating": 275}, {"opponent": "skeledirge", "rating": 320}, {"opponent": "talonflame", "rating": 329}, {"opponent": "jumpluff_shadow", "rating": 398}, {"opponent": "gligar", "rating": 446}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 37821}, {"moveId": "BITE", "uses": 15179}, {"moveId": "ZEN_HEADBUTT", "uses": 5255}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 15938}, {"moveId": "OUTRAGE", "uses": 15832}, {"moveId": "EARTHQUAKE", "uses": 10757}, {"moveId": "BULLDOZE", "uses": 10097}, {"moveId": "HYPER_BEAM", "uses": 5794}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "EARTHQUAKE"], "score": 74.5}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 687, "matchups": [{"opponent": "magcargo", "rating": 838, "opRating": 161}, {"opponent": "skeledirge", "rating": 816, "opRating": 183}, {"opponent": "talonflame", "rating": 757}, {"opponent": "drampa", "rating": 672, "opRating": 327}, {"opponent": "clodsire", "rating": 644, "opRating": 355}], "counters": [{"opponent": "swampert_shadow", "rating": 257}, {"opponent": "cradily", "rating": 329}, {"opponent": "gligar", "rating": 343}, {"opponent": "claydol", "rating": 355}, {"opponent": "furret", "rating": 381}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 15906}, {"moveId": "SAND_ATTACK", "uses": 15772}, {"moveId": "FIRE_FANG", "uses": 11785}, {"moveId": "THUNDER_FANG", "uses": 8964}, {"moveId": "BITE", "uses": 5882}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 18181}, {"moveId": "SCORCHING_SANDS", "uses": 13802}, {"moveId": "BODY_SLAM", "uses": 12073}, {"moveId": "EARTH_POWER", "uses": 5380}, {"moveId": "STONE_EDGE", "uses": 4943}, {"moveId": "EARTHQUAKE", "uses": 4023}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 74.4}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 684, "matchups": [{"opponent": "magcargo", "rating": 855, "opRating": 144}, {"opponent": "talonflame", "rating": 845}, {"opponent": "gligar", "rating": 604, "opRating": 395}, {"opponent": "swampert_shadow", "rating": 520, "opRating": 479}, {"opponent": "diggersby", "rating": 516, "opRating": 483}], "counters": [{"opponent": "cradily", "rating": 309}, {"opponent": "claydol", "rating": 359}, {"opponent": "clodsire", "rating": 367}, {"opponent": "furret", "rating": 431}, {"opponent": "jumpluff_shadow", "rating": 441}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 31202}, {"moveId": "WATER_GUN", "uses": 22476}, {"moveId": "TAKE_DOWN", "uses": 4650}], "chargedMoves": [{"moveId": "SURF", "uses": 34425}, {"moveId": "HYPER_FANG", "uses": 18745}, {"moveId": "HYPER_BEAM", "uses": 5060}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 73.8}, {"speciesId": "garcho<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 641, "matchups": [{"opponent": "magcargo", "rating": 789, "opRating": 210}, {"opponent": "skeledirge", "rating": 773, "opRating": 226}, {"opponent": "talonflame", "rating": 757}, {"opponent": "ninetales_shadow", "rating": 710, "opRating": 289}, {"opponent": "quagsire_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 310}, {"opponent": "furret", "rating": 365}, {"opponent": "diggersby", "rating": 376}, {"opponent": "gligar", "rating": 385}, {"opponent": "cradily", "rating": 409}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31030}, {"moveId": "MUD_SHOT", "uses": 27270}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 19542}, {"moveId": "EARTH_POWER", "uses": 16743}, {"moveId": "FIRE_BLAST", "uses": 8847}, {"moveId": "SAND_TOMB", "uses": 6937}, {"moveId": "EARTHQUAKE", "uses": 6076}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 73.7}, {"speciesId": "hippopotas", "speciesName": "Hippopotas", "rating": 652, "matchups": [{"opponent": "magcargo", "rating": 805, "opRating": 194}, {"opponent": "talonflame", "rating": 792}, {"opponent": "clodsire", "rating": 644}, {"opponent": "cradily", "rating": 634}, {"opponent": "jumpluff_shadow", "rating": 552}], "counters": [{"opponent": "swampert_shadow", "rating": 158}, {"opponent": "claydol", "rating": 305}, {"opponent": "furret", "rating": 393}, {"opponent": "diggersby", "rating": 456}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 27422}, {"moveId": "TACKLE", "uses": 17358}, {"moveId": "BITE", "uses": 13473}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21529}, {"moveId": "BODY_SLAM", "uses": 17551}, {"moveId": "DIG", "uses": 14925}, {"moveId": "RETURN", "uses": 4279}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 73.7}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 657, "matchups": [{"opponent": "swampert_shadow", "rating": 884, "opRating": 115}, {"opponent": "clodsire", "rating": 635}, {"opponent": "claydol", "rating": 604, "opRating": 395}, {"opponent": "magcargo", "rating": 593, "opRating": 406}, {"opponent": "gligar", "rating": 514}], "counters": [{"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "pidgeot", "rating": 313}, {"opponent": "talonflame", "rating": 362}, {"opponent": "cradily", "rating": 371}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 16692}, {"moveId": "MUD_SHOT", "uses": 16505}, {"moveId": "TACKLE", "uses": 15570}, {"moveId": "BITE", "uses": 9543}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25007}, {"moveId": "CRUNCH", "uses": 16685}, {"moveId": "TRAILBLAZE", "uses": 16619}]}, "moveset": ["MUD_SHOT", "BODY_SLAM", "TRAILBLAZE"], "score": 73.5}, {"speciesId": "staravia", "speciesName": "Staravia", "rating": 764, "matchups": [{"opponent": "talonflame", "rating": 685}, {"opponent": "gligar", "rating": 651, "opRating": 348}, {"opponent": "jumpluff_shadow", "rating": 644, "opRating": 355}, {"opponent": "swampert_shadow", "rating": 551, "opRating": 448}, {"opponent": "furret", "rating": 533, "opRating": 466}], "counters": [{"opponent": "piloswine", "rating": 198}, {"opponent": "ninetales_shadow", "rating": 202}, {"opponent": "cradily", "rating": 270}, {"opponent": "clodsire", "rating": 312}, {"opponent": "magcargo", "rating": 333}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 21873}, {"moveId": "SAND_ATTACK", "uses": 18406}, {"moveId": "WING_ATTACK", "uses": 18063}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20094}, {"moveId": "FLY", "uses": 17114}, {"moveId": "AERIAL_ACE", "uses": 13043}, {"moveId": "RETURN", "uses": 5929}, {"moveId": "HEAT_WAVE", "uses": 2077}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 73.5}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 750, "matchups": [{"opponent": "piloswine", "rating": 903, "opRating": 96}, {"opponent": "magcargo", "rating": 850, "opRating": 149}, {"opponent": "ninetales_shadow", "rating": 820, "opRating": 179}, {"opponent": "drampa", "rating": 697, "opRating": 302}, {"opponent": "flygon", "rating": 539, "opRating": 460}], "counters": [{"opponent": "claydol", "rating": 247}, {"opponent": "gligar", "rating": 255}, {"opponent": "clodsire", "rating": 314}, {"opponent": "cradily", "rating": 375}, {"opponent": "talonflame", "rating": 403}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 31333}, {"moveId": "EMBER", "uses": 26967}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23022}, {"moveId": "SCORCHING_SANDS", "uses": 16688}, {"moveId": "RETURN", "uses": 7394}, {"moveId": "FLAMETHROWER", "uses": 7184}, {"moveId": "FIRE_BLAST", "uses": 3906}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 73.4}, {"speciesId": "serperior_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 698, "matchups": [{"opponent": "swampert_shadow", "rating": 903, "opRating": 96}, {"opponent": "claydol", "rating": 857, "opRating": 142}, {"opponent": "diggersby", "rating": 646, "opRating": 353}, {"opponent": "furret", "rating": 534, "opRating": 465}, {"opponent": "clodsire", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "pidgeot", "rating": 264}, {"opponent": "cradily", "rating": 354}, {"opponent": "talonflame", "rating": 392}, {"opponent": "gligar", "rating": 442}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45715}, {"moveId": "IRON_TAIL", "uses": 12585}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 22848}, {"moveId": "AERIAL_ACE", "uses": 17529}, {"moveId": "LEAF_TORNADO", "uses": 11817}, {"moveId": "GRASS_KNOT", "uses": 6188}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 73.4}, {"speciesId": "swellow_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 707, "matchups": [{"opponent": "swampert_shadow", "rating": 871, "opRating": 128}, {"opponent": "clodsire", "rating": 797, "opRating": 202}, {"opponent": "cradily", "rating": 747}, {"opponent": "talonflame", "rating": 615}, {"opponent": "gligar", "rating": 566, "opRating": 433}], "counters": [{"opponent": "furret", "rating": 221}, {"opponent": "piloswine", "rating": 230}, {"opponent": "skeledirge", "rating": 262}, {"opponent": "drampa", "rating": 308}, {"opponent": "magcargo", "rating": 384}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 37032}, {"moveId": "STEEL_WING", "uses": 21268}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31414}, {"moveId": "AERIAL_ACE", "uses": 20191}, {"moveId": "SKY_ATTACK", "uses": 6836}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "AERIAL_ACE"], "score": 73.4}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 762, "matchups": [{"opponent": "swampert_shadow", "rating": 805, "opRating": 194}, {"opponent": "furret", "rating": 773, "opRating": 226}, {"opponent": "magcargo", "rating": 769, "opRating": 230}, {"opponent": "claydol", "rating": 646, "opRating": 353}, {"opponent": "talonflame", "rating": 579}], "counters": [{"opponent": "diggersby", "rating": 189}, {"opponent": "cradily", "rating": 260}, {"opponent": "jumpluff_shadow", "rating": 316}, {"opponent": "gligar", "rating": 332}, {"opponent": "clodsire", "rating": 391}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24184}, {"moveId": "COUNTER", "uses": 19623}, {"moveId": "METAL_CLAW", "uses": 14477}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20670}, {"moveId": "SWIFT", "uses": 19293}, {"moveId": "TRAILBLAZE", "uses": 11319}, {"moveId": "PLAY_ROUGH", "uses": 4393}, {"moveId": "HYPER_BEAM", "uses": 2607}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 73.4}, {"speciesId": "oinkologne", "speciesName": "Oinkologne", "rating": 666, "matchups": [{"opponent": "flygon", "rating": 669, "opRating": 330}, {"opponent": "clodsire", "rating": 625, "opRating": 374}, {"opponent": "drampa", "rating": 619, "opRating": 380}, {"opponent": "swampert_shadow", "rating": 553, "opRating": 446}, {"opponent": "furret", "rating": 506, "opRating": 493}], "counters": [{"opponent": "skeledirge", "rating": 237}, {"opponent": "magcargo", "rating": 290}, {"opponent": "cradily", "rating": 402}, {"opponent": "talonflame", "rating": 422}, {"opponent": "gligar", "rating": 450}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 46094}, {"moveId": "TAKE_DOWN", "uses": 12206}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28518}, {"moveId": "TRAILBLAZE", "uses": 18351}, {"moveId": "DIG", "uses": 11412}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 73.3}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 750, "matchups": [{"opponent": "jumpluff_shadow", "rating": 906, "opRating": 93}, {"opponent": "talonflame", "rating": 799}, {"opponent": "skeledirge", "rating": 785, "opRating": 214}, {"opponent": "furret", "rating": 727, "opRating": 272}, {"opponent": "drampa", "rating": 620, "opRating": 379}], "counters": [{"opponent": "quagsire_shadow", "rating": 133}, {"opponent": "magcargo", "rating": 162}, {"opponent": "diggersby", "rating": 350}, {"opponent": "clodsire", "rating": 375}, {"opponent": "cradily", "rating": 423}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 25800}, {"moveId": "EMBER", "uses": 15843}, {"moveId": "FIRE_SPIN", "uses": 14078}, {"moveId": "LOW_KICK", "uses": 2578}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 15246}, {"moveId": "FLAME_CHARGE", "uses": 14865}, {"moveId": "WILD_CHARGE", "uses": 13892}, {"moveId": "FIRE_BLAST", "uses": 6680}, {"moveId": "SCORCHING_SANDS", "uses": 5747}, {"moveId": "HEAT_WAVE", "uses": 2018}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "WILD_CHARGE"], "score": 73.1}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 716, "matchups": [{"opponent": "clodsire", "rating": 916}, {"opponent": "magcargo", "rating": 888, "opRating": 111}, {"opponent": "talonflame", "rating": 835}, {"opponent": "gligar", "rating": 608}, {"opponent": "swampert_shadow", "rating": 503}], "counters": [{"opponent": "dart<PERSON>", "rating": 35}, {"opponent": "furret", "rating": 312}, {"opponent": "cradily", "rating": 357}, {"opponent": "jumpluff_shadow", "rating": 398}, {"opponent": "flygon", "rating": 408}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30626}, {"moveId": "WATER_GUN", "uses": 27674}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 23954}, {"moveId": "MUD_BOMB", "uses": 12759}, {"moveId": "STONE_EDGE", "uses": 8926}, {"moveId": "SLUDGE_BOMB", "uses": 7786}, {"moveId": "EARTHQUAKE", "uses": 3040}, {"moveId": "ACID_SPRAY", "uses": 1704}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 73}, {"speciesId": "marowak", "speciesName": "Marowak", "rating": 676, "matchups": [{"opponent": "magcargo", "rating": 852, "opRating": 148}, {"opponent": "skeledirge", "rating": 840, "opRating": 160}, {"opponent": "talonflame", "rating": 788}, {"opponent": "clodsire", "rating": 656, "opRating": 344}, {"opponent": "cradily", "rating": 564}], "counters": [{"opponent": "abomasnow_shadow", "rating": 174}, {"opponent": "gligar", "rating": 240}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "gliscor", "rating": 262}, {"opponent": "furret", "rating": 393}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 44985}, {"moveId": "ROCK_SMASH", "uses": 13315}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 27192}, {"moveId": "ROCK_SLIDE", "uses": 14371}, {"moveId": "RETURN", "uses": 6933}, {"moveId": "DIG", "uses": 5305}, {"moveId": "EARTHQUAKE", "uses": 4547}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "ROCK_SLIDE"], "score": 72.7}, {"speciesId": "staravia_shadow", "speciesName": "Staravia (Shadow)", "rating": 733, "matchups": [{"opponent": "swampert_shadow", "rating": 885, "opRating": 114}, {"opponent": "clodsire", "rating": 781, "opRating": 218}, {"opponent": "talonflame", "rating": 625}, {"opponent": "jumpluff_shadow", "rating": 618, "opRating": 381}, {"opponent": "gligar", "rating": 614}], "counters": [{"opponent": "furret", "rating": 190}, {"opponent": "abomasnow_shadow", "rating": 223}, {"opponent": "flygon_shadow", "rating": 284}, {"opponent": "cradily", "rating": 326}, {"opponent": "magcargo", "rating": 371}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 22174}, {"moveId": "SAND_ATTACK", "uses": 18269}, {"moveId": "WING_ATTACK", "uses": 17889}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22395}, {"moveId": "FLY", "uses": 19057}, {"moveId": "AERIAL_ACE", "uses": 14573}, {"moveId": "HEAT_WAVE", "uses": 2269}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 72.6}, {"speciesId": "raticate_alolan", "speciesName": "Raticate (Alolan)", "rating": 714, "matchups": [{"opponent": "furret", "rating": 706}, {"opponent": "clodsire", "rating": 656, "opRating": 343}, {"opponent": "swampert_shadow", "rating": 600, "opRating": 400}, {"opponent": "claydol", "rating": 590, "opRating": 410}, {"opponent": "diggersby", "rating": 576, "opRating": 423}], "counters": [{"opponent": "magcargo", "rating": 260}, {"opponent": "gligar", "rating": 332}, {"opponent": "cradily", "rating": 361}, {"opponent": "talonflame", "rating": 374}, {"opponent": "jumpluff_shadow", "rating": 493}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 36008}, {"moveId": "BITE", "uses": 22292}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28577}, {"moveId": "HYPER_FANG", "uses": 18550}, {"moveId": "RETURN", "uses": 6391}, {"moveId": "HYPER_BEAM", "uses": 4821}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "RETURN"], "score": 72.4}, {"speciesId": "fletchinder", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 782, "matchups": [{"opponent": "jumpluff_shadow", "rating": 762, "opRating": 237}, {"opponent": "diggersby", "rating": 643, "opRating": 356}, {"opponent": "drampa", "rating": 594, "opRating": 405}, {"opponent": "gligar", "rating": 569, "opRating": 430}, {"opponent": "furret", "rating": 552, "opRating": 447}], "counters": [{"opponent": "swampert_shadow", "rating": 187}, {"opponent": "magcargo", "rating": 213}, {"opponent": "clodsire", "rating": 331}, {"opponent": "cradily", "rating": 343}, {"opponent": "talonflame", "rating": 355}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 29186}, {"moveId": "STEEL_WING", "uses": 15841}, {"moveId": "PECK", "uses": 13261}], "chargedMoves": [{"moveId": "FLY", "uses": 24188}, {"moveId": "AERIAL_ACE", "uses": 18479}, {"moveId": "FLAME_CHARGE", "uses": 13766}, {"moveId": "HEAT_WAVE", "uses": 2036}]}, "moveset": ["EMBER", "FLY", "AERIAL_ACE"], "score": 72.3}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 686, "matchups": [{"opponent": "magcargo", "rating": 733, "opRating": 266}, {"opponent": "diggersby", "rating": 681, "opRating": 318}, {"opponent": "cradily", "rating": 653}, {"opponent": "furret", "rating": 572, "opRating": 427}, {"opponent": "swampert_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "typhlosion_shadow", "rating": 260}, {"opponent": "talonflame", "rating": 266}, {"opponent": "skeledirge", "rating": 280}, {"opponent": "jumpluff_shadow", "rating": 415}, {"opponent": "gligar", "rating": 438}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47576}, {"moveId": "LOW_KICK", "uses": 6996}, {"moveId": "POUND", "uses": 3736}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 24105}, {"moveId": "FIRE_PUNCH", "uses": 15394}, {"moveId": "FOCUS_BLAST", "uses": 10843}, {"moveId": "HYPER_BEAM", "uses": 7933}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FOCUS_BLAST"], "score": 72.3}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 727, "matchups": [{"opponent": "piloswine", "rating": 898, "opRating": 101}, {"opponent": "talonflame", "rating": 828, "opRating": 171}, {"opponent": "typhlosion_shadow", "rating": 787, "opRating": 212}, {"opponent": "drampa", "rating": 555, "opRating": 444}, {"opponent": "skeledirge", "rating": 532, "opRating": 467}], "counters": [{"opponent": "claydol", "rating": 256}, {"opponent": "gligar", "rating": 263}, {"opponent": "diggersby", "rating": 327}, {"opponent": "clodsire", "rating": 358}, {"opponent": "jumpluff_shadow", "rating": 366}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33056}, {"moveId": "FIRE_SPIN", "uses": 25244}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19294}, {"moveId": "SCORCHING_SANDS", "uses": 12461}, {"moveId": "BRICK_BREAK", "uses": 11179}, {"moveId": "PSYCHIC", "uses": 6155}, {"moveId": "THUNDERBOLT", "uses": 6046}, {"moveId": "FIRE_BLAST", "uses": 3219}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 72.3}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 724, "matchups": [{"opponent": "swampert_shadow", "rating": 914, "opRating": 85}, {"opponent": "claydol", "rating": 808, "opRating": 191}, {"opponent": "flygon", "rating": 667, "opRating": 332}, {"opponent": "diggersby", "rating": 593, "opRating": 406}, {"opponent": "furret", "rating": 539, "opRating": 460}], "counters": [{"opponent": "talonflame", "rating": 288}, {"opponent": "gligar", "rating": 316}, {"opponent": "cradily", "rating": 333}, {"opponent": "magcargo", "rating": 354}, {"opponent": "clodsire", "rating": 367}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 20181}, {"moveId": "BULLET_SEED", "uses": 16175}, {"moveId": "MAGICAL_LEAF", "uses": 14761}, {"moveId": "RAZOR_LEAF", "uses": 7200}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42063}, {"moveId": "DAZZLING_GLEAM", "uses": 11797}, {"moveId": "PETAL_BLIZZARD", "uses": 4364}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 72}, {"speciesId": "obstagoon_shadow", "speciesName": "Obstagoon (Shadow)", "rating": 716, "matchups": [{"opponent": "magcargo", "rating": 748, "opRating": 251}, {"opponent": "furret", "rating": 678, "opRating": 321}, {"opponent": "diggersby", "rating": 675, "opRating": 324}, {"opponent": "flygon", "rating": 664, "opRating": 335}, {"opponent": "cradily", "rating": 580}], "counters": [{"opponent": "jumpluff_shadow", "rating": 267}, {"opponent": "swampert_shadow", "rating": 297}, {"opponent": "gligar", "rating": 312}, {"opponent": "talonflame", "rating": 344}, {"opponent": "clodsire", "rating": 391}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 35238}, {"moveId": "LICK", "uses": 23062}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25244}, {"moveId": "CROSS_CHOP", "uses": 21530}, {"moveId": "HYPER_BEAM", "uses": 6169}, {"moveId": "GUNK_SHOT", "uses": 4744}, {"moveId": "OBSTRUCT", "uses": 633}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "NIGHT_SLASH"], "score": 71.9}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 647, "matchups": [{"opponent": "magcargo", "rating": 780, "opRating": 220}, {"opponent": "talonflame", "rating": 764}, {"opponent": "clodsire", "rating": 592, "opRating": 408}, {"opponent": "cradily", "rating": 580}, {"opponent": "jumpluff_shadow", "rating": 516, "opRating": 484}], "counters": [{"opponent": "abomasnow_shadow", "rating": 136}, {"opponent": "swampert_shadow", "rating": 235}, {"opponent": "diggersby", "rating": 347}, {"opponent": "claydol", "rating": 359}, {"opponent": "furret", "rating": 390}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 22660}, {"moveId": "MUD_SHOT", "uses": 20260}, {"moveId": "METAL_CLAW", "uses": 15332}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 15314}, {"moveId": "ROCK_TOMB", "uses": 14853}, {"moveId": "SCORCHING_SANDS", "uses": 12822}, {"moveId": "BULLDOZE", "uses": 6852}, {"moveId": "RETURN", "uses": 4925}, {"moveId": "EARTHQUAKE", "uses": 3706}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "SCORCHING_SANDS"], "score": 71.9}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 676, "matchups": [{"opponent": "drampa", "rating": 711, "opRating": 288}, {"opponent": "flygon", "rating": 676, "opRating": 323}, {"opponent": "swampert_shadow", "rating": 605, "opRating": 394}, {"opponent": "diggersby", "rating": 564, "opRating": 435}, {"opponent": "furret", "rating": 518, "opRating": 481}], "counters": [{"opponent": "skeledirge", "rating": 255}, {"opponent": "cradily", "rating": 277}, {"opponent": "magcargo", "rating": 299}, {"opponent": "talonflame", "rating": 403}, {"opponent": "jumpluff_shadow", "rating": 408}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 33460}, {"moveId": "LICK", "uses": 24840}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35745}, {"moveId": "BULLDOZE", "uses": 13390}, {"moveId": "GUNK_SHOT", "uses": 9159}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 71.6}, {"speciesId": "raticate_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Al<PERSON><PERSON>) (Shadow)", "rating": 706, "matchups": [{"opponent": "flygon", "rating": 676, "opRating": 323}, {"opponent": "furret", "rating": 670, "opRating": 330}, {"opponent": "clodsire", "rating": 636, "opRating": 363}, {"opponent": "diggersby", "rating": 583, "opRating": 416}, {"opponent": "swampert_shadow", "rating": 546, "opRating": 453}], "counters": [{"opponent": "magcargo", "rating": 329}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "gligar", "rating": 374}, {"opponent": "cradily", "rating": 406}, {"opponent": "talonflame", "rating": 459}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 36823}, {"moveId": "BITE", "uses": 21477}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31721}, {"moveId": "HYPER_FANG", "uses": 20884}, {"moveId": "HYPER_BEAM", "uses": 5419}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_BEAM"], "score": 71.5}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 661, "matchups": [{"opponent": "clodsire", "rating": 830, "opRating": 169}, {"opponent": "magcargo", "rating": 822, "opRating": 177}, {"opponent": "cradily", "rating": 636}, {"opponent": "gligar", "rating": 632}, {"opponent": "talonflame", "rating": 545}], "counters": [{"opponent": "abomasnow_shadow", "rating": 199}, {"opponent": "furret", "rating": 284}, {"opponent": "gliscor", "rating": 310}, {"opponent": "diggersby", "rating": 445}, {"opponent": "jumpluff_shadow", "rating": 464}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23542}, {"moveId": "CONFUSION", "uses": 19241}, {"moveId": "EXTRASENSORY", "uses": 15513}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 12693}, {"moveId": "ICE_BEAM", "uses": 11607}, {"moveId": "SCORCHING_SANDS", "uses": 11443}, {"moveId": "PSYCHIC", "uses": 6085}, {"moveId": "SHADOW_BALL", "uses": 5629}, {"moveId": "EARTH_POWER", "uses": 4479}, {"moveId": "EARTHQUAKE", "uses": 3280}, {"moveId": "GYRO_BALL", "uses": 3068}]}, "moveset": ["MUD_SLAP", "ROCK_TOMB", "ICE_BEAM"], "score": 71.2}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 687, "matchups": [{"opponent": "magcargo", "rating": 791, "opRating": 208}, {"opponent": "furret", "rating": 733, "opRating": 266}, {"opponent": "diggersby", "rating": 649, "opRating": 350}, {"opponent": "cradily", "rating": 572}, {"opponent": "swampert_shadow", "rating": 543, "opRating": 456}], "counters": [{"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "gligar", "rating": 293}, {"opponent": "gliscor", "rating": 306}, {"opponent": "talonflame", "rating": 314}, {"opponent": "clodsire", "rating": 353}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 34711}, {"moveId": "LICK", "uses": 23589}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25218}, {"moveId": "CROSS_CHOP", "uses": 21598}, {"moveId": "HYPER_BEAM", "uses": 6192}, {"moveId": "GUNK_SHOT", "uses": 4749}, {"moveId": "OBSTRUCT", "uses": 647}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "CROSS_CHOP"], "score": 71.2}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 742, "matchups": [{"opponent": "jumpluff_shadow", "rating": 916, "opRating": 83}, {"opponent": "gliscor", "rating": 837, "opRating": 162}, {"opponent": "skeledirge", "rating": 777, "opRating": 222}, {"opponent": "swampert_shadow", "rating": 678, "opRating": 321}, {"opponent": "flygon", "rating": 662, "opRating": 337}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "magcargo", "rating": 252}, {"opponent": "clodsire", "rating": 377}, {"opponent": "talonflame", "rating": 414}, {"opponent": "cradily", "rating": 447}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 26669}, {"moveId": "EMBER", "uses": 16304}, {"moveId": "FIRE_FANG", "uses": 12682}, {"moveId": "TAKE_DOWN", "uses": 2611}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 19477}, {"moveId": "DARK_PULSE", "uses": 15397}, {"moveId": "OVERHEAT", "uses": 15016}, {"moveId": "SOLAR_BEAM", "uses": 8500}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "DARK_PULSE"], "score": 71.2}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 792, "matchups": [{"opponent": "gligar", "rating": 904, "opRating": 95}, {"opponent": "jumpluff_shadow", "rating": 769, "opRating": 230}, {"opponent": "furret", "rating": 650, "opRating": 349}, {"opponent": "drampa", "rating": 630, "opRating": 369}, {"opponent": "diggersby", "rating": 523, "opRating": 476}], "counters": [{"opponent": "magcargo", "rating": 222}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 288}, {"opponent": "clodsire", "rating": 288}, {"opponent": "swampert_shadow", "rating": 294}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 24221}, {"moveId": "FIRE_SPIN", "uses": 21654}, {"moveId": "FEINT_ATTACK", "uses": 12470}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 16632}, {"moveId": "PSYSHOCK", "uses": 11660}, {"moveId": "SCORCHING_SANDS", "uses": 8876}, {"moveId": "OVERHEAT", "uses": 6679}, {"moveId": "SOLAR_BEAM", "uses": 4045}, {"moveId": "RETURN", "uses": 3649}, {"moveId": "FLAMETHROWER", "uses": 3628}, {"moveId": "FIRE_BLAST", "uses": 1921}, {"moveId": "HEAT_WAVE", "uses": 1131}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 71.1}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 724, "matchups": [{"opponent": "swampert_shadow", "rating": 887, "opRating": 112}, {"opponent": "claydol", "rating": 860, "opRating": 139}, {"opponent": "furret", "rating": 725, "opRating": 274}, {"opponent": "flygon", "rating": 725, "opRating": 274}, {"opponent": "cradily", "rating": 617}], "counters": [{"opponent": "typhlosion_shadow", "rating": 123}, {"opponent": "skeledirge", "rating": 280}, {"opponent": "jumpluff_shadow", "rating": 330}, {"opponent": "clodsire", "rating": 341}, {"opponent": "talonflame", "rating": 366}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31923}, {"moveId": "BULLET_SEED", "uses": 26377}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 19603}, {"moveId": "BREAKING_SWIPE", "uses": 12284}, {"moveId": "AERIAL_ACE", "uses": 9849}, {"moveId": "FRENZY_PLANT", "uses": 7280}, {"moveId": "DRAGON_CLAW", "uses": 4861}, {"moveId": "EARTHQUAKE", "uses": 4456}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 70.9}, {"speciesId": "centiskorch", "speciesName": "Centiskorch", "rating": 717, "matchups": [{"opponent": "skeledirge", "rating": 716, "opRating": 283}, {"opponent": "drampa", "rating": 606, "opRating": 393}, {"opponent": "furret", "rating": 536, "opRating": 463}, {"opponent": "diggersby", "rating": 522, "opRating": 477}, {"opponent": "jumpluff_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "ninetales_shadow", "rating": 162}, {"opponent": "magcargo", "rating": 213}, {"opponent": "clodsire", "rating": 293}, {"opponent": "talonflame", "rating": 374}, {"opponent": "cradily", "rating": 423}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30480}, {"moveId": "BUG_BITE", "uses": 27820}], "chargedMoves": [{"moveId": "LUNGE", "uses": 20691}, {"moveId": "CRUNCH", "uses": 18748}, {"moveId": "BUG_BUZZ", "uses": 13430}, {"moveId": "HEAT_WAVE", "uses": 5407}]}, "moveset": ["EMBER", "LUNGE", "CRUNCH"], "score": 70.7}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 666, "matchups": [{"opponent": "magcargo", "rating": 722, "opRating": 277}, {"opponent": "clodsire", "rating": 591, "opRating": 408}, {"opponent": "diggersby", "rating": 558, "opRating": 441}, {"opponent": "swampert_shadow", "rating": 543, "opRating": 456}, {"opponent": "cradily", "rating": 510}], "counters": [{"opponent": "jumpluff_shadow", "rating": 264}, {"opponent": "flygon", "rating": 336}, {"opponent": "gligar", "rating": 339}, {"opponent": "furret", "rating": 368}, {"opponent": "talonflame", "rating": 429}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 47846}, {"moveId": "ZEN_HEADBUTT", "uses": 10454}], "chargedMoves": [{"moveId": "STOMP", "uses": 20909}, {"moveId": "MEGAHORN", "uses": 18444}, {"moveId": "EARTHQUAKE", "uses": 11816}, {"moveId": "SKULL_BASH", "uses": 7261}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 70.5}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 700, "matchups": [{"opponent": "jumpluff_shadow", "rating": 887, "opRating": 112}, {"opponent": "gligar", "rating": 851, "opRating": 148}, {"opponent": "clodsire", "rating": 833, "opRating": 166}, {"opponent": "cradily", "rating": 778}, {"opponent": "talonflame", "rating": 750}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "flygon_shadow", "rating": 196}, {"opponent": "claydol", "rating": 210}, {"opponent": "magcargo", "rating": 320}, {"opponent": "swampert_shadow", "rating": 360}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32402}, {"moveId": "FIRE_FANG", "uses": 15813}, {"moveId": "TACKLE", "uses": 10064}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17727}, {"moveId": "ROCK_SLIDE", "uses": 17397}, {"moveId": "FOCUS_BLAST", "uses": 13302}, {"moveId": "PSYCHIC", "uses": 9865}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 70.5}, {"speciesId": "rhyperior_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 666, "matchups": [{"opponent": "cradily", "rating": 906}, {"opponent": "clodsire", "rating": 906}, {"opponent": "talonflame", "rating": 787}, {"opponent": "furret", "rating": 574}, {"opponent": "jumpluff_shadow", "rating": 563}], "counters": [{"opponent": "dart<PERSON>", "rating": 68}, {"opponent": "abomasnow_shadow", "rating": 213}, {"opponent": "gligar", "rating": 244}, {"opponent": "claydol", "rating": 342}, {"opponent": "diggersby", "rating": 422}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 37867}, {"moveId": "SMACK_DOWN", "uses": 20433}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 14967}, {"moveId": "SUPER_POWER", "uses": 10975}, {"moveId": "BREAKING_SWIPE", "uses": 10499}, {"moveId": "SURF", "uses": 9348}, {"moveId": "EARTHQUAKE", "uses": 4816}, {"moveId": "STONE_EDGE", "uses": 4247}, {"moveId": "SKULL_BASH", "uses": 3417}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 70.5}, {"speciesId": "swellow", "speciesName": "Swellow", "rating": 732, "matchups": [{"opponent": "talonflame", "rating": 681, "opRating": 318}, {"opponent": "gligar", "rating": 603, "opRating": 396}, {"opponent": "flygon", "rating": 566, "opRating": 433}, {"opponent": "swampert_shadow", "rating": 520, "opRating": 479}, {"opponent": "furret", "rating": 516, "opRating": 483}], "counters": [{"opponent": "ninetales_shadow", "rating": 202}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "clodsire", "rating": 264}, {"opponent": "diggersby", "rating": 295}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 36405}, {"moveId": "STEEL_WING", "uses": 21895}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27121}, {"moveId": "AERIAL_ACE", "uses": 17522}, {"moveId": "RETURN", "uses": 7847}, {"moveId": "SKY_ATTACK", "uses": 5974}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "AERIAL_ACE"], "score": 70.5}, {"speciesId": "don<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 676, "matchups": [{"opponent": "clodsire", "rating": 900, "opRating": 99}, {"opponent": "swampert_shadow", "rating": 817, "opRating": 182}, {"opponent": "magcargo", "rating": 773, "opRating": 226}, {"opponent": "diggersby", "rating": 646, "opRating": 353}, {"opponent": "furret", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "gligar", "rating": 278}, {"opponent": "quagsire_shadow", "rating": 282}, {"opponent": "talonflame", "rating": 403}, {"opponent": "cradily", "rating": 461}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23420}, {"moveId": "COUNTER", "uses": 15831}, {"moveId": "TACKLE", "uses": 10558}, {"moveId": "CHARM", "uses": 8447}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16664}, {"moveId": "TRAILBLAZE", "uses": 15183}, {"moveId": "EARTHQUAKE", "uses": 11783}, {"moveId": "HEAVY_SLAM", "uses": 8155}, {"moveId": "PLAY_ROUGH", "uses": 6523}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "TRAILBLAZE", "BODY_SLAM"], "score": 70.2}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 666, "matchups": [{"opponent": "magcargo", "rating": 885, "opRating": 114}, {"opponent": "talonflame", "rating": 875}, {"opponent": "clodsire", "rating": 644, "opRating": 355}, {"opponent": "diggersby", "rating": 604, "opRating": 395}, {"opponent": "swampert_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "cradily", "rating": 260}, {"opponent": "claydol", "rating": 318}, {"opponent": "jumpluff_shadow", "rating": 379}, {"opponent": "furret", "rating": 381}, {"opponent": "gligar", "rating": 461}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 30181}, {"moveId": "WATER_GUN", "uses": 23282}, {"moveId": "TAKE_DOWN", "uses": 4857}], "chargedMoves": [{"moveId": "SURF", "uses": 31645}, {"moveId": "HYPER_FANG", "uses": 16619}, {"moveId": "RETURN", "uses": 5744}, {"moveId": "HYPER_BEAM", "uses": 4331}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 70.1}, {"speciesId": "hippopotas_shadow", "speciesName": "Hip<PERSON><PERSON><PERSON> (Shadow)", "rating": 659, "matchups": [{"opponent": "magcargo", "rating": 773, "opRating": 226}, {"opponent": "talonflame", "rating": 746}, {"opponent": "skeledirge", "rating": 733, "opRating": 266}, {"opponent": "clodsire", "rating": 595, "opRating": 404}, {"opponent": "cradily", "rating": 569}], "counters": [{"opponent": "abomasnow_shadow", "rating": 136}, {"opponent": "swampert_shadow", "rating": 235}, {"opponent": "gligar", "rating": 324}, {"opponent": "diggersby", "rating": 336}, {"opponent": "jumpluff_shadow", "rating": 392}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 28470}, {"moveId": "TACKLE", "uses": 17070}, {"moveId": "BITE", "uses": 12712}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 23066}, {"moveId": "BODY_SLAM", "uses": 19176}, {"moveId": "DIG", "uses": 15993}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 70.1}, {"speciesId": "vigoroth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 696, "matchups": [{"opponent": "furret", "rating": 744, "opRating": 255}, {"opponent": "magcargo", "rating": 689, "opRating": 310}, {"opponent": "flygon", "rating": 658, "opRating": 341}, {"opponent": "talonflame", "rating": 651, "opRating": 348}, {"opponent": "diggersby", "rating": 651, "opRating": 348}], "counters": [{"opponent": "skeledirge", "rating": 205}, {"opponent": "clodsire", "rating": 257}, {"opponent": "jumpluff_shadow", "rating": 271}, {"opponent": "swampert_shadow", "rating": 297}, {"opponent": "gligar", "rating": 347}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37255}, {"moveId": "SCRATCH", "uses": 21045}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 22585}, {"moveId": "ROCK_SLIDE", "uses": 13982}, {"moveId": "BRICK_BREAK", "uses": 13703}, {"moveId": "BULLDOZE", "uses": 7970}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 70}, {"speciesId": "magby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 713, "matchups": [{"opponent": "piloswine", "rating": 879, "opRating": 120}, {"opponent": "magcargo", "rating": 745, "opRating": 254}, {"opponent": "cradily", "rating": 725, "opRating": 275}, {"opponent": "typhlosion_shadow", "rating": 712, "opRating": 287}, {"opponent": "drampa", "rating": 683, "opRating": 316}], "counters": [{"opponent": "claydol", "rating": 243}, {"opponent": "gligar", "rating": 259}, {"opponent": "clodsire", "rating": 353}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 30906}, {"moveId": "EMBER", "uses": 27394}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26463}, {"moveId": "BRICK_BREAK", "uses": 18460}, {"moveId": "FLAMETHROWER", "uses": 8282}, {"moveId": "FLAME_BURST", "uses": 5025}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "BRICK_BREAK"], "score": 69.6}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 624, "matchups": [{"opponent": "skeledirge", "rating": 805, "opRating": 194}, {"opponent": "magcargo", "rating": 607, "opRating": 392}, {"opponent": "clodsire", "rating": 555, "opRating": 444}, {"opponent": "cradily", "rating": 515}, {"opponent": "diggersby", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "gligar", "rating": 324}, {"opponent": "claydol", "rating": 334}, {"opponent": "furret", "rating": 443}, {"opponent": "talonflame", "rating": 488}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31384}, {"moveId": "MUD_SHOT", "uses": 26916}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 19550}, {"moveId": "EARTH_POWER", "uses": 16734}, {"moveId": "FIRE_BLAST", "uses": 8856}, {"moveId": "SAND_TOMB", "uses": 6982}, {"moveId": "EARTHQUAKE", "uses": 6096}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 69.3}, {"speciesId": "castform", "speciesName": "Castform", "rating": 652, "matchups": [{"opponent": "swampert_shadow", "rating": 873, "opRating": 126}, {"opponent": "quagsire_shadow", "rating": 873, "opRating": 126}, {"opponent": "piloswine", "rating": 657, "opRating": 342}, {"opponent": "typhlosion_shadow", "rating": 636, "opRating": 363}, {"opponent": "gligar", "rating": 602, "opRating": 397}], "counters": [{"opponent": "drampa", "rating": 243}, {"opponent": "furret", "rating": 262}, {"opponent": "diggersby", "rating": 336}, {"opponent": "cradily", "rating": 378}, {"opponent": "talonflame", "rating": 492}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 33644}, {"moveId": "TACKLE", "uses": 24656}], "chargedMoves": [{"moveId": "WEATHER_BALL_NORMAL", "uses": 20791}, {"moveId": "WEATHER_BALL_ROCK", "uses": 20461}, {"moveId": "ENERGY_BALL", "uses": 9341}, {"moveId": "HURRICANE", "uses": 7664}]}, "moveset": ["HEX", "WEATHER_BALL_ROCK", "ENERGY_BALL"], "score": 69.1}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 719, "matchups": [{"opponent": "swampert_shadow", "rating": 696, "opRating": 303}, {"opponent": "drampa", "rating": 675, "opRating": 324}, {"opponent": "jumpluff_shadow", "rating": 642, "opRating": 357}, {"opponent": "flygon", "rating": 577, "opRating": 422}, {"opponent": "gligar", "rating": 538, "opRating": 461}], "counters": [{"opponent": "magcargo", "rating": 205}, {"opponent": "clodsire", "rating": 326}, {"opponent": "diggersby", "rating": 327}, {"opponent": "talonflame", "rating": 340}, {"opponent": "cradily", "rating": 399}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34356}, {"moveId": "EXTRASENSORY", "uses": 23944}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 24297}, {"moveId": "NIGHT_SHADE", "uses": 15315}, {"moveId": "PSYCHIC", "uses": 11669}, {"moveId": "SHADOW_BALL", "uses": 6927}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "NIGHT_SHADE"], "score": 69.1}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 693, "matchups": [{"opponent": "talonflame", "rating": 860}, {"opponent": "clodsire", "rating": 664}, {"opponent": "gligar", "rating": 627}, {"opponent": "diggersby", "rating": 618}, {"opponent": "swampert_shadow", "rating": 555}], "counters": [{"opponent": "bellossom", "rating": 74}, {"opponent": "lura<PERSON>s", "rating": 79}, {"opponent": "cradily", "rating": 302}, {"opponent": "jumpluff_shadow", "rating": 343}, {"opponent": "furret", "rating": 356}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29835}, {"moveId": "WATER_GUN", "uses": 28465}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 22568}, {"moveId": "MUD_BOMB", "uses": 12003}, {"moveId": "STONE_EDGE", "uses": 8345}, {"moveId": "SLUDGE_BOMB", "uses": 7167}, {"moveId": "RETURN", "uses": 3835}, {"moveId": "EARTHQUAKE", "uses": 2908}, {"moveId": "ACID_SPRAY", "uses": 1585}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 69.1}, {"speciesId": "vibrava_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 639, "matchups": [{"opponent": "magcargo", "rating": 775, "opRating": 224}, {"opponent": "talonflame", "rating": 759}, {"opponent": "skeledirge", "rating": 759, "opRating": 240}, {"opponent": "typhlosion_shadow", "rating": 759, "opRating": 240}, {"opponent": "ninetales_shadow", "rating": 736, "opRating": 263}], "counters": [{"opponent": "piloswine", "rating": 230}, {"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "diggersby", "rating": 316}, {"opponent": "furret", "rating": 346}, {"opponent": "cradily", "rating": 375}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21793}, {"moveId": "SAND_ATTACK", "uses": 18885}, {"moveId": "MUD_SHOT", "uses": 17583}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22514}, {"moveId": "BUG_BUZZ", "uses": 16425}, {"moveId": "BULLDOZE", "uses": 11908}, {"moveId": "SAND_TOMB", "uses": 7393}, {"moveId": "FRUSTRATION", "uses": 42}]}, "moveset": ["DRAGON_BREATH", "SAND_TOMB", "BUG_BUZZ"], "score": 69}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 625, "matchups": [{"opponent": "skeledirge", "rating": 761, "opRating": 238}, {"opponent": "magcargo", "rating": 720, "opRating": 279}, {"opponent": "clodsire", "rating": 610, "opRating": 389}, {"opponent": "swampert_shadow", "rating": 540, "opRating": 459}, {"opponent": "claydol", "rating": 536, "opRating": 463}], "counters": [{"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "gligar", "rating": 328}, {"opponent": "cradily", "rating": 347}, {"opponent": "talonflame", "rating": 422}, {"opponent": "furret", "rating": 443}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 25674}, {"moveId": "BULLET_SEED", "uses": 25500}, {"moveId": "TAKE_DOWN", "uses": 7098}], "chargedMoves": [{"moveId": "SWIFT", "uses": 32200}, {"moveId": "CRUNCH", "uses": 18597}, {"moveId": "PLAY_ROUGH", "uses": 7546}]}, "moveset": ["MUD_SHOT", "SWIFT", "CRUNCH"], "score": 68.9}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 684, "matchups": [{"opponent": "swampert_shadow", "rating": 903, "opRating": 96}, {"opponent": "claydol", "rating": 861, "opRating": 138}, {"opponent": "flygon", "rating": 688, "opRating": 311}, {"opponent": "diggersby", "rating": 661, "opRating": 338}, {"opponent": "clodsire", "rating": 565, "opRating": 434}], "counters": [{"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "skeledirge", "rating": 309}, {"opponent": "magcargo", "rating": 333}, {"opponent": "talonflame", "rating": 348}, {"opponent": "gligar", "rating": 381}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45484}, {"moveId": "IRON_TAIL", "uses": 12816}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 20905}, {"moveId": "AERIAL_ACE", "uses": 15390}, {"moveId": "LEAF_TORNADO", "uses": 10809}, {"moveId": "RETURN", "uses": 5665}, {"moveId": "GRASS_KNOT", "uses": 5501}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 68.7}, {"speciesId": "combusken", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "talonflame", "rating": 812, "opRating": 187}, {"opponent": "skeledirge", "rating": 767, "opRating": 232}, {"opponent": "ninetales_shadow", "rating": 645, "opRating": 354}, {"opponent": "furret", "rating": 622, "opRating": 377}, {"opponent": "gligar", "rating": 614, "opRating": 385}], "counters": [{"opponent": "swampert_shadow", "rating": 216}, {"opponent": "magcargo", "rating": 329}, {"opponent": "clodsire", "rating": 358}, {"opponent": "jumpluff_shadow", "rating": 359}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43253}, {"moveId": "PECK", "uses": 15047}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21730}, {"moveId": "ROCK_SLIDE", "uses": 18235}, {"moveId": "RETURN", "uses": 9241}, {"moveId": "FLAMETHROWER", "uses": 9036}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 68.5}, {"speciesId": "litleo", "speciesName": "Litleo", "rating": 765, "matchups": [{"opponent": "gliscor", "rating": 857, "opRating": 142}, {"opponent": "jumpluff_shadow", "rating": 760, "opRating": 239}, {"opponent": "skeledirge", "rating": 687, "opRating": 312}, {"opponent": "furret", "rating": 604, "opRating": 395}, {"opponent": "gligar", "rating": 524, "opRating": 475}], "counters": [{"opponent": "quagsire_shadow", "rating": 124}, {"opponent": "magcargo", "rating": 205}, {"opponent": "swampert_shadow", "rating": 220}, {"opponent": "cradily", "rating": 350}, {"opponent": "talonflame", "rating": 351}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 21277}, {"moveId": "EMBER", "uses": 14570}, {"moveId": "FIRE_FANG", "uses": 12607}, {"moveId": "TACKLE", "uses": 9884}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24301}, {"moveId": "FLAME_CHARGE", "uses": 24009}, {"moveId": "FLAMETHROWER", "uses": 9980}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "CRUNCH"], "score": 68.5}, {"speciesId": "lickitung", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 638, "matchups": [{"opponent": "quagsire_shadow", "rating": 747, "opRating": 252}, {"opponent": "swampert_shadow", "rating": 672, "opRating": 327}, {"opponent": "gligar", "rating": 604, "opRating": 395}, {"opponent": "claydol", "rating": 580, "opRating": 419}, {"opponent": "flygon", "rating": 532, "opRating": 467}], "counters": [{"opponent": "furret", "rating": 287}, {"opponent": "cradily", "rating": 326}, {"opponent": "magcargo", "rating": 350}, {"opponent": "talonflame", "rating": 370}, {"opponent": "clodsire", "rating": 396}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 44816}, {"moveId": "ZEN_HEADBUTT", "uses": 13484}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 19546}, {"moveId": "WRAP", "uses": 15995}, {"moveId": "POWER_WHIP", "uses": 12629}, {"moveId": "STOMP", "uses": 6951}, {"moveId": "HYPER_BEAM", "uses": 3125}]}, "moveset": ["LICK", "BODY_SLAM", "POWER_WHIP"], "score": 68.3}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 667, "matchups": [{"opponent": "clodsire", "rating": 824, "opRating": 175}, {"opponent": "cradily", "rating": 767}, {"opponent": "talonflame", "rating": 754}, {"opponent": "gligar", "rating": 697, "opRating": 302}, {"opponent": "furret", "rating": 504, "opRating": 495}], "counters": [{"opponent": "quagsire_shadow", "rating": 201}, {"opponent": "flygon", "rating": 228}, {"opponent": "swampert_shadow", "rating": 238}, {"opponent": "magcargo", "rating": 329}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 15334}, {"moveId": "EXTRASENSORY", "uses": 4010}, {"moveId": "STEEL_WING", "uses": 3561}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3161}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3099}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2989}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2575}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2490}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2407}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2281}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2231}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2025}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1964}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1949}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1845}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1715}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1686}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1586}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1446}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27137}, {"moveId": "SACRED_FIRE", "uses": 14606}, {"moveId": "EARTHQUAKE", "uses": 7692}, {"moveId": "SOLAR_BEAM", "uses": 5848}, {"moveId": "FIRE_BLAST", "uses": 2956}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 68}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 705, "matchups": [{"opponent": "quagsire_shadow", "rating": 941, "opRating": 58}, {"opponent": "swampert_shadow", "rating": 914, "opRating": 85}, {"opponent": "claydol", "rating": 839, "opRating": 160}, {"opponent": "flygon", "rating": 722, "opRating": 277}, {"opponent": "furret", "rating": 613, "opRating": 386}], "counters": [{"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "skeledirge", "rating": 262}, {"opponent": "gligar", "rating": 263}, {"opponent": "talonflame", "rating": 325}, {"opponent": "cradily", "rating": 430}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 20163}, {"moveId": "BULLET_SEED", "uses": 15958}, {"moveId": "MAGICAL_LEAF", "uses": 14733}, {"moveId": "RAZOR_LEAF", "uses": 7394}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36604}, {"moveId": "DAZZLING_GLEAM", "uses": 9671}, {"moveId": "RETURN", "uses": 8213}, {"moveId": "PETAL_BLIZZARD", "uses": 3719}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 67.8}, {"speciesId": "braixen", "speciesName": "Braixen", "rating": 712, "matchups": [{"opponent": "piloswine", "rating": 873, "opRating": 126}, {"opponent": "jumpluff_shadow", "rating": 723, "opRating": 276}, {"opponent": "gliscor", "rating": 626, "opRating": 373}, {"opponent": "drampa", "rating": 605, "opRating": 394}, {"opponent": "furret", "rating": 512, "opRating": 487}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "magcargo", "rating": 239}, {"opponent": "diggersby", "rating": 316}, {"opponent": "talonflame", "rating": 377}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43959}, {"moveId": "SCRATCH", "uses": 14341}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 28587}, {"moveId": "FLAME_CHARGE", "uses": 20920}, {"moveId": "FLAMETHROWER", "uses": 8778}]}, "moveset": ["EMBER", "PSYSHOCK", "FLAME_CHARGE"], "score": 67.5}, {"speciesId": "aipom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 637, "matchups": [{"opponent": "swampert_shadow", "rating": 796, "opRating": 203}, {"opponent": "talonflame", "rating": 670, "opRating": 329}, {"opponent": "flygon", "rating": 662, "opRating": 337}, {"opponent": "skeledirge", "rating": 651, "opRating": 348}, {"opponent": "quagsire_shadow", "rating": 570, "opRating": 429}], "counters": [{"opponent": "diggersby", "rating": 238}, {"opponent": "furret", "rating": 318}, {"opponent": "magcargo", "rating": 341}, {"opponent": "cradily", "rating": 347}, {"opponent": "clodsire", "rating": 348}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35428}, {"moveId": "SCRATCH", "uses": 22872}], "chargedMoves": [{"moveId": "SWIFT", "uses": 29336}, {"moveId": "AERIAL_ACE", "uses": 18509}, {"moveId": "LOW_SWEEP", "uses": 10413}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "SWIFT", "AERIAL_ACE"], "score": 67.4}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 743, "matchups": [{"opponent": "swampert_shadow", "rating": 907, "opRating": 92}, {"opponent": "claydol", "rating": 844, "opRating": 155}, {"opponent": "furret", "rating": 768, "opRating": 231}, {"opponent": "cradily", "rating": 693, "opRating": 306}, {"opponent": "diggersby", "rating": 579, "opRating": 420}], "counters": [{"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "skeledirge", "rating": 230}, {"opponent": "magcargo", "rating": 256}, {"opponent": "talonflame", "rating": 262}, {"opponent": "gligar", "rating": 339}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28596}, {"moveId": "LEAFAGE", "uses": 21104}, {"moveId": "RAZOR_LEAF", "uses": 8618}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 23149}, {"moveId": "SUPER_POWER", "uses": 14715}, {"moveId": "X_SCISSOR", "uses": 11727}, {"moveId": "TRAILBLAZE", "uses": 5747}, {"moveId": "LEAF_STORM", "uses": 3001}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 67.4}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 756, "matchups": [{"opponent": "diggersby", "rating": 829, "opRating": 170}, {"opponent": "swampert_shadow", "rating": 788, "opRating": 211}, {"opponent": "furret", "rating": 747, "opRating": 252}, {"opponent": "cradily", "rating": 726}, {"opponent": "jumpluff_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "typhlosion_shadow", "rating": 111}, {"opponent": "skeledirge", "rating": 183}, {"opponent": "talonflame", "rating": 229}, {"opponent": "ninetales_shadow", "rating": 230}, {"opponent": "gligar", "rating": 461}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 15555}, {"moveId": "POISON_JAB", "uses": 14220}, {"moveId": "BULLET_SEED", "uses": 11866}, {"moveId": "MAGICAL_LEAF", "uses": 11024}, {"moveId": "RAZOR_LEAF", "uses": 5628}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17099}, {"moveId": "GRASS_KNOT", "uses": 12677}, {"moveId": "SLUDGE_BOMB", "uses": 11913}, {"moveId": "LEAF_STORM", "uses": 7670}, {"moveId": "DAZZLING_GLEAM", "uses": 6305}, {"moveId": "SOLAR_BEAM", "uses": 2656}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 67.4}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 702, "matchups": [{"opponent": "flygon", "rating": 697, "opRating": 302}, {"opponent": "drampa", "rating": 644, "opRating": 355}, {"opponent": "cradily", "rating": 548, "opRating": 451}, {"opponent": "swampert_shadow", "rating": 545, "opRating": 454}, {"opponent": "claydol", "rating": 505, "opRating": 494}], "counters": [{"opponent": "diggersby", "rating": 209}, {"opponent": "furret", "rating": 243}, {"opponent": "jumpluff_shadow", "rating": 333}, {"opponent": "gligar", "rating": 374}, {"opponent": "talonflame", "rating": 451}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 46245}, {"moveId": "ZEN_HEADBUTT", "uses": 10028}, {"moveId": "YAWN", "uses": 2081}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16264}, {"moveId": "BODY_SLAM", "uses": 15981}, {"moveId": "OUTRAGE", "uses": 7575}, {"moveId": "EARTHQUAKE", "uses": 6156}, {"moveId": "HEAVY_SLAM", "uses": 5717}, {"moveId": "SKULL_BASH", "uses": 3913}, {"moveId": "HYPER_BEAM", "uses": 2609}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 67.2}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 632, "matchups": [{"opponent": "claydol", "rating": 748, "opRating": 251}, {"opponent": "swampert_shadow", "rating": 610, "opRating": 389}, {"opponent": "clodsire", "rating": 593, "opRating": 406}, {"opponent": "cradily", "rating": 531}, {"opponent": "gligar", "rating": 528, "opRating": 471}], "counters": [{"opponent": "piloswine", "rating": 253}, {"opponent": "jumpluff_shadow", "rating": 316}, {"opponent": "diggersby", "rating": 393}, {"opponent": "furret", "rating": 403}, {"opponent": "talonflame", "rating": 470}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33907}, {"moveId": "FEINT_ATTACK", "uses": 24393}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20658}, {"moveId": "FOUL_PLAY", "uses": 18836}, {"moveId": "SLUDGE_BOMB", "uses": 18748}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 66.9}, {"speciesId": "vibrava", "speciesName": "Vibrava", "rating": 623, "matchups": [{"opponent": "skeledirge", "rating": 779, "opRating": 220}, {"opponent": "magcargo", "rating": 759, "opRating": 240}, {"opponent": "claydol", "rating": 562, "opRating": 437}, {"opponent": "clodsire", "rating": 515, "opRating": 484}, {"opponent": "cradily", "rating": 511}], "counters": [{"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 312}, {"opponent": "diggersby", "rating": 382}, {"opponent": "talonflame", "rating": 481}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21575}, {"moveId": "SAND_ATTACK", "uses": 19141}, {"moveId": "MUD_SHOT", "uses": 17546}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 19362}, {"moveId": "BUG_BUZZ", "uses": 13354}, {"moveId": "BULLDOZE", "uses": 10260}, {"moveId": "RETURN", "uses": 9224}, {"moveId": "SAND_TOMB", "uses": 6385}]}, "moveset": ["DRAGON_BREATH", "SAND_TOMB", "BUG_BUZZ"], "score": 66.5}, {"speciesId": "whiscash_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 683, "matchups": [{"opponent": "magcargo", "rating": 896, "opRating": 103}, {"opponent": "clodsire", "rating": 879, "opRating": 120}, {"opponent": "talonflame", "rating": 839}, {"opponent": "diggersby", "rating": 584, "opRating": 415}, {"opponent": "swampert_shadow", "rating": 539, "opRating": 460}], "counters": [{"opponent": "farfetchd", "rating": 72}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "cradily", "rating": 298}, {"opponent": "furret", "rating": 321}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30599}, {"moveId": "WATER_GUN", "uses": 27701}], "chargedMoves": [{"moveId": "SCALD", "uses": 21698}, {"moveId": "MUD_BOMB", "uses": 17829}, {"moveId": "BLIZZARD", "uses": 12994}, {"moveId": "WATER_PULSE", "uses": 5693}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 66.5}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 702, "matchups": [{"opponent": "talonflame", "rating": 776, "opRating": 223}, {"opponent": "gligar", "rating": 732, "opRating": 267}, {"opponent": "jumpluff_shadow", "rating": 728, "opRating": 271}, {"opponent": "diggersby", "rating": 714, "opRating": 285}, {"opponent": "furret", "rating": 583, "opRating": 416}], "counters": [{"opponent": "flygon", "rating": 180}, {"opponent": "quagsire_shadow", "rating": 204}, {"opponent": "swampert_shadow", "rating": 209}, {"opponent": "magcargo", "rating": 273}, {"opponent": "cradily", "rating": 475}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 13495}, {"moveId": "EXTRASENSORY", "uses": 3979}, {"moveId": "STEEL_WING", "uses": 3667}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3272}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3254}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3044}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2760}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2533}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2490}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2327}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2309}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2154}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2105}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2083}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1974}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1819}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1811}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1707}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1531}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27184}, {"moveId": "SACRED_FIRE", "uses": 14638}, {"moveId": "EARTHQUAKE", "uses": 7676}, {"moveId": "SOLAR_BEAM", "uses": 5858}, {"moveId": "FIRE_BLAST", "uses": 2953}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 66.2}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 649, "matchups": [{"opponent": "typhlosion_shadow", "rating": 688, "opRating": 311}, {"opponent": "talonflame", "rating": 681}, {"opponent": "skeledirge", "rating": 661, "opRating": 338}, {"opponent": "drampa", "rating": 582, "opRating": 417}, {"opponent": "gligar", "rating": 523, "opRating": 476}], "counters": [{"opponent": "ninetales_shadow", "rating": 170}, {"opponent": "cradily", "rating": 270}, {"opponent": "furret", "rating": 356}, {"opponent": "magcargo", "rating": 427}, {"opponent": "jumpluff_shadow", "rating": 434}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7971}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4329}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3834}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3316}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3312}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3309}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2997}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2993}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2991}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2844}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2813}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2808}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2775}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2596}, {"moveId": "CHARGE_BEAM", "uses": 2571}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2536}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2337}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2026}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25622}, {"moveId": "HYPER_BEAM", "uses": 12554}, {"moveId": "SOLAR_BEAM", "uses": 11156}, {"moveId": "ZAP_CANNON", "uses": 9013}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 66.2}, {"speciesId": "steelix_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 608, "matchups": [{"opponent": "jumpluff_shadow", "rating": 706, "opRating": 293}, {"opponent": "drampa", "rating": 686, "opRating": 313}, {"opponent": "talonflame", "rating": 678}, {"opponent": "furret", "rating": 599, "opRating": 400}, {"opponent": "cradily", "rating": 519}], "counters": [{"opponent": "claydol", "rating": 210}, {"opponent": "swampert_shadow", "rating": 330}, {"opponent": "diggersby", "rating": 364}, {"opponent": "magcargo", "rating": 371}, {"opponent": "clodsire", "rating": 406}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 27526}, {"moveId": "THUNDER_FANG", "uses": 17851}, {"moveId": "IRON_TAIL", "uses": 13015}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 17017}, {"moveId": "PSYCHIC_FANGS", "uses": 13433}, {"moveId": "CRUNCH", "uses": 10956}, {"moveId": "EARTHQUAKE", "uses": 8517}, {"moveId": "HEAVY_SLAM", "uses": 8297}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 66.2}, {"speciesId": "rufflet", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 736, "matchups": [{"opponent": "jumpluff_shadow", "rating": 684, "opRating": 315}, {"opponent": "gligar", "rating": 657, "opRating": 342}, {"opponent": "flygon", "rating": 651, "opRating": 348}, {"opponent": "swampert_shadow", "rating": 601, "opRating": 398}, {"opponent": "furret", "rating": 542, "opRating": 457}], "counters": [{"opponent": "piloswine", "rating": 165}, {"opponent": "magcargo", "rating": 217}, {"opponent": "quagsire_shadow", "rating": 226}, {"opponent": "clodsire", "rating": 307}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 39971}, {"moveId": "PECK", "uses": 18329}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18566}, {"moveId": "FLY", "uses": 15675}, {"moveId": "ROCK_TOMB", "uses": 12130}, {"moveId": "AERIAL_ACE", "uses": 11976}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "FLY"], "score": 66.1}, {"speciesId": "rhydon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 664, "matchups": [{"opponent": "cradily", "rating": 908}, {"opponent": "clodsire", "rating": 908}, {"opponent": "talonflame", "rating": 790}, {"opponent": "furret", "rating": 580}, {"opponent": "jumpluff_shadow", "rating": 566}], "counters": [{"opponent": "dart<PERSON>", "rating": 51}, {"opponent": "gligar", "rating": 125}, {"opponent": "gliscor_shadow", "rating": 176}, {"opponent": "abomasnow_shadow", "rating": 213}, {"opponent": "claydol", "rating": 342}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 47808}, {"moveId": "ROCK_SMASH", "uses": 10492}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 14715}, {"moveId": "SURF", "uses": 13316}, {"moveId": "STONE_EDGE", "uses": 12939}, {"moveId": "MEGAHORN", "uses": 10273}, {"moveId": "EARTHQUAKE", "uses": 7076}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "STONE_EDGE"], "score": 66}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 651, "matchups": [{"opponent": "clodsire", "rating": 884, "opRating": 115}, {"opponent": "magcargo", "rating": 805, "opRating": 194}, {"opponent": "flygon", "rating": 662, "opRating": 337}, {"opponent": "diggersby", "rating": 626, "opRating": 373}, {"opponent": "furret", "rating": 559}], "counters": [{"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "gligar", "rating": 297}, {"opponent": "gliscor", "rating": 318}, {"opponent": "cradily", "rating": 347}, {"opponent": "talonflame", "rating": 348}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22542}, {"moveId": "COUNTER", "uses": 15930}, {"moveId": "TACKLE", "uses": 11190}, {"moveId": "CHARM", "uses": 8653}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16684}, {"moveId": "TRAILBLAZE", "uses": 15169}, {"moveId": "EARTHQUAKE", "uses": 11755}, {"moveId": "HEAVY_SLAM", "uses": 8158}, {"moveId": "PLAY_ROUGH", "uses": 6525}]}, "moveset": ["MUD_SLAP", "TRAILBLAZE", "BODY_SLAM"], "score": 65.4}, {"speciesId": "go<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "swampert_shadow", "rating": 773, "opRating": 226}, {"opponent": "magcargo", "rating": 744, "opRating": 255}, {"opponent": "clodsire", "rating": 719, "opRating": 280}, {"opponent": "diggersby", "rating": 599, "opRating": 400}, {"opponent": "flygon", "rating": 549, "opRating": 450}], "counters": [{"opponent": "jumpluff_shadow", "rating": 225}, {"opponent": "gligar", "rating": 320}, {"opponent": "furret", "rating": 325}, {"opponent": "talonflame", "rating": 414}, {"opponent": "cradily", "rating": 458}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31239}, {"moveId": "ASTONISH", "uses": 27061}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 29241}, {"moveId": "BRICK_BREAK", "uses": 20429}, {"moveId": "NIGHT_SHADE", "uses": 8583}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "BRICK_BREAK"], "score": 65.4}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 615, "matchups": [{"opponent": "flygon", "rating": 629, "opRating": 370}, {"opponent": "quagsire_shadow", "rating": 612, "opRating": 387}, {"opponent": "gliscor", "rating": 604, "opRating": 395}, {"opponent": "piloswine", "rating": 595, "opRating": 404}, {"opponent": "gligar", "rating": 583, "opRating": 416}], "counters": [{"opponent": "skeledirge", "rating": 294}, {"opponent": "magcargo", "rating": 299}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "cradily", "rating": 343}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 47984}, {"moveId": "ZEN_HEADBUTT", "uses": 10316}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 19352}, {"moveId": "TRAILBLAZE", "uses": 18382}, {"moveId": "EARTHQUAKE", "uses": 10416}, {"moveId": "IRON_HEAD", "uses": 10183}]}, "moveset": ["TACKLE", "TRAILBLAZE", "HORN_ATTACK"], "score": 65.4}, {"speciesId": "sandshrew", "speciesName": "Sandshrew", "rating": 602, "matchups": [{"opponent": "magcargo", "rating": 767, "opRating": 232}, {"opponent": "talonflame", "rating": 751}, {"opponent": "skeledirge", "rating": 740, "opRating": 259}, {"opponent": "clodsire", "rating": 578, "opRating": 421}, {"opponent": "cradily", "rating": 566, "opRating": 433}], "counters": [{"opponent": "swampert_shadow", "rating": 235}, {"opponent": "claydol", "rating": 309}, {"opponent": "diggersby", "rating": 316}, {"opponent": "furret", "rating": 350}, {"opponent": "jumpluff_shadow", "rating": 352}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24957}, {"moveId": "MUD_SHOT", "uses": 22002}, {"moveId": "SCRATCH", "uses": 11315}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18852}, {"moveId": "DIG", "uses": 13427}, {"moveId": "ROCK_SLIDE", "uses": 12194}, {"moveId": "RETURN", "uses": 7117}, {"moveId": "SAND_TOMB", "uses": 6691}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 65.3}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 720, "matchups": [{"opponent": "quagsire_shadow", "rating": 964, "opRating": 35}, {"opponent": "swampert_shadow", "rating": 720, "opRating": 279}, {"opponent": "diggersby", "rating": 588, "opRating": 411}, {"opponent": "claydol", "rating": 573, "opRating": 426}, {"opponent": "furret", "rating": 532, "opRating": 467}], "counters": [{"opponent": "ninetales_shadow", "rating": 198}, {"opponent": "magcargo", "rating": 217}, {"opponent": "cradily", "rating": 309}, {"opponent": "gligar", "rating": 343}, {"opponent": "talonflame", "rating": 351}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37067}, {"moveId": "RAZOR_LEAF", "uses": 21233}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 23641}, {"moveId": "AERIAL_ACE", "uses": 14672}, {"moveId": "BRUTAL_SWING", "uses": 13399}, {"moveId": "STOMP", "uses": 6573}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 65.3}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 703, "matchups": [{"opponent": "talonflame", "rating": 823, "opRating": 176}, {"opponent": "skeledirge", "rating": 823, "opRating": 176}, {"opponent": "cradily", "rating": 676, "opRating": 323}, {"opponent": "jumpluff_shadow", "rating": 604, "opRating": 395}, {"opponent": "furret", "rating": 528, "opRating": 471}], "counters": [{"opponent": "swampert_shadow", "rating": 231}, {"opponent": "claydol", "rating": 252}, {"opponent": "clodsire", "rating": 290}, {"opponent": "gligar", "rating": 312}, {"opponent": "diggersby", "rating": 313}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 27060}, {"moveId": "POISON_JAB", "uses": 22845}, {"moveId": "BITE", "uses": 8337}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 15729}, {"moveId": "EARTH_POWER", "uses": 15557}, {"moveId": "STONE_EDGE", "uses": 12812}, {"moveId": "SLUDGE_WAVE", "uses": 8508}, {"moveId": "EARTHQUAKE", "uses": 5701}]}, "moveset": ["POISON_STING", "STONE_EDGE", "POISON_FANG"], "score": 65.1}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 755, "matchups": [{"opponent": "swampert_shadow", "rating": 915, "opRating": 84}, {"opponent": "furret", "rating": 716}, {"opponent": "talonflame", "rating": 660}, {"opponent": "jumpluff_shadow", "rating": 632, "opRating": 368}, {"opponent": "gligar", "rating": 548}], "counters": [{"opponent": "cradily", "rating": 114}, {"opponent": "abomasnow_shadow", "rating": 192}, {"opponent": "flygon_shadow", "rating": 244}, {"opponent": "clodsire", "rating": 324}, {"opponent": "claydol", "rating": 396}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 16941}, {"moveId": "SAND_ATTACK", "uses": 14119}, {"moveId": "GUST", "uses": 14075}, {"moveId": "WING_ATTACK", "uses": 13120}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 19968}, {"moveId": "CLOSE_COMBAT", "uses": 19497}, {"moveId": "FLY", "uses": 16993}, {"moveId": "HEAT_WAVE", "uses": 1923}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 65.1}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 664, "matchups": [{"opponent": "magcargo", "rating": 875, "opRating": 125}, {"opponent": "skeledirge", "rating": 823, "opRating": 176}, {"opponent": "talonflame", "rating": 819}, {"opponent": "flygon", "rating": 709, "opRating": 290}, {"opponent": "furret", "rating": 643, "opRating": 356}], "counters": [{"opponent": "abomasnow_shadow", "rating": 171}, {"opponent": "gligar", "rating": 221}, {"opponent": "claydol", "rating": 342}, {"opponent": "diggersby", "rating": 362}, {"opponent": "cradily", "rating": 430}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 47135}, {"moveId": "ROCK_SMASH", "uses": 11165}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 14771}, {"moveId": "SURF", "uses": 13303}, {"moveId": "STONE_EDGE", "uses": 12915}, {"moveId": "MEGAHORN", "uses": 10258}, {"moveId": "EARTHQUAKE", "uses": 7067}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "STONE_EDGE"], "score": 65}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 605, "matchups": [{"opponent": "drampa", "rating": 662, "opRating": 337}, {"opponent": "furret", "rating": 599, "opRating": 400}, {"opponent": "cradily", "rating": 583, "opRating": 416}, {"opponent": "flygon", "rating": 571, "opRating": 428}, {"opponent": "jumpluff_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "claydol", "rating": 214}, {"opponent": "swampert_shadow", "rating": 275}, {"opponent": "diggersby", "rating": 318}, {"opponent": "magcargo", "rating": 346}, {"opponent": "talonflame", "rating": 466}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 27154}, {"moveId": "THUNDER_FANG", "uses": 17995}, {"moveId": "IRON_TAIL", "uses": 13190}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 15694}, {"moveId": "PSYCHIC_FANGS", "uses": 12419}, {"moveId": "CRUNCH", "uses": 10159}, {"moveId": "EARTHQUAKE", "uses": 7888}, {"moveId": "HEAVY_SLAM", "uses": 7640}, {"moveId": "RETURN", "uses": 4553}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 64.9}, {"speciesId": "darmanitan_standard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Standard) (Shadow)", "rating": 661, "matchups": [{"opponent": "jumpluff_shadow", "rating": 851, "opRating": 148}, {"opponent": "clodsire", "rating": 833, "opRating": 166}, {"opponent": "gligar", "rating": 778, "opRating": 221}, {"opponent": "cradily", "rating": 742}, {"opponent": "talonflame", "rating": 706}], "counters": [{"opponent": "quagsire_shadow", "rating": 180}, {"opponent": "flygon", "rating": 196}, {"opponent": "claydol", "rating": 198}, {"opponent": "drampa", "rating": 226}, {"opponent": "magcargo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34125}, {"moveId": "FIRE_FANG", "uses": 14987}, {"moveId": "TACKLE", "uses": 9211}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17778}, {"moveId": "ROCK_SLIDE", "uses": 17339}, {"moveId": "FOCUS_BLAST", "uses": 13316}, {"moveId": "PSYCHIC", "uses": 9843}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 64.7}, {"speciesId": "ceruledge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 713, "matchups": [{"opponent": "jumpluff_shadow", "rating": 900, "opRating": 99}, {"opponent": "clodsire", "rating": 872, "opRating": 127}, {"opponent": "cradily", "rating": 811, "opRating": 188}, {"opponent": "talonflame", "rating": 735, "opRating": 264}, {"opponent": "furret", "rating": 570, "opRating": 429}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "claydol", "rating": 194}, {"opponent": "flygon", "rating": 212}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 232}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 35069}, {"moveId": "EMBER", "uses": 23231}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23945}, {"moveId": "SHADOW_BALL", "uses": 21143}, {"moveId": "FLAMETHROWER", "uses": 9909}, {"moveId": "HEAT_WAVE", "uses": 3201}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 64.6}, {"speciesId": "go<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 622, "matchups": [{"opponent": "swampert_shadow", "rating": 804, "opRating": 195}, {"opponent": "magcargo", "rating": 783, "opRating": 216}, {"opponent": "diggersby", "rating": 638, "opRating": 361}, {"opponent": "flygon", "rating": 624, "opRating": 375}, {"opponent": "clodsire", "rating": 567, "opRating": 432}], "counters": [{"opponent": "jumpluff_shadow", "rating": 202}, {"opponent": "talonflame", "rating": 355}, {"opponent": "cradily", "rating": 371}, {"opponent": "gligar", "rating": 431}, {"opponent": "furret", "rating": 431}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31397}, {"moveId": "ASTONISH", "uses": 26903}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 25738}, {"moveId": "BRICK_BREAK", "uses": 17218}, {"moveId": "RETURN", "uses": 7731}, {"moveId": "NIGHT_SHADE", "uses": 7626}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "BRICK_BREAK"], "score": 64.6}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 656, "matchups": [{"opponent": "flygon", "rating": 793, "opRating": 206}, {"opponent": "cradily", "rating": 773, "opRating": 226}, {"opponent": "magcargo", "rating": 677, "opRating": 322}, {"opponent": "clodsire", "rating": 645, "opRating": 354}, {"opponent": "jumpluff_shadow", "rating": 584, "opRating": 415}], "counters": [{"opponent": "swampert_shadow", "rating": 150}, {"opponent": "gliscor", "rating": 189}, {"opponent": "ninetales_shadow", "rating": 202}, {"opponent": "gligar", "rating": 301}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31090}, {"moveId": "METAL_CLAW", "uses": 27210}], "chargedMoves": [{"moveId": "MUDDY_WATER", "uses": 17448}, {"moveId": "ROCK_SLIDE", "uses": 17312}, {"moveId": "EARTHQUAKE", "uses": 13541}, {"moveId": "FLASH_CANNON", "uses": 10030}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 64.5}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 767, "matchups": [{"opponent": "jumpluff_shadow", "rating": 934, "opRating": 65}, {"opponent": "gligar", "rating": 907, "opRating": 92}, {"opponent": "cradily", "rating": 824, "opRating": 175}, {"opponent": "talonflame", "rating": 767, "opRating": 232}, {"opponent": "furret", "rating": 688, "opRating": 311}], "counters": [{"opponent": "claydol", "rating": 119}, {"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "swampert_shadow", "rating": 180}, {"opponent": "diggersby", "rating": 264}, {"opponent": "magcargo", "rating": 307}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36402}, {"moveId": "POISON_JAB", "uses": 21898}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22598}, {"moveId": "SLUDGE_WAVE", "uses": 12274}, {"moveId": "FIRE_BLAST", "uses": 11770}, {"moveId": "DRAGON_PULSE", "uses": 11632}]}, "moveset": ["INCINERATE", "POISON_FANG", "DRAGON_PULSE"], "score": 64.3}, {"speciesId": "ponyta", "speciesName": "Ponyta", "rating": 685, "matchups": [{"opponent": "piloswine", "rating": 866, "opRating": 133}, {"opponent": "typhlosion_shadow", "rating": 737, "opRating": 262}, {"opponent": "jumpluff_shadow", "rating": 706, "opRating": 293}, {"opponent": "drampa", "rating": 599, "opRating": 400}, {"opponent": "gliscor", "rating": 599, "opRating": 400}], "counters": [{"opponent": "magcargo", "rating": 192}, {"opponent": "swampert_shadow", "rating": 238}, {"opponent": "clodsire", "rating": 355}, {"opponent": "talonflame", "rating": 385}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38420}, {"moveId": "TACKLE", "uses": 19880}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 24008}, {"moveId": "STOMP", "uses": 19053}, {"moveId": "FIRE_BLAST", "uses": 10789}, {"moveId": "FLAME_WHEEL", "uses": 4580}]}, "moveset": ["EMBER", "FLAME_CHARGE", "STOMP"], "score": 64}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 659, "matchups": [{"opponent": "pidgeot", "rating": 679, "opRating": 320}, {"opponent": "typhlosion_shadow", "rating": 671, "opRating": 328}, {"opponent": "talonflame", "rating": 667}, {"opponent": "skeledirge", "rating": 649, "opRating": 350}, {"opponent": "claydol", "rating": 593, "opRating": 406}], "counters": [{"opponent": "diggersby", "rating": 189}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "cradily", "rating": 298}, {"opponent": "gligar", "rating": 358}, {"opponent": "clodsire", "rating": 375}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 36953}, {"moveId": "SCRATCH", "uses": 21347}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 32682}, {"moveId": "PLAY_ROUGH", "uses": 13357}, {"moveId": "THUNDER", "uses": 12164}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 64}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 658, "matchups": [{"opponent": "piloswine", "rating": 700, "opRating": 299}, {"opponent": "flygon_shadow", "rating": 697, "opRating": 302}, {"opponent": "drampa", "rating": 689, "opRating": 310}, {"opponent": "swampert_shadow", "rating": 606, "opRating": 393}, {"opponent": "cradily", "rating": 582, "opRating": 417}], "counters": [{"opponent": "furret", "rating": 228}, {"opponent": "diggersby", "rating": 284}, {"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "talonflame", "rating": 322}, {"opponent": "clodsire", "rating": 367}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 46009}, {"moveId": "ZEN_HEADBUTT", "uses": 10009}, {"moveId": "YAWN", "uses": 2342}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16284}, {"moveId": "BODY_SLAM", "uses": 16065}, {"moveId": "OUTRAGE", "uses": 7583}, {"moveId": "EARTHQUAKE", "uses": 6138}, {"moveId": "HEAVY_SLAM", "uses": 5730}, {"moveId": "SKULL_BASH", "uses": 3914}, {"moveId": "HYPER_BEAM", "uses": 2603}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 64}, {"speciesId": "gra<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 688, "matchups": [{"opponent": "magcargo", "rating": 773, "opRating": 226}, {"opponent": "skeledirge", "rating": 665, "opRating": 334}, {"opponent": "drampa", "rating": 613, "opRating": 386}, {"opponent": "flygon", "rating": 543, "opRating": 456}, {"opponent": "cradily", "rating": 508, "opRating": 491}], "counters": [{"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "gligar", "rating": 274}, {"opponent": "diggersby", "rating": 281}, {"opponent": "furret", "rating": 300}, {"opponent": "talonflame", "rating": 374}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 30856}, {"moveId": "MUD_SLAP", "uses": 27444}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 30531}, {"moveId": "SLUDGE_BOMB", "uses": 24943}, {"moveId": "ACID_SPRAY", "uses": 2918}]}, "moveset": ["MUD_SLAP", "POISON_FANG", "SLUDGE_BOMB"], "score": 63.9}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 656, "matchups": [{"opponent": "talonflame", "rating": 741, "opRating": 258}, {"opponent": "skeledirge", "rating": 741, "opRating": 258}, {"opponent": "claydol", "rating": 690, "opRating": 309}, {"opponent": "gligar", "rating": 644, "opRating": 355}, {"opponent": "diggersby", "rating": 550, "opRating": 449}], "counters": [{"opponent": "quagsire_shadow", "rating": 189}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "furret", "rating": 309}, {"opponent": "clodsire", "rating": 314}, {"opponent": "cradily", "rating": 416}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31320}, {"moveId": "WING_ATTACK", "uses": 26980}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18468}, {"moveId": "ANCIENT_POWER", "uses": 16548}, {"moveId": "OVERHEAT", "uses": 15904}, {"moveId": "FIRE_BLAST", "uses": 4684}, {"moveId": "HEAT_WAVE", "uses": 2689}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 63.9}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 692, "matchups": [{"opponent": "jumpluff_shadow", "rating": 880, "opRating": 119}, {"opponent": "clodsire", "rating": 876, "opRating": 123}, {"opponent": "gligar", "rating": 811, "opRating": 188}, {"opponent": "furret", "rating": 536, "opRating": 463}, {"opponent": "magcargo", "rating": 532, "opRating": 467}], "counters": [{"opponent": "typhlosion_shadow", "rating": 106}, {"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "skeledirge", "rating": 262}, {"opponent": "talonflame", "rating": 381}, {"opponent": "cradily", "rating": 472}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34059}, {"moveId": "MUD_SLAP", "uses": 24241}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19387}, {"moveId": "ICICLE_SPEAR", "uses": 16239}, {"moveId": "STONE_EDGE", "uses": 6192}, {"moveId": "HIGH_HORSEPOWER", "uses": 6094}, {"moveId": "ANCIENT_POWER", "uses": 5554}, {"moveId": "BULLDOZE", "uses": 4963}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 63.8}, {"speciesId": "pidgeott<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 697, "matchups": [{"opponent": "abomasnow_shadow", "rating": 697, "opRating": 302}, {"opponent": "typhlosion_shadow", "rating": 690, "opRating": 309}, {"opponent": "gliscor", "rating": 608, "opRating": 391}, {"opponent": "gligar", "rating": 602, "opRating": 397}, {"opponent": "flygon_shadow", "rating": 544, "opRating": 455}], "counters": [{"opponent": "magcargo", "rating": 222}, {"opponent": "clodsire", "rating": 262}, {"opponent": "cradily", "rating": 263}, {"opponent": "furret", "rating": 346}, {"opponent": "talonflame", "rating": 381}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 33887}, {"moveId": "STEEL_WING", "uses": 24413}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 36683}, {"moveId": "AERIAL_ACE", "uses": 13872}, {"moveId": "TWISTER", "uses": 7746}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WING_ATTACK", "AIR_CUTTER", "AERIAL_ACE"], "score": 63.8}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 740, "matchups": [{"opponent": "piloswine", "rating": 895, "opRating": 104}, {"opponent": "jumpluff_shadow", "rating": 766, "opRating": 233}, {"opponent": "flygon", "rating": 689, "opRating": 310}, {"opponent": "drampa", "rating": 641, "opRating": 358}, {"opponent": "furret", "rating": 614, "opRating": 385}], "counters": [{"opponent": "quagsire_shadow", "rating": 124}, {"opponent": "magcargo", "rating": 158}, {"opponent": "swampert_shadow", "rating": 194}, {"opponent": "talonflame", "rating": 281}, {"opponent": "cradily", "rating": 392}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 44307}, {"moveId": "BITE", "uses": 13993}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21905}, {"moveId": "FLAMETHROWER", "uses": 19736}, {"moveId": "DISARMING_VOICE", "uses": 16774}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 63.6}, {"speciesId": "marshtomp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "clodsire", "rating": 895, "opRating": 104}, {"opponent": "magcargo", "rating": 866, "opRating": 133}, {"opponent": "talonflame", "rating": 856}, {"opponent": "swampert_shadow", "rating": 834, "opRating": 165}, {"opponent": "flygon", "rating": 643, "opRating": 356}], "counters": [{"opponent": "jumpluff_shadow", "rating": 169}, {"opponent": "cradily", "rating": 319}, {"opponent": "furret", "rating": 378}, {"opponent": "gligar", "rating": 454}, {"opponent": "diggersby", "rating": 454}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30941}, {"moveId": "WATER_GUN", "uses": 27359}], "chargedMoves": [{"moveId": "SURF", "uses": 23087}, {"moveId": "SLUDGE", "uses": 17958}, {"moveId": "MUD_BOMB", "uses": 17212}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 63.6}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 606, "matchups": [{"opponent": "magcargo", "rating": 793, "opRating": 206}, {"opponent": "jumpluff_shadow", "rating": 622, "opRating": 377}, {"opponent": "talonflame", "rating": 601}, {"opponent": "clodsire", "rating": 598, "opRating": 401}, {"opponent": "drampa", "rating": 569, "opRating": 430}], "counters": [{"opponent": "claydol", "rating": 280}, {"opponent": "swampert_shadow", "rating": 297}, {"opponent": "diggersby", "rating": 344}, {"opponent": "gligar", "rating": 358}, {"opponent": "cradily", "rating": 475}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30897}, {"moveId": "MUD_SHOT", "uses": 27403}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 25820}, {"moveId": "MUDDY_WATER", "uses": 16786}, {"moveId": "DISCHARGE", "uses": 15683}]}, "moveset": ["THUNDER_SHOCK", "MUD_BOMB", "DISCHARGE"], "score": 63.6}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 669, "matchups": [{"opponent": "magcargo", "rating": 873, "opRating": 126}, {"opponent": "talonflame", "rating": 817}, {"opponent": "flygon", "rating": 705, "opRating": 294}, {"opponent": "jumpluff_shadow", "rating": 649, "opRating": 350}, {"opponent": "furret", "rating": 638, "opRating": 361}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "abomasnow_shadow", "rating": 171}, {"opponent": "claydol", "rating": 355}, {"opponent": "diggersby", "rating": 376}, {"opponent": "cradily", "rating": 451}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 37135}, {"moveId": "SMACK_DOWN", "uses": 21165}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 14972}, {"moveId": "SUPER_POWER", "uses": 10974}, {"moveId": "BREAKING_SWIPE", "uses": 10512}, {"moveId": "SURF", "uses": 9330}, {"moveId": "EARTHQUAKE", "uses": 4831}, {"moveId": "STONE_EDGE", "uses": 4241}, {"moveId": "SKULL_BASH", "uses": 3441}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 63.2}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 710, "matchups": [{"opponent": "swampert_shadow", "rating": 876, "opRating": 124}, {"opponent": "furret", "rating": 675, "opRating": 324}, {"opponent": "jumpluff_shadow", "rating": 588, "opRating": 412}, {"opponent": "talonflame", "rating": 580}, {"opponent": "gligar", "rating": 556, "opRating": 444}], "counters": [{"opponent": "cradily", "rating": 138}, {"opponent": "flygon", "rating": 244}, {"opponent": "flygon_shadow", "rating": 260}, {"opponent": "drampa", "rating": 308}, {"opponent": "clodsire", "rating": 387}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 17285}, {"moveId": "SAND_ATTACK", "uses": 14387}, {"moveId": "GUST", "uses": 13602}, {"moveId": "WING_ATTACK", "uses": 12995}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 19996}, {"moveId": "CLOSE_COMBAT", "uses": 19512}, {"moveId": "FLY", "uses": 16969}, {"moveId": "HEAT_WAVE", "uses": 1941}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 63.2}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 616, "matchups": [{"opponent": "gligar", "rating": 786}, {"opponent": "swampert_shadow", "rating": 744, "opRating": 255}, {"opponent": "magcargo", "rating": 578, "opRating": 421}, {"opponent": "talonflame", "rating": 536}, {"opponent": "jumpluff_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "diggersby", "rating": 247}, {"opponent": "quagsire_shadow", "rating": 254}, {"opponent": "furret", "rating": 268}, {"opponent": "clodsire", "rating": 461}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 33455}, {"moveId": "SNARL", "uses": 24845}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 18729}, {"moveId": "SHADOW_BALL", "uses": 15639}, {"moveId": "SLUDGE_BOMB", "uses": 12069}, {"moveId": "FLAMETHROWER", "uses": 11964}]}, "moveset": ["SHADOW_CLAW", "FOUL_PLAY", "SHADOW_BALL"], "score": 63.1}, {"speciesId": "charm<PERSON>on_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 695, "matchups": [{"opponent": "piloswine", "rating": 839, "opRating": 160}, {"opponent": "pidgeot", "rating": 683, "opRating": 316}, {"opponent": "jumpluff_shadow", "rating": 675, "opRating": 324}, {"opponent": "gliscor", "rating": 617, "opRating": 382}, {"opponent": "ninetales_shadow", "rating": 531, "opRating": 468}], "counters": [{"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "magcargo", "rating": 243}, {"opponent": "talonflame", "rating": 374}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25795}, {"moveId": "FIRE_FANG", "uses": 22540}, {"moveId": "SCRATCH", "uses": 9942}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38637}, {"moveId": "FLAMETHROWER", "uses": 12220}, {"moveId": "FLAME_BURST", "uses": 7517}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["EMBER", "FIRE_PUNCH", "FLAMETHROWER"], "score": 62.9}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 664, "matchups": [{"opponent": "swampert_shadow", "rating": 849, "opRating": 150}, {"opponent": "furret", "rating": 544, "opRating": 455}, {"opponent": "clodsire", "rating": 540, "opRating": 459}, {"opponent": "cradily", "rating": 532}, {"opponent": "gligar", "rating": 504, "opRating": 495}], "counters": [{"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "typhlosion_shadow", "rating": 158}, {"opponent": "skeledirge", "rating": 309}, {"opponent": "talonflame", "rating": 337}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44254}, {"moveId": "RAZOR_LEAF", "uses": 14046}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30489}, {"moveId": "SLUDGE_BOMB", "uses": 20161}, {"moveId": "PETAL_BLIZZARD", "uses": 4242}, {"moveId": "SOLAR_BEAM", "uses": 3518}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 62.8}, {"speciesId": "aipom", "speciesName": "Aipom", "rating": 608, "matchups": [{"opponent": "typhlosion_shadow", "rating": 729, "opRating": 270}, {"opponent": "skeledirge", "rating": 711, "opRating": 288}, {"opponent": "flygon_shadow", "rating": 662, "opRating": 337}, {"opponent": "gligar", "rating": 611, "opRating": 388}, {"opponent": "abomasnow_shadow", "rating": 562, "opRating": 437}], "counters": [{"opponent": "furret", "rating": 262}, {"opponent": "magcargo", "rating": 294}, {"opponent": "diggersby", "rating": 313}, {"opponent": "cradily", "rating": 322}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35025}, {"moveId": "SCRATCH", "uses": 23275}], "chargedMoves": [{"moveId": "SWIFT", "uses": 26855}, {"moveId": "AERIAL_ACE", "uses": 17174}, {"moveId": "LOW_SWEEP", "uses": 9668}, {"moveId": "RETURN", "uses": 4655}]}, "moveset": ["ASTONISH", "SWIFT", "AERIAL_ACE"], "score": 62.7}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 636, "matchups": [{"opponent": "talonflame", "rating": 728}, {"opponent": "quagsire_shadow", "rating": 653, "opRating": 346}, {"opponent": "gliscor", "rating": 649, "opRating": 350}, {"opponent": "clodsire", "rating": 606, "opRating": 393}, {"opponent": "swampert_shadow", "rating": 543, "opRating": 456}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "gligar", "rating": 286}, {"opponent": "cradily", "rating": 336}, {"opponent": "jumpluff_shadow", "rating": 395}, {"opponent": "furret", "rating": 431}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7251}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4302}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3826}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3350}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3349}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3322}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3047}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3008}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2996}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2936}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2909}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2908}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2781}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2621}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2612}, {"moveId": "CHARGE_BEAM", "uses": 2549}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2426}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2057}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25629}, {"moveId": "HYPER_BEAM", "uses": 12566}, {"moveId": "SOLAR_BEAM", "uses": 11144}, {"moveId": "ZAP_CANNON", "uses": 9032}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 62.7}, {"speciesId": "<PERSON><PERSON>e", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 625, "matchups": [{"opponent": "quagsire_shadow", "rating": 926, "opRating": 73}, {"opponent": "swampert_shadow", "rating": 890, "opRating": 109}, {"opponent": "claydol", "rating": 873, "opRating": 126}, {"opponent": "flygon", "rating": 552, "opRating": 447}, {"opponent": "diggersby", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 300}, {"opponent": "magcargo", "rating": 311}, {"opponent": "furret", "rating": 331}, {"opponent": "gligar", "rating": 362}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42516}, {"moveId": "IRON_TAIL", "uses": 15784}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 17249}, {"moveId": "LEAF_TORNADO", "uses": 16613}, {"moveId": "WRAP", "uses": 16205}, {"moveId": "RETURN", "uses": 8191}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "WRAP"], "score": 62.7}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 633, "matchups": [{"opponent": "quagsire_shadow", "rating": 765, "opRating": 234}, {"opponent": "claydol", "rating": 714, "opRating": 285}, {"opponent": "swampert_shadow", "rating": 646, "opRating": 353}, {"opponent": "clodsire", "rating": 607, "opRating": 392}, {"opponent": "flygon", "rating": 567, "opRating": 432}], "counters": [{"opponent": "magcargo", "rating": 209}, {"opponent": "jumpluff_shadow", "rating": 294}, {"opponent": "talonflame", "rating": 351}, {"opponent": "furret", "rating": 356}, {"opponent": "cradily", "rating": 447}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33760}, {"moveId": "FEINT_ATTACK", "uses": 24540}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 18528}, {"moveId": "FOUL_PLAY", "uses": 16600}, {"moveId": "SLUDGE_BOMB", "uses": 16336}, {"moveId": "RETURN", "uses": 6831}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 62.5}, {"speciesId": "chandelure_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "gligar", "rating": 887, "opRating": 112}, {"opponent": "cradily", "rating": 833, "opRating": 166}, {"opponent": "jumpluff_shadow", "rating": 833, "opRating": 166}, {"opponent": "clodsire", "rating": 784, "opRating": 215}, {"opponent": "talonflame", "rating": 693, "opRating": 306}], "counters": [{"opponent": "quagsire_shadow", "rating": 180}, {"opponent": "claydol", "rating": 214}, {"opponent": "flygon", "rating": 224}, {"opponent": "furret", "rating": 256}, {"opponent": "swampert_shadow", "rating": 297}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 28815}, {"moveId": "HEX", "uses": 17053}, {"moveId": "FIRE_SPIN", "uses": 12472}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16619}, {"moveId": "SHADOW_BALL", "uses": 13625}, {"moveId": "OVERHEAT", "uses": 12835}, {"moveId": "ENERGY_BALL", "uses": 10804}, {"moveId": "POLTERGEIST", "uses": 4446}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "SHADOW_BALL"], "score": 62.5}, {"speciesId": "combusken_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 658, "matchups": [{"opponent": "piloswine", "rating": 843, "opRating": 156}, {"opponent": "talonflame", "rating": 767, "opRating": 232}, {"opponent": "skeledirge", "rating": 767, "opRating": 232}, {"opponent": "cradily", "rating": 748, "opRating": 251}, {"opponent": "furret", "rating": 541, "opRating": 458}], "counters": [{"opponent": "quagsire_shadow", "rating": 189}, {"opponent": "claydol", "rating": 227}, {"opponent": "swampert_shadow", "rating": 253}, {"opponent": "gligar", "rating": 324}, {"opponent": "jumpluff_shadow", "rating": 356}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43783}, {"moveId": "PECK", "uses": 14517}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25788}, {"moveId": "ROCK_SLIDE", "uses": 21639}, {"moveId": "FLAMETHROWER", "uses": 10821}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 62.5}, {"speciesId": "pumpkaboo_super", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Super)", "rating": 628, "matchups": [{"opponent": "quagsire_shadow", "rating": 868, "opRating": 131}, {"opponent": "claydol", "rating": 826, "opRating": 173}, {"opponent": "swampert_shadow", "rating": 656, "opRating": 343}, {"opponent": "clodsire", "rating": 609, "opRating": 390}, {"opponent": "gligar", "rating": 549, "opRating": 450}], "counters": [{"opponent": "furret", "rating": 234}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "diggersby", "rating": 356}, {"opponent": "talonflame", "rating": 444}, {"opponent": "cradily", "rating": 482}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 37852}, {"moveId": "RAZOR_LEAF", "uses": 20448}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23634}, {"moveId": "FOUL_PLAY", "uses": 23228}, {"moveId": "SHADOW_SNEAK", "uses": 11396}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 62.5}, {"speciesId": "sceptile_shadow", "speciesName": "<PERSON>eptile (Shadow)", "rating": 727, "matchups": [{"opponent": "swampert_shadow", "rating": 851, "opRating": 148}, {"opponent": "claydol", "rating": 837, "opRating": 162}, {"opponent": "diggersby", "rating": 815, "opRating": 184}, {"opponent": "furret", "rating": 743, "opRating": 256}, {"opponent": "cradily", "rating": 635}], "counters": [{"opponent": "ninetales_shadow", "rating": 115}, {"opponent": "typhlosion_shadow", "rating": 183}, {"opponent": "talonflame", "rating": 281}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "clodsire", "rating": 384}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32129}, {"moveId": "BULLET_SEED", "uses": 26171}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 19577}, {"moveId": "BREAKING_SWIPE", "uses": 12263}, {"moveId": "AERIAL_ACE", "uses": 9852}, {"moveId": "FRENZY_PLANT", "uses": 7267}, {"moveId": "DRAGON_CLAW", "uses": 4836}, {"moveId": "EARTHQUAKE", "uses": 4467}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 62.5}, {"speciesId": "castform_sunny", "speciesName": "Castform (Sunny)", "rating": 722, "matchups": [{"opponent": "furret", "rating": 633, "opRating": 366}, {"opponent": "jumpluff_shadow", "rating": 630, "opRating": 369}, {"opponent": "claydol", "rating": 551, "opRating": 448}, {"opponent": "gligar", "rating": 547, "opRating": 452}, {"opponent": "diggersby", "rating": 534, "opRating": 465}], "counters": [{"opponent": "magcargo", "rating": 132}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "quagsire_shadow", "rating": 189}, {"opponent": "talonflame", "rating": 377}, {"opponent": "cradily", "rating": 388}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 37793}, {"moveId": "TACKLE", "uses": 20507}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 42862}, {"moveId": "SOLAR_BEAM", "uses": 10396}, {"moveId": "FIRE_BLAST", "uses": 4985}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 62.3}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 613, "matchups": [{"opponent": "magcargo", "rating": 787, "opRating": 212}, {"opponent": "clodsire", "rating": 647, "opRating": 352}, {"opponent": "ninetales_shadow", "rating": 591, "opRating": 408}, {"opponent": "swampert_shadow", "rating": 526, "opRating": 473}, {"opponent": "drampa", "rating": 509, "opRating": 490}], "counters": [{"opponent": "jumpluff_shadow", "rating": 192}, {"opponent": "talonflame", "rating": 296}, {"opponent": "diggersby", "rating": 333}, {"opponent": "gligar", "rating": 389}, {"opponent": "cradily", "rating": 399}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45842}, {"moveId": "LOW_KICK", "uses": 12458}], "chargedMoves": [{"moveId": "STOMP", "uses": 13673}, {"moveId": "CRUNCH", "uses": 13123}, {"moveId": "BRICK_BREAK", "uses": 11052}, {"moveId": "OUTRAGE", "uses": 8689}, {"moveId": "EARTHQUAKE", "uses": 6785}, {"moveId": "POWER_UP_PUNCH", "uses": 4845}]}, "moveset": ["MUD_SLAP", "STOMP", "CRUNCH"], "score": 62}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 720, "matchups": [{"opponent": "jumpluff_shadow", "rating": 931, "opRating": 68}, {"opponent": "magcargo", "rating": 802, "opRating": 197}, {"opponent": "talonflame", "rating": 780, "opRating": 219}, {"opponent": "skeledirge", "rating": 780, "opRating": 219}, {"opponent": "cradily", "rating": 633, "opRating": 366}], "counters": [{"opponent": "clodsire", "rating": 180}, {"opponent": "quagsire_shadow", "rating": 198}, {"opponent": "diggersby", "rating": 229}, {"opponent": "swampert_shadow", "rating": 257}, {"opponent": "gligar", "rating": 328}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 28238}, {"moveId": "POISON_JAB", "uses": 22797}, {"moveId": "BITE", "uses": 7273}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 15738}, {"moveId": "EARTH_POWER", "uses": 15524}, {"moveId": "STONE_EDGE", "uses": 12781}, {"moveId": "SLUDGE_WAVE", "uses": 8482}, {"moveId": "EARTHQUAKE", "uses": 5718}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "STONE_EDGE", "POISON_FANG"], "score": 62}, {"speciesId": "growlithe_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 696, "matchups": [{"opponent": "piloswine", "rating": 877, "opRating": 122}, {"opponent": "jumpluff_shadow", "rating": 640, "opRating": 359}, {"opponent": "pidgeot", "rating": 625, "opRating": 374}, {"opponent": "gligar", "rating": 603, "opRating": 396}, {"opponent": "gliscor", "rating": 588, "opRating": 411}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "magcargo", "rating": 243}, {"opponent": "furret", "rating": 253}, {"opponent": "talonflame", "rating": 388}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41378}, {"moveId": "BITE", "uses": 16922}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28816}, {"moveId": "FLAMETHROWER", "uses": 24054}, {"moveId": "FLAME_WHEEL", "uses": 5478}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 61.8}, {"speciesId": "turtonator", "speciesName": "Turtonator", "rating": 697, "matchups": [{"opponent": "gliscor", "rating": 880, "opRating": 119}, {"opponent": "gligar", "rating": 747, "opRating": 252}, {"opponent": "furret", "rating": 637, "opRating": 362}, {"opponent": "jumpluff_shadow", "rating": 610, "opRating": 389}, {"opponent": "talonflame", "rating": 526, "opRating": 473}], "counters": [{"opponent": "flygon", "rating": 180}, {"opponent": "quagsire_shadow", "rating": 189}, {"opponent": "swampert_shadow", "rating": 194}, {"opponent": "drampa", "rating": 195}, {"opponent": "magcargo", "rating": 311}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 24926}, {"moveId": "EMBER", "uses": 17617}, {"moveId": "FIRE_SPIN", "uses": 15768}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 18275}, {"moveId": "OVERHEAT", "uses": 16897}, {"moveId": "DRAGON_PULSE", "uses": 15933}, {"moveId": "FLASH_CANNON", "uses": 7196}]}, "moveset": ["INCINERATE", "DRAGON_PULSE", "OVERHEAT"], "score": 61.8}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 659, "matchups": [{"opponent": "swampert_shadow", "rating": 891, "opRating": 108}, {"opponent": "flygon", "rating": 724, "opRating": 275}, {"opponent": "clodsire", "rating": 616, "opRating": 383}, {"opponent": "gligar", "rating": 542, "opRating": 457}, {"opponent": "cradily", "rating": 538}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "furret", "rating": 253}, {"opponent": "drampa", "rating": 273}, {"opponent": "magcargo", "rating": 333}, {"opponent": "talonflame", "rating": 477}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40077}, {"moveId": "RAZOR_LEAF", "uses": 18223}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17045}, {"moveId": "SEED_BOMB", "uses": 16032}, {"moveId": "SHADOW_BALL", "uses": 13878}, {"moveId": "FIRE_BLAST", "uses": 6905}, {"moveId": "POLTERGEIST", "uses": 4467}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 61.7}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 681, "matchups": [{"opponent": "piloswine", "rating": 856, "opRating": 143}, {"opponent": "talonflame", "rating": 773, "opRating": 226}, {"opponent": "skeledirge", "rating": 773, "opRating": 226}, {"opponent": "jumpluff_shadow", "rating": 694, "opRating": 305}, {"opponent": "drampa", "rating": 550, "opRating": 449}], "counters": [{"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "furret", "rating": 253}, {"opponent": "magcargo", "rating": 307}, {"opponent": "cradily", "rating": 364}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 29463}, {"moveId": "HEX", "uses": 28837}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 16916}, {"moveId": "WILD_CHARGE", "uses": 14269}, {"moveId": "NIGHT_SHADE", "uses": 11905}, {"moveId": "OVERHEAT", "uses": 9779}, {"moveId": "SHADOW_BALL", "uses": 5474}]}, "moveset": ["EMBER", "FIRE_PUNCH", "WILD_CHARGE"], "score": 61.7}, {"speciesId": "g<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 644, "matchups": [{"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "quagsire_shadow", "rating": 905, "opRating": 94}, {"opponent": "claydol", "rating": 790, "opRating": 209}, {"opponent": "flygon", "rating": 551, "opRating": 448}, {"opponent": "diggersby", "rating": 542, "opRating": 457}], "counters": [{"opponent": "typhlosion_shadow", "rating": 111}, {"opponent": "skeledirge", "rating": 208}, {"opponent": "magcargo", "rating": 311}, {"opponent": "talonflame", "rating": 377}, {"opponent": "cradily", "rating": 385}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 29263}, {"moveId": "QUICK_ATTACK", "uses": 29037}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30264}, {"moveId": "AERIAL_ACE", "uses": 16109}, {"moveId": "GRASS_KNOT", "uses": 5930}, {"moveId": "RETURN", "uses": 5853}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "AERIAL_ACE"], "score": 61.6}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 658, "matchups": [{"opponent": "swampert_shadow", "rating": 885, "opRating": 114}, {"opponent": "flygon", "rating": 709, "opRating": 290}, {"opponent": "clodsire", "rating": 598, "opRating": 401}, {"opponent": "cradily", "rating": 590, "opRating": 409}, {"opponent": "gligar", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "furret", "rating": 253}, {"opponent": "drampa", "rating": 265}, {"opponent": "magcargo", "rating": 333}, {"opponent": "talonflame", "rating": 481}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40131}, {"moveId": "RAZOR_LEAF", "uses": 18169}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17057}, {"moveId": "SEED_BOMB", "uses": 16062}, {"moveId": "SHADOW_BALL", "uses": 13853}, {"moveId": "FIRE_BLAST", "uses": 6901}, {"moveId": "POLTERGEIST", "uses": 4464}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 61.4}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 664, "matchups": [{"opponent": "flygon", "rating": 589, "opRating": 410}, {"opponent": "flygon_shadow", "rating": 558, "opRating": 441}, {"opponent": "jumpluff_shadow", "rating": 527, "opRating": 472}, {"opponent": "drampa", "rating": 527, "opRating": 472}, {"opponent": "gligar", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 217}, {"opponent": "clodsire", "rating": 269}, {"opponent": "swampert_shadow", "rating": 286}, {"opponent": "cradily", "rating": 302}, {"opponent": "talonflame", "rating": 455}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 52534}, {"moveId": "POUND", "uses": 5766}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 36630}, {"moveId": "AERIAL_ACE", "uses": 13892}, {"moveId": "HURRICANE", "uses": 7903}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 61.2}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 712, "matchups": [{"opponent": "gligar", "rating": 755, "opRating": 244}, {"opponent": "jumpluff_shadow", "rating": 702, "opRating": 297}, {"opponent": "furret", "rating": 632, "opRating": 367}, {"opponent": "clodsire", "rating": 583, "opRating": 416}, {"opponent": "cradily", "rating": 517}], "counters": [{"opponent": "magcargo", "rating": 81}, {"opponent": "skeledirge", "rating": 212}, {"opponent": "ninetales_shadow", "rating": 230}, {"opponent": "typhlosion_shadow", "rating": 247}, {"opponent": "talonflame", "rating": 348}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 31800}, {"moveId": "LEAFAGE", "uses": 18399}, {"moveId": "RAZOR_LEAF", "uses": 8125}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 26263}, {"moveId": "ICY_WIND", "uses": 8957}, {"moveId": "ENERGY_BALL", "uses": 8502}, {"moveId": "OUTRAGE", "uses": 6423}, {"moveId": "RETURN", "uses": 4110}, {"moveId": "BLIZZARD", "uses": 4015}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 61}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 621, "matchups": [{"opponent": "talonflame", "rating": 727, "opRating": 272}, {"opponent": "skeledirge", "rating": 727, "opRating": 272}, {"opponent": "typhlosion_shadow", "rating": 638, "opRating": 361}, {"opponent": "abomasnow_shadow", "rating": 533, "opRating": 466}, {"opponent": "gliscor", "rating": 514, "opRating": 485}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "magcargo", "rating": 264}, {"opponent": "diggersby", "rating": 278}, {"opponent": "furret", "rating": 315}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 36319}, {"moveId": "SCRATCH", "uses": 21981}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 24702}, {"moveId": "RETURN", "uses": 14488}, {"moveId": "PLAY_ROUGH", "uses": 9915}, {"moveId": "THUNDER", "uses": 9170}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 60.9}, {"speciesId": "raticate", "speciesName": "Raticate", "rating": 625, "matchups": [{"opponent": "magcargo", "rating": 750, "opRating": 250}, {"opponent": "flygon", "rating": 651, "opRating": 348}, {"opponent": "typhlosion_shadow", "rating": 635, "opRating": 364}, {"opponent": "drampa", "rating": 618, "opRating": 381}, {"opponent": "swampert_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "ninetales_shadow", "rating": 202}, {"opponent": "cradily", "rating": 243}, {"opponent": "diggersby", "rating": 339}, {"opponent": "jumpluff_shadow", "rating": 359}, {"opponent": "talonflame", "rating": 485}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39565}, {"moveId": "BITE", "uses": 18735}], "chargedMoves": [{"moveId": "HYPER_FANG", "uses": 25447}, {"moveId": "DIG", "uses": 17514}, {"moveId": "RETURN", "uses": 8697}, {"moveId": "HYPER_BEAM", "uses": 6748}]}, "moveset": ["QUICK_ATTACK", "HYPER_FANG", "DIG"], "score": 60.7}, {"speciesId": "sandshrew_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 604, "matchups": [{"opponent": "magcargo", "rating": 728, "opRating": 271}, {"opponent": "talonflame", "rating": 696}, {"opponent": "skeledirge", "rating": 696, "opRating": 303}, {"opponent": "flygon", "rating": 645, "opRating": 354}, {"opponent": "clodsire", "rating": 519, "opRating": 480}], "counters": [{"opponent": "quagsire_shadow", "rating": 180}, {"opponent": "swampert_shadow", "rating": 235}, {"opponent": "gligar", "rating": 316}, {"opponent": "diggersby", "rating": 341}, {"opponent": "cradily", "rating": 409}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 25211}, {"moveId": "MUD_SHOT", "uses": 22333}, {"moveId": "SCRATCH", "uses": 10772}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21423}, {"moveId": "DIG", "uses": 15339}, {"moveId": "ROCK_SLIDE", "uses": 13868}, {"moveId": "SAND_TOMB", "uses": 7667}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 60.7}, {"speciesId": "servine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 624, "matchups": [{"opponent": "swampert_shadow", "rating": 901, "opRating": 98}, {"opponent": "diggersby", "rating": 855, "opRating": 144}, {"opponent": "claydol", "rating": 848, "opRating": 151}, {"opponent": "furret", "rating": 528, "opRating": 471}, {"opponent": "clodsire", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gliscor", "rating": 176}, {"opponent": "talonflame", "rating": 262}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "cradily", "rating": 347}, {"opponent": "gligar", "rating": 347}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42699}, {"moveId": "IRON_TAIL", "uses": 15601}], "chargedMoves": [{"moveId": "WRAP", "uses": 19936}, {"moveId": "GRASS_KNOT", "uses": 19513}, {"moveId": "LEAF_TORNADO", "uses": 18840}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "WRAP"], "score": 60.7}, {"speciesId": "teddiu<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 596, "matchups": [{"opponent": "swampert_shadow", "rating": 750, "opRating": 250}, {"opponent": "flygon", "rating": 552, "opRating": 447}, {"opponent": "talonflame", "rating": 538}, {"opponent": "claydol", "rating": 538, "opRating": 461}, {"opponent": "drampa", "rating": 535, "opRating": 464}], "counters": [{"opponent": "diggersby", "rating": 264}, {"opponent": "clodsire", "rating": 341}, {"opponent": "jumpluff_shadow", "rating": 388}, {"opponent": "furret", "rating": 396}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 33045}, {"moveId": "SCRATCH", "uses": 25255}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17597}, {"moveId": "CROSS_CHOP", "uses": 16447}, {"moveId": "CRUNCH", "uses": 10244}, {"moveId": "TRAILBLAZE", "uses": 10129}, {"moveId": "PLAY_ROUGH", "uses": 3793}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "SWIFT", "CROSS_CHOP"], "score": 60.7}, {"speciesId": "emboar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 640, "matchups": [{"opponent": "clodsire", "rating": 792, "opRating": 207}, {"opponent": "talonflame", "rating": 751, "opRating": 248}, {"opponent": "cradily", "rating": 710, "opRating": 289}, {"opponent": "diggersby", "rating": 653, "opRating": 346}, {"opponent": "furret", "rating": 540, "opRating": 459}], "counters": [{"opponent": "quagsire_shadow", "rating": 198}, {"opponent": "claydol", "rating": 247}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "flygon", "rating": 268}, {"opponent": "gligar", "rating": 270}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 49809}, {"moveId": "LOW_KICK", "uses": 8491}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24318}, {"moveId": "FOCUS_BLAST", "uses": 12774}, {"moveId": "ROCK_SLIDE", "uses": 12764}, {"moveId": "FLAME_CHARGE", "uses": 6658}, {"moveId": "HEAT_WAVE", "uses": 1875}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 60.5}, {"speciesId": "linoone_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 604, "matchups": [{"opponent": "armarouge", "rating": 811, "opRating": 188}, {"opponent": "typhlosion_shadow", "rating": 772, "opRating": 227}, {"opponent": "pidgeot", "rating": 558, "opRating": 441}, {"opponent": "swampert_shadow", "rating": 532, "opRating": 467}, {"opponent": "furret", "rating": 509, "opRating": 490}], "counters": [{"opponent": "gligar", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "cradily", "rating": 340}, {"opponent": "diggersby", "rating": 405}, {"opponent": "talonflame", "rating": 414}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36524}, {"moveId": "LICK", "uses": 21776}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29673}, {"moveId": "DIG", "uses": 13680}, {"moveId": "GUNK_SHOT", "uses": 7708}, {"moveId": "RETURN", "uses": 7246}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 60.5}, {"speciesId": "tauros_blaze", "speciesName": "<PERSON><PERSON> (Blaze)", "rating": 689, "matchups": [{"opponent": "piloswine", "rating": 723, "opRating": 276}, {"opponent": "flygon", "rating": 657, "opRating": 342}, {"opponent": "furret", "rating": 640, "opRating": 359}, {"opponent": "drampa", "rating": 609, "opRating": 390}, {"opponent": "magcargo", "rating": 543, "opRating": 456}], "counters": [{"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "talonflame", "rating": 240}, {"opponent": "swampert_shadow", "rating": 242}, {"opponent": "gligar", "rating": 339}, {"opponent": "jumpluff_shadow", "rating": 359}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 38125}, {"moveId": "TACKLE", "uses": 16091}, {"moveId": "ZEN_HEADBUTT", "uses": 4092}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21059}, {"moveId": "TRAILBLAZE", "uses": 17753}, {"moveId": "EARTHQUAKE", "uses": 10715}, {"moveId": "IRON_HEAD", "uses": 8789}]}, "moveset": ["DOUBLE_KICK", "FLAME_CHARGE", "TRAILBLAZE"], "score": 60.5}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 661, "matchups": [{"opponent": "clodsire", "rating": 925, "opRating": 74}, {"opponent": "magcargo", "rating": 776, "opRating": 223}, {"opponent": "diggersby", "rating": 610, "opRating": 389}, {"opponent": "swampert_shadow", "rating": 550, "opRating": 449}, {"opponent": "flygon", "rating": 541, "opRating": 458}], "counters": [{"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "cradily", "rating": 263}, {"opponent": "talonflame", "rating": 303}, {"opponent": "gligar", "rating": 316}, {"opponent": "furret", "rating": 387}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8699}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4810}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4357}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3753}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3368}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3345}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3277}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3032}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2871}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2847}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2808}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2801}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2747}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2619}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2612}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2309}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2032}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 20114}, {"moveId": "WATER_PULSE", "uses": 16610}, {"moveId": "EARTH_POWER", "uses": 15830}, {"moveId": "EARTHQUAKE", "uses": 5824}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 60.3}, {"speciesId": "pumpkaboo_large", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Large)", "rating": 609, "matchups": [{"opponent": "claydol", "rating": 815, "opRating": 184}, {"opponent": "swampert_shadow", "rating": 639, "opRating": 360}, {"opponent": "clodsire", "rating": 590, "opRating": 409}, {"opponent": "gligar", "rating": 526, "opRating": 473}, {"opponent": "cradily", "rating": 507}], "counters": [{"opponent": "furret", "rating": 234}, {"opponent": "piloswine", "rating": 253}, {"opponent": "jumpluff_shadow", "rating": 320}, {"opponent": "diggersby", "rating": 350}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38075}, {"moveId": "RAZOR_LEAF", "uses": 20225}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23588}, {"moveId": "FOUL_PLAY", "uses": 23240}, {"moveId": "SHADOW_SNEAK", "uses": 11410}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 60.3}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 666, "matchups": [{"opponent": "talonflame", "rating": 792, "opRating": 207}, {"opponent": "skeledirge", "rating": 792, "opRating": 207}, {"opponent": "furret", "rating": 598, "opRating": 401}, {"opponent": "flygon", "rating": 534, "opRating": 465}, {"opponent": "jumpluff_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "diggersby", "rating": 209}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "claydol", "rating": 293}, {"opponent": "magcargo", "rating": 311}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 48848}, {"moveId": "LOW_KICK", "uses": 9452}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24335}, {"moveId": "FOCUS_BLAST", "uses": 12777}, {"moveId": "ROCK_SLIDE", "uses": 12765}, {"moveId": "FLAME_CHARGE", "uses": 6682}, {"moveId": "HEAT_WAVE", "uses": 1898}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 60.2}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 581, "matchups": [{"opponent": "magmar_shadow", "rating": 782, "opRating": 217}, {"opponent": "claydol", "rating": 721, "opRating": 278}, {"opponent": "ursaring_shadow", "rating": 721, "opRating": 278}, {"opponent": "typhlosion_shadow", "rating": 629, "opRating": 370}, {"opponent": "diggersby", "rating": 564, "opRating": 435}], "counters": [{"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "clodsire", "rating": 305}, {"opponent": "gligar", "rating": 324}, {"opponent": "cradily", "rating": 350}, {"opponent": "furret", "rating": 353}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 32746}, {"moveId": "BULLET_SEED", "uses": 25554}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20342}, {"moveId": "DRAGON_PULSE", "uses": 16921}, {"moveId": "DRACO_METEOR", "uses": 15027}, {"moveId": "SOLAR_BEAM", "uses": 5811}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 60.2}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 611, "matchups": [{"opponent": "magcargo", "rating": 858, "opRating": 141}, {"opponent": "talonflame", "rating": 790}, {"opponent": "clodsire", "rating": 722, "opRating": 277}, {"opponent": "furret", "rating": 581, "opRating": 418}, {"opponent": "jumpluff_shadow", "rating": 576, "opRating": 423}], "counters": [{"opponent": "gligar", "rating": 267}, {"opponent": "quagsire_shadow", "rating": 282}, {"opponent": "claydol", "rating": 318}, {"opponent": "diggersby", "rating": 333}, {"opponent": "cradily", "rating": 406}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23562}, {"moveId": "MUD_SHOT", "uses": 18696}, {"moveId": "ROCK_THROW", "uses": 16077}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17588}, {"moveId": "ROCK_BLAST", "uses": 15829}, {"moveId": "EARTHQUAKE", "uses": 13288}, {"moveId": "ANCIENT_POWER", "uses": 11482}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "ROCK_BLAST"], "score": 60.2}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 674, "matchups": [{"opponent": "gligar", "rating": 887, "opRating": 112}, {"opponent": "jumpluff_shadow", "rating": 887, "opRating": 112}, {"opponent": "clodsire", "rating": 854, "opRating": 145}, {"opponent": "cradily", "rating": 784, "opRating": 215}, {"opponent": "talonflame", "rating": 672, "opRating": 327}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "furret", "rating": 218}, {"opponent": "flygon", "rating": 228}, {"opponent": "claydol", "rating": 235}, {"opponent": "swampert_shadow", "rating": 253}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27990}, {"moveId": "HEX", "uses": 16694}, {"moveId": "FIRE_SPIN", "uses": 13626}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16608}, {"moveId": "SHADOW_BALL", "uses": 13644}, {"moveId": "OVERHEAT", "uses": 12845}, {"moveId": "ENERGY_BALL", "uses": 10815}, {"moveId": "POLTERGEIST", "uses": 4448}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "SHADOW_BALL"], "score": 60.1}, {"speciesId": "mudsdale", "speciesName": "Mudsdale", "rating": 607, "matchups": [{"opponent": "clodsire", "rating": 893, "opRating": 106}, {"opponent": "magcargo", "rating": 808, "opRating": 191}, {"opponent": "skeledirge", "rating": 731, "opRating": 268}, {"opponent": "drampa", "rating": 672, "opRating": 327}, {"opponent": "diggersby", "rating": 555, "opRating": 444}], "counters": [{"opponent": "jumpluff_shadow", "rating": 192}, {"opponent": "cradily", "rating": 284}, {"opponent": "talonflame", "rating": 344}, {"opponent": "gligar", "rating": 370}, {"opponent": "furret", "rating": 400}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43660}, {"moveId": "ROCK_SMASH", "uses": 14640}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21711}, {"moveId": "EARTHQUAKE", "uses": 13149}, {"moveId": "BULLDOZE", "uses": 12368}, {"moveId": "HEAVY_SLAM", "uses": 11064}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTHQUAKE"], "score": 60.1}, {"speciesId": "buneary", "speciesName": "Buneary", "rating": 639, "matchups": [{"opponent": "abomasnow_shadow", "rating": 818, "opRating": 181}, {"opponent": "talonflame", "rating": 644, "opRating": 355}, {"opponent": "typhlosion_shadow", "rating": 625, "opRating": 374}, {"opponent": "piloswine", "rating": 548, "opRating": 451}, {"opponent": "gliscor", "rating": 533, "opRating": 466}], "counters": [{"opponent": "skeledirge", "rating": 190}, {"opponent": "cradily", "rating": 204}, {"opponent": "magcargo", "rating": 269}, {"opponent": "gligar", "rating": 324}, {"opponent": "furret", "rating": 331}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 52817}, {"moveId": "POUND", "uses": 5483}], "chargedMoves": [{"moveId": "SWIFT", "uses": 36650}, {"moveId": "FIRE_PUNCH", "uses": 21650}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "FIRE_PUNCH"], "score": 59.9}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 650, "matchups": [{"opponent": "swampert_shadow", "rating": 917, "opRating": 82}, {"opponent": "flygon", "rating": 721, "opRating": 278}, {"opponent": "clodsire", "rating": 586, "opRating": 413}, {"opponent": "cradily", "rating": 573}, {"opponent": "gligar", "rating": 508, "opRating": 491}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "furret", "rating": 253}, {"opponent": "drampa", "rating": 278}, {"opponent": "magcargo", "rating": 333}, {"opponent": "talonflame", "rating": 481}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40133}, {"moveId": "RAZOR_LEAF", "uses": 18167}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17056}, {"moveId": "SEED_BOMB", "uses": 16063}, {"moveId": "SHADOW_BALL", "uses": 13855}, {"moveId": "FIRE_BLAST", "uses": 6901}, {"moveId": "POLTERGEIST", "uses": 4464}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 59.9}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 649, "matchups": [{"opponent": "swampert_shadow", "rating": 911, "opRating": 88}, {"opponent": "flygon", "rating": 700, "opRating": 299}, {"opponent": "clodsire", "rating": 644, "opRating": 355}, {"opponent": "cradily", "rating": 556, "opRating": 443}, {"opponent": "diggersby", "rating": 528, "opRating": 471}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "furret", "rating": 256}, {"opponent": "gligar", "rating": 332}, {"opponent": "magcargo", "rating": 333}, {"opponent": "talonflame", "rating": 481}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40214}, {"moveId": "RAZOR_LEAF", "uses": 18086}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17028}, {"moveId": "SEED_BOMB", "uses": 16072}, {"moveId": "SHADOW_BALL", "uses": 13839}, {"moveId": "FIRE_BLAST", "uses": 6901}, {"moveId": "POLTERGEIST", "uses": 4468}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 59.9}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 674, "matchups": [{"opponent": "piloswine", "rating": 852, "opRating": 147}, {"opponent": "furret", "rating": 651, "opRating": 348}, {"opponent": "gligar", "rating": 615, "opRating": 384}, {"opponent": "gliscor", "rating": 611, "opRating": 388}, {"opponent": "drampa", "rating": 582, "opRating": 417}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "magcargo", "rating": 277}, {"opponent": "talonflame", "rating": 370}, {"opponent": "cradily", "rating": 388}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 40956}, {"moveId": "ROCK_SMASH", "uses": 17344}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22183}, {"moveId": "LOW_SWEEP", "uses": 19846}, {"moveId": "RETURN", "uses": 11269}, {"moveId": "FLAME_WHEEL", "uses": 4941}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 59.9}, {"speciesId": "marshtomp", "speciesName": "Marshtom<PERSON>", "rating": 643, "matchups": [{"opponent": "clodsire", "rating": 896, "opRating": 103}, {"opponent": "magcargo", "rating": 889, "opRating": 110}, {"opponent": "skeledirge", "rating": 878, "opRating": 121}, {"opponent": "gligar", "rating": 632}, {"opponent": "diggersby", "rating": 589, "opRating": 410}], "counters": [{"opponent": "cradily", "rating": 239}, {"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "furret", "rating": 309}, {"opponent": "flygon", "rating": 404}, {"opponent": "talonflame", "rating": 496}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30400}, {"moveId": "WATER_GUN", "uses": 27900}], "chargedMoves": [{"moveId": "SURF", "uses": 21237}, {"moveId": "SLUDGE", "uses": 16280}, {"moveId": "MUD_BOMB", "uses": 15869}, {"moveId": "RETURN", "uses": 4924}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 59.8}, {"speciesId": "da<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 672, "matchups": [{"opponent": "piloswine", "rating": 837, "opRating": 162}, {"opponent": "clodsire", "rating": 789, "opRating": 210}, {"opponent": "jumpluff_shadow", "rating": 668, "opRating": 331}, {"opponent": "ninetales_shadow", "rating": 557, "opRating": 442}, {"opponent": "gliscor", "rating": 535, "opRating": 464}], "counters": [{"opponent": "swampert_shadow", "rating": 261}, {"opponent": "magcargo", "rating": 269}, {"opponent": "talonflame", "rating": 314}, {"opponent": "furret", "rating": 315}, {"opponent": "gligar", "rating": 324}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 36789}, {"moveId": "TACKLE", "uses": 21511}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 42352}, {"moveId": "FLAME_CHARGE", "uses": 15830}, {"moveId": "FRUSTRATION", "uses": 12}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 59.6}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 663, "matchups": [{"opponent": "talonflame", "rating": 792, "opRating": 207}, {"opponent": "gligar", "rating": 686, "opRating": 313}, {"opponent": "gliscor", "rating": 673, "opRating": 326}, {"opponent": "ninetales_shadow", "rating": 610, "opRating": 389}, {"opponent": "skeledirge", "rating": 567, "opRating": 432}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "clodsire", "rating": 218}, {"opponent": "swampert_shadow", "rating": 235}, {"opponent": "furret", "rating": 303}, {"opponent": "cradily", "rating": 340}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31169}, {"moveId": "WING_ATTACK", "uses": 27131}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18498}, {"moveId": "ANCIENT_POWER", "uses": 16550}, {"moveId": "OVERHEAT", "uses": 15910}, {"moveId": "FIRE_BLAST", "uses": 4683}, {"moveId": "HEAT_WAVE", "uses": 2693}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 59.2}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 661, "matchups": [{"opponent": "pidgeot", "rating": 839, "opRating": 160}, {"opponent": "talonflame", "rating": 686, "opRating": 313}, {"opponent": "drampa", "rating": 613, "opRating": 386}, {"opponent": "furret", "rating": 586, "opRating": 413}, {"opponent": "ninetales_shadow", "rating": 560, "opRating": 439}], "counters": [{"opponent": "cradily", "rating": 204}, {"opponent": "clodsire", "rating": 237}, {"opponent": "swampert_shadow", "rating": 283}, {"opponent": "gligar", "rating": 324}, {"opponent": "magcargo", "rating": 337}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 38192}, {"moveId": "TACKLE", "uses": 20108}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 23730}, {"moveId": "SWIFT", "uses": 19522}, {"moveId": "ENERGY_BALL", "uses": 15082}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 59.1}, {"speciesId": "gogoat", "speciesName": "Gogoat", "rating": 631, "matchups": [{"opponent": "swampert_shadow", "rating": 912, "opRating": 87}, {"opponent": "claydol", "rating": 827, "opRating": 172}, {"opponent": "diggersby", "rating": 660, "opRating": 339}, {"opponent": "furret", "rating": 581, "opRating": 418}, {"opponent": "clodsire", "rating": 539, "opRating": 460}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "talonflame", "rating": 255}, {"opponent": "skeledirge", "rating": 269}, {"opponent": "gligar", "rating": 343}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 38755}, {"moveId": "ROCK_SMASH", "uses": 12390}, {"moveId": "ZEN_HEADBUTT", "uses": 7145}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37621}, {"moveId": "BRICK_BREAK", "uses": 14756}, {"moveId": "SEED_BOMB", "uses": 5877}]}, "moveset": ["VINE_WHIP", "LEAF_BLADE", "BRICK_BREAK"], "score": 59.1}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 662, "matchups": [{"opponent": "magcargo", "rating": 912, "opRating": 87}, {"opponent": "clodsire", "rating": 699, "opRating": 300}, {"opponent": "diggersby", "rating": 648, "opRating": 351}, {"opponent": "swampert_shadow", "rating": 573, "opRating": 426}, {"opponent": "gligar", "rating": 530}], "counters": [{"opponent": "farfetchd", "rating": 72}, {"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "cradily", "rating": 250}, {"opponent": "furret", "rating": 353}, {"opponent": "talonflame", "rating": 492}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29348}, {"moveId": "WATER_GUN", "uses": 28952}], "chargedMoves": [{"moveId": "SCALD", "uses": 19857}, {"moveId": "MUD_BOMB", "uses": 16214}, {"moveId": "BLIZZARD", "uses": 11560}, {"moveId": "RETURN", "uses": 5446}, {"moveId": "WATER_PULSE", "uses": 5184}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 59.1}, {"speciesId": "darum<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "abomasnow_shadow", "rating": 901, "opRating": 98}, {"opponent": "piloswine", "rating": 719, "opRating": 280}, {"opponent": "pidgeot", "rating": 652, "opRating": 347}, {"opponent": "gliscor", "rating": 649, "opRating": 350}, {"opponent": "jumpluff_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "magcargo", "rating": 239}, {"opponent": "swampert_shadow", "rating": 246}, {"opponent": "cradily", "rating": 274}, {"opponent": "gligar", "rating": 316}, {"opponent": "talonflame", "rating": 403}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 36638}, {"moveId": "TACKLE", "uses": 21662}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 33338}, {"moveId": "FLAME_CHARGE", "uses": 12587}, {"moveId": "RETURN", "uses": 12458}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 58.8}, {"speciesId": "linoone_galarian_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>) (<PERSON>)", "rating": 628, "matchups": [{"opponent": "magcargo", "rating": 811, "opRating": 188}, {"opponent": "skeledirge", "rating": 792, "opRating": 207}, {"opponent": "typhlosion_shadow", "rating": 733, "opRating": 266}, {"opponent": "furret", "rating": 594, "opRating": 405}, {"opponent": "clodsire", "rating": 529, "opRating": 470}], "counters": [{"opponent": "gligar", "rating": 251}, {"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "cradily", "rating": 267}, {"opponent": "talonflame", "rating": 329}, {"opponent": "diggersby", "rating": 359}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36952}, {"moveId": "LICK", "uses": 21348}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34192}, {"moveId": "DIG", "uses": 15276}, {"moveId": "GUNK_SHOT", "uses": 8783}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 58.8}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 618, "matchups": [{"opponent": "swampert_shadow", "rating": 881, "opRating": 118}, {"opponent": "claydol", "rating": 835, "opRating": 164}, {"opponent": "diggersby", "rating": 660, "opRating": 339}, {"opponent": "clodsire", "rating": 572, "opRating": 427}, {"opponent": "talonflame", "rating": 553}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "furret", "rating": 300}, {"opponent": "magcargo", "rating": 320}, {"opponent": "gligar", "rating": 366}, {"opponent": "cradily", "rating": 468}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 29229}, {"moveId": "SHADOW_CLAW", "uses": 29071}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 21433}, {"moveId": "SEED_BOMB", "uses": 18991}, {"moveId": "SHADOW_BALL", "uses": 17853}]}, "moveset": ["SHADOW_CLAW", "SEED_BOMB", "SHADOW_BALL"], "score": 58.7}, {"speciesId": "wigglytuff", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 619, "matchups": [{"opponent": "flygon", "rating": 903, "opRating": 96}, {"opponent": "flygon_shadow", "rating": 896, "opRating": 103}, {"opponent": "drampa", "rating": 883, "opRating": 116}, {"opponent": "gligar", "rating": 672, "opRating": 327}, {"opponent": "furret", "rating": 522, "opRating": 477}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "magcargo", "rating": 252}, {"opponent": "cradily", "rating": 288}, {"opponent": "clodsire", "rating": 314}, {"opponent": "jumpluff_shadow", "rating": 362}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 26995}, {"moveId": "FEINT_ATTACK", "uses": 26928}, {"moveId": "POUND", "uses": 4377}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17931}, {"moveId": "ICY_WIND", "uses": 15898}, {"moveId": "DISARMING_VOICE", "uses": 10431}, {"moveId": "ICE_BEAM", "uses": 5109}, {"moveId": "DAZZLING_GLEAM", "uses": 3859}, {"moveId": "PLAY_ROUGH", "uses": 2721}, {"moveId": "HYPER_BEAM", "uses": 2488}]}, "moveset": ["CHARM", "ICY_WIND", "SWIFT"], "score": 58.7}, {"speciesId": "tranquill_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 623, "matchups": [{"opponent": "quagsire_shadow", "rating": 681, "opRating": 318}, {"opponent": "talonflame", "rating": 674, "opRating": 325}, {"opponent": "skeledirge", "rating": 674, "opRating": 325}, {"opponent": "gliscor", "rating": 601, "opRating": 398}, {"opponent": "gligar", "rating": 594, "opRating": 405}], "counters": [{"opponent": "furret", "rating": 221}, {"opponent": "magcargo", "rating": 264}, {"opponent": "jumpluff_shadow", "rating": 300}, {"opponent": "clodsire", "rating": 305}, {"opponent": "cradily", "rating": 333}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32533}, {"moveId": "STEEL_WING", "uses": 25767}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 39245}, {"moveId": "SKY_ATTACK", "uses": 13040}, {"moveId": "HEAT_WAVE", "uses": 5887}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "SKY_ATTACK"], "score": 58.5}, {"speciesId": "monferno_shadow", "speciesName": "Mon<PERSON> (Shadow)", "rating": 678, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "piloswine", "rating": 852, "opRating": 147}, {"opponent": "furret", "rating": 816, "opRating": 183}, {"opponent": "ninetales_shadow", "rating": 550, "opRating": 449}, {"opponent": "drampa", "rating": 525, "opRating": 474}], "counters": [{"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "claydol", "rating": 206}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 255}, {"opponent": "talonflame", "rating": 325}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41060}, {"moveId": "ROCK_SMASH", "uses": 17240}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 27328}, {"moveId": "LOW_SWEEP", "uses": 24838}, {"moveId": "FLAME_WHEEL", "uses": 6080}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 58.4}, {"speciesId": "pidgeotto", "speciesName": "Pidgeotto", "rating": 661, "matchups": [{"opponent": "bewear", "rating": 935, "opRating": 64}, {"opponent": "roserade", "rating": 860, "opRating": 139}, {"opponent": "victreebel", "rating": 806, "opRating": 193}, {"opponent": "swampert_shadow", "rating": 595, "opRating": 404}, {"opponent": "jumpluff_shadow", "rating": 527, "opRating": 472}], "counters": [{"opponent": "magcargo", "rating": 166}, {"opponent": "cradily", "rating": 246}, {"opponent": "talonflame", "rating": 311}, {"opponent": "clodsire", "rating": 322}, {"opponent": "gligar", "rating": 381}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34114}, {"moveId": "STEEL_WING", "uses": 24186}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 30481}, {"moveId": "AERIAL_ACE", "uses": 11460}, {"moveId": "RETURN", "uses": 10191}, {"moveId": "TWISTER", "uses": 6301}]}, "moveset": ["WING_ATTACK", "AIR_CUTTER", "AERIAL_ACE"], "score": 58.3}, {"speciesId": "stufful", "speciesName": "Stufful", "rating": 621, "matchups": [{"opponent": "cradily", "rating": 804, "opRating": 195}, {"opponent": "diggersby", "rating": 657, "opRating": 342}, {"opponent": "furret", "rating": 647, "opRating": 352}, {"opponent": "drampa", "rating": 612, "opRating": 387}, {"opponent": "gligar", "rating": 544, "opRating": 455}], "counters": [{"opponent": "skeledirge", "rating": 190}, {"opponent": "magcargo", "rating": 200}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "swampert_shadow", "rating": 327}, {"opponent": "talonflame", "rating": 459}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 48617}, {"moveId": "TAKE_DOWN", "uses": 9683}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 31246}, {"moveId": "STOMP", "uses": 18448}, {"moveId": "BRICK_BREAK", "uses": 8589}]}, "moveset": ["TACKLE", "SUPER_POWER", "STOMP"], "score": 58.3}, {"speciesId": "torracat", "speciesName": "Torracat", "rating": 655, "matchups": [{"opponent": "skeledirge", "rating": 788, "opRating": 211}, {"opponent": "typhlosion_shadow", "rating": 729, "opRating": 270}, {"opponent": "gliscor", "rating": 596, "opRating": 403}, {"opponent": "drampa", "rating": 570, "opRating": 429}, {"opponent": "furret", "rating": 503, "opRating": 496}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "swampert_shadow", "rating": 238}, {"opponent": "magcargo", "rating": 243}, {"opponent": "claydol", "rating": 260}, {"opponent": "gligar", "rating": 301}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41530}, {"moveId": "BITE", "uses": 16770}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24290}, {"moveId": "FLAME_CHARGE", "uses": 24037}, {"moveId": "FLAMETHROWER", "uses": 9967}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 58.1}, {"speciesId": "flapple", "speciesName": "Flapple", "rating": 575, "matchups": [{"opponent": "claydol", "rating": 726, "opRating": 273}, {"opponent": "staraptor", "rating": 688, "opRating": 311}, {"opponent": "diggersby", "rating": 611, "opRating": 388}, {"opponent": "flygon", "rating": 547, "opRating": 452}, {"opponent": "drampa", "rating": 517, "opRating": 482}], "counters": [{"opponent": "gligar", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "magcargo", "rating": 337}, {"opponent": "furret", "rating": 362}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 31683}, {"moveId": "BULLET_SEED", "uses": 26617}], "chargedMoves": [{"moveId": "FLY", "uses": 22425}, {"moveId": "SEED_BOMB", "uses": 15603}, {"moveId": "OUTRAGE", "uses": 15230}, {"moveId": "DRAGON_PULSE", "uses": 5015}]}, "moveset": ["DRAGON_BREATH", "FLY", "SEED_BOMB"], "score": 58}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 642, "matchups": [{"opponent": "quagsire_shadow", "rating": 926, "opRating": 73}, {"opponent": "swampert_shadow", "rating": 892, "opRating": 107}, {"opponent": "claydol", "rating": 799, "opRating": 200}, {"opponent": "diggersby", "rating": 598, "opRating": 401}, {"opponent": "furret", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "magcargo", "rating": 290}, {"opponent": "gligar", "rating": 305}, {"opponent": "cradily", "rating": 364}, {"opponent": "talonflame", "rating": 414}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 23882}, {"moveId": "BULLET_SEED", "uses": 23644}, {"moveId": "RAZOR_LEAF", "uses": 10715}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 38276}, {"moveId": "LAST_RESORT", "uses": 11446}, {"moveId": "ENERGY_BALL", "uses": 5378}, {"moveId": "SOLAR_BEAM", "uses": 3169}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 57.9}, {"speciesId": "pignite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "piloswine", "rating": 670, "opRating": 329}, {"opponent": "ninetales_shadow", "rating": 620, "opRating": 379}, {"opponent": "furret", "rating": 617, "opRating": 382}, {"opponent": "gligar", "rating": 610, "opRating": 389}, {"opponent": "gliscor", "rating": 594, "opRating": 405}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "skeledirge", "rating": 237}, {"opponent": "talonflame", "rating": 244}, {"opponent": "flygon", "rating": 244}, {"opponent": "claydol", "rating": 268}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 39668}, {"moveId": "TACKLE", "uses": 18632}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 26309}, {"moveId": "FLAME_CHARGE", "uses": 22503}, {"moveId": "FLAMETHROWER", "uses": 9432}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 57.7}, {"speciesId": "golem", "speciesName": "Golem", "rating": 611, "matchups": [{"opponent": "magcargo", "rating": 880, "opRating": 119}, {"opponent": "talonflame", "rating": 824, "opRating": 175}, {"opponent": "skeledirge", "rating": 824, "opRating": 175}, {"opponent": "ninetales_shadow", "rating": 692, "opRating": 307}, {"opponent": "drampa", "rating": 619, "opRating": 380}], "counters": [{"opponent": "gligar", "rating": 240}, {"opponent": "quagsire_shadow", "rating": 245}, {"opponent": "claydol", "rating": 285}, {"opponent": "cradily", "rating": 329}, {"opponent": "swampert_shadow", "rating": 338}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23365}, {"moveId": "MUD_SHOT", "uses": 18518}, {"moveId": "ROCK_THROW", "uses": 16479}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17553}, {"moveId": "ROCK_BLAST", "uses": 15783}, {"moveId": "EARTHQUAKE", "uses": 13264}, {"moveId": "ANCIENT_POWER", "uses": 11684}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "ROCK_BLAST"], "score": 57.6}, {"speciesId": "pignite", "speciesName": "Pignite", "rating": 662, "matchups": [{"opponent": "abomasnow_shadow", "rating": 908, "opRating": 91}, {"opponent": "piloswine", "rating": 715, "opRating": 284}, {"opponent": "ninetales_shadow", "rating": 680, "opRating": 319}, {"opponent": "furret", "rating": 664, "opRating": 335}, {"opponent": "magcargo", "rating": 518, "opRating": 481}], "counters": [{"opponent": "skeledirge", "rating": 190}, {"opponent": "talonflame", "rating": 196}, {"opponent": "claydol", "rating": 280}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "cradily", "rating": 454}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38763}, {"moveId": "TACKLE", "uses": 19537}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22866}, {"moveId": "FLAME_CHARGE", "uses": 19467}, {"moveId": "FLAMETHROWER", "uses": 8046}, {"moveId": "RETURN", "uses": 7988}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 57.6}, {"speciesId": "mudbray", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 598, "matchups": [{"opponent": "clodsire", "rating": 891, "opRating": 108}, {"opponent": "magcargo", "rating": 794, "opRating": 205}, {"opponent": "ninetales_shadow", "rating": 727, "opRating": 272}, {"opponent": "skeledirge", "rating": 697, "opRating": 302}, {"opponent": "drampa", "rating": 585, "opRating": 414}], "counters": [{"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "gligar", "rating": 240}, {"opponent": "quagsire_shadow", "rating": 245}, {"opponent": "cradily", "rating": 288}, {"opponent": "talonflame", "rating": 351}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43781}, {"moveId": "ROCK_SMASH", "uses": 14519}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27494}, {"moveId": "EARTHQUAKE", "uses": 15910}, {"moveId": "BULLDOZE", "uses": 14854}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTHQUAKE"], "score": 57.4}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 607, "matchups": [{"opponent": "swampert_shadow", "rating": 922, "opRating": 77}, {"opponent": "bewear", "rating": 886, "opRating": 113}, {"opponent": "quagsire_shadow", "rating": 670, "opRating": 329}, {"opponent": "gligar", "rating": 569, "opRating": 430}, {"opponent": "gliscor", "rating": 556, "opRating": 443}], "counters": [{"opponent": "clodsire", "rating": 221}, {"opponent": "cradily", "rating": 246}, {"opponent": "magcargo", "rating": 264}, {"opponent": "furret", "rating": 306}, {"opponent": "jumpluff_shadow", "rating": 330}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 28527}, {"moveId": "PECK", "uses": 19072}, {"moveId": "ROCK_SMASH", "uses": 10699}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 36487}, {"moveId": "ROCK_BLAST", "uses": 15255}, {"moveId": "FLASH_CANNON", "uses": 6593}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 57.3}, {"speciesId": "ursa<PERSON>na_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 620, "matchups": [{"opponent": "swampert_shadow", "rating": 788, "opRating": 211}, {"opponent": "clodsire", "rating": 687, "opRating": 312}, {"opponent": "talonflame", "rating": 663}, {"opponent": "claydol", "rating": 621, "opRating": 378}, {"opponent": "flygon", "rating": 607, "opRating": 392}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "magcargo", "rating": 226}, {"opponent": "quagsire_shadow", "rating": 239}, {"opponent": "skeledirge", "rating": 269}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 43367}, {"moveId": "ROCK_SMASH", "uses": 14933}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 12050}, {"moveId": "SWIFT", "uses": 11514}, {"moveId": "TRAILBLAZE", "uses": 8094}, {"moveId": "FIRE_PUNCH", "uses": 7488}, {"moveId": "AERIAL_ACE", "uses": 7170}, {"moveId": "HIGH_HORSEPOWER", "uses": 6742}, {"moveId": "THUNDER_PUNCH", "uses": 5307}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 57.2}, {"speciesId": "growlithe", "speciesName": "Grow<PERSON>he", "rating": 680, "matchups": [{"opponent": "piloswine", "rating": 688, "opRating": 311}, {"opponent": "jumpluff_shadow", "rating": 681, "opRating": 318}, {"opponent": "gliscor", "rating": 629, "opRating": 370}, {"opponent": "drampa", "rating": 551, "opRating": 448}, {"opponent": "furret", "rating": 533, "opRating": 466}], "counters": [{"opponent": "magcargo", "rating": 179}, {"opponent": "cradily", "rating": 204}, {"opponent": "skeledirge", "rating": 230}, {"opponent": "talonflame", "rating": 318}, {"opponent": "swampert_shadow", "rating": 338}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 40517}, {"moveId": "BITE", "uses": 17783}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25390}, {"moveId": "FLAMETHROWER", "uses": 21800}, {"moveId": "RETURN", "uses": 6251}, {"moveId": "FLAME_WHEEL", "uses": 4859}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 57}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 635, "matchups": [{"opponent": "piloswine", "rating": 843, "opRating": 156}, {"opponent": "talonflame", "rating": 677, "opRating": 322}, {"opponent": "skeledirge", "rating": 677, "opRating": 322}, {"opponent": "jumpluff_shadow", "rating": 563, "opRating": 436}, {"opponent": "furret", "rating": 504, "opRating": 495}], "counters": [{"opponent": "claydol", "rating": 235}, {"opponent": "diggersby", "rating": 278}, {"opponent": "clodsire", "rating": 283}, {"opponent": "gligar", "rating": 309}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 29546}, {"moveId": "FIRE_FANG", "uses": 28754}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 25728}, {"moveId": "FLAMETHROWER", "uses": 16183}, {"moveId": "CRUNCH", "uses": 12029}, {"moveId": "FIRE_BLAST", "uses": 4262}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 56.9}, {"speciesId": "pumpkaboo_average", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Average)", "rating": 589, "matchups": [{"opponent": "quagsire_shadow", "rating": 853, "opRating": 146}, {"opponent": "claydol", "rating": 805, "opRating": 194}, {"opponent": "swampert_shadow", "rating": 623, "opRating": 376}, {"opponent": "clodsire", "rating": 567, "opRating": 432}, {"opponent": "skeledirge", "rating": 543, "opRating": 456}], "counters": [{"opponent": "furret", "rating": 237}, {"opponent": "jumpluff_shadow", "rating": 323}, {"opponent": "diggersby", "rating": 353}, {"opponent": "talonflame", "rating": 411}, {"opponent": "cradily", "rating": 416}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38146}, {"moveId": "RAZOR_LEAF", "uses": 20154}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23591}, {"moveId": "FOUL_PLAY", "uses": 23234}, {"moveId": "SHADOW_SNEAK", "uses": 11418}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 56.9}, {"speciesId": "porygon", "speciesName": "Porygon", "rating": 596, "matchups": [{"opponent": "gligar", "rating": 651, "opRating": 348}, {"opponent": "quagsire_shadow", "rating": 633, "opRating": 366}, {"opponent": "jumpluff_shadow", "rating": 540, "opRating": 459}, {"opponent": "swampert_shadow", "rating": 522, "opRating": 477}, {"opponent": "furret", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 226}, {"opponent": "skeledirge", "rating": 233}, {"opponent": "flygon", "rating": 304}, {"opponent": "cradily", "rating": 305}, {"opponent": "talonflame", "rating": 451}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 6524}, {"moveId": "TACKLE", "uses": 4523}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3950}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3503}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3019}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3012}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3011}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2773}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2735}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2720}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2633}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2628}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2572}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2571}, {"moveId": "CHARGE_BEAM", "uses": 2451}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2360}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2346}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2111}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1873}, {"moveId": "ZEN_HEADBUTT", "uses": 1076}], "chargedMoves": [{"moveId": "RETURN", "uses": 11595}, {"moveId": "SIGNAL_BEAM", "uses": 11384}, {"moveId": "DISCHARGE", "uses": 10807}, {"moveId": "SOLAR_BEAM", "uses": 8489}, {"moveId": "ZAP_CANNON", "uses": 5747}, {"moveId": "PSYBEAM", "uses": 5687}, {"moveId": "HYPER_BEAM", "uses": 4515}]}, "moveset": ["QUICK_ATTACK", "DISCHARGE", "HYPER_BEAM"], "score": 56.6}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 623, "matchups": [{"opponent": "quagsire_shadow", "rating": 923, "opRating": 76}, {"opponent": "swampert_shadow", "rating": 884, "opRating": 115}, {"opponent": "claydol", "rating": 776, "opRating": 223}, {"opponent": "furret", "rating": 604, "opRating": 395}, {"opponent": "diggersby", "rating": 566, "opRating": 433}], "counters": [{"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "magcargo", "rating": 277}, {"opponent": "gligar", "rating": 290}, {"opponent": "talonflame", "rating": 340}, {"opponent": "cradily", "rating": 399}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 19381}, {"moveId": "BULLET_SEED", "uses": 18553}, {"moveId": "FEINT_ATTACK", "uses": 13394}, {"moveId": "RAZOR_LEAF", "uses": 6987}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 25237}, {"moveId": "FOUL_PLAY", "uses": 15875}, {"moveId": "HURRICANE", "uses": 7594}, {"moveId": "LEAF_TORNADO", "uses": 4824}, {"moveId": "RETURN", "uses": 4770}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 56.6}, {"speciesId": "quilladin", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 599, "matchups": [{"opponent": "swampert_shadow", "rating": 892, "opRating": 107}, {"opponent": "claydol", "rating": 861, "opRating": 138}, {"opponent": "diggersby", "rating": 657, "opRating": 342}, {"opponent": "furret", "rating": 580, "opRating": 419}, {"opponent": "clodsire", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "cradily", "rating": 312}, {"opponent": "magcargo", "rating": 337}, {"opponent": "talonflame", "rating": 340}, {"opponent": "gligar", "rating": 400}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45876}, {"moveId": "LOW_KICK", "uses": 12424}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28478}, {"moveId": "ENERGY_BALL", "uses": 21029}, {"moveId": "GYRO_BALL", "uses": 8794}]}, "moveset": ["VINE_WHIP", "BODY_SLAM", "ENERGY_BALL"], "score": 56.3}, {"speciesId": "rhy<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 604, "matchups": [{"opponent": "magcargo", "rating": 883, "opRating": 116}, {"opponent": "skeledirge", "rating": 833, "opRating": 166}, {"opponent": "clodsire", "rating": 783, "opRating": 216}, {"opponent": "flygon", "rating": 666, "opRating": 333}, {"opponent": "diggersby", "rating": 509, "opRating": 490}], "counters": [{"opponent": "jumpluff_shadow", "rating": 222}, {"opponent": "gligar", "rating": 240}, {"opponent": "quagsire_shadow", "rating": 263}, {"opponent": "cradily", "rating": 357}, {"opponent": "talonflame", "rating": 459}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 42558}, {"moveId": "ROCK_SMASH", "uses": 15742}], "chargedMoves": [{"moveId": "STOMP", "uses": 20377}, {"moveId": "BULLDOZE", "uses": 19299}, {"moveId": "HORN_ATTACK", "uses": 18578}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BULLDOZE", "STOMP"], "score": 56.3}, {"speciesId": "raticate_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 611, "matchups": [{"opponent": "magcargo", "rating": 700, "opRating": 299}, {"opponent": "talonflame", "rating": 668}, {"opponent": "flygon", "rating": 594, "opRating": 405}, {"opponent": "quagsire_shadow", "rating": 545, "opRating": 454}, {"opponent": "drampa", "rating": 508, "opRating": 491}], "counters": [{"opponent": "furret", "rating": 190}, {"opponent": "ninetales_shadow", "rating": 242}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "cradily", "rating": 267}, {"opponent": "clodsire", "rating": 379}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 41014}, {"moveId": "BITE", "uses": 17286}], "chargedMoves": [{"moveId": "HYPER_FANG", "uses": 30277}, {"moveId": "DIG", "uses": 19904}, {"moveId": "HYPER_BEAM", "uses": 8123}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "HYPER_FANG", "DIG"], "score": 56.2}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 588, "matchups": [{"opponent": "magcargo", "rating": 679, "opRating": 320}, {"opponent": "talonflame", "rating": 662, "opRating": 337}, {"opponent": "skeledirge", "rating": 662, "opRating": 337}, {"opponent": "clodsire", "rating": 614, "opRating": 385}, {"opponent": "flygon", "rating": 596, "opRating": 403}], "counters": [{"opponent": "abomasnow_shadow", "rating": 87}, {"opponent": "piloswine", "rating": 149}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "cradily", "rating": 364}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36364}, {"moveId": "ROCK_THROW", "uses": 21936}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 17512}, {"moveId": "ROCK_SLIDE", "uses": 16247}, {"moveId": "OUTRAGE", "uses": 12734}, {"moveId": "FOCUS_BLAST", "uses": 11689}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "ROCK_SLIDE"], "score": 56.1}, {"speciesId": "camerupt_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 581, "matchups": [{"opponent": "clodsire", "rating": 840, "opRating": 160}, {"opponent": "cradily", "rating": 788, "opRating": 212}, {"opponent": "magcargo", "rating": 736, "opRating": 264}, {"opponent": "talonflame", "rating": 696, "opRating": 304}, {"opponent": "skeledirge", "rating": 696, "opRating": 304}], "counters": [{"opponent": "claydol", "rating": 227}, {"opponent": "diggersby", "rating": 261}, {"opponent": "swampert_shadow", "rating": 268}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 303}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 33516}, {"moveId": "EMBER", "uses": 19907}, {"moveId": "ROCK_SMASH", "uses": 4885}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 22025}, {"moveId": "EARTH_POWER", "uses": 20102}, {"moveId": "SOLAR_BEAM", "uses": 8729}, {"moveId": "EARTHQUAKE", "uses": 7305}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 55.7}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 640, "matchups": [{"opponent": "jumpluff_shadow", "rating": 761, "opRating": 238}, {"opponent": "magcargo", "rating": 597, "opRating": 402}, {"opponent": "talonflame", "rating": 553}, {"opponent": "cradily", "rating": 548}, {"opponent": "furret", "rating": 539, "opRating": 460}], "counters": [{"opponent": "claydol", "rating": 136}, {"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "ninetales_shadow", "rating": 226}, {"opponent": "diggersby", "rating": 396}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 34670}, {"moveId": "BUG_BITE", "uses": 23630}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 19777}, {"moveId": "STONE_EDGE", "uses": 11703}, {"moveId": "EARTH_POWER", "uses": 9867}, {"moveId": "IRON_HEAD", "uses": 8682}, {"moveId": "FLAMETHROWER", "uses": 5410}, {"moveId": "FIRE_BLAST", "uses": 2843}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 55.5}, {"speciesId": "gloom", "speciesName": "Gloom", "rating": 632, "matchups": [{"opponent": "jumpluff_shadow", "rating": 656, "opRating": 343}, {"opponent": "swampert_shadow", "rating": 606, "opRating": 393}, {"opponent": "talonflame", "rating": 583}, {"opponent": "furret", "rating": 534, "opRating": 465}, {"opponent": "cradily", "rating": 519}], "counters": [{"opponent": "piloswine", "rating": 133}, {"opponent": "clodsire", "rating": 204}, {"opponent": "magcargo", "rating": 269}, {"opponent": "diggersby", "rating": 310}, {"opponent": "gligar", "rating": 404}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 40833}, {"moveId": "RAZOR_LEAF", "uses": 17467}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 21076}, {"moveId": "PETAL_BLIZZARD", "uses": 14891}, {"moveId": "MOONBLAST", "uses": 13039}, {"moveId": "RETURN", "uses": 9398}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 55.4}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 618, "matchups": [{"opponent": "claydol", "rating": 794, "opRating": 205}, {"opponent": "quagsire_shadow", "rating": 722, "opRating": 277}, {"opponent": "swampert_shadow", "rating": 621, "opRating": 378}, {"opponent": "clodsire", "rating": 542, "opRating": 457}, {"opponent": "gligar", "rating": 521}], "counters": [{"opponent": "furret", "rating": 128}, {"opponent": "ninetales_shadow", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 359}, {"opponent": "cradily", "rating": 444}, {"opponent": "talonflame", "rating": 455}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 19971}, {"moveId": "LEAFAGE", "uses": 16852}, {"moveId": "MAGICAL_LEAF", "uses": 15634}, {"moveId": "RAZOR_LEAF", "uses": 5846}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 21924}, {"moveId": "BRAVE_BIRD", "uses": 19196}, {"moveId": "SPIRIT_SHACKLE", "uses": 10241}, {"moveId": "ENERGY_BALL", "uses": 4194}, {"moveId": "SHADOW_SNEAK", "uses": 2695}]}, "moveset": ["ASTONISH", "FRENZY_PLANT", "SPIRIT_SHACKLE"], "score": 55.2}, {"speciesId": "te<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 564, "matchups": [{"opponent": "piloswine_shadow", "rating": 750, "opRating": 250}, {"opponent": "piloswine", "rating": 721, "opRating": 278}, {"opponent": "flygon", "rating": 573, "opRating": 426}, {"opponent": "flygon_shadow", "rating": 552, "opRating": 447}, {"opponent": "abomasnow_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "diggersby", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "furret", "rating": 303}, {"opponent": "clodsire", "rating": 319}, {"opponent": "cradily", "rating": 333}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 31835}, {"moveId": "SCRATCH", "uses": 26465}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16562}, {"moveId": "CROSS_CHOP", "uses": 15718}, {"moveId": "CRUNCH", "uses": 9731}, {"moveId": "TRAILBLAZE", "uses": 9727}, {"moveId": "PLAY_ROUGH", "uses": 3631}, {"moveId": "RETURN", "uses": 2808}]}, "moveset": ["LICK", "SWIFT", "CROSS_CHOP"], "score": 55.2}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 581, "matchups": [{"opponent": "fletchinder", "rating": 667, "opRating": 332}, {"opponent": "talonflame", "rating": 654, "opRating": 345}, {"opponent": "quagsire_shadow", "rating": 602, "opRating": 397}, {"opponent": "abomasnow_shadow", "rating": 551, "opRating": 448}, {"opponent": "pidgeot", "rating": 520, "opRating": 479}], "counters": [{"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "furret", "rating": 237}, {"opponent": "gligar", "rating": 255}, {"opponent": "claydol", "rating": 272}, {"opponent": "cradily", "rating": 371}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 29315}, {"moveId": "TACKLE", "uses": 22984}, {"moveId": "ZEN_HEADBUTT", "uses": 5970}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 16730}, {"moveId": "STOMP", "uses": 15537}, {"moveId": "MEGAHORN", "uses": 15161}, {"moveId": "PSYCHIC", "uses": 10859}]}, "moveset": ["CONFUSION", "STOMP", "WILD_CHARGE"], "score": 55.2}, {"speciesId": "dug<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 582, "matchups": [{"opponent": "clodsire", "rating": 933, "opRating": 66}, {"opponent": "cradily", "rating": 927}, {"opponent": "flygon", "rating": 683, "opRating": 316}, {"opponent": "magcargo", "rating": 577, "opRating": 422}, {"opponent": "furret", "rating": 505, "opRating": 494}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "claydol", "rating": 247}, {"opponent": "quagsire_shadow", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "talonflame", "rating": 322}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22527}, {"moveId": "SAND_ATTACK", "uses": 19635}, {"moveId": "METAL_CLAW", "uses": 16127}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 26174}, {"moveId": "IRON_HEAD", "uses": 16171}, {"moveId": "RETURN", "uses": 9697}, {"moveId": "EARTHQUAKE", "uses": 6212}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "IRON_HEAD"], "score": 55}, {"speciesId": "eevee", "speciesName": "Eevee", "rating": 540, "matchups": [{"opponent": "salazzle", "rating": 751, "opRating": 248}, {"opponent": "typhlosion_shadow", "rating": 740, "opRating": 259}, {"opponent": "gliscor_shadow", "rating": 707, "opRating": 292}, {"opponent": "rapidash", "rating": 629, "opRating": 370}, {"opponent": "quagsire_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "furret", "rating": 268}, {"opponent": "cradily", "rating": 295}, {"opponent": "diggersby", "rating": 318}, {"opponent": "talonflame", "rating": 325}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 33677}, {"moveId": "TACKLE", "uses": 24623}], "chargedMoves": [{"moveId": "SWIFT", "uses": 28716}, {"moveId": "BODY_SLAM", "uses": 11907}, {"moveId": "DIG", "uses": 11411}, {"moveId": "LAST_RESORT", "uses": 6204}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "DIG"], "score": 54.7}, {"speciesId": "grou<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 638, "matchups": [{"opponent": "clodsire", "rating": 890, "opRating": 109}, {"opponent": "magcargo", "rating": 652, "opRating": 347}, {"opponent": "typhlosion_shadow", "rating": 623, "opRating": 376}, {"opponent": "skeledirge", "rating": 614, "opRating": 385}, {"opponent": "claydol", "rating": 519, "opRating": 480}], "counters": [{"opponent": "furret", "rating": 159}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 248}, {"opponent": "talonflame", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 290}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31890}, {"moveId": "DRAGON_TAIL", "uses": 26410}], "chargedMoves": [{"moveId": "PRECIPICE_BLADES", "uses": 21923}, {"moveId": "FIRE_PUNCH", "uses": 20076}, {"moveId": "SOLAR_BEAM", "uses": 7090}, {"moveId": "EARTHQUAKE", "uses": 5766}, {"moveId": "FIRE_BLAST", "uses": 3304}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "PRECIPICE_BLADES", "FIRE_PUNCH"], "score": 54.7}, {"speciesId": "camerupt", "speciesName": "Camerupt", "rating": 616, "matchups": [{"opponent": "clodsire", "rating": 892, "opRating": 108}, {"opponent": "magcargo", "rating": 776, "opRating": 224}, {"opponent": "talonflame", "rating": 756, "opRating": 244}, {"opponent": "cradily", "rating": 580, "opRating": 420}, {"opponent": "jumpluff_shadow", "rating": 508, "opRating": 492}], "counters": [{"opponent": "quagsire_shadow", "rating": 133}, {"opponent": "claydol", "rating": 194}, {"opponent": "diggersby", "rating": 218}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 232}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32435}, {"moveId": "EMBER", "uses": 20547}, {"moveId": "ROCK_SMASH", "uses": 5346}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 18572}, {"moveId": "EARTH_POWER", "uses": 17410}, {"moveId": "RETURN", "uses": 8492}, {"moveId": "SOLAR_BEAM", "uses": 7569}, {"moveId": "EARTHQUAKE", "uses": 6359}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 54.6}, {"speciesId": "persian", "speciesName": "Persian", "rating": 542, "matchups": [{"opponent": "victini", "rating": 850, "opRating": 149}, {"opponent": "swellow", "rating": 759, "opRating": 240}, {"opponent": "farfetchd", "rating": 638, "opRating": 361}, {"opponent": "pidgeot", "rating": 580, "opRating": 419}, {"opponent": "ninetales_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "furret", "rating": 306}, {"opponent": "clodsire", "rating": 338}, {"opponent": "diggersby", "rating": 347}, {"opponent": "talonflame", "rating": 355}, {"opponent": "cradily", "rating": 423}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 31959}, {"moveId": "SCRATCH", "uses": 26341}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 18656}, {"moveId": "POWER_GEM", "uses": 13569}, {"moveId": "RETURN", "uses": 8521}, {"moveId": "FOUL_PLAY", "uses": 6932}, {"moveId": "PLAY_ROUGH", "uses": 6001}, {"moveId": "PAYBACK", "uses": 4584}]}, "moveset": ["FEINT_ATTACK", "NIGHT_SLASH", "POWER_GEM"], "score": 54.6}, {"speciesId": "rhyhorn", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 580, "matchups": [{"opponent": "magcargo", "rating": 783, "opRating": 216}, {"opponent": "drampa", "rating": 720, "opRating": 279}, {"opponent": "skeledirge", "rating": 704, "opRating": 295}, {"opponent": "talonflame", "rating": 569, "opRating": 430}, {"opponent": "clodsire", "rating": 525, "opRating": 474}], "counters": [{"opponent": "quagsire_shadow", "rating": 226}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "cradily", "rating": 295}, {"opponent": "swampert_shadow", "rating": 312}, {"opponent": "gligar", "rating": 343}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 42564}, {"moveId": "ROCK_SMASH", "uses": 15736}], "chargedMoves": [{"moveId": "BULLDOZE", "uses": 16782}, {"moveId": "STOMP", "uses": 16675}, {"moveId": "HORN_ATTACK", "uses": 15262}, {"moveId": "RETURN", "uses": 9576}]}, "moveset": ["MUD_SLAP", "BULLDOZE", "STOMP"], "score": 54.6}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 637, "matchups": [{"opponent": "cradily", "rating": 743, "opRating": 256}, {"opponent": "magcargo", "rating": 743, "opRating": 256}, {"opponent": "drampa", "rating": 659, "opRating": 340}, {"opponent": "diggersby", "rating": 655, "opRating": 344}, {"opponent": "furret", "rating": 554, "opRating": 445}], "counters": [{"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "claydol", "rating": 165}, {"opponent": "gligar", "rating": 187}, {"opponent": "clodsire", "rating": 283}, {"opponent": "talonflame", "rating": 366}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30610}, {"moveId": "COUNTER", "uses": 27690}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14742}, {"moveId": "BRAVE_BIRD", "uses": 12605}, {"moveId": "BLAZE_KICK", "uses": 10820}, {"moveId": "STONE_EDGE", "uses": 8793}, {"moveId": "FOCUS_BLAST", "uses": 8273}, {"moveId": "OVERHEAT", "uses": 3076}]}, "moveset": ["COUNTER", "BLAZE_KICK", "STONE_EDGE"], "score": 54.4}, {"speciesId": "ivysaur", "speciesName": "Ivysaur", "rating": 629, "matchups": [{"opponent": "swampert_shadow", "rating": 858, "opRating": 141}, {"opponent": "claydol", "rating": 687, "opRating": 312}, {"opponent": "diggersby", "rating": 606, "opRating": 393}, {"opponent": "clodsire", "rating": 603, "opRating": 396}, {"opponent": "cradily", "rating": 526}], "counters": [{"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "typhlosion_shadow", "rating": 158}, {"opponent": "talonflame", "rating": 329}, {"opponent": "magcargo", "rating": 341}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42321}, {"moveId": "RAZOR_LEAF", "uses": 15979}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22622}, {"moveId": "SLUDGE_BOMB", "uses": 21795}, {"moveId": "RETURN", "uses": 9123}, {"moveId": "SOLAR_BEAM", "uses": 4745}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 54.4}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 648, "matchups": [{"opponent": "swampert_shadow", "rating": 853, "opRating": 146}, {"opponent": "diggersby", "rating": 833, "opRating": 166}, {"opponent": "furret", "rating": 752, "opRating": 247}, {"opponent": "claydol", "rating": 711, "opRating": 288}, {"opponent": "flygon", "rating": 626, "opRating": 373}], "counters": [{"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "typhlosion_shadow", "rating": 158}, {"opponent": "talonflame", "rating": 388}, {"opponent": "clodsire", "rating": 389}, {"opponent": "cradily", "rating": 420}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45081}, {"moveId": "RAZOR_LEAF", "uses": 13219}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30462}, {"moveId": "SLUDGE_BOMB", "uses": 20158}, {"moveId": "PETAL_BLIZZARD", "uses": 4227}, {"moveId": "SOLAR_BEAM", "uses": 3537}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 54.4}, {"speciesId": "blazi<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 617, "matchups": [{"opponent": "furret", "rating": 806, "opRating": 193}, {"opponent": "magcargo", "rating": 806, "opRating": 193}, {"opponent": "cradily", "rating": 693, "opRating": 306}, {"opponent": "drampa", "rating": 693, "opRating": 306}, {"opponent": "diggersby", "rating": 634, "opRating": 365}], "counters": [{"opponent": "claydol", "rating": 136}, {"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "gligar", "rating": 194}, {"opponent": "clodsire", "rating": 338}, {"opponent": "talonflame", "rating": 437}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31042}, {"moveId": "COUNTER", "uses": 27258}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14717}, {"moveId": "BRAVE_BIRD", "uses": 12602}, {"moveId": "BLAZE_KICK", "uses": 10786}, {"moveId": "STONE_EDGE", "uses": 8785}, {"moveId": "FOCUS_BLAST", "uses": 8276}, {"moveId": "OVERHEAT", "uses": 3084}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BLAZE_KICK", "STONE_EDGE"], "score": 54.3}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 666, "matchups": [{"opponent": "clodsire", "rating": 876, "opRating": 123}, {"opponent": "jumpluff_shadow", "rating": 851, "opRating": 148}, {"opponent": "cradily", "rating": 836, "opRating": 163}, {"opponent": "gligar", "rating": 811, "opRating": 188}, {"opponent": "swampert_shadow", "rating": 793, "opRating": 206}], "counters": [{"opponent": "ninetales_shadow", "rating": 130}, {"opponent": "typhlosion_shadow", "rating": 132}, {"opponent": "quagsire_shadow", "rating": 198}, {"opponent": "furret", "rating": 221}, {"opponent": "magcargo", "rating": 252}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34608}, {"moveId": "MUD_SLAP", "uses": 23692}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19374}, {"moveId": "ICICLE_SPEAR", "uses": 16241}, {"moveId": "STONE_EDGE", "uses": 6192}, {"moveId": "HIGH_HORSEPOWER", "uses": 6097}, {"moveId": "ANCIENT_POWER", "uses": 5542}, {"moveId": "BULLDOZE", "uses": 4956}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 54.3}, {"speciesId": "pumpkaboo_small", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Small)", "rating": 573, "matchups": [{"opponent": "claydol", "rating": 794, "opRating": 205}, {"opponent": "quagsire_shadow", "rating": 726, "opRating": 273}, {"opponent": "swampert_shadow", "rating": 600, "opRating": 399}, {"opponent": "clodsire", "rating": 546, "opRating": 453}, {"opponent": "skeledirge", "rating": 529, "opRating": 470}], "counters": [{"opponent": "furret", "rating": 225}, {"opponent": "jumpluff_shadow", "rating": 323}, {"opponent": "magcargo", "rating": 337}, {"opponent": "cradily", "rating": 395}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38204}, {"moveId": "RAZOR_LEAF", "uses": 20096}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23615}, {"moveId": "FOUL_PLAY", "uses": 23216}, {"moveId": "SHADOW_SNEAK", "uses": 11405}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 54.3}, {"speciesId": "whimsicott", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 693, "matchups": [{"opponent": "swampert_shadow", "rating": 894, "opRating": 105}, {"opponent": "flygon", "rating": 877, "opRating": 122}, {"opponent": "furret", "rating": 673, "opRating": 326}, {"opponent": "diggersby", "rating": 639, "opRating": 360}, {"opponent": "gligar", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "clodsire", "rating": 182}, {"opponent": "magcargo", "rating": 247}, {"opponent": "cradily", "rating": 333}, {"opponent": "talonflame", "rating": 348}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 31663}, {"moveId": "CHARM", "uses": 14738}, {"moveId": "RAZOR_LEAF", "uses": 11882}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 17001}, {"moveId": "MOONBLAST", "uses": 15118}, {"moveId": "SEED_BOMB", "uses": 13550}, {"moveId": "HURRICANE", "uses": 12596}]}, "moveset": ["FAIRY_WIND", "SEED_BOMB", "MOONBLAST"], "score": 54.3}, {"speciesId": "zygarde_10", "speciesName": "Zygarde (10% Forme)", "rating": 534, "matchups": [{"opponent": "salazzle", "rating": 729, "opRating": 270}, {"opponent": "typhlosion_shadow", "rating": 709, "opRating": 290}, {"opponent": "swellow", "rating": 688, "opRating": 311}, {"opponent": "claydol", "rating": 581, "opRating": 418}, {"opponent": "gliscor", "rating": 505, "opRating": 494}], "counters": [{"opponent": "diggersby", "rating": 232}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "cradily", "rating": 288}, {"opponent": "gligar", "rating": 309}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 39344}, {"moveId": "BITE", "uses": 13931}, {"moveId": "ZEN_HEADBUTT", "uses": 5030}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 15917}, {"moveId": "OUTRAGE", "uses": 15860}, {"moveId": "EARTHQUAKE", "uses": 10759}, {"moveId": "BULLDOZE", "uses": 10104}, {"moveId": "HYPER_BEAM", "uses": 5781}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "EARTHQUAKE"], "score": 54.3}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 583, "matchups": [{"opponent": "talonflame", "rating": 724, "opRating": 276}, {"opponent": "ninetales_shadow", "rating": 716, "opRating": 284}, {"opponent": "skeledirge", "rating": 708, "opRating": 292}, {"opponent": "magcargo", "rating": 696, "opRating": 304}, {"opponent": "typhlosion_shadow", "rating": 644, "opRating": 356}], "counters": [{"opponent": "pidgeot", "rating": 91}, {"opponent": "furret", "rating": 159}, {"opponent": "cradily", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "claydol", "rating": 301}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23610}, {"moveId": "SAND_ATTACK", "uses": 18883}, {"moveId": "LICK", "uses": 11556}, {"moveId": "TAKE_DOWN", "uses": 4171}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 23895}, {"moveId": "CRUNCH", "uses": 23859}, {"moveId": "PLAY_ROUGH", "uses": 10488}]}, "moveset": ["SAND_ATTACK", "WILD_CHARGE", "CRUNCH"], "score": 54.1}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 681, "matchups": [{"opponent": "talonflame", "rating": 828, "opRating": 171}, {"opponent": "skeledirge", "rating": 828, "opRating": 171}, {"opponent": "jumpluff_shadow", "rating": 706, "opRating": 293}, {"opponent": "furret", "rating": 591, "opRating": 408}, {"opponent": "claydol", "rating": 538, "opRating": 461}], "counters": [{"opponent": "quagsire_shadow", "rating": 114}, {"opponent": "swampert_shadow", "rating": 158}, {"opponent": "magcargo", "rating": 235}, {"opponent": "clodsire", "rating": 314}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 20596}, {"moveId": "SNARL", "uses": 19977}, {"moveId": "FIRE_FANG", "uses": 17692}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 16883}, {"moveId": "BLAZE_KICK", "uses": 12387}, {"moveId": "DARKEST_LARIAT", "uses": 11662}, {"moveId": "DARK_PULSE", "uses": 10691}, {"moveId": "FLAME_CHARGE", "uses": 4738}, {"moveId": "FIRE_BLAST", "uses": 2119}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 54}, {"speciesId": "geodude_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 539, "matchups": [{"opponent": "talonflame", "rating": 893, "opRating": 106}, {"opponent": "skeledirge", "rating": 836, "opRating": 163}, {"opponent": "ninetales_shadow", "rating": 805, "opRating": 194}, {"opponent": "magcargo", "rating": 761, "opRating": 238}, {"opponent": "jumpluff_shadow", "rating": 619, "opRating": 380}], "counters": [{"opponent": "quagsire_shadow", "rating": 198}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "clodsire", "rating": 286}, {"opponent": "cradily", "rating": 298}, {"opponent": "gligar", "rating": 324}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32785}, {"moveId": "TACKLE", "uses": 25515}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 26256}, {"moveId": "ROCK_SLIDE", "uses": 16848}, {"moveId": "DIG", "uses": 15153}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 53.9}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 647, "matchups": [{"opponent": "piloswine", "rating": 840, "opRating": 160}, {"opponent": "cradily", "rating": 735, "opRating": 265}, {"opponent": "furret", "rating": 690, "opRating": 310}, {"opponent": "drampa", "rating": 570, "opRating": 430}, {"opponent": "flygon", "rating": 515, "opRating": 485}], "counters": [{"opponent": "claydol", "rating": 206}, {"opponent": "diggersby", "rating": 209}, {"opponent": "clodsire", "rating": 221}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 278}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30695}, {"moveId": "FIRE_SPIN", "uses": 27605}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21622}, {"moveId": "FLAMETHROWER", "uses": 12166}, {"moveId": "OVERHEAT", "uses": 11302}, {"moveId": "LAST_RESORT", "uses": 8042}, {"moveId": "FIRE_BLAST", "uses": 3189}, {"moveId": "HEAT_WAVE", "uses": 1932}]}, "moveset": ["EMBER", "SUPER_POWER", "FLAMETHROWER"], "score": 53.7}, {"speciesId": "drilbur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 548, "matchups": [{"opponent": "magcargo", "rating": 725, "opRating": 274}, {"opponent": "skeledirge", "rating": 700, "opRating": 299}, {"opponent": "clodsire", "rating": 697, "opRating": 302}, {"opponent": "claydol", "rating": 573, "opRating": 426}, {"opponent": "diggersby", "rating": 503, "opRating": 496}], "counters": [{"opponent": "pidgeot", "rating": 172}, {"opponent": "talonflame", "rating": 270}, {"opponent": "furret", "rating": 331}, {"opponent": "cradily", "rating": 336}, {"opponent": "gligar", "rating": 381}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45588}, {"moveId": "SCRATCH", "uses": 12712}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27213}, {"moveId": "ROCK_TOMB", "uses": 24110}, {"moveId": "DIG", "uses": 6837}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_TOMB"], "score": 53.6}, {"speciesId": "oddish", "speciesName": "<PERSON><PERSON>", "rating": 619, "matchups": [{"opponent": "jumpluff_shadow", "rating": 608, "opRating": 391}, {"opponent": "drampa", "rating": 541, "opRating": 458}, {"opponent": "swampert_shadow", "rating": 525, "opRating": 475}, {"opponent": "talonflame", "rating": 512}, {"opponent": "diggersby", "rating": 512, "opRating": 487}], "counters": [{"opponent": "clodsire", "rating": 189}, {"opponent": "ninetales_shadow", "rating": 218}, {"opponent": "magcargo", "rating": 269}, {"opponent": "gligar", "rating": 305}, {"opponent": "cradily", "rating": 375}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 40738}, {"moveId": "RAZOR_LEAF", "uses": 17562}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19566}, {"moveId": "SEED_BOMB", "uses": 18729}, {"moveId": "MOONBLAST", "uses": 11560}, {"moveId": "RETURN", "uses": 8391}]}, "moveset": ["ACID", "SEED_BOMB", "SLUDGE_BOMB"], "score": 53.6}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 592, "matchups": [{"opponent": "swampert_shadow", "rating": 897, "opRating": 102}, {"opponent": "claydol", "rating": 841, "opRating": 158}, {"opponent": "diggersby", "rating": 610, "opRating": 389}, {"opponent": "talonflame", "rating": 599, "opRating": 400}, {"opponent": "furret", "rating": 503, "opRating": 496}], "counters": [{"opponent": "typhlosion_shadow", "rating": 158}, {"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "cradily", "rating": 319}, {"opponent": "clodsire", "rating": 326}, {"opponent": "gligar", "rating": 381}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33743}, {"moveId": "INFESTATION", "uses": 24557}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20026}, {"moveId": "ROCK_SLIDE", "uses": 14920}, {"moveId": "SLUDGE_BOMB", "uses": 11977}, {"moveId": "ANCIENT_POWER", "uses": 7169}, {"moveId": "SOLAR_BEAM", "uses": 4215}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 53.6}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 635, "matchups": [{"opponent": "claydol", "rating": 820, "opRating": 179}, {"opponent": "cradily", "rating": 699, "opRating": 300}, {"opponent": "flygon", "rating": 578, "opRating": 421}, {"opponent": "furret", "rating": 531, "opRating": 468}, {"opponent": "clodsire", "rating": 503, "opRating": 496}], "counters": [{"opponent": "skeledirge", "rating": 190}, {"opponent": "magcargo", "rating": 226}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 248}, {"opponent": "talonflame", "rating": 274}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 32883}, {"moveId": "CONFUSION", "uses": 25417}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 27253}, {"moveId": "BULLDOZE", "uses": 21209}, {"moveId": "PSYBEAM", "uses": 9838}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BULLDOZE"], "score": 53.5}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 558, "matchups": [{"opponent": "staraptor", "rating": 744, "opRating": 255}, {"opponent": "diggersby", "rating": 614, "opRating": 385}, {"opponent": "typhlosion_shadow", "rating": 583, "opRating": 416}, {"opponent": "claydol", "rating": 511, "opRating": 488}, {"opponent": "quagsire_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "clodsire", "rating": 262}, {"opponent": "gligar", "rating": 270}, {"opponent": "furret", "rating": 287}, {"opponent": "cradily", "rating": 288}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33068}, {"moveId": "BULLET_SEED", "uses": 25232}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20275}, {"moveId": "DRAGON_PULSE", "uses": 16872}, {"moveId": "DRACO_METEOR", "uses": 14995}, {"moveId": "SOLAR_BEAM", "uses": 6035}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 53.3}, {"speciesId": "growl<PERSON>e_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 656, "matchups": [{"opponent": "talonflame", "rating": 869, "opRating": 130}, {"opponent": "skeledirge", "rating": 869, "opRating": 130}, {"opponent": "jumpluff_shadow", "rating": 767, "opRating": 232}, {"opponent": "drampa", "rating": 591, "opRating": 408}, {"opponent": "furret", "rating": 514, "opRating": 485}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "claydol", "rating": 169}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "diggersby", "rating": 209}, {"opponent": "clodsire", "rating": 221}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43320}, {"moveId": "BITE", "uses": 14980}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 21936}, {"moveId": "CRUNCH", "uses": 18349}, {"moveId": "FLAMETHROWER", "uses": 18031}]}, "moveset": ["EMBER", "ROCK_SLIDE", "FLAMETHROWER"], "score": 53}, {"speciesId": "herdier", "speciesName": "<PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "staravia", "rating": 785, "opRating": 214}, {"opponent": "magcargo", "rating": 771, "opRating": 228}, {"opponent": "skeledirge", "rating": 739, "opRating": 260}, {"opponent": "clodsire", "rating": 619, "opRating": 380}, {"opponent": "ninetales_shadow", "rating": 563, "opRating": 436}], "counters": [{"opponent": "gligar", "rating": 236}, {"opponent": "claydol", "rating": 247}, {"opponent": "jumpluff_shadow", "rating": 281}, {"opponent": "furret", "rating": 340}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 29860}, {"moveId": "LICK", "uses": 20054}, {"moveId": "TAKE_DOWN", "uses": 8396}], "chargedMoves": [{"moveId": "DIG", "uses": 23527}, {"moveId": "PLAY_ROUGH", "uses": 18258}, {"moveId": "THUNDERBOLT", "uses": 16542}]}, "moveset": ["SAND_ATTACK", "DIG", "THUNDERBOLT"], "score": 52.8}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 636, "matchups": [{"opponent": "piloswine", "rating": 826, "opRating": 173}, {"opponent": "skeledirge", "rating": 720, "opRating": 279}, {"opponent": "typhlosion_shadow", "rating": 656, "opRating": 343}, {"opponent": "pidgeot", "rating": 614, "opRating": 385}, {"opponent": "furret", "rating": 593, "opRating": 406}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "diggersby", "rating": 244}, {"opponent": "clodsire", "rating": 252}, {"opponent": "cradily", "rating": 277}, {"opponent": "gligar", "rating": 278}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 29479}, {"moveId": "SNARL", "uses": 28821}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 25751}, {"moveId": "FLAMETHROWER", "uses": 16163}, {"moveId": "CRUNCH", "uses": 12060}, {"moveId": "FIRE_BLAST", "uses": 4312}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 52.8}, {"speciesId": "phanpy_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 542, "matchups": [{"opponent": "staravia_shadow", "rating": 760, "opRating": 239}, {"opponent": "salazzle", "rating": 685, "opRating": 314}, {"opponent": "flygon_shadow", "rating": 567, "opRating": 432}, {"opponent": "clodsire", "rating": 564, "opRating": 435}, {"opponent": "quagsire_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "magcargo", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 300}, {"opponent": "cradily", "rating": 315}, {"opponent": "talonflame", "rating": 403}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 38683}, {"moveId": "ROCK_SMASH", "uses": 19617}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 16611}, {"moveId": "BODY_SLAM", "uses": 16082}, {"moveId": "ROCK_SLIDE", "uses": 15009}, {"moveId": "BULLDOZE", "uses": 10659}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "TRAILBLAZE", "BODY_SLAM"], "score": 52.8}, {"speciesId": "cacturne", "speciesName": "Cacturne", "rating": 623, "matchups": [{"opponent": "claydol", "rating": 764, "opRating": 235}, {"opponent": "quagsire_shadow", "rating": 652, "opRating": 347}, {"opponent": "cradily", "rating": 632, "opRating": 367}, {"opponent": "furret", "rating": 545, "opRating": 454}, {"opponent": "flygon", "rating": 533, "opRating": 466}], "counters": [{"opponent": "diggersby", "rating": 204}, {"opponent": "gligar", "rating": 229}, {"opponent": "magcargo", "rating": 235}, {"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "talonflame", "rating": 303}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 25676}, {"moveId": "SAND_ATTACK", "uses": 16977}, {"moveId": "POISON_JAB", "uses": 15623}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 17246}, {"moveId": "TRAILBLAZE", "uses": 13466}, {"moveId": "DARK_PULSE", "uses": 11939}, {"moveId": "PAYBACK", "uses": 5396}, {"moveId": "GRASS_KNOT", "uses": 5351}, {"moveId": "RETURN", "uses": 4857}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DYNAMIC_PUNCH"], "score": 52.5}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 596, "matchups": [{"opponent": "clodsire", "rating": 899, "opRating": 100}, {"opponent": "swampert_shadow", "rating": 822, "opRating": 177}, {"opponent": "drampa", "rating": 614, "opRating": 385}, {"opponent": "diggersby", "rating": 562, "opRating": 437}, {"opponent": "magcargo", "rating": 531, "opRating": 468}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "skeledirge", "rating": 190}, {"opponent": "furret", "rating": 237}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "talonflame", "rating": 466}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 43092}, {"moveId": "ROCK_SMASH", "uses": 15208}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 12046}, {"moveId": "SWIFT", "uses": 11570}, {"moveId": "TRAILBLAZE", "uses": 8083}, {"moveId": "FIRE_PUNCH", "uses": 7469}, {"moveId": "AERIAL_ACE", "uses": 7155}, {"moveId": "HIGH_HORSEPOWER", "uses": 6737}, {"moveId": "THUNDER_PUNCH", "uses": 5302}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 52.4}, {"speciesId": "charmeleon", "speciesName": "Charmeleon", "rating": 668, "matchups": [{"opponent": "abomasnow_shadow", "rating": 886, "opRating": 113}, {"opponent": "piloswine", "rating": 878, "opRating": 121}, {"opponent": "jumpluff_shadow", "rating": 746, "opRating": 253}, {"opponent": "pidgeot", "rating": 519, "opRating": 480}, {"opponent": "furret", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "talonflame", "rating": 303}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25740}, {"moveId": "FIRE_FANG", "uses": 22559}, {"moveId": "SCRATCH", "uses": 9994}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 31023}, {"moveId": "RETURN", "uses": 11684}, {"moveId": "FLAMETHROWER", "uses": 9678}, {"moveId": "FLAME_BURST", "uses": 5861}]}, "moveset": ["EMBER", "FIRE_PUNCH", "RETURN"], "score": 52.2}, {"speciesId": "drilbur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 565, "matchups": [{"opponent": "clodsire", "rating": 869, "opRating": 130}, {"opponent": "typhlosion_shadow", "rating": 778, "opRating": 221}, {"opponent": "magcargo", "rating": 764, "opRating": 235}, {"opponent": "skeledirge", "rating": 742, "opRating": 257}, {"opponent": "ninetales_shadow", "rating": 658, "opRating": 341}], "counters": [{"opponent": "pidgeot", "rating": 144}, {"opponent": "talonflame", "rating": 240}, {"opponent": "cradily", "rating": 288}, {"opponent": "furret", "rating": 328}, {"opponent": "gligar", "rating": 332}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 44495}, {"moveId": "SCRATCH", "uses": 13805}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 24028}, {"moveId": "ROCK_TOMB", "uses": 20956}, {"moveId": "RETURN", "uses": 7250}, {"moveId": "DIG", "uses": 6089}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_TOMB"], "score": 51.9}, {"speciesId": "quilava", "speciesName": "Quilava", "rating": 627, "matchups": [{"opponent": "piloswine", "rating": 726, "opRating": 273}, {"opponent": "ninetales_shadow", "rating": 628, "opRating": 371}, {"opponent": "gliscor", "rating": 613, "opRating": 386}, {"opponent": "jumpluff_shadow", "rating": 546, "opRating": 453}, {"opponent": "furret", "rating": 535, "opRating": 464}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "swampert_shadow", "rating": 202}, {"opponent": "flygon", "rating": 216}, {"opponent": "talonflame", "rating": 348}, {"opponent": "cradily", "rating": 388}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38429}, {"moveId": "TACKLE", "uses": 19871}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23331}, {"moveId": "DIG", "uses": 15304}, {"moveId": "RETURN", "uses": 9998}, {"moveId": "FLAMETHROWER", "uses": 9705}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 51.9}, {"speciesId": "fuecoco", "speciesName": "Fuecoco", "rating": 654, "matchups": [{"opponent": "abomasnow_shadow", "rating": 878, "opRating": 121}, {"opponent": "piloswine", "rating": 865, "opRating": 134}, {"opponent": "jumpluff_shadow", "rating": 720, "opRating": 279}, {"opponent": "drampa", "rating": 651, "opRating": 348}, {"opponent": "flygon_shadow", "rating": 605, "opRating": 394}], "counters": [{"opponent": "quagsire_shadow", "rating": 114}, {"opponent": "magcargo", "rating": 149}, {"opponent": "swampert_shadow", "rating": 180}, {"opponent": "talonflame", "rating": 251}, {"opponent": "cradily", "rating": 347}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 43894}, {"moveId": "BITE", "uses": 14406}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21903}, {"moveId": "FLAMETHROWER", "uses": 19695}, {"moveId": "DISARMING_VOICE", "uses": 16798}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 51.8}, {"speciesId": "quilava_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 609, "matchups": [{"opponent": "abomasnow_shadow", "rating": 886, "opRating": 113}, {"opponent": "clodsire", "rating": 792, "opRating": 207}, {"opponent": "piloswine", "rating": 671, "opRating": 328}, {"opponent": "gliscor", "rating": 578, "opRating": 421}, {"opponent": "ninetales_shadow", "rating": 554, "opRating": 445}], "counters": [{"opponent": "magcargo", "rating": 158}, {"opponent": "quagsire_shadow", "rating": 180}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "flygon", "rating": 244}, {"opponent": "gligar", "rating": 324}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 39227}, {"moveId": "TACKLE", "uses": 19073}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 28023}, {"moveId": "DIG", "uses": 18533}, {"moveId": "FLAMETHROWER", "uses": 11744}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 51.8}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 633, "matchups": [{"opponent": "quagsire_shadow", "rating": 898, "opRating": 101}, {"opponent": "swampert_shadow", "rating": 884, "opRating": 115}, {"opponent": "claydol", "rating": 744, "opRating": 255}, {"opponent": "drampa", "rating": 555, "opRating": 444}, {"opponent": "furret", "rating": 527, "opRating": 472}], "counters": [{"opponent": "typhlosion_shadow", "rating": 123}, {"opponent": "magcargo", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "gligar", "rating": 332}, {"opponent": "talonflame", "rating": 362}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 19765}, {"moveId": "BULLET_SEED", "uses": 19176}, {"moveId": "FEINT_ATTACK", "uses": 13076}, {"moveId": "RAZOR_LEAF", "uses": 6266}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 27111}, {"moveId": "FOUL_PLAY", "uses": 17530}, {"moveId": "HURRICANE", "uses": 8452}, {"moveId": "LEAF_TORNADO", "uses": 5183}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 51.7}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 604, "matchups": [{"opponent": "magcargo", "rating": 764, "opRating": 235}, {"opponent": "cradily", "rating": 743, "opRating": 256}, {"opponent": "diggersby", "rating": 676, "opRating": 323}, {"opponent": "furret", "rating": 567, "opRating": 432}, {"opponent": "flygon", "rating": 529, "opRating": 470}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "talonflame", "rating": 211}, {"opponent": "claydol", "rating": 219}, {"opponent": "swampert_shadow", "rating": 235}, {"opponent": "gligar", "rating": 278}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45738}, {"moveId": "ROCK_SMASH", "uses": 12562}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28476}, {"moveId": "BLAST_BURN", "uses": 20533}, {"moveId": "FLAMETHROWER", "uses": 4736}, {"moveId": "SOLAR_BEAM", "uses": 4505}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 51.3}, {"speciesId": "chansey", "speciesName": "<PERSON><PERSON>", "rating": 500, "matchups": [{"opponent": "bewear", "rating": 666, "opRating": 333}, {"opponent": "flygon_shadow", "rating": 597, "opRating": 402}, {"opponent": "drampa", "rating": 577, "opRating": 422}, {"opponent": "clodsire", "rating": 555, "opRating": 444}, {"opponent": "gligar", "rating": 538, "opRating": 461}], "counters": [{"opponent": "claydol", "rating": 235}, {"opponent": "cradily", "rating": 250}, {"opponent": "magcargo", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "talonflame", "rating": 340}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 32774}, {"moveId": "POUND", "uses": 25526}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 19994}, {"moveId": "PSYCHIC", "uses": 16784}, {"moveId": "HYPER_BEAM", "uses": 16592}, {"moveId": "PSYBEAM", "uses": 5008}]}, "moveset": ["ZEN_HEADBUTT", "DAZZLING_GLEAM", "PSYCHIC"], "score": 50.8}, {"speciesId": "stant<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 523, "matchups": [{"opponent": "snor<PERSON>_shadow", "rating": 808, "opRating": 191}, {"opponent": "staravia", "rating": 667, "opRating": 332}, {"opponent": "staraptor", "rating": 667, "opRating": 332}, {"opponent": "talonflame", "rating": 621}, {"opponent": "pidgeot", "rating": 574, "opRating": 425}], "counters": [{"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "skeledirge", "rating": 258}, {"opponent": "quagsire_shadow", "rating": 263}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 270}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 49355}, {"moveId": "ZEN_HEADBUTT", "uses": 8945}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20295}, {"moveId": "STOMP", "uses": 19506}, {"moveId": "MEGAHORN", "uses": 18477}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 50.8}, {"speciesId": "cacturne_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 609, "matchups": [{"opponent": "swampert_shadow", "rating": 789, "opRating": 210}, {"opponent": "claydol", "rating": 735, "opRating": 264}, {"opponent": "clodsire", "rating": 677, "opRating": 322}, {"opponent": "quagsire_shadow", "rating": 619, "opRating": 380}, {"opponent": "cradily", "rating": 541, "opRating": 458}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "gligar", "rating": 232}, {"opponent": "diggersby", "rating": 232}, {"opponent": "jumpluff_shadow", "rating": 294}, {"opponent": "magcargo", "rating": 311}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 26235}, {"moveId": "SAND_ATTACK", "uses": 16525}, {"moveId": "POISON_JAB", "uses": 15482}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 18852}, {"moveId": "TRAILBLAZE", "uses": 14431}, {"moveId": "DARK_PULSE", "uses": 13180}, {"moveId": "PAYBACK", "uses": 5899}, {"moveId": "GRASS_KNOT", "uses": 5772}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DYNAMIC_PUNCH"], "score": 50.7}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 582, "matchups": [{"opponent": "clodsire", "rating": 953, "opRating": 46}, {"opponent": "cradily", "rating": 910}, {"opponent": "flygon", "rating": 682, "opRating": 317}, {"opponent": "magcargo", "rating": 610, "opRating": 389}, {"opponent": "furret", "rating": 539, "opRating": 460}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "talonflame", "rating": 151}, {"opponent": "claydol", "rating": 227}, {"opponent": "quagsire_shadow", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22892}, {"moveId": "MUD_SHOT", "uses": 18764}, {"moveId": "METAL_CLAW", "uses": 16652}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20725}, {"moveId": "ROCK_SLIDE", "uses": 13670}, {"moveId": "IRON_HEAD", "uses": 11611}, {"moveId": "SCORCHING_SANDS", "uses": 7851}, {"moveId": "EARTHQUAKE", "uses": 4479}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_SLIDE"], "score": 50.7}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 664, "matchups": [{"opponent": "swampert_shadow", "rating": 875, "opRating": 125}, {"opponent": "furret", "rating": 733, "opRating": 266}, {"opponent": "diggersby", "rating": 669, "opRating": 330}, {"opponent": "clodsire", "rating": 629, "opRating": 370}, {"opponent": "cradily", "rating": 520}], "counters": [{"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "talonflame", "rating": 214}, {"opponent": "skeledirge", "rating": 316}, {"opponent": "gligar", "rating": 404}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 35092}, {"moveId": "SMACK_DOWN", "uses": 16783}, {"moveId": "LOW_KICK", "uses": 6409}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21145}, {"moveId": "FRENZY_PLANT", "uses": 19160}, {"moveId": "THUNDER_PUNCH", "uses": 8817}, {"moveId": "ENERGY_BALL", "uses": 3644}, {"moveId": "GYRO_BALL", "uses": 3495}, {"moveId": "SOLAR_BEAM", "uses": 2195}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SUPER_POWER"], "score": 50.6}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 578, "matchups": [{"opponent": "ninetales_shadow", "rating": 648, "opRating": 351}, {"opponent": "gligar", "rating": 625, "opRating": 375}, {"opponent": "jumpluff_shadow", "rating": 621, "opRating": 378}, {"opponent": "skeledirge", "rating": 562, "opRating": 437}, {"opponent": "furret", "rating": 503, "opRating": 496}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "claydol", "rating": 223}, {"opponent": "talonflame", "rating": 240}, {"opponent": "flygon", "rating": 244}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30718}, {"moveId": "FIRE_SPIN", "uses": 27582}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 28560}, {"moveId": "EARTHQUAKE", "uses": 17428}, {"moveId": "SOLAR_BEAM", "uses": 12323}]}, "moveset": ["EMBER", "OVERHEAT", "EARTHQUAKE"], "score": 50.6}, {"speciesId": "excadrill_shadow", "speciesName": "Excadrill (Shadow)", "rating": 575, "matchups": [{"opponent": "cradily", "rating": 910}, {"opponent": "clodsire", "rating": 910, "opRating": 89}, {"opponent": "flygon", "rating": 625, "opRating": 375}, {"opponent": "magcargo", "rating": 539, "opRating": 460}, {"opponent": "diggersby", "rating": 510, "opRating": 489}], "counters": [{"opponent": "talonflame", "rating": 137}, {"opponent": "gligar", "rating": 156}, {"opponent": "claydol", "rating": 210}, {"opponent": "furret", "rating": 353}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23170}, {"moveId": "MUD_SHOT", "uses": 18739}, {"moveId": "METAL_CLAW", "uses": 16386}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20723}, {"moveId": "ROCK_SLIDE", "uses": 13666}, {"moveId": "IRON_HEAD", "uses": 11644}, {"moveId": "SCORCHING_SANDS", "uses": 7840}, {"moveId": "EARTHQUAKE", "uses": 4423}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_SLIDE"], "score": 50.4}, {"speciesId": "dug<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 595, "matchups": [{"opponent": "clodsire", "rating": 885, "opRating": 115}, {"opponent": "magcargo", "rating": 730, "opRating": 270}, {"opponent": "skeledirge", "rating": 710, "opRating": 290}, {"opponent": "flygon", "rating": 540, "opRating": 460}, {"opponent": "talonflame", "rating": 520, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "gligar", "rating": 137}, {"opponent": "gliscor", "rating": 155}, {"opponent": "furret", "rating": 331}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 21566}, {"moveId": "MUD_SLAP", "uses": 20625}, {"moveId": "MUD_SHOT", "uses": 16123}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 29668}, {"moveId": "STONE_EDGE", "uses": 21426}, {"moveId": "EARTHQUAKE", "uses": 7080}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "STONE_EDGE"], "score": 50.3}, {"speciesId": "houndour_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 592, "matchups": [{"opponent": "piloswine", "rating": 787, "opRating": 212}, {"opponent": "talonflame", "rating": 695, "opRating": 304}, {"opponent": "skeledirge", "rating": 695, "opRating": 304}, {"opponent": "cradily", "rating": 670, "opRating": 329}, {"opponent": "typhlosion_shadow", "rating": 612, "opRating": 387}], "counters": [{"opponent": "claydol", "rating": 202}, {"opponent": "diggersby", "rating": 244}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "gligar", "rating": 270}, {"opponent": "furret", "rating": 284}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 35923}, {"moveId": "FEINT_ATTACK", "uses": 22377}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28156}, {"moveId": "FLAMETHROWER", "uses": 19176}, {"moveId": "DARK_PULSE", "uses": 10976}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 50.3}, {"speciesId": "geodude", "speciesName": "Geodude", "rating": 515, "matchups": [{"opponent": "talonflame", "rating": 867, "opRating": 132}, {"opponent": "skeledirge", "rating": 809, "opRating": 190}, {"opponent": "ninetales_shadow", "rating": 694, "opRating": 305}, {"opponent": "jumpluff_shadow", "rating": 650, "opRating": 349}, {"opponent": "magcargo", "rating": 561, "opRating": 438}], "counters": [{"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 301}, {"opponent": "furret", "rating": 303}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32634}, {"moveId": "TACKLE", "uses": 25666}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 23338}, {"moveId": "ROCK_SLIDE", "uses": 15097}, {"moveId": "DIG", "uses": 13314}, {"moveId": "RETURN", "uses": 6525}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 50.2}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 634, "matchups": [{"opponent": "claydol", "rating": 862, "opRating": 137}, {"opponent": "diggersby", "rating": 660, "opRating": 339}, {"opponent": "magcargo", "rating": 614, "opRating": 385}, {"opponent": "clodsire", "rating": 564, "opRating": 435}, {"opponent": "cradily", "rating": 526, "opRating": 473}], "counters": [{"opponent": "abomasnow_shadow", "rating": 97}, {"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "typhlosion_shadow", "rating": 132}, {"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "talonflame", "rating": 203}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 27093}, {"moveId": "MAGICAL_LEAF", "uses": 21025}, {"moveId": "RAZOR_LEAF", "uses": 10211}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 31143}, {"moveId": "EARTHQUAKE", "uses": 9807}, {"moveId": "RETURN", "uses": 9528}, {"moveId": "PETAL_BLIZZARD", "uses": 4338}, {"moveId": "SOLAR_BEAM", "uses": 3546}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 50.2}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 598, "matchups": [{"opponent": "abomasnow_shadow", "rating": 900, "opRating": 99}, {"opponent": "skeledirge", "rating": 789, "opRating": 210}, {"opponent": "piloswine", "rating": 658, "opRating": 341}, {"opponent": "pidgeot", "rating": 615, "opRating": 384}, {"opponent": "gliscor", "rating": 599, "opRating": 400}], "counters": [{"opponent": "swampert_shadow", "rating": 235}, {"opponent": "magcargo", "rating": 243}, {"opponent": "furret", "rating": 265}, {"opponent": "gligar", "rating": 278}, {"opponent": "cradily", "rating": 357}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 40255}, {"moveId": "BITE", "uses": 18045}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 27933}, {"moveId": "FLAMETHROWER", "uses": 23887}, {"moveId": "FIRE_BLAST", "uses": 6516}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 50.2}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 586, "matchups": [{"opponent": "piloswine", "rating": 844, "opRating": 155}, {"opponent": "cradily", "rating": 743, "opRating": 256}, {"opponent": "magcargo", "rating": 722, "opRating": 277}, {"opponent": "diggersby", "rating": 605, "opRating": 394}, {"opponent": "typhlosion_shadow", "rating": 558, "opRating": 441}], "counters": [{"opponent": "flygon", "rating": 220}, {"opponent": "talonflame", "rating": 240}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "clodsire", "rating": 262}, {"opponent": "gligar", "rating": 309}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46959}, {"moveId": "ROCK_SMASH", "uses": 11341}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28466}, {"moveId": "BLAST_BURN", "uses": 20514}, {"moveId": "FLAMETHROWER", "uses": 4737}, {"moveId": "SOLAR_BEAM", "uses": 4526}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 50}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 614, "matchups": [{"opponent": "swampert_shadow", "rating": 893, "opRating": 106}, {"opponent": "diggersby", "rating": 843, "opRating": 156}, {"opponent": "claydol", "rating": 835, "opRating": 164}, {"opponent": "furret", "rating": 557, "opRating": 442}, {"opponent": "clodsire", "rating": 503, "opRating": 496}], "counters": [{"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "jumpluff_shadow", "rating": 169}, {"opponent": "gliscor", "rating": 176}, {"opponent": "talonflame", "rating": 348}, {"opponent": "cradily", "rating": 357}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 27147}, {"moveId": "MAGICAL_LEAF", "uses": 21180}, {"moveId": "RAZOR_LEAF", "uses": 9958}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 36924}, {"moveId": "EARTHQUAKE", "uses": 11888}, {"moveId": "PETAL_BLIZZARD", "uses": 5213}, {"moveId": "SOLAR_BEAM", "uses": 4117}, {"moveId": "FRUSTRATION", "uses": 21}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 50}, {"speciesId": "stantler", "speciesName": "<PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "talonflame", "rating": 608, "opRating": 391}, {"opponent": "flygon", "rating": 562, "opRating": 437}, {"opponent": "drampa", "rating": 531, "opRating": 468}, {"opponent": "piloswine", "rating": 523, "opRating": 476}, {"opponent": "gliscor", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 183}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "furret", "rating": 237}, {"opponent": "cradily", "rating": 256}, {"opponent": "swampert_shadow", "rating": 312}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 49008}, {"moveId": "ZEN_HEADBUTT", "uses": 9292}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17396}, {"moveId": "STOMP", "uses": 15954}, {"moveId": "MEGAHORN", "uses": 15573}, {"moveId": "RETURN", "uses": 9354}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 50}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 564, "matchups": [{"opponent": "talonflame", "rating": 714, "opRating": 285}, {"opponent": "ninetales_shadow", "rating": 690, "opRating": 309}, {"opponent": "jumpluff_shadow", "rating": 633, "opRating": 366}, {"opponent": "skeledirge", "rating": 633, "opRating": 366}, {"opponent": "drampa", "rating": 538, "opRating": 461}], "counters": [{"opponent": "diggersby", "rating": 152}, {"opponent": "claydol", "rating": 177}, {"opponent": "clodsire", "rating": 257}, {"opponent": "gligar", "rating": 267}, {"opponent": "cradily", "rating": 357}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 20208}, {"moveId": "VOLT_SWITCH", "uses": 19390}, {"moveId": "MUD_SLAP", "uses": 18719}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 20243}, {"moveId": "PARABOLIC_CHARGE", "uses": 15635}, {"moveId": "GRASS_KNOT", "uses": 12143}, {"moveId": "BULLDOZE", "uses": 6580}, {"moveId": "THUNDERBOLT", "uses": 3739}]}, "moveset": ["VOLT_SWITCH", "BREAKING_SWIPE", "PARABOLIC_CHARGE"], "score": 49.6}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 605, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "lura<PERSON>s", "rating": 866, "opRating": 133}, {"opponent": "piloswine", "rating": 852, "opRating": 147}, {"opponent": "piloswine_shadow", "rating": 852, "opRating": 147}, {"opponent": "victreebel", "rating": 766, "opRating": 233}], "counters": [{"opponent": "magcargo", "rating": 158}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "clodsire", "rating": 283}, {"opponent": "talonflame", "rating": 292}, {"opponent": "cradily", "rating": 298}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31561}, {"moveId": "FIRE_FANG", "uses": 26739}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 15277}, {"moveId": "FLAME_CHARGE", "uses": 14787}, {"moveId": "OVERHEAT", "uses": 11457}, {"moveId": "IRON_HEAD", "uses": 7243}, {"moveId": "FLAMETHROWER", "uses": 6068}, {"moveId": "FIRE_BLAST", "uses": 3334}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 49.5}, {"speciesId": "phanpy", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 507, "matchups": [{"opponent": "hippow<PERSON>_shadow", "rating": 803, "opRating": 196}, {"opponent": "nidoqueen_shadow", "rating": 647, "opRating": 352}, {"opponent": "ursaring", "rating": 615, "opRating": 384}, {"opponent": "quagsire_shadow", "rating": 588, "opRating": 411}, {"opponent": "magmar_shadow", "rating": 588, "opRating": 411}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "talonflame", "rating": 292}, {"opponent": "furret", "rating": 306}, {"opponent": "cradily", "rating": 336}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 38486}, {"moveId": "ROCK_SMASH", "uses": 19814}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 15669}, {"moveId": "BODY_SLAM", "uses": 14843}, {"moveId": "ROCK_SLIDE", "uses": 13997}, {"moveId": "BULLDOZE", "uses": 10025}, {"moveId": "RETURN", "uses": 3725}]}, "moveset": ["TACKLE", "TRAILBLAZE", "BODY_SLAM"], "score": 49.5}, {"speciesId": "tranquill", "speciesName": "Tran<PERSON>ll", "rating": 616, "matchups": [{"opponent": "zangoose", "rating": 821, "opRating": 178}, {"opponent": "gliscor_shadow", "rating": 601, "opRating": 398}, {"opponent": "swampert_shadow", "rating": 587, "opRating": 412}, {"opponent": "jumpluff_shadow", "rating": 538, "opRating": 461}, {"opponent": "claydol", "rating": 510, "opRating": 489}], "counters": [{"opponent": "magcargo", "rating": 136}, {"opponent": "clodsire", "rating": 194}, {"opponent": "talonflame", "rating": 240}, {"opponent": "flygon", "rating": 260}, {"opponent": "cradily", "rating": 284}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32282}, {"moveId": "STEEL_WING", "uses": 26018}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 30317}, {"moveId": "RETURN", "uses": 13324}, {"moveId": "SKY_ATTACK", "uses": 10042}, {"moveId": "HEAT_WAVE", "uses": 4590}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "RETURN"], "score": 49.5}, {"speciesId": "dhelmise", "speciesName": "Dhelmise", "rating": 581, "matchups": [{"opponent": "swampert_shadow", "rating": 852, "opRating": 147}, {"opponent": "claydol", "rating": 795, "opRating": 204}, {"opponent": "quagsire_shadow", "rating": 709, "opRating": 290}, {"opponent": "gliscor", "rating": 709, "opRating": 290}, {"opponent": "flygon", "rating": 595, "opRating": 404}], "counters": [{"opponent": "magcargo", "rating": 217}, {"opponent": "talonflame", "rating": 218}, {"opponent": "furret", "rating": 259}, {"opponent": "gligar", "rating": 339}, {"opponent": "cradily", "rating": 385}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 22242}, {"moveId": "ASTONISH", "uses": 18057}, {"moveId": "METAL_SOUND", "uses": 17994}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18801}, {"moveId": "SHADOW_BALL", "uses": 15600}, {"moveId": "WRAP", "uses": 15166}, {"moveId": "HEAVY_SLAM", "uses": 8711}]}, "moveset": ["SHADOW_CLAW", "POWER_WHIP", "WRAP"], "score": 49.1}, {"speciesId": "porygon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 551, "matchups": [{"opponent": "staravia", "rating": 729, "opRating": 270}, {"opponent": "staraptor", "rating": 729, "opRating": 270}, {"opponent": "talonflame", "rating": 700, "opRating": 300}, {"opponent": "pidgeot", "rating": 640, "opRating": 359}, {"opponent": "drampa", "rating": 551, "opRating": 448}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "clodsire", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 315}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 5116}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4378}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3894}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3411}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3405}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3390}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3166}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3047}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3030}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2949}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2900}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2893}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2889}, {"moveId": "CHARGE_BEAM", "uses": 2803}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2654}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2583}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2427}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2054}, {"moveId": "ZEN_HEADBUTT", "uses": 1115}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 12927}, {"moveId": "DISCHARGE", "uses": 12228}, {"moveId": "HYPER_BEAM", "uses": 10404}, {"moveId": "SOLAR_BEAM", "uses": 9556}, {"moveId": "ZAP_CANNON", "uses": 6538}, {"moveId": "PSYBEAM", "uses": 6505}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "DISCHARGE", "SIGNAL_BEAM"], "score": 49.1}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 526, "matchups": [{"opponent": "claydol", "rating": 691, "opRating": 308}, {"opponent": "quagsire_shadow", "rating": 563, "opRating": 436}, {"opponent": "gliscor", "rating": 557, "opRating": 442}, {"opponent": "piloswine", "rating": 528, "opRating": 471}, {"opponent": "abomasnow_shadow", "rating": 515, "opRating": 484}], "counters": [{"opponent": "drampa", "rating": 178}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "diggersby", "rating": 252}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 371}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38816}, {"moveId": "BITE", "uses": 19484}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19545}, {"moveId": "BOOMBURST", "uses": 16259}, {"moveId": "DISARMING_VOICE", "uses": 15001}, {"moveId": "FIRE_BLAST", "uses": 7531}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "CRUNCH", "DISARMING_VOICE"], "score": 48.9}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 636, "matchups": [{"opponent": "piloswine", "rating": 810, "opRating": 189}, {"opponent": "jumpluff_shadow", "rating": 783, "opRating": 216}, {"opponent": "gliscor", "rating": 752, "opRating": 247}, {"opponent": "cradily", "rating": 585, "opRating": 414}, {"opponent": "furret", "rating": 522, "opRating": 477}], "counters": [{"opponent": "claydol", "rating": 119}, {"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "magcargo", "rating": 166}, {"opponent": "swampert_shadow", "rating": 180}, {"opponent": "talonflame", "rating": 451}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33628}, {"moveId": "BUG_BITE", "uses": 24672}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 19797}, {"moveId": "STONE_EDGE", "uses": 11690}, {"moveId": "EARTH_POWER", "uses": 9872}, {"moveId": "IRON_HEAD", "uses": 8674}, {"moveId": "FLAMETHROWER", "uses": 5400}, {"moveId": "FIRE_BLAST", "uses": 2843}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 48.9}, {"speciesId": "loudred_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 471, "matchups": [{"opponent": "gourgeist_average", "rating": 918, "opRating": 81}, {"opponent": "armarouge", "rating": 735, "opRating": 264}, {"opponent": "marowak_alolan_shadow", "rating": 710, "opRating": 289}, {"opponent": "staraptor", "rating": 693, "opRating": 306}, {"opponent": "skeledirge", "rating": 651, "opRating": 348}], "counters": [{"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 278}, {"opponent": "clodsire", "rating": 281}, {"opponent": "cradily", "rating": 284}, {"opponent": "talonflame", "rating": 400}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 32664}, {"moveId": "ROCK_SMASH", "uses": 25636}], "chargedMoves": [{"moveId": "STOMP", "uses": 24538}, {"moveId": "DISARMING_VOICE", "uses": 17968}, {"moveId": "FLAMETHROWER", "uses": 15705}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "STOMP", "DISARMING_VOICE"], "score": 48.9}, {"speciesId": "persian_shadow", "speciesName": "Persian (Shadow)", "rating": 550, "matchups": [{"opponent": "staraptor", "rating": 733, "opRating": 266}, {"opponent": "skeledirge", "rating": 704, "opRating": 295}, {"opponent": "typhlosion_shadow", "rating": 616, "opRating": 383}, {"opponent": "flygon_shadow", "rating": 562, "opRating": 437}, {"opponent": "drampa", "rating": 558, "opRating": 441}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "furret", "rating": 228}, {"opponent": "clodsire", "rating": 252}, {"opponent": "cradily", "rating": 340}, {"opponent": "talonflame", "rating": 381}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 32246}, {"moveId": "SCRATCH", "uses": 26054}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 21829}, {"moveId": "POWER_GEM", "uses": 15740}, {"moveId": "FOUL_PLAY", "uses": 8148}, {"moveId": "PLAY_ROUGH", "uses": 7184}, {"moveId": "PAYBACK", "uses": 5362}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "NIGHT_SLASH", "POWER_GEM"], "score": 48.9}, {"speciesId": "houndour", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 605, "matchups": [{"opponent": "skeledirge", "rating": 745, "opRating": 254}, {"opponent": "typhlosion_shadow", "rating": 679, "opRating": 320}, {"opponent": "jumpluff_shadow", "rating": 575, "opRating": 425}, {"opponent": "furret", "rating": 533, "opRating": 466}, {"opponent": "flygon", "rating": 529, "opRating": 470}], "counters": [{"opponent": "claydol", "rating": 206}, {"opponent": "diggersby", "rating": 209}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 255}, {"opponent": "magcargo", "rating": 277}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 35527}, {"moveId": "FEINT_ATTACK", "uses": 22773}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24276}, {"moveId": "FLAMETHROWER", "uses": 16651}, {"moveId": "DARK_PULSE", "uses": 9403}, {"moveId": "RETURN", "uses": 7927}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 48.8}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 569, "matchups": [{"opponent": "swampert_shadow", "rating": 878, "opRating": 121}, {"opponent": "diggersby", "rating": 849, "opRating": 150}, {"opponent": "claydol", "rating": 816, "opRating": 183}, {"opponent": "furret", "rating": 518, "opRating": 481}, {"opponent": "talonflame", "rating": 511}], "counters": [{"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "typhlosion_shadow", "rating": 158}, {"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "clodsire", "rating": 350}, {"opponent": "cradily", "rating": 392}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33934}, {"moveId": "INFESTATION", "uses": 24366}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20003}, {"moveId": "ROCK_SLIDE", "uses": 14956}, {"moveId": "SLUDGE_BOMB", "uses": 11978}, {"moveId": "ANCIENT_POWER", "uses": 7116}, {"moveId": "SOLAR_BEAM", "uses": 4217}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 48.8}, {"speciesId": "trapinch", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 531, "matchups": [{"opponent": "clodsire", "rating": 858, "opRating": 141}, {"opponent": "swampert_shadow", "rating": 745, "opRating": 254}, {"opponent": "magcargo", "rating": 645, "opRating": 354}, {"opponent": "skeledirge", "rating": 579, "opRating": 420}, {"opponent": "ninetales_shadow", "rating": 579, "opRating": 420}], "counters": [{"opponent": "quagsire_shadow", "rating": 180}, {"opponent": "jumpluff_shadow", "rating": 245}, {"opponent": "gligar", "rating": 259}, {"opponent": "cradily", "rating": 368}, {"opponent": "talonflame", "rating": 370}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24949}, {"moveId": "MUD_SHOT", "uses": 22103}, {"moveId": "STRUGGLE_BUG", "uses": 11250}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 18959}, {"moveId": "CRUNCH", "uses": 18167}, {"moveId": "RETURN", "uses": 8456}, {"moveId": "DIG", "uses": 6378}, {"moveId": "SAND_TOMB", "uses": 6264}]}, "moveset": ["SAND_ATTACK", "SCORCHING_SANDS", "CRUNCH"], "score": 48.8}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 545, "matchups": [{"opponent": "talonflame", "rating": 625, "opRating": 375}, {"opponent": "skeledirge", "rating": 600, "opRating": 399}, {"opponent": "flygon", "rating": 544, "opRating": 455}, {"opponent": "quagsire_shadow", "rating": 536, "opRating": 463}, {"opponent": "claydol", "rating": 512, "opRating": 487}], "counters": [{"opponent": "furret", "rating": 112}, {"opponent": "drampa", "rating": 134}, {"opponent": "diggersby", "rating": 232}, {"opponent": "clodsire", "rating": 329}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35279}, {"moveId": "SCRATCH", "uses": 23021}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28812}, {"moveId": "LOW_SWEEP", "uses": 16154}, {"moveId": "HYPER_BEAM", "uses": 13240}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 48.6}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 618, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "fletchinder", "rating": 772, "opRating": 227}, {"opponent": "pidgeot", "rating": 697, "opRating": 302}, {"opponent": "piloswine", "rating": 690, "opRating": 309}, {"opponent": "gliscor", "rating": 593, "opRating": 406}], "counters": [{"opponent": "swampert_shadow", "rating": 209}, {"opponent": "diggersby", "rating": 209}, {"opponent": "clodsire", "rating": 218}, {"opponent": "magcargo", "rating": 222}, {"opponent": "cradily", "rating": 256}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38162}, {"moveId": "LICK", "uses": 20138}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 27185}, {"moveId": "THUNDER_PUNCH", "uses": 18885}, {"moveId": "POWER_UP_PUNCH", "uses": 12248}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 48.5}, {"speciesId": "palossand", "speciesName": "Palossand", "rating": 521, "matchups": [{"opponent": "magcargo", "rating": 716, "opRating": 283}, {"opponent": "clodsire", "rating": 686, "opRating": 313}, {"opponent": "gligar", "rating": 630, "opRating": 369}, {"opponent": "jumpluff_shadow", "rating": 526, "opRating": 473}, {"opponent": "cradily", "rating": 522, "opRating": 477}], "counters": [{"opponent": "furret", "rating": 128}, {"opponent": "drampa", "rating": 200}, {"opponent": "diggersby", "rating": 290}, {"opponent": "talonflame", "rating": 300}, {"opponent": "swampert_shadow", "rating": 312}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20410}, {"moveId": "SAND_ATTACK", "uses": 19971}, {"moveId": "MUD_SHOT", "uses": 17820}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22475}, {"moveId": "SHADOW_BALL", "uses": 19521}, {"moveId": "EARTH_POWER", "uses": 8874}, {"moveId": "SAND_TOMB", "uses": 7525}]}, "moveset": ["ASTONISH", "SCORCHING_SANDS", "SHADOW_BALL"], "score": 48.5}, {"speciesId": "watchog_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 463, "matchups": [{"opponent": "gourgeist_average", "rating": 911, "opRating": 88}, {"opponent": "armarouge", "rating": 732, "opRating": 268}, {"opponent": "staraptor", "rating": 708, "opRating": 292}, {"opponent": "marowak_alolan_shadow", "rating": 708, "opRating": 292}, {"opponent": "skeledirge", "rating": 656, "opRating": 344}], "counters": [{"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 278}, {"opponent": "clodsire", "rating": 281}, {"opponent": "cradily", "rating": 284}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 37811}, {"moveId": "LOW_KICK", "uses": 20489}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21200}, {"moveId": "HYPER_FANG", "uses": 20071}, {"moveId": "GRASS_KNOT", "uses": 17019}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "HYPER_FANG"], "score": 48.2}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 520, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 697, "opRating": 302}, {"opponent": "abomasnow_shadow", "rating": 592, "opRating": 407}, {"opponent": "gliscor_shadow", "rating": 557, "opRating": 442}, {"opponent": "swampert_shadow", "rating": 509, "opRating": 490}, {"opponent": "claydol", "rating": 503, "opRating": 496}], "counters": [{"opponent": "drampa", "rating": 143}, {"opponent": "diggersby", "rating": 209}, {"opponent": "furret", "rating": 265}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 350}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 37265}, {"moveId": "BITE", "uses": 21035}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 17806}, {"moveId": "BOOMBURST", "uses": 14581}, {"moveId": "DISARMING_VOICE", "uses": 13639}, {"moveId": "FIRE_BLAST", "uses": 6903}, {"moveId": "RETURN", "uses": 5428}]}, "moveset": ["ASTONISH", "CRUNCH", "DISARMING_VOICE"], "score": 48.1}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 595, "matchups": [{"opponent": "cradily", "rating": 901, "opRating": 98}, {"opponent": "magcargo", "rating": 728, "opRating": 271}, {"opponent": "skeledirge", "rating": 712, "opRating": 287}, {"opponent": "furret", "rating": 704, "opRating": 295}, {"opponent": "flygon", "rating": 637, "opRating": 362}], "counters": [{"opponent": "claydol", "rating": 140}, {"opponent": "gligar", "rating": 160}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "diggersby", "rating": 238}, {"opponent": "talonflame", "rating": 311}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14707}, {"moveId": "FURY_CUTTER", "uses": 14432}, {"moveId": "DOUBLE_KICK", "uses": 13168}, {"moveId": "POISON_JAB", "uses": 12607}, {"moveId": "IRON_TAIL", "uses": 3351}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 16708}, {"moveId": "EARTH_POWER", "uses": 16426}, {"moveId": "SLUDGE_WAVE", "uses": 12308}, {"moveId": "SAND_TOMB", "uses": 6894}, {"moveId": "EARTHQUAKE", "uses": 5932}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 48.1}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 609, "matchups": [{"opponent": "piloswine", "rating": 879, "opRating": 120}, {"opponent": "ninetales_shadow", "rating": 763, "opRating": 236}, {"opponent": "gliscor", "rating": 670, "opRating": 329}, {"opponent": "jumpluff_shadow", "rating": 658, "opRating": 341}, {"opponent": "talonflame", "rating": 538, "opRating": 461}], "counters": [{"opponent": "magcargo", "rating": 158}, {"opponent": "claydol", "rating": 169}, {"opponent": "diggersby", "rating": 209}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "gligar", "rating": 278}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 25456}, {"moveId": "SNARL", "uses": 24241}, {"moveId": "ROCK_SMASH", "uses": 8614}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 15785}, {"moveId": "FLAMETHROWER", "uses": 14473}, {"moveId": "CRUNCH", "uses": 14306}, {"moveId": "WILD_CHARGE", "uses": 13750}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "ROCK_SLIDE"], "score": 48}, {"speciesId": "<PERSON><PERSON>o", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 601, "matchups": [{"opponent": "clodsire", "rating": 885, "opRating": 115}, {"opponent": "magcargo", "rating": 770, "opRating": 230}, {"opponent": "flygon", "rating": 610, "opRating": 390}, {"opponent": "drampa", "rating": 540, "opRating": 460}, {"opponent": "furret", "rating": 510, "opRating": 490}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 125}, {"opponent": "quagsire_shadow", "rating": 263}, {"opponent": "cradily", "rating": 388}, {"opponent": "talonflame", "rating": 474}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 21392}, {"moveId": "MUD_SLAP", "uses": 20518}, {"moveId": "MUD_SHOT", "uses": 16343}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 25586}, {"moveId": "STONE_EDGE", "uses": 17891}, {"moveId": "RETURN", "uses": 8616}, {"moveId": "EARTHQUAKE", "uses": 6048}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "STONE_EDGE"], "score": 48}, {"speciesId": "larve<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 602, "matchups": [{"opponent": "abomasnow_shadow", "rating": 889, "opRating": 110}, {"opponent": "cradily_shadow", "rating": 748, "opRating": 251}, {"opponent": "piloswine", "rating": 721, "opRating": 278}, {"opponent": "claydol", "rating": 652, "opRating": 347}, {"opponent": "gligar", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "quagsire_shadow", "rating": 149}, {"opponent": "swampert_shadow", "rating": 209}, {"opponent": "cradily", "rating": 274}, {"opponent": "talonflame", "rating": 351}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31264}, {"moveId": "BUG_BITE", "uses": 27036}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 29431}, {"moveId": "BUG_BUZZ", "uses": 23388}, {"moveId": "FLAME_WHEEL", "uses": 5449}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BUG_BUZZ"], "score": 48}, {"speciesId": "meowscarada", "speciesName": "Meowscarada", "rating": 615, "matchups": [{"opponent": "swampert_shadow", "rating": 904, "opRating": 95}, {"opponent": "quagsire_shadow", "rating": 891, "opRating": 108}, {"opponent": "furret", "rating": 821, "opRating": 178}, {"opponent": "diggersby", "rating": 821, "opRating": 178}, {"opponent": "claydol", "rating": 782, "opRating": 217}], "counters": [{"opponent": "typhlosion_shadow", "rating": 111}, {"opponent": "ninetales_shadow", "rating": 130}, {"opponent": "gligar", "rating": 175}, {"opponent": "jumpluff_shadow", "rating": 192}, {"opponent": "talonflame", "rating": 274}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 40611}, {"moveId": "CHARM", "uses": 17689}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 21353}, {"moveId": "FRENZY_PLANT", "uses": 17624}, {"moveId": "FLOWER_TRICK", "uses": 6961}, {"moveId": "GRASS_KNOT", "uses": 4715}, {"moveId": "PLAY_ROUGH", "uses": 4295}, {"moveId": "ENERGY_BALL", "uses": 3423}]}, "moveset": ["LEAFAGE", "NIGHT_SLASH", "FRENZY_PLANT"], "score": 48}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 539, "matchups": [{"opponent": "skeledirge", "rating": 669, "opRating": 330}, {"opponent": "claydol", "rating": 592, "opRating": 407}, {"opponent": "typhlosion_shadow", "rating": 592, "opRating": 407}, {"opponent": "flygon_shadow", "rating": 544, "opRating": 455}, {"opponent": "abomasnow_shadow", "rating": 532, "opRating": 467}], "counters": [{"opponent": "pidgeot", "rating": 116}, {"opponent": "diggersby", "rating": 189}, {"opponent": "furret", "rating": 246}, {"opponent": "clodsire", "rating": 293}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 34244}, {"moveId": "SCRATCH", "uses": 24056}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 24729}, {"moveId": "RETURN", "uses": 14124}, {"moveId": "LOW_SWEEP", "uses": 13937}, {"moveId": "HYPER_BEAM", "uses": 5466}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 47.5}, {"speciesId": "gumshoos", "speciesName": "<PERSON>ums<PERSON><PERSON>", "rating": 454, "matchups": [{"opponent": "chandelure_shadow", "rating": 768, "opRating": 231}, {"opponent": "armarouge", "rating": 741, "opRating": 258}, {"opponent": "swellow_shadow", "rating": 676, "opRating": 323}, {"opponent": "staraptor_shadow", "rating": 673, "opRating": 326}, {"opponent": "skeledirge", "rating": 666, "opRating": 333}], "counters": [{"opponent": "clodsire", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 287}, {"opponent": "cradily", "rating": 298}, {"opponent": "furret", "rating": 303}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 39621}, {"moveId": "TAKE_DOWN", "uses": 18679}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22998}, {"moveId": "CRUNCH", "uses": 18368}, {"moveId": "HYPER_FANG", "uses": 16895}]}, "moveset": ["BITE", "ROCK_TOMB", "CRUNCH"], "score": 47.5}, {"speciesId": "pupitar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 453, "matchups": [{"opponent": "armarouge", "rating": 845, "opRating": 154}, {"opponent": "staraptor", "rating": 823, "opRating": 176}, {"opponent": "skeledirge", "rating": 798, "opRating": 201}, {"opponent": "typhlosion_shadow", "rating": 654, "opRating": 345}, {"opponent": "flygon_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "diggersby", "rating": 218}, {"opponent": "gligar", "rating": 278}, {"opponent": "clodsire", "rating": 281}, {"opponent": "cradily", "rating": 284}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33496}, {"moveId": "ROCK_SMASH", "uses": 24804}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 22127}, {"moveId": "CRUNCH", "uses": 19373}, {"moveId": "DIG", "uses": 16718}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "ANCIENT_POWER", "DIG"], "score": 47.3}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 616, "matchups": [{"opponent": "abomasnow_shadow", "rating": 913, "opRating": 86}, {"opponent": "piloswine", "rating": 722, "opRating": 277}, {"opponent": "gliscor", "rating": 590, "opRating": 409}, {"opponent": "jumpluff_shadow", "rating": 581, "opRating": 418}, {"opponent": "pidgeot", "rating": 527, "opRating": 472}], "counters": [{"opponent": "swampert_shadow", "rating": 209}, {"opponent": "clodsire", "rating": 218}, {"opponent": "furret", "rating": 243}, {"opponent": "gligar", "rating": 248}, {"opponent": "talonflame", "rating": 340}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39297}, {"moveId": "SCRATCH", "uses": 11787}, {"moveId": "ZEN_HEADBUTT", "uses": 7247}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 20762}, {"moveId": "MYSTICAL_FIRE", "uses": 14907}, {"moveId": "PSYCHIC", "uses": 9580}, {"moveId": "FLAME_CHARGE", "uses": 5699}, {"moveId": "FLAMETHROWER", "uses": 4804}, {"moveId": "FIRE_BLAST", "uses": 2684}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 47.1}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 597, "matchups": [{"opponent": "cradily", "rating": 909, "opRating": 90}, {"opponent": "clodsire", "rating": 909, "opRating": 90}, {"opponent": "magcargo", "rating": 685, "opRating": 314}, {"opponent": "diggersby", "rating": 610, "opRating": 389}, {"opponent": "flygon", "rating": 582, "opRating": 417}], "counters": [{"opponent": "claydol", "rating": 90}, {"opponent": "jumpluff_shadow", "rating": 127}, {"opponent": "gligar", "rating": 145}, {"opponent": "quagsire_shadow", "rating": 170}, {"opponent": "talonflame", "rating": 377}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 15108}, {"moveId": "FURY_CUTTER", "uses": 14252}, {"moveId": "DOUBLE_KICK", "uses": 13570}, {"moveId": "POISON_JAB", "uses": 12406}, {"moveId": "IRON_TAIL", "uses": 2903}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 16707}, {"moveId": "EARTH_POWER", "uses": 16446}, {"moveId": "SLUDGE_WAVE", "uses": 12287}, {"moveId": "SAND_TOMB", "uses": 6889}, {"moveId": "EARTHQUAKE", "uses": 5923}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 47.1}, {"speciesId": "dart<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 662, "matchups": [{"opponent": "swampert_shadow", "rating": 967, "opRating": 32}, {"opponent": "claydol", "rating": 896, "opRating": 103}, {"opponent": "diggersby", "rating": 672, "opRating": 327}, {"opponent": "furret", "rating": 623, "opRating": 376}, {"opponent": "gligar", "rating": 616, "opRating": 383}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 286}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 19962}, {"moveId": "MAGICAL_LEAF", "uses": 18719}, {"moveId": "PECK", "uses": 12384}, {"moveId": "RAZOR_LEAF", "uses": 7316}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36656}, {"moveId": "SEED_BOMB", "uses": 15005}, {"moveId": "ENERGY_BALL", "uses": 6699}]}, "moveset": ["LEAFAGE", "BRAVE_BIRD", "SEED_BOMB"], "score": 47}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 598, "matchups": [{"opponent": "abomasnow_shadow", "rating": 910, "opRating": 89}, {"opponent": "roserade", "rating": 853, "opRating": 146}, {"opponent": "piloswine_shadow", "rating": 853, "opRating": 146}, {"opponent": "piloswine", "rating": 710, "opRating": 289}, {"opponent": "jumpluff_shadow", "rating": 539, "opRating": 460}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "clodsire", "rating": 221}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "cradily", "rating": 274}, {"opponent": "gligar", "rating": 297}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30933}, {"moveId": "FIRE_FANG", "uses": 27367}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 15271}, {"moveId": "FLAME_CHARGE", "uses": 14828}, {"moveId": "OVERHEAT", "uses": 11446}, {"moveId": "IRON_HEAD", "uses": 7282}, {"moveId": "FLAMETHROWER", "uses": 6093}, {"moveId": "FIRE_BLAST", "uses": 3333}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 47}, {"speciesId": "gloom_shadow", "speciesName": "Gloom (Shadow)", "rating": 595, "matchups": [{"opponent": "quagsire_shadow", "rating": 641, "opRating": 358}, {"opponent": "jumpluff_shadow", "rating": 603, "opRating": 396}, {"opponent": "furret", "rating": 530, "opRating": 469}, {"opponent": "swampert_shadow", "rating": 519, "opRating": 480}, {"opponent": "talonflame", "rating": 507, "opRating": 492}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "gliscor", "rating": 133}, {"opponent": "clodsire", "rating": 228}, {"opponent": "magcargo", "rating": 273}, {"opponent": "cradily", "rating": 371}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41689}, {"moveId": "RAZOR_LEAF", "uses": 16611}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25358}, {"moveId": "PETAL_BLIZZARD", "uses": 17303}, {"moveId": "MOONBLAST", "uses": 15395}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 46.7}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 604, "matchups": [{"opponent": "clodsire", "rating": 887, "opRating": 112}, {"opponent": "magcargo", "rating": 786, "opRating": 213}, {"opponent": "furret", "rating": 627, "opRating": 372}, {"opponent": "claydol", "rating": 608, "opRating": 391}, {"opponent": "flygon", "rating": 562, "opRating": 437}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 110}, {"opponent": "gliscor", "rating": 125}, {"opponent": "cradily", "rating": 388}, {"opponent": "talonflame", "rating": 451}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31761}, {"moveId": "SNARL", "uses": 26539}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22243}, {"moveId": "BRICK_BREAK", "uses": 13906}, {"moveId": "EARTHQUAKE", "uses": 11239}, {"moveId": "OUTRAGE", "uses": 10831}]}, "moveset": ["MUD_SLAP", "CRUNCH", "EARTHQUAKE"], "score": 46.7}, {"speciesId": "loudred", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 437, "matchups": [{"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 918, "opRating": 81}, {"opponent": "victini", "rating": 842, "opRating": 157}, {"opponent": "chandelure_shadow", "rating": 696, "opRating": 303}, {"opponent": "staraptor_shadow", "rating": 693, "opRating": 306}, {"opponent": "ceruledge", "rating": 676, "opRating": 323}], "counters": [{"opponent": "cradily", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 287}, {"opponent": "talonflame", "rating": 292}, {"opponent": "magcargo", "rating": 294}, {"opponent": "furret", "rating": 315}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33115}, {"moveId": "ROCK_SMASH", "uses": 25185}], "chargedMoves": [{"moveId": "STOMP", "uses": 19381}, {"moveId": "DISARMING_VOICE", "uses": 14637}, {"moveId": "FLAMETHROWER", "uses": 12998}, {"moveId": "RETURN", "uses": 11242}]}, "moveset": ["BITE", "STOMP", "DISARMING_VOICE"], "score": 46.6}, {"speciesId": "nuzleaf", "speciesName": "Nuz<PERSON>", "rating": 577, "matchups": [{"opponent": "swampert_shadow", "rating": 869, "opRating": 130}, {"opponent": "claydol", "rating": 818, "opRating": 181}, {"opponent": "quagsire_shadow", "rating": 735, "opRating": 264}, {"opponent": "flygon", "rating": 617, "opRating": 382}, {"opponent": "drampa", "rating": 557, "opRating": 442}], "counters": [{"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "talonflame", "rating": 225}, {"opponent": "clodsire", "rating": 286}, {"opponent": "magcargo", "rating": 324}, {"opponent": "cradily", "rating": 326}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 36530}, {"moveId": "RAZOR_LEAF", "uses": 21770}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28081}, {"moveId": "FOUL_PLAY", "uses": 18883}, {"moveId": "RETURN", "uses": 5699}, {"moveId": "GRASS_KNOT", "uses": 5521}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 46.4}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 564, "matchups": [{"opponent": "magcargo", "rating": 644, "opRating": 355}, {"opponent": "farfetchd", "rating": 629, "opRating": 370}, {"opponent": "gliscor", "rating": 596, "opRating": 403}, {"opponent": "gligar_shadow", "rating": 570, "opRating": 429}, {"opponent": "swampert_shadow", "rating": 529, "opRating": 470}], "counters": [{"opponent": "clodsire", "rating": 175}, {"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 228}, {"opponent": "talonflame", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32852}, {"moveId": "STEEL_WING", "uses": 25448}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 17232}, {"moveId": "BRAVE_BIRD", "uses": 17217}, {"moveId": "FLY", "uses": 14621}, {"moveId": "ROCK_SLIDE", "uses": 7465}, {"moveId": "HEAT_WAVE", "uses": 1598}]}, "moveset": ["AIR_SLASH", "FLY", "CLOSE_COMBAT"], "score": 46.3}, {"speciesId": "onix_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 536, "matchups": [{"opponent": "talonflame", "rating": 895, "opRating": 104}, {"opponent": "skeledirge", "rating": 809, "opRating": 190}, {"opponent": "jumpluff_shadow", "rating": 680, "opRating": 319}, {"opponent": "magcargo", "rating": 671, "opRating": 328}, {"opponent": "drampa", "rating": 638, "opRating": 361}], "counters": [{"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 228}, {"opponent": "diggersby", "rating": 235}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32961}, {"moveId": "TACKLE", "uses": 25339}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 17670}, {"moveId": "STONE_EDGE", "uses": 13301}, {"moveId": "ROCK_SLIDE", "uses": 12717}, {"moveId": "HEAVY_SLAM", "uses": 6066}, {"moveId": "SAND_TOMB", "uses": 5404}, {"moveId": "IRON_HEAD", "uses": 3101}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "BREAKING_SWIPE", "STONE_EDGE"], "score": 46.2}, {"speciesId": "shiinotic", "speciesName": "Shiinotic", "rating": 552, "matchups": [{"opponent": "quagsire_shadow", "rating": 898, "opRating": 101}, {"opponent": "flygon", "rating": 865, "opRating": 134}, {"opponent": "flygon_shadow", "rating": 833, "opRating": 166}, {"opponent": "claydol", "rating": 792, "opRating": 207}, {"opponent": "gligar", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "talonflame", "rating": 270}, {"opponent": "clodsire", "rating": 276}, {"opponent": "cradily", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 58300}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 21251}, {"moveId": "MOONBLAST", "uses": 19637}, {"moveId": "SLUDGE_BOMB", "uses": 17372}]}, "moveset": ["ASTONISH", "MOONBLAST", "SEED_BOMB"], "score": 46.2}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 592, "matchups": [{"opponent": "claydol", "rating": 620, "opRating": 379}, {"opponent": "jumpluff_shadow", "rating": 616, "opRating": 383}, {"opponent": "drampa", "rating": 612, "opRating": 387}, {"opponent": "talonflame", "rating": 545, "opRating": 454}, {"opponent": "furret", "rating": 512, "opRating": 487}], "counters": [{"opponent": "gliscor", "rating": 133}, {"opponent": "gligar", "rating": 152}, {"opponent": "magcargo", "rating": 252}, {"opponent": "clodsire", "rating": 302}, {"opponent": "cradily", "rating": 385}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41200}, {"moveId": "RAZOR_LEAF", "uses": 17100}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23724}, {"moveId": "PETAL_BLIZZARD", "uses": 14645}, {"moveId": "MOONBLAST", "uses": 13992}, {"moveId": "SOLAR_BEAM", "uses": 5853}]}, "moveset": ["ACID", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 46.2}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 562, "matchups": [{"opponent": "magcargo", "rating": 827, "opRating": 172}, {"opponent": "skeledirge", "rating": 792, "opRating": 207}, {"opponent": "ninetales_shadow", "rating": 698, "opRating": 301}, {"opponent": "clodsire", "rating": 629, "opRating": 370}, {"opponent": "swampert_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "gligar", "rating": 221}, {"opponent": "cradily", "rating": 298}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "furret", "rating": 328}, {"opponent": "talonflame", "rating": 333}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31791}, {"moveId": "MUD_SHOT", "uses": 26509}], "chargedMoves": [{"moveId": "MUDDY_WATER", "uses": 24826}, {"moveId": "EARTH_POWER", "uses": 17622}, {"moveId": "SLUDGE_BOMB", "uses": 15842}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "SLUDGE_BOMB"], "score": 45.9}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 721, "matchups": [{"opponent": "jumpluff_shadow", "rating": 898, "opRating": 101}, {"opponent": "diggersby", "rating": 856, "opRating": 143}, {"opponent": "gligar", "rating": 786, "opRating": 213}, {"opponent": "clodsire", "rating": 723, "opRating": 276}, {"opponent": "cradily", "rating": 632, "opRating": 367}], "counters": [{"opponent": "magcargo", "rating": 68}, {"opponent": "skeledirge", "rating": 75}, {"opponent": "typhlosion_shadow", "rating": 89}, {"opponent": "ninetales_shadow", "rating": 99}, {"opponent": "talonflame", "rating": 292}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32523}, {"moveId": "LEAFAGE", "uses": 18131}, {"moveId": "RAZOR_LEAF", "uses": 7630}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 28102}, {"moveId": "ICY_WIND", "uses": 9527}, {"moveId": "ENERGY_BALL", "uses": 9149}, {"moveId": "OUTRAGE", "uses": 7092}, {"moveId": "BLIZZARD", "uses": 4238}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 45.8}, {"speciesId": "parasect", "speciesName": "Parasect", "rating": 683, "matchups": [{"opponent": "swampert_shadow", "rating": 922, "opRating": 77}, {"opponent": "claydol", "rating": 882, "opRating": 117}, {"opponent": "cradily", "rating": 703, "opRating": 296}, {"opponent": "flygon", "rating": 682, "opRating": 317}, {"opponent": "furret", "rating": 532, "opRating": 467}], "counters": [{"opponent": "skeledirge", "rating": 104}, {"opponent": "clodsire", "rating": 206}, {"opponent": "magcargo", "rating": 226}, {"opponent": "talonflame", "rating": 237}, {"opponent": "gligar", "rating": 263}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28577}, {"moveId": "BUG_BITE", "uses": 20518}, {"moveId": "STRUGGLE_BUG", "uses": 9177}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25468}, {"moveId": "CROSS_POISON", "uses": 21742}, {"moveId": "SOLAR_BEAM", "uses": 11117}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "CROSS_POISON"], "score": 45.8}, {"speciesId": "onix", "speciesName": "Onix", "rating": 520, "matchups": [{"opponent": "talonflame", "rating": 880, "opRating": 119}, {"opponent": "skeledirge", "rating": 828, "opRating": 171}, {"opponent": "magcargo", "rating": 723, "opRating": 276}, {"opponent": "jumpluff_shadow", "rating": 709, "opRating": 290}, {"opponent": "drampa", "rating": 638, "opRating": 361}], "counters": [{"opponent": "quagsire_shadow", "rating": 114}, {"opponent": "swampert_shadow", "rating": 150}, {"opponent": "diggersby", "rating": 224}, {"opponent": "furret", "rating": 271}, {"opponent": "cradily", "rating": 281}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 31713}, {"moveId": "TACKLE", "uses": 26587}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 16124}, {"moveId": "STONE_EDGE", "uses": 12397}, {"moveId": "ROCK_SLIDE", "uses": 11835}, {"moveId": "HEAVY_SLAM", "uses": 5622}, {"moveId": "SAND_TOMB", "uses": 4973}, {"moveId": "RETURN", "uses": 4518}, {"moveId": "IRON_HEAD", "uses": 2770}]}, "moveset": ["ROCK_THROW", "BREAKING_SWIPE", "STONE_EDGE"], "score": 45.6}, {"speciesId": "raboot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 586, "matchups": [{"opponent": "abomasnow_shadow", "rating": 903, "opRating": 96}, {"opponent": "piloswine", "rating": 726, "opRating": 273}, {"opponent": "gliscor", "rating": 603, "opRating": 396}, {"opponent": "jumpluff_shadow", "rating": 530, "opRating": 469}, {"opponent": "furret", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 183}, {"opponent": "diggersby", "rating": 198}, {"opponent": "swampert_shadow", "rating": 216}, {"opponent": "talonflame", "rating": 218}, {"opponent": "cradily", "rating": 250}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 37598}, {"moveId": "TACKLE", "uses": 20702}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 41164}, {"moveId": "FLAMETHROWER", "uses": 17136}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 45.5}, {"speciesId": "cu<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 555, "matchups": [{"opponent": "magcargo", "rating": 807, "opRating": 192}, {"opponent": "skeledirge", "rating": 795, "opRating": 204}, {"opponent": "clodsire", "rating": 748, "opRating": 251}, {"opponent": "ninetales_shadow", "rating": 748, "opRating": 251}, {"opponent": "drampa", "rating": 649, "opRating": 350}], "counters": [{"opponent": "jumpluff_shadow", "rating": 84}, {"opponent": "gligar", "rating": 167}, {"opponent": "gliscor", "rating": 176}, {"opponent": "talonflame", "rating": 277}, {"opponent": "cradily", "rating": 326}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43522}, {"moveId": "ROCK_SMASH", "uses": 14778}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 43142}, {"moveId": "DIG", "uses": 8250}, {"moveId": "BULLDOZE", "uses": 6715}, {"moveId": "FRUSTRATION", "uses": 79}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "DIG"], "score": 45.3}, {"speciesId": "krokorok", "speciesName": "Krokorok", "rating": 600, "matchups": [{"opponent": "magcargo", "rating": 785, "opRating": 214}, {"opponent": "clodsire", "rating": 704, "opRating": 295}, {"opponent": "furret", "rating": 602, "opRating": 397}, {"opponent": "claydol", "rating": 573, "opRating": 426}, {"opponent": "flygon", "rating": 573, "opRating": 426}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 110}, {"opponent": "gliscor", "rating": 125}, {"opponent": "diggersby", "rating": 261}, {"opponent": "cradily", "rating": 388}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38467}, {"moveId": "BITE", "uses": 19833}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31174}, {"moveId": "EARTHQUAKE", "uses": 14049}, {"moveId": "BULLDOZE", "uses": 13059}]}, "moveset": ["MUD_SLAP", "CRUNCH", "EARTHQUAKE"], "score": 45.3}, {"speciesId": "<PERSON><PERSON><PERSON>_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 563, "matchups": [{"opponent": "clodsire", "rating": 933, "opRating": 66}, {"opponent": "cradily", "rating": 861, "opRating": 138}, {"opponent": "flygon", "rating": 644, "opRating": 355}, {"opponent": "drampa", "rating": 627, "opRating": 372}, {"opponent": "flygon_shadow", "rating": 627, "opRating": 372}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 156}, {"opponent": "claydol", "rating": 210}, {"opponent": "diggersby", "rating": 244}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23119}, {"moveId": "SAND_ATTACK", "uses": 19167}, {"moveId": "METAL_CLAW", "uses": 16025}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 30598}, {"moveId": "IRON_HEAD", "uses": 20271}, {"moveId": "EARTHQUAKE", "uses": 7227}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "IRON_HEAD"], "score": 45.2}, {"speciesId": "palpitoad", "speciesName": "Palpitoad", "rating": 547, "matchups": [{"opponent": "magcargo", "rating": 835, "opRating": 164}, {"opponent": "talonflame", "rating": 810}, {"opponent": "clodsire", "rating": 630, "opRating": 369}, {"opponent": "swampert_shadow", "rating": 546, "opRating": 453}, {"opponent": "diggersby", "rating": 518, "opRating": 481}], "counters": [{"opponent": "abomasnow_shadow", "rating": 129}, {"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "drampa", "rating": 213}, {"opponent": "cradily", "rating": 291}, {"opponent": "gligar", "rating": 366}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31567}, {"moveId": "MUD_SHOT", "uses": 26733}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 22786}, {"moveId": "EARTH_POWER", "uses": 21646}, {"moveId": "SLUDGE_WAVE", "uses": 13804}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "WATER_PULSE"], "score": 45.2}, {"speciesId": "watchog", "speciesName": "Watchog", "rating": 424, "matchups": [{"opponent": "zoro<PERSON>_his<PERSON>an", "rating": 911, "opRating": 88}, {"opponent": "victini", "rating": 820, "opRating": 180}, {"opponent": "staraptor_shadow", "rating": 708, "opRating": 292}, {"opponent": "chandelure_shadow", "rating": 700, "opRating": 300}, {"opponent": "ceruledge", "rating": 671, "opRating": 328}], "counters": [{"opponent": "jumpluff_shadow", "rating": 248}, {"opponent": "clodsire", "rating": 269}, {"opponent": "cradily", "rating": 270}, {"opponent": "diggersby", "rating": 278}, {"opponent": "talonflame", "rating": 292}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 38593}, {"moveId": "LOW_KICK", "uses": 19707}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19047}, {"moveId": "HYPER_FANG", "uses": 17650}, {"moveId": "GRASS_KNOT", "uses": 15545}, {"moveId": "RETURN", "uses": 6034}]}, "moveset": ["BITE", "CRUNCH", "GRASS_KNOT"], "score": 45.2}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "piloswine", "rating": 903, "opRating": 96}, {"opponent": "abomasnow_shadow", "rating": 771, "opRating": 228}, {"opponent": "jumpluff_shadow", "rating": 705, "opRating": 294}, {"opponent": "flygon_shadow", "rating": 581, "opRating": 418}, {"opponent": "drampa", "rating": 531, "opRating": 468}], "counters": [{"opponent": "cradily", "rating": 93}, {"opponent": "diggersby", "rating": 106}, {"opponent": "flygon", "rating": 196}, {"opponent": "magcargo", "rating": 264}, {"opponent": "talonflame", "rating": 366}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30826}, {"moveId": "CONFUSION", "uses": 27474}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 36894}, {"moveId": "PSYCHIC", "uses": 8830}, {"moveId": "FOCUS_BLAST", "uses": 8301}, {"moveId": "OVERHEAT", "uses": 4313}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 45.1}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 494, "matchups": [{"opponent": "quagsire_shadow", "rating": 943, "opRating": 56}, {"opponent": "swampert_shadow", "rating": 929, "opRating": 70}, {"opponent": "claydol", "rating": 797, "opRating": 202}, {"opponent": "piloswine", "rating": 741, "opRating": 258}, {"opponent": "diggersby", "rating": 588, "opRating": 411}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 270}, {"opponent": "cradily", "rating": 322}, {"opponent": "talonflame", "rating": 329}, {"opponent": "clodsire", "rating": 348}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30585}, {"moveId": "BITE", "uses": 27715}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30596}, {"moveId": "ENERGY_BALL", "uses": 21356}, {"moveId": "SOLAR_BEAM", "uses": 6268}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 44.8}, {"speciesId": "fearow", "speciesName": "<PERSON>ow", "rating": 571, "matchups": [{"opponent": "cradily_shadow", "rating": 733, "opRating": 266}, {"opponent": "gliscor", "rating": 592, "opRating": 407}, {"opponent": "gligar", "rating": 572, "opRating": 427}, {"opponent": "gligar_shadow", "rating": 568, "opRating": 431}, {"opponent": "swampert_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "quagsire_shadow", "rating": 152}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "skeledirge", "rating": 190}, {"opponent": "talonflame", "rating": 196}, {"opponent": "magcargo", "rating": 226}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 31938}, {"moveId": "PECK", "uses": 26362}], "chargedMoves": [{"moveId": "FLY", "uses": 20053}, {"moveId": "AERIAL_ACE", "uses": 15305}, {"moveId": "DRILL_RUN", "uses": 13915}, {"moveId": "SKY_ATTACK", "uses": 5121}, {"moveId": "TWISTER", "uses": 4035}]}, "moveset": ["STEEL_WING", "FLY", "DRILL_RUN"], "score": 44.7}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 619, "matchups": [{"opponent": "swampert_shadow", "rating": 880, "opRating": 119}, {"opponent": "furret", "rating": 841, "opRating": 158}, {"opponent": "magcargo", "rating": 662, "opRating": 337}, {"opponent": "cradily", "rating": 606, "opRating": 393}, {"opponent": "diggersby", "rating": 534, "opRating": 465}], "counters": [{"opponent": "ninetales_shadow", "rating": 75}, {"opponent": "jumpluff_shadow", "rating": 205}, {"opponent": "skeledirge", "rating": 205}, {"opponent": "gligar", "rating": 267}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 32285}, {"moveId": "MAGICAL_LEAF", "uses": 26015}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25325}, {"moveId": "UPPER_HAND", "uses": 23118}, {"moveId": "PETAL_BLIZZARD", "uses": 6835}, {"moveId": "SOLAR_BEAM", "uses": 2777}]}, "moveset": ["BULLET_SEED", "CLOSE_COMBAT", "UPPER_HAND"], "score": 44.7}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 576, "matchups": [{"opponent": "claydol", "rating": 831, "opRating": 168}, {"opponent": "cradily_shadow", "rating": 668, "opRating": 331}, {"opponent": "diggersby", "rating": 586, "opRating": 413}, {"opponent": "gliscor", "rating": 536, "opRating": 463}, {"opponent": "gligar", "rating": 522, "opRating": 477}], "counters": [{"opponent": "swampert_shadow", "rating": 187}, {"opponent": "clodsire", "rating": 189}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "furret", "rating": 221}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 30325}, {"moveId": "FEINT_ATTACK", "uses": 27975}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18973}, {"moveId": "DRILL_PECK", "uses": 16913}, {"moveId": "AIR_CUTTER", "uses": 16256}, {"moveId": "AERIAL_ACE", "uses": 6060}]}, "moveset": ["STEEL_WING", "BRAVE_BIRD", "DRILL_PECK"], "score": 44.5}, {"speciesId": "graveler", "speciesName": "<PERSON><PERSON>", "rating": 525, "matchups": [{"opponent": "magcargo", "rating": 879, "opRating": 120}, {"opponent": "typhlosion_shadow", "rating": 840, "opRating": 159}, {"opponent": "clodsire", "rating": 810, "opRating": 189}, {"opponent": "ninetales_shadow", "rating": 698, "opRating": 301}, {"opponent": "talonflame", "rating": 525, "opRating": 474}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "gliscor", "rating": 159}, {"opponent": "cradily", "rating": 256}, {"opponent": "furret", "rating": 278}, {"opponent": "swampert_shadow", "rating": 338}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23229}, {"moveId": "MUD_SHOT", "uses": 18402}, {"moveId": "ROCK_THROW", "uses": 16653}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 14168}, {"moveId": "ROCK_SLIDE", "uses": 13344}, {"moveId": "ROCK_BLAST", "uses": 12745}, {"moveId": "DIG", "uses": 12080}, {"moveId": "RETURN", "uses": 5919}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "DIG"], "score": 44.5}, {"speciesId": "graveler_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 523, "matchups": [{"opponent": "magcargo", "rating": 865, "opRating": 134}, {"opponent": "skeledirge", "rating": 852, "opRating": 147}, {"opponent": "ninetales_shadow", "rating": 800, "opRating": 200}, {"opponent": "clodsire", "rating": 726, "opRating": 273}, {"opponent": "jumpluff_shadow", "rating": 547, "opRating": 452}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "diggersby", "rating": 232}, {"opponent": "talonflame", "rating": 240}, {"opponent": "claydol", "rating": 256}, {"opponent": "cradily", "rating": 288}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23382}, {"moveId": "MUD_SHOT", "uses": 18555}, {"moveId": "ROCK_THROW", "uses": 16382}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15620}, {"moveId": "ROCK_SLIDE", "uses": 14803}, {"moveId": "ROCK_BLAST", "uses": 14067}, {"moveId": "DIG", "uses": 13661}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "DIG"], "score": 44.5}, {"speciesId": "ivy<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 602, "matchups": [{"opponent": "swampert_shadow", "rating": 841, "opRating": 158}, {"opponent": "claydol", "rating": 724, "opRating": 275}, {"opponent": "flygon", "rating": 643, "opRating": 356}, {"opponent": "diggersby", "rating": 608, "opRating": 391}, {"opponent": "furret", "rating": 573, "opRating": 426}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "ninetales_shadow", "rating": 162}, {"opponent": "gliscor", "rating": 176}, {"opponent": "cradily", "rating": 371}, {"opponent": "talonflame", "rating": 381}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42870}, {"moveId": "RAZOR_LEAF", "uses": 15430}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 26525}, {"moveId": "POWER_WHIP", "uses": 26230}, {"moveId": "SOLAR_BEAM", "uses": 5550}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 44.5}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 553, "matchups": [{"opponent": "pidgeot", "rating": 632, "opRating": 367}, {"opponent": "talonflame", "rating": 619, "opRating": 380}, {"opponent": "skeledirge", "rating": 598, "opRating": 401}, {"opponent": "drampa", "rating": 542, "opRating": 457}, {"opponent": "typhlosion_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "furret", "rating": 134}, {"opponent": "swampert_shadow", "rating": 158}, {"opponent": "diggersby", "rating": 258}, {"opponent": "cradily", "rating": 274}, {"opponent": "gligar", "rating": 290}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9816}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4434}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3712}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3216}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3165}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3158}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2899}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2895}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2841}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2705}, {"moveId": "CHARGE_BEAM", "uses": 2684}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2674}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2648}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2641}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2472}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2380}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2202}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1915}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18517}, {"moveId": "BLIZZARD", "uses": 15626}, {"moveId": "HYPER_BEAM", "uses": 9115}, {"moveId": "SOLAR_BEAM", "uses": 8164}, {"moveId": "ZAP_CANNON", "uses": 6945}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 44.5}, {"speciesId": "unfezant_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 481, "matchups": [{"opponent": "bewear", "rating": 872, "opRating": 127}, {"opponent": "chesnaught", "rating": 864, "opRating": 135}, {"opponent": "parasect", "rating": 823, "opRating": 176}, {"opponent": "roserade", "rating": 799, "opRating": 200}, {"opponent": "sceptile_shadow", "rating": 786, "opRating": 213}], "counters": [{"opponent": "magcargo", "rating": 170}, {"opponent": "clodsire", "rating": 218}, {"opponent": "cradily", "rating": 256}, {"opponent": "furret", "rating": 265}, {"opponent": "talonflame", "rating": 329}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32696}, {"moveId": "STEEL_WING", "uses": 25604}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33541}, {"moveId": "HYPER_BEAM", "uses": 17247}, {"moveId": "HEAT_WAVE", "uses": 7445}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 44.5}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 545, "matchups": [{"opponent": "swampert_shadow", "rating": 892, "opRating": 107}, {"opponent": "claydol", "rating": 834, "opRating": 165}, {"opponent": "quagsire_shadow", "rating": 753, "opRating": 246}, {"opponent": "flygon", "rating": 646, "opRating": 353}, {"opponent": "diggersby", "rating": 584, "opRating": 415}], "counters": [{"opponent": "typhlosion_shadow", "rating": 158}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "talonflame", "rating": 274}, {"opponent": "magcargo", "rating": 307}, {"opponent": "cradily", "rating": 333}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 39395}, {"moveId": "BITE", "uses": 18905}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25720}, {"moveId": "POWER_WHIP", "uses": 23947}, {"moveId": "ENERGY_BALL", "uses": 8465}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 44.4}, {"speciesId": "appletun", "speciesName": "Appletun", "rating": 563, "matchups": [{"opponent": "quagsire_shadow", "rating": 885, "opRating": 114}, {"opponent": "quagsire", "rating": 885, "opRating": 114}, {"opponent": "claydol", "rating": 810, "opRating": 189}, {"opponent": "swampert_shadow", "rating": 559, "opRating": 440}, {"opponent": "diggersby", "rating": 531, "opRating": 468}], "counters": [{"opponent": "drampa", "rating": 126}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "gligar", "rating": 190}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30474}, {"moveId": "ASTONISH", "uses": 27826}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23438}, {"moveId": "SEED_BOMB", "uses": 18683}, {"moveId": "ENERGY_BALL", "uses": 8383}, {"moveId": "DRAGON_PULSE", "uses": 7790}]}, "moveset": ["ASTONISH", "SEED_BOMB", "OUTRAGE"], "score": 44}, {"speciesId": "trumbeak", "speciesName": "Trumbeak", "rating": 524, "matchups": [{"opponent": "farfetchd", "rating": 625, "opRating": 375}, {"opponent": "gliscor", "rating": 583, "opRating": 416}, {"opponent": "gligar_shadow", "rating": 556, "opRating": 443}, {"opponent": "gliscor_shadow", "rating": 541, "opRating": 458}, {"opponent": "swampert_shadow", "rating": 526, "opRating": 473}], "counters": [{"opponent": "magcargo", "rating": 170}, {"opponent": "clodsire", "rating": 189}, {"opponent": "cradily", "rating": 204}, {"opponent": "furret", "rating": 246}, {"opponent": "talonflame", "rating": 292}], "moves": {"fastMoves": [{"moveId": "PECK", "uses": 37870}, {"moveId": "ROCK_SMASH", "uses": 20430}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 35032}, {"moveId": "ROCK_BLAST", "uses": 14902}, {"moveId": "SKY_ATTACK", "uses": 8271}]}, "moveset": ["PECK", "DRILL_PECK", "ROCK_BLAST"], "score": 44}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 565, "matchups": [{"opponent": "swampert_shadow", "rating": 726, "opRating": 273}, {"opponent": "drampa", "rating": 559, "opRating": 440}, {"opponent": "typhlosion_shadow", "rating": 559, "opRating": 440}, {"opponent": "talonflame", "rating": 551, "opRating": 448}, {"opponent": "skeledirge", "rating": 521, "opRating": 478}], "counters": [{"opponent": "quagsire_shadow", "rating": 121}, {"opponent": "furret", "rating": 134}, {"opponent": "diggersby", "rating": 278}, {"opponent": "cradily", "rating": 309}, {"opponent": "clodsire", "rating": 310}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 11346}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4323}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3596}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3142}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3058}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3051}, {"moveId": "CHARGE_BEAM", "uses": 2810}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2795}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2781}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2779}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2548}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2533}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2520}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2482}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2333}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2265}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2038}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1869}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18467}, {"moveId": "BLIZZARD", "uses": 15641}, {"moveId": "HYPER_BEAM", "uses": 9119}, {"moveId": "SOLAR_BEAM", "uses": 8187}, {"moveId": "ZAP_CANNON", "uses": 6947}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 43.8}, {"speciesId": "delcatty", "speciesName": "Delcat<PERSON>", "rating": 453, "matchups": [{"opponent": "shiftry_shadow", "rating": 898, "opRating": 101}, {"opponent": "drampa", "rating": 735, "opRating": 264}, {"opponent": "flygon", "rating": 722, "opRating": 277}, {"opponent": "flygon_shadow", "rating": 722, "opRating": 277}, {"opponent": "ursaring", "rating": 555, "opRating": 444}], "counters": [{"opponent": "clodsire", "rating": 189}, {"opponent": "magcargo", "rating": 205}, {"opponent": "talonflame", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 28814}, {"moveId": "CHARM", "uses": 21755}, {"moveId": "ZEN_HEADBUTT", "uses": 7669}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 29022}, {"moveId": "DISARMING_VOICE", "uses": 23251}, {"moveId": "PLAY_ROUGH", "uses": 6081}]}, "moveset": ["CHARM", "WILD_CHARGE", "DISARMING_VOICE"], "score": 43.7}, {"speciesId": "g<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 571, "matchups": [{"opponent": "quagsire_shadow", "rating": 876, "opRating": 123}, {"opponent": "swampert_shadow", "rating": 858, "opRating": 141}, {"opponent": "claydol", "rating": 722, "opRating": 277}, {"opponent": "piloswine", "rating": 688, "opRating": 311}, {"opponent": "flygon_shadow", "rating": 589, "opRating": 410}], "counters": [{"opponent": "typhlosion_shadow", "rating": 89}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "magcargo", "rating": 307}, {"opponent": "furret", "rating": 309}, {"opponent": "talonflame", "rating": 362}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 29843}, {"moveId": "QUICK_ATTACK", "uses": 28457}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33210}, {"moveId": "AERIAL_ACE", "uses": 18531}, {"moveId": "GRASS_KNOT", "uses": 6646}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "AERIAL_ACE"], "score": 43.7}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 576, "matchups": [{"opponent": "furret", "rating": 800, "opRating": 199}, {"opponent": "claydol", "rating": 689, "opRating": 310}, {"opponent": "drampa", "rating": 679, "opRating": 320}, {"opponent": "quagsire_shadow", "rating": 635, "opRating": 364}, {"opponent": "cradily", "rating": 587, "opRating": 412}], "counters": [{"opponent": "skeledirge", "rating": 111}, {"opponent": "jumpluff_shadow", "rating": 160}, {"opponent": "talonflame", "rating": 170}, {"opponent": "gligar", "rating": 190}, {"opponent": "clodsire", "rating": 288}], "moves": {"fastMoves": [{"moveId": "FORCE_PALM", "uses": 23561}, {"moveId": "BULLET_SEED", "uses": 18103}, {"moveId": "COUNTER", "uses": 16619}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25797}, {"moveId": "GRASS_KNOT", "uses": 12377}, {"moveId": "SLUDGE_BOMB", "uses": 10266}, {"moveId": "SEED_BOMB", "uses": 9905}]}, "moveset": ["FORCE_PALM", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 43.6}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "abomasnow_shadow", "rating": 893, "opRating": 106}, {"opponent": "sceptile_shadow", "rating": 871, "opRating": 128}, {"opponent": "piloswine_shadow", "rating": 824, "opRating": 175}, {"opponent": "piloswine", "rating": 688, "opRating": 311}, {"opponent": "gliscor", "rating": 551, "opRating": 448}], "counters": [{"opponent": "magcargo", "rating": 188}, {"opponent": "diggersby", "rating": 221}, {"opponent": "talonflame", "rating": 225}, {"opponent": "clodsire", "rating": 247}, {"opponent": "cradily", "rating": 277}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38714}, {"moveId": "TACKLE", "uses": 19586}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 27998}, {"moveId": "FOCUS_BLAST", "uses": 18591}, {"moveId": "FLAMETHROWER", "uses": 11645}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 43.6}, {"speciesId": "nuzleaf_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 582, "matchups": [{"opponent": "swampert_shadow", "rating": 837, "opRating": 162}, {"opponent": "claydol", "rating": 773, "opRating": 226}, {"opponent": "quagsire_shadow", "rating": 707, "opRating": 292}, {"opponent": "flygon", "rating": 550, "opRating": 449}, {"opponent": "furret", "rating": 525, "opRating": 474}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "magcargo", "rating": 196}, {"opponent": "talonflame", "rating": 237}, {"opponent": "cradily", "rating": 395}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 37204}, {"moveId": "RAZOR_LEAF", "uses": 21096}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30697}, {"moveId": "FOUL_PLAY", "uses": 21427}, {"moveId": "GRASS_KNOT", "uses": 6113}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 43.6}, {"speciesId": "pupitar", "speciesName": "Pupitar", "rating": 419, "matchups": [{"opponent": "victini", "rating": 877, "opRating": 122}, {"opponent": "chandelure_shadow", "rating": 827, "opRating": 172}, {"opponent": "staraptor_shadow", "rating": 823, "opRating": 176}, {"opponent": "ceruledge", "rating": 823, "opRating": 176}, {"opponent": "magcargo", "rating": 568, "opRating": 431}], "counters": [{"opponent": "clodsire", "rating": 211}, {"opponent": "furret", "rating": 237}, {"opponent": "cradily", "rating": 243}, {"opponent": "swampert_shadow", "rating": 312}, {"opponent": "gligar", "rating": 324}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33923}, {"moveId": "ROCK_SMASH", "uses": 24377}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 19354}, {"moveId": "CRUNCH", "uses": 16732}, {"moveId": "DIG", "uses": 14652}, {"moveId": "RETURN", "uses": 7557}]}, "moveset": ["BITE", "ANCIENT_POWER", "DIG"], "score": 43.4}, {"speciesId": "swadloon", "speciesName": "Swadloon", "rating": 605, "matchups": [{"opponent": "cradily", "rating": 755, "opRating": 244}, {"opponent": "claydol", "rating": 681, "opRating": 318}, {"opponent": "swampert_shadow", "rating": 625, "opRating": 374}, {"opponent": "furret", "rating": 611, "opRating": 388}, {"opponent": "diggersby", "rating": 585, "opRating": 414}], "counters": [{"opponent": "skeledirge", "rating": 147}, {"opponent": "gligar", "rating": 164}, {"opponent": "talonflame", "rating": 177}, {"opponent": "magcargo", "rating": 217}, {"opponent": "jumpluff_shadow", "rating": 228}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 40412}, {"moveId": "STRUGGLE_BUG", "uses": 17888}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 20188}, {"moveId": "SILVER_WIND", "uses": 20077}, {"moveId": "BUG_BUZZ", "uses": 17995}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "SILVER_WIND"], "score": 43.3}, {"speciesId": "komala", "speciesName": "Komala", "rating": 537, "matchups": [{"opponent": "talonflame", "rating": 665, "opRating": 334}, {"opponent": "skeledirge", "rating": 665, "opRating": 334}, {"opponent": "pidgeot", "rating": 610, "opRating": 389}, {"opponent": "typhlosion_shadow", "rating": 573, "opRating": 426}, {"opponent": "drampa", "rating": 555, "opRating": 444}], "counters": [{"opponent": "gligar", "rating": 175}, {"opponent": "cradily", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 212}, {"opponent": "furret", "rating": 253}, {"opponent": "clodsire", "rating": 286}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 57395}, {"moveId": "YAWN", "uses": 905}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 25404}, {"moveId": "BULLDOZE", "uses": 17266}, {"moveId": "PLAY_ROUGH", "uses": 15678}]}, "moveset": ["ROLLOUT", "PAYBACK", "BULLDOZE"], "score": 43.1}, {"speciesId": "oddish_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 596, "matchups": [{"opponent": "staravia", "rating": 745, "opRating": 254}, {"opponent": "quagsire_shadow", "rating": 637, "opRating": 362}, {"opponent": "piloswine", "rating": 620, "opRating": 379}, {"opponent": "drampa", "rating": 541, "opRating": 458}, {"opponent": "jumpluff_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "magcargo", "rating": 119}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "talonflame", "rating": 237}, {"opponent": "clodsire", "rating": 240}, {"opponent": "cradily", "rating": 340}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41443}, {"moveId": "RAZOR_LEAF", "uses": 16857}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23204}, {"moveId": "SEED_BOMB", "uses": 21465}, {"moveId": "MOONBLAST", "uses": 13559}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "SEED_BOMB", "SLUDGE_BOMB"], "score": 43}, {"speciesId": "tsar<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 555, "matchups": [{"opponent": "swampert_shadow", "rating": 912, "opRating": 87}, {"opponent": "claydol", "rating": 775, "opRating": 224}, {"opponent": "flygon", "rating": 591, "opRating": 408}, {"opponent": "diggersby", "rating": 545, "opRating": 454}, {"opponent": "furret", "rating": 509, "opRating": 490}], "counters": [{"opponent": "ninetales_shadow", "rating": 186}, {"opponent": "gligar", "rating": 213}, {"opponent": "magcargo", "rating": 222}, {"opponent": "talonflame", "rating": 277}, {"opponent": "cradily", "rating": 423}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 33273}, {"moveId": "CHARM", "uses": 13536}, {"moveId": "RAZOR_LEAF", "uses": 11474}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 18815}, {"moveId": "GRASS_KNOT", "uses": 12205}, {"moveId": "HIGH_JUMP_KICK", "uses": 11848}, {"moveId": "STOMP", "uses": 8399}, {"moveId": "ENERGY_BALL", "uses": 4421}, {"moveId": "DRAINING_KISS", "uses": 2666}]}, "moveset": ["MAGICAL_LEAF", "TRIPLE_AXEL", "HIGH_JUMP_KICK"], "score": 43}, {"speciesId": "chespin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 482, "matchups": [{"opponent": "swampert_shadow", "rating": 908, "opRating": 91}, {"opponent": "quagsire_shadow", "rating": 908, "opRating": 91}, {"opponent": "swampert", "rating": 908, "opRating": 91}, {"opponent": "claydol", "rating": 580, "opRating": 419}, {"opponent": "diggersby", "rating": 558, "opRating": 441}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "gligar", "rating": 209}, {"opponent": "cradily", "rating": 236}, {"opponent": "magcargo", "rating": 252}, {"opponent": "talonflame", "rating": 311}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 48692}, {"moveId": "TAKE_DOWN", "uses": 9608}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27619}, {"moveId": "SEED_BOMB", "uses": 22225}, {"moveId": "GYRO_BALL", "uses": 8437}]}, "moveset": ["VINE_WHIP", "BODY_SLAM", "SEED_BOMB"], "score": 42.9}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 451, "matchups": [{"opponent": "bewear", "rating": 890, "opRating": 109}, {"opponent": "flygon", "rating": 667, "opRating": 332}, {"opponent": "drampa", "rating": 664, "opRating": 335}, {"opponent": "flygon_shadow", "rating": 664, "opRating": 335}, {"opponent": "ursaring_shadow", "rating": 597, "opRating": 402}], "counters": [{"opponent": "clodsire", "rating": 153}, {"opponent": "magcargo", "rating": 209}, {"opponent": "talonflame", "rating": 240}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "cradily", "rating": 288}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46858}, {"moveId": "POUND", "uses": 11442}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 37413}, {"moveId": "HYPER_BEAM", "uses": 11528}, {"moveId": "THUNDERBOLT", "uses": 9341}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 42.7}, {"speciesId": "lampent", "speciesName": "<PERSON><PERSON>", "rating": 519, "matchups": [{"opponent": "roserade", "rating": 903, "opRating": 96}, {"opponent": "abomasnow_shadow", "rating": 887, "opRating": 112}, {"opponent": "piloswine", "rating": 717, "opRating": 282}, {"opponent": "diggersby", "rating": 519, "opRating": 480}, {"opponent": "jumpluff_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "quagsire_shadow", "rating": 142}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "cradily", "rating": 250}, {"opponent": "furret", "rating": 265}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31374}, {"moveId": "ASTONISH", "uses": 26926}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 19607}, {"moveId": "FLAME_BURST", "uses": 18743}, {"moveId": "RETURN", "uses": 14941}, {"moveId": "HEAT_WAVE", "uses": 5026}]}, "moveset": ["EMBER", "ENERGY_BALL", "FLAME_BURST"], "score": 42.7}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 485, "matchups": [{"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "quagsire_shadow", "rating": 932, "opRating": 67}, {"opponent": "claydol", "rating": 783, "opRating": 216}, {"opponent": "diggersby", "rating": 595, "opRating": 404}, {"opponent": "furret", "rating": 521, "opRating": 478}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 281}, {"opponent": "magcargo", "rating": 294}, {"opponent": "gligar", "rating": 301}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 29878}, {"moveId": "BITE", "uses": 28422}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26281}, {"moveId": "ENERGY_BALL", "uses": 19303}, {"moveId": "RETURN", "uses": 6948}, {"moveId": "SOLAR_BEAM", "uses": 5696}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 42.6}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 529, "matchups": [{"opponent": "magcargo", "rating": 735, "opRating": 264}, {"opponent": "diggersby", "rating": 701, "opRating": 298}, {"opponent": "claydol", "rating": 667, "opRating": 332}, {"opponent": "clodsire", "rating": 570, "opRating": 429}, {"opponent": "furret", "rating": 559, "opRating": 440}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "cradily", "rating": 177}, {"opponent": "drampa", "rating": 178}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 329}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 44204}, {"moveId": "RAZOR_LEAF", "uses": 14096}], "chargedMoves": [{"moveId": "SCALD", "uses": 19224}, {"moveId": "ICE_BEAM", "uses": 13183}, {"moveId": "ENERGY_BALL", "uses": 8050}, {"moveId": "LEAF_STORM", "uses": 6990}, {"moveId": "BLIZZARD", "uses": 4605}, {"moveId": "HYDRO_PUMP", "uses": 3862}, {"moveId": "SOLAR_BEAM", "uses": 2313}]}, "moveset": ["BUBBLE", "SCALD", "LEAF_STORM"], "score": 42.5}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 640, "matchups": [{"opponent": "swampert_shadow", "rating": 938, "opRating": 61}, {"opponent": "quagsire_shadow", "rating": 938, "opRating": 61}, {"opponent": "claydol", "rating": 880, "opRating": 119}, {"opponent": "cradily", "rating": 673, "opRating": 326}, {"opponent": "furret", "rating": 512, "opRating": 487}], "counters": [{"opponent": "magcargo", "rating": 89}, {"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "skeledirge", "rating": 147}, {"opponent": "talonflame", "rating": 185}, {"opponent": "jumpluff_shadow", "rating": 274}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25574}, {"moveId": "BUG_BITE", "uses": 23402}, {"moveId": "RAZOR_LEAF", "uses": 9316}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30259}, {"moveId": "X_SCISSOR", "uses": 18643}, {"moveId": "SILVER_WIND", "uses": 5526}, {"moveId": "LEAF_STORM", "uses": 3708}]}, "moveset": ["SHADOW_CLAW", "LEAF_BLADE", "X_SCISSOR"], "score": 42.3}, {"speciesId": "sandygast", "speciesName": "Sandygast", "rating": 478, "matchups": [{"opponent": "magcargo", "rating": 700, "opRating": 300}, {"opponent": "claydol", "rating": 662, "opRating": 337}, {"opponent": "gligar", "rating": 618, "opRating": 381}, {"opponent": "gligar_shadow", "rating": 588, "opRating": 411}, {"opponent": "clodsire", "rating": 585, "opRating": 414}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "drampa", "rating": 160}, {"opponent": "diggersby", "rating": 252}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 381}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20385}, {"moveId": "SAND_ATTACK", "uses": 19942}, {"moveId": "MUD_SHOT", "uses": 17925}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22512}, {"moveId": "SHADOW_BALL", "uses": 19514}, {"moveId": "EARTH_POWER", "uses": 8865}, {"moveId": "SAND_TOMB", "uses": 7509}]}, "moveset": ["ASTONISH", "SCORCHING_SANDS", "SHADOW_BALL"], "score": 42.2}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 469, "matchups": [{"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "quagsire_shadow", "rating": 939, "opRating": 60}, {"opponent": "claydol", "rating": 785, "opRating": 214}, {"opponent": "diggersby", "rating": 567, "opRating": 432}, {"opponent": "piloswine", "rating": 556, "opRating": 443}], "counters": [{"opponent": "jumpluff_shadow", "rating": 160}, {"opponent": "talonflame", "rating": 166}, {"opponent": "skeledirge", "rating": 215}, {"opponent": "gligar", "rating": 270}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30557}, {"moveId": "BITE", "uses": 27743}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25702}, {"moveId": "STONE_EDGE", "uses": 15276}, {"moveId": "EARTHQUAKE", "uses": 9210}, {"moveId": "SAND_TOMB", "uses": 5359}, {"moveId": "SOLAR_BEAM", "uses": 2879}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 42.2}, {"speciesId": "gabite", "speciesName": "Gabite", "rating": 581, "matchups": [{"opponent": "magcargo", "rating": 829, "opRating": 170}, {"opponent": "skeledirge", "rating": 814, "opRating": 185}, {"opponent": "ninetales_shadow", "rating": 670, "opRating": 329}, {"opponent": "clodsire", "rating": 564, "opRating": 435}, {"opponent": "cradily", "rating": 526, "opRating": 473}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "flygon", "rating": 164}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "talonflame", "rating": 285}, {"opponent": "gligar", "rating": 293}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 49427}, {"moveId": "TAKE_DOWN", "uses": 8873}], "chargedMoves": [{"moveId": "DIG", "uses": 19490}, {"moveId": "FLAMETHROWER", "uses": 16545}, {"moveId": "TWISTER", "uses": 12255}, {"moveId": "RETURN", "uses": 10031}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 42}, {"speciesId": "cubone", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 525, "matchups": [{"opponent": "magcargo", "rating": 842, "opRating": 157}, {"opponent": "ninetales_shadow", "rating": 783, "opRating": 216}, {"opponent": "typhlosion_shadow", "rating": 771, "opRating": 228}, {"opponent": "skeledirge", "rating": 748, "opRating": 251}, {"opponent": "clodsire", "rating": 633, "opRating": 366}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "gliscor", "rating": 120}, {"opponent": "gligar", "rating": 160}, {"opponent": "talonflame", "rating": 229}, {"opponent": "furret", "rating": 315}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43989}, {"moveId": "ROCK_SMASH", "uses": 14311}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 34833}, {"moveId": "RETURN", "uses": 11267}, {"moveId": "DIG", "uses": 6685}, {"moveId": "BULLDOZE", "uses": 5483}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "RETURN"], "score": 41.4}, {"speciesId": "deerling", "speciesName": "<PERSON><PERSON>", "rating": 471, "matchups": [{"opponent": "quagsire", "rating": 750, "opRating": 250}, {"opponent": "quagsire_shadow", "rating": 725, "opRating": 274}, {"opponent": "noctowl", "rating": 676, "opRating": 323}, {"opponent": "hippo<PERSON><PERSON>", "rating": 661, "opRating": 338}, {"opponent": "hippow<PERSON>_shadow", "rating": 630, "opRating": 369}], "counters": [{"opponent": "skeledirge", "rating": 89}, {"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 259}, {"opponent": "furret", "rating": 290}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 47442}, {"moveId": "TAKE_DOWN", "uses": 10858}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22490}, {"moveId": "TRAILBLAZE", "uses": 22336}, {"moveId": "SEED_BOMB", "uses": 7143}, {"moveId": "ENERGY_BALL", "uses": 6430}]}, "moveset": ["TACKLE", "WILD_CHARGE", "TRAILBLAZE"], "score": 40.9}, {"speciesId": "dolliv", "speciesName": "Doll<PERSON>", "rating": 532, "matchups": [{"opponent": "swampert_shadow", "rating": 903, "opRating": 96}, {"opponent": "claydol", "rating": 842, "opRating": 157}, {"opponent": "diggersby", "rating": 623, "opRating": 376}, {"opponent": "furret", "rating": 588, "opRating": 411}, {"opponent": "magcargo", "rating": 553, "opRating": 446}], "counters": [{"opponent": "jumpluff_shadow", "rating": 130}, {"opponent": "ninetales_shadow", "rating": 178}, {"opponent": "skeledirge", "rating": 183}, {"opponent": "talonflame", "rating": 203}, {"opponent": "cradily", "rating": 315}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 24378}, {"moveId": "TACKLE", "uses": 21491}, {"moveId": "RAZOR_LEAF", "uses": 12442}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 26394}, {"moveId": "EARTH_POWER", "uses": 16001}, {"moveId": "SEED_BOMB", "uses": 8448}, {"moveId": "ENERGY_BALL", "uses": 7509}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 40.9}, {"speciesId": "thwackey", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 450, "matchups": [{"opponent": "swampert_shadow", "rating": 941, "opRating": 58}, {"opponent": "quagsire_shadow", "rating": 930, "opRating": 69}, {"opponent": "swampert", "rating": 930, "opRating": 69}, {"opponent": "claydol", "rating": 797, "opRating": 202}, {"opponent": "piloswine", "rating": 702, "opRating": 297}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "skeledirge", "rating": 201}, {"opponent": "gligar", "rating": 251}, {"opponent": "talonflame", "rating": 277}, {"opponent": "cradily", "rating": 277}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 33190}, {"moveId": "SCRATCH", "uses": 25110}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 43142}, {"moveId": "ENERGY_BALL", "uses": 15158}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 40.9}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 554, "matchups": [{"opponent": "swampert_shadow", "rating": 879, "opRating": 120}, {"opponent": "claydol", "rating": 823, "opRating": 176}, {"opponent": "quagsire_shadow", "rating": 737, "opRating": 262}, {"opponent": "flygon", "rating": 663, "opRating": 336}, {"opponent": "diggersby", "rating": 577, "opRating": 422}], "counters": [{"opponent": "ninetales_shadow", "rating": 123}, {"opponent": "gligar", "rating": 156}, {"opponent": "gliscor", "rating": 176}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 326}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33914}, {"moveId": "INFESTATION", "uses": 24386}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 19530}, {"moveId": "SLUDGE_BOMB", "uses": 15965}, {"moveId": "POWER_WHIP", "uses": 9775}, {"moveId": "RETURN", "uses": 8925}, {"moveId": "SOLAR_BEAM", "uses": 4111}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 40.8}, {"speciesId": "audino", "speciesName": "Audino", "rating": 397, "matchups": [{"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 652, "opRating": 347}, {"opponent": "monferno", "rating": 589, "opRating": 410}, {"opponent": "combusken", "rating": 557, "opRating": 442}, {"opponent": "tauros_blaze", "rating": 513, "opRating": 486}, {"opponent": "ursaring", "rating": 502, "opRating": 497}], "counters": [{"opponent": "claydol", "rating": 185}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "cradily", "rating": 250}, {"opponent": "talonflame", "rating": 329}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 33508}, {"moveId": "POUND", "uses": 24792}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29682}, {"moveId": "DISARMING_VOICE", "uses": 15862}, {"moveId": "HYPER_BEAM", "uses": 6998}, {"moveId": "DAZZLING_GLEAM", "uses": 5811}]}, "moveset": ["ZEN_HEADBUTT", "BODY_SLAM", "DISARMING_VOICE"], "score": 40.5}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 565, "matchups": [{"opponent": "quagsire_shadow", "rating": 857, "opRating": 142}, {"opponent": "claydol", "rating": 811, "opRating": 188}, {"opponent": "abomasnow_shadow", "rating": 719, "opRating": 280}, {"opponent": "diggersby", "rating": 611, "opRating": 388}, {"opponent": "furret", "rating": 553, "opRating": 446}], "counters": [{"opponent": "ninetales_shadow", "rating": 87}, {"opponent": "skeledirge", "rating": 169}, {"opponent": "gligar", "rating": 225}, {"opponent": "jumpluff_shadow", "rating": 225}, {"opponent": "talonflame", "rating": 237}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38781}, {"moveId": "RAZOR_LEAF", "uses": 19519}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28360}, {"moveId": "SOLAR_BEAM", "uses": 11481}, {"moveId": "DAZZLING_GLEAM", "uses": 11265}, {"moveId": "HYPER_BEAM", "uses": 7228}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 40.4}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 524, "matchups": [{"opponent": "swampert_shadow", "rating": 853, "opRating": 146}, {"opponent": "diggersby", "rating": 837, "opRating": 162}, {"opponent": "claydol", "rating": 801, "opRating": 198}, {"opponent": "quagsire_shadow", "rating": 738, "opRating": 261}, {"opponent": "flygon", "rating": 607, "opRating": 392}], "counters": [{"opponent": "ninetales_shadow", "rating": 111}, {"opponent": "skeledirge", "rating": 133}, {"opponent": "jumpluff_shadow", "rating": 225}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 39626}, {"moveId": "BITE", "uses": 18674}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 27061}, {"moveId": "GRASS_KNOT", "uses": 25781}, {"moveId": "SOLAR_BEAM", "uses": 5476}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "CRUNCH"], "score": 39.8}, {"speciesId": "trapinch_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 487, "matchups": [{"opponent": "clodsire", "rating": 870, "opRating": 129}, {"opponent": "swampert_shadow", "rating": 662, "opRating": 337}, {"opponent": "magcargo", "rating": 583, "opRating": 416}, {"opponent": "furret", "rating": 579, "opRating": 420}, {"opponent": "ninetales_shadow", "rating": 512, "opRating": 487}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "flygon", "rating": 208}, {"opponent": "gligar", "rating": 293}, {"opponent": "cradily", "rating": 347}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24356}, {"moveId": "MUD_SHOT", "uses": 22697}, {"moveId": "STRUGGLE_BUG", "uses": 11282}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22125}, {"moveId": "SCORCHING_SANDS", "uses": 21739}, {"moveId": "DIG", "uses": 7295}, {"moveId": "SAND_TOMB", "uses": 7159}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "SCORCHING_SANDS", "CRUNCH"], "score": 39.8}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 426, "matchups": [{"opponent": "swellow", "rating": 649, "opRating": 350}, {"opponent": "pidgeot", "rating": 604, "opRating": 395}, {"opponent": "pidgeott<PERSON>_shadow", "rating": 599, "opRating": 400}, {"opponent": "farfetchd", "rating": 584, "opRating": 415}, {"opponent": "fletchinder", "rating": 561, "opRating": 438}], "counters": [{"opponent": "magcargo", "rating": 170}, {"opponent": "talonflame", "rating": 196}, {"opponent": "claydol", "rating": 223}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "cradily", "rating": 281}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 32312}, {"moveId": "POUND", "uses": 25988}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22785}, {"moveId": "DAZZLING_GLEAM", "uses": 13713}, {"moveId": "PSYCHIC", "uses": 11145}, {"moveId": "HYPER_BEAM", "uses": 10692}]}, "moveset": ["ZEN_HEADBUTT", "WILD_CHARGE", "DAZZLING_GLEAM"], "score": 39.7}, {"speciesId": "exeggcute_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 462, "matchups": [{"opponent": "swampert_shadow", "rating": 901, "opRating": 98}, {"opponent": "quagsire_shadow", "rating": 901, "opRating": 98}, {"opponent": "swampert", "rating": 901, "opRating": 98}, {"opponent": "quagsire", "rating": 901, "opRating": 98}, {"opponent": "diggersby", "rating": 570, "opRating": 429}], "counters": [{"opponent": "cradily", "rating": 232}, {"opponent": "jumpluff_shadow", "rating": 241}, {"opponent": "gligar", "rating": 259}, {"opponent": "furret", "rating": 284}, {"opponent": "magcargo", "rating": 286}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30638}, {"moveId": "BULLET_SEED", "uses": 27662}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 21830}, {"moveId": "ANCIENT_POWER", "uses": 20786}, {"moveId": "PSYCHIC", "uses": 15595}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "ANCIENT_POWER"], "score": 39.7}, {"speciesId": "weepinbell", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 588, "matchups": [{"opponent": "whimsicott", "rating": 899, "opRating": 100}, {"opponent": "quagsire_shadow", "rating": 661, "opRating": 338}, {"opponent": "flygon", "rating": 593, "opRating": 406}, {"opponent": "jumpluff_shadow", "rating": 550, "opRating": 449}, {"opponent": "flygon_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "skeledirge", "rating": 133}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "talonflame", "rating": 203}, {"opponent": "furret", "rating": 221}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 28349}, {"moveId": "BULLET_SEED", "uses": 20431}, {"moveId": "RAZOR_LEAF", "uses": 9483}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19145}, {"moveId": "POWER_WHIP", "uses": 17567}, {"moveId": "SEED_BOMB", "uses": 14030}, {"moveId": "RETURN", "uses": 7656}]}, "moveset": ["ACID", "POWER_WHIP", "SLUDGE_BOMB"], "score": 39.7}, {"speciesId": "volcanion", "speciesName": "Volcanion", "rating": 525, "matchups": [{"opponent": "abomasnow_shadow", "rating": 936, "opRating": 63}, {"opponent": "piloswine_shadow", "rating": 917, "opRating": 82}, {"opponent": "piloswine", "rating": 898, "opRating": 101}, {"opponent": "ninetales_shadow", "rating": 679, "opRating": 320}, {"opponent": "cradily_shadow", "rating": 679, "opRating": 320}], "counters": [{"opponent": "magcargo", "rating": 123}, {"opponent": "clodsire", "rating": 218}, {"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 232}, {"opponent": "cradily", "rating": 260}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36504}, {"moveId": "WATER_GUN", "uses": 19484}, {"moveId": "TAKE_DOWN", "uses": 2253}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 16871}, {"moveId": "HYDRO_PUMP", "uses": 15540}, {"moveId": "EARTH_POWER", "uses": 13357}, {"moveId": "SLUDGE_BOMB", "uses": 12665}]}, "moveset": ["INCINERATE", "OVERHEAT", "HYDRO_PUMP"], "score": 39.6}, {"speciesId": "exeggcute", "speciesName": "Exeggcute", "rating": 469, "matchups": [{"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 816, "opRating": 183}, {"opponent": "whiscash", "rating": 771, "opRating": 228}, {"opponent": "quagsire", "rating": 679, "opRating": 320}, {"opponent": "quagsire_shadow", "rating": 588, "opRating": 411}, {"opponent": "swampert_shadow", "rating": 535, "opRating": 464}], "counters": [{"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "gligar", "rating": 175}, {"opponent": "furret", "rating": 206}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 302}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30880}, {"moveId": "BULLET_SEED", "uses": 27420}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 19052}, {"moveId": "ANCIENT_POWER", "uses": 17719}, {"moveId": "PSYCHIC", "uses": 13273}, {"moveId": "RETURN", "uses": 8323}]}, "moveset": ["CONFUSION", "SEED_BOMB", "ANCIENT_POWER"], "score": 39.4}, {"speciesId": "rilla<PERSON>m", "speciesName": "R<PERSON>boom", "rating": 449, "matchups": [{"opponent": "swampert_shadow", "rating": 937, "opRating": 62}, {"opponent": "quagsire_shadow", "rating": 926, "opRating": 73}, {"opponent": "claydol", "rating": 751, "opRating": 248}, {"opponent": "piloswine", "rating": 686, "opRating": 313}, {"opponent": "diggersby", "rating": 542, "opRating": 457}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "talonflame", "rating": 166}, {"opponent": "clodsire", "rating": 252}, {"opponent": "gligar", "rating": 293}, {"opponent": "cradily", "rating": 315}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 32399}, {"moveId": "SCRATCH", "uses": 25901}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28442}, {"moveId": "EARTH_POWER", "uses": 19700}, {"moveId": "ENERGY_BALL", "uses": 10156}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "EARTH_POWER"], "score": 39.4}, {"speciesId": "turtwig_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 484, "matchups": [{"opponent": "quagsire", "rating": 862, "opRating": 137}, {"opponent": "quagsire_shadow", "rating": 722, "opRating": 277}, {"opponent": "staravia_shadow", "rating": 700, "opRating": 300}, {"opponent": "flygon", "rating": 551, "opRating": 448}, {"opponent": "drampa", "rating": 551, "opRating": 448}], "counters": [{"opponent": "skeledirge", "rating": 75}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "magcargo", "rating": 230}, {"opponent": "cradily", "rating": 281}, {"opponent": "talonflame", "rating": 355}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 31111}, {"moveId": "RAZOR_LEAF", "uses": 27189}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28114}, {"moveId": "SEED_BOMB", "uses": 20798}, {"moveId": "ENERGY_BALL", "uses": 9297}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 39.4}, {"speciesId": "chatot", "speciesName": "Chatot", "rating": 480, "matchups": [{"opponent": "cradily_shadow", "rating": 687, "opRating": 312}, {"opponent": "lura<PERSON>s", "rating": 632, "opRating": 367}, {"opponent": "noctowl", "rating": 629, "opRating": 370}, {"opponent": "gligar", "rating": 588, "opRating": 411}, {"opponent": "gliscor", "rating": 574, "opRating": 425}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "talonflame", "rating": 196}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 30348}, {"moveId": "PECK", "uses": 27952}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30784}, {"moveId": "NIGHT_SHADE", "uses": 20610}, {"moveId": "HEAT_WAVE", "uses": 7015}]}, "moveset": ["STEEL_WING", "SKY_ATTACK", "NIGHT_SHADE"], "score": 38.9}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 541, "matchups": [{"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "claydol", "rating": 814, "opRating": 185}, {"opponent": "flygon", "rating": 603, "opRating": 396}, {"opponent": "diggersby", "rating": 568, "opRating": 431}, {"opponent": "furret", "rating": 525, "opRating": 474}], "counters": [{"opponent": "typhlosion_shadow", "rating": 111}, {"opponent": "skeledirge", "rating": 133}, {"opponent": "gligar", "rating": 194}, {"opponent": "drampa", "rating": 195}, {"opponent": "talonflame", "rating": 359}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33893}, {"moveId": "INFESTATION", "uses": 24407}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 22498}, {"moveId": "SLUDGE_BOMB", "uses": 19731}, {"moveId": "POWER_WHIP", "uses": 11257}, {"moveId": "SOLAR_BEAM", "uses": 4750}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 38.9}, {"speciesId": "lampent_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 484, "matchups": [{"opponent": "magmortar_shadow", "rating": 903, "opRating": 96}, {"opponent": "skeledirge", "rating": 821, "opRating": 178}, {"opponent": "armarouge", "rating": 810, "opRating": 189}, {"opponent": "typhlosion_shadow", "rating": 775, "opRating": 224}, {"opponent": "ninetales_shadow", "rating": 542, "opRating": 457}], "counters": [{"opponent": "furret", "rating": 115}, {"opponent": "diggersby", "rating": 117}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "clodsire", "rating": 283}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31130}, {"moveId": "ASTONISH", "uses": 27170}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 26010}, {"moveId": "FLAME_BURST", "uses": 25432}, {"moveId": "HEAT_WAVE", "uses": 6750}, {"moveId": "FRUSTRATION", "uses": 100}]}, "moveset": ["ASTONISH", "ENERGY_BALL", "FLAME_BURST"], "score": 38.7}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 484, "matchups": [{"opponent": "claydol", "rating": 790, "opRating": 209}, {"opponent": "spinda", "rating": 698, "opRating": 301}, {"opponent": "run<PERSON><PERSON>", "rating": 629, "opRating": 370}, {"opponent": "furret", "rating": 534, "opRating": 465}, {"opponent": "quagsire_shadow", "rating": 530, "opRating": 469}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 207}, {"opponent": "magcargo", "rating": 222}, {"opponent": "swampert_shadow", "rating": 238}, {"opponent": "cradily", "rating": 347}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 45578}, {"moveId": "TAKE_DOWN", "uses": 12722}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 18626}, {"moveId": "WILD_CHARGE", "uses": 16667}, {"moveId": "MEGAHORN", "uses": 13139}, {"moveId": "HYPER_BEAM", "uses": 6442}, {"moveId": "SOLAR_BEAM", "uses": 3401}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "TRAILBLAZE"], "score": 38.7}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 446, "matchups": [{"opponent": "swampert_shadow", "rating": 939, "opRating": 60}, {"opponent": "quagsire_shadow", "rating": 928, "opRating": 71}, {"opponent": "swampert", "rating": 928, "opRating": 71}, {"opponent": "claydol", "rating": 661, "opRating": 338}, {"opponent": "clodsire", "rating": 605, "opRating": 394}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 137}, {"opponent": "gligar", "rating": 278}, {"opponent": "magcargo", "rating": 294}, {"opponent": "furret", "rating": 350}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30035}, {"moveId": "BITE", "uses": 28265}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25732}, {"moveId": "STONE_EDGE", "uses": 15277}, {"moveId": "EARTHQUAKE", "uses": 9201}, {"moveId": "SAND_TOMB", "uses": 5418}, {"moveId": "SOLAR_BEAM", "uses": 2881}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 38.7}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 439, "matchups": [{"opponent": "chesnaught", "rating": 850, "opRating": 149}, {"opponent": "bewear", "rating": 776, "opRating": 223}, {"opponent": "flygon_shadow", "rating": 666, "opRating": 333}, {"opponent": "flygon", "rating": 653, "opRating": 346}, {"opponent": "drampa", "rating": 644, "opRating": 355}], "counters": [{"opponent": "clodsire", "rating": 151}, {"opponent": "talonflame", "rating": 177}, {"opponent": "magcargo", "rating": 200}, {"opponent": "gligar", "rating": 267}, {"opponent": "cradily", "rating": 284}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 7342}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4210}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3661}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3526}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3302}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3296}, {"moveId": "CHARM", "uses": 3258}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3243}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2957}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2931}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2847}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2827}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2811}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2722}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2588}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2564}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2323}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2123}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 26882}, {"moveId": "HYPER_BEAM", "uses": 20671}, {"moveId": "SOLAR_BEAM", "uses": 10826}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 38.6}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 445, "matchups": [{"opponent": "lilligant_hisuian", "rating": 930, "opRating": 69}, {"opponent": "leavanny", "rating": 897, "opRating": 102}, {"opponent": "parasect", "rating": 856, "opRating": 143}, {"opponent": "chesnaught", "rating": 831, "opRating": 168}, {"opponent": "roserade", "rating": 590, "opRating": 409}], "counters": [{"opponent": "magcargo", "rating": 141}, {"opponent": "clodsire", "rating": 197}, {"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 221}, {"opponent": "talonflame", "rating": 270}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32322}, {"moveId": "STEEL_WING", "uses": 25978}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33625}, {"moveId": "HYPER_BEAM", "uses": 17300}, {"moveId": "HEAT_WAVE", "uses": 7484}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 38.6}, {"speciesId": "bayleef_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 547, "matchups": [{"opponent": "swampert_shadow", "rating": 933, "opRating": 66}, {"opponent": "claydol", "rating": 806, "opRating": 193}, {"opponent": "diggersby", "rating": 633, "opRating": 366}, {"opponent": "furret", "rating": 573, "opRating": 426}, {"opponent": "magcargo", "rating": 573, "opRating": 426}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 122}, {"opponent": "skeledirge", "rating": 176}, {"opponent": "drampa", "rating": 213}, {"opponent": "cradily", "rating": 375}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 25781}, {"moveId": "TACKLE", "uses": 19275}, {"moveId": "RAZOR_LEAF", "uses": 13225}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 25261}, {"moveId": "ANCIENT_POWER", "uses": 23910}, {"moveId": "ENERGY_BALL", "uses": 9049}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MAGICAL_LEAF", "ANCIENT_POWER", "ENERGY_BALL"], "score": 38.5}, {"speciesId": "arboliva", "speciesName": "Arboliva", "rating": 506, "matchups": [{"opponent": "swampert_shadow", "rating": 917, "opRating": 82}, {"opponent": "quagsire_shadow", "rating": 891, "opRating": 108}, {"opponent": "claydol", "rating": 786, "opRating": 213}, {"opponent": "flygon", "rating": 578, "opRating": 421}, {"opponent": "diggersby", "rating": 560, "opRating": 439}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 192}, {"opponent": "magcargo", "rating": 222}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 350}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 24839}, {"moveId": "TACKLE", "uses": 21225}, {"moveId": "RAZOR_LEAF", "uses": 12213}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 26413}, {"moveId": "EARTH_POWER", "uses": 16012}, {"moveId": "SEED_BOMB", "uses": 8446}, {"moveId": "ENERGY_BALL", "uses": 7494}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 38.3}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 451, "matchups": [{"opponent": "noctowl", "rating": 833, "opRating": 166}, {"opponent": "staravia", "rating": 779, "opRating": 220}, {"opponent": "staraptor", "rating": 779, "opRating": 220}, {"opponent": "pidgeot", "rating": 698, "opRating": 301}, {"opponent": "talonflame", "rating": 543, "opRating": 456}], "counters": [{"opponent": "clodsire", "rating": 137}, {"opponent": "diggersby", "rating": 163}, {"opponent": "gligar", "rating": 179}, {"opponent": "flygon", "rating": 192}, {"opponent": "cradily", "rating": 201}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 29893}, {"moveId": "ASTONISH", "uses": 28407}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 26186}, {"moveId": "OMINOUS_WIND", "uses": 20673}, {"moveId": "THUNDER", "uses": 11377}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 38.3}, {"speciesId": "turtwig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 480, "matchups": [{"opponent": "whiscash", "rating": 907, "opRating": 92}, {"opponent": "quagsire_shadow", "rating": 862, "opRating": 137}, {"opponent": "quagsire", "rating": 862, "opRating": 137}, {"opponent": "flygon", "rating": 611, "opRating": 388}, {"opponent": "flygon_shadow", "rating": 551, "opRating": 448}], "counters": [{"opponent": "magcargo", "rating": 153}, {"opponent": "gligar", "rating": 164}, {"opponent": "cradily", "rating": 208}, {"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "clodsire", "rating": 262}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 31589}, {"moveId": "RAZOR_LEAF", "uses": 26711}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24553}, {"moveId": "SEED_BOMB", "uses": 19025}, {"moveId": "ENERGY_BALL", "uses": 8510}, {"moveId": "RETURN", "uses": 6172}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 38.3}, {"speciesId": "bayleef", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 557, "matchups": [{"opponent": "quagsire_shadow", "rating": 926, "opRating": 73}, {"opponent": "swampert_shadow", "rating": 911, "opRating": 88}, {"opponent": "claydol", "rating": 838, "opRating": 161}, {"opponent": "diggersby", "rating": 707, "opRating": 292}, {"opponent": "flygon", "rating": 588, "opRating": 411}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 92}, {"opponent": "skeledirge", "rating": 147}, {"opponent": "magcargo", "rating": 209}, {"opponent": "cradily", "rating": 343}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 25025}, {"moveId": "TACKLE", "uses": 19503}, {"moveId": "RAZOR_LEAF", "uses": 13764}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21632}, {"moveId": "ANCIENT_POWER", "uses": 19768}, {"moveId": "RETURN", "uses": 9093}, {"moveId": "ENERGY_BALL", "uses": 7784}]}, "moveset": ["MAGICAL_LEAF", "ANCIENT_POWER", "ENERGY_BALL"], "score": 38.2}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 551, "matchups": [{"opponent": "staravia", "rating": 720, "opRating": 279}, {"opponent": "cradily_shadow", "rating": 695, "opRating": 304}, {"opponent": "quagsire_shadow", "rating": 604, "opRating": 395}, {"opponent": "flygon", "rating": 529, "opRating": 470}, {"opponent": "jumpluff_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "magcargo", "rating": 119}, {"opponent": "gligar", "rating": 156}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "furret", "rating": 221}, {"opponent": "talonflame", "rating": 237}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 42250}, {"moveId": "RAZOR_LEAF", "uses": 16050}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23681}, {"moveId": "PETAL_BLIZZARD", "uses": 14647}, {"moveId": "MOONBLAST", "uses": 13998}, {"moveId": "SOLAR_BEAM", "uses": 5866}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 37.9}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 529, "matchups": [{"opponent": "shiftry_shadow", "rating": 921, "opRating": 78}, {"opponent": "obstagoon_shadow", "rating": 824, "opRating": 175}, {"opponent": "sceptile", "rating": 785, "opRating": 214}, {"opponent": "claydol", "rating": 632, "opRating": 367}, {"opponent": "flygon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "clodsire", "rating": 151}, {"opponent": "gligar", "rating": 164}, {"opponent": "magcargo", "rating": 217}, {"opponent": "talonflame", "rating": 218}, {"opponent": "jumpluff_shadow", "rating": 228}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 33415}, {"moveId": "CONFUSION", "uses": 24885}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 24574}, {"moveId": "ENERGY_BALL", "uses": 23458}, {"moveId": "PSYBEAM", "uses": 10323}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 37.6}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 543, "matchups": [{"opponent": "swampert_shadow", "rating": 923, "opRating": 76}, {"opponent": "quagsire_shadow", "rating": 899, "opRating": 100}, {"opponent": "claydol", "rating": 600, "opRating": 399}, {"opponent": "furret", "rating": 596, "opRating": 403}, {"opponent": "diggersby", "rating": 560, "opRating": 439}], "counters": [{"opponent": "jumpluff_shadow", "rating": 81}, {"opponent": "skeledirge", "rating": 133}, {"opponent": "gligar", "rating": 190}, {"opponent": "talonflame", "rating": 262}, {"opponent": "cradily", "rating": 298}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 31518}, {"moveId": "MAGICAL_LEAF", "uses": 26782}], "chargedMoves": [{"moveId": "AURA_SPHERE", "uses": 15812}, {"moveId": "TRAILBLAZE", "uses": 15401}, {"moveId": "AERIAL_ACE", "uses": 13779}, {"moveId": "NIGHT_SHADE", "uses": 8810}, {"moveId": "ENERGY_BALL", "uses": 4465}]}, "moveset": ["MAGICAL_LEAF", "AERIAL_ACE", "AURA_SPHERE"], "score": 37.1}, {"speciesId": "weepin<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 537, "matchups": [{"opponent": "lileep", "rating": 760, "opRating": 239}, {"opponent": "cradily_shadow", "rating": 682, "opRating": 317}, {"opponent": "claydol", "rating": 621, "opRating": 378}, {"opponent": "quagsire_shadow", "rating": 560, "opRating": 439}, {"opponent": "flygon", "rating": 521, "opRating": 478}], "counters": [{"opponent": "diggersby", "rating": 117}, {"opponent": "gligar", "rating": 156}, {"opponent": "magcargo", "rating": 158}, {"opponent": "talonflame", "rating": 237}, {"opponent": "furret", "rating": 253}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 28610}, {"moveId": "BULLET_SEED", "uses": 20938}, {"moveId": "RAZOR_LEAF", "uses": 8743}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22723}, {"moveId": "POWER_WHIP", "uses": 19714}, {"moveId": "SEED_BOMB", "uses": 15832}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "POWER_WHIP", "SLUDGE_BOMB"], "score": 37}, {"speciesId": "bulbasaur", "speciesName": "Bulbasaur", "rating": 498, "matchups": [{"opponent": "whimsicott", "rating": 858, "opRating": 141}, {"opponent": "swampert_shadow", "rating": 787, "opRating": 212}, {"opponent": "quagsire_shadow", "rating": 687, "opRating": 312}, {"opponent": "claydol", "rating": 620, "opRating": 379}, {"opponent": "flygon_shadow", "rating": 558, "opRating": 441}], "counters": [{"opponent": "ninetales_shadow", "rating": 111}, {"opponent": "drampa", "rating": 147}, {"opponent": "clodsire", "rating": 286}, {"opponent": "talonflame", "rating": 314}, {"opponent": "cradily", "rating": 326}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 36678}, {"moveId": "TACKLE", "uses": 21622}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19072}, {"moveId": "POWER_WHIP", "uses": 17573}, {"moveId": "SEED_BOMB", "uses": 13989}, {"moveId": "RETURN", "uses": 7679}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 36.4}, {"speciesId": "lombre", "speciesName": "Lombre", "rating": 465, "matchups": [{"opponent": "quagsire_shadow", "rating": 788, "opRating": 211}, {"opponent": "claydol", "rating": 595, "opRating": 404}, {"opponent": "piloswine", "rating": 573, "opRating": 426}, {"opponent": "diggersby", "rating": 545, "opRating": 454}, {"opponent": "swampert_shadow", "rating": 521, "opRating": 478}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "drampa", "rating": 143}, {"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 217}, {"opponent": "talonflame", "rating": 300}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 43020}, {"moveId": "RAZOR_LEAF", "uses": 15280}], "chargedMoves": [{"moveId": "SCALD", "uses": 21767}, {"moveId": "ICE_BEAM", "uses": 16294}, {"moveId": "GRASS_KNOT", "uses": 13932}, {"moveId": "BUBBLE_BEAM", "uses": 6389}]}, "moveset": ["BUBBLE", "SCALD", "GRASS_KNOT"], "score": 36}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 486, "matchups": [{"opponent": "swampert_shadow", "rating": 855, "opRating": 144}, {"opponent": "claydol", "rating": 777, "opRating": 222}, {"opponent": "quagsire_shadow", "rating": 718, "opRating": 281}, {"opponent": "diggersby", "rating": 562, "opRating": 437}, {"opponent": "flygon", "rating": 558, "opRating": 441}], "counters": [{"opponent": "magcargo", "rating": 123}, {"opponent": "talonflame", "rating": 207}, {"opponent": "gligar", "rating": 225}, {"opponent": "jumpluff_shadow", "rating": 245}, {"opponent": "cradily", "rating": 347}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29254}, {"moveId": "BULLET_SEED", "uses": 29046}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33242}, {"moveId": "PETAL_BLIZZARD", "uses": 17911}, {"moveId": "SOLAR_BEAM", "uses": 7134}]}, "moveset": ["BULLET_SEED", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 35.7}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Eldegoss", "rating": 503, "matchups": [{"opponent": "quagsire_shadow", "rating": 903, "opRating": 96}, {"opponent": "quagsire", "rating": 903, "opRating": 96}, {"opponent": "swampert_shadow", "rating": 630, "opRating": 369}, {"opponent": "claydol", "rating": 554, "opRating": 445}, {"opponent": "piloswine", "rating": 525, "opRating": 474}], "counters": [{"opponent": "ninetales_shadow", "rating": 87}, {"opponent": "gligar", "rating": 95}, {"opponent": "jumpluff_shadow", "rating": 124}, {"opponent": "talonflame", "rating": 155}, {"opponent": "cradily", "rating": 281}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35266}, {"moveId": "RAZOR_LEAF", "uses": 23034}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 43142}, {"moveId": "ENERGY_BALL", "uses": 15158}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "ENERGY_BALL"], "score": 35}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 633, "matchups": [{"opponent": "talonflame", "rating": 801, "opRating": 198}, {"opponent": "jumpluff_shadow", "rating": 768, "opRating": 231}, {"opponent": "gliscor", "rating": 752, "opRating": 247}, {"opponent": "drampa", "rating": 607, "opRating": 392}, {"opponent": "furret", "rating": 537, "opRating": 462}], "counters": [{"opponent": "claydol", "rating": 37}, {"opponent": "swampert_shadow", "rating": 113}, {"opponent": "clodsire", "rating": 132}, {"opponent": "diggersby", "rating": 140}, {"opponent": "cradily", "rating": 201}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 34062}, {"moveId": "ASTONISH", "uses": 24238}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 31026}, {"moveId": "THUNDERBOLT", "uses": 19023}, {"moveId": "THUNDER", "uses": 8260}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 35}, {"speciesId": "roselia", "speciesName": "Roselia", "rating": 455, "matchups": [{"opponent": "whimsicott", "rating": 900, "opRating": 100}, {"opponent": "dart<PERSON>", "rating": 877, "opRating": 122}, {"opponent": "sceptile_shadow", "rating": 790, "opRating": 209}, {"opponent": "cradily_shadow", "rating": 700, "opRating": 300}, {"opponent": "claydol", "rating": 540, "opRating": 459}], "counters": [{"opponent": "clodsire", "rating": 103}, {"opponent": "gligar", "rating": 156}, {"opponent": "magcargo", "rating": 192}, {"opponent": "talonflame", "rating": 237}, {"opponent": "cradily", "rating": 253}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 27096}, {"moveId": "MAGICAL_LEAF", "uses": 20666}, {"moveId": "RAZOR_LEAF", "uses": 10585}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 26088}, {"moveId": "PETAL_BLIZZARD", "uses": 17837}, {"moveId": "DAZZLING_GLEAM", "uses": 14381}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 34.9}, {"speciesId": "gabite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 562, "matchups": [{"opponent": "magcargo", "rating": 799, "opRating": 200}, {"opponent": "skeledirge", "rating": 784, "opRating": 215}, {"opponent": "typhlosion_shadow", "rating": 708, "opRating": 291}, {"opponent": "ninetales_shadow", "rating": 602, "opRating": 397}, {"opponent": "quagsire_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "gligar", "rating": 95}, {"opponent": "furret", "rating": 159}, {"opponent": "talonflame", "rating": 311}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 49375}, {"moveId": "TAKE_DOWN", "uses": 8925}], "chargedMoves": [{"moveId": "DIG", "uses": 23045}, {"moveId": "FLAMETHROWER", "uses": 20070}, {"moveId": "TWISTER", "uses": 15075}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 34.8}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 487, "matchups": [{"opponent": "swampert_shadow", "rating": 903, "opRating": 96}, {"opponent": "quagsire_shadow", "rating": 879, "opRating": 120}, {"opponent": "claydol", "rating": 786, "opRating": 213}, {"opponent": "diggersby", "rating": 596, "opRating": 403}, {"opponent": "magcargo", "rating": 531, "opRating": 468}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 185}, {"opponent": "gligar", "rating": 190}, {"opponent": "furret", "rating": 200}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 24854}, {"moveId": "MAGICAL_LEAF", "uses": 23125}, {"moveId": "CHARGE_BEAM", "uses": 10322}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 15087}, {"moveId": "PSYCHIC", "uses": 13790}, {"moveId": "LEAF_STORM", "uses": 11871}, {"moveId": "DAZZLING_GLEAM", "uses": 10858}, {"moveId": "HYPER_BEAM", "uses": 6716}]}, "moveset": ["MAGICAL_LEAF", "SEED_BOMB", "LEAF_STORM"], "score": 34.6}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 462, "matchups": [{"opponent": "swampert_shadow", "rating": 916, "opRating": 83}, {"opponent": "quagsire_shadow", "rating": 881, "opRating": 118}, {"opponent": "claydol", "rating": 790, "opRating": 209}, {"opponent": "diggersby", "rating": 603, "opRating": 396}, {"opponent": "furret", "rating": 549, "opRating": 450}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 194}, {"opponent": "magcargo", "rating": 200}, {"opponent": "talonflame", "rating": 248}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 36819}, {"moveId": "CHARM", "uses": 21481}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31692}, {"moveId": "PLAY_ROUGH", "uses": 15247}, {"moveId": "ENERGY_BALL", "uses": 11269}]}, "moveset": ["LEAFAGE", "GRASS_KNOT", "PLAY_ROUGH"], "score": 34.3}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 466, "matchups": [{"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "quagsire_shadow", "rating": 882, "opRating": 117}, {"opponent": "claydol", "rating": 825, "opRating": 174}, {"opponent": "diggersby", "rating": 613, "opRating": 386}, {"opponent": "furret", "rating": 594, "opRating": 405}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "cradily", "rating": 208}, {"opponent": "magcargo", "rating": 209}, {"opponent": "gligar", "rating": 217}, {"opponent": "talonflame", "rating": 225}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 7553}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4389}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3865}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3684}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3401}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3398}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3368}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3061}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3054}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2959}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2899}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2886}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2791}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2672}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2597}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2389}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2109}, {"moveId": "ZEN_HEADBUTT", "uses": 1236}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31389}, {"moveId": "ENERGY_BALL", "uses": 11078}, {"moveId": "SEED_FLARE", "uses": 9311}, {"moveId": "SOLAR_BEAM", "uses": 6429}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "SEED_FLARE"], "score": 34.3}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 580, "matchups": [{"opponent": "swampert_shadow", "rating": 691, "opRating": 308}, {"opponent": "cradily", "rating": 621, "opRating": 378}, {"opponent": "furret", "rating": 582, "opRating": 417}, {"opponent": "flygon", "rating": 554, "opRating": 445}, {"opponent": "claydol", "rating": 539, "opRating": 460}], "counters": [{"opponent": "skeledirge", "rating": 68}, {"opponent": "ninetales_shadow", "rating": 75}, {"opponent": "typhlosion_shadow", "rating": 81}, {"opponent": "talonflame", "rating": 137}, {"opponent": "magcargo", "rating": 260}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30297}, {"moveId": "METAL_CLAW", "uses": 28003}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18591}, {"moveId": "MIRROR_SHOT", "uses": 14326}, {"moveId": "THUNDER", "uses": 7522}, {"moveId": "FLASH_CANNON", "uses": 7425}, {"moveId": "RETURN", "uses": 7412}, {"moveId": "ACID_SPRAY", "uses": 3043}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "FLASH_CANNON"], "score": 34.1}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 366, "matchups": [{"opponent": "gliscor_shadow", "rating": 725, "opRating": 274}, {"opponent": "staravia_shadow", "rating": 698, "opRating": 301}, {"opponent": "flygon_shadow", "rating": 657, "opRating": 342}, {"opponent": "flygon", "rating": 644, "opRating": 355}, {"opponent": "gligar", "rating": 536, "opRating": 463}], "counters": [{"opponent": "magcargo", "rating": 136}, {"opponent": "furret", "rating": 178}, {"opponent": "talonflame", "rating": 240}, {"opponent": "clodsire", "rating": 240}, {"opponent": "cradily", "rating": 288}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5246}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4511}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4157}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4022}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3969}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3614}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3595}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3420}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3419}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3407}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3384}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3370}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3086}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3066}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2760}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2454}, {"moveId": "ZEN_HEADBUTT", "uses": 967}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 32878}, {"moveId": "FOCUS_BLAST", "uses": 12929}, {"moveId": "THUNDER", "uses": 7771}, {"moveId": "GIGA_IMPACT", "uses": 4610}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "FOCUS_BLAST"], "score": 33.9}, {"speciesId": "kartana", "speciesName": "Kartana", "rating": 458, "matchups": [{"opponent": "swampert_shadow", "rating": 873, "opRating": 126}, {"opponent": "quagsire_shadow", "rating": 841, "opRating": 158}, {"opponent": "piloswine", "rating": 772, "opRating": 227}, {"opponent": "claydol", "rating": 613, "opRating": 386}, {"opponent": "flygon", "rating": 594, "opRating": 405}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "skeledirge", "rating": 125}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "magcargo", "rating": 170}, {"opponent": "cradily", "rating": 368}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 35440}, {"moveId": "RAZOR_LEAF", "uses": 22860}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 24103}, {"moveId": "NIGHT_SLASH", "uses": 13211}, {"moveId": "AERIAL_ACE", "uses": 10734}, {"moveId": "X_SCISSOR", "uses": 10287}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "NIGHT_SLASH"], "score": 33.2}, {"speciesId": "rowlet", "speciesName": "<PERSON><PERSON>", "rating": 451, "matchups": [{"opponent": "quagsire", "rating": 961, "opRating": 38}, {"opponent": "swampert_shadow", "rating": 931, "opRating": 68}, {"opponent": "quagsire_shadow", "rating": 925, "opRating": 74}, {"opponent": "marowak_shadow", "rating": 905, "opRating": 94}, {"opponent": "claydol", "rating": 899, "opRating": 100}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "clodsire", "rating": 127}, {"opponent": "talonflame", "rating": 177}, {"opponent": "cradily", "rating": 250}, {"opponent": "magcargo", "rating": 269}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 16770}, {"moveId": "MAGICAL_LEAF", "uses": 16401}, {"moveId": "TACKLE", "uses": 14012}, {"moveId": "RAZOR_LEAF", "uses": 11146}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 40252}, {"moveId": "ENERGY_BALL", "uses": 18048}]}, "moveset": ["LEAFAGE", "SEED_BOMB", "ENERGY_BALL"], "score": 32.7}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 424, "matchups": [{"opponent": "swampert", "rating": 963, "opRating": 36}, {"opponent": "swampert_shadow", "rating": 947, "opRating": 52}, {"opponent": "quagsire_shadow", "rating": 930, "opRating": 69}, {"opponent": "claydol", "rating": 886, "opRating": 113}, {"opponent": "diggersby", "rating": 524, "opRating": 475}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 200}, {"opponent": "gligar", "rating": 217}, {"opponent": "clodsire", "rating": 218}, {"opponent": "cradily", "rating": 239}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 8191}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4271}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4194}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3685}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3586}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3303}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3295}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2945}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2882}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2842}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2783}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2779}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2732}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2591}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2542}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2312}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2070}, {"moveId": "ZEN_HEADBUTT", "uses": 1212}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31356}, {"moveId": "ENERGY_BALL", "uses": 11077}, {"moveId": "SEED_FLARE", "uses": 9318}, {"moveId": "SOLAR_BEAM", "uses": 6417}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 32}, {"speciesId": "sunflora", "speciesName": "Sunflora", "rating": 496, "matchups": [{"opponent": "quagsire_shadow", "rating": 859, "opRating": 140}, {"opponent": "claydol", "rating": 814, "opRating": 185}, {"opponent": "flygon", "rating": 602, "opRating": 397}, {"opponent": "diggersby", "rating": 590, "opRating": 409}, {"opponent": "furret", "rating": 537, "opRating": 462}], "counters": [{"opponent": "ninetales_shadow", "rating": 87}, {"opponent": "gligar", "rating": 110}, {"opponent": "clodsire", "rating": 163}, {"opponent": "cradily", "rating": 243}, {"opponent": "talonflame", "rating": 277}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 40527}, {"moveId": "RAZOR_LEAF", "uses": 17773}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23920}, {"moveId": "LEAF_STORM", "uses": 19640}, {"moveId": "PETAL_BLIZZARD", "uses": 8190}, {"moveId": "SOLAR_BEAM", "uses": 6605}]}, "moveset": ["BULLET_SEED", "LEAF_STORM", "SLUDGE_BOMB"], "score": 30.8}, {"speciesId": "snover", "speciesName": "Snover", "rating": 488, "matchups": [{"opponent": "cradily_shadow", "rating": 658, "opRating": 341}, {"opponent": "flygon_shadow", "rating": 602, "opRating": 397}, {"opponent": "piloswine", "rating": 588, "opRating": 411}, {"opponent": "abomasnow_shadow", "rating": 549, "opRating": 450}, {"opponent": "swampert_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "skeledirge", "rating": 57}, {"opponent": "typhlosion_shadow", "rating": 68}, {"opponent": "magcargo", "rating": 81}, {"opponent": "ninetales_shadow", "rating": 87}, {"opponent": "talonflame", "rating": 329}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 23651}, {"moveId": "ICE_SHARD", "uses": 18576}, {"moveId": "LEAFAGE", "uses": 16147}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 24282}, {"moveId": "ENERGY_BALL", "uses": 14424}, {"moveId": "STOMP", "uses": 12384}, {"moveId": "RETURN", "uses": 7145}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 30.5}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 445, "matchups": [{"opponent": "quagsire_shadow", "rating": 904, "opRating": 95}, {"opponent": "swampert", "rating": 893, "opRating": 106}, {"opponent": "swampert_shadow", "rating": 858, "opRating": 141}, {"opponent": "piloswine", "rating": 687, "opRating": 312}, {"opponent": "claydol", "rating": 503, "opRating": 496}], "counters": [{"opponent": "skeledirge", "rating": 75}, {"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 300}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19586}, {"moveId": "BULLET_SEED", "uses": 18587}, {"moveId": "EXTRASENSORY", "uses": 15088}, {"moveId": "ZEN_HEADBUTT", "uses": 5065}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26304}, {"moveId": "PSYCHIC", "uses": 24361}, {"moveId": "SOLAR_BEAM", "uses": 7586}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 30.3}, {"speciesId": "bulbasaur_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 486, "matchups": [{"opponent": "swampert_shadow", "rating": 770, "opRating": 229}, {"opponent": "claydol", "rating": 675, "opRating": 325}, {"opponent": "quagsire_shadow", "rating": 637, "opRating": 362}, {"opponent": "flygon", "rating": 558, "opRating": 441}, {"opponent": "diggersby", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "magcargo", "rating": 158}, {"opponent": "furret", "rating": 159}, {"opponent": "talonflame", "rating": 344}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 37078}, {"moveId": "TACKLE", "uses": 21222}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22718}, {"moveId": "POWER_WHIP", "uses": 19729}, {"moveId": "SEED_BOMB", "uses": 15837}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 30.2}, {"speciesId": "ferroseed", "speciesName": "Ferroseed", "rating": 406, "matchups": [{"opponent": "lileep", "rating": 659, "opRating": 340}, {"opponent": "victreebel", "rating": 651, "opRating": 348}, {"opponent": "abomasnow_shadow", "rating": 613, "opRating": 386}, {"opponent": "cradily", "rating": 542, "opRating": 457}, {"opponent": "drampa", "rating": 516, "opRating": 483}], "counters": [{"opponent": "skeledirge", "rating": 68}, {"opponent": "magcargo", "rating": 119}, {"opponent": "swampert_shadow", "rating": 180}, {"opponent": "diggersby", "rating": 224}, {"opponent": "talonflame", "rating": 288}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 30787}, {"moveId": "TACKLE", "uses": 27513}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 25231}, {"moveId": "RETURN", "uses": 17080}, {"moveId": "FLASH_CANNON", "uses": 7999}, {"moveId": "GYRO_BALL", "uses": 7978}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "RETURN"], "score": 28.4}, {"speciesId": "ferroseed_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 405, "matchups": [{"opponent": "whimsicott", "rating": 886, "opRating": 113}, {"opponent": "lileep_shadow", "rating": 718, "opRating": 281}, {"opponent": "cradily_shadow", "rating": 630, "opRating": 369}, {"opponent": "flygon_shadow", "rating": 558, "opRating": 441}, {"opponent": "abomasnow_shadow", "rating": 529, "opRating": 470}], "counters": [{"opponent": "skeledirge", "rating": 68}, {"opponent": "ninetales_shadow", "rating": 75}, {"opponent": "magcargo", "rating": 106}, {"opponent": "talonflame", "rating": 177}, {"opponent": "diggersby", "rating": 244}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 31440}, {"moveId": "TACKLE", "uses": 26860}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 35602}, {"moveId": "FLASH_CANNON", "uses": 11383}, {"moveId": "GYRO_BALL", "uses": 11110}, {"moveId": "FRUSTRATION", "uses": 137}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "FLASH_CANNON"], "score": 27.6}, {"speciesId": "ferroth<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 556, "matchups": [{"opponent": "quagsire_shadow", "rating": 726, "opRating": 273}, {"opponent": "swampert_shadow", "rating": 660, "opRating": 339}, {"opponent": "flygon", "rating": 652, "opRating": 347}, {"opponent": "clodsire", "rating": 609, "opRating": 390}, {"opponent": "cradily", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 40}, {"opponent": "ninetales_shadow", "rating": 51}, {"opponent": "skeledirge", "rating": 57}, {"opponent": "magcargo", "rating": 102}, {"opponent": "jumpluff_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30562}, {"moveId": "METAL_CLAW", "uses": 27738}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20888}, {"moveId": "MIRROR_SHOT", "uses": 16410}, {"moveId": "THUNDER", "uses": 8866}, {"moveId": "FLASH_CANNON", "uses": 8555}, {"moveId": "ACID_SPRAY", "uses": 3557}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "THUNDER"], "score": 27.6}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 314, "matchups": [{"opponent": "rotom_heat", "rating": 813, "opRating": 186}, {"opponent": "incineroar", "rating": 777, "opRating": 222}, {"opponent": "salazzle", "rating": 759, "opRating": 240}, {"opponent": "magcargo", "rating": 631, "opRating": 368}, {"opponent": "magmortar_shadow", "rating": 631, "opRating": 368}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 83}, {"opponent": "talonflame", "rating": 159}, {"opponent": "diggersby", "rating": 163}, {"opponent": "cradily", "rating": 208}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5270}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4573}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4185}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4048}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3992}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3613}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3562}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3448}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3398}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3383}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3382}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3341}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3082}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2982}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2737}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2398}, {"moveId": "ZEN_HEADBUTT", "uses": 851}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 32805}, {"moveId": "FOCUS_BLAST", "uses": 12943}, {"moveId": "THUNDER", "uses": 7751}, {"moveId": "GIGA_IMPACT", "uses": 4604}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HIDDEN_POWER_GROUND", "THUNDER", "FOCUS_BLAST"], "score": 27.6}, {"speciesId": "snover_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 465, "matchups": [{"opponent": "claydol", "rating": 771, "opRating": 228}, {"opponent": "clodsire", "rating": 742, "opRating": 257}, {"opponent": "cradily", "rating": 658, "opRating": 341}, {"opponent": "flygon", "rating": 602, "opRating": 397}, {"opponent": "flygon_shadow", "rating": 598, "opRating": 401}], "counters": [{"opponent": "skeledirge", "rating": 57}, {"opponent": "magcargo", "rating": 68}, {"opponent": "talonflame", "rating": 96}, {"opponent": "diggersby", "rating": 175}, {"opponent": "furret", "rating": 190}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 23913}, {"moveId": "ICE_SHARD", "uses": 18461}, {"moveId": "LEAFAGE", "uses": 15983}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 27354}, {"moveId": "ENERGY_BALL", "uses": 16213}, {"moveId": "STOMP", "uses": 14684}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 27.6}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 444, "matchups": [{"opponent": "quagsire_shadow", "rating": 874, "opRating": 125}, {"opponent": "swampert_shadow", "rating": 858, "opRating": 141}, {"opponent": "swampert", "rating": 858, "opRating": 141}, {"opponent": "piloswine", "rating": 652, "opRating": 347}, {"opponent": "flygon_shadow", "rating": 515, "opRating": 484}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "skeledirge", "rating": 75}, {"opponent": "gligar", "rating": 99}, {"opponent": "cradily", "rating": 326}, {"opponent": "talonflame", "rating": 370}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19443}, {"moveId": "BULLET_SEED", "uses": 19217}, {"moveId": "EXTRASENSORY", "uses": 14872}, {"moveId": "ZEN_HEADBUTT", "uses": 4770}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26296}, {"moveId": "PSYCHIC", "uses": 24339}, {"moveId": "SOLAR_BEAM", "uses": 7522}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 25.5}, {"speciesId": "skiddo", "speciesName": "Skiddo", "rating": 336, "matchups": [{"opponent": "obstagoon_shadow", "rating": 678, "opRating": 321}, {"opponent": "marowak_shadow", "rating": 586, "opRating": 413}, {"opponent": "quagsire", "rating": 572, "opRating": 427}, {"opponent": "quagsire_shadow", "rating": 523, "opRating": 476}, {"opponent": "dubwool", "rating": 509, "opRating": 490}], "counters": [{"opponent": "gligar", "rating": 83}, {"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 107}, {"opponent": "clodsire", "rating": 117}, {"opponent": "cradily", "rating": 239}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 38274}, {"moveId": "ZEN_HEADBUTT", "uses": 20026}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 20826}, {"moveId": "SEED_BOMB", "uses": 19705}, {"moveId": "BRICK_BREAK", "uses": 17794}]}, "moveset": ["ROCK_SMASH", "SEED_BOMB", "ROCK_SLIDE"], "score": 25.4}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 413, "matchups": [{"opponent": "quagsire_shadow", "rating": 857, "opRating": 142}, {"opponent": "quagsire", "rating": 796, "opRating": 203}, {"opponent": "claydol", "rating": 765, "opRating": 234}, {"opponent": "obstagoon_shadow", "rating": 673, "opRating": 326}, {"opponent": "diggersby", "rating": 565, "opRating": 434}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "clodsire", "rating": 98}, {"opponent": "gligar", "rating": 110}, {"opponent": "talonflame", "rating": 200}, {"opponent": "cradily", "rating": 246}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35942}, {"moveId": "RAZOR_LEAF", "uses": 22358}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 23329}, {"moveId": "SOLAR_BEAM", "uses": 19771}, {"moveId": "HYPER_BEAM", "uses": 15240}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 23.3}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 189, "matchups": [{"opponent": "lampent_shadow", "rating": 721, "opRating": 278}, {"opponent": "regigigas", "rating": 603, "opRating": 396}, {"opponent": "regigigas_shadow", "rating": 567, "opRating": 432}, {"opponent": "unfezant", "rating": 525, "opRating": 475}, {"opponent": "pupitar", "rating": 525, "opRating": 475}], "counters": [{"opponent": "clodsire", "rating": 19}, {"opponent": "cradily", "rating": 24}, {"opponent": "magcargo", "rating": 106}, {"opponent": "gligar", "rating": 167}, {"opponent": "talonflame", "rating": 207}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 58300}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29800}, {"moveId": "EARTHQUAKE", "uses": 11849}, {"moveId": "PLAY_ROUGH", "uses": 8859}, {"moveId": "HYPER_BEAM", "uses": 7718}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 17}, {"speciesId": "slaking_shadow", "speciesName": "Slaking (Shadow)", "rating": 174, "matchups": [{"opponent": "slaking", "rating": 685, "opRating": 314}, {"opponent": "lampent_shadow", "rating": 642, "opRating": 357}, {"opponent": "regigigas", "rating": 567, "opRating": 432}, {"opponent": "deerling", "rating": 539, "opRating": 460}, {"opponent": "ambipom", "rating": 532, "opRating": 467}], "counters": [{"opponent": "clodsire", "rating": 16}, {"opponent": "cradily", "rating": 24}, {"opponent": "magcargo", "rating": 119}, {"opponent": "gligar", "rating": 194}, {"opponent": "talonflame", "rating": 237}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 58300}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29431}, {"moveId": "EARTHQUAKE", "uses": 11781}, {"moveId": "PLAY_ROUGH", "uses": 8847}, {"moveId": "HYPER_BEAM", "uses": 8120}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 13.7}]