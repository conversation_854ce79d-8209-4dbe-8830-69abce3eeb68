[{"speciesId": "gligar", "speciesName": "Gligar", "rating": 756, "matchups": [{"opponent": "clodsire", "rating": 687}, {"opponent": "cradily", "rating": 683}, {"opponent": "diggersby", "rating": 568}, {"opponent": "jumpluff_shadow", "rating": 561}, {"opponent": "swampert_shadow", "rating": 522}], "counters": [{"opponent": "talonflame", "rating": 248}, {"opponent": "skeledirge", "rating": 248}, {"opponent": "claydol", "rating": 392}, {"opponent": "furret", "rating": 425}, {"opponent": "magcargo", "rating": 465}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32540}, {"moveId": "WING_ATTACK", "uses": 25760}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 20913}, {"moveId": "NIGHT_SLASH", "uses": 19074}, {"moveId": "DIG", "uses": 12358}, {"moveId": "RETURN", "uses": 6022}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "DIG"], "score": 100}, {"speciesId": "gligar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 759, "matchups": [{"opponent": "magcargo", "rating": 767}, {"opponent": "claydol", "rating": 671}, {"opponent": "clodsire", "rating": 660}, {"opponent": "cradily", "rating": 614}, {"opponent": "jumpluff_shadow", "rating": 507}], "counters": [{"opponent": "swampert_shadow", "rating": 224}, {"opponent": "skeledirge", "rating": 276}, {"opponent": "talonflame", "rating": 277}, {"opponent": "diggersby", "rating": 428}, {"opponent": "furret", "rating": 431}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32254}, {"moveId": "WING_ATTACK", "uses": 26046}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 23299}, {"moveId": "NIGHT_SLASH", "uses": 21326}, {"moveId": "DIG", "uses": 13678}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "AERIAL_ACE", "DIG"], "score": 99.4}, {"speciesId": "furret", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 708, "matchups": [{"opponent": "clodsire", "rating": 643}, {"opponent": "diggersby", "rating": 606}, {"opponent": "cradily", "rating": 575}, {"opponent": "gligar", "rating": 575}, {"opponent": "swampert_shadow", "rating": 550}], "counters": [{"opponent": "magcargo", "rating": 410}, {"opponent": "talonflame", "rating": 411}, {"opponent": "ninetales_shadow", "rating": 464}, {"opponent": "gliscor", "rating": 495}, {"opponent": "jumpluff_shadow", "rating": 496}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 30287}, {"moveId": "QUICK_ATTACK", "uses": 28013}], "chargedMoves": [{"moveId": "SWIFT", "uses": 23054}, {"moveId": "TRAILBLAZE", "uses": 13171}, {"moveId": "BRICK_BREAK", "uses": 10856}, {"moveId": "DIG", "uses": 8124}, {"moveId": "HYPER_BEAM", "uses": 3134}]}, "moveset": ["SUCKER_PUNCH", "SWIFT", "TRAILBLAZE"], "score": 98.5}, {"speciesId": "magcargo", "speciesName": "Magcargo", "rating": 706, "matchups": [{"opponent": "jumpluff_shadow", "rating": 769}, {"opponent": "talonflame", "rating": 726}, {"opponent": "furret", "rating": 589}, {"opponent": "flygon", "rating": 547, "opRating": 452}, {"opponent": "gligar", "rating": 534}], "counters": [{"opponent": "claydol", "rating": 177}, {"opponent": "swampert_shadow", "rating": 180}, {"opponent": "clodsire", "rating": 343}, {"opponent": "diggersby", "rating": 485}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27327}, {"moveId": "EMBER", "uses": 18372}, {"moveId": "ROCK_THROW", "uses": 12632}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 28973}, {"moveId": "OVERHEAT", "uses": 16563}, {"moveId": "STONE_EDGE", "uses": 9946}, {"moveId": "HEAT_WAVE", "uses": 2971}]}, "moveset": ["INCINERATE", "ROCK_TOMB", "OVERHEAT"], "score": 97.9}, {"speciesId": "cradily", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 723, "matchups": [{"opponent": "jumpluff_shadow", "rating": 690}, {"opponent": "swampert_shadow", "rating": 666}, {"opponent": "diggersby", "rating": 628}, {"opponent": "talonflame", "rating": 527}, {"opponent": "magcargo", "rating": 513}], "counters": [{"opponent": "gligar", "rating": 316}, {"opponent": "claydol", "rating": 363}, {"opponent": "clodsire", "rating": 391}, {"opponent": "furret", "rating": 425}, {"opponent": "flygon", "rating": 456}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22842}, {"moveId": "BULLET_SEED", "uses": 19788}, {"moveId": "INFESTATION", "uses": 15686}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 17427}, {"moveId": "GRASS_KNOT", "uses": 14316}, {"moveId": "ROCK_SLIDE", "uses": 11254}, {"moveId": "STONE_EDGE", "uses": 5932}, {"moveId": "BULLDOZE", "uses": 5049}, {"moveId": "RETURN", "uses": 4330}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 97.4}, {"speciesId": "jumpluff_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 771, "matchups": [{"opponent": "flygon", "rating": 767, "opRating": 232}, {"opponent": "swampert_shadow", "rating": 692, "opRating": 307}, {"opponent": "diggersby", "rating": 539, "opRating": 460}, {"opponent": "claydol", "rating": 535, "opRating": 464}, {"opponent": "furret", "rating": 503}], "counters": [{"opponent": "magcargo", "rating": 230}, {"opponent": "talonflame", "rating": 251}, {"opponent": "cradily", "rating": 309}, {"opponent": "gligar", "rating": 438}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 22861}, {"moveId": "BULLET_SEED", "uses": 19709}, {"moveId": "INFESTATION", "uses": 15650}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 19443}, {"moveId": "AERIAL_ACE", "uses": 17565}, {"moveId": "ENERGY_BALL", "uses": 11280}, {"moveId": "DAZZLING_GLEAM", "uses": 6587}, {"moveId": "SOLAR_BEAM", "uses": 3204}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FAIRY_WIND", "AERIAL_ACE", "ACROBATICS"], "score": 96.1}, {"speciesId": "talonflame", "speciesName": "Talon<PERSON>lame", "rating": 718, "matchups": [{"opponent": "skeledirge", "rating": 770, "opRating": 229}, {"opponent": "gligar", "rating": 751}, {"opponent": "jumpluff_shadow", "rating": 748}, {"opponent": "furret", "rating": 588}, {"opponent": "diggersby", "rating": 537}], "counters": [{"opponent": "swampert_shadow", "rating": 209}, {"opponent": "magcargo", "rating": 273}, {"opponent": "clodsire", "rating": 442}, {"opponent": "cradily", "rating": 472}, {"opponent": "claydol", "rating": 491}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 26937}, {"moveId": "FIRE_SPIN", "uses": 15414}, {"moveId": "STEEL_WING", "uses": 9188}, {"moveId": "PECK", "uses": 6820}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22095}, {"moveId": "FLY", "uses": 18900}, {"moveId": "FLAME_CHARGE", "uses": 10837}, {"moveId": "HURRICANE", "uses": 4198}, {"moveId": "FIRE_BLAST", "uses": 2505}]}, "moveset": ["INCINERATE", "FLY", "BRAVE_BIRD"], "score": 96.1}, {"speciesId": "gliscor", "speciesName": "Gliscor", "rating": 707, "matchups": [{"opponent": "clodsire", "rating": 655}, {"opponent": "cradily", "rating": 650}, {"opponent": "gligar", "rating": 538}, {"opponent": "furret", "rating": 504}, {"opponent": "jumpluff_shadow", "rating": 504}], "counters": [{"opponent": "ninetales_shadow", "rating": 218}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "skeledirge", "rating": 258}, {"opponent": "talonflame", "rating": 259}, {"opponent": "typhlosion_shadow", "rating": 452}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 21699}, {"moveId": "SAND_ATTACK", "uses": 19122}, {"moveId": "WING_ATTACK", "uses": 17478}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 21984}, {"moveId": "NIGHT_SLASH", "uses": 19758}, {"moveId": "EARTHQUAKE", "uses": 10508}, {"moveId": "SAND_TOMB", "uses": 6061}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 95.3}, {"speciesId": "ursaring_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 688, "matchups": [{"opponent": "cradily", "rating": 757}, {"opponent": "swampert_shadow", "rating": 746}, {"opponent": "furret", "rating": 678}, {"opponent": "magcargo", "rating": 630}, {"opponent": "talonflame", "rating": 599}], "counters": [{"opponent": "diggersby", "rating": 215}, {"opponent": "clodsire", "rating": 322}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "gligar", "rating": 385}, {"opponent": "flygon", "rating": 484}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24776}, {"moveId": "COUNTER", "uses": 19324}, {"moveId": "METAL_CLAW", "uses": 14199}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20694}, {"moveId": "SWIFT", "uses": 19305}, {"moveId": "TRAILBLAZE", "uses": 11317}, {"moveId": "PLAY_ROUGH", "uses": 4393}, {"moveId": "HYPER_BEAM", "uses": 2607}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 94.6}, {"speciesId": "diggersby_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 698, "matchups": [{"opponent": "magcargo", "rating": 781}, {"opponent": "clodsire", "rating": 686}, {"opponent": "gligar", "rating": 571}, {"opponent": "furret", "rating": 517}, {"opponent": "swampert_shadow", "rating": 508}], "counters": [{"opponent": "talonflame", "rating": 274}, {"opponent": "claydol", "rating": 342}, {"opponent": "jumpluff_shadow", "rating": 379}, {"opponent": "skeledirge", "rating": 474}, {"opponent": "cradily", "rating": 489}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31410}, {"moveId": "MUD_SHOT", "uses": 26890}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19405}, {"moveId": "SCORCHING_SANDS", "uses": 18814}, {"moveId": "HYPER_BEAM", "uses": 8406}, {"moveId": "DIG", "uses": 6210}, {"moveId": "EARTHQUAKE", "uses": 5434}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "SCORCHING_SANDS", "FIRE_PUNCH"], "score": 94.3}, {"speciesId": "zangoose", "speciesName": "Zangoose", "rating": 694, "matchups": [{"opponent": "cradily", "rating": 790}, {"opponent": "swampert_shadow", "rating": 750}, {"opponent": "furret", "rating": 709}, {"opponent": "magcargo", "rating": 668}, {"opponent": "diggersby", "rating": 594}], "counters": [{"opponent": "clodsire", "rating": 223}, {"opponent": "talonflame", "rating": 251}, {"opponent": "gligar", "rating": 305}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "skeledirge", "rating": 431}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31712}, {"moveId": "SHADOW_CLAW", "uses": 26588}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28254}, {"moveId": "NIGHT_SLASH", "uses": 21487}, {"moveId": "DIG", "uses": 8602}]}, "moveset": ["FURY_CUTTER", "CLOSE_COMBAT", "NIGHT_SLASH"], "score": 93.4}, {"speciesId": "ninetales_shadow", "speciesName": "Ninetales (Shadow)", "rating": 708, "matchups": [{"opponent": "clodsire", "rating": 841, "opRating": 158}, {"opponent": "cradily", "rating": 789}, {"opponent": "gliscor", "rating": 781, "opRating": 218}, {"opponent": "jumpluff_shadow", "rating": 746}, {"opponent": "furret", "rating": 535}], "counters": [{"opponent": "magcargo", "rating": 158}, {"opponent": "diggersby", "rating": 209}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "talonflame", "rating": 348}, {"opponent": "gligar", "rating": 412}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 24799}, {"moveId": "FIRE_SPIN", "uses": 21854}, {"moveId": "FEINT_ATTACK", "uses": 11707}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17606}, {"moveId": "PSYSHOCK", "uses": 12571}, {"moveId": "SCORCHING_SANDS", "uses": 9512}, {"moveId": "OVERHEAT", "uses": 7069}, {"moveId": "SOLAR_BEAM", "uses": 4263}, {"moveId": "FLAMETHROWER", "uses": 3769}, {"moveId": "FIRE_BLAST", "uses": 2061}, {"moveId": "HEAT_WAVE", "uses": 1201}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 93.2}, {"speciesId": "swampert_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 633, "matchups": [{"opponent": "clodsire", "rating": 852, "opRating": 147}, {"opponent": "magcargo", "rating": 819, "opRating": 180}, {"opponent": "talonflame", "rating": 790}, {"opponent": "skeledirge", "rating": 790, "opRating": 209}, {"opponent": "flygon", "rating": 610, "opRating": 389}], "counters": [{"opponent": "jumpluff_shadow", "rating": 307}, {"opponent": "cradily", "rating": 333}, {"opponent": "furret", "rating": 450}, {"opponent": "diggersby", "rating": 465}, {"opponent": "gligar", "rating": 477}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 32705}, {"moveId": "WATER_GUN", "uses": 25595}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 23399}, {"moveId": "SLUDGE", "uses": 12232}, {"moveId": "MUDDY_WATER", "uses": 8944}, {"moveId": "SURF", "uses": 6528}, {"moveId": "EARTHQUAKE", "uses": 5193}, {"moveId": "SLUDGE_WAVE", "uses": 2213}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 93.1}, {"speciesId": "quagsire_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 630, "matchups": [{"opponent": "magcargo", "rating": 885}, {"opponent": "talonflame", "rating": 795}, {"opponent": "clodsire", "rating": 779}, {"opponent": "diggersby", "rating": 624}, {"opponent": "gligar", "rating": 571}], "counters": [{"opponent": "cradily", "rating": 243}, {"opponent": "furret", "rating": 312}, {"opponent": "jumpluff_shadow", "rating": 398}, {"opponent": "flygon", "rating": 408}, {"opponent": "swampert_shadow", "rating": 474}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30604}, {"moveId": "WATER_GUN", "uses": 27696}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 23955}, {"moveId": "MUD_BOMB", "uses": 12758}, {"moveId": "STONE_EDGE", "uses": 8925}, {"moveId": "SLUDGE_BOMB", "uses": 7786}, {"moveId": "EARTHQUAKE", "uses": 3039}, {"moveId": "ACID_SPRAY", "uses": 1704}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 92.8}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 750, "matchups": [{"opponent": "drampa", "rating": 771, "opRating": 228}, {"opponent": "swampert_shadow", "rating": 722, "opRating": 277}, {"opponent": "flygon", "rating": 656, "opRating": 343}, {"opponent": "claydol", "rating": 601, "opRating": 398}, {"opponent": "furret", "rating": 591}], "counters": [{"opponent": "magcargo", "rating": 158}, {"opponent": "talonflame", "rating": 222}, {"opponent": "gligar", "rating": 339}, {"opponent": "cradily", "rating": 350}, {"opponent": "clodsire", "rating": 430}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 22899}, {"moveId": "BULLET_SEED", "uses": 19709}, {"moveId": "INFESTATION", "uses": 15665}], "chargedMoves": [{"moveId": "ACROBATICS", "uses": 17843}, {"moveId": "AERIAL_ACE", "uses": 16131}, {"moveId": "ENERGY_BALL", "uses": 10502}, {"moveId": "DAZZLING_GLEAM", "uses": 6050}, {"moveId": "RETURN", "uses": 4656}, {"moveId": "SOLAR_BEAM", "uses": 3090}]}, "moveset": ["FAIRY_WIND", "AERIAL_ACE", "ACROBATICS"], "score": 92.5}, {"speciesId": "flygon", "speciesName": "Flygon", "rating": 661, "matchups": [{"opponent": "talonflame", "rating": 820}, {"opponent": "skeledirge", "rating": 820, "opRating": 180}, {"opponent": "clodsire", "rating": 740}, {"opponent": "quagsire_shadow", "rating": 592, "opRating": 408}, {"opponent": "cradily", "rating": 544}], "counters": [{"opponent": "jumpluff_shadow", "rating": 232}, {"opponent": "swampert_shadow", "rating": 389}, {"opponent": "magcargo", "rating": 452}, {"opponent": "furret", "rating": 478}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 21204}, {"moveId": "SAND_ATTACK", "uses": 19542}, {"moveId": "MUD_SHOT", "uses": 17614}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 19342}, {"moveId": "SCORCHING_SANDS", "uses": 12950}, {"moveId": "STONE_EDGE", "uses": 10723}, {"moveId": "BOOMBURST", "uses": 6416}, {"moveId": "EARTH_POWER", "uses": 5118}, {"moveId": "EARTHQUAKE", "uses": 3707}]}, "moveset": ["DRAGON_TAIL", "SCORCHING_SANDS", "DRAGON_CLAW"], "score": 92.3}, {"speciesId": "miltank", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 681, "matchups": [{"opponent": "gligar", "rating": 850}, {"opponent": "jumpluff_shadow", "rating": 773}, {"opponent": "flygon", "rating": 763, "opRating": 236}, {"opponent": "drampa", "rating": 703, "opRating": 296}, {"opponent": "diggersby", "rating": 516, "opRating": 483}], "counters": [{"opponent": "magcargo", "rating": 299}, {"opponent": "cradily", "rating": 354}, {"opponent": "clodsire", "rating": 372}, {"opponent": "furret", "rating": 403}, {"opponent": "talonflame", "rating": 466}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 31825}, {"moveId": "TACKLE", "uses": 21796}, {"moveId": "ZEN_HEADBUTT", "uses": 4689}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21537}, {"moveId": "ICE_BEAM", "uses": 16894}, {"moveId": "STOMP", "uses": 7620}, {"moveId": "THUNDERBOLT", "uses": 7160}, {"moveId": "GYRO_BALL", "uses": 5046}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "ICE_BEAM"], "score": 92.2}, {"speciesId": "diggersby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 685, "matchups": [{"opponent": "ninetales_shadow", "rating": 790, "opRating": 209}, {"opponent": "clodsire", "rating": 718, "opRating": 281}, {"opponent": "claydol", "rating": 554, "opRating": 445}, {"opponent": "swampert_shadow", "rating": 534, "opRating": 465}, {"opponent": "magcargo", "rating": 514, "opRating": 485}], "counters": [{"opponent": "cradily", "rating": 371}, {"opponent": "furret", "rating": 393}, {"opponent": "gligar", "rating": 431}, {"opponent": "jumpluff_shadow", "rating": 460}, {"opponent": "talonflame", "rating": 462}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 31631}, {"moveId": "MUD_SHOT", "uses": 26669}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 17381}, {"moveId": "SCORCHING_SANDS", "uses": 17165}, {"moveId": "RETURN", "uses": 9391}, {"moveId": "DIG", "uses": 5724}, {"moveId": "EARTHQUAKE", "uses": 4899}, {"moveId": "HYPER_BEAM", "uses": 3604}]}, "moveset": ["QUICK_ATTACK", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 92}, {"speciesId": "swampert", "speciesName": "<PERSON><PERSON>", "rating": 627, "matchups": [{"opponent": "magcargo", "rating": 849}, {"opponent": "talonflame", "rating": 834}, {"opponent": "gligar", "rating": 617}, {"opponent": "clodsire", "rating": 610}, {"opponent": "diggersby", "rating": 573}], "counters": [{"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "cradily", "rating": 281}, {"opponent": "furret", "rating": 368}, {"opponent": "drampa", "rating": 447}, {"opponent": "flygon", "rating": 480}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31968}, {"moveId": "WATER_GUN", "uses": 26332}], "chargedMoves": [{"moveId": "HYDRO_CANNON", "uses": 23427}, {"moveId": "SLUDGE", "uses": 12193}, {"moveId": "MUDDY_WATER", "uses": 8977}, {"moveId": "SURF", "uses": 6544}, {"moveId": "EARTHQUAKE", "uses": 5182}, {"moveId": "SLUDGE_WAVE", "uses": 2202}]}, "moveset": ["MUD_SHOT", "HYDRO_CANNON", "SLUDGE"], "score": 91.9}, {"speciesId": "marowak_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 717, "matchups": [{"opponent": "gliscor", "rating": 896, "opRating": 104}, {"opponent": "ninetales_shadow", "rating": 683, "opRating": 316}, {"opponent": "jumpluff_shadow", "rating": 584}, {"opponent": "gligar", "rating": 568}, {"opponent": "diggersby", "rating": 540, "opRating": 460}], "counters": [{"opponent": "talonflame", "rating": 311}, {"opponent": "clodsire", "rating": 370}, {"opponent": "cradily", "rating": 395}, {"opponent": "furret", "rating": 418}, {"opponent": "magcargo", "rating": 440}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 25794}, {"moveId": "FIRE_SPIN", "uses": 24528}, {"moveId": "ROCK_SMASH", "uses": 7986}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 20636}, {"moveId": "SHADOW_BONE", "uses": 17268}, {"moveId": "FIRE_BLAST", "uses": 8120}, {"moveId": "FLAME_WHEEL", "uses": 6785}, {"moveId": "SHADOW_BALL", "uses": 5482}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SHADOW_BONE", "BONE_CLUB"], "score": 91.4}, {"speciesId": "salazzle", "speciesName": "Salazzle", "rating": 663, "matchups": [{"opponent": "jumpluff_shadow", "rating": 907}, {"opponent": "cradily", "rating": 824}, {"opponent": "talonflame", "rating": 710}, {"opponent": "skeledirge", "rating": 675, "opRating": 324}, {"opponent": "drampa", "rating": 535, "opRating": 464}], "counters": [{"opponent": "magcargo", "rating": 196}, {"opponent": "diggersby", "rating": 281}, {"opponent": "clodsire", "rating": 302}, {"opponent": "gligar", "rating": 370}, {"opponent": "furret", "rating": 471}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36222}, {"moveId": "POISON_JAB", "uses": 22078}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 22665}, {"moveId": "SLUDGE_WAVE", "uses": 12287}, {"moveId": "FIRE_BLAST", "uses": 11740}, {"moveId": "DRAGON_PULSE", "uses": 11626}]}, "moveset": ["INCINERATE", "POISON_FANG", "DRAGON_PULSE"], "score": 91}, {"speciesId": "golurk_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 620, "matchups": [{"opponent": "cradily", "rating": 902}, {"opponent": "clodsire", "rating": 902, "opRating": 97}, {"opponent": "magcargo", "rating": 742}, {"opponent": "diggersby", "rating": 730, "opRating": 269}, {"opponent": "claydol", "rating": 527, "opRating": 472}], "counters": [{"opponent": "jumpluff_shadow", "rating": 245}, {"opponent": "furret", "rating": 284}, {"opponent": "gligar", "rating": 358}, {"opponent": "swampert_shadow", "rating": 441}, {"opponent": "talonflame", "rating": 496}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31602}, {"moveId": "ASTONISH", "uses": 26698}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 22176}, {"moveId": "SHADOW_PUNCH", "uses": 20828}, {"moveId": "EARTH_POWER", "uses": 11639}, {"moveId": "POLTERGEIST", "uses": 3666}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "DYNAMIC_PUNCH"], "score": 90.5}, {"speciesId": "quagsire", "speciesName": "Quagsire", "rating": 620, "matchups": [{"opponent": "magcargo", "rating": 903}, {"opponent": "talonflame", "rating": 826}, {"opponent": "clodsire", "rating": 664}, {"opponent": "diggersby", "rating": 540}, {"opponent": "swampert_shadow", "rating": 524}], "counters": [{"opponent": "cradily", "rating": 204}, {"opponent": "furret", "rating": 284}, {"opponent": "jumpluff_shadow", "rating": 343}, {"opponent": "gligar", "rating": 469}, {"opponent": "claydol", "rating": 479}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29857}, {"moveId": "WATER_GUN", "uses": 28443}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 22568}, {"moveId": "MUD_BOMB", "uses": 12003}, {"moveId": "STONE_EDGE", "uses": 8349}, {"moveId": "SLUDGE_BOMB", "uses": 7167}, {"moveId": "RETURN", "uses": 3835}, {"moveId": "EARTHQUAKE", "uses": 2908}, {"moveId": "ACID_SPRAY", "uses": 1585}]}, "moveset": ["MUD_SHOT", "AQUA_TAIL", "STONE_EDGE"], "score": 90.5}, {"speciesId": "magmar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 694, "matchups": [{"opponent": "magcargo", "rating": 767, "opRating": 232}, {"opponent": "skeledirge", "rating": 750, "opRating": 250}, {"opponent": "furret", "rating": 688}, {"opponent": "swampert_shadow", "rating": 600, "opRating": 399}, {"opponent": "flygon", "rating": 535, "opRating": 464}], "counters": [{"opponent": "talonflame", "rating": 296}, {"opponent": "gligar", "rating": 332}, {"opponent": "clodsire", "rating": 360}, {"opponent": "jumpluff_shadow", "rating": 405}, {"opponent": "cradily", "rating": 406}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 31535}, {"moveId": "EMBER", "uses": 26765}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26329}, {"moveId": "SCORCHING_SANDS", "uses": 19278}, {"moveId": "FLAMETHROWER", "uses": 8227}, {"moveId": "FIRE_BLAST", "uses": 4445}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 90.2}, {"speciesId": "flygon_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 652, "matchups": [{"opponent": "talonflame", "rating": 788}, {"opponent": "skeledirge", "rating": 788, "opRating": 212}, {"opponent": "magcargo", "rating": 564}, {"opponent": "claydol", "rating": 556, "opRating": 444}, {"opponent": "cradily", "rating": 516}], "counters": [{"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "furret", "rating": 331}, {"opponent": "gligar", "rating": 339}, {"opponent": "diggersby", "rating": 402}, {"opponent": "clodsire", "rating": 427}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 21091}, {"moveId": "SAND_ATTACK", "uses": 19615}, {"moveId": "MUD_SHOT", "uses": 17593}], "chargedMoves": [{"moveId": "DRAGON_CLAW", "uses": 19286}, {"moveId": "SCORCHING_SANDS", "uses": 12964}, {"moveId": "STONE_EDGE", "uses": 10719}, {"moveId": "BOOMBURST", "uses": 6423}, {"moveId": "EARTH_POWER", "uses": 5115}, {"moveId": "EARTHQUAKE", "uses": 3706}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "DRAGON_CLAW", "SCORCHING_SANDS"], "score": 90.1}, {"speciesId": "marowak_alolan", "speciesName": "Marowak (Alolan)", "rating": 701, "matchups": [{"opponent": "piloswine", "rating": 796, "opRating": 204}, {"opponent": "jumpluff_shadow", "rating": 632}, {"opponent": "gligar", "rating": 600}, {"opponent": "ninetales_shadow", "rating": 556, "opRating": 444}, {"opponent": "magcargo", "rating": 504}], "counters": [{"opponent": "furret", "rating": 346}, {"opponent": "clodsire", "rating": 372}, {"opponent": "diggersby", "rating": 405}, {"opponent": "talonflame", "rating": 429}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 25637}, {"moveId": "FIRE_SPIN", "uses": 24456}, {"moveId": "ROCK_SMASH", "uses": 8179}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 18656}, {"moveId": "SHADOW_BONE", "uses": 15766}, {"moveId": "FIRE_BLAST", "uses": 7141}, {"moveId": "FLAME_WHEEL", "uses": 5901}, {"moveId": "RETURN", "uses": 5865}, {"moveId": "SHADOW_BALL", "uses": 5017}]}, "moveset": ["FIRE_SPIN", "BONE_CLUB", "SHADOW_BONE"], "score": 90.1}, {"speciesId": "skeledirge", "speciesName": "Skeledirge", "rating": 697, "matchups": [{"opponent": "gligar", "rating": 751}, {"opponent": "jumpluff_shadow", "rating": 589}, {"opponent": "claydol", "rating": 564, "opRating": 435}, {"opponent": "diggersby", "rating": 550, "opRating": 449}, {"opponent": "cradily", "rating": 510}], "counters": [{"opponent": "magcargo", "rating": 162}, {"opponent": "swampert_shadow", "rating": 209}, {"opponent": "talonflame", "rating": 229}, {"opponent": "furret", "rating": 300}, {"opponent": "clodsire", "rating": 451}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32193}, {"moveId": "HEX", "uses": 20777}, {"moveId": "BITE", "uses": 5295}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 15447}, {"moveId": "TORCH_SONG", "uses": 14986}, {"moveId": "CRUNCH", "uses": 8823}, {"moveId": "SHADOW_BALL", "uses": 8393}, {"moveId": "DISARMING_VOICE", "uses": 7039}, {"moveId": "FLAMETHROWER", "uses": 3479}]}, "moveset": ["INCINERATE", "TORCH_SONG", "SHADOW_BALL"], "score": 90.1}, {"speciesId": "pidgeot", "speciesName": "Pidgeot", "rating": 691, "matchups": [{"opponent": "jumpluff_shadow", "rating": 725}, {"opponent": "swampert_shadow", "rating": 637, "opRating": 362}, {"opponent": "diggersby", "rating": 556, "opRating": 443}, {"opponent": "claydol", "rating": 535, "opRating": 464}, {"opponent": "furret", "rating": 528}], "counters": [{"opponent": "magcargo", "rating": 209}, {"opponent": "cradily", "rating": 295}, {"opponent": "gligar", "rating": 408}, {"opponent": "talonflame", "rating": 429}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 18163}, {"moveId": "WING_ATTACK", "uses": 16575}, {"moveId": "AIR_SLASH", "uses": 12611}, {"moveId": "STEEL_WING", "uses": 10910}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 21008}, {"moveId": "AIR_CUTTER", "uses": 17984}, {"moveId": "AERIAL_ACE", "uses": 6685}, {"moveId": "RETURN", "uses": 6094}, {"moveId": "HURRICANE", "uses": 3695}, {"moveId": "FEATHER_DANCE", "uses": 2547}]}, "moveset": ["GUST", "AIR_CUTTER", "BRAVE_BIRD"], "score": 89.8}, {"speciesId": "rhyperior_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 609, "matchups": [{"opponent": "magcargo", "rating": 854}, {"opponent": "talonflame", "rating": 735}, {"opponent": "clodsire", "rating": 731}, {"opponent": "furret", "rating": 600}, {"opponent": "jumpluff_shadow", "rating": 548}], "counters": [{"opponent": "gligar", "rating": 244}, {"opponent": "claydol", "rating": 342}, {"opponent": "cradily", "rating": 388}, {"opponent": "swampert_shadow", "rating": 415}, {"opponent": "diggersby", "rating": 422}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 37756}, {"moveId": "SMACK_DOWN", "uses": 20544}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 14967}, {"moveId": "SUPER_POWER", "uses": 10974}, {"moveId": "BREAKING_SWIPE", "uses": 10497}, {"moveId": "SURF", "uses": 9349}, {"moveId": "EARTHQUAKE", "uses": 4816}, {"moveId": "STONE_EDGE", "uses": 4247}, {"moveId": "SKULL_BASH", "uses": 3417}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 89.8}, {"speciesId": "claydol", "speciesName": "Claydol", "rating": 606, "matchups": [{"opponent": "magcargo", "rating": 822}, {"opponent": "clodsire", "rating": 727}, {"opponent": "cradily", "rating": 636}, {"opponent": "gligar", "rating": 607}, {"opponent": "talonflame", "rating": 508}], "counters": [{"opponent": "furret", "rating": 284}, {"opponent": "quagsire_shadow", "rating": 344}, {"opponent": "skeledirge", "rating": 435}, {"opponent": "diggersby", "rating": 445}, {"opponent": "jumpluff_shadow", "rating": 464}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23534}, {"moveId": "CONFUSION", "uses": 19245}, {"moveId": "EXTRASENSORY", "uses": 15517}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 12693}, {"moveId": "ICE_BEAM", "uses": 11607}, {"moveId": "SCORCHING_SANDS", "uses": 11444}, {"moveId": "PSYCHIC", "uses": 6085}, {"moveId": "SHADOW_BALL", "uses": 5629}, {"moveId": "EARTH_POWER", "uses": 4479}, {"moveId": "EARTHQUAKE", "uses": 3280}, {"moveId": "GYRO_BALL", "uses": 3068}]}, "moveset": ["MUD_SLAP", "ROCK_TOMB", "ICE_BEAM"], "score": 89.7}, {"speciesId": "pidgeot_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 683, "matchups": [{"opponent": "quagsire_shadow", "rating": 707, "opRating": 292}, {"opponent": "diggersby", "rating": 647, "opRating": 352}, {"opponent": "talonflame", "rating": 644}, {"opponent": "swampert_shadow", "rating": 580, "opRating": 419}, {"opponent": "claydol", "rating": 538, "opRating": 461}], "counters": [{"opponent": "clodsire", "rating": 319}, {"opponent": "cradily", "rating": 343}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "furret", "rating": 468}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "GUST", "uses": 18608}, {"moveId": "WING_ATTACK", "uses": 16668}, {"moveId": "AIR_SLASH", "uses": 12528}, {"moveId": "STEEL_WING", "uses": 10462}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 23334}, {"moveId": "AIR_CUTTER", "uses": 20084}, {"moveId": "AERIAL_ACE", "uses": 7589}, {"moveId": "HURRICANE", "uses": 4081}, {"moveId": "FEATHER_DANCE", "uses": 2915}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["GUST", "BRAVE_BIRD", "AIR_CUTTER"], "score": 89.7}, {"speciesId": "ninetales", "speciesName": "Ninetales", "rating": 719, "matchups": [{"opponent": "piloswine", "rating": 876, "opRating": 123}, {"opponent": "gliscor", "rating": 781, "opRating": 218}, {"opponent": "gligar", "rating": 615}, {"opponent": "furret", "rating": 611}, {"opponent": "jumpluff_shadow", "rating": 607}], "counters": [{"opponent": "magcargo", "rating": 175}, {"opponent": "talonflame", "rating": 285}, {"opponent": "clodsire", "rating": 288}, {"opponent": "cradily", "rating": 291}, {"opponent": "diggersby", "rating": 448}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 24292}, {"moveId": "FIRE_SPIN", "uses": 21657}, {"moveId": "FEINT_ATTACK", "uses": 12405}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 16631}, {"moveId": "PSYSHOCK", "uses": 11670}, {"moveId": "SCORCHING_SANDS", "uses": 8875}, {"moveId": "OVERHEAT", "uses": 6670}, {"moveId": "SOLAR_BEAM", "uses": 4037}, {"moveId": "RETURN", "uses": 3645}, {"moveId": "FLAMETHROWER", "uses": 3633}, {"moveId": "FIRE_BLAST", "uses": 1925}, {"moveId": "HEAT_WAVE", "uses": 1134}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "OVERHEAT"], "score": 89.5}, {"speciesId": "rhydon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 601, "matchups": [{"opponent": "magcargo", "rating": 856}, {"opponent": "talonflame", "rating": 738}, {"opponent": "clodsire", "rating": 735}, {"opponent": "furret", "rating": 606}, {"opponent": "jumpluff_shadow", "rating": 551}], "counters": [{"opponent": "gligar", "rating": 240}, {"opponent": "claydol", "rating": 342}, {"opponent": "cradily", "rating": 388}, {"opponent": "swampert_shadow", "rating": 415}, {"opponent": "diggersby", "rating": 422}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 47844}, {"moveId": "ROCK_SMASH", "uses": 10456}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 14714}, {"moveId": "SURF", "uses": 13317}, {"moveId": "STONE_EDGE", "uses": 12939}, {"moveId": "MEGAHORN", "uses": 10273}, {"moveId": "EARTHQUAKE", "uses": 7076}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "STONE_EDGE"], "score": 89.4}, {"speciesId": "whiscash_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 609, "matchups": [{"opponent": "magcargo", "rating": 862}, {"opponent": "talonflame", "rating": 800}, {"opponent": "clodsire", "rating": 615}, {"opponent": "diggersby", "rating": 556}, {"opponent": "swampert_shadow", "rating": 522}], "counters": [{"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "cradily", "rating": 298}, {"opponent": "furret", "rating": 321}, {"opponent": "claydol", "rating": 438}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30591}, {"moveId": "WATER_GUN", "uses": 27709}], "chargedMoves": [{"moveId": "SCALD", "uses": 21713}, {"moveId": "MUD_BOMB", "uses": 17840}, {"moveId": "BLIZZARD", "uses": 12990}, {"moveId": "WATER_PULSE", "uses": 5694}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 89.2}, {"speciesId": "gliscor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 694, "matchups": [{"opponent": "claydol", "rating": 840, "opRating": 159}, {"opponent": "clodsire", "rating": 659}, {"opponent": "flygon", "rating": 616, "opRating": 383}, {"opponent": "cradily", "rating": 590}, {"opponent": "gligar", "rating": 568}], "counters": [{"opponent": "magcargo", "rating": 183}, {"opponent": "furret", "rating": 237}, {"opponent": "talonflame", "rating": 288}, {"opponent": "diggersby", "rating": 425}, {"opponent": "jumpluff_shadow", "rating": 457}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 21441}, {"moveId": "SAND_ATTACK", "uses": 19893}, {"moveId": "WING_ATTACK", "uses": 16978}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 21914}, {"moveId": "NIGHT_SLASH", "uses": 19795}, {"moveId": "EARTHQUAKE", "uses": 10505}, {"moveId": "SAND_TOMB", "uses": 6077}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "EARTHQUAKE", "AERIAL_ACE"], "score": 88.8}, {"speciesId": "abomasnow_shadow", "speciesName": "<PERSON><PERSON><PERSON>no<PERSON> (Shadow)", "rating": 665, "matchups": [{"opponent": "jumpluff_shadow", "rating": 870}, {"opponent": "claydol", "rating": 800, "opRating": 199}, {"opponent": "gligar", "rating": 730}, {"opponent": "flygon", "rating": 702, "opRating": 297}, {"opponent": "furret", "rating": 541}], "counters": [{"opponent": "magcargo", "rating": 81}, {"opponent": "diggersby", "rating": 204}, {"opponent": "talonflame", "rating": 292}, {"opponent": "clodsire", "rating": 430}, {"opponent": "cradily", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 32454}, {"moveId": "LEAFAGE", "uses": 18218}, {"moveId": "RAZOR_LEAF", "uses": 7611}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 28103}, {"moveId": "ICY_WIND", "uses": 9527}, {"moveId": "ENERGY_BALL", "uses": 9149}, {"moveId": "OUTRAGE", "uses": 7093}, {"moveId": "BLIZZARD", "uses": 4238}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "WEATHER_BALL_ICE", "ENERGY_BALL"], "score": 88.5}, {"speciesId": "bewear", "speciesName": "Bewear", "rating": 653, "matchups": [{"opponent": "swampert_shadow", "rating": 800}, {"opponent": "magcargo", "rating": 669}, {"opponent": "diggersby", "rating": 607}, {"opponent": "furret", "rating": 581}, {"opponent": "gligar", "rating": 519}], "counters": [{"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "clodsire", "rating": 295}, {"opponent": "claydol", "rating": 396}, {"opponent": "talonflame", "rating": 411}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 31035}, {"moveId": "TACKLE", "uses": 20441}, {"moveId": "LOW_KICK", "uses": 6803}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 29053}, {"moveId": "STOMP", "uses": 15740}, {"moveId": "PAYBACK", "uses": 11504}, {"moveId": "DRAIN_PUNCH", "uses": 2028}]}, "moveset": ["SHADOW_CLAW", "SUPER_POWER", "STOMP"], "score": 88.3}, {"speciesId": "typhlosion_shadow", "speciesName": "Typhlosion (Shadow)", "rating": 655, "matchups": [{"opponent": "clodsire", "rating": 773, "opRating": 226}, {"opponent": "talonflame", "rating": 756}, {"opponent": "cradily", "rating": 717}, {"opponent": "skeledirge", "rating": 696, "opRating": 303}, {"opponent": "pidgeot", "rating": 632, "opRating": 367}], "counters": [{"opponent": "magcargo", "rating": 243}, {"opponent": "diggersby", "rating": 261}, {"opponent": "furret", "rating": 303}, {"opponent": "gligar", "rating": 381}, {"opponent": "jumpluff_shadow", "rating": 408}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27095}, {"moveId": "EMBER", "uses": 15836}, {"moveId": "SHADOW_CLAW", "uses": 15365}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 28455}, {"moveId": "THUNDER_PUNCH", "uses": 12333}, {"moveId": "SOLAR_BEAM", "uses": 7885}, {"moveId": "OVERHEAT", "uses": 5989}, {"moveId": "FIRE_BLAST", "uses": 3516}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 88.3}, {"speciesId": "farfetchd", "speciesName": "<PERSON><PERSON><PERSON>'d", "rating": 702, "matchups": [{"opponent": "swampert_shadow", "rating": 927, "opRating": 72}, {"opponent": "quagsire_shadow", "rating": 927, "opRating": 72}, {"opponent": "claydol", "rating": 858, "opRating": 141}, {"opponent": "diggersby", "rating": 648, "opRating": 351}, {"opponent": "flygon_shadow", "rating": 595, "opRating": 404}], "counters": [{"opponent": "talonflame", "rating": 233}, {"opponent": "jumpluff_shadow", "rating": 339}, {"opponent": "cradily", "rating": 381}, {"opponent": "gligar", "rating": 400}, {"opponent": "furret", "rating": 415}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31400}, {"moveId": "AIR_SLASH", "uses": 19736}, {"moveId": "CUT", "uses": 7124}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 24720}, {"moveId": "LEAF_BLADE", "uses": 24235}, {"moveId": "AERIAL_ACE", "uses": 9320}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "AIR_CUTTER"], "score": 87.7}, {"speciesId": "sceptile_shadow", "speciesName": "<PERSON>eptile (Shadow)", "rating": 635, "matchups": [{"opponent": "swampert_shadow", "rating": 815, "opRating": 184}, {"opponent": "claydol", "rating": 774, "opRating": 225}, {"opponent": "diggersby", "rating": 770, "opRating": 229}, {"opponent": "furret", "rating": 680}, {"opponent": "cradily", "rating": 635}], "counters": [{"opponent": "magcargo", "rating": 277}, {"opponent": "talonflame", "rating": 307}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "clodsire", "rating": 384}, {"opponent": "gligar", "rating": 492}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 32158}, {"moveId": "BULLET_SEED", "uses": 26142}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 19577}, {"moveId": "BREAKING_SWIPE", "uses": 12264}, {"moveId": "AERIAL_ACE", "uses": 9860}, {"moveId": "FRENZY_PLANT", "uses": 7267}, {"moveId": "DRAGON_CLAW", "uses": 4835}, {"moveId": "EARTHQUAKE", "uses": 4455}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 87.7}, {"speciesId": "armarouge", "speciesName": "Armarouge", "rating": 681, "matchups": [{"opponent": "jumpluff_shadow", "rating": 910, "opRating": 89}, {"opponent": "piloswine", "rating": 880, "opRating": 119}, {"opponent": "swampert_shadow", "rating": 696, "opRating": 303}, {"opponent": "drampa", "rating": 636, "opRating": 363}, {"opponent": "gliscor", "rating": 529, "opRating": 470}], "counters": [{"opponent": "gligar", "rating": 244}, {"opponent": "cradily", "rating": 388}, {"opponent": "talonflame", "rating": 422}, {"opponent": "clodsire", "rating": 451}, {"opponent": "furret", "rating": 468}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34422}, {"moveId": "EMBER", "uses": 23878}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 31938}, {"moveId": "FLAME_CHARGE", "uses": 16954}, {"moveId": "FLAMETHROWER", "uses": 7104}, {"moveId": "HEAT_WAVE", "uses": 2248}]}, "moveset": ["INCINERATE", "PSYSHOCK", "FLAME_CHARGE"], "score": 87.4}, {"speciesId": "clodsire", "speciesName": "Clodsire", "rating": 661, "matchups": [{"opponent": "magcargo", "rating": 656}, {"opponent": "cradily", "rating": 608}, {"opponent": "drampa", "rating": 608, "opRating": 391}, {"opponent": "talonflame", "rating": 557}, {"opponent": "jumpluff_shadow", "rating": 507}], "counters": [{"opponent": "swampert_shadow", "rating": 147}, {"opponent": "claydol", "rating": 272}, {"opponent": "diggersby", "rating": 281}, {"opponent": "gligar", "rating": 312}, {"opponent": "furret", "rating": 356}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 32869}, {"moveId": "MUD_SHOT", "uses": 25431}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 12380}, {"moveId": "STONE_EDGE", "uses": 11672}, {"moveId": "SLUDGE_BOMB", "uses": 11623}, {"moveId": "WATER_PULSE", "uses": 10069}, {"moveId": "EARTHQUAKE", "uses": 9960}, {"moveId": "ACID_SPRAY", "uses": 2641}]}, "moveset": ["POISON_STING", "EARTHQUAKE", "STONE_EDGE"], "score": 87.4}, {"speciesId": "lileep_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 646, "matchups": [{"opponent": "talonflame", "rating": 715}, {"opponent": "quagsire_shadow", "rating": 685, "opRating": 314}, {"opponent": "drampa", "rating": 665, "opRating": 334}, {"opponent": "swampert_shadow", "rating": 586, "opRating": 413}, {"opponent": "jumpluff_shadow", "rating": 569}], "counters": [{"opponent": "magcargo", "rating": 333}, {"opponent": "gligar", "rating": 400}, {"opponent": "clodsire", "rating": 435}, {"opponent": "diggersby", "rating": 436}, {"opponent": "cradily", "rating": 479}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22604}, {"moveId": "BULLET_SEED", "uses": 19856}, {"moveId": "INFESTATION", "uses": 15796}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 26705}, {"moveId": "GRASS_KNOT", "uses": 24889}, {"moveId": "MIRROR_COAT", "uses": 6669}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "GRASS_KNOT", "ANCIENT_POWER"], "score": 87.4}, {"speciesId": "magmortar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 665, "matchups": [{"opponent": "piloswine", "rating": 828, "opRating": 171}, {"opponent": "magcargo", "rating": 754, "opRating": 245}, {"opponent": "typhlosion_shadow", "rating": 745, "opRating": 254}, {"opponent": "talonflame", "rating": 736}, {"opponent": "drampa", "rating": 555, "opRating": 444}], "counters": [{"opponent": "clodsire", "rating": 250}, {"opponent": "gligar", "rating": 339}, {"opponent": "jumpluff_shadow", "rating": 418}, {"opponent": "cradily", "rating": 434}, {"opponent": "furret", "rating": 493}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33666}, {"moveId": "FIRE_SPIN", "uses": 24634}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19292}, {"moveId": "SCORCHING_SANDS", "uses": 12472}, {"moveId": "BRICK_BREAK", "uses": 11189}, {"moveId": "PSYCHIC", "uses": 6140}, {"moveId": "THUNDERBOLT", "uses": 6035}, {"moveId": "FIRE_BLAST", "uses": 3237}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 87.4}, {"speciesId": "nidoqueen", "speciesName": "Nido<PERSON><PERSON>", "rating": 642, "matchups": [{"opponent": "jumpluff_shadow", "rating": 769}, {"opponent": "talonflame", "rating": 766}, {"opponent": "skeledirge", "rating": 766, "opRating": 233}, {"opponent": "drampa", "rating": 665, "opRating": 334}, {"opponent": "cradily", "rating": 661}], "counters": [{"opponent": "clodsire", "rating": 290}, {"opponent": "diggersby", "rating": 307}, {"opponent": "gligar", "rating": 312}, {"opponent": "furret", "rating": 343}, {"opponent": "magcargo", "rating": 393}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 26901}, {"moveId": "POISON_JAB", "uses": 22764}, {"moveId": "BITE", "uses": 8608}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 15738}, {"moveId": "EARTH_POWER", "uses": 15557}, {"moveId": "STONE_EDGE", "uses": 12812}, {"moveId": "SLUDGE_WAVE", "uses": 8509}, {"moveId": "EARTHQUAKE", "uses": 5687}]}, "moveset": ["POISON_STING", "STONE_EDGE", "POISON_FANG"], "score": 87.4}, {"speciesId": "piloswine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 600, "matchups": [{"opponent": "clodsire", "rating": 870}, {"opponent": "cradily", "rating": 827}, {"opponent": "gligar", "rating": 801}, {"opponent": "jumpluff_shadow", "rating": 681}, {"opponent": "diggersby", "rating": 548}], "counters": [{"opponent": "skeledirge", "rating": 143}, {"opponent": "furret", "rating": 228}, {"opponent": "magcargo", "rating": 235}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "talonflame", "rating": 470}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 35298}, {"moveId": "ICE_SHARD", "uses": 23002}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 20901}, {"moveId": "ICICLE_SPEAR", "uses": 17526}, {"moveId": "STONE_EDGE", "uses": 7432}, {"moveId": "HIGH_HORSEPOWER", "uses": 6771}, {"moveId": "BULLDOZE", "uses": 5583}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 87.4}, {"speciesId": "lileep", "speciesName": "<PERSON><PERSON>", "rating": 658, "matchups": [{"opponent": "talonflame", "rating": 764}, {"opponent": "typhlosion_shadow", "rating": 662, "opRating": 337}, {"opponent": "diggersby", "rating": 642, "opRating": 357}, {"opponent": "swampert_shadow", "rating": 629, "opRating": 370}, {"opponent": "quagsire_shadow", "rating": 629, "opRating": 370}], "counters": [{"opponent": "gligar", "rating": 305}, {"opponent": "clodsire", "rating": 382}, {"opponent": "cradily", "rating": 392}, {"opponent": "furret", "rating": 428}, {"opponent": "jumpluff_shadow", "rating": 437}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 22593}, {"moveId": "BULLET_SEED", "uses": 19876}, {"moveId": "INFESTATION", "uses": 15844}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 23042}, {"moveId": "GRASS_KNOT", "uses": 21796}, {"moveId": "RETURN", "uses": 7871}, {"moveId": "MIRROR_COAT", "uses": 5577}]}, "moveset": ["ACID", "GRASS_KNOT", "ANCIENT_POWER"], "score": 87.3}, {"speciesId": "marowak_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 648, "matchups": [{"opponent": "clodsire", "rating": 900, "opRating": 100}, {"opponent": "magcargo", "rating": 828, "opRating": 172}, {"opponent": "skeledirge", "rating": 740, "opRating": 260}, {"opponent": "flygon", "rating": 688, "opRating": 312}, {"opponent": "diggersby", "rating": 508, "opRating": 492}], "counters": [{"opponent": "gligar", "rating": 305}, {"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "talonflame", "rating": 329}, {"opponent": "cradily", "rating": 357}, {"opponent": "furret", "rating": 471}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45106}, {"moveId": "ROCK_SMASH", "uses": 13194}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 30525}, {"moveId": "ROCK_SLIDE", "uses": 16766}, {"moveId": "DIG", "uses": 5880}, {"moveId": "EARTHQUAKE", "uses": 5088}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "ROCK_SLIDE"], "score": 87.3}, {"speciesId": "ursaring", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 677, "matchups": [{"opponent": "swampert_shadow", "rating": 757, "opRating": 242}, {"opponent": "furret", "rating": 718}, {"opponent": "magcargo", "rating": 694, "opRating": 305}, {"opponent": "flygon", "rating": 579, "opRating": 420}, {"opponent": "claydol", "rating": 559, "opRating": 440}], "counters": [{"opponent": "cradily", "rating": 184}, {"opponent": "clodsire", "rating": 281}, {"opponent": "jumpluff_shadow", "rating": 316}, {"opponent": "gligar", "rating": 332}, {"opponent": "talonflame", "rating": 425}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 24337}, {"moveId": "COUNTER", "uses": 19486}, {"moveId": "METAL_CLAW", "uses": 14456}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 20659}, {"moveId": "SWIFT", "uses": 19308}, {"moveId": "TRAILBLAZE", "uses": 11319}, {"moveId": "PLAY_ROUGH", "uses": 4393}, {"moveId": "HYPER_BEAM", "uses": 2607}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "CLOSE_COMBAT"], "score": 87.3}, {"speciesId": "whiscash", "speciesName": "Whiscash", "rating": 603, "matchups": [{"opponent": "clodsire", "rating": 699}, {"opponent": "diggersby", "rating": 575}, {"opponent": "swampert_shadow", "rating": 558}, {"opponent": "talonflame", "rating": 522}, {"opponent": "gligar", "rating": 508}], "counters": [{"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "cradily", "rating": 253}, {"opponent": "furret", "rating": 290}, {"opponent": "gliscor", "rating": 409}, {"opponent": "drampa", "rating": 460}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 29347}, {"moveId": "WATER_GUN", "uses": 28953}], "chargedMoves": [{"moveId": "SCALD", "uses": 19857}, {"moveId": "MUD_BOMB", "uses": 16214}, {"moveId": "BLIZZARD", "uses": 11559}, {"moveId": "RETURN", "uses": 5447}, {"moveId": "WATER_PULSE", "uses": 5184}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SCALD"], "score": 87.3}, {"speciesId": "cradily_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 672, "matchups": [{"opponent": "drampa", "rating": 663, "opRating": 336}, {"opponent": "jumpluff_shadow", "rating": 631}, {"opponent": "swampert_shadow", "rating": 611}, {"opponent": "furret", "rating": 531}, {"opponent": "diggersby", "rating": 510}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "gligar", "rating": 385}, {"opponent": "claydol", "rating": 409}, {"opponent": "clodsire", "rating": 454}, {"opponent": "magcargo", "rating": 470}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 23316}, {"moveId": "BULLET_SEED", "uses": 19833}, {"moveId": "INFESTATION", "uses": 15161}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18763}, {"moveId": "GRASS_KNOT", "uses": 15468}, {"moveId": "ROCK_SLIDE", "uses": 12123}, {"moveId": "STONE_EDGE", "uses": 6332}, {"moveId": "BULLDOZE", "uses": 5458}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "ROCK_TOMB", "GRASS_KNOT"], "score": 87.1}, {"speciesId": "hippow<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 618, "matchups": [{"opponent": "clodsire", "rating": 890, "opRating": 109}, {"opponent": "magcargo", "rating": 806, "opRating": 193}, {"opponent": "talonflame", "rating": 785}, {"opponent": "skeledirge", "rating": 785, "opRating": 214}, {"opponent": "flygon", "rating": 651, "opRating": 348}], "counters": [{"opponent": "gligar", "rating": 240}, {"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "cradily", "rating": 357}, {"opponent": "diggersby", "rating": 373}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 16490}, {"moveId": "ICE_FANG", "uses": 15791}, {"moveId": "FIRE_FANG", "uses": 11557}, {"moveId": "THUNDER_FANG", "uses": 8925}, {"moveId": "BITE", "uses": 5528}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 18174}, {"moveId": "SCORCHING_SANDS", "uses": 13836}, {"moveId": "BODY_SLAM", "uses": 12033}, {"moveId": "EARTH_POWER", "uses": 5375}, {"moveId": "STONE_EDGE", "uses": 4926}, {"moveId": "EARTHQUAKE", "uses": 4028}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 86.8}, {"speciesId": "fletchinder", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 692, "matchups": [{"opponent": "gliscor", "rating": 734, "opRating": 265}, {"opponent": "jumpluff_shadow", "rating": 576}, {"opponent": "furret", "rating": 573}, {"opponent": "gligar", "rating": 569}, {"opponent": "diggersby", "rating": 548, "opRating": 451}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "swampert_shadow", "rating": 187}, {"opponent": "clodsire", "rating": 346}, {"opponent": "talonflame", "rating": 355}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 29183}, {"moveId": "STEEL_WING", "uses": 15840}, {"moveId": "PECK", "uses": 13264}], "chargedMoves": [{"moveId": "FLY", "uses": 24190}, {"moveId": "AERIAL_ACE", "uses": 18479}, {"moveId": "FLAME_CHARGE", "uses": 13766}, {"moveId": "HEAT_WAVE", "uses": 2036}]}, "moveset": ["EMBER", "FLY", "AERIAL_ACE"], "score": 86.4}, {"speciesId": "bibarel", "speciesName": "B<PERSON>rel", "rating": 597, "matchups": [{"opponent": "magcargo", "rating": 848, "opRating": 151}, {"opponent": "talonflame", "rating": 835}, {"opponent": "skeledirge", "rating": 835, "opRating": 164}, {"opponent": "clodsire", "rating": 614}, {"opponent": "diggersby", "rating": 563}], "counters": [{"opponent": "cradily", "rating": 260}, {"opponent": "jumpluff_shadow", "rating": 379}, {"opponent": "furret", "rating": 381}, {"opponent": "gligar", "rating": 461}, {"opponent": "swampert_shadow", "rating": 463}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 30140}, {"moveId": "WATER_GUN", "uses": 23308}, {"moveId": "TAKE_DOWN", "uses": 4872}], "chargedMoves": [{"moveId": "SURF", "uses": 31645}, {"moveId": "HYPER_FANG", "uses": 16619}, {"moveId": "RETURN", "uses": 5744}, {"moveId": "HYPER_BEAM", "uses": 4331}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 86.2}, {"speciesId": "lickilicky", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 635, "matchups": [{"opponent": "talonflame", "rating": 796}, {"opponent": "skeledirge", "rating": 796, "opRating": 203}, {"opponent": "typhlosion_shadow", "rating": 746, "opRating": 253}, {"opponent": "claydol", "rating": 537, "opRating": 462}, {"opponent": "gligar", "rating": 515}], "counters": [{"opponent": "cradily", "rating": 305}, {"opponent": "magcargo", "rating": 346}, {"opponent": "clodsire", "rating": 375}, {"opponent": "jumpluff_shadow", "rating": 408}, {"opponent": "furret", "rating": 409}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 34966}, {"moveId": "LICK", "uses": 18062}, {"moveId": "ZEN_HEADBUTT", "uses": 5264}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25243}, {"moveId": "SHADOW_BALL", "uses": 11117}, {"moveId": "EARTHQUAKE", "uses": 8981}, {"moveId": "SOLAR_BEAM", "uses": 6825}, {"moveId": "HYPER_BEAM", "uses": 6182}]}, "moveset": ["ROLLOUT", "BODY_SLAM", "SHADOW_BALL"], "score": 86.2}, {"speciesId": "lura<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 674, "matchups": [{"opponent": "swampert_shadow", "rating": 882, "opRating": 117}, {"opponent": "claydol", "rating": 844, "opRating": 155}, {"opponent": "flygon", "rating": 642, "opRating": 357}, {"opponent": "furret", "rating": 571}, {"opponent": "diggersby", "rating": 546, "opRating": 453}], "counters": [{"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "talonflame", "rating": 262}, {"opponent": "clodsire", "rating": 266}, {"opponent": "gligar", "rating": 339}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28641}, {"moveId": "LEAFAGE", "uses": 21135}, {"moveId": "RAZOR_LEAF", "uses": 8561}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 23134}, {"moveId": "SUPER_POWER", "uses": 14731}, {"moveId": "X_SCISSOR", "uses": 11746}, {"moveId": "TRAILBLAZE", "uses": 5748}, {"moveId": "LEAF_STORM", "uses": 3000}]}, "moveset": ["FURY_CUTTER", "LEAF_BLADE", "SUPER_POWER"], "score": 86.1}, {"speciesId": "nidoqueen_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 643, "matchups": [{"opponent": "jumpluff_shadow", "rating": 741}, {"opponent": "magcargo", "rating": 737, "opRating": 262}, {"opponent": "talonflame", "rating": 708}, {"opponent": "skeledirge", "rating": 708, "opRating": 291}, {"opponent": "cradily", "rating": 625}], "counters": [{"opponent": "gligar", "rating": 171}, {"opponent": "clodsire", "rating": 180}, {"opponent": "diggersby", "rating": 224}, {"opponent": "swampert_shadow", "rating": 242}, {"opponent": "furret", "rating": 346}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 28148}, {"moveId": "POISON_JAB", "uses": 22834}, {"moveId": "BITE", "uses": 7344}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 15715}, {"moveId": "EARTH_POWER", "uses": 15542}, {"moveId": "STONE_EDGE", "uses": 12781}, {"moveId": "SLUDGE_WAVE", "uses": 8480}, {"moveId": "EARTHQUAKE", "uses": 5712}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POISON_STING", "STONE_EDGE", "POISON_FANG"], "score": 86.1}, {"speciesId": "magmar", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 680, "matchups": [{"opponent": "piloswine", "rating": 877, "opRating": 122}, {"opponent": "magcargo", "rating": 802, "opRating": 197}, {"opponent": "ninetales_shadow", "rating": 776, "opRating": 223}, {"opponent": "drampa", "rating": 627, "opRating": 372}, {"opponent": "flygon", "rating": 539, "opRating": 460}], "counters": [{"opponent": "gligar", "rating": 255}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "cradily", "rating": 361}, {"opponent": "talonflame", "rating": 400}, {"opponent": "furret", "rating": 475}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 31344}, {"moveId": "EMBER", "uses": 26956}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 23027}, {"moveId": "SCORCHING_SANDS", "uses": 16687}, {"moveId": "RETURN", "uses": 7393}, {"moveId": "FLAMETHROWER", "uses": 7184}, {"moveId": "FIRE_BLAST", "uses": 3906}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "SCORCHING_SANDS"], "score": 85.9}, {"speciesId": "golurk", "speciesName": "Golurk", "rating": 632, "matchups": [{"opponent": "magcargo", "rating": 785, "opRating": 214}, {"opponent": "clodsire", "rating": 730, "opRating": 269}, {"opponent": "skeledirge", "rating": 683, "opRating": 316}, {"opponent": "diggersby", "rating": 640, "opRating": 359}, {"opponent": "flygon", "rating": 613, "opRating": 386}], "counters": [{"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 334}, {"opponent": "talonflame", "rating": 400}, {"opponent": "cradily", "rating": 493}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31331}, {"moveId": "ASTONISH", "uses": 26969}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 22231}, {"moveId": "SHADOW_PUNCH", "uses": 20835}, {"moveId": "EARTH_POWER", "uses": 11629}, {"moveId": "POLTERGEIST", "uses": 3668}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "DYNAMIC_PUNCH"], "score": 85.5}, {"speciesId": "don<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 622, "matchups": [{"opponent": "clodsire", "rating": 900, "opRating": 99}, {"opponent": "magcargo", "rating": 773, "opRating": 226}, {"opponent": "diggersby", "rating": 630, "opRating": 369}, {"opponent": "flygon", "rating": 607, "opRating": 392}, {"opponent": "claydol", "rating": 559, "opRating": 440}], "counters": [{"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "gligar", "rating": 240}, {"opponent": "cradily", "rating": 378}, {"opponent": "talonflame", "rating": 403}, {"opponent": "furret", "rating": 456}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23311}, {"moveId": "COUNTER", "uses": 15752}, {"moveId": "TACKLE", "uses": 10767}, {"moveId": "CHARM", "uses": 8474}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16633}, {"moveId": "TRAILBLAZE", "uses": 15206}, {"moveId": "EARTHQUAKE", "uses": 11781}, {"moveId": "HEAVY_SLAM", "uses": 8175}, {"moveId": "PLAY_ROUGH", "uses": 6522}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "TRAILBLAZE", "BODY_SLAM"], "score": 85.2}, {"speciesId": "dunsparce", "speciesName": "Dunsparce", "rating": 617, "matchups": [{"opponent": "magcargo", "rating": 823, "opRating": 176}, {"opponent": "talonflame", "rating": 801}, {"opponent": "ninetales_shadow", "rating": 660, "opRating": 339}, {"opponent": "skeledirge", "rating": 548, "opRating": 451}, {"opponent": "jumpluff_shadow", "rating": 508}], "counters": [{"opponent": "diggersby", "rating": 330}, {"opponent": "furret", "rating": 337}, {"opponent": "cradily", "rating": 347}, {"opponent": "gligar", "rating": 423}, {"opponent": "clodsire", "rating": 447}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 27089}, {"moveId": "ASTONISH", "uses": 19977}, {"moveId": "BITE", "uses": 11247}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27807}, {"moveId": "ROCK_SLIDE", "uses": 23489}, {"moveId": "DIG", "uses": 6991}]}, "moveset": ["ROLLOUT", "DRILL_RUN", "ROCK_SLIDE"], "score": 85.2}, {"speciesId": "marowak", "speciesName": "Marowak", "rating": 630, "matchups": [{"opponent": "magcargo", "rating": 804, "opRating": 196}, {"opponent": "drampa", "rating": 732, "opRating": 268}, {"opponent": "clodsire", "rating": 648}, {"opponent": "cradily", "rating": 540}, {"opponent": "flygon", "rating": 536, "opRating": 464}], "counters": [{"opponent": "gligar", "rating": 240}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "talonflame", "rating": 281}, {"opponent": "furret", "rating": 393}, {"opponent": "diggersby", "rating": 474}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 44967}, {"moveId": "ROCK_SMASH", "uses": 13333}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 27192}, {"moveId": "ROCK_SLIDE", "uses": 14371}, {"moveId": "RETURN", "uses": 6933}, {"moveId": "DIG", "uses": 5306}, {"moveId": "EARTHQUAKE", "uses": 4548}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "ROCK_SLIDE"], "score": 85.2}, {"speciesId": "run<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 604, "matchups": [{"opponent": "claydol", "rating": 766, "opRating": 233}, {"opponent": "cradily", "rating": 688}, {"opponent": "clodsire", "rating": 669}, {"opponent": "gligar", "rating": 605}, {"opponent": "flygon", "rating": 568, "opRating": 431}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "furret", "rating": 431}, {"opponent": "jumpluff_shadow", "rating": 460}, {"opponent": "diggersby", "rating": 468}, {"opponent": "magcargo", "rating": 470}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 31189}, {"moveId": "ASTONISH", "uses": 27111}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 20805}, {"moveId": "ROCK_TOMB", "uses": 18700}, {"moveId": "SHADOW_BALL", "uses": 12188}, {"moveId": "SAND_TOMB", "uses": 6606}]}, "moveset": ["SHADOW_CLAW", "ROCK_TOMB", "SHADOW_BALL"], "score": 85.2}, {"speciesId": "sceptile", "speciesName": "Sceptile", "rating": 641, "matchups": [{"opponent": "swampert_shadow", "rating": 860, "opRating": 139}, {"opponent": "claydol", "rating": 806, "opRating": 193}, {"opponent": "furret", "rating": 725}, {"opponent": "cradily", "rating": 617}, {"opponent": "diggersby", "rating": 513, "opRating": 486}], "counters": [{"opponent": "gligar", "rating": 164}, {"opponent": "talonflame", "rating": 214}, {"opponent": "jumpluff_shadow", "rating": 330}, {"opponent": "clodsire", "rating": 341}, {"opponent": "magcargo", "rating": 388}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 31972}, {"moveId": "BULLET_SEED", "uses": 26328}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 19604}, {"moveId": "BREAKING_SWIPE", "uses": 12285}, {"moveId": "AERIAL_ACE", "uses": 9848}, {"moveId": "FRENZY_PLANT", "uses": 7281}, {"moveId": "DRAGON_CLAW", "uses": 4860}, {"moveId": "EARTHQUAKE", "uses": 4456}]}, "moveset": ["FURY_CUTTER", "FRENZY_PLANT", "BREAKING_SWIPE"], "score": 85.2}, {"speciesId": "rhyperior", "speciesName": "Rhyperior", "rating": 612, "matchups": [{"opponent": "magcargo", "rating": 873, "opRating": 126}, {"opponent": "skeledirge", "rating": 817, "opRating": 182}, {"opponent": "flygon", "rating": 656, "opRating": 343}, {"opponent": "jumpluff_shadow", "rating": 638}, {"opponent": "talonflame", "rating": 600}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "cradily", "rating": 315}, {"opponent": "diggersby", "rating": 376}, {"opponent": "furret", "rating": 428}, {"opponent": "clodsire", "rating": 485}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 37010}, {"moveId": "SMACK_DOWN", "uses": 21290}], "chargedMoves": [{"moveId": "ROCK_WRECKER", "uses": 14972}, {"moveId": "SUPER_POWER", "uses": 10974}, {"moveId": "BREAKING_SWIPE", "uses": 10513}, {"moveId": "SURF", "uses": 9331}, {"moveId": "EARTHQUAKE", "uses": 4831}, {"moveId": "STONE_EDGE", "uses": 4241}, {"moveId": "SKULL_BASH", "uses": 3441}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "ROCK_WRECKER"], "score": 85}, {"speciesId": "hippo<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 607, "matchups": [{"opponent": "magcargo", "rating": 785, "opRating": 214}, {"opponent": "talonflame", "rating": 757}, {"opponent": "skeledirge", "rating": 757, "opRating": 242}, {"opponent": "drampa", "rating": 672, "opRating": 327}, {"opponent": "clodsire", "rating": 630}], "counters": [{"opponent": "cradily", "rating": 329}, {"opponent": "gligar", "rating": 343}, {"opponent": "diggersby", "rating": 353}, {"opponent": "furret", "rating": 381}, {"opponent": "jumpluff_shadow", "rating": 434}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 15871}, {"moveId": "SAND_ATTACK", "uses": 15708}, {"moveId": "FIRE_FANG", "uses": 11790}, {"moveId": "THUNDER_FANG", "uses": 9009}, {"moveId": "BITE", "uses": 5914}], "chargedMoves": [{"moveId": "WEATHER_BALL_ROCK", "uses": 18188}, {"moveId": "SCORCHING_SANDS", "uses": 13795}, {"moveId": "BODY_SLAM", "uses": 12075}, {"moveId": "EARTH_POWER", "uses": 5381}, {"moveId": "STONE_EDGE", "uses": 4943}, {"moveId": "EARTHQUAKE", "uses": 4023}]}, "moveset": ["SAND_ATTACK", "WEATHER_BALL_ROCK", "SCORCHING_SANDS"], "score": 84.9}, {"speciesId": "kecleon", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 634, "matchups": [{"opponent": "claydol", "rating": 728, "opRating": 271}, {"opponent": "skeledirge", "rating": 686, "opRating": 313}, {"opponent": "quagsire_shadow", "rating": 656, "opRating": 343}, {"opponent": "gligar", "rating": 618}, {"opponent": "jumpluff_shadow", "rating": 529}], "counters": [{"opponent": "magcargo", "rating": 337}, {"opponent": "talonflame", "rating": 407}, {"opponent": "cradily", "rating": 451}, {"opponent": "furret", "rating": 468}, {"opponent": "clodsire", "rating": 495}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 39936}, {"moveId": "LICK", "uses": 18364}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 13823}, {"moveId": "FOUL_PLAY", "uses": 13274}, {"moveId": "AERIAL_ACE", "uses": 12923}, {"moveId": "FLAMETHROWER", "uses": 8270}, {"moveId": "THUNDER", "uses": 5455}, {"moveId": "SHADOW_SNEAK", "uses": 4557}]}, "moveset": ["SUCKER_PUNCH", "AERIAL_ACE", "ICE_BEAM"], "score": 84.9}, {"speciesId": "drampa", "speciesName": "Drampa", "rating": 661, "matchups": [{"opponent": "talonflame", "rating": 734}, {"opponent": "quagsire_shadow", "rating": 634, "opRating": 365}, {"opponent": "pidgeot", "rating": 630, "opRating": 369}, {"opponent": "ninetales_shadow", "rating": 578, "opRating": 421}, {"opponent": "flygon", "rating": 560, "opRating": 439}], "counters": [{"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "cradily", "rating": 336}, {"opponent": "furret", "rating": 362}, {"opponent": "clodsire", "rating": 391}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 37274}, {"moveId": "EXTRASENSORY", "uses": 21026}], "chargedMoves": [{"moveId": "SWIFT", "uses": 22811}, {"moveId": "FLY", "uses": 18143}, {"moveId": "OUTRAGE", "uses": 12845}, {"moveId": "DRAGON_PULSE", "uses": 4288}]}, "moveset": ["DRAGON_BREATH", "SWIFT", "FLY"], "score": 84.7}, {"speciesId": "victreebel", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "swampert_shadow", "rating": 824, "opRating": 175}, {"opponent": "quagsire_shadow", "rating": 824, "opRating": 175}, {"opponent": "claydol", "rating": 576, "opRating": 423}, {"opponent": "jumpluff_shadow", "rating": 553}, {"opponent": "diggersby", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 203}, {"opponent": "clodsire", "rating": 245}, {"opponent": "gligar", "rating": 312}, {"opponent": "cradily", "rating": 340}, {"opponent": "furret", "rating": 459}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 29796}, {"moveId": "MAGICAL_LEAF", "uses": 19906}, {"moveId": "RAZOR_LEAF", "uses": 8613}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28273}, {"moveId": "SLUDGE_BOMB", "uses": 13710}, {"moveId": "LEAF_TORNADO", "uses": 5390}, {"moveId": "RETURN", "uses": 5317}, {"moveId": "ACID_SPRAY", "uses": 3095}, {"moveId": "SOLAR_BEAM", "uses": 2388}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 84.6}, {"speciesId": "marshtomp_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 576, "matchups": [{"opponent": "magcargo", "rating": 823, "opRating": 176}, {"opponent": "talonflame", "rating": 809}, {"opponent": "skeledirge", "rating": 809, "opRating": 190}, {"opponent": "clodsire", "rating": 744}, {"opponent": "flygon", "rating": 643, "opRating": 356}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "cradily", "rating": 319}, {"opponent": "furret", "rating": 378}, {"opponent": "gligar", "rating": 454}, {"opponent": "diggersby", "rating": 454}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30886}, {"moveId": "WATER_GUN", "uses": 27414}], "chargedMoves": [{"moveId": "SURF", "uses": 23087}, {"moveId": "SLUDGE", "uses": 17959}, {"moveId": "MUD_BOMB", "uses": 17211}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 84.4}, {"speciesId": "roserade", "speciesName": "<PERSON><PERSON>", "rating": 646, "matchups": [{"opponent": "diggersby", "rating": 788, "opRating": 211}, {"opponent": "swampert_shadow", "rating": 737, "opRating": 262}, {"opponent": "furret", "rating": 685}, {"opponent": "claydol", "rating": 670, "opRating": 329}, {"opponent": "flygon", "rating": 561, "opRating": 438}], "counters": [{"opponent": "clodsire", "rating": 168}, {"opponent": "talonflame", "rating": 333}, {"opponent": "jumpluff_shadow", "rating": 405}, {"opponent": "cradily", "rating": 434}, {"opponent": "gligar", "rating": 461}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 15710}, {"moveId": "POISON_JAB", "uses": 14188}, {"moveId": "BULLET_SEED", "uses": 11817}, {"moveId": "MAGICAL_LEAF", "uses": 11061}, {"moveId": "RAZOR_LEAF", "uses": 5508}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 17123}, {"moveId": "GRASS_KNOT", "uses": 12634}, {"moveId": "SLUDGE_BOMB", "uses": 11907}, {"moveId": "LEAF_STORM", "uses": 7692}, {"moveId": "DAZZLING_GLEAM", "uses": 6325}, {"moveId": "SOLAR_BEAM", "uses": 2655}]}, "moveset": ["POISON_STING", "WEATHER_BALL_FIRE", "LEAF_STORM"], "score": 84.3}, {"speciesId": "piloswine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 618, "matchups": [{"opponent": "clodsire", "rating": 912, "opRating": 87}, {"opponent": "gligar", "rating": 801}, {"opponent": "jumpluff_shadow", "rating": 714}, {"opponent": "flygon", "rating": 678, "opRating": 321}, {"opponent": "diggersby", "rating": 603, "opRating": 396}], "counters": [{"opponent": "furret", "rating": 190}, {"opponent": "magcargo", "rating": 192}, {"opponent": "swampert_shadow", "rating": 224}, {"opponent": "talonflame", "rating": 392}, {"opponent": "cradily", "rating": 427}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34683}, {"moveId": "ICE_SHARD", "uses": 23617}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19982}, {"moveId": "ICICLE_SPEAR", "uses": 16740}, {"moveId": "STONE_EDGE", "uses": 6970}, {"moveId": "HIGH_HORSEPOWER", "uses": 6460}, {"moveId": "BULLDOZE", "uses": 5299}, {"moveId": "RETURN", "uses": 2784}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 84.1}, {"speciesId": "obstagoon", "speciesName": "Obstagoon", "rating": 628, "matchups": [{"opponent": "magcargo", "rating": 755}, {"opponent": "furret", "rating": 722}, {"opponent": "cradily", "rating": 543}, {"opponent": "swampert_shadow", "rating": 543}, {"opponent": "diggersby", "rating": 529}], "counters": [{"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "gligar", "rating": 293}, {"opponent": "claydol", "rating": 309}, {"opponent": "talonflame", "rating": 329}, {"opponent": "clodsire", "rating": 353}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 34714}, {"moveId": "LICK", "uses": 23586}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25216}, {"moveId": "CROSS_CHOP", "uses": 21600}, {"moveId": "HYPER_BEAM", "uses": 6193}, {"moveId": "GUNK_SHOT", "uses": 4749}, {"moveId": "OBSTRUCT", "uses": 647}]}, "moveset": ["COUNTER", "NIGHT_SLASH", "CROSS_CHOP"], "score": 84}, {"speciesId": "bibarel_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 591, "matchups": [{"opponent": "magcargo", "rating": 808, "opRating": 191}, {"opponent": "talonflame", "rating": 795}, {"opponent": "skeledirge", "rating": 795, "opRating": 204}, {"opponent": "pidgeot", "rating": 694, "opRating": 305}, {"opponent": "gligar", "rating": 593}], "counters": [{"opponent": "cradily", "rating": 305}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "clodsire", "rating": 367}, {"opponent": "diggersby", "rating": 373}, {"opponent": "furret", "rating": 412}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 31152}, {"moveId": "WATER_GUN", "uses": 22514}, {"moveId": "TAKE_DOWN", "uses": 4662}], "chargedMoves": [{"moveId": "SURF", "uses": 34426}, {"moveId": "HYPER_FANG", "uses": 18745}, {"moveId": "HYPER_BEAM", "uses": 5060}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROLLOUT", "SURF", "HYPER_FANG"], "score": 83.8}, {"speciesId": "tropius", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 675, "matchups": [{"opponent": "quagsire_shadow", "rating": 879, "opRating": 120}, {"opponent": "swampert_shadow", "rating": 714, "opRating": 285}, {"opponent": "claydol", "rating": 567, "opRating": 432}, {"opponent": "diggersby", "rating": 561, "opRating": 438}, {"opponent": "furret", "rating": 502}], "counters": [{"opponent": "cradily", "rating": 270}, {"opponent": "gligar", "rating": 343}, {"opponent": "talonflame", "rating": 351}, {"opponent": "clodsire", "rating": 401}, {"opponent": "jumpluff_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 37069}, {"moveId": "RAZOR_LEAF", "uses": 21231}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 23642}, {"moveId": "AERIAL_ACE", "uses": 14673}, {"moveId": "BRUTAL_SWING", "uses": 13399}, {"moveId": "STOMP", "uses": 6573}]}, "moveset": ["AIR_SLASH", "LEAF_BLADE", "AERIAL_ACE"], "score": 83.8}, {"speciesId": "litleo", "speciesName": "Litleo", "rating": 666, "matchups": [{"opponent": "gliscor", "rating": 659, "opRating": 340}, {"opponent": "skeledirge", "rating": 638, "opRating": 361}, {"opponent": "furret", "rating": 604}, {"opponent": "jumpluff_shadow", "rating": 590}, {"opponent": "gligar", "rating": 517}], "counters": [{"opponent": "magcargo", "rating": 226}, {"opponent": "clodsire", "rating": 338}, {"opponent": "diggersby", "rating": 339}, {"opponent": "cradily", "rating": 388}, {"opponent": "talonflame", "rating": 392}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 20956}, {"moveId": "EMBER", "uses": 14568}, {"moveId": "FIRE_FANG", "uses": 12747}, {"moveId": "TACKLE", "uses": 10037}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24314}, {"moveId": "FLAME_CHARGE", "uses": 23990}, {"moveId": "FLAMETHROWER", "uses": 9964}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "CRUNCH"], "score": 83.7}, {"speciesId": "oranguru", "speciesName": "Oranguru", "rating": 645, "matchups": [{"opponent": "clodsire", "rating": 669, "opRating": 330}, {"opponent": "quagsire_shadow", "rating": 654, "opRating": 345}, {"opponent": "piloswine", "rating": 644, "opRating": 355}, {"opponent": "swampert_shadow", "rating": 591, "opRating": 408}, {"opponent": "flygon", "rating": 542, "opRating": 457}], "counters": [{"opponent": "furret", "rating": 284}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "cradily", "rating": 381}, {"opponent": "talonflame", "rating": 422}, {"opponent": "gligar", "rating": 423}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 47110}, {"moveId": "ZEN_HEADBUTT", "uses": 10257}, {"moveId": "YAWN", "uses": 884}], "chargedMoves": [{"moveId": "BRUTAL_SWING", "uses": 19443}, {"moveId": "TRAILBLAZE", "uses": 13893}, {"moveId": "FUTURE_SIGHT", "uses": 9259}, {"moveId": "PSYCHIC", "uses": 8923}, {"moveId": "FOUL_PLAY", "uses": 6725}]}, "moveset": ["CONFUSION", "BRUTAL_SWING", "TRAILBLAZE"], "score": 83.7}, {"speciesId": "rhydon", "speciesName": "R<PERSON><PERSON>", "rating": 604, "matchups": [{"opponent": "magcargo", "rating": 875, "opRating": 125}, {"opponent": "skeledirge", "rating": 819, "opRating": 180}, {"opponent": "flygon", "rating": 661, "opRating": 338}, {"opponent": "talonflame", "rating": 606}, {"opponent": "drampa", "rating": 591, "opRating": 408}], "counters": [{"opponent": "gligar", "rating": 221}, {"opponent": "cradily", "rating": 315}, {"opponent": "furret", "rating": 425}, {"opponent": "clodsire", "rating": 473}, {"opponent": "jumpluff_shadow", "rating": 496}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 47170}, {"moveId": "ROCK_SMASH", "uses": 11130}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 14772}, {"moveId": "SURF", "uses": 13302}, {"moveId": "STONE_EDGE", "uses": 12916}, {"moveId": "MEGAHORN", "uses": 10258}, {"moveId": "EARTHQUAKE", "uses": 7067}]}, "moveset": ["MUD_SLAP", "BREAKING_SWIPE", "STONE_EDGE"], "score": 83.7}, {"speciesId": "gastrodon", "speciesName": "Gastrodon", "rating": 615, "matchups": [{"opponent": "clodsire", "rating": 913, "opRating": 86}, {"opponent": "skeledirge", "rating": 818, "opRating": 181}, {"opponent": "magcargo", "rating": 776, "opRating": 223}, {"opponent": "swampert_shadow", "rating": 550, "opRating": 449}, {"opponent": "flygon", "rating": 502, "opRating": 497}], "counters": [{"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "gligar", "rating": 217}, {"opponent": "cradily", "rating": 239}, {"opponent": "talonflame", "rating": 303}, {"opponent": "furret", "rating": 387}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 8703}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4802}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4357}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3755}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3360}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3344}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3276}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3022}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2860}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2836}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2799}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2789}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2746}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2611}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2609}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2319}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2041}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 20104}, {"moveId": "WATER_PULSE", "uses": 16621}, {"moveId": "EARTH_POWER", "uses": 15829}, {"moveId": "EARTHQUAKE", "uses": 5824}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTH_POWER"], "score": 83.5}, {"speciesId": "abomasnow", "speciesName": "Abomasnow", "rating": 627, "matchups": [{"opponent": "diggersby", "rating": 611}, {"opponent": "claydol", "rating": 608}, {"opponent": "swampert_shadow", "rating": 601}, {"opponent": "clodsire", "rating": 548}, {"opponent": "gligar", "rating": 513}], "counters": [{"opponent": "magcargo", "rating": 81}, {"opponent": "skeledirge", "rating": 212}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "talonflame", "rating": 348}, {"opponent": "furret", "rating": 403}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 31764}, {"moveId": "LEAFAGE", "uses": 18391}, {"moveId": "RAZOR_LEAF", "uses": 8169}], "chargedMoves": [{"moveId": "WEATHER_BALL_ICE", "uses": 26264}, {"moveId": "ICY_WIND", "uses": 8956}, {"moveId": "ENERGY_BALL", "uses": 8502}, {"moveId": "OUTRAGE", "uses": 6423}, {"moveId": "RETURN", "uses": 4109}, {"moveId": "BLIZZARD", "uses": 4014}]}, "moveset": ["POWDER_SNOW", "ICY_WIND", "ENERGY_BALL"], "score": 83.2}, {"speciesId": "spinda", "speciesName": "Spinda", "rating": 611, "matchups": [{"opponent": "gligar", "rating": 644}, {"opponent": "jumpluff_shadow", "rating": 602}, {"opponent": "swampert_shadow", "rating": 570, "opRating": 429}, {"opponent": "clodsire", "rating": 524}, {"opponent": "claydol", "rating": 521, "opRating": 478}], "counters": [{"opponent": "talonflame", "rating": 292}, {"opponent": "diggersby", "rating": 402}, {"opponent": "magcargo", "rating": 418}, {"opponent": "cradily", "rating": 440}, {"opponent": "furret", "rating": 450}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 31811}, {"moveId": "PSYCHO_CUT", "uses": 26489}], "chargedMoves": [{"moveId": "ICY_WIND", "uses": 27070}, {"moveId": "ROCK_TOMB", "uses": 20599}, {"moveId": "DIG", "uses": 10625}]}, "moveset": ["SUCKER_PUNCH", "ROCK_TOMB", "ICY_WIND"], "score": 83.2}, {"speciesId": "sandslash_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 594, "matchups": [{"opponent": "magcargo", "rating": 724}, {"opponent": "talonflame", "rating": 692}, {"opponent": "claydol", "rating": 636, "opRating": 364}, {"opponent": "clodsire", "rating": 548}, {"opponent": "jumpluff_shadow", "rating": 504}], "counters": [{"opponent": "gligar", "rating": 305}, {"opponent": "cradily", "rating": 333}, {"opponent": "furret", "rating": 368}, {"opponent": "diggersby", "rating": 370}, {"opponent": "swampert_shadow", "rating": 404}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 22914}, {"moveId": "MUD_SHOT", "uses": 20154}, {"moveId": "METAL_CLAW", "uses": 15241}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 16882}, {"moveId": "ROCK_TOMB", "uses": 16180}, {"moveId": "SCORCHING_SANDS", "uses": 13878}, {"moveId": "BULLDOZE", "uses": 7428}, {"moveId": "EARTHQUAKE", "uses": 3920}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "NIGHT_SLASH"], "score": 83.1}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Zygarde (50% Forme)", "rating": 591, "matchups": [{"opponent": "cradily", "rating": 642}, {"opponent": "clodsire", "rating": 612}, {"opponent": "magcargo", "rating": 597}, {"opponent": "diggersby", "rating": 545}, {"opponent": "furret", "rating": 526}], "counters": [{"opponent": "jumpluff_shadow", "rating": 267}, {"opponent": "talonflame", "rating": 329}, {"opponent": "gligar", "rating": 446}, {"opponent": "claydol", "rating": 458}, {"opponent": "swampert_shadow", "rating": 481}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 37847}, {"moveId": "BITE", "uses": 15152}, {"moveId": "ZEN_HEADBUTT", "uses": 5255}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 15939}, {"moveId": "OUTRAGE", "uses": 15832}, {"moveId": "EARTHQUAKE", "uses": 10758}, {"moveId": "BULLDOZE", "uses": 10097}, {"moveId": "HYPER_BEAM", "uses": 5794}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "EARTHQUAKE"], "score": 83.1}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 609, "matchups": [{"opponent": "clodsire", "rating": 884, "opRating": 115}, {"opponent": "magcargo", "rating": 805, "opRating": 194}, {"opponent": "skeledirge", "rating": 726, "opRating": 273}, {"opponent": "diggersby", "rating": 626, "opRating": 373}, {"opponent": "claydol", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "gligar", "rating": 297}, {"opponent": "talonflame", "rating": 348}, {"opponent": "cradily", "rating": 354}, {"opponent": "furret", "rating": 468}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22551}, {"moveId": "COUNTER", "uses": 15926}, {"moveId": "TACKLE", "uses": 11138}, {"moveId": "CHARM", "uses": 8711}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 16701}, {"moveId": "TRAILBLAZE", "uses": 15160}, {"moveId": "EARTHQUAKE", "uses": 11748}, {"moveId": "HEAVY_SLAM", "uses": 8160}, {"moveId": "PLAY_ROUGH", "uses": 6525}]}, "moveset": ["MUD_SLAP", "TRAILBLAZE", "BODY_SLAM"], "score": 82.9}, {"speciesId": "mamos<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 550, "matchups": [{"opponent": "clodsire", "rating": 865}, {"opponent": "cradily", "rating": 851}, {"opponent": "gligar", "rating": 778}, {"opponent": "diggersby", "rating": 778}, {"opponent": "jumpluff_shadow", "rating": 583}], "counters": [{"opponent": "magcargo", "rating": 106}, {"opponent": "skeledirge", "rating": 118}, {"opponent": "quagsire_shadow", "rating": 198}, {"opponent": "talonflame", "rating": 203}, {"opponent": "furret", "rating": 468}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34696}, {"moveId": "MUD_SLAP", "uses": 23604}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19375}, {"moveId": "ICICLE_SPEAR", "uses": 16242}, {"moveId": "STONE_EDGE", "uses": 6182}, {"moveId": "HIGH_HORSEPOWER", "uses": 6097}, {"moveId": "ANCIENT_POWER", "uses": 5551}, {"moveId": "BULLDOZE", "uses": 4958}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 82.9}, {"speciesId": "obstagoon_shadow", "speciesName": "Obstagoon (Shadow)", "rating": 634, "matchups": [{"opponent": "magcargo", "rating": 708}, {"opponent": "diggersby", "rating": 675, "opRating": 324}, {"opponent": "furret", "rating": 664}, {"opponent": "drampa", "rating": 613, "opRating": 386}, {"opponent": "cradily", "rating": 580}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 187}, {"opponent": "swampert_shadow", "rating": 297}, {"opponent": "talonflame", "rating": 359}, {"opponent": "clodsire", "rating": 391}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 35296}, {"moveId": "LICK", "uses": 23004}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 25231}, {"moveId": "CROSS_CHOP", "uses": 21542}, {"moveId": "HYPER_BEAM", "uses": 6168}, {"moveId": "GUNK_SHOT", "uses": 4744}, {"moveId": "OBSTRUCT", "uses": 633}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "CROSS_CHOP", "NIGHT_SLASH"], "score": 82.6}, {"speciesId": "golem_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 562, "matchups": [{"opponent": "magcargo", "rating": 858, "opRating": 141}, {"opponent": "skeledirge", "rating": 841, "opRating": 158}, {"opponent": "talonflame", "rating": 790}, {"opponent": "furret", "rating": 581}, {"opponent": "jumpluff_shadow", "rating": 538}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "diggersby", "rating": 333}, {"opponent": "cradily", "rating": 406}, {"opponent": "swampert_shadow", "rating": 415}, {"opponent": "clodsire", "rating": 487}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23579}, {"moveId": "MUD_SHOT", "uses": 18714}, {"moveId": "ROCK_THROW", "uses": 16024}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17721}, {"moveId": "ROCK_BLAST", "uses": 15943}, {"moveId": "EARTHQUAKE", "uses": 13307}, {"moveId": "ANCIENT_POWER", "uses": 11235}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "ROCK_BLAST"], "score": 82.3}, {"speciesId": "bellossom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 653, "matchups": [{"opponent": "swampert_shadow", "rating": 890, "opRating": 109}, {"opponent": "quagsire_shadow", "rating": 890, "opRating": 109}, {"opponent": "claydol", "rating": 761, "opRating": 238}, {"opponent": "drampa", "rating": 621, "opRating": 378}, {"opponent": "diggersby", "rating": 562, "opRating": 437}], "counters": [{"opponent": "clodsire", "rating": 221}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "talonflame", "rating": 307}, {"opponent": "gligar", "rating": 316}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 20214}, {"moveId": "BULLET_SEED", "uses": 16168}, {"moveId": "MAGICAL_LEAF", "uses": 14706}, {"moveId": "RAZOR_LEAF", "uses": 7223}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 42064}, {"moveId": "DAZZLING_GLEAM", "uses": 11798}, {"moveId": "PETAL_BLIZZARD", "uses": 4364}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 82.2}, {"speciesId": "magmortar", "speciesName": "Magmortar", "rating": 651, "matchups": [{"opponent": "piloswine", "rating": 870, "opRating": 129}, {"opponent": "talonflame", "rating": 773}, {"opponent": "typhlosion_shadow", "rating": 717, "opRating": 282}, {"opponent": "pidgeot", "rating": 671, "opRating": 328}, {"opponent": "drampa", "rating": 532, "opRating": 467}], "counters": [{"opponent": "furret", "rating": 221}, {"opponent": "clodsire", "rating": 223}, {"opponent": "gligar", "rating": 263}, {"opponent": "cradily", "rating": 364}, {"opponent": "jumpluff_shadow", "rating": 366}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 33101}, {"moveId": "FIRE_SPIN", "uses": 25199}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 19290}, {"moveId": "SCORCHING_SANDS", "uses": 12461}, {"moveId": "BRICK_BREAK", "uses": 11179}, {"moveId": "PSYCHIC", "uses": 6156}, {"moveId": "THUNDERBOLT", "uses": 6045}, {"moveId": "FIRE_BLAST", "uses": 3218}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "THUNDERBOLT"], "score": 82.2}, {"speciesId": "typhlosion", "speciesName": "Typhlosion", "rating": 657, "matchups": [{"opponent": "piloswine", "rating": 880, "opRating": 119}, {"opponent": "pidgeot", "rating": 696, "opRating": 303}, {"opponent": "flygon", "rating": 585, "opRating": 414}, {"opponent": "gliscor", "rating": 585, "opRating": 414}, {"opponent": "jumpluff_shadow", "rating": 508}], "counters": [{"opponent": "gligar", "rating": 320}, {"opponent": "furret", "rating": 409}, {"opponent": "clodsire", "rating": 449}, {"opponent": "talonflame", "rating": 455}, {"opponent": "cradily", "rating": 468}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 26114}, {"moveId": "EMBER", "uses": 16544}, {"moveId": "SHADOW_CLAW", "uses": 15664}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 28497}, {"moveId": "THUNDER_PUNCH", "uses": 12374}, {"moveId": "SOLAR_BEAM", "uses": 7874}, {"moveId": "OVERHEAT", "uses": 5998}, {"moveId": "FIRE_BLAST", "uses": 3530}]}, "moveset": ["INCINERATE", "BLAST_BURN", "THUNDER_PUNCH"], "score": 82}, {"speciesId": "arcanine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "piloswine", "rating": 852, "opRating": 148}, {"opponent": "jumpluff_shadow", "rating": 640}, {"opponent": "gliscor", "rating": 568, "opRating": 432}, {"opponent": "pidgeot", "rating": 564, "opRating": 436}, {"opponent": "ninetales_shadow", "rating": 508, "opRating": 492}], "counters": [{"opponent": "furret", "rating": 328}, {"opponent": "cradily", "rating": 364}, {"opponent": "gligar", "rating": 370}, {"opponent": "clodsire", "rating": 425}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 20909}, {"moveId": "SNARL", "uses": 19640}, {"moveId": "THUNDER_FANG", "uses": 11776}, {"moveId": "BITE", "uses": 5902}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 11568}, {"moveId": "WILD_CHARGE", "uses": 10942}, {"moveId": "FLAMETHROWER", "uses": 10068}, {"moveId": "CRUNCH", "uses": 9426}, {"moveId": "SCORCHING_SANDS", "uses": 8812}, {"moveId": "BULLDOZE", "uses": 4711}, {"moveId": "FIRE_BLAST", "uses": 2707}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 81.9}, {"speciesId": "bellossom", "speciesName": "Bellossom", "rating": 644, "matchups": [{"opponent": "quagsire_shadow", "rating": 925, "opRating": 74}, {"opponent": "swampert_shadow", "rating": 890, "opRating": 109}, {"opponent": "claydol", "rating": 800, "opRating": 199}, {"opponent": "furret", "rating": 574}, {"opponent": "flygon", "rating": 535, "opRating": 464}], "counters": [{"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "talonflame", "rating": 262}, {"opponent": "gligar", "rating": 263}, {"opponent": "cradily", "rating": 409}, {"opponent": "clodsire", "rating": 471}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 20166}, {"moveId": "BULLET_SEED", "uses": 15949}, {"moveId": "MAGICAL_LEAF", "uses": 14732}, {"moveId": "RAZOR_LEAF", "uses": 7400}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 36602}, {"moveId": "DAZZLING_GLEAM", "uses": 9672}, {"moveId": "RETURN", "uses": 8214}, {"moveId": "PETAL_BLIZZARD", "uses": 3719}]}, "moveset": ["ACID", "LEAF_BLADE", "DAZZLING_GLEAM"], "score": 81.9}, {"speciesId": "victree<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 662, "matchups": [{"opponent": "swampert_shadow", "rating": 790, "opRating": 209}, {"opponent": "quagsire_shadow", "rating": 790, "opRating": 209}, {"opponent": "piloswine", "rating": 652, "opRating": 347}, {"opponent": "flygon", "rating": 606, "opRating": 393}, {"opponent": "drampa", "rating": 541, "opRating": 458}], "counters": [{"opponent": "talonflame", "rating": 237}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "gligar", "rating": 389}, {"opponent": "cradily", "rating": 409}, {"opponent": "furret", "rating": 490}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 30555}, {"moveId": "MAGICAL_LEAF", "uses": 20049}, {"moveId": "RAZOR_LEAF", "uses": 7702}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30722}, {"moveId": "SLUDGE_BOMB", "uses": 15674}, {"moveId": "LEAF_TORNADO", "uses": 5862}, {"moveId": "ACID_SPRAY", "uses": 3485}, {"moveId": "SOLAR_BEAM", "uses": 2546}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "LEAF_BLADE", "SLUDGE_BOMB"], "score": 81.7}, {"speciesId": "turtonator", "speciesName": "Turtonator", "rating": 600, "matchups": [{"opponent": "ninetales_shadow", "rating": 796, "opRating": 203}, {"opponent": "gligar", "rating": 592}, {"opponent": "furret", "rating": 584}, {"opponent": "jumpluff_shadow", "rating": 575}, {"opponent": "talonflame", "rating": 526}], "counters": [{"opponent": "clodsire", "rating": 182}, {"opponent": "swampert_shadow", "rating": 194}, {"opponent": "magcargo", "rating": 286}, {"opponent": "cradily", "rating": 482}, {"opponent": "diggersby", "rating": 491}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 24885}, {"moveId": "EMBER", "uses": 17645}, {"moveId": "FIRE_SPIN", "uses": 15759}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 18257}, {"moveId": "OVERHEAT", "uses": 16883}, {"moveId": "DRAGON_PULSE", "uses": 15934}, {"moveId": "FLASH_CANNON", "uses": 7195}]}, "moveset": ["INCINERATE", "DRAGON_PULSE", "OVERHEAT"], "score": 81.6}, {"speciesId": "dubwool", "speciesName": "Dubwool", "rating": 644, "matchups": [{"opponent": "drampa", "rating": 703, "opRating": 296}, {"opponent": "furret", "rating": 582}, {"opponent": "swampert_shadow", "rating": 554, "opRating": 445}, {"opponent": "quagsire_shadow", "rating": 511, "opRating": 488}, {"opponent": "flygon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 266}, {"opponent": "clodsire", "rating": 288}, {"opponent": "jumpluff_shadow", "rating": 316}, {"opponent": "gligar", "rating": 328}, {"opponent": "cradily", "rating": 409}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 30597}, {"moveId": "TACKLE", "uses": 21981}, {"moveId": "TAKE_DOWN", "uses": 5696}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26455}, {"moveId": "WILD_CHARGE", "uses": 18558}, {"moveId": "PAYBACK", "uses": 13292}]}, "moveset": ["DOUBLE_KICK", "BODY_SLAM", "PAYBACK"], "score": 81.3}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "Mamoswine", "rating": 576, "matchups": [{"opponent": "clodsire", "rating": 855}, {"opponent": "gligar", "rating": 778}, {"opponent": "swampert_shadow", "rating": 778}, {"opponent": "jumpluff_shadow", "rating": 659}, {"opponent": "claydol", "rating": 561}], "counters": [{"opponent": "skeledirge", "rating": 118}, {"opponent": "furret", "rating": 228}, {"opponent": "magcargo", "rating": 230}, {"opponent": "talonflame", "rating": 396}, {"opponent": "cradily", "rating": 472}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 34063}, {"moveId": "MUD_SLAP", "uses": 24237}], "chargedMoves": [{"moveId": "AVALANCHE", "uses": 19382}, {"moveId": "ICICLE_SPEAR", "uses": 16238}, {"moveId": "STONE_EDGE", "uses": 6192}, {"moveId": "HIGH_HORSEPOWER", "uses": 6094}, {"moveId": "ANCIENT_POWER", "uses": 5551}, {"moveId": "BULLDOZE", "uses": 4962}]}, "moveset": ["POWDER_SNOW", "ICICLE_SPEAR", "HIGH_HORSEPOWER"], "score": 81.1}, {"speciesId": "arcanine", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 666, "matchups": [{"opponent": "abomasnow_shadow", "rating": 884, "opRating": 116}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 728, "opRating": 272}, {"opponent": "piloswine", "rating": 724, "opRating": 276}, {"opponent": "gliscor", "rating": 600, "opRating": 400}, {"opponent": "jumpluff_shadow", "rating": 516}], "counters": [{"opponent": "cradily", "rating": 340}, {"opponent": "clodsire", "rating": 343}, {"opponent": "talonflame", "rating": 377}, {"opponent": "gligar", "rating": 412}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 20967}, {"moveId": "SNARL", "uses": 18600}, {"moveId": "THUNDER_FANG", "uses": 11918}, {"moveId": "BITE", "uses": 6761}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 11572}, {"moveId": "WILD_CHARGE", "uses": 10945}, {"moveId": "FLAMETHROWER", "uses": 10091}, {"moveId": "CRUNCH", "uses": 9431}, {"moveId": "SCORCHING_SANDS", "uses": 8807}, {"moveId": "BULLDOZE", "uses": 4721}, {"moveId": "FIRE_BLAST", "uses": 2706}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "PSYCHIC_FANGS"], "score": 81}, {"speciesId": "linoone", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 606, "matchups": [{"opponent": "quagsire_shadow", "rating": 698, "opRating": 301}, {"opponent": "gligar", "rating": 623}, {"opponent": "swampert_shadow", "rating": 564, "opRating": 435}, {"opponent": "claydol", "rating": 545, "opRating": 454}, {"opponent": "flygon", "rating": 538, "opRating": 461}], "counters": [{"opponent": "talonflame", "rating": 344}, {"opponent": "furret", "rating": 390}, {"opponent": "clodsire", "rating": 408}, {"opponent": "jumpluff_shadow", "rating": 434}, {"opponent": "cradily", "rating": 472}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 21767}, {"moveId": "SAND_ATTACK", "uses": 20141}, {"moveId": "TACKLE", "uses": 16369}], "chargedMoves": [{"moveId": "SWIFT", "uses": 28161}, {"moveId": "GRASS_KNOT", "uses": 13776}, {"moveId": "DIG", "uses": 9973}, {"moveId": "THUNDER", "uses": 6361}]}, "moveset": ["SHADOW_CLAW", "SWIFT", "GRASS_KNOT"], "score": 81}, {"speciesId": "pyroar", "speciesName": "Pyroar", "rating": 633, "matchups": [{"opponent": "talonflame", "rating": 738}, {"opponent": "skeledirge", "rating": 714, "opRating": 285}, {"opponent": "ninetales_shadow", "rating": 607, "opRating": 392}, {"opponent": "flygon", "rating": 551, "opRating": 448}, {"opponent": "jumpluff_shadow", "rating": 503}], "counters": [{"opponent": "gligar", "rating": 248}, {"opponent": "furret", "rating": 253}, {"opponent": "magcargo", "rating": 277}, {"opponent": "clodsire", "rating": 377}, {"opponent": "cradily", "rating": 447}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 26109}, {"moveId": "EMBER", "uses": 16479}, {"moveId": "FIRE_FANG", "uses": 12958}, {"moveId": "TAKE_DOWN", "uses": 2714}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 19498}, {"moveId": "DARK_PULSE", "uses": 15366}, {"moveId": "OVERHEAT", "uses": 15011}, {"moveId": "SOLAR_BEAM", "uses": 8502}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "DARK_PULSE"], "score": 80.7}, {"speciesId": "furfrou", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 590, "matchups": [{"opponent": "gliscor", "rating": 794, "opRating": 205}, {"opponent": "claydol", "rating": 727, "opRating": 272}, {"opponent": "gligar", "rating": 645}, {"opponent": "swampert_shadow", "rating": 541, "opRating": 458}, {"opponent": "flygon", "rating": 518, "opRating": 481}], "counters": [{"opponent": "talonflame", "rating": 292}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "cradily", "rating": 385}, {"opponent": "clodsire", "rating": 459}, {"opponent": "magcargo", "rating": 491}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 24604}, {"moveId": "SAND_ATTACK", "uses": 19983}, {"moveId": "BITE", "uses": 9352}, {"moveId": "TAKE_DOWN", "uses": 4346}], "chargedMoves": [{"moveId": "SURF", "uses": 25557}, {"moveId": "DARK_PULSE", "uses": 16938}, {"moveId": "GRASS_KNOT", "uses": 15766}]}, "moveset": ["SUCKER_PUNCH", "SURF", "GRASS_KNOT"], "score": 80.5}, {"speciesId": "munchlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 627, "matchups": [{"opponent": "drampa", "rating": 711, "opRating": 288}, {"opponent": "flygon", "rating": 676, "opRating": 323}, {"opponent": "flygon_shadow", "rating": 646, "opRating": 353}, {"opponent": "typhlosion_shadow", "rating": 610, "opRating": 389}, {"opponent": "swampert_shadow", "rating": 605, "opRating": 394}], "counters": [{"opponent": "cradily", "rating": 277}, {"opponent": "jumpluff_shadow", "rating": 320}, {"opponent": "gligar", "rating": 377}, {"opponent": "talonflame", "rating": 414}, {"opponent": "furret", "rating": 453}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 33384}, {"moveId": "LICK", "uses": 24916}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 35731}, {"moveId": "BULLDOZE", "uses": 13403}, {"moveId": "GUNK_SHOT", "uses": 9161}]}, "moveset": ["TACKLE", "BODY_SLAM", "BULLDOZE"], "score": 80.5}, {"speciesId": "oinkologne_female", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Female)", "rating": 614, "matchups": [{"opponent": "drampa", "rating": 697, "opRating": 302}, {"opponent": "piloswine", "rating": 634, "opRating": 365}, {"opponent": "quagsire_shadow", "rating": 607, "opRating": 392}, {"opponent": "swampert_shadow", "rating": 553, "opRating": 446}, {"opponent": "flygon", "rating": 538, "opRating": 461}], "counters": [{"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "talonflame", "rating": 422}, {"opponent": "cradily", "rating": 444}, {"opponent": "furret", "rating": 478}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 46367}, {"moveId": "TAKE_DOWN", "uses": 11933}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28518}, {"moveId": "TRAILBLAZE", "uses": 18382}, {"moveId": "DIG", "uses": 11412}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 80.5}, {"speciesId": "marshtomp", "speciesName": "Marshtom<PERSON>", "rating": 571, "matchups": [{"opponent": "skeledirge", "rating": 839, "opRating": 160}, {"opponent": "ninetales_shadow", "rating": 817, "opRating": 182}, {"opponent": "magcargo", "rating": 717, "opRating": 282}, {"opponent": "clodsire", "rating": 625, "opRating": 375}, {"opponent": "diggersby", "rating": 560, "opRating": 439}], "counters": [{"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "cradily", "rating": 239}, {"opponent": "furret", "rating": 309}, {"opponent": "gligar", "rating": 416}, {"opponent": "talonflame", "rating": 496}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 30229}, {"moveId": "WATER_GUN", "uses": 28071}], "chargedMoves": [{"moveId": "SURF", "uses": 21236}, {"moveId": "SLUDGE", "uses": 16266}, {"moveId": "MUD_BOMB", "uses": 15874}, {"moveId": "RETURN", "uses": 4926}]}, "moveset": ["MUD_SHOT", "MUD_BOMB", "SURF"], "score": 80.4}, {"speciesId": "parasect", "speciesName": "Parasect", "rating": 629, "matchups": [{"opponent": "swampert_shadow", "rating": 914, "opRating": 85}, {"opponent": "claydol", "rating": 865, "opRating": 134}, {"opponent": "quagsire_shadow", "rating": 780, "opRating": 219}, {"opponent": "cradily", "rating": 703}, {"opponent": "flygon", "rating": 682, "opRating": 317}], "counters": [{"opponent": "clodsire", "rating": 206}, {"opponent": "talonflame", "rating": 237}, {"opponent": "gligar", "rating": 263}, {"opponent": "jumpluff_shadow", "rating": 343}, {"opponent": "furret", "rating": 378}], "moves": {"fastMoves": [{"moveId": "FURY_CUTTER", "uses": 28546}, {"moveId": "BUG_BITE", "uses": 20553}, {"moveId": "STRUGGLE_BUG", "uses": 9172}], "chargedMoves": [{"moveId": "X_SCISSOR", "uses": 25470}, {"moveId": "CROSS_POISON", "uses": 21745}, {"moveId": "SOLAR_BEAM", "uses": 11115}]}, "moveset": ["FURY_CUTTER", "X_SCISSOR", "CROSS_POISON"], "score": 80.4}, {"speciesId": "staravia", "speciesName": "Staravia", "rating": 632, "matchups": [{"opponent": "quagsire_shadow", "rating": 681, "opRating": 318}, {"opponent": "gligar", "rating": 614}, {"opponent": "talonflame", "rating": 607}, {"opponent": "flygon", "rating": 551, "opRating": 448}, {"opponent": "swampert_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "magcargo", "rating": 264}, {"opponent": "cradily", "rating": 274}, {"opponent": "clodsire", "rating": 312}, {"opponent": "furret", "rating": 437}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 21866}, {"moveId": "SAND_ATTACK", "uses": 18400}, {"moveId": "WING_ATTACK", "uses": 18072}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 20089}, {"moveId": "FLY", "uses": 17122}, {"moveId": "AERIAL_ACE", "uses": 13039}, {"moveId": "RETURN", "uses": 5929}, {"moveId": "HEAT_WAVE", "uses": 2077}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 80.4}, {"speciesId": "castform_sunny", "speciesName": "Castform (Sunny)", "rating": 656, "matchups": [{"opponent": "piloswine", "rating": 893, "opRating": 106}, {"opponent": "ninetales_shadow", "rating": 671, "opRating": 328}, {"opponent": "jumpluff_shadow", "rating": 630}, {"opponent": "gligar", "rating": 513}, {"opponent": "diggersby", "rating": 506, "opRating": 493}], "counters": [{"opponent": "magcargo", "rating": 123}, {"opponent": "talonflame", "rating": 266}, {"opponent": "clodsire", "rating": 288}, {"opponent": "cradily", "rating": 319}, {"opponent": "furret", "rating": 428}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 37977}, {"moveId": "TACKLE", "uses": 20323}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 42864}, {"moveId": "SOLAR_BEAM", "uses": 10395}, {"moveId": "FIRE_BLAST", "uses": 4985}]}, "moveset": ["EMBER", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 80.2}, {"speciesId": "serperior_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 616, "matchups": [{"opponent": "swampert_shadow", "rating": 892, "opRating": 107}, {"opponent": "claydol", "rating": 834, "opRating": 165}, {"opponent": "flygon", "rating": 700, "opRating": 300}, {"opponent": "diggersby", "rating": 646, "opRating": 353}, {"opponent": "furret", "rating": 534}], "counters": [{"opponent": "gligar", "rating": 171}, {"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "cradily", "rating": 354}, {"opponent": "talonflame", "rating": 385}, {"opponent": "clodsire", "rating": 403}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45777}, {"moveId": "IRON_TAIL", "uses": 12523}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 22849}, {"moveId": "AERIAL_ACE", "uses": 17529}, {"moveId": "LEAF_TORNADO", "uses": 11817}, {"moveId": "GRASS_KNOT", "uses": 6188}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 80.2}, {"speciesId": "stunfisk", "speciesName": "Stunfisk", "rating": 557, "matchups": [{"opponent": "magcargo", "rating": 793, "opRating": 206}, {"opponent": "pidgeot", "rating": 773, "opRating": 226}, {"opponent": "jumpluff_shadow", "rating": 622}, {"opponent": "talonflame", "rating": 601}, {"opponent": "clodsire", "rating": 587}], "counters": [{"opponent": "gligar", "rating": 286}, {"opponent": "swampert_shadow", "rating": 297}, {"opponent": "diggersby", "rating": 347}, {"opponent": "cradily", "rating": 364}, {"opponent": "furret", "rating": 490}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 30903}, {"moveId": "MUD_SHOT", "uses": 27397}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 25820}, {"moveId": "MUDDY_WATER", "uses": 16786}, {"moveId": "DISCHARGE", "uses": 15684}]}, "moveset": ["THUNDER_SHOCK", "MUD_BOMB", "DISCHARGE"], "score": 80.1}, {"speciesId": "stunfisk_galarian", "speciesName": "Stunfisk (Galarian)", "rating": 571, "matchups": [{"opponent": "drampa", "rating": 668, "opRating": 331}, {"opponent": "clodsire", "rating": 645}, {"opponent": "magcargo", "rating": 613}, {"opponent": "cradily", "rating": 598}, {"opponent": "jumpluff_shadow", "rating": 572}], "counters": [{"opponent": "swampert_shadow", "rating": 150}, {"opponent": "gligar", "rating": 301}, {"opponent": "diggersby", "rating": 339}, {"opponent": "talonflame", "rating": 455}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31134}, {"moveId": "METAL_CLAW", "uses": 27166}], "chargedMoves": [{"moveId": "MUDDY_WATER", "uses": 17458}, {"moveId": "ROCK_SLIDE", "uses": 17312}, {"moveId": "EARTHQUAKE", "uses": 13540}, {"moveId": "FLASH_CANNON", "uses": 10039}]}, "moveset": ["MUD_SHOT", "ROCK_SLIDE", "EARTHQUAKE"], "score": 80}, {"speciesId": "girafarig", "speciesName": "Girafarig", "rating": 608, "matchups": [{"opponent": "clodsire", "rating": 857, "opRating": 142}, {"opponent": "pidgeot", "rating": 650, "opRating": 350}, {"opponent": "talonflame", "rating": 630}, {"opponent": "skeledirge", "rating": 592, "opRating": 407}, {"opponent": "quagsire_shadow", "rating": 565, "opRating": 434}], "counters": [{"opponent": "gligar", "rating": 248}, {"opponent": "furret", "rating": 265}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "cradily", "rating": 354}, {"opponent": "magcargo", "rating": 388}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 22541}, {"moveId": "CONFUSION", "uses": 21188}, {"moveId": "TACKLE", "uses": 14579}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 22320}, {"moveId": "TRAILBLAZE", "uses": 14466}, {"moveId": "RETURN", "uses": 8291}, {"moveId": "THUNDERBOLT", "uses": 6305}, {"moveId": "PSYCHIC", "uses": 4317}, {"moveId": "MIRROR_COAT", "uses": 2593}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 79.8}, {"speciesId": "serperior", "speciesName": "Serperior", "rating": 619, "matchups": [{"opponent": "quagsire_shadow", "rating": 919, "opRating": 80}, {"opponent": "claydol", "rating": 823, "opRating": 176}, {"opponent": "diggersby", "rating": 661, "opRating": 338}, {"opponent": "swampert_shadow", "rating": 588, "opRating": 411}, {"opponent": "clodsire", "rating": 565, "opRating": 434}], "counters": [{"opponent": "talonflame", "rating": 214}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "cradily", "rating": 375}, {"opponent": "gligar", "rating": 381}, {"opponent": "furret", "rating": 493}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45515}, {"moveId": "IRON_TAIL", "uses": 12785}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 20906}, {"moveId": "AERIAL_ACE", "uses": 15391}, {"moveId": "LEAF_TORNADO", "uses": 10807}, {"moveId": "RETURN", "uses": 5665}, {"moveId": "GRASS_KNOT", "uses": 5501}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "AERIAL_ACE"], "score": 79.7}, {"speciesId": "raticate_alolan", "speciesName": "Raticate (Alolan)", "rating": 611, "matchups": [{"opponent": "furret", "rating": 706}, {"opponent": "quagsire_shadow", "rating": 586, "opRating": 413}, {"opponent": "swampert_shadow", "rating": 573, "opRating": 426}, {"opponent": "claydol", "rating": 563, "opRating": 436}, {"opponent": "drampa", "rating": 523, "opRating": 476}], "counters": [{"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 332}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "talonflame", "rating": 374}, {"opponent": "clodsire", "rating": 403}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 36034}, {"moveId": "BITE", "uses": 22266}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28577}, {"moveId": "HYPER_FANG", "uses": 18549}, {"moveId": "RETURN", "uses": 6391}, {"moveId": "HYPER_BEAM", "uses": 4820}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "RETURN"], "score": 79.4}, {"speciesId": "steelix", "speciesName": "Steelix", "rating": 562, "matchups": [{"opponent": "drampa", "rating": 662, "opRating": 337}, {"opponent": "talonflame", "rating": 658}, {"opponent": "cradily", "rating": 575}, {"opponent": "jumpluff_shadow", "rating": 559}, {"opponent": "flygon", "rating": 547, "opRating": 452}], "counters": [{"opponent": "diggersby", "rating": 321}, {"opponent": "magcargo", "rating": 346}, {"opponent": "gligar", "rating": 404}, {"opponent": "furret", "rating": 415}, {"opponent": "clodsire", "rating": 449}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 26994}, {"moveId": "THUNDER_FANG", "uses": 18095}, {"moveId": "IRON_TAIL", "uses": 13276}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 15692}, {"moveId": "PSYCHIC_FANGS", "uses": 12430}, {"moveId": "CRUNCH", "uses": 10157}, {"moveId": "EARTHQUAKE", "uses": 7889}, {"moveId": "HEAVY_SLAM", "uses": 7642}, {"moveId": "RETURN", "uses": 4551}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 79.2}, {"speciesId": "vigoroth", "speciesName": "Vigoroth", "rating": 604, "matchups": [{"opponent": "piloswine", "rating": 789, "opRating": 210}, {"opponent": "drampa", "rating": 679, "opRating": 320}, {"opponent": "furret", "rating": 586}, {"opponent": "pidgeot", "rating": 558, "opRating": 441}, {"opponent": "magcargo", "rating": 510, "opRating": 489}], "counters": [{"opponent": "gligar", "rating": 316}, {"opponent": "talonflame", "rating": 333}, {"opponent": "jumpluff_shadow", "rating": 349}, {"opponent": "clodsire", "rating": 358}, {"opponent": "cradily", "rating": 482}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 36239}, {"moveId": "SCRATCH", "uses": 22061}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 20293}, {"moveId": "ROCK_SLIDE", "uses": 12865}, {"moveId": "BRICK_BREAK", "uses": 12505}, {"moveId": "BULLDOZE", "uses": 7372}, {"moveId": "RETURN", "uses": 5333}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 79.2}, {"speciesId": "wigglytuff", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating": 606, "matchups": [{"opponent": "flygon", "rating": 903, "opRating": 96}, {"opponent": "drampa", "rating": 883, "opRating": 116}, {"opponent": "gligar", "rating": 524}, {"opponent": "diggersby", "rating": 513, "opRating": 486}, {"opponent": "furret", "rating": 511}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "magcargo", "rating": 252}, {"opponent": "cradily", "rating": 288}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "clodsire", "rating": 394}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 26991}, {"moveId": "FEINT_ATTACK", "uses": 26940}, {"moveId": "POUND", "uses": 4369}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17931}, {"moveId": "ICY_WIND", "uses": 15898}, {"moveId": "DISARMING_VOICE", "uses": 10431}, {"moveId": "ICE_BEAM", "uses": 5109}, {"moveId": "DAZZLING_GLEAM", "uses": 3859}, {"moveId": "PLAY_ROUGH", "uses": 2721}, {"moveId": "HYPER_BEAM", "uses": 2488}]}, "moveset": ["CHARM", "ICY_WIND", "SWIFT"], "score": 79.2}, {"speciesId": "crocalor", "speciesName": "Crocalor", "rating": 641, "matchups": [{"opponent": "piloswine", "rating": 750, "opRating": 250}, {"opponent": "gliscor", "rating": 668, "opRating": 331}, {"opponent": "furret", "rating": 614}, {"opponent": "jumpluff_shadow", "rating": 601}, {"opponent": "pidgeot", "rating": 581, "opRating": 418}], "counters": [{"opponent": "magcargo", "rating": 179}, {"opponent": "clodsire", "rating": 264}, {"opponent": "talonflame", "rating": 318}, {"opponent": "cradily", "rating": 392}, {"opponent": "gligar", "rating": 473}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 44275}, {"moveId": "BITE", "uses": 14025}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21905}, {"moveId": "FLAMETHROWER", "uses": 19730}, {"moveId": "DISARMING_VOICE", "uses": 16779}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 79.1}, {"speciesId": "lopunny", "speciesName": "Lopunny", "rating": 596, "matchups": [{"opponent": "drampa", "rating": 693, "opRating": 306}, {"opponent": "diggersby", "rating": 645, "opRating": 354}, {"opponent": "cradily", "rating": 629}, {"opponent": "flygon", "rating": 532, "opRating": 467}, {"opponent": "furret", "rating": 516}], "counters": [{"opponent": "talonflame", "rating": 270}, {"opponent": "magcargo", "rating": 307}, {"opponent": "jumpluff_shadow", "rating": 395}, {"opponent": "gligar", "rating": 435}, {"opponent": "clodsire", "rating": 442}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 47293}, {"moveId": "LOW_KICK", "uses": 7245}, {"moveId": "POUND", "uses": 3761}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 24124}, {"moveId": "FIRE_PUNCH", "uses": 15382}, {"moveId": "FOCUS_BLAST", "uses": 10832}, {"moveId": "HYPER_BEAM", "uses": 7925}]}, "moveset": ["DOUBLE_KICK", "TRIPLE_AXEL", "FOCUS_BLAST"], "score": 78.9}, {"speciesId": "noctowl", "speciesName": "Noctowl", "rating": 630, "matchups": [{"opponent": "swampert_shadow", "rating": 684, "opRating": 315}, {"opponent": "jumpluff_shadow", "rating": 625}, {"opponent": "quagsire_shadow", "rating": 550, "opRating": 449}, {"opponent": "flygon", "rating": 544, "opRating": 455}, {"opponent": "gliscor", "rating": 508, "opRating": 491}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "cradily", "rating": 298}, {"opponent": "clodsire", "rating": 326}, {"opponent": "gligar", "rating": 431}, {"opponent": "furret", "rating": 437}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34391}, {"moveId": "EXTRASENSORY", "uses": 23909}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 24297}, {"moveId": "NIGHT_SHADE", "uses": 15317}, {"moveId": "PSYCHIC", "uses": 11667}, {"moveId": "SHADOW_BALL", "uses": 6925}]}, "moveset": ["WING_ATTACK", "SKY_ATTACK", "NIGHT_SHADE"], "score": 78.9}, {"speciesId": "vigoroth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 605, "matchups": [{"opponent": "furret", "rating": 689}, {"opponent": "drampa", "rating": 668, "opRating": 331}, {"opponent": "diggersby", "rating": 651, "opRating": 348}, {"opponent": "pidgeot", "rating": 513, "opRating": 486}, {"opponent": "cradily", "rating": 503}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "clodsire", "rating": 257}, {"opponent": "gligar", "rating": 347}, {"opponent": "talonflame", "rating": 407}, {"opponent": "magcargo", "rating": 457}], "moves": {"fastMoves": [{"moveId": "COUNTER", "uses": 37152}, {"moveId": "SCRATCH", "uses": 21148}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 22595}, {"moveId": "ROCK_SLIDE", "uses": 13984}, {"moveId": "BRICK_BREAK", "uses": 13694}, {"moveId": "BULLDOZE", "uses": 7971}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BODY_SLAM", "ROCK_SLIDE"], "score": 78.8}, {"speciesId": "girafarig_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 591, "matchups": [{"opponent": "clodsire", "rating": 811, "opRating": 188}, {"opponent": "pidgeot", "rating": 580, "opRating": 419}, {"opponent": "talonflame", "rating": 553}, {"opponent": "quagsire_shadow", "rating": 523, "opRating": 476}, {"opponent": "skeledirge", "rating": 515, "opRating": 484}], "counters": [{"opponent": "jumpluff_shadow", "rating": 274}, {"opponent": "gligar", "rating": 293}, {"opponent": "furret", "rating": 331}, {"opponent": "cradily", "rating": 409}, {"opponent": "magcargo", "rating": 461}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 22381}, {"moveId": "CONFUSION", "uses": 21461}, {"moveId": "TACKLE", "uses": 14443}], "chargedMoves": [{"moveId": "PSYCHIC_FANGS", "uses": 26036}, {"moveId": "TRAILBLAZE", "uses": 16594}, {"moveId": "THUNDERBOLT", "uses": 7519}, {"moveId": "PSYCHIC", "uses": 4964}, {"moveId": "MIRROR_COAT", "uses": 3102}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["CONFUSION", "PSYCHIC_FANGS", "TRAILBLAZE"], "score": 78.5}, {"speciesId": "golem", "speciesName": "Golem", "rating": 552, "matchups": [{"opponent": "magcargo", "rating": 880, "opRating": 119}, {"opponent": "typhlosion_shadow", "rating": 829, "opRating": 170}, {"opponent": "talonflame", "rating": 824}, {"opponent": "skeledirge", "rating": 824, "opRating": 175}, {"opponent": "pidgeot", "rating": 666, "opRating": 333}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "cradily", "rating": 329}, {"opponent": "clodsire", "rating": 411}, {"opponent": "furret", "rating": 450}, {"opponent": "jumpluff_shadow", "rating": 493}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23367}, {"moveId": "MUD_SHOT", "uses": 18657}, {"moveId": "ROCK_THROW", "uses": 16338}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 17391}, {"moveId": "ROCK_BLAST", "uses": 15625}, {"moveId": "EARTHQUAKE", "uses": 13234}, {"moveId": "ANCIENT_POWER", "uses": 11992}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "ROCK_BLAST"], "score": 78.2}, {"speciesId": "staravia_shadow", "speciesName": "Staravia (Shadow)", "rating": 579, "matchups": [{"opponent": "claydol", "rating": 829, "opRating": 170}, {"opponent": "clodsire", "rating": 803}, {"opponent": "diggersby", "rating": 592, "opRating": 407}, {"opponent": "gligar", "rating": 577}, {"opponent": "talonflame", "rating": 533}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "furret", "rating": 190}, {"opponent": "swampert_shadow", "rating": 261}, {"opponent": "magcargo", "rating": 286}, {"opponent": "cradily", "rating": 458}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 22371}, {"moveId": "SAND_ATTACK", "uses": 18011}, {"moveId": "WING_ATTACK", "uses": 17946}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 22393}, {"moveId": "FLY", "uses": 19057}, {"moveId": "AERIAL_ACE", "uses": 14551}, {"moveId": "HEAT_WAVE", "uses": 2269}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "BRAVE_BIRD", "FLY"], "score": 78.2}, {"speciesId": "vibrava_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 583, "matchups": [{"opponent": "typhlosion_shadow", "rating": 759, "opRating": 240}, {"opponent": "talonflame", "rating": 700}, {"opponent": "skeledirge", "rating": 700, "opRating": 299}, {"opponent": "ninetales_shadow", "rating": 551, "opRating": 448}, {"opponent": "magcargo", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 261}, {"opponent": "furret", "rating": 334}, {"opponent": "cradily", "rating": 361}, {"opponent": "gligar", "rating": 385}, {"opponent": "clodsire", "rating": 401}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21899}, {"moveId": "SAND_ATTACK", "uses": 18927}, {"moveId": "MUD_SHOT", "uses": 17457}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22498}, {"moveId": "BUG_BUZZ", "uses": 16402}, {"moveId": "BULLDOZE", "uses": 11914}, {"moveId": "SAND_TOMB", "uses": 7416}, {"moveId": "FRUSTRATION", "uses": 42}]}, "moveset": ["DRAGON_BREATH", "SAND_TOMB", "BUG_BUZZ"], "score": 78.2}, {"speciesId": "magby", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 639, "matchups": [{"opponent": "piloswine", "rating": 845, "opRating": 154}, {"opponent": "abomasnow_shadow", "rating": 845, "opRating": 154}, {"opponent": "zangoose", "rating": 837, "opRating": 162}, {"opponent": "typhlosion_shadow", "rating": 712, "opRating": 287}, {"opponent": "drampa", "rating": 600, "opRating": 400}], "counters": [{"opponent": "gligar", "rating": 259}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "furret", "rating": 400}, {"opponent": "cradily", "rating": 402}, {"opponent": "talonflame", "rating": 407}], "moves": {"fastMoves": [{"moveId": "KARATE_CHOP", "uses": 30886}, {"moveId": "EMBER", "uses": 27414}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 26466}, {"moveId": "BRICK_BREAK", "uses": 18468}, {"moveId": "FLAMETHROWER", "uses": 8302}, {"moveId": "FLAME_BURST", "uses": 5025}]}, "moveset": ["KARATE_CHOP", "FIRE_PUNCH", "BRICK_BREAK"], "score": 78}, {"speciesId": "rapidash", "speciesName": "Rapidash", "rating": 617, "matchups": [{"opponent": "talonflame", "rating": 750}, {"opponent": "skeledirge", "rating": 723, "opRating": 276}, {"opponent": "ninetales_shadow", "rating": 714, "opRating": 285}, {"opponent": "gliscor", "rating": 571, "opRating": 428}, {"opponent": "pidgeot", "rating": 558, "opRating": 441}], "counters": [{"opponent": "gligar", "rating": 244}, {"opponent": "furret", "rating": 253}, {"opponent": "jumpluff_shadow", "rating": 343}, {"opponent": "clodsire", "rating": 375}, {"opponent": "cradily", "rating": 423}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 25175}, {"moveId": "EMBER", "uses": 16133}, {"moveId": "FIRE_SPIN", "uses": 14301}, {"moveId": "LOW_KICK", "uses": 2713}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 15245}, {"moveId": "FLAME_CHARGE", "uses": 14850}, {"moveId": "WILD_CHARGE", "uses": 13880}, {"moveId": "FIRE_BLAST", "uses": 6680}, {"moveId": "SCORCHING_SANDS", "uses": 5754}, {"moveId": "HEAT_WAVE", "uses": 2017}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "WILD_CHARGE"], "score": 78}, {"speciesId": "go<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 573, "matchups": [{"opponent": "magcargo", "rating": 741, "opRating": 258}, {"opponent": "clodsire", "rating": 719, "opRating": 280}, {"opponent": "skeledirge", "rating": 719, "opRating": 280}, {"opponent": "claydol", "rating": 627, "opRating": 372}, {"opponent": "ninetales_shadow", "rating": 606, "opRating": 393}], "counters": [{"opponent": "jumpluff_shadow", "rating": 225}, {"opponent": "furret", "rating": 246}, {"opponent": "gligar", "rating": 320}, {"opponent": "cradily", "rating": 427}, {"opponent": "talonflame", "rating": 444}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31157}, {"moveId": "ASTONISH", "uses": 27143}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 29206}, {"moveId": "BRICK_BREAK", "uses": 20422}, {"moveId": "NIGHT_SHADE", "uses": 8591}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "BRICK_BREAK"], "score": 77.9}, {"speciesId": "staraptor", "speciesName": "Staraptor", "rating": 629, "matchups": [{"opponent": "quagsire_shadow", "rating": 652, "opRating": 348}, {"opponent": "magcargo", "rating": 624, "opRating": 376}, {"opponent": "talonflame", "rating": 576}, {"opponent": "gliscor", "rating": 552, "opRating": 448}, {"opponent": "flygon", "rating": 516, "opRating": 484}], "counters": [{"opponent": "cradily", "rating": 114}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "furret", "rating": 190}, {"opponent": "clodsire", "rating": 324}, {"opponent": "gligar", "rating": 465}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 16953}, {"moveId": "SAND_ATTACK", "uses": 14095}, {"moveId": "GUST", "uses": 14087}, {"moveId": "WING_ATTACK", "uses": 13124}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 19967}, {"moveId": "CLOSE_COMBAT", "uses": 19500}, {"moveId": "FLY", "uses": 16991}, {"moveId": "HEAT_WAVE", "uses": 1921}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 77.9}, {"speciesId": "steelix_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 552, "matchups": [{"opponent": "drampa", "rating": 686, "opRating": 313}, {"opponent": "jumpluff_shadow", "rating": 615}, {"opponent": "talonflame", "rating": 599}, {"opponent": "furret", "rating": 579}, {"opponent": "cradily", "rating": 515}], "counters": [{"opponent": "diggersby", "rating": 272}, {"opponent": "swampert_shadow", "rating": 323}, {"opponent": "magcargo", "rating": 358}, {"opponent": "clodsire", "rating": 401}, {"opponent": "gligar", "rating": 458}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 27454}, {"moveId": "THUNDER_FANG", "uses": 17876}, {"moveId": "IRON_TAIL", "uses": 13047}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 17058}, {"moveId": "PSYCHIC_FANGS", "uses": 13440}, {"moveId": "CRUNCH", "uses": 10949}, {"moveId": "EARTHQUAKE", "uses": 8517}, {"moveId": "HEAVY_SLAM", "uses": 8288}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["THUNDER_FANG", "PSYCHIC_FANGS", "BREAKING_SWIPE"], "score": 77.9}, {"speciesId": "gra<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 614, "matchups": [{"opponent": "magmar_shadow", "rating": 800, "opRating": 200}, {"opponent": "magcargo", "rating": 773, "opRating": 226}, {"opponent": "skeledirge", "rating": 682, "opRating": 317}, {"opponent": "typhlosion_shadow", "rating": 682, "opRating": 317}, {"opponent": "talonflame", "rating": 604}], "counters": [{"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "furret", "rating": 271}, {"opponent": "clodsire", "rating": 382}, {"opponent": "cradily", "rating": 420}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 30721}, {"moveId": "MUD_SLAP", "uses": 27579}], "chargedMoves": [{"moveId": "POISON_FANG", "uses": 30551}, {"moveId": "SLUDGE_BOMB", "uses": 24986}, {"moveId": "ACID_SPRAY", "uses": 2915}]}, "moveset": ["MUD_SLAP", "POISON_FANG", "SLUDGE_BOMB"], "score": 77.7}, {"speciesId": "raticate_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Al<PERSON><PERSON>) (Shadow)", "rating": 600, "matchups": [{"opponent": "staravia", "rating": 796, "opRating": 203}, {"opponent": "clodsire", "rating": 636, "opRating": 363}, {"opponent": "diggersby", "rating": 583, "opRating": 416}, {"opponent": "swampert_shadow", "rating": 530, "opRating": 470}, {"opponent": "flygon", "rating": 523, "opRating": 476}], "counters": [{"opponent": "gligar", "rating": 187}, {"opponent": "jumpluff_shadow", "rating": 336}, {"opponent": "cradily", "rating": 406}, {"opponent": "furret", "rating": 446}, {"opponent": "talonflame", "rating": 459}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 36960}, {"moveId": "BITE", "uses": 21340}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31712}, {"moveId": "HYPER_FANG", "uses": 20894}, {"moveId": "HYPER_BEAM", "uses": 5419}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "CRUNCH", "HYPER_BEAM"], "score": 77.7}, {"speciesId": "braixen", "speciesName": "Braixen", "rating": 624, "matchups": [{"opponent": "abomasnow_shadow", "rating": 882, "opRating": 117}, {"opponent": "piloswine", "rating": 715, "opRating": 284}, {"opponent": "gliscor", "rating": 593, "opRating": 406}, {"opponent": "jumpluff_shadow", "rating": 524}, {"opponent": "furret", "rating": 512}], "counters": [{"opponent": "magcargo", "rating": 239}, {"opponent": "gligar", "rating": 263}, {"opponent": "clodsire", "rating": 367}, {"opponent": "talonflame", "rating": 377}, {"opponent": "cradily", "rating": 399}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43867}, {"moveId": "SCRATCH", "uses": 14433}], "chargedMoves": [{"moveId": "PSYSHOCK", "uses": 28594}, {"moveId": "FLAME_CHARGE", "uses": 20923}, {"moveId": "FLAMETHROWER", "uses": 8801}]}, "moveset": ["EMBER", "PSYSHOCK", "FLAME_CHARGE"], "score": 77.6}, {"speciesId": "chandelure_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 568, "matchups": [{"opponent": "piloswine", "rating": 822, "opRating": 177}, {"opponent": "cradily", "rating": 784}, {"opponent": "clodsire", "rating": 784, "opRating": 215}, {"opponent": "talonflame", "rating": 618}, {"opponent": "skeledirge", "rating": 618, "opRating": 381}], "counters": [{"opponent": "magcargo", "rating": 158}, {"opponent": "furret", "rating": 256}, {"opponent": "diggersby", "rating": 318}, {"opponent": "gligar", "rating": 328}, {"opponent": "jumpluff_shadow", "rating": 460}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 28260}, {"moveId": "HEX", "uses": 17096}, {"moveId": "FIRE_SPIN", "uses": 12961}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16637}, {"moveId": "SHADOW_BALL", "uses": 13606}, {"moveId": "OVERHEAT", "uses": 12836}, {"moveId": "ENERGY_BALL", "uses": 10794}, {"moveId": "POLTERGEIST", "uses": 4434}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "SHADOW_BALL"], "score": 77.6}, {"speciesId": "geodude_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 526, "matchups": [{"opponent": "talonflame", "rating": 893}, {"opponent": "skeledirge", "rating": 836, "opRating": 163}, {"opponent": "ninetales_shadow", "rating": 818, "opRating": 181}, {"opponent": "magcargo", "rating": 761, "opRating": 238}, {"opponent": "jumpluff_shadow", "rating": 619}], "counters": [{"opponent": "clodsire", "rating": 286}, {"opponent": "cradily", "rating": 298}, {"opponent": "gligar", "rating": 324}, {"opponent": "diggersby", "rating": 336}, {"opponent": "furret", "rating": 340}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32769}, {"moveId": "TACKLE", "uses": 25531}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 26256}, {"moveId": "ROCK_SLIDE", "uses": 16849}, {"moveId": "DIG", "uses": 15152}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 77.4}, {"speciesId": "lickitung", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 583, "matchups": [{"opponent": "swampert_shadow", "rating": 650, "opRating": 349}, {"opponent": "quagsire_shadow", "rating": 604, "opRating": 395}, {"opponent": "gligar", "rating": 588}, {"opponent": "claydol", "rating": 580, "opRating": 419}, {"opponent": "skeledirge", "rating": 510, "opRating": 489}], "counters": [{"opponent": "furret", "rating": 287}, {"opponent": "clodsire", "rating": 324}, {"opponent": "cradily", "rating": 326}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "talonflame", "rating": 370}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 44830}, {"moveId": "ZEN_HEADBUTT", "uses": 13470}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 19545}, {"moveId": "WRAP", "uses": 15997}, {"moveId": "POWER_WHIP", "uses": 12630}, {"moveId": "STOMP", "uses": 6951}, {"moveId": "HYPER_BEAM", "uses": 3125}]}, "moveset": ["LICK", "BODY_SLAM", "POWER_WHIP"], "score": 77.4}, {"speciesId": "darmanitan_standard", "speciesName": "Dar<PERSON><PERSON> (Standard)", "rating": 567, "matchups": [{"opponent": "clodsire", "rating": 760, "opRating": 239}, {"opponent": "cradily", "rating": 713}, {"opponent": "talonflame", "rating": 688}, {"opponent": "skeledirge", "rating": 688, "opRating": 311}, {"opponent": "gliscor", "rating": 565, "opRating": 434}], "counters": [{"opponent": "diggersby", "rating": 247}, {"opponent": "gligar", "rating": 263}, {"opponent": "furret", "rating": 290}, {"opponent": "magcargo", "rating": 346}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32587}, {"moveId": "FIRE_FANG", "uses": 15682}, {"moveId": "TACKLE", "uses": 10021}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17743}, {"moveId": "ROCK_SLIDE", "uses": 17377}, {"moveId": "FOCUS_BLAST", "uses": 13298}, {"moveId": "PSYCHIC", "uses": 9883}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 77.3}, {"speciesId": "gogoat", "speciesName": "Gogoat", "rating": 575, "matchups": [{"opponent": "swampert_shadow", "rating": 887, "opRating": 112}, {"opponent": "claydol", "rating": 784, "opRating": 215}, {"opponent": "diggersby", "rating": 636, "opRating": 363}, {"opponent": "furret", "rating": 539}, {"opponent": "clodsire", "rating": 509}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "talonflame", "rating": 244}, {"opponent": "magcargo", "rating": 307}, {"opponent": "cradily", "rating": 326}, {"opponent": "gligar", "rating": 343}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 38701}, {"moveId": "ROCK_SMASH", "uses": 12455}, {"moveId": "ZEN_HEADBUTT", "uses": 7138}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 37625}, {"moveId": "BRICK_BREAK", "uses": 14753}, {"moveId": "SEED_BOMB", "uses": 5874}]}, "moveset": ["VINE_WHIP", "LEAF_BLADE", "BRICK_BREAK"], "score": 77.3}, {"speciesId": "darmanitan_standard_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Standard) (Shadow)", "rating": 562, "matchups": [{"opponent": "abomasnow_shadow", "rating": 891, "opRating": 108}, {"opponent": "clodsire", "rating": 760, "opRating": 239}, {"opponent": "cradily", "rating": 666}, {"opponent": "talonflame", "rating": 634}, {"opponent": "skeledirge", "rating": 634, "opRating": 365}], "counters": [{"opponent": "diggersby", "rating": 290}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 340}, {"opponent": "magcargo", "rating": 427}, {"opponent": "jumpluff_shadow", "rating": 460}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34341}, {"moveId": "FIRE_FANG", "uses": 14731}, {"moveId": "TACKLE", "uses": 9225}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 17770}, {"moveId": "ROCK_SLIDE", "uses": 17345}, {"moveId": "FOCUS_BLAST", "uses": 13319}, {"moveId": "PSYCHIC", "uses": 9841}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "OVERHEAT", "ROCK_SLIDE"], "score": 77}, {"speciesId": "chesnaught", "speciesName": "Chesnaught", "rating": 570, "matchups": [{"opponent": "swampert_shadow", "rating": 887}, {"opponent": "furret", "rating": 717}, {"opponent": "diggersby", "rating": 669}, {"opponent": "clodsire", "rating": 596}, {"opponent": "magcargo", "rating": 580}], "counters": [{"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "skeledirge", "rating": 133}, {"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 214}, {"opponent": "cradily", "rating": 388}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 35305}, {"moveId": "SMACK_DOWN", "uses": 16659}, {"moveId": "LOW_KICK", "uses": 6323}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21110}, {"moveId": "FRENZY_PLANT", "uses": 19153}, {"moveId": "THUNDER_PUNCH", "uses": 8817}, {"moveId": "ENERGY_BALL", "uses": 3644}, {"moveId": "GYRO_BALL", "uses": 3501}, {"moveId": "SOLAR_BEAM", "uses": 2199}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SUPER_POWER"], "score": 76.8}, {"speciesId": "hippopotas", "speciesName": "Hippopotas", "rating": 574, "matchups": [{"opponent": "magcargo", "rating": 753, "opRating": 246}, {"opponent": "drampa", "rating": 641, "opRating": 358}, {"opponent": "clodsire", "rating": 631}, {"opponent": "cradily", "rating": 615}, {"opponent": "ninetales_shadow", "rating": 595, "opRating": 404}], "counters": [{"opponent": "talonflame", "rating": 96}, {"opponent": "diggersby", "rating": 333}, {"opponent": "jumpluff_shadow", "rating": 359}, {"opponent": "furret", "rating": 381}, {"opponent": "gligar", "rating": 480}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 27555}, {"moveId": "TACKLE", "uses": 17375}, {"moveId": "BITE", "uses": 13324}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21524}, {"moveId": "BODY_SLAM", "uses": 17549}, {"moveId": "DIG", "uses": 14926}, {"moveId": "RETURN", "uses": 4283}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 76.8}, {"speciesId": "aipom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 577, "matchups": [{"opponent": "talonflame", "rating": 670}, {"opponent": "skeledirge", "rating": 670, "opRating": 329}, {"opponent": "flygon", "rating": 607, "opRating": 392}, {"opponent": "quagsire_shadow", "rating": 570, "opRating": 429}, {"opponent": "gliscor", "rating": 522, "opRating": 477}], "counters": [{"opponent": "furret", "rating": 318}, {"opponent": "cradily", "rating": 340}, {"opponent": "clodsire", "rating": 348}, {"opponent": "jumpluff_shadow", "rating": 369}, {"opponent": "gligar", "rating": 416}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35610}, {"moveId": "SCRATCH", "uses": 22690}], "chargedMoves": [{"moveId": "SWIFT", "uses": 29354}, {"moveId": "AERIAL_ACE", "uses": 18489}, {"moveId": "LOW_SWEEP", "uses": 10415}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "SWIFT", "AERIAL_ACE"], "score": 76.7}, {"speciesId": "centiskorch", "speciesName": "Centiskorch", "rating": 626, "matchups": [{"opponent": "abomasnow_shadow", "rating": 886, "opRating": 113}, {"opponent": "sceptile_shadow", "rating": 863, "opRating": 136}, {"opponent": "piloswine", "rating": 852, "opRating": 147}, {"opponent": "cradily_shadow", "rating": 779, "opRating": 220}, {"opponent": "furret", "rating": 529}], "counters": [{"opponent": "clodsire", "rating": 307}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "talonflame", "rating": 385}, {"opponent": "cradily", "rating": 444}, {"opponent": "gligar", "rating": 446}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30470}, {"moveId": "BUG_BITE", "uses": 27830}], "chargedMoves": [{"moveId": "LUNGE", "uses": 20693}, {"moveId": "CRUNCH", "uses": 18749}, {"moveId": "BUG_BUZZ", "uses": 13428}, {"moveId": "HEAT_WAVE", "uses": 5407}]}, "moveset": ["EMBER", "LUNGE", "CRUNCH"], "score": 76.5}, {"speciesId": "rhy<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "magcargo", "rating": 883, "opRating": 116}, {"opponent": "typhlosion_shadow", "rating": 836, "opRating": 163}, {"opponent": "skeledirge", "rating": 833, "opRating": 166}, {"opponent": "ninetales_shadow", "rating": 808, "opRating": 191}, {"opponent": "clodsire", "rating": 783, "opRating": 216}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "jumpluff_shadow", "rating": 222}, {"opponent": "furret", "rating": 303}, {"opponent": "cradily", "rating": 322}, {"opponent": "talonflame", "rating": 429}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 42558}, {"moveId": "ROCK_SMASH", "uses": 15742}], "chargedMoves": [{"moveId": "STOMP", "uses": 20377}, {"moveId": "BULLDOZE", "uses": 19298}, {"moveId": "HORN_ATTACK", "uses": 18580}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "BULLDOZE", "STOMP"], "score": 76.5}, {"speciesId": "blazi<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 573, "matchups": [{"opponent": "piloswine", "rating": 861, "opRating": 138}, {"opponent": "furret", "rating": 806}, {"opponent": "magcargo", "rating": 806, "opRating": 193}, {"opponent": "cradily", "rating": 693}, {"opponent": "drampa", "rating": 693, "opRating": 306}], "counters": [{"opponent": "clodsire", "rating": 189}, {"opponent": "gligar", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 199}, {"opponent": "talonflame", "rating": 292}, {"opponent": "diggersby", "rating": 485}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31061}, {"moveId": "COUNTER", "uses": 27239}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14697}, {"moveId": "BRAVE_BIRD", "uses": 12602}, {"moveId": "BLAZE_KICK", "uses": 10786}, {"moveId": "STONE_EDGE", "uses": 8785}, {"moveId": "FOCUS_BLAST", "uses": 8276}, {"moveId": "OVERHEAT", "uses": 3084}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["COUNTER", "BLAZE_KICK", "STONE_EDGE"], "score": 76.4}, {"speciesId": "castform", "speciesName": "Castform", "rating": 574, "matchups": [{"opponent": "quagsire_shadow", "rating": 708, "opRating": 291}, {"opponent": "armarouge", "rating": 688, "opRating": 311}, {"opponent": "piloswine", "rating": 657, "opRating": 342}, {"opponent": "typhlosion_shadow", "rating": 636, "opRating": 363}, {"opponent": "swampert_shadow", "rating": 599, "opRating": 400}], "counters": [{"opponent": "furret", "rating": 262}, {"opponent": "cradily", "rating": 368}, {"opponent": "gligar", "rating": 454}, {"opponent": "jumpluff_shadow", "rating": 460}, {"opponent": "talonflame", "rating": 492}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 33610}, {"moveId": "TACKLE", "uses": 24690}], "chargedMoves": [{"moveId": "WEATHER_BALL_NORMAL", "uses": 20797}, {"moveId": "WEATHER_BALL_ROCK", "uses": 20453}, {"moveId": "ENERGY_BALL", "uses": 9343}, {"moveId": "HURRICANE", "uses": 7665}]}, "moveset": ["HEX", "WEATHER_BALL_ROCK", "ENERGY_BALL"], "score": 76.4}, {"speciesId": "heatran_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 581, "matchups": [{"opponent": "gliscor", "rating": 743, "opRating": 256}, {"opponent": "pidgeot", "rating": 730, "opRating": 269}, {"opponent": "jumpluff_shadow", "rating": 610}, {"opponent": "cradily", "rating": 548}, {"opponent": "drampa", "rating": 504, "opRating": 495}], "counters": [{"opponent": "magcargo", "rating": 209}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 328}, {"opponent": "talonflame", "rating": 407}, {"opponent": "clodsire", "rating": 423}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 34254}, {"moveId": "BUG_BITE", "uses": 24046}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 19787}, {"moveId": "STONE_EDGE", "uses": 11694}, {"moveId": "EARTH_POWER", "uses": 9867}, {"moveId": "IRON_HEAD", "uses": 8682}, {"moveId": "FLAMETHROWER", "uses": 5410}, {"moveId": "FIRE_BLAST", "uses": 2853}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 76.2}, {"speciesId": "snor<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 598, "matchups": [{"opponent": "cradily_shadow", "rating": 788, "opRating": 211}, {"opponent": "quagsire_shadow", "rating": 628, "opRating": 371}, {"opponent": "piloswine", "rating": 609, "opRating": 390}, {"opponent": "cradily", "rating": 534}, {"opponent": "ninetales_shadow", "rating": 521, "opRating": 478}], "counters": [{"opponent": "jumpluff_shadow", "rating": 333}, {"opponent": "gligar", "rating": 374}, {"opponent": "furret", "rating": 434}, {"opponent": "talonflame", "rating": 451}, {"opponent": "clodsire", "rating": 483}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 46406}, {"moveId": "ZEN_HEADBUTT", "uses": 10070}, {"moveId": "YAWN", "uses": 1889}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16283}, {"moveId": "BODY_SLAM", "uses": 15950}, {"moveId": "OUTRAGE", "uses": 7579}, {"moveId": "EARTHQUAKE", "uses": 6157}, {"moveId": "HEAVY_SLAM", "uses": 5708}, {"moveId": "SKULL_BASH", "uses": 3912}, {"moveId": "HYPER_BEAM", "uses": 2621}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 76.1}, {"speciesId": "tauros_blaze", "speciesName": "<PERSON><PERSON> (Blaze)", "rating": 609, "matchups": [{"opponent": "piloswine", "rating": 710, "opRating": 289}, {"opponent": "abomasnow_shadow", "rating": 710, "opRating": 289}, {"opponent": "furret", "rating": 605}, {"opponent": "drampa", "rating": 574, "opRating": 425}, {"opponent": "magcargo", "rating": 543, "opRating": 456}], "counters": [{"opponent": "talonflame", "rating": 229}, {"opponent": "gligar", "rating": 332}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "clodsire", "rating": 391}, {"opponent": "cradily", "rating": 437}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 37834}, {"moveId": "TACKLE", "uses": 16289}, {"moveId": "ZEN_HEADBUTT", "uses": 4193}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21067}, {"moveId": "TRAILBLAZE", "uses": 17754}, {"moveId": "EARTHQUAKE", "uses": 10705}, {"moveId": "IRON_HEAD", "uses": 8785}]}, "moveset": ["DOUBLE_KICK", "FLAME_CHARGE", "TRAILBLAZE"], "score": 76.1}, {"speciesId": "heatran", "speciesName": "Heatran", "rating": 584, "matchups": [{"opponent": "gliscor", "rating": 756, "opRating": 243}, {"opponent": "pidgeot", "rating": 675, "opRating": 324}, {"opponent": "jumpluff_shadow", "rating": 653}, {"opponent": "cradily", "rating": 576}, {"opponent": "furret", "rating": 522}], "counters": [{"opponent": "magcargo", "rating": 166}, {"opponent": "gligar", "rating": 248}, {"opponent": "diggersby", "rating": 347}, {"opponent": "talonflame", "rating": 351}, {"opponent": "clodsire", "rating": 353}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 33431}, {"moveId": "BUG_BITE", "uses": 24869}], "chargedMoves": [{"moveId": "MAGMA_STORM", "uses": 19797}, {"moveId": "STONE_EDGE", "uses": 11690}, {"moveId": "EARTH_POWER", "uses": 9871}, {"moveId": "IRON_HEAD", "uses": 8674}, {"moveId": "FLAMETHROWER", "uses": 5401}, {"moveId": "FIRE_BLAST", "uses": 2843}]}, "moveset": ["FIRE_SPIN", "MAGMA_STORM", "STONE_EDGE"], "score": 75.9}, {"speciesId": "whimsicott", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 597, "matchups": [{"opponent": "quagsire_shadow", "rating": 911, "opRating": 88}, {"opponent": "flygon", "rating": 860, "opRating": 139}, {"opponent": "furret", "rating": 648}, {"opponent": "diggersby", "rating": 614, "opRating": 385}, {"opponent": "claydol", "rating": 614, "opRating": 385}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "clodsire", "rating": 182}, {"opponent": "cradily", "rating": 333}, {"opponent": "gligar", "rating": 335}, {"opponent": "talonflame", "rating": 348}], "moves": {"fastMoves": [{"moveId": "FAIRY_WIND", "uses": 31667}, {"moveId": "CHARM", "uses": 14736}, {"moveId": "RAZOR_LEAF", "uses": 11877}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 17003}, {"moveId": "MOONBLAST", "uses": 15116}, {"moveId": "SEED_BOMB", "uses": 13551}, {"moveId": "HURRICANE", "uses": 12597}]}, "moveset": ["FAIRY_WIND", "SEED_BOMB", "MOONBLAST"], "score": 75.9}, {"speciesId": "garcho<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 576, "matchups": [{"opponent": "typhlosion_shadow", "rating": 769, "opRating": 230}, {"opponent": "ursaring_shadow", "rating": 742, "opRating": 257}, {"opponent": "magcargo", "rating": 738, "opRating": 261}, {"opponent": "ninetales_shadow", "rating": 535, "opRating": 464}, {"opponent": "flygon_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 300}, {"opponent": "furret", "rating": 353}, {"opponent": "cradily", "rating": 357}, {"opponent": "gligar", "rating": 377}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31314}, {"moveId": "MUD_SHOT", "uses": 26986}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 19553}, {"moveId": "EARTH_POWER", "uses": 16721}, {"moveId": "FIRE_BLAST", "uses": 8843}, {"moveId": "SAND_TOMB", "uses": 6991}, {"moveId": "EARTHQUAKE", "uses": 6082}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 75.8}, {"speciesId": "greedent", "speciesName": "Greedent", "rating": 576, "matchups": [{"opponent": "salazzle", "rating": 819, "opRating": 180}, {"opponent": "quagsire_shadow", "rating": 638, "opRating": 361}, {"opponent": "clodsire", "rating": 618, "opRating": 381}, {"opponent": "swampert_shadow", "rating": 618, "opRating": 381}, {"opponent": "claydol", "rating": 579, "opRating": 420}], "counters": [{"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "furret", "rating": 306}, {"opponent": "cradily", "rating": 357}, {"opponent": "talonflame", "rating": 362}, {"opponent": "gligar", "rating": 366}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 16652}, {"moveId": "MUD_SHOT", "uses": 16536}, {"moveId": "TACKLE", "uses": 15529}, {"moveId": "BITE", "uses": 9585}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25003}, {"moveId": "CRUNCH", "uses": 16674}, {"moveId": "TRAILBLAZE", "uses": 16618}]}, "moveset": ["MUD_SHOT", "BODY_SLAM", "TRAILBLAZE"], "score": 75.6}, {"speciesId": "kangaskhan", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 568, "matchups": [{"opponent": "magcargo", "rating": 787, "opRating": 212}, {"opponent": "clodsire", "rating": 637, "opRating": 362}, {"opponent": "ninetales_shadow", "rating": 568, "opRating": 431}, {"opponent": "swampert_shadow", "rating": 526, "opRating": 473}, {"opponent": "drampa", "rating": 509, "opRating": 490}], "counters": [{"opponent": "jumpluff_shadow", "rating": 199}, {"opponent": "gligar", "rating": 259}, {"opponent": "talonflame", "rating": 311}, {"opponent": "cradily", "rating": 364}, {"opponent": "furret", "rating": 440}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45845}, {"moveId": "LOW_KICK", "uses": 12455}], "chargedMoves": [{"moveId": "STOMP", "uses": 13673}, {"moveId": "CRUNCH", "uses": 13123}, {"moveId": "BRICK_BREAK", "uses": 11052}, {"moveId": "OUTRAGE", "uses": 8689}, {"moveId": "EARTHQUAKE", "uses": 6785}, {"moveId": "POWER_UP_PUNCH", "uses": 4844}]}, "moveset": ["MUD_SLAP", "STOMP", "CRUNCH"], "score": 75.3}, {"speciesId": "growlithe_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 605, "matchups": [{"opponent": "abomasnow_shadow", "rating": 885, "opRating": 114}, {"opponent": "piloswine", "rating": 848, "opRating": 151}, {"opponent": "jumpluff_shadow", "rating": 629}, {"opponent": "gliscor", "rating": 559, "opRating": 440}, {"opponent": "ninetales_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "furret", "rating": 253}, {"opponent": "talonflame", "rating": 388}, {"opponent": "gligar", "rating": 389}, {"opponent": "clodsire", "rating": 437}, {"opponent": "cradily", "rating": 465}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41279}, {"moveId": "BITE", "uses": 17021}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28815}, {"moveId": "FLAMETHROWER", "uses": 24054}, {"moveId": "FLAME_WHEEL", "uses": 5477}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 75.2}, {"speciesId": "pidgeott<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 636, "matchups": [{"opponent": "bewear", "rating": 894, "opRating": 105}, {"opponent": "ursaring", "rating": 884, "opRating": 115}, {"opponent": "bellossom", "rating": 792, "opRating": 207}, {"opponent": "victreebel", "rating": 724, "opRating": 275}, {"opponent": "gligar_shadow", "rating": 564, "opRating": 435}], "counters": [{"opponent": "cradily", "rating": 263}, {"opponent": "furret", "rating": 315}, {"opponent": "talonflame", "rating": 362}, {"opponent": "gligar", "rating": 385}, {"opponent": "jumpluff_shadow", "rating": 483}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 33799}, {"moveId": "STEEL_WING", "uses": 24501}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 36705}, {"moveId": "AERIAL_ACE", "uses": 13844}, {"moveId": "TWISTER", "uses": 7744}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WING_ATTACK", "AIR_CUTTER", "AERIAL_ACE"], "score": 75.2}, {"speciesId": "garcho<PERSON>", "speciesName": "Garchomp", "rating": 559, "matchups": [{"opponent": "magcargo", "rating": 777, "opRating": 222}, {"opponent": "typhlosion_shadow", "rating": 742, "opRating": 257}, {"opponent": "clodsire", "rating": 531}, {"opponent": "cradily", "rating": 515}, {"opponent": "quagsire_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "gligar", "rating": 316}, {"opponent": "furret", "rating": 328}, {"opponent": "talonflame", "rating": 359}, {"opponent": "diggersby", "rating": 405}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 31464}, {"moveId": "MUD_SHOT", "uses": 26836}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 19548}, {"moveId": "EARTH_POWER", "uses": 16751}, {"moveId": "FIRE_BLAST", "uses": 8853}, {"moveId": "SAND_TOMB", "uses": 6974}, {"moveId": "EARTHQUAKE", "uses": 6097}]}, "moveset": ["DRAGON_TAIL", "SAND_TOMB", "OUTRAGE"], "score": 75}, {"speciesId": "dug<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 548, "matchups": [{"opponent": "clodsire", "rating": 875, "opRating": 125}, {"opponent": "typhlosion_shadow", "rating": 745, "opRating": 255}, {"opponent": "magcargo", "rating": 730, "opRating": 270}, {"opponent": "skeledirge", "rating": 710, "opRating": 290}, {"opponent": "ninetales_shadow", "rating": 675, "opRating": 325}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 137}, {"opponent": "furret", "rating": 331}, {"opponent": "talonflame", "rating": 429}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 21364}, {"moveId": "MUD_SLAP", "uses": 20727}, {"moveId": "MUD_SHOT", "uses": 16204}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 29668}, {"moveId": "STONE_EDGE", "uses": 21425}, {"moveId": "EARTHQUAKE", "uses": 7080}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "STONE_EDGE"], "score": 74.7}, {"speciesId": "incineroar", "speciesName": "Incineroar", "rating": 580, "matchups": [{"opponent": "talonflame", "rating": 786}, {"opponent": "piloswine", "rating": 732, "opRating": 267}, {"opponent": "skeledirge", "rating": 641, "opRating": 358}, {"opponent": "furret", "rating": 591}, {"opponent": "jumpluff_shadow", "rating": 519}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "magcargo", "rating": 235}, {"opponent": "clodsire", "rating": 314}, {"opponent": "diggersby", "rating": 316}, {"opponent": "cradily", "rating": 319}], "moves": {"fastMoves": [{"moveId": "DOUBLE_KICK", "uses": 20648}, {"moveId": "SNARL", "uses": 20037}, {"moveId": "FIRE_FANG", "uses": 17577}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 16881}, {"moveId": "BLAZE_KICK", "uses": 12386}, {"moveId": "DARKEST_LARIAT", "uses": 11663}, {"moveId": "DARK_PULSE", "uses": 10688}, {"moveId": "FLAME_CHARGE", "uses": 4740}, {"moveId": "FIRE_BLAST", "uses": 2120}]}, "moveset": ["SNARL", "DARKEST_LARIAT", "BLAST_BURN"], "score": 74.7}, {"speciesId": "mudsdale", "speciesName": "Mudsdale", "rating": 555, "matchups": [{"opponent": "magcargo", "rating": 808, "opRating": 191}, {"opponent": "clodsire", "rating": 757, "opRating": 242}, {"opponent": "typhlosion_shadow", "rating": 731, "opRating": 268}, {"opponent": "drampa", "rating": 672, "opRating": 327}, {"opponent": "diggersby", "rating": 555, "opRating": 444}], "counters": [{"opponent": "jumpluff_shadow", "rating": 192}, {"opponent": "gligar", "rating": 263}, {"opponent": "furret", "rating": 278}, {"opponent": "cradily", "rating": 284}, {"opponent": "talonflame", "rating": 344}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43740}, {"moveId": "ROCK_SMASH", "uses": 14560}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 21729}, {"moveId": "EARTHQUAKE", "uses": 13149}, {"moveId": "BULLDOZE", "uses": 12368}, {"moveId": "HEAVY_SLAM", "uses": 11065}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTHQUAKE"], "score": 74.7}, {"speciesId": "oinkologne", "speciesName": "Oinkologne", "rating": 578, "matchups": [{"opponent": "armarouge", "rating": 666, "opRating": 333}, {"opponent": "quagsire_shadow", "rating": 613, "opRating": 386}, {"opponent": "swampert_shadow", "rating": 522, "opRating": 477}, {"opponent": "gligar_shadow", "rating": 512, "opRating": 487}, {"opponent": "furret", "rating": 506}], "counters": [{"opponent": "cradily", "rating": 267}, {"opponent": "clodsire", "rating": 384}, {"opponent": "jumpluff_shadow", "rating": 424}, {"opponent": "talonflame", "rating": 433}, {"opponent": "gligar", "rating": 435}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 46070}, {"moveId": "TAKE_DOWN", "uses": 12230}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28518}, {"moveId": "TRAILBLAZE", "uses": 18366}, {"moveId": "DIG", "uses": 11418}]}, "moveset": ["TACKLE", "BODY_SLAM", "TRAILBLAZE"], "score": 74.7}, {"speciesId": "go<PERSON>", "speciesName": "<PERSON><PERSON>", "rating": 567, "matchups": [{"opponent": "magcargo", "rating": 783, "opRating": 216}, {"opponent": "typhlosion_shadow", "rating": 698, "opRating": 301}, {"opponent": "skeledirge", "rating": 684, "opRating": 315}, {"opponent": "flygon", "rating": 624, "opRating": 375}, {"opponent": "clodsire", "rating": 560, "opRating": 439}], "counters": [{"opponent": "jumpluff_shadow", "rating": 202}, {"opponent": "furret", "rating": 253}, {"opponent": "gligar", "rating": 293}, {"opponent": "cradily", "rating": 322}, {"opponent": "talonflame", "rating": 377}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31382}, {"moveId": "ASTONISH", "uses": 26918}], "chargedMoves": [{"moveId": "SHADOW_PUNCH", "uses": 25725}, {"moveId": "BRICK_BREAK", "uses": 17222}, {"moveId": "RETURN", "uses": 7710}, {"moveId": "NIGHT_SHADE", "uses": 7622}]}, "moveset": ["MUD_SLAP", "SHADOW_PUNCH", "BRICK_BREAK"], "score": 74.6}, {"speciesId": "mudbray", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 553, "matchups": [{"opponent": "magcargo", "rating": 794, "opRating": 205}, {"opponent": "clodsire", "rating": 742, "opRating": 257}, {"opponent": "skeledirge", "rating": 697, "opRating": 302}, {"opponent": "claydol", "rating": 623, "opRating": 376}, {"opponent": "drampa", "rating": 585, "opRating": 414}], "counters": [{"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "gligar", "rating": 240}, {"opponent": "cradily", "rating": 288}, {"opponent": "furret", "rating": 303}, {"opponent": "talonflame", "rating": 351}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43716}, {"moveId": "ROCK_SMASH", "uses": 14584}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27485}, {"moveId": "EARTHQUAKE", "uses": 15907}, {"moveId": "BULLDOZE", "uses": 14866}]}, "moveset": ["MUD_SLAP", "BODY_SLAM", "EARTHQUAKE"], "score": 74.4}, {"speciesId": "<PERSON><PERSON>e", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 569, "matchups": [{"opponent": "quagsire_shadow", "rating": 911, "opRating": 88}, {"opponent": "claydol", "rating": 855, "opRating": 144}, {"opponent": "swampert_shadow", "rating": 619, "opRating": 380}, {"opponent": "flygon", "rating": 588, "opRating": 411}, {"opponent": "diggersby", "rating": 514, "opRating": 485}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "furret", "rating": 325}, {"opponent": "cradily", "rating": 329}, {"opponent": "gligar", "rating": 362}, {"opponent": "talonflame", "rating": 396}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42492}, {"moveId": "IRON_TAIL", "uses": 15808}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 17240}, {"moveId": "LEAF_TORNADO", "uses": 16623}, {"moveId": "WRAP", "uses": 16182}, {"moveId": "RETURN", "uses": 8216}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "WRAP"], "score": 74.4}, {"speciesId": "oricorio_baile", "speciesName": "Oricorio (Baile)", "rating": 609, "matchups": [{"opponent": "lura<PERSON>s", "rating": 796, "opRating": 203}, {"opponent": "tropius", "rating": 750, "opRating": 250}, {"opponent": "farfetchd", "rating": 730, "opRating": 269}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 558, "opRating": 441}, {"opponent": "jumpluff_shadow", "rating": 527}], "counters": [{"opponent": "clodsire", "rating": 250}, {"opponent": "furret", "rating": 256}, {"opponent": "cradily", "rating": 281}, {"opponent": "talonflame", "rating": 422}, {"opponent": "gligar", "rating": 446}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 52444}, {"moveId": "POUND", "uses": 5856}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 36630}, {"moveId": "AERIAL_ACE", "uses": 13894}, {"moveId": "HURRICANE", "uses": 7915}]}, "moveset": ["AIR_SLASH", "AIR_CUTTER", "AERIAL_ACE"], "score": 74.1}, {"speciesId": "<PERSON><PERSON>o", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 547, "matchups": [{"opponent": "clodsire", "rating": 875, "opRating": 125}, {"opponent": "magcargo", "rating": 770, "opRating": 230}, {"opponent": "skeledirge", "rating": 675, "opRating": 325}, {"opponent": "flygon", "rating": 555, "opRating": 445}, {"opponent": "furret", "rating": 510}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 125}, {"opponent": "diggersby", "rating": 364}, {"opponent": "talonflame", "rating": 374}, {"opponent": "cradily", "rating": 388}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 21279}, {"moveId": "MUD_SLAP", "uses": 20617}, {"moveId": "MUD_SHOT", "uses": 16377}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 25586}, {"moveId": "STONE_EDGE", "uses": 17891}, {"moveId": "RETURN", "uses": 8617}, {"moveId": "EARTHQUAKE", "uses": 6048}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "STONE_EDGE"], "score": 73.8}, {"speciesId": "gourgeist_large", "speciesName": "Gourgeist (Large)", "rating": 582, "matchups": [{"opponent": "quagsire_shadow", "rating": 905, "opRating": 94}, {"opponent": "swampert_shadow", "rating": 672, "opRating": 327}, {"opponent": "claydol", "rating": 627, "opRating": 372}, {"opponent": "clodsire", "rating": 586, "opRating": 413}, {"opponent": "flygon", "rating": 516, "opRating": 483}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 181}, {"opponent": "furret", "rating": 253}, {"opponent": "gligar", "rating": 351}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 39910}, {"moveId": "RAZOR_LEAF", "uses": 18390}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17057}, {"moveId": "SEED_BOMB", "uses": 16063}, {"moveId": "SHADOW_BALL", "uses": 13853}, {"moveId": "FIRE_BLAST", "uses": 6901}, {"moveId": "POLTERGEIST", "uses": 4464}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 73.8}, {"speciesId": "gourgeist_super", "speciesName": "Gourgeist (Super)", "rating": 582, "matchups": [{"opponent": "quagsire_shadow", "rating": 810, "opRating": 189}, {"opponent": "swampert_shadow", "rating": 678, "opRating": 321}, {"opponent": "claydol", "rating": 643, "opRating": 356}, {"opponent": "clodsire", "rating": 604, "opRating": 395}, {"opponent": "flygon", "rating": 538, "opRating": 461}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 181}, {"opponent": "furret", "rating": 253}, {"opponent": "gligar", "rating": 351}, {"opponent": "cradily", "rating": 475}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 39975}, {"moveId": "RAZOR_LEAF", "uses": 18325}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17044}, {"moveId": "SEED_BOMB", "uses": 16032}, {"moveId": "SHADOW_BALL", "uses": 13878}, {"moveId": "FIRE_BLAST", "uses": 6906}, {"moveId": "POLTERGEIST", "uses": 4467}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 73.8}, {"speciesId": "nidoking_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 523, "matchups": [{"opponent": "cradily", "rating": 901}, {"opponent": "clodsire", "rating": 901, "opRating": 98}, {"opponent": "magcargo", "rating": 681}, {"opponent": "diggersby", "rating": 610, "opRating": 389}, {"opponent": "flygon", "rating": 523, "opRating": 476}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "jumpluff_shadow", "rating": 140}, {"opponent": "swampert_shadow", "rating": 246}, {"opponent": "furret", "rating": 296}, {"opponent": "talonflame", "rating": 377}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 15159}, {"moveId": "FURY_CUTTER", "uses": 14247}, {"moveId": "DOUBLE_KICK", "uses": 13487}, {"moveId": "POISON_JAB", "uses": 12453}, {"moveId": "IRON_TAIL", "uses": 2913}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 16689}, {"moveId": "EARTH_POWER", "uses": 16441}, {"moveId": "SLUDGE_WAVE", "uses": 12304}, {"moveId": "SAND_TOMB", "uses": 6898}, {"moveId": "EARTHQUAKE", "uses": 5923}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 73.8}, {"speciesId": "sandslash", "speciesName": "Sandslash", "rating": 557, "matchups": [{"opponent": "salazzle", "rating": 768, "opRating": 232}, {"opponent": "magcargo", "rating": 720, "opRating": 280}, {"opponent": "clodsire", "rating": 584}, {"opponent": "cradily", "rating": 556}, {"opponent": "ninetales_shadow", "rating": 544, "opRating": 456}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "diggersby", "rating": 347}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "furret", "rating": 384}, {"opponent": "gligar", "rating": 465}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 22595}, {"moveId": "MUD_SHOT", "uses": 20377}, {"moveId": "METAL_CLAW", "uses": 15278}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 15315}, {"moveId": "ROCK_TOMB", "uses": 14852}, {"moveId": "SCORCHING_SANDS", "uses": 12821}, {"moveId": "BULLDOZE", "uses": 6855}, {"moveId": "RETURN", "uses": 4925}, {"moveId": "EARTHQUAKE", "uses": 3706}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "SCORCHING_SANDS"], "score": 73.8}, {"speciesId": "wormadam_sandy", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Sandy)", "rating": 580, "matchups": [{"opponent": "obstagoon_shadow", "rating": 824, "opRating": 175}, {"opponent": "claydol", "rating": 800, "opRating": 199}, {"opponent": "cradily", "rating": 691}, {"opponent": "furret", "rating": 531}, {"opponent": "flygon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "magcargo", "rating": 226}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 248}, {"opponent": "clodsire", "rating": 365}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 32994}, {"moveId": "CONFUSION", "uses": 25306}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 27273}, {"moveId": "BULLDOZE", "uses": 21226}, {"moveId": "PSYBEAM", "uses": 9845}]}, "moveset": ["BUG_BITE", "BUG_BUZZ", "BULLDOZE"], "score": 73.8}, {"speciesId": "exeggutor_alolan_shadow", "speciesName": "Exeggutor (Al<PERSON><PERSON>) (Shadow)", "rating": 569, "matchups": [{"opponent": "claydol", "rating": 690, "opRating": 309}, {"opponent": "typhlosion_shadow", "rating": 629, "opRating": 370}, {"opponent": "drampa", "rating": 549, "opRating": 450}, {"opponent": "diggersby", "rating": 526, "opRating": 473}, {"opponent": "flygon_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 258}, {"opponent": "gligar", "rating": 324}, {"opponent": "cradily", "rating": 350}, {"opponent": "furret", "rating": 353}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 32672}, {"moveId": "BULLET_SEED", "uses": 25628}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20351}, {"moveId": "DRAGON_PULSE", "uses": 16935}, {"moveId": "DRACO_METEOR", "uses": 15034}, {"moveId": "SOLAR_BEAM", "uses": 5774}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 73.7}, {"speciesId": "rhyhorn", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 548, "matchups": [{"opponent": "magcargo", "rating": 783, "opRating": 216}, {"opponent": "skeledirge", "rating": 704, "opRating": 295}, {"opponent": "drampa", "rating": 682, "opRating": 317}, {"opponent": "clodsire", "rating": 525, "opRating": 474}, {"opponent": "flygon", "rating": 515, "opRating": 484}], "counters": [{"opponent": "jumpluff_shadow", "rating": 176}, {"opponent": "gligar", "rating": 248}, {"opponent": "furret", "rating": 253}, {"opponent": "cradily", "rating": 267}, {"opponent": "talonflame", "rating": 381}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 42557}, {"moveId": "ROCK_SMASH", "uses": 15743}], "chargedMoves": [{"moveId": "BULLDOZE", "uses": 16788}, {"moveId": "STOMP", "uses": 16667}, {"moveId": "HORN_ATTACK", "uses": 15263}, {"moveId": "RETURN", "uses": 9576}]}, "moveset": ["MUD_SLAP", "BULLDOZE", "STOMP"], "score": 73.7}, {"speciesId": "amoon<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>)", "rating": 559, "matchups": [{"opponent": "claydol", "rating": 717, "opRating": 282}, {"opponent": "quagsire_shadow", "rating": 697, "opRating": 302}, {"opponent": "swampert_shadow", "rating": 576, "opRating": 423}, {"opponent": "magcargo", "rating": 545, "opRating": 454}, {"opponent": "gligar", "rating": 528}], "counters": [{"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "talonflame", "rating": 300}, {"opponent": "furret", "rating": 403}, {"opponent": "cradily", "rating": 447}, {"opponent": "clodsire", "rating": 468}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33887}, {"moveId": "FEINT_ATTACK", "uses": 24413}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 20657}, {"moveId": "FOUL_PLAY", "uses": 18796}, {"moveId": "SLUDGE_BOMB", "uses": 18749}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 73.5}, {"speciesId": "servine_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 559, "matchups": [{"opponent": "swampert_shadow", "rating": 890, "opRating": 109}, {"opponent": "quagsire_shadow", "rating": 880, "opRating": 119}, {"opponent": "claydol", "rating": 827, "opRating": 172}, {"opponent": "diggersby", "rating": 658, "opRating": 341}, {"opponent": "furret", "rating": 528}], "counters": [{"opponent": "jumpluff_shadow", "rating": 196}, {"opponent": "gligar", "rating": 202}, {"opponent": "talonflame", "rating": 274}, {"opponent": "cradily", "rating": 350}, {"opponent": "clodsire", "rating": 399}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42623}, {"moveId": "IRON_TAIL", "uses": 15677}], "chargedMoves": [{"moveId": "WRAP", "uses": 19954}, {"moveId": "GRASS_KNOT", "uses": 19514}, {"moveId": "LEAF_TORNADO", "uses": 18814}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "WRAP"], "score": 73.5}, {"speciesId": "snorlax", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 582, "matchups": [{"opponent": "piloswine", "rating": 673, "opRating": 326}, {"opponent": "abomasnow_shadow", "rating": 644, "opRating": 355}, {"opponent": "swampert_shadow", "rating": 593, "opRating": 406}, {"opponent": "typhlosion_shadow", "rating": 566, "opRating": 433}, {"opponent": "drampa", "rating": 508, "opRating": 491}], "counters": [{"opponent": "furret", "rating": 228}, {"opponent": "jumpluff_shadow", "rating": 313}, {"opponent": "cradily", "rating": 329}, {"opponent": "talonflame", "rating": 329}, {"opponent": "gligar", "rating": 370}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 46089}, {"moveId": "ZEN_HEADBUTT", "uses": 9956}, {"moveId": "YAWN", "uses": 2327}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 16328}, {"moveId": "BODY_SLAM", "uses": 16035}, {"moveId": "OUTRAGE", "uses": 7600}, {"moveId": "EARTHQUAKE", "uses": 6143}, {"moveId": "HEAVY_SLAM", "uses": 5727}, {"moveId": "SKULL_BASH", "uses": 3913}, {"moveId": "HYPER_BEAM", "uses": 2604}]}, "moveset": ["LICK", "BODY_SLAM", "SUPER_POWER"], "score": 73.5}, {"speciesId": "hippopotas_shadow", "speciesName": "Hip<PERSON><PERSON><PERSON> (Shadow)", "rating": 561, "matchups": [{"opponent": "salazzle", "rating": 750, "opRating": 250}, {"opponent": "magcargo", "rating": 710, "opRating": 289}, {"opponent": "clodsire", "rating": 588}, {"opponent": "cradily", "rating": 549}, {"opponent": "ninetales_shadow", "rating": 506, "opRating": 493}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "gligar", "rating": 320}, {"opponent": "diggersby", "rating": 336}, {"opponent": "jumpluff_shadow", "rating": 392}, {"opponent": "furret", "rating": 462}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 28631}, {"moveId": "TACKLE", "uses": 17033}, {"moveId": "BITE", "uses": 12586}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 23065}, {"moveId": "BODY_SLAM", "uses": 19178}, {"moveId": "DIG", "uses": 16013}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 73.4}, {"speciesId": "amoon<PERSON>s", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 570, "matchups": [{"opponent": "quagsire_shadow", "rating": 754, "opRating": 245}, {"opponent": "claydol", "rating": 742, "opRating": 257}, {"opponent": "zangoose", "rating": 641, "opRating": 358}, {"opponent": "swampert_shadow", "rating": 632, "opRating": 367}, {"opponent": "flygon", "rating": 531, "opRating": 468}], "counters": [{"opponent": "jumpluff_shadow", "rating": 294}, {"opponent": "gligar", "rating": 347}, {"opponent": "furret", "rating": 356}, {"opponent": "talonflame", "rating": 377}, {"opponent": "cradily", "rating": 392}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 33761}, {"moveId": "FEINT_ATTACK", "uses": 24539}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 18525}, {"moveId": "FOUL_PLAY", "uses": 16601}, {"moveId": "SLUDGE_BOMB", "uses": 16337}, {"moveId": "RETURN", "uses": 6832}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 73.2}, {"speciesId": "geodude", "speciesName": "Geodude", "rating": 496, "matchups": [{"opponent": "talonflame", "rating": 867}, {"opponent": "skeledirge", "rating": 809, "opRating": 190}, {"opponent": "ninetales_shadow", "rating": 694, "opRating": 305}, {"opponent": "jumpluff_shadow", "rating": 641}, {"opponent": "magcargo", "rating": 561, "opRating": 438}], "counters": [{"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 301}, {"opponent": "furret", "rating": 303}, {"opponent": "diggersby", "rating": 318}, {"opponent": "clodsire", "rating": 341}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32632}, {"moveId": "TACKLE", "uses": 25668}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 23337}, {"moveId": "ROCK_SLIDE", "uses": 15098}, {"moveId": "DIG", "uses": 13314}, {"moveId": "RETURN", "uses": 6524}]}, "moveset": ["ROCK_THROW", "ROCK_TOMB", "ROCK_SLIDE"], "score": 72.9}, {"speciesId": "pumpkaboo_super", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Super)", "rating": 565, "matchups": [{"opponent": "claydol", "rating": 804, "opRating": 195}, {"opponent": "quagsire_shadow", "rating": 744, "opRating": 255}, {"opponent": "swampert_shadow", "rating": 656, "opRating": 343}, {"opponent": "gliscor", "rating": 553, "opRating": 446}, {"opponent": "gligar", "rating": 549}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 427}, {"opponent": "clodsire", "rating": 442}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 37877}, {"moveId": "RAZOR_LEAF", "uses": 20423}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23635}, {"moveId": "FOUL_PLAY", "uses": 23227}, {"moveId": "SHADOW_SNEAK", "uses": 11396}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 72.9}, {"speciesId": "gourgeist_small", "speciesName": "Gourge<PERSON> (Small)", "rating": 568, "matchups": [{"opponent": "quagsire_shadow", "rating": 892, "opRating": 107}, {"opponent": "swampert_shadow", "rating": 696, "opRating": 303}, {"opponent": "claydol", "rating": 640, "opRating": 359}, {"opponent": "clodsire", "rating": 635, "opRating": 364}, {"opponent": "flygon", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 181}, {"opponent": "furret", "rating": 256}, {"opponent": "gligar", "rating": 332}, {"opponent": "cradily", "rating": 493}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 40014}, {"moveId": "RAZOR_LEAF", "uses": 18286}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17026}, {"moveId": "SEED_BOMB", "uses": 16071}, {"moveId": "SHADOW_BALL", "uses": 13839}, {"moveId": "FIRE_BLAST", "uses": 6901}, {"moveId": "POLTERGEIST", "uses": 4467}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 72.8}, {"speciesId": "nidoking", "speciesName": "Nidoking", "rating": 532, "matchups": [{"opponent": "cradily", "rating": 901}, {"opponent": "magcargo", "rating": 728, "opRating": 271}, {"opponent": "skeledirge", "rating": 712, "opRating": 287}, {"opponent": "furret", "rating": 673}, {"opponent": "flygon", "rating": 586, "opRating": 413}], "counters": [{"opponent": "gligar", "rating": 152}, {"opponent": "clodsire", "rating": 170}, {"opponent": "jumpluff_shadow", "rating": 205}, {"opponent": "diggersby", "rating": 238}, {"opponent": "talonflame", "rating": 311}], "moves": {"fastMoves": [{"moveId": "POISON_STING", "uses": 14755}, {"moveId": "FURY_CUTTER", "uses": 14392}, {"moveId": "DOUBLE_KICK", "uses": 13194}, {"moveId": "POISON_JAB", "uses": 12610}, {"moveId": "IRON_TAIL", "uses": 3342}], "chargedMoves": [{"moveId": "MEGAHORN", "uses": 16725}, {"moveId": "EARTH_POWER", "uses": 16411}, {"moveId": "SLUDGE_WAVE", "uses": 12312}, {"moveId": "SAND_TOMB", "uses": 6885}, {"moveId": "EARTHQUAKE", "uses": 5932}]}, "moveset": ["DOUBLE_KICK", "SAND_TOMB", "EARTH_POWER"], "score": 72.8}, {"speciesId": "pignite", "speciesName": "Pignite", "rating": 582, "matchups": [{"opponent": "abomasnow_shadow", "rating": 908, "opRating": 91}, {"opponent": "sceptile_shadow", "rating": 882, "opRating": 117}, {"opponent": "piloswine", "rating": 702, "opRating": 297}, {"opponent": "ninetales", "rating": 702, "opRating": 297}, {"opponent": "furret", "rating": 651}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "clodsire", "rating": 360}, {"opponent": "cradily", "rating": 423}, {"opponent": "gligar", "rating": 465}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38851}, {"moveId": "TACKLE", "uses": 19449}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22885}, {"moveId": "FLAME_CHARGE", "uses": 19447}, {"moveId": "FLAMETHROWER", "uses": 8047}, {"moveId": "RETURN", "uses": 7988}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 72.8}, {"speciesId": "rufflet", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 597, "matchups": [{"opponent": "<PERSON><PERSON><PERSON>", "rating": 726, "opRating": 273}, {"opponent": "gligar", "rating": 631}, {"opponent": "gligar_shadow", "rating": 625, "opRating": 375}, {"opponent": "gliscor", "rating": 621, "opRating": 378}, {"opponent": "swampert_shadow", "rating": 588, "opRating": 411}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "clodsire", "rating": 319}, {"opponent": "cradily", "rating": 322}, {"opponent": "furret", "rating": 456}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 39840}, {"moveId": "PECK", "uses": 18460}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18575}, {"moveId": "FLY", "uses": 15683}, {"moveId": "ROCK_TOMB", "uses": 12131}, {"moveId": "AERIAL_ACE", "uses": 11967}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "FLY"], "score": 72.8}, {"speciesId": "typhlosion_hisuian", "speciesName": "Typhlosion (Hisuian)", "rating": 595, "matchups": [{"opponent": "abomasnow_shadow", "rating": 884, "opRating": 115}, {"opponent": "gliscor", "rating": 666, "opRating": 333}, {"opponent": "typhlosion_shadow", "rating": 648, "opRating": 351}, {"opponent": "pidgeot", "rating": 606, "opRating": 393}, {"opponent": "ninetales_shadow", "rating": 555, "opRating": 444}], "counters": [{"opponent": "furret", "rating": 253}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 362}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "cradily", "rating": 364}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 29622}, {"moveId": "HEX", "uses": 28678}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 16916}, {"moveId": "WILD_CHARGE", "uses": 14271}, {"moveId": "NIGHT_SHADE", "uses": 11906}, {"moveId": "OVERHEAT", "uses": 9779}, {"moveId": "SHADOW_BALL", "uses": 5474}]}, "moveset": ["EMBER", "FIRE_PUNCH", "WILD_CHARGE"], "score": 72.6}, {"speciesId": "combusken", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 580, "matchups": [{"opponent": "abomasnow_shadow", "rating": 889, "opRating": 110}, {"opponent": "piloswine", "rating": 721, "opRating": 278}, {"opponent": "ninetales_shadow", "rating": 625, "opRating": 374}, {"opponent": "gligar", "rating": 599}, {"opponent": "furret", "rating": 591}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "magcargo", "rating": 277}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "clodsire", "rating": 365}, {"opponent": "cradily", "rating": 399}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43160}, {"moveId": "PECK", "uses": 15140}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 21725}, {"moveId": "ROCK_SLIDE", "uses": 18223}, {"moveId": "RETURN", "uses": 9246}, {"moveId": "FLAMETHROWER", "uses": 9039}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 72.3}, {"speciesId": "dart<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 562, "matchups": [{"opponent": "swampert_shadow", "rating": 967, "opRating": 32}, {"opponent": "claydol", "rating": 899, "opRating": 100}, {"opponent": "diggersby", "rating": 646, "opRating": 353}, {"opponent": "gligar", "rating": 590}, {"opponent": "furret", "rating": 584}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 100}, {"opponent": "cradily", "rating": 184}, {"opponent": "magcargo", "rating": 205}, {"opponent": "clodsire", "rating": 286}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 19938}, {"moveId": "MAGICAL_LEAF", "uses": 18724}, {"moveId": "PECK", "uses": 12401}, {"moveId": "RAZOR_LEAF", "uses": 7322}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 36656}, {"moveId": "SEED_BOMB", "uses": 15005}, {"moveId": "ENERGY_BALL", "uses": 6699}]}, "moveset": ["LEAFAGE", "BRAVE_BIRD", "SEED_BOMB"], "score": 72.3}, {"speciesId": "gourgeist_average", "speciesName": "Gourgeist (Average)", "rating": 572, "matchups": [{"opponent": "quagsire_shadow", "rating": 900, "opRating": 100}, {"opponent": "swampert_shadow", "rating": 704, "opRating": 295}, {"opponent": "claydol", "rating": 613, "opRating": 386}, {"opponent": "clodsire", "rating": 573, "opRating": 426}, {"opponent": "flygon", "rating": 530, "opRating": 469}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 181}, {"opponent": "furret", "rating": 253}, {"opponent": "gligar", "rating": 351}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "HEX", "uses": 39912}, {"moveId": "RAZOR_LEAF", "uses": 18388}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 17056}, {"moveId": "SEED_BOMB", "uses": 16064}, {"moveId": "SHADOW_BALL", "uses": 13855}, {"moveId": "FIRE_BLAST", "uses": 6901}, {"moveId": "POLTERGEIST", "uses": 4464}]}, "moveset": ["HEX", "SEED_BOMB", "SHADOW_BALL"], "score": 72.3}, {"speciesId": "swellow", "speciesName": "Swellow", "rating": 586, "matchups": [{"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 818, "opRating": 181}, {"opponent": "gligar", "rating": 570}, {"opponent": "gliscor", "rating": 557, "opRating": 442}, {"opponent": "gligar_shadow", "rating": 533, "opRating": 466}, {"opponent": "swampert_shadow", "rating": 520, "opRating": 479}], "counters": [{"opponent": "cradily", "rating": 184}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "clodsire", "rating": 264}, {"opponent": "furret", "rating": 403}, {"opponent": "talonflame", "rating": 418}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 36300}, {"moveId": "STEEL_WING", "uses": 22000}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27123}, {"moveId": "AERIAL_ACE", "uses": 17523}, {"moveId": "RETURN", "uses": 7846}, {"moveId": "SKY_ATTACK", "uses": 5971}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "AERIAL_ACE"], "score": 72.3}, {"speciesId": "pidgeotto", "speciesName": "Pidgeotto", "rating": 610, "matchups": [{"opponent": "bewear", "rating": 928, "opRating": 71}, {"opponent": "ursaring_shadow", "rating": 884, "opRating": 115}, {"opponent": "bellossom_shadow", "rating": 792, "opRating": 207}, {"opponent": "victreebel", "rating": 758, "opRating": 241}, {"opponent": "jumpluff_shadow", "rating": 506}], "counters": [{"opponent": "cradily", "rating": 246}, {"opponent": "clodsire", "rating": 252}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 296}, {"opponent": "furret", "rating": 381}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 34030}, {"moveId": "STEEL_WING", "uses": 24270}], "chargedMoves": [{"moveId": "AIR_CUTTER", "uses": 30477}, {"moveId": "AERIAL_ACE", "uses": 11478}, {"moveId": "RETURN", "uses": 10191}, {"moveId": "TWISTER", "uses": 6308}]}, "moveset": ["WING_ATTACK", "AIR_CUTTER", "AERIAL_ACE"], "score": 72.2}, {"speciesId": "shiftry", "speciesName": "Shiftry", "rating": 558, "matchups": [{"opponent": "quagsire_shadow", "rating": 912, "opRating": 87}, {"opponent": "swampert_shadow", "rating": 870, "opRating": 129}, {"opponent": "claydol", "rating": 751, "opRating": 248}, {"opponent": "furret", "rating": 552}, {"opponent": "diggersby", "rating": 513, "opRating": 486}], "counters": [{"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "clodsire", "rating": 245}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 290}, {"opponent": "talonflame", "rating": 340}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 19377}, {"moveId": "BULLET_SEED", "uses": 18542}, {"moveId": "FEINT_ATTACK", "uses": 13397}, {"moveId": "RAZOR_LEAF", "uses": 6998}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 25237}, {"moveId": "FOUL_PLAY", "uses": 15872}, {"moveId": "HURRICANE", "uses": 7596}, {"moveId": "LEAF_TORNADO", "uses": 4825}, {"moveId": "RETURN", "uses": 4771}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 72.2}, {"speciesId": "vibrava", "speciesName": "Vibrava", "rating": 548, "matchups": [{"opponent": "typhlosion", "rating": 720, "opRating": 279}, {"opponent": "typhlosion_shadow", "rating": 665, "opRating": 334}, {"opponent": "magcargo", "rating": 578, "opRating": 421}, {"opponent": "clodsire", "rating": 507, "opRating": 492}, {"opponent": "quagsire_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "gligar", "rating": 293}, {"opponent": "furret", "rating": 300}, {"opponent": "talonflame", "rating": 433}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 21714}, {"moveId": "SAND_ATTACK", "uses": 19203}, {"moveId": "MUD_SHOT", "uses": 17400}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 19361}, {"moveId": "BUG_BUZZ", "uses": 13351}, {"moveId": "BULLDOZE", "uses": 10268}, {"moveId": "RETURN", "uses": 9230}, {"moveId": "SAND_TOMB", "uses": 6372}]}, "moveset": ["DRAGON_BREATH", "SAND_TOMB", "BUG_BUZZ"], "score": 72.2}, {"speciesId": "aipom", "speciesName": "Aipom", "rating": 563, "matchups": [{"opponent": "skeledirge", "rating": 729, "opRating": 270}, {"opponent": "typhlosion_shadow", "rating": 640, "opRating": 359}, {"opponent": "flygon_shadow", "rating": 607, "opRating": 392}, {"opponent": "marowak_alolan_shadow", "rating": 544, "opRating": 455}, {"opponent": "abomasnow_shadow", "rating": 540, "opRating": 459}], "counters": [{"opponent": "furret", "rating": 262}, {"opponent": "cradily", "rating": 336}, {"opponent": "gligar", "rating": 412}, {"opponent": "jumpluff_shadow", "rating": 418}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 34886}, {"moveId": "SCRATCH", "uses": 23414}], "chargedMoves": [{"moveId": "SWIFT", "uses": 26816}, {"moveId": "AERIAL_ACE", "uses": 17194}, {"moveId": "LOW_SWEEP", "uses": 9688}, {"moveId": "RETURN", "uses": 4655}]}, "moveset": ["ASTONISH", "SWIFT", "AERIAL_ACE"], "score": 72}, {"speciesId": "electrode_hisuian", "speciesName": "Electrode (Hisuian)", "rating": 576, "matchups": [{"opponent": "pidgeot", "rating": 786, "opRating": 213}, {"opponent": "talonflame", "rating": 682}, {"opponent": "typhlosion_shadow", "rating": 591, "opRating": 408}, {"opponent": "ninetales_shadow", "rating": 560, "opRating": 439}, {"opponent": "furret", "rating": 543}], "counters": [{"opponent": "magcargo", "rating": 141}, {"opponent": "cradily", "rating": 197}, {"opponent": "gligar", "rating": 206}, {"opponent": "jumpluff_shadow", "rating": 232}, {"opponent": "clodsire", "rating": 237}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 38481}, {"moveId": "TACKLE", "uses": 19819}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 23731}, {"moveId": "SWIFT", "uses": 19498}, {"moveId": "ENERGY_BALL", "uses": 15082}]}, "moveset": ["THUNDER_SHOCK", "WILD_CHARGE", "SWIFT"], "score": 72}, {"speciesId": "ho_oh", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 567, "matchups": [{"opponent": "claydol", "rating": 776, "opRating": 223}, {"opponent": "diggersby", "rating": 714, "opRating": 285}, {"opponent": "gligar", "rating": 706}, {"opponent": "gliscor", "rating": 692, "opRating": 307}, {"opponent": "jumpluff_shadow", "rating": 530}], "counters": [{"opponent": "magcargo", "rating": 102}, {"opponent": "clodsire", "rating": 206}, {"opponent": "talonflame", "rating": 207}, {"opponent": "cradily", "rating": 225}, {"opponent": "furret", "rating": 359}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 13614}, {"moveId": "EXTRASENSORY", "uses": 3967}, {"moveId": "STEEL_WING", "uses": 3632}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3250}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3219}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3024}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2726}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2586}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2472}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2314}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2309}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2132}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2090}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2064}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1966}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1803}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1801}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1684}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1509}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27184}, {"moveId": "SACRED_FIRE", "uses": 14637}, {"moveId": "EARTHQUAKE", "uses": 7677}, {"moveId": "SOLAR_BEAM", "uses": 5858}, {"moveId": "FIRE_BLAST", "uses": 2953}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 72}, {"speciesId": "ma<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "typhlosion_shadow", "rating": 687, "opRating": 312}, {"opponent": "quagsire_shadow", "rating": 617, "opRating": 382}, {"opponent": "clodsire", "rating": 599, "opRating": 400}, {"opponent": "swampert_shadow", "rating": 511, "opRating": 488}, {"opponent": "piloswine", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "gligar", "rating": 328}, {"opponent": "cradily", "rating": 347}, {"opponent": "talonflame", "rating": 422}, {"opponent": "furret", "rating": 443}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 25644}, {"moveId": "BULLET_SEED", "uses": 25442}, {"moveId": "TAKE_DOWN", "uses": 7188}], "chargedMoves": [{"moveId": "SWIFT", "uses": 32192}, {"moveId": "CRUNCH", "uses": 18605}, {"moveId": "PLAY_ROUGH", "uses": 7546}]}, "moveset": ["MUD_SHOT", "SWIFT", "CRUNCH"], "score": 71.9}, {"speciesId": "ponyta", "speciesName": "Ponyta", "rating": 602, "matchups": [{"opponent": "abomasnow_shadow", "rating": 875, "opRating": 125}, {"opponent": "sceptile_shadow", "rating": 840, "opRating": 159}, {"opponent": "piloswine", "rating": 698, "opRating": 301}, {"opponent": "typhlosion_shadow", "rating": 672, "opRating": 327}, {"opponent": "gliscor", "rating": 573, "opRating": 426}], "counters": [{"opponent": "talonflame", "rating": 255}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "cradily", "rating": 402}, {"opponent": "furret", "rating": 409}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38810}, {"moveId": "TACKLE", "uses": 19490}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23981}, {"moveId": "STOMP", "uses": 19061}, {"moveId": "FIRE_BLAST", "uses": 10776}, {"moveId": "FLAME_WHEEL", "uses": 4559}]}, "moveset": ["EMBER", "FLAME_CHARGE", "STOMP"], "score": 71.9}, {"speciesId": "venusaur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 544, "matchups": [{"opponent": "swampert_shadow", "rating": 849, "opRating": 150}, {"opponent": "claydol", "rating": 735, "opRating": 264}, {"opponent": "diggersby", "rating": 617, "opRating": 382}, {"opponent": "furret", "rating": 544}, {"opponent": "clodsire", "rating": 516}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 340}, {"opponent": "cradily", "rating": 392}, {"opponent": "magcargo", "rating": 410}, {"opponent": "gligar", "rating": 484}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 44205}, {"moveId": "RAZOR_LEAF", "uses": 14095}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30488}, {"moveId": "SLUDGE_BOMB", "uses": 20161}, {"moveId": "PETAL_BLIZZARD", "uses": 4242}, {"moveId": "SOLAR_BEAM", "uses": 3518}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 71.9}, {"speciesId": "dug<PERSON><PERSON>_alolan", "speciesName": "<PERSON><PERSON><PERSON> (Alolan)", "rating": 521, "matchups": [{"opponent": "clodsire", "rating": 927, "opRating": 72}, {"opponent": "flygon", "rating": 638, "opRating": 361}, {"opponent": "drampa", "rating": 594, "opRating": 405}, {"opponent": "magcargo", "rating": 577}, {"opponent": "cradily", "rating": 555}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "talonflame", "rating": 292}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "furret", "rating": 309}, {"opponent": "diggersby", "rating": 376}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22548}, {"moveId": "SAND_ATTACK", "uses": 19603}, {"moveId": "METAL_CLAW", "uses": 16141}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 26174}, {"moveId": "IRON_HEAD", "uses": 16170}, {"moveId": "RETURN", "uses": 9698}, {"moveId": "EARTHQUAKE", "uses": 6212}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "IRON_HEAD"], "score": 71.7}, {"speciesId": "pignite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 567, "matchups": [{"opponent": "abomasnow_shadow", "rating": 901, "opRating": 98}, {"opponent": "sceptile", "rating": 882, "opRating": 117}, {"opponent": "piloswine", "rating": 670, "opRating": 329}, {"opponent": "gligar", "rating": 588}, {"opponent": "furret", "rating": 579}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 398}, {"opponent": "clodsire", "rating": 420}, {"opponent": "magcargo", "rating": 444}, {"opponent": "cradily", "rating": 454}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 39452}, {"moveId": "TACKLE", "uses": 18848}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 26304}, {"moveId": "FLAME_CHARGE", "uses": 22501}, {"moveId": "FLAMETHROWER", "uses": 9431}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "ROCK_TOMB", "FLAME_CHARGE"], "score": 71.7}, {"speciesId": "g<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 562, "matchups": [{"opponent": "swampert_shadow", "rating": 880, "opRating": 119}, {"opponent": "quagsire_shadow", "rating": 880, "opRating": 119}, {"opponent": "claydol", "rating": 739, "opRating": 260}, {"opponent": "flygon", "rating": 551, "opRating": 448}, {"opponent": "diggersby", "rating": 508, "opRating": 491}], "counters": [{"opponent": "talonflame", "rating": 229}, {"opponent": "cradily", "rating": 236}, {"opponent": "jumpluff_shadow", "rating": 330}, {"opponent": "gligar", "rating": 354}, {"opponent": "furret", "rating": 412}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 29305}, {"moveId": "QUICK_ATTACK", "uses": 28995}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30257}, {"moveId": "AERIAL_ACE", "uses": 16112}, {"moveId": "GRASS_KNOT", "uses": 5938}, {"moveId": "RETURN", "uses": 5853}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "AERIAL_ACE"], "score": 71.6}, {"speciesId": "growl<PERSON>e_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 565, "matchups": [{"opponent": "abomasnow_shadow", "rating": 897, "opRating": 102}, {"opponent": "ninetales_shadow", "rating": 735, "opRating": 264}, {"opponent": "pidgeot", "rating": 661, "opRating": 338}, {"opponent": "jumpluff_shadow", "rating": 623}, {"opponent": "talonflame", "rating": 535}], "counters": [{"opponent": "magcargo", "rating": 115}, {"opponent": "clodsire", "rating": 221}, {"opponent": "gligar", "rating": 232}, {"opponent": "cradily", "rating": 253}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43354}, {"moveId": "BITE", "uses": 14946}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 21937}, {"moveId": "CRUNCH", "uses": 18358}, {"moveId": "FLAMETHROWER", "uses": 18030}]}, "moveset": ["EMBER", "ROCK_SLIDE", "FLAMETHROWER"], "score": 71.6}, {"speciesId": "heatmor", "speciesName": "Heatmor", "rating": 575, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "piloswine", "rating": 817, "opRating": 182}, {"opponent": "fletchinder", "rating": 772, "opRating": 227}, {"opponent": "talonflame", "rating": 753}, {"opponent": "pidgeot", "rating": 638, "opRating": 361}], "counters": [{"opponent": "clodsire", "rating": 218}, {"opponent": "gligar", "rating": 248}, {"opponent": "furret", "rating": 278}, {"opponent": "cradily", "rating": 298}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38186}, {"moveId": "LICK", "uses": 20114}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 27183}, {"moveId": "THUNDER_PUNCH", "uses": 18885}, {"moveId": "POWER_UP_PUNCH", "uses": 12254}]}, "moveset": ["FIRE_SPIN", "THUNDER_PUNCH", "FLAMETHROWER"], "score": 71.4}, {"speciesId": "bouffalant", "speciesName": "Bouffalant", "rating": 572, "matchups": [{"opponent": "lileep_shadow", "rating": 777, "opRating": 222}, {"opponent": "oranguru", "rating": 711, "opRating": 288}, {"opponent": "typhlosion_shadow", "rating": 675, "opRating": 324}, {"opponent": "abomasnow_shadow", "rating": 602, "opRating": 397}, {"opponent": "swampert_shadow", "rating": 543, "opRating": 456}], "counters": [{"opponent": "talonflame", "rating": 255}, {"opponent": "jumpluff_shadow", "rating": 264}, {"opponent": "gligar", "rating": 335}, {"opponent": "furret", "rating": 368}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 47802}, {"moveId": "ZEN_HEADBUTT", "uses": 10498}], "chargedMoves": [{"moveId": "STOMP", "uses": 20870}, {"moveId": "MEGAHORN", "uses": 18465}, {"moveId": "EARTHQUAKE", "uses": 11816}, {"moveId": "SKULL_BASH", "uses": 7261}]}, "moveset": ["MUD_SHOT", "STOMP", "MEGAHORN"], "score": 71.3}, {"speciesId": "cacturne_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 567, "matchups": [{"opponent": "oranguru", "rating": 822, "opRating": 177}, {"opponent": "swampert_shadow", "rating": 789, "opRating": 210}, {"opponent": "ursaring_shadow", "rating": 772, "opRating": 227}, {"opponent": "claydol", "rating": 735, "opRating": 264}, {"opponent": "clodsire", "rating": 619, "opRating": 380}], "counters": [{"opponent": "gligar", "rating": 217}, {"opponent": "talonflame", "rating": 259}, {"opponent": "jumpluff_shadow", "rating": 267}, {"opponent": "cradily", "rating": 319}, {"opponent": "furret", "rating": 375}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 26177}, {"moveId": "SAND_ATTACK", "uses": 16543}, {"moveId": "POISON_JAB", "uses": 15539}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 18848}, {"moveId": "TRAILBLAZE", "uses": 14423}, {"moveId": "DARK_PULSE", "uses": 13174}, {"moveId": "PAYBACK", "uses": 5907}, {"moveId": "GRASS_KNOT", "uses": 5774}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DYNAMIC_PUNCH"], "score": 71.3}, {"speciesId": "drilbur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 521, "matchups": [{"opponent": "magcargo", "rating": 725, "opRating": 274}, {"opponent": "skeledirge", "rating": 700, "opRating": 299}, {"opponent": "ninetales_shadow", "rating": 658, "opRating": 341}, {"opponent": "claydol", "rating": 521, "opRating": 478}, {"opponent": "clodsire", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 270}, {"opponent": "furret", "rating": 331}, {"opponent": "cradily", "rating": 336}, {"opponent": "gligar", "rating": 381}, {"opponent": "jumpluff_shadow", "rating": 418}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 45456}, {"moveId": "SCRATCH", "uses": 12844}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 27213}, {"moveId": "ROCK_TOMB", "uses": 24110}, {"moveId": "DIG", "uses": 6837}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_TOMB"], "score": 71.3}, {"speciesId": "cacturne", "speciesName": "Cacturne", "rating": 554, "matchups": [{"opponent": "oranguru", "rating": 851, "opRating": 148}, {"opponent": "claydol", "rating": 731, "opRating": 268}, {"opponent": "cradily", "rating": 566}, {"opponent": "skeledirge", "rating": 549, "opRating": 450}, {"opponent": "furret", "rating": 504}], "counters": [{"opponent": "gligar", "rating": 221}, {"opponent": "magcargo", "rating": 235}, {"opponent": "jumpluff_shadow", "rating": 245}, {"opponent": "talonflame", "rating": 303}, {"opponent": "clodsire", "rating": 432}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 25573}, {"moveId": "SAND_ATTACK", "uses": 16999}, {"moveId": "POISON_JAB", "uses": 15710}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 17256}, {"moveId": "TRAILBLAZE", "uses": 13467}, {"moveId": "DARK_PULSE", "uses": 11939}, {"moveId": "PAYBACK", "uses": 5396}, {"moveId": "GRASS_KNOT", "uses": 5343}, {"moveId": "RETURN", "uses": 4861}]}, "moveset": ["SUCKER_PUNCH", "TRAILBLAZE", "DYNAMIC_PUNCH"], "score": 71.1}, {"speciesId": "leafeon", "speciesName": "Leafeon", "rating": 551, "matchups": [{"opponent": "quagsire_shadow", "rating": 906, "opRating": 93}, {"opponent": "swampert_shadow", "rating": 862, "opRating": 137}, {"opponent": "claydol", "rating": 750, "opRating": 250}, {"opponent": "piloswine", "rating": 730, "opRating": 269}, {"opponent": "diggersby", "rating": 568, "opRating": 431}], "counters": [{"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "cradily", "rating": 225}, {"opponent": "gligar", "rating": 305}, {"opponent": "furret", "rating": 375}, {"opponent": "talonflame", "rating": 425}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 23881}, {"moveId": "BULLET_SEED", "uses": 23653}, {"moveId": "RAZOR_LEAF", "uses": 10710}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 38276}, {"moveId": "LAST_RESORT", "uses": 11445}, {"moveId": "ENERGY_BALL", "uses": 5378}, {"moveId": "SOLAR_BEAM", "uses": 3169}]}, "moveset": ["QUICK_ATTACK", "LEAF_BLADE", "LAST_RESORT"], "score": 71.1}, {"speciesId": "meganium", "speciesName": "Meganium", "rating": 539, "matchups": [{"opponent": "quagsire_shadow", "rating": 904, "opRating": 95}, {"opponent": "swampert_shadow", "rating": 881, "opRating": 118}, {"opponent": "claydol", "rating": 843, "opRating": 156}, {"opponent": "diggersby", "rating": 660, "opRating": 339}, {"opponent": "clodsire", "rating": 564, "opRating": 435}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 203}, {"opponent": "cradily", "rating": 375}, {"opponent": "gligar", "rating": 381}, {"opponent": "furret", "rating": 493}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 27037}, {"moveId": "MAGICAL_LEAF", "uses": 21029}, {"moveId": "RAZOR_LEAF", "uses": 10261}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 31135}, {"moveId": "EARTHQUAKE", "uses": 9818}, {"moveId": "RETURN", "uses": 9528}, {"moveId": "PETAL_BLIZZARD", "uses": 4337}, {"moveId": "SOLAR_BEAM", "uses": 3546}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 71.1}, {"speciesId": "pumpkaboo_large", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Large)", "rating": 553, "matchups": [{"opponent": "claydol", "rating": 793, "opRating": 206}, {"opponent": "quagsire_shadow", "rating": 729, "opRating": 270}, {"opponent": "swampert_shadow", "rating": 639, "opRating": 360}, {"opponent": "gliscor", "rating": 533, "opRating": 466}, {"opponent": "gligar", "rating": 526}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "talonflame", "rating": 262}, {"opponent": "cradily", "rating": 434}, {"opponent": "clodsire", "rating": 444}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38097}, {"moveId": "RAZOR_LEAF", "uses": 20203}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23590}, {"moveId": "FOUL_PLAY", "uses": 23240}, {"moveId": "SHADOW_SNEAK", "uses": 11409}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 71.1}, {"speciesId": "swellow_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 559, "matchups": [{"opponent": "claydol", "rating": 809, "opRating": 190}, {"opponent": "diggersby", "rating": 541, "opRating": 458}, {"opponent": "gligar", "rating": 533}, {"opponent": "talonflame", "rating": 520}, {"opponent": "gliscor", "rating": 516, "opRating": 483}], "counters": [{"opponent": "clodsire", "rating": 189}, {"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 221}, {"opponent": "magcargo", "rating": 256}, {"opponent": "jumpluff_shadow", "rating": 290}], "moves": {"fastMoves": [{"moveId": "WING_ATTACK", "uses": 36916}, {"moveId": "STEEL_WING", "uses": 21384}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 31372}, {"moveId": "AERIAL_ACE", "uses": 20234}, {"moveId": "SKY_ATTACK", "uses": 6836}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["WING_ATTACK", "BRAVE_BIRD", "AERIAL_ACE"], "score": 71.1}, {"speciesId": "monferno", "speciesName": "Monferno", "rating": 578, "matchups": [{"opponent": "abomasnow_shadow", "rating": 895, "opRating": 104}, {"opponent": "piloswine", "rating": 694, "opRating": 305}, {"opponent": "farfetchd", "rating": 651, "opRating": 348}, {"opponent": "furret", "rating": 622}, {"opponent": "gligar", "rating": 615}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "clodsire", "rating": 204}, {"opponent": "magcargo", "rating": 277}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "cradily", "rating": 420}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 40962}, {"moveId": "ROCK_SMASH", "uses": 17338}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 22183}, {"moveId": "LOW_SWEEP", "uses": 19844}, {"moveId": "RETURN", "uses": 11269}, {"moveId": "FLAME_WHEEL", "uses": 4941}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 71}, {"speciesId": "meowscarada", "speciesName": "Meowscarada", "rating": 535, "matchups": [{"opponent": "swampert_shadow", "rating": 904, "opRating": 95}, {"opponent": "quagsire_shadow", "rating": 891, "opRating": 108}, {"opponent": "claydol", "rating": 782, "opRating": 217}, {"opponent": "furret", "rating": 586}, {"opponent": "diggersby", "rating": 539, "opRating": 460}], "counters": [{"opponent": "jumpluff_shadow", "rating": 62}, {"opponent": "gligar", "rating": 175}, {"opponent": "talonflame", "rating": 281}, {"opponent": "clodsire", "rating": 411}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 40598}, {"moveId": "CHARM", "uses": 17702}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 21343}, {"moveId": "FRENZY_PLANT", "uses": 17625}, {"moveId": "FLOWER_TRICK", "uses": 6971}, {"moveId": "GRASS_KNOT", "uses": 4716}, {"moveId": "PLAY_ROUGH", "uses": 4295}, {"moveId": "ENERGY_BALL", "uses": 3423}]}, "moveset": ["LEAFAGE", "NIGHT_SLASH", "FRENZY_PLANT"], "score": 70.8}, {"speciesId": "monferno_shadow", "speciesName": "Mon<PERSON> (Shadow)", "rating": 585, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "lura<PERSON>s", "rating": 866, "opRating": 133}, {"opponent": "piloswine", "rating": 852, "opRating": 147}, {"opponent": "roserade", "rating": 845, "opRating": 154}, {"opponent": "furret", "rating": 575}], "counters": [{"opponent": "gligar", "rating": 255}, {"opponent": "cradily", "rating": 277}, {"opponent": "talonflame", "rating": 325}, {"opponent": "jumpluff_shadow", "rating": 398}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41086}, {"moveId": "ROCK_SMASH", "uses": 17214}], "chargedMoves": [{"moveId": "FLAMETHROWER", "uses": 27328}, {"moveId": "LOW_SWEEP", "uses": 24836}, {"moveId": "FLAME_WHEEL", "uses": 6078}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAMETHROWER", "LOW_SWEEP"], "score": 70.8}, {"speciesId": "ferrothorn", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 535, "matchups": [{"opponent": "swampert_shadow", "rating": 660, "opRating": 339}, {"opponent": "cradily", "rating": 613}, {"opponent": "flygon", "rating": 554, "opRating": 445}, {"opponent": "furret", "rating": 550}, {"opponent": "claydol", "rating": 511, "opRating": 488}], "counters": [{"opponent": "magcargo", "rating": 94}, {"opponent": "jumpluff_shadow", "rating": 143}, {"opponent": "talonflame", "rating": 233}, {"opponent": "gligar", "rating": 328}, {"opponent": "clodsire", "rating": 439}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30332}, {"moveId": "METAL_CLAW", "uses": 27968}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18592}, {"moveId": "MIRROR_SHOT", "uses": 14336}, {"moveId": "THUNDER", "uses": 7522}, {"moveId": "FLASH_CANNON", "uses": 7415}, {"moveId": "RETURN", "uses": 7413}, {"moveId": "ACID_SPRAY", "uses": 3042}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "FLASH_CANNON"], "score": 70.7}, {"speciesId": "ho_oh_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 560, "matchups": [{"opponent": "abomasnow_shadow", "rating": 864, "opRating": 135}, {"opponent": "claydol", "rating": 732, "opRating": 267}, {"opponent": "gligar", "rating": 671}, {"opponent": "gliscor", "rating": 657, "opRating": 342}, {"opponent": "diggersby", "rating": 635, "opRating": 364}], "counters": [{"opponent": "clodsire", "rating": 242}, {"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 277}, {"opponent": "furret", "rating": 346}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 15428}, {"moveId": "EXTRASENSORY", "uses": 3991}, {"moveId": "STEEL_WING", "uses": 3544}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3161}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3074}, {"moveId": "HIDDEN_POWER_WATER", "uses": 2989}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2550}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2548}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 2389}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2280}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2272}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2045}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 1981}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 1967}, {"moveId": "HIDDEN_POWER_BUG", "uses": 1861}, {"moveId": "HIDDEN_POWER_POISON", "uses": 1705}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 1693}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 1603}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1406}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 27138}, {"moveId": "SACRED_FIRE", "uses": 14604}, {"moveId": "EARTHQUAKE", "uses": 7692}, {"moveId": "SOLAR_BEAM", "uses": 5847}, {"moveId": "FIRE_BLAST", "uses": 2957}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "BRAVE_BIRD", "EARTHQUAKE"], "score": 70.5}, {"speciesId": "leavanny", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 571, "matchups": [{"opponent": "swampert_shadow", "rating": 921, "opRating": 78}, {"opponent": "quagsire_shadow", "rating": 921, "opRating": 78}, {"opponent": "claydol", "rating": 830, "opRating": 169}, {"opponent": "piloswine", "rating": 735, "opRating": 264}, {"opponent": "flygon", "rating": 599, "opRating": 400}], "counters": [{"opponent": "jumpluff_shadow", "rating": 120}, {"opponent": "talonflame", "rating": 185}, {"opponent": "furret", "rating": 296}, {"opponent": "gligar", "rating": 343}, {"opponent": "cradily", "rating": 357}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 25564}, {"moveId": "BUG_BITE", "uses": 23472}, {"moveId": "RAZOR_LEAF", "uses": 9270}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30257}, {"moveId": "X_SCISSOR", "uses": 18644}, {"moveId": "SILVER_WIND", "uses": 5517}, {"moveId": "LEAF_STORM", "uses": 3708}]}, "moveset": ["SHADOW_CLAW", "LEAF_BLADE", "X_SCISSOR"], "score": 70.2}, {"speciesId": "drilbur", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 511, "matchups": [{"opponent": "typhlosion_shadow", "rating": 778, "opRating": 221}, {"opponent": "magcargo", "rating": 764, "opRating": 235}, {"opponent": "skeledirge", "rating": 742, "opRating": 257}, {"opponent": "clodsire", "rating": 735, "opRating": 264}, {"opponent": "ninetales_shadow", "rating": 658, "opRating": 341}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "cradily", "rating": 288}, {"opponent": "gligar", "rating": 328}, {"opponent": "furret", "rating": 328}, {"opponent": "jumpluff_shadow", "rating": 369}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 44263}, {"moveId": "SCRATCH", "uses": 14037}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 24029}, {"moveId": "ROCK_TOMB", "uses": 20955}, {"moveId": "RETURN", "uses": 7250}, {"moveId": "DIG", "uses": 6090}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_TOMB"], "score": 70.1}, {"speciesId": "moltres_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 572, "matchups": [{"opponent": "abomasnow_shadow", "rating": 847, "opRating": 152}, {"opponent": "piloswine", "rating": 741, "opRating": 258}, {"opponent": "claydol", "rating": 656, "opRating": 343}, {"opponent": "gligar", "rating": 644}, {"opponent": "gliscor", "rating": 516, "opRating": 483}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "clodsire", "rating": 262}, {"opponent": "furret", "rating": 309}, {"opponent": "cradily", "rating": 319}, {"opponent": "jumpluff_shadow", "rating": 415}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31270}, {"moveId": "WING_ATTACK", "uses": 27030}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18467}, {"moveId": "ANCIENT_POWER", "uses": 16549}, {"moveId": "OVERHEAT", "uses": 15903}, {"moveId": "FIRE_BLAST", "uses": 4682}, {"moveId": "HEAT_WAVE", "uses": 2689}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 70.1}, {"speciesId": "quilladin", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 533, "matchups": [{"opponent": "quagsire_shadow", "rating": 911, "opRating": 88}, {"opponent": "swampert_shadow", "rating": 892, "opRating": 107}, {"opponent": "claydol", "rating": 823, "opRating": 176}, {"opponent": "diggersby", "rating": 657, "opRating": 342}, {"opponent": "clodsire", "rating": 519, "opRating": 480}], "counters": [{"opponent": "cradily", "rating": 163}, {"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "gligar", "rating": 187}, {"opponent": "talonflame", "rating": 340}, {"opponent": "furret", "rating": 478}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45927}, {"moveId": "LOW_KICK", "uses": 12373}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28491}, {"moveId": "ENERGY_BALL", "uses": 21018}, {"moveId": "GYRO_BALL", "uses": 8794}]}, "moveset": ["VINE_WHIP", "BODY_SLAM", "ENERGY_BALL"], "score": 70.1}, {"speciesId": "shiftry_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 554, "matchups": [{"opponent": "quagsire_shadow", "rating": 884, "opRating": 115}, {"opponent": "swampert_shadow", "rating": 870, "opRating": 129}, {"opponent": "claydol", "rating": 681, "opRating": 318}, {"opponent": "drampa", "rating": 555, "opRating": 444}, {"opponent": "skeledirge", "rating": 555, "opRating": 444}], "counters": [{"opponent": "gligar", "rating": 99}, {"opponent": "jumpluff_shadow", "rating": 189}, {"opponent": "cradily", "rating": 309}, {"opponent": "talonflame", "rating": 359}, {"opponent": "furret", "rating": 378}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 19744}, {"moveId": "BULLET_SEED", "uses": 19164}, {"moveId": "FEINT_ATTACK", "uses": 13089}, {"moveId": "RAZOR_LEAF", "uses": 6283}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 27111}, {"moveId": "FOUL_PLAY", "uses": 17529}, {"moveId": "HURRICANE", "uses": 8453}, {"moveId": "LEAF_TORNADO", "uses": 5184}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "LEAF_BLADE", "FOUL_PLAY"], "score": 70.1}, {"speciesId": "charm<PERSON>on_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 599, "matchups": [{"opponent": "abomasnow_shadow", "rating": 902, "opRating": 97}, {"opponent": "roserade", "rating": 859, "opRating": 140}, {"opponent": "piloswine", "rating": 839, "opRating": 160}, {"opponent": "pidgeot", "rating": 621, "opRating": 378}, {"opponent": "gliscor", "rating": 593, "opRating": 406}], "counters": [{"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 303}, {"opponent": "talonflame", "rating": 374}, {"opponent": "jumpluff_shadow", "rating": 398}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25766}, {"moveId": "FIRE_FANG", "uses": 22528}, {"moveId": "SCRATCH", "uses": 9979}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 38635}, {"moveId": "FLAMETHROWER", "uses": 12220}, {"moveId": "FLAME_BURST", "uses": 7518}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["EMBER", "FIRE_PUNCH", "FLAMETHROWER"], "score": 70}, {"speciesId": "decid<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 550, "matchups": [{"opponent": "claydol", "rating": 794, "opRating": 205}, {"opponent": "swampert_shadow", "rating": 596, "opRating": 403}, {"opponent": "skeledirge", "rating": 542, "opRating": 457}, {"opponent": "clodsire", "rating": 529, "opRating": 470}, {"opponent": "quagsire_shadow", "rating": 525, "opRating": 474}], "counters": [{"opponent": "furret", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 232}, {"opponent": "talonflame", "rating": 288}, {"opponent": "gligar", "rating": 312}, {"opponent": "cradily", "rating": 444}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 19976}, {"moveId": "LEAFAGE", "uses": 16809}, {"moveId": "MAGICAL_LEAF", "uses": 15636}, {"moveId": "RAZOR_LEAF", "uses": 5878}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 21923}, {"moveId": "BRAVE_BIRD", "uses": 19207}, {"moveId": "SPIRIT_SHACKLE", "uses": 10231}, {"moveId": "ENERGY_BALL", "uses": 4205}, {"moveId": "SHADOW_SNEAK", "uses": 2695}]}, "moveset": ["ASTONISH", "FRENZY_PLANT", "SPIRIT_SHACKLE"], "score": 70}, {"speciesId": "linoone_galarian_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>) (<PERSON>)", "rating": 546, "matchups": [{"opponent": "magcargo", "rating": 750, "opRating": 250}, {"opponent": "skeledirge", "rating": 724, "opRating": 275}, {"opponent": "marowak_alolan_shadow", "rating": 701, "opRating": 298}, {"opponent": "oranguru", "rating": 694, "opRating": 305}, {"opponent": "typhlosion_shadow", "rating": 646, "opRating": 353}], "counters": [{"opponent": "gligar", "rating": 251}, {"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "cradily", "rating": 253}, {"opponent": "talonflame", "rating": 329}, {"opponent": "furret", "rating": 378}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36909}, {"moveId": "LICK", "uses": 21391}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 34190}, {"moveId": "DIG", "uses": 15276}, {"moveId": "GUNK_SHOT", "uses": 8784}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 69.8}, {"speciesId": "moltres", "speciesName": "Moltres", "rating": 580, "matchups": [{"opponent": "abomasnow_shadow", "rating": 843, "opRating": 156}, {"opponent": "gligar", "rating": 673}, {"opponent": "gliscor", "rating": 673, "opRating": 326}, {"opponent": "gligar_shadow", "rating": 648, "opRating": 351}, {"opponent": "skeledirge", "rating": 567, "opRating": 432}], "counters": [{"opponent": "talonflame", "rating": 211}, {"opponent": "clodsire", "rating": 218}, {"opponent": "cradily", "rating": 256}, {"opponent": "furret", "rating": 303}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31086}, {"moveId": "WING_ATTACK", "uses": 27214}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 18496}, {"moveId": "ANCIENT_POWER", "uses": 16550}, {"moveId": "OVERHEAT", "uses": 15910}, {"moveId": "FIRE_BLAST", "uses": 4683}, {"moveId": "HEAT_WAVE", "uses": 2693}]}, "moveset": ["FIRE_SPIN", "SKY_ATTACK", "ANCIENT_POWER"], "score": 69.8}, {"speciesId": "staraptor_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 559, "matchups": [{"opponent": "diggersby", "rating": 576, "opRating": 424}, {"opponent": "magcargo", "rating": 564, "opRating": 436}, {"opponent": "quagsire_shadow", "rating": 560, "opRating": 440}, {"opponent": "gligar", "rating": 556}, {"opponent": "gliscor", "rating": 540, "opRating": 460}], "counters": [{"opponent": "cradily", "rating": 138}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "furret", "rating": 221}, {"opponent": "talonflame", "rating": 307}, {"opponent": "clodsire", "rating": 387}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 17261}, {"moveId": "SAND_ATTACK", "uses": 14404}, {"moveId": "GUST", "uses": 13624}, {"moveId": "WING_ATTACK", "uses": 12988}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 19996}, {"moveId": "CLOSE_COMBAT", "uses": 19513}, {"moveId": "FLY", "uses": 16965}, {"moveId": "HEAT_WAVE", "uses": 1941}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "FLY", "CLOSE_COMBAT"], "score": 69.8}, {"speciesId": "breloom", "speciesName": "B<PERSON><PERSON>", "rating": 540, "matchups": [{"opponent": "furret", "rating": 800}, {"opponent": "drampa", "rating": 679, "opRating": 320}, {"opponent": "claydol", "rating": 655, "opRating": 344}, {"opponent": "quagsire_shadow", "rating": 621, "opRating": 378}, {"opponent": "cradily", "rating": 553}], "counters": [{"opponent": "jumpluff_shadow", "rating": 160}, {"opponent": "talonflame", "rating": 170}, {"opponent": "clodsire", "rating": 175}, {"opponent": "gligar", "rating": 190}, {"opponent": "magcargo", "rating": 414}], "moves": {"fastMoves": [{"moveId": "FORCE_PALM", "uses": 23562}, {"moveId": "BULLET_SEED", "uses": 18100}, {"moveId": "COUNTER", "uses": 16619}], "chargedMoves": [{"moveId": "DYNAMIC_PUNCH", "uses": 25797}, {"moveId": "GRASS_KNOT", "uses": 12377}, {"moveId": "SLUDGE_BOMB", "uses": 10267}, {"moveId": "SEED_BOMB", "uses": 9905}]}, "moveset": ["FORCE_PALM", "DYNAMIC_PUNCH", "GRASS_KNOT"], "score": 69.7}, {"speciesId": "charmeleon", "speciesName": "Charmeleon", "rating": 582, "matchups": [{"opponent": "abomasnow_shadow", "rating": 886, "opRating": 113}, {"opponent": "tropius", "rating": 777, "opRating": 222}, {"opponent": "piloswine", "rating": 726, "opRating": 273}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 613, "opRating": 386}, {"opponent": "jumpluff_shadow", "rating": 554}], "counters": [{"opponent": "gligar", "rating": 232}, {"opponent": "clodsire", "rating": 295}, {"opponent": "talonflame", "rating": 303}, {"opponent": "cradily", "rating": 309}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 25743}, {"moveId": "FIRE_FANG", "uses": 22558}, {"moveId": "SCRATCH", "uses": 9987}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 31025}, {"moveId": "RETURN", "uses": 11684}, {"moveId": "FLAMETHROWER", "uses": 9678}, {"moveId": "FLAME_BURST", "uses": 5861}]}, "moveset": ["EMBER", "FIRE_PUNCH", "RETURN"], "score": 69.7}, {"speciesId": "arcanine_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>uian)", "rating": 553, "matchups": [{"opponent": "piloswine", "rating": 879, "opRating": 120}, {"opponent": "ninetales_shadow", "rating": 740, "opRating": 259}, {"opponent": "jumpluff_shadow", "rating": 658}, {"opponent": "pidgeot", "rating": 624, "opRating": 375}, {"opponent": "talonflame", "rating": 507}], "counters": [{"opponent": "magcargo", "rating": 149}, {"opponent": "clodsire", "rating": 237}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 303}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 25413}, {"moveId": "SNARL", "uses": 24283}, {"moveId": "ROCK_SMASH", "uses": 8636}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 15785}, {"moveId": "FLAMETHROWER", "uses": 14472}, {"moveId": "CRUNCH", "uses": 14298}, {"moveId": "WILD_CHARGE", "uses": 13749}]}, "moveset": ["FIRE_FANG", "WILD_CHARGE", "ROCK_SLIDE"], "score": 69.5}, {"speciesId": "ceruledge", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 604, "matchups": [{"opponent": "dubwool", "rating": 919, "opRating": 80}, {"opponent": "lura<PERSON>s", "rating": 905, "opRating": 94}, {"opponent": "abomasnow_shadow", "rating": 882, "opRating": 117}, {"opponent": "piloswine", "rating": 679, "opRating": 320}, {"opponent": "gliscor", "rating": 669, "opRating": 330}], "counters": [{"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 260}, {"opponent": "furret", "rating": 265}, {"opponent": "jumpluff_shadow", "rating": 356}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 34902}, {"moveId": "EMBER", "uses": 23398}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23923}, {"moveId": "SHADOW_BALL", "uses": 21173}, {"moveId": "FLAMETHROWER", "uses": 9922}, {"moveId": "HEAT_WAVE", "uses": 3201}]}, "moveset": ["INCINERATE", "SHADOW_BALL", "FLAME_CHARGE"], "score": 69.4}, {"speciesId": "cu<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 512, "matchups": [{"opponent": "clodsire", "rating": 748, "opRating": 251}, {"opponent": "magcargo", "rating": 744, "opRating": 255}, {"opponent": "skeledirge", "rating": 728, "opRating": 271}, {"opponent": "typhlosion_shadow", "rating": 724, "opRating": 275}, {"opponent": "drampa", "rating": 649, "opRating": 350}], "counters": [{"opponent": "jumpluff_shadow", "rating": 84}, {"opponent": "gligar", "rating": 167}, {"opponent": "cradily", "rating": 295}, {"opponent": "talonflame", "rating": 370}, {"opponent": "furret", "rating": 390}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43508}, {"moveId": "ROCK_SMASH", "uses": 14792}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 43143}, {"moveId": "DIG", "uses": 8250}, {"moveId": "BULLDOZE", "uses": 6717}, {"moveId": "FRUSTRATION", "uses": 79}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "DIG"], "score": 69.4}, {"speciesId": "da<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 589, "matchups": [{"opponent": "abomasnow_shadow", "rating": 901, "opRating": 98}, {"opponent": "roserade", "rating": 863, "opRating": 136}, {"opponent": "piloswine", "rating": 837, "opRating": 162}, {"opponent": "lura<PERSON>s", "rating": 834, "opRating": 165}, {"opponent": "zangoose", "rating": 805, "opRating": 194}], "counters": [{"opponent": "talonflame", "rating": 292}, {"opponent": "furret", "rating": 315}, {"opponent": "cradily", "rating": 322}, {"opponent": "gligar", "rating": 324}, {"opponent": "jumpluff_shadow", "rating": 490}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 36802}, {"moveId": "TACKLE", "uses": 21498}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 42357}, {"moveId": "FLAME_CHARGE", "uses": 15827}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 69.4}, {"speciesId": "onix_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 487, "matchups": [{"opponent": "talonflame", "rating": 895}, {"opponent": "typhlosion_shadow", "rating": 866, "opRating": 133}, {"opponent": "pidgeot", "rating": 723, "opRating": 276}, {"opponent": "magcargo", "rating": 671, "opRating": 328}, {"opponent": "jumpluff_shadow", "rating": 609}], "counters": [{"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 228}, {"opponent": "diggersby", "rating": 235}, {"opponent": "gligar", "rating": 251}, {"opponent": "clodsire", "rating": 338}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 32960}, {"moveId": "TACKLE", "uses": 25340}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 17681}, {"moveId": "STONE_EDGE", "uses": 13301}, {"moveId": "ROCK_SLIDE", "uses": 12706}, {"moveId": "HEAVY_SLAM", "uses": 6056}, {"moveId": "SAND_TOMB", "uses": 5404}, {"moveId": "IRON_HEAD", "uses": 3101}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ROCK_THROW", "BREAKING_SWIPE", "STONE_EDGE"], "score": 69.4}, {"speciesId": "blaziken", "speciesName": "Blaziken", "rating": 545, "matchups": [{"opponent": "abomasnow_shadow", "rating": 861, "opRating": 138}, {"opponent": "piloswine", "rating": 827, "opRating": 172}, {"opponent": "magcargo", "rating": 743, "opRating": 256}, {"opponent": "drampa", "rating": 659, "opRating": 340}, {"opponent": "furret", "rating": 554}], "counters": [{"opponent": "clodsire", "rating": 158}, {"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "gligar", "rating": 187}, {"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 458}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30564}, {"moveId": "COUNTER", "uses": 27736}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 14742}, {"moveId": "BRAVE_BIRD", "uses": 12604}, {"moveId": "BLAZE_KICK", "uses": 10821}, {"moveId": "STONE_EDGE", "uses": 8793}, {"moveId": "FOCUS_BLAST", "uses": 8273}, {"moveId": "OVERHEAT", "uses": 3076}]}, "moveset": ["COUNTER", "BLAZE_KICK", "STONE_EDGE"], "score": 69.2}, {"speciesId": "growlithe", "speciesName": "Grow<PERSON>he", "rating": 594, "matchups": [{"opponent": "abomasnow_shadow", "rating": 892, "opRating": 107}, {"opponent": "piloswine", "rating": 848, "opRating": 151}, {"opponent": "serperior_shadow", "rating": 848, "opRating": 151}, {"opponent": "gliscor", "rating": 600, "opRating": 400}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 566, "opRating": 433}], "counters": [{"opponent": "cradily", "rating": 208}, {"opponent": "talonflame", "rating": 318}, {"opponent": "gligar", "rating": 324}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "furret", "rating": 390}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 40471}, {"moveId": "BITE", "uses": 17829}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 25389}, {"moveId": "FLAMETHROWER", "uses": 21809}, {"moveId": "RETURN", "uses": 6246}, {"moveId": "FLAME_WHEEL", "uses": 4860}]}, "moveset": ["EMBER", "BODY_SLAM", "FLAMETHROWER"], "score": 69.1}, {"speciesId": "excadrill", "speciesName": "Excadrill", "rating": 507, "matchups": [{"opponent": "clodsire", "rating": 839, "opRating": 160}, {"opponent": "magcargo", "rating": 610, "opRating": 389}, {"opponent": "skeledirge", "rating": 578, "opRating": 421}, {"opponent": "cradily", "rating": 567}, {"opponent": "flygon", "rating": 539, "opRating": 460}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "talonflame", "rating": 151}, {"opponent": "diggersby", "rating": 232}, {"opponent": "furret", "rating": 328}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 22895}, {"moveId": "MUD_SHOT", "uses": 18759}, {"moveId": "METAL_CLAW", "uses": 16656}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20724}, {"moveId": "ROCK_SLIDE", "uses": 13671}, {"moveId": "IRON_HEAD", "uses": 11613}, {"moveId": "SCORCHING_SANDS", "uses": 7850}, {"moveId": "EARTHQUAKE", "uses": 4482}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_SLIDE"], "score": 68.9}, {"speciesId": "swadloon", "speciesName": "Swadloon", "rating": 558, "matchups": [{"opponent": "claydol", "rating": 670, "opRating": 329}, {"opponent": "swampert_shadow", "rating": 611, "opRating": 388}, {"opponent": "furret", "rating": 592}, {"opponent": "flygon", "rating": 533, "opRating": 466}, {"opponent": "quagsire_shadow", "rating": 533, "opRating": 466}], "counters": [{"opponent": "gligar", "rating": 164}, {"opponent": "clodsire", "rating": 165}, {"opponent": "talonflame", "rating": 200}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 40349}, {"moveId": "STRUGGLE_BUG", "uses": 17951}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 20187}, {"moveId": "SILVER_WIND", "uses": 20057}, {"moveId": "BUG_BUZZ", "uses": 17995}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "SILVER_WIND"], "score": 68.8}, {"speciesId": "buneary", "speciesName": "Buneary", "rating": 556, "matchups": [{"opponent": "ursaring_shadow", "rating": 896, "opRating": 103}, {"opponent": "typhlosion_shadow", "rating": 625, "opRating": 374}, {"opponent": "flygon_shadow", "rating": 581, "opRating": 418}, {"opponent": "abomasnow_shadow", "rating": 570, "opRating": 429}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 522, "opRating": 477}], "counters": [{"opponent": "cradily", "rating": 204}, {"opponent": "gligar", "rating": 324}, {"opponent": "furret", "rating": 331}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "talonflame", "rating": 400}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 52907}, {"moveId": "POUND", "uses": 5393}], "chargedMoves": [{"moveId": "SWIFT", "uses": 36649}, {"moveId": "FIRE_PUNCH", "uses": 21651}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "FIRE_PUNCH"], "score": 68.6}, {"speciesId": "stoutland", "speciesName": "Stoutland", "rating": 511, "matchups": [{"opponent": "talonflame", "rating": 724}, {"opponent": "skeledirge", "rating": 708, "opRating": 292}, {"opponent": "fletchinder", "rating": 708, "opRating": 292}, {"opponent": "magcargo", "rating": 696, "opRating": 304}, {"opponent": "farfetchd", "rating": 668, "opRating": 332}], "counters": [{"opponent": "furret", "rating": 159}, {"opponent": "cradily", "rating": 270}, {"opponent": "gligar", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "clodsire", "rating": 478}], "moves": {"fastMoves": [{"moveId": "ICE_FANG", "uses": 23521}, {"moveId": "SAND_ATTACK", "uses": 18755}, {"moveId": "LICK", "uses": 11741}, {"moveId": "TAKE_DOWN", "uses": 4222}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 23896}, {"moveId": "CRUNCH", "uses": 23860}, {"moveId": "PLAY_ROUGH", "uses": 10488}]}, "moveset": ["SAND_ATTACK", "WILD_CHARGE", "CRUNCH"], "score": 68.6}, {"speciesId": "pumpkaboo_average", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Average)", "rating": 533, "matchups": [{"opponent": "quagsire", "rating": 789, "opRating": 210}, {"opponent": "claydol", "rating": 781, "opRating": 218}, {"opponent": "quagsire_shadow", "rating": 718, "opRating": 281}, {"opponent": "swampert_shadow", "rating": 623, "opRating": 376}, {"opponent": "gliscor", "rating": 507, "opRating": 492}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "talonflame", "rating": 262}, {"opponent": "cradily", "rating": 416}, {"opponent": "clodsire", "rating": 447}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38176}, {"moveId": "RAZOR_LEAF", "uses": 20124}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23589}, {"moveId": "FOUL_PLAY", "uses": 23235}, {"moveId": "SHADOW_SNEAK", "uses": 11419}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 68.5}, {"speciesId": "flapple", "speciesName": "Flapple", "rating": 531, "matchups": [{"opponent": "claydol", "rating": 696, "opRating": 303}, {"opponent": "staraptor", "rating": 688, "opRating": 311}, {"opponent": "diggersby", "rating": 576, "opRating": 423}, {"opponent": "flygon", "rating": 547, "opRating": 452}, {"opponent": "drampa", "rating": 517, "opRating": 482}], "counters": [{"opponent": "gligar", "rating": 244}, {"opponent": "jumpluff_shadow", "rating": 251}, {"opponent": "cradily", "rating": 336}, {"opponent": "furret", "rating": 362}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "DRAGON_BREATH", "uses": 31667}, {"moveId": "BULLET_SEED", "uses": 26633}], "chargedMoves": [{"moveId": "FLY", "uses": 22425}, {"moveId": "SEED_BOMB", "uses": 15605}, {"moveId": "OUTRAGE", "uses": 15228}, {"moveId": "DRAGON_PULSE", "uses": 5015}]}, "moveset": ["DRAGON_BREATH", "FLY", "SEED_BOMB"], "score": 68.3}, {"speciesId": "houndoom_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 570, "matchups": [{"opponent": "abomasnow", "rating": 898, "opRating": 101}, {"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "piloswine", "rating": 843, "opRating": 156}, {"opponent": "ursaring_shadow", "rating": 792, "opRating": 207}, {"opponent": "jumpluff_shadow", "rating": 563}], "counters": [{"opponent": "clodsire", "rating": 283}, {"opponent": "talonflame", "rating": 292}, {"opponent": "gligar", "rating": 309}, {"opponent": "cradily", "rating": 322}, {"opponent": "furret", "rating": 409}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 29548}, {"moveId": "FIRE_FANG", "uses": 28752}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 25727}, {"moveId": "FLAMETHROWER", "uses": 16184}, {"moveId": "CRUNCH", "uses": 12028}, {"moveId": "FIRE_BLAST", "uses": 4262}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 68.3}, {"speciesId": "onix", "speciesName": "Onix", "rating": 475, "matchups": [{"opponent": "talonflame", "rating": 880}, {"opponent": "pidgeot", "rating": 771, "opRating": 228}, {"opponent": "magcargo", "rating": 695, "opRating": 304}, {"opponent": "jumpluff_shadow", "rating": 690}, {"opponent": "skeledirge", "rating": 604, "opRating": 395}], "counters": [{"opponent": "cradily", "rating": 163}, {"opponent": "furret", "rating": 206}, {"opponent": "diggersby", "rating": 224}, {"opponent": "clodsire", "rating": 264}, {"opponent": "gligar", "rating": 305}], "moves": {"fastMoves": [{"moveId": "ROCK_THROW", "uses": 31789}, {"moveId": "TACKLE", "uses": 26511}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 16166}, {"moveId": "STONE_EDGE", "uses": 12387}, {"moveId": "ROCK_SLIDE", "uses": 11825}, {"moveId": "HEAVY_SLAM", "uses": 5600}, {"moveId": "SAND_TOMB", "uses": 5011}, {"moveId": "RETURN", "uses": 4517}, {"moveId": "IRON_HEAD", "uses": 2770}]}, "moveset": ["ROCK_THROW", "BREAKING_SWIPE", "STONE_EDGE"], "score": 68.3}, {"speciesId": "sandshrew", "speciesName": "Sandshrew", "rating": 509, "matchups": [{"opponent": "salazzle", "rating": 748, "opRating": 251}, {"opponent": "magcargo", "rating": 704, "opRating": 295}, {"opponent": "clodsire", "rating": 562}, {"opponent": "cradily", "rating": 543}, {"opponent": "ninetales_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "diggersby", "rating": 316}, {"opponent": "furret", "rating": 350}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "gligar", "rating": 458}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24870}, {"moveId": "MUD_SHOT", "uses": 22132}, {"moveId": "SCRATCH", "uses": 11270}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 18832}, {"moveId": "DIG", "uses": 13426}, {"moveId": "ROCK_SLIDE", "uses": 12196}, {"moveId": "RETURN", "uses": 7117}, {"moveId": "SAND_TOMB", "uses": 6690}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 68.3}, {"speciesId": "linoone_galarian", "speciesName": "<PERSON><PERSON><PERSON> (Galarian)", "rating": 535, "matchups": [{"opponent": "armarouge", "rating": 750, "opRating": 250}, {"opponent": "oranguru", "rating": 711, "opRating": 288}, {"opponent": "typhlosion_shadow", "rating": 698, "opRating": 301}, {"opponent": "girafarig", "rating": 694, "opRating": 305}, {"opponent": "typhlosion", "rating": 688, "opRating": 311}], "counters": [{"opponent": "jumpluff_shadow", "rating": 222}, {"opponent": "gligar", "rating": 244}, {"opponent": "cradily", "rating": 319}, {"opponent": "talonflame", "rating": 414}, {"opponent": "furret", "rating": 428}], "moves": {"fastMoves": [{"moveId": "SNARL", "uses": 36595}, {"moveId": "LICK", "uses": 21705}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29680}, {"moveId": "DIG", "uses": 13690}, {"moveId": "GUNK_SHOT", "uses": 7708}, {"moveId": "RETURN", "uses": 7239}]}, "moveset": ["SNARL", "BODY_SLAM", "DIG"], "score": 68.2}, {"speciesId": "exeggutor_alolan", "speciesName": "Exeggutor (Alolan)", "rating": 533, "matchups": [{"opponent": "staraptor", "rating": 744, "opRating": 255}, {"opponent": "claydol", "rating": 729, "opRating": 270}, {"opponent": "diggersby", "rating": 614, "opRating": 385}, {"opponent": "typhlosion_shadow", "rating": 583, "opRating": 416}, {"opponent": "diggersby_shadow", "rating": 526, "opRating": 473}], "counters": [{"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "gligar", "rating": 270}, {"opponent": "furret", "rating": 287}, {"opponent": "cradily", "rating": 288}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 33120}, {"moveId": "BULLET_SEED", "uses": 25180}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 20163}, {"moveId": "DRAGON_PULSE", "uses": 16809}, {"moveId": "DRACO_METEOR", "uses": 14945}, {"moveId": "SOLAR_BEAM", "uses": 6238}]}, "moveset": ["DRAGON_TAIL", "SEED_BOMB", "DRACO_METEOR"], "score": 68}, {"speciesId": "krookodile", "speciesName": "Krookodile", "rating": 521, "matchups": [{"opponent": "clodsire", "rating": 887, "opRating": 112}, {"opponent": "magcargo", "rating": 786, "opRating": 213}, {"opponent": "ninetales_shadow", "rating": 717, "opRating": 282}, {"opponent": "typhlosion_shadow", "rating": 693, "opRating": 306}, {"opponent": "skeledirge", "rating": 686, "opRating": 313}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 110}, {"opponent": "talonflame", "rating": 211}, {"opponent": "cradily", "rating": 270}, {"opponent": "furret", "rating": 303}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 31726}, {"moveId": "SNARL", "uses": 26574}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22242}, {"moveId": "BRICK_BREAK", "uses": 13908}, {"moveId": "EARTHQUAKE", "uses": 11248}, {"moveId": "OUTRAGE", "uses": 10830}]}, "moveset": ["MUD_SLAP", "CRUNCH", "EARTHQUAKE"], "score": 67.9}, {"speciesId": "<PERSON><PERSON><PERSON>_alolan_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Alolan) (Shadow)", "rating": 513, "matchups": [{"opponent": "clodsire", "rating": 927, "opRating": 72}, {"opponent": "flygon", "rating": 644, "opRating": 355}, {"opponent": "flygon_shadow", "rating": 627, "opRating": 372}, {"opponent": "drampa", "rating": 611, "opRating": 388}, {"opponent": "typhlosion_shadow", "rating": 516, "opRating": 483}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 151}, {"opponent": "gligar", "rating": 156}, {"opponent": "furret", "rating": 353}, {"opponent": "cradily", "rating": 482}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23115}, {"moveId": "SAND_ATTACK", "uses": 19176}, {"moveId": "METAL_CLAW", "uses": 16020}], "chargedMoves": [{"moveId": "MUD_BOMB", "uses": 30596}, {"moveId": "IRON_HEAD", "uses": 20273}, {"moveId": "EARTHQUAKE", "uses": 7228}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "MUD_BOMB", "IRON_HEAD"], "score": 67.7}, {"speciesId": "excadrill_shadow", "speciesName": "Excadrill (Shadow)", "rating": 507, "matchups": [{"opponent": "clodsire", "rating": 771, "opRating": 228}, {"opponent": "flygon", "rating": 625, "opRating": 375}, {"opponent": "drampa", "rating": 585, "opRating": 414}, {"opponent": "pidgeot", "rating": 553, "opRating": 446}, {"opponent": "magcargo", "rating": 539, "opRating": 460}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 166}, {"opponent": "cradily", "rating": 350}, {"opponent": "furret", "rating": 353}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23165}, {"moveId": "MUD_SHOT", "uses": 18756}, {"moveId": "METAL_CLAW", "uses": 16375}], "chargedMoves": [{"moveId": "DRILL_RUN", "uses": 20723}, {"moveId": "ROCK_SLIDE", "uses": 13666}, {"moveId": "IRON_HEAD", "uses": 11644}, {"moveId": "SCORCHING_SANDS", "uses": 7840}, {"moveId": "EARTHQUAKE", "uses": 4425}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "DRILL_RUN", "ROCK_SLIDE"], "score": 67.7}, {"speciesId": "ivysaur", "speciesName": "Ivysaur", "rating": 514, "matchups": [{"opponent": "swampert_shadow", "rating": 858, "opRating": 141}, {"opponent": "quagsire_shadow", "rating": 713, "opRating": 286}, {"opponent": "claydol", "rating": 687, "opRating": 312}, {"opponent": "diggersby", "rating": 622, "opRating": 377}, {"opponent": "clodsire", "rating": 580, "opRating": 419}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 333}, {"opponent": "cradily", "rating": 392}, {"opponent": "gligar", "rating": 454}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42318}, {"moveId": "RAZOR_LEAF", "uses": 15982}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 22622}, {"moveId": "SLUDGE_BOMB", "uses": 21796}, {"moveId": "RETURN", "uses": 9123}, {"moveId": "SOLAR_BEAM", "uses": 4745}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 67.7}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Toucannon", "rating": 539, "matchups": [{"opponent": "whiscash_shadow", "rating": 898, "opRating": 101}, {"opponent": "quagsire_shadow", "rating": 670, "opRating": 329}, {"opponent": "gligar", "rating": 552}, {"opponent": "gliscor", "rating": 540, "opRating": 459}, {"opponent": "swampert_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "furret", "rating": 112}, {"opponent": "clodsire", "rating": 221}, {"opponent": "cradily", "rating": 243}, {"opponent": "jumpluff_shadow", "rating": 330}, {"opponent": "talonflame", "rating": 440}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 28439}, {"moveId": "PECK", "uses": 19147}, {"moveId": "ROCK_SMASH", "uses": 10732}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 36467}, {"moveId": "ROCK_BLAST", "uses": 15258}, {"moveId": "FLASH_CANNON", "uses": 6594}]}, "moveset": ["BULLET_SEED", "DRILL_PECK", "ROCK_BLAST"], "score": 67.7}, {"speciesId": "entei_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 573, "matchups": [{"opponent": "abomasnow_shadow", "rating": 906, "opRating": 93}, {"opponent": "lura<PERSON>s", "rating": 874, "opRating": 125}, {"opponent": "roserade", "rating": 866, "opRating": 133}, {"opponent": "piloswine", "rating": 852, "opRating": 147}, {"opponent": "serperior_shadow", "rating": 852, "opRating": 147}], "counters": [{"opponent": "talonflame", "rating": 292}, {"opponent": "cradily", "rating": 298}, {"opponent": "furret", "rating": 321}, {"opponent": "gligar", "rating": 347}, {"opponent": "jumpluff_shadow", "rating": 454}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 31454}, {"moveId": "FIRE_FANG", "uses": 26846}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 15281}, {"moveId": "FLAME_CHARGE", "uses": 14789}, {"moveId": "OVERHEAT", "uses": 11451}, {"moveId": "IRON_HEAD", "uses": 7249}, {"moveId": "FLAMETHROWER", "uses": 6067}, {"moveId": "FIRE_BLAST", "uses": 3334}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 67.6}, {"speciesId": "stufful", "speciesName": "Stufful", "rating": 486, "matchups": [{"opponent": "diggersby", "rating": 657, "opRating": 342}, {"opponent": "furret", "rating": 615}, {"opponent": "quagsire_shadow", "rating": 586, "opRating": 413}, {"opponent": "gligar", "rating": 544}, {"opponent": "cradily", "rating": 528}], "counters": [{"opponent": "magcargo", "rating": 200}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "talonflame", "rating": 292}, {"opponent": "swampert_shadow", "rating": 327}, {"opponent": "clodsire", "rating": 355}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 48556}, {"moveId": "TAKE_DOWN", "uses": 9744}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 31235}, {"moveId": "STOMP", "uses": 18449}, {"moveId": "BRICK_BREAK", "uses": 8594}]}, "moveset": ["TACKLE", "SUPER_POWER", "STOMP"], "score": 67.6}, {"speciesId": "victini", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 575, "matchups": [{"opponent": "piloswine", "rating": 872, "opRating": 127}, {"opponent": "abomasnow_shadow", "rating": 767, "opRating": 232}, {"opponent": "gliscor_shadow", "rating": 763, "opRating": 236}, {"opponent": "jumpluff_shadow", "rating": 717, "opRating": 282}, {"opponent": "flygon_shadow", "rating": 527, "opRating": 472}], "counters": [{"opponent": "cradily", "rating": 93}, {"opponent": "clodsire", "rating": 127}, {"opponent": "furret", "rating": 128}, {"opponent": "gligar", "rating": 366}, {"opponent": "talonflame", "rating": 414}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 30807}, {"moveId": "CONFUSION", "uses": 27493}], "chargedMoves": [{"moveId": "V_CREATE", "uses": 36895}, {"moveId": "PSYCHIC", "uses": 8830}, {"moveId": "FOCUS_BLAST", "uses": 8298}, {"moveId": "OVERHEAT", "uses": 4313}]}, "moveset": ["QUICK_ATTACK", "V_CREATE", "PSYCHIC"], "score": 67.4}, {"speciesId": "zoro<PERSON>_his<PERSON>an", "speciesName": "<PERSON><PERSON><PERSON> (Hisuian)", "rating": 488, "matchups": [{"opponent": "swampert_shadow", "rating": 682, "opRating": 317}, {"opponent": "claydol", "rating": 557, "opRating": 442}, {"opponent": "talonflame", "rating": 536}, {"opponent": "ninetales_shadow", "rating": 526, "opRating": 473}, {"opponent": "skeledirge", "rating": 505, "opRating": 494}], "counters": [{"opponent": "furret", "rating": 87}, {"opponent": "jumpluff_shadow", "rating": 359}, {"opponent": "gligar", "rating": 400}, {"opponent": "clodsire", "rating": 461}, {"opponent": "cradily", "rating": 489}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 33433}, {"moveId": "SNARL", "uses": 24867}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 18732}, {"moveId": "SHADOW_BALL", "uses": 15649}, {"moveId": "SLUDGE_BOMB", "uses": 12048}, {"moveId": "FLAMETHROWER", "uses": 11965}]}, "moveset": ["SHADOW_CLAW", "FOUL_PLAY", "SHADOW_BALL"], "score": 67.4}, {"speciesId": "tranquill", "speciesName": "Tran<PERSON>ll", "rating": 556, "matchups": [{"opponent": "lura<PERSON>s", "rating": 657, "opRating": 342}, {"opponent": "tropius", "rating": 632, "opRating": 367}, {"opponent": "gliscor_shadow", "rating": 601, "opRating": 398}, {"opponent": "swampert_shadow", "rating": 587, "opRating": 412}, {"opponent": "jumpluff_shadow", "rating": 527}], "counters": [{"opponent": "cradily", "rating": 194}, {"opponent": "clodsire", "rating": 194}, {"opponent": "talonflame", "rating": 240}, {"opponent": "gligar", "rating": 374}, {"opponent": "furret", "rating": 443}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32283}, {"moveId": "STEEL_WING", "uses": 26017}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 30317}, {"moveId": "RETURN", "uses": 13323}, {"moveId": "SKY_ATTACK", "uses": 10042}, {"moveId": "HEAT_WAVE", "uses": 4590}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "RETURN"], "score": 67.3}, {"speciesId": "darum<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 579, "matchups": [{"opponent": "abomasnow_shadow", "rating": 901, "opRating": 98}, {"opponent": "roserade", "rating": 843, "opRating": 156}, {"opponent": "piloswine", "rating": 719, "opRating": 280}, {"opponent": "farfetchd", "rating": 665, "opRating": 334}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 592, "opRating": 407}], "counters": [{"opponent": "talonflame", "rating": 255}, {"opponent": "cradily", "rating": 267}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 328}, {"opponent": "jumpluff_shadow", "rating": 496}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 36572}, {"moveId": "TACKLE", "uses": 21728}], "chargedMoves": [{"moveId": "FIRE_PUNCH", "uses": 33326}, {"moveId": "FLAME_CHARGE", "uses": 12569}, {"moveId": "RETURN", "uses": 12449}]}, "moveset": ["FIRE_FANG", "FIRE_PUNCH", "FLAME_CHARGE"], "score": 67}, {"speciesId": "houndoom", "speciesName": "Hound<PERSON>", "rating": 552, "matchups": [{"opponent": "abomasnow_shadow", "rating": 911, "opRating": 88}, {"opponent": "roserade", "rating": 847, "opRating": 152}, {"opponent": "piloswine", "rating": 826, "opRating": 173}, {"opponent": "oranguru", "rating": 733, "opRating": 266}, {"opponent": "furret", "rating": 559}], "counters": [{"opponent": "clodsire", "rating": 252}, {"opponent": "cradily", "rating": 277}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 431}, {"opponent": "talonflame", "rating": 455}], "moves": {"fastMoves": [{"moveId": "FIRE_FANG", "uses": 29474}, {"moveId": "SNARL", "uses": 28826}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 25750}, {"moveId": "FLAMETHROWER", "uses": 16166}, {"moveId": "CRUNCH", "uses": 12061}, {"moveId": "FIRE_BLAST", "uses": 4314}]}, "moveset": ["FIRE_FANG", "FOUL_PLAY", "FLAMETHROWER"], "score": 67}, {"speciesId": "combusken_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 566, "matchups": [{"opponent": "abomasnow_shadow", "rating": 900, "opRating": 99}, {"opponent": "sceptile", "rating": 858, "opRating": 141}, {"opponent": "piloswine", "rating": 667, "opRating": 332}, {"opponent": "ninetales_shadow", "rating": 553, "opRating": 446}, {"opponent": "furret", "rating": 503}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "clodsire", "rating": 286}, {"opponent": "gligar", "rating": 309}, {"opponent": "cradily", "rating": 312}, {"opponent": "jumpluff_shadow", "rating": 336}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 43522}, {"moveId": "PECK", "uses": 14778}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 25791}, {"moveId": "ROCK_SLIDE", "uses": 21647}, {"moveId": "FLAMETHROWER", "uses": 10823}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAME_CHARGE", "ROCK_SLIDE"], "score": 66.8}, {"speciesId": "purugly_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 537, "matchups": [{"opponent": "gourgeist_large", "rating": 917, "opRating": 82}, {"opponent": "marowak_alolan_shadow", "rating": 656, "opRating": 343}, {"opponent": "skeledirge", "rating": 649, "opRating": 350}, {"opponent": "flygon_shadow", "rating": 526, "opRating": 473}, {"opponent": "abomasnow_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "furret", "rating": 268}, {"opponent": "cradily", "rating": 326}, {"opponent": "gligar", "rating": 358}, {"opponent": "jumpluff_shadow", "rating": 382}, {"opponent": "talonflame", "rating": 448}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 36643}, {"moveId": "SCRATCH", "uses": 21657}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 32671}, {"moveId": "PLAY_ROUGH", "uses": 13366}, {"moveId": "THUNDER", "uses": 12163}, {"moveId": "FRUSTRATION", "uses": 2}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 66.8}, {"speciesId": "venusaur_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 522, "matchups": [{"opponent": "swampert_shadow", "rating": 853, "opRating": 146}, {"opponent": "claydol", "rating": 670, "opRating": 329}, {"opponent": "quagsire_shadow", "rating": 666, "opRating": 333}, {"opponent": "diggersby", "rating": 577, "opRating": 422}, {"opponent": "furret", "rating": 528}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "clodsire", "rating": 389}, {"opponent": "talonflame", "rating": 392}, {"opponent": "cradily", "rating": 420}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 45066}, {"moveId": "RAZOR_LEAF", "uses": 13234}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 30463}, {"moveId": "SLUDGE_BOMB", "uses": 20157}, {"moveId": "PETAL_BLIZZARD", "uses": 4227}, {"moveId": "SOLAR_BEAM", "uses": 3537}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "SLUDGE_BOMB"], "score": 66.8}, {"speciesId": "porygon", "speciesName": "Porygon", "rating": 483, "matchups": [{"opponent": "staravia", "rating": 811, "opRating": 188}, {"opponent": "gligar", "rating": 622}, {"opponent": "quagsire_shadow", "rating": 603, "opRating": 396}, {"opponent": "pidgeot", "rating": 525, "opRating": 474}, {"opponent": "jumpluff_shadow", "rating": 518}], "counters": [{"opponent": "magcargo", "rating": 200}, {"opponent": "furret", "rating": 206}, {"opponent": "cradily", "rating": 312}, {"opponent": "talonflame", "rating": 451}, {"opponent": "clodsire", "rating": 478}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 6442}, {"moveId": "TACKLE", "uses": 4501}, {"moveId": "HIDDEN_POWER_ICE", "uses": 3968}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3503}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3034}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3033}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 2980}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2795}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2755}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2718}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2654}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2630}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2594}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2593}, {"moveId": "CHARGE_BEAM", "uses": 2411}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2380}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2368}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2133}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1875}, {"moveId": "ZEN_HEADBUTT", "uses": 1073}], "chargedMoves": [{"moveId": "RETURN", "uses": 11594}, {"moveId": "SIGNAL_BEAM", "uses": 11385}, {"moveId": "DISCHARGE", "uses": 10804}, {"moveId": "SOLAR_BEAM", "uses": 8489}, {"moveId": "ZAP_CANNON", "uses": 5748}, {"moveId": "PSYBEAM", "uses": 5687}, {"moveId": "HYPER_BEAM", "uses": 4516}]}, "moveset": ["QUICK_ATTACK", "DISCHARGE", "HYPER_BEAM"], "score": 66.5}, {"speciesId": "bayleef", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 513, "matchups": [{"opponent": "quagsire_shadow", "rating": 926, "opRating": 73}, {"opponent": "swampert_shadow", "rating": 911, "opRating": 88}, {"opponent": "claydol", "rating": 820, "opRating": 179}, {"opponent": "diggersby", "rating": 686, "opRating": 313}, {"opponent": "flygon", "rating": 552, "opRating": 447}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 232}, {"opponent": "cradily", "rating": 343}, {"opponent": "furret", "rating": 475}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 25026}, {"moveId": "TACKLE", "uses": 19501}, {"moveId": "RAZOR_LEAF", "uses": 13764}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 21633}, {"moveId": "ANCIENT_POWER", "uses": 19768}, {"moveId": "RETURN", "uses": 9094}, {"moveId": "ENERGY_BALL", "uses": 7784}]}, "moveset": ["MAGICAL_LEAF", "ANCIENT_POWER", "ENERGY_BALL"], "score": 66.4}, {"speciesId": "dolliv", "speciesName": "Doll<PERSON>", "rating": 494, "matchups": [{"opponent": "swampert_shadow", "rating": 903, "opRating": 96}, {"opponent": "quagsire_shadow", "rating": 880, "opRating": 119}, {"opponent": "claydol", "rating": 823, "opRating": 176}, {"opponent": "diggersby", "rating": 607, "opRating": 392}, {"opponent": "furret", "rating": 588}], "counters": [{"opponent": "jumpluff_shadow", "rating": 130}, {"opponent": "talonflame", "rating": 203}, {"opponent": "cradily", "rating": 336}, {"opponent": "gligar", "rating": 385}, {"opponent": "clodsire", "rating": 423}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 24329}, {"moveId": "TACKLE", "uses": 21297}, {"moveId": "RAZOR_LEAF", "uses": 12687}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 26408}, {"moveId": "EARTH_POWER", "uses": 15998}, {"moveId": "SEED_BOMB", "uses": 8446}, {"moveId": "ENERGY_BALL", "uses": 7494}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 66.4}, {"speciesId": "krokorok", "speciesName": "Krokorok", "rating": 518, "matchups": [{"opponent": "magcargo", "rating": 785, "opRating": 214}, {"opponent": "ninetales_shadow", "rating": 711, "opRating": 288}, {"opponent": "clodsire", "rating": 704, "opRating": 295}, {"opponent": "typhlosion_shadow", "rating": 690, "opRating": 309}, {"opponent": "skeledirge", "rating": 686, "opRating": 313}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "gligar", "rating": 110}, {"opponent": "talonflame", "rating": 211}, {"opponent": "cradily", "rating": 270}, {"opponent": "furret", "rating": 303}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 38501}, {"moveId": "BITE", "uses": 19799}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 31175}, {"moveId": "EARTHQUAKE", "uses": 14041}, {"moveId": "BULLDOZE", "uses": 13069}]}, "moveset": ["MUD_SLAP", "CRUNCH", "EARTHQUAKE"], "score": 66.4}, {"speciesId": "torkoal", "speciesName": "Torkoal", "rating": 526, "matchups": [{"opponent": "abomasnow_shadow", "rating": 902, "opRating": 97}, {"opponent": "abomasnow", "rating": 824, "opRating": 175}, {"opponent": "ninetales_shadow", "rating": 632, "opRating": 367}, {"opponent": "ninetales", "rating": 632, "opRating": 367}, {"opponent": "skeledirge", "rating": 531, "opRating": 468}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "furret", "rating": 340}, {"opponent": "gligar", "rating": 347}, {"opponent": "jumpluff_shadow", "rating": 421}, {"opponent": "cradily", "rating": 451}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30745}, {"moveId": "FIRE_SPIN", "uses": 27555}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 28563}, {"moveId": "EARTHQUAKE", "uses": 17424}, {"moveId": "SOLAR_BEAM", "uses": 12324}]}, "moveset": ["EMBER", "OVERHEAT", "EARTHQUAKE"], "score": 66.4}, {"speciesId": "grotle_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 483, "matchups": [{"opponent": "quagsire_shadow", "rating": 943, "opRating": 56}, {"opponent": "swampert_shadow", "rating": 929, "opRating": 70}, {"opponent": "claydol", "rating": 797, "opRating": 202}, {"opponent": "piloswine", "rating": 741, "opRating": 258}, {"opponent": "diggersby", "rating": 588, "opRating": 411}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "gligar", "rating": 270}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 343}, {"opponent": "furret", "rating": 440}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30585}, {"moveId": "BITE", "uses": 27715}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 30596}, {"moveId": "ENERGY_BALL", "uses": 21356}, {"moveId": "SOLAR_BEAM", "uses": 6268}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 66.1}, {"speciesId": "lilligant_hisuian", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "rating": 500, "matchups": [{"opponent": "quagsire_shadow", "rating": 735, "opRating": 264}, {"opponent": "furret", "rating": 628}, {"opponent": "flygon", "rating": 606, "opRating": 393}, {"opponent": "magcargo", "rating": 551, "opRating": 448}, {"opponent": "diggersby", "rating": 534, "opRating": 465}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 110}, {"opponent": "clodsire", "rating": 204}, {"opponent": "talonflame", "rating": 418}, {"opponent": "cradily", "rating": 434}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 32337}, {"moveId": "MAGICAL_LEAF", "uses": 25963}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 25354}, {"moveId": "UPPER_HAND", "uses": 23119}, {"moveId": "PETAL_BLIZZARD", "uses": 6825}, {"moveId": "SOLAR_BEAM", "uses": 2777}]}, "moveset": ["BULLET_SEED", "CLOSE_COMBAT", "UPPER_HAND"], "score": 65.9}, {"speciesId": "chandelure", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 562, "matchups": [{"opponent": "piloswine", "rating": 865, "opRating": 134}, {"opponent": "abomasnow_shadow", "rating": 865, "opRating": 134}, {"opponent": "cradily_shadow", "rating": 784, "opRating": 215}, {"opponent": "gliscor", "rating": 602, "opRating": 397}, {"opponent": "ninetales_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "furret", "rating": 218}, {"opponent": "talonflame", "rating": 262}, {"opponent": "cradily", "rating": 277}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 382}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 27392}, {"moveId": "HEX", "uses": 16920}, {"moveId": "FIRE_SPIN", "uses": 13993}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 16637}, {"moveId": "SHADOW_BALL", "uses": 13634}, {"moveId": "OVERHEAT", "uses": 12822}, {"moveId": "ENERGY_BALL", "uses": 10836}, {"moveId": "POLTERGEIST", "uses": 4457}]}, "moveset": ["INCINERATE", "FLAME_CHARGE", "SHADOW_BALL"], "score": 65.8}, {"speciesId": "landorus_incarnate", "speciesName": "Landorus (Incarnate)", "rating": 485, "matchups": [{"opponent": "clodsire", "rating": 881, "opRating": 118}, {"opponent": "marowak_shadow", "rating": 723, "opRating": 276}, {"opponent": "magcargo", "rating": 627, "opRating": 372}, {"opponent": "talonflame", "rating": 578}, {"opponent": "skeledirge", "rating": 578, "opRating": 421}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "furret", "rating": 190}, {"opponent": "gligar", "rating": 274}, {"opponent": "cradily", "rating": 368}, {"opponent": "diggersby", "rating": 376}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 36421}, {"moveId": "ROCK_THROW", "uses": 21879}], "chargedMoves": [{"moveId": "EARTH_POWER", "uses": 17511}, {"moveId": "ROCK_SLIDE", "uses": 16268}, {"moveId": "OUTRAGE", "uses": 12744}, {"moveId": "FOCUS_BLAST", "uses": 11689}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "ROCK_SLIDE"], "score": 65.8}, {"speciesId": "chansey", "speciesName": "<PERSON><PERSON>", "rating": 490, "matchups": [{"opponent": "ursaring", "rating": 633, "opRating": 366}, {"opponent": "flygon_shadow", "rating": 581, "opRating": 418}, {"opponent": "drampa", "rating": 562, "opRating": 437}, {"opponent": "clodsire", "rating": 548}, {"opponent": "gligar", "rating": 530}], "counters": [{"opponent": "cradily", "rating": 250}, {"opponent": "magcargo", "rating": 282}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "talonflame", "rating": 340}, {"opponent": "furret", "rating": 362}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 32758}, {"moveId": "POUND", "uses": 25542}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 19993}, {"moveId": "PSYCHIC", "uses": 16784}, {"moveId": "HYPER_BEAM", "uses": 16591}, {"moveId": "PSYBEAM", "uses": 5008}]}, "moveset": ["ZEN_HEADBUTT", "DAZZLING_GLEAM", "PSYCHIC"], "score": 65.6}, {"speciesId": "emboar", "speciesName": "Emboar", "rating": 548, "matchups": [{"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "sceptile_shadow", "rating": 894, "opRating": 105}, {"opponent": "piloswine", "rating": 697, "opRating": 302}, {"opponent": "ninetales_shadow", "rating": 595, "opRating": 404}, {"opponent": "furret", "rating": 581}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 362}, {"opponent": "clodsire", "rating": 475}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 48821}, {"moveId": "LOW_KICK", "uses": 9479}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24333}, {"moveId": "FOCUS_BLAST", "uses": 12776}, {"moveId": "ROCK_SLIDE", "uses": 12765}, {"moveId": "FLAME_CHARGE", "uses": 6681}, {"moveId": "HEAT_WAVE", "uses": 1898}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 65.6}, {"speciesId": "graveler_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 493, "matchups": [{"opponent": "salazzle", "rating": 939, "opRating": 60}, {"opponent": "magcargo", "rating": 865, "opRating": 134}, {"opponent": "skeledirge", "rating": 852, "opRating": 147}, {"opponent": "typhlosion_shadow", "rating": 804, "opRating": 195}, {"opponent": "ninetales_shadow", "rating": 800, "opRating": 200}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "talonflame", "rating": 240}, {"opponent": "cradily", "rating": 288}, {"opponent": "furret", "rating": 353}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23382}, {"moveId": "MUD_SHOT", "uses": 18543}, {"moveId": "ROCK_THROW", "uses": 16394}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 15624}, {"moveId": "ROCK_SLIDE", "uses": 14803}, {"moveId": "ROCK_BLAST", "uses": 14067}, {"moveId": "DIG", "uses": 13660}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "DIG"], "score": 65.6}, {"speciesId": "tauros", "speciesName": "<PERSON><PERSON>", "rating": 510, "matchups": [{"opponent": "quagsire", "rating": 666, "opRating": 333}, {"opponent": "gliscor", "rating": 595, "opRating": 404}, {"opponent": "nidoqueen", "rating": 595, "opRating": 404}, {"opponent": "gligar", "rating": 583}, {"opponent": "farfetchd", "rating": 583, "opRating": 416}], "counters": [{"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 319}, {"opponent": "clodsire", "rating": 372}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 48045}, {"moveId": "ZEN_HEADBUTT", "uses": 10255}], "chargedMoves": [{"moveId": "HORN_ATTACK", "uses": 19358}, {"moveId": "TRAILBLAZE", "uses": 18388}, {"moveId": "EARTHQUAKE", "uses": 10411}, {"moveId": "IRON_HEAD", "uses": 10178}]}, "moveset": ["TACKLE", "TRAILBLAZE", "HORN_ATTACK"], "score": 65.6}, {"speciesId": "zygarde_10", "speciesName": "Zygarde (10% Forme)", "rating": 507, "matchups": [{"opponent": "salazzle", "rating": 729, "opRating": 270}, {"opponent": "typhlosion_shadow", "rating": 709, "opRating": 290}, {"opponent": "lickilicky", "rating": 678, "opRating": 321}, {"opponent": "flygon_shadow", "rating": 566, "opRating": 433}, {"opponent": "claydol", "rating": 535, "opRating": 464}], "counters": [{"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "cradily", "rating": 288}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 309}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "DRAGON_TAIL", "uses": 39113}, {"moveId": "BITE", "uses": 14115}, {"moveId": "ZEN_HEADBUTT", "uses": 5077}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 15916}, {"moveId": "OUTRAGE", "uses": 15859}, {"moveId": "EARTHQUAKE", "uses": 10760}, {"moveId": "BULLDOZE", "uses": 10104}, {"moveId": "HYPER_BEAM", "uses": 5781}]}, "moveset": ["DRAGON_TAIL", "CRUNCH", "EARTHQUAKE"], "score": 65.6}, {"speciesId": "meganium_shadow", "speciesName": "Megan<PERSON> (Shadow)", "rating": 497, "matchups": [{"opponent": "swampert_shadow", "rating": 893, "opRating": 106}, {"opponent": "quagsire_shadow", "rating": 881, "opRating": 118}, {"opponent": "claydol", "rating": 812, "opRating": 187}, {"opponent": "diggersby", "rating": 629, "opRating": 370}, {"opponent": "furret", "rating": 580}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 171}, {"opponent": "talonflame", "rating": 222}, {"opponent": "cradily", "rating": 357}, {"opponent": "magcargo", "rating": 423}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 27205}, {"moveId": "MAGICAL_LEAF", "uses": 21158}, {"moveId": "RAZOR_LEAF", "uses": 9933}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 36923}, {"moveId": "EARTHQUAKE", "uses": 11887}, {"moveId": "PETAL_BLIZZARD", "uses": 5223}, {"moveId": "SOLAR_BEAM", "uses": 4127}, {"moveId": "FRUSTRATION", "uses": 21}]}, "moveset": ["VINE_WHIP", "FRENZY_PLANT", "EARTHQUAKE"], "score": 65.5}, {"speciesId": "heliolisk", "speciesName": "Heliolisk", "rating": 476, "matchups": [{"opponent": "pidgeot", "rating": 766, "opRating": 233}, {"opponent": "talonflame", "rating": 671}, {"opponent": "skeledirge", "rating": 566, "opRating": 433}, {"opponent": "typhlosion_shadow", "rating": 557, "opRating": 442}, {"opponent": "drampa", "rating": 538, "opRating": 461}], "counters": [{"opponent": "clodsire", "rating": 165}, {"opponent": "gligar", "rating": 236}, {"opponent": "cradily", "rating": 263}, {"opponent": "jumpluff_shadow", "rating": 421}, {"opponent": "furret", "rating": 471}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 20269}, {"moveId": "VOLT_SWITCH", "uses": 19210}, {"moveId": "MUD_SLAP", "uses": 18833}], "chargedMoves": [{"moveId": "BREAKING_SWIPE", "uses": 20245}, {"moveId": "PARABOLIC_CHARGE", "uses": 15636}, {"moveId": "GRASS_KNOT", "uses": 12141}, {"moveId": "BULLDOZE", "uses": 6579}, {"moveId": "THUNDERBOLT", "uses": 3739}]}, "moveset": ["VOLT_SWITCH", "BREAKING_SWIPE", "PARABOLIC_CHARGE"], "score": 65.3}, {"speciesId": "quilava", "speciesName": "Quilava", "rating": 536, "matchups": [{"opponent": "abomasnow_shadow", "rating": 886, "opRating": 113}, {"opponent": "sceptile_shadow", "rating": 855, "opRating": 144}, {"opponent": "piloswine", "rating": 714, "opRating": 285}, {"opponent": "ninetales", "rating": 664, "opRating": 335}, {"opponent": "jumpluff_shadow", "rating": 531}], "counters": [{"opponent": "gligar", "rating": 248}, {"opponent": "talonflame", "rating": 337}, {"opponent": "clodsire", "rating": 341}, {"opponent": "cradily", "rating": 350}, {"opponent": "furret", "rating": 375}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 38720}, {"moveId": "TACKLE", "uses": 19580}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 23344}, {"moveId": "DIG", "uses": 15295}, {"moveId": "RETURN", "uses": 10021}, {"moveId": "FLAMETHROWER", "uses": 9700}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 65.3}, {"speciesId": "torracat", "speciesName": "Torracat", "rating": 552, "matchups": [{"opponent": "abomasnow_shadow", "rating": 907, "opRating": 92}, {"opponent": "sceptile_shadow", "rating": 796, "opRating": 203}, {"opponent": "abomasnow", "rating": 722, "opRating": 277}, {"opponent": "piloswine", "rating": 674, "opRating": 325}, {"opponent": "farfetchd", "rating": 633, "opRating": 366}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "gligar", "rating": 293}, {"opponent": "furret", "rating": 343}, {"opponent": "jumpluff_shadow", "rating": 382}, {"opponent": "cradily", "rating": 406}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 41229}, {"moveId": "BITE", "uses": 17071}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24289}, {"moveId": "FLAME_CHARGE", "uses": 24033}, {"moveId": "FLAMETHROWER", "uses": 9980}]}, "moveset": ["EMBER", "FLAME_CHARGE", "CRUNCH"], "score": 65.3}, {"speciesId": "tranquill_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 550, "matchups": [{"opponent": "sceptile_shadow", "rating": 818, "opRating": 181}, {"opponent": "farfetchd", "rating": 636, "opRating": 363}, {"opponent": "gliscor", "rating": 601, "opRating": 398}, {"opponent": "gligar_shadow", "rating": 576, "opRating": 423}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 517, "opRating": 482}], "counters": [{"opponent": "furret", "rating": 221}, {"opponent": "cradily", "rating": 225}, {"opponent": "talonflame", "rating": 300}, {"opponent": "jumpluff_shadow", "rating": 300}, {"opponent": "gligar", "rating": 477}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32528}, {"moveId": "STEEL_WING", "uses": 25772}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 39244}, {"moveId": "SKY_ATTACK", "uses": 13040}, {"moveId": "HEAT_WAVE", "uses": 5887}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["AIR_SLASH", "AERIAL_ACE", "SKY_ATTACK"], "score": 65.2}, {"speciesId": "u<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 511, "matchups": [{"opponent": "pidgeot_shadow", "rating": 621, "opRating": 378}, {"opponent": "clodsire", "rating": 597, "opRating": 402}, {"opponent": "diggersby", "rating": 548, "opRating": 451}, {"opponent": "gliscor", "rating": 548, "opRating": 451}, {"opponent": "typhlosion_shadow", "rating": 548, "opRating": 451}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "furret", "rating": 237}, {"opponent": "talonflame", "rating": 459}, {"opponent": "gligar", "rating": 469}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 42943}, {"moveId": "ROCK_SMASH", "uses": 15357}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 12038}, {"moveId": "SWIFT", "uses": 11559}, {"moveId": "TRAILBLAZE", "uses": 8093}, {"moveId": "FIRE_PUNCH", "uses": 7479}, {"moveId": "AERIAL_ACE", "uses": 7144}, {"moveId": "HIGH_HORSEPOWER", "uses": 6747}, {"moveId": "THUNDER_PUNCH", "uses": 5302}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 65.2}, {"speciesId": "entei", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 553, "matchups": [{"opponent": "abomasnow_shadow", "rating": 910, "opRating": 89}, {"opponent": "parasect", "rating": 903, "opRating": 96}, {"opponent": "piloswine", "rating": 696, "opRating": 303}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 585, "opRating": 414}, {"opponent": "jumpluff_shadow", "rating": 539}], "counters": [{"opponent": "clodsire", "rating": 221}, {"opponent": "talonflame", "rating": 255}, {"opponent": "cradily", "rating": 267}, {"opponent": "gligar", "rating": 290}, {"opponent": "furret", "rating": 353}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 30835}, {"moveId": "FIRE_FANG", "uses": 27465}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 15274}, {"moveId": "FLAME_CHARGE", "uses": 14826}, {"moveId": "OVERHEAT", "uses": 11446}, {"moveId": "IRON_HEAD", "uses": 7288}, {"moveId": "FLAMETHROWER", "uses": 6093}, {"moveId": "FIRE_BLAST", "uses": 3333}]}, "moveset": ["FIRE_FANG", "FLAME_CHARGE", "SCORCHING_SANDS"], "score": 64.9}, {"speciesId": "fuecoco", "speciesName": "Fuecoco", "rating": 545, "matchups": [{"opponent": "abomasnow_shadow", "rating": 878, "opRating": 121}, {"opponent": "gliscor_shadow", "rating": 825, "opRating": 174}, {"opponent": "piloswine", "rating": 700, "opRating": 299}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 631, "opRating": 368}, {"opponent": "jumpluff_shadow", "rating": 536}], "counters": [{"opponent": "gligar", "rating": 232}, {"opponent": "clodsire", "rating": 233}, {"opponent": "talonflame", "rating": 285}, {"opponent": "cradily", "rating": 347}, {"opponent": "furret", "rating": 478}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 43867}, {"moveId": "BITE", "uses": 14433}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21903}, {"moveId": "FLAMETHROWER", "uses": 19691}, {"moveId": "DISARMING_VOICE", "uses": 16795}]}, "moveset": ["INCINERATE", "FLAMETHROWER", "DISARMING_VOICE"], "score": 64.9}, {"speciesId": "to<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 470, "matchups": [{"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "quagsire_shadow", "rating": 939, "opRating": 60}, {"opponent": "claydol", "rating": 785, "opRating": 214}, {"opponent": "flygon_shadow", "rating": 575, "opRating": 424}, {"opponent": "diggersby", "rating": 567, "opRating": 432}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 177}, {"opponent": "gligar", "rating": 293}, {"opponent": "cradily", "rating": 340}, {"opponent": "furret", "rating": 406}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 30557}, {"moveId": "BITE", "uses": 27743}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25702}, {"moveId": "STONE_EDGE", "uses": 15276}, {"moveId": "EARTHQUAKE", "uses": 9210}, {"moveId": "SAND_TOMB", "uses": 5359}, {"moveId": "SOLAR_BEAM", "uses": 2879}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 64.9}, {"speciesId": "cubone", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 489, "matchups": [{"opponent": "magcargo", "rating": 791, "opRating": 208}, {"opponent": "skeledirge", "rating": 759, "opRating": 240}, {"opponent": "typhlosion_shadow", "rating": 696, "opRating": 303}, {"opponent": "clodsire", "rating": 618, "opRating": 381}, {"opponent": "ninetales_shadow", "rating": 582, "opRating": 417}], "counters": [{"opponent": "jumpluff_shadow", "rating": 68}, {"opponent": "gligar", "rating": 118}, {"opponent": "talonflame", "rating": 151}, {"opponent": "furret", "rating": 315}, {"opponent": "cradily", "rating": 371}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 43991}, {"moveId": "ROCK_SMASH", "uses": 14309}], "chargedMoves": [{"moveId": "BONE_CLUB", "uses": 34833}, {"moveId": "RETURN", "uses": 11268}, {"moveId": "DIG", "uses": 6685}, {"moveId": "BULLDOZE", "uses": 5483}]}, "moveset": ["MUD_SLAP", "BONE_CLUB", "RETURN"], "score": 64.7}, {"speciesId": "ferroth<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 496, "matchups": [{"opponent": "flygon", "rating": 652, "opRating": 347}, {"opponent": "quagsire_shadow", "rating": 640, "opRating": 359}, {"opponent": "swampert_shadow", "rating": 628, "opRating": 371}, {"opponent": "clodsire", "rating": 601, "opRating": 398}, {"opponent": "pidgeot", "rating": 585, "opRating": 414}], "counters": [{"opponent": "talonflame", "rating": 40}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "gligar", "rating": 396}, {"opponent": "cradily", "rating": 434}, {"opponent": "furret", "rating": 443}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30576}, {"moveId": "METAL_CLAW", "uses": 27724}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20875}, {"moveId": "MIRROR_SHOT", "uses": 16423}, {"moveId": "THUNDER", "uses": 8864}, {"moveId": "FLASH_CANNON", "uses": 8556}, {"moveId": "ACID_SPRAY", "uses": 3557}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "POWER_WHIP", "THUNDER"], "score": 64.7}, {"speciesId": "flareon", "speciesName": "Flareon", "rating": 558, "matchups": [{"opponent": "abomasnow_shadow", "rating": 875, "opRating": 125}, {"opponent": "sceptile_shadow", "rating": 845, "opRating": 155}, {"opponent": "litleo", "rating": 705, "opRating": 295}, {"opponent": "cradily_shadow", "rating": 690, "opRating": 310}, {"opponent": "piloswine", "rating": 670, "opRating": 330}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 253}, {"opponent": "furret", "rating": 275}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 362}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 30777}, {"moveId": "FIRE_SPIN", "uses": 27523}], "chargedMoves": [{"moveId": "SUPER_POWER", "uses": 21655}, {"moveId": "FLAMETHROWER", "uses": 12165}, {"moveId": "OVERHEAT", "uses": 11305}, {"moveId": "LAST_RESORT", "uses": 8049}, {"moveId": "FIRE_BLAST", "uses": 3192}, {"moveId": "HEAT_WAVE", "uses": 1931}]}, "moveset": ["EMBER", "SUPER_POWER", "FLAMETHROWER"], "score": 64.7}, {"speciesId": "graveler", "speciesName": "<PERSON><PERSON>", "rating": 483, "matchups": [{"opponent": "magcargo", "rating": 879, "opRating": 120}, {"opponent": "typhlosion_shadow", "rating": 840, "opRating": 159}, {"opponent": "ninetales_shadow", "rating": 698, "opRating": 301}, {"opponent": "pidgeot", "rating": 508, "opRating": 491}, {"opponent": "clodsire", "rating": 504, "opRating": 495}], "counters": [{"opponent": "gligar", "rating": 125}, {"opponent": "jumpluff_shadow", "rating": 143}, {"opponent": "cradily", "rating": 256}, {"opponent": "talonflame", "rating": 262}, {"opponent": "furret", "rating": 278}], "moves": {"fastMoves": [{"moveId": "MUD_SLAP", "uses": 23228}, {"moveId": "MUD_SHOT", "uses": 18401}, {"moveId": "ROCK_THROW", "uses": 16654}], "chargedMoves": [{"moveId": "STONE_EDGE", "uses": 14169}, {"moveId": "ROCK_SLIDE", "uses": 13338}, {"moveId": "ROCK_BLAST", "uses": 12747}, {"moveId": "DIG", "uses": 12083}, {"moveId": "RETURN", "uses": 5920}]}, "moveset": ["MUD_SLAP", "STONE_EDGE", "DIG"], "score": 64.6}, {"speciesId": "purugly", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 528, "matchups": [{"opponent": "gourgeist_small", "rating": 917, "opRating": 82}, {"opponent": "victreebel", "rating": 772, "opRating": 227}, {"opponent": "marowak_alolan", "rating": 585, "opRating": 414}, {"opponent": "abomasnow_shadow", "rating": 533, "opRating": 466}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 533, "opRating": 466}], "counters": [{"opponent": "furret", "rating": 78}, {"opponent": "gligar", "rating": 320}, {"opponent": "jumpluff_shadow", "rating": 352}, {"opponent": "cradily", "rating": 368}, {"opponent": "talonflame", "rating": 374}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 36113}, {"moveId": "SCRATCH", "uses": 22187}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 24701}, {"moveId": "RETURN", "uses": 14488}, {"moveId": "PLAY_ROUGH", "uses": 9916}, {"moveId": "THUNDER", "uses": 9170}]}, "moveset": ["SHADOW_CLAW", "AERIAL_ACE", "THUNDER"], "score": 64.6}, {"speciesId": "emboar_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 544, "matchups": [{"opponent": "sceptile_shadow", "rating": 914, "opRating": 85}, {"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "lura<PERSON>s", "rating": 874, "opRating": 125}, {"opponent": "piloswine", "rating": 826, "opRating": 173}, {"opponent": "ninetales_shadow", "rating": 523, "opRating": 476}], "counters": [{"opponent": "gligar", "rating": 270}, {"opponent": "talonflame", "rating": 292}, {"opponent": "cradily", "rating": 298}, {"opponent": "furret", "rating": 368}, {"opponent": "jumpluff_shadow", "rating": 392}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 49798}, {"moveId": "LOW_KICK", "uses": 8502}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 24318}, {"moveId": "FOCUS_BLAST", "uses": 12774}, {"moveId": "ROCK_SLIDE", "uses": 12761}, {"moveId": "FLAME_CHARGE", "uses": 6656}, {"moveId": "HEAT_WAVE", "uses": 1875}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "BLAST_BURN", "ROCK_SLIDE"], "score": 64.4}, {"speciesId": "g<PERSON><PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 498, "matchups": [{"opponent": "quagsire_shadow", "rating": 858, "opRating": 141}, {"opponent": "swampert_shadow", "rating": 841, "opRating": 158}, {"opponent": "claydol", "rating": 653, "opRating": 346}, {"opponent": "piloswine", "rating": 649, "opRating": 350}, {"opponent": "flygon_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "jumpluff_shadow", "rating": 130}, {"opponent": "talonflame", "rating": 170}, {"opponent": "cradily", "rating": 312}, {"opponent": "gligar", "rating": 354}, {"opponent": "furret", "rating": 400}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 29840}, {"moveId": "QUICK_ATTACK", "uses": 28460}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 33210}, {"moveId": "AERIAL_ACE", "uses": 18531}, {"moveId": "GRASS_KNOT", "uses": 6646}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "LEAF_BLADE", "AERIAL_ACE"], "score": 64.4}, {"speciesId": "delphox", "speciesName": "Delphox", "rating": 545, "matchups": [{"opponent": "abomasnow_shadow", "rating": 913, "opRating": 86}, {"opponent": "piloswine", "rating": 709, "opRating": 290}, {"opponent": "gliscor", "rating": 590, "opRating": 409}, {"opponent": "jumpluff_shadow", "rating": 572}, {"opponent": "pidgeot", "rating": 527, "opRating": 472}], "counters": [{"opponent": "talonflame", "rating": 211}, {"opponent": "clodsire", "rating": 218}, {"opponent": "furret", "rating": 243}, {"opponent": "gligar", "rating": 248}, {"opponent": "cradily", "rating": 256}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 39278}, {"moveId": "SCRATCH", "uses": 11798}, {"moveId": "ZEN_HEADBUTT", "uses": 7256}], "chargedMoves": [{"moveId": "BLAST_BURN", "uses": 20759}, {"moveId": "MYSTICAL_FIRE", "uses": 14907}, {"moveId": "PSYCHIC", "uses": 9579}, {"moveId": "FLAME_CHARGE", "uses": 5700}, {"moveId": "FLAMETHROWER", "uses": 4803}, {"moveId": "FIRE_BLAST", "uses": 2684}]}, "moveset": ["FIRE_SPIN", "BLAST_BURN", "MYSTICAL_FIRE"], "score": 64.3}, {"speciesId": "pumpkaboo_small", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Small)", "rating": 510, "matchups": [{"opponent": "victini", "rating": 903, "opRating": 96}, {"opponent": "quagsire", "rating": 777, "opRating": 222}, {"opponent": "claydol", "rating": 768, "opRating": 231}, {"opponent": "swampert_shadow", "rating": 600, "opRating": 399}, {"opponent": "quagsire_shadow", "rating": 529, "opRating": 470}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "talonflame", "rating": 262}, {"opponent": "gligar", "rating": 278}, {"opponent": "cradily", "rating": 395}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38220}, {"moveId": "RAZOR_LEAF", "uses": 20080}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 23613}, {"moveId": "FOUL_PLAY", "uses": 23217}, {"moveId": "SHADOW_SNEAK", "uses": 11405}]}, "moveset": ["ASTONISH", "GRASS_KNOT", "FOUL_PLAY"], "score": 64.3}, {"speciesId": "trevenant", "speciesName": "Trevenant", "rating": 514, "matchups": [{"opponent": "quagsire_shadow", "rating": 858, "opRating": 141}, {"opponent": "quagsire", "rating": 858, "opRating": 141}, {"opponent": "claydol", "rating": 790, "opRating": 209}, {"opponent": "swampert_shadow", "rating": 622, "opRating": 377}, {"opponent": "clodsire", "rating": 549, "opRating": 450}], "counters": [{"opponent": "furret", "rating": 115}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "gligar", "rating": 194}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 375}], "moves": {"fastMoves": [{"moveId": "SUCKER_PUNCH", "uses": 29179}, {"moveId": "SHADOW_CLAW", "uses": 29121}], "chargedMoves": [{"moveId": "FOUL_PLAY", "uses": 21422}, {"moveId": "SEED_BOMB", "uses": 19001}, {"moveId": "SHADOW_BALL", "uses": 17835}]}, "moveset": ["SHADOW_CLAW", "SEED_BOMB", "SHADOW_BALL"], "score": 64.1}, {"speciesId": "bayleef_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 496, "matchups": [{"opponent": "swampert_shadow", "rating": 933, "opRating": 66}, {"opponent": "quagsire_shadow", "rating": 911, "opRating": 88}, {"opponent": "claydol", "rating": 785, "opRating": 214}, {"opponent": "diggersby", "rating": 605, "opRating": 394}, {"opponent": "furret", "rating": 531}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 122}, {"opponent": "cradily", "rating": 243}, {"opponent": "gligar", "rating": 244}, {"opponent": "clodsire", "rating": 420}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 25751}, {"moveId": "TACKLE", "uses": 19334}, {"moveId": "RAZOR_LEAF", "uses": 13193}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 25260}, {"moveId": "ANCIENT_POWER", "uses": 23911}, {"moveId": "ENERGY_BALL", "uses": 9049}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MAGICAL_LEAF", "ANCIENT_POWER", "ENERGY_BALL"], "score": 64}, {"speciesId": "dhelmise", "speciesName": "Dhelmise", "rating": 509, "matchups": [{"opponent": "quagsire", "rating": 880, "opRating": 119}, {"opponent": "claydol", "rating": 766, "opRating": 233}, {"opponent": "quagsire_shadow", "rating": 680, "opRating": 319}, {"opponent": "swampert_shadow", "rating": 542, "opRating": 457}, {"opponent": "flygon", "rating": 538, "opRating": 461}], "counters": [{"opponent": "furret", "rating": 96}, {"opponent": "gligar", "rating": 206}, {"opponent": "talonflame", "rating": 218}, {"opponent": "jumpluff_shadow", "rating": 369}, {"opponent": "cradily", "rating": 402}], "moves": {"fastMoves": [{"moveId": "SHADOW_CLAW", "uses": 22268}, {"moveId": "ASTONISH", "uses": 18085}, {"moveId": "METAL_SOUND", "uses": 17940}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 18807}, {"moveId": "SHADOW_BALL", "uses": 15601}, {"moveId": "WRAP", "uses": 15179}, {"moveId": "HEAVY_SLAM", "uses": 8694}]}, "moveset": ["SHADOW_CLAW", "POWER_WHIP", "WRAP"], "score": 64}, {"speciesId": "ursa<PERSON>na_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 504, "matchups": [{"opponent": "snor<PERSON>_shadow", "rating": 829, "opRating": 170}, {"opponent": "pidgeot", "rating": 621, "opRating": 378}, {"opponent": "fletchinder", "rating": 621, "opRating": 378}, {"opponent": "talonflame", "rating": 579}, {"opponent": "gliscor", "rating": 534, "opRating": 465}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 278}, {"opponent": "furret", "rating": 290}, {"opponent": "clodsire", "rating": 343}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 43304}, {"moveId": "ROCK_SMASH", "uses": 14996}], "chargedMoves": [{"moveId": "ICE_PUNCH", "uses": 12049}, {"moveId": "SWIFT", "uses": 11511}, {"moveId": "TRAILBLAZE", "uses": 8095}, {"moveId": "FIRE_PUNCH", "uses": 7486}, {"moveId": "AERIAL_ACE", "uses": 7170}, {"moveId": "HIGH_HORSEPOWER", "uses": 6743}, {"moveId": "THUNDER_PUNCH", "uses": 5296}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "SWIFT", "HIGH_HORSEPOWER"], "score": 63.8}, {"speciesId": "decid<PERSON><PERSON>_<PERSON><PERSON>an", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Hisuian)", "rating": 491, "matchups": [{"opponent": "swampert_shadow", "rating": 923, "opRating": 76}, {"opponent": "quagsire_shadow", "rating": 899, "opRating": 100}, {"opponent": "claydol", "rating": 754, "opRating": 245}, {"opponent": "furret", "rating": 564}, {"opponent": "diggersby", "rating": 528, "opRating": 471}], "counters": [{"opponent": "jumpluff_shadow", "rating": 81}, {"opponent": "gligar", "rating": 190}, {"opponent": "talonflame", "rating": 259}, {"opponent": "cradily", "rating": 298}, {"opponent": "clodsire", "rating": 346}], "moves": {"fastMoves": [{"moveId": "PSYCHO_CUT", "uses": 31512}, {"moveId": "MAGICAL_LEAF", "uses": 26788}], "chargedMoves": [{"moveId": "AURA_SPHERE", "uses": 15813}, {"moveId": "TRAILBLAZE", "uses": 15400}, {"moveId": "AERIAL_ACE", "uses": 13789}, {"moveId": "NIGHT_SHADE", "uses": 8801}, {"moveId": "ENERGY_BALL", "uses": 4465}]}, "moveset": ["MAGICAL_LEAF", "AERIAL_ACE", "AURA_SPHERE"], "score": 63.7}, {"speciesId": "sandshrew_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 487, "matchups": [{"opponent": "nidoqueen", "rating": 736, "opRating": 263}, {"opponent": "nidoqueen_shadow", "rating": 704, "opRating": 295}, {"opponent": "salazzle", "rating": 700, "opRating": 299}, {"opponent": "magcargo", "rating": 653, "opRating": 346}, {"opponent": "clodsire", "rating": 519, "opRating": 480}], "counters": [{"opponent": "talonflame", "rating": 188}, {"opponent": "gligar", "rating": 316}, {"opponent": "jumpluff_shadow", "rating": 379}, {"opponent": "cradily", "rating": 395}, {"opponent": "furret", "rating": 456}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 25271}, {"moveId": "MUD_SHOT", "uses": 22384}, {"moveId": "SCRATCH", "uses": 10660}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 21421}, {"moveId": "DIG", "uses": 15319}, {"moveId": "ROCK_SLIDE", "uses": 13870}, {"moveId": "SAND_TOMB", "uses": 7668}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "ROCK_TOMB", "DIG"], "score": 63.7}, {"speciesId": "palossand", "speciesName": "Palossand", "rating": 459, "matchups": [{"opponent": "nidoqueen", "rating": 738, "opRating": 261}, {"opponent": "claydol", "rating": 697, "opRating": 302}, {"opponent": "clodsire", "rating": 690, "opRating": 309}, {"opponent": "magmar", "rating": 667, "opRating": 332}, {"opponent": "cradily", "rating": 514}], "counters": [{"opponent": "furret", "rating": 128}, {"opponent": "magcargo", "rating": 294}, {"opponent": "talonflame", "rating": 300}, {"opponent": "jumpluff_shadow", "rating": 343}, {"opponent": "gligar", "rating": 385}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20418}, {"moveId": "SAND_ATTACK", "uses": 20009}, {"moveId": "MUD_SHOT", "uses": 17774}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22474}, {"moveId": "SHADOW_BALL", "uses": 19524}, {"moveId": "EARTH_POWER", "uses": 8873}, {"moveId": "SAND_TOMB", "uses": 7515}]}, "moveset": ["ASTONISH", "SCORCHING_SANDS", "SHADOW_BALL"], "score": 63.5}, {"speciesId": "ambipom_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 480, "matchups": [{"opponent": "bewear", "rating": 862, "opRating": 137}, {"opponent": "armarouge", "rating": 693, "opRating": 306}, {"opponent": "talonflame", "rating": 625}, {"opponent": "skeledirge", "rating": 625, "opRating": 375}, {"opponent": "claydol", "rating": 512, "opRating": 487}], "counters": [{"opponent": "furret", "rating": 112}, {"opponent": "jumpluff_shadow", "rating": 232}, {"opponent": "gligar", "rating": 278}, {"opponent": "clodsire", "rating": 329}, {"opponent": "cradily", "rating": 378}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 35283}, {"moveId": "SCRATCH", "uses": 23017}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 28801}, {"moveId": "LOW_SWEEP", "uses": 16155}, {"moveId": "HYPER_BEAM", "uses": 13240}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "LOW_SWEEP"], "score": 63.2}, {"speciesId": "larve<PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 524, "matchups": [{"opponent": "abomasnow_shadow", "rating": 889, "opRating": 110}, {"opponent": "sceptile_shadow", "rating": 858, "opRating": 141}, {"opponent": "abomasnow", "rating": 763, "opRating": 236}, {"opponent": "piloswine", "rating": 721, "opRating": 278}, {"opponent": "diggersby", "rating": 622, "opRating": 377}], "counters": [{"opponent": "cradily", "rating": 267}, {"opponent": "gligar", "rating": 316}, {"opponent": "furret", "rating": 318}, {"opponent": "talonflame", "rating": 340}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31206}, {"moveId": "BUG_BITE", "uses": 27094}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 29456}, {"moveId": "BUG_BUZZ", "uses": 23371}, {"moveId": "FLAME_WHEEL", "uses": 5447}]}, "moveset": ["EMBER", "FLAME_CHARGE", "BUG_BUZZ"], "score": 63.2}, {"speciesId": "ludico<PERSON>", "speciesName": "Ludicolo", "rating": 460, "matchups": [{"opponent": "diggersby", "rating": 656, "opRating": 343}, {"opponent": "claydol", "rating": 649, "opRating": 350}, {"opponent": "quagsire_shadow", "rating": 574, "opRating": 425}, {"opponent": "clodsire", "rating": 570, "opRating": 429}, {"opponent": "swampert_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "cradily", "rating": 177}, {"opponent": "furret", "rating": 221}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 329}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 44251}, {"moveId": "RAZOR_LEAF", "uses": 14049}], "chargedMoves": [{"moveId": "SCALD", "uses": 19243}, {"moveId": "ICE_BEAM", "uses": 13178}, {"moveId": "ENERGY_BALL", "uses": 8049}, {"moveId": "LEAF_STORM", "uses": 6988}, {"moveId": "BLIZZARD", "uses": 4603}, {"moveId": "HYDRO_PUMP", "uses": 3852}, {"moveId": "SOLAR_BEAM", "uses": 2313}]}, "moveset": ["BUBBLE", "SCALD", "LEAF_STORM"], "score": 63.1}, {"speciesId": "houndour_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 522, "matchups": [{"opponent": "abomasnow_shadow", "rating": 870, "opRating": 129}, {"opponent": "abomasnow", "rating": 870, "opRating": 129}, {"opponent": "piloswine", "rating": 787, "opRating": 212}, {"opponent": "oranguru", "rating": 779, "opRating": 220}, {"opponent": "ursaring_shadow", "rating": 770, "opRating": 229}], "counters": [{"opponent": "gligar", "rating": 270}, {"opponent": "furret", "rating": 284}, {"opponent": "talonflame", "rating": 292}, {"opponent": "jumpluff_shadow", "rating": 434}, {"opponent": "cradily", "rating": 482}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 35936}, {"moveId": "FEINT_ATTACK", "uses": 22364}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 28142}, {"moveId": "FLAMETHROWER", "uses": 19174}, {"moveId": "DARK_PULSE", "uses": 10975}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 62.9}, {"speciesId": "quilava_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 542, "matchups": [{"opponent": "abomasnow_shadow", "rating": 902, "opRating": 97}, {"opponent": "roserade", "rating": 859, "opRating": 140}, {"opponent": "sceptile", "rating": 855, "opRating": 144}, {"opponent": "victreebel", "rating": 750, "opRating": 250}, {"opponent": "piloswine", "rating": 671, "opRating": 328}], "counters": [{"opponent": "talonflame", "rating": 255}, {"opponent": "cradily", "rating": 270}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 328}, {"opponent": "jumpluff_shadow", "rating": 418}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 39112}, {"moveId": "TACKLE", "uses": 19188}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 28017}, {"moveId": "DIG", "uses": 18528}, {"moveId": "FLAMETHROWER", "uses": 11772}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["EMBER", "FLAME_CHARGE", "DIG"], "score": 62.9}, {"speciesId": "appletun", "speciesName": "Appletun", "rating": 507, "matchups": [{"opponent": "quagsire", "rating": 816, "opRating": 183}, {"opponent": "quagsire_shadow", "rating": 807, "opRating": 192}, {"opponent": "claydol", "rating": 645, "opRating": 354}, {"opponent": "magmar", "rating": 602, "opRating": 397}, {"opponent": "swampert_shadow", "rating": 549, "opRating": 450}], "counters": [{"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "gligar", "rating": 190}, {"opponent": "talonflame", "rating": 270}, {"opponent": "furret", "rating": 306}, {"opponent": "cradily", "rating": 309}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 30447}, {"moveId": "ASTONISH", "uses": 27853}], "chargedMoves": [{"moveId": "OUTRAGE", "uses": 23427}, {"moveId": "SEED_BOMB", "uses": 18684}, {"moveId": "ENERGY_BALL", "uses": 8383}, {"moveId": "DRAGON_PULSE", "uses": 7790}]}, "moveset": ["ASTONISH", "SEED_BOMB", "OUTRAGE"], "score": 62.8}, {"speciesId": "raboot", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 535, "matchups": [{"opponent": "abomasnow_shadow", "rating": 903, "opRating": 96}, {"opponent": "sceptile_shadow", "rating": 857, "opRating": 142}, {"opponent": "roserade", "rating": 823, "opRating": 176}, {"opponent": "piloswine", "rating": 726, "opRating": 273}, {"opponent": "jumpluff_shadow", "rating": 515}], "counters": [{"opponent": "talonflame", "rating": 218}, {"opponent": "cradily", "rating": 250}, {"opponent": "gligar", "rating": 270}, {"opponent": "furret", "rating": 340}, {"opponent": "clodsire", "rating": 367}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 37368}, {"moveId": "TACKLE", "uses": 20932}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 41162}, {"moveId": "FLAMETHROWER", "uses": 17138}]}, "moveset": ["FIRE_SPIN", "FLAME_CHARGE", "FLAMETHROWER"], "score": 62.8}, {"speciesId": "gloom", "speciesName": "Gloom", "rating": 502, "matchups": [{"opponent": "whimsicott", "rating": 919, "opRating": 80}, {"opponent": "tropius", "rating": 618, "opRating": 381}, {"opponent": "farfetchd", "rating": 580, "opRating": 419}, {"opponent": "swampert_shadow", "rating": 576, "opRating": 423}, {"opponent": "furret", "rating": 534}], "counters": [{"opponent": "clodsire", "rating": 204}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "talonflame", "rating": 270}, {"opponent": "gligar", "rating": 404}, {"opponent": "cradily", "rating": 423}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 40873}, {"moveId": "RAZOR_LEAF", "uses": 17427}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 21077}, {"moveId": "PETAL_BLIZZARD", "uses": 14892}, {"moveId": "MOONBLAST", "uses": 13040}, {"moveId": "RETURN", "uses": 9399}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 62.5}, {"speciesId": "ivy<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 473, "matchups": [{"opponent": "swampert_shadow", "rating": 841, "opRating": 158}, {"opponent": "quagsire_shadow", "rating": 682, "opRating": 317}, {"opponent": "claydol", "rating": 647, "opRating": 352}, {"opponent": "diggersby", "rating": 577, "opRating": 422}, {"opponent": "furret", "rating": 527}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "cradily", "rating": 385}, {"opponent": "talonflame", "rating": 385}, {"opponent": "clodsire", "rating": 399}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 42866}, {"moveId": "RAZOR_LEAF", "uses": 15434}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 26525}, {"moveId": "POWER_WHIP", "uses": 26230}, {"moveId": "SOLAR_BEAM", "uses": 5549}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 62.5}, {"speciesId": "tsar<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 465, "matchups": [{"opponent": "swampert_shadow", "rating": 912, "opRating": 87}, {"opponent": "quagsire_shadow", "rating": 885, "opRating": 114}, {"opponent": "claydol", "rating": 747, "opRating": 252}, {"opponent": "diggersby", "rating": 527, "opRating": 472}, {"opponent": "furret", "rating": 509}], "counters": [{"opponent": "gligar", "rating": 206}, {"opponent": "cradily", "rating": 246}, {"opponent": "talonflame", "rating": 277}, {"opponent": "clodsire", "rating": 375}, {"opponent": "jumpluff_shadow", "rating": 418}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 32629}, {"moveId": "CHARM", "uses": 13779}, {"moveId": "RAZOR_LEAF", "uses": 11877}], "chargedMoves": [{"moveId": "TRIPLE_AXEL", "uses": 18804}, {"moveId": "GRASS_KNOT", "uses": 12206}, {"moveId": "HIGH_JUMP_KICK", "uses": 11863}, {"moveId": "STOMP", "uses": 8408}, {"moveId": "ENERGY_BALL", "uses": 4412}, {"moveId": "DRAINING_KISS", "uses": 2668}]}, "moveset": ["MAGICAL_LEAF", "TRIPLE_AXEL", "HIGH_JUMP_KICK"], "score": 62.5}, {"speciesId": "grou<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 477, "matchups": [{"opponent": "clodsire", "rating": 871, "opRating": 128}, {"opponent": "cradily_shadow", "rating": 747, "opRating": 252}, {"opponent": "salazzle", "rating": 657, "opRating": 342}, {"opponent": "piloswine", "rating": 614, "opRating": 385}, {"opponent": "claydol", "rating": 566, "opRating": 433}], "counters": [{"opponent": "furret", "rating": 159}, {"opponent": "gligar", "rating": 248}, {"opponent": "talonflame", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "cradily", "rating": 489}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 31794}, {"moveId": "DRAGON_TAIL", "uses": 26506}], "chargedMoves": [{"moveId": "PRECIPICE_BLADES", "uses": 21932}, {"moveId": "FIRE_PUNCH", "uses": 20066}, {"moveId": "SOLAR_BEAM", "uses": 7090}, {"moveId": "EARTHQUAKE", "uses": 5766}, {"moveId": "FIRE_BLAST", "uses": 3304}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "PRECIPICE_BLADES", "FIRE_PUNCH"], "score": 62.3}, {"speciesId": "shiinotic", "speciesName": "Shiinotic", "rating": 493, "matchups": [{"opponent": "flygon", "rating": 849, "opRating": 150}, {"opponent": "flygon_shadow", "rating": 813, "opRating": 186}, {"opponent": "quagsire_shadow", "rating": 780, "opRating": 219}, {"opponent": "obstagoon", "rating": 715, "opRating": 284}, {"opponent": "claydol", "rating": 626, "opRating": 373}], "counters": [{"opponent": "jumpluff_shadow", "rating": 163}, {"opponent": "cradily", "rating": 208}, {"opponent": "talonflame", "rating": 270}, {"opponent": "furret", "rating": 318}, {"opponent": "gligar", "rating": 427}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 58300}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 21262}, {"moveId": "MOONBLAST", "uses": 19635}, {"moveId": "SLUDGE_BOMB", "uses": 17372}]}, "moveset": ["ASTONISH", "MOONBLAST", "SEED_BOMB"], "score": 62.2}, {"speciesId": "braviary", "speciesName": "Braviary", "rating": 506, "matchups": [{"opponent": "parasect", "rating": 870, "opRating": 129}, {"opponent": "gliscor", "rating": 574, "opRating": 425}, {"opponent": "gligar_shadow", "rating": 548, "opRating": 451}, {"opponent": "diggersby_shadow", "rating": 537, "opRating": 462}, {"opponent": "swampert_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 228}, {"opponent": "talonflame", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "gligar", "rating": 492}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32689}, {"moveId": "STEEL_WING", "uses": 25611}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 17233}, {"moveId": "BRAVE_BIRD", "uses": 17217}, {"moveId": "FLY", "uses": 14620}, {"moveId": "ROCK_SLIDE", "uses": 7465}, {"moveId": "HEAT_WAVE", "uses": 1598}]}, "moveset": ["AIR_SLASH", "FLY", "CLOSE_COMBAT"], "score": 61.9}, {"speciesId": "camerupt", "speciesName": "Camerupt", "rating": 507, "matchups": [{"opponent": "abomasnow_shadow", "rating": 852, "opRating": 148}, {"opponent": "victreebel", "rating": 852, "opRating": 148}, {"opponent": "piloswine", "rating": 796, "opRating": 204}, {"opponent": "castform_sunny", "rating": 772, "opRating": 228}, {"opponent": "cradily", "rating": 564}], "counters": [{"opponent": "clodsire", "rating": 218}, {"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 232}, {"opponent": "furret", "rating": 315}, {"opponent": "jumpluff_shadow", "rating": 428}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 32402}, {"moveId": "EMBER", "uses": 20539}, {"moveId": "ROCK_SMASH", "uses": 5358}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 18552}, {"moveId": "EARTH_POWER", "uses": 17421}, {"moveId": "RETURN", "uses": 8513}, {"moveId": "SOLAR_BEAM", "uses": 7572}, {"moveId": "EARTHQUAKE", "uses": 6353}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 61.9}, {"speciesId": "grotle", "speciesName": "Grotle", "rating": 468, "matchups": [{"opponent": "swampert_shadow", "rating": 943, "opRating": 56}, {"opponent": "quagsire_shadow", "rating": 932, "opRating": 67}, {"opponent": "swampert", "rating": 932, "opRating": 67}, {"opponent": "rhyperior_shadow", "rating": 886, "opRating": 113}, {"opponent": "claydol", "rating": 783, "opRating": 216}], "counters": [{"opponent": "jumpluff_shadow", "rating": 111}, {"opponent": "talonflame", "rating": 148}, {"opponent": "cradily", "rating": 284}, {"opponent": "gligar", "rating": 297}, {"opponent": "furret", "rating": 403}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 29878}, {"moveId": "BITE", "uses": 28422}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 26289}, {"moveId": "ENERGY_BALL", "uses": 19305}, {"moveId": "RETURN", "uses": 6938}, {"moveId": "SOLAR_BEAM", "uses": 5697}]}, "moveset": ["RAZOR_LEAF", "BODY_SLAM", "ENERGY_BALL"], "score": 61.7}, {"speciesId": "teddiu<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 478, "matchups": [{"opponent": "incineroar", "rating": 799, "opRating": 200}, {"opponent": "lileep_shadow", "rating": 700, "opRating": 299}, {"opponent": "talonflame", "rating": 538}, {"opponent": "claydol", "rating": 538, "opRating": 461}, {"opponent": "gliscor", "rating": 510, "opRating": 489}], "counters": [{"opponent": "furret", "rating": 134}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 278}, {"opponent": "clodsire", "rating": 341}, {"opponent": "cradily", "rating": 413}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 33010}, {"moveId": "SCRATCH", "uses": 25290}], "chargedMoves": [{"moveId": "SWIFT", "uses": 17597}, {"moveId": "CROSS_CHOP", "uses": 16443}, {"moveId": "CRUNCH", "uses": 10244}, {"moveId": "TRAILBLAZE", "uses": 10128}, {"moveId": "PLAY_ROUGH", "uses": 3793}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LICK", "SWIFT", "CROSS_CHOP"], "score": 61.7}, {"speciesId": "lampent_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 471, "matchups": [{"opponent": "magmortar_shadow", "rating": 903, "opRating": 96}, {"opponent": "skeledirge", "rating": 821, "opRating": 178}, {"opponent": "marowak_alolan_shadow", "rating": 786, "opRating": 213}, {"opponent": "typhlosion_shadow", "rating": 775, "opRating": 224}, {"opponent": "ninetales_shadow", "rating": 542, "opRating": 457}], "counters": [{"opponent": "furret", "rating": 115}, {"opponent": "jumpluff_shadow", "rating": 277}, {"opponent": "cradily", "rating": 319}, {"opponent": "gligar", "rating": 339}, {"opponent": "talonflame", "rating": 418}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31134}, {"moveId": "ASTONISH", "uses": 27166}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 26012}, {"moveId": "FLAME_BURST", "uses": 25431}, {"moveId": "HEAT_WAVE", "uses": 6750}, {"moveId": "FRUSTRATION", "uses": 100}]}, "moveset": ["ASTONISH", "ENERGY_BALL", "FLAME_BURST"], "score": 61.6}, {"speciesId": "wyr<PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 485, "matchups": [{"opponent": "victree<PERSON>_shadow", "rating": 804, "opRating": 195}, {"opponent": "victreebel", "rating": 780, "opRating": 219}, {"opponent": "salazzle", "rating": 729, "opRating": 270}, {"opponent": "magmar", "rating": 544, "opRating": 455}, {"opponent": "pidgeot", "rating": 520, "opRating": 479}], "counters": [{"opponent": "jumpluff_shadow", "rating": 215}, {"opponent": "furret", "rating": 237}, {"opponent": "gligar", "rating": 255}, {"opponent": "cradily", "rating": 336}, {"opponent": "talonflame", "rating": 337}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 29366}, {"moveId": "TACKLE", "uses": 22979}, {"moveId": "ZEN_HEADBUTT", "uses": 5905}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 16749}, {"moveId": "STOMP", "uses": 15539}, {"moveId": "MEGAHORN", "uses": 15160}, {"moveId": "PSYCHIC", "uses": 10859}]}, "moveset": ["CONFUSION", "STOMP", "WILD_CHARGE"], "score": 61.6}, {"speciesId": "pupitar_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 456, "matchups": [{"opponent": "armarouge", "rating": 845, "opRating": 154}, {"opponent": "staraptor", "rating": 823, "opRating": 176}, {"opponent": "skeledirge", "rating": 798, "opRating": 201}, {"opponent": "typhlosion_shadow", "rating": 654, "opRating": 345}, {"opponent": "flygon_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "cradily", "rating": 284}, {"opponent": "gligar", "rating": 286}, {"opponent": "jumpluff_shadow", "rating": 316}, {"opponent": "furret", "rating": 325}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33496}, {"moveId": "ROCK_SMASH", "uses": 24804}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 22127}, {"moveId": "CRUNCH", "uses": 19373}, {"moveId": "DIG", "uses": 16718}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "ANCIENT_POWER", "DIG"], "score": 61.4}, {"speciesId": "rilla<PERSON>m", "speciesName": "R<PERSON>boom", "rating": 454, "matchups": [{"opponent": "swampert_shadow", "rating": 937, "opRating": 62}, {"opponent": "quagsire_shadow", "rating": 926, "opRating": 73}, {"opponent": "claydol", "rating": 751, "opRating": 248}, {"opponent": "piloswine", "rating": 686, "opRating": 313}, {"opponent": "diggersby", "rating": 542, "opRating": 457}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 177}, {"opponent": "gligar", "rating": 293}, {"opponent": "cradily", "rating": 315}, {"opponent": "furret", "rating": 368}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 32399}, {"moveId": "SCRATCH", "uses": 25901}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 28442}, {"moveId": "EARTH_POWER", "uses": 19700}, {"moveId": "ENERGY_BALL", "uses": 10156}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "EARTH_POWER"], "score": 61.4}, {"speciesId": "camerupt_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 519, "matchups": [{"opponent": "roserade", "rating": 900, "opRating": 100}, {"opponent": "victree<PERSON>_shadow", "rating": 876, "opRating": 124}, {"opponent": "lura<PERSON>s", "rating": 852, "opRating": 148}, {"opponent": "abomasnow_shadow", "rating": 828, "opRating": 172}, {"opponent": "piloswine", "rating": 804, "opRating": 196}], "counters": [{"opponent": "talonflame", "rating": 262}, {"opponent": "gligar", "rating": 278}, {"opponent": "cradily", "rating": 295}, {"opponent": "furret", "rating": 303}, {"opponent": "jumpluff_shadow", "rating": 408}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 33522}, {"moveId": "EMBER", "uses": 19851}, {"moveId": "ROCK_SMASH", "uses": 4923}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 22014}, {"moveId": "EARTH_POWER", "uses": 20142}, {"moveId": "SOLAR_BEAM", "uses": 8728}, {"moveId": "EARTHQUAKE", "uses": 7307}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["INCINERATE", "EARTH_POWER", "OVERHEAT"], "score": 61.3}, {"speciesId": "thwackey", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 452, "matchups": [{"opponent": "swampert_shadow", "rating": 941, "opRating": 58}, {"opponent": "quagsire_shadow", "rating": 930, "opRating": 69}, {"opponent": "claydol", "rating": 797, "opRating": 202}, {"opponent": "piloswine", "rating": 702, "opRating": 297}, {"opponent": "flygon_shadow", "rating": 555, "opRating": 444}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 177}, {"opponent": "gligar", "rating": 251}, {"opponent": "cradily", "rating": 277}, {"opponent": "furret", "rating": 453}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 33190}, {"moveId": "SCRATCH", "uses": 25110}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 43142}, {"moveId": "ENERGY_BALL", "uses": 15158}]}, "moveset": ["RAZOR_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 61.3}, {"speciesId": "te<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 482, "matchups": [{"opponent": "oricorio_baile", "rating": 686, "opRating": 313}, {"opponent": "braixen", "rating": 658, "opRating": 341}, {"opponent": "flygon", "rating": 573, "opRating": 426}, {"opponent": "pyroar", "rating": 573, "opRating": 426}, {"opponent": "gliscor_shadow", "rating": 510, "opRating": 489}], "counters": [{"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "furret", "rating": 303}, {"opponent": "cradily", "rating": 333}, {"opponent": "gligar", "rating": 343}, {"opponent": "talonflame", "rating": 477}], "moves": {"fastMoves": [{"moveId": "LICK", "uses": 31812}, {"moveId": "SCRATCH", "uses": 26488}], "chargedMoves": [{"moveId": "SWIFT", "uses": 16561}, {"moveId": "CROSS_CHOP", "uses": 15721}, {"moveId": "CRUNCH", "uses": 9732}, {"moveId": "TRAILBLAZE", "uses": 9728}, {"moveId": "PLAY_ROUGH", "uses": 3631}, {"moveId": "RETURN", "uses": 2807}]}, "moveset": ["LICK", "SWIFT", "CROSS_CHOP"], "score": 61.1}, {"speciesId": "torterra", "speciesName": "Torterra", "rating": 445, "matchups": [{"opponent": "swampert_shadow", "rating": 939, "opRating": 60}, {"opponent": "quagsire_shadow", "rating": 928, "opRating": 71}, {"opponent": "golurk_shadow", "rating": 849, "opRating": 150}, {"opponent": "claydol", "rating": 770, "opRating": 229}, {"opponent": "piloswine", "rating": 582, "opRating": 417}], "counters": [{"opponent": "jumpluff_shadow", "rating": 117}, {"opponent": "talonflame", "rating": 137}, {"opponent": "gligar", "rating": 274}, {"opponent": "furret", "rating": 350}, {"opponent": "cradily", "rating": 440}], "moves": {"fastMoves": [{"moveId": "RAZOR_LEAF", "uses": 29905}, {"moveId": "BITE", "uses": 28395}], "chargedMoves": [{"moveId": "FRENZY_PLANT", "uses": 25742}, {"moveId": "STONE_EDGE", "uses": 15277}, {"moveId": "EARTHQUAKE", "uses": 9201}, {"moveId": "SAND_TOMB", "uses": 5408}, {"moveId": "SOLAR_BEAM", "uses": 2881}]}, "moveset": ["RAZOR_LEAF", "FRENZY_PLANT", "STONE_EDGE"], "score": 61.1}, {"speciesId": "loudred_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 469, "matchups": [{"opponent": "gourgeist_average", "rating": 918, "opRating": 81}, {"opponent": "armarouge", "rating": 735, "opRating": 264}, {"opponent": "marowak_alolan_shadow", "rating": 710, "opRating": 289}, {"opponent": "staraptor", "rating": 693, "opRating": 306}, {"opponent": "skeledirge", "rating": 651, "opRating": 348}], "counters": [{"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 278}, {"opponent": "cradily", "rating": 284}, {"opponent": "furret", "rating": 325}, {"opponent": "talonflame", "rating": 400}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 32664}, {"moveId": "ROCK_SMASH", "uses": 25636}], "chargedMoves": [{"moveId": "STOMP", "uses": 24538}, {"moveId": "DISARMING_VOICE", "uses": 17968}, {"moveId": "FLAMETHROWER", "uses": 15705}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "STOMP", "DISARMING_VOICE"], "score": 61}, {"speciesId": "carnivine", "speciesName": "Carnivine", "rating": 455, "matchups": [{"opponent": "swampert_shadow", "rating": 892, "opRating": 107}, {"opponent": "claydol", "rating": 811, "opRating": 188}, {"opponent": "quagsire_shadow", "rating": 742, "opRating": 257}, {"opponent": "diggersby", "rating": 600, "opRating": 400}, {"opponent": "flygon", "rating": 596, "opRating": 403}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 285}, {"opponent": "cradily", "rating": 336}, {"opponent": "furret", "rating": 493}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 39400}, {"moveId": "BITE", "uses": 18900}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 25721}, {"moveId": "POWER_WHIP", "uses": 23948}, {"moveId": "ENERGY_BALL", "uses": 8465}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "CRUNCH"], "score": 60.8}, {"speciesId": "infernape_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 514, "matchups": [{"opponent": "parasect", "rating": 920, "opRating": 79}, {"opponent": "sceptile_shadow", "rating": 894, "opRating": 105}, {"opponent": "abomasnow_shadow", "rating": 890, "opRating": 109}, {"opponent": "lura<PERSON>s", "rating": 852, "opRating": 147}, {"opponent": "piloswine", "rating": 844, "opRating": 155}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "gligar", "rating": 309}, {"opponent": "cradily", "rating": 319}, {"opponent": "furret", "rating": 353}, {"opponent": "jumpluff_shadow", "rating": 356}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 46816}, {"moveId": "ROCK_SMASH", "uses": 11484}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28486}, {"moveId": "BLAST_BURN", "uses": 20495}, {"moveId": "FLAMETHROWER", "uses": 4737}, {"moveId": "SOLAR_BEAM", "uses": 4516}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 60.8}, {"speciesId": "kartana", "speciesName": "Kartana", "rating": 451, "matchups": [{"opponent": "swampert_shadow", "rating": 873, "opRating": 126}, {"opponent": "quagsire_shadow", "rating": 841, "opRating": 158}, {"opponent": "piloswine", "rating": 772, "opRating": 227}, {"opponent": "claydol", "rating": 613, "opRating": 386}, {"opponent": "flygon", "rating": 594, "opRating": 405}], "counters": [{"opponent": "talonflame", "rating": 92}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "gligar", "rating": 347}, {"opponent": "cradily", "rating": 368}, {"opponent": "furret", "rating": 412}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 35470}, {"moveId": "RAZOR_LEAF", "uses": 22830}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 24103}, {"moveId": "NIGHT_SLASH", "uses": 13221}, {"moveId": "AERIAL_ACE", "uses": 10734}, {"moveId": "X_SCISSOR", "uses": 10287}]}, "moveset": ["RAZOR_LEAF", "LEAF_BLADE", "NIGHT_SLASH"], "score": 60.8}, {"speciesId": "watchog_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 464, "matchups": [{"opponent": "gourgeist_average", "rating": 911, "opRating": 88}, {"opponent": "armarouge", "rating": 732, "opRating": 268}, {"opponent": "marowak_alolan_shadow", "rating": 708, "opRating": 292}, {"opponent": "staraptor", "rating": 708, "opRating": 292}, {"opponent": "skeledirge", "rating": 656, "opRating": 344}], "counters": [{"opponent": "jumpluff_shadow", "rating": 235}, {"opponent": "cradily", "rating": 284}, {"opponent": "gligar", "rating": 286}, {"opponent": "furret", "rating": 287}, {"opponent": "talonflame", "rating": 400}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 37811}, {"moveId": "LOW_KICK", "uses": 20489}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 21200}, {"moveId": "HYPER_FANG", "uses": 20071}, {"moveId": "GRASS_KNOT", "uses": 17019}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BITE", "CRUNCH", "HYPER_FANG"], "score": 60.7}, {"speciesId": "nuzleaf_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 488, "matchups": [{"opponent": "oranguru", "rating": 824, "opRating": 175}, {"opponent": "kecleon", "rating": 767, "opRating": 232}, {"opponent": "claydol", "rating": 742, "opRating": 257}, {"opponent": "quagsire_shadow", "rating": 681, "opRating": 318}, {"opponent": "furret", "rating": 544}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "jumpluff_shadow", "rating": 179}, {"opponent": "talonflame", "rating": 237}, {"opponent": "clodsire", "rating": 341}, {"opponent": "cradily", "rating": 395}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 37194}, {"moveId": "RAZOR_LEAF", "uses": 21106}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 30687}, {"moveId": "FOUL_PLAY", "uses": 21438}, {"moveId": "GRASS_KNOT", "uses": 6113}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 60.5}, {"speciesId": "infernape", "speciesName": "Infernape", "rating": 513, "matchups": [{"opponent": "abomasnow_shadow", "rating": 894, "opRating": 105}, {"opponent": "parasect", "rating": 878, "opRating": 121}, {"opponent": "roserade", "rating": 827, "opRating": 172}, {"opponent": "piloswine", "rating": 680, "opRating": 319}, {"opponent": "furret", "rating": 567}], "counters": [{"opponent": "talonflame", "rating": 211}, {"opponent": "clodsire", "rating": 218}, {"opponent": "cradily", "rating": 256}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 45647}, {"moveId": "ROCK_SMASH", "uses": 12653}], "chargedMoves": [{"moveId": "CLOSE_COMBAT", "uses": 28476}, {"moveId": "BLAST_BURN", "uses": 20532}, {"moveId": "FLAMETHROWER", "uses": 4735}, {"moveId": "SOLAR_BEAM", "uses": 4505}]}, "moveset": ["FIRE_SPIN", "CLOSE_COMBAT", "BLAST_BURN"], "score": 60.4}, {"speciesId": "seismitoad", "speciesName": "Seismitoad", "rating": 458, "matchups": [{"opponent": "ninetales", "rating": 727, "opRating": 272}, {"opponent": "ninetales_shadow", "rating": 681, "opRating": 318}, {"opponent": "magcargo", "rating": 678, "opRating": 321}, {"opponent": "nidoqueen", "rating": 672, "opRating": 327}, {"opponent": "clodsire", "rating": 629, "opRating": 370}], "counters": [{"opponent": "cradily", "rating": 138}, {"opponent": "furret", "rating": 153}, {"opponent": "gligar", "rating": 221}, {"opponent": "jumpluff_shadow", "rating": 303}, {"opponent": "talonflame", "rating": 351}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31787}, {"moveId": "MUD_SHOT", "uses": 26513}], "chargedMoves": [{"moveId": "MUDDY_WATER", "uses": 24827}, {"moveId": "EARTH_POWER", "uses": 17622}, {"moveId": "SLUDGE_BOMB", "uses": 15841}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "SLUDGE_BOMB"], "score": 60.4}, {"speciesId": "simisear", "speciesName": "Simisear", "rating": 519, "matchups": [{"opponent": "abomasnow_shadow", "rating": 900, "opRating": 99}, {"opponent": "parasect", "rating": 884, "opRating": 115}, {"opponent": "piloswine", "rating": 642, "opRating": 357}, {"opponent": "gliscor", "rating": 575, "opRating": 424}, {"opponent": "skeledirge", "rating": 563, "opRating": 436}], "counters": [{"opponent": "talonflame", "rating": 211}, {"opponent": "cradily", "rating": 256}, {"opponent": "furret", "rating": 265}, {"opponent": "gligar", "rating": 278}, {"opponent": "jumpluff_shadow", "rating": 346}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 40242}, {"moveId": "BITE", "uses": 18058}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 27932}, {"moveId": "FLAMETHROWER", "uses": 23883}, {"moveId": "FIRE_BLAST", "uses": 6517}]}, "moveset": ["FIRE_SPIN", "CRUNCH", "FLAMETHROWER"], "score": 60.4}, {"speciesId": "tangrowth", "speciesName": "Tangrow<PERSON>", "rating": 475, "matchups": [{"opponent": "quagsire", "rating": 908, "opRating": 91}, {"opponent": "swampert_shadow", "rating": 897, "opRating": 102}, {"opponent": "quagsire_shadow", "rating": 863, "opRating": 136}, {"opponent": "claydol", "rating": 819, "opRating": 180}, {"opponent": "diggersby", "rating": 610, "opRating": 389}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 156}, {"opponent": "cradily", "rating": 322}, {"opponent": "furret", "rating": 490}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33668}, {"moveId": "INFESTATION", "uses": 24632}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20026}, {"moveId": "ROCK_SLIDE", "uses": 14969}, {"moveId": "SLUDGE_BOMB", "uses": 11980}, {"moveId": "ANCIENT_POWER", "uses": 7140}, {"moveId": "SOLAR_BEAM", "uses": 4214}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 60.2}, {"speciesId": "phanpy_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 456, "matchups": [{"opponent": "nidoqueen_shadow", "rating": 720, "opRating": 279}, {"opponent": "salazzle", "rating": 607, "opRating": 392}, {"opponent": "magmar", "rating": 588, "opRating": 411}, {"opponent": "typhlosion_shadow", "rating": 569, "opRating": 430}, {"opponent": "clodsire", "rating": 548, "opRating": 451}], "counters": [{"opponent": "cradily", "rating": 194}, {"opponent": "jumpluff_shadow", "rating": 300}, {"opponent": "furret", "rating": 390}, {"opponent": "talonflame", "rating": 414}, {"opponent": "gligar", "rating": 438}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 38574}, {"moveId": "ROCK_SMASH", "uses": 19726}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 16579}, {"moveId": "BODY_SLAM", "uses": 16120}, {"moveId": "ROCK_SLIDE", "uses": 14979}, {"moveId": "BULLDOZE", "uses": 10668}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "TRAILBLAZE", "BODY_SLAM"], "score": 60.1}, {"speciesId": "cinderace", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 518, "matchups": [{"opponent": "abomasnow_shadow", "rating": 893, "opRating": 106}, {"opponent": "sceptile", "rating": 841, "opRating": 158}, {"opponent": "sceptile_shadow", "rating": 841, "opRating": 158}, {"opponent": "bellossom_shadow", "rating": 739, "opRating": 260}, {"opponent": "piloswine", "rating": 688, "opRating": 311}], "counters": [{"opponent": "talonflame", "rating": 225}, {"opponent": "cradily", "rating": 256}, {"opponent": "furret", "rating": 275}, {"opponent": "gligar", "rating": 301}, {"opponent": "jumpluff_shadow", "rating": 359}], "moves": {"fastMoves": [{"moveId": "FIRE_SPIN", "uses": 38552}, {"moveId": "TACKLE", "uses": 19748}], "chargedMoves": [{"moveId": "FLAME_CHARGE", "uses": 27975}, {"moveId": "FOCUS_BLAST", "uses": 18593}, {"moveId": "FLAMETHROWER", "uses": 11646}]}, "moveset": ["FIRE_SPIN", "FLAMETHROWER", "FLAME_CHARGE"], "score": 60}, {"speciesId": "wormadam_plant", "speciesName": "Wormadam (Plant)", "rating": 506, "matchups": [{"opponent": "obstagoon_shadow", "rating": 824, "opRating": 175}, {"opponent": "whiscash", "rating": 804, "opRating": 195}, {"opponent": "sceptile", "rating": 785, "opRating": 214}, {"opponent": "claydol", "rating": 632, "opRating": 367}, {"opponent": "flygon", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 140}, {"opponent": "gligar", "rating": 164}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "cradily", "rating": 388}, {"opponent": "furret", "rating": 403}], "moves": {"fastMoves": [{"moveId": "BUG_BITE", "uses": 33411}, {"moveId": "CONFUSION", "uses": 24889}], "chargedMoves": [{"moveId": "BUG_BUZZ", "uses": 24573}, {"moveId": "ENERGY_BALL", "uses": 23458}, {"moveId": "PSYBEAM", "uses": 10323}]}, "moveset": ["BUG_BITE", "ENERGY_BALL", "BUG_BUZZ"], "score": 60}, {"speciesId": "nuzleaf", "speciesName": "Nuz<PERSON>", "rating": 488, "matchups": [{"opponent": "quagsire", "rating": 882, "opRating": 117}, {"opponent": "claydol", "rating": 792, "opRating": 207}, {"opponent": "oranguru", "rating": 732, "opRating": 267}, {"opponent": "quagsire_shadow", "rating": 710, "opRating": 289}, {"opponent": "girafarig", "rating": 671, "opRating": 328}], "counters": [{"opponent": "jumpluff_shadow", "rating": 150}, {"opponent": "gligar", "rating": 194}, {"opponent": "talonflame", "rating": 225}, {"opponent": "cradily", "rating": 326}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 36548}, {"moveId": "RAZOR_LEAF", "uses": 21752}], "chargedMoves": [{"moveId": "LEAF_BLADE", "uses": 28082}, {"moveId": "FOUL_PLAY", "uses": 18884}, {"moveId": "RETURN", "uses": 5709}, {"moveId": "GRASS_KNOT", "uses": 5522}]}, "moveset": ["FEINT_ATTACK", "LEAF_BLADE", "FOUL_PLAY"], "score": 59.8}, {"speciesId": "persian", "speciesName": "Persian", "rating": 452, "matchups": [{"opponent": "victini", "rating": 821, "opRating": 178}, {"opponent": "skeledirge", "rating": 656, "opRating": 343}, {"opponent": "marowak_alolan_shadow", "rating": 554, "opRating": 445}, {"opponent": "marowak_alolan", "rating": 543, "opRating": 456}, {"opponent": "pidgeot", "rating": 532, "opRating": 467}], "counters": [{"opponent": "furret", "rating": 306}, {"opponent": "gligar", "rating": 335}, {"opponent": "cradily", "rating": 336}, {"opponent": "talonflame", "rating": 355}, {"opponent": "jumpluff_shadow", "rating": 464}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 31969}, {"moveId": "SCRATCH", "uses": 26331}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 18658}, {"moveId": "POWER_GEM", "uses": 13570}, {"moveId": "RETURN", "uses": 8521}, {"moveId": "FOUL_PLAY", "uses": 6931}, {"moveId": "PLAY_ROUGH", "uses": 6001}, {"moveId": "PAYBACK", "uses": 4584}]}, "moveset": ["FEINT_ATTACK", "NIGHT_SLASH", "POWER_GEM"], "score": 59.7}, {"speciesId": "arboliva", "speciesName": "Arboliva", "rating": 456, "matchups": [{"opponent": "swampert_shadow", "rating": 917, "opRating": 82}, {"opponent": "quagsire", "rating": 908, "opRating": 91}, {"opponent": "quagsire_shadow", "rating": 891, "opRating": 108}, {"opponent": "claydol", "rating": 760, "opRating": 239}, {"opponent": "diggersby", "rating": 543, "opRating": 456}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 192}, {"opponent": "gligar", "rating": 206}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 375}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 24384}, {"moveId": "TACKLE", "uses": 21424}, {"moveId": "RAZOR_LEAF", "uses": 12454}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 26411}, {"moveId": "EARTH_POWER", "uses": 16009}, {"moveId": "SEED_BOMB", "uses": 8444}, {"moveId": "ENERGY_BALL", "uses": 7495}]}, "moveset": ["MAGICAL_LEAF", "EARTH_POWER", "TRAILBLAZE"], "score": 59.5}, {"speciesId": "cherrim_sunny", "speciesName": "<PERSON><PERSON><PERSON> (Sunshine)", "rating": 480, "matchups": [{"opponent": "claydol", "rating": 788, "opRating": 211}, {"opponent": "quagsire_shadow", "rating": 726, "opRating": 273}, {"opponent": "abomasnow_shadow", "rating": 719, "opRating": 280}, {"opponent": "diggersby", "rating": 580, "opRating": 419}, {"opponent": "furret", "rating": 507}], "counters": [{"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 225}, {"opponent": "talonflame", "rating": 237}, {"opponent": "clodsire", "rating": 377}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 38785}, {"moveId": "RAZOR_LEAF", "uses": 19515}], "chargedMoves": [{"moveId": "WEATHER_BALL_FIRE", "uses": 28358}, {"moveId": "SOLAR_BEAM", "uses": 11484}, {"moveId": "DAZZLING_GLEAM", "uses": 11264}, {"moveId": "HYPER_BEAM", "uses": 7227}]}, "moveset": ["BULLET_SEED", "WEATHER_BALL_FIRE", "SOLAR_BEAM"], "score": 59.5}, {"speciesId": "herdier", "speciesName": "<PERSON><PERSON>", "rating": 433, "matchups": [{"opponent": "magcargo", "rating": 714, "opRating": 285}, {"opponent": "nidoqueen", "rating": 672, "opRating": 327}, {"opponent": "clodsire", "rating": 598, "opRating": 401}, {"opponent": "farfetchd", "rating": 542, "opRating": 457}, {"opponent": "ninetales_shadow", "rating": 514, "opRating": 485}], "counters": [{"opponent": "gligar", "rating": 236}, {"opponent": "jumpluff_shadow", "rating": 281}, {"opponent": "furret", "rating": 340}, {"opponent": "cradily", "rating": 381}, {"opponent": "talonflame", "rating": 462}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 29879}, {"moveId": "LICK", "uses": 20054}, {"moveId": "TAKE_DOWN", "uses": 8378}], "chargedMoves": [{"moveId": "DIG", "uses": 23528}, {"moveId": "PLAY_ROUGH", "uses": 18258}, {"moveId": "THUNDERBOLT", "uses": 16540}]}, "moveset": ["SAND_ATTACK", "DIG", "THUNDERBOLT"], "score": 59.4}, {"speciesId": "palpitoad", "speciesName": "Palpitoad", "rating": 439, "matchups": [{"opponent": "magcargo", "rating": 692, "opRating": 307}, {"opponent": "ninetales_shadow", "rating": 683, "opRating": 316}, {"opponent": "clodsire", "rating": 624, "opRating": 375}, {"opponent": "swampert_shadow", "rating": 515, "opRating": 484}, {"opponent": "piloswine", "rating": 509, "opRating": 490}], "counters": [{"opponent": "cradily", "rating": 138}, {"opponent": "talonflame", "rating": 148}, {"opponent": "furret", "rating": 153}, {"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "gligar", "rating": 366}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 31682}, {"moveId": "MUD_SHOT", "uses": 26618}], "chargedMoves": [{"moveId": "WATER_PULSE", "uses": 22797}, {"moveId": "EARTH_POWER", "uses": 21646}, {"moveId": "SLUDGE_WAVE", "uses": 13793}]}, "moveset": ["MUD_SHOT", "EARTH_POWER", "WATER_PULSE"], "score": 59.4}, {"speciesId": "porygon2", "speciesName": "Porygon2", "rating": 490, "matchups": [{"opponent": "noctowl", "rating": 791, "opRating": 208}, {"opponent": "pidgeotto", "rating": 657, "opRating": 342}, {"opponent": "farfetchd", "rating": 610, "opRating": 389}, {"opponent": "pidgeot", "rating": 551, "opRating": 448}, {"opponent": "gliscor_shadow", "rating": 519, "opRating": 480}], "counters": [{"opponent": "furret", "rating": 81}, {"opponent": "talonflame", "rating": 100}, {"opponent": "cradily", "rating": 253}, {"opponent": "jumpluff_shadow", "rating": 395}, {"opponent": "gligar", "rating": 492}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7338}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4274}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3779}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3378}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3348}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3301}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3034}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3003}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2983}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2938}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2909}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2899}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2784}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2611}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2597}, {"moveId": "CHARGE_BEAM", "uses": 2584}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2426}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2032}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25657}, {"moveId": "HYPER_BEAM", "uses": 12567}, {"moveId": "SOLAR_BEAM", "uses": 11139}, {"moveId": "ZAP_CANNON", "uses": 9012}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 59.4}, {"speciesId": "porygon2_shadow", "speciesName": "Porygon2 (Shadow)", "rating": 496, "matchups": [{"opponent": "noctowl", "rating": 740, "opRating": 259}, {"opponent": "staraptor", "rating": 736, "opRating": 263}, {"opponent": "drampa", "rating": 566, "opRating": 433}, {"opponent": "flygon_shadow", "rating": 555, "opRating": 444}, {"opponent": "gliscor", "rating": 519, "opRating": 480}], "counters": [{"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "talonflame", "rating": 196}, {"opponent": "cradily", "rating": 270}, {"opponent": "gligar", "rating": 309}, {"opponent": "furret", "rating": 356}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 7911}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4341}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3847}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3376}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3328}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3312}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3004}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2998}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2969}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2865}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2828}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2825}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2778}, {"moveId": "CHARGE_BEAM", "uses": 2633}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2598}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2542}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2355}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2021}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 25632}, {"moveId": "HYPER_BEAM", "uses": 12555}, {"moveId": "SOLAR_BEAM", "uses": 11165}, {"moveId": "ZAP_CANNON", "uses": 9010}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 59.4}, {"speciesId": "houndour", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 512, "matchups": [{"opponent": "abomasnow", "rating": 879, "opRating": 120}, {"opponent": "abomasnow_shadow", "rating": 870, "opRating": 129}, {"opponent": "oranguru", "rating": 658, "opRating": 341}, {"opponent": "piloswine", "rating": 633, "opRating": 366}, {"opponent": "ninetales_shadow", "rating": 508, "opRating": 491}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 255}, {"opponent": "furret", "rating": 328}, {"opponent": "jumpluff_shadow", "rating": 362}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 35505}, {"moveId": "FEINT_ATTACK", "uses": 22795}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 24276}, {"moveId": "FLAMETHROWER", "uses": 16651}, {"moveId": "DARK_PULSE", "uses": 9403}, {"moveId": "RETURN", "uses": 7926}]}, "moveset": ["EMBER", "CRUNCH", "FLAMETHROWER"], "score": 59.2}, {"speciesId": "oddish", "speciesName": "<PERSON><PERSON>", "rating": 489, "matchups": [{"opponent": "obstagoon_shadow", "rating": 745, "opRating": 254}, {"opponent": "quagsire_shadow", "rating": 670, "opRating": 329}, {"opponent": "bellossom", "rating": 670, "opRating": 329}, {"opponent": "tropius", "rating": 629, "opRating": 370}, {"opponent": "swampert_shadow", "rating": 504, "opRating": 495}], "counters": [{"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "talonflame", "rating": 292}, {"opponent": "gligar", "rating": 305}, {"opponent": "cradily", "rating": 350}, {"opponent": "furret", "rating": 456}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 40733}, {"moveId": "RAZOR_LEAF", "uses": 17567}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19568}, {"moveId": "SEED_BOMB", "uses": 18727}, {"moveId": "MOONBLAST", "uses": 11560}, {"moveId": "RETURN", "uses": 8393}]}, "moveset": ["ACID", "SEED_BOMB", "SLUDGE_BOMB"], "score": 59.2}, {"speciesId": "tangrowth_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 459, "matchups": [{"opponent": "swampert_shadow", "rating": 878, "opRating": 121}, {"opponent": "claydol", "rating": 790, "opRating": 209}, {"opponent": "quagsire_shadow", "rating": 753, "opRating": 246}, {"opponent": "diggersby", "rating": 599, "opRating": 400}, {"opponent": "furret", "rating": 518}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 92}, {"opponent": "gligar", "rating": 156}, {"opponent": "cradily", "rating": 315}, {"opponent": "clodsire", "rating": 362}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33830}, {"moveId": "INFESTATION", "uses": 24470}], "chargedMoves": [{"moveId": "POWER_WHIP", "uses": 20003}, {"moveId": "ROCK_SLIDE", "uses": 14958}, {"moveId": "SLUDGE_BOMB", "uses": 11978}, {"moveId": "ANCIENT_POWER", "uses": 7117}, {"moveId": "SOLAR_BEAM", "uses": 4217}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "ROCK_SLIDE"], "score": 59.1}, {"speciesId": "delcatty", "speciesName": "Delcat<PERSON>", "rating": 450, "matchups": [{"opponent": "cacturne_shadow", "rating": 771, "opRating": 228}, {"opponent": "drampa", "rating": 735, "opRating": 264}, {"opponent": "flygon", "rating": 722, "opRating": 277}, {"opponent": "flygon_shadow", "rating": 722, "opRating": 277}, {"opponent": "ursaring", "rating": 555, "opRating": 444}], "counters": [{"opponent": "talonflame", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "cradily", "rating": 322}, {"opponent": "gligar", "rating": 339}, {"opponent": "furret", "rating": 381}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 28766}, {"moveId": "CHARM", "uses": 21787}, {"moveId": "ZEN_HEADBUTT", "uses": 7681}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 29022}, {"moveId": "DISARMING_VOICE", "uses": 23251}, {"moveId": "PLAY_ROUGH", "uses": 6081}]}, "moveset": ["CHARM", "WILD_CHARGE", "DISARMING_VOICE"], "score": 58.6}, {"speciesId": "unfezant_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 478, "matchups": [{"opponent": "leavanny", "rating": 922, "opRating": 77}, {"opponent": "bewear", "rating": 872, "opRating": 127}, {"opponent": "parasect", "rating": 823, "opRating": 176}, {"opponent": "roserade", "rating": 799, "opRating": 200}, {"opponent": "sceptile_shadow", "rating": 786, "opRating": 213}], "counters": [{"opponent": "cradily", "rating": 256}, {"opponent": "furret", "rating": 265}, {"opponent": "talonflame", "rating": 329}, {"opponent": "jumpluff_shadow", "rating": 346}, {"opponent": "gligar", "rating": 347}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32697}, {"moveId": "STEEL_WING", "uses": 25603}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33541}, {"moveId": "HYPER_BEAM", "uses": 17247}, {"moveId": "HEAT_WAVE", "uses": 7445}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 58.6}, {"speciesId": "eevee", "speciesName": "Eevee", "rating": 455, "matchups": [{"opponent": "growlithe", "rating": 711, "opRating": 288}, {"opponent": "heatran", "rating": 711, "opRating": 288}, {"opponent": "charm<PERSON>on_shadow", "rating": 670, "opRating": 329}, {"opponent": "typhlosion_shadow", "rating": 655, "opRating": 344}, {"opponent": "arcanine_shadow", "rating": 566, "opRating": 433}], "counters": [{"opponent": "jumpluff_shadow", "rating": 248}, {"opponent": "cradily", "rating": 250}, {"opponent": "furret", "rating": 268}, {"opponent": "talonflame", "rating": 325}, {"opponent": "gligar", "rating": 404}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 33686}, {"moveId": "TACKLE", "uses": 24614}], "chargedMoves": [{"moveId": "SWIFT", "uses": 28712}, {"moveId": "BODY_SLAM", "uses": 11908}, {"moveId": "DIG", "uses": 11415}, {"moveId": "LAST_RESORT", "uses": 6207}]}, "moveset": ["QUICK_ATTACK", "SWIFT", "DIG"], "score": 58.5}, {"speciesId": "phanpy", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 448, "matchups": [{"opponent": "zangoose", "rating": 771, "opRating": 228}, {"opponent": "nidoqueen_shadow", "rating": 647, "opRating": 352}, {"opponent": "ursaring", "rating": 615, "opRating": 384}, {"opponent": "magmar_shadow", "rating": 588, "opRating": 411}, {"opponent": "quagsire_shadow", "rating": 561, "opRating": 438}], "counters": [{"opponent": "furret", "rating": 265}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 326}, {"opponent": "gligar", "rating": 354}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 38713}, {"moveId": "ROCK_SMASH", "uses": 19587}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 15652}, {"moveId": "BODY_SLAM", "uses": 14865}, {"moveId": "ROCK_SLIDE", "uses": 13993}, {"moveId": "BULLDOZE", "uses": 10032}, {"moveId": "RETURN", "uses": 3720}]}, "moveset": ["TACKLE", "TRAILBLAZE", "BODY_SLAM"], "score": 58.5}, {"speciesId": "shaymin_land", "speciesName": "<PERSON><PERSON> (Land)", "rating": 426, "matchups": [{"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "quagsire_shadow", "rating": 882, "opRating": 117}, {"opponent": "claydol", "rating": 806, "opRating": 193}, {"opponent": "diggersby", "rating": 583, "opRating": 416}, {"opponent": "furret", "rating": 556}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "cradily", "rating": 208}, {"opponent": "gligar", "rating": 217}, {"opponent": "talonflame", "rating": 225}, {"opponent": "clodsire", "rating": 492}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 7554}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4388}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3863}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3683}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3401}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3399}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3368}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3061}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3053}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2958}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2899}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2886}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2792}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2672}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2597}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2388}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2108}, {"moveId": "ZEN_HEADBUTT", "uses": 1234}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31389}, {"moveId": "ENERGY_BALL", "uses": 11078}, {"moveId": "SEED_FLARE", "uses": 9311}, {"moveId": "SOLAR_BEAM", "uses": 6429}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "SEED_FLARE"], "score": 58.5}, {"speciesId": "tangela", "speciesName": "Tangela", "rating": 452, "matchups": [{"opponent": "swampert_shadow", "rating": 879, "opRating": 120}, {"opponent": "claydol", "rating": 801, "opRating": 198}, {"opponent": "rhydon_shadow", "rating": 793, "opRating": 206}, {"opponent": "quagsire_shadow", "rating": 737, "opRating": 262}, {"opponent": "diggersby", "rating": 577, "opRating": 422}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 303}, {"opponent": "cradily", "rating": 340}, {"opponent": "clodsire", "rating": 372}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33920}, {"moveId": "INFESTATION", "uses": 24380}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 19530}, {"moveId": "SLUDGE_BOMB", "uses": 15966}, {"moveId": "POWER_WHIP", "uses": 9775}, {"moveId": "RETURN", "uses": 8925}, {"moveId": "SOLAR_BEAM", "uses": 4111}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 58.5}, {"speciesId": "trumbeak", "speciesName": "Trumbeak", "rating": 479, "matchups": [{"opponent": "leavanny", "rating": 916, "opRating": 83}, {"opponent": "parasect", "rating": 867, "opRating": 132}, {"opponent": "sceptile", "rating": 643, "opRating": 356}, {"opponent": "swampert_shadow", "rating": 511, "opRating": 488}, {"opponent": "marowak_alolan", "rating": 507, "opRating": 492}], "counters": [{"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 246}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "talonflame", "rating": 292}, {"opponent": "gligar", "rating": 309}], "moves": {"fastMoves": [{"moveId": "PECK", "uses": 37816}, {"moveId": "ROCK_SMASH", "uses": 20484}], "chargedMoves": [{"moveId": "DRILL_PECK", "uses": 35031}, {"moveId": "ROCK_BLAST", "uses": 14904}, {"moveId": "SKY_ATTACK", "uses": 8270}]}, "moveset": ["PECK", "DRILL_PECK", "ROCK_BLAST"], "score": 58.2}, {"speciesId": "exploud", "speciesName": "Exploud", "rating": 451, "matchups": [{"opponent": "run<PERSON><PERSON>", "rating": 684, "opRating": 315}, {"opponent": "marowak_alolan", "rating": 614, "opRating": 385}, {"opponent": "gliscor_shadow", "rating": 547, "opRating": 452}, {"opponent": "marowak_alolan_shadow", "rating": 538, "opRating": 461}, {"opponent": "claydol", "rating": 503, "opRating": 496}], "counters": [{"opponent": "furret", "rating": 115}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 350}, {"opponent": "jumpluff_shadow", "rating": 375}, {"opponent": "gligar", "rating": 416}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 37255}, {"moveId": "BITE", "uses": 21045}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 17808}, {"moveId": "BOOMBURST", "uses": 14583}, {"moveId": "DISARMING_VOICE", "uses": 13638}, {"moveId": "FIRE_BLAST", "uses": 6903}, {"moveId": "RETURN", "uses": 5431}]}, "moveset": ["ASTONISH", "CRUNCH", "DISARMING_VOICE"], "score": 58}, {"speciesId": "gumshoos", "speciesName": "<PERSON>ums<PERSON><PERSON>", "rating": 444, "matchups": [{"opponent": "pumpkaboo_super", "rating": 843, "opRating": 156}, {"opponent": "chandelure_shadow", "rating": 768, "opRating": 231}, {"opponent": "typhlosion_hisuian", "rating": 751, "opRating": 248}, {"opponent": "armarouge", "rating": 741, "opRating": 258}, {"opponent": "skeledirge", "rating": 666, "opRating": 333}], "counters": [{"opponent": "jumpluff_shadow", "rating": 235}, {"opponent": "cradily", "rating": 284}, {"opponent": "furret", "rating": 287}, {"opponent": "gligar", "rating": 312}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 39610}, {"moveId": "TAKE_DOWN", "uses": 18690}], "chargedMoves": [{"moveId": "ROCK_TOMB", "uses": 22996}, {"moveId": "CRUNCH", "uses": 18369}, {"moveId": "HYPER_FANG", "uses": 16897}]}, "moveset": ["BITE", "ROCK_TOMB", "CRUNCH"], "score": 57.9}, {"speciesId": "lampent", "speciesName": "<PERSON><PERSON>", "rating": 476, "matchups": [{"opponent": "roserade", "rating": 903, "opRating": 96}, {"opponent": "abomasnow_shadow", "rating": 887, "opRating": 112}, {"opponent": "victree<PERSON>_shadow", "rating": 841, "opRating": 158}, {"opponent": "piloswine", "rating": 717, "opRating": 282}, {"opponent": "diggersby", "rating": 503, "opRating": 496}], "counters": [{"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 253}, {"opponent": "furret", "rating": 265}, {"opponent": "gligar", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 444}], "moves": {"fastMoves": [{"moveId": "EMBER", "uses": 31370}, {"moveId": "ASTONISH", "uses": 26930}], "chargedMoves": [{"moveId": "ENERGY_BALL", "uses": 19605}, {"moveId": "FLAME_BURST", "uses": 18742}, {"moveId": "RETURN", "uses": 14941}, {"moveId": "HEAT_WAVE", "uses": 5026}]}, "moveset": ["EMBER", "ENERGY_BALL", "FLAME_BURST"], "score": 57.9}, {"speciesId": "exploud_shadow", "speciesName": "Exploud (Shadow)", "rating": 439, "matchups": [{"opponent": "victini", "rating": 821, "opRating": 178}, {"opponent": "armarouge", "rating": 757, "opRating": 242}, {"opponent": "claydol", "rating": 652, "opRating": 347}, {"opponent": "gliscor", "rating": 547, "opRating": 452}, {"opponent": "marowak_alolan", "rating": 538, "opRating": 461}], "counters": [{"opponent": "furret", "rating": 128}, {"opponent": "jumpluff_shadow", "rating": 209}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 371}, {"opponent": "gligar", "rating": 450}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 38823}, {"moveId": "BITE", "uses": 19477}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19543}, {"moveId": "BOOMBURST", "uses": 16258}, {"moveId": "DISARMING_VOICE", "uses": 15001}, {"moveId": "FIRE_BLAST", "uses": 7531}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ASTONISH", "CRUNCH", "DISARMING_VOICE"], "score": 57.7}, {"speciesId": "simisage", "speciesName": "Simisage", "rating": 439, "matchups": [{"opponent": "swampert_shadow", "rating": 853, "opRating": 146}, {"opponent": "claydol", "rating": 773, "opRating": 226}, {"opponent": "quagsire_shadow", "rating": 738, "opRating": 261}, {"opponent": "diggersby", "rating": 571, "opRating": 428}, {"opponent": "flygon", "rating": 551, "opRating": 448}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "furret", "rating": 228}, {"opponent": "talonflame", "rating": 270}, {"opponent": "cradily", "rating": 361}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 39655}, {"moveId": "BITE", "uses": 18645}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 27062}, {"moveId": "GRASS_KNOT", "uses": 25780}, {"moveId": "SOLAR_BEAM", "uses": 5476}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "CRUNCH"], "score": 57.7}, {"speciesId": "fearow", "speciesName": "<PERSON>ow", "rating": 450, "matchups": [{"opponent": "rhydon_shadow", "rating": 826, "opRating": 173}, {"opponent": "gligar", "rating": 572}, {"opponent": "gliscor", "rating": 560, "opRating": 439}, {"opponent": "gligar_shadow", "rating": 536, "opRating": 463}, {"opponent": "swampert_shadow", "rating": 524, "opRating": 475}], "counters": [{"opponent": "clodsire", "rating": 158}, {"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "talonflame", "rating": 196}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 298}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 31941}, {"moveId": "PECK", "uses": 26359}], "chargedMoves": [{"moveId": "FLY", "uses": 20053}, {"moveId": "AERIAL_ACE", "uses": 15307}, {"moveId": "DRILL_RUN", "uses": 13914}, {"moveId": "SKY_ATTACK", "uses": 5121}, {"moveId": "TWISTER", "uses": 4034}]}, "moveset": ["STEEL_WING", "FLY", "DRILL_RUN"], "score": 57.3}, {"speciesId": "cinccino", "speciesName": "Cinccino", "rating": 447, "matchups": [{"opponent": "bewear", "rating": 890, "opRating": 109}, {"opponent": "flygon", "rating": 667, "opRating": 332}, {"opponent": "drampa", "rating": 664, "opRating": 335}, {"opponent": "flygon_shadow", "rating": 664, "opRating": 335}, {"opponent": "ursaring_shadow", "rating": 597, "opRating": 402}], "counters": [{"opponent": "talonflame", "rating": 240}, {"opponent": "jumpluff_shadow", "rating": 254}, {"opponent": "furret", "rating": 287}, {"opponent": "cradily", "rating": 288}, {"opponent": "gligar", "rating": 309}], "moves": {"fastMoves": [{"moveId": "CHARM", "uses": 46854}, {"moveId": "POUND", "uses": 11446}], "chargedMoves": [{"moveId": "AQUA_TAIL", "uses": 37413}, {"moveId": "HYPER_BEAM", "uses": 11527}, {"moveId": "THUNDERBOLT", "uses": 9341}]}, "moveset": ["CHARM", "AQUA_TAIL", "THUNDERBOLT"], "score": 57.1}, {"speciesId": "loudred", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 432, "matchups": [{"opponent": "decid<PERSON><PERSON>", "rating": 879, "opRating": 120}, {"opponent": "victini", "rating": 842, "opRating": 157}, {"opponent": "golurk", "rating": 682, "opRating": 317}, {"opponent": "golurk_shadow", "rating": 676, "opRating": 323}, {"opponent": "marowak_alolan_shadow", "rating": 556, "opRating": 443}], "counters": [{"opponent": "cradily", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 287}, {"opponent": "talonflame", "rating": 292}, {"opponent": "furret", "rating": 325}, {"opponent": "gligar", "rating": 370}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33105}, {"moveId": "ROCK_SMASH", "uses": 25195}], "chargedMoves": [{"moveId": "STOMP", "uses": 19381}, {"moveId": "DISARMING_VOICE", "uses": 14637}, {"moveId": "FLAMETHROWER", "uses": 12998}, {"moveId": "RETURN", "uses": 11242}]}, "moveset": ["BITE", "STOMP", "DISARMING_VOICE"], "score": 56.7}, {"speciesId": "gabite", "speciesName": "Gabite", "rating": 427, "matchups": [{"opponent": "ninetales", "rating": 700, "opRating": 299}, {"opponent": "ninetales_shadow", "rating": 632, "opRating": 367}, {"opponent": "magcargo", "rating": 625, "opRating": 375}, {"opponent": "clodsire", "rating": 549}, {"opponent": "cradily", "rating": 503}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "gligar", "rating": 103}, {"opponent": "furret", "rating": 153}, {"opponent": "talonflame", "rating": 292}, {"opponent": "diggersby", "rating": 313}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 49414}, {"moveId": "TAKE_DOWN", "uses": 8886}], "chargedMoves": [{"moveId": "DIG", "uses": 19479}, {"moveId": "FLAMETHROWER", "uses": 16547}, {"moveId": "TWISTER", "uses": 12266}, {"moveId": "RETURN", "uses": 10031}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 56.5}, {"speciesId": "volcanion", "speciesName": "Volcanion", "rating": 477, "matchups": [{"opponent": "abomasnow_shadow", "rating": 936, "opRating": 63}, {"opponent": "piloswine", "rating": 898, "opRating": 101}, {"opponent": "abomasnow", "rating": 830, "opRating": 169}, {"opponent": "victree<PERSON>_shadow", "rating": 825, "opRating": 174}, {"opponent": "ninetales_shadow", "rating": 567, "opRating": 432}], "counters": [{"opponent": "talonflame", "rating": 225}, {"opponent": "gligar", "rating": 232}, {"opponent": "cradily", "rating": 260}, {"opponent": "furret", "rating": 265}, {"opponent": "jumpluff_shadow", "rating": 343}], "moves": {"fastMoves": [{"moveId": "INCINERATE", "uses": 36703}, {"moveId": "WATER_GUN", "uses": 19352}, {"moveId": "TAKE_DOWN", "uses": 2195}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 16873}, {"moveId": "HYDRO_PUMP", "uses": 15539}, {"moveId": "EARTH_POWER", "uses": 13358}, {"moveId": "SLUDGE_BOMB", "uses": 12668}]}, "moveset": ["INCINERATE", "OVERHEAT", "HYDRO_PUMP"], "score": 56.5}, {"speciesId": "pupitar", "speciesName": "Pupitar", "rating": 418, "matchups": [{"opponent": "victini", "rating": 877, "opRating": 122}, {"opponent": "chandelure_shadow", "rating": 827, "opRating": 172}, {"opponent": "ceruledge", "rating": 823, "opRating": 176}, {"opponent": "staraptor_shadow", "rating": 823, "opRating": 176}, {"opponent": "typhlosion_hisuian", "rating": 816, "opRating": 183}], "counters": [{"opponent": "furret", "rating": 237}, {"opponent": "cradily", "rating": 243}, {"opponent": "gligar", "rating": 320}, {"opponent": "jumpluff_shadow", "rating": 356}, {"opponent": "talonflame", "rating": 451}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 33923}, {"moveId": "ROCK_SMASH", "uses": 24377}], "chargedMoves": [{"moveId": "ANCIENT_POWER", "uses": 19354}, {"moveId": "CRUNCH", "uses": 16732}, {"moveId": "DIG", "uses": 14652}, {"moveId": "RETURN", "uses": 7557}]}, "moveset": ["BITE", "ANCIENT_POWER", "DIG"], "score": 56.4}, {"speciesId": "rotom_heat", "speciesName": "<PERSON><PERSON><PERSON> (Heat)", "rating": 470, "matchups": [{"opponent": "gliscor", "rating": 736, "opRating": 263}, {"opponent": "pidgeot", "rating": 672, "opRating": 327}, {"opponent": "piloswine", "rating": 655, "opRating": 344}, {"opponent": "jumpluff_shadow", "rating": 634}, {"opponent": "ninetales_shadow", "rating": 596, "opRating": 403}], "counters": [{"opponent": "gligar", "rating": 95}, {"opponent": "clodsire", "rating": 137}, {"opponent": "furret", "rating": 165}, {"opponent": "talonflame", "rating": 292}, {"opponent": "cradily", "rating": 354}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 33796}, {"moveId": "ASTONISH", "uses": 24504}], "chargedMoves": [{"moveId": "OVERHEAT", "uses": 30994}, {"moveId": "THUNDERBOLT", "uses": 19028}, {"moveId": "THUNDER", "uses": 8294}]}, "moveset": ["THUNDER_SHOCK", "OVERHEAT", "THUNDERBOLT"], "score": 56.4}, {"speciesId": "vileplume", "speciesName": "Vileplume", "rating": 468, "matchups": [{"opponent": "dart<PERSON>", "rating": 887, "opRating": 112}, {"opponent": "whimsicott", "rating": 883, "opRating": 116}, {"opponent": "bellossom", "rating": 637, "opRating": 362}, {"opponent": "bellossom_shadow", "rating": 633, "opRating": 366}, {"opponent": "claydol", "rating": 620, "opRating": 379}], "counters": [{"opponent": "gligar", "rating": 152}, {"opponent": "talonflame", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "cradily", "rating": 364}, {"opponent": "furret", "rating": 487}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41104}, {"moveId": "RAZOR_LEAF", "uses": 17196}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23722}, {"moveId": "PETAL_BLIZZARD", "uses": 14648}, {"moveId": "MOONBLAST", "uses": 13993}, {"moveId": "SOLAR_BEAM", "uses": 5853}]}, "moveset": ["ACID", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 56.4}, {"speciesId": "komala", "speciesName": "Komala", "rating": 428, "matchups": [{"opponent": "talonflame", "rating": 665}, {"opponent": "skeledirge", "rating": 665, "opRating": 334}, {"opponent": "typhlosion", "rating": 646, "opRating": 353}, {"opponent": "fletchinder", "rating": 610, "opRating": 389}, {"opponent": "typhlosion_shadow", "rating": 573, "opRating": 426}], "counters": [{"opponent": "cradily", "rating": 145}, {"opponent": "gligar", "rating": 175}, {"opponent": "jumpluff_shadow", "rating": 212}, {"opponent": "furret", "rating": 253}, {"opponent": "clodsire", "rating": 286}], "moves": {"fastMoves": [{"moveId": "ROLLOUT", "uses": 57428}, {"moveId": "YAWN", "uses": 872}], "chargedMoves": [{"moveId": "PAYBACK", "uses": 25411}, {"moveId": "BULLDOZE", "uses": 17246}, {"moveId": "PLAY_ROUGH", "uses": 15680}]}, "moveset": ["ROLLOUT", "PAYBACK", "BULLDOZE"], "score": 56.2}, {"speciesId": "ambipom", "speciesName": "Ambipom", "rating": 462, "matchups": [{"opponent": "amoon<PERSON><PERSON>_shadow", "rating": 866, "opRating": 133}, {"opponent": "pumpkaboo_super", "rating": 834, "opRating": 165}, {"opponent": "skeledirge", "rating": 689, "opRating": 310}, {"opponent": "flygon", "rating": 576, "opRating": 423}, {"opponent": "marowak_alolan", "rating": 556, "opRating": 443}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "jumpluff_shadow", "rating": 186}, {"opponent": "gligar", "rating": 248}, {"opponent": "talonflame", "rating": 300}, {"opponent": "cradily", "rating": 302}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 34197}, {"moveId": "SCRATCH", "uses": 24103}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 24720}, {"moveId": "RETURN", "uses": 14132}, {"moveId": "LOW_SWEEP", "uses": 13925}, {"moveId": "HYPER_BEAM", "uses": 5475}]}, "moveset": ["ASTONISH", "AERIAL_ACE", "RETURN"], "score": 56.1}, {"speciesId": "persian_shadow", "speciesName": "Persian (Shadow)", "rating": 455, "matchups": [{"opponent": "pumpkaboo_super", "rating": 850, "opRating": 149}, {"opponent": "oranguru", "rating": 667, "opRating": 332}, {"opponent": "noctowl", "rating": 667, "opRating": 332}, {"opponent": "skeledirge", "rating": 594, "opRating": 405}, {"opponent": "marowak_alolan", "rating": 554, "opRating": 445}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "cradily", "rating": 184}, {"opponent": "furret", "rating": 228}, {"opponent": "gligar", "rating": 309}, {"opponent": "talonflame", "rating": 381}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 32240}, {"moveId": "SCRATCH", "uses": 26060}], "chargedMoves": [{"moveId": "NIGHT_SLASH", "uses": 21829}, {"moveId": "POWER_GEM", "uses": 15740}, {"moveId": "FOUL_PLAY", "uses": 8147}, {"moveId": "PLAY_ROUGH", "uses": 7184}, {"moveId": "PAYBACK", "uses": 5363}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["FEINT_ATTACK", "NIGHT_SLASH", "POWER_GEM"], "score": 56.1}, {"speciesId": "sandygast", "speciesName": "Sandygast", "rating": 423, "matchups": [{"opponent": "nidoqueen", "rating": 725, "opRating": 274}, {"opponent": "nidoqueen_shadow", "rating": 696, "opRating": 303}, {"opponent": "claydol", "rating": 662, "opRating": 337}, {"opponent": "magmar_shadow", "rating": 640, "opRating": 359}, {"opponent": "clodsire", "rating": 585, "opRating": 414}], "counters": [{"opponent": "furret", "rating": 103}, {"opponent": "cradily", "rating": 239}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "talonflame", "rating": 300}, {"opponent": "gligar", "rating": 385}], "moves": {"fastMoves": [{"moveId": "ASTONISH", "uses": 20372}, {"moveId": "SAND_ATTACK", "uses": 19986}, {"moveId": "MUD_SHOT", "uses": 17879}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 22523}, {"moveId": "SHADOW_BALL", "uses": 19514}, {"moveId": "EARTH_POWER", "uses": 8865}, {"moveId": "SAND_TOMB", "uses": 7497}]}, "moveset": ["ASTONISH", "SCORCHING_SANDS", "SHADOW_BALL"], "score": 56.1}, {"speciesId": "watchog", "speciesName": "Watchog", "rating": 426, "matchups": [{"opponent": "pumpkaboo_average", "rating": 868, "opRating": 132}, {"opponent": "decid<PERSON><PERSON>", "rating": 856, "opRating": 144}, {"opponent": "victini", "rating": 820, "opRating": 180}, {"opponent": "golurk", "rating": 688, "opRating": 312}, {"opponent": "marowak_alolan_shadow", "rating": 548, "opRating": 452}], "counters": [{"opponent": "jumpluff_shadow", "rating": 238}, {"opponent": "cradily", "rating": 270}, {"opponent": "talonflame", "rating": 300}, {"opponent": "furret", "rating": 325}, {"opponent": "gligar", "rating": 377}], "moves": {"fastMoves": [{"moveId": "BITE", "uses": 38593}, {"moveId": "LOW_KICK", "uses": 19707}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 19047}, {"moveId": "HYPER_FANG", "uses": 17650}, {"moveId": "GRASS_KNOT", "uses": 15545}, {"moveId": "RETURN", "uses": 6034}]}, "moveset": ["BITE", "CRUNCH", "GRASS_KNOT"], "score": 55.9}, {"speciesId": "celebi", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 431, "matchups": [{"opponent": "swampert_shadow", "rating": 903, "opRating": 96}, {"opponent": "swampert", "rating": 903, "opRating": 96}, {"opponent": "quagsire_shadow", "rating": 879, "opRating": 120}, {"opponent": "claydol", "rating": 786, "opRating": 213}, {"opponent": "diggersby", "rating": 565, "opRating": 434}], "counters": [{"opponent": "jumpluff_shadow", "rating": 71}, {"opponent": "talonflame", "rating": 185}, {"opponent": "gligar", "rating": 190}, {"opponent": "furret", "rating": 200}, {"opponent": "cradily", "rating": 208}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 24832}, {"moveId": "MAGICAL_LEAF", "uses": 23203}, {"moveId": "CHARGE_BEAM", "uses": 10256}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 15103}, {"moveId": "PSYCHIC", "uses": 13767}, {"moveId": "LEAF_STORM", "uses": 11875}, {"moveId": "DAZZLING_GLEAM", "uses": 10848}, {"moveId": "HYPER_BEAM", "uses": 6718}]}, "moveset": ["MAGICAL_LEAF", "SEED_BOMB", "LEAF_STORM"], "score": 55.8}, {"speciesId": "chespin", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 422, "matchups": [{"opponent": "swampert_shadow", "rating": 897, "opRating": 102}, {"opponent": "quagsire_shadow", "rating": 886, "opRating": 113}, {"opponent": "quagsire", "rating": 886, "opRating": 113}, {"opponent": "marowak_shadow", "rating": 790, "opRating": 209}, {"opponent": "claydol", "rating": 558, "opRating": 441}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "cradily", "rating": 236}, {"opponent": "talonflame", "rating": 311}, {"opponent": "furret", "rating": 350}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 48688}, {"moveId": "TAKE_DOWN", "uses": 9612}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 27618}, {"moveId": "SEED_BOMB", "uses": 22227}, {"moveId": "GYRO_BALL", "uses": 8438}]}, "moveset": ["VINE_WHIP", "BODY_SLAM", "SEED_BOMB"], "score": 55.8}, {"speciesId": "tangela_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 416, "matchups": [{"opponent": "swampert_shadow", "rating": 905, "opRating": 94}, {"opponent": "quagsire_shadow", "rating": 879, "opRating": 120}, {"opponent": "rhydon", "rating": 793, "opRating": 206}, {"opponent": "claydol", "rating": 762, "opRating": 237}, {"opponent": "diggersby", "rating": 534, "opRating": 465}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 194}, {"opponent": "furret", "rating": 228}, {"opponent": "talonflame", "rating": 359}, {"opponent": "cradily", "rating": 420}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 33963}, {"moveId": "INFESTATION", "uses": 24337}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 22497}, {"moveId": "SLUDGE_BOMB", "uses": 19742}, {"moveId": "POWER_WHIP", "uses": 11257}, {"moveId": "SOLAR_BEAM", "uses": 4750}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "GRASS_KNOT", "SLUDGE_BOMB"], "score": 55.8}, {"speciesId": "lilligant", "speciesName": "Lilligant", "rating": 439, "matchups": [{"opponent": "chesnaught", "rating": 850, "opRating": 149}, {"opponent": "bewear", "rating": 776, "opRating": 223}, {"opponent": "flygon_shadow", "rating": 666, "opRating": 333}, {"opponent": "flygon", "rating": 653, "opRating": 346}, {"opponent": "drampa", "rating": 644, "opRating": 355}], "counters": [{"opponent": "talonflame", "rating": 177}, {"opponent": "gligar", "rating": 267}, {"opponent": "cradily", "rating": 284}, {"opponent": "jumpluff_shadow", "rating": 287}, {"opponent": "furret", "rating": 325}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 7342}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4210}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3661}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3526}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3302}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3296}, {"moveId": "CHARM", "uses": 3258}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3243}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2957}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2931}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2847}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2827}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2811}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2722}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2588}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2564}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2323}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2123}], "chargedMoves": [{"moveId": "PETAL_BLIZZARD", "uses": 26882}, {"moveId": "HYPER_BEAM", "uses": 20671}, {"moveId": "SOLAR_BEAM", "uses": 10826}]}, "moveset": ["CHARM", "PETAL_BLIZZARD", "HYPER_BEAM"], "score": 55.2}, {"speciesId": "raticate", "speciesName": "Raticate", "rating": 462, "matchups": [{"opponent": "swellow", "rating": 729, "opRating": 270}, {"opponent": "turtonator", "rating": 668, "opRating": 331}, {"opponent": "nidoqueen", "rating": 639, "opRating": 360}, {"opponent": "farfetchd", "rating": 594, "opRating": 405}, {"opponent": "fletchinder", "rating": 512, "opRating": 487}], "counters": [{"opponent": "jumpluff_shadow", "rating": 173}, {"opponent": "furret", "rating": 190}, {"opponent": "talonflame", "rating": 244}, {"opponent": "cradily", "rating": 246}, {"opponent": "gligar", "rating": 442}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 39622}, {"moveId": "BITE", "uses": 18678}], "chargedMoves": [{"moveId": "HYPER_FANG", "uses": 25434}, {"moveId": "DIG", "uses": 17526}, {"moveId": "RETURN", "uses": 8697}, {"moveId": "HYPER_BEAM", "uses": 6750}]}, "moveset": ["QUICK_ATTACK", "HYPER_FANG", "DIG"], "score": 55}, {"speciesId": "<PERSON><PERSON><PERSON>", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 401, "matchups": [{"opponent": "swampert_shadow", "rating": 916, "opRating": 83}, {"opponent": "quagsire_shadow", "rating": 881, "opRating": 118}, {"opponent": "claydol", "rating": 767, "opRating": 232}, {"opponent": "diggersby", "rating": 572, "opRating": 427}, {"opponent": "furret", "rating": 503}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 194}, {"opponent": "cradily", "rating": 208}, {"opponent": "talonflame", "rating": 225}, {"opponent": "clodsire", "rating": 358}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 36822}, {"moveId": "CHARM", "uses": 21478}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31692}, {"moveId": "PLAY_ROUGH", "uses": 15247}, {"moveId": "ENERGY_BALL", "uses": 11269}]}, "moveset": ["LEAFAGE", "GRASS_KNOT", "PLAY_ROUGH"], "score": 54.9}, {"speciesId": "oddish_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 476, "matchups": [{"opponent": "lopunny", "rating": 820, "opRating": 179}, {"opponent": "obstagoon_shadow", "rating": 745, "opRating": 254}, {"opponent": "obstagoon", "rating": 745, "opRating": 254}, {"opponent": "lileep_shadow", "rating": 695, "opRating": 304}, {"opponent": "quagsire_shadow", "rating": 595, "opRating": 404}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "furret", "rating": 221}, {"opponent": "talonflame", "rating": 237}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "cradily", "rating": 343}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41432}, {"moveId": "RAZOR_LEAF", "uses": 16868}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23204}, {"moveId": "SEED_BOMB", "uses": 21467}, {"moveId": "MOONBLAST", "uses": 13559}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "SEED_BOMB", "SLUDGE_BOMB"], "score": 54.3}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "speciesName": "Eldegoss", "rating": 441, "matchups": [{"opponent": "whiscash_shadow", "rating": 894, "opRating": 105}, {"opponent": "whiscash", "rating": 894, "opRating": 105}, {"opponent": "quagsire_shadow", "rating": 798, "opRating": 201}, {"opponent": "swampert_shadow", "rating": 613, "opRating": 386}, {"opponent": "claydol", "rating": 537, "opRating": 462}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 95}, {"opponent": "talonflame", "rating": 159}, {"opponent": "cradily", "rating": 253}, {"opponent": "furret", "rating": 387}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35270}, {"moveId": "RAZOR_LEAF", "uses": 23030}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 43142}, {"moveId": "ENERGY_BALL", "uses": 15158}]}, "moveset": ["BULLET_SEED", "GRASS_KNOT", "ENERGY_BALL"], "score": 54.1}, {"speciesId": "gloom_shadow", "speciesName": "Gloom (Shadow)", "rating": 467, "matchups": [{"opponent": "whimsicott", "rating": 893, "opRating": 106}, {"opponent": "dart<PERSON>", "rating": 847, "opRating": 152}, {"opponent": "dubwool", "rating": 702, "opRating": 297}, {"opponent": "gogoat", "rating": 702, "opRating": 297}, {"opponent": "lickilicky", "rating": 660, "opRating": 339}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 287}, {"opponent": "talonflame", "rating": 314}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 41606}, {"moveId": "RAZOR_LEAF", "uses": 16694}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 25358}, {"moveId": "PETAL_BLIZZARD", "uses": 17318}, {"moveId": "MOONBLAST", "uses": 15398}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 53.8}, {"speciesId": "lombre", "speciesName": "Lombre", "rating": 400, "matchups": [{"opponent": "diggersby_shadow", "rating": 644, "opRating": 355}, {"opponent": "claydol", "rating": 595, "opRating": 404}, {"opponent": "quagsire_shadow", "rating": 573, "opRating": 426}, {"opponent": "diggersby", "rating": 545, "opRating": 454}, {"opponent": "piloswine", "rating": 538, "opRating": 461}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "cradily", "rating": 145}, {"opponent": "furret", "rating": 200}, {"opponent": "gligar", "rating": 217}, {"opponent": "talonflame", "rating": 300}], "moves": {"fastMoves": [{"moveId": "BUBBLE", "uses": 42986}, {"moveId": "RAZOR_LEAF", "uses": 15314}], "chargedMoves": [{"moveId": "SCALD", "uses": 21767}, {"moveId": "ICE_BEAM", "uses": 16294}, {"moveId": "GRASS_KNOT", "uses": 13932}, {"moveId": "BUBBLE_BEAM", "uses": 6390}]}, "moveset": ["BUBBLE", "SCALD", "GRASS_KNOT"], "score": 53.7}, {"speciesId": "porygon_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 433, "matchups": [{"opponent": "amoon<PERSON>s", "rating": 681, "opRating": 318}, {"opponent": "oinkologne", "rating": 651, "opRating": 348}, {"opponent": "linoone", "rating": 640, "opRating": 359}, {"opponent": "pidgeotto", "rating": 603, "opRating": 396}, {"opponent": "dunsparce", "rating": 600, "opRating": 400}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "gligar", "rating": 290}, {"opponent": "talonflame", "rating": 292}, {"opponent": "furret", "rating": 315}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 5158}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4358}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3898}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3426}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3412}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3395}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3166}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3054}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3035}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2934}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2888}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2885}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2878}, {"moveId": "CHARGE_BEAM", "uses": 2838}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2639}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2590}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2414}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2055}, {"moveId": "ZEN_HEADBUTT", "uses": 1115}], "chargedMoves": [{"moveId": "SIGNAL_BEAM", "uses": 12925}, {"moveId": "DISCHARGE", "uses": 12234}, {"moveId": "HYPER_BEAM", "uses": 10405}, {"moveId": "SOLAR_BEAM", "uses": 9555}, {"moveId": "ZAP_CANNON", "uses": 6539}, {"moveId": "PSYBEAM", "uses": 6505}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "DISCHARGE", "SIGNAL_BEAM"], "score": 53.7}, {"speciesId": "exeggcute", "speciesName": "Exeggcute", "rating": 431, "matchups": [{"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 816, "opRating": 183}, {"opponent": "whiscash", "rating": 757, "opRating": 242}, {"opponent": "whiscash_shadow", "rating": 676, "opRating": 323}, {"opponent": "quagsire", "rating": 672, "opRating": 327}, {"opponent": "quagsire_shadow", "rating": 577, "opRating": 422}], "counters": [{"opponent": "jumpluff_shadow", "rating": 166}, {"opponent": "gligar", "rating": 175}, {"opponent": "furret", "rating": 206}, {"opponent": "cradily", "rating": 239}, {"opponent": "talonflame", "rating": 270}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30850}, {"moveId": "BULLET_SEED", "uses": 27450}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 19052}, {"moveId": "ANCIENT_POWER", "uses": 17720}, {"moveId": "PSYCHIC", "uses": 13274}, {"moveId": "RETURN", "uses": 8301}]}, "moveset": ["CONFUSION", "SEED_BOMB", "ANCIENT_POWER"], "score": 53.5}, {"speciesId": "weepinbell", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 455, "matchups": [{"opponent": "whimsicott", "rating": 899, "opRating": 100}, {"opponent": "dart<PERSON>", "rating": 856, "opRating": 143}, {"opponent": "sceptile_shadow", "rating": 737, "opRating": 262}, {"opponent": "bellossom", "rating": 633, "opRating": 366}, {"opponent": "lura<PERSON>s", "rating": 600, "opRating": 399}], "counters": [{"opponent": "gligar", "rating": 118}, {"opponent": "talonflame", "rating": 203}, {"opponent": "furret", "rating": 221}, {"opponent": "jumpluff_shadow", "rating": 290}, {"opponent": "cradily", "rating": 402}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 28349}, {"moveId": "BULLET_SEED", "uses": 20420}, {"moveId": "RAZOR_LEAF", "uses": 9491}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19144}, {"moveId": "POWER_WHIP", "uses": 17569}, {"moveId": "SEED_BOMB", "uses": 14032}, {"moveId": "RETURN", "uses": 7656}]}, "moveset": ["ACID", "POWER_WHIP", "SLUDGE_BOMB"], "score": 53.1}, {"speciesId": "stant<PERSON>_shadow", "speciesName": "<PERSON><PERSON> (Shadow)", "rating": 416, "matchups": [{"opponent": "castform", "rating": 695, "opRating": 304}, {"opponent": "lickitung", "rating": 679, "opRating": 320}, {"opponent": "staraptor", "rating": 667, "opRating": 332}, {"opponent": "linoone", "rating": 609, "opRating": 390}, {"opponent": "noctowl", "rating": 578, "opRating": 421}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "furret", "rating": 265}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 388}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 49383}, {"moveId": "ZEN_HEADBUTT", "uses": 8917}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 20314}, {"moveId": "STOMP", "uses": 19503}, {"moveId": "MEGAHORN", "uses": 18498}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 52.9}, {"speciesId": "trapinch", "speciesName": "<PERSON><PERSON><PERSON><PERSON>", "rating": 380, "matchups": [{"opponent": "clodsire", "rating": 845, "opRating": 154}, {"opponent": "lileep_shadow", "rating": 795, "opRating": 204}, {"opponent": "nidoqueen", "rating": 687, "opRating": 312}, {"opponent": "magcargo", "rating": 612, "opRating": 387}, {"opponent": "skeledirge", "rating": 554, "opRating": 445}], "counters": [{"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "gligar", "rating": 83}, {"opponent": "furret", "rating": 200}, {"opponent": "cradily", "rating": 319}, {"opponent": "talonflame", "rating": 396}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24971}, {"moveId": "MUD_SHOT", "uses": 22071}, {"moveId": "STRUGGLE_BUG", "uses": 11266}], "chargedMoves": [{"moveId": "SCORCHING_SANDS", "uses": 18970}, {"moveId": "CRUNCH", "uses": 18168}, {"moveId": "RETURN", "uses": 8456}, {"moveId": "DIG", "uses": 6389}, {"moveId": "SAND_TOMB", "uses": 6266}]}, "moveset": ["SAND_ATTACK", "SCORCHING_SANDS", "CRUNCH"], "score": 52.9}, {"speciesId": "blissey", "speciesName": "<PERSON><PERSON>", "rating": 407, "matchups": [{"opponent": "pidgeot", "rating": 604, "opRating": 395}, {"opponent": "bewear", "rating": 581, "opRating": 418}, {"opponent": "farfetchd", "rating": 570, "opRating": 429}, {"opponent": "pidgeot_shadow", "rating": 558, "opRating": 441}, {"opponent": "fletchinder", "rating": 543, "opRating": 456}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "cradily", "rating": 281}, {"opponent": "gligar", "rating": 358}, {"opponent": "jumpluff_shadow", "rating": 372}, {"opponent": "furret", "rating": 384}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 32340}, {"moveId": "POUND", "uses": 25960}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22786}, {"moveId": "DAZZLING_GLEAM", "uses": 13713}, {"moveId": "PSYCHIC", "uses": 11147}, {"moveId": "HYPER_BEAM", "uses": 10693}]}, "moveset": ["ZEN_HEADBUTT", "WILD_CHARGE", "DAZZLING_GLEAM"], "score": 52.8}, {"speciesId": "stantler", "speciesName": "<PERSON><PERSON>", "rating": 428, "matchups": [{"opponent": "snorlax", "rating": 717, "opRating": 282}, {"opponent": "linoone", "rating": 693, "opRating": 306}, {"opponent": "noctowl", "rating": 651, "opRating": 348}, {"opponent": "turtonator", "rating": 647, "opRating": 352}, {"opponent": "farfetchd", "rating": 519, "opRating": 480}], "counters": [{"opponent": "cradily", "rating": 190}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "furret", "rating": 237}, {"opponent": "gligar", "rating": 278}, {"opponent": "talonflame", "rating": 292}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 49042}, {"moveId": "ZEN_HEADBUTT", "uses": 9258}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 17393}, {"moveId": "STOMP", "uses": 15955}, {"moveId": "MEGAHORN", "uses": 15576}, {"moveId": "RETURN", "uses": 9354}]}, "moveset": ["TACKLE", "WILD_CHARGE", "STOMP"], "score": 52.8}, {"speciesId": "shaymin_sky", "speciesName": "<PERSON><PERSON> (Sky)", "rating": 398, "matchups": [{"opponent": "swampert", "rating": 963, "opRating": 36}, {"opponent": "swampert_shadow", "rating": 947, "opRating": 52}, {"opponent": "quagsire_shadow", "rating": 930, "opRating": 69}, {"opponent": "rhydon", "rating": 918, "opRating": 81}, {"opponent": "claydol", "rating": 873, "opRating": 126}], "counters": [{"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 200}, {"opponent": "gligar", "rating": 217}, {"opponent": "cradily", "rating": 239}, {"opponent": "furret", "rating": 243}], "moves": {"fastMoves": [{"moveId": "MAGICAL_LEAF", "uses": 8191}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4271}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4194}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3685}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3586}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3303}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3295}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2945}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2882}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2842}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2783}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2779}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2732}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2591}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2542}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2312}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2070}, {"moveId": "ZEN_HEADBUTT", "uses": 1212}], "chargedMoves": [{"moveId": "GRASS_KNOT", "uses": 31356}, {"moveId": "ENERGY_BALL", "uses": 11077}, {"moveId": "SEED_FLARE", "uses": 9318}, {"moveId": "SOLAR_BEAM", "uses": 6417}]}, "moveset": ["MAGICAL_LEAF", "GRASS_KNOT", "ENERGY_BALL"], "score": 52.6}, {"speciesId": "unfezant", "speciesName": "Unfezant", "rating": 439, "matchups": [{"opponent": "leavanny", "rating": 897, "opRating": 102}, {"opponent": "parasect", "rating": 856, "opRating": 143}, {"opponent": "chesnaught", "rating": 831, "opRating": 168}, {"opponent": "decid<PERSON><PERSON>", "rating": 831, "opRating": 168}, {"opponent": "shiftry_shadow", "rating": 823, "opRating": 176}], "counters": [{"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 221}, {"opponent": "talonflame", "rating": 270}, {"opponent": "gligar", "rating": 270}, {"opponent": "jumpluff_shadow", "rating": 277}], "moves": {"fastMoves": [{"moveId": "AIR_SLASH", "uses": 32324}, {"moveId": "STEEL_WING", "uses": 25976}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 33625}, {"moveId": "HYPER_BEAM", "uses": 17300}, {"moveId": "HEAT_WAVE", "uses": 7484}]}, "moveset": ["AIR_SLASH", "SKY_ATTACK", "HYPER_BEAM"], "score": 52.3}, {"speciesId": "dodrio", "speciesName": "Dodr<PERSON>", "rating": 409, "matchups": [{"opponent": "rhydon_shadow", "rating": 831, "opRating": 168}, {"opponent": "rhyperior", "rating": 813, "opRating": 186}, {"opponent": "farfetchd", "rating": 559, "opRating": 440}, {"opponent": "diggersby", "rating": 550, "opRating": 450}, {"opponent": "gliscor", "rating": 509, "opRating": 490}], "counters": [{"opponent": "talonflame", "rating": 196}, {"opponent": "jumpluff_shadow", "rating": 218}, {"opponent": "furret", "rating": 221}, {"opponent": "gligar", "rating": 290}, {"opponent": "cradily", "rating": 322}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 30294}, {"moveId": "FEINT_ATTACK", "uses": 28006}], "chargedMoves": [{"moveId": "BRAVE_BIRD", "uses": 18970}, {"moveId": "DRILL_PECK", "uses": 16935}, {"moveId": "AIR_CUTTER", "uses": 16251}, {"moveId": "AERIAL_ACE", "uses": 6061}]}, "moveset": ["STEEL_WING", "BRAVE_BIRD", "DRILL_PECK"], "score": 51.7}, {"speciesId": "gabite_shadow", "speciesName": "<PERSON><PERSON><PERSON> (<PERSON>)", "rating": 390, "matchups": [{"opponent": "clodsire", "rating": 662, "opRating": 337}, {"opponent": "ninetales", "rating": 632, "opRating": 367}, {"opponent": "magcargo", "rating": 556, "opRating": 443}, {"opponent": "ninetales_shadow", "rating": 556, "opRating": 443}, {"opponent": "quagsire_shadow", "rating": 511, "opRating": 488}], "counters": [{"opponent": "jumpluff_shadow", "rating": 39}, {"opponent": "gligar", "rating": 95}, {"opponent": "furret", "rating": 159}, {"opponent": "talonflame", "rating": 318}, {"opponent": "cradily", "rating": 333}], "moves": {"fastMoves": [{"moveId": "MUD_SHOT", "uses": 49379}, {"moveId": "TAKE_DOWN", "uses": 8921}], "chargedMoves": [{"moveId": "DIG", "uses": 23035}, {"moveId": "FLAMETHROWER", "uses": 20071}, {"moveId": "TWISTER", "uses": 15075}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["MUD_SHOT", "DIG", "FLAMETHROWER"], "score": 51.7}, {"speciesId": "sunflora", "speciesName": "Sunflora", "rating": 410, "matchups": [{"opponent": "claydol", "rating": 791, "opRating": 208}, {"opponent": "quagsire", "rating": 734, "opRating": 265}, {"opponent": "quagsire_shadow", "rating": 712, "opRating": 287}, {"opponent": "diggersby", "rating": 575, "opRating": 424}, {"opponent": "furret", "rating": 515}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 110}, {"opponent": "cradily", "rating": 239}, {"opponent": "talonflame", "rating": 274}, {"opponent": "clodsire", "rating": 375}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 40883}, {"moveId": "RAZOR_LEAF", "uses": 17417}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23910}, {"moveId": "LEAF_STORM", "uses": 19644}, {"moveId": "PETAL_BLIZZARD", "uses": 8194}, {"moveId": "SOLAR_BEAM", "uses": 6607}]}, "moveset": ["BULLET_SEED", "LEAF_STORM", "SLUDGE_BOMB"], "score": 51.7}, {"speciesId": "weepin<PERSON>_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 426, "matchups": [{"opponent": "whimsicott", "rating": 896, "opRating": 103}, {"opponent": "dart<PERSON>", "rating": 857, "opRating": 142}, {"opponent": "sceptile", "rating": 739, "opRating": 260}, {"opponent": "cradily_shadow", "rating": 625, "opRating": 375}, {"opponent": "claydol", "rating": 514, "opRating": 485}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 237}, {"opponent": "furret", "rating": 253}, {"opponent": "jumpluff_shadow", "rating": 297}, {"opponent": "cradily", "rating": 486}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 28606}, {"moveId": "BULLET_SEED", "uses": 20944}, {"moveId": "RAZOR_LEAF", "uses": 8744}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22722}, {"moveId": "POWER_WHIP", "uses": 19714}, {"moveId": "SEED_BOMB", "uses": 15834}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["ACID", "POWER_WHIP", "SLUDGE_BOMB"], "score": 51.7}, {"speciesId": "exeggcute_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 390, "matchups": [{"opponent": "quagsire_shadow", "rating": 880, "opRating": 119}, {"opponent": "quagsire", "rating": 880, "opRating": 119}, {"opponent": "bibarel", "rating": 848, "opRating": 151}, {"opponent": "marowak_shadow", "rating": 672, "opRating": 327}, {"opponent": "swampert_shadow", "rating": 503, "opRating": 496}], "counters": [{"opponent": "gligar", "rating": 110}, {"opponent": "furret", "rating": 112}, {"opponent": "cradily", "rating": 229}, {"opponent": "jumpluff_shadow", "rating": 241}, {"opponent": "talonflame", "rating": 462}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 30645}, {"moveId": "BULLET_SEED", "uses": 27655}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 21819}, {"moveId": "ANCIENT_POWER", "uses": 20775}, {"moveId": "PSYCHIC", "uses": 15596}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "ANCIENT_POWER"], "score": 51.6}, {"speciesId": "sawsbuck", "speciesName": "Sawsbuck", "rating": 404, "matchups": [{"opponent": "claydol", "rating": 767, "opRating": 232}, {"opponent": "gourgeist_super", "rating": 732, "opRating": 267}, {"opponent": "golurk_shadow", "rating": 690, "opRating": 309}, {"opponent": "marowak", "rating": 618, "opRating": 381}, {"opponent": "quagsire_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 207}, {"opponent": "furret", "rating": 259}, {"opponent": "cradily", "rating": 309}, {"opponent": "gligar", "rating": 335}], "moves": {"fastMoves": [{"moveId": "FEINT_ATTACK", "uses": 45577}, {"moveId": "TAKE_DOWN", "uses": 12723}], "chargedMoves": [{"moveId": "TRAILBLAZE", "uses": 18626}, {"moveId": "WILD_CHARGE", "uses": 16651}, {"moveId": "MEGAHORN", "uses": 13116}, {"moveId": "HYPER_BEAM", "uses": 6435}, {"moveId": "SOLAR_BEAM", "uses": 3459}]}, "moveset": ["FEINT_ATTACK", "WILD_CHARGE", "TRAILBLAZE"], "score": 51.6}, {"speciesId": "deerling", "speciesName": "<PERSON><PERSON>", "rating": 397, "matchups": [{"opponent": "whiscash", "rating": 672, "opRating": 327}, {"opponent": "whiscash_shadow", "rating": 640, "opRating": 359}, {"opponent": "gastrodon", "rating": 612, "opRating": 387}, {"opponent": "marowak", "rating": 580, "opRating": 419}, {"opponent": "quagsire_shadow", "rating": 521, "opRating": 478}], "counters": [{"opponent": "jumpluff_shadow", "rating": 156}, {"opponent": "talonflame", "rating": 259}, {"opponent": "furret", "rating": 290}, {"opponent": "cradily", "rating": 295}, {"opponent": "gligar", "rating": 332}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 47148}, {"moveId": "TAKE_DOWN", "uses": 11152}], "chargedMoves": [{"moveId": "WILD_CHARGE", "uses": 22509}, {"moveId": "TRAILBLAZE", "uses": 22316}, {"moveId": "SEED_BOMB", "uses": 7141}, {"moveId": "ENERGY_BALL", "uses": 6432}]}, "moveset": ["TACKLE", "WILD_CHARGE", "TRAILBLAZE"], "score": 51.3}, {"speciesId": "raticate_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 418, "matchups": [{"opponent": "linoone", "rating": 688, "opRating": 311}, {"opponent": "spinda", "rating": 676, "opRating": 323}, {"opponent": "lopunny", "rating": 627, "opRating": 372}, {"opponent": "furfrou", "rating": 622, "opRating": 377}, {"opponent": "turtonator", "rating": 606, "opRating": 393}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "furret", "rating": 190}, {"opponent": "gligar", "rating": 232}, {"opponent": "cradily", "rating": 277}, {"opponent": "talonflame", "rating": 292}], "moves": {"fastMoves": [{"moveId": "QUICK_ATTACK", "uses": 40934}, {"moveId": "BITE", "uses": 17366}], "chargedMoves": [{"moveId": "HYPER_FANG", "uses": 30277}, {"moveId": "DIG", "uses": 19903}, {"moveId": "HYPER_BEAM", "uses": 8123}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["QUICK_ATTACK", "HYPER_FANG", "DIG"], "score": 50.7}, {"speciesId": "rowlet", "speciesName": "<PERSON><PERSON>", "rating": 390, "matchups": [{"opponent": "quagsire", "rating": 954, "opRating": 45}, {"opponent": "swampert_shadow", "rating": 931, "opRating": 68}, {"opponent": "marowak_shadow", "rating": 844, "opRating": 155}, {"opponent": "quagsire_shadow", "rating": 795, "opRating": 204}, {"opponent": "claydol", "rating": 597, "opRating": 402}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "talonflame", "rating": 162}, {"opponent": "cradily", "rating": 163}, {"opponent": "gligar", "rating": 202}, {"opponent": "furret", "rating": 443}], "moves": {"fastMoves": [{"moveId": "LEAFAGE", "uses": 16724}, {"moveId": "MAGICAL_LEAF", "uses": 16412}, {"moveId": "TACKLE", "uses": 14039}, {"moveId": "RAZOR_LEAF", "uses": 11158}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 40261}, {"moveId": "ENERGY_BALL", "uses": 18039}]}, "moveset": ["LEAFAGE", "SEED_BOMB", "ENERGY_BALL"], "score": 50.4}, {"speciesId": "chatot", "speciesName": "Chatot", "rating": 399, "matchups": [{"opponent": "rhydon_shadow", "rating": 806, "opRating": 193}, {"opponent": "gourgeist_large", "rating": 700, "opRating": 299}, {"opponent": "gourgeist_super", "rating": 700, "opRating": 299}, {"opponent": "<PERSON><PERSON><PERSON>", "rating": 615, "opRating": 384}, {"opponent": "gliscor", "rating": 574, "opRating": 425}], "counters": [{"opponent": "jumpluff_shadow", "rating": 183}, {"opponent": "talonflame", "rating": 196}, {"opponent": "furret", "rating": 265}, {"opponent": "cradily", "rating": 322}, {"opponent": "gligar", "rating": 458}], "moves": {"fastMoves": [{"moveId": "STEEL_WING", "uses": 30388}, {"moveId": "PECK", "uses": 27912}], "chargedMoves": [{"moveId": "SKY_ATTACK", "uses": 30803}, {"moveId": "NIGHT_SHADE", "uses": 20612}, {"moveId": "HEAT_WAVE", "uses": 7014}]}, "moveset": ["STEEL_WING", "SKY_ATTACK", "NIGHT_SHADE"], "score": 50.2}, {"speciesId": "porygon_z_shadow", "speciesName": "Porygon-Z (Shadow)", "rating": 391, "matchups": [{"opponent": "swampert_shadow", "rating": 696, "opRating": 303}, {"opponent": "noctowl", "rating": 662, "opRating": 337}, {"opponent": "gliscor_shadow", "rating": 653, "opRating": 346}, {"opponent": "pidgeott<PERSON>_shadow", "rating": 653, "opRating": 346}, {"opponent": "staravia", "rating": 653, "opRating": 346}], "counters": [{"opponent": "jumpluff_shadow", "rating": 133}, {"opponent": "furret", "rating": 134}, {"opponent": "cradily", "rating": 309}, {"opponent": "gligar", "rating": 332}, {"opponent": "talonflame", "rating": 462}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 10746}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4339}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3647}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3134}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3128}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3113}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2845}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2839}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2839}, {"moveId": "CHARGE_BEAM", "uses": 2763}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2610}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2600}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2578}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2547}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2386}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2331}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2102}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1911}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18473}, {"moveId": "BLIZZARD", "uses": 15646}, {"moveId": "HYPER_BEAM", "uses": 9117}, {"moveId": "SOLAR_BEAM", "uses": 8185}, {"moveId": "ZAP_CANNON", "uses": 6945}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 49.8}, {"speciesId": "vileplume_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 434, "matchups": [{"opponent": "whimsicott", "rating": 895, "opRating": 104}, {"opponent": "dart<PERSON>", "rating": 833, "opRating": 166}, {"opponent": "sceptile_shadow", "rating": 712, "opRating": 287}, {"opponent": "cradily_shadow", "rating": 670, "opRating": 329}, {"opponent": "bellossom", "rating": 633, "opRating": 366}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "furret", "rating": 221}, {"opponent": "talonflame", "rating": 237}, {"opponent": "cradily", "rating": 253}, {"opponent": "jumpluff_shadow", "rating": 326}], "moves": {"fastMoves": [{"moveId": "ACID", "uses": 42251}, {"moveId": "RAZOR_LEAF", "uses": 16049}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 23681}, {"moveId": "PETAL_BLIZZARD", "uses": 14647}, {"moveId": "MOONBLAST", "uses": 14000}, {"moveId": "SOLAR_BEAM", "uses": 5866}, {"moveId": "FRUSTRATION", "uses": 1}]}, "moveset": ["ACID", "SLUDGE_BOMB", "MOONBLAST"], "score": 49.8}, {"speciesId": "audino", "speciesName": "Audino", "rating": 380, "matchups": [{"opponent": "gra<PERSON><PERSON><PERSON>", "rating": 621, "opRating": 378}, {"opponent": "amoon<PERSON><PERSON>_shadow", "rating": 610, "opRating": 389}, {"opponent": "aipom_shadow", "rating": 602, "opRating": 397}, {"opponent": "monferno", "rating": 589, "opRating": 410}, {"opponent": "tauros_blaze", "rating": 550, "opRating": 450}], "counters": [{"opponent": "jumpluff_shadow", "rating": 160}, {"opponent": "cradily", "rating": 250}, {"opponent": "furret", "rating": 300}, {"opponent": "talonflame", "rating": 344}, {"opponent": "gligar", "rating": 347}], "moves": {"fastMoves": [{"moveId": "ZEN_HEADBUTT", "uses": 33506}, {"moveId": "POUND", "uses": 24794}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29679}, {"moveId": "DISARMING_VOICE", "uses": 15865}, {"moveId": "HYPER_BEAM", "uses": 6998}, {"moveId": "DAZZLING_GLEAM", "uses": 5811}]}, "moveset": ["ZEN_HEADBUTT", "BODY_SLAM", "DISARMING_VOICE"], "score": 49.2}, {"speciesId": "porygon_z", "speciesName": "Porygon-Z", "rating": 401, "matchups": [{"opponent": "victini", "rating": 782, "opRating": 217}, {"opponent": "noctowl", "rating": 717, "opRating": 282}, {"opponent": "staraptor", "rating": 666, "opRating": 333}, {"opponent": "drampa", "rating": 529, "opRating": 470}, {"opponent": "ursaring_shadow", "rating": 517, "opRating": 482}], "counters": [{"opponent": "jumpluff_shadow", "rating": 78}, {"opponent": "furret", "rating": 134}, {"opponent": "cradily", "rating": 274}, {"opponent": "gligar", "rating": 305}, {"opponent": "talonflame", "rating": 437}], "moves": {"fastMoves": [{"moveId": "LOCK_ON", "uses": 9924}, {"moveId": "HIDDEN_POWER_ICE", "uses": 4395}, {"moveId": "HIDDEN_POWER_WATER", "uses": 3696}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3219}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3211}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 3139}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 2886}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 2879}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 2825}, {"moveId": "CHARGE_BEAM", "uses": 2731}, {"moveId": "HIDDEN_POWER_DARK", "uses": 2695}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 2662}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 2643}, {"moveId": "HIDDEN_POWER_BUG", "uses": 2623}, {"moveId": "HIDDEN_POWER_POISON", "uses": 2455}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2362}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2196}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 1901}], "chargedMoves": [{"moveId": "TRI_ATTACK", "uses": 18514}, {"moveId": "BLIZZARD", "uses": 15623}, {"moveId": "HYPER_BEAM", "uses": 9119}, {"moveId": "SOLAR_BEAM", "uses": 8167}, {"moveId": "ZAP_CANNON", "uses": 6944}]}, "moveset": ["LOCK_ON", "TRI_ATTACK", "ZAP_CANNON"], "score": 49.2}, {"speciesId": "trapinch_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 345, "matchups": [{"opponent": "clodsire", "rating": 845, "opRating": 154}, {"opponent": "lileep", "rating": 795, "opRating": 204}, {"opponent": "lileep_shadow", "rating": 745, "opRating": 254}, {"opponent": "cradily_shadow", "rating": 725, "opRating": 275}, {"opponent": "magcargo", "rating": 545, "opRating": 454}], "counters": [{"opponent": "jumpluff_shadow", "rating": 65}, {"opponent": "gligar", "rating": 83}, {"opponent": "furret", "rating": 200}, {"opponent": "cradily", "rating": 347}, {"opponent": "talonflame", "rating": 411}], "moves": {"fastMoves": [{"moveId": "SAND_ATTACK", "uses": 24354}, {"moveId": "MUD_SHOT", "uses": 22679}, {"moveId": "STRUGGLE_BUG", "uses": 11299}], "chargedMoves": [{"moveId": "CRUNCH", "uses": 22127}, {"moveId": "SCORCHING_SANDS", "uses": 21729}, {"moveId": "DIG", "uses": 7295}, {"moveId": "SAND_TOMB", "uses": 7157}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["SAND_ATTACK", "SCORCHING_SANDS", "CRUNCH"], "score": 48.6}, {"speciesId": "bulbasaur", "speciesName": "Bulbasaur", "rating": 381, "matchups": [{"opponent": "whimsicott", "rating": 833, "opRating": 166}, {"opponent": "swampert_shadow", "rating": 787, "opRating": 212}, {"opponent": "vigoroth_shadow", "rating": 725, "opRating": 275}, {"opponent": "run<PERSON><PERSON>", "rating": 725, "opRating": 275}, {"opponent": "claydol", "rating": 620, "opRating": 379}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 141}, {"opponent": "furret", "rating": 206}, {"opponent": "talonflame", "rating": 318}, {"opponent": "cradily", "rating": 329}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 36657}, {"moveId": "TACKLE", "uses": 21643}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 19072}, {"moveId": "POWER_WHIP", "uses": 17572}, {"moveId": "SEED_BOMB", "uses": 13991}, {"moveId": "RETURN", "uses": 7679}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 48.5}, {"speciesId": "regigigas", "speciesName": "Regigigas", "rating": 361, "matchups": [{"opponent": "gliscor_shadow", "rating": 725, "opRating": 274}, {"opponent": "vibrava_shadow", "rating": 684, "opRating": 315}, {"opponent": "flygon_shadow", "rating": 657, "opRating": 342}, {"opponent": "flygon", "rating": 644, "opRating": 355}, {"opponent": "gligar", "rating": 536}], "counters": [{"opponent": "furret", "rating": 178}, {"opponent": "talonflame", "rating": 240}, {"opponent": "clodsire", "rating": 240}, {"opponent": "cradily", "rating": 288}, {"opponent": "jumpluff_shadow", "rating": 369}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5245}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4511}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4156}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 4021}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 3970}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3614}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3594}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3421}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3420}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3407}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3385}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3369}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3085}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 3067}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2760}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2454}, {"moveId": "ZEN_HEADBUTT", "uses": 967}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 32878}, {"moveId": "FOCUS_BLAST", "uses": 12929}, {"moveId": "THUNDER", "uses": 7771}, {"moveId": "GIGA_IMPACT", "uses": 4610}]}, "moveset": ["HIDDEN_POWER_ICE", "CRUSH_GRIP", "FOCUS_BLAST"], "score": 48.5}, {"speciesId": "snover", "speciesName": "Snover", "rating": 399, "matchups": [{"opponent": "dart<PERSON>", "rating": 799, "opRating": 200}, {"opponent": "whiscash", "rating": 700, "opRating": 299}, {"opponent": "flygon_shadow", "rating": 602, "opRating": 397}, {"opponent": "piloswine", "rating": 552, "opRating": 447}, {"opponent": "abomasnow_shadow", "rating": 507, "opRating": 492}], "counters": [{"opponent": "talonflame", "rating": 103}, {"opponent": "cradily", "rating": 208}, {"opponent": "gligar", "rating": 309}, {"opponent": "jumpluff_shadow", "rating": 326}, {"opponent": "furret", "rating": 465}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 23648}, {"moveId": "ICE_SHARD", "uses": 18572}, {"moveId": "LEAFAGE", "uses": 16150}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 24284}, {"moveId": "ENERGY_BALL", "uses": 14422}, {"moveId": "STOMP", "uses": 12383}, {"moveId": "RETURN", "uses": 7146}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 48.5}, {"speciesId": "snover_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 387, "matchups": [{"opponent": "claydol", "rating": 742, "opRating": 257}, {"opponent": "whiscash", "rating": 669, "opRating": 330}, {"opponent": "whiscash_shadow", "rating": 633, "opRating": 366}, {"opponent": "flygon", "rating": 602, "opRating": 397}, {"opponent": "flygon_shadow", "rating": 598, "opRating": 401}], "counters": [{"opponent": "talonflame", "rating": 129}, {"opponent": "furret", "rating": 190}, {"opponent": "cradily", "rating": 253}, {"opponent": "gligar", "rating": 385}, {"opponent": "jumpluff_shadow", "rating": 431}], "moves": {"fastMoves": [{"moveId": "POWDER_SNOW", "uses": 23930}, {"moveId": "ICE_SHARD", "uses": 18457}, {"moveId": "LEAFAGE", "uses": 15969}], "chargedMoves": [{"moveId": "ICE_BEAM", "uses": 27353}, {"moveId": "ENERGY_BALL", "uses": 16215}, {"moveId": "STOMP", "uses": 14685}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["POWDER_SNOW", "ICE_BEAM", "ENERGY_BALL"], "score": 48.5}, {"speciesId": "turtwig", "speciesName": "<PERSON><PERSON><PERSON>", "rating": 399, "matchups": [{"opponent": "whiscash", "rating": 788, "opRating": 211}, {"opponent": "quagsire", "rating": 744, "opRating": 255}, {"opponent": "quagsire_shadow", "rating": 722, "opRating": 277}, {"opponent": "whiscash_shadow", "rating": 662, "opRating": 337}, {"opponent": "gastrodon", "rating": 629, "opRating": 370}], "counters": [{"opponent": "cradily", "rating": 97}, {"opponent": "jumpluff_shadow", "rating": 153}, {"opponent": "gligar", "rating": 164}, {"opponent": "talonflame", "rating": 300}, {"opponent": "furret", "rating": 456}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 31568}, {"moveId": "RAZOR_LEAF", "uses": 26732}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 24551}, {"moveId": "SEED_BOMB", "uses": 19025}, {"moveId": "ENERGY_BALL", "uses": 8510}, {"moveId": "RETURN", "uses": 6171}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 48.5}, {"speciesId": "maractus", "speciesName": "Maractus", "rating": 384, "matchups": [{"opponent": "claydol", "rating": 750, "opRating": 250}, {"opponent": "golurk_shadow", "rating": 722, "opRating": 277}, {"opponent": "quagsire_shadow", "rating": 718, "opRating": 281}, {"opponent": "quagsire", "rating": 718, "opRating": 281}, {"opponent": "diggersby", "rating": 546, "opRating": 453}], "counters": [{"opponent": "gligar", "rating": 110}, {"opponent": "talonflame", "rating": 200}, {"opponent": "cradily", "rating": 211}, {"opponent": "jumpluff_shadow", "rating": 245}, {"opponent": "furret", "rating": 278}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 29243}, {"moveId": "BULLET_SEED", "uses": 29057}], "chargedMoves": [{"moveId": "AERIAL_ACE", "uses": 33243}, {"moveId": "PETAL_BLIZZARD", "uses": 17911}, {"moveId": "SOLAR_BEAM", "uses": 7132}]}, "moveset": ["BULLET_SEED", "AERIAL_ACE", "PETAL_BLIZZARD"], "score": 48.3}, {"speciesId": "roselia", "speciesName": "Roselia", "rating": 413, "matchups": [{"opponent": "whimsicott", "rating": 900, "opRating": 100}, {"opponent": "dart<PERSON>", "rating": 877, "opRating": 122}, {"opponent": "sceptile_shadow", "rating": 790, "opRating": 209}, {"opponent": "sceptile", "rating": 750, "opRating": 250}, {"opponent": "lileep", "rating": 536, "opRating": 463}], "counters": [{"opponent": "gligar", "rating": 156}, {"opponent": "talonflame", "rating": 211}, {"opponent": "cradily", "rating": 253}, {"opponent": "furret", "rating": 303}, {"opponent": "jumpluff_shadow", "rating": 362}], "moves": {"fastMoves": [{"moveId": "POISON_JAB", "uses": 27054}, {"moveId": "MAGICAL_LEAF", "uses": 20659}, {"moveId": "RAZOR_LEAF", "uses": 10634}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 26079}, {"moveId": "PETAL_BLIZZARD", "uses": 17837}, {"moveId": "DAZZLING_GLEAM", "uses": 14393}]}, "moveset": ["POISON_JAB", "SLUDGE_BOMB", "PETAL_BLIZZARD"], "score": 48.3}, {"speciesId": "exeggutor", "speciesName": "Exeggutor", "rating": 380, "matchups": [{"opponent": "quagsire", "rating": 870, "opRating": 129}, {"opponent": "tauros_blaze", "rating": 801, "opRating": 198}, {"opponent": "marowak_shadow", "rating": 755, "opRating": 244}, {"opponent": "golurk", "rating": 755, "opRating": 244}, {"opponent": "quagsire_shadow", "rating": 732, "opRating": 267}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "gligar", "rating": 110}, {"opponent": "furret", "rating": 134}, {"opponent": "cradily", "rating": 253}, {"opponent": "talonflame", "rating": 300}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19577}, {"moveId": "BULLET_SEED", "uses": 18610}, {"moveId": "EXTRASENSORY", "uses": 15089}, {"moveId": "ZEN_HEADBUTT", "uses": 5068}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26314}, {"moveId": "PSYCHIC", "uses": 24351}, {"moveId": "SOLAR_BEAM", "uses": 7586}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 47}, {"speciesId": "exeggutor_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 377, "matchups": [{"opponent": "marowak", "rating": 755, "opRating": 244}, {"opponent": "gastrodon", "rating": 755, "opRating": 244}, {"opponent": "quagsire", "rating": 732, "opRating": 267}, {"opponent": "marowak_shadow", "rating": 729, "opRating": 270}, {"opponent": "quagsire_shadow", "rating": 664, "opRating": 335}], "counters": [{"opponent": "jumpluff_shadow", "rating": 42}, {"opponent": "gligar", "rating": 99}, {"opponent": "furret", "rating": 134}, {"opponent": "cradily", "rating": 298}, {"opponent": "talonflame", "rating": 370}], "moves": {"fastMoves": [{"moveId": "CONFUSION", "uses": 19474}, {"moveId": "BULLET_SEED", "uses": 19204}, {"moveId": "EXTRASENSORY", "uses": 14862}, {"moveId": "ZEN_HEADBUTT", "uses": 4761}], "chargedMoves": [{"moveId": "SEED_BOMB", "uses": 26226}, {"moveId": "PSYCHIC", "uses": 24298}, {"moveId": "SOLAR_BEAM", "uses": 7631}, {"moveId": "FRUSTRATION", "uses": 3}]}, "moveset": ["BULLET_SEED", "SEED_BOMB", "PSYCHIC"], "score": 46.8}, {"speciesId": "bulbasaur_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 356, "matchups": [{"opponent": "whimsicott", "rating": 833, "opRating": 166}, {"opponent": "swampert", "rating": 787, "opRating": 212}, {"opponent": "swampert_shadow", "rating": 770, "opRating": 229}, {"opponent": "quagsire_shadow", "rating": 637, "opRating": 362}, {"opponent": "claydol", "rating": 583, "opRating": 416}], "counters": [{"opponent": "jumpluff_shadow", "rating": 75}, {"opponent": "gligar", "rating": 156}, {"opponent": "furret", "rating": 159}, {"opponent": "talonflame", "rating": 344}, {"opponent": "cradily", "rating": 350}], "moves": {"fastMoves": [{"moveId": "VINE_WHIP", "uses": 37071}, {"moveId": "TACKLE", "uses": 21229}], "chargedMoves": [{"moveId": "SLUDGE_BOMB", "uses": 22719}, {"moveId": "POWER_WHIP", "uses": 19729}, {"moveId": "SEED_BOMB", "uses": 15835}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["VINE_WHIP", "POWER_WHIP", "SLUDGE_BOMB"], "score": 46.4}, {"speciesId": "turtwig_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 390, "matchups": [{"opponent": "quagsire", "rating": 722, "opRating": 277}, {"opponent": "whiscash_shadow", "rating": 688, "opRating": 311}, {"opponent": "whiscash", "rating": 662, "opRating": 337}, {"opponent": "hippo<PERSON><PERSON>", "rating": 618, "opRating": 381}, {"opponent": "hippow<PERSON>_shadow", "rating": 585, "opRating": 414}], "counters": [{"opponent": "cradily", "rating": 149}, {"opponent": "gligar", "rating": 164}, {"opponent": "furret", "rating": 218}, {"opponent": "jumpluff_shadow", "rating": 228}, {"opponent": "talonflame", "rating": 355}], "moves": {"fastMoves": [{"moveId": "TACKLE", "uses": 31095}, {"moveId": "RAZOR_LEAF", "uses": 27205}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 28114}, {"moveId": "SEED_BOMB", "uses": 20799}, {"moveId": "ENERGY_BALL", "uses": 9299}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["TACKLE", "BODY_SLAM", "SEED_BOMB"], "score": 46.1}, {"speciesId": "ferroseed", "speciesName": "Ferroseed", "rating": 353, "matchups": [{"opponent": "lileep", "rating": 642, "opRating": 357}, {"opponent": "nidoqueen_shadow", "rating": 600, "opRating": 399}, {"opponent": "victree<PERSON>_shadow", "rating": 571, "opRating": 428}, {"opponent": "lileep_shadow", "rating": 571, "opRating": 428}, {"opponent": "cradily", "rating": 542}], "counters": [{"opponent": "talonflame", "rating": 159}, {"opponent": "jumpluff_shadow", "rating": 264}, {"opponent": "gligar", "rating": 293}, {"opponent": "clodsire", "rating": 293}, {"opponent": "furret", "rating": 325}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 30639}, {"moveId": "TACKLE", "uses": 27661}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 25211}, {"moveId": "RETURN", "uses": 17091}, {"moveId": "FLASH_CANNON", "uses": 8001}, {"moveId": "GYRO_BALL", "uses": 7978}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "RETURN"], "score": 45.3}, {"speciesId": "rotom_mow", "speciesName": "Rotom (Mow)", "rating": 363, "matchups": [{"opponent": "victini", "rating": 833, "opRating": 166}, {"opponent": "incineroar", "rating": 822, "opRating": 177}, {"opponent": "noctowl", "rating": 801, "opRating": 198}, {"opponent": "pidgeot", "rating": 639, "opRating": 360}, {"opponent": "pidgeot_shadow", "rating": 575, "opRating": 424}], "counters": [{"opponent": "gligar", "rating": 80}, {"opponent": "jumpluff_shadow", "rating": 147}, {"opponent": "furret", "rating": 196}, {"opponent": "cradily", "rating": 208}, {"opponent": "talonflame", "rating": 481}], "moves": {"fastMoves": [{"moveId": "THUNDER_SHOCK", "uses": 29854}, {"moveId": "ASTONISH", "uses": 28446}], "chargedMoves": [{"moveId": "THUNDERBOLT", "uses": 26187}, {"moveId": "OMINOUS_WIND", "uses": 20671}, {"moveId": "THUNDER", "uses": 11377}]}, "moveset": ["THUNDER_SHOCK", "THUNDERBOLT", "OMINOUS_WIND"], "score": 43.8}, {"speciesId": "ferroseed_shadow", "speciesName": "<PERSON><PERSON><PERSON><PERSON> (Shadow)", "rating": 340, "matchups": [{"opponent": "lileep_shadow", "rating": 693, "opRating": 306}, {"opponent": "cradily_shadow", "rating": 605, "opRating": 394}, {"opponent": "nidoqueen", "rating": 600, "opRating": 399}, {"opponent": "victreebel", "rating": 571, "opRating": 428}, {"opponent": "lileep", "rating": 571, "opRating": 428}], "counters": [{"opponent": "talonflame", "rating": 77}, {"opponent": "jumpluff_shadow", "rating": 284}, {"opponent": "gligar", "rating": 316}, {"opponent": "furret", "rating": 350}, {"opponent": "cradily", "rating": 493}], "moves": {"fastMoves": [{"moveId": "METAL_CLAW", "uses": 31437}, {"moveId": "TACKLE", "uses": 26863}], "chargedMoves": [{"moveId": "IRON_HEAD", "uses": 35601}, {"moveId": "FLASH_CANNON", "uses": 11384}, {"moveId": "GYRO_BALL", "uses": 11109}, {"moveId": "FRUSTRATION", "uses": 137}]}, "moveset": ["METAL_CLAW", "IRON_HEAD", "FLASH_CANNON"], "score": 42.9}, {"speciesId": "regigigas_shadow", "speciesName": "<PERSON><PERSON><PERSON> (Shadow)", "rating": 316, "matchups": [{"opponent": "incineroar", "rating": 777, "opRating": 222}, {"opponent": "salazzle", "rating": 759, "opRating": 240}, {"opponent": "arcanine_<PERSON><PERSON>an", "rating": 681, "opRating": 318}, {"opponent": "magcargo", "rating": 631, "opRating": 368}, {"opponent": "magmortar_shadow", "rating": 631, "opRating": 368}], "counters": [{"opponent": "jumpluff_shadow", "rating": 52}, {"opponent": "gligar", "rating": 80}, {"opponent": "talonflame", "rating": 177}, {"opponent": "cradily", "rating": 208}, {"opponent": "furret", "rating": 218}], "moves": {"fastMoves": [{"moveId": "HIDDEN_POWER_ICE", "uses": 5270}, {"moveId": "HIDDEN_POWER_WATER", "uses": 4573}, {"moveId": "HIDDEN_POWER_FIGHTING", "uses": 4185}, {"moveId": "HIDDEN_POWER_ROCK", "uses": 4048}, {"moveId": "HIDDEN_POWER_FLYING", "uses": 3992}, {"moveId": "HIDDEN_POWER_GROUND", "uses": 3613}, {"moveId": "HIDDEN_POWER_FIRE", "uses": 3562}, {"moveId": "HIDDEN_POWER_GRASS", "uses": 3448}, {"moveId": "HIDDEN_POWER_DARK", "uses": 3398}, {"moveId": "HIDDEN_POWER_DRAGON", "uses": 3383}, {"moveId": "HIDDEN_POWER_PSYCHIC", "uses": 3382}, {"moveId": "HIDDEN_POWER_BUG", "uses": 3341}, {"moveId": "HIDDEN_POWER_POISON", "uses": 3082}, {"moveId": "HIDDEN_POWER_STEEL", "uses": 2982}, {"moveId": "HIDDEN_POWER_GHOST", "uses": 2737}, {"moveId": "HIDDEN_POWER_ELECTRIC", "uses": 2398}, {"moveId": "ZEN_HEADBUTT", "uses": 851}], "chargedMoves": [{"moveId": "CRUSH_GRIP", "uses": 32805}, {"moveId": "FOCUS_BLAST", "uses": 12943}, {"moveId": "THUNDER", "uses": 7751}, {"moveId": "GIGA_IMPACT", "uses": 4604}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["HIDDEN_POWER_GROUND", "THUNDER", "FOCUS_BLAST"], "score": 42}, {"speciesId": "cherrim_overcast", "speciesName": "<PERSON><PERSON><PERSON> (Overcast)", "rating": 347, "matchups": [{"opponent": "quagsire", "rating": 796, "opRating": 203}, {"opponent": "whiscash", "rating": 738, "opRating": 261}, {"opponent": "quagsire_shadow", "rating": 726, "opRating": 273}, {"opponent": "gastrodon", "rating": 688, "opRating": 311}, {"opponent": "claydol", "rating": 584, "opRating": 415}], "counters": [{"opponent": "jumpluff_shadow", "rating": 49}, {"opponent": "cradily", "rating": 104}, {"opponent": "gligar", "rating": 110}, {"opponent": "furret", "rating": 159}, {"opponent": "talonflame", "rating": 200}], "moves": {"fastMoves": [{"moveId": "BULLET_SEED", "uses": 35931}, {"moveId": "RAZOR_LEAF", "uses": 22369}], "chargedMoves": [{"moveId": "DAZZLING_GLEAM", "uses": 23329}, {"moveId": "SOLAR_BEAM", "uses": 19783}, {"moveId": "HYPER_BEAM", "uses": 15242}]}, "moveset": ["BULLET_SEED", "DAZZLING_GLEAM", "SOLAR_BEAM"], "score": 40.7}, {"speciesId": "skiddo", "speciesName": "Skiddo", "rating": 319, "matchups": [{"opponent": "obstagoon_shadow", "rating": 678, "opRating": 321}, {"opponent": "quagsire", "rating": 562, "opRating": 437}, {"opponent": "marowak", "rating": 559, "opRating": 440}, {"opponent": "quagsire_shadow", "rating": 513, "opRating": 486}, {"opponent": "dunsparce", "rating": 509, "opRating": 490}], "counters": [{"opponent": "gligar", "rating": 83}, {"opponent": "jumpluff_shadow", "rating": 94}, {"opponent": "talonflame", "rating": 107}, {"opponent": "cradily", "rating": 239}, {"opponent": "furret", "rating": 346}], "moves": {"fastMoves": [{"moveId": "ROCK_SMASH", "uses": 38277}, {"moveId": "ZEN_HEADBUTT", "uses": 20023}], "chargedMoves": [{"moveId": "ROCK_SLIDE", "uses": 20826}, {"moveId": "SEED_BOMB", "uses": 19703}, {"moveId": "BRICK_BREAK", "uses": 17796}]}, "moveset": ["ROCK_SMASH", "SEED_BOMB", "ROCK_SLIDE"], "score": 38.9}, {"speciesId": "slaking", "speciesName": "Slaking", "rating": 127, "matchups": [{"opponent": "regigigas", "rating": 532, "opRating": 467}], "counters": [{"opponent": "furret", "rating": 18}, {"opponent": "jumpluff_shadow", "rating": 19}, {"opponent": "cradily", "rating": 24}, {"opponent": "gligar", "rating": 26}, {"opponent": "talonflame", "rating": 207}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 58300}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29807}, {"moveId": "EARTHQUAKE", "uses": 11853}, {"moveId": "PLAY_ROUGH", "uses": 8860}, {"moveId": "HYPER_BEAM", "uses": 7706}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 14.1}, {"speciesId": "slaking_shadow", "speciesName": "Slaking (Shadow)", "rating": 108, "matchups": [], "counters": [{"opponent": "furret", "rating": 18}, {"opponent": "jumpluff_shadow", "rating": 19}, {"opponent": "gligar", "rating": 22}, {"opponent": "cradily", "rating": 24}, {"opponent": "talonflame", "rating": 233}], "moves": {"fastMoves": [{"moveId": "YAWN", "uses": 58300}], "chargedMoves": [{"moveId": "BODY_SLAM", "uses": 29434}, {"moveId": "EARTHQUAKE", "uses": 11782}, {"moveId": "PLAY_ROUGH", "uses": 8848}, {"moveId": "HYPER_BEAM", "uses": 8114}, {"moveId": "FRUSTRATION", "uses": 0}]}, "moveset": ["YAWN", "BODY_SLAM", "EARTHQUAKE"], "score": 12.3}]